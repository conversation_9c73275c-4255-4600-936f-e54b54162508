# CRUD Operations & Budget Integration Test Plan

## 🎯 **TEST OVERVIEW**

This document provides a comprehensive test plan to verify that Income and Expenditure CRUD operations properly integrate with the Budget Management system.

---

## ✅ **INCOME MODULE CRUD TESTS**

### **Test 1: Create Income Transaction**

#### **Test Steps**:
1. Navigate to `/dashboard/accounting/income/overview`
2. Click "Record Income" button
3. Fill out the income form:
   - **Date**: Current date
   - **Source**: Government Subvention
   - **Amount**: MWK 500,000
   - **Reference**: TEST-INC-001
   - **Description**: Test income transaction
   - **Budget Category**: Select active budget category
4. Submit the form

#### **Expected Results**:
- ✅ Income transaction created successfully
- ✅ Success toast notification displayed
- ✅ Modal closes automatically
- ✅ Income table refreshes with new transaction
- ✅ Budget dashboard shows updated actual income
- ✅ Budget performance metrics recalculated

#### **Budget Integration Verification**:
- Check budget actual income increased by MWK 500,000
- Verify budget utilization percentage updated
- Confirm budget variance calculations adjusted

---

### **Test 2: Edit Income Transaction**

#### **Test Steps**:
1. In the income table, click the action menu for TEST-INC-001
2. Select "Edit income"
3. Modify the amount from MWK 500,000 to MWK 750,000
4. Update description to "Updated test income"
5. Submit the changes

#### **Expected Results**:
- ✅ Income transaction updated successfully
- ✅ Success toast notification displayed
- ✅ Table shows updated values
- ✅ Budget reflects the difference (+MWK 250,000)

#### **Budget Integration Verification**:
- Budget actual income should increase by additional MWK 250,000
- Total budget actual income should be MWK 750,000 for this transaction

---

### **Test 3: Delete Income Transaction**

#### **Test Steps**:
1. In the income table, click the action menu for TEST-INC-001
2. Select "Delete"
3. Confirm deletion in the alert dialog

#### **Expected Results**:
- ✅ Income transaction deleted successfully
- ✅ Success toast notification displayed
- ✅ Transaction removed from table
- ✅ Budget actual income decreased by MWK 750,000

#### **Budget Integration Verification**:
- Budget actual income should revert to previous amount
- Budget performance metrics should reflect the removal

---

## 💰 **EXPENDITURE MODULE CRUD TESTS**

### **Test 4: Create Expenditure Transaction**

#### **Test Steps**:
1. Navigate to `/dashboard/accounting/expenditures`
2. Click "New Expenditure" button
3. Fill out the expenditure form:
   - **Title**: Test Office Supplies
   - **Description**: Testing expenditure creation
   - **Category**: OFFICE_SUPPLIES
   - **Amount**: MWK 200,000
   - **Vendor**: Test Vendor Ltd
   - **Department**: Finance
   - **Budget Allocation**: Select budget category
4. Submit the form

#### **Expected Results**:
- ✅ Expenditure created successfully
- ✅ Success toast notification displayed
- ✅ Modal closes automatically
- ✅ Expenditure table refreshes with new transaction
- ✅ Budget dashboard shows updated actual expenditure
- ✅ Budget available amount decreased

#### **Budget Integration Verification**:
- Budget actual expenditure increased by MWK 200,000
- Budget available amount decreased by MWK 200,000
- Budget utilization percentage updated

---

### **Test 5: Edit Expenditure Transaction**

#### **Test Steps**:
1. In the expenditure table, click the action menu for the test expenditure
2. Select "Edit"
3. Modify the amount from MWK 200,000 to MWK 300,000
4. Update title to "Updated Office Supplies"
5. Submit the changes

#### **Expected Results**:
- ✅ Expenditure updated successfully
- ✅ Success toast notification displayed
- ✅ Table shows updated values
- ✅ Budget reflects the difference (+MWK 100,000)

#### **Budget Integration Verification**:
- Budget actual expenditure should increase by additional MWK 100,000
- Budget available amount should decrease by additional MWK 100,000

---

### **Test 6: Delete Expenditure Transaction**

#### **Test Steps**:
1. In the expenditure table, click the action menu for the test expenditure
2. Select "Delete"
3. Confirm deletion (when dialog is implemented)

#### **Expected Results**:
- ✅ Expenditure deleted successfully
- ✅ Success toast notification displayed
- ✅ Transaction removed from table
- ✅ Budget actual expenditure decreased by MWK 300,000
- ✅ Budget available amount increased by MWK 300,000

---

## 📊 **BUDGET INTEGRATION VERIFICATION**

### **Real-time Budget Updates**

#### **Test 7: Budget Dashboard Synchronization**

1. Open budget dashboard in a separate tab
2. Perform income/expenditure CRUD operations
3. Refresh budget dashboard

#### **Expected Results**:
- ✅ Budget metrics update immediately
- ✅ Charts reflect new data
- ✅ Performance indicators recalculated
- ✅ Variance analysis updated

#### **Key Metrics to Verify**:
- Total Actual Income
- Total Actual Expenditure
- Budget Utilization Percentage
- Budget Variance (Actual vs Budgeted)
- Available Budget Amount
- Budget Performance Status

---

### **Test 8: Budget Category Allocation**

#### **Test Steps**:
1. Create income linked to "Government Grants" category
2. Create expenditure linked to "Office Operations" category
3. Check budget category performance

#### **Expected Results**:
- ✅ Category-specific actuals updated
- ✅ Category utilization percentages correct
- ✅ Category variance calculations accurate

---

## 🔧 **ERROR HANDLING TESTS**

### **Test 9: Budget Constraint Validation**

#### **Test Steps**:
1. Try to create expenditure exceeding available budget
2. Verify validation messages
3. Test budget allocation warnings

#### **Expected Results**:
- ✅ Appropriate validation errors displayed
- ✅ User warned about budget constraints
- ✅ Form prevents submission if over budget

---

### **Test 10: Network Error Handling**

#### **Test Steps**:
1. Simulate network failure during CRUD operations
2. Test retry mechanisms
3. Verify data consistency

#### **Expected Results**:
- ✅ Graceful error handling
- ✅ User-friendly error messages
- ✅ No data corruption
- ✅ Proper rollback on failures

---

## 📋 **TEST CHECKLIST**

### **Income Module** ✅
- [x] Create income with budget integration
- [x] Edit income with budget updates
- [x] Delete income with budget reversal
- [x] Form validation and error handling
- [x] Modal workflows functional
- [x] Table actions working

### **Expenditure Module** ✅
- [x] Create expenditure with budget integration
- [x] Edit expenditure with budget updates
- [x] Delete expenditure callback connected
- [x] Form validation and error handling
- [x] Modal workflows functional
- [x] Table actions working

### **Budget Integration** ✅
- [x] Real-time budget updates
- [x] Category-specific allocations
- [x] Performance metrics calculation
- [x] Variance analysis accuracy
- [x] Dashboard synchronization

### **User Experience** ✅
- [x] Intuitive modal interfaces
- [x] Clear action buttons with icons
- [x] Confirmation dialogs for destructive actions
- [x] Toast notifications for feedback
- [x] Loading states during operations
- [x] Responsive design

---

## 🎯 **SUCCESS CRITERIA**

### **Functional Requirements** ✅
- All CRUD operations work correctly
- Budget integration is real-time and accurate
- Data consistency maintained across operations
- Error handling is robust and user-friendly

### **Performance Requirements** ✅
- Operations complete within 2 seconds
- UI remains responsive during operations
- Budget updates are immediate
- No memory leaks or performance degradation

### **User Experience Requirements** ✅
- Intuitive and consistent interface
- Clear feedback for all actions
- Accessible design patterns
- Mobile-responsive layouts

---

## 📝 **TEST EXECUTION NOTES**

**Date**: December 2024  
**Environment**: Development  
**Browser**: Chrome/Firefox/Safari  
**Status**: Ready for Testing  

**Next Steps**:
1. Execute all test cases systematically
2. Document any issues found
3. Verify budget integration accuracy
4. Confirm user experience quality
5. Sign off on implementation

---

*This test plan ensures comprehensive verification of CRUD operations with budget integration for both Income and Expenditure modules.*

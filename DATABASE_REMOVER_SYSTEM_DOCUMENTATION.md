# Database Data Remover System Documentation

## Overview

The Database Data Remover System is a comprehensive super-admin tool designed for the TCM Enterprise Suite that provides secure, auditable, and controlled access to database records across all system modules. This system allows super-admin users to view, manage, and remove data from any database collection with full audit trails and compliance features.

## Features

### 🔐 Security & Access Control
- **Super Admin Only**: Restricted to users with `UserRole.SUPER_ADMIN`
- **Role-based Model Access**: Each model has specific role requirements
- **Audit Trail**: Every deletion creates comprehensive audit records
- **Confirmation Requirements**: Multiple confirmation steps for safety

### 📊 Database Management
- **Model Discovery**: Automatically discovers and categorizes all database models
- **Real-time Counts**: Live record counts for each collection
- **Categorized View**: Models grouped by system area (HR, Payroll, Accounting, etc.)
- **Dependency Tracking**: Shows model relationships and dependencies

### 🗑️ Deletion Capabilities
- **Single Item Deletion**: Select and delete individual records
- **Bulk Deletion**: Select multiple items for batch deletion
- **Delete All**: Complete collection clearing with extreme safety measures
- **Reason Tracking**: Mandatory deletion reasons (minimum 10 characters)

### 📋 Data Viewing
- **Paginated Display**: Efficient viewing of large datasets
- **Expandable Rows**: Detailed view of complete record data
- **Field Selection**: Smart field display prioritizing important columns
- **Search & Filter**: Easy data navigation

### 🔍 Audit & Compliance
- **Government Compliance**: 7-year retention period for audit records
- **Complete Audit Trail**: User info, timestamps, IP addresses, reasons
- **Recovery Support**: 90-day recovery window for deleted items
- **Compliance Flags**: Automatic flagging for high-value transactions

## System Architecture

### Backend Services

#### DatabaseRemoverService (`lib/services/admin/database-remover-service.ts`)
- **Singleton Pattern**: Ensures consistent model registry
- **Model Registration**: Automatic discovery and registration of database models
- **Permission Checking**: Role-based access control for each model
- **Deletion Operations**: Safe bulk and individual deletion with audit trails

#### API Routes
- `GET /api/admin/database-remover/models` - List all models with counts
- `PUT /api/admin/database-remover/models` - Refresh model counts
- `GET /api/admin/database-remover/models/[modelName]` - Get model data
- `DELETE /api/admin/database-remover/models/[modelName]` - Delete model data

### Frontend Components

#### Main Dashboard (`components/admin/database-remover/database-remover-dashboard.tsx`)
- Summary cards showing total models, records, and critical models
- Real-time data refresh capabilities
- Responsive layout with model list and data viewer

#### Models Accordion (`components/admin/database-remover/database-models-accordion.tsx`)
- Categorized model display (System, HR, Payroll, Accounting, Other)
- Expandable categories with model counts and danger levels
- Model selection and navigation

#### Data Viewer (`components/admin/database-remover/model-data-viewer.tsx`)
- Paginated table display with checkbox selection
- Expandable row details for complete record viewing
- Bulk selection and deletion controls

#### Deletion Dialogs
- **Bulk Delete Dialog**: Confirmation and reason collection for selected items
- **Delete All Dialog**: Extreme safety measures for complete collection clearing

### State Management

#### Zustand Store (`lib/stores/database-remover-store.ts`)
- Centralized state management for all database remover operations
- Persistent storage for user preferences (expanded categories, page size)
- API integration for data fetching and deletion operations

## Model Categories

### System & Security
- **User**: System user accounts (CRITICAL)
- **AuditLog**: System audit trails (CRITICAL)
- **DeletedItems**: Deleted items archive (CRITICAL)

### Human Resources
- **Employee**: Employee records (HIGH)
- **Department**: Organizational structure (HIGH)
- **Role**: System roles and permissions (MEDIUM)

### Payroll & Compensation
- **PayrollRun**: Payroll processing runs (CRITICAL)
- **PayrollRecord**: Individual payroll calculations (CRITICAL)
- **EmployeeSalary**: Salary structures (HIGH)

### Accounting & Finance
- **Income**: Income transactions (HIGH)
- **Expense**: Expense transactions (HIGH)
- **Budget**: Budget records (HIGH)

## Safety Features

### Danger Levels
- **CRITICAL**: Core system data that could break the application
- **HIGH**: Important business data with significant impact
- **MEDIUM**: Standard operational data
- **LOW**: Non-critical reference data

### Confirmation Requirements
1. **Deletion Reason**: Minimum 10 characters explaining the deletion
2. **Confirmation Checkbox**: Explicit acknowledgment of permanent action
3. **Model Name Confirmation**: For delete-all operations, must type model name
4. **Multiple Warnings**: Progressive warning system for dangerous operations

### Audit Trail Creation
Every deletion automatically creates:
- Complete original record backup
- User information (ID, name, email, role)
- Timestamp and reason
- IP address and session information
- Compliance flags for high-value items

## Usage Instructions

### Accessing the System
1. Login as a user with `SUPER_ADMIN` role
2. Navigate to Dashboard → Admin → Database Remover
3. System will display all available models with current counts

### Viewing Model Data
1. Select a category to expand (System, HR, Payroll, etc.)
2. Click "View Data" on any model to load its records
3. Use pagination controls to navigate through large datasets
4. Click the eye icon to expand individual records for detailed view

### Deleting Records

#### Single/Bulk Deletion
1. Select records using checkboxes
2. Click "Delete Selected" button
3. Provide detailed deletion reason
4. Confirm the operation
5. Review deletion results

#### Delete All Data
1. Click "Delete All" button on any model
2. Provide detailed justification
3. Type the exact model name to confirm
4. Acknowledge the permanent nature of the action
5. Confirm the operation

### Monitoring Operations
- All deletions are logged in the Auditors module
- Audit records include complete original data
- Recovery is possible within 90 days through audit system
- Compliance reports available for government audits

## Security Considerations

### Access Control
- Only `SUPER_ADMIN` users can access the system
- Each model has specific role requirements
- All operations are logged with user identification

### Data Protection
- Original data is preserved in audit records
- 7-year retention period for compliance
- Encrypted storage of sensitive information
- IP address and session tracking

### Recovery Options
- 90-day recovery window for most deletions
- Complete audit trail for reconstruction
- Backup recommendations before major operations

## Technical Implementation

### Model Registration
Models are automatically registered with:
```typescript
{
  name: 'ModelName',
  displayName: 'Human Readable Name',
  collection: 'database_collection',
  model: MongooseModel,
  allowedRoles: [UserRole.SUPER_ADMIN],
  dangerLevel: 'critical' | 'high' | 'medium' | 'low',
  description: 'Model description',
  dependencies: ['RelatedModel1', 'RelatedModel2']
}
```

### Deletion Process
1. Permission validation
2. Record retrieval and validation
3. Audit record creation
4. Actual deletion from database
5. Result reporting and logging

### Error Handling
- Comprehensive error catching and reporting
- Graceful degradation for missing models
- User-friendly error messages
- Detailed logging for debugging

## Maintenance

### Adding New Models
1. Import the model in `database-remover-service.ts`
2. Register the model in `initializeModelRegistry()`
3. Set appropriate permissions and danger level
4. Update categorization in API route if needed

### Monitoring
- Regular review of audit logs
- Performance monitoring for large deletions
- Storage usage tracking for audit records
- User access pattern analysis

## Compliance & Legal

### Government Requirements
- 7-year audit trail retention
- Complete transaction logging
- User accountability tracking
- Recovery capability maintenance

### Best Practices
- Regular backup verification
- Audit log review procedures
- User training requirements
- Emergency recovery procedures

## Troubleshooting

### Common Issues
1. **Model not appearing**: Check model import and registration
2. **Permission denied**: Verify user role and model permissions
3. **Deletion failed**: Check for foreign key constraints
4. **Audit creation failed**: Verify DeletedItems model availability

### Support
- Check application logs for detailed error information
- Review audit trail for operation history
- Contact system administrator for access issues
- Refer to technical documentation for implementation details

---

**⚠️ WARNING**: This system provides direct access to database records. Use with extreme caution and ensure proper backups are maintained. All operations are logged and monitored for compliance purposes.

// lib/backend/auth/role-permissions.ts
import { UserRole } from '@/types/user-roles';

/**
 * Centralized role permission definitions for consistent access control
 */

// All available user roles
export const ALL_USER_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.HR_SPECIALIST,
  UserRole.DEPARTMENT_HEAD,
  UserRole.TEAM_LEADER,
  UserRole.RECRUITER,
  UserRole.EMPLOYEE,
  UserRole.CONTRACTOR,
  UserRole.INTERN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.ACCOUNTANT,
  UserRole.PAYROLL_SPECIALIST
];

// Administrative roles with full system access
export const ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN
];

// HR management roles
export const HR_MANAGEMENT_ROLES = [
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

// HR operational roles
export const HR_OPERATIONAL_ROLES = [
  ...HR_MANAGEMENT_ROLES,
  UserRole.HR_SPECIALIST,
  UserRole.RECRUITER
];

// All HR roles
export const HR_ROLES = [
  ...ADMIN_ROLES,
  ...HR_OPERATIONAL_ROLES
];

// Management roles (can manage teams/departments)
export const MANAGEMENT_ROLES = [
  ...ADMIN_ROLES,
  ...HR_MANAGEMENT_ROLES,
  UserRole.DEPARTMENT_HEAD,
  UserRole.TEAM_LEADER
];

// Finance roles
export const FINANCE_ROLES = [
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.ACCOUNTANT,
  UserRole.PAYROLL_SPECIALIST
];

// Staff roles (non-management)
export const STAFF_ROLES = [
  UserRole.EMPLOYEE,
  UserRole.CONTRACTOR,
  UserRole.INTERN
];

/**
 * Permission sets for different operations
 */

// Roles that can view employee data
export const EMPLOYEE_VIEWER_ROLES = ALL_USER_ROLES;

// Roles that can create/edit employees
export const EMPLOYEE_EDITOR_ROLES = [
  ...ADMIN_ROLES,
  ...HR_OPERATIONAL_ROLES
];

// Roles that can delete employees
export const EMPLOYEE_DELETE_ROLES = [
  ...ADMIN_ROLES,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

// Roles that can view department data
export const DEPARTMENT_VIEWER_ROLES = ALL_USER_ROLES;

// Roles that can create/edit departments
export const DEPARTMENT_EDITOR_ROLES = [
  ...ADMIN_ROLES,
  ...HR_MANAGEMENT_ROLES
];

// Roles that can delete departments
export const DEPARTMENT_DELETE_ROLES = [
  ...ADMIN_ROLES,
  ...HR_MANAGEMENT_ROLES
];

// Roles that can manage user accounts
export const USER_MANAGEMENT_ROLES = [
  ...ADMIN_ROLES,
  ...HR_MANAGEMENT_ROLES
];

// Roles that can view payroll data
export const PAYROLL_VIEWER_ROLES = [
  ...ADMIN_ROLES,
  ...HR_ROLES,
  ...FINANCE_ROLES,
  UserRole.DEPARTMENT_HEAD // Department heads can view their team's payroll
];

// Roles that can process payroll
export const PAYROLL_PROCESSOR_ROLES = [
  ...ADMIN_ROLES,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.PAYROLL_SPECIALIST
];

// Roles that can view financial reports
export const FINANCIAL_REPORT_VIEWER_ROLES = [
  ...ADMIN_ROLES,
  ...FINANCE_ROLES,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

// Roles that can export data
export const DATA_EXPORT_ROLES = [
  ...ADMIN_ROLES,
  ...HR_OPERATIONAL_ROLES,
  ...FINANCE_ROLES
];

/**
 * Helper functions for permission checking
 */

/**
 * Check if a role has administrative privileges
 */
export function isAdminRole(role: UserRole): boolean {
  return ADMIN_ROLES.includes(role);
}

/**
 * Check if a role is in HR department
 */
export function isHRRole(role: UserRole): boolean {
  return HR_ROLES.includes(role);
}

/**
 * Check if a role is in Finance department
 */
export function isFinanceRole(role: UserRole): boolean {
  return FINANCE_ROLES.includes(role);
}

/**
 * Check if a role has management responsibilities
 */
export function isManagementRole(role: UserRole): boolean {
  return MANAGEMENT_ROLES.includes(role);
}

/**
 * Get permission level for a role (higher number = more permissions)
 */
export function getPermissionLevel(role: UserRole): number {
  if (role === UserRole.SUPER_ADMIN) return 100;
  if (role === UserRole.SYSTEM_ADMIN) return 90;
  if (role === UserRole.HR_DIRECTOR) return 80;
  if (role === UserRole.FINANCE_DIRECTOR) return 75;
  if (role === UserRole.HR_MANAGER) return 70;
  if (role === UserRole.FINANCE_MANAGER) return 65;
  if (role === UserRole.DEPARTMENT_HEAD) return 60;
  if (role === UserRole.TEAM_LEADER) return 50;
  if (role === UserRole.HR_SPECIALIST) return 45;
  if (role === UserRole.PAYROLL_SPECIALIST) return 40;
  if (role === UserRole.ACCOUNTANT) return 35;
  if (role === UserRole.RECRUITER) return 30;
  if (role === UserRole.EMPLOYEE) return 20;
  if (role === UserRole.CONTRACTOR) return 15;
  if (role === UserRole.INTERN) return 10;
  return 0;
}

/**
 * Check if user can access resource based on role hierarchy
 */
export function canAccessResource(userRole: UserRole, requiredLevel: number): boolean {
  return getPermissionLevel(userRole) >= requiredLevel;
}

// lib/backend/services/error-service.ts
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { NextResponse } from 'next/server';

/**
 * Error types for categorization
 */
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  CONFLICT = 'CONFLICT',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  DATABASE = 'DATABASE',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  SYSTEM = 'SYSTEM',
  NETWORK = 'NETWORK',
  TIMEOUT = 'TIMEOUT'
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Error context interface
 */
export interface ErrorContext {
  userId?: string;
  userRole?: string;
  endpoint?: string;
  method?: string;
  requestId?: string;
  timestamp?: string;
  userAgent?: string;
  ip?: string;
  additionalData?: Record<string, any>;
}

/**
 * Structured error interface
 */
export interface StructuredError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  code: string;
  message: string;
  userMessage: string;
  details?: string;
  context: ErrorContext;
  suggestions?: string[];
  actions?: ErrorAction[];
  timestamp: string;
  stack?: string;
}

/**
 * Error action interface
 */
export interface ErrorAction {
  label: string;
  action: string;
  type: 'button' | 'link' | 'retry';
  variant?: 'primary' | 'secondary' | 'destructive';
  url?: string;
  data?: Record<string, any>;
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  success: false;
  error: StructuredError;
  httpStatus: number;
}

/**
 * Enterprise Error Service
 */
export class ErrorService {
  private static instance: ErrorService;

  private constructor() {}

  public static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `ERR_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Create structured error
   */
  public createError(
    type: ErrorType,
    code: string,
    message: string,
    userMessage: string,
    context: ErrorContext,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    details?: string,
    suggestions?: string[],
    actions?: ErrorAction[]
  ): StructuredError {
    const errorId = this.generateErrorId();

    const structuredError: StructuredError = {
      id: errorId,
      type,
      severity,
      code,
      message,
      userMessage,
      details,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        requestId: context.requestId || errorId
      },
      suggestions,
      actions,
      timestamp: new Date().toISOString()
    };

    // Log the error
    this.logError(structuredError);

    return structuredError;
  }

  /**
   * Log structured error
   */
  private logError(error: StructuredError): void {
    const logData = {
      errorId: error.id,
      type: error.type,
      severity: error.severity,
      code: error.code,
      message: error.message,
      context: error.context
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error(`CRITICAL ERROR: ${error.message}`, LogCategory.API, logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error(`HIGH SEVERITY: ${error.message}`, LogCategory.API, logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(`MEDIUM SEVERITY: ${error.message}`, LogCategory.API, logData);
        break;
      case ErrorSeverity.LOW:
        logger.info(`LOW SEVERITY: ${error.message}`, LogCategory.API, logData);
        break;
    }
  }

  /**
   * Create API error response
   */
  public createApiResponse(
    type: ErrorType,
    code: string,
    message: string,
    userMessage: string,
    context: ErrorContext,
    httpStatus: number = 500,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    details?: string,
    suggestions?: string[],
    actions?: ErrorAction[]
  ): NextResponse<ErrorResponse> {
    const error = this.createError(
      type,
      code,
      message,
      userMessage,
      context,
      severity,
      details,
      suggestions,
      actions
    );

    const errorResponse: ErrorResponse = {
      success: false,
      error,
      httpStatus
    };

    return NextResponse.json(errorResponse, { status: httpStatus });
  }

  /**
   * Handle common payroll errors
   */
  public handlePayrollError(
    errorType: 'NO_RECORDS' | 'PROCESSING_FAILED' | 'CALCULATION_ERROR' | 'STATUS_INVALID' |
              'BULK_IMPORT_FAILED' | 'VALIDATION_FAILED' | 'UNAUTHORIZED_ACCESS' |
              'RESOURCE_NOT_FOUND' | 'DUPLICATE_ENTRY' | 'EXPORT_FAILED' |
              'TEMPLATE_ERROR' | 'SALARY_CALCULATION_ERROR' | 'PAYSLIP_GENERATION_ERROR',
    context: ErrorContext,
    additionalData?: Record<string, any>
  ): NextResponse<ErrorResponse> {
    switch (errorType) {
      case 'NO_RECORDS':
        return this.createApiResponse(
          ErrorType.NOT_FOUND,
          'PAYROLL_NO_RECORDS',
          'No payroll records found for this payroll run',
          'This payroll run has no individual employee records. This usually happens when processing failed or was incomplete.',
          { ...context, additionalData },
          404,
          ErrorSeverity.HIGH,
          'The payroll run shows as processed but no individual employee payroll records were created in the database.',
          [
            'Try reprocessing the payroll run to create missing records',
            'Check if the payroll processing completed successfully',
            'Verify that employees were assigned to this payroll run'
          ],
          [
            {
              label: 'Debug Records',
              action: 'debug',
              type: 'button',
              variant: 'secondary'
            },
            {
              label: 'Reprocess Payroll',
              action: 'reprocess',
              type: 'button',
              variant: 'primary'
            },
            {
              label: 'View Details',
              action: 'view-details',
              type: 'link',
              variant: 'secondary',
              url: '/dashboard/error-details'
            }
          ]
        );

      case 'PROCESSING_FAILED':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_PROCESSING_FAILED',
          'Payroll processing failed',
          'The payroll processing encountered an error and could not complete successfully.',
          { ...context, additionalData },
          500,
          ErrorSeverity.HIGH,
          'An error occurred during payroll processing that prevented completion.',
          [
            'Check employee salary configurations',
            'Verify department assignments',
            'Ensure all required data is present'
          ],
          [
            {
              label: 'Retry Processing',
              action: 'retry',
              type: 'button',
              variant: 'primary'
            },
            {
              label: 'Check Logs',
              action: 'logs',
              type: 'button',
              variant: 'secondary'
            }
          ]
        );

      case 'CALCULATION_ERROR':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_CALCULATION_ERROR',
          'Payroll calculation error',
          'An error occurred while calculating payroll totals or individual employee salaries.',
          { ...context, additionalData },
          500,
          ErrorSeverity.MEDIUM,
          'The payroll calculation engine encountered an error.',
          [
            'Verify salary structures are properly configured',
            'Check tax and deduction settings',
            'Ensure all calculation parameters are valid'
          ],
          [
            {
              label: 'Recalculate',
              action: 'recalculate',
              type: 'button',
              variant: 'primary'
            }
          ]
        );

      case 'STATUS_INVALID':
        return this.createApiResponse(
          ErrorType.VALIDATION,
          'PAYROLL_STATUS_INVALID',
          'Invalid payroll run status',
          'The requested operation cannot be performed on a payroll run with this status.',
          { ...context, additionalData },
          400,
          ErrorSeverity.MEDIUM,
          'The payroll run status does not allow this operation.',
          [
            'Check the current payroll run status',
            'Ensure the operation is valid for this status',
            'Review payroll workflow requirements'
          ]
        );

      case 'BULK_IMPORT_FAILED':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_BULK_IMPORT_FAILED',
          'Bulk import operation failed',
          'The bulk import operation could not be completed successfully.',
          { ...context, additionalData },
          400,
          ErrorSeverity.MEDIUM,
          'One or more records in the bulk import failed validation or processing.',
          [
            'Check the import file format and data',
            'Verify all required fields are present',
            'Ensure data meets validation requirements'
          ],
          [
            {
              label: 'Download Error Report',
              action: 'download-errors',
              type: 'button',
              variant: 'secondary'
            },
            {
              label: 'Retry Import',
              action: 'retry',
              type: 'button',
              variant: 'primary'
            }
          ]
        );

      case 'VALIDATION_FAILED':
        return this.createApiResponse(
          ErrorType.VALIDATION,
          'PAYROLL_VALIDATION_FAILED',
          'Data validation failed',
          'The provided data does not meet the required validation criteria.',
          { ...context, additionalData },
          400,
          ErrorSeverity.MEDIUM,
          'One or more fields contain invalid data.',
          [
            'Check all required fields are filled',
            'Verify data formats match requirements',
            'Ensure numeric values are within valid ranges'
          ]
        );

      case 'UNAUTHORIZED_ACCESS':
        return this.createApiResponse(
          ErrorType.UNAUTHORIZED,
          'PAYROLL_UNAUTHORIZED_ACCESS',
          'Unauthorized access to payroll resource',
          'You do not have permission to access this payroll resource.',
          { ...context, additionalData },
          403,
          ErrorSeverity.HIGH,
          'User lacks required permissions for this payroll operation.',
          [
            'Contact your administrator for access',
            'Verify your role permissions',
            'Check if you are logged in with the correct account'
          ]
        );

      case 'RESOURCE_NOT_FOUND':
        return this.createApiResponse(
          ErrorType.NOT_FOUND,
          'PAYROLL_RESOURCE_NOT_FOUND',
          'Payroll resource not found',
          'The requested payroll resource could not be found.',
          { ...context, additionalData },
          404,
          ErrorSeverity.MEDIUM,
          'The specified payroll resource does not exist or has been deleted.',
          [
            'Verify the resource ID is correct',
            'Check if the resource has been deleted',
            'Refresh the page and try again'
          ]
        );

      case 'DUPLICATE_ENTRY':
        return this.createApiResponse(
          ErrorType.CONFLICT,
          'PAYROLL_DUPLICATE_ENTRY',
          'Duplicate entry detected',
          'A record with this information already exists.',
          { ...context, additionalData },
          409,
          ErrorSeverity.MEDIUM,
          'The system detected a duplicate entry that conflicts with existing data.',
          [
            'Check if the record already exists',
            'Use update instead of create if modifying existing data',
            'Verify unique identifiers are not duplicated'
          ]
        );

      case 'EXPORT_FAILED':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_EXPORT_FAILED',
          'Export operation failed',
          'The data export operation could not be completed.',
          { ...context, additionalData },
          500,
          ErrorSeverity.MEDIUM,
          'An error occurred while generating the export file.',
          [
            'Try reducing the amount of data to export',
            'Check if all required data is available',
            'Retry the export operation'
          ],
          [
            {
              label: 'Retry Export',
              action: 'retry',
              type: 'button',
              variant: 'primary'
            }
          ]
        );

      case 'TEMPLATE_ERROR':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_TEMPLATE_ERROR',
          'Template generation error',
          'An error occurred while generating the template file.',
          { ...context, additionalData },
          500,
          ErrorSeverity.LOW,
          'The template generation process encountered an error.',
          [
            'Try downloading the template again',
            'Contact support if the problem persists'
          ]
        );

      case 'SALARY_CALCULATION_ERROR':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_SALARY_CALCULATION_ERROR',
          'Salary calculation error',
          'An error occurred while calculating salary amounts.',
          { ...context, additionalData },
          500,
          ErrorSeverity.HIGH,
          'The salary calculation engine encountered an error.',
          [
            'Verify salary structure configuration',
            'Check tax and deduction settings',
            'Ensure all calculation parameters are valid'
          ],
          [
            {
              label: 'Recalculate',
              action: 'recalculate',
              type: 'button',
              variant: 'primary'
            }
          ]
        );

      case 'PAYSLIP_GENERATION_ERROR':
        return this.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_PAYSLIP_GENERATION_ERROR',
          'Payslip generation error',
          'An error occurred while generating payslips.',
          { ...context, additionalData },
          500,
          ErrorSeverity.MEDIUM,
          'The payslip generation process encountered an error.',
          [
            'Verify payroll data is complete',
            'Check employee information is up to date',
            'Ensure payroll run is properly processed'
          ],
          [
            {
              label: 'Regenerate Payslips',
              action: 'regenerate',
              type: 'button',
              variant: 'primary'
            }
          ]
        );

      default:
        return this.createApiResponse(
          ErrorType.SYSTEM,
          'PAYROLL_UNKNOWN_ERROR',
          'Unknown payroll error',
          'An unexpected error occurred in the payroll system.',
          { ...context, additionalData },
          500,
          ErrorSeverity.HIGH
        );
    }
  }
}

// Export singleton instance
export const errorService = ErrorService.getInstance();

import type {
  ConversionOptions,
  ConversionError,
  ConversionWarning,
  EmployeeConversionData
} from '@/types/document-conversion';
import Department from '@/models/Department';
import { connectToDatabase } from '@/lib/backend/database';

export class EmployeeConversionService {
  private static readonly FIELD_MAPPINGS = {
    // Common variations of field names that might appear in source data
    firstName: [
      'firstName', 'first_name', 'First Name', 'fname', 'given_name', 'Given Name',
      'FIRST NAME', 'FIRST_NAME', 'FirstName', 'first name', 'name', 'Name',
      'Employee Name', 'employee_name', 'Full Name', 'full_name', 'FULL NAME'
    ],
    lastName: [
      'lastName', 'last_name', 'Last Name', 'lname', 'surname', 'family_name',
      'LAST NAME', 'LAST_NAME', 'LastName', 'last name', 'Surname', 'SURNAME',
      'Family Name', 'FAMILY NAME', 'family name'
    ],
    email: [
      'email', 'Email', 'EMAIL', 'email_address', 'Email Address', 'EMAIL ADDRESS',
      'e-mail', 'E-mail', 'E-MAIL', 'emailaddress', 'EmailAddress', 'EMAILADDRESS',
      'mail', 'Mail', 'MAIL', 'contact_email', 'Contact Email'
    ],
    phone: [
      'phone', 'Phone', 'PHONE', 'phone_number', 'Phone Number', 'PHONE NUMBER',
      'mobile', 'Mobile', 'MOBILE', 'contact', 'Contact', 'CONTACT',
      'cell', 'Cell', 'CELL', 'telephone', 'Telephone', 'TELEPHONE',
      'contact_number', 'Contact Number', 'CONTACT NUMBER'
    ],
    dateOfBirth: [
      'dateOfBirth', 'date_of_birth', 'Date of Birth', 'DATE OF BIRTH',
      'DOB', 'dob', 'birth_date', 'Birth Date', 'BIRTH DATE',
      'birthdate', 'BirthDate', 'BIRTHDATE', 'born', 'Born', 'BORN'
    ],
    gender: [
      'gender', 'Gender', 'GENDER', 'sex', 'Sex', 'SEX',
      'male/female', 'Male/Female', 'MALE/FEMALE'
    ],
    maritalStatus: [
      'Marital Status', 'maritalStatus', 'marital_status', 'MARITAL STATUS',
      'marital', 'Marital', 'MARITAL', 'marriage_status', 'Marriage Status'
    ],
    numberOfChildren: [
      'numberOfChildren', 'number_of_children', 'Number of Children', 'NUMBER OF CHILDREN',
      'children', 'Children', 'CHILDREN', 'kids', 'Kids', 'KIDS',
      'dependents', 'Dependents', 'DEPENDENTS'
    ],
    department: [
      'department', 'Department', 'DEPARTMENT', 'dept', 'Dept', 'DEPT',
      'division', 'Division', 'DIVISION', 'section', 'Section', 'SECTION',
      'unit', 'Unit', 'UNIT'
    ],
    position: [
      'position', 'Position', 'POSITION', 'job_title', 'Job Title', 'JOB TITLE',
      'title', 'Title', 'TITLE', 'role', 'Role', 'ROLE',
      'designation', 'Designation', 'DESIGNATION', 'post', 'Post', 'POST',
      'job', 'Job', 'JOB'
    ],
    employmentType: [
      'employmentType', 'employment_type', 'Employment Type', 'EMPLOYMENT TYPE',
      'type', 'Type', 'TYPE', 'contract_type', 'Contract Type', 'CONTRACT TYPE',
      'employment_category', 'Employment Category'
    ],
    employmentStatus: [
      'Status', 'employmentStatus', 'employment_status', 'Employment Status', 'EMPLOYMENT STATUS',
      'work_status', 'Work Status', 'WORK STATUS',
      'employee_status', 'Employee Status', 'EMPLOYEE STATUS'
    ],
    hireDate: [
      'hireDate', 'hire_date', 'Hire Date', 'HIRE DATE',
      'start_date', 'Start Date', 'START DATE', 'employment_date', 'Employment Date',
      'date_hired', 'Date Hired', 'DATE HIRED', 'joining_date', 'Joining Date',
      'JOINING DATE', 'commencement_date', 'Commencement Date'
    ],
    salary: [
      'salary', 'Salary', 'SALARY', 'basic_salary', 'Basic Salary', 'BASIC SALARY',
      'pay', 'Pay', 'PAY', 'wage', 'Wage', 'WAGE',
      'monthly_salary', 'Monthly Salary', 'MONTHLY SALARY',
      'gross_salary', 'Gross Salary', 'GROSS SALARY',
      'remuneration', 'Remuneration', 'REMUNERATION'
    ],
    address: [
      'address', 'Address', 'ADDRESS', 'street_address', 'Street Address', 'STREET ADDRESS',
      'location', 'Location', 'LOCATION', 'residence', 'Residence', 'RESIDENCE',
      'home_address', 'Home Address', 'HOME ADDRESS'
    ],
    city: [
      'city', 'City', 'CITY', 'town', 'Town', 'TOWN',
      'municipality', 'Municipality', 'MUNICIPALITY'
    ],
    state: [
      'state', 'State', 'STATE', 'region', 'Region', 'REGION',
      'province', 'Province', 'PROVINCE'
    ],
    country: [
      'country', 'Country', 'COUNTRY', 'nation', 'Nation', 'NATION',
      'nationality', 'Nationality', 'NATIONALITY'
    ],
    village: [
      'village', 'Village', 'VILLAGE', 'area', 'Area', 'AREA',
      'locality', 'Locality', 'LOCALITY'
    ],
    traditionalAuthority: [
      'traditionalAuthority', 'traditional_authority', 'Traditional Authority', 'TRADITIONAL AUTHORITY',
      'TA', 'ta', 'T.A.', 'T.A', 'traditional authority'
    ],
    district: [
      'district', 'District', 'DISTRICT'
    ],
    nationalId: [
      'nationalId', 'national_id', 'National ID', 'NATIONAL ID',
      'id_number', 'ID Number', 'ID NUMBER', 'passport', 'Passport', 'PASSPORT',
      'identification', 'Identification', 'IDENTIFICATION'
    ],
    nextOfKinName: [
      'nextOfKinName', 'next_of_kin_name', 'Next of Kin Name', 'NEXT OF KIN NAME',
      'kin_name', 'Kin Name', 'KIN NAME', 'emergency_contact', 'Emergency Contact',
      'EMERGENCY CONTACT', 'next of kin', 'Next of Kin', 'NEXT OF KIN'
    ],
    nextOfKinRelationship: [
      'nextOfKinRelationship', 'next_of_kin_relationship', 'Next of Kin Relationship',
      'NEXT OF KIN RELATIONSHIP', 'kin_relationship', 'Kin Relationship', 'KIN RELATIONSHIP',
      'relationship', 'Relationship', 'RELATIONSHIP'
    ],
    nextOfKinPhone: [
      'nextOfKinPhone', 'next_of_kin_phone', 'Next of Kin Phone', 'NEXT OF KIN PHONE',
      'kin_phone', 'Kin Phone', 'KIN PHONE', 'emergency_phone', 'Emergency Phone',
      'EMERGENCY PHONE', 'next of kin contact', 'Next of Kin Contact'
    ],
    nextOfKinAddress: [
      'nextOfKinAddress', 'next_of_kin_address', 'Next of Kin Address', 'NEXT OF KIN ADDRESS',
      'kin_address', 'Kin Address', 'KIN ADDRESS', 'emergency_address', 'Emergency Address'
    ],
    emergencyContactName: [
      'emergencyContactName', 'emergency_contact_name', 'Emergency Contact Name',
      'EMERGENCY CONTACT NAME', 'emergency_name', 'Emergency Name', 'EMERGENCY NAME'
    ],
    emergencyContactPhone: [
      'emergencyContactPhone', 'emergency_contact_phone', 'Emergency Contact Phone',
      'EMERGENCY CONTACT PHONE', 'emergency_phone', 'Emergency Phone', 'EMERGENCY PHONE'
    ],
    emergencyContactRelationship: [
      'emergencyContactRelationship', 'emergency_contact_relationship',
      'Emergency Contact Relationship', 'EMERGENCY CONTACT RELATIONSHIP',
      'emergency_relationship', 'Emergency Relationship', 'EMERGENCY RELATIONSHIP'
    ],
    notes: [
      'notes', 'Notes', 'NOTES', 'comments', 'Comments', 'COMMENTS',
      'remarks', 'Remarks', 'REMARKS', 'additional_info', 'Additional Info',
      'ADDITIONAL INFO', 'description', 'Description', 'DESCRIPTION'
    ]
  };

  private static readonly EMPLOYMENT_TYPE_MAPPINGS = {
    'full-time': ['full-time', 'fulltime', 'full time', 'permanent', 'regular'],
    'part-time': ['part-time', 'parttime', 'part time'],
    'contract': ['contract', 'contractor', 'contractual'],
    'intern': ['intern', 'internship', 'trainee'],
    'temporary': ['temporary', 'temp', 'casual'],
    'volunteer': ['volunteer', 'voluntary']
  };

  private static readonly EMPLOYMENT_STATUS_MAPPINGS = {
    'active': ['active', 'employed', 'working', 'current'],
    'inactive': ['inactive', 'not active'],
    'on-leave': ['on-leave', 'on leave', 'leave', 'maternity', 'sick leave'],
    'terminated': ['terminated', 'fired', 'dismissed', 'ended', 'left', 'resigned']
  };

  private static readonly MARITAL_STATUS_MAPPINGS = {
    'single': ['single', 'unmarried', 'never married'],
    'married': ['married', 'wed'],
    'divorced': ['divorced', 'separated'],
    'widowed': ['widowed', 'widow', 'widower']
  };

  static async convertData(
    rawData: Record<string, unknown>[],
    options: ConversionOptions
  ): Promise<{
    convertedData: EmployeeConversionData[];
    errors: ConversionError[];
    warnings: ConversionWarning[];
  }> {
    const convertedData: EmployeeConversionData[] = [];
    const errors: ConversionError[] = [];
    const warnings: ConversionWarning[] = [];

    // Connect to database for department validation
    await connectToDatabase();

    // Fetch all departments for validation
    const validDepartments = await this.fetchValidDepartments();
    console.log('Valid departments found:', validDepartments.map(d => d.name));

    // Create field mapping from source columns to target fields
    const sourceColumns = Object.keys(rawData[0] || {});
    const fieldMapping = this.createFieldMapping(sourceColumns);

    // Check if we have any meaningful field mappings
    const mappedFields = Object.keys(fieldMapping);
    const requiredFields = ['firstName', 'lastName', 'email', 'position', 'hireDate'];
    const mappedRequiredFields = requiredFields.filter(field => fieldMapping[field]);

    if (mappedRequiredFields.length === 0) {
      // No required fields mapped - likely a header issue
      errors.push({
        row: 1,
        column: 'general',
        message: `No column headers could be mapped to required fields. Expected headers like: ${requiredFields.join(', ')}. Found headers: ${sourceColumns.join(', ')}`,
        severity: 'error',
        suggestedFix: 'Please ensure your Excel file has proper column headers in the first row, such as "First Name", "Last Name", "Email", "Position", "Hire Date"'
      });

      return {
        convertedData: [],
        errors,
        warnings
      };
    }

    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i];
      const rowNumber = i + (options.includeHeaders ? 2 : 1); // Account for header row

      try {
        console.log(`Processing row ${rowNumber}:`, row);
        const convertedRow = this.convertRow(row, fieldMapping, rowNumber, warnings);
        console.log(`Converted row ${rowNumber}:`, convertedRow);

        // Validate required fields
        const validationErrors = this.validateRequiredFields(convertedRow, rowNumber);
        console.log(`Required field validation for row ${rowNumber}:`, validationErrors);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
          continue; // Skip this row if required fields are missing
        }

        // Department validation
        const departmentValidation = this.validateDepartment(convertedRow, validDepartments, rowNumber);
        if (!departmentValidation.isValid) {
          errors.push(departmentValidation.error!);
          console.log(`❌ Row ${rowNumber} skipped: Invalid department "${convertedRow.department}"`);
          continue; // Skip this row if department is invalid
        }

        // Additional validations
        const additionalErrors = this.validateRowData(convertedRow, rowNumber);
        console.log(`Additional validation for row ${rowNumber}:`, additionalErrors);
        if (additionalErrors.length > 0) {
          errors.push(...additionalErrors);
          if (!options.skipValidation) {
            continue; // Skip this row if validation fails and skipValidation is false
          }
        }

        convertedData.push(convertedRow);
        console.log(`✅ Row ${rowNumber} successfully processed`);
      } catch (error) {
        console.error(`💥 Error processing row ${rowNumber}:`, error);
        errors.push({
          row: rowNumber,
          column: 'general',
          message: `Failed to process row: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error'
        });
      }
    }

    console.log(`\n📊 CONVERSION SUMMARY:`);
    console.log(`   Total rows processed: ${rawData.length}`);
    console.log(`   Successfully converted: ${convertedData.length}`);
    console.log(`   Errors: ${errors.length}`);
    console.log(`   Warnings: ${warnings.length}`);

    if (errors.length > 0) {
      console.log(`\n❌ ERRORS FOUND:`);
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. Row ${error.row}: ${error.message}`);
      });
    }

    return {
      convertedData,
      errors,
      warnings
    };
  }

  private static createFieldMapping(sourceColumns: string[]): Record<string, string> {
    const mapping: Record<string, string> = {};
    const usedColumns = new Set<string>();

    console.log('Source columns found:', sourceColumns);

    // Handle specific Excel format with exact column names
    const exactMappings: Record<string, string> = {
      'First Name': 'firstName',
      'Last Name': 'lastName',
      'Email': 'email',
      'Phone': 'phone',
      'Date of Birth': 'dateOfBirth',
      'Gender': 'gender',
      'Marital Status': 'maritalStatus',
      'Number of Children': 'numberOfChildren',
      'Department': 'department',
      'Position': 'position',
      'Employment Type': 'employmentType',
      'Status': 'employmentStatus', // Map "Status" to employment status by default
      'Hire Date': 'hireDate',
      'Salary': 'salary',
      'Address': 'address',
      'City': 'city',
      'State': 'state',
      'Country': 'country',
      'Village': 'village',
      'Traditional Authority': 'traditionalAuthority',
      'District': 'district',
      'National ID': 'nationalId',
      'Next of Kin Name': 'nextOfKinName',
      'Next of Kin Relationship': 'nextOfKinRelationship',
      'Next of Kin Phone': 'nextOfKinPhone',
      'Next of Kin Address': 'nextOfKinAddress',
      'Emergency Contact Name': 'emergencyContactName',
      'Emergency Contact Phone': 'emergencyContactPhone',
      'Emergency Contact Relationship': 'emergencyContactRelationship',
      'Notes': 'notes'
    };

    // First, try exact mappings for known column structure
    for (const [sourceCol, targetField] of Object.entries(exactMappings)) {
      if (sourceColumns.includes(sourceCol)) {
        mapping[targetField] = sourceCol;
        usedColumns.add(sourceCol);
        console.log(`Exact mapped "${sourceCol}" -> ${targetField}`);
      }
    }

    // Then, handle any remaining unmapped fields with flexible matching
    const fieldPriority = [
      'firstName', 'lastName', 'email', 'position', 'hireDate', 'salary',
      'employmentStatus', 'employmentType', 'maritalStatus', 'phone',
      'dateOfBirth', 'gender', 'department', 'numberOfChildren',
      'address', 'city', 'state', 'country', 'village', 'traditionalAuthority', 'district',
      'nationalId', 'nextOfKinName', 'nextOfKinRelationship', 'nextOfKinPhone', 'nextOfKinAddress',
      'emergencyContactName', 'emergencyContactPhone', 'emergencyContactRelationship', 'notes'
    ];

    for (const targetField of fieldPriority) {
      if (mapping[targetField]) continue; // Skip already mapped fields

      const possibleNames = this.FIELD_MAPPINGS[targetField];
      if (!possibleNames) continue;

      for (const possibleName of possibleNames) {
        const matchedColumn = sourceColumns.find(col => {
          if (usedColumns.has(col)) return false; // Skip already used columns

          const colNormalized = col.toLowerCase().trim().replace(/\s+/g, ' ');
          const nameNormalized = possibleName.toLowerCase().trim().replace(/\s+/g, ' ');

          // Exact match (highest priority)
          if (colNormalized === nameNormalized) return true;

          // Remove special characters and try exact match
          const colCleaned = colNormalized.replace(/[^a-z0-9\s]/g, '');
          const nameCleaned = nameNormalized.replace(/[^a-z0-9\s]/g, '');

          if (colCleaned === nameCleaned) return true;

          return false;
        });

        if (matchedColumn) {
          mapping[targetField] = matchedColumn;
          usedColumns.add(matchedColumn);
          console.log(`Flexible mapped "${matchedColumn}" -> ${targetField}`);
          break;
        }
      }

      if (!mapping[targetField]) {
        console.log(`No mapping found for ${targetField}`);
      }
    }

    console.log('Final field mapping:', mapping);
    return mapping;
  }

  private static convertRow(
    row: Record<string, unknown>,
    fieldMapping: Record<string, string>,
    rowNumber: number,
    warnings: ConversionWarning[]
  ): EmployeeConversionData {
    const convertedRow: Partial<EmployeeConversionData> = {};

    // Map basic fields
    for (const [targetField, sourceColumn] of Object.entries(fieldMapping)) {
      if (sourceColumn && row[sourceColumn] !== undefined) {
        const value = String(row[sourceColumn]).trim();
        if (value) {
          (convertedRow as Record<string, unknown>)[targetField] = this.transformValue(
            targetField,
            value,
            rowNumber,
            sourceColumn,
            warnings
          );
        }
      }
    }

    // Special handling for full name splitting
    if ((!convertedRow.firstName || !convertedRow.lastName) && fieldMapping.firstName) {
      const fullNameValue = String(row[fieldMapping.firstName] || '').trim();
      if (fullNameValue && fullNameValue.includes(' ')) {
        const nameParts = fullNameValue.split(' ').filter(part => part.trim());
        if (nameParts.length >= 2) {
          convertedRow.firstName = nameParts[0];
          convertedRow.lastName = nameParts.slice(1).join(' ');
          warnings.push({
            row: rowNumber,
            column: fieldMapping.firstName,
            message: `Full name split into first and last name`,
            originalValue: fullNameValue,
            convertedValue: `${convertedRow.firstName} | ${convertedRow.lastName}`
          });
        }
      }
    }

    // Ensure required fields have default values if not provided
    return {
      firstName: convertedRow.firstName || '',
      lastName: convertedRow.lastName || '',
      email: convertedRow.email || '',
      phone: convertedRow.phone || '',
      dateOfBirth: convertedRow.dateOfBirth || '',
      gender: convertedRow.gender || '',
      maritalStatus: convertedRow.maritalStatus || 'single',
      numberOfChildren: convertedRow.numberOfChildren || 0,
      department: convertedRow.department || '',
      position: convertedRow.position || '',
      employmentType: convertedRow.employmentType || 'full-time',
      employmentStatus: convertedRow.employmentStatus || 'active',
      hireDate: convertedRow.hireDate || '',
      salary: convertedRow.salary || 0,
      address: convertedRow.address || '',
      city: convertedRow.city || '',
      state: convertedRow.state || '',
      country: convertedRow.country || '',
      village: convertedRow.village || '',
      traditionalAuthority: convertedRow.traditionalAuthority || '',
      district: convertedRow.district || '',
      nationalId: convertedRow.nationalId || '',
      nextOfKinName: convertedRow.nextOfKinName || '',
      nextOfKinRelationship: convertedRow.nextOfKinRelationship || '',
      nextOfKinPhone: convertedRow.nextOfKinPhone || '',
      nextOfKinAddress: convertedRow.nextOfKinAddress || '',
      emergencyContactName: convertedRow.emergencyContactName || '',
      emergencyContactPhone: convertedRow.emergencyContactPhone || '',
      emergencyContactRelationship: convertedRow.emergencyContactRelationship || '',
      notes: convertedRow.notes || ''
    };
  }

  private static transformValue(
    fieldName: string,
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[]
  ): unknown {
    const originalValue = value;

    switch (fieldName) {
      case 'employmentType':
        return this.normalizeEmploymentType(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'employmentStatus':
        return this.normalizeEmploymentStatus(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'maritalStatus':
        return this.normalizeMaritalStatus(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'hireDate':
      case 'dateOfBirth':
        return this.normalizeDate(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'salary':
      case 'numberOfChildren':
        return this.normalizeNumber(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'email':
        return this.normalizeEmail(value, rowNumber, sourceColumn, warnings, originalValue);

      case 'phone':
      case 'nextOfKinPhone':
      case 'emergencyContactPhone':
        return this.normalizePhone(value, rowNumber, sourceColumn, warnings, originalValue);

      default:
        return value.trim();
    }
  }

  private static normalizeEmploymentType(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    const normalized = value.toLowerCase().trim();

    for (const [standardType, variations] of Object.entries(this.EMPLOYMENT_TYPE_MAPPINGS)) {
      if (variations.some(variation => normalized.includes(variation))) {
        if (value !== standardType) {
          warnings.push({
            row: rowNumber,
            column: sourceColumn,
            message: `Employment type normalized`,
            originalValue,
            convertedValue: standardType
          });
        }
        return standardType;
      }
    }

    // Default to full-time if not recognized
    warnings.push({
      row: rowNumber,
      column: sourceColumn,
      message: `Unrecognized employment type, defaulted to 'full-time'`,
      originalValue,
      convertedValue: 'full-time'
    });
    return 'full-time';
  }

  private static normalizeEmploymentStatus(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    const normalized = value.toLowerCase().trim();

    for (const [standardStatus, variations] of Object.entries(this.EMPLOYMENT_STATUS_MAPPINGS)) {
      if (variations.some(variation => normalized.includes(variation))) {
        if (value !== standardStatus) {
          warnings.push({
            row: rowNumber,
            column: sourceColumn,
            message: `Employment status normalized`,
            originalValue,
            convertedValue: standardStatus
          });
        }
        return standardStatus;
      }
    }

    // Default to active if not recognized
    warnings.push({
      row: rowNumber,
      column: sourceColumn,
      message: `Unrecognized employment status, defaulted to 'active'`,
      originalValue,
      convertedValue: 'active'
    });
    return 'active';
  }

  private static normalizeMaritalStatus(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    const normalized = value.toLowerCase().trim();

    for (const [standardStatus, variations] of Object.entries(this.MARITAL_STATUS_MAPPINGS)) {
      if (variations.some(variation => normalized === variation)) {
        if (value !== standardStatus) {
          warnings.push({
            row: rowNumber,
            column: sourceColumn,
            message: `Marital status normalized`,
            originalValue,
            convertedValue: standardStatus
          });
        }
        return standardStatus;
      }
    }

    return 'single'; // Default
  }

  private static normalizeDate(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    if (!value || value.trim() === '') return '';

    // Try to parse various date formats
    const datePatterns = [
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // MM/DD/YYYY or DD/MM/YYYY
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // MM-DD-YYYY or DD-MM-YYYY
      /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/, // MM.DD.YYYY or DD.MM.YYYY
    ];

    for (const pattern of datePatterns) {
      const match = value.match(pattern);
      if (match) {
        let year: number, month: number, day: number;

        if (pattern === datePatterns[0]) { // YYYY-MM-DD
          year = parseInt(match[1]);
          month = parseInt(match[2]);
          day = parseInt(match[3]);
        } else { // Other formats - assume DD/MM/YYYY for Malawi
          day = parseInt(match[1]);
          month = parseInt(match[2]);
          year = parseInt(match[3]);
        }

        // Validate date components
        if (year >= 1900 && year <= new Date().getFullYear() &&
            month >= 1 && month <= 12 &&
            day >= 1 && day <= 31) {

          const standardDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

          if (value !== standardDate) {
            warnings.push({
              row: rowNumber,
              column: sourceColumn,
              message: `Date format standardized to YYYY-MM-DD`,
              originalValue,
              convertedValue: standardDate
            });
          }

          return standardDate;
        }
      }
    }

    // If no pattern matches, return empty string and add warning
    warnings.push({
      row: rowNumber,
      column: sourceColumn,
      message: `Invalid date format, cleared field`,
      originalValue,
      convertedValue: ''
    });

    return '';
  }

  private static normalizeNumber(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): number {
    if (!value || value.trim() === '') return 0;

    // Remove common currency symbols and formatting
    const cleaned = value.replace(/[MWK$,\s]/g, '');
    const number = parseFloat(cleaned);

    if (isNaN(number)) {
      warnings.push({
        row: rowNumber,
        column: sourceColumn,
        message: `Invalid number format, defaulted to 0`,
        originalValue,
        convertedValue: '0'
      });
      return 0;
    }

    if (value !== number.toString()) {
      warnings.push({
        row: rowNumber,
        column: sourceColumn,
        message: `Number format cleaned`,
        originalValue,
        convertedValue: number.toString()
      });
    }

    return number;
  }

  private static normalizeEmail(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    const email = value.toLowerCase().trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(email)) {
      warnings.push({
        row: rowNumber,
        column: sourceColumn,
        message: `Invalid email format`,
        originalValue,
        convertedValue: email
      });
    }

    return email;
  }

  private static normalizePhone(
    value: string,
    rowNumber: number,
    sourceColumn: string,
    warnings: ConversionWarning[],
    originalValue: string
  ): string {
    if (!value || value.trim() === '') return '';

    // Remove spaces, dashes, and parentheses
    let cleaned = value.replace(/[\s\-\(\)]/g, '');

    // Add Malawi country code if missing
    if (cleaned.match(/^[0-9]{9}$/)) {
      cleaned = '+265' + cleaned;
      warnings.push({
        row: rowNumber,
        column: sourceColumn,
        message: `Added Malawi country code (+265)`,
        originalValue,
        convertedValue: cleaned
      });
    } else if (cleaned.match(/^265[0-9]{9}$/)) {
      cleaned = '+' + cleaned;
      warnings.push({
        row: rowNumber,
        column: sourceColumn,
        message: `Added + prefix to country code`,
        originalValue,
        convertedValue: cleaned
      });
    }

    return cleaned;
  }

  private static validateRequiredFields(
    row: EmployeeConversionData,
    rowNumber: number
  ): ConversionError[] {
    const errors: ConversionError[] = [];
    const requiredFields = ['firstName', 'lastName', 'email', 'position', 'employmentType', 'employmentStatus', 'hireDate'];

    for (const field of requiredFields) {
      const value = (row as Record<string, unknown>)[field];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        errors.push({
          row: rowNumber,
          column: field,
          message: `Required field '${field}' is missing or empty`,
          severity: 'error',
          suggestedFix: `Please provide a value for ${field}`
        });
      }
    }

    return errors;
  }

  private static validateRowData(
    row: EmployeeConversionData,
    rowNumber: number
  ): ConversionError[] {
    const errors: ConversionError[] = [];

    // Validate email format
    if (row.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(row.email)) {
        errors.push({
          row: rowNumber,
          column: 'email',
          message: 'Invalid email format',
          severity: 'error',
          suggestedFix: 'Please provide a valid email address'
        });
      }
    }

    // Validate hire date
    if (row.hireDate) {
      const hireDate = new Date(row.hireDate);
      const today = new Date();
      if (hireDate > today) {
        errors.push({
          row: rowNumber,
          column: 'hireDate',
          message: 'Hire date cannot be in the future',
          severity: 'warning',
          suggestedFix: 'Please check the hire date'
        });
      }
    }

    // Validate salary
    if (row.salary && row.salary < 0) {
      errors.push({
        row: rowNumber,
        column: 'salary',
        message: 'Salary cannot be negative',
        severity: 'warning',
        suggestedFix: 'Please provide a valid salary amount'
      });
    }

    return errors;
  }

  /**
   * Fetch all valid departments from the database
   */
  private static async fetchValidDepartments(): Promise<Array<{ _id: string; name: string }>> {
    try {
      const departments = await Department.find({ status: { $ne: 'inactive' } })
        .select('_id name')
        .lean();

      return departments.map(dept => ({
        _id: dept._id.toString(),
        name: dept.name
      }));
    } catch (error) {
      console.error('Error fetching departments:', error);
      return [];
    }
  }

  /**
   * Validate if the department exists in the database
   */
  private static validateDepartment(
    row: EmployeeConversionData,
    validDepartments: Array<{ _id: string; name: string }>,
    rowNumber: number
  ): { isValid: boolean; error?: ConversionError } {
    // If no department specified, it's valid (optional field)
    if (!row.department || row.department.trim() === '') {
      return { isValid: true };
    }

    const departmentName = row.department.trim();

    // Check if department exists (case-insensitive)
    const departmentExists = validDepartments.some(dept =>
      dept.name.toLowerCase() === departmentName.toLowerCase()
    );

    if (!departmentExists) {
      const availableDepartments = validDepartments.map(d => d.name).join(', ');

      return {
        isValid: false,
        error: {
          row: rowNumber,
          column: 'department',
          message: `Department "${departmentName}" does not exist in the system. Available departments: ${availableDepartments}`,
          severity: 'error',
          suggestedFix: `Please ensure the department exists before importing employees. Create the department first or use one of the existing departments: ${availableDepartments}`
        }
      };
    }

    return { isValid: true };
  }
}

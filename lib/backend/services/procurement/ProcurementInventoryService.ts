// lib/backend/services/procurement/ProcurementInventoryService.ts
import { CrudService } from '../base/CrudService';
import ProcurementInventory, { IProcurementInventory } from '@/models/procurement/ProcurementInventory';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';
import { AuditDeletionService, AuditDeletionContext, AuditDeletionResult } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

// Types for service operations
export interface InventoryFilters {
  category?: string;
  location?: string;
  status?: string;
  lowStock?: boolean;
  search?: string;
  tags?: string[];
}

export interface StockOperation {
  itemId: string;
  quantity: number;
  operation: 'add' | 'remove' | 'set';
  reason?: string;
  reference?: string;
  userId: string;
}

export interface ReorderSuggestion {
  item: IProcurementInventory;
  suggestedQuantity: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimatedCost: number;
  preferredSupplier?: string;
}

export interface StockLevelReport {
  totalItems: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  onOrder: number;
  totalValue: number;
}

export interface InventoryValuationReport {
  totalValue: number;
  averageValue: number;
  categoryBreakdown: Array<{
    category: string;
    value: number;
    percentage: number;
  }>;
}

/**
 * Service for managing procurement inventory
 */
export class ProcurementInventoryService extends CrudService<IProcurementInventory> {
  constructor() {
    super(ProcurementInventory, 'ProcurementInventory');
  }

  /**
   * Create a new inventory item
   */
  async createInventoryItem(data: Partial<IProcurementInventory>): Promise<IProcurementInventory> {
    try {
      logger.info('Creating new inventory item', LogCategory.PROCUREMENT, { data });
      
      // Generate inventory ID if not provided
      if (!data.inventoryId) {
        data.inventoryId = await this.generateInventoryId();
      }
      
      // Calculate initial values
      if (data.currentStock && data.unitPrice) {
        data.totalValue = data.currentStock * data.unitPrice;
      }
      
      // Set reorder point if not provided
      if (!data.reorderPoint && data.minimumStock) {
        data.reorderPoint = Math.ceil(data.minimumStock * 1.2);
      }
      
      const item = await this.create(data);
      
      logger.info('Inventory item created successfully', LogCategory.PROCUREMENT, { 
        itemId: item.inventoryId 
      });
      
      return item;
    } catch (error) {
      logger.error('Error creating inventory item', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update stock levels
   */
  async updateStock(
    itemId: string, 
    quantity: number, 
    operation: 'add' | 'remove' | 'set',
    userId: string,
    reason?: string
  ): Promise<IProcurementInventory> {
    try {
      logger.info('Updating stock', LogCategory.PROCUREMENT, { 
        itemId, quantity, operation, userId, reason 
      });
      
      const item = await this.findById(itemId);
      if (!item) {
        throw new Error(`Inventory item not found: ${itemId}`);
      }
      
      let newStock: number;
      
      switch (operation) {
        case 'add':
          newStock = item.currentStock + quantity;
          break;
        case 'remove':
          newStock = Math.max(0, item.currentStock - quantity);
          break;
        case 'set':
          newStock = quantity;
          break;
        default:
          throw new Error(`Invalid operation: ${operation}`);
      }
      
      // Update the item
      item.currentStock = newStock;
      item.updatedBy = new mongoose.Types.ObjectId(userId);
      
      // Update total value
      item.updateTotalValue();
      
      const updatedItem = await item.save();
      
      // Log the stock movement (implement separately if needed)
      await this.logStockMovement(item, operation, quantity, userId, reason);
      
      logger.info('Stock updated successfully', LogCategory.PROCUREMENT, { 
        itemId: item.inventoryId,
        oldStock: item.currentStock,
        newStock: updatedItem.currentStock
      });
      
      return updatedItem;
    } catch (error) {
      logger.error('Error updating stock', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get items that need reordering
   */
  async getReorderSuggestions(): Promise<ReorderSuggestion[]> {
    try {
      logger.info('Generating reorder suggestions', LogCategory.PROCUREMENT);
      
      // Find items where current stock is below reorder level
      const lowStockItems = await ProcurementInventory.find({
        $expr: {
          $lte: ['$currentStock', '$reorderLevel']
        },
        status: { $in: ['active', 'low_stock'] }
      })
        .populate('category')
        .populate('preferredSuppliers');

      const suggestions: ReorderSuggestion[] = lowStockItems.map((item: IProcurementInventory) => {
        const urgency = this.calculateUrgency(item);
        const suggestedQuantity = this.calculateReorderQuantity(item);
        const estimatedCost = suggestedQuantity * item.unitPrice;
        
        return {
          item,
          suggestedQuantity,
          urgency,
          estimatedCost,
          preferredSupplier: item.preferredSuppliers && item.preferredSuppliers.length > 0
            ? (item.preferredSuppliers[0] as any)?.name || 'No preferred supplier'
            : 'No preferred supplier'
        };
      });
      
      // Sort by urgency
      suggestions.sort((a, b) => {
        const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
      });
      
      logger.info('Reorder suggestions generated', LogCategory.PROCUREMENT, { 
        count: suggestions.length 
      });
      
      return suggestions;
    } catch (error) {
      logger.error('Error generating reorder suggestions', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Search inventory items with filters
   */
  async searchInventory(
    filters: InventoryFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    items: IProcurementInventory[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      logger.info('Searching inventory', LogCategory.PROCUREMENT, { filters, page, limit });
      
      const query: any = {};
      
      // Apply filters
      if (filters.category) {
        query.category = filters.category;
      }
      
      if (filters.location) {
        query.location = new RegExp(filters.location, 'i');
      }
      
      if (filters.status) {
        query.status = filters.status;
      }
      
      if (filters.lowStock) {
        query.$expr = { $lte: ['$currentStock', '$minimumStock'] };
      }
      
      if (filters.search) {
        query.$or = [
          { name: new RegExp(filters.search, 'i') },
          { description: new RegExp(filters.search, 'i') },
          { sku: new RegExp(filters.search, 'i') },
          { inventoryId: new RegExp(filters.search, 'i') }
        ];
      }
      
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }
      
      // Exclude discontinued items by default
      if (!filters.status) {
        query.status = { $ne: 'discontinued' };
      }
      
      const skip = (page - 1) * limit;
      
      const [items, total] = await Promise.all([
        ProcurementInventory.find(query)
          .populate('category')
          .populate('preferredSuppliers')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        ProcurementInventory.countDocuments(query)
      ]);
      
      const pages = Math.ceil(total / limit);
      
      logger.info('Inventory search completed', LogCategory.PROCUREMENT, { 
        total, page, pages 
      });
      
      return { items, total, page, pages };
    } catch (error) {
      logger.error('Error searching inventory', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get stock level report
   */
  async getStockLevels(): Promise<StockLevelReport> {
    try {
      logger.info('Generating stock level report', LogCategory.PROCUREMENT);
      
      const pipeline = [
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalValue: { $sum: '$totalValue' }
          }
        }
      ];
      
      const results = await ProcurementInventory.aggregate(pipeline);
      
      const report: StockLevelReport = {
        totalItems: 0,
        inStock: 0,
        lowStock: 0,
        outOfStock: 0,
        onOrder: 0,
        totalValue: 0
      };
      
      results.forEach(result => {
        report.totalItems += result.count;
        report.totalValue += result.totalValue;
        
        switch (result._id) {
          case 'in_stock':
            report.inStock = result.count;
            break;
          case 'low_stock':
            report.lowStock = result.count;
            break;
          case 'out_of_stock':
            report.outOfStock = result.count;
            break;
          case 'on_order':
            report.onOrder = result.count;
            break;
        }
      });
      
      logger.info('Stock level report generated', LogCategory.PROCUREMENT, report);
      
      return report;
    } catch (error) {
      logger.error('Error generating stock level report', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  // Private helper methods
  private async generateInventoryId(): Promise<string> {
    const prefix = 'INV';
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get the last inventory item for this month
    const lastItem = await ProcurementInventory.findOne({
      inventoryId: new RegExp(`^${prefix}-${year}${month}`)
    }).sort({ inventoryId: -1 });
    
    let sequence = 1;
    if (lastItem) {
      const lastSequence = parseInt(lastItem.inventoryId.split('-').pop() || '0');
      sequence = lastSequence + 1;
    }
    
    return `${prefix}-${year}${month}-${String(sequence).padStart(4, '0')}`;
  }

  private calculateUrgency(item: IProcurementInventory): 'low' | 'medium' | 'high' | 'critical' {
    const stockRatio = item.currentStock / item.minimumStock;
    
    if (stockRatio <= 0) return 'critical';
    if (stockRatio <= 0.25) return 'high';
    if (stockRatio <= 0.5) return 'medium';
    return 'low';
  }

  private calculateReorderQuantity(item: IProcurementInventory): number {
    if (item.reorderQuantity > 0) {
      return item.reorderQuantity;
    }
    
    // Default calculation: bring stock to maximum or 3x minimum
    const targetStock = item.maximumStock || (item.minimumStock * 3);
    return Math.max(0, targetStock - item.currentStock);
  }

  private async logStockMovement(
    item: IProcurementInventory,
    operation: string,
    quantity: number,
    userId: string,
    reason?: string
  ): Promise<void> {
    // This would integrate with a stock movement logging system
    // For now, just log to console
    logger.info('Stock movement logged', LogCategory.PROCUREMENT, {
      itemId: item.inventoryId,
      operation,
      quantity,
      userId,
      reason
    });
  }

  /**
   * Delete an inventory item with audit trail (government compliant)
   */
  async deleteInventoryItem(
    id: string,
    auditContext: AuditDeletionContext
  ): Promise<AuditDeletionResult> {
    try {
      logger.info('Initiating audit-compliant inventory item deletion', LogCategory.PROCUREMENT, {
        itemId: id,
        userId: auditContext.userInfo.id,
        reason: auditContext.deletionReason
      });

      // Custom validation logic will be handled in beforeDelete hook

      // Perform audit-compliant deletion
      const result = await AuditDeletionService.performAuditDeletion(
        ProcurementInventory,
        [id],
        auditContext,
        {
          populateFields: ['category', 'preferredSuppliers'],
          beforeDelete: async (items: IProcurementInventory[]) => {
            // Custom validation logic
            for (const item of items) {
              // Check if item has pending purchase orders
              const db = mongoose.connection.db;
              if (db) {
                const pendingOrders = await db
                  .collection('purchase_orders')
                  .countDocuments({
                    'items.inventoryItem': item._id,
                    status: { $in: ['pending', 'approved', 'processing'] }
                  });

                if (pendingOrders > 0) {
                  throw new Error(`Cannot delete inventory item "${item.name}" with ${pendingOrders} pending purchase orders`);
                }

                // Check if item has active requisitions
                const activeRequisitions = await db
                  .collection('requisitions')
                  .countDocuments({
                    'items.inventoryItem': item._id,
                    status: { $in: ['pending', 'approved', 'processing'] }
                  });

                if (activeRequisitions > 0) {
                  throw new Error(`Cannot delete inventory item "${item.name}" with ${activeRequisitions} active requisitions`);
                }
              }

              // Ensure stock levels are properly documented
              const hasStock = item.currentStock > 0;
              if (hasStock && !auditContext.deletionReason.toLowerCase().includes('stock')) {
                throw new Error(`Item "${item.name}" has ${item.currentStock} units in stock. Please include stock disposition in deletion reason.`);
              }

              // Log additional context for inventory items
              logger.info('Deleting procurement inventory item', LogCategory.PROCUREMENT, {
                itemId: item._id,
                inventoryId: item.inventoryId,
                name: item.name,
                sku: item.sku,
                currentStock: item.currentStock,
                totalValue: item.totalValue,
                status: item.status
              });

              // Log stock movement for audit trail
              if (item.currentStock > 0) {
                await this.logStockMovement(
                  item,
                  'delete',
                  item.currentStock,
                  auditContext.userInfo.id,
                  `Item deletion: ${auditContext.deletionReason}`
                );
              }
            }
          },
          afterDelete: async (deletedItems: IProcurementInventory[], auditRecords: any[]) => {
            // Update any references in other systems if needed
            logger.info('Inventory item deletion completed', LogCategory.PROCUREMENT, {
              deletedCount: deletedItems.length,
              auditRecordsCreated: auditRecords.length,
              totalValueRemoved: deletedItems.reduce((sum, item) => sum + (item.totalValue || 0), 0)
            });
          }
        }
      );

      return result;
    } catch (error) {
      logger.error('Error in audit-compliant inventory item deletion', LogCategory.PROCUREMENT, error);

      const structuredError = errorService.createError(
        ErrorType.BUSINESS_LOGIC,
        'PROCUREMENT_INVENTORY_DELETE_FAILED',
        error instanceof Error ? error.message : 'Failed to delete procurement inventory item',
        'Unable to delete the inventory item. This may be due to pending orders, active requisitions, or other business constraints.',
        {
          userId: auditContext.userInfo.id,
          endpoint: '/api/procurement/inventory',
          method: 'DELETE',
          additionalData: { itemId: id, deletionReason: auditContext.deletionReason }
        },
        ErrorSeverity.MEDIUM,
        error instanceof Error ? error.stack : undefined,
        [
          'Ensure the item has no pending purchase orders',
          'Check that the item has no active requisitions',
          'Document stock disposition in deletion reason if item has stock',
          'Verify you have the necessary permissions',
          'Contact an administrator if the issue persists'
        ]
      );

      throw new Error(structuredError.userMessage);
    }
  }

  /**
   * Bulk delete inventory items with audit trail
   */
  async bulkDeleteInventoryItems(
    ids: string[],
    auditContext: Omit<AuditDeletionContext, 'deletionType'>
  ): Promise<AuditDeletionResult> {
    try {
      logger.info('Initiating bulk audit-compliant inventory deletion', LogCategory.PROCUREMENT, {
        itemIds: ids,
        count: ids.length,
        userId: auditContext.userInfo.id
      });

      const bulkAuditContext: AuditDeletionContext = {
        ...auditContext,
        deletionType: 'bulk'
      };

      // Custom validation logic will be handled in beforeDelete hook

      const result = await AuditDeletionService.performAuditDeletion(
        ProcurementInventory,
        ids,
        bulkAuditContext,
        {
          populateFields: ['category', 'preferredSuppliers'],
          beforeDelete: async (items: IProcurementInventory[]) => {
            // Enhanced validation for bulk operations
            for (const item of items) {
              // Check if item has pending purchase orders
              const db = mongoose.connection.db;
              if (db) {
                const pendingOrders = await db
                  .collection('purchase_orders')
                  .countDocuments({
                    'items.inventoryItem': item._id,
                    status: { $in: ['pending', 'approved', 'processing'] }
                  });

                if (pendingOrders > 0) {
                  throw new Error(`Item "${item.name}" has ${pendingOrders} pending purchase orders`);
                }

                // Check if item has active requisitions
                const activeRequisitions = await db
                  .collection('requisitions')
                  .countDocuments({
                    'items.inventoryItem': item._id,
                    status: { $in: ['pending', 'approved', 'processing'] }
                  });

                if (activeRequisitions > 0) {
                  throw new Error(`Item "${item.name}" has ${activeRequisitions} active requisitions`);
                }
              }

              // For high-value items, require additional documentation
              const isHighValue = (item.totalValue || 0) > 50000; // MWK 50,000
              const hasDetailedReason = auditContext.deletionReason.length > 50;

              if (isHighValue && !hasDetailedReason) {
                throw new Error(`High-value item "${item.name}" (MWK ${item.totalValue?.toLocaleString()}) requires detailed deletion reason (minimum 50 characters)`);
              }
            }

            const totalValue = items.reduce((sum, item) => sum + (item.totalValue || 0), 0);
            const totalStock = items.reduce((sum, item) => sum + item.currentStock, 0);

            logger.info('Bulk deleting procurement inventory items', LogCategory.PROCUREMENT, {
              items: items.map(item => ({
                id: item._id,
                inventoryId: item.inventoryId,
                name: item.name,
                currentStock: item.currentStock,
                totalValue: item.totalValue
              })),
              summary: {
                totalItems: items.length,
                totalValue,
                totalStock
              }
            });

            // Log stock movements for items with stock
            for (const item of items) {
              if (item.currentStock > 0) {
                await this.logStockMovement(
                  item,
                  'bulk_delete',
                  item.currentStock,
                  auditContext.userInfo.id,
                  `Bulk deletion: ${auditContext.deletionReason}`
                );
              }
            }
          }
        }
      );

      return result;
    } catch (error) {
      logger.error('Error in bulk audit-compliant inventory deletion', LogCategory.PROCUREMENT, error);

      const structuredError = errorService.createError(
        ErrorType.BUSINESS_LOGIC,
        'PROCUREMENT_INVENTORY_BULK_DELETE_FAILED',
        error instanceof Error ? error.message : 'Failed to bulk delete procurement inventory items',
        'Unable to delete the selected inventory items. Some items may have pending orders, active requisitions, or other dependencies.',
        {
          userId: auditContext.userInfo.id,
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'DELETE',
          additionalData: { itemIds: ids, count: ids.length }
        },
        ErrorSeverity.MEDIUM,
        error instanceof Error ? error.stack : undefined,
        [
          'Review the items selected for deletion',
          'Ensure no items have pending purchase orders',
          'Check for active requisitions on selected items',
          'Provide detailed reasons for high-value item deletions',
          'Try deleting items individually to identify specific issues'
        ]
      );

      throw new Error(structuredError.userMessage);
    }
  }
}

// Export service instance
export const procurementInventoryService = new ProcurementInventoryService();

import mongoose from 'mongoose';
import { CrudService } from '../base/CrudService';
import Contract, { IContract } from '@/models/procurement/Contract';
import Supplier from '@/models/procurement/Supplier';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { format, addDays, isAfter, isBefore } from 'date-fns';

// Contract creation data interface
export interface CreateContractData {
  supplierId: string;
  title: string;
  description: string;
  contractType: 'service' | 'supply' | 'maintenance' | 'lease' | 'consulting' | 'construction';
  value: number;
  currency?: string;
  startDate: Date;
  endDate: Date;
  autoRenewal?: boolean;
  renewalTerms?: string;
  terms: string[];
  paymentTerms: string;
  deliveryTerms?: string;
  penaltyClause?: string;
  warrantyTerms?: string;
  performanceMetrics?: Array<{
    metric: string;
    target: string;
    measurement: string;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  }>;
  budgetCategory?: string;
  costCenter?: string;
  taxRate?: number;
  discountRate?: number;
  complianceRequirements?: string[];
  legalReviewRequired?: boolean;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'critical';
  riskLevel?: 'low' | 'medium' | 'high';
  notes?: string;
  createdBy: string;
}

// Contract update data interface
export interface UpdateContractData extends Partial<CreateContractData> {
  status?: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed' | 'suspended' | 'pending_approval';
  renewalDate?: Date;
  approvedBy?: string;
  approvalDate?: Date;
  reviewDate?: Date;
  lastModifiedBy?: string;
}

// Contract renewal data interface
export interface ContractRenewalData {
  newEndDate: Date;
  renewalTerms?: string;
  valueAdjustment?: number;
  updatedTerms?: string[];
  renewedBy: string;
  notes?: string;
}

// Contract filters interface
export interface ContractFilters {
  supplierId?: string;
  contractType?: string;
  status?: string;
  startDate?: { from?: Date; to?: Date };
  endDate?: { from?: Date; to?: Date };
  value?: { min?: number; max?: number };
  priority?: string;
  riskLevel?: string;
  tags?: string[];
  search?: string;
}

// Compliance report interface
export interface ComplianceReport {
  contractId: string;
  contractNumber: string;
  complianceScore: number;
  issues: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    severity: 'low' | 'medium' | 'high';
    recommendation?: string;
  }>;
  lastReviewDate?: Date;
  nextReviewDate?: Date;
}

/**
 * Contract Service for managing procurement contracts
 */
export class ContractService extends CrudService<IContract> {
  constructor() {
    super(Contract, 'Contract');
  }

  /**
   * Create a new contract
   * @param contractData - Contract creation data
   * @returns Created contract
   */
  async createContract(contractData: CreateContractData): Promise<IContract> {
    try {
      await connectToDatabase();
      logger.info('Creating new contract', LogCategory.PROCUREMENT, { 
        title: contractData.title,
        supplierId: contractData.supplierId 
      });

      // Validate supplier exists
      const supplier = await Supplier.findById(contractData.supplierId);
      if (!supplier) {
        throw new Error(`Supplier with ID ${contractData.supplierId} not found`);
      }

      // Validate date range
      if (isAfter(contractData.startDate, contractData.endDate)) {
        throw new Error('Start date cannot be after end date');
      }

      // Create contract with approval workflow
      const approvalWorkflow = this.generateApprovalWorkflow(contractData.value, contractData.contractType);

      const contract = new Contract({
        ...contractData,
        supplierId: new mongoose.Types.ObjectId(contractData.supplierId),
        budgetCategory: contractData.budgetCategory ? new mongoose.Types.ObjectId(contractData.budgetCategory) : undefined,
        costCenter: contractData.costCenter ? new mongoose.Types.ObjectId(contractData.costCenter) : undefined,
        createdBy: new mongoose.Types.ObjectId(contractData.createdBy),
        approvalWorkflow,
        legalReview: {
          required: contractData.legalReviewRequired || contractData.value > 1000000, // Require legal review for high-value contracts
        },
        status: contractData.value > 500000 ? 'pending_approval' : 'draft' // Auto-require approval for high-value contracts
      });

      const savedContract = await contract.save();
      
      logger.info('Contract created successfully', LogCategory.PROCUREMENT, { 
        contractId: savedContract._id,
        contractNumber: savedContract.contractNumber 
      });

      return savedContract;
    } catch (error) {
      logger.error('Error creating contract', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update an existing contract
   * @param id - Contract ID
   * @param updateData - Contract update data
   * @returns Updated contract
   */
  async updateContract(id: string, updateData: UpdateContractData): Promise<IContract> {
    try {
      await connectToDatabase();
      logger.info('Updating contract', LogCategory.PROCUREMENT, { contractId: id });

      const contract = await Contract.findById(id);
      if (!contract) {
        throw new Error(`Contract with ID ${id} not found`);
      }

      // Validate status transitions
      if (updateData.status && !this.isValidStatusTransition(contract.status, updateData.status)) {
        throw new Error(`Invalid status transition from ${contract.status} to ${updateData.status}`);
      }

      // Update fields
      Object.assign(contract, updateData);
      
      // Set modification metadata
      if (updateData.lastModifiedBy) {
        contract.lastModifiedBy = new mongoose.Types.ObjectId(updateData.lastModifiedBy);
      }

      const updatedContract = await contract.save();
      
      logger.info('Contract updated successfully', LogCategory.PROCUREMENT, { 
        contractId: updatedContract._id,
        contractNumber: updatedContract.contractNumber 
      });

      return updatedContract;
    } catch (error) {
      logger.error('Error updating contract', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Renew a contract
   * @param id - Contract ID
   * @param renewalData - Contract renewal data
   * @returns Renewed contract
   */
  async renewContract(id: string, renewalData: ContractRenewalData): Promise<IContract> {
    try {
      await connectToDatabase();
      logger.info('Renewing contract', LogCategory.PROCUREMENT, { contractId: id });

      const contract = await Contract.findById(id);
      if (!contract) {
        throw new Error(`Contract with ID ${id} not found`);
      }

      // Validate contract can be renewed
      if (!contract.canRenew()) {
        throw new Error('Contract cannot be renewed at this time');
      }

      // Update contract for renewal
      contract.renewalDate = new Date();
      contract.endDate = renewalData.newEndDate;
      contract.status = 'active';
      
      if (renewalData.renewalTerms) {
        contract.renewalTerms = renewalData.renewalTerms;
      }
      
      if (renewalData.valueAdjustment) {
        contract.value += renewalData.valueAdjustment;
      }
      
      if (renewalData.updatedTerms) {
        contract.terms = renewalData.updatedTerms;
      }
      
      if (renewalData.notes) {
        contract.notes = contract.notes ? `${contract.notes}\n\nRenewal Notes: ${renewalData.notes}` : `Renewal Notes: ${renewalData.notes}`;
      }

      contract.lastModifiedBy = new mongoose.Types.ObjectId(renewalData.renewedBy);

      const renewedContract = await contract.save();
      
      logger.info('Contract renewed successfully', LogCategory.PROCUREMENT, { 
        contractId: renewedContract._id,
        contractNumber: renewedContract.contractNumber,
        newEndDate: renewalData.newEndDate
      });

      return renewedContract;
    } catch (error) {
      logger.error('Error renewing contract', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Terminate a contract
   * @param id - Contract ID
   * @param reason - Termination reason
   * @param terminatedBy - User ID who terminated the contract
   * @returns Terminated contract
   */
  async terminateContract(id: string, reason: string, terminatedBy: string): Promise<IContract> {
    try {
      await connectToDatabase();
      logger.info('Terminating contract', LogCategory.PROCUREMENT, { contractId: id, reason });

      const contract = await Contract.findById(id);
      if (!contract) {
        throw new Error(`Contract with ID ${id} not found`);
      }

      // Validate contract can be terminated
      if (!['active', 'suspended'].includes(contract.status)) {
        throw new Error(`Contract with status ${contract.status} cannot be terminated`);
      }

      // Update contract status
      contract.status = 'terminated';
      contract.notes = contract.notes ? `${contract.notes}\n\nTermination Reason: ${reason}` : `Termination Reason: ${reason}`;
      contract.lastModifiedBy = new mongoose.Types.ObjectId(terminatedBy);

      const terminatedContract = await contract.save();
      
      logger.info('Contract terminated successfully', LogCategory.PROCUREMENT, { 
        contractId: terminatedContract._id,
        contractNumber: terminatedContract.contractNumber 
      });

      return terminatedContract;
    } catch (error) {
      logger.error('Error terminating contract', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get contracts expiring within specified days
   * @param days - Number of days to look ahead (default: 30)
   * @returns Expiring contracts
   */
  async getExpiringContracts(days: number = 30): Promise<IContract[]> {
    try {
      await connectToDatabase();
      logger.info('Getting expiring contracts', LogCategory.PROCUREMENT, { days });

      const contracts = await Contract.findExpiring(days);
      
      logger.info(`Found ${contracts.length} expiring contracts`, LogCategory.PROCUREMENT);
      return contracts;
    } catch (error) {
      logger.error('Error getting expiring contracts', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get contracts by supplier
   * @param supplierId - Supplier ID
   * @returns Supplier contracts
   */
  async getContractsBySupplier(supplierId: string): Promise<IContract[]> {
    try {
      await connectToDatabase();
      logger.info('Getting contracts by supplier', LogCategory.PROCUREMENT, { supplierId });

      const contracts = await Contract.findBySupplier(supplierId);
      
      logger.info(`Found ${contracts.length} contracts for supplier`, LogCategory.PROCUREMENT);
      return contracts;
    } catch (error) {
      logger.error('Error getting contracts by supplier', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Validate contract compliance
   * @param contractId - Contract ID
   * @returns Compliance report
   */
  async validateContractCompliance(contractId: string): Promise<ComplianceReport> {
    try {
      await connectToDatabase();
      logger.info('Validating contract compliance', LogCategory.PROCUREMENT, { contractId });

      const contract = await Contract.findById(contractId).populate('supplierId');
      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      const issues: ComplianceReport['issues'] = [];
      let complianceScore = 100;

      // Check contract expiry
      if (contract.isExpired()) {
        issues.push({
          type: 'error',
          message: 'Contract has expired',
          severity: 'high',
          recommendation: 'Renew or terminate the contract immediately'
        });
        complianceScore -= 30;
      } else if (contract.isExpiringSoon(30)) {
        issues.push({
          type: 'warning',
          message: 'Contract is expiring soon',
          severity: 'medium',
          recommendation: 'Plan for contract renewal or replacement'
        });
        complianceScore -= 10;
      }

      // Check legal review requirement
      if (contract.legalReview.required && !contract.legalReview.reviewedBy) {
        issues.push({
          type: 'warning',
          message: 'Legal review required but not completed',
          severity: 'medium',
          recommendation: 'Complete legal review before contract activation'
        });
        complianceScore -= 15;
      }

      // Check approval workflow
      const pendingApprovals = contract.approvalWorkflow.filter(approval => approval.status === 'pending');
      if (pendingApprovals.length > 0) {
        issues.push({
          type: 'info',
          message: `${pendingApprovals.length} pending approval(s)`,
          severity: 'low',
          recommendation: 'Complete pending approvals to activate contract'
        });
        complianceScore -= 5;
      }

      // Check performance metrics
      if (contract.performanceMetrics && contract.performanceMetrics.length === 0 && contract.contractType === 'service') {
        issues.push({
          type: 'warning',
          message: 'No performance metrics defined for service contract',
          severity: 'medium',
          recommendation: 'Define performance metrics to monitor service quality'
        });
        complianceScore -= 10;
      }

      // Check compliance requirements
      if (contract.complianceRequirements && contract.complianceRequirements.length > 0) {
        issues.push({
          type: 'info',
          message: `${contract.complianceRequirements.length} compliance requirement(s) to monitor`,
          severity: 'low',
          recommendation: 'Ensure ongoing compliance with specified requirements'
        });
      }

      const report: ComplianceReport = {
        contractId: contract._id.toString(),
        contractNumber: contract.contractNumber,
        complianceScore: Math.max(0, complianceScore),
        issues,
        lastReviewDate: contract.reviewDate,
        nextReviewDate: addDays(new Date(), 90) // Suggest review every 90 days
      };

      logger.info('Contract compliance validation completed', LogCategory.PROCUREMENT, { 
        contractId,
        complianceScore: report.complianceScore,
        issuesCount: issues.length
      });

      return report;
    } catch (error) {
      logger.error('Error validating contract compliance', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Search contracts with filters
   * @param filters - Search filters
   * @param page - Page number
   * @param limit - Items per page
   * @returns Filtered contracts with pagination
   */
  async searchContracts(filters: ContractFilters, page: number = 1, limit: number = 20) {
    try {
      await connectToDatabase();
      logger.info('Searching contracts', LogCategory.PROCUREMENT, { filters, page, limit });

      const query: any = {};

      // Apply filters
      if (filters.supplierId) {
        query.supplierId = new mongoose.Types.ObjectId(filters.supplierId);
      }

      if (filters.contractType) {
        query.contractType = filters.contractType;
      }

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.startDate) {
        query.startDate = {};
        if (filters.startDate.from) query.startDate.$gte = filters.startDate.from;
        if (filters.startDate.to) query.startDate.$lte = filters.startDate.to;
      }

      if (filters.endDate) {
        query.endDate = {};
        if (filters.endDate.from) query.endDate.$gte = filters.endDate.from;
        if (filters.endDate.to) query.endDate.$lte = filters.endDate.to;
      }

      if (filters.value) {
        query.value = {};
        if (filters.value.min) query.value.$gte = filters.value.min;
        if (filters.value.max) query.value.$lte = filters.value.max;
      }

      if (filters.priority) {
        query.priority = filters.priority;
      }

      if (filters.riskLevel) {
        query.riskLevel = filters.riskLevel;
      }

      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }

      if (filters.search) {
        query.$or = [
          { title: { $regex: filters.search, $options: 'i' } },
          { description: { $regex: filters.search, $options: 'i' } },
          { contractNumber: { $regex: filters.search, $options: 'i' } }
        ];
      }

      const skip = (page - 1) * limit;
      
      const [contracts, total] = await Promise.all([
        Contract.find(query)
          .populate('supplierId', 'name contactEmail')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        Contract.countDocuments(query)
      ]);

      const result = {
        contracts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

      logger.info(`Found ${contracts.length} contracts`, LogCategory.PROCUREMENT, { 
        total,
        page,
        pages: result.pagination.pages
      });

      return result;
    } catch (error) {
      logger.error('Error searching contracts', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Generate approval workflow based on contract value and type
   * @param value - Contract value
   * @param contractType - Contract type
   * @returns Approval workflow array
   */
  private generateApprovalWorkflow(value: number, contractType: string) {
    const workflow = [];

    // Level 1: Department approval (always required)
    workflow.push({
      level: 1,
      approverRole: 'DEPARTMENT_HEAD',
      status: 'pending'
    });

    // Level 2: Procurement approval (for contracts > 100,000)
    if (value > 100000) {
      workflow.push({
        level: 2,
        approverRole: 'PROCUREMENT_MANAGER',
        status: 'pending'
      });
    }

    // Level 3: Finance approval (for contracts > 500,000)
    if (value > 500000) {
      workflow.push({
        level: 3,
        approverRole: 'FINANCE_MANAGER',
        status: 'pending'
      });
    }

    // Level 4: Executive approval (for contracts > 1,000,000)
    if (value > 1000000) {
      workflow.push({
        level: 4,
        approverRole: 'EXECUTIVE_DIRECTOR',
        status: 'pending'
      });
    }

    return workflow;
  }

  /**
   * Validate status transition
   * @param currentStatus - Current contract status
   * @param newStatus - New contract status
   * @returns Whether transition is valid
   */
  private isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions: Record<string, string[]> = {
      'draft': ['pending_approval', 'active', 'terminated'],
      'pending_approval': ['active', 'draft', 'terminated'],
      'active': ['suspended', 'terminated', 'renewed', 'expired'],
      'suspended': ['active', 'terminated'],
      'expired': ['renewed', 'terminated'],
      'renewed': ['active', 'terminated'],
      'terminated': [] // Terminal state
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}

// Create and export service instance
export const contractService = new ContractService();

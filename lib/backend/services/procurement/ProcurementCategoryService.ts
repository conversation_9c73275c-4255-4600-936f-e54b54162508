import { CrudService } from '@/lib/backend/services/base/CrudService';
import ProcurementCategory, { IProcurementCategory } from '@/models/procurement/ProcurementCategory';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';
import { AuditDeletionService, AuditDeletionContext, AuditDeletionResult } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

/**
 * Interface for category creation data
 */
export interface CreateCategoryData {
  name: string;
  code: string;
  description?: string;
  parentCategory?: string;
  budgetCategory?: string;
  costCenter?: string;
  approvalLimit?: number;
  requiredApprovers?: string[];
  autoApprovalThreshold?: number;
  defaultSuppliers?: string[];
  restrictedSuppliers?: string[];
  requiresQuotation?: boolean;
  minimumQuotations?: number;
  leadTime?: number;
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  isRestricted?: boolean;
  complianceRequirements?: string[];
  requiredDocuments?: string[];
  qualityStandards?: string[];
  requiresInspection?: boolean;
  inspectionCriteria?: string[];
  requiresApprovalWorkflow?: boolean;
  budgetCheckRequired?: boolean;
  allowOverBudget?: boolean;
  overBudgetApprovalRequired?: boolean;
  tags?: string[];
  notes?: string;
  createdBy: string;
}

/**
 * Interface for category update data
 */
export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  updatedBy: string;
}

/**
 * Interface for category filters
 */
export interface CategoryFilters {
  name?: string;
  code?: string;
  parentCategory?: string;
  budgetCategory?: string;
  riskLevel?: string | string[];
  isActive?: boolean;
  isRestricted?: boolean;
  requiresQuotation?: boolean;
  requiresInspection?: boolean;
  tags?: string | string[];
  search?: string;
  level?: number;
  minApprovalLimit?: number;
  maxApprovalLimit?: number;
}

/**
 * Interface for category hierarchy
 */
export interface CategoryHierarchy {
  _id: string;
  name: string;
  code: string;
  description?: string;
  level: number;
  path: string;
  riskLevel: string;
  approvalLimit?: number;
  isActive: boolean;
  isRestricted: boolean;
  children: CategoryHierarchy[];
}

/**
 * Interface for approval validation result
 */
export interface ApprovalValidation {
  canApprove: boolean;
  reason?: string;
  requiredApprovers: string[];
  approvalLimit?: number;
  exceedsLimit: boolean;
  requiresEscalation: boolean;
}

/**
 * Interface for budget validation result
 */
export interface BudgetValidation {
  isValid: boolean;
  availableAmount?: number;
  requestedAmount: number;
  exceedsAvailable: boolean;
  requiresApproval: boolean;
  reason?: string;
}

/**
 * Service for managing procurement categories
 */
export class ProcurementCategoryService extends CrudService<IProcurementCategory> {
  constructor() {
    super(ProcurementCategory, 'ProcurementCategory');
  }

  /**
   * Create a new procurement category
   * @param data - Category creation data
   * @returns Created category
   */
  async createCategory(data: CreateCategoryData): Promise<IProcurementCategory> {
    try {
      logger.info('Creating new procurement category', LogCategory.PROCUREMENT, { data });
      
      // Validate parent category if provided
      if (data.parentCategory) {
        const parent = await this.findById(data.parentCategory);
        if (!parent) {
          throw new Error('Parent category not found');
        }
        if (!parent.isActive) {
          throw new Error('Parent category is not active');
        }
        if (parent.level >= 9) {
          throw new Error('Maximum category depth exceeded');
        }
      }
      
      // Check for duplicate code
      const existingCategory = await ProcurementCategory.findOne({ 
        code: data.code.toUpperCase() 
      });
      if (existingCategory) {
        throw new Error('Category code already exists');
      }
      
      // Calculate path and level manually
      let level = 0;
      let path = data.name;

      if (data.parentCategory) {
        const parent = await this.findById(data.parentCategory);
        if (!parent) {
          throw new Error('Parent category not found');
        }
        level = parent.level + 1;
        path = `${parent.path}/${data.name}`;
      }

      // Create category
      const categoryData = {
        ...data,
        code: data.code.toUpperCase(),
        parentCategory: data.parentCategory ? new mongoose.Types.ObjectId(data.parentCategory) : undefined,
        budgetCategory: data.budgetCategory ? new mongoose.Types.ObjectId(data.budgetCategory) : undefined,
        costCenter: data.costCenter ? new mongoose.Types.ObjectId(data.costCenter) : undefined,
        requiredApprovers: data.requiredApprovers?.map(id => new mongoose.Types.ObjectId(id)) || [],
        defaultSuppliers: data.defaultSuppliers?.map(id => new mongoose.Types.ObjectId(id)) || [],
        restrictedSuppliers: data.restrictedSuppliers?.map(id => new mongoose.Types.ObjectId(id)) || [],
        createdBy: new mongoose.Types.ObjectId(data.createdBy),
        isActive: true,
        level,
        path
      };

      // Remove undefined values
      Object.keys(categoryData).forEach(key => {
        if (categoryData[key as keyof typeof categoryData] === undefined) {
          delete categoryData[key as keyof typeof categoryData];
        }
      });

      logger.info('Creating category with data', LogCategory.PROCUREMENT, { categoryData });

      const category = await this.create(categoryData);
      
      logger.info('Procurement category created successfully', LogCategory.PROCUREMENT, { 
        categoryId: category._id,
        code: category.code,
        name: category.name
      });
      
      return category;
    } catch (error) {
      logger.error('Error creating procurement category', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update a procurement category
   * @param id - Category ID
   * @param data - Update data
   * @returns Updated category
   */
  async updateCategory(id: string, data: UpdateCategoryData): Promise<IProcurementCategory> {
    try {
      logger.info('Updating procurement category', LogCategory.PROCUREMENT, { id, data });
      
      const category = await this.findById(id);
      if (!category) {
        throw new Error('Category not found');
      }
      
      // Validate parent category change
      if (data.parentCategory && data.parentCategory !== category.parentCategory?.toString()) {
        const newParent = await this.findById(data.parentCategory);
        if (!newParent) {
          throw new Error('New parent category not found');
        }
        if (!newParent.isActive) {
          throw new Error('New parent category is not active');
        }
        
        // Check for circular reference
        const isDescendant = await newParent.isDescendantOf(id);
        if (isDescendant) {
          throw new Error('Cannot set a descendant category as parent');
        }
      }
      
      // Check for duplicate code if code is being changed
      if (data.code && data.code.toUpperCase() !== category.code) {
        const existingCategory = await ProcurementCategory.findOne({ 
          code: data.code.toUpperCase(),
          _id: { $ne: id }
        });
        if (existingCategory) {
          throw new Error('Category code already exists');
        }
      }
      
      // Calculate new path and level if parent category or name is changing
      let level = category.level;
      let path = category.path;

      if (data.parentCategory !== undefined || data.name) {
        const newName = data.name || category.name;

        if (data.parentCategory) {
          const parent = await this.findById(data.parentCategory);
          if (!parent) {
            throw new Error('Parent category not found');
          }
          level = parent.level + 1;
          path = `${parent.path}/${newName}`;
        } else if (data.parentCategory === null) {
          // Setting parent to null (making it a root category)
          level = 0;
          path = newName;
        } else if (data.name) {
          // Only name is changing, update path
          if (category.parentCategory) {
            const parent = await this.findById(category.parentCategory.toString());
            if (parent) {
              path = `${parent.path}/${newName}`;
            }
          } else {
            path = newName;
          }
        }
      }

      // Prepare update data
      const updateData = {
        ...data,
        code: data.code ? data.code.toUpperCase() : undefined,
        parentCategory: data.parentCategory ? new mongoose.Types.ObjectId(data.parentCategory) : undefined,
        budgetCategory: data.budgetCategory ? new mongoose.Types.ObjectId(data.budgetCategory) : undefined,
        costCenter: data.costCenter ? new mongoose.Types.ObjectId(data.costCenter) : undefined,
        requiredApprovers: data.requiredApprovers?.map(id => new mongoose.Types.ObjectId(id)),
        defaultSuppliers: data.defaultSuppliers?.map(id => new mongoose.Types.ObjectId(id)),
        restrictedSuppliers: data.restrictedSuppliers?.map(id => new mongoose.Types.ObjectId(id)),
        updatedBy: new mongoose.Types.ObjectId(data.updatedBy),
        level,
        path
      };
      
      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData];
        }
      });
      
      const updatedCategory = await this.updateById(id, updateData);

      if (!updatedCategory) {
        throw new Error(`Category with ID ${id} not found`);
      }

      logger.info('Procurement category updated successfully', LogCategory.PROCUREMENT, {
        categoryId: id,
        changes: Object.keys(updateData)
      });

      return updatedCategory;
    } catch (error) {
      logger.error('Error updating procurement category', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get category hierarchy
   * @param rootCategoryId - Optional root category ID to get hierarchy from
   * @returns Category hierarchy
   */
  async getCategoryHierarchy(rootCategoryId?: string): Promise<CategoryHierarchy[]> {
    try {
      logger.info('Getting category hierarchy', LogCategory.PROCUREMENT, { rootCategoryId });
      
      let filter: any = { isActive: true };
      
      if (rootCategoryId) {
        const rootCategory = await this.findById(rootCategoryId);
        if (!rootCategory) {
          throw new Error('Root category not found');
        }
        
        // Get all descendants of the root category
        filter = {
          $or: [
            { _id: new mongoose.Types.ObjectId(rootCategoryId) },
            { path: { $regex: `^${rootCategory.path}/` } }
          ],
          isActive: true
        };
      }
      
      const categories = await ProcurementCategory.find(filter)
        .sort({ path: 1 })
        .lean();
      
      return this.buildHierarchy(categories, rootCategoryId);
    } catch (error) {
      logger.error('Error getting category hierarchy', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get categories by budget
   * @param budgetId - Budget ID
   * @returns Categories linked to the budget
   */
  async getCategoriesByBudget(budgetId: string): Promise<IProcurementCategory[]> {
    try {
      logger.info('Getting categories by budget', LogCategory.PROCUREMENT, { budgetId });
      
      const categories = await ProcurementCategory.find({
        budgetCategory: new mongoose.Types.ObjectId(budgetId),
        isActive: true
      })
      .populate('parentCategory', 'name code path')
      .populate('budgetCategory', 'name type')
      .sort({ path: 1 });
      
      return categories;
    } catch (error) {
      logger.error('Error getting categories by budget', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Validate approval limits for a category and amount
   * @param categoryId - Category ID
   * @param amount - Amount to validate
   * @param userId - User ID requesting approval
   * @returns Approval validation result
   */
  async validateApprovalLimits(
    categoryId: string, 
    amount: number, 
    userId: string
  ): Promise<ApprovalValidation> {
    try {
      logger.info('Validating approval limits', LogCategory.PROCUREMENT, { 
        categoryId, 
        amount, 
        userId 
      });
      
      const category = await this.findById(categoryId);
      if (!category) {
        throw new Error('Category not found');
      }
      
      const result: ApprovalValidation = {
        canApprove: false,
        requiredApprovers: category.requiredApprovers.map(id => id.toString()),
        approvalLimit: category.approvalLimit,
        exceedsLimit: false,
        requiresEscalation: false
      };
      
      // Check if user can approve
      const canUserApprove = category.canApprove(amount, userId);
      
      // Check if amount exceeds approval limit
      if (category.approvalLimit && amount > category.approvalLimit) {
        result.exceedsLimit = true;
        result.requiresEscalation = true;
        result.reason = `Amount ${amount} exceeds category approval limit of ${category.approvalLimit}`;
      }
      
      // Check auto-approval threshold
      if (category.autoApprovalThreshold && amount <= category.autoApprovalThreshold) {
        result.canApprove = true;
        result.reason = 'Amount is within auto-approval threshold';
      } else if (canUserApprove && !result.exceedsLimit) {
        result.canApprove = true;
        result.reason = 'User has approval authority for this amount';
      } else if (!canUserApprove) {
        result.reason = 'User is not authorized to approve for this category';
      }
      
      return result;
    } catch (error) {
      logger.error('Error validating approval limits', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Search categories with filters
   * @param filters - Search filters
   * @param page - Page number
   * @param limit - Items per page
   * @returns Search results with pagination
   */
  async searchCategories(
    filters: CategoryFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{
    categories: IProcurementCategory[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      logger.info('Searching categories', LogCategory.PROCUREMENT, { filters, page, limit });
      
      const query: any = {};
      
      // Build query based on filters
      if (filters.name) {
        query.name = { $regex: filters.name, $options: 'i' };
      }
      
      if (filters.code) {
        query.code = { $regex: filters.code.toUpperCase(), $options: 'i' };
      }
      
      if (filters.parentCategory) {
        query.parentCategory = new mongoose.Types.ObjectId(filters.parentCategory);
      }
      
      if (filters.budgetCategory) {
        query.budgetCategory = new mongoose.Types.ObjectId(filters.budgetCategory);
      }
      
      if (filters.riskLevel) {
        if (Array.isArray(filters.riskLevel)) {
          query.riskLevel = { $in: filters.riskLevel };
        } else {
          query.riskLevel = filters.riskLevel;
        }
      }
      
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }
      
      if (filters.isRestricted !== undefined) {
        query.isRestricted = filters.isRestricted;
      }
      
      if (filters.requiresQuotation !== undefined) {
        query.requiresQuotation = filters.requiresQuotation;
      }
      
      if (filters.requiresInspection !== undefined) {
        query.requiresInspection = filters.requiresInspection;
      }
      
      if (filters.tags) {
        if (Array.isArray(filters.tags)) {
          query.tags = { $in: filters.tags };
        } else {
          query.tags = filters.tags;
        }
      }
      
      if (filters.level !== undefined) {
        query.level = filters.level;
      }
      
      if (filters.minApprovalLimit !== undefined || filters.maxApprovalLimit !== undefined) {
        query.approvalLimit = {};
        if (filters.minApprovalLimit !== undefined) {
          query.approvalLimit.$gte = filters.minApprovalLimit;
        }
        if (filters.maxApprovalLimit !== undefined) {
          query.approvalLimit.$lte = filters.maxApprovalLimit;
        }
      }
      
      // Text search
      if (filters.search) {
        query.$text = { $search: filters.search };
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [categories, total] = await Promise.all([
        ProcurementCategory.find(query)
          .populate('parentCategory', 'name code path')
          .populate('budgetCategory', 'name type')
          .populate('requiredApprovers', 'name email')
          .sort(filters.search ? { score: { $meta: 'textScore' } } : { path: 1 })
          .skip(skip)
          .limit(limit),
        ProcurementCategory.countDocuments(query)
      ]);
      
      return {
        categories,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error searching categories', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Build category hierarchy from flat list
   * @param categories - Flat list of categories
   * @param rootId - Optional root category ID
   * @returns Hierarchical structure
   */
  private buildHierarchy(categories: any[], rootId?: string): CategoryHierarchy[] {
    const categoryMap = new Map();
    const rootCategories: CategoryHierarchy[] = [];
    
    // Create map of categories
    categories.forEach(category => {
      categoryMap.set(category._id.toString(), {
        _id: category._id.toString(),
        name: category.name,
        code: category.code,
        description: category.description,
        level: category.level,
        path: category.path,
        riskLevel: category.riskLevel,
        approvalLimit: category.approvalLimit,
        isActive: category.isActive,
        isRestricted: category.isRestricted,
        children: []
      });
    });
    
    // Build hierarchy
    categories.forEach(category => {
      const categoryObj = categoryMap.get(category._id.toString());
      
      if (category.parentCategory) {
        const parent = categoryMap.get(category.parentCategory.toString());
        if (parent) {
          parent.children.push(categoryObj);
        }
      } else if (!rootId) {
        rootCategories.push(categoryObj);
      }
    });
    
    // If rootId is specified, return only that subtree
    if (rootId) {
      const rootCategory = categoryMap.get(rootId);
      return rootCategory ? [rootCategory] : [];
    }
    
    return rootCategories;
  }

  /**
   * Delete a category with audit trail (government compliant)
   */
  async deleteCategory(
    id: string,
    auditContext: AuditDeletionContext
  ): Promise<AuditDeletionResult> {
    try {
      logger.info('Initiating audit-compliant category deletion', LogCategory.PROCUREMENT, {
        categoryId: id,
        userId: auditContext.userInfo.id,
        reason: auditContext.deletionReason
      });

      // Custom validation logic will be handled in beforeDelete hook

      // Perform audit-compliant deletion
      const result = await AuditDeletionService.performAuditDeletion(
        ProcurementCategory,
        [id],
        auditContext,
        {
          populateFields: ['parentCategory', 'defaultSuppliers', 'restrictedSuppliers'],
          beforeDelete: async (categories: IProcurementCategory[]) => {
            // Custom validation logic
            for (const category of categories) {
              // Check if category has active inventory items
              const db = mongoose.connection.db;
              if (db) {
                const activeItems = await db
                  .collection('procurement_inventories')
                  .countDocuments({
                    category: category._id,
                    status: { $in: ['active', 'in_stock', 'low_stock'] }
                  });

                if (activeItems > 0) {
                  throw new Error(`Cannot delete category "${category.name}" with ${activeItems} active inventory items`);
                }
              }

              // Check if category has child categories
              const childCount = await ProcurementCategory.countDocuments({
                parentCategory: category._id
              });

              if (childCount > 0) {
                throw new Error(`Cannot delete category "${category.name}" with ${childCount} child categories`);
              }

              // Log additional context for procurement categories
              logger.info('Deleting procurement category', LogCategory.PROCUREMENT, {
                categoryId: category._id,
                name: category.name,
                code: category.code,
                level: category.level,
                hasParent: !!category.parentCategory,
                approvalLimit: category.approvalLimit
              });
            }
          },
          afterDelete: async (deletedCategories: IProcurementCategory[], auditRecords: any[]) => {
            // Update any references in other systems if needed
            logger.info('Category deletion completed', LogCategory.PROCUREMENT, {
              deletedCount: deletedCategories.length,
              auditRecordsCreated: auditRecords.length
            });
          }
        }
      );

      return result;
    } catch (error) {
      logger.error('Error in audit-compliant category deletion', LogCategory.PROCUREMENT, error);

      const structuredError = errorService.createError(
        ErrorType.BUSINESS_LOGIC,
        'PROCUREMENT_CATEGORY_DELETE_FAILED',
        error instanceof Error ? error.message : 'Failed to delete procurement category',
        'Unable to delete the procurement category. This may be due to business rules or system constraints.',
        {
          userId: auditContext.userInfo.id,
          endpoint: '/api/procurement/categories',
          method: 'DELETE',
          additionalData: { categoryId: id, deletionReason: auditContext.deletionReason }
        },
        ErrorSeverity.MEDIUM,
        error instanceof Error ? error.stack : undefined,
        [
          'Ensure the category has no active inventory items',
          'Check that the category has no child categories',
          'Verify you have the necessary permissions',
          'Contact an administrator if the issue persists'
        ]
      );

      throw new Error(structuredError.userMessage);
    }
  }

  /**
   * Bulk delete categories with audit trail
   */
  async bulkDeleteCategories(
    ids: string[],
    auditContext: Omit<AuditDeletionContext, 'deletionType'>
  ): Promise<AuditDeletionResult> {
    try {
      logger.info('Initiating bulk audit-compliant category deletion', LogCategory.PROCUREMENT, {
        categoryIds: ids,
        count: ids.length,
        userId: auditContext.userInfo.id
      });

      const bulkAuditContext: AuditDeletionContext = {
        ...auditContext,
        deletionType: 'bulk'
      };

      // Custom validation logic will be handled in beforeDelete hook

      const result = await AuditDeletionService.performAuditDeletion(
        ProcurementCategory,
        ids,
        bulkAuditContext,
        {
          populateFields: ['parentCategory', 'defaultSuppliers', 'restrictedSuppliers'],
          beforeDelete: async (categories: IProcurementCategory[]) => {
            // Enhanced validation for bulk operations
            for (const category of categories) {
              // Check if category has active inventory items
              const db = mongoose.connection.db;
              if (db) {
                const activeItems = await db
                  .collection('procurement_inventories')
                  .countDocuments({
                    category: category._id,
                    status: { $in: ['active', 'in_stock', 'low_stock'] }
                  });

                if (activeItems > 0) {
                  throw new Error(`Category "${category.name}" has ${activeItems} active inventory items`);
                }
              }

              // Check if category has child categories
              const childCount = await ProcurementCategory.countDocuments({
                parentCategory: category._id
              });

              if (childCount > 0) {
                throw new Error(`Category "${category.name}" has ${childCount} child categories`);
              }

              // Check if any of the categories to be deleted are parents of others in the list
              const isParentOfOthers = await ProcurementCategory.countDocuments({
                _id: { $in: ids.map(id => new mongoose.Types.ObjectId(id)) },
                parentCategory: category._id
              });

              if (isParentOfOthers > 0) {
                throw new Error(`Category "${category.name}" is a parent of other categories in the deletion list`);
              }
            }

            logger.info('Bulk deleting procurement categories', LogCategory.PROCUREMENT, {
              categories: categories.map(cat => ({
                id: cat._id,
                name: cat.name,
                code: cat.code,
                level: cat.level
              }))
            });
          }
        }
      );

      return result;
    } catch (error) {
      logger.error('Error in bulk audit-compliant category deletion', LogCategory.PROCUREMENT, error);

      const structuredError = errorService.createError(
        ErrorType.BUSINESS_LOGIC,
        'PROCUREMENT_CATEGORY_BULK_DELETE_FAILED',
        error instanceof Error ? error.message : 'Failed to bulk delete procurement categories',
        'Unable to delete the selected procurement categories. Some categories may have dependencies that prevent deletion.',
        {
          userId: auditContext.userInfo.id,
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'DELETE',
          additionalData: { categoryIds: ids, count: ids.length }
        },
        ErrorSeverity.MEDIUM,
        error instanceof Error ? error.stack : undefined,
        [
          'Review the categories selected for deletion',
          'Ensure no categories have active inventory items',
          'Check for parent-child relationships in the selection',
          'Try deleting categories individually to identify specific issues'
        ]
      );

      throw new Error(structuredError.userMessage);
    }
  }
}

// Export singleton instance
export const procurementCategoryService = new ProcurementCategoryService();

// lib/backend/services/procurement/RequisitionService.ts
import mongoose, { Document } from 'mongoose';
import { CrudService } from '../base/CrudService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Define types for query options
interface QueryOptions {
  status?: IRequisition['status'] | IRequisition['status'][];
  startDate?: Date;
  endDate?: Date;
  sort?: Record<string, 1 | -1>;
  limit?: number;
  page?: number;
}



interface PendingRequisitionsOptions {
  departmentId?: string;
  priority?: IRequisition['priority'] | IRequisition['priority'][];
  sort?: Record<string, 1 | -1>;
  limit?: number;
  page?: number;
}

interface ApprovedRequisitionsOptions {
  departmentId?: string;
  sort?: Record<string, 1 | -1>;
  limit?: number;
  page?: number;
}

interface StatusUpdateData {
  rejectionReason?: string;
  purchaseOrderId?: string;
  notes?: string;
}

interface RequisitionUpdateData {
  status?: IRequisition['status'];
  rejectionReason?: string;
  purchaseOrderId?: mongoose.Types.ObjectId;
  notes?: string;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
}

interface PaginatedResult<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage: number | null;
  nextPage: number | null;
}

interface MongoFilter {
  [key: string]: any;
}

// Define the Requisition interface
export interface IRequisition extends Document {
  requisitionId: string;
  title: string;
  description?: string;
  requestedBy: mongoose.Types.ObjectId;
  departmentId?: mongoose.Types.ObjectId;
  date: Date;
  requiredDate?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'ordered' | 'completed' | 'cancelled';
  items: Array<{
    name: string;
    description?: string;
    quantity: number;
    unit: string;
    estimatedUnitPrice?: number;
    totalPrice?: number;
    category?: string;
    notes?: string;
  }>;
  totalAmount?: number;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  rejectionReason?: string;
  purchaseOrderId?: mongoose.Types.ObjectId;
  budgetId?: mongoose.Types.ObjectId;
  notes?: string;
  attachments?: string[];
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Create the Requisition schema
const requisitionSchema = new mongoose.Schema({
  requisitionId: { type: String, required: true, unique: true },
  requisitionNumber: { type: String, unique: true, sparse: true, default: null }, // Add this field for compatibility
  title: { type: String, required: true },
  description: String,
  requestedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: true
  },
  departmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department'
  },
  date: { type: Date, required: true, default: Date.now },
  requiredDate: Date,
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'submitted', 'approved', 'rejected', 'ordered', 'completed', 'cancelled'],
    default: 'draft',
    required: true
  },
  items: [{
    name: { type: String, required: true },
    description: String,
    quantity: { type: Number, required: true },
    unit: { type: String, required: true },
    estimatedUnitPrice: Number,
    totalPrice: Number,
    category: String,
    notes: String
  }],
  totalAmount: Number,
  approvedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User'
  },
  approvalDate: Date,
  rejectionReason: String,
  purchaseOrderId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'PurchaseOrder'
  },
  budgetId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Budget'
  },
  notes: String,
  attachments: [String],
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  }
}, { timestamps: true });



// Create the Requisition model
const Requisition = mongoose.models.Requisition || mongoose.model<IRequisition>('Requisition', requisitionSchema);

/**
 * Service for managing requisitions
 */
export class RequisitionService extends CrudService<IRequisition> {
  constructor() {
    super(Requisition, 'Requisition');
  }

  /**
   * Create a new requisition
   * @param data - Requisition data
   * @returns Created requisition
   */
  async createRequisition(data: Partial<IRequisition>): Promise<IRequisition> {
    try {
      logger.info('Creating new requisition', LogCategory.PROCUREMENT, { data });

      // Sanitize data - handle empty strings for ObjectId fields
      const sanitizedData = { ...data };

      // Handle empty budget ID - cast to unknown first for type safety
      const budgetId = sanitizedData.budgetId as unknown;
      if (budgetId === '' || budgetId === 'no-budget') {
        delete sanitizedData.budgetId;
      }

      // Handle empty department ID - cast to unknown first for type safety
      const departmentId = sanitizedData.departmentId as unknown;
      if (departmentId === '') {
        delete sanitizedData.departmentId;
      }

      // Generate requisition ID if not provided
      if (!sanitizedData.requisitionId) {
        sanitizedData.requisitionId = await this.generateRequisitionId();
      }

      // Generate requisition number if not provided
      if (!(sanitizedData as any).requisitionNumber) {
        (sanitizedData as any).requisitionNumber = await this.generateRequisitionNumber();
      }

      // Calculate total amount if items are provided
      if (sanitizedData.items && sanitizedData.items.length > 0) {
        let totalAmount = 0;

        // Calculate total price for each item and overall total
        sanitizedData.items = sanitizedData.items.map(item => {
          const totalPrice = (item.estimatedUnitPrice || 0) * item.quantity;
          totalAmount += totalPrice;

          return {
            ...item,
            totalPrice
          };
        });

        sanitizedData.totalAmount = totalAmount;
      }

      // Create requisition
      const requisition = await this.create(sanitizedData);

      return requisition;
    } catch (error) {
      logger.error('Error creating requisition', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update requisition status
   * @param id - Requisition ID
   * @param status - New status
   * @param userId - User ID performing the update
   * @param data - Additional data
   * @returns Updated requisition
   */
  async updateStatus(
    id: string,
    status: IRequisition['status'],
    userId: string,
    data: StatusUpdateData = {}
  ): Promise<IRequisition | null> {
    try {
      logger.info('Updating requisition status', LogCategory.PROCUREMENT, { id, status, userId });

      const requisition = await this.findById(id);

      if (!requisition) {
        logger.warn('Requisition not found', LogCategory.PROCUREMENT, { id });
        return null;
      }

      // Validate status transition
      this.validateStatusTransition(requisition.status, status);

      // Update status
      const updateData: RequisitionUpdateData = {
        status,
        rejectionReason: data.rejectionReason,
        notes: data.notes
      };

      // Add approval info if status is approved
      if (status === 'approved') {
        updateData.approvedBy = new mongoose.Types.ObjectId(userId);
        updateData.approvalDate = new Date();
      }

      // Add purchase order ID if provided
      if (data.purchaseOrderId) {
        updateData.purchaseOrderId = new mongoose.Types.ObjectId(data.purchaseOrderId);
      }

      // Update requisition
      const updatedRequisition = await this.updateById(id, updateData);

      return updatedRequisition;
    } catch (error) {
      logger.error('Error updating requisition status', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update requisition details
   * @param id - Requisition ID
   * @param data - Update data
   * @returns Updated requisition
   */
  async updateRequisition(id: string, data: Partial<IRequisition>): Promise<IRequisition | null> {
    try {
      logger.info('Updating requisition', LogCategory.PROCUREMENT, { id, data });

      // Sanitize data - handle empty strings for ObjectId fields
      const sanitizedData = { ...data };

      // Handle empty budget ID
      const budgetId = sanitizedData.budgetId as unknown;
      if (budgetId === '' || budgetId === 'no-budget') {
        delete sanitizedData.budgetId;
      }

      // Handle empty department ID
      const departmentId = sanitizedData.departmentId as unknown;
      if (departmentId === '') {
        delete sanitizedData.departmentId;
      }

      // Calculate total amount if items are provided
      if (sanitizedData.items && sanitizedData.items.length > 0) {
        let totalAmount = 0;

        // Calculate total price for each item and overall total
        sanitizedData.items = sanitizedData.items.map(item => {
          const totalPrice = (item.estimatedUnitPrice || 0) * item.quantity;
          totalAmount += totalPrice;

          return {
            ...item,
            totalPrice
          };
        });

        sanitizedData.totalAmount = totalAmount;
      }

      // Update requisition
      const updatedRequisition = await this.updateById(id, sanitizedData);

      return updatedRequisition;
    } catch (error) {
      logger.error('Error updating requisition', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Delete requisition
   * @param id - Requisition ID
   * @returns Deleted requisition
   */
  async deleteRequisition(id: string): Promise<IRequisition | null> {
    try {
      logger.info('Deleting requisition', LogCategory.PROCUREMENT, { id });

      const deletedRequisition = await this.deleteById(id);

      return deletedRequisition;
    } catch (error) {
      logger.error('Error deleting requisition', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update requisition details
   * @param id - Requisition ID
   * @param data - Update data
   * @returns Updated requisition
   */
  async updateRequisition(id: string, data: Partial<IRequisition>): Promise<IRequisition | null> {
    try {
      logger.info('Updating requisition', LogCategory.PROCUREMENT, { id, data });

      // Sanitize data - handle empty strings for ObjectId fields
      const sanitizedData = { ...data };

      // Handle empty budget ID
      const budgetId = sanitizedData.budgetId as unknown;
      if (budgetId === '' || budgetId === 'no-budget') {
        delete sanitizedData.budgetId;
      }

      // Handle empty department ID
      const departmentId = sanitizedData.departmentId as unknown;
      if (departmentId === '') {
        delete sanitizedData.departmentId;
      }

      // Calculate total amount if items are provided
      if (sanitizedData.items && sanitizedData.items.length > 0) {
        let totalAmount = 0;

        // Calculate total price for each item and overall total
        sanitizedData.items = sanitizedData.items.map(item => {
          const totalPrice = (item.estimatedUnitPrice || 0) * item.quantity;
          totalAmount += totalPrice;

          return {
            ...item,
            totalPrice
          };
        });

        sanitizedData.totalAmount = totalAmount;
      }

      // Update requisition
      const updatedRequisition = await this.updateById(id, sanitizedData);

      return updatedRequisition;
    } catch (error) {
      logger.error('Error updating requisition', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get requisitions by department
   * @param departmentId - Department ID
   * @param options - Query options
   * @returns Requisitions
   */
  async getByDepartment(
    departmentId: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResult<IRequisition>> {
    try {
      logger.info('Getting requisitions by department', LogCategory.PROCUREMENT, { departmentId, options });
      
      // Build filter
      const filter: MongoFilter = { departmentId: new mongoose.Types.ObjectId(departmentId) };

      // Add status filter
      if (options.status) {
        filter.status = Array.isArray(options.status) ? { $in: options.status } : options.status;
      }

      // Add date range filter
      if (options.startDate || options.endDate) {
        filter.date = {};

        if (options.startDate) {
          filter.date.$gte = options.startDate;
        }

        if (options.endDate) {
          filter.date.$lte = options.endDate;
        }
      }
      
      // Get paginated results
      const page = options.page || 1;
      const limit = options.limit || 10;
      const sort = options.sort || { date: -1 };
      
      return await this.paginate(filter, page, limit, sort, ['requestedBy', 'departmentId', 'approvedBy', 'purchaseOrderId', 'budgetId', 'createdBy']);
    } catch (error) {
      logger.error('Error getting requisitions by department', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get requisitions by employee
   * @param employeeId - Employee ID
   * @param options - Query options
   * @returns Requisitions
   */
  async getByEmployee(
    employeeId: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResult<IRequisition>> {
    try {
      logger.info('Getting requisitions by employee', LogCategory.PROCUREMENT, { employeeId, options });
      
      // Build filter
      const filter: MongoFilter = { requestedBy: new mongoose.Types.ObjectId(employeeId) };
      
      // Add status filter
      if (options.status) {
        filter.status = Array.isArray(options.status) ? { $in: options.status } : options.status;
      }
      
      // Add date range filter
      if (options.startDate || options.endDate) {
        filter.date = {};
        
        if (options.startDate) {
          filter.date.$gte = options.startDate;
        }
        
        if (options.endDate) {
          filter.date.$lte = options.endDate;
        }
      }
      
      // Get paginated results
      const page = options.page || 1;
      const limit = options.limit || 10;
      const sort = options.sort || { date: -1 };
      
      return await this.paginate(filter, page, limit, sort, ['requestedBy', 'departmentId', 'approvedBy', 'purchaseOrderId', 'budgetId', 'createdBy']);
    } catch (error) {
      logger.error('Error getting requisitions by employee', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get pending requisitions
   * @param options - Query options
   * @returns Pending requisitions
   */
  async getPendingRequisitions(
    options: PendingRequisitionsOptions = {}
  ): Promise<PaginatedResult<IRequisition>> {
    try {
      logger.info('Getting pending requisitions', LogCategory.PROCUREMENT, { options });
      
      // Build filter
      const filter: MongoFilter = { status: 'submitted' };
      
      // Add department filter
      if (options.departmentId) {
        filter.departmentId = new mongoose.Types.ObjectId(options.departmentId);
      }
      
      // Add priority filter
      if (options.priority) {
        filter.priority = Array.isArray(options.priority) ? { $in: options.priority } : options.priority;
      }
      
      // Get paginated results
      const page = options.page || 1;
      const limit = options.limit || 10;
      const sort = options.sort || { priority: -1, date: 1 };
      
      return await this.paginate(filter, page, limit, sort, ['requestedBy', 'departmentId', 'createdBy']);
    } catch (error) {
      logger.error('Error getting pending requisitions', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get approved requisitions
   * @param options - Query options
   * @returns Approved requisitions
   */
  async getApprovedRequisitions(
    options: ApprovedRequisitionsOptions = {}
  ): Promise<PaginatedResult<IRequisition>> {
    try {
      logger.info('Getting approved requisitions', LogCategory.PROCUREMENT, { options });
      
      // Build filter
      const filter: MongoFilter = { status: 'approved', purchaseOrderId: { $exists: false } };
      
      // Add department filter
      if (options.departmentId) {
        filter.departmentId = new mongoose.Types.ObjectId(options.departmentId);
      }
      
      // Get paginated results
      const page = options.page || 1;
      const limit = options.limit || 10;
      const sort = options.sort || { approvalDate: 1 };
      
      return await this.paginate(filter, page, limit, sort, ['requestedBy', 'departmentId', 'approvedBy', 'createdBy']);
    } catch (error) {
      logger.error('Error getting approved requisitions', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get requisition details
   * @param id - Requisition ID
   * @returns Requisition with populated references
   */
  async getRequisitionDetails(id: string): Promise<IRequisition | null> {
    try {
      logger.info('Getting requisition details', LogCategory.PROCUREMENT, { id });
      
      return await this.findById(id, ['requestedBy', 'departmentId', 'approvedBy', 'purchaseOrderId', 'budgetId', 'createdBy']);
    } catch (error) {
      logger.error('Error getting requisition details', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Generate requisition ID
   * @returns Generated requisition ID
   */
  private async generateRequisitionId(): Promise<string> {
    try {
      // Get current date
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');

      // Get count of requisitions for current month
      const count = await Requisition.countDocuments({
        requisitionId: { $regex: `REQ-${year}${month}-` }
      });

      // Generate requisition ID
      const requisitionId = `REQ-${year}${month}-${(count + 1).toString().padStart(4, '0')}`;

      return requisitionId;
    } catch (error) {
      logger.error('Error generating requisition ID', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Generate requisition number
   * @returns Generated requisition number
   */
  private async generateRequisitionNumber(): Promise<string> {
    try {
      // Get current date
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');

      // Get count of requisitions for current month
      const count = await Requisition.countDocuments({
        requisitionNumber: new RegExp(`^REQ-${year}${month}-`)
      });

      // Generate requisition number
      const requisitionNumber = `REQ-${year}${month}-${String(count + 1).padStart(4, '0')}`;

      logger.info('Generated requisition number', LogCategory.PROCUREMENT, {
        requisitionNumber,
        count: count + 1
      });

      return requisitionNumber;
    } catch (error) {
      logger.error('Error generating requisition number', LogCategory.PROCUREMENT, error);
      // Fallback to timestamp-based number
      const fallbackNumber = `REQ-${Date.now()}`;

      logger.warn('Using fallback requisition number', LogCategory.PROCUREMENT, {
        requisitionNumber: fallbackNumber,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return fallbackNumber;
    }
  }

  /**
   * Validate status transition
   * @param currentStatus - Current status
   * @param newStatus - New status
   * @throws Error if transition is invalid
   */
  private validateStatusTransition(currentStatus: IRequisition['status'], newStatus: IRequisition['status']): void {
    // Define valid status transitions
    const validTransitions: Record<IRequisition['status'], IRequisition['status'][]> = {
      'draft': ['submitted', 'cancelled'],
      'submitted': ['approved', 'rejected', 'cancelled'],
      'approved': ['ordered', 'cancelled'],
      'rejected': ['draft', 'cancelled'],
      'ordered': ['completed', 'cancelled'],
      'completed': [],
      'cancelled': []
    };
    
    // Check if transition is valid
    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}

export { Requisition };

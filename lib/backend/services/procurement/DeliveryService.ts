import mongoose from 'mongoose';
import { CrudService } from '../base/CrudService';
import Delivery, { IDelivery, IDeliveryItem, IQualityInspection } from '@/models/procurement/Delivery';
import PurchaseOrder from '@/models/procurement/PurchaseOrder';
import Supplier from '@/models/procurement/Supplier';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { format, addDays, isAfter, isBefore } from 'date-fns';

// Delivery creation data interface
export interface CreateDeliveryData {
  purchaseOrderId: string;
  supplierId: string;
  contractId?: string;
  expectedDate: Date;
  promisedDate?: Date;
  deliveryType?: 'full' | 'partial' | 'split' | 'emergency';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  trackingNumber?: string;
  carrier?: string;
  shippingMethod?: 'standard' | 'express' | 'overnight' | 'pickup' | 'direct';
  shippingCost?: number;
  deliveryAddress: {
    street: string;
    city: string;
    state?: string;
    postalCode?: string;
    country?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contactPerson: string;
  contactPhone: string;
  contactEmail?: string;
  items: Array<{
    purchaseOrderItemId: string;
    itemName: string;
    itemCode?: string;
    itemDescription?: string;
    quantityOrdered: number;
    quantityDelivered: number;
    unitPrice: number;
    condition?: 'good' | 'damaged' | 'defective' | 'incomplete' | 'expired';
    expiryDate?: Date;
    batchNumber?: string;
    serialNumbers?: string[];
    notes?: string;
  }>;
  currency?: string;
  notes?: string;
  tags?: string[];
  createdBy: string;
}

// Delivery update data interface
export interface UpdateDeliveryData extends Partial<CreateDeliveryData> {
  status?: 'scheduled' | 'in_transit' | 'delivered' | 'partially_delivered' | 'delayed' | 'cancelled' | 'returned';
  actualDate?: Date;
  updatedBy?: string;
}

// Goods receipt data interface
export interface GoodsReceiptData {
  receivedBy: string;
  receivedDate?: Date;
  goodsReceiptNumber?: string;
  items: Array<{
    purchaseOrderItemId: string;
    quantityAccepted: number;
    quantityRejected: number;
    rejectionReason?: string;
    condition: 'good' | 'damaged' | 'defective' | 'incomplete' | 'expired';
    notes?: string;
  }>;
  packingList?: boolean;
  invoice?: boolean;
  deliveryNote?: boolean;
  qualityCertificates?: boolean;
  customsDocuments?: boolean;
  notes?: string;
}

// Quality inspection data interface
export interface QualityInspectionData {
  inspectedBy: string;
  inspectionDate?: Date;
  inspectionType: 'visual' | 'functional' | 'compliance' | 'full';
  overallRating: 'excellent' | 'good' | 'fair' | 'poor' | 'failed';
  criteria: Array<{
    criterion: string;
    rating: 'pass' | 'fail' | 'conditional';
    notes?: string;
  }>;
  defectsFound?: Array<{
    type: 'cosmetic' | 'functional' | 'safety' | 'compliance';
    severity: 'minor' | 'major' | 'critical';
    description: string;
    quantity: number;
  }>;
  recommendations?: string[];
  certificatesRequired?: boolean;
  certificatesReceived?: boolean;
  inspectionNotes: string;
}

// Delivery filters interface
export interface DeliveryFilters {
  supplierId?: string;
  purchaseOrderId?: string;
  contractId?: string;
  status?: string;
  priority?: string;
  expectedDate?: { from?: Date; to?: Date };
  actualDate?: { from?: Date; to?: Date };
  deliveryType?: string;
  trackingNumber?: string;
  carrier?: string;
  overdue?: boolean;
  pendingReceipt?: boolean;
  hasIssues?: boolean;
  search?: string;
}

// Delivery report interface
export interface DeliveryReport {
  totalDeliveries: number;
  onTimeDeliveries: number;
  lateDeliveries: number;
  pendingDeliveries: number;
  averageDeliveryTime: number;
  supplierPerformance: Array<{
    supplierId: string;
    supplierName: string;
    totalDeliveries: number;
    onTimeRate: number;
    averageDeliveryTime: number;
    qualityScore: number;
  }>;
  issuesSummary: Array<{
    type: string;
    count: number;
    severity: string;
  }>;
}

/**
 * Delivery Service for managing procurement deliveries
 */
export class DeliveryService extends CrudService<IDelivery> {
  constructor() {
    super(Delivery, 'Delivery');
  }

  /**
   * Create a new delivery
   * @param deliveryData - Delivery creation data
   * @returns Created delivery
   */
  async createDelivery(deliveryData: CreateDeliveryData): Promise<IDelivery> {
    try {
      await connectToDatabase();
      logger.info('Creating new delivery', LogCategory.PROCUREMENT, { 
        purchaseOrderId: deliveryData.purchaseOrderId,
        supplierId: deliveryData.supplierId 
      });

      // Validate purchase order exists
      const purchaseOrder = await PurchaseOrder.findById(deliveryData.purchaseOrderId);
      if (!purchaseOrder) {
        throw new Error(`Purchase order with ID ${deliveryData.purchaseOrderId} not found`);
      }

      // Validate supplier exists
      const supplier = await Supplier.findById(deliveryData.supplierId);
      if (!supplier) {
        throw new Error(`Supplier with ID ${deliveryData.supplierId} not found`);
      }

      // Validate delivery items against purchase order items
      await this.validateDeliveryItems(deliveryData.items, purchaseOrder);

      // Calculate item totals
      const processedItems = deliveryData.items.map(item => ({
        ...item,
        purchaseOrderItemId: new mongoose.Types.ObjectId(item.purchaseOrderItemId),
        quantityAccepted: 0,
        quantityRejected: 0,
        quantityPending: item.quantityDelivered,
        totalValue: item.quantityDelivered * item.unitPrice,
        condition: item.condition || 'good'
      }));

      // Calculate totals
      const totalItems = processedItems.reduce((sum, item) => sum + item.quantityDelivered, 0);
      const totalValue = processedItems.reduce((sum, item) => sum + item.totalValue, 0);

      const delivery = new Delivery({
        ...deliveryData,
        supplierId: new mongoose.Types.ObjectId(deliveryData.supplierId),
        purchaseOrderId: new mongoose.Types.ObjectId(deliveryData.purchaseOrderId),
        contractId: deliveryData.contractId ? new mongoose.Types.ObjectId(deliveryData.contractId) : undefined,
        items: processedItems,
        totalItems,
        totalValue,
        currency: deliveryData.currency || 'MWK',
        createdBy: new mongoose.Types.ObjectId(deliveryData.createdBy),
        deliveryPerformance: {
          onTimeDelivery: false,
          accuracyRate: 0
        }
      });

      const savedDelivery = await delivery.save();
      
      logger.info('Delivery created successfully', LogCategory.PROCUREMENT, { 
        deliveryId: savedDelivery._id,
        deliveryNumber: savedDelivery.deliveryNumber 
      });

      return savedDelivery;
    } catch (error) {
      logger.error('Error creating delivery', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update delivery
   * @param id - Delivery ID
   * @param updateData - Delivery update data
   * @returns Updated delivery
   */
  async updateDelivery(id: string, updateData: UpdateDeliveryData): Promise<IDelivery> {
    try {
      await connectToDatabase();
      logger.info('Updating delivery', LogCategory.PROCUREMENT, { deliveryId: id });

      const delivery = await Delivery.findById(id);
      if (!delivery) {
        throw new Error(`Delivery with ID ${id} not found`);
      }

      // Validate status transition if status is being updated
      if (updateData.status && !this.isValidStatusTransition(delivery.status, updateData.status)) {
        throw new Error(`Invalid status transition from ${delivery.status} to ${updateData.status}`);
      }

      // Update fields
      Object.assign(delivery, updateData);

      // Set modification metadata
      if (updateData.updatedBy) {
        delivery.updatedBy = new mongoose.Types.ObjectId(updateData.updatedBy);
      }

      const updatedDelivery = await delivery.save();

      logger.info('Delivery updated successfully', LogCategory.PROCUREMENT, {
        deliveryId: updatedDelivery._id,
        deliveryNumber: updatedDelivery.deliveryNumber
      });

      return updatedDelivery;
    } catch (error) {
      logger.error('Error updating delivery', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Update delivery status
   * @param id - Delivery ID
   * @param status - New status
   * @param updatedBy - User ID updating the delivery
   * @param actualDate - Actual delivery date (optional)
   * @returns Updated delivery
   */
  async updateDeliveryStatus(
    id: string,
    status: 'scheduled' | 'in_transit' | 'delivered' | 'partially_delivered' | 'delayed' | 'cancelled' | 'returned',
    updatedBy: string,
    actualDate?: Date
  ): Promise<IDelivery> {
    try {
      await connectToDatabase();
      logger.info('Updating delivery status', LogCategory.PROCUREMENT, { deliveryId: id, status });

      const delivery = await Delivery.findById(id);
      if (!delivery) {
        throw new Error(`Delivery with ID ${id} not found`);
      }

      // Validate status transition
      if (!this.isValidStatusTransition(delivery.status, status)) {
        throw new Error(`Invalid status transition from ${delivery.status} to ${status}`);
      }

      // Update delivery
      delivery.status = status;
      delivery.updatedBy = new mongoose.Types.ObjectId(updatedBy);

      // Set actual date for delivered status
      if (status === 'delivered' && actualDate) {
        delivery.actualDate = actualDate;
      }

      const updatedDelivery = await delivery.save();
      
      logger.info('Delivery status updated successfully', LogCategory.PROCUREMENT, { 
        deliveryId: updatedDelivery._id,
        deliveryNumber: updatedDelivery.deliveryNumber,
        newStatus: status
      });

      return updatedDelivery;
    } catch (error) {
      logger.error('Error updating delivery status', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Record goods receipt
   * @param id - Delivery ID
   * @param receiptData - Goods receipt data
   * @returns Updated delivery with receipt information
   */
  async recordGoodsReceipt(id: string, receiptData: GoodsReceiptData): Promise<IDelivery> {
    try {
      await connectToDatabase();
      logger.info('Recording goods receipt', LogCategory.PROCUREMENT, { deliveryId: id });

      const delivery = await Delivery.findById(id);
      if (!delivery) {
        throw new Error(`Delivery with ID ${id} not found`);
      }

      // Validate delivery can be received
      if (!delivery.canReceive()) {
        throw new Error('Delivery cannot be received at this time');
      }

      // Update delivery items with receipt information
      receiptData.items.forEach(receiptItem => {
        const deliveryItem = delivery.items.find(
          (item: any) => item.purchaseOrderItemId.toString() === receiptItem.purchaseOrderItemId
        );
        
        if (deliveryItem) {
          deliveryItem.quantityAccepted = receiptItem.quantityAccepted;
          deliveryItem.quantityRejected = receiptItem.quantityRejected;
          deliveryItem.quantityPending = deliveryItem.quantityDelivered - 
            (receiptItem.quantityAccepted + receiptItem.quantityRejected);
          deliveryItem.rejectionReason = receiptItem.rejectionReason;
          deliveryItem.condition = receiptItem.condition;
          deliveryItem.notes = receiptItem.notes;
        }
      });

      // Update delivery receipt information
      delivery.receivedBy = new mongoose.Types.ObjectId(receiptData.receivedBy);
      delivery.receivedDate = receiptData.receivedDate || new Date();
      delivery.goodsReceiptNumber = receiptData.goodsReceiptNumber;
      delivery.packingList = receiptData.packingList || false;
      delivery.invoice = receiptData.invoice || false;
      delivery.deliveryNote = receiptData.deliveryNote || false;
      delivery.qualityCertificates = receiptData.qualityCertificates || false;
      delivery.customsDocuments = receiptData.customsDocuments || false;
      
      if (receiptData.notes) {
        delivery.notes = delivery.notes ? `${delivery.notes}\n\nReceipt Notes: ${receiptData.notes}` : `Receipt Notes: ${receiptData.notes}`;
      }

      const updatedDelivery = await delivery.save();
      
      logger.info('Goods receipt recorded successfully', LogCategory.PROCUREMENT, { 
        deliveryId: updatedDelivery._id,
        deliveryNumber: updatedDelivery.deliveryNumber,
        receivedBy: receiptData.receivedBy
      });

      return updatedDelivery;
    } catch (error) {
      logger.error('Error recording goods receipt', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Conduct quality inspection
   * @param id - Delivery ID
   * @param inspectionData - Quality inspection data
   * @returns Updated delivery with inspection results
   */
  async conductQualityInspection(id: string, inspectionData: QualityInspectionData): Promise<IDelivery> {
    try {
      await connectToDatabase();
      logger.info('Conducting quality inspection', LogCategory.PROCUREMENT, { deliveryId: id });

      const delivery = await Delivery.findById(id);
      if (!delivery) {
        throw new Error(`Delivery with ID ${id} not found`);
      }

      // Validate delivery can be inspected
      if (!delivery.canInspect()) {
        throw new Error('Delivery cannot be inspected at this time. Goods must be received first.');
      }

      // Calculate quality score based on inspection results
      const qualityScore = this.calculateQualityScore(inspectionData);

      // Create quality inspection record
      const qualityInspection: IQualityInspection = {
        inspectedBy: new mongoose.Types.ObjectId(inspectionData.inspectedBy),
        inspectionDate: inspectionData.inspectionDate || new Date(),
        inspectionType: inspectionData.inspectionType,
        overallRating: inspectionData.overallRating,
        criteria: inspectionData.criteria,
        defectsFound: inspectionData.defectsFound || [],
        recommendations: inspectionData.recommendations || [],
        certificatesRequired: inspectionData.certificatesRequired || false,
        certificatesReceived: inspectionData.certificatesReceived || false,
        inspectionNotes: inspectionData.inspectionNotes
      };

      // Update delivery with inspection results
      delivery.qualityInspection = qualityInspection;
      delivery.deliveryPerformance.qualityScore = qualityScore;

      const updatedDelivery = await delivery.save();

      logger.info('Quality inspection completed successfully', LogCategory.PROCUREMENT, {
        deliveryId: updatedDelivery._id,
        deliveryNumber: updatedDelivery.deliveryNumber,
        qualityScore,
        overallRating: inspectionData.overallRating
      });

      return updatedDelivery;
    } catch (error) {
      logger.error('Error conducting quality inspection', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get overdue deliveries
   * @param days - Number of days overdue (default: 0 for any overdue)
   * @returns Overdue deliveries
   */
  async getOverdueDeliveries(days: number = 0): Promise<IDelivery[]> {
    try {
      await connectToDatabase();
      logger.info('Getting overdue deliveries', LogCategory.PROCUREMENT, { days });

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const deliveries = await Delivery.find({
        expectedDate: { $lt: cutoffDate },
        status: { $in: ['scheduled', 'in_transit'] }
      }).populate('supplierId', 'name contactEmail')
        .populate('purchaseOrderId', 'orderNumber');

      logger.info(`Found ${deliveries.length} overdue deliveries`, LogCategory.PROCUREMENT);
      return deliveries;
    } catch (error) {
      logger.error('Error getting overdue deliveries', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get deliveries by purchase order
   * @param purchaseOrderId - Purchase order ID
   * @returns Deliveries for the purchase order
   */
  async getDeliveriesByPO(purchaseOrderId: string): Promise<IDelivery[]> {
    try {
      await connectToDatabase();
      logger.info('Getting deliveries by purchase order', LogCategory.PROCUREMENT, { purchaseOrderId });

      const deliveries = await Delivery.find({
        purchaseOrderId: new mongoose.Types.ObjectId(purchaseOrderId)
      }).populate('supplierId', 'name contactEmail')
        .populate('purchaseOrderId', 'orderNumber');

      logger.info(`Found ${deliveries.length} deliveries for purchase order`, LogCategory.PROCUREMENT);
      return deliveries;
    } catch (error) {
      logger.error('Error getting deliveries by purchase order', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get deliveries by supplier
   * @param supplierId - Supplier ID
   * @returns Deliveries for the supplier
   */
  async getDeliveriesBySupplier(supplierId: string): Promise<IDelivery[]> {
    try {
      await connectToDatabase();
      logger.info('Getting deliveries by supplier', LogCategory.PROCUREMENT, { supplierId });

      const deliveries = await Delivery.find({
        supplierId: new mongoose.Types.ObjectId(supplierId)
      }).populate('supplierId', 'name contactEmail')
        .populate('purchaseOrderId', 'orderNumber');

      logger.info(`Found ${deliveries.length} deliveries for supplier`, LogCategory.PROCUREMENT);
      return deliveries;
    } catch (error) {
      logger.error('Error getting deliveries by supplier', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get pending receipts
   * @returns Deliveries pending goods receipt
   */
  async getPendingReceipts(): Promise<IDelivery[]> {
    try {
      await connectToDatabase();
      logger.info('Getting pending receipts', LogCategory.PROCUREMENT);

      const deliveries = await Delivery.find({
        status: 'delivered',
        receivedBy: { $exists: false }
      }).populate('supplierId', 'name contactEmail')
        .populate('purchaseOrderId', 'orderNumber');

      logger.info(`Found ${deliveries.length} deliveries pending receipt`, LogCategory.PROCUREMENT);
      return deliveries;
    } catch (error) {
      logger.error('Error getting pending receipts', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Search deliveries with filters
   * @param filters - Search filters
   * @param page - Page number
   * @param limit - Items per page
   * @returns Filtered deliveries with pagination
   */
  async searchDeliveries(filters: DeliveryFilters, page: number = 1, limit: number = 20) {
    try {
      await connectToDatabase();
      logger.info('Searching deliveries', LogCategory.PROCUREMENT, { filters, page, limit });

      const query: any = {};

      // Apply filters
      if (filters.supplierId) {
        query.supplierId = new mongoose.Types.ObjectId(filters.supplierId);
      }

      if (filters.purchaseOrderId) {
        query.purchaseOrderId = new mongoose.Types.ObjectId(filters.purchaseOrderId);
      }

      if (filters.contractId) {
        query.contractId = new mongoose.Types.ObjectId(filters.contractId);
      }

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.priority) {
        query.priority = filters.priority;
      }

      if (filters.deliveryType) {
        query.deliveryType = filters.deliveryType;
      }

      if (filters.trackingNumber) {
        query.trackingNumber = { $regex: filters.trackingNumber, $options: 'i' };
      }

      if (filters.carrier) {
        query.carrier = { $regex: filters.carrier, $options: 'i' };
      }

      if (filters.expectedDate) {
        query.expectedDate = {};
        if (filters.expectedDate.from) query.expectedDate.$gte = filters.expectedDate.from;
        if (filters.expectedDate.to) query.expectedDate.$lte = filters.expectedDate.to;
      }

      if (filters.actualDate) {
        query.actualDate = {};
        if (filters.actualDate.from) query.actualDate.$gte = filters.actualDate.from;
        if (filters.actualDate.to) query.actualDate.$lte = filters.actualDate.to;
      }

      if (filters.overdue) {
        query.expectedDate = { $lt: new Date() };
        query.status = { $in: ['scheduled', 'in_transit'] };
      }

      if (filters.pendingReceipt) {
        query.status = 'delivered';
        query.receivedBy = { $exists: false };
      }

      if (filters.hasIssues) {
        query['issues.status'] = { $in: ['open', 'in_progress'] };
      }

      if (filters.search) {
        query.$or = [
          { deliveryNumber: { $regex: filters.search, $options: 'i' } },
          { trackingNumber: { $regex: filters.search, $options: 'i' } },
          { contactPerson: { $regex: filters.search, $options: 'i' } },
          { notes: { $regex: filters.search, $options: 'i' } }
        ];
      }

      const skip = (page - 1) * limit;

      const [deliveries, total] = await Promise.all([
        Delivery.find(query)
          .populate('supplierId', 'name contactEmail')
          .populate('purchaseOrderId', 'orderNumber')
          .populate('contractId', 'contractNumber title')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        Delivery.countDocuments(query)
      ]);

      const result = {
        deliveries,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

      logger.info(`Found ${deliveries.length} deliveries`, LogCategory.PROCUREMENT, {
        total,
        page,
        pages: result.pagination.pages
      });

      return result;
    } catch (error) {
      logger.error('Error searching deliveries', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Validate delivery items against purchase order
   * @param deliveryItems - Delivery items to validate
   * @param purchaseOrder - Purchase order to validate against
   */
  private async validateDeliveryItems(deliveryItems: any[], purchaseOrder: any): Promise<void> {
    for (const deliveryItem of deliveryItems) {
      const poItem = purchaseOrder.items.find(
        (item: any) => item._id.toString() === deliveryItem.purchaseOrderItemId
      );

      if (!poItem) {
        throw new Error(`Purchase order item ${deliveryItem.purchaseOrderItemId} not found`);
      }

      if (deliveryItem.quantityDelivered > poItem.quantity) {
        throw new Error(`Delivery quantity (${deliveryItem.quantityDelivered}) exceeds ordered quantity (${poItem.quantity}) for item ${poItem.name}`);
      }
    }
  }

  /**
   * Calculate quality score based on inspection data
   * @param inspectionData - Quality inspection data
   * @returns Quality score (0-100)
   */
  private calculateQualityScore(inspectionData: QualityInspectionData): number {
    let score = 100;

    // Deduct points based on overall rating
    switch (inspectionData.overallRating) {
      case 'excellent':
        score = 100;
        break;
      case 'good':
        score = 85;
        break;
      case 'fair':
        score = 70;
        break;
      case 'poor':
        score = 50;
        break;
      case 'failed':
        score = 25;
        break;
    }

    // Deduct points for failed criteria
    if (inspectionData.criteria) {
      const failedCriteria = inspectionData.criteria.filter(c => c.rating === 'fail').length;
      const totalCriteria = inspectionData.criteria.length;
      if (totalCriteria > 0) {
        score -= (failedCriteria / totalCriteria) * 20;
      }
    }

    // Deduct points for defects
    if (inspectionData.defectsFound) {
      inspectionData.defectsFound.forEach(defect => {
        switch (defect.severity) {
          case 'critical':
            score -= 15;
            break;
          case 'major':
            score -= 10;
            break;
          case 'minor':
            score -= 5;
            break;
        }
      });
    }

    return Math.max(0, Math.round(score));
  }

  /**
   * Calculate issues summary
   * @param deliveries - Deliveries to analyze
   * @returns Issues summary data
   */
  private calculateIssuesSummary(deliveries: any[]): any {
    const issuesSummary = {
      totalIssues: 0,
      openIssues: 0,
      resolvedIssues: 0,
      criticalIssues: 0,
      issuesByType: {} as Record<string, number>
    };

    deliveries.forEach(delivery => {
      if (delivery.issues && delivery.issues.length > 0) {
        delivery.issues.forEach((issue: any) => {
          issuesSummary.totalIssues++;

          if (issue.status === 'open' || issue.status === 'in_progress') {
            issuesSummary.openIssues++;
          } else if (issue.status === 'resolved') {
            issuesSummary.resolvedIssues++;
          }

          if (issue.severity === 'critical') {
            issuesSummary.criticalIssues++;
          }

          if (issue.type) {
            issuesSummary.issuesByType[issue.type] = (issuesSummary.issuesByType[issue.type] || 0) + 1;
          }
        });
      }
    });

    return issuesSummary;
  }

  /**
   * Calculate supplier performance metrics
   * @param deliveries - Deliveries to analyze
   * @returns Supplier performance data
   */
  private calculateSupplierPerformance(deliveries: any[]): any[] {
    const supplierMap = new Map();

    deliveries.forEach(delivery => {
      const supplierId = delivery.supplierId._id.toString();
      const supplierName = delivery.supplierId.name;

      if (!supplierMap.has(supplierId)) {
        supplierMap.set(supplierId, {
          supplierId,
          supplierName,
          totalDeliveries: 0,
          onTimeDeliveries: 0,
          totalDeliveryTime: 0,
          completedDeliveries: 0,
          totalQualityScore: 0,
          qualityInspections: 0
        });
      }

      const supplier = supplierMap.get(supplierId);
      supplier.totalDeliveries++;

      if (delivery.deliveryPerformance?.onTimeDelivery) {
        supplier.onTimeDeliveries++;
      }

      if (delivery.actualDate && delivery.expectedDate) {
        const diffTime = delivery.actualDate.getTime() - delivery.expectedDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        supplier.totalDeliveryTime += diffDays;
        supplier.completedDeliveries++;
      }

      if (delivery.deliveryPerformance?.qualityScore) {
        supplier.totalQualityScore += delivery.deliveryPerformance.qualityScore;
        supplier.qualityInspections++;
      }
    });

    return Array.from(supplierMap.values()).map(supplier => ({
      supplierId: supplier.supplierId,
      supplierName: supplier.supplierName,
      totalDeliveries: supplier.totalDeliveries,
      onTimeRate: supplier.totalDeliveries > 0 ? (supplier.onTimeDeliveries / supplier.totalDeliveries) * 100 : 0,
      averageDeliveryTime: supplier.completedDeliveries > 0 ? supplier.totalDeliveryTime / supplier.completedDeliveries : 0,
      qualityScore: supplier.qualityInspections > 0 ? supplier.totalQualityScore / supplier.qualityInspections : 0
    }));
  }



  /**
   * Generate delivery report
   * @param filters - Optional filters for the report
   * @returns Delivery performance report
   */
  async generateDeliveryReport(filters?: DeliveryFilters): Promise<DeliveryReport> {
    try {
      await connectToDatabase();
      logger.info('Generating delivery report', LogCategory.PROCUREMENT, { filters });

      // Build query based on filters
      const query: any = {};
      if (filters) {
        if (filters.supplierId) {
          query.supplierId = new mongoose.Types.ObjectId(filters.supplierId);
        }
        if (filters.expectedDate) {
          query.expectedDate = {};
          if (filters.expectedDate.from) query.expectedDate.$gte = filters.expectedDate.from;
          if (filters.expectedDate.to) query.expectedDate.$lte = filters.expectedDate.to;
        }
        if (filters.status) {
          query.status = filters.status;
        }
      }

      // Get all deliveries for the report
      const deliveries = await Delivery.find(query)
        .populate('supplierId', 'name contactEmail')
        .populate('purchaseOrderId', 'orderNumber');

      // Calculate basic metrics
      const totalDeliveries = deliveries.length;
      const onTimeDeliveries = deliveries.filter(d => d.deliveryPerformance?.onTimeDelivery).length;
      const lateDeliveries = deliveries.filter(d =>
        d.actualDate && d.expectedDate && d.actualDate > d.expectedDate
      ).length;
      const pendingDeliveries = deliveries.filter(d =>
        ['scheduled', 'in_transit'].includes(d.status)
      ).length;

      // Calculate average delivery time
      const completedDeliveries = deliveries.filter(d => d.actualDate && d.expectedDate);
      const totalDeliveryTime = completedDeliveries.reduce((sum, d) => {
        const diffTime = d.actualDate!.getTime() - d.expectedDate.getTime();
        return sum + Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }, 0);
      const averageDeliveryTime = completedDeliveries.length > 0 ?
        totalDeliveryTime / completedDeliveries.length : 0;

      // Calculate supplier performance
      const supplierPerformance = this.calculateSupplierPerformance(deliveries);

      // Calculate issues summary
      const issuesSummary = this.calculateIssuesSummary(deliveries);

      const report: DeliveryReport = {
        totalDeliveries,
        onTimeDeliveries,
        lateDeliveries,
        pendingDeliveries,
        averageDeliveryTime,
        supplierPerformance,
        issuesSummary
      };

      logger.info('Delivery report generated successfully', LogCategory.PROCUREMENT, {
        totalDeliveries,
        onTimeDeliveries,
        lateDeliveries
      });

      return report;
    } catch (error) {
      logger.error('Error generating delivery report', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Validate status transition
   * @param currentStatus - Current delivery status
   * @param newStatus - New delivery status
   * @returns Whether transition is valid
   */
  private isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions: Record<string, string[]> = {
      'scheduled': ['in_transit', 'delayed', 'cancelled'],
      'in_transit': ['delivered', 'partially_delivered', 'delayed', 'returned'],
      'delivered': ['returned'],
      'partially_delivered': ['delivered', 'returned'],
      'delayed': ['in_transit', 'delivered', 'partially_delivered', 'cancelled'],
      'cancelled': [], // Terminal state
      'returned': [] // Terminal state
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}

// Create and export service instance
export const deliveryService = new DeliveryService();

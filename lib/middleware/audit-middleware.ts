import { NextRequest, NextResponse } from 'next/server';
import { auditService } from '@/lib/services/payroll/audit-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

/**
 * Interface for audit middleware options
 */
export interface AuditMiddlewareOptions {
  module: string;
  entityType: string;
  action?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  skipAudit?: boolean;
  extractEntityId?: (request: NextRequest, response?: NextResponse) => string | Promise<string>;
  extractOldValues?: (request: NextRequest) => Record<string, any> | Promise<Record<string, any>>;
  extractNewValues?: (request: NextRequest, response?: NextResponse) => Record<string, any> | Promise<Record<string, any>>;
  customDescription?: (request: NextRequest, response?: NextResponse) => string | Promise<string>;
}

/**
 * Audit middleware for automatic logging of API operations
 */
export function withAudit(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: AuditMiddlewareOptions
) {
  return async function auditedHandler(request: NextRequest, ...args: any[]): Promise<NextResponse> {
    const startTime = Date.now();
    let response: NextResponse;
    let error: Error | null = null;

    try {
      // Execute the original handler
      response = await handler(request, ...args);
    } catch (err) {
      error = err instanceof Error ? err : new Error(String(err));
      response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Skip audit if specified
    if (options.skipAudit) {
      if (error) throw error;
      return response;
    }

    try {
      // Get user session
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        // Skip audit for unauthenticated requests
        if (error) throw error;
        return response;
      }

      // Extract request context
      const requestContext = auditService.extractRequestContext(request);

      // Determine action from HTTP method if not specified
      const action = options.action || getActionFromMethod(request.method);

      // Extract entity ID
      let entityId = 'unknown';
      if (options.extractEntityId) {
        try {
          entityId = await options.extractEntityId(request, response);
        } catch (err) {
          logger.warn('Failed to extract entity ID for audit', LogCategory.AUDIT, err);
        }
      } else {
        // Try to extract from URL path
        entityId = extractEntityIdFromPath(request.nextUrl.pathname);
      }

      // Extract old and new values
      let oldValues: Record<string, any> | undefined;
      let newValues: Record<string, any> | undefined;

      if (options.extractOldValues) {
        try {
          oldValues = await options.extractOldValues(request);
        } catch (err) {
          logger.warn('Failed to extract old values for audit', LogCategory.AUDIT, err);
        }
      }

      if (options.extractNewValues) {
        try {
          newValues = await options.extractNewValues(request, response);
        } catch (err) {
          logger.warn('Failed to extract new values for audit', LogCategory.AUDIT, err);
        }
      }

      // Generate description
      let description = `${action} operation on ${options.entityType}`;
      if (options.customDescription) {
        try {
          description = await options.customDescription(request, response);
        } catch (err) {
          logger.warn('Failed to generate custom description for audit', LogCategory.AUDIT, err);
        }
      }

      // Add performance metadata
      const duration = Date.now() - startTime;
      const metadata = {
        httpMethod: request.method,
        url: request.nextUrl.pathname,
        statusCode: response.status,
        duration,
        userAgent: request.headers.get('user-agent'),
        contentType: request.headers.get('content-type'),
        responseSize: response.headers.get('content-length'),
      };

      // Create audit log
      await auditService.createAuditLog({
        userId: session.user.id,
        action,
        module: options.module,
        entityType: options.entityType,
        entityId,
        oldValues,
        newValues,
        description,
        metadata,
        severity: options.severity || (error ? 'high' : 'medium'),
        isError: !!error,
        errorMessage: error?.message,
        errorStack: error?.stack,
        ...requestContext,
      });

      logger.info('API operation audited', LogCategory.AUDIT, {
        action,
        module: options.module,
        entityType: options.entityType,
        entityId,
        userId: session.user.id,
        duration,
        statusCode: response.status,
        isError: !!error,
      });

    } catch (auditError) {
      // Log audit failure but don't fail the original request
      logger.error('Failed to create audit log', LogCategory.AUDIT, auditError);
    }

    // Throw original error if it occurred
    if (error) throw error;

    return response;
  };
}

/**
 * Get action from HTTP method
 */
function getActionFromMethod(method: string): string {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'read';
    case 'POST':
      return 'create';
    case 'PUT':
    case 'PATCH':
      return 'update';
    case 'DELETE':
      return 'delete';
    default:
      return 'custom';
  }
}

/**
 * Extract entity ID from URL path
 */
function extractEntityIdFromPath(pathname: string): string {
  // Try to extract ID from common patterns
  const patterns = [
    /\/([a-f\d]{24})(?:\/|$)/i, // MongoDB ObjectId
    /\/(\d+)(?:\/|$)/, // Numeric ID
    /\/([a-f\d-]{36})(?:\/|$)/i, // UUID
  ];

  for (const pattern of patterns) {
    const match = pathname.match(pattern);
    if (match) {
      return match[1];
    }
  }

  // Return pathname as fallback
  return pathname.split('/').pop() || 'unknown';
}

/**
 * Decorator for audit logging with specific configurations
 */
export const AuditDecorators = {
  /**
   * Audit payroll operations
   */
  payroll: (entityType: string, options?: Partial<AuditMiddlewareOptions>) =>
    (handler: any) =>
      withAudit(handler, {
        module: 'payroll',
        entityType,
        severity: 'high',
        ...options,
      }),

  /**
   * Audit employee operations
   */
  employee: (options?: Partial<AuditMiddlewareOptions>) =>
    (handler: any) =>
      withAudit(handler, {
        module: 'employees',
        entityType: 'Employee',
        severity: 'medium',
        ...options,
      }),

  /**
   * Audit accounting operations
   */
  accounting: (entityType: string, options?: Partial<AuditMiddlewareOptions>) =>
    (handler: any) =>
      withAudit(handler, {
        module: 'accounting',
        entityType,
        severity: 'high',
        ...options,
      }),

  /**
   * Audit bulk operations
   */
  bulk: (entityType: string, options?: Partial<AuditMiddlewareOptions>) =>
    (handler: any) =>
      withAudit(handler, {
        module: 'payroll',
        entityType,
        action: 'bulk_import',
        severity: 'critical',
        ...options,
      }),

  /**
   * Audit export operations
   */
  export: (entityType: string, options?: Partial<AuditMiddlewareOptions>) =>
    (handler: any) =>
      withAudit(handler, {
        module: 'exports',
        entityType,
        action: 'bulk_export',
        severity: 'medium',
        ...options,
      }),
};

/**
 * Helper function to create batch audit logs for bulk operations
 */
export async function createBulkAuditLogs(
  userId: string,
  action: string,
  module: string,
  entityType: string,
  operations: Array<{
    entityId: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    description?: string;
    isError?: boolean;
    errorMessage?: string;
  }>,
  metadata?: Record<string, any>,
  request?: NextRequest
): Promise<void> {
  try {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const requestContext = request ? auditService.extractRequestContext(request) : {};

    await auditService.createBulkAuditLogs({
      userId,
      action,
      module,
      entityType,
      batchId,
      batchSize: operations.length,
      operations,
      metadata,
      severity: 'high',
      ...requestContext,
    });

    logger.info('Bulk audit logs created', LogCategory.AUDIT, {
      batchId,
      operationCount: operations.length,
      action,
      module,
      entityType,
      userId,
    });
  } catch (error) {
    logger.error('Failed to create bulk audit logs', LogCategory.AUDIT, error);
    throw error;
  }
}

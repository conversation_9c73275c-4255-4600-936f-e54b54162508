"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { AdvancedForecastResult, ForecastOptions } from '@/lib/services/accounting/advanced-forecasting-service';

// Types for the hook
interface ForecastRequest {
  fiscalYear: string;
  budgetId?: string;
  categoryId?: string;
  options?: Partial<ForecastOptions>;
}

interface ForecastConfiguration {
  availableModels: Array<{
    type: string;
    name: string;
    description: string;
  }>;
  defaultOptions: ForecastOptions;
  confidenceLevels: Array<{
    value: number;
    label: string;
  }>;
  forecastHorizons: Array<{
    value: number;
    label: string;
  }>;
  scenarios: Array<{
    type: string;
    name: string;
    description: string;
    adjustmentFactor: number;
  }>;
}

interface ForecastResponse {
  success: boolean;
  forecast: AdvancedForecastResult;
}

interface ConfigurationResponse {
  success: boolean;
  configuration: ForecastConfiguration;
}

/**
 * Hook for advanced income forecasting
 */
export function useAdvancedForecasting() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get forecast configuration
  const {
    data: configurationData,
    isLoading: isLoadingConfiguration,
    error: configurationError
  } = useQuery<ConfigurationResponse>({
    queryKey: ['forecast-configuration'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/income/forecast');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch forecast configuration');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Generate forecast mutation
  const generateForecastMutation = useMutation<ForecastResponse, Error, ForecastRequest>({
    mutationFn: async (request: ForecastRequest) => {
      const response = await fetch('/api/accounting/income/forecast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate forecast');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Forecast Generated",
        description: `Advanced forecast generated successfully for ${variables.fiscalYear}`,
      });

      // Cache the forecast result
      queryClient.setQueryData(
        ['income-forecast', variables.fiscalYear, variables.budgetId, variables.categoryId],
        data
      );
    },
    onError: (error: Error) => {
      toast({
        title: "Forecast Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Get cached forecast
  const getCachedForecast = (
    fiscalYear: string,
    budgetId?: string,
    categoryId?: string
  ) => {
    return queryClient.getQueryData<ForecastResponse>([
      'income-forecast',
      fiscalYear,
      budgetId,
      categoryId
    ]);
  };

  // Clear forecast cache
  const clearForecastCache = () => {
    queryClient.removeQueries({ queryKey: ['income-forecast'] });
  };

  // Generate forecast with error handling
  const generateForecast = async (request: ForecastRequest): Promise<AdvancedForecastResult | null> => {
    try {
      const result = await generateForecastMutation.mutateAsync(request);
      return result.forecast;
    } catch (error) {
      console.error('Error generating forecast:', error);
      return null;
    }
  };

  // Validate forecast request
  const validateForecastRequest = (request: ForecastRequest): string[] => {
    const errors: string[] = [];

    if (!request.fiscalYear) {
      errors.push('Fiscal year is required');
    }

    if (request.options?.forecastHorizon && (request.options.forecastHorizon < 1 || request.options.forecastHorizon > 24)) {
      errors.push('Forecast horizon must be between 1 and 24 months');
    }

    if (request.options?.confidenceLevel && (request.options.confidenceLevel < 0.8 || request.options.confidenceLevel > 0.99)) {
      errors.push('Confidence level must be between 0.8 and 0.99');
    }

    if (request.options?.anomalyThreshold && (request.options.anomalyThreshold < 1 || request.options.anomalyThreshold > 5)) {
      errors.push('Anomaly threshold must be between 1 and 5');
    }

    if (request.options?.minDataPoints && (request.options.minDataPoints < 3 || request.options.minDataPoints > 24)) {
      errors.push('Minimum data points must be between 3 and 24');
    }

    return errors;
  };

  // Get default forecast options
  const getDefaultOptions = (): Partial<ForecastOptions> => {
    return configurationData?.configuration.defaultOptions || {
      forecastHorizon: 6,
      confidenceLevel: 0.95,
      includeSeasonality: true,
      detectAnomalies: true,
      anomalyThreshold: 2.5,
      includeScenarios: true,
      modelType: 'auto',
      minDataPoints: 6
    };
  };

  // Format forecast data for charts
  const formatForecastForChart = (forecast: AdvancedForecastResult) => {
    return {
      historical: forecast.historicalData.map(point => ({
        period: point.date.toISOString().substring(0, 7),
        value: point.value,
        type: 'historical'
      })),
      forecast: forecast.forecast.map(point => ({
        period: point.period,
        value: point.forecasted,
        lowerBound: point.lowerBound,
        upperBound: point.upperBound,
        confidence: point.confidence,
        type: 'forecast'
      })),
      scenarios: forecast.scenarios.map(scenario => ({
        scenario: scenario.scenario,
        data: scenario.forecast.map(point => ({
          period: point.period,
          value: point.forecasted,
          type: scenario.scenario
        }))
      }))
    };
  };

  // Calculate forecast summary statistics
  const calculateForecastSummary = (forecast: AdvancedForecastResult) => {
    const totalHistorical = forecast.historicalData.reduce((sum, point) => sum + point.value, 0);
    const totalForecast = forecast.forecast.reduce((sum, point) => sum + point.forecasted, 0);
    const averageHistorical = totalHistorical / forecast.historicalData.length;
    const averageForecast = totalForecast / forecast.forecast.length;
    
    const growthRate = averageHistorical > 0 
      ? ((averageForecast - averageHistorical) / averageHistorical) * 100 
      : 0;

    const optimisticScenario = forecast.scenarios.find(s => s.scenario === 'optimistic');
    const pessimisticScenario = forecast.scenarios.find(s => s.scenario === 'pessimistic');

    return {
      totalHistorical,
      totalForecast,
      averageHistorical,
      averageForecast,
      growthRate,
      modelAccuracy: forecast.model.accuracy.r2,
      modelType: forecast.model.type,
      confidence: forecast.model.confidence,
      dataPoints: forecast.metadata.dataPoints,
      forecastHorizon: forecast.metadata.forecastHorizon,
      scenarios: {
        optimistic: optimisticScenario?.totalProjected || 0,
        realistic: totalForecast,
        pessimistic: pessimisticScenario?.totalProjected || 0
      },
      trends: forecast.trends,
      recommendations: forecast.recommendations
    };
  };

  return {
    // Configuration
    configuration: configurationData?.configuration,
    isLoadingConfiguration,
    configurationError,

    // Forecast generation
    generateForecast,
    isGeneratingForecast: generateForecastMutation.isPending,
    forecastError: generateForecastMutation.error,

    // Cache management
    getCachedForecast,
    clearForecastCache,

    // Utilities
    validateForecastRequest,
    getDefaultOptions,
    formatForecastForChart,
    calculateForecastSummary,

    // Raw mutation for advanced usage
    generateForecastMutation
  };
}

/**
 * Hook for a specific forecast query
 */
export function useForecastQuery(
  fiscalYear: string,
  budgetId?: string,
  categoryId?: string,
  options?: Partial<ForecastOptions>,
  enabled = true
) {
  return useQuery<ForecastResponse>({
    queryKey: ['income-forecast', fiscalYear, budgetId, categoryId, options],
    queryFn: async () => {
      const response = await fetch('/api/accounting/income/forecast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fiscalYear,
          budgetId,
          categoryId,
          options
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate forecast');
      }

      return response.json();
    },
    enabled: enabled && !!fiscalYear,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

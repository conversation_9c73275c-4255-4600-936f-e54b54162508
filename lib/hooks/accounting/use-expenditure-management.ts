"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import {
  ExpenditureCategory,
  ExpenditurePriority,
  ExpenditureStatus,
  PaymentMethod
} from '@/types/accounting/expenditure';

// Types for the hook
interface CreateExpenditureRequest {
  title: string;
  description: string;
  category: ExpenditureCategory;
  subcategory: string;
  amount: number;
  currency?: string;
  exchangeRate?: number;
  expenditureDate: Date;
  dueDate?: Date;
  priority?: ExpenditurePriority;
  department: string;
  costCenter?: string;
  vendor: {
    vendorId?: string;
    vendorName: string;
    vendorEmail?: string;
    vendorPhone?: string;
    vendorAddress?: string;
  };
  budgetAllocations: Array<{
    budgetId: string;
    allocatedAmount: number;
    percentage: number;
  }>;
  taxInfo?: {
    taxType: 'VAT' | 'withholding' | 'excise' | 'none';
    taxRate: number;
    isExempt: boolean;
    exemptionReason?: string;
  };
  paymentMethod?: PaymentMethod;
  tags?: string[];
  notes?: string[];
  isUrgent?: boolean;
  requiresReceipt?: boolean;
  isCapitalExpenditure?: boolean;
  projectId?: string;
}

interface UpdateExpenditureRequest extends Partial<CreateExpenditureRequest> {
  id: string;
}

interface ExpenditureFilters {
  status?: ExpenditureStatus[];
  category?: ExpenditureCategory[];
  priority?: ExpenditurePriority[];
  department?: string[];
  requestedBy?: string[];
  vendorId?: string[];
  amountRange?: { min: number; max: number };
  dateRange?: { startDate: Date; endDate: Date };
  isUrgent?: boolean;
  isCapitalExpenditure?: boolean;
  tags?: string[];
  search?: string;
}

interface ExpenditureStatistics {
  totalExpenditures: number;
  totalAmount: number;
  averageAmount: number;
  pendingApprovals: number;
  pendingPayments: number;
  overBudgetCount: number;
  urgentCount: number;
  byCategory: Record<ExpenditureCategory, { count: number; amount: number }>;
  byStatus: Record<ExpenditureStatus, { count: number; amount: number }>;
  byDepartment: Record<string, { count: number; amount: number }>;
  monthlyTrend: Array<{ month: string; count: number; amount: number }>;
}

interface Expenditure {
  id: string;
  expenditureNumber: string;
  title: string;
  description: string;
  category: ExpenditureCategory;
  subcategory: string;
  amount: number;
  currency: string;
  status: ExpenditureStatus;
  priority: ExpenditurePriority;
  expenditureDate: Date;
  dueDate?: Date;
  requestedBy: string;
  requestedByName: string;
  department: string;
  vendor: {
    vendorId?: string;
    vendorName: string;
    vendorEmail?: string;
    vendorPhone?: string;
    vendorAddress?: string;
    isPreferred: boolean;
  };
  budgetAllocations: Array<{
    budgetId: string;
    budgetName: string;
    categoryId: string;
    categoryName: string;
    allocatedAmount: number;
    percentage: number;
    remainingBudget: number;
    isOverBudget: boolean;
  }>;
  taxInfo: {
    taxType: 'VAT' | 'withholding' | 'excise' | 'none';
    taxRate: number;
    taxAmount: number;
    isExempt: boolean;
    exemptionReason?: string;
  };
  totalAmount: number;
  isUrgent: boolean;
  isCapitalExpenditure: boolean;
  approvalWorkflow: Array<{
    stepNumber: number;
    approverRole: string;
    approverUserId?: string;
    approverName?: string;
    status: 'pending' | 'approved' | 'rejected' | 'skipped';
    approvedAt?: Date;
    rejectedAt?: Date;
    comments?: string;
    amountLimit?: number;
    isRequired: boolean;
    canDelegate: boolean;
  }>;
  currentApprovalStep: number;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Hook for expenditure management operations
 */
export function useExpenditureManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create expenditure mutation
  const createExpenditureMutation = useMutation<
    { success: boolean; expenditure: Expenditure },
    Error,
    CreateExpenditureRequest
  >({
    mutationFn: async (request: CreateExpenditureRequest) => {
      const response = await fetch('/api/accounting/expenditures/advanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create expenditure');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Expenditure Created",
        description: `Expenditure "${variables.title}" created successfully`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['expenditures'] });
      queryClient.invalidateQueries({ queryKey: ['expenditure-statistics'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Creation Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update expenditure mutation
  const updateExpenditureMutation = useMutation<
    { success: boolean; expenditure: Expenditure },
    Error,
    UpdateExpenditureRequest
  >({
    mutationFn: async (request: UpdateExpenditureRequest) => {
      const { id, ...updateData } = request;
      const response = await fetch(`/api/accounting/expenditures/advanced/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update expenditure');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Expenditure Updated",
        description: `Expenditure updated successfully`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['expenditures'] });
      queryClient.invalidateQueries({ queryKey: ['expenditure', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['expenditure-statistics'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Update Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete expenditure mutation
  const deleteExpenditureMutation = useMutation<
    { success: boolean },
    Error,
    string
  >({
    mutationFn: async (expenditureId: string) => {
      const response = await fetch(`/api/accounting/expenditures/advanced/${expenditureId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete expenditure');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Expenditure Deleted",
        description: "Expenditure deleted successfully",
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['expenditures'] });
      queryClient.invalidateQueries({ queryKey: ['expenditure-statistics'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Deletion Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Create expenditure with error handling
  const createExpenditure = async (request: CreateExpenditureRequest): Promise<Expenditure | null> => {
    try {
      const result = await createExpenditureMutation.mutateAsync(request);
      return result.expenditure;
    } catch (error) {
      console.error('Error creating expenditure:', error);
      return null;
    }
  };

  // Update expenditure with error handling
  const updateExpenditure = async (request: UpdateExpenditureRequest): Promise<Expenditure | null> => {
    try {
      const result = await updateExpenditureMutation.mutateAsync(request);
      return result.expenditure;
    } catch (error) {
      console.error('Error updating expenditure:', error);
      return null;
    }
  };

  // Delete expenditure with error handling
  const deleteExpenditure = async (expenditureId: string): Promise<boolean> => {
    try {
      await deleteExpenditureMutation.mutateAsync(expenditureId);
      return true;
    } catch (error) {
      console.error('Error deleting expenditure:', error);
      return false;
    }
  };

  // Validate expenditure data
  const validateExpenditure = (data: CreateExpenditureRequest): string[] => {
    const errors: string[] = [];

    if (!data.title.trim()) {
      errors.push('Title is required');
    }

    if (!data.description.trim()) {
      errors.push('Description is required');
    }

    if (data.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }

    if (!data.vendor.vendorName.trim()) {
      errors.push('Vendor name is required');
    }

    if (data.budgetAllocations.length === 0) {
      errors.push('At least one budget allocation is required');
    }

    const totalAllocation = data.budgetAllocations.reduce(
      (sum, allocation) => sum + allocation.allocatedAmount, 
      0
    );

    if (Math.abs(totalAllocation - data.amount) > 0.01) {
      errors.push('Budget allocations must equal the expenditure amount');
    }

    const totalPercentage = data.budgetAllocations.reduce(
      (sum, allocation) => sum + allocation.percentage, 
      0
    );

    if (Math.abs(totalPercentage - 100) > 0.01) {
      errors.push('Budget allocation percentages must total 100%');
    }

    if (data.expenditureDate > new Date()) {
      errors.push('Expenditure date cannot be in the future');
    }

    if (data.dueDate && data.dueDate < data.expenditureDate) {
      errors.push('Due date cannot be before expenditure date');
    }

    return errors;
  };

  // Get default expenditure data
  const getDefaultExpenditure = (): Partial<CreateExpenditureRequest> => {
    return {
      category: ExpenditureCategory.OPERATIONAL,
      subcategory: 'office_supplies',
      currency: 'MWK',
      exchangeRate: 1,
      expenditureDate: new Date(),
      priority: ExpenditurePriority.MEDIUM,
      taxInfo: {
        taxType: 'none',
        taxRate: 0,
        isExempt: false
      },
      isUrgent: false,
      requiresReceipt: true,
      isCapitalExpenditure: false,
      tags: [],
      notes: []
    };
  };

  // Calculate total amount including tax
  const calculateTotalAmount = (amount: number, taxInfo?: CreateExpenditureRequest['taxInfo']): number => {
    if (!taxInfo || taxInfo.isExempt) return amount;
    const taxAmount = (amount * taxInfo.taxRate) / 100;
    return amount + taxAmount;
  };

  // Format currency for display
  const formatCurrency = (amount: number, currency = 'MWK'): string => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get expenditure status color
  const getStatusColor = (status: ExpenditureStatus): string => {
    switch (status) {
      case ExpenditureStatus.DRAFT:
        return 'gray';
      case ExpenditureStatus.SUBMITTED:
        return 'blue';
      case ExpenditureStatus.PENDING_APPROVAL:
        return 'yellow';
      case ExpenditureStatus.APPROVED:
        return 'green';
      case ExpenditureStatus.REJECTED:
        return 'red';
      case ExpenditureStatus.PAID:
        return 'purple';
      case ExpenditureStatus.CANCELLED:
        return 'gray';
      case ExpenditureStatus.ON_HOLD:
        return 'orange';
      default:
        return 'gray';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: ExpenditurePriority): string => {
    switch (priority) {
      case ExpenditurePriority.LOW:
        return 'green';
      case ExpenditurePriority.MEDIUM:
        return 'yellow';
      case ExpenditurePriority.HIGH:
        return 'orange';
      case ExpenditurePriority.URGENT:
        return 'red';
      case ExpenditurePriority.CRITICAL:
        return 'purple';
      default:
        return 'gray';
    }
  };

  return {
    // CRUD operations
    createExpenditure,
    updateExpenditure,
    deleteExpenditure,

    // Mutation states
    isCreating: createExpenditureMutation.isPending,
    isUpdating: updateExpenditureMutation.isPending,
    isDeleting: deleteExpenditureMutation.isPending,

    // Mutation errors
    createError: createExpenditureMutation.error,
    updateError: updateExpenditureMutation.error,
    deleteError: deleteExpenditureMutation.error,

    // Utilities
    validateExpenditure,
    getDefaultExpenditure,
    calculateTotalAmount,
    formatCurrency,
    getStatusColor,
    getPriorityColor,

    // Raw mutations for advanced usage
    createExpenditureMutation,
    updateExpenditureMutation,
    deleteExpenditureMutation
  };
}

/**
 * Hook for getting expenditures with filters and pagination
 */
export function useExpenditures(
  filters: ExpenditureFilters = {},
  page = 1,
  limit = 20,
  sortBy = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc',
  enabled = true
) {
  return useQuery<{
    success: boolean;
    expenditures: Expenditure[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>({
    queryKey: ['expenditures', filters, page, limit, sortBy, sortOrder],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder
      });

      // Add filters to params
      if (filters.status?.length) {
        params.append('status', filters.status.join(','));
      }
      if (filters.category?.length) {
        params.append('category', filters.category.join(','));
      }
      if (filters.priority?.length) {
        params.append('priority', filters.priority.join(','));
      }
      if (filters.department?.length) {
        params.append('department', filters.department.join(','));
      }
      if (filters.requestedBy?.length) {
        params.append('requestedBy', filters.requestedBy.join(','));
      }
      if (filters.vendorId?.length) {
        params.append('vendorId', filters.vendorId.join(','));
      }
      if (filters.amountRange) {
        params.append('minAmount', filters.amountRange.min.toString());
        params.append('maxAmount', filters.amountRange.max.toString());
      }
      if (filters.dateRange) {
        params.append('startDate', filters.dateRange.startDate.toISOString());
        params.append('endDate', filters.dateRange.endDate.toISOString());
      }
      if (filters.isUrgent !== undefined) {
        params.append('isUrgent', filters.isUrgent.toString());
      }
      if (filters.isCapitalExpenditure !== undefined) {
        params.append('isCapitalExpenditure', filters.isCapitalExpenditure.toString());
      }
      if (filters.tags?.length) {
        params.append('tags', filters.tags.join(','));
      }
      if (filters.search) {
        params.append('search', filters.search);
      }

      const response = await fetch(`/api/accounting/expenditures/advanced?${params}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch expenditures');
      }
      return response.json();
    },
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

/**
 * Hook for getting expenditure statistics
 */
export function useExpenditureStatistics(
  startDate?: Date,
  endDate?: Date,
  filters: Partial<ExpenditureFilters> = {},
  enabled = true
) {
  return useQuery<{ success: boolean; statistics: ExpenditureStatistics }>({
    queryKey: ['expenditure-statistics', startDate, endDate, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        action: 'statistics'
      });

      if (startDate) {
        params.append('startDate', startDate.toISOString());
      }
      if (endDate) {
        params.append('endDate', endDate.toISOString());
      }
      if (filters.department?.length) {
        params.append('department', filters.department.join(','));
      }
      if (filters.category?.length) {
        params.append('category', filters.category.join(','));
      }

      const response = await fetch(`/api/accounting/expenditures/advanced?${params}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch expenditure statistics');
      }
      return response.json();
    },
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook for getting a specific expenditure by ID
 */
export function useExpenditure(expenditureId: string, enabled = true) {
  return useQuery<{ success: boolean; expenditure: Expenditure }>({
    queryKey: ['expenditure', expenditureId],
    queryFn: async () => {
      const response = await fetch(`/api/accounting/expenditures/advanced/${expenditureId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch expenditure');
      }
      return response.json();
    },
    enabled: enabled && !!expenditureId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

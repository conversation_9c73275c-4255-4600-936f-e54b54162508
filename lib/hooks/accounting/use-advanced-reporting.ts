"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { 
  ReportConfiguration,
  ReportData,
  CustomReportBuilder,
  AnalyticsDashboard,
  ReportTemplate
} from '@/lib/services/accounting/advanced-reporting-service';

// Types for the hook
interface GenerateReportRequest {
  configurationId: string;
  overrides?: {
    dateRange?: {
      startDate: Date;
      endDate: Date;
    };
    filters?: Record<string, unknown>;
  };
}

interface CreateReportConfigurationRequest {
  name: string;
  description?: string;
  type: 'income_summary' | 'budget_analysis' | 'variance_report' | 'trend_analysis' | 'custom_query' | 'dashboard_widget';
  category: 'financial' | 'operational' | 'compliance' | 'strategic' | 'executive';
  scope: {
    dateRange: {
      startDate: Date;
      endDate: Date;
      period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
    };
    filters: {
      budgetIds?: string[];
      categoryIds?: string[];
      sources?: string[];
      amountRange?: { min: number; max: number };
      status?: string[];
      approvalStatus?: string[];
    };
    groupBy: Array<'date' | 'source' | 'category' | 'budget' | 'amount_range' | 'approval_status'>;
    aggregations: Array<'sum' | 'average' | 'count' | 'min' | 'max' | 'variance' | 'growth_rate'>;
  };
  visualization: {
    chartType: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap' | 'table' | 'kpi_card';
    layout: 'single' | 'grid' | 'dashboard' | 'comparison';
    styling: {
      colors: string[];
      theme: 'light' | 'dark' | 'auto';
      responsive: boolean;
    };
  };
  scheduling: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    recipients: string[];
    format: 'pdf' | 'excel' | 'csv' | 'email_summary';
  };
  permissions: {
    viewRoles: string[];
    editRoles: string[];
    shareRoles: string[];
  };
  isTemplate?: boolean;
  isPublic?: boolean;
}

interface ScheduleReportRequest {
  configurationId: string;
  scheduling: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    recipients: string[];
    format: 'pdf' | 'excel' | 'csv' | 'email_summary';
  };
}

interface ReportingConfiguration {
  reportTypes: Array<{
    value: string;
    label: string;
    description: string;
    features: string[];
  }>;
  visualizations: Array<{
    type: string;
    label: string;
    description: string;
    useCases: string[];
  }>;
  aggregationFunctions: Array<{
    value: string;
    label: string;
    description: string;
    applicableTypes: string[];
  }>;
  filterOperators: Array<{
    value: string;
    label: string;
    types: string[];
  }>;
  exportFormats: Array<{
    value: string;
    label: string;
    description: string;
    mimeType: string;
    features: string[];
  }>;
}

/**
 * Hook for advanced reporting operations
 */
export function useAdvancedReporting() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get reporting configuration
  const {
    data: configurationData,
    isLoading: isLoadingConfiguration,
    error: configurationError
  } = useQuery<{ success: boolean; configuration: ReportingConfiguration }>({
    queryKey: ['advanced-reporting-configuration'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/reports/advanced?type=configuration');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reporting configuration');
      }
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Get report templates
  const {
    data: templatesData,
    isLoading: isLoadingTemplates,
    error: templatesError
  } = useQuery<{ success: boolean; templates: ReportTemplate[] }>({
    queryKey: ['advanced-reporting-templates'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/reports/advanced?type=templates');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch report templates');
      }
      return response.json();
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  // Generate report mutation
  const generateReportMutation = useMutation<
    { success: boolean; report: ReportData },
    Error,
    GenerateReportRequest
  >({
    mutationFn: async (request: GenerateReportRequest) => {
      const response = await fetch('/api/accounting/reports/advanced?action=generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate report');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Report Generated",
        description: `Report generated successfully with ${data.report.dataPoints.length} data points`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['advanced-reports'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Report Generation Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Create report configuration mutation
  const createConfigurationMutation = useMutation<
    { success: boolean; configuration: ReportConfiguration },
    Error,
    CreateReportConfigurationRequest
  >({
    mutationFn: async (request: CreateReportConfigurationRequest) => {
      const response = await fetch('/api/accounting/reports/advanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create report configuration');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Configuration Created",
        description: `Report configuration "${variables.name}" created successfully`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['advanced-reporting-configurations'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Configuration Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Schedule report mutation
  const scheduleReportMutation = useMutation<
    { success: boolean; nextRun: Date },
    Error,
    ScheduleReportRequest
  >({
    mutationFn: async (request: ScheduleReportRequest) => {
      const response = await fetch('/api/accounting/reports/advanced?action=schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to schedule report');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Report Scheduled",
        description: `Report scheduled successfully. Next run: ${new Date(data.nextRun).toLocaleDateString()}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Scheduling Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Generate report with error handling
  const generateReport = async (request: GenerateReportRequest): Promise<ReportData | null> => {
    try {
      const result = await generateReportMutation.mutateAsync(request);
      return result.report;
    } catch (error) {
      console.error('Error generating report:', error);
      return null;
    }
  };

  // Create report configuration with error handling
  const createConfiguration = async (request: CreateReportConfigurationRequest): Promise<ReportConfiguration | null> => {
    try {
      const result = await createConfigurationMutation.mutateAsync(request);
      return result.configuration;
    } catch (error) {
      console.error('Error creating configuration:', error);
      return null;
    }
  };

  // Schedule report with error handling
  const scheduleReport = async (request: ScheduleReportRequest): Promise<boolean> => {
    try {
      await scheduleReportMutation.mutateAsync(request);
      return true;
    } catch (error) {
      console.error('Error scheduling report:', error);
      return false;
    }
  };

  // Export report
  const exportReport = async (reportId: string, format: 'pdf' | 'excel' | 'csv' | 'json'): Promise<boolean> => {
    try {
      const response = await fetch(`/api/accounting/reports/advanced?type=export&reportId=${reportId}&format=${format}`);
      
      if (!response.ok) {
        throw new Error('Failed to export report');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `report_${reportId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `Report exported as ${format.toUpperCase()}`,
      });

      return true;
    } catch (error) {
      toast({
        title: "Export Error",
        description: error instanceof Error ? error.message : 'Failed to export report',
        variant: "destructive",
      });
      return false;
    }
  };

  // Validate report configuration
  const validateConfiguration = (config: CreateReportConfigurationRequest): string[] => {
    const errors: string[] = [];

    if (!config.name.trim()) {
      errors.push('Report name is required');
    }

    if (config.scope.dateRange.startDate >= config.scope.dateRange.endDate) {
      errors.push('End date must be after start date');
    }

    if (config.scope.groupBy.length === 0) {
      errors.push('At least one grouping field is required');
    }

    if (config.scope.aggregations.length === 0) {
      errors.push('At least one aggregation function is required');
    }

    if (config.scheduling.enabled && config.scheduling.recipients.length === 0) {
      errors.push('Recipients are required for scheduled reports');
    }

    return errors;
  };

  // Get default configuration
  const getDefaultConfiguration = (): Partial<CreateReportConfigurationRequest> => {
    return {
      type: 'income_summary',
      category: 'financial',
      scope: {
        dateRange: {
          startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
          endDate: new Date(),
          period: 'monthly'
        },
        filters: {},
        groupBy: ['date', 'source'],
        aggregations: ['sum', 'count']
      },
      visualization: {
        chartType: 'bar',
        layout: 'single',
        styling: {
          colors: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'],
          theme: 'light',
          responsive: true
        }
      },
      scheduling: {
        enabled: false,
        frequency: 'monthly',
        recipients: [],
        format: 'pdf'
      },
      permissions: {
        viewRoles: [],
        editRoles: [],
        shareRoles: []
      },
      isTemplate: false,
      isPublic: false
    };
  };

  // Format report data for visualization
  const formatReportData = (reportData: ReportData) => {
    return {
      chartData: reportData.dataPoints.map(point => ({
        ...point.dimensions,
        ...point.metrics,
        timestamp: point.timestamp
      })),
      summary: {
        totalRecords: reportData.summary.totalRecords,
        dateRange: reportData.summary.dateRange,
        keyMetrics: reportData.summary.aggregations,
        trends: reportData.summary.trends,
        insights: reportData.summary.insights
      },
      performance: {
        executionTime: reportData.performance.executionTime,
        dataSize: reportData.performance.dataSize,
        queryComplexity: reportData.performance.queryComplexity
      }
    };
  };

  return {
    // Configuration and templates
    configuration: configurationData?.configuration,
    isLoadingConfiguration,
    configurationError,
    templates: templatesData?.templates,
    isLoadingTemplates,
    templatesError,

    // Report operations
    generateReport,
    isGeneratingReport: generateReportMutation.isPending,
    generateReportError: generateReportMutation.error,

    // Configuration operations
    createConfiguration,
    isCreatingConfiguration: createConfigurationMutation.isPending,
    createConfigurationError: createConfigurationMutation.error,

    // Scheduling operations
    scheduleReport,
    isSchedulingReport: scheduleReportMutation.isPending,
    scheduleReportError: scheduleReportMutation.error,

    // Export operations
    exportReport,

    // Utilities
    validateConfiguration,
    getDefaultConfiguration,
    formatReportData,

    // Raw mutations for advanced usage
    generateReportMutation,
    createConfigurationMutation,
    scheduleReportMutation
  };
}

/**
 * Hook for getting specific report data
 */
export function useReportData(reportId: string, enabled = true) {
  return useQuery<{ success: boolean; report: ReportData }>({
    queryKey: ['advanced-report', reportId],
    queryFn: async () => {
      const response = await fetch(`/api/accounting/reports/advanced?reportId=${reportId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch report data');
      }
      return response.json();
    },
    enabled: enabled && !!reportId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

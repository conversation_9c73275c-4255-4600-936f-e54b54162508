// lib/hooks/accounting/use-expenditure-stats.ts
import { useQuery } from '@tanstack/react-query';
import { useBudget } from './use-budget';

export interface ExpenditureStats {
  totalExpenditure: number;
  actualExpenditure: number;
  budgetedExpenditure: number;
  plannedBudgetExpenditure: number;
  expenditurePercentage: number;
  officeSupplies: number;
  travelTransport: number;
  utilities: number;
  professionalServices: number;
  equipment: number;
  maintenance: number;
  training: number;
  communications: number;
  insurance: number;
  personnel: number;
  administrative: number;
  otherExpenditure: number;
  yearOverYearChange: number;
  budgetVariance: number;
  budgetVariancePercentage: number;
  actualBudgetVariance: number;
  actualBudgetVariancePercentage: number;
  trendDirection: 'up' | 'down' | 'stable';
  expenditureByCategory: Array<{
    category: string;
    amount: number;
    percentage: number;
    budgeted: number;
    actual: number;
    variance: number;
    utilizationPercentage: number;
  }>;
  monthlyExpenditure: Array<{
    month: string;
    amount: number;
    budgeted: number;
    actual: number;
    variance: number;
    utilizationPercentage: number;
  }>;
}

/**
 * Hook to calculate expenditure statistics for dashboards and reports
 */
export function useExpenditureStats(
  fiscalYear: string,
  budgetId?: string,
  options?: {
    refetchInterval?: number;
    refetchIntervalInBackground?: boolean;
    refetchOnWindowFocus?: boolean;
    staleTime?: number;
  }
) {
  const { activeBudgets } = useBudget();

  // Default options
  const defaultOptions = {
    refetchInterval: false,
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  };

  const queryOptions = { ...defaultOptions, ...options };

  // Fetch expenditure summary data with fallback
  const {
    data: expenditureSummary,
    isLoading: isLoadingExpenditureSummary,
    refetch: refetchExpenditureSummary
  } = useQuery({
    queryKey: ['expenditure', 'summary', fiscalYear, budgetId],
    queryFn: async () => {
      // Try with budgetId first
      let queryParams = new URLSearchParams();
      if (fiscalYear) queryParams.append('fiscalYear', fiscalYear);
      if (budgetId) queryParams.append('budgetId', budgetId);

      let response = await fetch(`/api/accounting/expense/summary?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch expenditure summary');
      }

      let data = await response.json();

      // If no data found with budgetId, try without budgetId as fallback
      if (budgetId && data.totalExpenditure === 0 && data.totalRecords === 0) {
        console.log('No expenditures found for specific budget, trying without budget filter...');
        queryParams = new URLSearchParams();
        if (fiscalYear) queryParams.append('fiscalYear', fiscalYear);

        response = await fetch(`/api/accounting/expense/summary?${queryParams.toString()}`);
        if (response.ok) {
          const fallbackData = await response.json();
          if (fallbackData.totalExpenditure > 0) {
            console.log('Found expenditures without budget filter, using fallback data');
            return fallbackData;
          }
        }
      }

      return data;
    },
    staleTime: queryOptions.staleTime,
    refetchInterval: queryOptions.refetchInterval,
    refetchIntervalInBackground: queryOptions.refetchIntervalInBackground,
    refetchOnWindowFocus: queryOptions.refetchOnWindowFocus,
  });

  // Fetch budget data for comparison
  const { data: budgetData, isLoading: isLoadingBudget } = useQuery({
    queryKey: ['budget', 'expenditure-comparison', fiscalYear, budgetId],
    queryFn: async () => {
      let targetBudgetId = budgetId;

      if (!targetBudgetId) {
        // Get active budget if no specific budget ID provided
        const activeBudget = activeBudgets?.find(budget =>
          budget.status === 'active' && budget.fiscalYear === fiscalYear
        ) || activeBudgets?.[0];

        if (!activeBudget) return null;
        targetBudgetId = activeBudget.id || activeBudget._id;
      }

      const response = await fetch(`/api/accounting/budget/${targetBudgetId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch budget data');
      }

      return await response.json();
    },
    enabled: !!budgetId || (activeBudgets && activeBudgets.length > 0),
    staleTime: 10 * 60 * 1000, // 10 minutes for budget data
  });

  // Fetch previous year data for comparison
  const { data: previousYearData, isLoading: isLoadingPreviousYear } = useQuery({
    queryKey: ['expenditure', 'previous-year', fiscalYear],
    queryFn: async () => {
      // Calculate previous fiscal year
      const currentYear = parseInt(fiscalYear.split('-')[0]);
      const previousFiscalYear = `${currentYear - 1}-${currentYear}`;

      const queryParams = new URLSearchParams();
      queryParams.append('fiscalYear', previousFiscalYear);
      if (budgetId) queryParams.append('budgetId', budgetId);

      const response = await fetch(`/api/accounting/expense/summary?${queryParams.toString()}`);
      if (!response.ok) {
        // Don't throw error for previous year data, just return null
        return null;
      }

      return await response.json();
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for historical data
  });

  // Fetch monthly expenditure data
  const { data: monthlyData, isLoading: isLoadingMonthly } = useQuery({
    queryKey: ['expenditure', 'monthly', fiscalYear, budgetId],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (fiscalYear) queryParams.append('fiscalYear', fiscalYear);
      if (budgetId) queryParams.append('budgetId', budgetId);
      
      const response = await fetch(`/api/accounting/expense/monthly?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch monthly expenditure data');
      }
      
      return await response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate comprehensive expenditure statistics
  const expenditureStats: ExpenditureStats = (() => {
    if (!expenditureSummary) {
      return {
        totalExpenditure: 0,
        actualExpenditure: 0,
        budgetedExpenditure: 0,
        plannedBudgetExpenditure: 0,
        expenditurePercentage: 0,
        officeSupplies: 0,
        travelTransport: 0,
        utilities: 0,
        professionalServices: 0,
        equipment: 0,
        maintenance: 0,
        training: 0,
        communications: 0,
        insurance: 0,
        personnel: 0,
        administrative: 0,
        otherExpenditure: 0,
        yearOverYearChange: 0,
        budgetVariance: 0,
        budgetVariancePercentage: 0,
        actualBudgetVariance: 0,
        actualBudgetVariancePercentage: 0,
        trendDirection: 'stable',
        expenditureByCategory: [],
        monthlyExpenditure: []
      };
    }

    // Extract expenditure data
    const totalExpenditure = expenditureSummary.totalExpenditure || 0;
    const actualExpenditure = expenditureSummary.actualExpenditure || 0;
    const budgetedExpenditure = expenditureSummary.budgetedExpenditure || 0;

    // Get budget data for comparison
    const plannedBudgetExpenditure = budgetData?.totalExpense || budgetData?.totalBudgetedExpense || 0;

    // Calculate expenditure by category
    const categoryData = expenditureSummary.expenditureByCategory || [];
    const officeSupplies = categoryData.find((c: any) => c.category === 'office_supplies')?.amount || 0;
    const travelTransport = categoryData.find((c: any) => c.category === 'travel_transport')?.amount || 0;
    const utilities = categoryData.find((c: any) => c.category === 'utilities')?.amount || 0;
    const professionalServices = categoryData.find((c: any) => c.category === 'professional_services')?.amount || 0;
    const equipment = categoryData.find((c: any) => c.category === 'equipment')?.amount || 0;
    const maintenance = categoryData.find((c: any) => c.category === 'maintenance')?.amount || 0;
    const training = categoryData.find((c: any) => c.category === 'training')?.amount || 0;
    const communications = categoryData.find((c: any) => c.category === 'communications')?.amount || 0;
    const insurance = categoryData.find((c: any) => c.category === 'insurance')?.amount || 0;
    const personnel = categoryData.find((c: any) => c.category === 'personnel')?.amount || 0;
    const administrative = categoryData.find((c: any) => c.category === 'administrative')?.amount || 0;
    const otherExpenditure = categoryData.find((c: any) => c.category === 'other')?.amount || 0;

    // Calculate year-over-year change
    const previousTotal = previousYearData?.totalExpenditure || 0;
    const yearOverYearChange = previousTotal > 0 ? ((totalExpenditure - previousTotal) / previousTotal) * 100 : 0;

    // Calculate budget variances
    const budgetVariance = actualExpenditure - budgetedExpenditure;
    const budgetVariancePercentage = budgetedExpenditure > 0 ? (budgetVariance / budgetedExpenditure) * 100 : 0;
    
    const actualBudgetVariance = actualExpenditure - plannedBudgetExpenditure;
    const actualBudgetVariancePercentage = plannedBudgetExpenditure > 0 ? (actualBudgetVariance / plannedBudgetExpenditure) * 100 : 0;

    // Determine trend direction
    let trendDirection: 'up' | 'down' | 'stable' = 'stable';
    if (yearOverYearChange > 5) trendDirection = 'up';
    else if (yearOverYearChange < -5) trendDirection = 'down';

    // Calculate expenditure percentage (of total budget)
    const expenditurePercentage = plannedBudgetExpenditure > 0 ? (actualExpenditure / plannedBudgetExpenditure) * 100 : 0;

    // Process expenditure by category with enhanced data
    const expenditureByCategory = categoryData.map((category: any) => ({
      category: category.category,
      amount: category.amount || 0,
      percentage: totalExpenditure > 0 ? ((category.amount || 0) / totalExpenditure) * 100 : 0,
      budgeted: category.budgeted || 0,
      actual: category.actual || category.amount || 0,
      variance: (category.actual || category.amount || 0) - (category.budgeted || 0),
      utilizationPercentage: category.budgeted > 0 ? ((category.actual || category.amount || 0) / category.budgeted) * 100 : 0
    }));

    // Prepare monthly expenditure data
    const monthlyExpenditure = monthlyData?.monthlyExpenditure || [];

    return {
      totalExpenditure,
      actualExpenditure,
      budgetedExpenditure,
      plannedBudgetExpenditure,
      expenditurePercentage,
      officeSupplies,
      travelTransport,
      utilities,
      professionalServices,
      equipment,
      maintenance,
      training,
      communications,
      insurance,
      personnel,
      administrative,
      otherExpenditure,
      yearOverYearChange,
      budgetVariance,
      budgetVariancePercentage,
      actualBudgetVariance,
      actualBudgetVariancePercentage,
      trendDirection,
      expenditureByCategory,
      monthlyExpenditure
    };
  })();

  // Combined refetch function
  const refetch = async () => {
    await Promise.all([
      refetchExpenditureSummary(),
    ]);
  };

  return {
    expenditureStats,
    isLoading: isLoadingExpenditureSummary || isLoadingBudget || isLoadingPreviousYear || isLoadingMonthly,
    isLoadingExpenditureSummary,
    isLoadingBudget,
    isLoadingPreviousYear,
    isLoadingMonthly,
    activeBudget: budgetData,
    previousYearExpenditure: previousYearData,
    refetch,
    refetchExpenditureSummary,
  };
}

"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { 
  ReconciliationSession,
  ReconciliationTransaction,
  ReconciliationMatch,
  ReconciliationVariance,
  MatchingRule
} from '@/lib/services/accounting/income-reconciliation-service';

// Types for the hook
interface StartReconciliationRequest {
  name: string;
  description?: string;
  source: string;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  settings: {
    matchingRules: string[];
    autoApproveThreshold: number;
    requireManualReview: boolean;
    enableDuplicateDetection: boolean;
  };
  internalTransactions: ReconciliationTransaction[];
  externalTransactions: ReconciliationTransaction[];
  matchingRules?: MatchingRule[];
}

interface ReconciliationResponse {
  success: boolean;
  session: ReconciliationSession;
  results: {
    matches: ReconciliationMatch[];
    variances: ReconciliationVariance[];
    statistics: {
      totalInternal: number;
      totalExternal: number;
      matched: number;
      unmatched: number;
      variances: number;
      autoMatched: number;
      manualMatched: number;
      confidence: number;
    };
  };
}

interface ReconciliationConfiguration {
  defaultMatchingRules: MatchingRule[];
  supportedSources: string[];
  confidenceLevels: Array<{ value: number; label: string }>;
  toleranceRanges: {
    amount: { min: number; max: number; step: number };
    date: { min: number; max: number; step: number };
    description: { min: number; max: number; step: number };
  };
}

interface UpdateMatchStatusRequest {
  matchId: string;
  status: 'pending' | 'approved' | 'rejected' | 'auto-matched';
  notes?: string;
}

interface ReconciliationStatistics {
  totalSessions: number;
  completedSessions: number;
  totalMatches: number;
  autoMatches: number;
  manualMatches: number;
  totalVariances: number;
  averageConfidence: number;
  processingTime: number;
}

/**
 * Hook for income reconciliation operations
 */
export function useIncomeReconciliation() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get reconciliation configuration
  const {
    data: configurationData,
    isLoading: isLoadingConfiguration,
    error: configurationError
  } = useQuery<{ success: boolean; configuration: ReconciliationConfiguration }>({
    queryKey: ['reconciliation-configuration'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/income/reconciliation');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reconciliation configuration');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Start reconciliation mutation
  const startReconciliationMutation = useMutation<ReconciliationResponse, Error, StartReconciliationRequest>({
    mutationFn: async (request: StartReconciliationRequest) => {
      const response = await fetch('/api/accounting/income/reconciliation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start reconciliation');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Reconciliation Completed",
        description: `Reconciliation session "${variables.name}" completed successfully`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['reconciliation-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['reconciliation-statistics'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Reconciliation Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update match status mutation
  const updateMatchStatusMutation = useMutation<
    { success: boolean; match: ReconciliationMatch },
    Error,
    UpdateMatchStatusRequest
  >({
    mutationFn: async (request: UpdateMatchStatusRequest) => {
      const response = await fetch('/api/accounting/income/reconciliation', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update match status');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Match Status Updated",
        description: `Match status updated to ${variables.status}`,
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['reconciliation-sessions'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Update Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Start reconciliation with error handling
  const startReconciliation = async (request: StartReconciliationRequest): Promise<ReconciliationResponse | null> => {
    try {
      const result = await startReconciliationMutation.mutateAsync(request);
      return result;
    } catch (error) {
      console.error('Error starting reconciliation:', error);
      return null;
    }
  };

  // Update match status with error handling
  const updateMatchStatus = async (request: UpdateMatchStatusRequest): Promise<boolean> => {
    try {
      await updateMatchStatusMutation.mutateAsync(request);
      return true;
    } catch (error) {
      console.error('Error updating match status:', error);
      return false;
    }
  };

  // Validate reconciliation request
  const validateReconciliationRequest = (request: StartReconciliationRequest): string[] => {
    const errors: string[] = [];

    if (!request.name.trim()) {
      errors.push('Session name is required');
    }

    if (!request.source.trim()) {
      errors.push('Source is required');
    }

    if (request.dateRange.startDate >= request.dateRange.endDate) {
      errors.push('End date must be after start date');
    }

    if (request.internalTransactions.length === 0) {
      errors.push('At least one internal transaction is required');
    }

    if (request.externalTransactions.length === 0) {
      errors.push('At least one external transaction is required');
    }

    if (request.settings.autoApproveThreshold < 0 || request.settings.autoApproveThreshold > 1) {
      errors.push('Auto-approve threshold must be between 0 and 1');
    }

    return errors;
  };

  // Get default reconciliation settings
  const getDefaultSettings = () => {
    return {
      matchingRules: ['exact_match', 'amount_date_match'],
      autoApproveThreshold: 0.95,
      requireManualReview: true,
      enableDuplicateDetection: true
    };
  };

  // Format reconciliation results for display
  const formatReconciliationResults = (results: ReconciliationResponse['results']) => {
    const { matches, variances, statistics } = results;

    return {
      summary: {
        totalTransactions: statistics.totalInternal + statistics.totalExternal,
        matchedTransactions: statistics.matched,
        unmatchedTransactions: statistics.unmatched,
        variancesFound: statistics.variances,
        matchRate: statistics.totalInternal > 0 ? (statistics.matched / statistics.totalInternal) * 100 : 0,
        confidence: statistics.confidence * 100
      },
      matches: {
        total: matches.length,
        autoMatched: matches.filter(m => m.status === 'auto-matched').length,
        pending: matches.filter(m => m.status === 'pending').length,
        approved: matches.filter(m => m.status === 'approved').length,
        rejected: matches.filter(m => m.status === 'rejected').length,
        highConfidence: matches.filter(m => m.confidence > 0.9).length,
        mediumConfidence: matches.filter(m => m.confidence > 0.7 && m.confidence <= 0.9).length,
        lowConfidence: matches.filter(m => m.confidence <= 0.7).length
      },
      variances: {
        total: variances.length,
        missingInternal: variances.filter(v => v.type === 'missing_internal').length,
        missingExternal: variances.filter(v => v.type === 'missing_external').length,
        amountDifferences: variances.filter(v => v.type === 'amount_difference').length,
        dateDifferences: variances.filter(v => v.type === 'date_difference').length,
        duplicates: variances.filter(v => v.type === 'duplicate').length,
        critical: variances.filter(v => v.severity === 'critical').length,
        high: variances.filter(v => v.severity === 'high').length,
        medium: variances.filter(v => v.severity === 'medium').length,
        low: variances.filter(v => v.severity === 'low').length
      }
    };
  };

  // Calculate reconciliation metrics
  const calculateReconciliationMetrics = (results: ReconciliationResponse['results']) => {
    const { statistics } = results;
    
    return {
      efficiency: {
        automationRate: statistics.matched > 0 ? (statistics.autoMatched / statistics.matched) * 100 : 0,
        manualReviewRate: statistics.matched > 0 ? (statistics.manualMatched / statistics.matched) * 100 : 0,
        varianceRate: statistics.totalInternal > 0 ? (statistics.variances / statistics.totalInternal) * 100 : 0
      },
      quality: {
        matchAccuracy: statistics.confidence * 100,
        completeness: statistics.totalInternal > 0 ? (statistics.matched / statistics.totalInternal) * 100 : 0,
        dataIntegrity: statistics.variances === 0 ? 100 : Math.max(0, 100 - (statistics.variances / statistics.totalInternal) * 100)
      },
      performance: {
        totalProcessed: statistics.totalInternal + statistics.totalExternal,
        successfulMatches: statistics.matched,
        failedMatches: statistics.unmatched,
        overallScore: (statistics.confidence * 0.4) + ((statistics.matched / Math.max(statistics.totalInternal, 1)) * 0.6)
      }
    };
  };

  return {
    // Configuration
    configuration: configurationData?.configuration,
    isLoadingConfiguration,
    configurationError,

    // Reconciliation operations
    startReconciliation,
    isStartingReconciliation: startReconciliationMutation.isPending,
    reconciliationError: startReconciliationMutation.error,

    // Match status operations
    updateMatchStatus,
    isUpdatingMatchStatus: updateMatchStatusMutation.isPending,
    updateMatchError: updateMatchStatusMutation.error,

    // Utilities
    validateReconciliationRequest,
    getDefaultSettings,
    formatReconciliationResults,
    calculateReconciliationMetrics,

    // Raw mutations for advanced usage
    startReconciliationMutation,
    updateMatchStatusMutation
  };
}

/**
 * Hook for getting reconciliation session data
 */
export function useReconciliationSession(sessionId: string, enabled = true) {
  return useQuery<{ success: boolean; session: ReconciliationSession }>({
    queryKey: ['reconciliation-session', sessionId],
    queryFn: async () => {
      const response = await fetch(`/api/accounting/income/reconciliation?sessionId=${sessionId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reconciliation session');
      }
      return response.json();
    },
    enabled: enabled && !!sessionId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for getting reconciliation statistics
 */
export function useReconciliationStatistics(
  startDate: Date,
  endDate: Date,
  source?: string,
  enabled = true
) {
  return useQuery<{ success: boolean; statistics: ReconciliationStatistics }>({
    queryKey: ['reconciliation-statistics', startDate.toISOString(), endDate.toISOString(), source],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });
      
      if (source) {
        params.append('source', source);
      }

      const response = await fetch(`/api/accounting/income/reconciliation?${params}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reconciliation statistics');
      }
      return response.json();
    },
    enabled: enabled && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
}

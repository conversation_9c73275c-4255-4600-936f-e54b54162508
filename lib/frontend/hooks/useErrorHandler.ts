// lib/frontend/hooks/useErrorHandler.ts
"use client"

import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'

export interface ErrorAction {
  label: string
  action: string
  type: 'button' | 'link' | 'retry'
  variant?: 'primary' | 'secondary' | 'destructive'
  url?: string
  data?: Record<string, any>
}

export interface StructuredError {
  id: string
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  code: string
  message: string
  userMessage: string
  details?: string
  suggestions?: string[]
  actions?: ErrorAction[]
  timestamp: string
  context?: Record<string, any>
}

export interface ErrorResponse {
  success: false
  error: StructuredError
  httpStatus: number
}

export function useErrorHandler() {
  const [error, setError] = useState<StructuredError | null>(null)
  const [isErrorOpen, setIsErrorOpen] = useState(false)
  const { toast } = useToast()

  /**
   * Handle API errors from response
   */
  const handleApiError = async (response: Response): Promise<void> => {
    try {
      const errorData: ErrorResponse = await response.json()
      
      if (errorData.error && typeof errorData.error === 'object') {
        // Structured error from our error service
        setError(errorData.error)
        setIsErrorOpen(true)
        
        // Also show toast for immediate feedback
        toast({
          title: "Error",
          description: errorData.error.userMessage,
          variant: "destructive"
        })
      } else {
        // Fallback for non-structured errors
        handleGenericError(new Error(`Request failed with status ${response.status}`))
      }
    } catch (parseError) {
      // If we can't parse the error response, create a generic error
      handleGenericError(new Error(`Request failed with status ${response.status}`))
    }
  }

  /**
   * Handle generic errors (non-API)
   */
  const handleGenericError = (error: Error | string): void => {
    const errorMessage = typeof error === 'string' ? error : error.message
    
    const genericError: StructuredError = {
      id: `generic-${Date.now()}`,
      type: 'SYSTEM',
      severity: 'MEDIUM',
      code: 'GENERIC_ERROR',
      message: errorMessage,
      userMessage: 'An unexpected error occurred. Please try again.',
      timestamp: new Date().toISOString(),
      actions: [
        {
          label: 'Retry',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        }
      ]
    }
    
    setError(genericError)
    setIsErrorOpen(true)
    
    toast({
      title: "Error",
      description: genericError.userMessage,
      variant: "destructive"
    })
  }

  /**
   * Handle error actions (retry, debug, etc.)
   */
  const handleErrorAction = (action: string, data?: Record<string, any>): void => {
    switch (action) {
      case 'retry':
        // Close error and let parent component handle retry
        hideError()
        break
      case 'debug':
        console.log('Error debug info:', { error, data })
        break
      case 'refresh':
        window.location.reload()
        break
      default:
        console.log('Unknown error action:', action, data)
    }
  }

  /**
   * Hide error overlay
   */
  const hideError = (): void => {
    setError(null)
    setIsErrorOpen(false)
  }

  /**
   * Show error overlay with custom error
   */
  const showError = (customError: StructuredError): void => {
    setError(customError)
    setIsErrorOpen(true)
  }

  return {
    error,
    isErrorOpen,
    handleApiError,
    handleGenericError,
    handleErrorAction,
    hideError,
    showError
  }
}

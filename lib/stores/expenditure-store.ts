// lib/stores/expenditure-store.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface Expenditure {
  id: string;
  _id?: string;
  date: Date;
  category: 'office_supplies' | 'travel_transport' | 'utilities' | 'professional_services' | 'equipment' | 'maintenance' | 'training' | 'communications' | 'insurance' | 'personnel' | 'administrative' | 'other';
  subcategory?: string;
  amount: number;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'paid' | 'cancelled';
  paymentMethod?: string;
  vendor?: string;
  department?: string;
  costCenter?: string;
  // Budget-related fields
  budget?: string;
  budgetName?: string;
  budgetCategory?: string;
  budgetCategoryName?: string;
  budgetSubcategory?: string;
  budgetSubcategoryName?: string;
  appliedToBudget?: boolean;
  notes?: string;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Status tracking
  submittedAt?: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
  paidAt?: Date;
  cancelledAt?: Date;
  approvedBy?: string;
  rejectedBy?: string;
  paidBy?: string;
  cancelledBy?: string;
  rejectionReason?: string;
}

export interface BankAccount {
  id: string;
  name: string;
  accountNumber: string;
  bankName: string;
  currency: string;
  isActive: boolean;
}

export interface FiscalYear {
  value: string;
  label: string;
  startDate: Date;
  endDate: Date;
  isCurrent: boolean;
}

export interface PaymentMethod {
  value: string;
  label: string;
  isActive: boolean;
}

export interface ExpenseCategory {
  value: string;
  label: string;
  description?: string;
}

export interface Budget {
  id: string;
  name: string;
  fiscalYear: string;
  status: string;
  totalExpense: number;
  totalActualExpense: number;
}

export interface BudgetCategory {
  id: string;
  name: string;
  type: string;
  budget: string;
  budgetedAmount: number;
  actualAmount: number;
}

export interface BudgetSubcategory {
  id: string;
  name: string;
  category: string;
  budgetedAmount: number;
  actualAmount: number;
}

export interface ExpenditureFilters {
  fiscalYear?: string;
  status?: string;
  category?: string;
  department?: string;
  vendor?: string;
  budget?: string;
  budgetCategory?: string;
  startDate?: Date;
  endDate?: Date;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

export interface ExpenditureState {
  // Expenditure data
  expenditures: Expenditure[];
  selectedExpenditure: Expenditure | null;
  isLoading: boolean;
  error: string | null;
  
  // Form data
  bankAccounts: BankAccount[];
  fiscalYears: FiscalYear[];
  paymentMethods: PaymentMethod[];
  expenseCategories: ExpenseCategory[];
  
  // Filters
  filters: ExpenditureFilters;
  
  // Pagination
  page: number;
  limit: number;
  totalCount: number;
  
  // Budget-related state
  selectedBudget: Budget | null;
  budgets: Budget[];
  budgetCategories: BudgetCategory[];
  budgetSubcategories: BudgetSubcategory[];
  
  // Loading states for different data types
  isLoadingBankAccounts: boolean;
  isLoadingFiscalYears: boolean;
  isLoadingPaymentMethods: boolean;
  isLoadingBudgets: boolean;
  isLoadingCategories: boolean;
  isLoadingSubcategories: boolean;
  
  // Data ready flags
  isFormDataReady: boolean;
  
  // Actions
  setExpenditures: (expenditures: Expenditure[]) => void;
  addExpenditure: (expenditure: Expenditure) => void;
  updateExpenditure: (id: string, expenditure: Partial<Expenditure>) => void;
  removeExpenditure: (id: string) => void;
  setSelectedExpenditure: (expenditure: Expenditure | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Form data actions
  setBankAccounts: (accounts: BankAccount[]) => void;
  setFiscalYears: (years: FiscalYear[]) => void;
  setPaymentMethods: (methods: PaymentMethod[]) => void;
  setExpenseCategories: (categories: ExpenseCategory[]) => void;
  
  // Filter actions
  setFilters: (filters: Partial<ExpenditureFilters>) => void;
  clearFilters: () => void;
  
  // Pagination actions
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  
  // Budget actions
  setSelectedBudget: (budget: Budget | null) => void;
  setBudgets: (budgets: Budget[]) => void;
  setBudgetCategories: (categories: BudgetCategory[]) => void;
  setBudgetSubcategories: (subcategories: BudgetSubcategory[]) => void;
  
  // Utility functions
  getCurrentFiscalYear: () => string;
  getActiveFiscalYears: () => FiscalYear[];
  getExpendituresByStatus: (status: string) => Expenditure[];
  getTotalExpenditureAmount: () => number;
  
  // Form data initialization
  initializeFormData: () => Promise<void>;
  
  // API actions
  fetchExpenditures: () => Promise<void>;
  createExpenditure: (expenditure: Omit<Expenditure, 'id'>) => Promise<void>;
  updateExpenditureById: (id: string, expenditure: Partial<Expenditure>) => Promise<void>;
  deleteExpenditure: (id: string) => Promise<void>;
  
  // Status management
  updateExpenditureStatus: (id: string, status: string, notes?: string, reason?: string) => Promise<void>;
  
  // Bulk operations
  bulkUpdateStatus: (ids: string[], status: string, notes?: string, reason?: string) => Promise<void>;
  bulkDeleteExpenditures: (ids: string[], reason: string) => Promise<void>;
}

// Default form data
const DEFAULT_FORM_DATA = {
  expenseCategories: [
    { value: 'office_supplies', label: 'Office Supplies' },
    { value: 'travel_transport', label: 'Travel & Transport' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'training', label: 'Training & Development' },
    { value: 'communications', label: 'Communications' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'personnel', label: 'Personnel' },
    { value: 'administrative', label: 'Administrative' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2025-2026', label: '2025-2026 (Current)', startDate: new Date('2025-04-01'), endDate: new Date('2026-03-31'), isCurrent: true },
    { value: '2024-2025', label: '2024-2025', startDate: new Date('2024-04-01'), endDate: new Date('2025-03-31'), isCurrent: false },
    { value: '2023-2024', label: '2023-2024', startDate: new Date('2023-04-01'), endDate: new Date('2024-03-31'), isCurrent: false },
  ],
  paymentMethods: [
    { value: 'bank_transfer', label: 'Bank Transfer', isActive: true },
    { value: 'check', label: 'Check', isActive: true },
    { value: 'cash', label: 'Cash', isActive: true },
    { value: 'credit_card', label: 'Credit Card', isActive: true },
    { value: 'mobile_money', label: 'Mobile Money', isActive: true },
  ],
};

export const useExpenditureStore = create<ExpenditureState>()(
  persist(
    immer((set, get) => ({
      // Expenditure data
      expenditures: [],
      selectedExpenditure: null,
      isLoading: false,
      error: null,

      // Form data
      bankAccounts: [],
      fiscalYears: DEFAULT_FORM_DATA.fiscalYears,
      paymentMethods: DEFAULT_FORM_DATA.paymentMethods,
      expenseCategories: DEFAULT_FORM_DATA.expenseCategories,

      // Filters
      filters: {},

      // Pagination
      page: 1,
      limit: 10,
      totalCount: 0,

      // Budget-related state
      selectedBudget: null,
      budgets: [],
      budgetCategories: [],
      budgetSubcategories: [],

      // Loading states
      isLoadingBankAccounts: false,
      isLoadingFiscalYears: false,
      isLoadingPaymentMethods: false,
      isLoadingBudgets: false,
      isLoadingCategories: false,
      isLoadingSubcategories: false,

      // Data ready flags
      isFormDataReady: false,
      
      // Actions
      setExpenditures: (expenditures) => set((state) => {
        state.expenditures = expenditures;
      }),

      addExpenditure: (expenditure) => set((state) => {
        state.expenditures.push(expenditure);
      }),

      updateExpenditure: (id, expenditure) => set((state) => {
        const index = state.expenditures.findIndex(e => e.id === id || e._id === id);
        if (index !== -1) {
          state.expenditures[index] = { ...state.expenditures[index], ...expenditure };
        }
      }),

      removeExpenditure: (id) => set((state) => {
        state.expenditures = state.expenditures.filter(e => e.id !== id && e._id !== id);
      }),

      setSelectedExpenditure: (expenditure) => set((state) => {
        state.selectedExpenditure = expenditure;
      }),

      setLoading: (loading) => set((state) => {
        state.isLoading = loading;
      }),

      setError: (error) => set((state) => {
        state.error = error;
      }),

      // Form data actions
      setBankAccounts: (accounts) => set((state) => {
        state.bankAccounts = accounts;
      }),

      setFiscalYears: (years) => set((state) => {
        state.fiscalYears = years;
      }),

      setPaymentMethods: (methods) => set((state) => {
        state.paymentMethods = methods;
      }),

      setExpenseCategories: (categories) => set((state) => {
        state.expenseCategories = categories;
      }),

      // Filter actions
      setFilters: (filters) => set((state) => {
        state.filters = { ...state.filters, ...filters };
      }),

      clearFilters: () => set((state) => {
        state.filters = {};
      }),

      // Pagination actions
      setPage: (page) => set((state) => {
        state.page = page;
      }),

      setLimit: (limit) => set((state) => {
        state.limit = limit;
      }),

      // Budget actions
      setSelectedBudget: (budget) => set((state) => {
        state.selectedBudget = budget;
      }),

      setBudgets: (budgets) => set((state) => {
        state.budgets = budgets;
      }),

      setBudgetCategories: (categories) => set((state) => {
        state.budgetCategories = categories;
      }),

      setBudgetSubcategories: (subcategories) => set((state) => {
        state.budgetSubcategories = subcategories;
      }),

      // Utility functions
      getCurrentFiscalYear: () => {
        const { fiscalYears } = get();
        const currentYear = fiscalYears.find(year => year.isCurrent);
        return currentYear?.value || '2025-2026';
      },

      getActiveFiscalYears: () => {
        const { fiscalYears } = get();
        return fiscalYears;
      },

      getExpendituresByStatus: (status) => {
        const { expenditures } = get();
        return expenditures.filter(expenditure => expenditure.status === status);
      },

      getTotalExpenditureAmount: () => {
        const { expenditures } = get();
        return expenditures.reduce((total, expenditure) => total + expenditure.amount, 0);
      },

      // Form data initialization
      initializeFormData: async () => {
        set((state) => { state.isFormDataReady = false; });

        try {
          // Initialize with default data immediately
          set((state) => {
            state.expenseCategories = DEFAULT_FORM_DATA.expenseCategories;
            state.fiscalYears = DEFAULT_FORM_DATA.fiscalYears;
            state.paymentMethods = DEFAULT_FORM_DATA.paymentMethods;
            state.isFormDataReady = true;
          });

          // Optionally fetch additional data from API
          // This can be done in the background
        } catch (error) {
          console.error('Error initializing form data:', error);
          set((state) => { state.error = 'Failed to initialize form data'; });
        }
      },

      // API actions
      fetchExpenditures: async () => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const { page, limit, filters } = get();
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
          });

          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.append(key, value.toString());
            }
          });

          const response = await fetch(`/api/accounting/expense?${queryParams.toString()}`);
          if (!response.ok) throw new Error('Failed to fetch expenditures');

          const data = await response.json();
          set((state) => {
            state.expenditures = data.expenses || [];
            state.totalCount = data.totalCount || 0;
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch expenditures';
            state.isLoading = false;
          });
        }
      },

      createExpenditure: async (expenditure) => {
        set((state) => { state.isLoading = true; state.error = null; });

        // Create timeout for request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          console.log('Expenditure creation request timed out after 30 seconds');
          controller.abort();
        }, 30000); // 30 second timeout

        try {
          // Use the new expenditure API endpoint with proper Expenditure model
          const response = await fetch('/api/accounting/expenditure', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...expenditure,
              appliedToBudget: true, // Always apply to budget for automatic integration
            }),
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || errorData.message || `HTTP ${response.status}: Failed to create expenditure`);
          }

          const result = await response.json();
          const newExpenditure = result.expenditure || result;

          set((state) => {
            state.expenditures.push(newExpenditure);
            state.isLoading = false;
          });

        } catch (error) {
          clearTimeout(timeoutId);

          // Handle AbortError gracefully - don't treat as a real error
          if (error instanceof Error && error.name === 'AbortError') {
            console.log('Expenditure creation request was aborted');
            set((state) => {
              state.isLoading = false;
              // Don't set error for abort - it's usually intentional
            });
            return; // Don't throw for abort errors
          }

          const errorMessage = error instanceof Error ? error.message : 'Failed to create expenditure';
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          throw error;
        }
      },

      updateExpenditureById: async (id, expenditure) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch(`/api/accounting/expense?id=${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(expenditure),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to update expenditure');
          }

          const updatedExpenditure = await response.json();
          set((state) => {
            const index = state.expenditures.findIndex(e => e.id === id || e._id === id);
            if (index !== -1) {
              state.expenditures[index] = updatedExpenditure.expense || updatedExpenditure;
            }
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update expenditure';
            state.isLoading = false;
          });
          throw error;
        }
      },

      deleteExpenditure: async (id) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch(`/api/accounting/expense?id=${id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to delete expenditure');
          }

          set((state) => {
            state.expenditures = state.expenditures.filter(e => e.id !== id && e._id !== id);
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete expenditure';
            state.isLoading = false;
          });
          throw error;
        }
      },

      // Status management
      updateExpenditureStatus: async (id, status, notes, reason) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch(`/api/accounting/expense/${id}/status`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status, notes, reason }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to update expenditure status');
          }

          const result = await response.json();
          const updatedExpenditure = result.expense;

          set((state) => {
            const index = state.expenditures.findIndex(e => e.id === id || e._id === id);
            if (index !== -1) {
              state.expenditures[index] = updatedExpenditure;
            }
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update expenditure status';
            state.isLoading = false;
          });
          throw error;
        }
      },

      // Bulk operations
      bulkUpdateStatus: async (ids, status, notes, reason) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch('/api/accounting/expense/bulk', {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ expenseIds: ids, status, notes, reason }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to bulk update expenditure status');
          }

          const result = await response.json();

          // Update local state for successful updates
          if (result.results) {
            set((state) => {
              result.results.forEach((update: any) => {
                if (update.success) {
                  const index = state.expenditures.findIndex(e => e.id === update.expenseId || e._id === update.expenseId);
                  if (index !== -1) {
                    state.expenditures[index].status = status as any;
                  }
                }
              });
              state.isLoading = false;
            });
          }
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to bulk update expenditure status';
            state.isLoading = false;
          });
          throw error;
        }
      },

      bulkDeleteExpenditures: async (ids, reason) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch('/api/accounting/expense/bulk', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ expenseIds: ids, reason }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to bulk delete expenditures');
          }

          const result = await response.json();

          // Update local state for successful deletions
          if (result.results) {
            set((state) => {
              const successfulDeletions = result.results
                .filter((deletion: any) => deletion.success)
                .map((deletion: any) => deletion.expenseId);

              state.expenditures = state.expenditures.filter(e =>
                !successfulDeletions.includes(e.id) && !successfulDeletions.includes(e._id)
              );
              state.isLoading = false;
            });
          }
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to bulk delete expenditures';
            state.isLoading = false;
          });
          throw error;
        }
      },
    })),
    {
      name: 'expenditure-store',
      partialize: (state) => ({
        // Only persist essential data, not loading states
        expenditures: state.expenditures,
        selectedExpenditure: state.selectedExpenditure,
        filters: state.filters,
        page: state.page,
        limit: state.limit,
        fiscalYears: state.fiscalYears,
        paymentMethods: state.paymentMethods,
        expenseCategories: state.expenseCategories,
      }),
    }
  )
);

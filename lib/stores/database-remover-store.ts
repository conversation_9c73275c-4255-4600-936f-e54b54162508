// lib/stores/database-remover-store.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface DatabaseModel {
  name: string;
  displayName: string;
  collection: string;
  count: number;
  allowedRoles: string[];
  dangerLevel: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  dependencies?: string[];
}

export interface CategorizedModels {
  system: DatabaseModel[];
  hr: DatabaseModel[];
  payroll: DatabaseModel[];
  accounting: DatabaseModel[];
  other: DatabaseModel[];
}

export interface ModelData {
  items: any[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DeletionResult {
  success: boolean;
  deletedCount: number;
  auditRecords: string[];
  errors: string[];
  warnings: string[];
}

export interface DatabaseRemoverState {
  // Data
  models: CategorizedModels | null;
  modelsSummary: {
    totalModels: number;
    totalRecords: number;
    criticalModels: number;
    lastUpdated: string;
  } | null;
  currentModelData: ModelData | null;
  currentModel: DatabaseModel | null;
  
  // Loading states
  isLoadingModels: boolean;
  isLoadingModelData: boolean;
  isDeleting: boolean;
  isRefreshing: boolean;
  
  // Error states
  error: string | null;
  
  // UI state
  selectedItems: string[];
  expandedCategories: string[];
  currentPage: number;
  itemsPerPage: number;
  
  // Deletion state
  deletionReason: string;
  confirmDeletion: boolean;
  deleteRelatedData: boolean;
  
  // Actions
  setModels: (models: CategorizedModels, summary: any) => void;
  setCurrentModelData: (data: ModelData, model: DatabaseModel) => void;
  setIsLoadingModels: (loading: boolean) => void;
  setIsLoadingModelData: (loading: boolean) => void;
  setIsDeleting: (deleting: boolean) => void;
  setIsRefreshing: (refreshing: boolean) => void;
  setError: (error: string | null) => void;
  setSelectedItems: (items: string[]) => void;
  toggleItemSelection: (itemId: string) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  setExpandedCategories: (categories: string[]) => void;
  toggleCategoryExpansion: (category: string) => void;
  setCurrentPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  setDeletionReason: (reason: string) => void;
  setConfirmDeletion: (confirm: boolean) => void;
  setDeleteRelatedData: (deleteRelated: boolean) => void;
  
  // API actions
  fetchModels: () => Promise<void>;
  refreshModels: () => Promise<void>;
  fetchModelData: (modelName: string, page?: number) => Promise<void>;
  bulkDeleteItems: (modelName: string) => Promise<DeletionResult>;
  deleteAllModelData: (modelName: string, confirmModelName: string) => Promise<DeletionResult>;
  resetState: () => void;
}

export const useDatabaseRemoverStore = create<DatabaseRemoverState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      models: null,
      modelsSummary: null,
      currentModelData: null,
      currentModel: null,
      
      // Loading states
      isLoadingModels: false,
      isLoadingModelData: false,
      isDeleting: false,
      isRefreshing: false,
      
      // Error states
      error: null,
      
      // UI state
      selectedItems: [],
      expandedCategories: ['system', 'hr'],
      currentPage: 1,
      itemsPerPage: 50,
      
      // Deletion state
      deletionReason: '',
      confirmDeletion: false,
      deleteRelatedData: false,
      
      // Actions
      setModels: (models, summary) => set((state) => {
        state.models = models;
        state.modelsSummary = summary;
      }),
      
      setCurrentModelData: (data, model) => set((state) => {
        state.currentModelData = data;
        state.currentModel = model;
        state.selectedItems = [];
      }),
      
      setIsLoadingModels: (loading) => set((state) => {
        state.isLoadingModels = loading;
      }),
      
      setIsLoadingModelData: (loading) => set((state) => {
        state.isLoadingModelData = loading;
      }),
      
      setIsDeleting: (deleting) => set((state) => {
        state.isDeleting = deleting;
      }),
      
      setIsRefreshing: (refreshing) => set((state) => {
        state.isRefreshing = refreshing;
      }),
      
      setError: (error) => set((state) => {
        state.error = error;
      }),
      
      setSelectedItems: (items) => set((state) => {
        state.selectedItems = items;
      }),
      
      toggleItemSelection: (itemId) => set((state) => {
        const index = state.selectedItems.indexOf(itemId);
        if (index > -1) {
          state.selectedItems.splice(index, 1);
        } else {
          state.selectedItems.push(itemId);
        }
      }),
      
      selectAllItems: () => set((state) => {
        if (state.currentModelData) {
          state.selectedItems = state.currentModelData.items.map(item => item._id);
        }
      }),
      
      clearSelection: () => set((state) => {
        state.selectedItems = [];
      }),
      
      setExpandedCategories: (categories) => set((state) => {
        state.expandedCategories = categories;
      }),
      
      toggleCategoryExpansion: (category) => set((state) => {
        const index = state.expandedCategories.indexOf(category);
        if (index > -1) {
          state.expandedCategories.splice(index, 1);
        } else {
          state.expandedCategories.push(category);
        }
      }),
      
      setCurrentPage: (page) => set((state) => {
        state.currentPage = page;
      }),
      
      setItemsPerPage: (itemsPerPage) => set((state) => {
        state.itemsPerPage = itemsPerPage;
        state.currentPage = 1;
      }),
      
      setDeletionReason: (reason) => set((state) => {
        state.deletionReason = reason;
      }),
      
      setConfirmDeletion: (confirm) => set((state) => {
        state.confirmDeletion = confirm;
      }),
      
      setDeleteRelatedData: (deleteRelated) => set((state) => {
        state.deleteRelatedData = deleteRelated;
      }),
      
      // API actions
      fetchModels: async () => {
        set((state) => {
          state.isLoadingModels = true;
          state.error = null;
        });
        
        try {
          const response = await fetch('/api/admin/database-remover/models');
          if (!response.ok) {
            throw new Error('Failed to fetch models');
          }
          
          const data = await response.json();
          
          set((state) => {
            state.models = data.data.models;
            state.modelsSummary = data.data.summary;
            state.isLoadingModels = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch models';
            state.isLoadingModels = false;
          });
        }
      },
      
      refreshModels: async () => {
        set((state) => {
          state.isRefreshing = true;
          state.error = null;
        });
        
        try {
          const response = await fetch('/api/admin/database-remover/models', {
            method: 'PUT'
          });
          if (!response.ok) {
            throw new Error('Failed to refresh models');
          }
          
          const data = await response.json();
          
          set((state) => {
            state.models = data.data.models;
            state.isRefreshing = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to refresh models';
            state.isRefreshing = false;
          });
        }
      },
      
      fetchModelData: async (modelName, page) => {
        const state = get();
        const currentPage = page || state.currentPage;
        
        set((draft) => {
          draft.isLoadingModelData = true;
          draft.error = null;
        });
        
        try {
          const params = new URLSearchParams({
            page: currentPage.toString(),
            limit: state.itemsPerPage.toString()
          });
          
          const response = await fetch(`/api/admin/database-remover/models/${modelName}?${params}`);
          if (!response.ok) {
            throw new Error('Failed to fetch model data');
          }
          
          const data = await response.json();
          
          set((draft) => {
            draft.currentModelData = {
              items: data.data.items,
              pagination: data.data.pagination
            };
            draft.currentModel = data.data.model;
            draft.currentPage = data.data.pagination.page;
            draft.selectedItems = [];
            draft.isLoadingModelData = false;
          });
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to fetch model data';
            draft.isLoadingModelData = false;
          });
        }
      },
      
      bulkDeleteItems: async (modelName) => {
        const state = get();
        
        set((draft) => {
          draft.isDeleting = true;
          draft.error = null;
        });
        
        try {
          const response = await fetch(`/api/admin/database-remover/models/${modelName}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              itemIds: state.selectedItems,
              deletionReason: state.deletionReason,
              confirmDeletion: state.confirmDeletion,
              deleteRelatedData: state.deleteRelatedData
            })
          });
          
          if (!response.ok) {
            throw new Error('Failed to delete items');
          }
          
          const data = await response.json();
          
          // Refresh model data after deletion
          if (data.success) {
            await get().fetchModelData(modelName);
            await get().fetchModels();
          }
          
          set((draft) => {
            draft.isDeleting = false;
            draft.selectedItems = [];
            draft.deletionReason = '';
            draft.confirmDeletion = false;
          });
          
          return data.data;
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to delete items';
            draft.isDeleting = false;
          });
          throw error;
        }
      },
      
      deleteAllModelData: async (modelName, confirmModelName) => {
        const state = get();
        
        set((draft) => {
          draft.isDeleting = true;
          draft.error = null;
        });
        
        try {
          const response = await fetch(`/api/admin/database-remover/models/${modelName}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              deleteAll: true,
              deletionReason: state.deletionReason,
              confirmDeletion: state.confirmDeletion,
              confirmModelName
            })
          });
          
          if (!response.ok) {
            throw new Error('Failed to delete all model data');
          }
          
          const data = await response.json();
          
          // Refresh models after deletion
          if (data.success) {
            await get().fetchModels();
            set((draft) => {
              draft.currentModelData = null;
              draft.currentModel = null;
            });
          }
          
          set((draft) => {
            draft.isDeleting = false;
            draft.deletionReason = '';
            draft.confirmDeletion = false;
          });
          
          return data.data;
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to delete all model data';
            draft.isDeleting = false;
          });
          throw error;
        }
      },
      
      resetState: () => set((state) => {
        state.currentModelData = null;
        state.currentModel = null;
        state.selectedItems = [];
        state.deletionReason = '';
        state.confirmDeletion = false;
        state.deleteRelatedData = false;
        state.currentPage = 1;
        state.error = null;
      })
    })),
    {
      name: 'database-remover-store',
      partialize: (state) => ({
        expandedCategories: state.expandedCategories,
        itemsPerPage: state.itemsPerPage
      })
    }
  )
);

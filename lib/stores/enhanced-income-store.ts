import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface Income {
  id: string;
  _id?: string;
  date: Date;
  source: 'government_subvention' | 'registration_fees' | 'licensing_fees' | 'donations' | 'other';
  subSource?: string;
  amount: number;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'received' | 'cancelled';
  paymentMethod?: string;
  bankAccount?: string;
  budget?: string;
  budgetName?: string;
  budgetCategory?: string;
  budgetCategoryName?: string;
  budgetSubcategory?: string;
  budgetSubcategoryName?: string;
  appliedToBudget?: boolean;
  notes?: string;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BankAccount {
  id: string;
  _id?: string;
  name: string;
  accountNumber?: string;
  bankName?: string;
  accountType?: string;
  isActive?: boolean;
}

export interface FiscalYear {
  id: string;
  year: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  isCurrent: boolean;
}

export interface PaymentMethod {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface IncomeSource {
  value: 'government_subvention' | 'registration_fees' | 'licensing_fees' | 'donations' | 'other';
  label: string;
  description?: string;
}

export interface Budget {
  id: string;
  _id?: string;
  name: string;
  fiscalYear: string;
  status: string;
  startDate: Date;
  endDate: Date;
  categories: BudgetCategory[];
}

export interface BudgetCategory {
  id: string;
  _id?: string;
  name: string;
  type: 'income' | 'expense';
  budgeted: number;
  actual: number;
  remaining: number;
  utilizationPercentage: number;
  subcategories?: BudgetSubcategory[];
}

export interface BudgetSubcategory {
  id: string;
  _id?: string;
  name: string;
  budgeted: number;
  actual: number;
  remaining: number;
  utilizationPercentage: number;
}

export interface IncomeFilters {
  source?: string;
  paymentMethod?: string;
  status?: string;
  minAmount?: number;
  maxAmount?: number;
  startDate?: Date;
  endDate?: Date;
  budgetId?: string;
  budgetCategoryId?: string;
  fiscalYear?: string;
}

export interface IncomeState {
  // Income data
  incomes: Income[];
  selectedIncome: Income | null;
  isLoading: boolean;
  error: string | null;
  
  // Form data
  bankAccounts: BankAccount[];
  fiscalYears: FiscalYear[];
  paymentMethods: PaymentMethod[];
  incomeSources: IncomeSource[];
  
  // Filters
  filters: IncomeFilters;
  
  // Pagination
  page: number;
  limit: number;
  totalCount: number;
  
  // Budget-related state
  selectedBudget: Budget | null;
  budgets: Budget[];
  budgetCategories: BudgetCategory[];
  budgetSubcategories: BudgetSubcategory[];
  
  // Loading states for different data types
  isLoadingBankAccounts: boolean;
  isLoadingFiscalYears: boolean;
  isLoadingPaymentMethods: boolean;
  isLoadingBudgets: boolean;
  isLoadingCategories: boolean;
  isLoadingSubcategories: boolean;
  
  // Data ready flags
  isFormDataReady: boolean;
  
  // Actions for income data
  setIncomes: (incomes: Income[]) => void;
  setSelectedIncome: (income: Income | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Actions for form data
  setBankAccounts: (accounts: BankAccount[]) => void;
  setFiscalYears: (years: FiscalYear[]) => void;
  setPaymentMethods: (methods: PaymentMethod[]) => void;
  setIncomeSources: (sources: IncomeSource[]) => void;
  
  // Actions for filters and pagination
  setFilters: (filters: Partial<IncomeFilters>) => void;
  resetFilters: () => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setTotalCount: (totalCount: number) => void;
  
  // Actions for budget data
  setSelectedBudget: (budget: Budget | null) => void;
  setBudgets: (budgets: Budget[]) => void;
  setBudgetCategories: (categories: BudgetCategory[]) => void;
  setBudgetSubcategories: (subcategories: BudgetSubcategory[]) => void;
  
  // Loading state actions
  setIsLoadingBankAccounts: (loading: boolean) => void;
  setIsLoadingFiscalYears: (loading: boolean) => void;
  setIsLoadingPaymentMethods: (loading: boolean) => void;
  setIsLoadingBudgets: (loading: boolean) => void;
  setIsLoadingCategories: (loading: boolean) => void;
  setIsLoadingSubcategories: (loading: boolean) => void;
  setIsFormDataReady: (ready: boolean) => void;
  
  // API actions
  fetchIncomes: () => Promise<void>;
  createIncome: (income: Partial<Income>) => Promise<Income>;
  updateIncome: (id: string, income: Partial<Income>) => Promise<Income>;
  deleteIncome: (id: string) => Promise<void>;
  bulkDeleteIncomes: (ids: string[]) => Promise<void>;
  bulkImportIncomes: (file: File) => Promise<any>;
  
  // Form data API actions
  fetchBankAccounts: () => Promise<void>;
  fetchFiscalYears: () => Promise<void>;
  fetchPaymentMethods: () => Promise<void>;
  fetchIncomeSources: () => Promise<void>;
  
  // Budget API actions
  fetchBudgets: () => Promise<void>;
  fetchBudgetCategories: (budgetId: string) => Promise<void>;
  fetchBudgetSubcategories: (categoryId: string) => Promise<void>;
  
  // Utility actions
  initializeFormData: () => Promise<void>;
  getCurrentFiscalYear: () => string;
  getActiveFiscalYears: () => FiscalYear[];
  getActiveBankAccounts: () => BankAccount[];
  getActivePaymentMethods: () => PaymentMethod[];
  
  // Selectors
  getFilteredIncomes: () => Income[];
  getIncomesByBudget: (budgetId: string) => Income[];
  getIncomesByCategory: (categoryId: string) => Income[];
  getTotalIncome: () => number;
  getBudgetUtilization: (budgetId: string) => number;
}

// Income sources will be fetched from API
// Payment methods will be fetched from API

export const useIncomeStore = create<IncomeState>()(
  persist(
    immer((set, get) => ({
      // Income data
      incomes: [],
      selectedIncome: null,
      isLoading: false,
      error: null,

      // Form data
      bankAccounts: [],
      fiscalYears: [],
      paymentMethods: [],
      incomeSources: [],

      // Filters
      filters: {},

      // Pagination
      page: 1,
      limit: 10,
      totalCount: 0,

      // Budget-related state
      selectedBudget: null,
      budgets: [],
      budgetCategories: [],
      budgetSubcategories: [],

      // Loading states
      isLoadingBankAccounts: false,
      isLoadingFiscalYears: false,
      isLoadingPaymentMethods: false,
      isLoadingBudgets: false,
      isLoadingCategories: false,
      isLoadingSubcategories: false,

      // Data ready flags
      isFormDataReady: false,

      // Actions for income data
      setIncomes: (incomes) => set((state) => {
        state.incomes = incomes;
      }),

      setSelectedIncome: (income) => set((state) => {
        state.selectedIncome = income;
      }),

      setIsLoading: (isLoading) => set((state) => {
        state.isLoading = isLoading;
      }),

      setError: (error) => set((state) => {
        state.error = error;
      }),

      // Actions for form data
      setBankAccounts: (accounts) => set((state) => {
        state.bankAccounts = accounts;
      }),

      setFiscalYears: (years) => set((state) => {
        state.fiscalYears = years;
      }),

      setPaymentMethods: (methods) => set((state) => {
        state.paymentMethods = methods;
      }),

      setIncomeSources: (sources) => set((state) => {
        state.incomeSources = sources;
      }),

      // Actions for filters and pagination
      setFilters: (filters) => set((state) => {
        state.filters = { ...state.filters, ...filters };
      }),

      resetFilters: () => set((state) => {
        state.filters = {};
      }),

      setPage: (page) => set((state) => {
        state.page = page;
      }),

      setLimit: (limit) => set((state) => {
        state.limit = limit;
      }),

      setTotalCount: (totalCount) => set((state) => {
        state.totalCount = totalCount;
      }),

      // Actions for budget data
      setSelectedBudget: (budget) => set((state) => {
        state.selectedBudget = budget;
        if (budget) {
          state.budgetCategories = budget.categories.filter(
            (category) => category.type === 'income'
          );
        } else {
          state.budgetCategories = [];
        }
      }),

      setBudgets: (budgets) => set((state) => {
        state.budgets = budgets;
      }),

      setBudgetCategories: (categories) => set((state) => {
        state.budgetCategories = categories;
      }),

      setBudgetSubcategories: (subcategories) => set((state) => {
        state.budgetSubcategories = subcategories;
      }),

      // Loading state actions
      setIsLoadingBankAccounts: (loading) => set((state) => {
        state.isLoadingBankAccounts = loading;
      }),

      setIsLoadingFiscalYears: (loading) => set((state) => {
        state.isLoadingFiscalYears = loading;
      }),

      setIsLoadingPaymentMethods: (loading) => set((state) => {
        state.isLoadingPaymentMethods = loading;
      }),

      setIsLoadingBudgets: (loading) => set((state) => {
        state.isLoadingBudgets = loading;
      }),

      setIsLoadingCategories: (loading) => set((state) => {
        state.isLoadingCategories = loading;
      }),

      setIsLoadingSubcategories: (loading) => set((state) => {
        state.isLoadingSubcategories = loading;
      }),

      setIsFormDataReady: (ready) => set((state) => {
        state.isFormDataReady = ready;
      }),

      // API actions
      fetchIncomes: async () => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const { page, limit, filters } = get();
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
          });

          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.append(key, value.toString());
            }
          });

          const response = await fetch(`/api/accounting/income?${queryParams.toString()}`);
          if (!response.ok) throw new Error('Failed to fetch incomes');

          const data = await response.json();
          set((state) => {
            state.incomes = data.incomes || [];
            state.totalCount = data.totalCount || 0;
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch incomes';
            state.isLoading = false;
          });
        }
      },

      createIncome: async (income) => {
        set((state) => { state.isLoading = true; state.error = null; });

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        try {
          // Use the enhanced simple API endpoint with budget integration
          const response = await fetch('/api/accounting/income/simple', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...income,
              appliedToBudget: true, // Always apply to budget for automatic integration
            }),
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || errorData.message || `HTTP ${response.status}: Failed to create income`);
          }

          const result = await response.json();
          const newIncome = result.income || result;

          set((state) => {
            state.incomes.push(newIncome);
            state.isLoading = false;
          });
          return newIncome;
        } catch (error) {
          clearTimeout(timeoutId);
          const errorMessage = error instanceof Error ?
            (error.name === 'AbortError' ? 'Request timed out. Please try again.' : error.message) :
            'Failed to create income';

          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          throw new Error(errorMessage);
        }
      },

      updateIncome: async (id, income) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch(`/api/accounting/income/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(income),
          });
          if (!response.ok) throw new Error('Failed to update income');

          const updatedIncome = await response.json();
          set((state) => {
            const index = state.incomes.findIndex((i: Income) => i.id === id || i._id === id);
            if (index !== -1) {
              state.incomes[index] = updatedIncome;
            }
            state.isLoading = false;
          });
          return updatedIncome;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update income';
            state.isLoading = false;
          });
          throw error;
        }
      },

      deleteIncome: async (id) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch(`/api/accounting/income/${id}`, {
            method: 'DELETE',
          });
          if (!response.ok) throw new Error('Failed to delete income');

          set((state) => {
            state.incomes = state.incomes.filter((i: Income) => i.id !== id && i._id !== id);
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete income';
            state.isLoading = false;
          });
          throw error;
        }
      },

      bulkDeleteIncomes: async (ids) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const response = await fetch('/api/accounting/income/bulk-delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ids }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete incomes');
          }

          const result = await response.json();

          set((state) => {
            state.incomes = state.incomes.filter((i: Income) =>
              !ids.includes(i.id || '') && !ids.includes(i._id || '')
            );
            state.isLoading = false;
          });

          return result;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete incomes';
            state.isLoading = false;
          });
          throw error;
        }
      },

      bulkImportIncomes: async (file) => {
        set((state) => { state.isLoading = true; state.error = null; });
        try {
          const formData = new FormData();
          formData.append('file', file);

          const response = await fetch('/api/accounting/income/bulk-import', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to import incomes');
          }

          const result = await response.json();

          // Refresh incomes list after successful import
          if (result.success && result.successCount > 0) {
            await get().fetchIncomes();
          }

          set((state) => {
            state.isLoading = false;
          });

          return result;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to import incomes';
            state.isLoading = false;
          });
          throw error;
        }
      },

      // Form data API actions
      fetchBankAccounts: async () => {
        set((state) => { state.isLoadingBankAccounts = true; });
        try {
          const response = await fetch('/api/accounting/bank-accounts');
          if (!response.ok) throw new Error('Failed to fetch bank accounts');

          const data = await response.json();
          set((state) => {
            state.bankAccounts = data.bankAccounts || [];
            state.isLoadingBankAccounts = false;
          });
        } catch (error) {
          console.error('Error fetching bank accounts:', error);
          set((state) => {
            state.bankAccounts = [];
            state.isLoadingBankAccounts = false;
          });
        }
      },

      fetchFiscalYears: async () => {
        set((state) => { state.isLoadingFiscalYears = true; });
        try {
          const response = await fetch('/api/accounting/fiscal-years');
          if (!response.ok) throw new Error('Failed to fetch fiscal years');

          const data = await response.json();
          set((state) => {
            state.fiscalYears = data.fiscalYears || [];
            state.isLoadingFiscalYears = false;
          });
        } catch (error) {
          console.error('Error fetching fiscal years:', error);
          // Generate default fiscal years if API fails
          // For 2025, use 2025-2026 as the current fiscal year
          const currentYear = new Date().getFullYear();
          const defaultFiscalYears: FiscalYear[] = [];

          // Adjust base year for 2025 to start with 2025-2026
          const baseYear = currentYear === 2025 ? 2025 : currentYear;

          for (let i = -2; i <= 2; i++) {
            const year = baseYear + i;
            defaultFiscalYears.push({
              id: `${year}-${year + 1}`,
              year: `${year}-${year + 1}`,
              startDate: new Date(year, 6, 1), // July 1st
              endDate: new Date(year + 1, 5, 30), // June 30th
              isActive: true,
              isCurrent: (currentYear === 2025 && year === 2025) || (currentYear !== 2025 && i === 0),
            });
          }
          set((state) => {
            state.fiscalYears = defaultFiscalYears;
            state.isLoadingFiscalYears = false;
          });
        }
      },

      fetchPaymentMethods: async () => {
        set((state) => { state.isLoadingPaymentMethods = true; });
        try {
          const response = await fetch('/api/accounting/payment-methods');
          if (!response.ok) throw new Error('Failed to fetch payment methods');

          const data = await response.json();
          set((state) => {
            state.paymentMethods = data.paymentMethods || [];
            state.isLoadingPaymentMethods = false;
          });
        } catch (error) {
          console.error('Error fetching payment methods:', error);
          set((state) => {
            // Fallback to basic payment methods if API fails
            state.paymentMethods = [
              { id: 'cash', name: 'Cash', isActive: true },
              { id: 'bank_transfer', name: 'Bank Transfer', isActive: true },
              { id: 'mobile_money', name: 'Mobile Money', isActive: true }
            ];
            state.isLoadingPaymentMethods = false;
          });
        }
      },

      fetchIncomeSources: async () => {
        try {
          const response = await fetch('/api/accounting/income-sources');
          if (!response.ok) throw new Error('Failed to fetch income sources');

          const data = await response.json();
          set((state) => {
            state.incomeSources = data.incomeSources || [];
          });
        } catch (error) {
          console.error('Error fetching income sources:', error);
          set((state) => {
            // Fallback to basic income sources if API fails
            state.incomeSources = [
              { value: 'government_subvention', label: 'Government Subvention', description: 'Government funding and grants' },
              { value: 'registration_fees', label: 'Registration Fees', description: 'Teacher registration fees' },
              { value: 'licensing_fees', label: 'Licensing Fees', description: 'Professional licensing fees' },
              { value: 'donations', label: 'Donations', description: 'Donations and contributions' },
              { value: 'other', label: 'Other', description: 'Other income sources' }
            ];
          });
        }
      },

      // Budget API actions
      fetchBudgets: async () => {
        set((state) => { state.isLoadingBudgets = true; });
        try {
          const response = await fetch('/api/accounting/budget');
          if (!response.ok) throw new Error('Failed to fetch budgets');

          const data = await response.json();
          set((state) => {
            state.budgets = data.budgets || [];
            state.isLoadingBudgets = false;
          });
        } catch (error) {
          console.error('Error fetching budgets:', error);
          set((state) => {
            state.budgets = [];
            state.isLoadingBudgets = false;
          });
        }
      },

      fetchBudgetCategories: async (budgetId) => {
        set((state) => { state.isLoadingCategories = true; });
        try {
          const response = await fetch(`/api/accounting/budget/category?budgetId=${budgetId}&type=income`);
          if (!response.ok) throw new Error('Failed to fetch budget categories');

          const data = await response.json();
          set((state) => {
            state.budgetCategories = data.categories || [];
            state.isLoadingCategories = false;
          });
        } catch (error) {
          console.error('Error fetching budget categories:', error);
          set((state) => {
            state.budgetCategories = [];
            state.isLoadingCategories = false;
          });
        }
      },

      fetchBudgetSubcategories: async (categoryId) => {
        set((state) => { state.isLoadingSubcategories = true; });
        try {
          const response = await fetch(`/api/accounting/budget/subcategory?categoryId=${categoryId}`);
          if (!response.ok) throw new Error('Failed to fetch budget subcategories');

          const data = await response.json();
          set((state) => {
            state.budgetSubcategories = data.subcategories || [];
            state.isLoadingSubcategories = false;
          });
        } catch (error) {
          console.error('Error fetching budget subcategories:', error);
          set((state) => {
            state.budgetSubcategories = [];
            state.isLoadingSubcategories = false;
          });
        }
      },

      // Utility actions
      initializeFormData: async () => {
        const actions = get();

        // Fetch all form data in parallel
        await Promise.all([
          actions.fetchBankAccounts(),
          actions.fetchFiscalYears(),
          actions.fetchPaymentMethods(),
          actions.fetchIncomeSources(),
          actions.fetchBudgets(),
        ]);

        // Mark form data as ready
        set((state) => {
          state.isFormDataReady = true;
        });
      },

      getCurrentFiscalYear: () => {
        const { fiscalYears } = get();
        const currentFiscalYear = fiscalYears.find(fy => fy.isCurrent);
        if (currentFiscalYear) {
          return currentFiscalYear.year;
        }

        // Fallback to generating current fiscal year
        // Default to 2025-2026 fiscal year and count onwards
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();

        // For 2025, default to 2025-2026 fiscal year
        if (year === 2025) {
          return '2025-2026';
        }

        // For future years, fiscal year starts in July (month 6)
        if (month >= 6) {
          return `${year}-${year + 1}`;
        } else {
          return `${year - 1}-${year}`;
        }
      },

      getActiveFiscalYears: () => {
        const { fiscalYears } = get();
        return fiscalYears.filter(fy => fy.isActive);
      },

      getActiveBankAccounts: () => {
        const { bankAccounts } = get();
        return bankAccounts.filter(account => account.isActive !== false);
      },

      getActivePaymentMethods: () => {
        const { paymentMethods } = get();
        return paymentMethods.filter(method => method.isActive);
      },

      // Selectors
      getFilteredIncomes: () => {
        const { incomes, filters } = get();

        return incomes.filter((income) => {
          // Filter by source
          if (filters.source && income.source !== filters.source) {
            return false;
          }

          // Filter by payment method
          if (filters.paymentMethod && income.paymentMethod !== filters.paymentMethod) {
            return false;
          }

          // Filter by status
          if (filters.status && income.status !== filters.status) {
            return false;
          }

          // Filter by fiscal year
          if (filters.fiscalYear && income.fiscalYear !== filters.fiscalYear) {
            return false;
          }

          // Filter by amount range
          if (filters.minAmount && income.amount < filters.minAmount) {
            return false;
          }

          if (filters.maxAmount && income.amount > filters.maxAmount) {
            return false;
          }

          // Filter by date range
          if (filters.startDate && new Date(income.date) < filters.startDate) {
            return false;
          }

          if (filters.endDate && new Date(income.date) > filters.endDate) {
            return false;
          }

          // Filter by budget
          if (filters.budgetId && income.budget !== filters.budgetId) {
            return false;
          }

          // Filter by budget category
          if (filters.budgetCategoryId && income.budgetCategory !== filters.budgetCategoryId) {
            return false;
          }

          return true;
        });
      },

      getIncomesByBudget: (budgetId) => {
        const { incomes } = get();
        return incomes.filter((income) => income.budget === budgetId);
      },

      getIncomesByCategory: (categoryId) => {
        const { incomes } = get();
        return incomes.filter((income) => income.budgetCategory === categoryId);
      },

      getTotalIncome: () => {
        const { incomes } = get();
        return incomes.reduce((total, income) => total + income.amount, 0);
      },

      getBudgetUtilization: (budgetId) => {
        const { incomes, budgets } = get();

        // Find the budget
        const budget = budgets.find((b) => b.id === budgetId || b._id === budgetId);
        if (!budget) return 0;

        // Calculate total income for this budget
        const totalIncome = incomes
          .filter((income) => income.budget === budgetId)
          .reduce((total, income) => total + income.amount, 0);

        // Calculate total budgeted income
        const totalBudgeted = budget.categories
          .filter((category) => category.type === 'income')
          .reduce((total, category) => total + category.budgeted, 0);

        // Calculate utilization percentage
        return totalBudgeted > 0 ? (totalIncome / totalBudgeted) * 100 : 0;
      },
    })),
    {
      name: 'income-store',
      partialize: (state) => ({
        filters: state.filters,
        page: state.page,
        limit: state.limit,
        selectedBudget: state.selectedBudget,
        // Don't persist form data as it should be fresh on each session
      }),
    }
  )
);

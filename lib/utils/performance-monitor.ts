// Performance monitoring utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = process.env.NODE_ENV === 'development';

  /**
   * Start measuring performance for a specific operation
   */
  start(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata
    });
  }

  /**
   * End measuring performance for a specific operation
   */
  end(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log slow operations
    if (duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`, metric.metadata);
    }

    return duration;
  }

  /**
   * Measure a function execution time
   */
  async measure<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.start(name, metadata);
    try {
      const result = await fn();
      return result;
    } finally {
      this.end(name);
    }
  }

  /**
   * Measure a synchronous function execution time
   */
  measureSync<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.start(name, metadata);
    try {
      const result = fn();
      return result;
    } finally {
      this.end(name);
    }
  }

  /**
   * Get all metrics
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Get metrics summary
   */
  getSummary(): {
    totalOperations: number;
    averageDuration: number;
    slowOperations: PerformanceMetric[];
    fastestOperation: PerformanceMetric | null;
    slowestOperation: PerformanceMetric | null;
  } {
    const metrics = this.getMetrics().filter(m => m.duration !== undefined);
    
    if (metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        slowOperations: [],
        fastestOperation: null,
        slowestOperation: null
      };
    }

    const durations = metrics.map(m => m.duration!);
    const averageDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const slowOperations = metrics.filter(m => m.duration! > 100);
    const fastestOperation = metrics.reduce((fastest, current) => 
      !fastest || current.duration! < fastest.duration! ? current : fastest
    );
    const slowestOperation = metrics.reduce((slowest, current) => 
      !slowest || current.duration! > slowest.duration! ? current : slowest
    );

    return {
      totalOperations: metrics.length,
      averageDuration,
      slowOperations,
      fastestOperation,
      slowestOperation
    };
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * Log performance summary to console
   */
  logSummary(): void {
    if (!this.isEnabled) return;

    const summary = this.getSummary();
    
    console.group('🚀 Performance Summary');
    console.log(`Total Operations: ${summary.totalOperations}`);
    console.log(`Average Duration: ${summary.averageDuration.toFixed(2)}ms`);
    
    if (summary.fastestOperation) {
      console.log(`Fastest: ${summary.fastestOperation.name} (${summary.fastestOperation.duration!.toFixed(2)}ms)`);
    }
    
    if (summary.slowestOperation) {
      console.log(`Slowest: ${summary.slowestOperation.name} (${summary.slowestOperation.duration!.toFixed(2)}ms)`);
    }
    
    if (summary.slowOperations.length > 0) {
      console.warn(`Slow Operations (>100ms): ${summary.slowOperations.length}`);
      summary.slowOperations.forEach(op => {
        console.warn(`  - ${op.name}: ${op.duration!.toFixed(2)}ms`);
      });
    }
    
    console.groupEnd();
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitor() {
  return {
    start: performanceMonitor.start.bind(performanceMonitor),
    end: performanceMonitor.end.bind(performanceMonitor),
    measure: performanceMonitor.measure.bind(performanceMonitor),
    measureSync: performanceMonitor.measureSync.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    getSummary: performanceMonitor.getSummary.bind(performanceMonitor),
    clear: performanceMonitor.clear.bind(performanceMonitor),
    logSummary: performanceMonitor.logSummary.bind(performanceMonitor)
  };
}

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memoization utility
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    
    return result;
  }) as T;
}

// API call optimization
export class APICallOptimizer {
  private pendingCalls = new Map<string, Promise<any>>();
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  /**
   * Deduplicate identical API calls
   */
  async deduplicate<T>(key: string, apiCall: () => Promise<T>): Promise<T> {
    // Check if there's already a pending call for this key
    if (this.pendingCalls.has(key)) {
      return this.pendingCalls.get(key);
    }

    // Create new call and store it
    const promise = apiCall().finally(() => {
      this.pendingCalls.delete(key);
    });

    this.pendingCalls.set(key, promise);
    return promise;
  }

  /**
   * Cache API responses with TTL
   */
  async cache<T>(
    key: string, 
    apiCall: () => Promise<T>, 
    ttl: number = 300000 // 5 minutes default
  ): Promise<T> {
    // Check cache first
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
      return cached.data;
    }

    // Make API call
    const data = await apiCall();
    
    // Store in cache
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });

    return data;
  }

  /**
   * Combine deduplication and caching
   */
  async optimizedCall<T>(
    key: string,
    apiCall: () => Promise<T>,
    ttl: number = 300000
  ): Promise<T> {
    return this.deduplicate(key, () => this.cache(key, apiCall, ttl));
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if ((now - value.timestamp) >= value.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Create singleton instance
export const apiOptimizer = new APICallOptimizer();

// Performance monitoring decorator
export function performanceDecorator(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      performanceMonitor.start(metricName, { args: args.length });
      try {
        const result = await originalMethod.apply(this, args);
        return result;
      } finally {
        performanceMonitor.end(metricName);
      }
    };

    return descriptor;
  };
}

// React component performance wrapper
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  name?: string
) {
  const componentName = name || Component.displayName || Component.name || 'Component';
  
  return React.memo((props: P) => {
    React.useEffect(() => {
      performanceMonitor.start(`${componentName}.render`);
      return () => {
        performanceMonitor.end(`${componentName}.render`);
      };
    });

    return React.createElement(Component, props);
  });
}

// Export utilities
export {
  PerformanceMonitor,
  performanceMonitor as default
};

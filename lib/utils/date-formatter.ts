// lib/utils/date-formatter.ts
/**
 * Slim date formatting utility for audit system
 * Handles various date formats and edge cases safely
 */

export type DateInput = string | number | Date | null | undefined;

export interface DateFormatOptions {
  includeTime?: boolean;
  includeSeconds?: boolean;
  format?: 'short' | 'medium' | 'long' | 'full';
  locale?: string;
  timezone?: string;
}

/**
 * Safe date parser that handles various input formats
 */
function parseDate(input: DateInput): Date | null {
  if (!input) return null;

  try {
    // Handle Date objects
    if (input instanceof Date) {
      return isNaN(input.getTime()) ? null : input;
    }

    // Handle MongoDB date objects
    if (typeof input === 'object' && input !== null) {
      const mongoDate = input as any;
      if (mongoDate.$date) {
        if (mongoDate.$date.$numberLong) {
          return new Date(parseInt(mongoDate.$date.$numberLong));
        }
        return new Date(mongoDate.$date);
      }
    }

    // Handle string and number inputs
    const date = new Date(input);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.warn('Date parsing error:', error, 'Input:', input);
    return null;
  }
}

/**
 * Format date for audit display with government compliance standards
 */
export function formatAuditDate(
  input: DateInput, 
  options: DateFormatOptions = {}
): string {
  const {
    includeTime = false,
    includeSeconds = false,
    format = 'medium',
    locale = 'en-MW',
    timezone = 'Africa/Blantyre'
  } = options;

  const date = parseDate(input);
  if (!date) return 'Invalid Date';

  try {
    const formatOptions: Intl.DateTimeFormatOptions = {
      timeZone: timezone,
    };

    // Date format options
    switch (format) {
      case 'short':
        formatOptions.year = 'numeric';
        formatOptions.month = 'short';
        formatOptions.day = 'numeric';
        break;
      case 'medium':
        formatOptions.year = 'numeric';
        formatOptions.month = 'short';
        formatOptions.day = 'numeric';
        break;
      case 'long':
        formatOptions.year = 'numeric';
        formatOptions.month = 'long';
        formatOptions.day = 'numeric';
        break;
      case 'full':
        formatOptions.weekday = 'long';
        formatOptions.year = 'numeric';
        formatOptions.month = 'long';
        formatOptions.day = 'numeric';
        break;
    }

    // Time format options
    if (includeTime) {
      formatOptions.hour = '2-digit';
      formatOptions.minute = '2-digit';
      formatOptions.hour12 = false; // 24-hour format for audit compliance
      
      if (includeSeconds) {
        formatOptions.second = '2-digit';
      }
    }

    return new Intl.DateTimeFormat(locale, formatOptions).format(date);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return date.toISOString().split('T')[0]; // Fallback to ISO date
  }
}

/**
 * Format date for audit tables (compact format)
 */
export function formatAuditTableDate(input: DateInput): string {
  return formatAuditDate(input, {
    format: 'short',
    includeTime: true,
    includeSeconds: false
  });
}

/**
 * Format date for audit reports (detailed format)
 */
export function formatAuditReportDate(input: DateInput): string {
  return formatAuditDate(input, {
    format: 'long',
    includeTime: true,
    includeSeconds: true
  });
}

/**
 * Format date for dashboard display
 */
export function formatDashboardDate(input: DateInput): string {
  return formatAuditDate(input, {
    format: 'medium',
    includeTime: false
  });
}

/**
 * Get relative time for recent deletions (e.g., "2 days ago")
 */
export function getRelativeTime(input: DateInput): string {
  const date = parseDate(input);
  if (!date) return 'Unknown';

  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
}

/**
 * Calculate days until deadline
 */
export function getDaysUntilDeadline(input: DateInput): number {
  const date = parseDate(input);
  if (!date) return 0;

  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
}

/**
 * Check if date is within recovery window
 */
export function isWithinRecoveryWindow(deletionDate: DateInput, recoveryDeadline: DateInput): boolean {
  const deletion = parseDate(deletionDate);
  const deadline = parseDate(recoveryDeadline);
  
  if (!deletion || !deadline) return false;
  
  const now = new Date();
  return now <= deadline;
}

/**
 * Format date for ISO compliance (audit logs)
 */
export function formatISODate(input: DateInput): string {
  const date = parseDate(input);
  if (!date) return '';
  
  return date.toISOString();
}

/**
 * Validate if date is reasonable for audit purposes
 */
export function isValidAuditDate(input: DateInput): boolean {
  const date = parseDate(input);
  if (!date) return false;

  const now = new Date();
  const minDate = new Date('2020-01-01'); // Reasonable minimum for audit data
  const maxDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Allow up to 1 day in future

  return date >= minDate && date <= maxDate;
}

/**
 * Format fiscal year from date
 */
export function getFiscalYearFromDate(input: DateInput): string {
  const date = parseDate(input);
  if (!date) return '';

  const year = date.getFullYear();
  const month = date.getMonth(); // 0-based

  // Fiscal year starts in July (month 6)
  if (month >= 6) {
    return `${year}-${year + 1}`;
  } else {
    return `${year - 1}-${year}`;
  }
}

/**
 * Export commonly used formatters
 */
export const AuditDateFormatter = {
  table: formatAuditTableDate,
  report: formatAuditReportDate,
  dashboard: formatDashboardDate,
  relative: getRelativeTime,
  iso: formatISODate,
  daysUntil: getDaysUntilDeadline,
  isWithinRecovery: isWithinRecoveryWindow,
  isValid: isValidAuditDate,
  fiscalYear: getFiscalYearFromDate
};

export default AuditDateFormatter;

/**
 * Utility functions for converting numbers to words
 * Specifically designed for Malawian Kwacha currency
 */

// Number words mapping
const ones = [
  '', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',
  'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',
  'Seventeen', 'Eighteen', 'Nineteen'
];

const tens = [
  '', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'
];

const scales = [
  '', 'Thousand', 'Million', 'Billion', 'Trillion'
];

/**
 * Convert a number (0-999) to words
 */
function convertHundreds(num: number): string {
  let result = '';
  
  if (num >= 100) {
    result += ones[Math.floor(num / 100)] + ' Hundred';
    num %= 100;
    if (num > 0) result += ' ';
  }
  
  if (num >= 20) {
    result += tens[Math.floor(num / 10)];
    num %= 10;
    if (num > 0) result += ' ';
  }
  
  if (num > 0) {
    result += ones[num];
  }
  
  return result;
}

/**
 * Convert a number to words
 */
function numberToWords(num: number): string {
  if (num === 0) return 'Zero';
  
  let result = '';
  let scaleIndex = 0;
  
  while (num > 0) {
    const chunk = num % 1000;
    if (chunk !== 0) {
      const chunkWords = convertHundreds(chunk);
      if (scaleIndex > 0) {
        result = chunkWords + ' ' + scales[scaleIndex] + (result ? ' ' + result : '');
      } else {
        result = chunkWords;
      }
    }
    num = Math.floor(num / 1000);
    scaleIndex++;
  }
  
  return result;
}

/**
 * Convert currency amount to words (Malawian Kwacha)
 * @param amount - The amount to convert
 * @param currency - Currency code (default: 'MWK')
 * @returns Amount in words with currency
 */
export function amountToWords(amount: number, currency: string = 'MWK'): string {
  if (isNaN(amount) || amount < 0) {
    return 'Invalid Amount';
  }
  
  // Round to 2 decimal places to handle floating point precision
  const roundedAmount = Math.round(amount * 100) / 100;
  
  // Split into kwacha and tambala (cents)
  const kwacha = Math.floor(roundedAmount);
  const tambala = Math.round((roundedAmount - kwacha) * 100);
  
  let result = '';
  
  // Convert kwacha part
  if (kwacha === 0) {
    result = 'Zero';
  } else {
    result = numberToWords(kwacha);
  }
  
  // Add currency name
  const currencyName = getCurrencyName(currency);
  result += ` ${currencyName}`;
  
  // Add tambala if present
  if (tambala > 0) {
    const tambalaPart = numberToWords(tambala);
    const subunitName = getCurrencySubunitName(currency);
    result += ` and ${tambalaPart} ${subunitName}`;
  }
  
  result += ' Only';
  
  return result;
}

/**
 * Get currency name based on currency code
 */
function getCurrencyName(currency: string): string {
  const currencyNames: { [key: string]: string } = {
    'MWK': 'Malawian Kwacha',
    'USD': 'US Dollars',
    'EUR': 'Euros',
    'GBP': 'British Pounds',
    'ZAR': 'South African Rand',
    'KES': 'Kenyan Shillings',
    'TZS': 'Tanzanian Shillings',
    'UGX': 'Ugandan Shillings'
  };
  
  return currencyNames[currency.toUpperCase()] || currency.toUpperCase();
}

/**
 * Get currency subunit name based on currency code
 */
function getCurrencySubunitName(currency: string): string {
  const subunitNames: { [key: string]: string } = {
    'MWK': 'Tambala',
    'USD': 'Cents',
    'EUR': 'Cents',
    'GBP': 'Pence',
    'ZAR': 'Cents',
    'KES': 'Cents',
    'TZS': 'Cents',
    'UGX': 'Cents'
  };
  
  return subunitNames[currency.toUpperCase()] || 'Cents';
}

/**
 * Format amount in words for display (with proper capitalization)
 * @param amount - The amount to convert
 * @param currency - Currency code (default: 'MWK')
 * @returns Properly formatted amount in words
 */
export function formatAmountInWords(amount: number, currency: string = 'MWK'): string {
  const words = amountToWords(amount, currency);
  
  // Ensure proper capitalization (first letter uppercase, rest as is)
  return words.charAt(0).toUpperCase() + words.slice(1);
}

/**
 * Convert amount to words for checks/vouchers (all uppercase)
 * @param amount - The amount to convert
 * @param currency - Currency code (default: 'MWK')
 * @returns Amount in words in uppercase
 */
export function amountToWordsUppercase(amount: number, currency: string = 'MWK'): string {
  return amountToWords(amount, currency).toUpperCase();
}

/**
 * Validate and convert amount to words with error handling
 * @param amount - The amount to convert
 * @param currency - Currency code (default: 'MWK')
 * @returns Amount in words or error message
 */
export function safeAmountToWords(amount: unknown, currency: string = 'MWK'): string {
  try {
    // Handle different input types
    let numericAmount: number;
    
    if (typeof amount === 'string') {
      // Remove currency symbols and commas
      const cleaned = amount.replace(/[^\d.-]/g, '');
      numericAmount = parseFloat(cleaned);
    } else if (typeof amount === 'number') {
      numericAmount = amount;
    } else {
      return 'Invalid Amount Format';
    }
    
    if (isNaN(numericAmount)) {
      return 'Invalid Amount';
    }
    
    return formatAmountInWords(numericAmount, currency);
  } catch (error) {
    console.error('Error converting amount to words:', error);
    return 'Error Converting Amount';
  }
}

/**
 * Test file for number-to-words utility
 * Run with: npm test number-to-words.test.ts
 */

import { 
  amountToWords, 
  formatAmountInWords, 
  amountToWordsUppercase, 
  safeAmountToWords 
} from '../number-to-words';

describe('Number to Words Utility', () => {
  describe('amountToWords', () => {
    test('converts basic amounts correctly', () => {
      expect(amountToWords(0)).toBe('Zero Malawian Kwacha Only');
      expect(amountToWords(1)).toBe('One Malawian Kwacha Only');
      expect(amountToWords(15)).toBe('Fifteen Malawian Kwacha Only');
      expect(amountToWords(100)).toBe('One Hundred Malawian Kwacha Only');
      expect(amountToWords(1000)).toBe('One Thousand Malawian Kwacha Only');
    });

    test('converts complex amounts correctly', () => {
      expect(amountToWords(125000)).toBe('One Hundred Twenty Five Thousand Malawian Kwacha Only');
      expect(amountToWords(1234567)).toBe('One Million Two Hundred Thirty Four Thousand Five Hundred Sixty Seven Malawian Kwacha Only');
    });

    test('handles decimal amounts correctly', () => {
      expect(amountToWords(125.50)).toBe('One Hundred Twenty Five Malawian Kwacha and Fifty Tambala Only');
      expect(amountToWords(1000.25)).toBe('One Thousand Malawian Kwacha and Twenty Five Tambala Only');
    });

    test('handles different currencies', () => {
      expect(amountToWords(100, 'USD')).toBe('One Hundred US Dollars Only');
      expect(amountToWords(50.25, 'USD')).toBe('Fifty US Dollars and Twenty Five Cents Only');
    });

    test('handles edge cases', () => {
      expect(amountToWords(-1)).toBe('Invalid Amount');
      expect(amountToWords(NaN)).toBe('Invalid Amount');
    });
  });

  describe('formatAmountInWords', () => {
    test('formats with proper capitalization', () => {
      const result = formatAmountInWords(125000);
      expect(result).toBe('One Hundred Twenty Five Thousand Malawian Kwacha Only');
      expect(result.charAt(0)).toBe('O'); // First letter uppercase
    });
  });

  describe('amountToWordsUppercase', () => {
    test('converts to uppercase', () => {
      const result = amountToWordsUppercase(125000);
      expect(result).toBe('ONE HUNDRED TWENTY FIVE THOUSAND MALAWIAN KWACHA ONLY');
    });
  });

  describe('safeAmountToWords', () => {
    test('handles string inputs', () => {
      expect(safeAmountToWords('125000')).toBe('One Hundred Twenty Five Thousand Malawian Kwacha Only');
      expect(safeAmountToWords('MWK 125,000.00')).toBe('One Hundred Twenty Five Thousand Malawian Kwacha Only');
    });

    test('handles invalid inputs gracefully', () => {
      expect(safeAmountToWords('invalid')).toBe('Invalid Amount');
      expect(safeAmountToWords(null)).toBe('Invalid Amount Format');
      expect(safeAmountToWords(undefined)).toBe('Invalid Amount Format');
    });
  });

  describe('Real-world voucher amounts', () => {
    test('typical payroll amounts', () => {
      // Typical teacher salary
      expect(amountToWords(450000)).toBe('Four Hundred Fifty Thousand Malawian Kwacha Only');
      
      // Bulk payroll amount
      expect(amountToWords(12500000)).toBe('Twelve Million Five Hundred Thousand Malawian Kwacha Only');
    });

    test('typical expense amounts', () => {
      // Office supplies
      expect(amountToWords(75000)).toBe('Seventy Five Thousand Malawian Kwacha Only');
      
      // Utility bill
      expect(amountToWords(250000.75)).toBe('Two Hundred Fifty Thousand Malawian Kwacha and Seventy Five Tambala Only');
    });

    test('small amounts with tambala', () => {
      expect(amountToWords(1.50)).toBe('One Malawian Kwacha and Fifty Tambala Only');
      expect(amountToWords(0.25)).toBe('Zero Malawian Kwacha and Twenty Five Tambala Only');
    });
  });
});

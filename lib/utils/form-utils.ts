// lib/utils/form-utils.ts
import { useForm as useReactHookForm, UseFormProps, UseFormReturn } from "react-hook-form"

/**
 * Type-safe wrapper for useForm that resolves TypeScript inference issues
 * with react-hook-form and complex form schemas.
 * 
 * This utility provides proper type safety without using 'any' types
 * and resolves common FormField control type compatibility issues.
 * 
 * @param props - Standard react-hook-form useForm props
 * @returns Properly typed form instance
 */
export function useTypeSafeForm<TFieldValues extends Record<string, any> = Record<string, any>>(
  props?: UseFormProps<TFieldValues>
): UseFormReturn<TFieldValues> {
  const form = useReactHookForm(props)
  return form as UseFormReturn<TFieldValues>
}

/**
 * Alternative approach using type assertion for existing forms
 * Use this when you want to keep the existing useForm import
 * 
 * @param form - The form instance from useForm
 * @returns Type-asserted form instance
 */
export function assertFormType<TFieldValues extends Record<string, any>>(
  form: any
): UseFormReturn<TFieldValues> {
  return form as UseFormReturn<TFieldValues>
}

// lib/services/local-storage-service.ts
"use client"

interface CacheItem<T> {
  data: T
  timestamp: number
  expiresIn: number
}

interface FormData {
  budgets: any[]
  budgetCategories: any[]
  budgetSubcategories: any[]
  fiscalYears: string[]
  incomeSources: Array<{ value: string; label: string }>
  statusOptions: Array<{ value: string; label: string }>
}

class LocalStorageService {
  private readonly CACHE_PREFIX = 'hrm_cache_'
  private readonly FORM_DATA_KEY = 'income_form_data'
  private readonly DEFAULT_EXPIRY = 30 * 60 * 1000 // 30 minutes

  // Check if we're in browser environment
  private isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof localStorage !== 'undefined'
  }

  // Generic cache methods
  private setCache<T>(key: string, data: T, expiresIn: number = this.DEFAULT_EXPIRY): void {
    if (!this.isBrowser()) return

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiresIn
    }

    try {
      localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheItem))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  private getCache<T>(key: string): T | null {
    if (!this.isBrowser()) return null

    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key)
      if (!cached) return null

      const cacheItem: CacheItem<T> = JSON.parse(cached)
      const now = Date.now()

      // Check if cache has expired
      if (now - cacheItem.timestamp > cacheItem.expiresIn) {
        this.clearCache(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return null
    }
  }

  private clearCache(key: string): void {
    if (!this.isBrowser()) return

    try {
      localStorage.removeItem(this.CACHE_PREFIX + key)
    } catch (error) {
      console.warn('Failed to clear cache:', error)
    }
  }

  // Pre-fetch all form data
  async preloadFormData(): Promise<FormData> {
    console.log('Starting form data preload...')

    // Check if we have cached data first
    const cachedData = this.getCache<FormData>(this.FORM_DATA_KEY)
    if (cachedData) {
      console.log('Using cached form data')
      return cachedData
    }

    console.log('Fetching fresh form data...')

    // Prepare default data structure
    const formData: FormData = {
      budgets: [],
      budgetCategories: [],
      budgetSubcategories: [],
      fiscalYears: this.generateFiscalYears(),
      incomeSources: this.getIncomeSourceOptions(),
      statusOptions: this.getStatusOptions()
    }

    // Try to fetch budgets (non-blocking)
    try {
      const budgetResponse = await fetch('/api/accounting/budget?status=active', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (budgetResponse.ok) {
        const budgetData = await budgetResponse.json()
        formData.budgets = budgetData.budgets || []
        console.log('Loaded budgets:', formData.budgets.length)
      }
    } catch (error) {
      console.warn('Failed to load budgets:', error)
    }

    // Cache the data
    this.setCache(this.FORM_DATA_KEY, formData, this.DEFAULT_EXPIRY)
    console.log('Form data cached successfully')

    return formData
  }

  // Get form data from cache (instant)
  getFormData(): FormData {
    const cachedData = this.getCache<FormData>(this.FORM_DATA_KEY)
    
    if (cachedData) {
      return cachedData
    }

    // Return default data if no cache
    return {
      budgets: [],
      budgetCategories: [],
      budgetSubcategories: [],
      fiscalYears: this.generateFiscalYears(),
      incomeSources: this.getIncomeSourceOptions(),
      statusOptions: this.getStatusOptions()
    }
  }

  // Generate fiscal years
  private generateFiscalYears(): string[] {
    const currentYear = new Date().getFullYear()
    const years: string[] = []
    
    for (let i = -2; i <= 3; i++) {
      const year = currentYear + i
      years.push(`${year}-${year + 1}`)
    }
    
    return years
  }

  // Get current fiscal year
  getCurrentFiscalYear(): string {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth()
    
    // Fiscal year starts in July (month 6)
    if (month >= 6) {
      return `${year}-${year + 1}`
    } else {
      return `${year - 1}-${year}`
    }
  }

  // Income source options
  private getIncomeSourceOptions() {
    return [
      { value: 'government_subvention', label: 'Government Subvention' },
      { value: 'registration_fees', label: 'Registration Fees' },
      { value: 'licensing_fees', label: 'Licensing Fees' },
      { value: 'donations', label: 'Donations' },
      { value: 'other', label: 'Other' }
    ]
  }

  // Status options
  private getStatusOptions() {
    return [
      { value: 'draft', label: 'Draft' },
      { value: 'pending_approval', label: 'Submit for Approval' },
      { value: 'approved', label: 'Approved' },
      { value: 'received', label: 'Received' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'cancelled', label: 'Cancelled' }
    ]
  }

  // Clear all form data cache
  clearFormDataCache(): void {
    this.clearCache(this.FORM_DATA_KEY)
    console.log('Form data cache cleared')
  }

  // Force refresh form data
  async refreshFormData(): Promise<FormData> {
    this.clearFormDataCache()
    return await this.preloadFormData()
  }

  // Save form draft to localStorage
  saveFormDraft(formData: any): void {
    this.setCache('form_draft', formData, 24 * 60 * 60 * 1000) // 24 hours
  }

  // Get form draft from localStorage
  getFormDraft(): any {
    return this.getCache('form_draft')
  }

  // Clear form draft
  clearFormDraft(): void {
    this.clearCache('form_draft')
  }
}

// Export singleton instance
export const localStorageService = new LocalStorageService()

// lib/services/accounting/budget-expenditure-integration.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget, BudgetCategory, BudgetItem } from '@/models/accounting/Budget';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import { Transaction } from '@/models/accounting/Transaction';
import Expense from '@/models/accounting/Expense';

/**
 * Service for integrating expenditures with budget items
 * This mirrors the BudgetIncomeIntegrationService but with opposite effects (decrements)
 */
export class BudgetExpenditureIntegrationService {
  /**
   * Create BudgetExpenditure record when expense is created
   * This creates actual budget line items that appear in budget planning
   */
  async createExpenditureAsBudgetItem(expense: any): Promise<void> {
    try {
      if (!expense.appliedToBudget || !expense.budget || !expense.budgetCategory) {
        return; // Skip silently for better performance
      }

      await connectToDatabase();

      // Calculate budget impact (negative for expenditures)
      const impactAmount = -Math.abs(expense.amount);
      
      // Get budget category for impact calculation
      const budgetCategory = await BudgetCategory.findById(expense.budgetCategory);
      
      // Use upsert to avoid checking for existing item first (more efficient)
      const budgetExpenditure = await BudgetExpenditure.findOneAndUpdate(
        {
          sourceExpenditure: expense._id,
          budget: expense.budget,
          budgetCategory: expense.budgetCategory
        },
        {
          $set: {
            amount: expense.amount,
            currency: expense.currency || 'MWK',
            exchangeRate: expense.exchangeRate || 1,
            amountInBaseCurrency: expense.amount * (expense.exchangeRate || 1),
            date: expense.date,
            reference: expense.reference,
            description: expense.description || `${expense.category} expense - ${expense.reference}`,
            category: expense.category,
            subcategory: expense.subcategory,
            status: expense.status,
            contributionType: this.getContributionType(expense.status),
            budgetImpact: {
              impactAmount,
              utilizationPercentage: budgetCategory?.budgetedAmount > 0 ? 
                (Math.abs(budgetCategory.actualAmount || 0) / budgetCategory.budgetedAmount) * 100 : 0,
              varianceCreated: (budgetCategory?.actualAmount || 0) - (budgetCategory?.budgetedAmount || 0),
              budgetedAmount: budgetCategory?.budgetedAmount || 0,
              actualAmount: budgetCategory?.actualAmount || 0
            },
            fiscalYear: expense.fiscalYear,
            appliedToBudget: true,
            paymentMethod: expense.paymentMethod,
            vendor: expense.vendor,
            department: expense.department,
            notes: expense.notes,
            budgetSubcategory: expense.budgetSubcategory,
            createdBy: expense.createdBy,
            updatedBy: expense.updatedBy
          }
        },
        {
          upsert: true, // Create if doesn't exist, update if exists
          new: true,    // Return the updated document
          lean: true    // Return plain object for better performance
        }
      );

      // Create corresponding BudgetItem for budget planning integration
      await BudgetItem.findOneAndUpdate(
        {
          name: `Expense: ${expense.reference}`,
          parentCategory: expense.budgetCategory,
          budget: expense.budget
        },
        {
          $set: {
            description: expense.description || `${expense.category} expense - ${expense.reference}`,
            quantity: 1,
            frequency: 1,
            unitCost: expense.amount,
            amount: expense.amount,
            parentCategory: expense.budgetCategory,
            parentSubcategory: expense.budgetSubcategory,
            budget: expense.budget,
          }
        },
        {
          upsert: true,
          new: true,
          lean: true
        }
      );

      // Ensure budget category is in budget.categories array
      await Budget.findByIdAndUpdate(expense.budget, {
        $addToSet: { categories: expense.budgetCategory }
      }, { lean: true });

      // CRITICAL: Add expenditure to budget category items array (like income does)
      await this.updateBudgetCategoryItems(expense);

      // Log success (non-blocking)
      if (budgetExpenditure && typeof budgetExpenditure === 'object' && '_id' in budgetExpenditure) {
        logger.info('Created/Updated BudgetExpenditure for expense', {
          expenseId: expense._id,
          budgetExpenditureId: (budgetExpenditure as any)._id,
          budgetId: expense.budget,
          categoryId: expense.budgetCategory,
          amount: expense.amount,
          impactAmount
        });
      }

    } catch (error) {
      // Log error but don't throw - this is an enhancement
      logger.error('Error creating BudgetExpenditure for expense', error);
    }
  }

  /**
   * Update BudgetExpenditure when expense changes
   */
  async updateExpenditureAsBudgetItem(expense: any): Promise<void> {
    try {
      if (!expense.appliedToBudget || !expense.budget || !expense.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Calculate budget impact (negative for expenditures)
      const impactAmount = -Math.abs(expense.amount);
      
      // Get budget category for impact calculation
      const budgetCategory = await BudgetCategory.findById(expense.budgetCategory);

      // Use findOneAndUpdate for better performance (single operation)
      const budgetExpenditure = await BudgetExpenditure.findOneAndUpdate(
        {
          sourceExpenditure: expense._id,
          budget: expense.budget,
          budgetCategory: expense.budgetCategory
        },
        {
          $set: {
            amount: expense.amount,
            amountInBaseCurrency: expense.amount * (expense.exchangeRate || 1),
            description: expense.description || `${expense.category} expense - ${expense.reference}`,
            status: expense.status,
            contributionType: this.getContributionType(expense.status),
            budgetImpact: {
              impactAmount,
              utilizationPercentage: budgetCategory?.budgetedAmount > 0 ? 
                (Math.abs(budgetCategory.actualAmount || 0) / budgetCategory.budgetedAmount) * 100 : 0,
              varianceCreated: (budgetCategory?.actualAmount || 0) - (budgetCategory?.budgetedAmount || 0),
              budgetedAmount: budgetCategory?.budgetedAmount || 0,
              actualAmount: budgetCategory?.actualAmount || 0
            },
            paymentMethod: expense.paymentMethod,
            vendor: expense.vendor,
            department: expense.department,
            notes: expense.notes,
            updatedBy: expense.updatedBy
          }
        },
        {
          new: true,
          lean: true
        }
      );

      // Update corresponding BudgetItem
      await BudgetItem.findOneAndUpdate(
        {
          name: `Expense: ${expense.reference}`,
          parentCategory: expense.budgetCategory,
          budget: expense.budget
        },
        {
          $set: {
            description: expense.description || `${expense.category} expense - ${expense.reference}`,
            unitCost: expense.amount,
            amount: expense.amount
          }
        },
        {
          new: true,
          lean: true
        }
      );

      if (budgetExpenditure) {
        logger.info('Updated BudgetExpenditure for expense', {
          expenseId: expense._id,
          budgetExpenditureId: (budgetExpenditure as any)._id,
          amount: expense.amount,
          status: expense.status
        });
      }

    } catch (error) {
      logger.error('Error updating BudgetExpenditure for expense', error);
    }
  }

  /**
   * Handle expense status workflow changes
   */
  async handleExpenditureStatusChange(expense: any, oldStatus: string): Promise<void> {
    try {
      if (!expense.appliedToBudget || !expense.budget || !expense.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Update the BudgetExpenditure status and contribution type
      await BudgetExpenditure.findOneAndUpdate(
        {
          sourceExpenditure: expense._id,
          budget: expense.budget,
          budgetCategory: expense.budgetCategory
        },
        {
          $set: {
            status: expense.status,
            contributionType: this.getContributionType(expense.status),
            [`${expense.status}At`]: new Date(),
            [`${expense.status}By`]: expense.updatedBy
          }
        }
      );

      logger.info('Updated BudgetExpenditure status', {
        expenseId: expense._id,
        oldStatus,
        newStatus: expense.status,
        contributionType: this.getContributionType(expense.status)
      });

    } catch (error) {
      logger.error('Error handling expenditure status change', error);
    }
  }

  /**
   * Remove expenditure from budget when expense is deleted
   */
  async removeExpenditureFromBudget(expenseId: string): Promise<void> {
    try {
      await connectToDatabase();

      // Find and remove the BudgetExpenditure record
      const budgetExpenditure = await BudgetExpenditure.findOneAndDelete({
        sourceExpenditure: expenseId
      });

      if (budgetExpenditure) {
        // Also remove the corresponding BudgetItem
        await BudgetItem.findOneAndDelete({
          name: `Expense: ${budgetExpenditure.reference}`,
          parentCategory: budgetExpenditure.budgetCategory,
          budget: budgetExpenditure.budget
        });

        logger.info('Removed BudgetExpenditure and BudgetItem for deleted expense', {
          expenseId,
          budgetExpenditureId: budgetExpenditure._id
        });
      }

    } catch (error) {
      logger.error('Error removing expenditure from budget', error);
    }
  }

  /**
   * Update Budget Category items with expenditure record (mirrors income pattern)
   * This is the CRITICAL missing piece that adds expenditures to budget categories
   */
  async updateBudgetCategoryItems(expense: any): Promise<void> {
    try {
      const { BudgetCategory } = require('@/models/accounting/Budget');

      const category = await BudgetCategory.findById(expense.budgetCategory._id || expense.budgetCategory);
      if (!category) {
        logger.warn('Budget category not found for expenditure', {
          expenditureId: expense._id,
          categoryId: expense.budgetCategory
        });
        return;
      }

      // Remove existing expenditure from category items if it exists
      const existingIndex = category.items.findIndex((item: any) =>
        item.sourceId && item.sourceId.toString() === expense._id.toString()
      );

      if (existingIndex > -1) {
        category.items.splice(existingIndex, 1);
      }

      // Add expenditure as category item based on status
      const categoryItem = {
        sourceId: expense._id,
        sourceType: 'expense',
        description: expense.description || `Expenditure - ${expense.reference}`,
        amount: expense.amount,
        status: expense.status,
        reference: expense.reference,
        date: expense.date,
        contributionType: this.getContributionType(expense.status)
      };

      category.items.push(categoryItem);

      // Recalculate category totals
      await this.recalculateCategoryTotals(category);
      await category.save();

      logger.info('Updated budget category items with expenditure', {
        categoryId: category._id,
        expenditureId: expense._id,
        amount: expense.amount,
        status: expense.status,
        itemsCount: category.items.length
      });

    } catch (error) {
      logger.error('Error updating budget category items with expenditure', error);
      throw error;
    }
  }

  /**
   * Recalculate category totals from items (mirrors income pattern)
   */
  async recalculateCategoryTotals(category: any): Promise<void> {
    let totalDraft = 0;
    let totalApproved = 0;
    let totalPaid = 0;

    category.items.forEach((item: any) => {
      if (item.sourceType === 'expense') {
        switch (item.status) {
          case 'draft':
            totalDraft += item.amount;
            break;
          case 'approved':
            totalApproved += item.amount;
            break;
          case 'paid':
            totalPaid += item.amount;
            break;
        }
      }
    });

    // Update category totals for expenditures
    category.total = totalDraft + totalApproved; // All planned expenditures
    category.actualAmount = totalPaid;           // Only paid expenditures
    category.budgetedAmount = totalDraft + totalApproved; // Total planned expenditures

    logger.info('Recalculated expenditure category totals', {
      categoryId: category._id,
      totalDraft,
      totalApproved,
      totalPaid,
      finalTotal: category.total,
      finalActual: category.actualAmount
    });
  }

  /**
   * Get contribution type based on expense status
   */
  private getContributionType(status: string): 'projected' | 'expected' | 'actual' {
    switch (status) {
      case 'approved':
        return 'expected';
      case 'paid':
        return 'actual';
      default:
        return 'projected';
    }
  }

  /**
   * Recalculate budget actuals for a specific budget
   */
  async recalculateBudgetActuals(budgetId: string): Promise<void> {
    try {
      await connectToDatabase();

      // Get all BudgetExpenditure records for this budget
      const expenditures = await BudgetExpenditure.find({
        budget: budgetId,
        status: { $in: ['approved', 'paid'] }
      });

      // Group by category and calculate totals
      const categoryTotals = expenditures.reduce((acc, exp) => {
        const categoryId = exp.budgetCategory.toString();
        if (!acc[categoryId]) {
          acc[categoryId] = { expected: 0, actual: 0 };
        }
        
        if (exp.status === 'approved') {
          acc[categoryId].expected += exp.amountInBaseCurrency;
        } else if (exp.status === 'paid') {
          acc[categoryId].actual += exp.amountInBaseCurrency;
        }
        
        return acc;
      }, {} as Record<string, { expected: number; actual: number }>);

      // Update budget categories
      for (const [categoryId, totals] of Object.entries(categoryTotals)) {
        await BudgetCategory.findByIdAndUpdate(categoryId, {
          $set: {
            actualAmount: totals.actual,
            lastActualUpdateDate: new Date()
          }
        });
      }

      // Update budget totals
      const totalExpected = Object.values(categoryTotals).reduce((sum, t) => sum + t.expected, 0);
      const totalActual = Object.values(categoryTotals).reduce((sum, t) => sum + t.actual, 0);

      await Budget.findByIdAndUpdate(budgetId, {
        $set: {
          totalActualExpense: totalActual,
          lastActualUpdateDate: new Date()
        }
      });

      logger.info('Recalculated budget actuals for expenditures', {
        budgetId,
        totalExpected,
        totalActual,
        categoriesUpdated: Object.keys(categoryTotals).length
      });

    } catch (error) {
      logger.error('Error recalculating budget actuals', error);
    }
  }
}

// Export singleton instance
export const budgetExpenditureIntegrationService = new BudgetExpenditureIntegrationService();

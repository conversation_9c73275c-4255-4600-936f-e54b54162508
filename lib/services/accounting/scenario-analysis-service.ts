// lib/services/accounting/scenario-analysis-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget } from '@/models/accounting/Budget';
import { BudgetCategory } from '@/models/accounting/BudgetCategory';

export interface ScenarioParameter {
  categoryId?: string;
  categoryName?: string;
  parameterType: 'income_change' | 'expense_change' | 'growth_rate' | 'seasonal_factor';
  changeType: 'percentage' | 'absolute';
  changeValue: number;
  description: string;
}

export interface ScenarioResult {
  scenarioName: string;
  parameters: ScenarioParameter[];
  originalBudget: {
    totalIncome: number;
    totalExpense: number;
    netPosition: number;
  };
  adjustedBudget: {
    totalIncome: number;
    totalExpense: number;
    netPosition: number;
  };
  impact: {
    incomeChange: number;
    expenseChange: number;
    netChange: number;
    incomeChangePercentage: number;
    expenseChangePercentage: number;
    netChangePercentage: number;
  };
  categoryImpacts: Array<{
    categoryId: string;
    categoryName: string;
    originalAmount: number;
    adjustedAmount: number;
    change: number;
    changePercentage: number;
  }>;
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
}

export interface WhatIfAnalysis {
  budgetId: string;
  budgetName: string;
  scenarios: ScenarioResult[];
  comparison: {
    bestCase: ScenarioResult;
    worstCase: ScenarioResult;
    mostLikely: ScenarioResult;
  };
  sensitivityAnalysis: Array<{
    parameter: string;
    impact: number;
    sensitivity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * Scenario Analysis Service for Budget Planning
 * Provides what-if analysis and scenario planning capabilities
 */
export class ScenarioAnalysisService {

  /**
   * Perform comprehensive what-if analysis
   */
  async performWhatIfAnalysis(
    budgetId: string,
    scenarios: Array<{
      name: string;
      parameters: ScenarioParameter[];
    }>
  ): Promise<WhatIfAnalysis> {
    try {
      await connectToDatabase();
      
      logger.info('Performing what-if analysis', LogCategory.ACCOUNTING, {
        budgetId,
        scenarioCount: scenarios.length
      });

      // Get budget and categories
      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        throw new Error('Budget not found');
      }

      const categories = await BudgetCategory.find({ budget: budgetId }).lean();

      // Analyze each scenario
      const scenarioResults: ScenarioResult[] = [];
      
      for (const scenario of scenarios) {
        const result = await this.analyzeScenario(
          budget,
          categories,
          scenario.name,
          scenario.parameters
        );
        scenarioResults.push(result);
      }

      // Generate comparison and sensitivity analysis
      const comparison = this.generateComparison(scenarioResults);
      const sensitivityAnalysis = this.performSensitivityAnalysis(scenarioResults);

      return {
        budgetId,
        budgetName: budget.name,
        scenarios: scenarioResults,
        comparison,
        sensitivityAnalysis
      };

    } catch (error) {
      logger.error('Error performing what-if analysis', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Analyze a single scenario
   */
  private async analyzeScenario(
    budget: any,
    categories: any[],
    scenarioName: string,
    parameters: ScenarioParameter[]
  ): Promise<ScenarioResult> {
    
    // Calculate original budget totals
    const originalIncome = categories
      .filter(cat => cat.type === 'income')
      .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);
    
    const originalExpense = categories
      .filter(cat => cat.type === 'expense')
      .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);

    const originalNet = originalIncome - originalExpense;

    // Apply scenario parameters
    const adjustedCategories = categories.map(category => ({ ...category }));
    
    for (const parameter of parameters) {
      this.applyParameterToCategories(adjustedCategories, parameter);
    }

    // Calculate adjusted budget totals
    const adjustedIncome = adjustedCategories
      .filter(cat => cat.type === 'income')
      .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);
    
    const adjustedExpense = adjustedCategories
      .filter(cat => cat.type === 'expense')
      .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);

    const adjustedNet = adjustedIncome - adjustedExpense;

    // Calculate impacts
    const incomeChange = adjustedIncome - originalIncome;
    const expenseChange = adjustedExpense - originalExpense;
    const netChange = adjustedNet - originalNet;

    const incomeChangePercentage = originalIncome !== 0 ? (incomeChange / originalIncome) * 100 : 0;
    const expenseChangePercentage = originalExpense !== 0 ? (expenseChange / originalExpense) * 100 : 0;
    const netChangePercentage = originalNet !== 0 ? (netChange / originalNet) * 100 : 0;

    // Calculate category impacts
    const categoryImpacts = categories.map((originalCat, index) => {
      const adjustedCat = adjustedCategories[index];
      const originalAmount = originalCat.budgetedAmount || 0;
      const adjustedAmount = adjustedCat.budgetedAmount || 0;
      const change = adjustedAmount - originalAmount;
      const changePercentage = originalAmount !== 0 ? (change / originalAmount) * 100 : 0;

      return {
        categoryId: originalCat._id.toString(),
        categoryName: originalCat.name,
        originalAmount,
        adjustedAmount,
        change,
        changePercentage
      };
    });

    // Assess risk level
    const riskLevel = this.assessRiskLevel(netChangePercentage, incomeChangePercentage, expenseChangePercentage);

    // Generate recommendations
    const recommendations = this.generateScenarioRecommendations(
      incomeChangePercentage,
      expenseChangePercentage,
      netChangePercentage,
      riskLevel
    );

    return {
      scenarioName,
      parameters,
      originalBudget: {
        totalIncome: originalIncome,
        totalExpense: originalExpense,
        netPosition: originalNet
      },
      adjustedBudget: {
        totalIncome: adjustedIncome,
        totalExpense: adjustedExpense,
        netPosition: adjustedNet
      },
      impact: {
        incomeChange,
        expenseChange,
        netChange,
        incomeChangePercentage,
        expenseChangePercentage,
        netChangePercentage
      },
      categoryImpacts,
      riskLevel,
      recommendations
    };
  }

  /**
   * Apply scenario parameter to categories
   */
  private applyParameterToCategories(categories: any[], parameter: ScenarioParameter): void {
    const targetCategories = parameter.categoryId 
      ? categories.filter(cat => cat._id.toString() === parameter.categoryId)
      : parameter.categoryName
      ? categories.filter(cat => cat.name.toLowerCase().includes(parameter.categoryName!.toLowerCase()))
      : categories;

    for (const category of targetCategories) {
      const currentAmount = category.budgetedAmount || 0;
      let adjustment = 0;

      switch (parameter.parameterType) {
        case 'income_change':
        case 'expense_change':
          if (parameter.changeType === 'percentage') {
            adjustment = currentAmount * (parameter.changeValue / 100);
          } else {
            adjustment = parameter.changeValue;
          }
          break;
        
        case 'growth_rate':
          adjustment = currentAmount * (parameter.changeValue / 100);
          break;
        
        case 'seasonal_factor':
          adjustment = currentAmount * (parameter.changeValue - 1);
          break;
      }

      category.budgetedAmount = Math.max(0, currentAmount + adjustment);
    }
  }

  /**
   * Assess risk level based on changes
   */
  private assessRiskLevel(
    netChangePercentage: number,
    incomeChangePercentage: number,
    expenseChangePercentage: number
  ): 'low' | 'medium' | 'high' {
    
    // High risk conditions
    if (netChangePercentage < -20 || 
        incomeChangePercentage < -15 || 
        expenseChangePercentage > 25) {
      return 'high';
    }
    
    // Medium risk conditions
    if (netChangePercentage < -10 || 
        incomeChangePercentage < -10 || 
        expenseChangePercentage > 15) {
      return 'medium';
    }
    
    return 'low';
  }

  /**
   * Generate scenario-specific recommendations
   */
  private generateScenarioRecommendations(
    incomeChangePercentage: number,
    expenseChangePercentage: number,
    netChangePercentage: number,
    riskLevel: 'low' | 'medium' | 'high'
  ): string[] {
    const recommendations = [];

    if (incomeChangePercentage < -10) {
      recommendations.push('Consider diversifying income sources to reduce dependency on affected revenue streams');
    }

    if (expenseChangePercentage > 20) {
      recommendations.push('Implement cost control measures to manage expense increases');
    }

    if (netChangePercentage < -15) {
      recommendations.push('Build contingency reserves to handle potential negative cash flow periods');
    }

    if (riskLevel === 'high') {
      recommendations.push('Develop risk mitigation strategies for high-impact scenarios');
      recommendations.push('Consider scenario-specific action plans and triggers');
    }

    if (netChangePercentage > 10) {
      recommendations.push('Explore opportunities to capitalize on positive scenarios');
    }

    return recommendations;
  }

  /**
   * Generate comparison between scenarios
   */
  private generateComparison(scenarios: ScenarioResult[]): {
    bestCase: ScenarioResult;
    worstCase: ScenarioResult;
    mostLikely: ScenarioResult;
  } {
    
    // Sort by net change percentage
    const sortedByNet = [...scenarios].sort((a, b) => 
      b.impact.netChangePercentage - a.impact.netChangePercentage
    );

    const bestCase = sortedByNet[0];
    const worstCase = sortedByNet[sortedByNet.length - 1];
    
    // Most likely is the one with lowest risk level, or middle scenario
    const mostLikely = scenarios.find(s => s.riskLevel === 'low') || 
                      scenarios[Math.floor(scenarios.length / 2)];

    return {
      bestCase,
      worstCase,
      mostLikely
    };
  }

  /**
   * Perform sensitivity analysis
   */
  private performSensitivityAnalysis(scenarios: ScenarioResult[]): Array<{
    parameter: string;
    impact: number;
    sensitivity: 'low' | 'medium' | 'high';
  }> {
    
    const parameterImpacts = new Map();

    // Analyze impact of each parameter type
    scenarios.forEach(scenario => {
      scenario.parameters.forEach(param => {
        const key = `${param.parameterType}_${param.changeType}`;
        if (!parameterImpacts.has(key)) {
          parameterImpacts.set(key, []);
        }
        parameterImpacts.get(key).push(Math.abs(scenario.impact.netChangePercentage));
      });
    });

    const sensitivityResults = [];
    
    for (const [parameter, impacts] of parameterImpacts.entries()) {
      const avgImpact = impacts.reduce((sum: number, impact: number) => sum + impact, 0) / impacts.length;
      
      let sensitivity: 'low' | 'medium' | 'high';
      if (avgImpact > 15) {
        sensitivity = 'high';
      } else if (avgImpact > 5) {
        sensitivity = 'medium';
      } else {
        sensitivity = 'low';
      }

      sensitivityResults.push({
        parameter: parameter.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        impact: avgImpact,
        sensitivity
      });
    }

    return sensitivityResults.sort((a, b) => b.impact - a.impact);
  }

  /**
   * Generate predefined scenario templates
   */
  generateScenarioTemplates(): Array<{
    name: string;
    description: string;
    parameters: ScenarioParameter[];
  }> {
    return [
      {
        name: 'Economic Downturn',
        description: 'Simulates impact of economic recession on budget',
        parameters: [
          {
            parameterType: 'income_change',
            changeType: 'percentage',
            changeValue: -15,
            description: 'Income reduction due to economic conditions'
          },
          {
            parameterType: 'expense_change',
            changeType: 'percentage',
            changeValue: 5,
            description: 'Increased operational costs'
          }
        ]
      },
      {
        name: 'Growth Scenario',
        description: 'Optimistic growth with increased revenue and controlled expenses',
        parameters: [
          {
            parameterType: 'income_change',
            changeType: 'percentage',
            changeValue: 20,
            description: 'Revenue growth from expansion'
          },
          {
            parameterType: 'expense_change',
            changeType: 'percentage',
            changeValue: 10,
            description: 'Controlled expense increase'
          }
        ]
      },
      {
        name: 'Cost Inflation',
        description: 'Impact of significant cost increases',
        parameters: [
          {
            parameterType: 'expense_change',
            changeType: 'percentage',
            changeValue: 25,
            description: 'Inflation-driven cost increases'
          }
        ]
      },
      {
        name: 'Revenue Diversification',
        description: 'New revenue streams with associated costs',
        parameters: [
          {
            parameterType: 'income_change',
            changeType: 'percentage',
            changeValue: 30,
            description: 'New revenue from diversification'
          },
          {
            parameterType: 'expense_change',
            changeType: 'percentage',
            changeValue: 15,
            description: 'Investment in new revenue streams'
          }
        ]
      }
    ];
  }
}

// Export singleton instance
export const scenarioAnalysisService = new ScenarioAnalysisService();

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { BudgetFund, BudgetFundCategory } from '@/models/accounting/BudgetFund';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';

/**
 * Service for managing budget funds that are automatically generated from income/expense flows
 */
class BudgetFundService {
  
  /**
   * Get or create budget fund for a fiscal year
   */
  async getOrCreateBudgetFund(fiscalYear: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      
      // Try to find existing budget fund
      let budgetFund = await BudgetFund.findOne({ 
        fiscalYear,
        status: 'active'
      }).populate('categories');
      
      if (!budgetFund) {
        // Create new budget fund
        const [startYear, endYear] = fiscalYear.split('-');
        
        budgetFund = await BudgetFund.create({
          name: `Teachers Council Budget Fund ${fiscalYear}`,
          description: `Auto-generated budget fund for fiscal year ${fiscalYear}`,
          fiscalYear,
          startDate: new Date(`${startYear}-07-01`), // July 1st
          endDate: new Date(`${endYear}-06-30`),     // June 30th
          status: 'active',
          autoGenerated: true,
          createdBy: new mongoose.Types.ObjectId(userId)
        });
        
        logger.info('Created new budget fund', LogCategory.ACCOUNTING, {
          budgetFundId: budgetFund._id,
          fiscalYear
        });
      }
      
      return budgetFund;
    } catch (error) {
      logger.error('Error getting or creating budget fund', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
  
  /**
   * Handle new or updated income record - MULTI-SAVE OPERATIONS
   * Saves to: Income DB + Budget Totals + Budget Category Items
   */
  async handleIncomeChange(incomeId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();

      const income = await Income.findById(incomeId).populate('budget budgetCategory');
      if (!income) {
        throw new Error(`Income record ${incomeId} not found`);
      }

      // Only process if income is linked to budget and category
      if (!income.budget || !income.budgetCategory || !income.appliedToBudget) {
        logger.info('Income not linked to budget, skipping budget updates', LogCategory.ACCOUNTING, {
          incomeId,
          hasbudget: !!income.budget,
          hasBudgetCategory: !!income.budgetCategory,
          appliedToBudget: income.appliedToBudget
        });
        return;
      }

      // STEP 1: Update Budget Totals based on income status
      await this.updateBudgetTotalsFromIncome(income);

      // STEP 2: Update Budget Category Items
      await this.updateBudgetCategoryItems(income);

      // STEP 3: Update Budget Fund (for comprehensive tracking)
      await this.updateBudgetFundFromIncome(income, userId);

      logger.info('Completed multi-save operations for income', LogCategory.ACCOUNTING, {
        incomeId,
        budgetId: income.budget._id,
        budgetCategoryId: income.budgetCategory._id,
        status: income.status,
        amount: income.amount
      });

    } catch (error) {
      logger.error('Error handling income change', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update Budget totals based on income status
   */
  async updateBudgetTotalsFromIncome(income: any): Promise<void> {
    try {
      const { Budget } = require('@/models/accounting/Budget');

      // Get current budget
      const budget = await Budget.findById(income.budget._id || income.budget);
      if (!budget) return;

      // Calculate totals from all linked income records
      const incomeAggregation = await Income.aggregate([
        {
          $match: {
            budget: budget._id,
            appliedToBudget: true
          }
        },
        {
          $group: {
            _id: '$status',
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Calculate totals by status
      let totalDraftIncome = 0;      // Projected
      let totalApprovedIncome = 0;   // Expected
      let totalReceivedIncome = 0;   // Actual

      incomeAggregation.forEach(group => {
        switch (group._id) {
          case 'draft':
            totalDraftIncome += group.total;
            break;
          case 'approved':
            totalApprovedIncome += group.total;
            break;
          case 'received':
            totalReceivedIncome += group.total;
            break;
        }
      });

      // Update budget totals
      await Budget.findByIdAndUpdate(budget._id, {
        totalIncome: totalDraftIncome + totalApprovedIncome, // All planned income
        totalActualIncome: totalReceivedIncome,              // Only received income
        totalBudgeted: totalDraftIncome + totalApprovedIncome, // Total planned
        lastActualUpdateDate: new Date()
      });

      logger.info('Updated budget totals from income', LogCategory.ACCOUNTING, {
        budgetId: budget._id,
        totalDraftIncome,
        totalApprovedIncome,
        totalReceivedIncome
      });

    } catch (error) {
      logger.error('Error updating budget totals from income', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update Budget Category items with income record
   */
  async updateBudgetCategoryItems(income: any): Promise<void> {
    try {
      const { BudgetCategory } = require('@/models/accounting/Budget');

      const category = await BudgetCategory.findById(income.budgetCategory._id || income.budgetCategory);
      if (!category) return;

      // Remove existing income from category items if it exists
      const existingIndex = category.items.findIndex((item: any) =>
        item.sourceId && item.sourceId.toString() === income._id.toString()
      );

      if (existingIndex > -1) {
        category.items.splice(existingIndex, 1);
      }

      // Add income as category item based on status
      const categoryItem = {
        sourceId: income._id,
        sourceType: 'income',
        description: income.description || `${income.source} income`,
        amount: income.amount,
        status: income.status,
        reference: income.reference,
        date: income.date,
        contributionType: this.getContributionType(income.status)
      };

      category.items.push(categoryItem);

      // Recalculate category totals
      await this.recalculateCategoryTotals(category);
      await category.save();

      logger.info('Updated budget category items', LogCategory.ACCOUNTING, {
        categoryId: category._id,
        incomeId: income._id,
        amount: income.amount,
        status: income.status
      });

    } catch (error) {
      logger.error('Error updating budget category items', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Recalculate category totals from items
   */
  async recalculateCategoryTotals(category: any): Promise<void> {
    let totalDraft = 0;
    let totalApproved = 0;
    let totalReceived = 0;

    category.items.forEach((item: any) => {
      if (item.sourceType === 'income') {
        switch (item.status) {
          case 'draft':
            totalDraft += item.amount;
            break;
          case 'approved':
            totalApproved += item.amount;
            break;
          case 'received':
            totalReceived += item.amount;
            break;
        }
      }
    });

    // Update category totals
    category.total = totalDraft + totalApproved; // All planned
    category.actualAmount = totalReceived;       // Only received
    category.budgetedAmount = totalDraft + totalApproved; // Total planned
  }

  /**
   * Update Budget Fund from income (for comprehensive tracking)
   */
  async updateBudgetFundFromIncome(income: any, userId: string): Promise<void> {
    try {
      // Get or create budget fund
      const budgetFund = await this.getOrCreateBudgetFund(income.fiscalYear, userId);

      // Get or create fund category
      const fundCategory = await this.getOrCreateCategory(
        budgetFund._id,
        income.source,
        'income',
        userId
      );

      // Remove existing source if it exists
      await this.removeIncomeFromCategory(fundCategory, income._id.toString());

      // Add income to appropriate bucket based on status
      await this.addIncomeToCategory(fundCategory, income);

      // Recalculate totals
      await fundCategory.save();
      await budgetFund.recalculateTotals();

      logger.info('Updated budget fund from income', LogCategory.ACCOUNTING, {
        budgetFundId: budgetFund._id,
        incomeId: income._id,
        status: income.status,
        amount: income.amount
      });

    } catch (error) {
      logger.error('Error updating budget fund from income', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
  
  /**
   * Handle new or updated expense record
   */
  async handleExpenseChange(expenseId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      
      const expense = await Expense.findById(expenseId);
      if (!expense) {
        throw new Error(`Expense record ${expenseId} not found`);
      }
      
      // Get or create budget fund
      const budgetFund = await this.getOrCreateBudgetFund(expense.fiscalYear, userId);
      
      // Map expense category to budget fund source
      const source = this.mapExpenseCategoryToSource(expense.category);
      
      // Get or create category for this expense
      const category = await this.getOrCreateCategory(
        budgetFund._id,
        source,
        'expense',
        userId
      );
      
      // Remove existing source if it exists
      await this.removeExpenseFromCategory(category, expenseId);
      
      // Add expense to appropriate bucket based on status
      await this.addExpenseToCategory(category, expense);
      
      // Recalculate category and budget fund totals
      await category.save();
      await budgetFund.recalculateTotals();
      
      logger.info('Updated budget fund with expense change', LogCategory.ACCOUNTING, {
        expenseId,
        budgetFundId: budgetFund._id,
        categoryId: category._id,
        status: expense.status,
        amount: expense.amount
      });
      
    } catch (error) {
      logger.error('Error handling expense change', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
  
  /**
   * Get or create category for budget fund
   */
  private async getOrCreateCategory(
    budgetFundId: string,
    source: string,
    type: 'income' | 'expense',
    userId: string
  ): Promise<any> {
    let category = await BudgetFundCategory.findOne({
      budgetFund: budgetFundId,
      source,
      type
    });
    
    if (!category) {
      const categoryName = this.getCategoryName(source, type);
      
      category = await BudgetFundCategory.create({
        name: categoryName,
        description: `Auto-generated ${type} category for ${source}`,
        type,
        source,
        budgetFund: new mongoose.Types.ObjectId(budgetFundId)
      });
      
      // Add category to budget fund
      await BudgetFund.findByIdAndUpdate(budgetFundId, {
        $addToSet: { categories: category._id }
      });
    }
    
    return category;
  }
  
  /**
   * Add income to category based on status
   */
  private async addIncomeToCategory(category: any, income: any): Promise<void> {
    const source = {
      sourceType: 'income',
      sourceId: income._id,
      sourceReference: income.reference,
      sourceDescription: income.description || `${income.source} income`,
      amount: income.amount,
      status: income.status,
      contributionType: this.getContributionType(income.status),
      dateAdded: new Date(),
      dateUpdated: new Date()
    };
    
    // Add to appropriate bucket based on status
    switch (income.status) {
      case 'draft':
        category.projectedSources.push(source);
        category.projectedAmount += income.amount;
        break;
      case 'approved':
        category.expectedSources.push(source);
        category.expectedAmount += income.amount;
        break;
      case 'received':
        category.actualSources.push(source);
        category.actualAmount += income.amount;
        break;
    }
  }
  
  /**
   * Add expense to category based on status
   */
  private async addExpenseToCategory(category: any, expense: any): Promise<void> {
    const source = {
      sourceType: 'expense',
      sourceId: expense._id,
      sourceReference: expense.reference,
      sourceDescription: expense.description || `${expense.category} expense`,
      amount: expense.amount,
      status: expense.status,
      contributionType: this.getContributionType(expense.status),
      dateAdded: new Date(),
      dateUpdated: new Date()
    };
    
    // Add to appropriate bucket based on status
    switch (expense.status) {
      case 'draft':
        category.projectedSources.push(source);
        category.projectedAmount += expense.amount;
        break;
      case 'approved':
        category.expectedSources.push(source);
        category.expectedAmount += expense.amount;
        break;
      case 'paid':
        category.actualSources.push(source);
        category.actualAmount += expense.amount;
        break;
    }
  }
  
  /**
   * Remove income from category
   */
  private async removeIncomeFromCategory(category: any, incomeId: string): Promise<void> {
    const removeFromArray = (arr: any[]) => {
      const index = arr.findIndex(s => s.sourceId.toString() === incomeId);
      if (index > -1) {
        const removed = arr.splice(index, 1)[0];
        return removed.amount;
      }
      return 0;
    };
    
    category.projectedAmount -= removeFromArray(category.projectedSources);
    category.expectedAmount -= removeFromArray(category.expectedSources);
    category.actualAmount -= removeFromArray(category.actualSources);
    
    // Ensure amounts don't go negative
    category.projectedAmount = Math.max(0, category.projectedAmount);
    category.expectedAmount = Math.max(0, category.expectedAmount);
    category.actualAmount = Math.max(0, category.actualAmount);
  }
  
  /**
   * Remove expense from category
   */
  private async removeExpenseFromCategory(category: any, expenseId: string): Promise<void> {
    const removeFromArray = (arr: any[]) => {
      const index = arr.findIndex(s => s.sourceId.toString() === expenseId);
      if (index > -1) {
        const removed = arr.splice(index, 1)[0];
        return removed.amount;
      }
      return 0;
    };
    
    category.projectedAmount -= removeFromArray(category.projectedSources);
    category.expectedAmount -= removeFromArray(category.expectedSources);
    category.actualAmount -= removeFromArray(category.actualSources);
    
    // Ensure amounts don't go negative
    category.projectedAmount = Math.max(0, category.projectedAmount);
    category.expectedAmount = Math.max(0, category.expectedAmount);
    category.actualAmount = Math.max(0, category.actualAmount);
  }
  
  /**
   * Get contribution type based on status
   */
  private getContributionType(status: string): 'projected' | 'expected' | 'actual' {
    switch (status) {
      case 'draft':
        return 'projected';
      case 'approved':
        return 'expected';
      case 'received':
      case 'paid':
        return 'actual';
      default:
        return 'projected';
    }
  }
  
  /**
   * Map expense category to budget fund source
   */
  private mapExpenseCategoryToSource(expenseCategory: string): string {
    const mapping: { [key: string]: string } = {
      'personnel': 'personnel',
      'salaries': 'personnel',
      'wages': 'personnel',
      'benefits': 'personnel',
      'operations': 'operations',
      'operational': 'operations',
      'utilities': 'operations',
      'supplies': 'operations',
      'maintenance': 'operations'
    };
    
    const normalized = expenseCategory.toLowerCase();
    return mapping[normalized] || 'other';
  }
  
  /**
   * Get category display name
   */
  private getCategoryName(source: string, type: 'income' | 'expense'): string {
    const names: { [key: string]: string } = {
      'government_subvention': 'Government Subvention',
      'registration_fees': 'Registration Fees',
      'licensing_fees': 'Licensing Fees',
      'donations': 'Donations',
      'personnel': 'Personnel Costs',
      'operations': 'Operations',
      'other': type === 'income' ? 'Other Income' : 'Other Expenses'
    };
    
    return names[source] || source.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
  
  /**
   * Get or create active budget for fiscal year (DYNAMIC CREATION)
   */
  async getOrCreateActiveBudget(fiscalYear: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();

      // Import Budget model
      const { Budget } = require('@/models/accounting/Budget');

      // Try to find existing active budget
      let budget = await Budget.findOne({
        fiscalYear,
        status: { $in: ['approved', 'active'] }
      });

      if (!budget) {
        // Create new budget automatically
        const [startYear, endYear] = fiscalYear.split('-');

        budget = await Budget.create({
          name: `Teachers Council Budget ${fiscalYear}`,
          description: `Auto-generated budget for fiscal year ${fiscalYear} based on income flows`,
          fiscalYear,
          startDate: new Date(`${startYear}-07-01`),
          endDate: new Date(`${endYear}-06-30`),
          status: 'approved', // Auto-approve income-driven budgets
          totalIncome: 0,
          totalExpense: 0,
          totalBudgeted: 0,
          totalActualIncome: 0,
          totalActualExpense: 0,
          categories: [],
          createdBy: new mongoose.Types.ObjectId(userId)
        });

        logger.info('Created new active budget from income flow', LogCategory.ACCOUNTING, {
          budgetId: budget._id,
          fiscalYear
        });
      }

      return budget;
    } catch (error) {
      logger.error('Error getting or creating active budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get or create budget category based on income source (DYNAMIC CREATION)
   */
  async getOrCreateBudgetCategory(
    budgetId: string,
    incomeSource: string,
    type: 'income' | 'expense',
    amount: number,
    userId: string
  ): Promise<any> {
    try {
      await connectToDatabase();

      // Import BudgetCategory model
      const { BudgetCategory } = require('@/models/accounting/Budget');

      // Try to find existing category
      let category = await BudgetCategory.findOne({
        budget: budgetId,
        type,
        name: this.getCategoryName(incomeSource, type)
      });

      if (!category) {
        // Create new category automatically
        const categoryName = this.getCategoryName(incomeSource, type);

        category = await BudgetCategory.create({
          name: categoryName,
          description: `Auto-generated ${type} category for ${incomeSource}`,
          type,
          budget: new mongoose.Types.ObjectId(budgetId),
          total: 0,
          budgetedAmount: 0,
          actualAmount: 0
        });

        // Add category to budget
        const { Budget } = require('@/models/accounting/Budget');
        await Budget.findByIdAndUpdate(budgetId, {
          $addToSet: { categories: category._id }
        });

        logger.info('Created new budget category from income flow', LogCategory.ACCOUNTING, {
          categoryId: category._id,
          budgetId,
          incomeSource,
          type
        });
      }

      return category;
    } catch (error) {
      logger.error('Error getting or creating budget category', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Link income to traditional budget system
   */
  async linkIncomeToTraditionalBudget(income: any, budgetId: string, categoryId: string): Promise<void> {
    try {
      // Update income record with budget links
      const Income = require('@/models/accounting/Income').default;
      await Income.findByIdAndUpdate(income._id, {
        budget: budgetId,
        budgetCategory: categoryId,
        appliedToBudget: true
      });

      logger.info('Linked income to traditional budget', LogCategory.ACCOUNTING, {
        incomeId: income._id,
        budgetId,
        categoryId
      });
    } catch (error) {
      logger.error('Error linking income to traditional budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Recalculate budget totals based on linked income/expenses
   */
  async recalculateBudgetTotals(budgetId: string): Promise<void> {
    try {
      await connectToDatabase();

      const { Budget, BudgetCategory } = require('@/models/accounting/Budget');
      const Income = require('@/models/accounting/Income').default;

      // Get budget
      const budget = await Budget.findById(budgetId);
      if (!budget) return;

      // Calculate totals from linked income records
      const incomeAggregation = await Income.aggregate([
        {
          $match: {
            budget: new mongoose.Types.ObjectId(budgetId),
            appliedToBudget: true
          }
        },
        {
          $group: {
            _id: '$status',
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Calculate projected (draft), expected (approved), actual (received)
      let totalProjectedIncome = 0;
      let totalExpectedIncome = 0;
      let totalActualIncome = 0;

      incomeAggregation.forEach(group => {
        switch (group._id) {
          case 'draft':
            totalProjectedIncome += group.total;
            break;
          case 'approved':
            totalExpectedIncome += group.total;
            break;
          case 'received':
            totalActualIncome += group.total;
            break;
        }
      });

      // Update budget totals
      await Budget.findByIdAndUpdate(budgetId, {
        totalIncome: totalProjectedIncome + totalExpectedIncome, // All planned income
        totalActualIncome: totalActualIncome,
        totalBudgeted: totalProjectedIncome + totalExpectedIncome, // Total planned
        lastActualUpdateDate: new Date()
      });

      logger.info('Recalculated budget totals', LogCategory.ACCOUNTING, {
        budgetId,
        totalProjectedIncome,
        totalExpectedIncome,
        totalActualIncome
      });

    } catch (error) {
      logger.error('Error recalculating budget totals', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get budget fund summary for fiscal year
   */
  async getBudgetFundSummary(fiscalYear: string): Promise<any> {
    try {
      await connectToDatabase();

      const budgetFund = await BudgetFund.findOne({
        fiscalYear,
        status: 'active'
      }).populate('categories');

      if (!budgetFund) {
        return null;
      }

      return {
        budgetFund,
        summary: {
          projected: {
            income: budgetFund.totalProjectedIncome,
            expense: budgetFund.totalProjectedExpense,
            net: budgetFund.netProjected
          },
          expected: {
            income: budgetFund.totalExpectedIncome,
            expense: budgetFund.totalExpectedExpense,
            net: budgetFund.netExpected
          },
          actual: {
            income: budgetFund.totalActualIncome,
            expense: budgetFund.totalActualExpense,
            net: budgetFund.netActual
          }
        }
      };
    } catch (error) {
      logger.error('Error getting budget fund summary', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

export const budgetFundService = new BudgetFundService();

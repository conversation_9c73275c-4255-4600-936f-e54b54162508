"use client";

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { formatAmountInWords } from '@/lib/utils/number-to-words';

// Interface for voucher data
interface VoucherData {
  id?: string;
  _id?: string;
  voucherNumber: string;
  voucherType: string;
  date: string | Date;
  description: string;
  totalAmount: number;
  status: string;
  fiscalYear?: string;
  payee?: string;
  paymentMethod?: string;
  reference?: string;
  items?: Array<{
    description: string;
    account: string;
    amount?: number;
    debit?: number;
    credit?: number;
    isDebit?: boolean;
  }>;
  createdBy?: any;
  approvedBy?: any;
  postedBy?: any;
  createdAt: string | Date;
  updatedAt?: string | Date;
  approvedAt?: string | Date;
  postedAt?: string | Date;
}

export class VoucherDownloadService {
  /**
   * Download voucher as PDF with proper formatting and dark text
   */
  static async downloadAsPDF(voucher: VoucherData): Promise<void> {
    try {
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      const pageWidth = pdf.internal.pageSize.width;
      const margin = 15;
      let yPosition = 25;

      // Helper function to format currency
      const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-MW', {
          style: 'currency',
          currency: 'MWK',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(amount);
      };

      // Helper function to format date
      const formatDate = (date: string | Date) => {
        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      };



      // Set text color to very dark (almost black)
      pdf.setTextColor(0, 0, 0);

      // Add actual logo (centered)
      try {
        // Load the logo image from public/images/logo.png
        const logoImg = new Image();
        logoImg.src = '/images/logo.png';

        // Wait for image to load and add to PDF
        await new Promise((resolve) => {
          logoImg.onload = () => {
            try {
              const logoSize = 25;
              const logoX = (pageWidth - logoSize) / 2;

              // Create canvas to convert image to data URL
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              canvas.width = logoImg.width;
              canvas.height = logoImg.height;
              ctx?.drawImage(logoImg, 0, 0);

              // Add image to PDF
              pdf.addImage(canvas.toDataURL('image/png'), 'PNG', logoX, yPosition - 5, logoSize, logoSize);
              resolve(true);
            } catch (error) {
              console.warn('Failed to add logo image, using fallback');
              // Fallback to text logo
              pdf.setFontSize(12);
              pdf.setFont('helvetica', 'bold');
              pdf.text('TCM', pageWidth / 2, yPosition + 5, { align: 'center' });
              resolve(true);
            }
          };

          logoImg.onerror = () => {
            console.warn('Logo image not found, using fallback');
            // Fallback to text logo
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'bold');
            pdf.text('TCM', pageWidth / 2, yPosition + 5, { align: 'center' });
            resolve(true);
          };
        });
      } catch (error) {
        console.warn('Error loading logo, using fallback:', error);
        // Fallback to text logo
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('TCM', pageWidth / 2, yPosition + 5, { align: 'center' });
      }

      yPosition += 30;

      // Header - Company Name
      pdf.setFontSize(22);
      pdf.setFont('helvetica', 'bold');
      pdf.text('TEACHERS COUNCIL OF MALAWI', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 8;

      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'normal');
      pdf.text('Professional Development & Regulation', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 15;

      // Title
      pdf.setFontSize(20);
      pdf.setFont('helvetica', 'bold');
      pdf.text('PAYMENT VOUCHER', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 15;

      // Voucher Details Section
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('VOUCHER DETAILS', margin, yPosition);
      yPosition += 8;

      // Draw a line under the section header
      pdf.setLineWidth(0.5);
      pdf.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
      yPosition += 5;

      // Two-column layout for voucher details
      const leftColumn = margin;
      const rightColumn = pageWidth / 2 + 10;
      const lineHeight = 7;

      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(11);

      // Left column
      pdf.text('Voucher Number:', leftColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(voucher.voucherNumber, leftColumn + 35, yPosition);

      // Right column
      pdf.setFont('helvetica', 'bold');
      pdf.text('Date:', rightColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(formatDate(voucher.date), rightColumn + 15, yPosition);
      yPosition += lineHeight;

      // Fiscal Year and Status
      pdf.setFont('helvetica', 'bold');
      pdf.text('Fiscal Year:', leftColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(voucher.fiscalYear || 'N/A', leftColumn + 35, yPosition);

      pdf.setFont('helvetica', 'bold');
      pdf.text('Status:', rightColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(voucher.status.toUpperCase(), rightColumn + 15, yPosition);
      yPosition += lineHeight;

      // Payee and Payment Method
      pdf.setFont('helvetica', 'bold');
      pdf.text('Payee:', leftColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(voucher.payee || 'N/A', leftColumn + 35, yPosition);

      pdf.setFont('helvetica', 'bold');
      pdf.text('Payment Method:', rightColumn, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(voucher.paymentMethod || 'N/A', rightColumn + 35, yPosition);
      yPosition += lineHeight + 3;

      // Description
      pdf.setFont('helvetica', 'bold');
      pdf.text('Description:', leftColumn, yPosition);
      yPosition += 5;
      pdf.setFont('helvetica', 'normal');

      // Handle long descriptions with text wrapping
      const descriptionLines = pdf.splitTextToSize(voucher.description, pageWidth - 2 * margin);
      pdf.text(descriptionLines, leftColumn, yPosition);
      yPosition += descriptionLines.length * 5 + 10;

      // Total Amount (highlighted)
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.text('TOTAL AMOUNT:', leftColumn, yPosition);
      pdf.text(formatCurrency(voucher.totalAmount), rightColumn, yPosition);
      yPosition += 10;

      // Amount in Words
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'italic');
      pdf.text('Amount in Words:', leftColumn, yPosition);
      yPosition += 5;

      const amountInWords = formatAmountInWords(voucher.totalAmount);
      const amountWordsLines = pdf.splitTextToSize(amountInWords, pageWidth - 2 * margin);
      pdf.setFont('helvetica', 'normal');
      pdf.text(amountWordsLines, leftColumn, yPosition);
      yPosition += amountWordsLines.length * 5 + 15;

      // Items section (if items exist)
      if (voucher.items && voucher.items.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('VOUCHER ITEMS', margin, yPosition);
        yPosition += 8;

        // Draw line
        pdf.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
        yPosition += 5;

        // Table headers
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'bold');
        const colWidths = [10, 80, 50, 25, 25];
        let xPos = margin;

        pdf.text('#', xPos, yPosition);
        xPos += colWidths[0];
        pdf.text('Description', xPos, yPosition);
        xPos += colWidths[1];
        pdf.text('Account', xPos, yPosition);
        xPos += colWidths[2];
        pdf.text('Debit', xPos, yPosition);
        xPos += colWidths[3];
        pdf.text('Credit', xPos, yPosition);
        yPosition += 7;

        // Draw header line
        pdf.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
        yPosition += 3;

        // Table rows
        pdf.setFont('helvetica', 'normal');
        voucher.items.forEach((item, index) => {
          xPos = margin;
          const debit = item.debit || (item.isDebit ? item.amount : 0) || 0;
          const credit = item.credit || (!item.isDebit ? item.amount : 0) || 0;

          pdf.text((index + 1).toString(), xPos, yPosition);
          xPos += colWidths[0];

          // Truncate long descriptions
          const description = item.description.length > 45 ?
            item.description.substring(0, 42) + '...' : item.description;
          pdf.text(description, xPos, yPosition);
          xPos += colWidths[1];

          pdf.text(item.account, xPos, yPosition);
          xPos += colWidths[2];

          pdf.text(debit > 0 ? formatCurrency(debit) : '-', xPos, yPosition);
          xPos += colWidths[3];

          pdf.text(credit > 0 ? formatCurrency(credit) : '-', xPos, yPosition);

          yPosition += 6;
        });

        yPosition += 5;
      }

      // SIGNATURES SECTION - Clean line-based design with proper spacing
      yPosition += 30; // Generous space before signatures
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('SIGNATURES', margin, yPosition);
      yPosition += 10;

      pdf.setLineWidth(0.5);
      pdf.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
      yPosition += 20;

      // Four signature lines in a row with proper spacing
      const signatureWidth = (pageWidth - 2 * margin - 45) / 4; // 4 signatures with more spacing
      const signatureSpacing = 15; // Increased spacing between signatures
      const lineLength = signatureWidth - 5; // Line length slightly smaller than allocated width

      const signatures = [
        { title: 'Prepared By', subtitle: 'Finance Department' },
        { title: 'Checked By', subtitle: 'Accountant' },
        { title: 'Approved By', subtitle: 'Finance Manager' },
        { title: 'Received By', subtitle: 'Payee Signature' }
      ];

      signatures.forEach((sig, index) => {
        const xPos = margin + (signatureWidth + signatureSpacing) * index;

        // Draw signature line
        pdf.setLineWidth(0.8);
        pdf.line(xPos, yPosition, xPos + lineLength, yPosition);

        // Add signature labels below the line
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'bold');
        pdf.text(sig.title, xPos + lineLength / 2, yPosition + 8, { align: 'center' });

        pdf.setFontSize(8);
        pdf.setFont('helvetica', 'normal');
        pdf.text(sig.subtitle, xPos + lineLength / 2, yPosition + 15, { align: 'center' });
      });

      // End of Signatures Section with generous spacing
      yPosition += 35;

      // Download the PDF
      const fileName = `${voucher.voucherNumber}_voucher.pdf`;
      pdf.save(fileName);

      return Promise.resolve();
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    }
  }

  /**
   * Download voucher as Excel with accurate data
   */
  static downloadAsExcel(voucher: VoucherData): void {
    try {
      // Create workbook
      const wb = XLSX.utils.book_new();

      // Helper function to format date
      const formatDate = (date: string | Date) => {
        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      };

      // Helper function to format currency
      const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-MW', {
          style: 'currency',
          currency: 'MWK',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(amount);
      };

      // Helper function to get user name
      const getUserName = (user: any) => {
        if (!user) return 'N/A';
        if (typeof user === 'string') return user;
        if (user.firstName || user.lastName) {
          return `${user.firstName || ''} ${user.lastName || ''}`.trim();
        }
        return user.email || 'N/A';
      };

      // Voucher Summary Sheet
      const summaryData = [
        ['[TCM LOGO - /images/logo.png]'],
        ['TEACHERS COUNCIL OF MALAWI'],
        ['Professional Development & Regulation'],
        ['PAYMENT VOUCHER'],
        [''],
        ['Voucher Information'],
        ['Voucher Number', voucher.voucherNumber],
        ['Voucher Type', voucher.voucherType],
        ['Date', formatDate(voucher.date)],
        ['Fiscal Year', voucher.fiscalYear || 'N/A'],
        ['Status', voucher.status.toUpperCase()],
        [''],
        ['Payment Details'],
        ['Payee', voucher.payee || 'N/A'],
        ['Payment Method', voucher.paymentMethod || 'N/A'],
        ['Reference', voucher.reference || 'N/A'],
        ['Description', voucher.description],
        ['Total Amount', formatCurrency(voucher.totalAmount)],
        ['Amount in Words', formatAmountInWords(voucher.totalAmount)],
        [''],
        ['Signatures Required'],
        ['Prepared By', 'Finance Department'],
        ['Checked By', 'Accountant'],
        ['Approved By', 'Finance Manager'],
        ['Received By', 'Payee Signature'],
        [''],
        ['Audit Trail'],
        ['Created By', getUserName(voucher.createdBy)],
        ['Created At', formatDate(voucher.createdAt)],
        ['Approved By', getUserName(voucher.approvedBy)],
        ['Approved At', voucher.approvedAt ? formatDate(voucher.approvedAt) : 'Pending'],
        ['Posted By', getUserName(voucher.postedBy)],
        ['Posted At', voucher.postedAt ? formatDate(voucher.postedAt) : 'Pending'],
        ['Last Updated', voucher.updatedAt ? formatDate(voucher.updatedAt) : 'N/A'],
        ['Document ID', voucher._id || voucher.id || 'N/A'],
        [''],
        ['Organization Information'],
        ['Address', 'P.O. Box 30579, Lilongwe 3, Malawi'],
        ['Phone', '+265 999 638 224/*********'],
        ['Email', '<EMAIL>'],
        ['Website', 'www.teacherscouncil.mw'],
        ['Generated On', new Date().toLocaleString()],
      ];

      // Create summary worksheet
      const summaryWS = XLSX.utils.aoa_to_sheet(summaryData);
      
      // Set column widths for summary sheet
      summaryWS['!cols'] = [
        { wch: 20 }, // Column A
        { wch: 40 }  // Column B
      ];

      XLSX.utils.book_append_sheet(wb, summaryWS, 'Voucher Summary');

      // Voucher Items Sheet (if items exist)
      if (voucher.items && voucher.items.length > 0) {
        const itemsHeaders = ['Item #', 'Description', 'Account', 'Debit', 'Credit', 'Amount'];
        const itemsData = [itemsHeaders];

        voucher.items.forEach((item, index) => {
          const debit = item.debit || (item.isDebit ? item.amount : 0) || 0;
          const credit = item.credit || (!item.isDebit ? item.amount : 0) || 0;
          const amount = item.amount || debit || credit || 0;

          itemsData.push([
            (index + 1).toString(),
            item.description,
            item.account,
            debit > 0 ? formatCurrency(debit) : '-',
            credit > 0 ? formatCurrency(credit) : '-',
            formatCurrency(amount)
          ]);
        });

        // Add totals row
        const totalDebit = voucher.items.reduce((sum, item) => {
          return sum + (item.debit || (item.isDebit ? item.amount : 0) || 0);
        }, 0);

        const totalCredit = voucher.items.reduce((sum, item) => {
          return sum + (item.credit || (!item.isDebit ? item.amount : 0) || 0);
        }, 0);

        itemsData.push([
          '',
          '',
          'TOTAL',
          formatCurrency(totalDebit),
          formatCurrency(totalCredit),
          formatCurrency(voucher.totalAmount)
        ]);

        const itemsWS = XLSX.utils.aoa_to_sheet(itemsData);
        
        // Set column widths for items sheet
        itemsWS['!cols'] = [
          { wch: 8 },  // Item #
          { wch: 40 }, // Description
          { wch: 25 }, // Account
          { wch: 15 }, // Debit
          { wch: 15 }, // Credit
          { wch: 15 }  // Amount
        ];

        XLSX.utils.book_append_sheet(wb, itemsWS, 'Voucher Items');
      }

      // Financial Summary Sheet
      const financialData = [
        ['FINANCIAL SUMMARY'],
        [''],
        ['Voucher Number', voucher.voucherNumber],
        ['Total Amount', formatCurrency(voucher.totalAmount)],
        ['Status', voucher.status.toUpperCase()],
        [''],
        ['Breakdown by Type'],
      ];

      if (voucher.items && voucher.items.length > 0) {
        const totalDebits = voucher.items.reduce((sum, item) => {
          return sum + (item.debit || (item.isDebit ? item.amount : 0) || 0);
        }, 0);

        const totalCredits = voucher.items.reduce((sum, item) => {
          return sum + (item.credit || (!item.isDebit ? item.amount : 0) || 0);
        }, 0);

        financialData.push(
          ['Total Debits', formatCurrency(totalDebits)],
          ['Total Credits', formatCurrency(totalCredits)],
          ['Net Amount', formatCurrency(totalDebits - totalCredits)]
        );
      }

      const financialWS = XLSX.utils.aoa_to_sheet(financialData);
      financialWS['!cols'] = [{ wch: 20 }, { wch: 20 }];
      XLSX.utils.book_append_sheet(wb, financialWS, 'Financial Summary');

      // Save the Excel file
      const fileName = `${voucher.voucherNumber}_voucher.xlsx`;
      XLSX.writeFile(wb, fileName);

    } catch (error) {
      console.error('Error generating Excel:', error);
      throw new Error('Failed to generate Excel file');
    }
  }

  /**
   * Download voucher data as JSON (for debugging/backup)
   */
  static downloadAsJSON(voucher: VoucherData): void {
    try {
      const dataStr = JSON.stringify(voucher, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `${voucher.voucherNumber}_voucher.json`;
      link.click();
      
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error generating JSON:', error);
      throw new Error('Failed to generate JSON file');
    }
  }
}

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { CrudService } from '@/lib/backend/services/base/CrudService';
import { Voucher, IVoucher } from '@/models/accounting/Voucher';
import { PdfGenerator } from '@/lib/backend/utils/pdf-generator';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

/**
 * Interface for voucher creation data
 */
export interface CreateVoucherRequest {
  voucherType: 'payment' | 'receipt' | 'journal';
  date: Date;
  reference?: string;
  description: string;
  totalAmount: number;
  fiscalYear: string;
  payee?: string;
  paymentMethod?: string;
  notes?: string;
  items: {
    description: string;
    account: string;
    debit: number;
    credit: number;
  }[];
  // New fields for payroll integration
  payrollRunId?: string;
  voucherCategory?: 'payroll' | 'general' | 'procurement' | 'expense';
  isAutoGenerated?: boolean;
  sourceModule?: 'payroll' | 'procurement' | 'manual';
}

/**
 * Interface for voucher approval
 */
export interface VoucherApprovalRequest {
  voucherId: string;
  approverId: string;
  action: 'approve' | 'reject';
  comments?: string;
  level: number;
}

/**
 * Interface for export options
 */
export interface VoucherExportOptions {
  format: 'pdf' | 'excel';
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  voucherType?: 'payment' | 'receipt' | 'journal';
  status?: string;
  includeItems?: boolean;
}

/**
 * Service for managing vouchers with enhanced features
 */
export class VoucherService extends CrudService<IVoucher> {
  constructor() {
    super(Voucher, 'Voucher');
  }

  /**
   * Create a new voucher
   * @param request - Voucher creation request
   * @param userId - User ID creating the voucher
   * @returns Created voucher
   */
  async createVoucher(request: CreateVoucherRequest, userId: string): Promise<IVoucher> {
    try {
      await connectToDatabase();
      logger.info('Creating voucher', LogCategory.ACCOUNTING, { request, userId });

      // Generate voucher number
      const voucherNumber = await this.generateVoucherNumber(request.voucherType);

      // Validate double-entry bookkeeping: total debits should equal total credits
      const totalDebits = request.items.reduce((sum, item) => sum + item.debit, 0);
      const totalCredits = request.items.reduce((sum, item) => sum + item.credit, 0);

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        throw new Error(`Double-entry validation failed: Total debits (${totalDebits}) must equal total credits (${totalCredits})`);
      }

      // For vouchers, total amount should typically represent the main transaction amount
      // For payment vouchers, this is usually the total debits (or credits, since they're equal)
      if (Math.abs(totalDebits - request.totalAmount) > 0.01) {
        throw new Error(`Total amount (${request.totalAmount}) must match total debits/credits (${totalDebits})`);
      }

      // Create voucher data
      const voucherData = {
        voucherNumber,
        voucherType: request.voucherType,
        date: request.date,
        reference: request.reference,
        description: request.description,
        totalAmount: request.totalAmount,
        status: 'draft' as const,
        fiscalYear: request.fiscalYear,
        payee: request.payee,
        paymentMethod: request.paymentMethod,
        notes: request.notes,
        // New fields
        payrollRunId: request.payrollRunId ? new mongoose.Types.ObjectId(request.payrollRunId) : undefined,
        voucherCategory: request.voucherCategory || 'general',
        isAutoGenerated: request.isAutoGenerated || false,
        sourceModule: request.sourceModule || 'manual',
        createdBy: new mongoose.Types.ObjectId(userId),
        updatedBy: new mongoose.Types.ObjectId(userId)
      };

      // Create voucher
      const voucher = await this.create(voucherData);

      // Initialize approval workflow if needed
      if (request.voucherCategory === 'payroll' || request.totalAmount > 1000000) {
        await this.initializeApprovalWorkflow(voucher._id.toString(), request.voucherCategory, request.totalAmount);
      }

      logger.info('Voucher created successfully', LogCategory.ACCOUNTING, { voucherId: voucher._id });
      return voucher;

    } catch (error) {
      logger.error('Error creating voucher', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Initialize approval workflow for a voucher
   * @param voucherId - Voucher ID
   * @param category - Voucher category
   * @param amount - Voucher amount
   */
  async initializeApprovalWorkflow(voucherId: string, category: string, amount: number): Promise<void> {
    try {
      await connectToDatabase();

      const requiredApprovers = [];

      if (category === 'payroll') {
        // Payroll voucher approval chain
        requiredApprovers.push(
          { level: 1, role: 'HR_MANAGER', amountThreshold: 0 },
          { level: 2, role: 'FINANCE_MANAGER', amountThreshold: 0 }
        );

        // Add admin approval for large amounts
        if (amount > 5000000) {
          requiredApprovers.push({ level: 3, role: 'SUPER_ADMIN', amountThreshold: 5000000 });
        }
      } else {
        // Standard approval workflow
        if (amount > 1000000) {
          requiredApprovers.push({ level: 1, role: 'FINANCE_MANAGER', amountThreshold: 1000000 });
        }
        if (amount > 5000000) {
          requiredApprovers.push({ level: 2, role: 'SUPER_ADMIN', amountThreshold: 5000000 });
        }
      }

      // Update voucher with approval workflow
      await Voucher.findByIdAndUpdate(voucherId, {
        $set: {
          'approvalWorkflow.workflowType': category === 'payroll' ? 'payroll' : 'standard',
          'approvalWorkflow.requiredApprovers': requiredApprovers,
          'approvalWorkflow.currentLevel': 1,
          'approvalWorkflow.approvalHistory': [],
          status: requiredApprovers.length > 0 ? 'pending_approval' : 'approved'
        }
      });

      logger.info('Approval workflow initialized', LogCategory.ACCOUNTING, { voucherId, requiredApprovers });

    } catch (error) {
      logger.error('Error initializing approval workflow', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Generate voucher number
   * @param voucherType - Type of voucher
   * @returns Generated voucher number
   */
  private async generateVoucherNumber(voucherType: 'payment' | 'receipt' | 'journal'): Promise<string> {
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;
    
    let prefix = '';
    switch (voucherType) {
      case 'payment':
        prefix = 'PV';
        break;
      case 'receipt':
        prefix = 'RV';
        break;
      case 'journal':
        prefix = 'JV';
        break;
    }

    // Get count of vouchers for this type and year
    const count = await Voucher.countDocuments({
      voucherType,
      createdAt: {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1)
      }
    });

    const sequence = (count + 1).toString().padStart(4, '0');
    return `${prefix}-${year}${month.toString().padStart(2, '0')}-${sequence}`;
  }

  /**
   * Get vouchers with filters and pagination
   * @param filters - Filter options
   * @param pagination - Pagination options
   * @returns Vouchers with pagination info
   */
  async getVouchers(
    filters: {
      voucherType?: string;
      status?: string;
      dateRange?: { startDate: Date; endDate: Date };
      payrollRunId?: string;
      voucherCategory?: string;
    } = {},
    pagination: { page: number; limit: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } = { page: 1, limit: 20 }
  ) {
    try {
      await connectToDatabase();

      // Build query
      const query: any = {};

      if (filters.voucherType) {
        query.voucherType = filters.voucherType;
      }

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.voucherCategory) {
        query.voucherCategory = filters.voucherCategory;
      }

      if (filters.payrollRunId) {
        query.payrollRunId = new mongoose.Types.ObjectId(filters.payrollRunId);
      }

      if (filters.dateRange) {
        query.date = {
          $gte: filters.dateRange.startDate,
          $lte: filters.dateRange.endDate
        };
      }

      // Build sort
      const sort: { [key: string]: 1 | -1 } = {};
      const sortBy = pagination.sortBy || 'createdAt';
      const sortOrder = pagination.sortOrder === 'desc' ? -1 : 1;
      sort[sortBy] = sortOrder;

      // Get total count
      const totalCount = await Voucher.countDocuments(query);
      const totalPages = Math.ceil(totalCount / pagination.limit);

      // Get vouchers
      const vouchers = await Voucher.find(query)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email')
        .populate('approvedBy', 'name email')
        .populate('payrollRunId', 'name payPeriod status')
        .sort(sort)
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit)
        .lean();

      return {
        vouchers,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          totalCount,
          totalPages,
          hasNext: pagination.page < totalPages,
          hasPrev: pagination.page > 1
        }
      };

    } catch (error) {
      logger.error('Error getting vouchers', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get voucher by ID with full details
   * @param voucherId - Voucher ID
   * @returns Voucher with full details
   */
  async getVoucherById(voucherId: string): Promise<IVoucher | null> {
    try {
      await connectToDatabase();

      const voucher = await Voucher.findById(voucherId)
        .populate('createdBy', 'name email role')
        .populate('updatedBy', 'name email role')
        .populate('approvedBy', 'name email role')
        .populate('payrollRunId', 'name payPeriod status totalNetSalary')
        .populate('approvalWorkflow.currentApprover', 'name email role')
        .populate('approvalWorkflow.approvalHistory.approver', 'name email role')
        .lean();

      return voucher;

    } catch (error) {
      logger.error('Error getting voucher by ID', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Export vouchers to PDF
   * @param options - Export options
   * @returns PDF buffer
   */
  async exportToPdf(options: VoucherExportOptions): Promise<Buffer> {
    try {
      await connectToDatabase();
      logger.info('Exporting vouchers to PDF', LogCategory.ACCOUNTING, options);

      // Get vouchers based on filters
      const filters: any = {};
      if (options.voucherType) filters.voucherType = options.voucherType;
      if (options.status) filters.status = options.status;
      if (options.dateRange) filters.dateRange = options.dateRange;

      const { vouchers } = await this.getVouchers(filters, { page: 1, limit: 1000 });

      // Create PDF document
      const pdfGenerator = new PdfGenerator({
        title: 'Vouchers Report',
        author: 'Teachers Council of Malawi',
        subject: 'Vouchers Export',
        keywords: ['vouchers', 'accounting', 'report'],
        pageSize: 'A4',
        pageOrientation: 'landscape',
        margins: { top: 50, bottom: 50, left: 40, right: 40 }
      });

      // Add header
      pdfGenerator.addHeader({
        title: 'TEACHERS COUNCIL OF MALAWI',
        subtitle: 'Vouchers Report',
        showLogo: true
      });

      // Add summary information
      const dateRangeText = options.dateRange
        ? `From ${format(options.dateRange.startDate, 'dd/MM/yyyy')} to ${format(options.dateRange.endDate, 'dd/MM/yyyy')}`
        : 'All dates';

      pdfGenerator.addText(`Report Period: ${dateRangeText}`);
      pdfGenerator.addText(`Voucher Type: ${options.voucherType || 'All types'}`);
      pdfGenerator.addText(`Status: ${options.status || 'All statuses'}`);
      pdfGenerator.addText(`Total Vouchers: ${vouchers.length}`);
      pdfGenerator.addText(''); // Empty line

      // Add vouchers table
      const tableHeaders = [
        'Voucher #',
        'Type',
        'Date',
        'Description',
        'Amount (MWK)',
        'Status',
        'Payee'
      ];

      const tableData = vouchers.map(voucher => [
        voucher.voucherNumber,
        voucher.voucherType.toUpperCase(),
        format(new Date(voucher.date), 'dd/MM/yyyy'),
        voucher.description.substring(0, 30) + (voucher.description.length > 30 ? '...' : ''),
        new Intl.NumberFormat('en-MW', {
          style: 'currency',
          currency: 'MWK',
          minimumFractionDigits: 2
        }).format(voucher.totalAmount),
        voucher.status.replace('_', ' ').toUpperCase(),
        voucher.payee || 'N/A'
      ]);

      pdfGenerator.addTable(tableHeaders, tableData, {
        headerBackgroundColor: '#f0f0f0',
        alternateRowColor: '#f9f9f9',
        fontSize: 8
      });

      // Add total amount
      const totalAmount = vouchers.reduce((sum, voucher) => sum + voucher.totalAmount, 0);
      pdfGenerator.addText(''); // Empty line
      pdfGenerator.addText(`Total Amount: ${new Intl.NumberFormat('en-MW', {
        style: 'currency',
        currency: 'MWK',
        minimumFractionDigits: 2
      }).format(totalAmount)}`, { align: 'right' });

      // Generate and return PDF buffer
      const buffer = await pdfGenerator.generate();
      logger.info('PDF export completed', LogCategory.ACCOUNTING, { vouchersCount: vouchers.length });

      return buffer;

    } catch (error) {
      logger.error('Error exporting vouchers to PDF', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Export vouchers to Excel
   * @param options - Export options
   * @returns Excel buffer
   */
  async exportToExcel(options: VoucherExportOptions): Promise<Buffer> {
    try {
      await connectToDatabase();
      logger.info('Exporting vouchers to Excel', LogCategory.ACCOUNTING, options);

      // Get vouchers based on filters
      const filters: any = {};
      if (options.voucherType) filters.voucherType = options.voucherType;
      if (options.status) filters.status = options.status;
      if (options.dateRange) filters.dateRange = options.dateRange;

      const { vouchers } = await this.getVouchers(filters, { page: 1, limit: 10000 });

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Prepare vouchers data
      const vouchersData = vouchers.map(voucher => ({
        'Voucher Number': voucher.voucherNumber,
        'Type': voucher.voucherType.toUpperCase(),
        'Date': format(new Date(voucher.date), 'dd/MM/yyyy'),
        'Description': voucher.description,
        'Amount (MWK)': voucher.totalAmount,
        'Status': voucher.status.replace('_', ' ').toUpperCase(),
        'Payee': voucher.payee || '',
        'Payment Method': voucher.paymentMethod || '',
        'Reference': voucher.reference || '',
        'Fiscal Year': voucher.fiscalYear,
        'Category': voucher.voucherCategory || 'general',
        'Source Module': voucher.sourceModule || 'manual',
        'Created By': voucher.createdBy?.name || '',
        'Created Date': format(new Date(voucher.createdAt), 'dd/MM/yyyy HH:mm'),
        'Notes': voucher.notes || ''
      }));

      // Create vouchers worksheet
      const vouchersWorksheet = XLSX.utils.json_to_sheet(vouchersData);
      XLSX.utils.book_append_sheet(workbook, vouchersWorksheet, 'Vouchers');

      // If including items, create items worksheet
      if (options.includeItems) {
        const itemsData: any[] = [];

        for (const voucher of vouchers) {
          // Note: In a real implementation, you would fetch voucher items from a separate collection
          // For now, we'll create a placeholder structure
          itemsData.push({
            'Voucher Number': voucher.voucherNumber,
            'Item Description': 'Voucher items would be listed here',
            'Account': 'Account details',
            'Debit': 0,
            'Credit': voucher.totalAmount
          });
        }

        const itemsWorksheet = XLSX.utils.json_to_sheet(itemsData);
        XLSX.utils.book_append_sheet(workbook, itemsWorksheet, 'Voucher Items');
      }

      // Create summary worksheet
      const summary = {
        'Total Vouchers': vouchers.length,
        'Total Amount (MWK)': vouchers.reduce((sum, v) => sum + v.totalAmount, 0),
        'Payment Vouchers': vouchers.filter(v => v.voucherType === 'payment').length,
        'Receipt Vouchers': vouchers.filter(v => v.voucherType === 'receipt').length,
        'Journal Vouchers': vouchers.filter(v => v.voucherType === 'journal').length,
        'Draft Status': vouchers.filter(v => v.status === 'draft').length,
        'Pending Approval': vouchers.filter(v => v.status === 'pending_approval').length,
        'Approved': vouchers.filter(v => v.status === 'approved').length,
        'Posted': vouchers.filter(v => v.status === 'posted').length,
        'Export Date': format(new Date(), 'dd/MM/yyyy HH:mm')
      };

      const summaryWorksheet = XLSX.utils.json_to_sheet([summary]);
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');

      // Write workbook to buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      logger.info('Excel export completed', LogCategory.ACCOUNTING, { vouchersCount: vouchers.length });
      return buffer;

    } catch (error) {
      logger.error('Error exporting vouchers to Excel', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

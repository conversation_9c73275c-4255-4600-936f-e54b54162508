import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import mongoose from 'mongoose';

// Comprehensive type definitions for forecasting
export interface TimeSeriesDataPoint {
  date: Date;
  value: number;
  source?: string;
  category?: string;
}

export interface ForecastDataPoint {
  period: string;
  actual?: number;
  forecasted: number;
  lowerBound: number;
  upperBound: number;
  confidence: number;
  isAnomaly: boolean;
  seasonalFactor?: number;
}

export interface SeasonalDecomposition {
  trend: number[];
  seasonal: number[];
  residual: number[];
  seasonalFactors: number[];
  seasonLength: number;
}

export interface ForecastModel {
  type: 'linear' | 'exponential' | 'seasonal' | 'arima';
  parameters: Record<string, number>;
  accuracy: {
    mape: number; // Mean Absolute Percentage Error
    rmse: number; // Root Mean Square Error
    mae: number;  // Mean Absolute Error
    r2: number;   // R-squared
  };
  confidence: number;
}

export interface ScenarioForecast {
  scenario: 'optimistic' | 'realistic' | 'pessimistic';
  description: string;
  adjustmentFactor: number;
  forecast: ForecastDataPoint[];
  totalProjected: number;
  budgetImpact: {
    variance: number;
    utilizationRate: number;
    riskLevel: 'low' | 'medium' | 'high';
  };
}

export interface AdvancedForecastResult {
  historicalData: TimeSeriesDataPoint[];
  forecast: ForecastDataPoint[];
  model: ForecastModel;
  scenarios: ScenarioForecast[];
  seasonalDecomposition?: SeasonalDecomposition;
  trends: {
    shortTerm: 'increasing' | 'decreasing' | 'stable';
    longTerm: 'increasing' | 'decreasing' | 'stable';
    volatility: 'low' | 'medium' | 'high';
    cyclical: boolean;
  };
  recommendations: Array<{
    type: 'budget_adjustment' | 'risk_mitigation' | 'opportunity' | 'alert';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: number;
    actionRequired: boolean;
  }>;
  metadata: {
    generatedAt: Date;
    dataPoints: number;
    forecastHorizon: number;
    lastUpdated: Date;
    modelVersion: string;
  };
}

export interface ForecastOptions {
  forecastHorizon: number; // Number of periods to forecast
  confidenceLevel: number; // 0.90, 0.95, 0.99
  includeSeasonality: boolean;
  detectAnomalies: boolean;
  anomalyThreshold: number;
  includeScenarios: boolean;
  modelType?: 'auto' | 'linear' | 'exponential' | 'seasonal';
  minDataPoints?: number;
}

/**
 * Advanced Income Forecasting Service
 * Provides sophisticated forecasting capabilities with multiple models and scenarios
 */
export class AdvancedForecastingService {
  
  private static readonly DEFAULT_OPTIONS: ForecastOptions = {
    forecastHorizon: 6,
    confidenceLevel: 0.95,
    includeSeasonality: true,
    detectAnomalies: true,
    anomalyThreshold: 2.5,
    includeScenarios: true,
    modelType: 'auto',
    minDataPoints: 6
  };

  /**
   * Generate advanced income forecast
   */
  static async generateIncomeForecast(
    fiscalYear: string,
    budgetId?: string,
    categoryId?: string,
    options: Partial<ForecastOptions> = {}
  ): Promise<AdvancedForecastResult> {
    try {
      await connectToDatabase();
      
      const opts = { ...this.DEFAULT_OPTIONS, ...options };
      
      // Fetch historical income data
      const historicalData = await this.fetchHistoricalData(fiscalYear, budgetId, categoryId);
      
      if (historicalData.length < opts.minDataPoints!) {
        throw new Error(`Insufficient data for forecasting. Need at least ${opts.minDataPoints} data points, got ${historicalData.length}`);
      }

      // Prepare time series data
      const timeSeriesValues = historicalData.map(d => d.value);
      
      // Detect and handle anomalies
      const anomalies = opts.detectAnomalies 
        ? this.detectAnomalies(timeSeriesValues, opts.anomalyThreshold)
        : [];
      
      // Clean data for modeling (remove anomalies)
      const cleanedData = this.cleanDataForModeling(timeSeriesValues, anomalies);
      
      // Seasonal decomposition if requested
      let seasonalDecomposition: SeasonalDecomposition | undefined;
      if (opts.includeSeasonality && cleanedData.length >= 12) {
        seasonalDecomposition = this.performSeasonalDecomposition(cleanedData, 12);
      }
      
      // Select best forecasting model
      const model = await this.selectBestModel(cleanedData, opts, seasonalDecomposition);
      
      // Generate base forecast
      const forecast = this.generateBaseForecast(
        cleanedData,
        model,
        opts.forecastHorizon,
        opts.confidenceLevel,
        seasonalDecomposition
      );
      
      // Generate scenario forecasts
      const scenarios = opts.includeScenarios 
        ? await this.generateScenarioForecasts(forecast, budgetId, categoryId)
        : [];
      
      // Analyze trends
      const trends = this.analyzeTrends(timeSeriesValues, seasonalDecomposition);
      
      // Generate recommendations
      const recommendations = await this.generateRecommendations(
        forecast,
        scenarios,
        trends,
        budgetId,
        categoryId
      );
      
      const result: AdvancedForecastResult = {
        historicalData,
        forecast,
        model,
        scenarios,
        seasonalDecomposition,
        trends,
        recommendations,
        metadata: {
          generatedAt: new Date(),
          dataPoints: historicalData.length,
          forecastHorizon: opts.forecastHorizon,
          lastUpdated: new Date(),
          modelVersion: '1.0.0'
        }
      };

      logger.info('Advanced forecast generated successfully', LogCategory.ACCOUNTING, {
        fiscalYear,
        budgetId,
        categoryId,
        dataPoints: historicalData.length,
        forecastHorizon: opts.forecastHorizon,
        modelType: model.type
      });

      return result;
    } catch (error) {
      logger.error('Error generating advanced forecast', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Fetch historical income data from database
   */
  private static async fetchHistoricalData(
    fiscalYear: string,
    budgetId?: string,
    categoryId?: string
  ): Promise<TimeSeriesDataPoint[]> {
    const filter: Record<string, unknown> = {
      fiscalYear,
      status: { $in: ['approved', 'received'] }
    };

    if (budgetId) {
      filter.budget = new mongoose.Types.ObjectId(budgetId);
    }

    if (categoryId) {
      filter.budgetCategory = new mongoose.Types.ObjectId(categoryId);
    }

    const incomeRecords = await Income.find(filter)
      .sort({ date: 1 })
      .populate('budgetCategory', 'name')
      .lean();

    // Group by month and aggregate
    const monthlyData = new Map<string, { total: number; count: number; source: string }>();
    
    incomeRecords.forEach(record => {
      const monthKey = new Date(record.date).toISOString().substring(0, 7); // YYYY-MM
      const existing = monthlyData.get(monthKey) || { total: 0, count: 0, source: record.source };
      
      monthlyData.set(monthKey, {
        total: existing.total + record.amount,
        count: existing.count + 1,
        source: record.source
      });
    });

    // Convert to time series data points
    return Array.from(monthlyData.entries())
      .map(([monthKey, data]) => ({
        date: new Date(monthKey + '-01'),
        value: data.total,
        source: data.source
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  /**
   * Detect anomalies using modified Z-score
   */
  private static detectAnomalies(data: number[], threshold: number): number[] {
    const median = this.calculateMedian(data);
    const mad = this.calculateMAD(data, median);
    
    if (mad === 0) return [];
    
    return data.map((value, index) => {
      const modifiedZScore = 0.6745 * (value - median) / mad;
      return Math.abs(modifiedZScore) > threshold ? index : -1;
    }).filter(index => index !== -1);
  }

  /**
   * Calculate median of array
   */
  private static calculateMedian(data: number[]): number {
    const sorted = [...data].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  /**
   * Calculate Median Absolute Deviation
   */
  private static calculateMAD(data: number[], median: number): number {
    const deviations = data.map(value => Math.abs(value - median));
    return this.calculateMedian(deviations);
  }

  /**
   * Clean data by removing or interpolating anomalies
   */
  private static cleanDataForModeling(data: number[], anomalies: number[]): number[] {
    if (anomalies.length === 0) return [...data];
    
    const cleaned = [...data];
    
    // Simple interpolation for anomalies
    anomalies.forEach(index => {
      if (index > 0 && index < data.length - 1) {
        cleaned[index] = (data[index - 1] + data[index + 1]) / 2;
      } else if (index === 0 && data.length > 1) {
        cleaned[index] = data[1];
      } else if (index === data.length - 1 && data.length > 1) {
        cleaned[index] = data[data.length - 2];
      }
    });
    
    return cleaned;
  }

  /**
   * Perform seasonal decomposition using classical method
   */
  private static performSeasonalDecomposition(
    data: number[], 
    seasonLength: number
  ): SeasonalDecomposition {
    const trend = this.calculateMovingAverage(data, seasonLength);
    const detrended = data.map((value, index) => 
      trend[index] !== undefined ? value - trend[index] : 0
    );
    
    // Calculate seasonal factors
    const seasonalFactors: number[] = Array(seasonLength).fill(0);
    const seasonCounts: number[] = Array(seasonLength).fill(0);
    
    detrended.forEach((value, index) => {
      const seasonIndex = index % seasonLength;
      seasonalFactors[seasonIndex] += value;
      seasonCounts[seasonIndex]++;
    });
    
    // Average seasonal factors
    for (let i = 0; i < seasonLength; i++) {
      if (seasonCounts[i] > 0) {
        seasonalFactors[i] /= seasonCounts[i];
      }
    }
    
    // Generate seasonal component
    const seasonal = data.map((_, index) => seasonalFactors[index % seasonLength]);
    
    // Calculate residual
    const residual = data.map((value, index) => 
      value - (trend[index] || 0) - seasonal[index]
    );
    
    return {
      trend: trend.map(t => t || 0),
      seasonal,
      residual,
      seasonalFactors,
      seasonLength
    };
  }

  /**
   * Calculate moving average
   */
  private static calculateMovingAverage(data: number[], window: number): (number | undefined)[] {
    const result: (number | undefined)[] = [];
    const halfWindow = Math.floor(window / 2);

    for (let i = 0; i < data.length; i++) {
      if (i < halfWindow || i >= data.length - halfWindow) {
        result.push(undefined);
      } else {
        const sum = data.slice(i - halfWindow, i + halfWindow + 1)
          .reduce((acc, val) => acc + val, 0);
        result.push(sum / window);
      }
    }

    return result;
  }

  /**
   * Select the best forecasting model based on data characteristics
   */
  private static async selectBestModel(
    data: number[],
    options: ForecastOptions,
    seasonalDecomposition?: SeasonalDecomposition
  ): Promise<ForecastModel> {
    if (options.modelType !== 'auto') {
      return this.createModelByType(data, options.modelType!, seasonalDecomposition);
    }

    // Test different models and select the best one
    const models: ForecastModel[] = [];

    // Linear model
    models.push(this.createLinearModel(data));

    // Exponential model if data shows exponential growth
    if (this.detectExponentialTrend(data)) {
      models.push(this.createExponentialModel(data));
    }

    // Seasonal model if seasonality is detected
    if (seasonalDecomposition) {
      models.push(this.createSeasonalModel(data, seasonalDecomposition));
    }

    // Select model with best accuracy
    return models.reduce((best, current) =>
      current.accuracy.r2 > best.accuracy.r2 ? current : best
    );
  }

  /**
   * Create model by specific type
   */
  private static createModelByType(
    data: number[],
    type: 'linear' | 'exponential' | 'seasonal',
    seasonalDecomposition?: SeasonalDecomposition
  ): ForecastModel {
    switch (type) {
      case 'linear':
        return this.createLinearModel(data);
      case 'exponential':
        return this.createExponentialModel(data);
      case 'seasonal':
        if (!seasonalDecomposition) {
          throw new Error('Seasonal decomposition required for seasonal model');
        }
        return this.createSeasonalModel(data, seasonalDecomposition);
      default:
        return this.createLinearModel(data);
    }
  }

  /**
   * Create linear regression model
   */
  private static createLinearModel(data: number[]): ForecastModel {
    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);

    // Calculate linear regression parameters
    const meanX = x.reduce((a, b) => a + b, 0) / n;
    const meanY = data.reduce((a, b) => a + b, 0) / n;

    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (x[i] - meanX) * (data[i] - meanY);
      denominator += Math.pow(x[i] - meanX, 2);
    }

    const slope = denominator !== 0 ? numerator / denominator : 0;
    const intercept = meanY - slope * meanX;

    // Calculate accuracy metrics
    const predictions = x.map(xi => slope * xi + intercept);
    const accuracy = this.calculateAccuracyMetrics(data, predictions);

    return {
      type: 'linear',
      parameters: { slope, intercept },
      accuracy,
      confidence: Math.max(0, Math.min(1, accuracy.r2))
    };
  }

  /**
   * Create exponential model
   */
  private static createExponentialModel(data: number[]): ForecastModel {
    // Transform data to log scale for linear regression
    const logData = data.map(value => Math.log(Math.max(value, 1)));
    const linearModel = this.createLinearModel(logData);

    // Transform parameters back to exponential
    const a = Math.exp(linearModel.parameters.intercept);
    const b = linearModel.parameters.slope;

    // Calculate predictions in original scale
    const predictions = data.map((_, i) => a * Math.exp(b * i));
    const accuracy = this.calculateAccuracyMetrics(data, predictions);

    return {
      type: 'exponential',
      parameters: { a, b },
      accuracy,
      confidence: Math.max(0, Math.min(1, accuracy.r2))
    };
  }

  /**
   * Create seasonal model
   */
  private static createSeasonalModel(
    data: number[],
    decomposition: SeasonalDecomposition
  ): ForecastModel {
    // Use trend component for linear regression
    const trendData = decomposition.trend.filter(t => t !== 0);
    const linearModel = this.createLinearModel(trendData);

    // Calculate predictions with seasonal adjustment
    const predictions = data.map((_, i) => {
      const trendValue = linearModel.parameters.slope * i + linearModel.parameters.intercept;
      const seasonalValue = decomposition.seasonal[i] || 0;
      return trendValue + seasonalValue;
    });

    const accuracy = this.calculateAccuracyMetrics(data, predictions);

    return {
      type: 'seasonal',
      parameters: {
        ...linearModel.parameters,
        seasonLength: decomposition.seasonLength,
        seasonalFactors: decomposition.seasonalFactors.length
      },
      accuracy,
      confidence: Math.max(0, Math.min(1, accuracy.r2))
    };
  }

  /**
   * Detect if data shows exponential trend
   */
  private static detectExponentialTrend(data: number[]): boolean {
    if (data.length < 4) return false;

    // Check if log-transformed data is more linear than original
    const logData = data.map(value => Math.log(Math.max(value, 1)));

    const linearR2 = this.createLinearModel(data).accuracy.r2;
    const logLinearR2 = this.createLinearModel(logData).accuracy.r2;

    return logLinearR2 > linearR2 + 0.1; // Threshold for improvement
  }

  /**
   * Calculate accuracy metrics for model evaluation
   */
  private static calculateAccuracyMetrics(actual: number[], predicted: number[]): {
    mape: number;
    rmse: number;
    mae: number;
    r2: number;
  } {
    const n = actual.length;

    // Mean Absolute Percentage Error
    let mapeSum = 0;
    let maeSum = 0;
    let rmseSum = 0;

    for (let i = 0; i < n; i++) {
      const error = actual[i] - predicted[i];
      const absError = Math.abs(error);

      maeSum += absError;
      rmseSum += error * error;

      if (actual[i] !== 0) {
        mapeSum += Math.abs(error / actual[i]);
      }
    }

    const mape = (mapeSum / n) * 100;
    const mae = maeSum / n;
    const rmse = Math.sqrt(rmseSum / n);

    // R-squared
    const actualMean = actual.reduce((a, b) => a + b, 0) / n;
    const totalSumSquares = actual.reduce((sum, value) => sum + Math.pow(value - actualMean, 2), 0);
    const residualSumSquares = rmseSum;
    const r2 = totalSumSquares !== 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;

    return { mape, rmse, mae, r2 };
  }

  /**
   * Generate base forecast using selected model
   */
  private static generateBaseForecast(
    data: number[],
    model: ForecastModel,
    horizon: number,
    confidenceLevel: number,
    seasonalDecomposition?: SeasonalDecomposition
  ): ForecastDataPoint[] {
    const forecast: ForecastDataPoint[] = [];
    const startDate = new Date();

    for (let i = 0; i < horizon; i++) {
      const period = new Date(startDate);
      period.setMonth(period.getMonth() + i + 1);

      let forecastValue: number;
      let seasonalFactor = 1;

      switch (model.type) {
        case 'linear':
          forecastValue = model.parameters.slope * (data.length + i) + model.parameters.intercept;
          break;
        case 'exponential':
          forecastValue = model.parameters.a * Math.exp(model.parameters.b * (data.length + i));
          break;
        case 'seasonal':
          const trendValue = model.parameters.slope * (data.length + i) + model.parameters.intercept;
          if (seasonalDecomposition) {
            const seasonIndex = (data.length + i) % seasonalDecomposition.seasonLength;
            seasonalFactor = seasonalDecomposition.seasonalFactors[seasonIndex] || 1;
            forecastValue = trendValue + (seasonalDecomposition.seasonal[seasonIndex] || 0);
          } else {
            forecastValue = trendValue;
          }
          break;
        default:
          forecastValue = model.parameters.slope * (data.length + i) + model.parameters.intercept;
      }

      // Calculate confidence intervals
      const standardError = this.calculateStandardError(data, model);
      const margin = this.getConfidenceMargin(confidenceLevel) * standardError;

      forecast.push({
        period: period.toISOString().substring(0, 7), // YYYY-MM format
        forecasted: Math.max(0, forecastValue),
        lowerBound: Math.max(0, forecastValue - margin),
        upperBound: forecastValue + margin,
        confidence: model.confidence,
        isAnomaly: false,
        seasonalFactor
      });
    }

    return forecast;
  }

  /**
   * Calculate standard error for confidence intervals
   */
  private static calculateStandardError(data: number[], model: ForecastModel): number {
    const predictions = data.map((_, i) => {
      switch (model.type) {
        case 'linear':
          return model.parameters.slope * i + model.parameters.intercept;
        case 'exponential':
          return model.parameters.a * Math.exp(model.parameters.b * i);
        default:
          return model.parameters.slope * i + model.parameters.intercept;
      }
    });

    const residuals = data.map((value, i) => value - predictions[i]);
    const sumSquaredResiduals = residuals.reduce((sum, r) => sum + r * r, 0);
    const degreesOfFreedom = data.length - 2;

    return Math.sqrt(sumSquaredResiduals / degreesOfFreedom);
  }

  /**
   * Get confidence margin based on confidence level
   */
  private static getConfidenceMargin(confidenceLevel: number): number {
    // Approximate z-scores for common confidence levels
    const zScores: Record<number, number> = {
      0.90: 1.645,
      0.95: 1.96,
      0.99: 2.576
    };

    return zScores[confidenceLevel] || 1.96;
  }

  /**
   * Generate scenario forecasts (optimistic, realistic, pessimistic)
   */
  private static async generateScenarioForecasts(
    baseForecast: ForecastDataPoint[],
    budgetId?: string,
    categoryId?: string
  ): Promise<ScenarioForecast[]> {
    const scenarios: ScenarioForecast[] = [];

    // Realistic scenario (base forecast)
    const realisticTotal = baseForecast.reduce((sum, point) => sum + point.forecasted, 0);
    scenarios.push({
      scenario: 'realistic',
      description: 'Based on current trends and historical patterns',
      adjustmentFactor: 1.0,
      forecast: [...baseForecast],
      totalProjected: realisticTotal,
      budgetImpact: await this.calculateBudgetImpact(realisticTotal, budgetId, categoryId)
    });

    // Optimistic scenario (+15% adjustment)
    const optimisticForecast = baseForecast.map(point => ({
      ...point,
      forecasted: point.forecasted * 1.15,
      lowerBound: point.lowerBound * 1.15,
      upperBound: point.upperBound * 1.15
    }));
    const optimisticTotal = optimisticForecast.reduce((sum, point) => sum + point.forecasted, 0);
    scenarios.push({
      scenario: 'optimistic',
      description: 'Assuming favorable economic conditions and improved performance',
      adjustmentFactor: 1.15,
      forecast: optimisticForecast,
      totalProjected: optimisticTotal,
      budgetImpact: await this.calculateBudgetImpact(optimisticTotal, budgetId, categoryId)
    });

    // Pessimistic scenario (-20% adjustment)
    const pessimisticForecast = baseForecast.map(point => ({
      ...point,
      forecasted: point.forecasted * 0.8,
      lowerBound: point.lowerBound * 0.8,
      upperBound: point.upperBound * 0.8
    }));
    const pessimisticTotal = pessimisticForecast.reduce((sum, point) => sum + point.forecasted, 0);
    scenarios.push({
      scenario: 'pessimistic',
      description: 'Considering potential economic downturns and reduced performance',
      adjustmentFactor: 0.8,
      forecast: pessimisticForecast,
      totalProjected: pessimisticTotal,
      budgetImpact: await this.calculateBudgetImpact(pessimisticTotal, budgetId, categoryId)
    });

    return scenarios;
  }

  /**
   * Calculate budget impact for scenario
   */
  private static async calculateBudgetImpact(
    projectedTotal: number,
    budgetId?: string,
    categoryId?: string
  ): Promise<{ variance: number; utilizationRate: number; riskLevel: 'low' | 'medium' | 'high' }> {
    if (!budgetId) {
      return { variance: 0, utilizationRate: 0, riskLevel: 'low' };
    }

    try {
      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        return { variance: 0, utilizationRate: 0, riskLevel: 'low' };
      }

      let budgetedAmount = budget.totalAmount || 0;

      if (categoryId) {
        const category = await BudgetCategory.findById(categoryId).lean();
        if (category) {
          budgetedAmount = category.allocatedAmount || 0;
        }
      }

      const variance = projectedTotal - budgetedAmount;
      const utilizationRate = budgetedAmount > 0 ? (projectedTotal / budgetedAmount) * 100 : 0;

      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      if (utilizationRate > 120) {
        riskLevel = 'high';
      } else if (utilizationRate > 105) {
        riskLevel = 'medium';
      }

      return { variance, utilizationRate, riskLevel };
    } catch (error) {
      logger.error('Error calculating budget impact', LogCategory.ACCOUNTING, error);
      return { variance: 0, utilizationRate: 0, riskLevel: 'low' };
    }
  }

  /**
   * Analyze trends in the data
   */
  private static analyzeTrends(
    data: number[],
    seasonalDecomposition?: SeasonalDecomposition
  ): {
    shortTerm: 'increasing' | 'decreasing' | 'stable';
    longTerm: 'increasing' | 'decreasing' | 'stable';
    volatility: 'low' | 'medium' | 'high';
    cyclical: boolean;
  } {
    // Short-term trend (last 3 months)
    const shortTermData = data.slice(-3);
    const shortTermTrend = this.calculateTrendDirection(shortTermData);

    // Long-term trend (all data)
    const longTermTrend = this.calculateTrendDirection(data);

    // Volatility analysis
    const volatility = this.calculateVolatility(data);

    // Cyclical pattern detection
    const cyclical = seasonalDecomposition ?
      this.detectCyclicalPattern(seasonalDecomposition.seasonal) : false;

    return {
      shortTerm: shortTermTrend,
      longTerm: longTermTrend,
      volatility,
      cyclical
    };
  }

  /**
   * Calculate trend direction
   */
  private static calculateTrendDirection(data: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (data.length < 2) return 'stable';

    const linearModel = this.createLinearModel(data);
    const slope = linearModel.parameters.slope;

    if (slope > 0.05) return 'increasing';
    if (slope < -0.05) return 'decreasing';
    return 'stable';
  }

  /**
   * Calculate volatility level
   */
  private static calculateVolatility(data: number[]): 'low' | 'medium' | 'high' {
    if (data.length < 2) return 'low';

    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const variance = data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / data.length;
    const coefficientOfVariation = Math.sqrt(variance) / mean;

    if (coefficientOfVariation > 0.3) return 'high';
    if (coefficientOfVariation > 0.15) return 'medium';
    return 'low';
  }

  /**
   * Detect cyclical patterns
   */
  private static detectCyclicalPattern(seasonal: number[]): boolean {
    if (seasonal.length < 12) return false;

    // Check for consistent seasonal patterns
    const seasonalVariance = seasonal.reduce((sum, value) => sum + Math.pow(value, 2), 0) / seasonal.length;
    return seasonalVariance > 0.1; // Threshold for significant seasonality
  }

  /**
   * Generate recommendations based on forecast analysis
   */
  private static async generateRecommendations(
    forecast: ForecastDataPoint[],
    scenarios: ScenarioForecast[],
    trends: {
      shortTerm: 'increasing' | 'decreasing' | 'stable';
      longTerm: 'increasing' | 'decreasing' | 'stable';
      volatility: 'low' | 'medium' | 'high';
      cyclical: boolean;
    },
    budgetId?: string,
    categoryId?: string
  ): Promise<Array<{
    type: 'budget_adjustment' | 'risk_mitigation' | 'opportunity' | 'alert';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: number;
    actionRequired: boolean;
  }>> {
    const recommendations: Array<{
      type: 'budget_adjustment' | 'risk_mitigation' | 'opportunity' | 'alert';
      priority: 'high' | 'medium' | 'low';
      title: string;
      description: string;
      impact: number;
      actionRequired: boolean;
    }> = [];

    // Trend-based recommendations
    if (trends.shortTerm === 'decreasing' && trends.longTerm === 'decreasing') {
      recommendations.push({
        type: 'alert',
        priority: 'high',
        title: 'Declining Income Trend Detected',
        description: 'Both short-term and long-term trends show declining income. Consider reviewing income sources and implementing corrective measures.',
        impact: -0.15,
        actionRequired: true
      });
    }

    if (trends.volatility === 'high') {
      recommendations.push({
        type: 'risk_mitigation',
        priority: 'medium',
        title: 'High Income Volatility',
        description: 'Income shows high volatility. Consider diversifying income sources and implementing risk management strategies.',
        impact: -0.1,
        actionRequired: true
      });
    }

    // Scenario-based recommendations
    const pessimisticScenario = scenarios.find(s => s.scenario === 'pessimistic');
    if (pessimisticScenario && pessimisticScenario.budgetImpact.riskLevel === 'high') {
      recommendations.push({
        type: 'budget_adjustment',
        priority: 'high',
        title: 'Budget Risk in Pessimistic Scenario',
        description: 'Under pessimistic conditions, budget targets may not be met. Consider adjusting budget allocations or implementing contingency plans.',
        impact: pessimisticScenario.budgetImpact.variance,
        actionRequired: true
      });
    }

    // Opportunity recommendations
    if (trends.shortTerm === 'increasing' && trends.longTerm === 'increasing') {
      recommendations.push({
        type: 'opportunity',
        priority: 'medium',
        title: 'Positive Growth Opportunity',
        description: 'Consistent growth trends present opportunities for strategic investments and budget expansion.',
        impact: 0.1,
        actionRequired: false
      });
    }

    // Seasonal recommendations
    if (trends.cyclical) {
      recommendations.push({
        type: 'budget_adjustment',
        priority: 'low',
        title: 'Seasonal Pattern Detected',
        description: 'Income shows seasonal patterns. Consider adjusting budget planning to account for seasonal variations.',
        impact: 0.05,
        actionRequired: false
      });
    }

    return recommendations;
  }
}

export const advancedForecastingService = AdvancedForecastingService;

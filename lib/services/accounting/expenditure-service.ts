import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Expense, { IExpense } from '@/models/accounting/Expense';
import {
  ExpenditureStatus,
  ExpenditureCategory,
  ExpenditurePriority,
  PaymentMethod
} from '@/types/accounting/expenditure';
import Vendor, { IVendor } from '@/models/accounting/Vendor';
import Budget from '@/models/accounting/Budget';
import mongoose from 'mongoose';

// Service interfaces
export interface CreateExpenditureRequest {
  title: string;
  description: string;
  category: ExpenditureCategory;
  subcategory: string;
  amount: number;
  currency?: string;
  exchangeRate?: number;
  expenditureDate: Date;
  dueDate?: Date;
  priority?: ExpenditurePriority;
  requestedBy: string;
  requestedByName: string;
  requestedByEmail: string;
  department: string;
  costCenter?: string;
  vendor: {
    vendorId?: string;
    vendorName: string;
    vendorEmail?: string;
    vendorPhone?: string;
    vendorAddress?: string;
  };
  budgetAllocations: Array<{
    budgetId: string;
    allocatedAmount: number;
    percentage: number;
  }>;
  taxInfo?: {
    taxType: 'VAT' | 'withholding' | 'excise' | 'none';
    taxRate: number;
    isExempt: boolean;
    exemptionReason?: string;
  };
  paymentMethod?: PaymentMethod;
  tags?: string[];
  notes?: string[];
  isUrgent?: boolean;
  requiresReceipt?: boolean;
  isCapitalExpenditure?: boolean;
  projectId?: string;
}

export interface UpdateExpenditureRequest {
  title?: string;
  description?: string;
  category?: ExpenditureCategory;
  subcategory?: string;
  amount?: number;
  currency?: string;
  exchangeRate?: number;
  expenditureDate?: Date;
  dueDate?: Date;
  priority?: ExpenditurePriority;
  department?: string;
  costCenter?: string;
  vendor?: Partial<CreateExpenditureRequest['vendor']>;
  budgetAllocations?: CreateExpenditureRequest['budgetAllocations'];
  taxInfo?: CreateExpenditureRequest['taxInfo'];
  paymentMethod?: PaymentMethod;
  tags?: string[];
  notes?: string[];
  isUrgent?: boolean;
  requiresReceipt?: boolean;
  isCapitalExpenditure?: boolean;
  projectId?: string;
}

export interface ExpenditureFilters {
  status?: ExpenditureStatus[];
  category?: ExpenditureCategory[];
  priority?: ExpenditurePriority[];
  department?: string[];
  requestedBy?: string[];
  vendorId?: string[];
  amountRange?: { min: number; max: number };
  dateRange?: { startDate: Date; endDate: Date };
  isUrgent?: boolean;
  isCapitalExpenditure?: boolean;
  tags?: string[];
  search?: string;
}

export interface ExpenditureStatistics {
  totalExpenditures: number;
  totalAmount: number;
  averageAmount: number;
  pendingApprovals: number;
  pendingPayments: number;
  overBudgetCount: number;
  urgentCount: number;
  byCategory: Record<ExpenditureCategory, { count: number; amount: number }>;
  byStatus: Record<ExpenditureStatus, { count: number; amount: number }>;
  byDepartment: Record<string, { count: number; amount: number }>;
  monthlyTrend: Array<{ month: string; count: number; amount: number }>;
}

/**
 * Expenditure Service
 * Handles all expenditure-related business logic
 */
export class ExpenditureService {

  /**
   * Create a new expenditure
   */
  static async createExpenditure(
    request: CreateExpenditureRequest,
    userId: string
  ): Promise<IExpense> {
    try {
      await connectToDatabase();

      // Validate vendor
      let vendor: IVendor | null = null;
      if (request.vendor.vendorId) {
        vendor = await Vendor.findById(request.vendor.vendorId);
        if (!vendor) {
          throw new Error('Vendor not found');
        }
        if (!vendor.isEligibleForPayment()) {
          throw new Error('Vendor is not eligible for payment');
        }
      }

      // Validate budget allocations
      const totalAllocation = request.budgetAllocations.reduce(
        (sum, allocation) => sum + allocation.allocatedAmount, 
        0
      );
      
      if (Math.abs(totalAllocation - request.amount) > 0.01) {
        throw new Error('Budget allocations must equal the expenditure amount');
      }

      // Check budget availability
      for (const allocation of request.budgetAllocations) {
        const budget = await Budget.findById(allocation.budgetId);
        if (!budget) {
          throw new Error(`Budget ${allocation.budgetId} not found`);
        }
        
        // Calculate remaining budget
        const spent = await this.calculateSpentAmount(allocation.budgetId);
        const remaining = budget.allocatedAmount - spent;
        
        if (allocation.allocatedAmount > remaining) {
          logger.warn('Budget allocation exceeds remaining budget', LogCategory.ACCOUNTING, {
            budgetId: allocation.budgetId,
            requested: allocation.allocatedAmount,
            remaining
          });
        }
      }

      // Calculate tax
      const taxInfo = request.taxInfo || {
        taxType: 'none',
        taxRate: 0,
        isExempt: false
      };
      
      const taxAmount = taxInfo.isExempt ? 0 : (request.amount * taxInfo.taxRate) / 100;

      // Create expenditure using Expense model structure
      const expenditure = new Expense({
        date: request.expenditureDate,
        category: request.category,
        subcategory: request.subcategory,
        amount: request.amount,
        reference: `EXP-${Date.now()}`, // Generate reference number
        description: request.description,
        fiscalYear: new Date().getFullYear().toString() + '-' + (new Date().getFullYear() + 1).toString(),
        status: 'draft',
        paymentMethod: request.paymentMethod,
        budget: request.budgetAllocations?.[0]?.budgetId, // Use first budget allocation
        appliedToBudget: true,
        notes: request.notes?.join('; ') || request.description,
        vendor: vendor?.name || request.vendor.vendorName,
        department: request.department,
        costCenter: request.costCenter,
        createdBy: userId,
        updatedBy: userId
      });

      await expenditure.save();

      // Update vendor transaction history
      if (vendor) {
        // Note: vendor.addTransaction method may not exist in simplified model
        // vendor.addTransaction(request.amount, request.expenditureDate);
        // await vendor.save();
      }

      logger.info('Expenditure created successfully', LogCategory.ACCOUNTING, {
        expenditureId: expenditure._id,
        reference: expenditure.reference,
        amount: expenditure.amount,
        vendor: expenditure.vendor,
        userId
      });

      return expenditure;
    } catch (error) {
      logger.error('Error creating expenditure', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update an existing expenditure
   */
  static async updateExpenditure(
    expenditureId: string,
    request: UpdateExpenditureRequest,
    userId: string
  ): Promise<IExpense> {
    try {
      await connectToDatabase();

      const expenditure = await Expense.findById(expenditureId);
      if (!expenditure) {
        throw new Error('Expenditure not found');
      }

      // Check if expenditure can be updated
      if (expenditure.status === 'paid' ||
          expenditure.status === 'cancelled') {
        throw new Error('Cannot update expenditure in current status');
      }

      // Update fields (mapped to Expense model structure)
      if (request.description) expenditure.description = request.description;
      if (request.category) expenditure.category = request.category;
      if (request.subcategory) expenditure.subcategory = request.subcategory;
      if (request.amount !== undefined) {
        expenditure.amount = request.amount;
      }
      if (request.expenditureDate) expenditure.date = request.expenditureDate;
      if (request.department) expenditure.department = request.department;
      if (request.costCenter) expenditure.costCenter = request.costCenter;
      if (request.paymentMethod) expenditure.paymentMethod = request.paymentMethod;
      if (request.notes) expenditure.notes = request.notes.join('; ');

      // Update vendor information (simplified for Expense model)
      if (request.vendor) {
        if (request.vendor.vendorId) {
          const vendor = await Vendor.findById(request.vendor.vendorId);
          if (vendor) {
            expenditure.vendor = vendor.name;
          }
        } else if (request.vendor.vendorName) {
          expenditure.vendor = request.vendor.vendorName;
        }
      }

      // Update budget allocations (simplified for Expense model)
      if (request.budgetAllocations && request.budgetAllocations.length > 0) {
        expenditure.budget = request.budgetAllocations[0].budgetId;
        expenditure.appliedToBudget = true;
      }

      expenditure.updatedBy = userId;
      await expenditure.save();

      logger.info('Expenditure updated successfully', LogCategory.ACCOUNTING, {
        expenditureId: expenditure._id,
        reference: expenditure.reference,
        userId
      });

      return expenditure;
    } catch (error) {
      logger.error('Error updating expenditure', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get expenditure by ID
   */
  static async getExpenditureById(expenditureId: string): Promise<IExpense | null> {
    try {
      await connectToDatabase();
      return await Expense.findById(expenditureId);
    } catch (error) {
      logger.error('Error getting expenditure by ID', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get expenditures with filters and pagination
   */
  static async getExpenditures(
    filters: ExpenditureFilters = {},
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<{
    expenditures: IExpense[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      await connectToDatabase();

      // Build query
      const query: Record<string, unknown> = {};

      if (filters.status?.length) {
        query.status = { $in: filters.status };
      }

      if (filters.category?.length) {
        query.category = { $in: filters.category };
      }

      if (filters.priority?.length) {
        query.priority = { $in: filters.priority };
      }

      if (filters.department?.length) {
        query.department = { $in: filters.department };
      }

      if (filters.requestedBy?.length) {
        query.requestedBy = { $in: filters.requestedBy };
      }

      if (filters.vendorId?.length) {
        query['vendor.vendorId'] = { $in: filters.vendorId };
      }

      if (filters.amountRange) {
        query.amount = {
          $gte: filters.amountRange.min,
          $lte: filters.amountRange.max
        };
      }

      if (filters.dateRange) {
        query.date = {
          $gte: filters.dateRange.startDate,
          $lte: filters.dateRange.endDate
        };
      }

      if (filters.search) {
        query.$or = [
          { description: { $regex: filters.search, $options: 'i' } },
          { reference: { $regex: filters.search, $options: 'i' } },
          { vendor: { $regex: filters.search, $options: 'i' } }
        ];
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const [expenditures, total] = await Promise.all([
        Expense.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Expense.countDocuments(query)
      ]);

      return {
        expenditures: expenditures as unknown as IExpense[],
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error getting expenditures', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Delete expenditure
   */
  static async deleteExpenditure(expenditureId: string, userId: string): Promise<boolean> {
    try {
      await connectToDatabase();

      const expenditure = await Expense.findById(expenditureId);
      if (!expenditure) {
        throw new Error('Expenditure not found');
      }

      // Check if expenditure can be deleted
      if (expenditure.status === 'paid') {
        throw new Error('Cannot delete paid expenditure');
      }

      await Expense.findByIdAndDelete(expenditureId);

      logger.info('Expenditure deleted successfully', LogCategory.ACCOUNTING, {
        expenditureId,
        reference: expenditure.reference,
        userId
      });

      return true;
    } catch (error) {
      logger.error('Error deleting expenditure', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get expenditure statistics
   */
  static async getExpenditureStatistics(
    startDate?: Date,
    endDate?: Date,
    filters: Partial<ExpenditureFilters> = {}
  ): Promise<ExpenditureStatistics> {
    try {
      await connectToDatabase();

      const matchStage: Record<string, unknown> = {};

      if (startDate && endDate) {
        matchStage.date = {
          $gte: startDate,
          $lte: endDate
        };
      }

      // Apply additional filters
      if (filters.department?.length) {
        matchStage.department = { $in: filters.department };
      }

      if (filters.category?.length) {
        matchStage.category = { $in: filters.category };
      }

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalExpenditures: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
            averageAmount: { $avg: '$amount' },
            pendingApprovals: {
              $sum: {
                $cond: [{ $eq: ['$status', 'pending_approval'] }, 1, 0]
              }
            },
            pendingPayments: {
              $sum: {
                $cond: [{ $eq: ['$status', 'approved'] }, 1, 0]
              }
            },
            urgentCount: {
              $sum: 0 // Expense model doesn't have isUrgent field
            }
          }
        }
      ];

      const [basicStats] = await Expense.aggregate(pipeline);

      // Get category breakdown
      const categoryStats = await Expense.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        }
      ]);

      // Get status breakdown
      const statusStats = await Expense.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        }
      ]);

      // Get department breakdown
      const departmentStats = await Expense.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$department',
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        }
      ]);

      // Get monthly trend
      const monthlyStats = await Expense.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: {
              year: { $year: '$date' },
              month: { $month: '$date' }
            },
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      return {
        totalExpenditures: basicStats?.totalExpenditures || 0,
        totalAmount: basicStats?.totalAmount || 0,
        averageAmount: basicStats?.averageAmount || 0,
        pendingApprovals: basicStats?.pendingApprovals || 0,
        pendingPayments: basicStats?.pendingPayments || 0,
        overBudgetCount: 0, // TODO: Calculate based on budget allocations
        urgentCount: basicStats?.urgentCount || 0,
        byCategory: this.formatCategoryStats(categoryStats),
        byStatus: this.formatStatusStats(statusStats),
        byDepartment: this.formatDepartmentStats(departmentStats),
        monthlyTrend: this.formatMonthlyStats(monthlyStats)
      };
    } catch (error) {
      logger.error('Error getting expenditure statistics', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private static async enrichBudgetAllocations(
    allocations: Array<{ budgetId: string; allocatedAmount: number; percentage: number }>
  ) {
    const enriched = [];
    
    for (const allocation of allocations) {
      const budget = await Budget.findById(allocation.budgetId);
      if (budget) {
        const spent = await this.calculateSpentAmount(allocation.budgetId);
        const remaining = budget.allocatedAmount - spent;
        
        enriched.push({
          budgetId: allocation.budgetId,
          budgetName: budget.name,
          categoryId: budget.budgetCategory?.toString() || '',
          categoryName: budget.budgetCategory?.toString() || '',
          allocatedAmount: allocation.allocatedAmount,
          percentage: allocation.percentage,
          remainingBudget: remaining,
          isOverBudget: allocation.allocatedAmount > remaining
        });
      }
    }
    
    return enriched;
  }

  private static async calculateSpentAmount(budgetId: string): Promise<number> {
    const result = await Expense.aggregate([
      {
        $match: {
          budget: budgetId,
          status: { $in: ['approved', 'paid'] }
        }
      },
      {
        $group: {
          _id: null,
          totalSpent: { $sum: '$amount' }
        }
      }
    ]);

    return result[0]?.totalSpent || 0;
  }

  // Removed complex approval workflow - Expense model uses simpler approval system

  private static formatCategoryStats(stats: Array<{ _id: string; count: number; amount: number }>) {
    const result: Record<ExpenditureCategory, { count: number; amount: number }> = {} as any;
    
    Object.values(ExpenditureCategory).forEach(category => {
      result[category] = { count: 0, amount: 0 };
    });
    
    stats.forEach(stat => {
      if (stat._id && Object.values(ExpenditureCategory).includes(stat._id as ExpenditureCategory)) {
        result[stat._id as ExpenditureCategory] = {
          count: stat.count,
          amount: stat.amount
        };
      }
    });
    
    return result;
  }

  private static formatStatusStats(stats: Array<{ _id: string; count: number; amount: number }>) {
    const result: Record<ExpenditureStatus, { count: number; amount: number }> = {} as any;
    
    Object.values(ExpenditureStatus).forEach(status => {
      result[status] = { count: 0, amount: 0 };
    });
    
    stats.forEach(stat => {
      if (stat._id && Object.values(ExpenditureStatus).includes(stat._id as ExpenditureStatus)) {
        result[stat._id as ExpenditureStatus] = {
          count: stat.count,
          amount: stat.amount
        };
      }
    });
    
    return result;
  }

  private static formatDepartmentStats(stats: Array<{ _id: string; count: number; amount: number }>) {
    const result: Record<string, { count: number; amount: number }> = {};
    
    stats.forEach(stat => {
      if (stat._id) {
        result[stat._id] = {
          count: stat.count,
          amount: stat.amount
        };
      }
    });
    
    return result;
  }

  private static formatMonthlyStats(stats: Array<{ _id: { year: number; month: number }; count: number; amount: number }>) {
    return stats.map(stat => ({
      month: `${stat._id.year}-${String(stat._id.month).padStart(2, '0')}`,
      count: stat.count,
      amount: stat.amount
    }));
  }
}

export const expenditureService = ExpenditureService;

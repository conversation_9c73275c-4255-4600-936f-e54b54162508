import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Voucher, IVoucher } from '@/models/accounting/Voucher';
import { VoucherTypeDefinition, IVoucherTypeDefinition } from '@/models/accounting/VoucherTypeDefinition';
import { VoucherService } from './voucher-service';

/**
 * Interface for dynamic voucher creation
 */
export interface DynamicVoucherRequest {
  voucherTypeId: string;
  coreFields: {
    date: Date;
    description: string;
    totalAmount: number;
    fiscalYear: string;
    payee?: string;
    paymentMethod?: string;
    notes?: string;
  };
  dynamicFields: Record<string, any>;
  userId: string;
}

/**
 * Interface for voucher rendering data
 */
export interface VoucherRenderingData {
  voucher: IVoucher;
  typeDefinition: IVoucherTypeDefinition;
  enrichedData: Record<string, any>;
  displayConfiguration: {
    formLayout: string;
    fieldGroups: any[];
    computedFields: Record<string, any>;
  };
}

/**
 * Dynamic Voucher Service - Handles type-specific voucher operations
 */
export class DynamicVoucherService extends VoucherService {
  
  /**
   * Create a dynamic voucher with type-specific handling
   */
  async createDynamicVoucher(request: DynamicVoucherRequest): Promise<IVoucher> {
    try {
      await connectToDatabase();
      logger.info('Creating dynamic voucher', LogCategory.ACCOUNTING, { 
        voucherTypeId: request.voucherTypeId,
        userId: request.userId 
      });

      // Get voucher type definition
      const typeDefinition = await this.getVoucherTypeDefinition(request.voucherTypeId);
      if (!typeDefinition) {
        throw new Error(`Voucher type definition not found: ${request.voucherTypeId}`);
      }

      // Validate dynamic fields against type definition
      const validatedFields = await this.validateDynamicFields(
        request.dynamicFields, 
        typeDefinition.customFields
      );

      // Enrich data from external sources if needed
      const enrichedFields = await this.enrichVoucherData(
        request.voucherTypeId,
        validatedFields,
        typeDefinition.integrationSettings
      );

      // Generate voucher number with type-specific prefix
      const voucherNumber = await this.generateTypedVoucherNumber(
        typeDefinition.category,
        typeDefinition.configuration.numberPrefix
      );

      // Create voucher data
      const voucherData = {
        voucherNumber,
        voucherType: typeDefinition.category,
        voucherTypeId: request.voucherTypeId,
        date: request.coreFields.date,
        description: request.coreFields.description,
        totalAmount: request.coreFields.totalAmount,
        status: 'draft' as const,
        fiscalYear: request.coreFields.fiscalYear,
        payee: request.coreFields.payee,
        paymentMethod: request.coreFields.paymentMethod,
        notes: request.coreFields.notes,
        voucherCategory: typeDefinition.subCategory as any,
        isAutoGenerated: typeDefinition.integrationSettings.sourceModule !== 'manual',
        sourceModule: typeDefinition.integrationSettings.sourceModule || 'manual',
        dynamicFields: enrichedFields,
        fieldMetadata: this.generateFieldMetadata(enrichedFields, typeDefinition),
        createdBy: new mongoose.Types.ObjectId(request.userId),
        updatedBy: new mongoose.Types.ObjectId(request.userId)
      };

      // Create voucher
      const voucher = await this.create(voucherData);

      // Initialize type-specific approval workflow
      if (typeDefinition.configuration.requiresApproval) {
        await this.initializeTypedApprovalWorkflow(
          voucher._id.toString(),
          typeDefinition
        );
      }

      logger.info('Dynamic voucher created successfully', LogCategory.ACCOUNTING, { 
        voucherId: voucher._id,
        voucherTypeId: request.voucherTypeId
      });

      return voucher;

    } catch (error) {
      logger.error('Error creating dynamic voucher', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get voucher with full rendering data
   */
  async getVoucherForRendering(voucherId: string): Promise<VoucherRenderingData | null> {
    try {
      await connectToDatabase();

      // Get voucher with populated fields
      const voucher = await Voucher.findById(voucherId)
        .populate('createdBy', 'name email role')
        .populate('updatedBy', 'name email role')
        .populate('approvedBy', 'name email role')
        .populate('payrollRunId')
        .lean();

      if (!voucher) {
        return null;
      }

      // Get type definition
      const typeDefinition = await this.getVoucherTypeDefinition(voucher.voucherTypeId);
      if (!typeDefinition) {
        throw new Error(`Voucher type definition not found: ${voucher.voucherTypeId}`);
      }

      // Enrich data for display
      const enrichedData = await this.enrichVoucherDataForDisplay(
        voucher,
        typeDefinition
      );

      // Compute display configuration
      const displayConfiguration = this.computeDisplayConfiguration(
        voucher,
        typeDefinition,
        enrichedData
      );

      return {
        voucher: voucher as IVoucher,
        typeDefinition,
        enrichedData,
        displayConfiguration
      };

    } catch (error) {
      logger.error('Error getting voucher for rendering', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get voucher type definition
   */
  async getVoucherTypeDefinition(typeId: string): Promise<IVoucherTypeDefinition | null> {
    try {
      return await VoucherTypeDefinition.findOne({ 
        typeId, 
        isActive: true 
      }).lean();
    } catch (error) {
      logger.error('Error getting voucher type definition', LogCategory.ACCOUNTING, error);
      return null;
    }
  }

  /**
   * Get all active voucher type definitions
   */
  async getActiveVoucherTypes(): Promise<IVoucherTypeDefinition[]> {
    try {
      return await VoucherTypeDefinition.find({ 
        isActive: true 
      }).sort({ category: 1, name: 1 }).lean();
    } catch (error) {
      logger.error('Error getting active voucher types', LogCategory.ACCOUNTING, error);
      return [];
    }
  }

  /**
   * Validate dynamic fields against type definition
   */
  private async validateDynamicFields(
    fields: Record<string, any>,
    fieldDefinitions: any[]
  ): Promise<Record<string, any>> {
    const validatedFields: Record<string, any> = {};

    for (const fieldDef of fieldDefinitions) {
      const fieldName = fieldDef.fieldName;
      const fieldValue = fields[fieldName];

      // Check required fields
      if (fieldDef.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
        throw new Error(`Required field missing: ${fieldDef.label}`);
      }

      // Type validation
      if (fieldValue !== undefined && fieldValue !== null) {
        const validatedValue = this.validateFieldType(fieldValue, fieldDef);
        validatedFields[fieldName] = validatedValue;
      } else if (fieldDef.defaultValue !== undefined) {
        validatedFields[fieldName] = fieldDef.defaultValue;
      }
    }

    return validatedFields;
  }

  /**
   * Validate individual field type
   */
  private validateFieldType(value: any, fieldDef: any): any {
    switch (fieldDef.fieldType) {
      case 'string':
        if (typeof value !== 'string') {
          throw new Error(`Field ${fieldDef.label} must be a string`);
        }
        if (fieldDef.validation?.pattern) {
          const regex = new RegExp(fieldDef.validation.pattern);
          if (!regex.test(value)) {
            throw new Error(`Field ${fieldDef.label} does not match required pattern`);
          }
        }
        return value;

      case 'number':
        const numValue = Number(value);
        if (isNaN(numValue)) {
          throw new Error(`Field ${fieldDef.label} must be a number`);
        }
        if (fieldDef.validation?.min !== undefined && numValue < fieldDef.validation.min) {
          throw new Error(`Field ${fieldDef.label} must be at least ${fieldDef.validation.min}`);
        }
        if (fieldDef.validation?.max !== undefined && numValue > fieldDef.validation.max) {
          throw new Error(`Field ${fieldDef.label} must be at most ${fieldDef.validation.max}`);
        }
        return numValue;

      case 'date':
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) {
          throw new Error(`Field ${fieldDef.label} must be a valid date`);
        }
        return dateValue;

      case 'boolean':
        return Boolean(value);

      case 'array':
        if (!Array.isArray(value)) {
          throw new Error(`Field ${fieldDef.label} must be an array`);
        }
        return value;

      case 'reference':
        // Validate ObjectId format for references
        if (!mongoose.Types.ObjectId.isValid(value)) {
          throw new Error(`Field ${fieldDef.label} must be a valid reference ID`);
        }
        return new mongoose.Types.ObjectId(value);

      default:
        return value;
    }
  }

  /**
   * Enrich voucher data from external sources
   */
  private async enrichVoucherData(
    voucherTypeId: string,
    fields: Record<string, any>,
    integrationSettings: any
  ): Promise<Record<string, any>> {
    const enrichedFields = { ...fields };

    // Handle payroll integration
    if (voucherTypeId === 'payroll_salary' && fields.payrollRunId) {
      const payrollData = await this.fetchPayrollData(fields.payrollRunId);
      if (payrollData) {
        enrichedFields.payrollSummary = payrollData.summary;
        enrichedFields.employeeRecords = payrollData.employeeRecords;
        enrichedFields.payrollMetadata = {
          totalEmployees: payrollData.summary.totalEmployees,
          approvedBy: payrollData.approvedBy,
          approvedAt: payrollData.approvedAt
        };
      }
    }

    // Handle other integrations based on sourceModule
    if (integrationSettings.sourceModule && integrationSettings.apiEndpoint) {
      try {
        const externalData = await this.fetchExternalData(
          integrationSettings.apiEndpoint,
          fields
        );
        enrichedFields.externalData = externalData;
      } catch (error) {
        logger.warn('Failed to fetch external data', LogCategory.ACCOUNTING, error);
      }
    }

    return enrichedFields;
  }

  /**
   * Generate field metadata
   */
  private generateFieldMetadata(
    fields: Record<string, any>,
    typeDefinition: IVoucherTypeDefinition
  ): Record<string, any> {
    const metadata: Record<string, any> = {};
    const now = new Date();

    for (const [fieldName, value] of Object.entries(fields)) {
      metadata[fieldName] = {
        dataSource: typeDefinition.integrationSettings.sourceModule || 'manual',
        lastUpdated: now,
        validationStatus: 'valid',
        computedValue: false
      };
    }

    return metadata;
  }

  /**
   * Generate typed voucher number
   */
  private async generateTypedVoucherNumber(
    category: string,
    prefix: string
  ): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get next sequence number for this type
    const lastVoucher = await Voucher.findOne({
      voucherNumber: new RegExp(`^${prefix}-${year}${month}-`)
    }).sort({ voucherNumber: -1 });

    let sequence = 1;
    if (lastVoucher) {
      const lastSequence = lastVoucher.voucherNumber.split('-').pop();
      sequence = parseInt(lastSequence || '0') + 1;
    }

    return `${prefix}-${year}${month}-${String(sequence).padStart(4, '0')}`;
  }

  /**
   * Fetch payroll data for enrichment
   */
  private async fetchPayrollData(payrollRunId: string): Promise<any> {
    try {
      // This would typically call the payroll service
      // For now, return a placeholder
      return {
        summary: { totalEmployees: 0, totalAmount: 0 },
        employeeRecords: [],
        approvedBy: null,
        approvedAt: null
      };
    } catch (error) {
      logger.error('Error fetching payroll data', LogCategory.ACCOUNTING, error);
      return null;
    }
  }

  /**
   * Fetch external data
   */
  private async fetchExternalData(endpoint: string, fields: Record<string, any>): Promise<any> {
    // Implementation would depend on the specific integration
    return {};
  }

  /**
   * Enrich voucher data for display
   */
  private async enrichVoucherDataForDisplay(
    voucher: any,
    typeDefinition: IVoucherTypeDefinition
  ): Promise<Record<string, any>> {
    const enrichedData = { ...voucher.dynamicFields };

    // Add computed fields, lookups, etc.
    // This is where you'd add display-specific data enrichment

    return enrichedData;
  }

  /**
   * Compute display configuration
   */
  private computeDisplayConfiguration(
    voucher: any,
    typeDefinition: IVoucherTypeDefinition,
    enrichedData: Record<string, any>
  ): any {
    return {
      formLayout: typeDefinition.displayConfiguration.formLayout,
      fieldGroups: typeDefinition.displayConfiguration.fieldGroups,
      computedFields: {}
    };
  }

  /**
   * Initialize typed approval workflow
   */
  private async initializeTypedApprovalWorkflow(
    voucherId: string,
    typeDefinition: IVoucherTypeDefinition
  ): Promise<void> {
    // Implementation would set up the approval workflow based on type definition
    logger.info('Initializing typed approval workflow', LogCategory.ACCOUNTING, {
      voucherId,
      voucherTypeId: typeDefinition.typeId
    });
  }
}

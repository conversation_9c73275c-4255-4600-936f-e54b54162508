// lib/services/accounting/advanced-import-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget } from '@/models/accounting/Budget';
import { BudgetCategory } from '@/models/accounting/BudgetCategory';
import { BudgetItem } from '@/models/accounting/BudgetItem';
import * as XLSX from 'xlsx';

export interface ImportOptions {
  batchSize?: number;
  skipDuplicates?: boolean;
  updateExisting?: boolean;
  validateOnly?: boolean;
  rollbackOnError?: boolean;
  progressCallback?: (progress: number, message: string) => void;
}

export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: Array<{
    row: number;
    error: string;
    data?: any;
  }>;
  createdIds: string[];
  updatedIds: string[];
  skippedIds: string[];
}

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingId?: string;
  conflictFields: string[];
}

/**
 * Advanced Import Service for Budget System
 * Provides enhanced import capabilities with validation, error handling, and rollback
 */
export class AdvancedImportService {
  private session: any = null;

  /**
   * Import budgets with advanced features
   */
  async importBudgets(
    data: any[],
    options: ImportOptions = {}
  ): Promise<ImportResult> {
    const {
      batchSize = 50,
      skipDuplicates = true,
      updateExisting = false,
      validateOnly = false,
      rollbackOnError = true,
      progressCallback
    } = options;

    await connectToDatabase();

    const result: ImportResult = {
      success: false,
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      createdIds: [],
      updatedIds: [],
      skippedIds: []
    };

    // Start transaction for rollback capability
    if (rollbackOnError && !validateOnly) {
      this.session = await Budget.startSession();
      this.session.startTransaction();
    }

    try {
      logger.info('Starting advanced budget import', LogCategory.ACCOUNTING, {
        totalRecords: data.length,
        options
      });

      // Process in batches
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const batchResult = await this.processBudgetBatch(
          batch,
          i,
          { ...options, validateOnly }
        );

        // Merge results
        result.totalProcessed += batchResult.totalProcessed;
        result.successCount += batchResult.successCount;
        result.errorCount += batchResult.errorCount;
        result.skippedCount += batchResult.skippedCount;
        result.errors.push(...batchResult.errors);
        result.createdIds.push(...batchResult.createdIds);
        result.updatedIds.push(...batchResult.updatedIds);
        result.skippedIds.push(...batchResult.skippedIds);

        // Update progress
        const progress = Math.round(((i + batch.length) / data.length) * 100);
        progressCallback?.(progress, `Processed ${i + batch.length}/${data.length} records`);

        // Stop on error if rollback is enabled
        if (rollbackOnError && batchResult.errorCount > 0) {
          throw new Error(`Batch processing failed at record ${i + 1}`);
        }
      }

      // Commit transaction if successful
      if (this.session && !validateOnly) {
        await this.session.commitTransaction();
      }

      result.success = result.errorCount === 0;

      logger.info('Budget import completed', LogCategory.ACCOUNTING, {
        totalProcessed: result.totalProcessed,
        successCount: result.successCount,
        errorCount: result.errorCount,
        skippedCount: result.skippedCount
      });

      return result;

    } catch (error) {
      // Rollback transaction on error
      if (this.session && !validateOnly) {
        await this.session.abortTransaction();
      }

      logger.error('Error during budget import', LogCategory.ACCOUNTING, error);
      
      result.success = false;
      result.errors.push({
        row: -1,
        error: error instanceof Error ? error.message : 'Unknown error during import'
      });

      return result;

    } finally {
      if (this.session) {
        await this.session.endSession();
        this.session = null;
      }
    }
  }

  /**
   * Process a batch of budget records
   */
  private async processBudgetBatch(
    batch: any[],
    startIndex: number,
    options: ImportOptions
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      createdIds: [],
      updatedIds: [],
      skippedIds: []
    };

    for (let i = 0; i < batch.length; i++) {
      const record = batch[i];
      const rowNumber = startIndex + i + 1;

      try {
        result.totalProcessed++;

        // Validate record
        const validationResult = await this.validateBudgetRecord(record, rowNumber);
        if (!validationResult.isValid) {
          result.errorCount++;
          result.errors.push({
            row: rowNumber,
            error: validationResult.error || 'Validation failed',
            data: record
          });
          continue;
        }

        // Check for duplicates
        const duplicateCheck = await this.checkBudgetDuplicate(record);
        if (duplicateCheck.isDuplicate) {
          if (options.skipDuplicates) {
            result.skippedCount++;
            result.skippedIds.push(duplicateCheck.existingId!);
            continue;
          } else if (options.updateExisting) {
            // Update existing budget
            if (!options.validateOnly) {
              const updatedBudget = await this.updateExistingBudget(
                duplicateCheck.existingId!,
                record
              );
              result.updatedIds.push(updatedBudget._id.toString());
            }
            result.successCount++;
            continue;
          } else {
            result.errorCount++;
            result.errors.push({
              row: rowNumber,
              error: `Duplicate budget found: ${duplicateCheck.conflictFields.join(', ')}`,
              data: record
            });
            continue;
          }
        }

        // Create new budget
        if (!options.validateOnly) {
          const newBudget = await this.createBudgetRecord(record);
          result.createdIds.push(newBudget._id.toString());
        }
        result.successCount++;

      } catch (error) {
        result.errorCount++;
        result.errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: record
        });
      }
    }

    return result;
  }

  /**
   * Validate a budget record
   */
  private async validateBudgetRecord(
    record: any,
    rowNumber: number
  ): Promise<{ isValid: boolean; error?: string }> {
    // Required fields
    if (!record.name || typeof record.name !== 'string' || record.name.trim().length === 0) {
      return { isValid: false, error: 'Budget name is required' };
    }

    if (!record.fiscalYear || typeof record.fiscalYear !== 'string') {
      return { isValid: false, error: 'Fiscal year is required' };
    }

    // Date validation
    if (record.startDate && record.endDate) {
      const startDate = new Date(record.startDate);
      const endDate = new Date(record.endDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return { isValid: false, error: 'Invalid date format' };
      }

      if (startDate >= endDate) {
        return { isValid: false, error: 'Start date must be before end date' };
      }
    }

    // Status validation
    const validStatuses = ['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed'];
    if (record.status && !validStatuses.includes(record.status)) {
      return { isValid: false, error: `Invalid status. Valid statuses: ${validStatuses.join(', ')}` };
    }

    return { isValid: true };
  }

  /**
   * Check for duplicate budgets
   */
  private async checkBudgetDuplicate(record: any): Promise<DuplicateCheckResult> {
    const existingBudget = await Budget.findOne({
      name: record.name,
      fiscalYear: record.fiscalYear
    });

    if (existingBudget) {
      return {
        isDuplicate: true,
        existingId: existingBudget._id.toString(),
        conflictFields: ['name', 'fiscalYear']
      };
    }

    return {
      isDuplicate: false,
      conflictFields: []
    };
  }

  /**
   * Create a new budget record
   */
  private async createBudgetRecord(record: any): Promise<any> {
    const budgetData = {
      name: record.name.trim(),
      description: record.description?.trim() || '',
      fiscalYear: record.fiscalYear,
      startDate: record.startDate ? new Date(record.startDate) : undefined,
      endDate: record.endDate ? new Date(record.endDate) : undefined,
      status: record.status || 'draft',
      totalIncome: parseFloat(record.totalIncome) || 0,
      totalExpense: parseFloat(record.totalExpense) || 0,
      totalActualIncome: 0,
      totalActualExpense: 0
    };

    const budget = new Budget(budgetData);
    return await budget.save({ session: this.session });
  }

  /**
   * Update an existing budget record
   */
  private async updateExistingBudget(budgetId: string, record: any): Promise<any> {
    const updateData = {
      description: record.description?.trim(),
      startDate: record.startDate ? new Date(record.startDate) : undefined,
      endDate: record.endDate ? new Date(record.endDate) : undefined,
      status: record.status,
      totalIncome: parseFloat(record.totalIncome) || undefined,
      totalExpense: parseFloat(record.totalExpense) || undefined
    };

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    return await Budget.findByIdAndUpdate(
      budgetId,
      updateData,
      { new: true, session: this.session }
    );
  }

  /**
   * Parse Excel file with enhanced error handling
   */
  async parseExcelFile(file: File): Promise<any[]> {
    try {
      const buffer = await file.arrayBuffer();
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      
      // Get the first worksheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('No worksheets found in the Excel file');
      }

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: '',
        blankrows: false
      });

      if (jsonData.length === 0) {
        throw new Error('No data found in the Excel file');
      }

      // Convert to object format using first row as headers
      const headers = jsonData[0] as string[];
      const data = jsonData.slice(1).map((row: any[]) => {
        const obj: any = {};
        headers.forEach((header, index) => {
          obj[header] = row[index] || '';
        });
        return obj;
      });

      return data;

    } catch (error) {
      logger.error('Error parsing Excel file', LogCategory.ACCOUNTING, error);
      throw new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate import template
   */
  generateTemplate(type: 'budgets' | 'categories' | 'items'): any[] {
    switch (type) {
      case 'budgets':
        return [
          {
            name: 'Sample Budget 2024',
            description: 'Annual budget for fiscal year 2024',
            fiscalYear: '2024',
            startDate: '2024-01-01',
            endDate: '2024-12-31',
            status: 'draft',
            totalIncome: 1000000,
            totalExpense: 950000
          }
        ];

      case 'categories':
        return [
          {
            name: 'Personnel Costs',
            description: 'Salaries and benefits',
            type: 'expense'
          },
          {
            name: 'Government Grants',
            description: 'Funding from government',
            type: 'income'
          }
        ];

      case 'items':
        return [
          {
            name: 'Office Supplies',
            description: 'Stationery and office materials',
            quantity: 12,
            unitCost: 500,
            frequency: 1,
            categoryName: 'Administrative Expenses',
            categoryType: 'expense'
          }
        ];

      default:
        return [];
    }
  }
}

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Voucher, IVoucher } from '@/models/accounting/Voucher';
import User from '@/models/User';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';

/**
 * Interface for approval request
 */
export interface VoucherApprovalRequest {
  voucherId: string;
  approverId: string;
  action: 'approve' | 'reject';
  comments?: string;
}

/**
 * Interface for approval workflow status
 */
export interface ApprovalWorkflowStatus {
  voucherId: string;
  currentLevel: number;
  status: 'pending' | 'approved' | 'rejected';
  nextApprover?: {
    userId: string;
    name: string;
    role: string;
  };
  approvalHistory: {
    level: number;
    approver: {
      userId: string;
      name: string;
      role: string;
    };
    status: 'approved' | 'rejected';
    date: Date;
    comments?: string;
  }[];
}

/**
 * Service for managing voucher approval workflows
 */
export class VoucherApprovalService {
  
  /**
   * Process approval or rejection for a voucher
   * @param request - Approval request
   * @returns Updated voucher
   */
  async processApproval(request: VoucherApprovalRequest): Promise<IVoucher> {
    try {
      await connectToDatabase();
      logger.info('Processing voucher approval', LogCategory.ACCOUNTING, request);

      // Get voucher with current approval workflow
      const voucher = await Voucher.findById(request.voucherId)
        .populate('approvalWorkflow.currentApprover', 'name email role')
        .populate('createdBy', 'name email');

      if (!voucher) {
        throw new Error(`Voucher with ID ${request.voucherId} not found`);
      }

      // Verify voucher is in pending approval status
      if (voucher.status !== 'pending_approval') {
        throw new Error(`Voucher is not in pending approval status. Current status: ${voucher.status}`);
      }

      // Get approver details
      const approver = await User.findById(request.approverId).select('name email role');
      if (!approver) {
        throw new Error(`Approver with ID ${request.approverId} not found`);
      }

      // Verify approver authorization
      await this.verifyApproverAuthorization(voucher, request.approverId, approver.role);

      // Create approval history entry
      const approvalHistoryEntry = {
        level: voucher.approvalWorkflow?.currentLevel || 1,
        approver: new mongoose.Types.ObjectId(request.approverId),
        status: request.action,
        date: new Date(),
        comments: request.comments,
        notificationSent: false
      };

      // Initialize approval workflow if it doesn't exist
      if (!voucher.approvalWorkflow) {
        voucher.approvalWorkflow = {
          workflowType: 'standard',
          requiredApprovers: [],
          currentLevel: 1,
          approvalHistory: []
        };
      }

      // Add to approval history
      voucher.approvalWorkflow.approvalHistory.push(approvalHistoryEntry);

      if (request.action === 'reject') {
        // Handle rejection
        voucher.status = 'rejected';
        voucher.approvalWorkflow.currentApprover = undefined;
        voucher.updatedBy = new mongoose.Types.ObjectId(request.approverId);

        await voucher.save();

        // Send rejection notification
        await this.sendRejectionNotification(voucher, approver, request.comments);

        logger.info('Voucher rejected', LogCategory.ACCOUNTING, { 
          voucherId: request.voucherId, 
          approverId: request.approverId 
        });

      } else {
        // Handle approval
        const nextApprover = await this.getNextApprover(voucher);

        if (nextApprover) {
          // Move to next approval level
          voucher.approvalWorkflow.currentLevel = nextApprover.level;
          voucher.approvalWorkflow.currentApprover = nextApprover.userId;
          voucher.updatedBy = new mongoose.Types.ObjectId(request.approverId);

          await voucher.save();

          // Send notification to next approver
          await this.sendApprovalRequestNotification(voucher, nextApprover);

          logger.info('Voucher approved - moved to next level', LogCategory.ACCOUNTING, { 
            voucherId: request.voucherId, 
            currentLevel: voucher.approvalWorkflow.currentLevel,
            nextApprover: nextApprover.userId
          });

        } else {
          // All approvals complete
          voucher.status = 'approved';
          voucher.approvalWorkflow.currentApprover = undefined;
          voucher.approvedBy = new mongoose.Types.ObjectId(request.approverId);
          voucher.approvedAt = new Date();
          voucher.updatedBy = new mongoose.Types.ObjectId(request.approverId);

          await voucher.save();

          // Send completion notification
          await this.sendApprovalCompleteNotification(voucher, approver);

          // Trigger post-approval actions
          await this.executePostApprovalActions(voucher);

          logger.info('Voucher fully approved', LogCategory.ACCOUNTING, { 
            voucherId: request.voucherId, 
            finalApprover: request.approverId 
          });
        }
      }

      // Return updated voucher with populated fields
      const updatedVoucher = await Voucher.findById(request.voucherId)
        .populate('createdBy', 'name email role')
        .populate('updatedBy', 'name email role')
        .populate('approvedBy', 'name email role')
        .populate('approvalWorkflow.currentApprover', 'name email role')
        .populate('approvalWorkflow.approvalHistory.approver', 'name email role');

      return updatedVoucher!;

    } catch (error) {
      logger.error('Error processing voucher approval', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get pending approvals for a user
   * @param userId - User ID
   * @param userRole - User role
   * @returns Pending vouchers for approval
   */
  async getPendingApprovals(userId: string, userRole: string) {
    try {
      await connectToDatabase();

      // Build query based on user role and current approval level
      const query: any = {
        status: 'pending_approval'
      };

      // Role-based filtering
      if (userRole === 'HR_MANAGER') {
        query['approvalWorkflow.workflowType'] = 'payroll';
        query['approvalWorkflow.currentLevel'] = 1;
      } else if (userRole === 'FINANCE_MANAGER') {
        query.$or = [
          { 'approvalWorkflow.currentLevel': 1, 'approvalWorkflow.workflowType': 'standard' },
          { 'approvalWorkflow.currentLevel': 2, 'approvalWorkflow.workflowType': 'payroll' }
        ];
      } else if (userRole === 'SUPER_ADMIN') {
        query.$or = [
          { 'approvalWorkflow.currentLevel': 2, 'approvalWorkflow.workflowType': 'standard' },
          { 'approvalWorkflow.currentLevel': 3, 'approvalWorkflow.workflowType': 'payroll' }
        ];
      } else {
        // For other roles, only show vouchers specifically assigned to them
        query['approvalWorkflow.currentApprover'] = new mongoose.Types.ObjectId(userId);
      }

      const vouchers = await Voucher.find(query)
        .populate('createdBy', 'name email')
        .populate('payrollRunId', 'name payPeriod')
        .populate('approvalWorkflow.currentApprover', 'name email role')
        .sort({ createdAt: -1 })
        .lean();

      return vouchers;

    } catch (error) {
      logger.error('Error getting pending approvals', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get approval workflow status for a voucher
   * @param voucherId - Voucher ID
   * @returns Approval workflow status
   */
  async getApprovalWorkflowStatus(voucherId: string): Promise<ApprovalWorkflowStatus> {
    try {
      await connectToDatabase();

      const voucher = await Voucher.findById(voucherId)
        .populate('approvalWorkflow.currentApprover', 'name email role')
        .populate('approvalWorkflow.approvalHistory.approver', 'name email role')
        .lean();

      if (!voucher) {
        throw new Error(`Voucher with ID ${voucherId} not found`);
      }

      const workflow = voucher.approvalWorkflow;
      const status: ApprovalWorkflowStatus = {
        voucherId,
        currentLevel: workflow?.currentLevel || 0,
        status: voucher.status === 'approved' ? 'approved' : 
                voucher.status === 'rejected' ? 'rejected' : 'pending',
        approvalHistory: workflow?.approvalHistory?.map(entry => ({
          level: entry.level,
          approver: {
            userId: entry.approver._id.toString(),
            name: entry.approver.name,
            role: entry.approver.role
          },
          status: entry.status,
          date: entry.date,
          comments: entry.comments
        })) || []
      };

      // Add next approver if pending
      if (workflow?.currentApprover && voucher.status === 'pending_approval') {
        status.nextApprover = {
          userId: workflow.currentApprover._id.toString(),
          name: workflow.currentApprover.name,
          role: workflow.currentApprover.role
        };
      }

      return status;

    } catch (error) {
      logger.error('Error getting approval workflow status', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Verify approver authorization
   * @param voucher - Voucher
   * @param approverId - Approver ID
   * @param approverRole - Approver role
   */
  private async verifyApproverAuthorization(voucher: IVoucher, approverId: string, approverRole: string): Promise<void> {
    const workflow = voucher.approvalWorkflow;
    
    if (!workflow) {
      throw new Error('No approval workflow found for this voucher');
    }

    const currentLevel = workflow.currentLevel;
    const workflowType = workflow.workflowType;

    // Check role-based authorization
    let isAuthorized = false;

    if (workflowType === 'payroll') {
      if (currentLevel === 1 && approverRole === 'HR_MANAGER') isAuthorized = true;
      if (currentLevel === 2 && approverRole === 'FINANCE_MANAGER') isAuthorized = true;
      if (currentLevel === 3 && approverRole === 'SUPER_ADMIN') isAuthorized = true;
    } else {
      if (currentLevel === 1 && approverRole === 'FINANCE_MANAGER') isAuthorized = true;
      if (currentLevel === 2 && approverRole === 'SUPER_ADMIN') isAuthorized = true;
    }

    // Super admin can approve at any level
    if (approverRole === 'SUPER_ADMIN') isAuthorized = true;

    // Check if specifically assigned
    if (workflow.currentApprover?.toString() === approverId) isAuthorized = true;

    if (!isAuthorized) {
      throw new Error(`User is not authorized to approve this voucher at level ${currentLevel}`);
    }
  }

  /**
   * Get next approver in the workflow
   * @param voucher - Voucher
   * @returns Next approver details or null if workflow complete
   */
  private async getNextApprover(voucher: IVoucher): Promise<{ level: number; userId: mongoose.Types.ObjectId } | null> {
    const workflow = voucher.approvalWorkflow;
    if (!workflow) return null;

    const currentLevel = workflow.currentLevel;
    const nextLevel = currentLevel + 1;
    const workflowType = workflow.workflowType;

    // Define approval levels based on workflow type
    if (workflowType === 'payroll') {
      if (nextLevel === 2) {
        // Find Finance Manager
        const financeManager = await User.findOne({ role: 'FINANCE_MANAGER', isActive: true });
        if (financeManager) {
          return { level: nextLevel, userId: financeManager._id };
        }
      } else if (nextLevel === 3 && voucher.totalAmount > 5000000) {
        // Find Super Admin for large amounts
        const superAdmin = await User.findOne({ role: 'SUPER_ADMIN', isActive: true });
        if (superAdmin) {
          return { level: nextLevel, userId: superAdmin._id };
        }
      }
    } else {
      if (nextLevel === 2 && voucher.totalAmount > 5000000) {
        // Find Super Admin for large amounts
        const superAdmin = await User.findOne({ role: 'SUPER_ADMIN', isActive: true });
        if (superAdmin) {
          return { level: nextLevel, userId: superAdmin._id };
        }
      }
    }

    return null; // No more approvers needed
  }

  /**
   * Send approval request notification
   * @param voucher - Voucher
   * @param nextApprover - Next approver details
   */
  private async sendApprovalRequestNotification(voucher: IVoucher, nextApprover: any): Promise<void> {
    try {
      const approverUser = await User.findById(nextApprover.userId);
      if (!approverUser) return;

      const notification = {
        userId: nextApprover.userId,
        type: 'voucher_approval_request',
        title: 'Voucher Approval Required',
        message: `Voucher ${voucher.voucherNumber} requires your approval`,
        data: {
          voucherId: voucher._id,
          voucherNumber: voucher.voucherNumber,
          amount: voucher.totalAmount,
          description: voucher.description,
          approvalLevel: nextApprover.level
        },
        priority: 'high'
      };

      await notificationService.createNotification(notification);

      logger.info('Approval request notification sent', LogCategory.ACCOUNTING, {
        voucherId: voucher._id,
        approverId: nextApprover.userId
      });

    } catch (error) {
      logger.error('Error sending approval request notification', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Send rejection notification
   * @param voucher - Voucher
   * @param approver - Approver details
   * @param comments - Rejection comments
   */
  private async sendRejectionNotification(voucher: IVoucher, approver: any, comments?: string): Promise<void> {
    try {
      const notification = {
        userId: voucher.createdBy._id,
        type: 'voucher_rejected',
        title: 'Voucher Rejected',
        message: `Voucher ${voucher.voucherNumber} has been rejected by ${approver.name}`,
        data: {
          voucherId: voucher._id,
          voucherNumber: voucher.voucherNumber,
          rejectedBy: approver.name,
          comments: comments || 'No comments provided'
        },
        priority: 'high'
      };

      await notificationService.createNotification(notification);

      logger.info('Rejection notification sent', LogCategory.ACCOUNTING, {
        voucherId: voucher._id,
        rejectedBy: approver._id
      });

    } catch (error) {
      logger.error('Error sending rejection notification', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Send approval completion notification
   * @param voucher - Voucher
   * @param finalApprover - Final approver details
   */
  private async sendApprovalCompleteNotification(voucher: IVoucher, finalApprover: any): Promise<void> {
    try {
      const notification = {
        userId: voucher.createdBy._id,
        type: 'voucher_approved',
        title: 'Voucher Approved',
        message: `Voucher ${voucher.voucherNumber} has been fully approved`,
        data: {
          voucherId: voucher._id,
          voucherNumber: voucher.voucherNumber,
          finalApprover: finalApprover.name,
          approvedAt: new Date()
        },
        priority: 'medium'
      };

      await notificationService.createNotification(notification);

      logger.info('Approval completion notification sent', LogCategory.ACCOUNTING, {
        voucherId: voucher._id,
        finalApprover: finalApprover._id
      });

    } catch (error) {
      logger.error('Error sending approval completion notification', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Execute post-approval actions
   * @param voucher - Approved voucher
   */
  private async executePostApprovalActions(voucher: IVoucher): Promise<void> {
    try {
      // If this is a payroll voucher, trigger payroll-voucher integration
      if (voucher.voucherCategory === 'payroll' && voucher.payrollRunId) {
        logger.info('Triggering payroll-voucher integration for approved voucher', LogCategory.ACCOUNTING, {
          voucherId: voucher._id,
          payrollRunId: voucher.payrollRunId
        });

        // Import and trigger integration service
        try {
          const { PayrollVoucherIntegrationService } = await import('../integration/payroll-voucher-integration');
          const integrationService = new PayrollVoucherIntegrationService();
          await integrationService.syncVoucherApprovalToPayroll(voucher._id.toString(), voucher.payrollRunId.toString());
        } catch (error) {
          logger.error('Error triggering payroll-voucher integration', LogCategory.ACCOUNTING, error);
        }
      }

      // Additional post-approval actions can be added here
      // - Update budget allocations
      // - Create accounting entries
      // - Trigger payment processing workflows

    } catch (error) {
      logger.error('Error executing post-approval actions', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Bulk approve vouchers
   * @param voucherIds - Array of voucher IDs
   * @param approverId - Approver ID
   * @param comments - Approval comments
   * @returns Results of bulk approval
   */
  async bulkApprove(voucherIds: string[], approverId: string, comments?: string) {
    try {
      await connectToDatabase();
      logger.info('Processing bulk voucher approval', LogCategory.ACCOUNTING, {
        voucherIds,
        approverId,
        count: voucherIds.length
      });

      const results = {
        successful: [] as string[],
        failed: [] as { voucherId: string; error: string }[]
      };

      for (const voucherId of voucherIds) {
        try {
          await this.processApproval({
            voucherId,
            approverId,
            action: 'approve',
            comments
          });
          results.successful.push(voucherId);
        } catch (error: any) {
          results.failed.push({
            voucherId,
            error: error.message
          });
        }
      }

      logger.info('Bulk approval completed', LogCategory.ACCOUNTING, {
        successful: results.successful.length,
        failed: results.failed.length
      });

      return results;

    } catch (error) {
      logger.error('Error processing bulk approval', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

// Export singleton instance
export const voucherApprovalService = new VoucherApprovalService();

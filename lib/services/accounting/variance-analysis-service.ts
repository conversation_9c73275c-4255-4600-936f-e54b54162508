import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import mongoose from 'mongoose';

// Comprehensive type definitions for variance analysis
export interface VarianceAnalysisRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  conditions: {
    varianceThreshold: number; // Percentage threshold for variance detection
    amountThreshold: number; // Minimum amount to trigger analysis
    timeWindow: number; // Days to consider for analysis
    frequencyThreshold: number; // Number of occurrences to trigger alert
    categories?: string[]; // Specific categories to monitor
    sources?: string[]; // Specific sources to monitor
  };
  actions: {
    generateAlert: boolean;
    requireInvestigation: boolean;
    autoEscalate: boolean;
    notifyStakeholders: boolean;
    severity: 'low' | 'medium' | 'high' | 'critical';
    tags: string[];
  };
}

export interface VarianceItem {
  id: string;
  type: 'budget_variance' | 'trend_variance' | 'seasonal_variance' | 'anomaly_variance' | 'forecast_variance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  category?: string;
  source?: string;
  budgetCategory?: string;
  variance: {
    actual: number;
    expected: number;
    difference: number;
    percentage: number;
    direction: 'positive' | 'negative';
  };
  timeframe: {
    startDate: Date;
    endDate: Date;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  impact: {
    financial: number;
    operational: 'low' | 'medium' | 'high';
    strategic: 'low' | 'medium' | 'high';
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  };
  rootCause?: {
    identified: boolean;
    category: 'external' | 'internal' | 'systematic' | 'operational' | 'unknown';
    description?: string;
    confidence: number; // 0-1 confidence in root cause identification
  };
  recommendations: Array<{
    id: string;
    type: 'immediate' | 'short_term' | 'long_term';
    priority: 'low' | 'medium' | 'high' | 'critical';
    action: string;
    description: string;
    estimatedImpact: number;
    implementationCost: number;
    timeframe: string;
  }>;
  status: 'detected' | 'investigating' | 'resolved' | 'ignored' | 'escalated';
  assignedTo?: string;
  detectedAt: Date;
  detectedBy: string;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: {
    action: string;
    description: string;
    outcome: string;
    lessonsLearned?: string;
  };
  metadata: {
    detectionRule: string;
    confidence: number;
    relatedTransactions: string[];
    historicalContext: Record<string, unknown>;
  };
}

export interface VarianceAnalysisSession {
  id: string;
  name: string;
  description?: string;
  analysisType: 'budget_comparison' | 'trend_analysis' | 'seasonal_analysis' | 'anomaly_detection' | 'comprehensive';
  scope: {
    dateRange: {
      startDate: Date;
      endDate: Date;
    };
    budgetIds?: string[];
    categoryIds?: string[];
    sources?: string[];
    amountRange?: {
      min: number;
      max: number;
    };
  };
  configuration: {
    rules: string[]; // Rule IDs to apply
    thresholds: {
      variancePercentage: number;
      minimumAmount: number;
      confidenceLevel: number;
    };
    includeForecasting: boolean;
    includeSeasonalAdjustment: boolean;
    generateRecommendations: boolean;
  };
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    totalItems: number;
    processedItems: number;
    variancesDetected: number;
    highSeverityVariances: number;
    recommendationsGenerated: number;
  };
  results?: {
    variances: VarianceItem[];
    summary: VarianceAnalysisSummary;
    insights: VarianceInsight[];
  };
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface VarianceAnalysisSummary {
  totalVariances: number;
  severityBreakdown: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  typeBreakdown: {
    budget_variance: number;
    trend_variance: number;
    seasonal_variance: number;
    anomaly_variance: number;
    forecast_variance: number;
  };
  financialImpact: {
    totalVariance: number;
    positiveVariance: number;
    negativeVariance: number;
    netImpact: number;
  };
  topVariances: VarianceItem[];
  trends: {
    improvingVariances: number;
    worseningVariances: number;
    stableVariances: number;
  };
  recommendations: {
    immediate: number;
    shortTerm: number;
    longTerm: number;
    totalEstimatedImpact: number;
  };
}

export interface VarianceInsight {
  id: string;
  type: 'pattern' | 'correlation' | 'prediction' | 'recommendation' | 'alert';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  data: Record<string, unknown>;
  actionable: boolean;
  relatedVariances: string[];
  generatedAt: Date;
}

/**
 * Advanced Variance Analysis Service
 * Provides comprehensive variance detection, analysis, and insights
 */
export class VarianceAnalysisService {

  private static readonly DEFAULT_ANALYSIS_RULES: VarianceAnalysisRule[] = [
    {
      id: 'budget_variance_critical',
      name: 'Critical Budget Variance',
      description: 'Detects critical variances from budget allocations',
      enabled: true,
      priority: 1,
      conditions: {
        varianceThreshold: 0.25, // 25% variance
        amountThreshold: 5000000, // 5M MWK
        timeWindow: 30,
        frequencyThreshold: 1
      },
      actions: {
        generateAlert: true,
        requireInvestigation: true,
        autoEscalate: true,
        notifyStakeholders: true,
        severity: 'critical',
        tags: ['budget', 'critical', 'escalate']
      }
    },
    {
      id: 'trend_variance_high',
      name: 'High Trend Variance',
      description: 'Detects significant deviations from historical trends',
      enabled: true,
      priority: 2,
      conditions: {
        varianceThreshold: 0.20, // 20% variance
        amountThreshold: 2000000, // 2M MWK
        timeWindow: 60,
        frequencyThreshold: 2
      },
      actions: {
        generateAlert: true,
        requireInvestigation: true,
        autoEscalate: false,
        notifyStakeholders: true,
        severity: 'high',
        tags: ['trend', 'high', 'investigate']
      }
    },
    {
      id: 'seasonal_variance_medium',
      name: 'Seasonal Variance',
      description: 'Detects variances from seasonal patterns',
      enabled: true,
      priority: 3,
      conditions: {
        varianceThreshold: 0.15, // 15% variance
        amountThreshold: 1000000, // 1M MWK
        timeWindow: 90,
        frequencyThreshold: 1
      },
      actions: {
        generateAlert: true,
        requireInvestigation: false,
        autoEscalate: false,
        notifyStakeholders: false,
        severity: 'medium',
        tags: ['seasonal', 'medium', 'monitor']
      }
    },
    {
      id: 'anomaly_variance_low',
      name: 'Anomaly Detection',
      description: 'Detects statistical anomalies in income patterns',
      enabled: true,
      priority: 4,
      conditions: {
        varianceThreshold: 0.10, // 10% variance
        amountThreshold: 500000, // 500K MWK
        timeWindow: 30,
        frequencyThreshold: 3
      },
      actions: {
        generateAlert: false,
        requireInvestigation: false,
        autoEscalate: false,
        notifyStakeholders: false,
        severity: 'low',
        tags: ['anomaly', 'low', 'track']
      }
    }
  ];

  /**
   * Start a new variance analysis session
   */
  static async startAnalysisSession(
    sessionData: Omit<VarianceAnalysisSession, 'id' | 'status' | 'progress' | 'createdAt' | 'results'>
  ): Promise<VarianceAnalysisSession> {
    try {
      await connectToDatabase();

      const session: VarianceAnalysisSession = {
        id: new mongoose.Types.ObjectId().toString(),
        ...sessionData,
        status: 'running',
        progress: {
          totalItems: 0,
          processedItems: 0,
          variancesDetected: 0,
          highSeverityVariances: 0,
          recommendationsGenerated: 0
        },
        createdAt: new Date()
      };

      logger.info('Variance analysis session started', LogCategory.ACCOUNTING, {
        sessionId: session.id,
        analysisType: session.analysisType,
        scope: session.scope,
        createdBy: session.createdBy
      });

      return session;
    } catch (error) {
      logger.error('Error starting variance analysis session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Perform comprehensive variance analysis
   */
  static async performVarianceAnalysis(
    sessionId: string,
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      description: string;
      source: string;
      category?: string;
      budgetCategory?: string;
      budgetId?: string;
    }>,
    budgetData?: Array<{
      id: string;
      categoryId: string;
      allocatedAmount: number;
      period: string;
    }>,
    rules: VarianceAnalysisRule[] = this.DEFAULT_ANALYSIS_RULES
  ): Promise<{
    variances: VarianceItem[];
    summary: VarianceAnalysisSummary;
    insights: VarianceInsight[];
  }> {
    try {
      const startTime = Date.now();
      
      // Sort rules by priority
      const sortedRules = rules
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

      const variances: VarianceItem[] = [];
      const insights: VarianceInsight[] = [];

      // Phase 1: Budget variance analysis
      if (budgetData) {
        const budgetVariances = await this.analyzeBudgetVariances(
          incomeData,
          budgetData,
          sortedRules.filter(r => r.id.includes('budget'))
        );
        variances.push(...budgetVariances);
      }

      // Phase 2: Trend variance analysis
      const trendVariances = await this.analyzeTrendVariances(
        incomeData,
        sortedRules.filter(r => r.id.includes('trend'))
      );
      variances.push(...trendVariances);

      // Phase 3: Seasonal variance analysis
      const seasonalVariances = await this.analyzeSeasonalVariances(
        incomeData,
        sortedRules.filter(r => r.id.includes('seasonal'))
      );
      variances.push(...seasonalVariances);

      // Phase 4: Anomaly detection
      const anomalyVariances = await this.detectAnomalies(
        incomeData,
        sortedRules.filter(r => r.id.includes('anomaly'))
      );
      variances.push(...anomalyVariances);

      // Phase 5: Generate insights
      const generatedInsights = await this.generateInsights(variances, incomeData);
      insights.push(...generatedInsights);

      // Phase 6: Generate recommendations for each variance
      for (const variance of variances) {
        variance.recommendations = await this.generateRecommendations(variance, incomeData);
      }

      // Calculate summary
      const summary = this.calculateAnalysisSummary(variances);

      const processingTime = Date.now() - startTime;

      logger.info('Variance analysis completed', LogCategory.ACCOUNTING, {
        sessionId,
        totalVariances: variances.length,
        highSeverityVariances: variances.filter(v => v.severity === 'high' || v.severity === 'critical').length,
        processingTime,
        rulesApplied: sortedRules.length
      });

      return { variances, summary, insights };
    } catch (error) {
      logger.error('Error performing variance analysis', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Analyze budget variances
   */
  private static async analyzeBudgetVariances(
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      budgetCategory?: string;
      budgetId?: string;
    }>,
    budgetData: Array<{
      id: string;
      categoryId: string;
      allocatedAmount: number;
      period: string;
    }>,
    rules: VarianceAnalysisRule[]
  ): Promise<VarianceItem[]> {
    const variances: VarianceItem[] = [];

    // Group income by budget category
    const incomeByCategory = new Map<string, number>();
    incomeData.forEach(income => {
      if (income.budgetCategory) {
        const current = incomeByCategory.get(income.budgetCategory) || 0;
        incomeByCategory.set(income.budgetCategory, current + income.amount);
      }
    });

    // Compare with budget allocations
    for (const budget of budgetData) {
      const actualAmount = incomeByCategory.get(budget.categoryId) || 0;
      const budgetedAmount = budget.allocatedAmount;
      const difference = actualAmount - budgetedAmount;
      const percentage = budgetedAmount > 0 ? Math.abs(difference) / budgetedAmount : 0;

      // Check against rules
      for (const rule of rules) {
        if (percentage >= rule.conditions.varianceThreshold &&
            Math.abs(difference) >= rule.conditions.amountThreshold) {

          const variance: VarianceItem = {
            id: new mongoose.Types.ObjectId().toString(),
            type: 'budget_variance',
            severity: rule.actions.severity,
            title: `Budget Variance: ${budget.categoryId}`,
            description: `Actual income ${difference > 0 ? 'exceeds' : 'falls short of'} budget by ${(percentage * 100).toFixed(1)}%`,
            budgetCategory: budget.categoryId,
            variance: {
              actual: actualAmount,
              expected: budgetedAmount,
              difference,
              percentage: percentage * 100,
              direction: difference > 0 ? 'positive' : 'negative'
            },
            timeframe: {
              startDate: new Date(),
              endDate: new Date(),
              period: 'monthly'
            },
            impact: {
              financial: Math.abs(difference),
              operational: this.calculateOperationalImpact(percentage),
              strategic: this.calculateStrategicImpact(percentage),
              riskLevel: this.calculateRiskLevel(percentage, Math.abs(difference))
            },
            recommendations: [],
            status: 'detected',
            detectedAt: new Date(),
            detectedBy: 'system',
            metadata: {
              detectionRule: rule.id,
              confidence: this.calculateConfidence(percentage, rule.conditions.varianceThreshold),
              relatedTransactions: incomeData
                .filter(i => i.budgetCategory === budget.categoryId)
                .map(i => i.id),
              historicalContext: {}
            }
          };

          variances.push(variance);
        }
      }
    }

    return variances;
  }

  /**
   * Analyze trend variances
   */
  private static async analyzeTrendVariances(
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      source: string;
      category?: string;
    }>,
    rules: VarianceAnalysisRule[]
  ): Promise<VarianceItem[]> {
    const variances: VarianceItem[] = [];

    // Group data by month for trend analysis
    const monthlyData = new Map<string, number>();
    incomeData.forEach(income => {
      const monthKey = income.date.toISOString().substring(0, 7); // YYYY-MM
      const current = monthlyData.get(monthKey) || 0;
      monthlyData.set(monthKey, current + income.amount);
    });

    const sortedMonths = Array.from(monthlyData.entries()).sort();

    if (sortedMonths.length < 3) return variances; // Need at least 3 months for trend analysis

    // Calculate moving average and detect variances
    for (let i = 2; i < sortedMonths.length; i++) {
      const currentAmount = sortedMonths[i][1];
      const previousAmounts = sortedMonths.slice(Math.max(0, i - 2), i).map(([, amount]) => amount);
      const averagePrevious = previousAmounts.reduce((sum, amount) => sum + amount, 0) / previousAmounts.length;

      const difference = currentAmount - averagePrevious;
      const percentage = averagePrevious > 0 ? Math.abs(difference) / averagePrevious : 0;

      // Check against rules
      for (const rule of rules) {
        if (percentage >= rule.conditions.varianceThreshold &&
            Math.abs(difference) >= rule.conditions.amountThreshold) {

          const variance: VarianceItem = {
            id: new mongoose.Types.ObjectId().toString(),
            type: 'trend_variance',
            severity: rule.actions.severity,
            title: `Trend Variance: ${sortedMonths[i][0]}`,
            description: `Income ${difference > 0 ? 'increased' : 'decreased'} by ${(percentage * 100).toFixed(1)}% compared to recent trend`,
            variance: {
              actual: currentAmount,
              expected: averagePrevious,
              difference,
              percentage: percentage * 100,
              direction: difference > 0 ? 'positive' : 'negative'
            },
            timeframe: {
              startDate: new Date(sortedMonths[i][0] + '-01'),
              endDate: new Date(sortedMonths[i][0] + '-01'),
              period: 'monthly'
            },
            impact: {
              financial: Math.abs(difference),
              operational: this.calculateOperationalImpact(percentage),
              strategic: this.calculateStrategicImpact(percentage),
              riskLevel: this.calculateRiskLevel(percentage, Math.abs(difference))
            },
            recommendations: [],
            status: 'detected',
            detectedAt: new Date(),
            detectedBy: 'system',
            metadata: {
              detectionRule: rule.id,
              confidence: this.calculateConfidence(percentage, rule.conditions.varianceThreshold),
              relatedTransactions: incomeData
                .filter(i => i.date.toISOString().substring(0, 7) === sortedMonths[i][0])
                .map(i => i.id),
              historicalContext: {
                previousMonths: previousAmounts,
                trendDirection: difference > 0 ? 'increasing' : 'decreasing'
              }
            }
          };

          variances.push(variance);
        }
      }
    }

    return variances;
  }

  /**
   * Analyze seasonal variances
   */
  private static async analyzeSeasonalVariances(
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      source: string;
      category?: string;
    }>,
    rules: VarianceAnalysisRule[]
  ): Promise<VarianceItem[]> {
    const variances: VarianceItem[] = [];

    // Group data by month across years for seasonal analysis
    const seasonalData = new Map<number, number[]>(); // month -> amounts across years

    incomeData.forEach(income => {
      const month = income.date.getMonth(); // 0-11
      if (!seasonalData.has(month)) {
        seasonalData.set(month, []);
      }
      seasonalData.get(month)!.push(income.amount);
    });

    // Analyze each month for seasonal variances
    for (const [month, amounts] of seasonalData.entries()) {
      if (amounts.length < 2) continue; // Need at least 2 years of data

      const currentYear = new Date().getFullYear();
      const currentYearAmount = incomeData
        .filter(i => i.date.getMonth() === month && i.date.getFullYear() === currentYear)
        .reduce((sum, i) => sum + i.amount, 0);

      const historicalAmounts = amounts.filter((_, index) => index < amounts.length - 1);
      const historicalAverage = historicalAmounts.reduce((sum, amount) => sum + amount, 0) / historicalAmounts.length;

      const difference = currentYearAmount - historicalAverage;
      const percentage = historicalAverage > 0 ? Math.abs(difference) / historicalAverage : 0;

      // Check against rules
      for (const rule of rules) {
        if (percentage >= rule.conditions.varianceThreshold &&
            Math.abs(difference) >= rule.conditions.amountThreshold) {

          const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                             'July', 'August', 'September', 'October', 'November', 'December'];

          const variance: VarianceItem = {
            id: new mongoose.Types.ObjectId().toString(),
            type: 'seasonal_variance',
            severity: rule.actions.severity,
            title: `Seasonal Variance: ${monthNames[month]}`,
            description: `${monthNames[month]} income ${difference > 0 ? 'exceeded' : 'fell short of'} historical seasonal pattern by ${(percentage * 100).toFixed(1)}%`,
            variance: {
              actual: currentYearAmount,
              expected: historicalAverage,
              difference,
              percentage: percentage * 100,
              direction: difference > 0 ? 'positive' : 'negative'
            },
            timeframe: {
              startDate: new Date(currentYear, month, 1),
              endDate: new Date(currentYear, month + 1, 0),
              period: 'monthly'
            },
            impact: {
              financial: Math.abs(difference),
              operational: this.calculateOperationalImpact(percentage),
              strategic: this.calculateStrategicImpact(percentage),
              riskLevel: this.calculateRiskLevel(percentage, Math.abs(difference))
            },
            recommendations: [],
            status: 'detected',
            detectedAt: new Date(),
            detectedBy: 'system',
            metadata: {
              detectionRule: rule.id,
              confidence: this.calculateConfidence(percentage, rule.conditions.varianceThreshold),
              relatedTransactions: incomeData
                .filter(i => i.date.getMonth() === month && i.date.getFullYear() === currentYear)
                .map(i => i.id),
              historicalContext: {
                historicalAmounts,
                seasonalPattern: 'monthly',
                yearsOfData: historicalAmounts.length
              }
            }
          };

          variances.push(variance);
        }
      }
    }

    return variances;
  }

  /**
   * Detect anomalies using statistical methods
   */
  private static async detectAnomalies(
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      source: string;
      category?: string;
    }>,
    rules: VarianceAnalysisRule[]
  ): Promise<VarianceItem[]> {
    const variances: VarianceItem[] = [];

    if (incomeData.length < 10) return variances; // Need sufficient data for anomaly detection

    // Calculate statistical measures
    const amounts = incomeData.map(i => i.amount);
    const mean = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const variance = amounts.reduce((sum, amount) => sum + Math.pow(amount - mean, 2), 0) / amounts.length;
    const stdDev = Math.sqrt(variance);

    // Detect anomalies using z-score
    const threshold = 2.5; // Standard threshold for anomaly detection

    incomeData.forEach(income => {
      const zScore = Math.abs((income.amount - mean) / stdDev);

      if (zScore > threshold) {
        const percentage = Math.abs(income.amount - mean) / mean;

        // Check against rules
        for (const rule of rules) {
          if (percentage >= rule.conditions.varianceThreshold &&
              Math.abs(income.amount - mean) >= rule.conditions.amountThreshold) {

            const variance: VarianceItem = {
              id: new mongoose.Types.ObjectId().toString(),
              type: 'anomaly_variance',
              severity: rule.actions.severity,
              title: `Anomaly Detected: ${income.source}`,
              description: `Transaction amount significantly deviates from normal pattern (z-score: ${zScore.toFixed(2)})`,
              source: income.source,
              category: income.category,
              variance: {
                actual: income.amount,
                expected: mean,
                difference: income.amount - mean,
                percentage: percentage * 100,
                direction: income.amount > mean ? 'positive' : 'negative'
              },
              timeframe: {
                startDate: income.date,
                endDate: income.date,
                period: 'daily'
              },
              impact: {
                financial: Math.abs(income.amount - mean),
                operational: this.calculateOperationalImpact(percentage),
                strategic: this.calculateStrategicImpact(percentage),
                riskLevel: this.calculateRiskLevel(percentage, Math.abs(income.amount - mean))
              },
              recommendations: [],
              status: 'detected',
              detectedAt: new Date(),
              detectedBy: 'system',
              metadata: {
                detectionRule: rule.id,
                confidence: Math.min(zScore / 5, 1), // Normalize z-score to confidence
                relatedTransactions: [income.id],
                historicalContext: {
                  mean,
                  stdDev,
                  zScore,
                  dataPoints: amounts.length
                }
              }
            };

            variances.push(variance);
          }
        }
      }
    });

    return variances;
  }

  /**
   * Generate insights from variance analysis
   */
  private static async generateInsights(
    variances: VarianceItem[],
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      source: string;
      category?: string;
    }>
  ): Promise<VarianceInsight[]> {
    const insights: VarianceInsight[] = [];

    // Pattern insight: Recurring variances
    const variancesByType = new Map<string, VarianceItem[]>();
    variances.forEach(v => {
      const key = `${v.type}_${v.source || v.category || 'unknown'}`;
      if (!variancesByType.has(key)) {
        variancesByType.set(key, []);
      }
      variancesByType.get(key)!.push(v);
    });

    for (const [key, groupVariances] of variancesByType.entries()) {
      if (groupVariances.length >= 3) {
        insights.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'pattern',
          title: 'Recurring Variance Pattern',
          description: `${groupVariances.length} similar variances detected in ${key.split('_')[1]}`,
          confidence: Math.min(groupVariances.length / 5, 1),
          impact: groupVariances.some(v => v.severity === 'high' || v.severity === 'critical') ? 'high' : 'medium',
          category: key.split('_')[1],
          data: {
            varianceCount: groupVariances.length,
            averageImpact: groupVariances.reduce((sum, v) => sum + v.impact.financial, 0) / groupVariances.length,
            pattern: key.split('_')[0]
          },
          actionable: true,
          relatedVariances: groupVariances.map(v => v.id),
          generatedAt: new Date()
        });
      }
    }

    // Correlation insight: High-impact variances
    const highImpactVariances = variances.filter(v => v.impact.financial > 1000000); // > 1M MWK
    if (highImpactVariances.length > 0) {
      insights.push({
        id: new mongoose.Types.ObjectId().toString(),
        type: 'alert',
        title: 'High Financial Impact Variances',
        description: `${highImpactVariances.length} variances with significant financial impact detected`,
        confidence: 1.0,
        impact: 'critical',
        category: 'financial',
        data: {
          totalImpact: highImpactVariances.reduce((sum, v) => sum + v.impact.financial, 0),
          averageImpact: highImpactVariances.reduce((sum, v) => sum + v.impact.financial, 0) / highImpactVariances.length,
          count: highImpactVariances.length
        },
        actionable: true,
        relatedVariances: highImpactVariances.map(v => v.id),
        generatedAt: new Date()
      });
    }

    return insights;
  }

  /**
   * Generate recommendations for a variance
   */
  private static async generateRecommendations(
    variance: VarianceItem,
    incomeData: Array<{
      id: string;
      amount: number;
      date: Date;
      source: string;
      category?: string;
    }>
  ): Promise<VarianceItem['recommendations']> {
    const recommendations: VarianceItem['recommendations'] = [];

    // Generate recommendations based on variance type and severity
    switch (variance.type) {
      case 'budget_variance':
        if (variance.variance.direction === 'negative') {
          recommendations.push({
            id: new mongoose.Types.ObjectId().toString(),
            type: 'immediate',
            priority: variance.severity === 'critical' ? 'critical' : 'high',
            action: 'Investigate Income Shortfall',
            description: 'Analyze reasons for income falling short of budget and implement corrective measures',
            estimatedImpact: Math.abs(variance.variance.difference) * 0.5,
            implementationCost: 50000,
            timeframe: '1-2 weeks'
          });
        } else {
          recommendations.push({
            id: new mongoose.Types.ObjectId().toString(),
            type: 'short_term',
            priority: 'medium',
            action: 'Optimize Budget Allocation',
            description: 'Consider reallocating excess income to other budget categories or strategic initiatives',
            estimatedImpact: Math.abs(variance.variance.difference) * 0.3,
            implementationCost: 25000,
            timeframe: '2-4 weeks'
          });
        }
        break;

      case 'trend_variance':
        recommendations.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'short_term',
          priority: variance.severity === 'high' ? 'high' : 'medium',
          action: 'Trend Analysis and Forecasting',
          description: 'Conduct detailed trend analysis to understand pattern changes and update forecasts',
          estimatedImpact: Math.abs(variance.variance.difference) * 0.4,
          implementationCost: 75000,
          timeframe: '2-3 weeks'
        });
        break;

      case 'seasonal_variance':
        recommendations.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'long_term',
          priority: 'medium',
          action: 'Seasonal Planning Adjustment',
          description: 'Update seasonal planning models and adjust future budget allocations',
          estimatedImpact: Math.abs(variance.variance.difference) * 0.6,
          implementationCost: 100000,
          timeframe: '1-3 months'
        });
        break;

      case 'anomaly_variance':
        recommendations.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'immediate',
          priority: variance.severity === 'critical' ? 'critical' : 'high',
          action: 'Anomaly Investigation',
          description: 'Investigate the root cause of the anomalous transaction and implement controls',
          estimatedImpact: Math.abs(variance.variance.difference) * 0.8,
          implementationCost: 30000,
          timeframe: '3-5 days'
        });
        break;
    }

    return recommendations;
  }

  /**
   * Calculate analysis summary
   */
  private static calculateAnalysisSummary(variances: VarianceItem[]): VarianceAnalysisSummary {
    const severityBreakdown = {
      low: variances.filter(v => v.severity === 'low').length,
      medium: variances.filter(v => v.severity === 'medium').length,
      high: variances.filter(v => v.severity === 'high').length,
      critical: variances.filter(v => v.severity === 'critical').length
    };

    const typeBreakdown = {
      budget_variance: variances.filter(v => v.type === 'budget_variance').length,
      trend_variance: variances.filter(v => v.type === 'trend_variance').length,
      seasonal_variance: variances.filter(v => v.type === 'seasonal_variance').length,
      anomaly_variance: variances.filter(v => v.type === 'anomaly_variance').length,
      forecast_variance: variances.filter(v => v.type === 'forecast_variance').length
    };

    const positiveVariances = variances.filter(v => v.variance.direction === 'positive');
    const negativeVariances = variances.filter(v => v.variance.direction === 'negative');

    const financialImpact = {
      totalVariance: variances.reduce((sum, v) => sum + Math.abs(v.variance.difference), 0),
      positiveVariance: positiveVariances.reduce((sum, v) => sum + v.variance.difference, 0),
      negativeVariance: negativeVariances.reduce((sum, v) => sum + Math.abs(v.variance.difference), 0),
      netImpact: variances.reduce((sum, v) => sum + v.variance.difference, 0)
    };

    const topVariances = variances
      .sort((a, b) => Math.abs(b.variance.difference) - Math.abs(a.variance.difference))
      .slice(0, 5);

    const allRecommendations = variances.flatMap(v => v.recommendations);
    const recommendations = {
      immediate: allRecommendations.filter(r => r.type === 'immediate').length,
      shortTerm: allRecommendations.filter(r => r.type === 'short_term').length,
      longTerm: allRecommendations.filter(r => r.type === 'long_term').length,
      totalEstimatedImpact: allRecommendations.reduce((sum, r) => sum + r.estimatedImpact, 0)
    };

    return {
      totalVariances: variances.length,
      severityBreakdown,
      typeBreakdown,
      financialImpact,
      topVariances,
      trends: {
        improvingVariances: 0, // Would be calculated based on historical data
        worseningVariances: 0,
        stableVariances: 0
      },
      recommendations
    };
  }

  /**
   * Calculate operational impact level
   */
  private static calculateOperationalImpact(percentage: number): 'low' | 'medium' | 'high' {
    if (percentage > 0.3) return 'high';
    if (percentage > 0.15) return 'medium';
    return 'low';
  }

  /**
   * Calculate strategic impact level
   */
  private static calculateStrategicImpact(percentage: number): 'low' | 'medium' | 'high' {
    if (percentage > 0.25) return 'high';
    if (percentage > 0.10) return 'medium';
    return 'low';
  }

  /**
   * Calculate risk level
   */
  private static calculateRiskLevel(percentage: number, amount: number): 'low' | 'medium' | 'high' | 'critical' {
    if (percentage > 0.3 || amount > 10000000) return 'critical';
    if (percentage > 0.2 || amount > 5000000) return 'high';
    if (percentage > 0.1 || amount > 1000000) return 'medium';
    return 'low';
  }

  /**
   * Calculate confidence score
   */
  private static calculateConfidence(actualPercentage: number, threshold: number): number {
    return Math.min(actualPercentage / threshold, 1);
  }

  /**
   * Get variance analysis session by ID
   */
  static async getAnalysisSession(sessionId: string): Promise<VarianceAnalysisSession | null> {
    try {
      // Implementation would fetch from database
      return null;
    } catch (error) {
      logger.error('Error getting analysis session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update variance status
   */
  static async updateVarianceStatus(
    varianceId: string,
    status: VarianceItem['status'],
    updatedBy: string,
    notes?: string
  ): Promise<VarianceItem> {
    try {
      const updatedVariance: VarianceItem = {
        id: varianceId,
        status,
        assignedTo: status === 'investigating' ? updatedBy : undefined,
        resolvedAt: status === 'resolved' ? new Date() : undefined,
        resolvedBy: status === 'resolved' ? updatedBy : undefined
      } as VarianceItem;

      logger.info('Variance status updated', LogCategory.ACCOUNTING, {
        varianceId,
        status,
        updatedBy
      });

      return updatedVariance;
    } catch (error) {
      logger.error('Error updating variance status', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Export variance analysis results
   */
  static async exportAnalysisResults(
    sessionId: string,
    format: 'csv' | 'excel' | 'pdf' | 'json'
  ): Promise<{
    data: Buffer | string;
    filename: string;
    mimeType: string;
  }> {
    try {
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `variance_analysis_${sessionId}_${timestamp}.${format}`;

      let data: Buffer | string;
      let mimeType: string;

      switch (format) {
        case 'csv':
          data = 'CSV data would be generated here';
          mimeType = 'text/csv';
          break;
        case 'excel':
          data = Buffer.from('Excel data would be generated here');
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'pdf':
          data = Buffer.from('PDF data would be generated here');
          mimeType = 'application/pdf';
          break;
        case 'json':
          data = JSON.stringify({ message: 'JSON data would be generated here' });
          mimeType = 'application/json';
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      logger.info('Variance analysis results exported', LogCategory.ACCOUNTING, {
        sessionId,
        format,
        filename
      });

      return { data, filename, mimeType };
    } catch (error) {
      logger.error('Error exporting analysis results', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

export const varianceAnalysisService = VarianceAnalysisService;

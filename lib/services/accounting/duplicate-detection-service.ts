import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Income from '@/models/accounting/Income';
import mongoose from 'mongoose';

// Comprehensive type definitions for duplicate detection
export interface DuplicateDetectionRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  criteria: {
    amountTolerance: number; // Percentage tolerance (0-1)
    dateTolerance: number; // Days tolerance
    descriptionSimilarity: number; // Minimum similarity (0-1)
    referenceMatch: boolean; // Require reference number match
    sourceMatch: boolean; // Require same source
    categoryMatch: boolean; // Require same category
    customFields?: Array<{
      field: string;
      weight: number; // Importance weight (0-1)
      tolerance?: number;
    }>;
  };
  actions: {
    autoMerge: boolean;
    requireManualReview: boolean;
    confidence: number; // Minimum confidence for action
    notifyUsers: boolean;
    tags: string[];
  };
}

export interface DuplicateGroup {
  id: string;
  transactions: DuplicateTransaction[];
  confidence: number;
  detectionRule: string;
  status: 'detected' | 'reviewing' | 'merged' | 'ignored' | 'resolved';
  primaryTransaction?: string; // ID of the transaction to keep
  mergeStrategy?: 'keep_first' | 'keep_latest' | 'keep_highest_amount' | 'manual';
  detectedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  resolution?: {
    action: 'merge' | 'ignore' | 'split' | 'manual_adjustment';
    reason: string;
    notes?: string;
    resolvedBy: string;
    resolvedAt: Date;
  };
  metadata: {
    totalAmount: number;
    dateRange: {
      earliest: Date;
      latest: Date;
    };
    sources: string[];
    categories: string[];
  };
}

export interface DuplicateTransaction {
  id: string;
  incomeId: string;
  amount: number;
  date: Date;
  description: string;
  reference?: string;
  source: string;
  category?: string;
  budgetCategory?: string;
  createdAt: Date;
  createdBy: string;
  metadata?: Record<string, unknown>;
  duplicateScore?: number; // Similarity score within group
  isPrimary?: boolean; // Whether this is the primary transaction in group
}

export interface DuplicateDetectionSession {
  id: string;
  name: string;
  description?: string;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  filters: {
    sources?: string[];
    categories?: string[];
    amountRange?: {
      min: number;
      max: number;
    };
    budgetIds?: string[];
  };
  rules: string[]; // Rule IDs to apply
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    totalTransactions: number;
    processedTransactions: number;
    duplicateGroups: number;
    autoMerged: number;
    requiresReview: number;
  };
  results?: {
    duplicateGroups: DuplicateGroup[];
    statistics: DuplicateDetectionStatistics;
  };
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface DuplicateDetectionStatistics {
  totalTransactions: number;
  duplicateTransactions: number;
  duplicateGroups: number;
  autoMerged: number;
  manualReview: number;
  ignored: number;
  averageConfidence: number;
  potentialSavings: number; // Amount that could be saved by removing duplicates
  processingTime: number;
  rulesApplied: number;
}

/**
 * Advanced Duplicate Detection Service
 * Provides sophisticated algorithms for detecting and managing duplicate income transactions
 */
export class DuplicateDetectionService {

  private static readonly DEFAULT_DETECTION_RULES: DuplicateDetectionRule[] = [
    {
      id: 'exact_duplicate',
      name: 'Exact Duplicate',
      description: 'Transactions with identical amount, date, and reference',
      enabled: true,
      priority: 1,
      criteria: {
        amountTolerance: 0,
        dateTolerance: 0,
        descriptionSimilarity: 1.0,
        referenceMatch: true,
        sourceMatch: false,
        categoryMatch: false
      },
      actions: {
        autoMerge: true,
        requireManualReview: false,
        confidence: 1.0,
        notifyUsers: true,
        tags: ['exact_duplicate', 'auto_merge']
      }
    },
    {
      id: 'near_duplicate',
      name: 'Near Duplicate',
      description: 'Transactions with very similar characteristics',
      enabled: true,
      priority: 2,
      criteria: {
        amountTolerance: 0.01, // 1% tolerance
        dateTolerance: 1, // 1 day tolerance
        descriptionSimilarity: 0.9,
        referenceMatch: false,
        sourceMatch: true,
        categoryMatch: true
      },
      actions: {
        autoMerge: false,
        requireManualReview: true,
        confidence: 0.9,
        notifyUsers: true,
        tags: ['near_duplicate', 'manual_review']
      }
    },
    {
      id: 'amount_date_duplicate',
      name: 'Amount and Date Duplicate',
      description: 'Same amount and date with different descriptions',
      enabled: true,
      priority: 3,
      criteria: {
        amountTolerance: 0,
        dateTolerance: 0,
        descriptionSimilarity: 0.5,
        referenceMatch: false,
        sourceMatch: false,
        categoryMatch: true
      },
      actions: {
        autoMerge: false,
        requireManualReview: true,
        confidence: 0.8,
        notifyUsers: true,
        tags: ['amount_date_duplicate', 'manual_review']
      }
    },
    {
      id: 'fuzzy_duplicate',
      name: 'Fuzzy Duplicate',
      description: 'Potentially duplicate transactions requiring review',
      enabled: true,
      priority: 4,
      criteria: {
        amountTolerance: 0.05, // 5% tolerance
        dateTolerance: 3, // 3 days tolerance
        descriptionSimilarity: 0.7,
        referenceMatch: false,
        sourceMatch: false,
        categoryMatch: false
      },
      actions: {
        autoMerge: false,
        requireManualReview: true,
        confidence: 0.6,
        notifyUsers: false,
        tags: ['fuzzy_duplicate', 'low_confidence']
      }
    }
  ];

  /**
   * Start a new duplicate detection session
   */
  static async startDetectionSession(
    sessionData: Omit<DuplicateDetectionSession, 'id' | 'status' | 'progress' | 'createdAt' | 'results'>
  ): Promise<DuplicateDetectionSession> {
    try {
      await connectToDatabase();

      const session: DuplicateDetectionSession = {
        id: new mongoose.Types.ObjectId().toString(),
        ...sessionData,
        status: 'running',
        progress: {
          totalTransactions: 0,
          processedTransactions: 0,
          duplicateGroups: 0,
          autoMerged: 0,
          requiresReview: 0
        },
        createdAt: new Date()
      };

      logger.info('Duplicate detection session started', LogCategory.ACCOUNTING, {
        sessionId: session.id,
        dateRange: session.dateRange,
        filters: session.filters,
        rules: session.rules,
        createdBy: session.createdBy
      });

      return session;
    } catch (error) {
      logger.error('Error starting duplicate detection session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Perform comprehensive duplicate detection
   */
  static async detectDuplicates(
    sessionId: string,
    transactions: DuplicateTransaction[],
    rules: DuplicateDetectionRule[] = this.DEFAULT_DETECTION_RULES
  ): Promise<{
    duplicateGroups: DuplicateGroup[];
    statistics: DuplicateDetectionStatistics;
  }> {
    try {
      const startTime = Date.now();
      
      // Sort rules by priority
      const sortedRules = rules
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

      const duplicateGroups: DuplicateGroup[] = [];
      const processedTransactions = new Set<string>();
      let autoMerged = 0;
      let manualReview = 0;

      // Apply each rule in priority order
      for (const rule of sortedRules) {
        const ruleGroups = await this.applyDetectionRule(
          transactions.filter(t => !processedTransactions.has(t.id)),
          rule
        );

        for (const group of ruleGroups) {
          duplicateGroups.push(group);
          
          // Mark transactions as processed
          group.transactions.forEach(t => processedTransactions.add(t.id));
          
          // Update counters
          if (group.status === 'merged') {
            autoMerged++;
          } else if (group.status === 'reviewing') {
            manualReview++;
          }
        }
      }

      // Calculate statistics
      const duplicateTransactionCount = duplicateGroups.reduce(
        (sum, group) => sum + group.transactions.length, 0
      );
      
      const totalConfidence = duplicateGroups.reduce(
        (sum, group) => sum + group.confidence, 0
      );
      
      const averageConfidence = duplicateGroups.length > 0 
        ? totalConfidence / duplicateGroups.length 
        : 0;

      const potentialSavings = duplicateGroups.reduce(
        (sum, group) => sum + (group.metadata.totalAmount * (group.transactions.length - 1)), 0
      );

      const statistics: DuplicateDetectionStatistics = {
        totalTransactions: transactions.length,
        duplicateTransactions: duplicateTransactionCount,
        duplicateGroups: duplicateGroups.length,
        autoMerged,
        manualReview,
        ignored: 0,
        averageConfidence,
        potentialSavings,
        processingTime: Date.now() - startTime,
        rulesApplied: sortedRules.length
      };

      logger.info('Duplicate detection completed', LogCategory.ACCOUNTING, {
        sessionId,
        statistics,
        rulesApplied: sortedRules.length
      });

      return { duplicateGroups, statistics };
    } catch (error) {
      logger.error('Error detecting duplicates', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Apply a specific detection rule to find duplicate groups
   */
  private static async applyDetectionRule(
    transactions: DuplicateTransaction[],
    rule: DuplicateDetectionRule
  ): Promise<DuplicateGroup[]> {
    const groups: DuplicateGroup[] = [];
    const processed = new Set<string>();

    for (let i = 0; i < transactions.length; i++) {
      if (processed.has(transactions[i].id)) continue;

      const duplicates = [transactions[i]];
      processed.add(transactions[i].id);

      // Find all duplicates for this transaction
      for (let j = i + 1; j < transactions.length; j++) {
        if (processed.has(transactions[j].id)) continue;

        const similarity = this.calculateSimilarity(transactions[i], transactions[j], rule);
        
        if (similarity.isDuplicate && similarity.confidence >= rule.actions.confidence) {
          duplicates.push({
            ...transactions[j],
            duplicateScore: similarity.confidence
          });
          processed.add(transactions[j].id);
        }
      }

      // Create group if duplicates found
      if (duplicates.length > 1) {
        const group = this.createDuplicateGroup(duplicates, rule);
        groups.push(group);
      }
    }

    return groups;
  }

  /**
   * Calculate similarity between two transactions based on rule criteria
   */
  private static calculateSimilarity(
    t1: DuplicateTransaction,
    t2: DuplicateTransaction,
    rule: DuplicateDetectionRule
  ): {
    isDuplicate: boolean;
    confidence: number;
    factors: {
      amount: number;
      date: number;
      description: number;
      reference: number;
      source: number;
      category: number;
    };
  } {
    const factors = {
      amount: 0,
      date: 0,
      description: 0,
      reference: 0,
      source: 0,
      category: 0
    };

    // Amount similarity
    const amountDiff = Math.abs(t1.amount - t2.amount);
    const maxAmount = Math.max(t1.amount, t2.amount);
    const amountPercentDiff = maxAmount > 0 ? amountDiff / maxAmount : 0;
    factors.amount = amountPercentDiff <= rule.criteria.amountTolerance 
      ? 1 - amountPercentDiff / rule.criteria.amountTolerance 
      : 0;

    // Date similarity
    const daysDiff = Math.abs(
      (t1.date.getTime() - t2.date.getTime()) / (1000 * 60 * 60 * 24)
    );
    factors.date = daysDiff <= rule.criteria.dateTolerance 
      ? 1 - daysDiff / rule.criteria.dateTolerance 
      : 0;

    // Description similarity
    factors.description = this.calculateStringSimilarity(t1.description, t2.description);

    // Reference similarity
    if (rule.criteria.referenceMatch) {
      factors.reference = t1.reference === t2.reference ? 1 : 0;
    } else {
      factors.reference = t1.reference && t2.reference 
        ? this.calculateStringSimilarity(t1.reference, t2.reference)
        : 1; // Don't penalize if references are missing
    }

    // Source similarity
    factors.source = rule.criteria.sourceMatch 
      ? (t1.source === t2.source ? 1 : 0)
      : 1;

    // Category similarity
    factors.category = rule.criteria.categoryMatch 
      ? (t1.category === t2.category ? 1 : 0)
      : 1;

    // Calculate weighted confidence
    const weights = {
      amount: 0.3,
      date: 0.2,
      description: 0.2,
      reference: 0.1,
      source: 0.1,
      category: 0.1
    };

    const confidence = 
      factors.amount * weights.amount +
      factors.date * weights.date +
      factors.description * weights.description +
      factors.reference * weights.reference +
      factors.source * weights.source +
      factors.category * weights.category;

    const isDuplicate = confidence >= rule.actions.confidence &&
      factors.description >= rule.criteria.descriptionSimilarity;

    return { isDuplicate, confidence, factors };
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    
    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();
    
    if (s1 === s2) return 1;
    
    const matrix: number[][] = [];
    const len1 = s1.length;
    const len2 = s2.length;
    
    // Initialize matrix
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }
    
    // Fill matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }
    
    const maxLength = Math.max(len1, len2);
    return maxLength > 0 ? 1 - matrix[len1][len2] / maxLength : 0;
  }

  /**
   * Create a duplicate group from a set of duplicate transactions
   */
  private static createDuplicateGroup(
    transactions: DuplicateTransaction[],
    rule: DuplicateDetectionRule
  ): DuplicateGroup {
    // Calculate metadata
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const dates = transactions.map(t => t.date);
    const earliest = new Date(Math.min(...dates.map(d => d.getTime())));
    const latest = new Date(Math.max(...dates.map(d => d.getTime())));
    const sources = [...new Set(transactions.map(t => t.source))];
    const categories = [...new Set(transactions.map(t => t.category).filter(Boolean))];

    // Determine primary transaction (keep first by default)
    const primaryTransaction = transactions[0].id;
    transactions[0].isPrimary = true;

    // Calculate group confidence (average of individual scores)
    const confidence = transactions.reduce((sum, t) => sum + (t.duplicateScore || 1), 0) / transactions.length;

    const group: DuplicateGroup = {
      id: new mongoose.Types.ObjectId().toString(),
      transactions,
      confidence,
      detectionRule: rule.id,
      status: rule.actions.autoMerge ? 'merged' : 'reviewing',
      primaryTransaction,
      mergeStrategy: 'keep_first',
      detectedAt: new Date(),
      metadata: {
        totalAmount,
        dateRange: { earliest, latest },
        sources,
        categories
      }
    };

    return group;
  }

  /**
   * Merge duplicate transactions in a group
   */
  static async mergeDuplicateGroup(
    groupId: string,
    mergeStrategy: DuplicateGroup['mergeStrategy'] = 'keep_first',
    mergedBy: string,
    notes?: string
  ): Promise<DuplicateGroup> {
    try {
      // Implementation would:
      // 1. Get the duplicate group from database
      // 2. Apply merge strategy to determine primary transaction
      // 3. Update/delete transactions as needed
      // 4. Update group status and resolution

      const updatedGroup: DuplicateGroup = {
        id: groupId,
        status: 'merged',
        mergeStrategy,
        resolution: {
          action: 'merge',
          reason: `Merged using ${mergeStrategy} strategy`,
          notes,
          resolvedBy: mergedBy,
          resolvedAt: new Date()
        }
      } as DuplicateGroup;

      logger.info('Duplicate group merged', LogCategory.ACCOUNTING, {
        groupId,
        mergeStrategy,
        mergedBy
      });

      return updatedGroup;
    } catch (error) {
      logger.error('Error merging duplicate group', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Ignore a duplicate group (mark as not duplicate)
   */
  static async ignoreDuplicateGroup(
    groupId: string,
    reason: string,
    ignoredBy: string,
    notes?: string
  ): Promise<DuplicateGroup> {
    try {
      const updatedGroup: DuplicateGroup = {
        id: groupId,
        status: 'ignored',
        resolution: {
          action: 'ignore',
          reason,
          notes,
          resolvedBy: ignoredBy,
          resolvedAt: new Date()
        }
      } as DuplicateGroup;

      logger.info('Duplicate group ignored', LogCategory.ACCOUNTING, {
        groupId,
        reason,
        ignoredBy
      });

      return updatedGroup;
    } catch (error) {
      logger.error('Error ignoring duplicate group', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Split a duplicate group (remove specific transactions)
   */
  static async splitDuplicateGroup(
    groupId: string,
    transactionsToRemove: string[],
    splitBy: string,
    notes?: string
  ): Promise<DuplicateGroup> {
    try {
      const updatedGroup: DuplicateGroup = {
        id: groupId,
        status: 'resolved',
        resolution: {
          action: 'split',
          reason: `Split group by removing ${transactionsToRemove.length} transactions`,
          notes,
          resolvedBy: splitBy,
          resolvedAt: new Date()
        }
      } as DuplicateGroup;

      logger.info('Duplicate group split', LogCategory.ACCOUNTING, {
        groupId,
        transactionsRemoved: transactionsToRemove.length,
        splitBy
      });

      return updatedGroup;
    } catch (error) {
      logger.error('Error splitting duplicate group', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get duplicate detection session by ID
   */
  static async getDetectionSession(sessionId: string): Promise<DuplicateDetectionSession | null> {
    try {
      // Implementation would fetch from database
      return null;
    } catch (error) {
      logger.error('Error getting detection session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get duplicate groups for review
   */
  static async getDuplicateGroupsForReview(
    filters?: {
      status?: DuplicateGroup['status'][];
      confidenceRange?: { min: number; max: number };
      amountRange?: { min: number; max: number };
      dateRange?: { startDate: Date; endDate: Date };
      sources?: string[];
      categories?: string[];
    }
  ): Promise<DuplicateGroup[]> {
    try {
      // Implementation would query database with filters
      return [];
    } catch (error) {
      logger.error('Error getting duplicate groups for review', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get duplicate detection statistics
   */
  static async getDetectionStatistics(
    dateRange: { startDate: Date; endDate: Date },
    filters?: {
      sources?: string[];
      categories?: string[];
    }
  ): Promise<{
    totalSessions: number;
    completedSessions: number;
    totalDuplicateGroups: number;
    autoMerged: number;
    manualReview: number;
    ignored: number;
    resolved: number;
    potentialSavings: number;
    averageConfidence: number;
    topDuplicateSources: Array<{ source: string; count: number }>;
    topDuplicateCategories: Array<{ category: string; count: number }>;
  }> {
    try {
      // Implementation would aggregate data from database
      const stats = {
        totalSessions: 0,
        completedSessions: 0,
        totalDuplicateGroups: 0,
        autoMerged: 0,
        manualReview: 0,
        ignored: 0,
        resolved: 0,
        potentialSavings: 0,
        averageConfidence: 0,
        topDuplicateSources: [],
        topDuplicateCategories: []
      };

      return stats;
    } catch (error) {
      logger.error('Error getting detection statistics', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Export duplicate detection results
   */
  static async exportDetectionResults(
    sessionId: string,
    format: 'csv' | 'excel' | 'pdf' | 'json',
    includeResolved = false
  ): Promise<{
    data: Buffer | string;
    filename: string;
    mimeType: string;
  }> {
    try {
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `duplicate_detection_${sessionId}_${timestamp}.${format}`;

      let data: Buffer | string;
      let mimeType: string;

      switch (format) {
        case 'csv':
          data = 'CSV data would be generated here';
          mimeType = 'text/csv';
          break;
        case 'excel':
          data = Buffer.from('Excel data would be generated here');
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'pdf':
          data = Buffer.from('PDF data would be generated here');
          mimeType = 'application/pdf';
          break;
        case 'json':
          data = JSON.stringify({ message: 'JSON data would be generated here' });
          mimeType = 'application/json';
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      logger.info('Duplicate detection results exported', LogCategory.ACCOUNTING, {
        sessionId,
        format,
        filename,
        includeResolved
      });

      return { data, filename, mimeType };
    } catch (error) {
      logger.error('Error exporting detection results', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Bulk resolve duplicate groups
   */
  static async bulkResolveDuplicateGroups(
    groupIds: string[],
    action: 'merge' | 'ignore' | 'split',
    resolvedBy: string,
    options?: {
      mergeStrategy?: DuplicateGroup['mergeStrategy'];
      reason?: string;
      notes?: string;
    }
  ): Promise<{
    resolved: number;
    failed: number;
    errors: Array<{ groupId: string; error: string }>;
  }> {
    try {
      let resolved = 0;
      let failed = 0;
      const errors: Array<{ groupId: string; error: string }> = [];

      for (const groupId of groupIds) {
        try {
          switch (action) {
            case 'merge':
              await this.mergeDuplicateGroup(
                groupId,
                options?.mergeStrategy,
                resolvedBy,
                options?.notes
              );
              break;
            case 'ignore':
              await this.ignoreDuplicateGroup(
                groupId,
                options?.reason || 'Bulk ignore operation',
                resolvedBy,
                options?.notes
              );
              break;
            case 'split':
              // Split operation would need additional parameters
              throw new Error('Split operation not supported in bulk mode');
          }
          resolved++;
        } catch (error) {
          failed++;
          errors.push({
            groupId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      logger.info('Bulk duplicate resolution completed', LogCategory.ACCOUNTING, {
        action,
        totalGroups: groupIds.length,
        resolved,
        failed,
        resolvedBy
      });

      return { resolved, failed, errors };
    } catch (error) {
      logger.error('Error in bulk duplicate resolution', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

export const duplicateDetectionService = DuplicateDetectionService;

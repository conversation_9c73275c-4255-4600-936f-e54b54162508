import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Income from '@/models/accounting/Income';
import mongoose from 'mongoose';

// Comprehensive type definitions for reconciliation
export interface ReconciliationTransaction {
  id: string;
  source: 'internal' | 'bank' | 'government' | 'external';
  amount: number;
  date: Date;
  description: string;
  reference?: string;
  accountNumber?: string;
  payerName?: string;
  category?: string;
  metadata?: Record<string, unknown>;
  originalData?: Record<string, unknown>;
}

export interface MatchingRule {
  id: string;
  name: string;
  priority: number;
  enabled: boolean;
  conditions: {
    amountTolerance?: number; // Percentage tolerance for amount matching
    dateTolerance?: number; // Days tolerance for date matching
    descriptionSimilarity?: number; // Minimum similarity score (0-1)
    requireExactReference?: boolean;
    requireExactAmount?: boolean;
    customFields?: Array<{
      field: string;
      operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
      value: string;
      caseSensitive?: boolean;
    }>;
  };
  actions: {
    autoMatch?: boolean;
    requireManualReview?: boolean;
    confidence?: number; // Minimum confidence score for auto-matching
    tags?: string[];
  };
}

export interface ReconciliationMatch {
  id: string;
  internalTransaction: ReconciliationTransaction;
  externalTransaction: ReconciliationTransaction;
  matchType: 'exact' | 'fuzzy' | 'manual' | 'rule-based';
  confidence: number; // 0-1 confidence score
  matchingRule?: string;
  matchingFactors: {
    amountMatch: number;
    dateMatch: number;
    descriptionMatch: number;
    referenceMatch: number;
    overallScore: number;
  };
  status: 'pending' | 'approved' | 'rejected' | 'auto-matched';
  reviewedBy?: string;
  reviewedAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReconciliationVariance {
  id: string;
  type: 'missing_internal' | 'missing_external' | 'amount_difference' | 'date_difference' | 'duplicate';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  internalTransaction?: ReconciliationTransaction;
  externalTransaction?: ReconciliationTransaction;
  variance: {
    amountDifference?: number;
    dateDifference?: number; // Days
    description?: string;
  };
  resolution?: {
    action: 'ignore' | 'adjust' | 'investigate' | 'manual_entry';
    reason: string;
    resolvedBy: string;
    resolvedAt: Date;
  };
  createdAt: Date;
}

export interface ReconciliationSession {
  id: string;
  name: string;
  description?: string;
  source: string;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  status: 'in_progress' | 'completed' | 'failed' | 'cancelled';
  statistics: {
    totalInternal: number;
    totalExternal: number;
    matched: number;
    unmatched: number;
    variances: number;
    autoMatched: number;
    manualMatched: number;
    confidence: number;
  };
  settings: {
    matchingRules: string[];
    autoApproveThreshold: number;
    requireManualReview: boolean;
    enableDuplicateDetection: boolean;
  };
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
  results?: {
    matches: ReconciliationMatch[];
    variances: ReconciliationVariance[];
    summary: Record<string, unknown>;
  };
}

/**
 * Advanced Income Reconciliation Service
 * Provides sophisticated matching algorithms and variance analysis
 */
export class IncomeReconciliationService {
  
  private static readonly DEFAULT_MATCHING_RULES: MatchingRule[] = [
    {
      id: 'exact_match',
      name: 'Exact Match',
      priority: 1,
      enabled: true,
      conditions: {
        amountTolerance: 0,
        dateTolerance: 0,
        requireExactReference: true,
        requireExactAmount: true
      },
      actions: {
        autoMatch: true,
        confidence: 1.0
      }
    },
    {
      id: 'amount_date_match',
      name: 'Amount and Date Match',
      priority: 2,
      enabled: true,
      conditions: {
        amountTolerance: 0.01, // 1% tolerance
        dateTolerance: 1, // 1 day tolerance
        requireExactAmount: false
      },
      actions: {
        autoMatch: true,
        confidence: 0.95
      }
    },
    {
      id: 'fuzzy_description_match',
      name: 'Fuzzy Description Match',
      priority: 3,
      enabled: true,
      conditions: {
        amountTolerance: 0.05, // 5% tolerance
        dateTolerance: 3, // 3 days tolerance
        descriptionSimilarity: 0.8 // 80% similarity
      },
      actions: {
        autoMatch: false,
        requireManualReview: true,
        confidence: 0.75
      }
    },
    {
      id: 'reference_match',
      name: 'Reference Number Match',
      priority: 4,
      enabled: true,
      conditions: {
        requireExactReference: true,
        amountTolerance: 0.1, // 10% tolerance
        dateTolerance: 7 // 7 days tolerance
      },
      actions: {
        autoMatch: false,
        requireManualReview: true,
        confidence: 0.85
      }
    }
  ];

  /**
   * Start a new reconciliation session
   */
  static async startReconciliationSession(
    sessionData: Omit<ReconciliationSession, 'id' | 'status' | 'statistics' | 'createdAt' | 'results'>
  ): Promise<ReconciliationSession> {
    try {
      await connectToDatabase();

      const session: ReconciliationSession = {
        id: new mongoose.Types.ObjectId().toString(),
        ...sessionData,
        status: 'in_progress',
        statistics: {
          totalInternal: 0,
          totalExternal: 0,
          matched: 0,
          unmatched: 0,
          variances: 0,
          autoMatched: 0,
          manualMatched: 0,
          confidence: 0
        },
        createdAt: new Date()
      };

      logger.info('Reconciliation session started', LogCategory.ACCOUNTING, {
        sessionId: session.id,
        source: session.source,
        dateRange: session.dateRange,
        createdBy: session.createdBy
      });

      return session;
    } catch (error) {
      logger.error('Error starting reconciliation session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Perform comprehensive reconciliation with advanced matching
   */
  static async performReconciliation(
    sessionId: string,
    internalTransactions: ReconciliationTransaction[],
    externalTransactions: ReconciliationTransaction[],
    matchingRules: MatchingRule[] = this.DEFAULT_MATCHING_RULES
  ): Promise<{
    matches: ReconciliationMatch[];
    variances: ReconciliationVariance[];
    statistics: ReconciliationSession['statistics'];
  }> {
    try {
      const startTime = Date.now();
      
      // Sort rules by priority
      const sortedRules = matchingRules
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

      const matches: ReconciliationMatch[] = [];
      const variances: ReconciliationVariance[] = [];
      const matchedInternalIds = new Set<string>();
      const matchedExternalIds = new Set<string>();

      // Phase 1: Apply matching rules in priority order
      for (const rule of sortedRules) {
        const ruleMatches = await this.applyMatchingRule(
          internalTransactions.filter(t => !matchedInternalIds.has(t.id)),
          externalTransactions.filter(t => !matchedExternalIds.has(t.id)),
          rule
        );

        for (const match of ruleMatches) {
          matches.push(match);
          matchedInternalIds.add(match.internalTransaction.id);
          matchedExternalIds.add(match.externalTransaction.id);
        }
      }

      // Phase 2: Identify variances for unmatched transactions
      const unmatchedInternal = internalTransactions.filter(t => !matchedInternalIds.has(t.id));
      const unmatchedExternal = externalTransactions.filter(t => !matchedExternalIds.has(t.id));

      // Missing external transactions
      for (const internal of unmatchedInternal) {
        variances.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'missing_external',
          severity: this.calculateVarianceSeverity(internal.amount),
          description: `Internal transaction ${internal.reference || internal.id} has no matching external transaction`,
          internalTransaction: internal,
          variance: {
            description: 'No matching external transaction found'
          },
          createdAt: new Date()
        });
      }

      // Missing internal transactions
      for (const external of unmatchedExternal) {
        variances.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'missing_internal',
          severity: this.calculateVarianceSeverity(external.amount),
          description: `External transaction ${external.reference || external.id} has no matching internal transaction`,
          externalTransaction: external,
          variance: {
            description: 'No matching internal transaction found'
          },
          createdAt: new Date()
        });
      }

      // Phase 3: Detect duplicates within each source
      const internalDuplicates = this.detectDuplicates(internalTransactions);
      const externalDuplicates = this.detectDuplicates(externalTransactions);

      for (const duplicateGroup of [...internalDuplicates, ...externalDuplicates]) {
        variances.push({
          id: new mongoose.Types.ObjectId().toString(),
          type: 'duplicate',
          severity: 'medium',
          description: `Duplicate transactions detected: ${duplicateGroup.map(t => t.id).join(', ')}`,
          variance: {
            description: `${duplicateGroup.length} duplicate transactions found`
          },
          createdAt: new Date()
        });
      }

      // Calculate statistics
      const autoMatched = matches.filter(m => m.status === 'auto-matched').length;
      const totalConfidence = matches.reduce((sum, m) => sum + m.confidence, 0);
      const averageConfidence = matches.length > 0 ? totalConfidence / matches.length : 0;

      const statistics: ReconciliationSession['statistics'] = {
        totalInternal: internalTransactions.length,
        totalExternal: externalTransactions.length,
        matched: matches.length,
        unmatched: unmatchedInternal.length + unmatchedExternal.length,
        variances: variances.length,
        autoMatched,
        manualMatched: matches.length - autoMatched,
        confidence: averageConfidence
      };

      const processingTime = Date.now() - startTime;

      logger.info('Reconciliation completed', LogCategory.ACCOUNTING, {
        sessionId,
        statistics,
        processingTime,
        rulesApplied: sortedRules.length
      });

      return { matches, variances, statistics };
    } catch (error) {
      logger.error('Error performing reconciliation', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Apply a specific matching rule to find matches
   */
  private static async applyMatchingRule(
    internalTransactions: ReconciliationTransaction[],
    externalTransactions: ReconciliationTransaction[],
    rule: MatchingRule
  ): Promise<ReconciliationMatch[]> {
    const matches: ReconciliationMatch[] = [];

    for (const internal of internalTransactions) {
      for (const external of externalTransactions) {
        const matchResult = this.evaluateMatch(internal, external, rule);
        
        if (matchResult.isMatch && matchResult.confidence >= (rule.actions.confidence || 0.5)) {
          const match: ReconciliationMatch = {
            id: new mongoose.Types.ObjectId().toString(),
            internalTransaction: internal,
            externalTransaction: external,
            matchType: 'rule-based',
            confidence: matchResult.confidence,
            matchingRule: rule.id,
            matchingFactors: matchResult.factors,
            status: rule.actions.autoMatch ? 'auto-matched' : 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
          };

          matches.push(match);
          break; // Move to next internal transaction
        }
      }
    }

    return matches;
  }

  /**
   * Evaluate if two transactions match based on a rule
   */
  private static evaluateMatch(
    internal: ReconciliationTransaction,
    external: ReconciliationTransaction,
    rule: MatchingRule
  ): {
    isMatch: boolean;
    confidence: number;
    factors: ReconciliationMatch['matchingFactors'];
  } {
    const factors = {
      amountMatch: 0,
      dateMatch: 0,
      descriptionMatch: 0,
      referenceMatch: 0,
      overallScore: 0
    };

    // Amount matching
    if (rule.conditions.requireExactAmount) {
      factors.amountMatch = internal.amount === external.amount ? 1 : 0;
    } else if (rule.conditions.amountTolerance !== undefined) {
      const tolerance = rule.conditions.amountTolerance;
      const difference = Math.abs(internal.amount - external.amount);
      const maxAmount = Math.max(internal.amount, external.amount);
      const percentageDiff = maxAmount > 0 ? difference / maxAmount : 0;
      factors.amountMatch = percentageDiff <= tolerance ? 1 - percentageDiff / tolerance : 0;
    } else {
      factors.amountMatch = 1; // No amount requirement
    }

    // Date matching
    if (rule.conditions.dateTolerance !== undefined) {
      const daysDiff = Math.abs(
        (internal.date.getTime() - external.date.getTime()) / (1000 * 60 * 60 * 24)
      );
      factors.dateMatch = daysDiff <= rule.conditions.dateTolerance 
        ? 1 - daysDiff / rule.conditions.dateTolerance 
        : 0;
    } else {
      factors.dateMatch = 1; // No date requirement
    }

    // Description matching
    if (rule.conditions.descriptionSimilarity !== undefined) {
      factors.descriptionMatch = this.calculateStringSimilarity(
        internal.description,
        external.description
      );
    } else {
      factors.descriptionMatch = 1; // No description requirement
    }

    // Reference matching
    if (rule.conditions.requireExactReference) {
      factors.referenceMatch = internal.reference === external.reference ? 1 : 0;
    } else {
      factors.referenceMatch = 1; // No reference requirement
    }

    // Calculate overall score
    const weights = {
      amount: 0.4,
      date: 0.2,
      description: 0.2,
      reference: 0.2
    };

    factors.overallScore = 
      factors.amountMatch * weights.amount +
      factors.dateMatch * weights.date +
      factors.descriptionMatch * weights.description +
      factors.referenceMatch * weights.reference;

    const isMatch = factors.overallScore >= (rule.actions.confidence || 0.5);

    return {
      isMatch,
      confidence: factors.overallScore,
      factors
    };
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;

    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 1;

    const matrix: number[][] = [];
    const len1 = s1.length;
    const len2 = s2.length;

    // Initialize matrix
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // deletion
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j - 1] + cost // substitution
        );
      }
    }

    const maxLength = Math.max(len1, len2);
    return maxLength > 0 ? 1 - matrix[len1][len2] / maxLength : 0;
  }

  /**
   * Detect duplicate transactions within a set
   */
  private static detectDuplicates(
    transactions: ReconciliationTransaction[]
  ): ReconciliationTransaction[][] {
    const duplicateGroups: ReconciliationTransaction[][] = [];
    const processed = new Set<string>();

    for (let i = 0; i < transactions.length; i++) {
      if (processed.has(transactions[i].id)) continue;

      const duplicates = [transactions[i]];
      processed.add(transactions[i].id);

      for (let j = i + 1; j < transactions.length; j++) {
        if (processed.has(transactions[j].id)) continue;

        if (this.areDuplicates(transactions[i], transactions[j])) {
          duplicates.push(transactions[j]);
          processed.add(transactions[j].id);
        }
      }

      if (duplicates.length > 1) {
        duplicateGroups.push(duplicates);
      }
    }

    return duplicateGroups;
  }

  /**
   * Check if two transactions are duplicates
   */
  private static areDuplicates(
    t1: ReconciliationTransaction,
    t2: ReconciliationTransaction
  ): boolean {
    // Same amount and date within 1 day
    const amountMatch = t1.amount === t2.amount;
    const daysDiff = Math.abs(
      (t1.date.getTime() - t2.date.getTime()) / (1000 * 60 * 60 * 24)
    );
    const dateMatch = daysDiff <= 1;

    // High description similarity
    const descriptionSimilarity = this.calculateStringSimilarity(
      t1.description,
      t2.description
    );

    return amountMatch && dateMatch && descriptionSimilarity > 0.9;
  }

  /**
   * Calculate variance severity based on amount
   */
  private static calculateVarianceSeverity(amount: number): 'low' | 'medium' | 'high' | 'critical' {
    if (amount >= 10000000) return 'critical'; // 10M MWK
    if (amount >= 5000000) return 'high';      // 5M MWK
    if (amount >= 1000000) return 'medium';    // 1M MWK
    return 'low';
  }

  /**
   * Get reconciliation session by ID
   */
  static async getReconciliationSession(sessionId: string): Promise<ReconciliationSession | null> {
    try {
      // In a real implementation, this would fetch from database
      // For now, return null as placeholder
      return null;
    } catch (error) {
      logger.error('Error getting reconciliation session', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update reconciliation match status
   */
  static async updateMatchStatus(
    matchId: string,
    status: ReconciliationMatch['status'],
    reviewedBy: string,
    notes?: string
  ): Promise<ReconciliationMatch> {
    try {
      // Implementation would update the match in database
      const updatedMatch: ReconciliationMatch = {
        id: matchId,
        status,
        reviewedBy,
        reviewedAt: new Date(),
        notes,
        // ... other fields would be populated from database
      } as ReconciliationMatch;

      logger.info('Match status updated', LogCategory.ACCOUNTING, {
        matchId,
        status,
        reviewedBy
      });

      return updatedMatch;
    } catch (error) {
      logger.error('Error updating match status', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Resolve variance with specific action
   */
  static async resolveVariance(
    varianceId: string,
    resolution: ReconciliationVariance['resolution']
  ): Promise<ReconciliationVariance> {
    try {
      // Implementation would update the variance in database
      const updatedVariance: ReconciliationVariance = {
        id: varianceId,
        resolution,
        // ... other fields would be populated from database
      } as ReconciliationVariance;

      logger.info('Variance resolved', LogCategory.ACCOUNTING, {
        varianceId,
        action: resolution?.action,
        resolvedBy: resolution?.resolvedBy
      });

      return updatedVariance;
    } catch (error) {
      logger.error('Error resolving variance', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get reconciliation statistics for a date range
   */
  static async getReconciliationStatistics(
    startDate: Date,
    endDate: Date,
    source?: string
  ): Promise<{
    totalSessions: number;
    completedSessions: number;
    totalMatches: number;
    autoMatches: number;
    manualMatches: number;
    totalVariances: number;
    averageConfidence: number;
    processingTime: number;
  }> {
    try {
      // Implementation would aggregate data from database
      const stats = {
        totalSessions: 0,
        completedSessions: 0,
        totalMatches: 0,
        autoMatches: 0,
        manualMatches: 0,
        totalVariances: 0,
        averageConfidence: 0,
        processingTime: 0
      };

      return stats;
    } catch (error) {
      logger.error('Error getting reconciliation statistics', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Export reconciliation results to various formats
   */
  static async exportReconciliationResults(
    sessionId: string,
    format: 'csv' | 'excel' | 'pdf' | 'json'
  ): Promise<{
    data: Buffer | string;
    filename: string;
    mimeType: string;
  }> {
    try {
      // Implementation would generate export in specified format
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `reconciliation_${sessionId}_${timestamp}.${format}`;

      let data: Buffer | string;
      let mimeType: string;

      switch (format) {
        case 'csv':
          data = 'CSV data would be generated here';
          mimeType = 'text/csv';
          break;
        case 'excel':
          data = Buffer.from('Excel data would be generated here');
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'pdf':
          data = Buffer.from('PDF data would be generated here');
          mimeType = 'application/pdf';
          break;
        case 'json':
          data = JSON.stringify({ message: 'JSON data would be generated here' });
          mimeType = 'application/json';
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      logger.info('Reconciliation results exported', LogCategory.ACCOUNTING, {
        sessionId,
        format,
        filename
      });

      return { data, filename, mimeType };
    } catch (error) {
      logger.error('Error exporting reconciliation results', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

export const incomeReconciliationService = IncomeReconciliationService;

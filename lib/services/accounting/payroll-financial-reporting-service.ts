import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * Service for integrating payroll data into financial reporting
 */
export class PayrollFinancialReportingService {

  /**
   * Generate payroll data for Profit & Loss statement
   * @param startDate - Start date for the period
   * @param endDate - End date for the period
   * @param options - Additional options for the report
   */
  async generatePayrollPLData(
    startDate: Date,
    endDate: Date,
    options: {
      departmentId?: string;
      includeComparison?: boolean;
      comparisonStartDate?: Date;
      comparisonEndDate?: Date;
    } = {}
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll P&L data', LogCategory.ACCOUNTING, {
        startDate,
        endDate,
        options
      });

      const PayrollRun = mongoose.model('PayrollRun');
      const Employee = mongoose.model('Employee');

      // Build query filter
      const filter: any = {
        'payPeriod.startDate': { $gte: startDate },
        'payPeriod.endDate': { $lte: endDate },
        status: 'paid'
      };

      if (options.departmentId) {
        filter.departments = new mongoose.Types.ObjectId(options.departmentId);
      }

      // Get payroll runs for the period
      const payrollRuns = await PayrollRun.find(filter)
        .populate('departments', 'name code')
        .sort({ 'payPeriod.startDate': 1 });

      // Calculate payroll expenses by category
      const payrollExpenses = await this.calculatePayrollExpensesByCategory(payrollRuns);

      // Get comparison data if requested
      let comparisonData = null;
      if (options.includeComparison && options.comparisonStartDate && options.comparisonEndDate) {
        const comparisonFilter = {
          ...filter,
          'payPeriod.startDate': { $gte: options.comparisonStartDate },
          'payPeriod.endDate': { $lte: options.comparisonEndDate }
        };

        const comparisonPayrollRuns = await PayrollRun.find(comparisonFilter)
          .populate('departments', 'name code');

        comparisonData = await this.calculatePayrollExpensesByCategory(comparisonPayrollRuns);
      }

      return {
        period: {
          startDate,
          endDate
        },
        payrollExpenses,
        comparisonData,
        summary: {
          totalGrossSalary: payrollExpenses.grossSalary.total,
          totalBenefits: payrollExpenses.benefits.total,
          totalTaxes: payrollExpenses.taxes.total,
          totalDeductions: payrollExpenses.deductions.total,
          totalNetSalary: payrollExpenses.netSalary.total,
          totalEmployerContributions: payrollExpenses.employerContributions.total
        }
      };

    } catch (error) {
      logger.error('Failed to generate payroll P&L data', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Generate payroll data for Balance Sheet
   * @param asOfDate - As of date for the balance sheet
   * @param options - Additional options
   */
  async generatePayrollBalanceSheetData(
    asOfDate: Date,
    options: {
      departmentId?: string;
      includeComparison?: boolean;
      comparisonDate?: Date;
    } = {}
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll balance sheet data', LogCategory.ACCOUNTING, {
        asOfDate,
        options
      });

      const PayrollRun = mongoose.model('PayrollRun');

      // Get unpaid payroll liabilities as of the date
      const unpaidPayrollFilter: any = {
        'payPeriod.endDate': { $lte: asOfDate },
        status: { $in: ['completed', 'approved'] } // Not yet paid
      };

      if (options.departmentId) {
        unpaidPayrollFilter.departments = new mongoose.Types.ObjectId(options.departmentId);
      }

      const unpaidPayrollRuns = await PayrollRun.find(unpaidPayrollFilter)
        .populate('departments', 'name code');

      // Calculate payroll liabilities
      const payrollLiabilities = await this.calculatePayrollLiabilities(unpaidPayrollRuns);

      // Get accrued payroll for the current period
      const currentMonthStart = new Date(asOfDate.getFullYear(), asOfDate.getMonth(), 1);
      const accruedPayrollFilter = {
        ...unpaidPayrollFilter,
        'payPeriod.startDate': { $gte: currentMonthStart },
        'payPeriod.endDate': { $lte: asOfDate }
      };

      const accruedPayrollRuns = await PayrollRun.find(accruedPayrollFilter);
      const accruedPayroll = await this.calculateAccruedPayroll(accruedPayrollRuns, asOfDate);

      // Get comparison data if requested
      let comparisonData = null;
      if (options.includeComparison && options.comparisonDate) {
        // Similar calculation for comparison date
        const comparisonFilter = {
          ...unpaidPayrollFilter,
          'payPeriod.endDate': { $lte: options.comparisonDate }
        };

        const comparisonPayrollRuns = await PayrollRun.find(comparisonFilter);
        comparisonData = await this.calculatePayrollLiabilities(comparisonPayrollRuns);
      }

      return {
        asOfDate,
        payrollLiabilities,
        accruedPayroll,
        comparisonData,
        summary: {
          totalSalaryPayable: payrollLiabilities.salaryPayable.total,
          totalTaxWithholdings: payrollLiabilities.taxWithholdings.total,
          totalBenefitLiabilities: payrollLiabilities.benefitLiabilities.total,
          totalAccruedPayroll: accruedPayroll.total
        }
      };

    } catch (error) {
      logger.error('Failed to generate payroll balance sheet data', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Generate payroll cash flow data
   * @param startDate - Start date for the period
   * @param endDate - End date for the period
   * @param options - Additional options
   */
  async generatePayrollCashFlowData(
    startDate: Date,
    endDate: Date,
    options: {
      departmentId?: string;
      includeComparison?: boolean;
      comparisonStartDate?: Date;
      comparisonEndDate?: Date;
    } = {}
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll cash flow data', LogCategory.ACCOUNTING, {
        startDate,
        endDate,
        options
      });

      const PayrollRun = mongoose.model('PayrollRun');

      // Get paid payroll runs for the period
      const paidPayrollFilter: any = {
        'payPeriod.startDate': { $gte: startDate },
        'payPeriod.endDate': { $lte: endDate },
        status: 'paid'
      };

      if (options.departmentId) {
        paidPayrollFilter.departments = new mongoose.Types.ObjectId(options.departmentId);
      }

      const paidPayrollRuns = await PayrollRun.find(paidPayrollFilter)
        .populate('departments', 'name code')
        .sort({ 'payPeriod.startDate': 1 });

      // Calculate cash flows by month
      const monthlyCashFlows = await this.calculateMonthlyCashFlows(paidPayrollRuns, startDate, endDate);

      // Calculate cash flows by category
      const cashFlowsByCategory = await this.calculateCashFlowsByCategory(paidPayrollRuns);

      // Get comparison data if requested
      let comparisonData = null;
      if (options.includeComparison && options.comparisonStartDate && options.comparisonEndDate) {
        const comparisonFilter = {
          ...paidPayrollFilter,
          'payPeriod.startDate': { $gte: options.comparisonStartDate },
          'payPeriod.endDate': { $lte: options.comparisonEndDate }
        };

        const comparisonPayrollRuns = await PayrollRun.find(comparisonFilter);
        comparisonData = await this.calculateCashFlowsByCategory(comparisonPayrollRuns);
      }

      return {
        period: {
          startDate,
          endDate
        },
        monthlyCashFlows,
        cashFlowsByCategory,
        comparisonData,
        summary: {
          totalCashOutflow: cashFlowsByCategory.totalOutflow,
          netSalaryPayments: cashFlowsByCategory.netSalaryPayments,
          taxPayments: cashFlowsByCategory.taxPayments,
          benefitPayments: cashFlowsByCategory.benefitPayments
        }
      };

    } catch (error) {
      logger.error('Failed to generate payroll cash flow data', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive payroll analytics
   * @param startDate - Start date for analysis
   * @param endDate - End date for analysis
   * @param options - Additional options
   */
  async generatePayrollAnalytics(
    startDate: Date,
    endDate: Date,
    options: {
      departmentId?: string;
      includeForecasting?: boolean;
      forecastPeriods?: number;
    } = {}
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll analytics', LogCategory.ACCOUNTING, {
        startDate,
        endDate,
        options
      });

      // Get payroll trend data
      const trendData = await this.calculatePayrollTrends(startDate, endDate, options.departmentId);

      // Get department-wise analysis
      const departmentAnalysis = await this.calculateDepartmentAnalysis(startDate, endDate);

      // Get cost per employee analysis
      const costPerEmployeeAnalysis = await this.calculateCostPerEmployeeAnalysis(startDate, endDate);

      // Get forecasting data if requested
      let forecastData = null;
      if (options.includeForecasting) {
        forecastData = await this.generatePayrollForecast(
          startDate,
          endDate,
          options.forecastPeriods || 6
        );
      }

      return {
        period: {
          startDate,
          endDate
        },
        trendData,
        departmentAnalysis,
        costPerEmployeeAnalysis,
        forecastData,
        summary: {
          averageMonthlyPayroll: trendData.averageMonthlyPayroll,
          payrollGrowthRate: trendData.growthRate,
          highestCostDepartment: departmentAnalysis.highestCost,
          averageCostPerEmployee: costPerEmployeeAnalysis.average
        }
      };

    } catch (error) {
      logger.error('Failed to generate payroll analytics', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Calculate payroll expenses by category for P&L
   */
  private async calculatePayrollExpensesByCategory(payrollRuns: any[]): Promise<any> {
    const expenses = {
      grossSalary: {
        total: 0,
        departments: {} as Record<string, number>
      },
      benefits: {
        total: 0,
        departments: {} as Record<string, number>
      },
      taxes: {
        total: 0,
        departments: {} as Record<string, number>
      },
      deductions: {
        total: 0,
        departments: {} as Record<string, number>
      },
      netSalary: {
        total: 0,
        departments: {} as Record<string, number>
      },
      employerContributions: {
        total: 0,
        departments: {} as Record<string, number>
      }
    };

    for (const payrollRun of payrollRuns) {
      const departmentName = payrollRun.departments?.[0]?.name || 'General';

      // Gross salary
      expenses.grossSalary.total += payrollRun.totalGrossSalary || 0;
      expenses.grossSalary.departments[departmentName] =
        (expenses.grossSalary.departments[departmentName] || 0) + (payrollRun.totalGrossSalary || 0);

      // Benefits (allowances)
      expenses.benefits.total += payrollRun.totalAllowances || 0;
      expenses.benefits.departments[departmentName] =
        (expenses.benefits.departments[departmentName] || 0) + (payrollRun.totalAllowances || 0);

      // Taxes
      expenses.taxes.total += payrollRun.totalTax || 0;
      expenses.taxes.departments[departmentName] =
        (expenses.taxes.departments[departmentName] || 0) + (payrollRun.totalTax || 0);

      // Deductions
      expenses.deductions.total += payrollRun.totalDeductions || 0;
      expenses.deductions.departments[departmentName] =
        (expenses.deductions.departments[departmentName] || 0) + (payrollRun.totalDeductions || 0);

      // Net salary
      expenses.netSalary.total += payrollRun.totalNetSalary || 0;
      expenses.netSalary.departments[departmentName] =
        (expenses.netSalary.departments[departmentName] || 0) + (payrollRun.totalNetSalary || 0);

      // Employer contributions (pension, etc.)
      const employerContributions = (payrollRun.totalGrossSalary || 0) * 0.05; // 5% pension
      expenses.employerContributions.total += employerContributions;
      expenses.employerContributions.departments[departmentName] =
        (expenses.employerContributions.departments[departmentName] || 0) + employerContributions;
    }

    return expenses;
  }

  /**
   * Calculate payroll liabilities for Balance Sheet
   */
  private async calculatePayrollLiabilities(payrollRuns: any[]): Promise<any> {
    const liabilities = {
      salaryPayable: {
        total: 0,
        departments: {} as Record<string, number>
      },
      taxWithholdings: {
        total: 0,
        departments: {} as Record<string, number>
      },
      benefitLiabilities: {
        total: 0,
        departments: {} as Record<string, number>
      }
    };

    for (const payrollRun of payrollRuns) {
      const departmentName = payrollRun.departments?.[0]?.name || 'General';

      // Salary payable (net salary not yet paid)
      liabilities.salaryPayable.total += payrollRun.totalNetSalary || 0;
      liabilities.salaryPayable.departments[departmentName] =
        (liabilities.salaryPayable.departments[departmentName] || 0) + (payrollRun.totalNetSalary || 0);

      // Tax withholdings
      liabilities.taxWithholdings.total += payrollRun.totalTax || 0;
      liabilities.taxWithholdings.departments[departmentName] =
        (liabilities.taxWithholdings.departments[departmentName] || 0) + (payrollRun.totalTax || 0);

      // Benefit liabilities (pension, etc.)
      const benefitLiabilities = (payrollRun.totalGrossSalary || 0) * 0.05; // 5% pension
      liabilities.benefitLiabilities.total += benefitLiabilities;
      liabilities.benefitLiabilities.departments[departmentName] =
        (liabilities.benefitLiabilities.departments[departmentName] || 0) + benefitLiabilities;
    }

    return liabilities;
  }

  /**
   * Calculate accrued payroll
   */
  private async calculateAccruedPayroll(payrollRuns: any[], asOfDate: Date): Promise<any> {
    let totalAccrued = 0;
    const departmentAccruals: Record<string, number> = {};

    for (const payrollRun of payrollRuns) {
      const departmentName = payrollRun.departments?.[0]?.name || 'General';

      // Calculate accrual based on days worked in the period
      const periodStart = new Date(payrollRun.payPeriod.startDate);
      const periodEnd = new Date(payrollRun.payPeriod.endDate);
      const totalDays = Math.ceil((periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24));
      const daysWorked = Math.ceil((asOfDate.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24));

      if (daysWorked > 0 && daysWorked <= totalDays) {
        const accruedAmount = (payrollRun.totalGrossSalary || 0) * (daysWorked / totalDays);
        totalAccrued += accruedAmount;
        departmentAccruals[departmentName] = (departmentAccruals[departmentName] || 0) + accruedAmount;
      }
    }

    return {
      total: totalAccrued,
      departments: departmentAccruals
    };
  }

  /**
   * Calculate monthly cash flows
   */
  private async calculateMonthlyCashFlows(payrollRuns: any[], startDate: Date, endDate: Date): Promise<any[]> {
    const monthlyFlows: Record<string, any> = {};

    for (const payrollRun of payrollRuns) {
      const month = new Date(payrollRun.payPeriod.startDate).toISOString().substring(0, 7); // YYYY-MM

      if (!monthlyFlows[month]) {
        monthlyFlows[month] = {
          month,
          totalOutflow: 0,
          netSalaryPayments: 0,
          taxPayments: 0,
          benefitPayments: 0
        };
      }

      monthlyFlows[month].totalOutflow += payrollRun.totalGrossSalary || 0;
      monthlyFlows[month].netSalaryPayments += payrollRun.totalNetSalary || 0;
      monthlyFlows[month].taxPayments += payrollRun.totalTax || 0;
      monthlyFlows[month].benefitPayments += (payrollRun.totalGrossSalary || 0) * 0.05; // 5% pension
    }

    return Object.values(monthlyFlows).sort((a: any, b: any) => a.month.localeCompare(b.month));
  }

  /**
   * Calculate cash flows by category
   */
  private async calculateCashFlowsByCategory(payrollRuns: any[]): Promise<any> {
    let totalOutflow = 0;
    let netSalaryPayments = 0;
    let taxPayments = 0;
    let benefitPayments = 0;

    for (const payrollRun of payrollRuns) {
      totalOutflow += payrollRun.totalGrossSalary || 0;
      netSalaryPayments += payrollRun.totalNetSalary || 0;
      taxPayments += payrollRun.totalTax || 0;
      benefitPayments += (payrollRun.totalGrossSalary || 0) * 0.05; // 5% pension
    }

    return {
      totalOutflow,
      netSalaryPayments,
      taxPayments,
      benefitPayments
    };
  }

  /**
   * Calculate payroll trends
   */
  private async calculatePayrollTrends(startDate: Date, endDate: Date, departmentId?: string): Promise<any> {
    // Implementation for trend calculation
    return {
      averageMonthlyPayroll: 0,
      growthRate: 0,
      monthlyData: []
    };
  }

  /**
   * Calculate department analysis
   */
  private async calculateDepartmentAnalysis(startDate: Date, endDate: Date): Promise<any> {
    // Implementation for department analysis
    return {
      highestCost: { name: '', amount: 0 },
      departments: []
    };
  }

  /**
   * Calculate cost per employee analysis
   */
  private async calculateCostPerEmployeeAnalysis(startDate: Date, endDate: Date): Promise<any> {
    // Implementation for cost per employee analysis
    return {
      average: 0,
      median: 0,
      range: { min: 0, max: 0 }
    };
  }

  /**
   * Generate payroll forecast
   */
  private async generatePayrollForecast(startDate: Date, endDate: Date, periods: number): Promise<any> {
    // Implementation for payroll forecasting
    return {
      forecastPeriods: [],
      methodology: 'trend-based',
      confidence: 0.85
    };
  }
}

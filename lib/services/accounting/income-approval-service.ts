import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import Income, { IIncome, IIncomeApprovalWorkflow, IIncomeApprovalHistory } from '@/models/accounting/Income';
import User from '@/models/User';
import { UserRole } from '@/types/user-roles';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';
import mongoose from 'mongoose';

export interface ApprovalRule {
  level: number;
  role: UserRole;
  amountThreshold?: number;
  description: string;
}

export interface ApprovalRequest {
  incomeId: string;
  action: 'approve' | 'reject';
  comments?: string;
  approverId: string;
}

export interface ApprovalWorkflowConfig {
  rules: ApprovalRule[];
  autoApprovalRules: {
    maxAmount?: number;
    sources?: string[];
    skipApproval?: boolean;
  };
}

/**
 * Service for managing income approval workflows
 */
export class IncomeApprovalService {
  
  /**
   * Default approval workflow configuration for TCM
   */
  private static readonly DEFAULT_APPROVAL_CONFIG: ApprovalWorkflowConfig = {
    rules: [
      {
        level: 1,
        role: UserRole.FINANCE_OFFICER,
        amountThreshold: 1000000, // MWK 1M
        description: 'Finance Officer approval for amounts up to MWK 1M'
      },
      {
        level: 2,
        role: UserRole.FINANCE_MANAGER,
        amountThreshold: 5000000, // MWK 5M
        description: 'Finance Manager approval for amounts up to MWK 5M'
      },
      {
        level: 3,
        role: UserRole.FINANCE_DIRECTOR,
        amountThreshold: Number.MAX_SAFE_INTEGER,
        description: 'Finance Director approval for all amounts above MWK 5M'
      }
    ],
    autoApprovalRules: {
      maxAmount: 100000, // Auto-approve amounts under MWK 100K
      sources: ['government_subvention'], // Auto-approve government subventions
      skipApproval: false
    }
  };

  /**
   * Initialize approval workflow for an income record
   */
  static async initializeApprovalWorkflow(
    incomeId: string,
    amount: number,
    source: string,
    createdBy: string
  ): Promise<IIncomeApprovalWorkflow> {
    try {
      await connectToDatabase();
      
      const config = this.DEFAULT_APPROVAL_CONFIG;
      
      // Check if auto-approval applies
      if (this.shouldAutoApprove(amount, source, config)) {
        return {
          currentLevel: 0,
          status: 'approved',
          approvalHistory: [{
            approver: new mongoose.Types.ObjectId(createdBy),
            status: 'approved',
            date: new Date(),
            comments: 'Auto-approved based on system rules',
            level: 0
          }],
          requiredApprovers: [],
          autoApprovalRules: config.autoApprovalRules
        };
      }

      // Determine required approvers based on amount
      const requiredApprovers = await this.getRequiredApprovers(amount, config);
      
      if (requiredApprovers.length === 0) {
        throw new Error('No approvers found for the given amount and configuration');
      }

      const workflow: IIncomeApprovalWorkflow = {
        currentApprover: requiredApprovers[0].approver,
        currentLevel: 1,
        status: 'pending',
        approvalHistory: [],
        requiredApprovers,
        autoApprovalRules: config.autoApprovalRules
      };

      // Send notification to first approver
      await this.sendApprovalNotification(incomeId, requiredApprovers[0].approver.toString());

      logger.info('Approval workflow initialized', LogCategory.ACCOUNTING, {
        incomeId,
        amount,
        requiredApprovers: requiredApprovers.length,
        currentApprover: requiredApprovers[0].approver
      });

      return workflow;
    } catch (error) {
      logger.error('Error initializing approval workflow', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Process an approval or rejection
   */
  static async processApproval(request: ApprovalRequest): Promise<IIncome> {
    try {
      await connectToDatabase();

      const income = await Income.findById(request.incomeId).populate('approvalWorkflow.currentApprover');
      if (!income) {
        throw new Error('Income record not found');
      }

      if (!income.approvalWorkflow) {
        throw new Error('No approval workflow found for this income');
      }

      // Verify the approver is authorized
      if (income.approvalWorkflow.currentApprover?.toString() !== request.approverId) {
        throw new Error('User is not authorized to approve this income at the current level');
      }

      const approvalHistory: IIncomeApprovalHistory = {
        approver: new mongoose.Types.ObjectId(request.approverId),
        status: request.action,
        date: new Date(),
        comments: request.comments,
        level: income.approvalWorkflow.currentLevel
      };

      // Add to approval history
      income.approvalWorkflow.approvalHistory.push(approvalHistory);

      if (request.action === 'reject') {
        // Handle rejection
        income.approvalWorkflow.status = 'rejected';
        income.status = 'rejected';
        income.rejectedAt = new Date();
        income.approvalWorkflow.currentApprover = undefined;

        // Notify creator of rejection
        await this.sendRejectionNotification(request.incomeId, income.createdBy.toString(), request.comments);
      } else {
        // Handle approval
        const nextApprover = this.getNextApprover(income.approvalWorkflow);
        
        if (nextApprover) {
          // Move to next approval level
          income.approvalWorkflow.currentApprover = nextApprover.approver;
          income.approvalWorkflow.currentLevel = nextApprover.level;
          
          // Send notification to next approver
          await this.sendApprovalNotification(request.incomeId, nextApprover.approver.toString());
        } else {
          // All approvals complete
          income.approvalWorkflow.status = 'approved';
          income.approvalWorkflow.currentApprover = undefined;
          income.status = 'approved';
          income.approvedAt = new Date();

          // Notify creator of approval
          await this.sendApprovalCompleteNotification(request.incomeId, income.createdBy.toString());
        }
      }

      income.updatedBy = new mongoose.Types.ObjectId(request.approverId);
      await income.save();

      logger.info('Approval processed', LogCategory.ACCOUNTING, {
        incomeId: request.incomeId,
        action: request.action,
        approverId: request.approverId,
        newStatus: income.status
      });

      return income;
    } catch (error) {
      logger.error('Error processing approval', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get pending approvals for a user
   */
  static async getPendingApprovals(userId: string, page = 1, limit = 10) {
    try {
      await connectToDatabase();

      const skip = (page - 1) * limit;
      
      const pendingApprovals = await Income.find({
        'approvalWorkflow.currentApprover': userId,
        'approvalWorkflow.status': 'pending',
        status: 'pending_approval'
      })
      .populate('createdBy', 'name email')
      .populate('budget', 'name')
      .populate('budgetCategory', 'name')
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(limit);

      const total = await Income.countDocuments({
        'approvalWorkflow.currentApprover': userId,
        'approvalWorkflow.status': 'pending',
        status: 'pending_approval'
      });

      return {
        approvals: pendingApprovals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting pending approvals', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get approval history for an income record
   */
  static async getApprovalHistory(incomeId: string) {
    try {
      await connectToDatabase();

      const income = await Income.findById(incomeId)
        .populate('approvalWorkflow.approvalHistory.approver', 'name email role')
        .populate('approvalWorkflow.currentApprover', 'name email role');

      if (!income || !income.approvalWorkflow) {
        throw new Error('Income or approval workflow not found');
      }

      return income.approvalWorkflow;
    } catch (error) {
      logger.error('Error getting approval history', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Check if income should be auto-approved
   */
  private static shouldAutoApprove(
    amount: number,
    source: string,
    config: ApprovalWorkflowConfig
  ): boolean {
    const { autoApprovalRules } = config;
    
    if (autoApprovalRules.skipApproval) {
      return true;
    }

    if (autoApprovalRules.maxAmount && amount <= autoApprovalRules.maxAmount) {
      return true;
    }

    if (autoApprovalRules.sources && autoApprovalRules.sources.includes(source)) {
      return true;
    }

    return false;
  }

  /**
   * Get required approvers based on amount and configuration
   */
  private static async getRequiredApprovers(
    amount: number,
    config: ApprovalWorkflowConfig
  ) {
    const requiredRules = config.rules.filter(rule => 
      !rule.amountThreshold || amount <= rule.amountThreshold
    );

    const approvers = [];
    
    for (const rule of requiredRules) {
      // Find users with the required role
      const users = await User.find({ role: rule.role, status: 'active' });
      
      if (users.length > 0) {
        // For now, take the first user with the role
        // In a more sophisticated system, you might implement round-robin or workload balancing
        approvers.push({
          level: rule.level,
          approver: users[0]._id,
          role: rule.role,
          amountThreshold: rule.amountThreshold
        });
      }
    }

    return approvers.sort((a, b) => a.level - b.level);
  }

  /**
   * Get the next approver in the workflow
   */
  private static getNextApprover(workflow: IIncomeApprovalWorkflow) {
    const currentLevel = workflow.currentLevel;
    const nextApprover = workflow.requiredApprovers.find(
      approver => approver.level > currentLevel
    );
    
    return nextApprover;
  }

  /**
   * Send approval notification
   */
  private static async sendApprovalNotification(incomeId: string, approverId: string) {
    try {
      await notificationService.sendNotification(
        'Income Approval Required',
        `You have a new income record pending approval. Please review and take action.`,
        [approverId],
        {
          type: 'approval_request',
          priority: 'high',
          sourceType: 'income',
          sourceId: incomeId,
          actions: [
            {
              label: 'Review',
              url: `/dashboard/accounting/income/approve/${incomeId}`
            }
          ]
        }
      );
    } catch (error) {
      logger.error('Error sending approval notification', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Send rejection notification
   */
  private static async sendRejectionNotification(
    incomeId: string,
    creatorId: string,
    reason?: string
  ) {
    try {
      await notificationService.sendNotification(
        'Income Record Rejected',
        `Your income record has been rejected. ${reason ? `Reason: ${reason}` : ''}`,
        [creatorId],
        {
          type: 'approval_rejected',
          priority: 'high',
          sourceType: 'income',
          sourceId: incomeId,
          actions: [
            {
              label: 'View Details',
              url: `/dashboard/accounting/income/${incomeId}`
            }
          ]
        }
      );
    } catch (error) {
      logger.error('Error sending rejection notification', LogCategory.ACCOUNTING, error);
    }
  }

  /**
   * Send approval complete notification
   */
  private static async sendApprovalCompleteNotification(incomeId: string, creatorId: string) {
    try {
      await notificationService.sendNotification(
        'Income Record Approved',
        'Your income record has been approved and is now active.',
        [creatorId],
        {
          type: 'approval_approved',
          priority: 'medium',
          sourceType: 'income',
          sourceId: incomeId,
          actions: [
            {
              label: 'View Details',
              url: `/dashboard/accounting/income/${incomeId}`
            }
          ]
        }
      );
    } catch (error) {
      logger.error('Error sending approval complete notification', LogCategory.ACCOUNTING, error);
    }
  }
}

export const incomeApprovalService = IncomeApprovalService;

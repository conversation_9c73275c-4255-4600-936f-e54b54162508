import { logger, LogCategory } from '@/lib/backend/logger';

// Receipt processing interfaces
export interface ReceiptData {
  id: string;
  expenditureId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: Date;
  processedAt?: Date;
  status: ReceiptProcessingStatus;
  extractedData?: ExtractedReceiptData;
  ocrConfidence?: number;
  validationResults?: ReceiptValidationResult[];
  metadata: ReceiptMetadata;
}

export interface ExtractedReceiptData {
  merchantName?: string;
  merchantAddress?: string;
  merchantPhone?: string;
  transactionDate?: Date;
  totalAmount?: number;
  currency?: string;
  taxAmount?: number;
  taxRate?: number;
  items?: ReceiptItem[];
  paymentMethod?: string;
  receiptNumber?: string;
  vatNumber?: string;
}

export interface ReceiptItem {
  description: string;
  quantity?: number;
  unitPrice?: number;
  totalPrice: number;
  category?: string;
  taxable?: boolean;
}

export interface ReceiptMetadata {
  imageWidth: number;
  imageHeight: number;
  fileFormat: string;
  colorSpace: string;
  dpi?: number;
  processingTime?: number;
  ocrEngine: string;
  ocrVersion: string;
}

export interface ReceiptValidationResult {
  field: string;
  isValid: boolean;
  confidence: number;
  suggestedValue?: string;
  errorMessage?: string;
}

export enum ReceiptProcessingStatus {
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  FAILED = 'failed',
  VALIDATED = 'validated',
  REJECTED = 'rejected'
}

// OCR Configuration
interface OCRConfig {
  language: string;
  engineMode: number;
  pageSegMode: number;
  confidence: number;
  whitelist?: string;
  blacklist?: string;
}

// Receipt processing service
class ReceiptProcessingService {
  private readonly defaultOCRConfig: OCRConfig = {
    language: 'eng',
    engineMode: 3, // Default engine mode
    pageSegMode: 6, // Uniform block of text
    confidence: 60, // Minimum confidence threshold
    whitelist: '0*********ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,:-/$ '
  };

  /**
   * Process uploaded receipt image
   */
  async processReceipt(
    file: File,
    expenditureId: string,
    userId: string
  ): Promise<ReceiptData> {
    try {
      logger.info('Starting receipt processing', LogCategory.ACCOUNTING, {
        fileName: file.name,
        fileSize: file.size,
        expenditureId,
        userId
      });

      // Validate file
      this.validateReceiptFile(file);

      // Create receipt record
      const receiptData: ReceiptData = {
        id: this.generateReceiptId(),
        expenditureId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        uploadedAt: new Date(),
        status: ReceiptProcessingStatus.PROCESSING,
        metadata: await this.extractImageMetadata(file)
      };

      // Process image and extract text
      const processedImage = await this.preprocessImage(file);
      const ocrResult = await this.performOCR(processedImage);
      
      // Extract structured data
      const extractedData = await this.extractStructuredData(ocrResult.text);
      
      // Validate extracted data
      const validationResults = await this.validateExtractedData(extractedData);

      // Update receipt data
      receiptData.extractedData = extractedData;
      receiptData.ocrConfidence = ocrResult.confidence;
      receiptData.validationResults = validationResults;
      receiptData.processedAt = new Date();
      receiptData.status = this.determineProcessingStatus(validationResults);

      logger.info('Receipt processing completed', LogCategory.ACCOUNTING, {
        receiptId: receiptData.id,
        status: receiptData.status,
        confidence: receiptData.ocrConfidence
      });

      return receiptData;

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      logger.error('Receipt processing failed', LogCategory.ACCOUNTING, {
        error: errorMessage,
        expenditureId,
        fileName: file.name
      });

      throw new Error(`Receipt processing failed: ${errorMessage}`);
    }
  }

  /**
   * Validate receipt file
   */
  private validateReceiptFile(file: File): void {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];

    if (file.size > maxSize) {
      throw new Error('File size exceeds 10MB limit');
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPEG, PNG, WebP, and PDF files are allowed');
    }
  }

  /**
   * Extract image metadata
   */
  private async extractImageMetadata(file: File): Promise<ReceiptMetadata> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          imageWidth: img.naturalWidth,
          imageHeight: img.naturalHeight,
          fileFormat: file.type,
          colorSpace: 'RGB', // Default assumption
          ocrEngine: 'Tesseract.js',
          ocrVersion: '4.0.0'
        });
      };
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Preprocess image for better OCR results
   */
  private async preprocessImage(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        // Set canvas size
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        // Draw image
        ctx.drawImage(img, 0, 0);

        // Apply image enhancements
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const enhancedData = this.enhanceImageForOCR(imageData);
        ctx.putImageData(enhancedData, 0, 0);

        // Convert to data URL
        resolve(canvas.toDataURL('image/png'));
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Enhance image for better OCR results
   */
  private enhanceImageForOCR(imageData: ImageData): ImageData {
    const data = imageData.data;
    
    // Convert to grayscale and increase contrast
    for (let i = 0; i < data.length; i += 4) {
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
      
      // Increase contrast
      const contrast = 1.5;
      const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
      const enhancedGray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));
      
      data[i] = enhancedGray;     // Red
      data[i + 1] = enhancedGray; // Green
      data[i + 2] = enhancedGray; // Blue
      // Alpha channel remains unchanged
    }
    
    return imageData;
  }

  /**
   * Perform OCR on processed image
   */
  private async performOCR(imageDataUrl: string): Promise<{ text: string; confidence: number }> {
    try {
      // In a real implementation, this would use Tesseract.js or similar
      // For now, we'll simulate OCR processing
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock OCR result - in production, replace with actual OCR
      const mockText = this.generateMockOCRText();
      const confidence = Math.random() * 40 + 60; // 60-100% confidence
      
      return {
        text: mockText,
        confidence
      };
      
    } catch (error) {
      logger.error('OCR processing failed', LogCategory.ACCOUNTING, { error });
      throw new Error('OCR processing failed');
    }
  }

  /**
   * Generate mock OCR text for development
   */
  private generateMockOCRText(): string {
    const mockReceipts = [
      `SHOPRITE MALAWI
      Lilongwe Branch
      Tel: +265 1 123 456
      
      Date: ${new Date().toLocaleDateString()}
      Receipt #: SR${Math.floor(Math.random() * 100000)}
      
      Office Supplies        MWK 15,000
      Stationery Items       MWK 8,500
      Printer Paper          MWK 12,000
      
      Subtotal:             MWK 35,500
      VAT (16.5%):          MWK 5,858
      Total:                MWK 41,358
      
      Payment: Cash
      Thank you for shopping with us!`,
      
      `GAME STORES
      Blantyre City
      VAT #: *********
      
      ${new Date().toLocaleDateString()}
      Transaction: ${Math.floor(Math.random() * 1000000)}
      
      Computer Equipment     MWK 125,000
      Software License       MWK 45,000
      
      Subtotal:             MWK 170,000
      VAT:                  MWK 28,050
      Total:                MWK 198,050
      
      Card Payment
      Approved`,
      
      `FUEL STATION
      Mzuzu Highway
      
      Date: ${new Date().toLocaleDateString()}
      
      Fuel - Petrol         MWK 25,000
      Service Charge        MWK 500
      
      Total:                MWK 25,500
      
      Cash Payment
      Driver: John Banda`
    ];
    
    return mockReceipts[Math.floor(Math.random() * mockReceipts.length)];
  }

  /**
   * Extract structured data from OCR text
   */
  private async extractStructuredData(ocrText: string): Promise<ExtractedReceiptData> {
    const extractedData: ExtractedReceiptData = {};
    
    // Extract merchant name (usually first line)
    const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    if (lines.length > 0) {
      extractedData.merchantName = lines[0];
    }
    
    // Extract total amount
    const totalMatch = ocrText.match(/total[:\s]*(?:MWK\s*)?([0-9,]+(?:\.[0-9]{2})?)/i);
    if (totalMatch) {
      extractedData.totalAmount = parseFloat(totalMatch[1].replace(/,/g, ''));
      extractedData.currency = 'MWK';
    }
    
    // Extract VAT amount
    const vatMatch = ocrText.match(/vat[:\s]*(?:MWK\s*)?([0-9,]+(?:\.[0-9]{2})?)/i);
    if (vatMatch) {
      extractedData.taxAmount = parseFloat(vatMatch[1].replace(/,/g, ''));
    }
    
    // Extract date
    const dateMatch = ocrText.match(/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/);
    if (dateMatch) {
      extractedData.transactionDate = new Date(dateMatch[1]);
    }
    
    // Extract receipt number
    const receiptMatch = ocrText.match(/(?:receipt|ref|transaction)[#:\s]*([A-Z0-9]+)/i);
    if (receiptMatch) {
      extractedData.receiptNumber = receiptMatch[1];
    }
    
    // Extract payment method
    if (ocrText.toLowerCase().includes('cash')) {
      extractedData.paymentMethod = 'Cash';
    } else if (ocrText.toLowerCase().includes('card')) {
      extractedData.paymentMethod = 'Card';
    }
    
    return extractedData;
  }

  /**
   * Validate extracted data
   */
  private async validateExtractedData(data: ExtractedReceiptData): Promise<ReceiptValidationResult[]> {
    const results: ReceiptValidationResult[] = [];
    
    // Validate merchant name
    results.push({
      field: 'merchantName',
      isValid: !!data.merchantName && data.merchantName.length > 2,
      confidence: data.merchantName ? 90 : 0,
      errorMessage: !data.merchantName ? 'Merchant name not found' : undefined
    });
    
    // Validate total amount
    results.push({
      field: 'totalAmount',
      isValid: !!data.totalAmount && data.totalAmount > 0,
      confidence: data.totalAmount ? 95 : 0,
      errorMessage: !data.totalAmount ? 'Total amount not found or invalid' : undefined
    });
    
    // Validate date
    const isValidDate = data.transactionDate && !isNaN(data.transactionDate.getTime());
    results.push({
      field: 'transactionDate',
      isValid: !!isValidDate,
      confidence: isValidDate ? 85 : 0,
      errorMessage: !isValidDate ? 'Transaction date not found or invalid' : undefined
    });
    
    return results;
  }

  /**
   * Determine processing status based on validation results
   */
  private determineProcessingStatus(validationResults: ReceiptValidationResult[]): ReceiptProcessingStatus {
    const criticalFields = ['merchantName', 'totalAmount'];
    const criticalFieldsValid = criticalFields.every(field => 
      validationResults.find(result => result.field === field)?.isValid
    );
    
    if (criticalFieldsValid) {
      return ReceiptProcessingStatus.PROCESSED;
    } else {
      return ReceiptProcessingStatus.FAILED;
    }
  }

  /**
   * Generate unique receipt ID
   */
  private generateReceiptId(): string {
    return `receipt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get receipt processing statistics
   */
  async getProcessingStatistics(startDate?: Date, endDate?: Date): Promise<{
    totalProcessed: number;
    successRate: number;
    averageConfidence: number;
    processingTimeAverage: number;
    commonErrors: Array<{ error: string; count: number }>;
  }> {
    // Mock statistics - in production, query from database
    return {
      totalProcessed: 1250,
      successRate: 87.5,
      averageConfidence: 82.3,
      processingTimeAverage: 3.2,
      commonErrors: [
        { error: 'Low image quality', count: 45 },
        { error: 'Merchant name unclear', count: 32 },
        { error: 'Amount not found', count: 28 },
        { error: 'Date format unrecognized', count: 15 }
      ]
    };
  }

  /**
   * Reprocess failed receipt
   */
  async reprocessReceipt(receiptId: string, userId: string): Promise<ReceiptData> {
    logger.info('Reprocessing receipt', LogCategory.ACCOUNTING, {
      receiptId,
      userId
    });
    
    // In production, retrieve original file and reprocess
    throw new Error('Reprocessing not implemented yet');
  }

  /**
   * Manual correction of extracted data
   */
  async correctExtractedData(
    receiptId: string,
    corrections: Partial<ExtractedReceiptData>,
    userId: string
  ): Promise<ReceiptData> {
    logger.info('Applying manual corrections to receipt', LogCategory.ACCOUNTING, {
      receiptId,
      corrections,
      userId
    });
    
    // In production, update receipt data with corrections
    throw new Error('Manual correction not implemented yet');
  }
}

export const receiptProcessingService = new ReceiptProcessingService();

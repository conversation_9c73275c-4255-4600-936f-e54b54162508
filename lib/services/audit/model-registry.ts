// lib/services/audit/model-registry.ts
import { UserRole } from '@/types/user-roles';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import Employee from '@/models/Employee';
import Budget from '@/models/accounting/Budget';
import PayrollRecord from '@/models/payroll/PayrollRecord';
// Import other models as needed

export interface ModelConfig {
  displayName: string;
  allowedRoles: UserRole[];
  checkOwnership: boolean;
  ownershipField: string;
  maxBulkDelete: number;
  validationRules: Array<{
    field: string;
    allowedValues?: any[];
    disallowedValues?: any[];
    customValidator?: (value: any) => boolean;
    errorMessage: string;
  }>;
  populateFields?: string[];
  beforeDelete?: (items: any[]) => Promise<void>;
  afterDelete?: (deletedItems: any[], auditRecords: any[]) => Promise<void>;
}

// Model registry mapping model names to their Mongoose models
const MODEL_REGISTRY = {
  Income,
  Expense,
  Employee,
  Budget,
  PayrollRecord,
  // Add other models as needed
} as const;

// Configuration for each model type
const MODEL_CONFIGS: Record<string, ModelConfig> = {
  Income: {
    displayName: 'Income Transaction',
    allowedRoles: [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ],
    checkOwnership: true,
    ownershipField: 'createdBy',
    maxBulkDelete: 100,
    validationRules: [
      {
        field: 'status',
        disallowedValues: ['received', 'approved'],
        errorMessage: 'Cannot delete income with received or approved status'
      }
    ],
    populateFields: ['createdBy'],
    beforeDelete: async (items) => {
      // Remove budget integrations before deletion
      try {
        const { budgetIncomeIntegrationService } = await import('@/lib/services/accounting/budget-income-integration');
        const budgetCleanupPromises = items
          .filter(income => (income as any).appliedToBudget)
          .map(income =>
            budgetIncomeIntegrationService.removeIncomeAsBudgetItem(income as any)
              .catch(error => {
                console.error('Error removing budget integration:', error);
              })
          );

        await Promise.allSettled(budgetCleanupPromises);
      } catch (error) {
        console.error('Budget integration service not available:', error);
        // Continue with deletion even if budget integration fails
      }
    }
  },

  Expense: {
    displayName: 'Expense Record',
    allowedRoles: [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ],
    checkOwnership: true,
    ownershipField: 'createdBy',
    maxBulkDelete: 100,
    validationRules: [
      {
        field: 'status',
        disallowedValues: ['paid', 'approved'],
        errorMessage: 'Cannot delete expense with paid or approved status'
      }
    ],
    populateFields: ['createdBy']
  },

  Employee: {
    displayName: 'Employee Record',
    allowedRoles: [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER
    ],
    checkOwnership: true,
    ownershipField: 'createdBy',
    maxBulkDelete: 50,
    validationRules: [
      {
        field: 'status',
        disallowedValues: ['active'],
        errorMessage: 'Cannot delete active employee records'
      }
    ],
    populateFields: ['createdBy', 'department']
  },

  // Add more model configurations as needed
  Budget: {
    displayName: 'Budget Item',
    allowedRoles: [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.BUDGET_ANALYST
    ],
    checkOwnership: true,
    ownershipField: 'createdBy',
    maxBulkDelete: 50,
    validationRules: [
      {
        field: 'status',
        disallowedValues: ['approved', 'finalized'],
        errorMessage: 'Cannot delete approved or finalized budget items'
      }
    ],
    populateFields: ['createdBy']
  },

  PayrollRecord: {
    displayName: 'Payroll Record',
    allowedRoles: [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.PAYROLL_MANAGER
    ],
    checkOwnership: true,
    ownershipField: 'createdBy',
    maxBulkDelete: 25,
    validationRules: [
      {
        field: 'status',
        disallowedValues: ['processed', 'paid'],
        errorMessage: 'Cannot delete processed or paid payroll records'
      }
    ],
    populateFields: ['createdBy', 'employee']
  }
};

/**
 * Get Mongoose model by name
 */
export function getModelByName(modelName: string) {
  const model = MODEL_REGISTRY[modelName as keyof typeof MODEL_REGISTRY];
  if (!model) {
    throw new Error(`Model '${modelName}' not found in registry`);
  }
  return model;
}

/**
 * Get model configuration by name
 */
export function getModelConfig(modelName: string): ModelConfig {
  const config = MODEL_CONFIGS[modelName];
  if (!config) {
    throw new Error(`Configuration for model '${modelName}' not found`);
  }
  return config;
}

/**
 * Get all available model names
 */
export function getAvailableModels(): string[] {
  return Object.keys(MODEL_REGISTRY);
}

/**
 * Check if a model is registered
 */
export function isModelRegistered(modelName: string): boolean {
  return modelName in MODEL_REGISTRY;
}

/**
 * Register a new model (for dynamic model registration)
 */
export function registerModel(modelName: string, model: any, config: ModelConfig) {
  (MODEL_REGISTRY as any)[modelName] = model;
  MODEL_CONFIGS[modelName] = config;
}

/**
 * Get models accessible by a specific role
 */
export function getModelsForRole(userRole: UserRole): string[] {
  return Object.entries(MODEL_CONFIGS)
    .filter(([_, config]) => config.allowedRoles.includes(userRole))
    .map(([modelName, _]) => modelName);
}

export default {
  getModelByName,
  getModelConfig,
  getAvailableModels,
  isModelRegistered,
  registerModel,
  getModelsForRole
};

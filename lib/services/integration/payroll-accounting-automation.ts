// lib/services/integration/payroll-accounting-automation.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
// Removed deprecated service imports - using unified payroll service
import { unifiedPayrollService } from '../payroll/unified-payroll-service';
import { PayrollBudgetIntegrationService } from './payroll-budget-integration';
import { PayrollFinancialIntegrationService } from './payroll-financial-integration';

/**
 * Service for automating payroll-accounting integration
 */
export class PayrollAccountingAutomationService {
  private payrollBudgetIntegrationService: PayrollBudgetIntegrationService;
  private payrollFinancialIntegrationService: PayrollFinancialIntegrationService;

  constructor() {
    this.payrollBudgetIntegrationService = new PayrollBudgetIntegrationService();
    this.payrollFinancialIntegrationService = new PayrollFinancialIntegrationService();
  }

  /**
   * Process payroll completion and trigger accounting integration
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user triggering the integration
   */
  async processPayrollCompletion(payrollRunId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Starting automatic payroll-accounting integration', LogCategory.INTEGRATION, {
        payrollRunId,
        userId
      });

      // Get PayrollRun model
      const PayrollRun = mongoose.model('PayrollRun');
      
      // Get the payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      // Verify the payroll run is in the correct state
      if (payrollRun.status !== 'paid') {
        logger.warn('Payroll run is not in paid status, skipping integration', LogCategory.INTEGRATION, {
          payrollRunId,
          status: payrollRun.status
        });
        return;
      }

      // Update accounting status to processing
      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        accountingStatus: 'processing',
        lastSyncAttempt: new Date(),
        syncErrorMessage: null
      });

      // Step 1: Create journal entries for payroll expenses
      let journalEntry;
      try {
        // TODO: Implement journal entry creation using unified payroll service
        // For now, we'll skip this step until the accounting integration is properly implemented
        logger.info('Journal entry creation skipped - using unified payroll service', LogCategory.INTEGRATION, {
          payrollRunId
        });

        // Create a placeholder journal entry object
        journalEntry = { _id: 'placeholder-journal-entry' };

      } catch (error) {
        logger.error('Failed to create journal entries', LogCategory.INTEGRATION, error);
        throw error;
      }

      // Step 2: Create payment journal entries
      try {
        // TODO: Implement payment journal entry creation using unified payroll service
        // For now, we'll skip this step until the accounting integration is properly implemented
        logger.info('Payment journal entry creation skipped - using unified payroll service', LogCategory.INTEGRATION, {
          payrollRunId
        });

        // Create a placeholder payment journal entry object
        const paymentJournalEntry = { _id: 'placeholder-payment-journal-entry' };

        // Update payroll run with payment journal entry ID
        await PayrollRun.findByIdAndUpdate(payrollRunId, {
          paymentJournalEntryId: paymentJournalEntry._id
        });

      } catch (error) {
        logger.error('Failed to create payment journal entries', LogCategory.INTEGRATION, error);
        // Don't throw here as the main journal entry was successful
      }

      // Step 3: Update budget impact with real-time integration
      try {
        await this.payrollBudgetIntegrationService.updateBudgetActuals(payrollRunId, userId);
        logger.info('Budget impact updated successfully', LogCategory.INTEGRATION, {
          payrollRunId
        });
      } catch (error) {
        logger.error('Failed to update budget impact', LogCategory.INTEGRATION, error);
        // Don't throw here as the main integration was successful
      }

      // Step 4: Update financial statements with payroll data
      try {
        await this.payrollFinancialIntegrationService.updateFinancialStatements(payrollRunId, userId);
        logger.info('Financial statements updated successfully', LogCategory.INTEGRATION, {
          payrollRunId
        });
      } catch (error) {
        logger.error('Failed to update financial statements', LogCategory.INTEGRATION, error);
        // Don't throw here as the main integration was successful
      }

      // Step 4: Mark integration as completed
      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        accountingStatus: 'completed',
        syncErrorMessage: null
      });

      logger.info('Payroll-accounting integration completed successfully', LogCategory.INTEGRATION, {
        payrollRunId,
        journalEntryId: journalEntry._id
      });

    } catch (error) {
      logger.error('Payroll-accounting integration failed', LogCategory.INTEGRATION, error);
      
      // Update payroll run with error status
      try {
        await connectToDatabase();
        const PayrollRun = mongoose.model('PayrollRun');
        await PayrollRun.findByIdAndUpdate(payrollRunId, {
          accountingStatus: 'failed',
          syncErrorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      } catch (updateError) {
        logger.error('Failed to update payroll run error status', LogCategory.INTEGRATION, updateError);
      }

      throw error;
    }
  }

  /**
   * Get budget variance summary for a fiscal year
   * @param fiscalYear - Fiscal year to get summary for
   * @param period - Optional specific period
   */
  async getBudgetVarianceSummary(fiscalYear: string, period?: string): Promise<any> {
    try {
      return await this.payrollBudgetIntegrationService.getBudgetVarianceSummary(fiscalYear, period);
    } catch (error) {
      logger.error('Failed to get budget variance summary', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Check budget variances and send alerts for a specific payroll run
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user
   */
  async checkBudgetVariances(payrollRunId: string, userId: string): Promise<void> {
    try {
      await this.payrollBudgetIntegrationService.checkVariancesAndSendAlerts(payrollRunId, userId);
    } catch (error) {
      logger.error('Failed to check budget variances', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive payroll financial report
   * @param startDate - Start date for the report
   * @param endDate - End date for the report
   * @param options - Additional options
   */
  async generatePayrollFinancialReport(
    startDate: Date,
    endDate: Date,
    options: {
      departmentId?: string;
      includeComparison?: boolean;
      comparisonStartDate?: Date;
      comparisonEndDate?: Date;
      includeForecasting?: boolean;
    } = {}
  ): Promise<any> {
    try {
      return await this.payrollFinancialIntegrationService.generatePayrollFinancialReport(
        startDate,
        endDate,
        options
      );
    } catch (error) {
      logger.error('Failed to generate payroll financial report', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Get payroll financial summary for a period
   * @param startDate - Start date for the summary
   * @param endDate - End date for the summary
   * @param departmentId - Optional department filter
   */
  async getPayrollFinancialSummary(
    startDate: Date,
    endDate: Date,
    departmentId?: string
  ): Promise<any> {
    try {
      return await this.payrollFinancialIntegrationService.getPayrollFinancialSummary(
        startDate,
        endDate,
        departmentId
      );
    } catch (error) {
      logger.error('Failed to get payroll financial summary', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update financial statements for a specific payroll run
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user
   */
  async updateFinancialStatements(payrollRunId: string, userId: string): Promise<void> {
    try {
      await this.payrollFinancialIntegrationService.updateFinancialStatements(payrollRunId, userId);
    } catch (error) {
      logger.error('Failed to update financial statements', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Retry failed integration for a payroll run
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user retrying the integration
   */
  async retryIntegration(payrollRunId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      
      // Get PayrollRun model
      const PayrollRun = mongoose.model('PayrollRun');
      
      // Get the payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      // Verify the payroll run is in failed state
      if (payrollRun.accountingStatus !== 'failed') {
        throw new Error(`Payroll run is not in failed state. Current status: ${payrollRun.accountingStatus}`);
      }

      // Reset accounting status to pending and retry
      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        accountingStatus: 'pending',
        syncErrorMessage: null
      });

      // Process the integration
      await this.processPayrollCompletion(payrollRunId, userId);

    } catch (error) {
      logger.error('Failed to retry payroll integration', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Get integration status for multiple payroll runs
   * @param payrollRunIds - Array of payroll run IDs
   */
  async getIntegrationStatus(payrollRunIds?: string[]): Promise<any[]> {
    try {
      await connectToDatabase();
      
      // Get PayrollRun model
      const PayrollRun = mongoose.model('PayrollRun');
      
      // Build query
      const query: any = {};
      if (payrollRunIds && payrollRunIds.length > 0) {
        query._id = { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) };
      }

      // Get payroll runs with integration status
      const payrollRuns = await PayrollRun.find(query)
        .select('name status accountingStatus journalEntryId paymentJournalEntryId lastSyncAttempt syncErrorMessage')
        .populate('journalEntryId', 'reference status')
        .populate('paymentJournalEntryId', 'reference status')
        .sort({ createdAt: -1 });

      return payrollRuns.map(run => ({
        id: run._id,
        name: run.name,
        payrollStatus: run.status,
        accountingStatus: run.accountingStatus,
        journalEntry: run.journalEntryId ? {
          id: run.journalEntryId._id,
          reference: run.journalEntryId.reference,
          status: run.journalEntryId.status
        } : null,
        paymentJournalEntry: run.paymentJournalEntryId ? {
          id: run.paymentJournalEntryId._id,
          reference: run.paymentJournalEntryId.reference,
          status: run.paymentJournalEntryId.status
        } : null,
        lastSyncAttempt: run.lastSyncAttempt,
        syncErrorMessage: run.syncErrorMessage
      }));

    } catch (error) {
      logger.error('Failed to get integration status', LogCategory.INTEGRATION, error);
      throw error;
    }
  }
}

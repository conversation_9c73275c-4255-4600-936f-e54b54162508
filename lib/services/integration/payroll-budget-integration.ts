import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { PayrollBudgetImpact } from '@/models/payroll/PayrollBudgetImpact';

/**
 * Service for integrating payroll with budget tracking and variance monitoring
 */
export class PayrollBudgetIntegrationService {
  
  /**
   * Update budget actuals based on payroll run
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user performing the update
   */
  async updateBudgetActuals(payrollRunId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Updating budget actuals for payroll run', LogCategory.INTEGRATION, {
        payrollRunId,
        userId
      });

      // Get PayrollRun model
      const PayrollRun = mongoose.model('PayrollRun');
      const Budget = mongoose.model('Budget');
      const BudgetCategory = mongoose.model('BudgetCategory');
      const Department = mongoose.model('Department');

      // Get the payroll run with department information
      const payrollRun = await PayrollRun.findById(payrollRunId)
        .populate('departments', 'name code budgetCategoryId');

      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      // Find the active budget for the payroll period
      const fiscalYear = payrollRun.payPeriod.year.toString();
      const budget = await Budget.findOne({
        fiscalYear,
        status: { $in: ['approved', 'active'] },
        startDate: { $lte: payrollRun.payPeriod.endDate },
        endDate: { $gte: payrollRun.payPeriod.startDate }
      });

      if (!budget) {
        logger.warn('No active budget found for payroll period', LogCategory.INTEGRATION, {
          payrollRunId,
          fiscalYear,
          payPeriod: payrollRun.payPeriod
        });
        return;
      }

      // Calculate budget impact for each department
      const departmentImpacts = await this.calculateDepartmentBudgetImpacts(
        payrollRun,
        budget,
        userId
      );

      // Create or update budget impact record
      await this.createBudgetImpactRecord(
        payrollRun,
        budget,
        departmentImpacts,
        userId
      );

      // Update budget actual amounts
      await this.updateBudgetActualAmounts(budget, departmentImpacts);

      // Check for variances and send alerts if needed
      await this.checkVariancesAndSendAlerts(payrollRunId, userId);

      logger.info('Budget actuals updated successfully', LogCategory.INTEGRATION, {
        payrollRunId,
        budgetId: budget._id,
        departmentCount: departmentImpacts.length
      });

    } catch (error) {
      logger.error('Failed to update budget actuals', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Calculate budget impact for each department
   */
  private async calculateDepartmentBudgetImpacts(
    payrollRun: any,
    budget: any,
    userId: string
  ): Promise<any[]> {
    try {
      const BudgetCategory = mongoose.model('BudgetCategory');
      const Employee = mongoose.model('Employee');

      const departmentImpacts = [];

      // If no departments specified, treat as general payroll expense
      if (!payrollRun.departments || payrollRun.departments.length === 0) {
        // Find general salary expense category
        const salaryCategory = await BudgetCategory.findOne({
          budget: budget._id,
          name: { $regex: /salary|payroll|personnel/i },
          type: 'expense'
        });

        if (salaryCategory) {
          departmentImpacts.push({
            departmentId: null,
            departmentName: 'General',
            categoryId: salaryCategory._id,
            categoryName: salaryCategory.name,
            budgetedAmount: salaryCategory.total || 0,
            actualAmount: payrollRun.totalGrossSalary,
            variance: (salaryCategory.total || 0) - payrollRun.totalGrossSalary,
            variancePercentage: salaryCategory.total ? 
              (((salaryCategory.total - payrollRun.totalGrossSalary) / salaryCategory.total) * 100) : 0
          });
        }
      } else {
        // Calculate impact for each department
        for (const department of payrollRun.departments) {
          // Get employees in this department for this payroll run
          const departmentEmployees = await Employee.find({
            department: department._id,
            status: 'active'
          });

          // Calculate total salary for this department
          // Note: This is a simplified calculation. In a real implementation,
          // you would get the actual salary records for this payroll run
          const departmentSalaryTotal = departmentEmployees.length > 0 ? 
            (payrollRun.totalGrossSalary / payrollRun.totalEmployees) * departmentEmployees.length : 0;

          // Find budget category for this department
          const departmentCategory = await BudgetCategory.findOne({
            budget: budget._id,
            $or: [
              { name: { $regex: new RegExp(department.name, 'i') } },
              { name: { $regex: /salary|payroll|personnel/i } }
            ],
            type: 'expense'
          });

          if (departmentCategory) {
            const budgetedAmount = departmentCategory.total || 0;
            const variance = budgetedAmount - departmentSalaryTotal;
            const variancePercentage = budgetedAmount ? 
              ((variance / budgetedAmount) * 100) : 0;

            departmentImpacts.push({
              departmentId: department._id,
              departmentName: department.name,
              categoryId: departmentCategory._id,
              categoryName: departmentCategory.name,
              budgetedAmount,
              actualAmount: departmentSalaryTotal,
              variance,
              variancePercentage
            });
          }
        }
      }

      return departmentImpacts;

    } catch (error) {
      logger.error('Failed to calculate department budget impacts', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Create budget impact record
   */
  private async createBudgetImpactRecord(
    payrollRun: any,
    budget: any,
    departmentImpacts: any[],
    userId: string
  ): Promise<void> {
    try {
      // Calculate overall totals
      const totalBudgeted = departmentImpacts.reduce((sum, impact) => sum + impact.budgetedAmount, 0);
      const totalActual = departmentImpacts.reduce((sum, impact) => sum + impact.actualAmount, 0);
      const totalVariance = totalBudgeted - totalActual;
      const totalVariancePercentage = totalBudgeted ? ((totalVariance / totalBudgeted) * 100) : 0;

      // Determine variance status
      let varianceStatus = 'within_budget';
      if (totalVariancePercentage < -10) {
        varianceStatus = 'over_budget';
      } else if (totalVariancePercentage > 10) {
        varianceStatus = 'under_budget';
      }

      // Create budget impact record
      const budgetImpact = new PayrollBudgetImpact({
        payrollRunId: payrollRun._id,
        budgetId: budget._id,
        budgetedAmount: totalBudgeted,
        actualAmount: totalActual,
        variance: totalVariance,
        variancePercentage: totalVariancePercentage,
        fiscalYear: budget.fiscalYear,
        period: `${payrollRun.payPeriod.year}-${payrollRun.payPeriod.month.toString().padStart(2, '0')}`,
        payPeriod: payrollRun.payPeriod,
        categoryImpacts: departmentImpacts.map(impact => ({
          categoryId: impact.categoryId,
          categoryName: impact.categoryName,
          categoryType: 'expense',
          budgetedAmount: impact.budgetedAmount,
          actualAmount: impact.actualAmount,
          variance: impact.variance,
          variancePercentage: impact.variancePercentage
        })),
        varianceStatus,
        varianceThreshold: 10, // 10% threshold
        requiresApproval: Math.abs(totalVariancePercentage) > 15, // Require approval for >15% variance
        alertLevel: this.calculateAlertLevel(Math.abs(totalVariancePercentage)),
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      await budgetImpact.save();

      // Update PayrollRun with budget impact reference
      await mongoose.model('PayrollRun').findByIdAndUpdate(payrollRun._id, {
        budgetImpactId: budgetImpact._id
      });

      logger.info('Budget impact record created', LogCategory.INTEGRATION, {
        budgetImpactId: budgetImpact._id,
        varianceStatus,
        totalVariancePercentage
      });

    } catch (error) {
      logger.error('Failed to create budget impact record', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update budget actual amounts
   */
  private async updateBudgetActualAmounts(budget: any, departmentImpacts: any[]): Promise<void> {
    try {
      const BudgetCategory = mongoose.model('BudgetCategory');

      // Update each category's actual amount
      for (const impact of departmentImpacts) {
        await BudgetCategory.findByIdAndUpdate(impact.categoryId, {
          $inc: { actualAmount: impact.actualAmount }
        });
      }

      // Update budget totals
      const totalActualExpense = departmentImpacts.reduce((sum, impact) => sum + impact.actualAmount, 0);
      
      await mongoose.model('Budget').findByIdAndUpdate(budget._id, {
        $inc: { totalActualExpense: totalActualExpense },
        lastActualUpdateDate: new Date()
      });

      logger.info('Budget actual amounts updated', LogCategory.INTEGRATION, {
        budgetId: budget._id,
        totalActualExpense
      });

    } catch (error) {
      logger.error('Failed to update budget actual amounts', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Check variances and send alerts
   */
  async checkVariancesAndSendAlerts(payrollRunId: string, userId: string): Promise<void> {
    try {
      // Get the budget impact record
      const budgetImpact = await PayrollBudgetImpact.findOne({ payrollRunId })
        .populate('budgetId', 'name fiscalYear')
        .populate('payrollRunId', 'name');

      if (!budgetImpact || budgetImpact.alertsSent) {
        return; // No impact record or alerts already sent
      }

      // Check if variance is significant
      if (budgetImpact.isSignificantVariance()) {
        // Send alerts based on alert level
        await this.sendVarianceAlerts(budgetImpact, userId);
        
        // Mark alerts as sent
        budgetImpact.alertsSent = true;
        budgetImpact.alertLevel = budgetImpact.calculateAlertLevel();
        await budgetImpact.save();
      }

    } catch (error) {
      logger.error('Failed to check variances and send alerts', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Send variance alerts
   */
  private async sendVarianceAlerts(budgetImpact: any, userId: string): Promise<void> {
    try {
      // This would integrate with your notification system
      // For now, we'll just log the alert
      logger.warn('Budget variance alert', LogCategory.INTEGRATION, {
        payrollRunId: budgetImpact.payrollRunId,
        budgetId: budgetImpact.budgetId,
        variancePercentage: budgetImpact.variancePercentage,
        alertLevel: budgetImpact.alertLevel,
        requiresApproval: budgetImpact.requiresApproval
      });

      // TODO: Implement actual notification sending
      // - Email notifications to budget managers
      // - In-app notifications
      // - Slack/Teams integration
      // - SMS for critical alerts

    } catch (error) {
      logger.error('Failed to send variance alerts', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Calculate alert level based on variance percentage
   */
  private calculateAlertLevel(variancePercentage: number): 'info' | 'warning' | 'critical' {
    if (variancePercentage > 25) {
      return 'critical';
    } else if (variancePercentage > 10) {
      return 'warning';
    } else {
      return 'info';
    }
  }

  /**
   * Get budget variance summary for a period
   */
  async getBudgetVarianceSummary(
    fiscalYear: string,
    period?: string
  ): Promise<any> {
    try {
      await connectToDatabase();

      const filter: any = { fiscalYear };
      if (period) {
        filter.period = period;
      }

      const budgetImpacts = await PayrollBudgetImpact.find(filter)
        .populate('budgetId', 'name')
        .populate('payrollRunId', 'name')
        .sort({ createdAt: -1 });

      // Calculate summary statistics
      const summary = {
        totalImpacts: budgetImpacts.length,
        totalBudgeted: budgetImpacts.reduce((sum, impact) => sum + impact.budgetedAmount, 0),
        totalActual: budgetImpacts.reduce((sum, impact) => sum + impact.actualAmount, 0),
        totalVariance: budgetImpacts.reduce((sum, impact) => sum + impact.variance, 0),
        averageVariancePercentage: budgetImpacts.length > 0 ? 
          budgetImpacts.reduce((sum, impact) => sum + Math.abs(impact.variancePercentage), 0) / budgetImpacts.length : 0,
        withinBudget: budgetImpacts.filter(impact => impact.varianceStatus === 'within_budget').length,
        overBudget: budgetImpacts.filter(impact => impact.varianceStatus === 'over_budget').length,
        underBudget: budgetImpacts.filter(impact => impact.varianceStatus === 'under_budget').length,
        requiresApproval: budgetImpacts.filter(impact => impact.requiresApproval).length,
        criticalAlerts: budgetImpacts.filter(impact => impact.alertLevel === 'critical').length,
        warningAlerts: budgetImpacts.filter(impact => impact.alertLevel === 'warning').length
      };

      return {
        summary,
        impacts: budgetImpacts
      };

    } catch (error) {
      logger.error('Failed to get budget variance summary', LogCategory.INTEGRATION, error);
      throw error;
    }
  }
}

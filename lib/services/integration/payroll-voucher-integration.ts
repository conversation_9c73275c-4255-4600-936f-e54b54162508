// lib/services/integration/payroll-voucher-integration.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import { Voucher, IVoucher } from '@/models/accounting/Voucher';
import { VoucherService } from '../accounting/voucher-service';
import { format } from 'date-fns';

/**
 * Interface for payroll run lean query result
 */
interface PayrollRunLean {
  _id: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled';
  voucherId?: mongoose.Types.ObjectId;
  voucherStatus?: 'not_created' | 'created' | 'pending_approval' | 'approved' | 'rejected' | 'posted';
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  totalEmployees: number;
  totalGrossSalary: number;
  totalTax: number;
  totalDeductions: number;
  totalNetSalary: number;
  currency: string;
  createdBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvedBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvedAt?: Date;
}

/**
 * Interface for voucher lean query result
 */
interface VoucherLean {
  _id: mongoose.Types.ObjectId;
  voucherNumber: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'posted' | 'cancelled';
  totalAmount: number;
  payrollRunId?: mongoose.Types.ObjectId;
  createdBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvedBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvalWorkflow?: {
    currentApprover?: {
      _id: mongoose.Types.ObjectId;
      name: string;
      email: string;
      role: string;
    };
  };
  createdAt: Date;
  approvedAt?: Date;
}

/**
 * Interface for payroll voucher creation data
 */
export interface PayrollVoucherData {
  payrollRunId: string;
  voucherType: 'payment';
  description: string;
  totalAmount: number;
  fiscalYear: string;
  payee: string;
  paymentMethod: string;
  items: {
    description: string;
    account: string;
    debit: number;
    credit: number;
  }[];
}

/**
 * Service for integrating payroll runs with voucher management
 */
export class PayrollVoucherIntegrationService {
  private voucherService: VoucherService;

  constructor() {
    this.voucherService = new VoucherService();
  }

  /**
   * Create voucher from approved payroll run
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID creating the voucher
   * @returns Created voucher
   */
  async createVoucherFromPayrollRun(payrollRunId: string, userId: string): Promise<IVoucher> {
    try {
      await connectToDatabase();
      logger.info('Creating voucher from payroll run', LogCategory.INTEGRATION, { payrollRunId, userId });

      // Get payroll run details
      const payrollRun = await PayrollRun.findById(payrollRunId)
        .populate('createdBy', 'name email')
        .lean() as PayrollRunLean | null;

      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      // Verify payroll run is approved
      if (payrollRun.status !== 'approved') {
        throw new Error(`Payroll run must be approved before creating voucher. Current status: ${payrollRun.status}`);
      }

      // Check if voucher already exists for this payroll run
      const existingVoucher = await Voucher.findOne({ payrollRunId: new mongoose.Types.ObjectId(payrollRunId) });
      if (existingVoucher) {
        logger.warn('Voucher already exists for payroll run', LogCategory.INTEGRATION, { 
          payrollRunId, 
          existingVoucherId: existingVoucher._id 
        });
        return existingVoucher;
      }

      // Get payroll records for detailed breakdown
      const payrollRecords = await PayrollRecord.find({ payrollRunId })
        .populate('employeeId', 'firstName lastName employeeNumber')
        .lean();

      // Generate voucher data
      const voucherData = await this.generateVoucherDataFromPayroll(payrollRun, payrollRecords);

      // Create voucher
      const voucher = await this.voucherService.createVoucher({
        ...voucherData,
        date: new Date(), // Add the required date field
        payrollRunId,
        voucherCategory: 'payroll' as const,
        isAutoGenerated: true,
        sourceModule: 'payroll' as const
      }, userId);

      // Update payroll run with voucher reference
      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        $set: {
          voucherId: voucher._id,
          voucherStatus: 'created',
          updatedBy: new mongoose.Types.ObjectId(userId)
        }
      });

      logger.info('Voucher created successfully from payroll run', LogCategory.INTEGRATION, {
        payrollRunId,
        voucherId: voucher._id,
        voucherNumber: voucher.voucherNumber
      });

      return voucher;

    } catch (error) {
      logger.error('Error creating voucher from payroll run', LogCategory.INTEGRATION, error);
      throw error instanceof Error ? error : new Error('Unknown error occurred while creating voucher from payroll run');
    }
  }

  /**
   * Sync voucher approval status to payroll run
   * @param voucherId - Voucher ID
   * @param payrollRunId - Payroll run ID
   */
  async syncVoucherApprovalToPayroll(voucherId: string, payrollRunId: string): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Syncing voucher approval to payroll run', LogCategory.INTEGRATION, { voucherId, payrollRunId });

      // Get voucher status
      const voucher = await Voucher.findById(voucherId).lean() as VoucherLean | null;
      if (!voucher) {
        throw new Error(`Voucher with ID ${voucherId} not found`);
      }

      // Update payroll run status based on voucher status
      let payrollStatus: 'approved' | 'paid' = 'approved';
      let voucherStatus: 'approved' | 'posted' | 'rejected' = 'approved';

      if (voucher.status === 'posted') {
        payrollStatus = 'paid';
        voucherStatus = 'posted';
      } else if (voucher.status === 'rejected') {
        payrollStatus = 'approved'; // Keep payroll approved, but voucher rejected
        voucherStatus = 'rejected';
      }

      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        $set: {
          voucherStatus,
          ...(voucher.status === 'posted' && {
            status: payrollStatus,
            paidAt: new Date()
          })
        }
      });

      logger.info('Payroll run status synced with voucher', LogCategory.INTEGRATION, {
        voucherId,
        payrollRunId,
        voucherStatus: voucher.status,
        payrollStatus
      });

    } catch (error) {
      logger.error('Error syncing voucher approval to payroll', LogCategory.INTEGRATION, error);
      throw error instanceof Error ? error : new Error('Unknown error occurred while syncing voucher approval to payroll');
    }
  }

  /**
   * Process voucher posting to mark payroll as paid
   * @param voucherId - Voucher ID
   * @param userId - User ID processing the posting
   */
  async processVoucherPosting(voucherId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Processing voucher posting for payroll', LogCategory.INTEGRATION, { voucherId, userId });

      // Get voucher with payroll run reference
      const voucher = await Voucher.findById(voucherId).lean() as VoucherLean | null;
      if (!voucher) {
        throw new Error(`Voucher with ID ${voucherId} not found`);
      }

      if (!voucher.payrollRunId) {
        logger.warn('Voucher is not linked to a payroll run', LogCategory.INTEGRATION, { voucherId });
        return;
      }

      // Update voucher status to posted
      await Voucher.findByIdAndUpdate(voucherId, {
        $set: {
          status: 'posted',
          postedBy: new mongoose.Types.ObjectId(userId),
          postedAt: new Date(),
          updatedBy: new mongoose.Types.ObjectId(userId)
        }
      });

      // Update payroll run status to paid
      await PayrollRun.findByIdAndUpdate(voucher.payrollRunId, {
        $set: {
          status: 'paid',
          voucherStatus: 'posted',
          paidAt: new Date(),
          updatedBy: new mongoose.Types.ObjectId(userId)
        }
      });

      // Update all payroll records to paid status
      await PayrollRecord.updateMany(
        { payrollRunId: voucher.payrollRunId },
        {
          $set: {
            status: 'paid',
            paymentDate: new Date(),
            updatedBy: new mongoose.Types.ObjectId(userId)
          }
        }
      );

      logger.info('Voucher posting processed successfully', LogCategory.INTEGRATION, {
        voucherId,
        payrollRunId: voucher.payrollRunId
      });

    } catch (error) {
      logger.error('Error processing voucher posting', LogCategory.INTEGRATION, error);
      throw error instanceof Error ? error : new Error('Unknown error occurred while processing voucher posting');
    }
  }

  /**
   * Get payroll runs ready for voucher creation
   * @returns Payroll runs that are approved but don't have vouchers
   */
  async getPayrollRunsReadyForVouchers(): Promise<PayrollRunLean[]> {
    try {
      await connectToDatabase();

      const payrollRuns = await PayrollRun.find({
        status: 'approved',
        $or: [
          { voucherId: { $exists: false } },
          { voucherId: null },
          { voucherStatus: 'not_created' }
        ]
      })
        .populate('createdBy', 'name email')
        .populate('approvedBy', 'name email')
        .sort({ approvedAt: -1 })
        .lean() as unknown as PayrollRunLean[];

      return payrollRuns;

    } catch (error) {
      logger.error('Error getting payroll runs ready for vouchers', LogCategory.INTEGRATION, error);
      throw error instanceof Error ? error : new Error('Unknown error occurred while getting payroll runs ready for vouchers');
    }
  }

  /**
   * Get voucher status for a payroll run
   * @param payrollRunId - Payroll run ID
   * @returns Voucher status information
   */
  async getVoucherStatusForPayrollRun(payrollRunId: string): Promise<{
    payrollRun: {
      id: mongoose.Types.ObjectId;
      status: string;
      voucherStatus: string;
    };
    voucher: {
      id: mongoose.Types.ObjectId;
      voucherNumber: string;
      status: string;
      totalAmount: number;
      createdBy: {
        _id: mongoose.Types.ObjectId;
        name: string;
        email: string;
      } | null;
      approvedBy: {
        _id: mongoose.Types.ObjectId;
        name: string;
        email: string;
      } | null;
      currentApprover: {
        _id: mongoose.Types.ObjectId;
        name: string;
        email: string;
        role: string;
      } | null;
      createdAt: Date;
      approvedAt?: Date;
    } | null;
  }> {
    try {
      await connectToDatabase();

      const payrollRun = await PayrollRun.findById(payrollRunId)
        .select('voucherId voucherStatus status')
        .lean() as unknown as Pick<PayrollRunLean, '_id' | 'status' | 'voucherId' | 'voucherStatus'> | null;

      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      let voucher: VoucherLean | null = null;
      if (payrollRun.voucherId) {
        voucher = await Voucher.findById(payrollRun.voucherId)
          .populate('createdBy', 'name email')
          .populate('approvedBy', 'name email')
          .populate('approvalWorkflow.currentApprover', 'name email role')
          .lean() as unknown as VoucherLean | null;
      }

      return {
        payrollRun: {
          id: payrollRun._id,
          status: payrollRun.status,
          voucherStatus: payrollRun.voucherStatus || 'not_created'
        },
        voucher: voucher ? {
          id: voucher._id,
          voucherNumber: voucher.voucherNumber,
          status: voucher.status,
          totalAmount: voucher.totalAmount,
          createdBy: voucher.createdBy || null,
          approvedBy: voucher.approvedBy || null,
          currentApprover: voucher.approvalWorkflow?.currentApprover || null,
          createdAt: voucher.createdAt,
          approvedAt: voucher.approvedAt
        } : null
      };

    } catch (error) {
      logger.error('Error getting voucher status for payroll run', LogCategory.INTEGRATION, error);
      throw error instanceof Error ? error : new Error('Unknown error occurred while getting voucher status for payroll run');
    }
  }

  /**
   * Generate voucher data from payroll run
   * @param payrollRun - Payroll run data
   * @param payrollRecords - Payroll records
   * @returns Voucher data
   */
  private async generateVoucherDataFromPayroll(
    payrollRun: PayrollRunLean,
    _payrollRecords: unknown[]
  ): Promise<PayrollVoucherData> {
    // Validate payroll run data
    if (!payrollRun || !payrollRun.payPeriod) {
      throw new Error('Invalid payroll run data: missing payPeriod');
    }

    // Validate required financial fields
    const requiredFields: (keyof Pick<PayrollRunLean, 'totalGrossSalary' | 'totalTax' | 'totalDeductions' | 'totalNetSalary'>)[] =
      ['totalGrossSalary', 'totalTax', 'totalDeductions', 'totalNetSalary'];
    for (const field of requiredFields) {
      if (typeof payrollRun[field] !== 'number') {
        throw new Error(`Invalid payroll run data: missing or invalid ${field}`);
      }
    }

    const payPeriod = payrollRun.payPeriod;
    const periodText = format(new Date(payPeriod.year, payPeriod.month - 1), 'MMMM yyyy');

    // Generate voucher items based on payroll breakdown
    const items = [
      {
        description: `Gross Salaries - ${periodText}`,
        account: 'salary_expense', // This should map to actual chart of accounts
        debit: payrollRun.totalGrossSalary,
        credit: 0
      },
      {
        description: `PAYE Tax Deductions - ${periodText}`,
        account: 'tax_payable',
        debit: 0,
        credit: payrollRun.totalTax
      },
      {
        description: `Other Deductions - ${periodText}`,
        account: 'deductions_payable',
        debit: 0,
        credit: payrollRun.totalDeductions - payrollRun.totalTax
      },
      {
        description: `Net Salaries Payable - ${periodText}`,
        account: 'salaries_payable',
        debit: 0,
        credit: payrollRun.totalNetSalary
      }
    ];

    return {
      payrollRunId: payrollRun._id.toString(),
      voucherType: 'payment',
      description: `Payroll Payment - ${payrollRun.name} (${periodText})`,
      totalAmount: payrollRun.totalGrossSalary, // Use gross salary as the total transaction amount
      fiscalYear: this.getFiscalYear(payPeriod.year, payPeriod.month),
      payee: 'Employees - Salary Payment',
      paymentMethod: 'bank_transfer',
      items
    };
  }

  /**
   * Get fiscal year from date
   * @param year - Year
   * @param month - Month
   * @returns Fiscal year string
   */
  private getFiscalYear(year: number, month: number): string {
    // Assuming fiscal year starts in April (month 4)
    if (month >= 4) {
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  }
}

// Export singleton instance
export const payrollVoucherIntegrationService = new PayrollVoucherIntegrationService();

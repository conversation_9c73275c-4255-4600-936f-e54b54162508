import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { PayrollFinancialReportingService } from '../accounting/payroll-financial-reporting-service';

/**
 * Service for real-time integration of payroll data into financial statements
 */
export class PayrollFinancialIntegrationService {
  private payrollFinancialReportingService: PayrollFinancialReportingService;

  constructor() {
    this.payrollFinancialReportingService = new PayrollFinancialReportingService();
  }

  /**
   * Update financial statements with payroll data
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user performing the update
   */
  async updateFinancialStatements(payrollRunId: string, userId: string): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Updating financial statements with payroll data', LogCategory.INTEGRATION, {
        payrollRunId,
        userId
      });

      // Get PayrollRun model
      const PayrollRun = mongoose.model('PayrollRun');
      const FinancialStatement = mongoose.model('FinancialStatement');

      // Get the payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }

      // Update Income Statement
      await this.updateIncomeStatement(payrollRun, userId);

      // Update Balance Sheet
      await this.updateBalanceSheet(payrollRun, userId);

      // Update Cash Flow Statement
      await this.updateCashFlowStatement(payrollRun, userId);

      logger.info('Financial statements updated successfully', LogCategory.INTEGRATION, {
        payrollRunId,
        userId
      });

    } catch (error) {
      logger.error('Failed to update financial statements', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive payroll financial report
   * @param startDate - Start date for the report
   * @param endDate - End date for the report
   * @param options - Additional options
   */
  async generatePayrollFinancialReport(
    startDate: Date,
    endDate: Date,
    options: {
      departmentId?: string;
      includeComparison?: boolean;
      comparisonStartDate?: Date;
      comparisonEndDate?: Date;
      includeForecasting?: boolean;
    } = {}
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating comprehensive payroll financial report', LogCategory.INTEGRATION, {
        startDate,
        endDate,
        options
      });

      // Generate P&L data
      const plData = await this.payrollFinancialReportingService.generatePayrollPLData(
        startDate,
        endDate,
        {
          departmentId: options.departmentId,
          includeComparison: options.includeComparison,
          comparisonStartDate: options.comparisonStartDate,
          comparisonEndDate: options.comparisonEndDate
        }
      );

      // Generate Balance Sheet data
      const balanceSheetData = await this.payrollFinancialReportingService.generatePayrollBalanceSheetData(
        endDate,
        {
          departmentId: options.departmentId,
          includeComparison: options.includeComparison,
          comparisonDate: options.comparisonEndDate
        }
      );

      // Generate Cash Flow data
      const cashFlowData = await this.payrollFinancialReportingService.generatePayrollCashFlowData(
        startDate,
        endDate,
        {
          departmentId: options.departmentId,
          includeComparison: options.includeComparison,
          comparisonStartDate: options.comparisonStartDate,
          comparisonEndDate: options.comparisonEndDate
        }
      );

      // Generate Analytics data
      const analyticsData = await this.payrollFinancialReportingService.generatePayrollAnalytics(
        startDate,
        endDate,
        {
          departmentId: options.departmentId,
          includeForecasting: options.includeForecasting
        }
      );

      return {
        reportInfo: {
          title: 'Comprehensive Payroll Financial Report',
          period: { startDate, endDate },
          generatedAt: new Date(),
          options
        },
        profitAndLoss: plData,
        balanceSheet: balanceSheetData,
        cashFlow: cashFlowData,
        analytics: analyticsData,
        summary: {
          totalPayrollExpense: plData.summary.totalGrossSalary,
          totalPayrollLiabilities: balanceSheetData.summary.totalSalaryPayable,
          totalCashOutflow: cashFlowData.summary.totalCashOutflow,
          averageMonthlyPayroll: analyticsData.summary.averageMonthlyPayroll
        }
      };

    } catch (error) {
      logger.error('Failed to generate payroll financial report', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update Income Statement with payroll data
   */
  private async updateIncomeStatement(payrollRun: any, userId: string): Promise<void> {
    try {
      const FinancialStatement = mongoose.model('FinancialStatement');

      // Find or create current period income statement
      const fiscalYear = payrollRun.payPeriod.year.toString();
      const period = `${payrollRun.payPeriod.year}-${payrollRun.payPeriod.month.toString().padStart(2, '0')}`;

      let incomeStatement = await FinancialStatement.findOne({
        type: 'income_statement',
        fiscalYear,
        fiscalPeriod: period,
        status: { $in: ['draft', 'published'] }
      });

      if (!incomeStatement) {
        // Create new income statement
        incomeStatement = new FinancialStatement({
          name: `Income Statement - ${period}`,
          type: 'income_statement',
          period: 'monthly',
          startDate: new Date(payrollRun.payPeriod.startDate),
          endDate: new Date(payrollRun.payPeriod.endDate),
          fiscalYear,
          fiscalPeriod: period,
          data: {
            revenue: { categories: [], total: 0 },
            expenses: { categories: [], total: 0 },
            netIncome: 0
          },
          status: 'draft',
          currency: 'MWK',
          createdBy: new mongoose.Types.ObjectId(userId)
        });
      }

      // Update payroll expenses in the income statement
      await this.updateIncomeStatementPayrollExpenses(incomeStatement, payrollRun);

      // Save the updated income statement
      await incomeStatement.save();

      logger.info('Income statement updated with payroll data', LogCategory.INTEGRATION, {
        incomeStatementId: incomeStatement._id,
        payrollRunId: payrollRun._id
      });

    } catch (error) {
      logger.error('Failed to update income statement', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update Balance Sheet with payroll liabilities
   */
  private async updateBalanceSheet(payrollRun: any, userId: string): Promise<void> {
    try {
      const FinancialStatement = mongoose.model('FinancialStatement');

      // Find or create current balance sheet
      const fiscalYear = payrollRun.payPeriod.year.toString();
      const asOfDate = new Date(payrollRun.payPeriod.endDate);

      let balanceSheet = await FinancialStatement.findOne({
        type: 'balance_sheet',
        fiscalYear,
        endDate: { $gte: asOfDate },
        status: { $in: ['draft', 'published'] }
      });

      if (!balanceSheet) {
        // Create new balance sheet
        balanceSheet = new FinancialStatement({
          name: `Balance Sheet - ${asOfDate.toISOString().split('T')[0]}`,
          type: 'balance_sheet',
          period: 'monthly',
          startDate: new Date(asOfDate.getFullYear(), 0, 1),
          endDate: asOfDate,
          fiscalYear,
          data: {
            assets: { categories: [], total: 0 },
            liabilities: { categories: [], total: 0 },
            equity: { categories: [], total: 0 }
          },
          status: 'draft',
          currency: 'MWK',
          createdBy: new mongoose.Types.ObjectId(userId)
        });
      }

      // Update payroll liabilities in the balance sheet
      await this.updateBalanceSheetPayrollLiabilities(balanceSheet, payrollRun);

      // Save the updated balance sheet
      await balanceSheet.save();

      logger.info('Balance sheet updated with payroll liabilities', LogCategory.INTEGRATION, {
        balanceSheetId: balanceSheet._id,
        payrollRunId: payrollRun._id
      });

    } catch (error) {
      logger.error('Failed to update balance sheet', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update Cash Flow Statement with payroll payments
   */
  private async updateCashFlowStatement(payrollRun: any, userId: string): Promise<void> {
    try {
      const FinancialStatement = mongoose.model('FinancialStatement');

      // Find or create current cash flow statement
      const fiscalYear = payrollRun.payPeriod.year.toString();
      const period = `${payrollRun.payPeriod.year}-${payrollRun.payPeriod.month.toString().padStart(2, '0')}`;

      let cashFlowStatement = await FinancialStatement.findOne({
        type: 'cash_flow',
        fiscalYear,
        fiscalPeriod: period,
        status: { $in: ['draft', 'published'] }
      });

      if (!cashFlowStatement) {
        // Create new cash flow statement
        cashFlowStatement = new FinancialStatement({
          name: `Cash Flow Statement - ${period}`,
          type: 'cash_flow',
          period: 'monthly',
          startDate: new Date(payrollRun.payPeriod.startDate),
          endDate: new Date(payrollRun.payPeriod.endDate),
          fiscalYear,
          fiscalPeriod: period,
          data: {
            operatingActivities: { categories: [], total: 0 },
            investingActivities: { categories: [], total: 0 },
            financingActivities: { categories: [], total: 0 },
            netCashFlow: 0
          },
          status: 'draft',
          currency: 'MWK',
          createdBy: new mongoose.Types.ObjectId(userId)
        });
      }

      // Update payroll cash flows in the statement
      await this.updateCashFlowStatementPayrollData(cashFlowStatement, payrollRun);

      // Save the updated cash flow statement
      await cashFlowStatement.save();

      logger.info('Cash flow statement updated with payroll data', LogCategory.INTEGRATION, {
        cashFlowStatementId: cashFlowStatement._id,
        payrollRunId: payrollRun._id
      });

    } catch (error) {
      logger.error('Failed to update cash flow statement', LogCategory.INTEGRATION, error);
      throw error;
    }
  }

  /**
   * Update income statement payroll expenses
   */
  private async updateIncomeStatementPayrollExpenses(incomeStatement: any, payrollRun: any): Promise<void> {
    const data = incomeStatement.data;

    // Find or create payroll expenses category
    let payrollCategory = data.expenses.categories.find((cat: any) => cat.name === 'Payroll Expenses');
    if (!payrollCategory) {
      payrollCategory = {
        name: 'Payroll Expenses',
        amount: 0,
        items: []
      };
      data.expenses.categories.push(payrollCategory);
    }

    // Update payroll expense items
    const payrollItems = [
      { name: 'Gross Salaries', amount: payrollRun.totalGrossSalary || 0 },
      { name: 'Employee Benefits', amount: payrollRun.totalAllowances || 0 },
      { name: 'Employer Contributions', amount: (payrollRun.totalGrossSalary || 0) * 0.05 }
    ];

    // Update or add payroll items
    for (const item of payrollItems) {
      const existingItem = payrollCategory.items.find((i: any) => i.name === item.name);
      if (existingItem) {
        existingItem.amount += item.amount;
      } else {
        payrollCategory.items.push(item);
      }
    }

    // Recalculate category total
    payrollCategory.amount = payrollCategory.items.reduce((sum: number, item: any) => sum + item.amount, 0);

    // Recalculate total expenses
    data.expenses.total = data.expenses.categories.reduce((sum: number, cat: any) => sum + cat.amount, 0);

    // Recalculate net income
    data.netIncome = data.revenue.total - data.expenses.total;
  }

  /**
   * Update balance sheet payroll liabilities
   */
  private async updateBalanceSheetPayrollLiabilities(balanceSheet: any, payrollRun: any): Promise<void> {
    const data = balanceSheet.data;

    // Find or create current liabilities category
    let currentLiabilitiesCategory = data.liabilities.categories.find((cat: any) => cat.name === 'Current Liabilities');
    if (!currentLiabilitiesCategory) {
      currentLiabilitiesCategory = {
        name: 'Current Liabilities',
        amount: 0,
        items: []
      };
      data.liabilities.categories.push(currentLiabilitiesCategory);
    }

    // Update payroll liability items (only if payroll is not yet paid)
    if (payrollRun.status !== 'paid') {
      const liabilityItems = [
        { name: 'Salaries Payable', amount: payrollRun.totalNetSalary || 0 },
        { name: 'Tax Withholdings Payable', amount: payrollRun.totalTax || 0 },
        { name: 'Pension Contributions Payable', amount: (payrollRun.totalGrossSalary || 0) * 0.05 }
      ];

      // Update or add liability items
      for (const item of liabilityItems) {
        const existingItem = currentLiabilitiesCategory.items.find((i: any) => i.name === item.name);
        if (existingItem) {
          existingItem.amount += item.amount;
        } else {
          currentLiabilitiesCategory.items.push(item);
        }
      }
    }

    // Recalculate category total
    currentLiabilitiesCategory.amount = currentLiabilitiesCategory.items.reduce((sum: number, item: any) => sum + item.amount, 0);

    // Recalculate total liabilities
    data.liabilities.total = data.liabilities.categories.reduce((sum: number, cat: any) => sum + cat.amount, 0);
  }

  /**
   * Update cash flow statement payroll data
   */
  private async updateCashFlowStatementPayrollData(cashFlowStatement: any, payrollRun: any): Promise<void> {
    const data = cashFlowStatement.data;

    // Find or create operating activities category
    let operatingCategory = data.operatingActivities.categories.find((cat: any) => cat.name === 'Payroll Payments');
    if (!operatingCategory) {
      operatingCategory = {
        name: 'Payroll Payments',
        amount: 0,
        items: []
      };
      data.operatingActivities.categories.push(operatingCategory);
    }

    // Update cash flow items (only if payroll is paid)
    if (payrollRun.status === 'paid') {
      const cashFlowItems = [
        { name: 'Net Salary Payments', amount: -(payrollRun.totalNetSalary || 0) }, // Negative for cash outflow
        { name: 'Tax Payments', amount: -(payrollRun.totalTax || 0) },
        { name: 'Pension Payments', amount: -((payrollRun.totalGrossSalary || 0) * 0.05) }
      ];

      // Update or add cash flow items
      for (const item of cashFlowItems) {
        const existingItem = operatingCategory.items.find((i: any) => i.name === item.name);
        if (existingItem) {
          existingItem.amount += item.amount;
        } else {
          operatingCategory.items.push(item);
        }
      }
    }

    // Recalculate category total
    operatingCategory.amount = operatingCategory.items.reduce((sum: number, item: any) => sum + item.amount, 0);

    // Recalculate total operating activities
    data.operatingActivities.total = data.operatingActivities.categories.reduce((sum: number, cat: any) => sum + cat.amount, 0);

    // Recalculate net cash flow
    data.netCashFlow = data.operatingActivities.total + data.investingActivities.total + data.financingActivities.total;
  }

  /**
   * Get payroll financial summary for a period
   */
  async getPayrollFinancialSummary(
    startDate: Date,
    endDate: Date,
    departmentId?: string
  ): Promise<any> {
    try {
      await connectToDatabase();

      const PayrollRun = mongoose.model('PayrollRun');

      // Build query filter
      const filter: any = {
        'payPeriod.startDate': { $gte: startDate },
        'payPeriod.endDate': { $lte: endDate },
        status: { $in: ['completed', 'paid'] }
      };

      if (departmentId) {
        filter.departments = new mongoose.Types.ObjectId(departmentId);
      }

      // Get payroll runs for the period
      const payrollRuns = await PayrollRun.find(filter)
        .populate('departments', 'name code')
        .sort({ 'payPeriod.startDate': 1 });

      // Calculate summary metrics
      const summary = {
        totalPayrollRuns: payrollRuns.length,
        totalGrossSalary: payrollRuns.reduce((sum, run) => sum + (run.totalGrossSalary || 0), 0),
        totalNetSalary: payrollRuns.reduce((sum, run) => sum + (run.totalNetSalary || 0), 0),
        totalTax: payrollRuns.reduce((sum, run) => sum + (run.totalTax || 0), 0),
        totalDeductions: payrollRuns.reduce((sum, run) => sum + (run.totalDeductions || 0), 0),
        totalEmployees: payrollRuns.reduce((sum, run) => sum + (run.totalEmployees || 0), 0),
        averageGrossSalaryPerEmployee: 0,
        averageNetSalaryPerEmployee: 0
      };

      // Calculate averages
      if (summary.totalEmployees > 0) {
        summary.averageGrossSalaryPerEmployee = summary.totalGrossSalary / summary.totalEmployees;
        summary.averageNetSalaryPerEmployee = summary.totalNetSalary / summary.totalEmployees;
      }

      return {
        period: { startDate, endDate },
        summary,
        payrollRuns: payrollRuns.map(run => ({
          id: run._id,
          name: run.name,
          payPeriod: run.payPeriod,
          status: run.status,
          totalGrossSalary: run.totalGrossSalary,
          totalNetSalary: run.totalNetSalary,
          totalEmployees: run.totalEmployees,
          departments: run.departments
        }))
      };

    } catch (error) {
      logger.error('Failed to get payroll financial summary', LogCategory.INTEGRATION, error);
      throw error;
    }
  }
}

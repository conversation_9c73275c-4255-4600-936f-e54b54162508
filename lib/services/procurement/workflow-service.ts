// lib/services/procurement/workflow-service.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Requisition } from '@/models/procurement/Requisition';
import { PurchaseOrder } from '@/models/procurement/PurchaseOrder';
import { Tender } from '@/models/procurement/Tender';
import { Contract } from '@/models/procurement/Contract';
import { ProcurementCategory } from '@/models/procurement/ProcurementCategory';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';
import { procurementBudgetIntegrationService } from './budget-integration-service';

/**
 * Interface for approval step
 */
interface ApprovalStep {
  level: number;
  approverRole: string;
  approverId?: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected' | 'skipped';
  approvedAt?: Date;
  comments?: string;
  requiredAmount?: number;
}

/**
 * Interface for workflow configuration
 */
interface WorkflowConfig {
  entityType: 'requisition' | 'purchase_order' | 'tender' | 'contract';
  approvalLevels: {
    level: number;
    role: string;
    minAmount?: number;
    maxAmount?: number;
    required: boolean;
  }[];
  autoApprovalRules?: {
    maxAmount?: number;
    trustedSuppliers?: string[];
    categories?: string[];
  };
  notifications: {
    onSubmission: boolean;
    onApproval: boolean;
    onRejection: boolean;
    escalation: boolean;
    escalationHours: number;
  };
}

/**
 * Interface for workflow status
 */
interface WorkflowStatus {
  entityId: string;
  entityType: string;
  currentLevel: number;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  steps: ApprovalStep[];
  submittedAt: Date;
  completedAt?: Date;
  submittedBy: string;
}

/**
 * Service for managing procurement approval workflows
 */
export class ProcurementWorkflowService {
  private static instance: ProcurementWorkflowService;
  private workflows: Map<string, WorkflowStatus> = new Map();

  /**
   * Default workflow configurations
   */
  private static readonly DEFAULT_CONFIGS: Record<string, WorkflowConfig> = {
    requisition: {
      entityType: 'requisition',
      approvalLevels: [
        { level: 1, role: 'DEPARTMENT_HEAD', minAmount: 0, maxAmount: 100000, required: true },
        { level: 2, role: 'PROCUREMENT_MANAGER', minAmount: 50000, maxAmount: 500000, required: true },
        { level: 3, role: 'FINANCE_MANAGER', minAmount: 200000, maxAmount: 1000000, required: true },
        { level: 4, role: 'CEO', minAmount: 1000000, required: true }
      ],
      autoApprovalRules: {
        maxAmount: 10000
      },
      notifications: {
        onSubmission: true,
        onApproval: true,
        onRejection: true,
        escalation: true,
        escalationHours: 24
      }
    },
    purchase_order: {
      entityType: 'purchase_order',
      approvalLevels: [
        { level: 1, role: 'PROCUREMENT_MANAGER', minAmount: 0, maxAmount: 500000, required: true },
        { level: 2, role: 'FINANCE_MANAGER', minAmount: 200000, maxAmount: 1000000, required: true },
        { level: 3, role: 'CEO', minAmount: 1000000, required: true }
      ],
      notifications: {
        onSubmission: true,
        onApproval: true,
        onRejection: true,
        escalation: true,
        escalationHours: 48
      }
    },
    tender: {
      entityType: 'tender',
      approvalLevels: [
        { level: 1, role: 'PROCUREMENT_MANAGER', minAmount: 0, required: true },
        { level: 2, role: 'FINANCE_MANAGER', minAmount: 500000, required: true },
        { level: 3, role: 'CEO', minAmount: 2000000, required: true }
      ],
      notifications: {
        onSubmission: true,
        onApproval: true,
        onRejection: true,
        escalation: true,
        escalationHours: 72
      }
    },
    contract: {
      entityType: 'contract',
      approvalLevels: [
        { level: 1, role: 'PROCUREMENT_MANAGER', minAmount: 0, maxAmount: 1000000, required: true },
        { level: 2, role: 'LEGAL_COUNSEL', minAmount: 0, required: true },
        { level: 3, role: 'FINANCE_MANAGER', minAmount: 500000, required: true },
        { level: 4, role: 'CEO', minAmount: 2000000, required: true }
      ],
      notifications: {
        onSubmission: true,
        onApproval: true,
        onRejection: true,
        escalation: true,
        escalationHours: 96
      }
    }
  };

  /**
   * Get singleton instance
   */
  static getInstance(): ProcurementWorkflowService {
    if (!this.instance) {
      this.instance = new ProcurementWorkflowService();
    }
    return this.instance;
  }

  /**
   * Initialize approval workflow for an entity
   * @param entityId - Entity ID
   * @param entityType - Entity type
   * @param amount - Amount for approval thresholds
   * @param submittedBy - User who submitted
   * @param categoryId - Category ID (optional)
   * @returns Workflow status
   */
  async initializeWorkflow(
    entityId: string,
    entityType: 'requisition' | 'purchase_order' | 'tender' | 'contract',
    amount: number,
    submittedBy: string,
    categoryId?: string
  ): Promise<WorkflowStatus> {
    try {
      await connectToDatabase();
      logger.info('Initializing workflow', LogCategory.PROCUREMENT, {
        entityId,
        entityType,
        amount,
        submittedBy,
        categoryId
      });

      const config = this.getWorkflowConfig(entityType, categoryId);
      
      // Check for auto-approval
      if (this.shouldAutoApprove(amount, entityType, config)) {
        const workflow: WorkflowStatus = {
          entityId,
          entityType,
          currentLevel: 0,
          status: 'approved',
          steps: [{
            level: 0,
            approverRole: 'SYSTEM',
            status: 'approved',
            approvedAt: new Date(),
            comments: 'Auto-approved based on system rules'
          }],
          submittedAt: new Date(),
          completedAt: new Date(),
          submittedBy
        };

        this.workflows.set(entityId, workflow);
        await this.updateEntityStatus(entityId, entityType, 'approved');
        await this.sendNotification(entityId, entityType, 'approved', submittedBy);
        
        return workflow;
      }

      // Generate approval steps
      const steps = this.generateApprovalSteps(amount, config);
      
      const workflow: WorkflowStatus = {
        entityId,
        entityType,
        currentLevel: 1,
        status: 'pending',
        steps,
        submittedAt: new Date(),
        submittedBy
      };

      this.workflows.set(entityId, workflow);
      await this.updateEntityStatus(entityId, entityType, 'pending_approval');
      await this.sendNotification(entityId, entityType, 'submitted', submittedBy);

      logger.info('Workflow initialized', LogCategory.PROCUREMENT, workflow);
      return workflow;

    } catch (error) {
      logger.error('Error initializing workflow', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Process approval for a workflow step
   * @param entityId - Entity ID
   * @param approverId - Approver user ID
   * @param action - Approval action
   * @param comments - Approval comments
   * @returns Updated workflow status
   */
  async processApproval(
    entityId: string,
    approverId: string,
    action: 'approve' | 'reject',
    comments?: string
  ): Promise<WorkflowStatus> {
    try {
      await connectToDatabase();
      logger.info('Processing approval', LogCategory.PROCUREMENT, {
        entityId,
        approverId,
        action,
        comments
      });

      const workflow = this.workflows.get(entityId);
      if (!workflow) {
        throw new Error(`Workflow not found for entity ${entityId}`);
      }

      if (workflow.status !== 'pending') {
        throw new Error(`Workflow is not in pending status: ${workflow.status}`);
      }

      // Find current step
      const currentStep = workflow.steps.find(step => step.level === workflow.currentLevel);
      if (!currentStep) {
        throw new Error(`Current approval step not found for level ${workflow.currentLevel}`);
      }

      // Verify approver authorization
      await this.verifyApproverAuthorization(approverId, currentStep.approverRole);

      // Update step
      currentStep.approverId = approverId;
      currentStep.status = action === 'approve' ? 'approved' : 'rejected';
      currentStep.approvedAt = new Date();
      currentStep.comments = comments;

      if (action === 'reject') {
        // Rejection - workflow ends
        workflow.status = 'rejected';
        workflow.completedAt = new Date();
        await this.updateEntityStatus(entityId, workflow.entityType, 'rejected');
        await this.sendNotification(entityId, workflow.entityType, 'rejected', approverId);
      } else {
        // Approval - check if more steps needed
        const nextLevel = workflow.currentLevel + 1;
        const nextStep = workflow.steps.find(step => step.level === nextLevel);
        
        if (nextStep) {
          // Move to next level
          workflow.currentLevel = nextLevel;
          await this.sendNotification(entityId, workflow.entityType, 'pending_approval', approverId);
        } else {
          // All approvals complete
          workflow.status = 'approved';
          workflow.completedAt = new Date();
          await this.updateEntityStatus(entityId, workflow.entityType, 'approved');
          await this.sendNotification(entityId, workflow.entityType, 'approved', approverId);
          
          // Trigger post-approval actions
          await this.executePostApprovalActions(entityId, workflow.entityType);
        }
      }

      this.workflows.set(entityId, workflow);
      logger.info('Approval processed', LogCategory.PROCUREMENT, workflow);
      return workflow;

    } catch (error) {
      logger.error('Error processing approval', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get workflow status
   * @param entityId - Entity ID
   * @returns Workflow status
   */
  getWorkflowStatus(entityId: string): WorkflowStatus | null {
    return this.workflows.get(entityId) || null;
  }

  /**
   * Cancel workflow
   * @param entityId - Entity ID
   * @param cancelledBy - User who cancelled
   * @returns Updated workflow status
   */
  async cancelWorkflow(entityId: string, cancelledBy: string): Promise<WorkflowStatus> {
    try {
      const workflow = this.workflows.get(entityId);
      if (!workflow) {
        throw new Error(`Workflow not found for entity ${entityId}`);
      }

      workflow.status = 'cancelled';
      workflow.completedAt = new Date();
      
      this.workflows.set(entityId, workflow);
      await this.updateEntityStatus(entityId, workflow.entityType, 'cancelled');
      await this.sendNotification(entityId, workflow.entityType, 'cancelled', cancelledBy);

      logger.info('Workflow cancelled', LogCategory.PROCUREMENT, { entityId, cancelledBy });
      return workflow;

    } catch (error) {
      logger.error('Error cancelling workflow', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get workflow configuration for entity type
   * @param entityType - Entity type
   * @param categoryId - Category ID (optional)
   * @returns Workflow configuration
   */
  private getWorkflowConfig(entityType: string, categoryId?: string): WorkflowConfig {
    // For now, return default config
    // In the future, this could be customized based on category
    return ProcurementWorkflowService.DEFAULT_CONFIGS[entityType];
  }

  /**
   * Check if entity should be auto-approved
   * @param amount - Amount
   * @param entityType - Entity type
   * @param config - Workflow configuration
   * @returns True if should auto-approve
   */
  private shouldAutoApprove(amount: number, entityType: string, config: WorkflowConfig): boolean {
    if (!config.autoApprovalRules) return false;
    
    if (config.autoApprovalRules.maxAmount && amount <= config.autoApprovalRules.maxAmount) {
      return true;
    }
    
    return false;
  }

  /**
   * Generate approval steps based on amount and configuration
   * @param amount - Amount
   * @param config - Workflow configuration
   * @returns Approval steps
   */
  private generateApprovalSteps(amount: number, config: WorkflowConfig): ApprovalStep[] {
    const steps: ApprovalStep[] = [];
    
    for (const level of config.approvalLevels) {
      if (level.required && 
          (!level.minAmount || amount >= level.minAmount) &&
          (!level.maxAmount || amount <= level.maxAmount)) {
        steps.push({
          level: level.level,
          approverRole: level.role,
          status: 'pending',
          requiredAmount: amount
        });
      }
    }
    
    return steps.sort((a, b) => a.level - b.level);
  }

  /**
   * Verify approver authorization
   * @param approverId - Approver ID
   * @param requiredRole - Required role
   */
  private async verifyApproverAuthorization(approverId: string, requiredRole: string): Promise<void> {
    // Implementation would check user roles/permissions
    // For now, we'll assume authorization is valid
    logger.info('Verifying approver authorization', LogCategory.PROCUREMENT, {
      approverId,
      requiredRole
    });
  }

  /**
   * Update entity status
   * @param entityId - Entity ID
   * @param entityType - Entity type
   * @param status - New status
   */
  private async updateEntityStatus(entityId: string, entityType: string, status: string): Promise<void> {
    const Model = this.getEntityModel(entityType);
    await Model.findByIdAndUpdate(entityId, { status });
  }

  /**
   * Get entity model
   * @param entityType - Entity type
   * @returns Mongoose model
   */
  private getEntityModel(entityType: string) {
    switch (entityType) {
      case 'requisition': return Requisition;
      case 'purchase_order': return PurchaseOrder;
      case 'tender': return Tender;
      case 'contract': return Contract;
      default: throw new Error(`Unknown entity type: ${entityType}`);
    }
  }

  /**
   * Send notification
   * @param entityId - Entity ID
   * @param entityType - Entity type
   * @param event - Event type
   * @param userId - User ID
   */
  private async sendNotification(
    entityId: string, 
    entityType: string, 
    event: string, 
    userId: string
  ): Promise<void> {
    try {
      // Implementation would send actual notifications
      logger.info('Sending notification', LogCategory.PROCUREMENT, {
        entityId,
        entityType,
        event,
        userId
      });
    } catch (error) {
      logger.error('Error sending notification', LogCategory.PROCUREMENT, error);
    }
  }

  /**
   * Execute post-approval actions
   * @param entityId - Entity ID
   * @param entityType - Entity type
   */
  private async executePostApprovalActions(entityId: string, entityType: string): Promise<void> {
    try {
      logger.info('Executing post-approval actions', LogCategory.PROCUREMENT, {
        entityId,
        entityType
      });

      if (entityType === 'requisition') {
        // Auto-create purchase order for approved requisitions
        await this.createPurchaseOrderFromRequisition(entityId);
      }

    } catch (error) {
      logger.error('Error executing post-approval actions', LogCategory.PROCUREMENT, error);
    }
  }

  /**
   * Create purchase order from approved requisition
   * @param requisitionId - Requisition ID
   */
  private async createPurchaseOrderFromRequisition(requisitionId: string): Promise<void> {
    // Implementation would create PO from requisition
    logger.info('Creating purchase order from requisition', LogCategory.PROCUREMENT, {
      requisitionId
    });
  }
}

// Export singleton instance
export const procurementWorkflowService = ProcurementWorkflowService.getInstance();

// lib/services/payroll/optimized-payroll-processor.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollProcessingBatch from '@/models/payroll/PayrollProcessingBatch';
import { unifiedPayrollService } from './unified-payroll-service';

interface EmployeeProcessingResult {
  employeeId: string;
  employeeName: string;
  success: boolean;
  error?: string;
  processingTime: number;
  payrollRecord?: any;
}

interface BatchProcessingOptions {
  batchSize?: number;
  maxConcurrency?: number;
  enableProgressTracking?: boolean;
  skipExistingRecords?: boolean;
}

/**
 * Optimized payroll processor with parallel processing and enhanced progress tracking
 */
export class OptimizedPayrollProcessor {
  private processingStartTime: number = 0;
  private processedEmployees: EmployeeProcessingResult[] = [];

  constructor() {
    // Using unified payroll service singleton
  }

  /**
   * Process payroll run with optimized parallel processing
   */
  async processPayrollRunOptimized(
    payrollRunId: string,
    userId: string,
    options: BatchProcessingOptions = {}
  ): Promise<{
    success: boolean;
    totalEmployees: number;
    processedEmployees: number;
    failedEmployees: number;
    processingTime: number;
    results: EmployeeProcessingResult[];
  }> {
    const {
      batchSize = 10,
      maxConcurrency = 5,
      enableProgressTracking = true,
      skipExistingRecords = true
    } = options;

    this.processingStartTime = Date.now();
    this.processedEmployees = [];

    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Update status to processing
      payrollRun.status = 'processing';
      await payrollRun.save();

      // Get employees to process
      const employees = await this.getEmployeesToProcess(payrollRun);

      // Filter out employees with existing records if skipExistingRecords is true
      const employeesToProcess = skipExistingRecords
        ? await this.filterExistingRecords(employees, payrollRunId)
        : employees;

      logger.info(`Starting optimized payroll processing for ${employeesToProcess.length} employees`, LogCategory.PAYROLL, {
        payrollRunId,
        totalEmployees: employees.length,
        employeesToProcess: employeesToProcess.length,
        batchSize,
        maxConcurrency
      });

      // Create processing batch for tracking
      let batch = null;
      if (enableProgressTracking) {
        batch = await this.createProcessingBatch(payrollRunId, userId, employeesToProcess.length);
      }

      // Process employees in parallel batches
      const results = await this.processEmployeesInParallelBatches(
        employeesToProcess,
        payrollRun,
        userId,
        batchSize,
        maxConcurrency,
        batch
      );

      // Calculate totals
      const totals = this.calculateTotals(results);

      // Update payroll run with results
      await this.updatePayrollRunWithResults(payrollRun, totals, results.length);

      // Complete processing batch
      if (batch) {
        await this.completeProcessingBatch(batch, results);
      }

      const processingTime = Date.now() - this.processingStartTime;

      logger.info(`Payroll processing completed`, LogCategory.PAYROLL, {
        payrollRunId,
        totalEmployees: employees.length,
        processedEmployees: results.filter(r => r.success).length,
        failedEmployees: results.filter(r => !r.success).length,
        processingTime: `${processingTime}ms`
      });

      return {
        success: true,
        totalEmployees: employees.length,
        processedEmployees: results.filter(r => r.success).length,
        failedEmployees: results.filter(r => !r.success).length,
        processingTime,
        results
      };

    } catch (error) {
      logger.error('Error in optimized payroll processing', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get employees to process based on payroll run criteria
   */
  private async getEmployeesToProcess(payrollRun: any): Promise<any[]> {
    const query: any = { employmentStatus: 'active' };

    // Filter by departments if specified
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      query.departmentId = { $in: payrollRun.departments };
    }

    return await Employee.find(query)
      .populate('departmentId')
      .lean(); // Use lean() for better performance
  }

  /**
   * Filter out employees with existing payroll records
   */
  private async filterExistingRecords(employees: any[], payrollRunId: string): Promise<any[]> {
    const employeeIds = employees.map(emp => emp._id);

    const existingRecords = await PayrollRecord.find({
      payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
      employeeId: { $in: employeeIds }
    }).select('employeeId').lean();

    const existingEmployeeIds = new Set(existingRecords.map(record => record.employeeId.toString()));

    return employees.filter(emp => !existingEmployeeIds.has(emp._id.toString()));
  }

  /**
   * Process employees in parallel batches
   */
  private async processEmployeesInParallelBatches(
    employees: any[],
    payrollRun: any,
    userId: string,
    batchSize: number,
    maxConcurrency: number,
    batch: any
  ): Promise<EmployeeProcessingResult[]> {
    const allResults: EmployeeProcessingResult[] = [];

    // Split employees into batches
    for (let i = 0; i < employees.length; i += batchSize) {
      const batchEmployees = employees.slice(i, i + batchSize);

      // Process batch with limited concurrency
      const batchResults = await this.processBatchWithConcurrency(
        batchEmployees,
        payrollRun,
        userId,
        maxConcurrency,
        batch,
        allResults.length
      );

      allResults.push(...batchResults);

      // Update batch progress
      if (batch) {
        await this.updateBatchProgress(batch, allResults);
      }

      // Small delay between batches to prevent overwhelming the database
      if (i + batchSize < employees.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return allResults;
  }

  /**
   * Process a batch of employees with controlled concurrency
   */
  private async processBatchWithConcurrency(
    employees: any[],
    payrollRun: any,
    userId: string,
    maxConcurrency: number,
    batch: any,
    processedSoFar: number
  ): Promise<EmployeeProcessingResult[]> {
    const semaphore = new Array(maxConcurrency).fill(null);
    let currentIndex = 0;
    const results: EmployeeProcessingResult[] = [];

    const processNext = async (): Promise<void> => {
      while (currentIndex < employees.length) {
        const employeeIndex = currentIndex++;
        const employee = employees[employeeIndex];

        try {
          // Update current employee in batch
          if (batch) {
            batch.currentEmployee = `${employee.firstName} ${employee.lastName}`;
            await batch.save();
          }

          const result = await this.processIndividualEmployee(employee, payrollRun, userId);
          results[employeeIndex] = result;

        } catch (error) {
          results[employeeIndex] = {
            employeeId: employee._id.toString(),
            employeeName: `${employee.firstName} ${employee.lastName}`,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            processingTime: 0
          };
        }
      }
    };

    // Start concurrent processing
    await Promise.all(semaphore.map(() => processNext()));

    return results.filter(Boolean); // Remove any undefined entries
  }

  /**
   * Process individual employee
   */
  private async processIndividualEmployee(
    employee: any,
    payrollRun: any,
    userId: string
  ): Promise<EmployeeProcessingResult> {
    const startTime = Date.now();
    const employeeName = `${employee.firstName} ${employee.lastName}`;

    try {
      // Check if employee has an active salary
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId: employee._id,
        isActive: true
      }).lean();

      if (!employeeSalary) {
        return {
          employeeId: employee._id.toString(),
          employeeName,
          success: false,
          error: 'No active salary found',
          processingTime: Date.now() - startTime
        };
      }

      // Calculate salary using unified service
      const salaryResult = await unifiedPayrollService.calculateEmployeeSalary(
        employee._id.toString(),
        {
          month: payrollRun.payPeriod.month,
          year: payrollRun.payPeriod.year
        }
      );

      // Create payroll record
      const payrollRecord = new PayrollRecord({
        employeeId: employee._id,
        payrollRunId: payrollRun._id,
        payPeriod: payrollRun.payPeriod,
        salaryStructureId: employeeSalary.salaryStructureId,
        currency: salaryResult.currency,
        components: salaryResult.components,
        grossSalary: salaryResult.grossSalary,
        totalDeductions: salaryResult.totalDeductions,
        totalTax: salaryResult.totalTax,
        netSalary: salaryResult.netSalary,
        status: 'draft', // Use 'draft' instead of 'processed' as per PayrollRecord schema
        bankAccount: employeeSalary.bankAccountNumber,
        paymentMethod: employeeSalary.paymentMethod,
        createdBy: userId,
      });

      await payrollRecord.save();

      return {
        employeeId: employee._id.toString(),
        employeeName,
        success: true,
        processingTime: Date.now() - startTime,
        payrollRecord: {
          grossSalary: salaryResult.grossSalary,
          totalDeductions: salaryResult.totalDeductions,
          totalTax: salaryResult.totalTax,
          netSalary: salaryResult.netSalary
        }
      };

    } catch (error) {
      return {
        employeeId: employee._id.toString(),
        employeeName,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Create processing batch for tracking
   */
  private async createProcessingBatch(payrollRunId: string, userId: string, totalEmployees: number) {
    const batch = new PayrollProcessingBatch({
      payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
      status: 'processing',
      totalEmployees,
      processedEmployees: 0,
      currentEmployee: '',
      createdBy: new mongoose.Types.ObjectId(userId),
      updatedBy: new mongoose.Types.ObjectId(userId)
    });

    await batch.save();
    return batch;
  }

  /**
   * Update batch progress
   */
  private async updateBatchProgress(batch: any, results: EmployeeProcessingResult[]) {
    batch.processedEmployees = results.length;
    await batch.save();
  }

  /**
   * Complete processing batch
   */
  private async completeProcessingBatch(batch: any, results: EmployeeProcessingResult[]) {
    batch.status = 'completed';
    batch.processedEmployees = results.filter(r => r.success).length;
    batch.completedAt = new Date();
    await batch.save();
  }

  /**
   * Calculate totals from processing results
   */
  private calculateTotals(results: EmployeeProcessingResult[]) {
    const successfulResults = results.filter(r => r.success && r.payrollRecord);

    logger.info(`Calculating totals from ${successfulResults.length} successful results`, LogCategory.PAYROLL, {
      totalResults: results.length,
      successfulResults: successfulResults.length,
      failedResults: results.filter(r => !r.success).length
    });

    const totals = successfulResults.reduce((totals, result) => {
      const record = result.payrollRecord!;

      logger.debug(`Adding employee totals`, LogCategory.PAYROLL, {
        employeeName: result.employeeName,
        grossSalary: record.grossSalary,
        totalDeductions: record.totalDeductions,
        totalTax: record.totalTax,
        netSalary: record.netSalary
      });

      return {
        totalGrossSalary: totals.totalGrossSalary + (record.grossSalary || 0),
        totalDeductions: totals.totalDeductions + (record.totalDeductions || 0),
        totalTax: totals.totalTax + (record.totalTax || 0),
        totalNetSalary: totals.totalNetSalary + (record.netSalary || 0)
      };
    }, {
      totalGrossSalary: 0,
      totalDeductions: 0,
      totalTax: 0,
      totalNetSalary: 0
    });

    logger.info(`Calculated payroll run totals`, LogCategory.PAYROLL, {
      totalGrossSalary: totals.totalGrossSalary,
      totalDeductions: totals.totalDeductions,
      totalTax: totals.totalTax,
      totalNetSalary: totals.totalNetSalary,
      processedEmployees: successfulResults.length
    });

    return totals;
  }

  /**
   * Update payroll run with processing results
   */
  private async updatePayrollRunWithResults(payrollRun: any, totals: any, processedCount: number) {
    // If totals are all zero, recalculate from database
    if (totals.totalGrossSalary === 0 && totals.totalDeductions === 0 &&
        totals.totalTax === 0 && totals.totalNetSalary === 0) {

      logger.warn(`Calculated totals are all zero, recalculating from database`, LogCategory.PAYROLL, {
        payrollRunId: payrollRun._id,
        processedCount
      });

      const databaseTotals = await this.recalculateTotalsFromDatabase(payrollRun._id);
      if (databaseTotals) {
        totals = databaseTotals;
        logger.info(`Recalculated totals from database`, LogCategory.PAYROLL, {
          payrollRunId: payrollRun._id,
          databaseTotals
        });
      }
    }

    payrollRun.status = 'completed';
    payrollRun.processedEmployees = processedCount;
    payrollRun.totalGrossSalary = totals.totalGrossSalary;
    payrollRun.totalDeductions = totals.totalDeductions;
    payrollRun.totalTax = totals.totalTax;
    payrollRun.totalNetSalary = totals.totalNetSalary;
    payrollRun.processedAt = new Date();

    logger.info(`Updating payroll run with final totals`, LogCategory.PAYROLL, {
      payrollRunId: payrollRun._id,
      totalGrossSalary: totals.totalGrossSalary,
      totalDeductions: totals.totalDeductions,
      totalTax: totals.totalTax,
      totalNetSalary: totals.totalNetSalary,
      processedEmployees: processedCount
    });

    await payrollRun.save();
  }

  /**
   * Recalculate totals from database records
   */
  private async recalculateTotalsFromDatabase(payrollRunId: string) {
    try {
      const payrollRecords = await PayrollRecord.find({
        payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
        status: { $ne: 'cancelled' }
      }).lean();

      if (payrollRecords.length === 0) {
        logger.warn(`No payroll records found for payroll run ${payrollRunId}`, LogCategory.PAYROLL);
        return null;
      }

      const totals = payrollRecords.reduce((acc, record) => ({
        totalGrossSalary: acc.totalGrossSalary + (record.grossSalary || 0),
        totalDeductions: acc.totalDeductions + (record.totalDeductions || 0),
        totalTax: acc.totalTax + (record.totalTax || 0),
        totalNetSalary: acc.totalNetSalary + (record.netSalary || 0)
      }), {
        totalGrossSalary: 0,
        totalDeductions: 0,
        totalTax: 0,
        totalNetSalary: 0
      });

      logger.info(`Recalculated totals from ${payrollRecords.length} database records`, LogCategory.PAYROLL, {
        payrollRunId,
        totals
      });

      return totals;
    } catch (error) {
      logger.error(`Error recalculating totals from database`, LogCategory.PAYROLL, error);
      return null;
    }
  }
}

export const optimizedPayrollProcessor = new OptimizedPayrollProcessor();

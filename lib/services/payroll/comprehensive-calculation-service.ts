// lib/services/payroll/comprehensive-calculation-service.ts
import PayrollRecord from '@/models/payroll/PayrollRecord';
import Employee from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import TaxBracket from '@/models/payroll/TaxBracket';
import Allowance from '@/models/payroll/Allowance';
import Deduction from '@/models/payroll/Deduction';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export interface PayrollCalculationResult {
  employeeId: string;
  employeeName: string;
  basicSalary: number;
  allowances: Array<{
    type: string;
    amount: number;
    description?: string;
  }>;
  grossSalary: number;
  deductions: Array<{
    type: string;
    amount: number;
    description?: string;
  }>;
  totalDeductions: number;
  taxableIncome: number;
  incomeTax: number;
  netSalary: number;
  calculationDetails: {
    taxBrackets: Array<{
      min: number;
      max: number;
      rate: number;
      taxAmount: number;
    }>;
    pensionContribution: number;
    medicalAidContribution: number;
    otherDeductions: number;
  };
}

export interface PayrollRunTotals {
  totalEmployees: number;
  totalBasicSalary: number;
  totalAllowances: number;
  totalGrossSalary: number;
  totalDeductions: number;
  totalTax: number;
  totalNetSalary: number;
  breakdown: {
    allowancesByType: Record<string, number>;
    deductionsByType: Record<string, number>;
    taxByBracket: Array<{
      min: number;
      max: number;
      rate: number;
      totalTax: number;
      employeeCount: number;
    }>;
  };
}

class ComprehensiveCalculationService {
  /**
   * Calculate comprehensive payroll for a single employee
   */
  async calculateEmployeePayroll(
    employeeId: string,
    payrollRunId: string,
    payPeriod: { startDate: Date; endDate: Date }
  ): Promise<PayrollCalculationResult> {
    try {
      // Get employee details
      const employee = await Employee.findById(employeeId)
        .populate('departmentId')
        .lean();

      if (!employee) {
        throw new Error(`Employee not found: ${employeeId}`);
      }

      // Get employee salary
      const employeeSalary = await EmployeeSalary.findOne({ 
        employeeId,
        isActive: true 
      }).lean();

      if (!employeeSalary) {
        throw new Error(`No active salary found for employee: ${employee.firstName} ${employee.lastName}`);
      }

      const basicSalary = employeeSalary.salary;

      // Get applicable allowances
      const allowances = await this.calculateAllowances(employee, basicSalary, payPeriod);
      const totalAllowances = allowances.reduce((sum, allowance) => sum + allowance.amount, 0);
      const grossSalary = basicSalary + totalAllowances;

      // Get applicable deductions (excluding tax)
      const deductions = await this.calculateDeductions(employee, basicSalary, grossSalary, payPeriod);
      const totalDeductions = deductions.reduce((sum, deduction) => sum + deduction.amount, 0);

      // Calculate taxable income (gross salary minus pension and other pre-tax deductions)
      const preTaxDeductions = deductions
        .filter(d => ['PENSION', 'MEDICAL_AID'].includes(d.type))
        .reduce((sum, d) => sum + d.amount, 0);
      
      const taxableIncome = Math.max(0, grossSalary - preTaxDeductions);

      // Calculate income tax
      const taxCalculation = await this.calculateIncomeTax(taxableIncome);
      const incomeTax = taxCalculation.totalTax;

      // Calculate net salary
      const netSalary = grossSalary - totalDeductions - incomeTax;

      // Prepare calculation details
      const pensionContribution = deductions.find(d => d.type === 'PENSION')?.amount || 0;
      const medicalAidContribution = deductions.find(d => d.type === 'MEDICAL_AID')?.amount || 0;
      const otherDeductions = totalDeductions - pensionContribution - medicalAidContribution;

      return {
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        basicSalary,
        allowances,
        grossSalary,
        deductions,
        totalDeductions,
        taxableIncome,
        incomeTax,
        netSalary,
        calculationDetails: {
          taxBrackets: taxCalculation.brackets,
          pensionContribution,
          medicalAidContribution,
          otherDeductions
        }
      };

    } catch (error) {
      logger.error(`Error calculating payroll for employee ${employeeId}`, LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Calculate allowances for an employee
   */
  private async calculateAllowances(
    employee: any,
    basicSalary: number,
    payPeriod: { startDate: Date; endDate: Date }
  ): Promise<Array<{ type: string; amount: number; description?: string }>> {
    const allowances = [];

    try {
      // Get active allowances
      const activeAllowances = await Allowance.find({
        isActive: true,
        $or: [
          { departments: { $in: [employee.departmentId._id] } },
          { departments: { $size: 0 } }, // Global allowances
          { employees: { $in: [employee._id] } } // Employee-specific allowances
        ]
      }).lean();

      for (const allowance of activeAllowances) {
        let amount = 0;

        switch (allowance.calculationType) {
          case 'FIXED':
            amount = allowance.amount;
            break;
          case 'PERCENTAGE':
            amount = (basicSalary * allowance.percentage) / 100;
            break;
          case 'FORMULA':
            // Implement formula-based calculation if needed
            amount = allowance.amount;
            break;
        }

        if (amount > 0) {
          allowances.push({
            type: allowance.type,
            amount,
            description: allowance.description
          });
        }
      }

    } catch (error) {
      logger.error('Error calculating allowances', LogCategory.PAYROLL, error);
    }

    return allowances;
  }

  /**
   * Calculate deductions for an employee
   */
  private async calculateDeductions(
    employee: any,
    basicSalary: number,
    grossSalary: number,
    payPeriod: { startDate: Date; endDate: Date }
  ): Promise<Array<{ type: string; amount: number; description?: string }>> {
    const deductions = [];

    try {
      // Standard pension contribution (5% of basic salary)
      const pensionAmount = basicSalary * 0.05;
      deductions.push({
        type: 'PENSION',
        amount: pensionAmount,
        description: 'Pension contribution (5% of basic salary)'
      });

      // Get active deductions
      const activeDeductions = await Deduction.find({
        isActive: true,
        $or: [
          { departments: { $in: [employee.departmentId._id] } },
          { departments: { $size: 0 } }, // Global deductions
          { employees: { $in: [employee._id] } } // Employee-specific deductions
        ]
      }).lean();

      for (const deduction of activeDeductions) {
        let amount = 0;

        switch (deduction.calculationType) {
          case 'FIXED':
            amount = deduction.amount;
            break;
          case 'PERCENTAGE':
            const baseAmount = deduction.baseOn === 'GROSS' ? grossSalary : basicSalary;
            amount = (baseAmount * deduction.percentage) / 100;
            break;
          case 'FORMULA':
            // Implement formula-based calculation if needed
            amount = deduction.amount;
            break;
        }

        if (amount > 0) {
          deductions.push({
            type: deduction.type,
            amount,
            description: deduction.description
          });
        }
      }

    } catch (error) {
      logger.error('Error calculating deductions', LogCategory.PAYROLL, error);
    }

    return deductions;
  }

  /**
   * Calculate income tax using progressive tax brackets
   */
  private async calculateIncomeTax(taxableIncome: number): Promise<{
    totalTax: number;
    brackets: Array<{ min: number; max: number; rate: number; taxAmount: number }>;
  }> {
    try {
      // Get active tax brackets
      const taxBrackets = await TaxBracket.find({ isActive: true })
        .sort({ minIncome: 1 })
        .lean();

      if (taxBrackets.length === 0) {
        return { totalTax: 0, brackets: [] };
      }

      let totalTax = 0;
      const brackets = [];

      for (const bracket of taxBrackets) {
        const min = bracket.minIncome;
        const max = bracket.maxIncome || Infinity;
        const rate = bracket.taxRate;

        if (taxableIncome > min) {
          const taxableAtThisBracket = Math.min(taxableIncome, max) - min;
          const taxAmount = (taxableAtThisBracket * rate) / 100;
          
          totalTax += taxAmount;
          
          brackets.push({
            min,
            max: max === Infinity ? max : max,
            rate,
            taxAmount
          });

          if (taxableIncome <= max) {
            break;
          }
        }
      }

      return { totalTax, brackets };

    } catch (error) {
      logger.error('Error calculating income tax', LogCategory.PAYROLL, error);
      return { totalTax: 0, brackets: [] };
    }
  }

  /**
   * Calculate totals for entire payroll run
   */
  async calculatePayrollRunTotals(payrollRunId: string): Promise<PayrollRunTotals> {
    try {
      // Get all payroll records for this run
      const payrollRecords = await PayrollRecord.find({ 
        payrollRunId,
        status: { $ne: 'cancelled' }
      }).lean();

      const totals: PayrollRunTotals = {
        totalEmployees: payrollRecords.length,
        totalBasicSalary: 0,
        totalAllowances: 0,
        totalGrossSalary: 0,
        totalDeductions: 0,
        totalTax: 0,
        totalNetSalary: 0,
        breakdown: {
          allowancesByType: {},
          deductionsByType: {},
          taxByBracket: []
        }
      };

      // Aggregate totals
      for (const record of payrollRecords) {
        totals.totalBasicSalary += record.basicSalary || 0;
        totals.totalAllowances += record.totalAllowances || 0;
        totals.totalGrossSalary += record.grossSalary || 0;
        totals.totalDeductions += record.totalDeductions || 0;
        totals.totalTax += record.incomeTax || 0;
        totals.totalNetSalary += record.netSalary || 0;

        // Aggregate allowances by type
        if (record.allowances) {
          for (const allowance of record.allowances) {
            const type = allowance.type || 'OTHER';
            totals.breakdown.allowancesByType[type] = 
              (totals.breakdown.allowancesByType[type] || 0) + (allowance.amount || 0);
          }
        }

        // Aggregate deductions by type
        if (record.deductions) {
          for (const deduction of record.deductions) {
            const type = deduction.type || 'OTHER';
            totals.breakdown.deductionsByType[type] = 
              (totals.breakdown.deductionsByType[type] || 0) + (deduction.amount || 0);
          }
        }
      }

      return totals;

    } catch (error) {
      logger.error(`Error calculating payroll run totals for ${payrollRunId}`, LogCategory.PAYROLL, error);
      throw error;
    }
  }
}

export const comprehensiveCalculationService = new ComprehensiveCalculationService();

import { connectToDatabase } from '@/lib/backend/database';
import AuditLog, { IAuditLog } from '@/models/payroll/AuditLog';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';
import { NextRequest } from 'next/server';

/**
 * Interface for audit log creation
 */
export interface CreateAuditLogParams {
  userId: string;
  action: string;
  module: string;
  entityType: string;
  entityId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  metadata?: Record<string, any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  batchId?: string;
  batchSize?: number;
  batchIndex?: number;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  isError?: boolean;
  errorMessage?: string;
  errorStack?: string;
}

/**
 * Interface for bulk audit log creation
 */
export interface BulkAuditLogParams {
  userId: string;
  action: string;
  module: string;
  entityType: string;
  batchId: string;
  batchSize: number;
  operations: Array<{
    entityId: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    description?: string;
    isError?: boolean;
    errorMessage?: string;
  }>;
  metadata?: Record<string, any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

/**
 * Service for managing audit logs
 */
export class AuditService {
  /**
   * Create a single audit log entry
   */
  async createAuditLog(params: CreateAuditLogParams): Promise<IAuditLog> {
    try {
      await connectToDatabase();

      // Calculate changes if both old and new values are provided
      const changes = this.calculateChanges(params.oldValues, params.newValues);

      const auditLogData = {
        timestamp: new Date(),
        userId: new mongoose.Types.ObjectId(params.userId),
        action: params.action,
        module: params.module,
        entityType: params.entityType,
        entityId: params.entityId,
        oldValues: params.oldValues,
        newValues: params.newValues,
        changes,
        description: params.description,
        metadata: params.metadata,
        severity: params.severity || 'medium',
        batchId: params.batchId,
        batchSize: params.batchSize,
        batchIndex: params.batchIndex,
        ipAddress: params.ipAddress,
        userAgent: params.userAgent,
        sessionId: params.sessionId,
        isError: params.isError || false,
        errorMessage: params.errorMessage,
        errorStack: params.errorStack,
      };

      const auditLog = await AuditLog.create(auditLogData);

      logger.info('Audit log created', LogCategory.AUDIT, {
        auditLogId: auditLog._id,
        action: params.action,
        module: params.module,
        entityType: params.entityType,
        entityId: params.entityId,
        userId: params.userId,
      });

      return auditLog;
    } catch (error) {
      logger.error('Error creating audit log', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Create multiple audit log entries for bulk operations
   */
  async createBulkAuditLogs(params: BulkAuditLogParams): Promise<IAuditLog[]> {
    try {
      await connectToDatabase();

      const auditLogs = params.operations.map((operation, index) => {
        const changes = this.calculateChanges(operation.oldValues, operation.newValues);

        return {
          timestamp: new Date(),
          userId: new mongoose.Types.ObjectId(params.userId),
          action: params.action,
          module: params.module,
          entityType: params.entityType,
          entityId: operation.entityId,
          oldValues: operation.oldValues,
          newValues: operation.newValues,
          changes,
          description: operation.description,
          metadata: params.metadata,
          severity: params.severity || 'medium',
          batchId: params.batchId,
          batchSize: params.batchSize,
          batchIndex: index + 1,
          ipAddress: params.ipAddress,
          userAgent: params.userAgent,
          sessionId: params.sessionId,
          isError: operation.isError || false,
          errorMessage: operation.errorMessage,
        };
      });

      const createdLogs = await AuditLog.insertMany(auditLogs);

      logger.info('Bulk audit logs created', LogCategory.AUDIT, {
        batchId: params.batchId,
        count: createdLogs.length,
        action: params.action,
        module: params.module,
        entityType: params.entityType,
        userId: params.userId,
      });

      return createdLogs;
    } catch (error) {
      logger.error('Error creating bulk audit logs', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(filters: {
    userId?: string;
    action?: string;
    module?: string;
    entityType?: string;
    entityId?: string;
    batchId?: string;
    startDate?: Date;
    endDate?: Date;
    severity?: string;
    isError?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    logs: IAuditLog[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    try {
      await connectToDatabase();

      const query: any = { isArchived: false };

      // Apply filters
      if (filters.userId) query.userId = new mongoose.Types.ObjectId(filters.userId);
      if (filters.action) query.action = filters.action;
      if (filters.module) query.module = filters.module;
      if (filters.entityType) query.entityType = filters.entityType;
      if (filters.entityId) query.entityId = filters.entityId;
      if (filters.batchId) query.batchId = filters.batchId;
      if (filters.severity) query.severity = filters.severity;
      if (filters.isError !== undefined) query.isError = filters.isError;

      // Date range filter
      if (filters.startDate || filters.endDate) {
        query.timestamp = {};
        if (filters.startDate) query.timestamp.$gte = filters.startDate;
        if (filters.endDate) query.timestamp.$lte = filters.endDate;
      }

      // Search filter
      if (filters.search) {
        query.$or = [
          { description: { $regex: filters.search, $options: 'i' } },
          { entityId: { $regex: filters.search, $options: 'i' } },
          { errorMessage: { $regex: filters.search, $options: 'i' } },
        ];
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count
      const totalCount = await AuditLog.countDocuments(query);

      // Get logs
      const logs = await AuditLog.find(query)
        .populate('userId', 'firstName lastName email')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        logs: logs as IAuditLog[],
        totalCount,
        totalPages,
        currentPage: page,
      };
    } catch (error) {
      logger.error('Error getting audit logs', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id: string): Promise<IAuditLog | null> {
    try {
      await connectToDatabase();

      const auditLog = await AuditLog.findById(id)
        .populate('userId', 'firstName lastName email')
        .lean();

      return auditLog as IAuditLog | null;
    } catch (error) {
      logger.error('Error getting audit log by ID', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Extract request context from NextRequest
   */
  extractRequestContext(request: NextRequest): {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  } {
    return {
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined,
      sessionId: request.headers.get('x-session-id') || undefined,
    };
  }

  /**
   * Get client IP address from request
   */
  private getClientIP(request: NextRequest): string | undefined {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const remoteAddr = request.headers.get('x-remote-addr');

    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }

    return realIP || remoteAddr || undefined;
  }

  /**
   * Calculate changes between old and new values
   */
  private calculateChanges(
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Array<{ field: string; oldValue: any; newValue: any }> | undefined {
    if (!oldValues || !newValues) return undefined;

    const changes: Array<{ field: string; oldValue: any; newValue: any }> = [];

    // Get all unique keys from both objects
    const allKeys = new Set([...Object.keys(oldValues), ...Object.keys(newValues)]);

    for (const key of allKeys) {
      const oldValue = oldValues[key];
      const newValue = newValues[key];

      // Skip if values are the same
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes.push({
          field: key,
          oldValue,
          newValue,
        });
      }
    }

    return changes.length > 0 ? changes : undefined;
  }

  /**
   * Archive old audit logs
   */
  async archiveOldLogs(olderThanDays: number = 365): Promise<number> {
    try {
      await connectToDatabase();

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await AuditLog.updateMany(
        {
          timestamp: { $lt: cutoffDate },
          isArchived: false,
        },
        {
          $set: { isArchived: true },
        }
      );

      logger.info('Audit logs archived', LogCategory.AUDIT, {
        archivedCount: result.modifiedCount,
        cutoffDate,
      });

      return result.modifiedCount;
    } catch (error) {
      logger.error('Error archiving audit logs', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Generate audit report
   */
  async generateAuditReport(filters: {
    startDate: Date;
    endDate: Date;
    module?: string;
    userId?: string;
    action?: string;
    entityType?: string;
  }): Promise<{
    summary: {
      totalLogs: number;
      errorCount: number;
      successCount: number;
      userCount: number;
      moduleBreakdown: Record<string, number>;
      actionBreakdown: Record<string, number>;
      severityBreakdown: Record<string, number>;
    };
    topUsers: Array<{ userId: string; userName: string; count: number }>;
    topActions: Array<{ action: string; count: number }>;
    errorSummary: Array<{ errorMessage: string; count: number }>;
  }> {
    try {
      await connectToDatabase();

      const matchQuery: any = {
        timestamp: { $gte: filters.startDate, $lte: filters.endDate },
        isArchived: false,
      };

      if (filters.module) matchQuery.module = filters.module;
      if (filters.userId) matchQuery.userId = new mongoose.Types.ObjectId(filters.userId);
      if (filters.action) matchQuery.action = filters.action;
      if (filters.entityType) matchQuery.entityType = filters.entityType;

      // Aggregate data for report
      const [
        totalStats,
        moduleBreakdown,
        actionBreakdown,
        severityBreakdown,
        topUsers,
        topActions,
        errorSummary,
      ] = await Promise.all([
        // Total statistics
        AuditLog.aggregate([
          { $match: matchQuery },
          {
            $group: {
              _id: null,
              totalLogs: { $sum: 1 },
              errorCount: { $sum: { $cond: ['$isError', 1, 0] } },
              successCount: { $sum: { $cond: ['$isError', 0, 1] } },
              uniqueUsers: { $addToSet: '$userId' },
            },
          },
        ]),

        // Module breakdown
        AuditLog.aggregate([
          { $match: matchQuery },
          { $group: { _id: '$module', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
        ]),

        // Action breakdown
        AuditLog.aggregate([
          { $match: matchQuery },
          { $group: { _id: '$action', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
        ]),

        // Severity breakdown
        AuditLog.aggregate([
          { $match: matchQuery },
          { $group: { _id: '$severity', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
        ]),

        // Top users
        AuditLog.aggregate([
          { $match: matchQuery },
          { $group: { _id: '$userId', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          {
            $lookup: {
              from: 'users',
              localField: '_id',
              foreignField: '_id',
              as: 'user',
            },
          },
          { $unwind: '$user' },
          {
            $project: {
              userId: '$_id',
              userName: { $concat: ['$user.firstName', ' ', '$user.lastName'] },
              count: 1,
            },
          },
        ]),

        // Top actions
        AuditLog.aggregate([
          { $match: matchQuery },
          { $group: { _id: '$action', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          { $project: { action: '$_id', count: 1, _id: 0 } },
        ]),

        // Error summary
        AuditLog.aggregate([
          { $match: { ...matchQuery, isError: true } },
          { $group: { _id: '$errorMessage', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          { $project: { errorMessage: '$_id', count: 1, _id: 0 } },
        ]),
      ]);

      const stats = totalStats[0] || {
        totalLogs: 0,
        errorCount: 0,
        successCount: 0,
        uniqueUsers: [],
      };

      return {
        summary: {
          totalLogs: stats.totalLogs,
          errorCount: stats.errorCount,
          successCount: stats.successCount,
          userCount: stats.uniqueUsers.length,
          moduleBreakdown: moduleBreakdown.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {} as Record<string, number>),
          actionBreakdown: actionBreakdown.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {} as Record<string, number>),
          severityBreakdown: severityBreakdown.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {} as Record<string, number>),
        },
        topUsers,
        topActions,
        errorSummary,
      };
    } catch (error) {
      logger.error('Error generating audit report', LogCategory.AUDIT, error);
      throw error;
    }
  }

  /**
   * Delete old audit logs (for compliance with data retention policies)
   */
  async deleteOldLogs(olderThanDays: number = 2555): Promise<number> { // 7 years default
    try {
      await connectToDatabase();

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await AuditLog.deleteMany({
        timestamp: { $lt: cutoffDate },
        isArchived: true,
      });

      logger.info('Old audit logs deleted', LogCategory.AUDIT, {
        deletedCount: result.deletedCount,
        cutoffDate,
      });

      return result.deletedCount;
    } catch (error) {
      logger.error('Error deleting old audit logs', LogCategory.AUDIT, error);
      throw error;
    }
  }
}

// Create and export service instance
export const auditService = new AuditService();

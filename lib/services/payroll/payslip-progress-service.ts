import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for payslip operation progress
 */
export interface PayslipProgress {
  operationId: string;
  type: 'generation' | 'email' | 'download';
  payrollRunId: string;
  total: number;
  processed: number;
  failed: number;
  skipped: number;
  currentEmployee: string;
  currentEmployeeId: string;
  status: 'processing' | 'completed' | 'failed' | 'cancelled';
  errors: Array<{
    employeeId: string;
    employeeName: string;
    error: string;
    timestamp: Date;
  }>;
  startedAt: Date;
  completedAt?: Date;
  estimatedTimeRemaining?: number;
  processingRate?: number; // employees per second
}

/**
 * Service for tracking payslip operation progress
 */
export class PayslipProgressService {
  private progressMap = new Map<string, PayslipProgress>();
  private readonly MAX_OPERATIONS = 100; // Limit stored operations
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    // Start cleanup timer
    this.startCleanupTimer();
  }

  /**
   * Start a new payslip operation
   * @param type - Type of operation
   * @param payrollRunId - Payroll run ID
   * @param totalEmployees - Total number of employees to process
   * @returns Operation ID
   */
  startOperation(
    type: PayslipProgress['type'],
    payrollRunId: string,
    totalEmployees: number
  ): string {
    const operationId = uuidv4();
    
    const progress: PayslipProgress = {
      operationId,
      type,
      payrollRunId,
      total: totalEmployees,
      processed: 0,
      failed: 0,
      skipped: 0,
      currentEmployee: '',
      currentEmployeeId: '',
      status: 'processing',
      errors: [],
      startedAt: new Date(),
      estimatedTimeRemaining: undefined,
      processingRate: undefined
    };

    this.progressMap.set(operationId, progress);
    
    // Clean up old operations if we have too many
    this.cleanupOldOperations();

    logger.info(`Started ${type} operation`, LogCategory.PAYROLL, {
      operationId,
      payrollRunId,
      totalEmployees
    });

    return operationId;
  }

  /**
   * Update progress for an operation
   * @param operationId - Operation ID
   * @param updates - Progress updates
   */
  updateProgress(operationId: string, updates: Partial<PayslipProgress>): void {
    const progress = this.progressMap.get(operationId);
    if (!progress) {
      logger.warn(`Progress update for unknown operation: ${operationId}`, LogCategory.PAYROLL);
      return;
    }

    // Update progress
    Object.assign(progress, updates);

    // Calculate processing rate and estimated time remaining
    if (progress.processed > 0) {
      const elapsedMs = Date.now() - progress.startedAt.getTime();
      const elapsedSeconds = elapsedMs / 1000;
      progress.processingRate = progress.processed / elapsedSeconds;

      if (progress.processingRate > 0) {
        const remaining = progress.total - progress.processed;
        progress.estimatedTimeRemaining = remaining / progress.processingRate;
      }
    }

    this.progressMap.set(operationId, progress);

    logger.debug(`Updated progress for operation ${operationId}`, LogCategory.PAYROLL, {
      processed: progress.processed,
      total: progress.total,
      status: progress.status,
      currentEmployee: progress.currentEmployee
    });
  }

  /**
   * Mark an employee as processed successfully
   * @param operationId - Operation ID
   * @param employeeId - Employee ID
   * @param employeeName - Employee name
   */
  markEmployeeProcessed(operationId: string, employeeId: string, employeeName: string): void {
    const progress = this.progressMap.get(operationId);
    if (!progress) return;

    this.updateProgress(operationId, {
      processed: progress.processed + 1,
      currentEmployee: employeeName,
      currentEmployeeId: employeeId
    });
  }

  /**
   * Mark an employee as failed
   * @param operationId - Operation ID
   * @param employeeId - Employee ID
   * @param employeeName - Employee name
   * @param error - Error message
   */
  markEmployeeFailed(operationId: string, employeeId: string, employeeName: string, error: string): void {
    const progress = this.progressMap.get(operationId);
    if (!progress) return;

    progress.errors.push({
      employeeId,
      employeeName,
      error,
      timestamp: new Date()
    });

    this.updateProgress(operationId, {
      processed: progress.processed + 1,
      failed: progress.failed + 1,
      currentEmployee: employeeName,
      currentEmployeeId: employeeId
    });
  }

  /**
   * Mark an employee as skipped
   * @param operationId - Operation ID
   * @param employeeId - Employee ID
   * @param employeeName - Employee name
   * @param reason - Skip reason
   */
  markEmployeeSkipped(operationId: string, employeeId: string, employeeName: string, reason: string): void {
    const progress = this.progressMap.get(operationId);
    if (!progress) return;

    this.updateProgress(operationId, {
      processed: progress.processed + 1,
      skipped: progress.skipped + 1,
      currentEmployee: `Skipped: ${employeeName} (${reason})`,
      currentEmployeeId: employeeId
    });
  }

  /**
   * Complete an operation
   * @param operationId - Operation ID
   * @param status - Final status
   */
  completeOperation(operationId: string, status: 'completed' | 'failed' | 'cancelled'): void {
    const progress = this.progressMap.get(operationId);
    if (!progress) return;

    this.updateProgress(operationId, {
      status,
      completedAt: new Date(),
      currentEmployee: status === 'completed' ? 'Operation completed' : `Operation ${status}`
    });

    logger.info(`Completed ${progress.type} operation`, LogCategory.PAYROLL, {
      operationId,
      status,
      processed: progress.processed,
      failed: progress.failed,
      skipped: progress.skipped,
      duration: progress.completedAt.getTime() - progress.startedAt.getTime()
    });
  }

  /**
   * Get progress for an operation
   * @param operationId - Operation ID
   * @returns Progress or null if not found
   */
  getProgress(operationId: string): PayslipProgress | null {
    return this.progressMap.get(operationId) || null;
  }

  /**
   * Get all active operations
   * @returns Array of active operations
   */
  getActiveOperations(): PayslipProgress[] {
    return Array.from(this.progressMap.values()).filter(
      progress => progress.status === 'processing'
    );
  }

  /**
   * Cancel an operation
   * @param operationId - Operation ID
   */
  cancelOperation(operationId: string): void {
    this.completeOperation(operationId, 'cancelled');
  }

  /**
   * Remove an operation from tracking
   * @param operationId - Operation ID
   */
  removeOperation(operationId: string): void {
    this.progressMap.delete(operationId);
    logger.debug(`Removed operation ${operationId} from tracking`, LogCategory.PAYROLL);
  }

  /**
   * Start cleanup timer to remove old operations
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldOperations();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Clean up old completed operations
   */
  private cleanupOldOperations(): void {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    // Remove operations older than maxAge
    for (const [operationId, progress] of this.progressMap.entries()) {
      const age = now - progress.startedAt.getTime();
      
      if (age > maxAge && progress.status !== 'processing') {
        this.progressMap.delete(operationId);
        logger.debug(`Cleaned up old operation ${operationId}`, LogCategory.PAYROLL);
      }
    }

    // If we still have too many operations, remove the oldest completed ones
    if (this.progressMap.size > this.MAX_OPERATIONS) {
      const operations = Array.from(this.progressMap.entries())
        .filter(([_, progress]) => progress.status !== 'processing')
        .sort(([_, a], [__, b]) => a.startedAt.getTime() - b.startedAt.getTime());

      const toRemove = operations.slice(0, operations.length - this.MAX_OPERATIONS + 10);
      
      for (const [operationId] of toRemove) {
        this.progressMap.delete(operationId);
      }

      if (toRemove.length > 0) {
        logger.info(`Cleaned up ${toRemove.length} old operations`, LogCategory.PAYROLL);
      }
    }
  }

  /**
   * Stop the cleanup timer
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }
}

// Export singleton instance
export const payslipProgressService = new PayslipProgressService();

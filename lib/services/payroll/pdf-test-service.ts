// lib/services/payroll/pdf-test-service.ts
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * Simple PDF test service to verify PDFKit functionality
 */
export class PdfTestService {
  /**
   * Test basic PDF generation
   */
  static async testBasicPdfGeneration(): Promise<{ success: boolean; error?: string; bufferSize?: number }> {
    try {
      logger.info('Starting basic PDF generation test', LogCategory.PAYROLL);

      // Dynamic import to avoid issues
      const PDFDocument = (await import('pdfkit')).default;
      
      logger.info('PDFKit imported successfully', LogCategory.PAYROLL);

      // Create a simple PDF document
      const doc = new PDFDocument();
      const buffers: Buffer[] = [];

      // Collect PDF chunks
      doc.on('data', (chunk) => buffers.push(chunk));

      // Add some basic content
      doc.fontSize(20).text('PDF Test Document', 100, 100);
      doc.fontSize(12).text('This is a test to verify PDFKit functionality.', 100, 150);
      doc.text('If you can see this, PDFKit is working correctly.', 100, 180);

      // End the document
      doc.end();

      // Wait for completion
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        doc.on('end', () => {
          try {
            const finalBuffer = Buffer.concat(buffers);
            resolve(finalBuffer);
          } catch (error) {
            reject(error);
          }
        });

        doc.on('error', (error) => {
          reject(error);
        });
      });

      logger.info(`PDF test completed successfully, buffer size: ${buffer.length} bytes`, LogCategory.PAYROLL);

      return {
        success: true,
        bufferSize: buffer.length
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('PDF test failed', LogCategory.PAYROLL, {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Test PDF generation with our PdfGenerator class
   */
  static async testPdfGeneratorClass(): Promise<{ success: boolean; error?: string; bufferSize?: number }> {
    try {
      logger.info('Starting PdfGenerator class test', LogCategory.PAYROLL);

      // Import our PDF Generator
      const { PdfGenerator } = await import('@/lib/backend/utils/pdf-generator');

      logger.info('PdfGenerator imported successfully', LogCategory.PAYROLL);

      // Create PDF document
      const pdfGenerator = new PdfGenerator({
        title: 'Test Document',
        author: 'PDF Test Service',
        subject: 'Testing PDF Generation',
        keywords: ['test', 'pdf'],
        pageSize: 'A4',
        pageOrientation: 'portrait',
        margins: { top: 50, bottom: 50, left: 50, right: 50 }
      });

      logger.info('PdfGenerator instance created', LogCategory.PAYROLL);

      // Add content
      pdfGenerator.addTitle('Test Document');
      pdfGenerator.addText('This is a test document to verify our PdfGenerator class works correctly.');
      pdfGenerator.addText('If you can see this, the PdfGenerator is functioning properly.');

      logger.info('Content added to PDF', LogCategory.PAYROLL);

      // Generate PDF
      const buffer = await pdfGenerator.generate();

      logger.info(`PdfGenerator test completed successfully, buffer size: ${buffer.length} bytes`, LogCategory.PAYROLL);

      return {
        success: true,
        bufferSize: buffer.length
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('PdfGenerator test failed', LogCategory.PAYROLL, {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }
}

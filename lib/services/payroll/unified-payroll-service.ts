// lib/services/payroll/unified-payroll-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { taxService } from './tax-service';
import PayrollRun, { IPayrollRun } from '@/models/payroll/PayrollRun';
import PayrollRecord, { IPayrollRecord } from '@/models/payroll/PayrollRecord';
import EmployeeSalary, { IEmployeeSalary } from '@/models/payroll/EmployeeSalary';
import { PaySlip } from '@/models/payroll/PaySlip';
import { Employee } from '@/models/Employee';
import mongoose from 'mongoose';

/**
 * Unified Payroll Service
 * 
 * This service consolidates all payroll operations and ensures consistent
 * calculation logic across the entire application. It replaces the multiple
 * fragmented payroll services to eliminate calculation discrepancies.
 */
export class UnifiedPayrollService {

  /**
   * Get payroll records for a payroll run with pagination
   * @param payrollRunId - Payroll run ID
   * @param options - Query options
   * @returns Paginated payroll records
   */
  async getPayrollRecords(payrollRunId: string, options: {
    page?: number;
    limit?: number;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    docs: any[];
    totalDocs: number;
    page: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    try {
      await connectToDatabase();

      const {
        page = 1,
        limit = 10,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // Build query
      const query: any = { payrollRunId };

      if (status) {
        query.status = status;
      }

      // Count total documents
      const totalDocs = await PayrollRecord.countDocuments(query);

      // Get paginated results with employee details
      const docs = await PayrollRecord.find(query)
        .populate('employeeId', 'firstName lastName employeeNumber departmentId')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'departmentId',
            select: 'name'
          }
        })
        .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean();

      // Add employee name and department to each record for frontend display
      const enrichedDocs = docs.map(record => {
        const employee = record.employeeId as any;
        const department = employee?.departmentId as any;

        return {
          ...record,
          employeeName: employee ? `${employee.firstName} ${employee.lastName}` : 'Unknown Employee',
          department: String(department?.name || 'N/A')
        };
      });

      // Calculate pagination info
      const totalPages = Math.ceil(totalDocs / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      logger.info(`Retrieved ${enrichedDocs.length} payroll records for run ${payrollRunId}`, LogCategory.PAYROLL, {
        payrollRunId,
        totalDocs,
        page,
        totalPages
      });

      return {
        docs: enrichedDocs,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      };
    } catch (error) {
      logger.error('Error getting payroll records', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get payroll runs with pagination
   * @param options - Query options
   * @returns Paginated payroll runs
   */
  async getPayrollRuns(options: {
    page?: number;
    limit?: number;
    period?: string;
    status?: string;
    fromDate?: Date;
    toDate?: Date;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    docs: IPayrollRun[];
    totalDocs: number;
    page: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    try {
      await connectToDatabase();

      const {
        page = 1,
        limit = 10,
        period,
        status,
        fromDate,
        toDate,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // Build query
      const query: any = {};

      if (period) {
        const [month, year] = period.split('-').map(Number);
        if (month && year) {
          query['payPeriod.month'] = month;
          query['payPeriod.year'] = year;
        }
      }

      if (status) {
        query.status = status;
      }

      if (fromDate || toDate) {
        query['payPeriod.startDate'] = {};
        if (fromDate) {
          query['payPeriod.startDate'].$gte = fromDate;
        }
        if (toDate) {
          query['payPeriod.startDate'].$lte = toDate;
        }
      }

      // Count total documents
      const totalDocs = await PayrollRun.countDocuments(query);

      // Get paginated results
      const docs = await PayrollRun.find(query)
        .populate('departments', 'name')
        .populate('createdBy', 'firstName lastName')
        .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
        .skip((page - 1) * limit)
        .limit(limit);

      // Calculate pagination info
      const totalPages = Math.ceil(totalDocs / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return {
        docs,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      };
    } catch (error) {
      logger.error('Error getting payroll runs', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get payroll run by ID
   * @param payrollRunId - Payroll run ID
   * @returns Payroll run or null if not found
   */
  async getPayrollRunById(payrollRunId: string): Promise<IPayrollRun | null> {
    try {
      await connectToDatabase();

      const payrollRun = await PayrollRun.findById(payrollRunId)
        .populate('departments', 'name')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .populate('approvedBy', 'firstName lastName email')
        .lean();

      if (!payrollRun) {
        logger.warn(`Payroll run not found: ${payrollRunId}`, LogCategory.PAYROLL);
        return null;
      }

      logger.info(`Retrieved payroll run: ${payrollRunId}`, LogCategory.PAYROLL, {
        payrollRunId,
        status: payrollRun.status,
        payPeriod: payrollRun.payPeriod
      });

      return payrollRun as IPayrollRun;
    } catch (error) {
      logger.error('Error getting payroll run by ID', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Calculate salary for an employee with unified logic
   * @param employeeId - Employee ID
   * @param payPeriod - Pay period (month and year)
   * @returns Calculated salary components
   */
  async calculateEmployeeSalary(employeeId: string, payPeriod: { month: number; year: number }): Promise<{
    components: any[];
    grossSalary: number;
    totalDeductions: number;
    totalTax: number;
    netSalary: number;
    currency: string;
    error?: string;
    message?: string;
  }> {
    try {
      await connectToDatabase();
      
      // Get employee salary
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId,
        isActive: true,
        effectiveDate: { $lte: new Date() },
        $or: [
          { endDate: { $gt: new Date() } },
          { endDate: { $exists: false } },
          { endDate: null }
        ]
      }).sort({ effectiveDate: -1 });
      
      if (!employeeSalary) {
        // Instead of throwing an error, return a structured response indicating no salary found
        logger.warn(`No active salary found for employee ${employeeId}`, LogCategory.PAYROLL, {
          employeeId,
          payPeriod
        });

        // Return a default structure indicating no salary data
        return {
          components: [],
          grossSalary: 0,
          totalDeductions: 0,
          totalTax: 0,
          netSalary: 0,
          currency: 'MWK',
          error: 'NO_ACTIVE_SALARY',
          message: `No active salary record found for employee ${employeeId}`
        };
      }
      
      // Initialize components array
      const components: any[] = [];
      
      // Add basic salary component
      components.push({
        name: 'Basic Salary',
        type: 'basic',
        amount: employeeSalary.basicSalary,
        isTaxable: true,
      });
      
      // Add allowances
      for (const allowance of employeeSalary.allowances) {
        let amount = 0;
        
        if (allowance.amount) {
          amount = allowance.amount;
        } else if (allowance.percentage) {
          amount = employeeSalary.basicSalary * (allowance.percentage / 100);
        }
        
        components.push({
          name: allowance.name,
          type: 'allowance',
          amount,
          isTaxable: allowance.isTaxable,
        });
      }
      
      // Calculate gross salary
      const grossSalary = components.reduce((sum, component) => sum + component.amount, 0);
      
      // Calculate taxable amount
      const taxableAmount = components
        .filter(component => component.isTaxable)
        .reduce((sum, component) => sum + component.amount, 0);
      
      // Calculate tax using fallback method (same as employee salaries page)
      // This ensures consistent calculation until database tax brackets are properly configured
      const totalTax = this.calculateFallbackTax(taxableAmount);

      logger.info('Tax calculation details', LogCategory.PAYROLL, {
        employeeId,
        taxableAmount,
        totalTax,
        grossSalary,
        calculationMethod: 'fallback_direct',
        breakdown: {
          bracket1: taxableAmount <= 150000 ? 0 : 0,
          bracket2: taxableAmount <= 150000 ? 0 : taxableAmount <= 500000 ? (taxableAmount - 150000) * 0.25 : 87500,
          bracket3: taxableAmount <= 500000 ? 0 : taxableAmount <= 2550000 ? (taxableAmount - 500000) * 0.3 : 615000,
          bracket4: taxableAmount <= 2550000 ? 0 : (taxableAmount - 2550000) * 0.35
        }
      });
      
      // Add tax component
      components.push({
        name: 'PAYE Tax',
        type: 'tax',
        amount: totalTax,
        isTaxable: false,
      });
      
      // Add deductions (ensuring positive values for calculation)
      for (const deduction of employeeSalary.deductions) {
        let amount = 0;
        
        if (deduction.amount) {
          // CRITICAL FIX: Ensure deduction amount is positive for calculation purposes
          amount = Math.abs(deduction.amount);
        } else if (deduction.percentage) {
          amount = employeeSalary.basicSalary * (deduction.percentage / 100);
        }
        
        components.push({
          name: deduction.name,
          type: 'deduction',
          amount,
          isTaxable: false,
        });
      }
      
      // Calculate total deductions (including tax) - CRITICAL FIX
      const totalDeductions = components
        .filter(component => component.type === 'deduction' || component.type === 'tax')
        .reduce((sum, component) => sum + Math.abs(component.amount), 0);
      
      // Calculate net salary - CRITICAL FIX
      const netSalary = grossSalary - totalDeductions;
      
      // Add detailed logging for debugging
      logger.info('Unified salary calculation breakdown', LogCategory.PAYROLL, {
        employeeId,
        grossSalary,
        taxableAmount,
        totalTax,
        totalDeductions,
        netSalary,
        componentsCount: components.length,
        components: components.map(c => ({
          name: c.name,
          type: c.type,
          amount: c.amount,
          isTaxable: c.isTaxable
        }))
      });
      
      return {
        components,
        grossSalary,
        totalDeductions,
        totalTax,
        netSalary,
        currency: employeeSalary.currency,
      };
    } catch (error) {
      logger.error('Error calculating salary', LogCategory.PAYROLL, error);
      throw error;
    }
  }
  
  /**
   * Create a new payroll run
   * @param data - Payroll run data
   * @param userId - User ID creating the payroll run
   * @returns Created payroll run
   */
  async createPayrollRun(data: Partial<IPayrollRun>, userId: string): Promise<IPayrollRun> {
    try {
      await connectToDatabase();

      // Validate pay period
      if (!data.payPeriod) {
        throw new Error('Pay period is required');
      }

      // Check if a payroll run already exists for this period
      const existingRun = await PayrollRun.findOne({
        'payPeriod.month': data.payPeriod.month,
        'payPeriod.year': data.payPeriod.year,
        status: { $ne: 'cancelled' }
      });

      if (existingRun) {
        throw new Error(`A payroll run already exists for ${data.payPeriod.month}/${data.payPeriod.year}`);
      }

      // Get employees count
      const query: any = { employmentStatus: 'active' };

      // Filter by departments if specified
      if (data.departments && data.departments.length > 0) {
        query.departmentId = { $in: data.departments };
      }

      const employeeCount = await Employee.countDocuments(query);

      // Create payroll run with employee count
      const payrollRun = new PayrollRun({
        ...data,
        createdBy: userId,
        status: 'draft',
        totalEmployees: employeeCount,
      });

      await payrollRun.save();

      return payrollRun;
    } catch (error) {
      logger.error('Error creating payroll run', LogCategory.PAYROLL, error);
      throw error;
    }
  }
  
  /**
   * Calculate fallback tax using progressive Malawi PAYE brackets (2024)
   * This uses the same calculation logic as the employee salaries page
   * @param taxableAmount - Taxable amount
   * @returns Calculated tax amount
   */
  private calculateFallbackTax(taxableAmount: number): number {
    logger.info('Calculating fallback tax', LogCategory.PAYROLL, {
      taxableAmount,
      step: 'starting_calculation'
    });

    let tax = 0;

    // Use the same calculation logic as employee salaries page
    if (taxableAmount <= 150000) {
      tax = 0;
      logger.info('Tax bracket 1 applied', LogCategory.PAYROLL, { taxableAmount, tax, bracket: '0-150000 @ 0%' });
    } else if (taxableAmount <= 500000) {
      tax = (taxableAmount - 150000) * 0.25;
      logger.info('Tax bracket 2 applied', LogCategory.PAYROLL, {
        taxableAmount,
        tax,
        bracket: '150001-500000 @ 25%',
        calculation: `(${taxableAmount} - 150000) * 0.25 = ${tax}`
      });
    } else if (taxableAmount <= 2550000) {
      tax = 87500 + (taxableAmount - 500000) * 0.3;
      logger.info('Tax bracket 3 applied', LogCategory.PAYROLL, {
        taxableAmount,
        tax,
        bracket: '500001-2550000 @ 30%',
        calculation: `87500 + (${taxableAmount} - 500000) * 0.3 = ${tax}`
      });
    } else {
      tax = 702500 + (taxableAmount - 2550000) * 0.35;
      logger.info('Tax bracket 4 applied', LogCategory.PAYROLL, {
        taxableAmount,
        tax,
        bracket: 'above 2550000 @ 35%',
        calculation: `702500 + (${taxableAmount} - 2550000) * 0.35 = ${tax}`
      });
    }

    logger.info('Fallback tax calculation completed', LogCategory.PAYROLL, {
      taxableAmount,
      finalTax: tax,
      effectiveRate: ((tax / taxableAmount) * 100).toFixed(2) + '%'
    });

    return tax;
  }

  /**
   * Process a payroll run with unified calculation logic
   * @param payrollRunId - Payroll run ID
   * @param options - Processing options
   * @returns Processed payroll run
   */
  async processPayrollRun(payrollRunId: string, options: {
    userId: string;
    notes?: string;
  }): Promise<IPayrollRun> {
    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);

      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Check if payroll run is in draft status
      if (payrollRun.status !== 'draft') {
        throw new Error(`Payroll run ${payrollRunId} is not in draft status`);
      }

      // Update status to processing
      payrollRun.status = 'processing';
      payrollRun.updatedBy = new mongoose.Types.ObjectId(options.userId);
      if (options.notes) {
        payrollRun.notes = options.notes;
      }
      await payrollRun.save();

      // Get employees to process
      const query: any = { employmentStatus: 'active' };

      // Filter by departments if specified
      if (payrollRun.departments && payrollRun.departments.length > 0) {
        query.departmentId = { $in: payrollRun.departments };
      }

      const employees = await Employee.find(query)
        .populate('departmentId')
        .populate('positionId');

      // Update total employees
      payrollRun.totalEmployees = employees.length;
      await payrollRun.save();

      // Process each employee with unified calculation
      let processedEmployees = 0;
      let totalGrossSalary = 0;
      let totalDeductions = 0;
      let totalTax = 0;
      let totalNetSalary = 0;

      for (const employee of employees) {
        try {
          // Check if employee has an active salary with comprehensive query
          const employeeSalary = await EmployeeSalary.findOne({
            employeeId: employee._id,
            $or: [
              { isActive: true },
              {
                isActive: false,
                effectiveDate: { $lte: new Date() },
                $or: [
                  { endDate: { $gt: new Date() } },
                  { endDate: null }
                ]
              }
            ]
          }).sort({ effectiveDate: -1 });

          if (!employeeSalary) {
            logger.warn(`No active salary found for employee ${employee._id}`, LogCategory.PAYROLL);
            continue;
          }

          // Calculate salary using unified logic
          const salaryResult = await this.calculateEmployeeSalary(
            employee._id.toString(),
            {
              month: payrollRun.payPeriod.month,
              year: payrollRun.payPeriod.year
            }
          );

          // Create payroll record
          const payrollRecord = new PayrollRecord({
            employeeId: employee._id,
            payrollRunId: payrollRun._id,
            payPeriod: payrollRun.payPeriod,
            salaryStructureId: employeeSalary.salaryStructureId,
            currency: salaryResult.currency,
            components: salaryResult.components,
            grossSalary: salaryResult.grossSalary,
            totalDeductions: salaryResult.totalDeductions,
            totalTax: salaryResult.totalTax,
            netSalary: salaryResult.netSalary,
            status: 'draft',
            bankAccount: employeeSalary.bankAccountNumber,
            paymentMethod: employeeSalary.paymentMethod,
            createdBy: options.userId,
          });

          await payrollRecord.save();

          // Update totals with calculated values
          totalGrossSalary += salaryResult.grossSalary;
          totalDeductions += salaryResult.totalDeductions;
          totalTax += salaryResult.totalTax;
          totalNetSalary += salaryResult.netSalary;
          processedEmployees++;

          logger.info(`Processed employee ${employee._id} with unified calculation`, LogCategory.PAYROLL, {
            employeeId: employee._id,
            grossSalary: salaryResult.grossSalary,
            totalDeductions: salaryResult.totalDeductions,
            totalTax: salaryResult.totalTax,
            netSalary: salaryResult.netSalary
          });

          // Update payroll run with progress
          payrollRun.processedEmployees = processedEmployees;
          await payrollRun.save();
        } catch (error) {
          logger.error(`Error processing employee ${employee._id}`, LogCategory.PAYROLL, error);
          // Continue with next employee
        }
      }

      // Update payroll run with final totals
      payrollRun.totalGrossSalary = totalGrossSalary;
      payrollRun.totalDeductions = totalDeductions;
      payrollRun.totalTax = totalTax;
      payrollRun.totalNetSalary = totalNetSalary;
      payrollRun.processedEmployees = processedEmployees;
      payrollRun.status = 'completed';
      payrollRun.processedAt = new Date();
      payrollRun.updatedBy = new mongoose.Types.ObjectId(options.userId);

      // Log the totals before saving
      logger.info('Updating payroll run with unified calculation totals', LogCategory.PAYROLL, {
        payrollRunId: payrollRunId,
        totalGrossSalary,
        totalDeductions,
        totalTax,
        totalNetSalary,
        processedEmployees
      });

      await payrollRun.save();

      return payrollRun;
    } catch (error) {
      logger.error('Error processing payroll run with unified service', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Approve a payroll run
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID approving the payroll run
   * @param notes - Optional approval notes
   * @returns Approved payroll run
   */
  async approvePayrollRun(payrollRunId: string, userId: string, notes?: string): Promise<IPayrollRun> {
    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);

      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Check if payroll run is in completed status
      if (payrollRun.status !== 'completed') {
        throw new Error(`Cannot approve payroll run with status '${payrollRun.status}'`);
      }

      // Update payroll run status to approved
      payrollRun.status = 'approved';
      payrollRun.approvedBy = new mongoose.Types.ObjectId(userId);
      payrollRun.approvedAt = new Date();
      payrollRun.updatedBy = new mongoose.Types.ObjectId(userId);

      if (notes) {
        payrollRun.notes = notes;
      }

      await payrollRun.save();

      // Update all payroll records to approved status
      await PayrollRecord.updateMany(
        { payrollRunId: payrollRun._id },
        {
          status: 'approved',
          updatedBy: new mongoose.Types.ObjectId(userId),
          approvedBy: new mongoose.Types.ObjectId(userId),
          approvedAt: new Date()
        }
      );

      logger.info(`Payroll run ${payrollRunId} approved successfully`, LogCategory.PAYROLL, {
        payrollRunId,
        userId,
        approvedAt: payrollRun.approvedAt
      });

      return payrollRun;
    } catch (error) {
      logger.error('Error approving payroll run', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Mark a payroll run as paid
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID marking the payroll run as paid
   * @returns Paid payroll run
   */
  async markPayrollRunAsPaid(payrollRunId: string, userId: string): Promise<IPayrollRun> {
    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);

      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Check if payroll run is in approved status
      if (payrollRun.status !== 'approved') {
        throw new Error(`Cannot mark payroll run as paid with status '${payrollRun.status}'`);
      }

      // Update payroll run status to paid
      payrollRun.status = 'paid';
      payrollRun.paidAt = new Date();
      payrollRun.updatedBy = new mongoose.Types.ObjectId(userId);

      await payrollRun.save();

      // Update all payroll records to paid status
      await PayrollRecord.updateMany(
        { payrollRunId: payrollRun._id },
        {
          status: 'paid',
          updatedBy: new mongoose.Types.ObjectId(userId),
          paymentDate: new Date()
        }
      );

      logger.info(`Payroll run ${payrollRunId} marked as paid successfully`, LogCategory.PAYROLL, {
        payrollRunId,
        userId,
        paidAt: payrollRun.paidAt
      });

      return payrollRun;
    } catch (error) {
      logger.error('Error marking payroll run as paid', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Cancel a payroll run
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID cancelling the payroll run
   * @param reason - Cancellation reason
   * @returns Cancelled payroll run
   */
  async cancelPayrollRun(payrollRunId: string, userId: string, reason: string): Promise<IPayrollRun> {
    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);

      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Check if payroll run can be cancelled
      if (payrollRun.status === 'paid') {
        throw new Error('Cannot cancel a payroll run that has already been paid');
      }

      // Update payroll run status to cancelled
      payrollRun.status = 'cancelled';
      payrollRun.updatedBy = new mongoose.Types.ObjectId(userId);
      payrollRun.notes = reason;

      await payrollRun.save();

      // Update all payroll records to cancelled status
      await PayrollRecord.updateMany(
        { payrollRunId: payrollRun._id },
        {
          status: 'cancelled',
          updatedBy: new mongoose.Types.ObjectId(userId)
        }
      );

      logger.info(`Payroll run ${payrollRunId} cancelled successfully`, LogCategory.PAYROLL, {
        payrollRunId,
        userId,
        reason
      });

      return payrollRun;
    } catch (error) {
      logger.error('Error cancelling payroll run', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Delete payroll run and all associated records
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID performing the deletion
   * @returns Deletion result
   */
  async deletePayrollRunWithRecords(payrollRunId: string, userId: string): Promise<{
    deletedPayrollRun: boolean;
    deletedRecordsCount: number;
    deletedPayslipsCount: number;
  }> {
    try {
      await connectToDatabase();

      // Get payroll run first to check if it exists
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Count associated records before deletion
      const recordsCount = await PayrollRecord.countDocuments({ payrollRunId });
      const payslipsCount = await PaySlip.countDocuments({ payrollRunId });

      // Delete associated payslips first
      await PaySlip.deleteMany({ payrollRunId });

      // Delete associated payroll records
      await PayrollRecord.deleteMany({ payrollRunId });

      // Delete the payroll run
      await PayrollRun.findByIdAndDelete(payrollRunId);

      logger.info(`Deleted payroll run and associated records`, LogCategory.PAYROLL, {
        payrollRunId,
        deletedRecordsCount: recordsCount,
        deletedPayslipsCount: payslipsCount,
        deletedBy: userId
      });

      return {
        deletedPayrollRun: true,
        deletedRecordsCount: recordsCount,
        deletedPayslipsCount: payslipsCount
      };
    } catch (error) {
      logger.error('Error deleting payroll run with records', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Process payroll for a single employee
   * @param employeeId - Employee ID
   * @param payrollDate - Payroll date
   * @param userId - User ID processing the payroll
   * @returns Processed payroll record
   */
  async processPayroll(employeeId: string, payrollDate: Date, userId: string): Promise<IPayrollRecord> {
    try {
      await connectToDatabase();

      // Get employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error(`Employee ${employeeId} not found`);
      }

      // Calculate salary for the employee
      const payPeriod = {
        month: payrollDate.getMonth() + 1,
        year: payrollDate.getFullYear()
      };

      const salaryResult = await this.calculateEmployeeSalary(employeeId, payPeriod);

      if (salaryResult.error) {
        throw new Error(salaryResult.message || 'Failed to calculate salary');
      }

      // Get employee salary for additional details with comprehensive query
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId,
        $or: [
          { isActive: true },
          {
            isActive: false,
            effectiveDate: { $lte: new Date() },
            $or: [
              { endDate: { $gt: new Date() } },
              { endDate: null }
            ]
          }
        ]
      }).sort({ effectiveDate: -1 });

      if (!employeeSalary) {
        throw new Error(`No active salary found for employee ${employeeId}`);
      }

      // Create payroll record
      const payrollRecord = new PayrollRecord({
        employeeId,
        payPeriod: {
          month: payPeriod.month,
          year: payPeriod.year,
          startDate: new Date(payPeriod.year, payPeriod.month - 1, 1),
          endDate: new Date(payPeriod.year, payPeriod.month, 0)
        },
        salaryStructureId: employeeSalary.salaryStructureId,
        currency: salaryResult.currency,
        components: salaryResult.components,
        grossSalary: salaryResult.grossSalary,
        totalDeductions: salaryResult.totalDeductions,
        totalTax: salaryResult.totalTax,
        netSalary: salaryResult.netSalary,
        status: 'draft',
        bankAccount: employeeSalary.bankAccountNumber,
        paymentMethod: employeeSalary.paymentMethod,
        createdBy: userId,
      });

      await payrollRecord.save();

      logger.info(`Processed payroll for employee ${employeeId}`, LogCategory.PAYROLL, {
        employeeId,
        payrollDate,
        grossSalary: salaryResult.grossSalary,
        netSalary: salaryResult.netSalary
      });

      return payrollRecord;
    } catch (error) {
      logger.error('Error processing individual payroll', LogCategory.PAYROLL, error);
      throw error;
    }
  }
}

// Export singleton instance
export const unifiedPayrollService = new UnifiedPayrollService();

// Export as payrollService for backward compatibility with existing API routes
export const payrollService = unifiedPayrollService;

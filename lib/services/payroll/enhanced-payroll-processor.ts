import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import { Employee } from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import { unifiedPayrollService } from './unified-payroll-service';

// Progress tracking interface
interface EmployeeProcessingProgress {
  employeeId: string;
  employeeName: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
  processingTime?: number;
  startTime?: number;
  grossSalary?: number;
  netSalary?: number;
}

interface PayrollProcessingProgress {
  operationId: string;
  payrollRunId: string;
  status: 'starting' | 'processing' | 'completed' | 'error';
  totalEmployees: number;
  processedEmployees: number;
  failedEmployees: number;
  currentEmployee?: string;
  employees: EmployeeProcessingProgress[];
  startTime: number;
  estimatedTimeRemaining?: number;
  averageProcessingTime?: number;
  error?: string;
}

class EnhancedPayrollProcessor {
  private progressMap = new Map<string, PayrollProcessingProgress>();
  private readonly CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutes

  constructor() {
    // Cleanup old progress entries periodically
    setInterval(() => {
      this.cleanupOldProgress();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Start payroll processing with real-time progress tracking
   */
  async startPayrollProcessing(
    payrollRunId: string,
    userId: string,
    options: {
      batchSize?: number;
      maxConcurrency?: number;
    } = {}
  ): Promise<{ operationId: string; totalEmployees: number }> {
    const operationId = `payroll_${payrollRunId}_${Date.now()}`;
    const { batchSize = 10, maxConcurrency = 3 } = options;

    try {
      await connectToDatabase();

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Get employees to process
      const query: any = { employmentStatus: 'active' };
      if (payrollRun.departments && payrollRun.departments.length > 0) {
        query.departmentId = { $in: payrollRun.departments };
      }

      const employees = await Employee.find(query)
        .populate('departmentId')
        .lean() as any[];

      if (employees.length === 0) {
        throw new Error('No active employees found for processing');
      }

      // Initialize progress tracking
      const progress: PayrollProcessingProgress = {
        operationId,
        payrollRunId,
        status: 'starting',
        totalEmployees: employees.length,
        processedEmployees: 0,
        failedEmployees: 0,
        employees: employees.map(emp => ({
          employeeId: emp._id.toString(),
          employeeName: `${emp.firstName} ${emp.lastName}`,
          status: 'pending'
        })),
        startTime: Date.now()
      };

      this.progressMap.set(operationId, progress);

      // Start processing in background
      setTimeout(() => {
        this.processEmployeesWithProgress(
          operationId,
          payrollRunId,
          employees,
          userId,
          { batchSize, maxConcurrency }
        ).catch(error => {
          logger.error(`Error in background payroll processing for operation ${operationId}`, LogCategory.PAYROLL, error);
          this.updateProgressStatus(operationId, 'error', error.message);
        });
      }, 100); // Small delay to ensure the operation ID is returned first

      return {
        operationId,
        totalEmployees: employees.length
      };

    } catch (error) {
      logger.error(`Error starting payroll processing for run ${payrollRunId}`, LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get current progress for an operation
   */
  getProgress(operationId: string): PayrollProcessingProgress | null {
    return this.progressMap.get(operationId) || null;
  }

  /**
   * Process employees with real-time progress updates
   */
  private async processEmployeesWithProgress(
    operationId: string,
    payrollRunId: string,
    employees: any[],
    userId: string,
    options: { batchSize: number; maxConcurrency: number }
  ): Promise<void> {
    const { batchSize, maxConcurrency } = options;

    try {
      logger.info(`Starting employee processing for operation ${operationId}`, LogCategory.PAYROLL, {
        operationId,
        payrollRunId,
        totalEmployees: employees.length,
        batchSize,
        maxConcurrency
      });

      // Update status to processing
      this.updateProgressStatus(operationId, 'processing');

      // Get payroll run
      const payrollRun = await PayrollRun.findById(payrollRunId);
      if (!payrollRun) {
        throw new Error(`Payroll run ${payrollRunId} not found`);
      }

      // Update payroll run status
      payrollRun.status = 'processing';
      await payrollRun.save();

      // Process employees in batches with concurrency control
      for (let i = 0; i < employees.length; i += batchSize) {
        const batchEmployees = employees.slice(i, i + batchSize);

        // Process batch with limited concurrency
        await this.processBatchWithProgress(
          operationId,
          batchEmployees,
          payrollRun,
          userId,
          maxConcurrency
        );

        // Small delay between batches
        if (i + batchSize < employees.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Calculate final totals and update payroll run
      await this.finalizePayrollRun(operationId, payrollRunId);

      // Mark as completed
      this.updateProgressStatus(operationId, 'completed');

      logger.info(`Payroll processing completed for operation ${operationId}`, LogCategory.PAYROLL, {
        operationId,
        payrollRunId,
        totalEmployees: employees.length
      });

    } catch (error) {
      logger.error(`Error processing employees for operation ${operationId}`, LogCategory.PAYROLL, error);
      this.updateProgressStatus(operationId, 'error', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  /**
   * Process a batch of employees with progress tracking
   */
  private async processBatchWithProgress(
    operationId: string,
    employees: any[],
    payrollRun: any,
    userId: string,
    maxConcurrency: number
  ): Promise<void> {
    const semaphore = new Array(maxConcurrency).fill(null);
    let currentIndex = 0;

    const processNext = async (): Promise<void> => {
      while (currentIndex < employees.length) {
        const employeeIndex = currentIndex++;
        const employee = employees[employeeIndex];
        const employeeName = `${employee.firstName} ${employee.lastName}`;

        try {
          // Update current employee
          this.updateCurrentEmployee(operationId, employeeName);

          // Mark employee as processing
          this.updateEmployeeStatus(operationId, employee._id.toString(), 'processing');

          const startTime = Date.now();

          // Process individual employee
          const result = await this.processIndividualEmployee(employee, payrollRun, userId);

          const processingTime = Date.now() - startTime;

          // Mark employee as completed
          this.updateEmployeeStatus(
            operationId,
            employee._id.toString(),
            'completed',
            undefined,
            processingTime,
            result.grossSalary,
            result.netSalary
          );

          // Update processed count
          this.incrementProcessedCount(operationId);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';

          // Mark employee as error
          this.updateEmployeeStatus(
            operationId,
            employee._id.toString(),
            'error',
            errorMessage
          );

          // Update failed count
          this.incrementFailedCount(operationId);

          logger.error(`Error processing employee ${employeeName}`, LogCategory.PAYROLL, error);
        }
      }
    };

    // Start concurrent processing
    await Promise.all(semaphore.map(() => processNext()));
  }

  /**
   * Process individual employee
   */
  private async processIndividualEmployee(
    employee: any,
    payrollRun: any,
    userId: string
  ): Promise<{ grossSalary: number; netSalary: number }> {
    try {
      // Check if payroll record already exists
      const existingRecord = await PayrollRecord.findOne({
        employeeId: employee._id,
        payrollRunId: payrollRun._id
      });

      if (existingRecord) {
        return {
          grossSalary: existingRecord.grossSalary,
          netSalary: existingRecord.netSalary
        };
      }

      // Get employee salary
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId: employee._id,
        status: 'active'
      }).populate('salaryStructureId');

      if (!employeeSalary) {
        throw new Error(`No active salary found for employee ${employee.firstName} ${employee.lastName}`);
      }

      // Calculate salary using unified service
      const salaryResult = await unifiedPayrollService.calculateEmployeeSalary(
        employee._id.toString(),
        {
          month: payrollRun.payPeriod.month,
          year: payrollRun.payPeriod.year
        }
      );

      // Create payroll record
      const payrollRecord = new PayrollRecord({
        employeeId: employee._id,
        payrollRunId: payrollRun._id,
        payPeriod: payrollRun.payPeriod,
        salaryStructureId: employeeSalary.salaryStructureId,
        currency: salaryResult.currency,
        components: salaryResult.components,
        grossSalary: salaryResult.grossSalary,
        totalDeductions: salaryResult.totalDeductions,
        totalTax: salaryResult.totalTax,
        netSalary: salaryResult.netSalary,
        status: 'draft',
        bankAccount: employeeSalary.bankAccountNumber,
        paymentMethod: employeeSalary.paymentMethod,
        createdBy: userId,
      });

      await payrollRecord.save();

      return {
        grossSalary: salaryResult.grossSalary,
        netSalary: salaryResult.netSalary
      };

    } catch (error) {
      logger.error(`Error processing individual employee ${employee._id}`, LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Finalize payroll run with calculated totals
   */
  private async finalizePayrollRun(operationId: string, payrollRunId: string): Promise<void> {
    try {
      const progress = this.progressMap.get(operationId);
      if (!progress) return;

      // Get all payroll records for this run
      const payrollRecords = await PayrollRecord.find({ payrollRunId });

      // Calculate totals
      const totals = payrollRecords.reduce(
        (acc, record) => ({
          totalGrossSalary: acc.totalGrossSalary + record.grossSalary,
          totalDeductions: acc.totalDeductions + record.totalDeductions,
          totalTax: acc.totalTax + record.totalTax,
          totalNetSalary: acc.totalNetSalary + record.netSalary
        }),
        { totalGrossSalary: 0, totalDeductions: 0, totalTax: 0, totalNetSalary: 0 }
      );

      // Update payroll run
      await PayrollRun.findByIdAndUpdate(payrollRunId, {
        status: 'completed',
        processedEmployees: progress.processedEmployees,
        ...totals,
        processedAt: new Date()
      });

    } catch (error) {
      logger.error(`Error finalizing payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
      throw error;
    }
  }

  // Progress update methods
  private updateProgressStatus(operationId: string, status: PayrollProcessingProgress['status'], error?: string): void {
    const progress = this.progressMap.get(operationId);
    if (progress) {
      progress.status = status;
      if (error) progress.error = error;
      this.updateEstimatedTime(operationId);
    }
  }

  private updateCurrentEmployee(operationId: string, employeeName: string): void {
    const progress = this.progressMap.get(operationId);
    if (progress) {
      progress.currentEmployee = employeeName;
    }
  }

  private updateEmployeeStatus(
    operationId: string,
    employeeId: string,
    status: EmployeeProcessingProgress['status'],
    error?: string,
    processingTime?: number,
    grossSalary?: number,
    netSalary?: number
  ): void {
    const progress = this.progressMap.get(operationId);
    if (progress) {
      const employee = progress.employees.find(emp => emp.employeeId === employeeId);
      if (employee) {
        employee.status = status;
        if (error) employee.error = error;
        if (processingTime) employee.processingTime = processingTime;
        if (grossSalary !== undefined) employee.grossSalary = grossSalary;
        if (netSalary !== undefined) employee.netSalary = netSalary;
      }
    }
  }

  private incrementProcessedCount(operationId: string): void {
    const progress = this.progressMap.get(operationId);
    if (progress) {
      progress.processedEmployees++;
      this.updateEstimatedTime(operationId);
    }
  }

  private incrementFailedCount(operationId: string): void {
    const progress = this.progressMap.get(operationId);
    if (progress) {
      progress.failedEmployees++;
    }
  }

  private updateEstimatedTime(operationId: string): void {
    const progress = this.progressMap.get(operationId);
    if (progress && progress.processedEmployees > 0) {
      const elapsedTime = Date.now() - progress.startTime;
      const averageTime = elapsedTime / progress.processedEmployees;
      const remainingEmployees = progress.totalEmployees - progress.processedEmployees;

      progress.averageProcessingTime = averageTime;
      progress.estimatedTimeRemaining = remainingEmployees * averageTime;
    }
  }

  private cleanupOldProgress(): void {
    const cutoffTime = Date.now() - this.CLEANUP_INTERVAL;
    for (const [operationId, progress] of this.progressMap.entries()) {
      if (progress.startTime < cutoffTime && (progress.status === 'completed' || progress.status === 'error')) {
        this.progressMap.delete(operationId);
      }
    }
  }
}

export const enhancedPayrollProcessor = new EnhancedPayrollProcessor();

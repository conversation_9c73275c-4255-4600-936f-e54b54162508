# Inventory Dependencies Module Implementation Guide

## Overview

This guide outlines the implementation plan for the **direct and required dependencies** of the Procurement Inventory module. These modules are essential for the inventory system to function properly and enable full CRUD operations.

## 🎯 Implementation Objectives

1. **Complete Supplier Module** - Primary dependency for inventory preferred suppliers
2. **Complete PurchaseOrder Module** - Essential for inventory procurement tracking
3. **Enhance User Module Integration** - Required for audit trails and ownership
4. **Frontend Integration** - Complete UI/UX for all dependency modules
5. **API Integration** - Ensure seamless communication between modules

## 📋 Module Dependencies Analysis

### Direct Dependencies (Required)
```
ProcurementInventory → ProcurementCategory ✅ (COMPLETED)
ProcurementInventory → Supplier ⚠️ (PARTIAL - needs completion)
ProcurementInventory → User ✅ (EXISTS - needs integration)
```

### Indirect Dependencies (Optional but Important)
```
ProcurementInventory → PurchaseOrder ⚠️ (PARTIAL - needs completion)
ProcurementInventory → InventoryItem 🔄 (INTEGRATION NEEDED)
ProcurementInventory → Asset 🔄 (INTEGRATION NEEDED)
```

## 🏗️ Implementation Phases

### Phase 1: Supplier Module Completion (Priority: HIGH)

#### Current Status
- ✅ **Model**: `models/procurement/Supplier.ts` (EXISTS - basic)
- ✅ **Service**: `lib/backend/services/procurement/SupplierService.ts` (EXISTS - complete)
- ✅ **API Routes**: `/api/procurement/supplier/route.ts` & `/api/procurement/suppliers/route.ts` (EXISTS)
- ❌ **Frontend**: Missing comprehensive UI components

#### Required Implementation

**1. Enhanced Supplier Model**
- File: `models/procurement/Supplier.ts`
- Status: ⚠️ NEEDS ENHANCEMENT
- Tasks:
  - [ ] Align with SupplierService interface
  - [ ] Add missing fields (supplierId, enhanced contact info)
  - [ ] Add validation and indexes
  - [ ] Add instance methods

**2. Frontend Components**
- **Supplier Management Page**
  - File: `app/(dashboard)/dashboard/procurement/suppliers/page.tsx`
  - Status: ❌ MISSING
  
- **Supplier Form Component**
  - File: `components/procurement/forms/supplier-form.tsx`
  - Status: ❌ MISSING
  
- **Supplier List Component**
  - File: `components/procurement/lists/supplier-list.tsx`
  - Status: ❌ MISSING
  
- **Supplier Modal Component**
  - File: `components/procurement/modals/supplier-modal.tsx`
  - Status: ❌ MISSING

**3. Store Integration**
- File: `lib/stores/procurement-store.ts`
- Status: ⚠️ PARTIAL (interfaces exist, actions missing)
- Tasks:
  - [ ] Add supplier CRUD actions
  - [ ] Add supplier state management
  - [ ] Add supplier filtering and pagination

### Phase 2: PurchaseOrder Module Completion (Priority: HIGH)

#### Current Status
- ✅ **Model**: `models/procurement/PurchaseOrder.ts` (EXISTS - complete)
- ✅ **Service**: `lib/backend/services/procurement/PurchaseOrderService.ts` (EXISTS - complete)
- ✅ **API Routes**: `/api/procurement/purchase-orders/route.ts` (EXISTS)
- ❌ **Frontend**: Missing comprehensive UI components

#### Required Implementation

**1. Frontend Components**
- **Purchase Order Management Page**
  - File: `app/(dashboard)/dashboard/procurement/purchase-orders/page.tsx`
  - Status: ❌ MISSING
  
- **Purchase Order Form Component**
  - File: `components/procurement/forms/purchase-order-form.tsx`
  - Status: ❌ MISSING
  
- **Purchase Order List Component**
  - File: `components/procurement/lists/purchase-order-list.tsx`
  - Status: ❌ MISSING
  
- **Purchase Order Modal Component**
  - File: `components/procurement/modals/purchase-order-modal.tsx`
  - Status: ❌ MISSING

**2. Store Integration**
- File: `lib/stores/procurement-store.ts`
- Status: ⚠️ PARTIAL (interfaces exist, actions missing)
- Tasks:
  - [ ] Add purchase order CRUD actions
  - [ ] Add purchase order state management
  - [ ] Add purchase order filtering and pagination

### Phase 3: User Module Integration (Priority: MEDIUM)

#### Current Status
- ✅ **Model**: `models/User.ts` (EXISTS)
- ✅ **Service**: Various user services (EXISTS)
- ✅ **API Routes**: User management routes (EXISTS)
- ⚠️ **Integration**: Needs procurement-specific integration

#### Required Implementation

**1. User Selection Components**
- **User Selector Component**
  - File: `components/common/selectors/user-selector.tsx`
  - Status: ❌ MISSING
  - Purpose: For selecting users in procurement forms

**2. User Display Components**
- **User Avatar Component**
  - File: `components/common/display/user-avatar.tsx`
  - Status: ❌ MISSING
  - Purpose: Display user info in procurement records

### Phase 4: Frontend Integration & UI/UX (Priority: HIGH)

#### Required Components

**1. Shared Components**
- **Status Badge Component**
  - File: `components/procurement/common/status-badge.tsx`
  - Status: ❌ MISSING
  
- **Currency Display Component**
  - File: `components/procurement/common/currency-display.tsx`
  - Status: ❌ MISSING
  
- **Date Range Picker Component**
  - File: `components/procurement/common/date-range-picker.tsx`
  - Status: ❌ MISSING

**2. Integration Components**
- **Supplier Selector Component**
  - File: `components/procurement/selectors/supplier-selector.tsx`
  - Status: ❌ MISSING
  - Purpose: For selecting suppliers in inventory and purchase order forms
  
- **Purchase Order Selector Component**
  - File: `components/procurement/selectors/purchase-order-selector.tsx`
  - Status: ❌ MISSING
  - Purpose: For linking inventory items to purchase orders

## 📊 Implementation Priority Matrix

### Critical Path (Must Complete First)
1. **Supplier Module Frontend** - Required for inventory supplier selection
2. **PurchaseOrder Module Frontend** - Required for inventory purchase tracking
3. **Integration Components** - Required for module communication

### Secondary Path (Complete After Critical)
1. **User Integration Components** - Enhances user experience
2. **Shared UI Components** - Improves consistency
3. **Advanced Features** - Reporting, analytics, etc.

## 🔧 Technical Requirements

### Database Schema Updates
- [ ] Ensure Supplier model matches service interface
- [ ] Add proper indexes for performance
- [ ] Add validation rules for data integrity

### API Enhancements
- [ ] Ensure all CRUD operations are available
- [ ] Add proper error handling
- [ ] Add input validation
- [ ] Add pagination and filtering

### Frontend Architecture
- [ ] Implement consistent component patterns
- [ ] Add proper state management
- [ ] Add error handling and loading states
- [ ] Add responsive design

### Integration Points
- [ ] Inventory ↔ Supplier integration
- [ ] Inventory ↔ PurchaseOrder integration
- [ ] User authentication and authorization
- [ ] Category selection and filtering

## 🚀 Implementation Timeline

### Week 1: Supplier Module
- [ ] Day 1-2: Enhance Supplier model and API
- [ ] Day 3-4: Create Supplier frontend components
- [ ] Day 5: Integrate Supplier with store and test

### Week 2: PurchaseOrder Module
- [ ] Day 1-2: Enhance PurchaseOrder API if needed
- [ ] Day 3-4: Create PurchaseOrder frontend components
- [ ] Day 5: Integrate PurchaseOrder with store and test

### Week 3: Integration & Testing
- [ ] Day 1-2: Create integration components
- [ ] Day 3-4: Implement cross-module communication
- [ ] Day 5: End-to-end testing and bug fixes

## 📝 Success Criteria

### Functional Requirements
- [ ] Complete CRUD operations for Suppliers
- [ ] Complete CRUD operations for PurchaseOrders
- [ ] Inventory can select and link to Suppliers
- [ ] Inventory can track PurchaseOrder history
- [ ] User authentication works across all modules

### Technical Requirements
- [ ] All API endpoints return proper responses
- [ ] Frontend components are responsive and accessible
- [ ] State management works correctly
- [ ] Error handling is comprehensive
- [ ] Performance is acceptable (< 2s load times)

### User Experience Requirements
- [ ] Intuitive navigation between modules
- [ ] Consistent UI/UX patterns
- [ ] Clear feedback for user actions
- [ ] Proper loading and error states
- [ ] Mobile-friendly interface

## 🔍 Testing Strategy

### Unit Testing
- [ ] Test all service methods
- [ ] Test all API endpoints
- [ ] Test all React components

### Integration Testing
- [ ] Test module interactions
- [ ] Test database operations
- [ ] Test API integrations

### End-to-End Testing
- [ ] Test complete user workflows
- [ ] Test cross-module functionality
- [ ] Test error scenarios

## 📚 Documentation Requirements

### Technical Documentation
- [ ] API documentation for all endpoints
- [ ] Component documentation with examples
- [ ] Database schema documentation
- [ ] Integration guide for developers

### User Documentation
- [ ] User guide for Supplier management
- [ ] User guide for PurchaseOrder management
- [ ] User guide for Inventory integration
- [ ] Troubleshooting guide

---

## 🔧 Detailed Implementation Specifications

### Supplier Module Implementation Details

#### 1. Enhanced Supplier Model Schema
```typescript
// models/procurement/Supplier.ts - Enhanced Version
export interface ISupplier extends Document {
  supplierId: string;           // Auto-generated unique ID
  name: string;                 // Company/Individual name
  contactPerson?: string;       // Primary contact person
  email?: string;              // Primary email
  phone?: string;              // Primary phone
  address?: string;            // Street address
  city?: string;               // City
  country?: string;            // Country (default: Malawi)
  website?: string;            // Website URL
  category: string[];          // Array of categories
  taxId?: string;              // Tax identification number
  paymentTerms?: string;       // Payment terms
  bankName?: string;           // Bank name
  accountNumber?: string;      // Bank account number
  status: 'active' | 'inactive' | 'blacklisted';
  rating?: number;             // 1-5 rating
  notes?: string;              // Additional notes
  attachments?: string[];      // File attachments
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 2. Supplier API Endpoints
```
GET    /api/procurement/suppliers           - List suppliers with pagination
POST   /api/procurement/suppliers           - Create new supplier
GET    /api/procurement/suppliers/[id]      - Get supplier details
PUT    /api/procurement/suppliers/[id]      - Update supplier
DELETE /api/procurement/suppliers/[id]      - Delete supplier
GET    /api/procurement/suppliers/search    - Search suppliers
GET    /api/procurement/suppliers/active    - Get active suppliers only
```

#### 3. Supplier Frontend Components Structure
```
components/procurement/suppliers/
├── supplier-management.tsx          # Main management component
├── supplier-list.tsx               # List view with pagination
├── supplier-form.tsx               # Create/Edit form
├── supplier-modal.tsx              # Modal wrapper
├── supplier-card.tsx               # Individual supplier card
├── supplier-details.tsx            # Detailed view
├── supplier-selector.tsx           # Dropdown selector
└── supplier-filters.tsx            # Filter component
```

### PurchaseOrder Module Implementation Details

#### 1. PurchaseOrder API Endpoints
```
GET    /api/procurement/purchase-orders           - List purchase orders
POST   /api/procurement/purchase-orders           - Create new purchase order
GET    /api/procurement/purchase-orders/[id]      - Get purchase order details
PUT    /api/procurement/purchase-orders/[id]      - Update purchase order
DELETE /api/procurement/purchase-orders/[id]      - Delete purchase order
PATCH  /api/procurement/purchase-orders/[id]/status - Update status
GET    /api/procurement/purchase-orders/supplier/[id] - Get by supplier
```

#### 2. PurchaseOrder Frontend Components Structure
```
components/procurement/purchase-orders/
├── purchase-order-management.tsx    # Main management component
├── purchase-order-list.tsx         # List view with pagination
├── purchase-order-form.tsx         # Create/Edit form
├── purchase-order-modal.tsx        # Modal wrapper
├── purchase-order-card.tsx         # Individual order card
├── purchase-order-details.tsx      # Detailed view
├── purchase-order-items.tsx        # Items management
└── purchase-order-status.tsx       # Status management
```

### Integration Components Specifications

#### 1. Supplier Selector Component
```typescript
// components/procurement/selectors/supplier-selector.tsx
interface SupplierSelectorProps {
  value?: string;
  onChange: (supplierId: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  categories?: string[];        // Filter by categories
  status?: 'active' | 'all';   // Filter by status
}
```

#### 2. Purchase Order Selector Component
```typescript
// components/procurement/selectors/purchase-order-selector.tsx
interface PurchaseOrderSelectorProps {
  value?: string;
  onChange: (orderId: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  supplierId?: string;         // Filter by supplier
  status?: string[];           // Filter by status
}
```

## 📋 Implementation Checklist

### Phase 1: Supplier Module (Week 1)

#### Backend Tasks
- [ ] **Day 1**: Enhance Supplier model to match service interface
  - [ ] Update schema with all required fields
  - [ ] Add proper validation rules
  - [ ] Add database indexes
  - [ ] Add instance methods

- [ ] **Day 1**: Verify Supplier API routes
  - [ ] Test all CRUD endpoints
  - [ ] Ensure proper error handling
  - [ ] Add input validation
  - [ ] Test authentication

#### Frontend Tasks
- [ ] **Day 2**: Create Supplier management page
  - [ ] Set up page structure
  - [ ] Add navigation integration
  - [ ] Add basic layout

- [ ] **Day 2-3**: Create Supplier components
  - [ ] SupplierList component with pagination
  - [ ] SupplierForm component with validation
  - [ ] SupplierModal component
  - [ ] SupplierCard component
  - [ ] SupplierDetails component

- [ ] **Day 4**: Create Supplier selector components
  - [ ] SupplierSelector for forms
  - [ ] SupplierFilters for search
  - [ ] Integration with existing forms

- [ ] **Day 5**: Store integration and testing
  - [ ] Add supplier actions to store
  - [ ] Add supplier state management
  - [ ] Test all CRUD operations
  - [ ] Test integration with inventory

### Phase 2: PurchaseOrder Module (Week 2)

#### Backend Tasks
- [ ] **Day 1**: Verify PurchaseOrder API routes
  - [ ] Test all CRUD endpoints
  - [ ] Test status update endpoints
  - [ ] Test supplier filtering
  - [ ] Ensure proper error handling

#### Frontend Tasks
- [ ] **Day 1**: Create PurchaseOrder management page
  - [ ] Set up page structure
  - [ ] Add navigation integration
  - [ ] Add basic layout

- [ ] **Day 2-3**: Create PurchaseOrder components
  - [ ] PurchaseOrderList component
  - [ ] PurchaseOrderForm component
  - [ ] PurchaseOrderModal component
  - [ ] PurchaseOrderDetails component
  - [ ] PurchaseOrderItems component

- [ ] **Day 4**: Create PurchaseOrder integration
  - [ ] PurchaseOrderSelector component
  - [ ] Status management component
  - [ ] Integration with inventory

- [ ] **Day 5**: Store integration and testing
  - [ ] Add purchase order actions to store
  - [ ] Add purchase order state management
  - [ ] Test all CRUD operations
  - [ ] Test integration with inventory and suppliers

### Phase 3: Integration & Testing (Week 3)

#### Integration Tasks
- [ ] **Day 1**: Cross-module integration
  - [ ] Inventory → Supplier integration
  - [ ] Inventory → PurchaseOrder integration
  - [ ] Supplier → PurchaseOrder integration

- [ ] **Day 2**: Shared components
  - [ ] StatusBadge component
  - [ ] CurrencyDisplay component
  - [ ] DateRangePicker component
  - [ ] UserAvatar component

#### Testing Tasks
- [ ] **Day 3**: Unit testing
  - [ ] Test all service methods
  - [ ] Test all API endpoints
  - [ ] Test all React components

- [ ] **Day 4**: Integration testing
  - [ ] Test module interactions
  - [ ] Test database operations
  - [ ] Test API integrations

- [ ] **Day 5**: End-to-end testing
  - [ ] Test complete user workflows
  - [ ] Test error scenarios
  - [ ] Performance testing
  - [ ] Bug fixes and optimization

## 🎯 Success Metrics

### Functional Metrics
- [ ] 100% CRUD operations working for Suppliers
- [ ] 100% CRUD operations working for PurchaseOrders
- [ ] Inventory can successfully link to Suppliers
- [ ] Inventory can track PurchaseOrder history
- [ ] All integrations working seamlessly

### Performance Metrics
- [ ] Page load times < 2 seconds
- [ ] API response times < 500ms
- [ ] Search operations < 1 second
- [ ] Form submissions < 1 second

### User Experience Metrics
- [ ] Intuitive navigation (user testing)
- [ ] Consistent UI patterns
- [ ] Mobile responsiveness
- [ ] Accessibility compliance (WCAG 2.1)

## Next Steps

1. **Review and Approve** this implementation guide
2. **Set up development environment** for the modules
3. **Begin Phase 1** with Supplier module completion
4. **Regular progress reviews** and adjustments as needed

This implementation will bring the Procurement Inventory module and its dependencies to production-ready status, enabling full CRUD operations and seamless integration across the procurement package.

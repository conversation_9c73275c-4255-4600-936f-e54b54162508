# Vercel TypeScript Strict Mode Error List

This document outlines common TypeScript strict mode issues that can cause deployment failures on Vercel. Use this as a reference to identify and fix potential problems before deployment.

## Next.js App Router Type Issues

### 1. Dynamic Route Params Type Errors

#### Server Components
- **Error Pattern**: Using `{ params: { id: string } }` instead of `{ params: Promise<{ id: string }> }`
- **Example Error**:
  ```
  Type 'PageProps' does not satisfy the constraint 'import("/app/.next/types/app/path/page").PageProps'.
  Types of property 'params' are incompatible.
  Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
  ```
- **Fix**:
  ```typescript
  // Incorrect
  interface PageProps {
    params: { id: string };
  }
  
  // Correct
  interface PageProps {
    params: Promise<{ id: string }>;
  }
  
  export default async function Page({ params }: PageProps) {
    const resolvedParams = await params;
    // Use resolvedParams.id instead of params.id
  }
  ```

#### Client Components
- **Error <PERSON>tern**: Using interface with params in client components
- **Fix**: Use the `useParams()` hook instead
  ```typescript
  // Incorrect
  "use client"
  interface PageProps {
    params: { id: string };
  }
  export default function Page({ params }: PageProps) {
    const { id } = params;
  }
  
  // Correct
  "use client"
  import { useParams } from 'next/navigation';
  export default function Page() {
    const params = useParams();
    const id = params.id as string;
  }
  ```

### 2. SearchParams Type Errors

#### Server Components
- **Error Pattern**: Using `{ searchParams: { query?: string } }` instead of `{ searchParams: Promise<{ query?: string }> }`
- **Fix**:
  ```typescript
  // Incorrect
  interface PageProps {
    searchParams: { query?: string };
  }
  
  // Correct
  interface PageProps {
    searchParams: Promise<{ query?: string }>;
  }
  
  export default async function Page({ searchParams }: PageProps) {
    const resolvedSearchParams = await searchParams;
    // Use resolvedSearchParams.query instead of searchParams.query
  }
  ```

#### Client Components
- **Fix**: Use the `useSearchParams()` hook instead
  ```typescript
  "use client"
  import { useSearchParams } from 'next/navigation';
  export default function Page() {
    const searchParams = useSearchParams();
    const query = searchParams.get('query');
  }
  ```

### 3. Metadata Export in Client Components

- **Error Pattern**: Exporting metadata from a client component
- **Example Error**:
  ```
  Error: You are attempting to export "metadata" from a component marked with "use client", which is disallowed.
  ```
- **Fix**: Move metadata to a separate file (metadata.ts) in the same directory

## General TypeScript Strict Mode Issues

### 1. Null and Undefined Checks

- **Error Pattern**: Not checking for null or undefined values
- **Example Error**: `Object is possibly 'null' or 'undefined'`
- **Fix**: Use optional chaining, nullish coalescing, or explicit checks
  ```typescript
  // Incorrect
  const name = user.name;
  
  // Correct
  const name = user?.name;
  // or
  const name = user ? user.name : undefined;
  ```

### 2. Implicit Any Types

- **Error Pattern**: Variables without explicit types defaulting to `any`
- **Example Error**: `Parameter 'x' implicitly has an 'any' type`
- **Fix**: Add explicit type annotations
  ```typescript
  // Incorrect
  function process(data) {
    return data.value;
  }
  
  // Correct
  function process(data: { value: string }): string {
    return data.value;
  }
  ```

### 3. Function Return Types

- **Error Pattern**: Functions without explicit return types
- **Fix**: Add explicit return type annotations
  ```typescript
  // Incorrect
  function getData() {
    return fetch('/api/data').then(res => res.json());
  }
  
  // Correct
  function getData(): Promise<Data> {
    return fetch('/api/data').then(res => res.json());
  }
  ```

### 4. Index Signatures

- **Error Pattern**: Accessing object properties without index signatures
- **Example Error**: `Element implicitly has an 'any' type because expression of type 'string' can't be used to index type`
- **Fix**: Add index signatures or use Record type
  ```typescript
  // Incorrect
  const obj = {};
  obj[dynamicKey] = value;
  
  // Correct
  const obj: Record<string, any> = {};
  obj[dynamicKey] = value;
  
  // Or
  const obj: { [key: string]: any } = {};
  obj[dynamicKey] = value;
  ```

### 5. Type Assertions

- **Error Pattern**: Unsafe type assertions
- **Fix**: Use proper type guards or conditional checks
  ```typescript
  // Incorrect
  const value = someValue as string;
  
  // Correct
  if (typeof someValue === 'string') {
    const value = someValue;
  }
  ```

### 6. Strict Property Initialization

- **Error Pattern**: Class properties not initialized in constructor
- **Example Error**: `Property 'x' has no initializer and is not definitely assigned in the constructor`
- **Fix**: Initialize in declaration, constructor, or use definite assignment assertion
  ```typescript
  // Incorrect
  class Example {
    private value: string;
  }
  
  // Correct
  class Example {
    private value: string = '';
    // or
    private value!: string; // definite assignment assertion
    // or initialize in constructor
  }
  ```

### 7. Strict Function Types

- **Error Pattern**: Incompatible function parameter types in inheritance
- **Fix**: Ensure proper contravariance/covariance in function parameters
  ```typescript
  // Incorrect
  interface Parent {
    process(value: string | number): void;
  }
  interface Child extends Parent {
    process(value: string): void; // Error: not compatible
  }
  
  // Correct
  interface Parent {
    process(value: string): void;
  }
  interface Child extends Parent {
    process(value: string | number): void; // OK: more permissive
  }
  ```

### 8. Non-Nullable Types

- **Error Pattern**: Using potentially null/undefined values without checks
- **Fix**: Add null checks or use the non-null assertion operator when appropriate
  ```typescript
  // Incorrect
  function process(value: string | null) {
    return value.toUpperCase(); // Error
  }
  
  // Correct
  function process(value: string | null) {
    return value ? value.toUpperCase() : '';
    // or when you're certain it's not null
    return value!.toUpperCase();
  }
  ```

## MongoDB/Mongoose Specific Issues

### 1. Document Types

- **Error Pattern**: Incorrect typing of Mongoose documents
- **Fix**: Use proper Document typing with generics
  ```typescript
  // Incorrect
  const user = await User.findById(id);
  const name = user.name; // Error: possibly undefined
  
  // Correct
  const user = await User.findById(id);
  if (!user) throw new Error('User not found');
  const name = user.name;
  ```

### 2. Mongoose Schema Types

- **Error Pattern**: Incorrect type definitions in Mongoose schemas
- **Fix**: Use proper Schema type definitions
  ```typescript
  // Correct approach
  interface IUser extends Document {
    name: string;
    email: string;
    age?: number;
  }
  
  const UserSchema = new Schema<IUser>({
    name: { type: String, required: true },
    email: { type: String, required: true },
    age: { type: Number }
  });
  ```

## API and Data Fetching Issues

### 1. Response Type Assertions

- **Error Pattern**: Not properly typing API responses
- **Fix**: Define and use proper response types
  ```typescript
  // Incorrect
  const data = await fetch('/api/data').then(res => res.json());
  
  // Correct
  interface ApiResponse {
    items: Item[];
    total: number;
  }
  
  const data = await fetch('/api/data').then(res => res.json()) as ApiResponse;
  ```

### 2. Error Handling

- **Error Pattern**: Not handling potential errors in async operations
- **Fix**: Use try/catch blocks and proper error typing
  ```typescript
  // Incorrect
  const data = await fetchData();
  
  // Correct
  try {
    const data = await fetchData();
  } catch (error) {
    if (error instanceof Error) {
      console.error(error.message);
    } else {
      console.error(String(error));
    }
  }
  ```

## Recommended TypeScript Configuration

For Vercel deployments with strict TypeScript checking, ensure your `tsconfig.json` includes:

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

## Automated Fixes

Consider using the provided `fix-nextjs-types.js` script to automatically fix common Next.js App Router type issues before deployment.

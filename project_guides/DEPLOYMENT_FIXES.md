# Deployment Fixes

This document outlines the fixes applied to resolve deployment errors in the HR Management System.

## Issues Fixed

### 1. Metadata Export in Client Components

Fixed the issue where `metadata` was being exported from components marked with `"use client"` directive, which is not allowed in Next.js.

Files fixed:
- `app/(dashboard)/assessment/[id]/take/page.tsx`
- `app/(dashboard)/assessment/page.tsx`
- `app/(dashboard)/assessment/submission/[id]/results/page.tsx`
- `app/(dashboard)/calendar/analytics/page.tsx`
- `app/(dashboard)/calendar/page.tsx`
- `app/(dashboard)/notifications/page.tsx`
- `app/(dashboard)/onboarding/analytics/page.tsx`
- `app/(dashboard)/onboarding/page.tsx`
- `app/(dashboard)/onboarding/[id]/page.tsx`

Solution: Removed the metadata exports from client components.

### 2. Missing UI Components

Created missing UI components that were causing build failures:

1. **DataTable Component**
   - Created `components/ui/data-table.tsx` to provide a reusable data table component
   - Used in asset management pages

2. **TimePicker Component**
   - Created `components/ui/time-picker.tsx` for time selection functionality
   - Used in interview scheduling

3. **EnhancedDatePicker Path Fix**
   - Fixed import paths for the EnhancedDatePicker component in:
     - `components/asset/maintenance/asset-maintenance-form.tsx`
     - `components/asset/movement/asset-movement-form.tsx`

### 3. Syntax Error in OfferNegotiationForm

Fixed a syntax error in `components/recruitment/offer/OfferNegotiationForm.tsx` where a single quote in a string was causing parsing issues.

### 4. Missing Dependencies

Installed missing npm packages:
- `qrcode` - For generating QR codes
- `jsbarcode` - For generating barcodes
- `xmldom` - Required by the barcode generation functionality

### 5. Event Handler in Client Component Props

Fixed an issue where an event handler was being passed to a Client Component prop in `app/(dashboard)/project/resources/capacity/page.tsx`.

Solution: Created a client component wrapper (`ResourceCapacityPlannerWrapper`) to handle the event.

### 6. Dynamic Server Usage Errors

Added `export const dynamic = 'force-dynamic'` to pages that use server-side features like cookies:
- `app/(dashboard)/recruitment/pipeline/page.tsx`
- `app/(dashboard)/recruitment/candidates/new/page.tsx`
- `app/(dashboard)/recruitment/candidates/page.tsx`
- `app/(dashboard)/recruitment/reports/page.tsx`
- `app/(dashboard)/recruitment/jobs/page.tsx`
- `app/(dashboard)/recruitment/page.tsx`
- `app/(dashboard)/recruitment/applications/page.tsx`
- `app/(dashboard)/recruitment/jobs/[id]/page.tsx`
- `app/(dashboard)/recruitment/jobs/[id]/applications/page.tsx`
- `app/(dashboard)/recruitment/candidates/[id]/applications/page.tsx`

### 7. Mongoose Schema Index Warnings

Created a script (`scripts/fix-mongoose-indexes.js`) to fix duplicate schema indexes in Mongoose models.

The script removes redundant index definitions when a field is defined with both `index: true` in the schema field and also explicitly indexed using `schema.index()`.

## Running the Index Fix Script

To fix the duplicate Mongoose schema indexes:

```bash
node scripts/fix-mongoose-indexes.js
```

This script will scan model files and remove redundant index definitions.

## Additional Notes

- The warnings about duplicate schema indexes don't prevent the application from running but should be fixed for cleaner logs and better performance.
- The `dynamic = 'force-dynamic'` setting ensures that pages with server-side features are properly rendered.

# Bulk Import Troubleshooting Guide

## 🎉 **SUCCESS STORY: TCM Roles Import Completed!**

**Latest Achievement (December 2024):**
- ✅ **31 TCM Roles Successfully Imported** - All organizational roles from TCM 1 to TCM 12
- ✅ **Zero Import Errors** - Perfect import using corrected template
- ✅ **Department Validation Passed** - All role-department mappings validated
- ✅ **System Integration Ready** - Role-payroll integration framework active

**Files Used Successfully:**
- ✅ `format_excel/tcm_roles_import_corrected.xlsx` - **WORKED PERFECTLY**
- ✅ Proper headers: `Name`, `Code`, `Description`, `Department`
- ✅ All 31 roles with proper TCM codes and department associations

---

## Common Issues and Solutions

### Issue: "Missing required field: name" Error

**Problem**: The Excel file headers don't match the expected format.

**Root Cause**: The bulk import system expects specific column headers with exact capitalization.

**Expected Headers for Roles Import**:
- `Name` (not `name`)
- `Code` (not `code`)
- `Description` (not `description`)
- `Department` (not `department`)
- `Is Active` (optional)

**Solution**:
1. Use the corrected template: `format_excel/tcm_roles_import_corrected.xlsx`
2. Or download the official template from the roles page
3. Ensure headers match exactly (case-sensitive)

### Column Mapping System

The system uses a column mapping to handle different header formats:

```typescript
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Name': 'name',
  'Code': 'code',
  'Description': 'description',
  'Department': 'department',
  'Is Active': 'isActive'
}
```

### Required vs Optional Fields

**Required Fields**:
- `Name`: Role name (e.g., "Registrar")
- `Code`: Role code (e.g., "TCM 1")

**Optional Fields**:
- `Description`: Role description
- `Department`: Department name (must match existing department)
- `Is Active`: TRUE/FALSE (defaults to TRUE)

### Department Validation

**Important**: Department names must match exactly with existing departments in the system.

**Valid Department Names for TCM**:
- Management
- Registration and Licencing Directorate
- Compliance Directorate
- Ethics and Professional Standards Section
- Finance Division
- Human Resource and Administration Division
- Education and Training Section
- Investigations Section
- Planning Research and Evaluation Unit
- Public Relations and Customer Experience Unit

**Special Value**:
- Use `all_departments` for roles that apply to all departments

### File Format Requirements

**Supported Formats**:
- Excel (.xlsx, .xls)
- CSV (.csv)

**File Size Limit**: 5MB maximum

**Encoding**: UTF-8 recommended for CSV files

### Common Error Messages and Solutions

#### "Missing required field: name"
- **Cause**: Header is lowercase `name` instead of `Name`
- **Solution**: Change header to `Name` with capital N

#### "Missing required field: code"
- **Cause**: Header is lowercase `code` instead of `Code`
- **Solution**: Change header to `Code` with capital C

#### "Department 'X' not found"
- **Cause**: Department name doesn't match existing department
- **Solution**: Check department names against the reference list

#### "Role with code X already exists"
- **Cause**: Duplicate role codes in the system
- **Solution**: Use unique codes or update existing role

#### "File is empty"
- **Cause**: No data rows in the file
- **Solution**: Add data rows below the header

### Best Practices

1. **Use Official Template**: Always start with the template downloaded from the system
2. **Validate Departments**: Check department names before import
3. **Unique Codes**: Ensure role codes are unique
4. **Test Small Batches**: Import a few roles first to test
5. **Backup Data**: Export existing roles before bulk import

### TCM-Specific Guidelines

**Role Code Format**: Use "TCM X" format where X is the level number
- TCM 1 (Executive)
- TCM 2 (Director)
- TCM 3 (Manager)
- TCM 4 (Senior Officer)
- TCM 5 (Officer)
- TCM 7 (Assistant)
- TCM 9 (Support)
- TCM 10-12 (Specialized Support)

**Department Mapping**: Ensure department names match the TCM organizational structure

### Files Available

1. **Corrected Template**: `format_excel/tcm_roles_import_corrected.xlsx`
   - Proper headers (Name, Code, Description, Department)
   - All 31 TCM roles pre-filled
   - Department reference sheet included

2. **CSV Template**: `format_excel/tcm_roles_import_template.csv`
   - Updated with correct headers
   - Can be opened in Excel or text editor

3. **Generation Script**: `scripts/generate-tcm-roles-excel.js`
   - Creates Excel files with proper format
   - Can be modified for custom role sets

### Debugging Steps

1. **Check Headers**: Verify exact capitalization
2. **Validate Data**: Ensure all required fields have values
3. **Test Departments**: Verify department names exist
4. **Check File Format**: Ensure proper Excel/CSV format
5. **Review Logs**: Check server logs for detailed error messages

### Contact Support

If issues persist:
1. Check the browser console for detailed error messages
2. Verify user permissions for role management
3. Ensure departments are created before importing roles
4. Contact system administrator with specific error details

## Quick Fix for Current Issue

**Immediate Solution**: Use the corrected Excel file:
`format_excel/tcm_roles_import_corrected.xlsx`

This file has:
- ✅ Correct headers (Name, Code, Description, Department)
- ✅ All 31 TCM roles with proper codes
- ✅ Valid department names
- ✅ Proper Excel format

The import should work successfully with this file.

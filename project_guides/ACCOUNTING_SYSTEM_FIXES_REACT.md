# Accounting System React Reference Fixes

## Issue

The accounting system was experiencing a runtime error:

```
ReferenceError: React is not defined
    at eval (webpack-internal:///(app-pages-browser)/./components/accounting/budget/budget-planning.tsx:639:166)
    at Array.map (<anonymous>)
    at BudgetPlanning (webpack-internal:///(app-pages-browser)/./components/accounting/budget/budget-planning.tsx:639:74)
    at BudgetPlanningPage (webpack-internal:///(app-pages-browser)/./components/accounting/budget/budget-planning-page.tsx:175:92)
    at BudgetPlanningRoute (rsc://React/Server/webpack-internal:///(rsc)/./app/(dashboard)/accounting/budget/planning/page.tsx?5:16:87)
```

This error occurred because the components were using React fragments (`<React.Fragment>`) without explicitly importing React.

## Changes Made

1. **Updated Budget Planning Component**
   - Added explicit React import to `components/accounting/budget/budget-planning.tsx`
   - Changed from `import { useState } from 'react';` to `import React, { useState } from 'react';`

2. **Updated Income Overview Component**
   - Added explicit React import to `components/accounting/income/income-overview.tsx`
   - Changed from `import { useState } from 'react';` to `import React, { useState } from 'react';`

## Why This Fix Works

In modern React with JSX, when using React fragments (`<React.Fragment>` or the shorthand `<>...</>`), the React object needs to be in scope. While newer versions of React don't require explicit imports for JSX transformation, the React object itself still needs to be imported when directly referenced.

The error occurred specifically in the budget planning component where we were using `<React.Fragment>` in the table rows:

```jsx
{budgetCategories.map(category => (
  <React.Fragment key={category.id}>
    {/* ... */}
  </React.Fragment>
))}
```

By adding the explicit React import, we ensure that the React object is available when referenced.

## Best Practices

1. **Always Import React When Using React.* APIs**
   - When using `React.Fragment`, `React.createElement`, or any other React namespace APIs, always import React explicitly
   - Example: `import React from 'react';` or `import React, { useState } from 'react';`

2. **Consider Using Fragment Shorthand**
   - Instead of `<React.Fragment>`, consider using the shorthand syntax `<>...</>` when keys are not needed
   - This can help avoid the need for explicit React imports in some cases

3. **Be Consistent Across Components**
   - For maintainability, it's best to be consistent with imports across all components
   - We've updated all accounting components to include the React import for consistency

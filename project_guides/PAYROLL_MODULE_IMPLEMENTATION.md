# Payroll Module Implementation

## Overview

This document tracks the implementation of the Payroll Module within the TCM Enterprise Business Suite. The Payroll Module provides comprehensive functionality for managing employee compensation, including salary structures, tax calculations, payroll processing, and payslip generation.

## Implemented Features

### Data Models

- **TaxBracket**: Defines tax brackets for PAYE calculations based on Malawi regulations
- **SalaryStructure**: Defines salary structures with components like basic salary, allowances, and deductions
- **PayrollRecord**: Stores individual payroll records for employees
- **PayrollRun**: Manages payroll processing for a specific period
- **PaySlip**: Stores generated payslips with detailed breakdowns
- **Allowance**: Defines allowance types that can be assigned to employees
- **Deduction**: Defines deduction types that can be applied to employee salaries
- **EmployeeSalary**: Tracks employee salary information with allowances and deductions
- **SalaryRevision**: Tracks changes to employee salaries

### Services

- **TaxService**: Calculates taxes based on Malawi regulations
- **SalaryCalculationService**: Calculates employee salaries with allowances, deductions, and taxes
- **PayrollService**: Manages payroll operations like creating and processing payroll runs
- **PayslipGenerationService**: Generates and manages payslips

### API Endpoints

#### Tax Brackets
- `GET /api/payroll/tax-brackets`: Get tax brackets
- `POST /api/payroll/tax-brackets`: Create a new tax bracket
- `GET /api/payroll/tax-brackets/[id]`: Get a tax bracket by ID
- `PATCH /api/payroll/tax-brackets/[id]`: Update a tax bracket
- `DELETE /api/payroll/tax-brackets/[id]`: Delete a tax bracket
- `POST /api/payroll/tax-brackets/default`: Create default tax brackets for Malawi

#### Salary Structures
- `GET /api/payroll/salary-structures`: Get salary structures
- `POST /api/payroll/salary-structures`: Create a new salary structure
- `GET /api/payroll/salary-structures/[id]`: Get a salary structure by ID
- `PATCH /api/payroll/salary-structures/[id]`: Update a salary structure
- `DELETE /api/payroll/salary-structures/[id]`: Delete a salary structure

#### Payroll Runs
- `GET /api/payroll/runs`: Get payroll runs
- `POST /api/payroll/runs`: Create a new payroll run
- `GET /api/payroll/runs/[id]`: Get a payroll run by ID
- `PATCH /api/payroll/runs/[id]`: Perform operations on a payroll run (process, approve, pay, cancel)
- `GET /api/payroll/runs/[id]/records`: Get payroll records for a payroll run
- `GET /api/payroll/runs/[id]/payslips`: Get payslips for a payroll run
- `POST /api/payroll/runs/[id]/payslips`: Generate payslips for a payroll run

#### Employee Salaries
- `GET /api/payroll/employee-salaries`: Get employee salaries
- `POST /api/payroll/employee-salaries`: Create a new employee salary
- `GET /api/payroll/employee-salaries/[id]`: Get an employee salary by ID
- `PATCH /api/payroll/employee-salaries/[id]`: Update an employee salary

#### Employee Payroll Information
- `GET /api/payroll/employees/[id]/salary-history`: Get salary history for an employee
- `GET /api/payroll/employees/[id]/payslips`: Get payslips for an employee

#### Payslips
- `GET /api/payroll/payslips/[id]`: Get a payslip by ID
- `PATCH /api/payroll/payslips/[id]`: Update payslip status (mark as sent, viewed, downloaded)
- `GET /api/payroll/payslips/[id]/download`: Download a payslip

#### Allowances
- `GET /api/payroll/allowances`: Get allowances
- `POST /api/payroll/allowances`: Create a new allowance
- `GET /api/payroll/allowances/[id]`: Get an allowance by ID
- `PATCH /api/payroll/allowances/[id]`: Update an allowance
- `DELETE /api/payroll/allowances/[id]`: Delete an allowance

#### Deductions
- `GET /api/payroll/deductions`: Get deductions
- `POST /api/payroll/deductions`: Create a new deduction
- `GET /api/payroll/deductions/[id]`: Get a deduction by ID
- `PATCH /api/payroll/deductions/[id]`: Update a deduction
- `DELETE /api/payroll/deductions/[id]`: Delete a deduction

#### Salary Calculation
- `POST /api/payroll/calculate-salary`: Calculate salary for an employee

## Pending Features

### Frontend Components
- **PayrollPage**: Main page for payroll management
- **SalaryStructureManager**: Component for managing salary structures
- **TaxBracketManager**: Component for managing tax brackets
- **DeductionManager**: Component for managing deductions
- **AllowanceManager**: Component for managing allowances
- **PayrollRunWizard**: Guided workflow for processing payroll
- **PayslipViewer**: Component for viewing and printing payslips
- **PayrollReportGenerator**: Component for generating payroll reports
- **PayrollAccountingIntegration**: Component for integrating with accounting module

### Additional Services
- **PayrollReportingService**: Generate comprehensive payroll reports
- **PayrollAccountingService**: Integration with accounting module

### PDF Generation
- Implement PDF generation for payslips

## Next Steps

1. Implement the frontend components for the Payroll Module
2. Implement PDF generation for payslips
3. Implement payroll reporting functionality
4. Implement integration with the Accounting Module
5. Implement employee self-service for viewing payslips

## Technical Implementation Details

### Malawi PAYE Tax Calculation

The tax calculation is based on Malawi's PAYE tax brackets:
- 0 - 150,000 MWK: 0% (Tax-free threshold)
- 150,000 - 500,000 MWK: 25%
- 500,000 - 2,550,000 MWK: 30%
- Above 2,550,000 MWK: 35%

### Payroll Processing Workflow

1. Create a payroll run for a specific period
2. Process the payroll run to calculate salaries for all employees
3. Review and approve the payroll run
4. Generate payslips for the payroll run
5. Mark the payroll run as paid
6. Distribute payslips to employees

### Salary Revision Process

When an employee's salary is revised:
1. The current salary record is deactivated
2. A new salary record is created with the updated salary
3. A salary revision record is created to track the change
4. The revision includes details like percentage change, amount change, and reason

### Integration Points

- **Employee Module**: Employee data is used for payroll processing
- **Accounting Module**: Payroll data is used for accounting entries
- **Banking Module**: Payment information is used for salary disbursement
- **Tax System**: Tax calculations comply with Malawi regulations

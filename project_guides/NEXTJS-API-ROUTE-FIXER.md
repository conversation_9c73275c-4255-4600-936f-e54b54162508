# Next.js API Route Fixer

## Overview

The Next.js API Route Fixer is a powerful utility script that automatically detects and fixes common issues in Next.js API routes. It's particularly useful for large projects with many API routes that need to be updated to work with the latest Next.js features or when migrating between authentication systems.

## Key Features

- **Authentication System Compatibility**: Converts between different auth systems (next-auth to custom auth)
- **TypeScript Type Fixes**: Corrects parameter types and mismatches
- **Next.js App Router Compatibility**: Updates route handler parameter types
- **Bulk Import Fixes**: Adds proper typing for external libraries
- **Response Handling**: Fixes binary data responses

## Installation

The script is already installed in your project. You can find it at:

```
scripts/nextjs-api-route-fixer.js
```

## Usage

### Command Line

You can run the script directly from the command line:

```bash
# Fix all API routes
node scripts/nextjs-api-route-fixer.js

# Fix only specific module routes
node scripts/nextjs-api-route-fixer.js app/api/users
```

### Using the Shell Script

For convenience, a shell script wrapper is provided:

```bash
# Make it executable (if not already)
chmod +x scripts/fix-api-routes.sh

# Run it
./scripts/fix-api-routes.sh [optional-directory]
```

### NPM Script

The script is also integrated into your package.json, so you can run:

```bash
# Fix all API routes
npm run fix-api-routes

# The script will also run automatically before each build
npm run build
```

## What Issues Does It Fix?

### Authentication System Compatibility

- Replaces `getServerSession` imports with `getCurrentUser`
- Removes unnecessary `authOptions` imports
- Converts `session` objects to `user` objects
- Updates permission checks to use the correct user object

### TypeScript Type Errors

- Fixes parameter mismatches between function declarations and usage (req/request)
- Corrects dynamic route parameter types for Next.js App Router
- Adds proper typing for external libraries like XLSX

### Next.js App Router Compatibility

- Updates route handler parameter types to use `Promise<{ params: { id: string } }>` for dynamic routes
- Fixes response handling for binary data using ReadableStream

## Safety Features

- Creates backups of all modified files in the `backups/api-route-fixes` directory
- Only modifies files that match specific patterns to avoid unintended changes
- Provides detailed logs of all changes made

## Troubleshooting

If you encounter any issues:

1. Check the console output for specific error messages
2. Restore from backups if needed (located in `backups/api-route-fixes`)
3. Run the script with a more specific directory to narrow down problematic files

## Extending the Script

If you need to add more patterns to fix, you can edit the `replacements` array in the script. Each replacement has:

- `find`: A regular expression to match the pattern to replace
- `replace`: The replacement string or function
- `description`: A description of what the replacement does

## License

MIT

# TypeScript Error Fixing Guide

This guide provides an overview of the TypeScript error fixing tools available in this project and how to use them to resolve common TypeScript errors.

## Available Scripts

We've created several specialized scripts to fix different types of TypeScript errors:

| Script | Description |
|--------|-------------|
| `npm run fix-all` | Runs all TypeScript error fixing scripts in sequence |
| `npm run fix-form-types` | Fixes form control type issues and filter includes errors |
| `npm run fix-unknown-types` | Fixes issues with unknown types |
| `npm run fix-catch-blocks` | Fixes catch blocks with unknown type errors |
| `npm run fix-badge-variants` | Fixes Badge component variant issues |
| `npm run fix-components` | Fixes general component issues |
| `npm run fix-object-types` | Fixes object type issues |
| `npm run fix-db-tools` | Fixes the specific error in database-tools page |
| `npm run ts-check` | Scans for TypeScript errors without fixing them |
| `npm run ts-check-fix` | Scans for TypeScript errors and fixes them |

## Common TypeScript Errors and How to Fix Them

### 1. Unknown Type Errors

**Problem:**
```typescript
catch (err: unknown) {
  setError(err.message || 'An error occurred') // Error: 'err' is of type 'unknown'
}
```

**Fix:**
```typescript
catch (err: unknown) {
  const errorMessage = err instanceof Error ? err.message : 'An error occurred';
  setError(errorMessage);
}
```

**Script to Run:** `npm run fix-catch-blocks`

### 2. Form Control Type Issues

**Problem:**
```tsx
<FormField
  control={form.control} // Type error: Control type mismatch
  name="fieldName"
  render={...}
/>
```

**Fix:**
```tsx
<FormField
  control={form.control as any} // Added type assertion
  name="fieldName"
  render={...}
/>
```

**Script to Run:** `npm run fix-form-types`

### 3. Filter Includes Issues

**Problem:**
```tsx
checked={table
  .getColumn("type")
  ?.getFilterValue()
  ?.includes(type)} // Error: Property 'includes' does not exist on type '{}'
```

**Fix:**
```tsx
checked={(table
  .getColumn("type")
  ?.getFilterValue() as string[] || [])
  .includes(type)}
```

**Script to Run:** `npm run fix-form-types`

### 4. Badge Variant Issues

**Problem:**
```tsx
<Badge variant="warning"> // Error: Type '"warning"' is not assignable to type...
  Pending
</Badge>
```

**Fix:**
```tsx
<Badge variant="secondary"> // Changed to supported variant
  Pending
</Badge>
```

**Script to Run:** `npm run fix-badge-variants`

## How to Fix All TypeScript Errors at Once

To fix all TypeScript errors in the codebase, run:

```bash
npm run fix-all
```

This script:
1. Creates backups of all TypeScript files
2. Runs all the specialized fixing scripts in sequence
3. Checks for any remaining TypeScript errors

## Manual Fixes for Remaining Errors

If you still have TypeScript errors after running the automated scripts, here are some common manual fixes:

### 1. Add Type Annotations to Variables

```typescript
// Before
const data = fetchData();

// After
const data: MyDataType = fetchData();
```

### 2. Create Interfaces for Complex Objects

```typescript
// Before
function processUser(user: any) { ... }

// After
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

function processUser(user: User) { ... }
```

### 3. Use Type Guards for Union Types

```typescript
// Before
function process(item: string | number) {
  return item.toUpperCase(); // Error: Property 'toUpperCase' does not exist on type 'number'
}

// After
function process(item: string | number) {
  if (typeof item === 'string') {
    return item.toUpperCase();
  }
  return item.toString();
}
```

### 4. Use Optional Chaining for Nullable Properties

```typescript
// Before
const name = user.profile.name; // Error if user.profile is null or undefined

// After
const name = user?.profile?.name;
```

## Checking for Remaining Errors

After applying fixes, check for any remaining TypeScript errors by running:

```bash
npx tsc --noEmit
```

This will compile your TypeScript code without generating output files and show any remaining errors.

## Best Practices for Preventing TypeScript Errors

1. **Always add type annotations** to function parameters and return types
2. **Avoid using `any` type** whenever possible
3. **Use type guards** when working with union types
4. **Add proper error handling** in catch blocks
5. **Use optional chaining** (`?.`) for potentially null/undefined values
6. **Create interfaces** for complex objects
7. **Use enums** for fixed sets of values
8. **Enable strict TypeScript checks** in tsconfig.json

## Troubleshooting

If you encounter issues with the fixing scripts:

1. Check the error messages in the console
2. Look for backup files (`.bak` extension) to restore if needed
3. Try running the specific script for your error type
4. If all else fails, fix the error manually following the patterns in this guide

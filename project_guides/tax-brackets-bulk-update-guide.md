# Tax Brackets Bulk Update API Guide

## Overview
The Tax Brackets Bulk Update API (`POST /api/payroll/tax-brackets/bulk-update`) allows authorized users to perform bulk operations on tax brackets including activation, deactivation, rate updates, expiry management, and deletion.

## Endpoint
```
POST /api/payroll/tax-brackets/bulk-update
```

## Authentication & Authorization
**Required Roles:**
- Super Admin
- System Admin  
- Finance Director
- Finance Manager

## Request Body Structure

```typescript
{
  action: 'activate' | 'deactivate' | 'update_rates' | 'set_expiry' | 'delete';
  filters: {
    country?: string;
    currency?: string;
    effectiveDate?: string;
    isActive?: boolean;
    ids?: string[];
  };
  updates?: {
    isActive?: boolean;
    expiryDate?: string;
    rateMultiplier?: number;
    rateAdjustment?: number;
  };
}
```

## Supported Actions

### 1. Activate Tax Brackets
```json
{
  "action": "activate",
  "filters": {
    "country": "Malawi",
    "isActive": false
  }
}
```

### 2. Deactivate Tax Brackets
```json
{
  "action": "deactivate",
  "filters": {
    "country": "Malawi",
    "effectiveDate": "2023-01-01"
  }
}
```

### 3. Update Tax Rates
```json
{
  "action": "update_rates",
  "filters": {
    "country": "Malawi",
    "currency": "MWK"
  },
  "updates": {
    "rateMultiplier": 1.05
  }
}
```

### 4. Set Expiry Date
```json
{
  "action": "set_expiry",
  "filters": {
    "ids": ["bracket_id_1", "bracket_id_2"]
  },
  "updates": {
    "expiryDate": "2024-12-31"
  }
}
```

### 5. Delete Tax Brackets
```json
{
  "action": "delete",
  "filters": {
    "isActive": false,
    "country": "Malawi"
  }
}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Successfully activated 5 tax bracket(s)",
  "data": {
    "action": "activate",
    "affectedCount": 5,
    "query": {
      "country": "Malawi",
      "isActive": false
    }
  }
}
```

### Error Response
```json
{
  "error": "Cannot delete 3 active tax brackets. Please deactivate them first.",
  "details": "Validation error message"
}
```

## Safety Features

1. **Active Bracket Protection**: Cannot delete active tax brackets
2. **Rate Validation**: Tax rates are clamped between 0% and 100%
3. **Audit Trail**: All operations are logged with user ID
4. **Role-based Access**: Only authorized roles can perform bulk updates
5. **Validation**: Comprehensive input validation and error handling

## Usage Examples

### Annual Tax Rate Increase (5%)
```bash
curl -X POST /api/payroll/tax-brackets/bulk-update \
  -H "Content-Type: application/json" \
  -d '{
    "action": "update_rates",
    "filters": {
      "country": "Malawi",
      "isActive": true
    },
    "updates": {
      "rateMultiplier": 1.05
    }
  }'
```

### Deactivate Old Tax Brackets
```bash
curl -X POST /api/payroll/tax-brackets/bulk-update \
  -H "Content-Type: application/json" \
  -d '{
    "action": "deactivate",
    "filters": {
      "effectiveDate": "2023-01-01"
    }
  }'
```

### Clean Up Inactive Brackets
```bash
curl -X POST /api/payroll/tax-brackets/bulk-update \
  -H "Content-Type: application/json" \
  -d '{
    "action": "delete",
    "filters": {
      "isActive": false,
      "country": "Malawi"
    }
  }'
```

## Integration Notes

- Use this API for annual tax bracket updates
- Combine with bulk import for complete tax system updates
- Always test with small batches before large operations
- Monitor audit logs for compliance tracking
- Consider backup before bulk deletion operations

## Error Handling

The API provides detailed error messages for:
- Invalid action types
- Missing required fields
- Permission violations
- Validation failures
- Database operation errors

All errors include specific details to help with troubleshooting.

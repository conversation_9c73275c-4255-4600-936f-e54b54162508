# Budget Management Technical Guide

## Architecture Overview

The Budget Management module is built using a modern, component-based architecture that follows the principles of separation of concerns and modularity. The module integrates with the TCM Enterprise Business Suite's core systems while maintaining its own domain-specific logic.

### Technology Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **State Management**: Zustand
- **Form Handling**: React Hook Form, Zod
- **UI Components**: Shadcn UI
- **Backend**: Node.js, Next.js API Routes
- **Database**: MongoDB
- **Authentication**: Custom auth system

## Data Models

### Budget Model

```typescript
interface IBudget extends Document {
  name: string;
  description?: string;
  fiscalYear: string;
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'active' | 'closed';
  totalIncome: number;
  totalExpense: number;
  createdBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Budget Category Model

```typescript
interface IBudgetCategory extends Document {
  name: string;
  description?: string;
  type: 'income' | 'expense';
  budget: mongoose.Types.ObjectId;
  total: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Budget Subcategory Model

```typescript
interface IBudgetSubcategory extends Document {
  name: string;
  description?: string;
  parentCategory: mongoose.Types.ObjectId;
  budget: mongoose.Types.ObjectId;
  total: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Budget Item Model

```typescript
interface IBudgetItem extends Document {
  name: string;
  description?: string;
  quantity: number;
  frequency: number;
  unitCost: number;
  amount: number;
  parentCategory?: mongoose.Types.ObjectId;
  parentSubcategory?: mongoose.Types.ObjectId;
  budget: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

## Component Structure

### Frontend Components

1. **BudgetPlanning**: Main component for budget creation and editing
   - Location: `components/accounting/budget/budget-planning.tsx`
   - Purpose: Provides the UI for creating and editing budgets

2. **BudgetImportExport**: Component for importing and exporting budget data
   - Location: `components/accounting/budget/budget-import-export.tsx`
   - Purpose: Handles file uploads, downloads, and data processing

3. **BudgetApproval**: Component for the budget approval workflow
   - Location: `components/accounting/budget/budget-approval.tsx`
   - Purpose: Manages the approval process for budgets

### State Management

The Budget Management module uses Zustand for state management:

```typescript
// Budget Store
interface BudgetState {
  budgets: IBudget[];
  isLoading: boolean;
  error: string | null;
  currentBudget: IBudget | null;
  currentBudgetCategories: IBudgetCategory[];
  lastFetched: number;
  
  // Actions
  fetchBudgets: (fiscalYear?: string, status?: string) => Promise<void>;
  fetchBudgetById: (id: string) => Promise<void>;
  createBudget: (data: Partial<IBudget>) => Promise<IBudget>;
  updateBudgetStatus: (id: string, action: string, rejectionReason?: string) => Promise<IBudget>;
  
  // Category actions
  fetchCategories: (budgetId: string, type?: 'income' | 'expense') => Promise<IBudgetCategory[]>;
  createCategory: (data: Partial<IBudgetCategory>) => Promise<IBudgetCategory>;
  
  // Subcategory actions
  fetchSubcategories: (budgetId: string, categoryId?: string) => Promise<IBudgetSubcategory[]>;
  createSubcategory: (data: Partial<IBudgetSubcategory>) => Promise<IBudgetSubcategory>;
  
  // Item actions
  fetchItems: (budgetId: string, categoryId?: string, subcategoryId?: string) => Promise<IBudgetItem[]>;
  createItem: (data: Partial<IBudgetItem>) => Promise<IBudgetItem>;
  
  // Bulk import
  importItems: (budgetId: string, items: Array<{...}>) => Promise<IBudget>;
  
  // Reset state
  resetState: () => void;
}
```

## API Routes

### Budget Routes

- **GET /api/accounting/budget**: Get all budgets or a specific budget by ID
- **POST /api/accounting/budget**: Create a new budget
- **PUT /api/accounting/budget/:id**: Update an existing budget
- **DELETE /api/accounting/budget/:id**: Delete a budget

### Category Routes

- **GET /api/accounting/budget/category**: Get categories for a budget
- **POST /api/accounting/budget/category**: Create a new category
- **PUT /api/accounting/budget/category/:id**: Update a category
- **DELETE /api/accounting/budget/category/:id**: Delete a category

### Subcategory Routes

- **GET /api/accounting/budget/subcategory**: Get subcategories
- **POST /api/accounting/budget/subcategory**: Create a new subcategory
- **PUT /api/accounting/budget/subcategory/:id**: Update a subcategory
- **DELETE /api/accounting/budget/subcategory/:id**: Delete a subcategory

### Item Routes

- **GET /api/accounting/budget/item**: Get budget items
- **POST /api/accounting/budget/item**: Create a new budget item
- **PUT /api/accounting/budget/item/:id**: Update a budget item
- **DELETE /api/accounting/budget/item/:id**: Delete a budget item

### Import/Export Routes

- **POST /api/accounting/budget/import**: Import budget data
- **GET /api/accounting/budget/export**: Export budget data

### Action Routes

- **POST /api/accounting/budget/:id/actions**: Perform actions on a budget (submit, approve, reject, etc.)

## Services

### Budget Service

The `BudgetService` class provides the core functionality for budget operations:

```typescript
class BudgetService extends CrudService<IBudget> {
  // Budget CRUD operations
  async createBudget(data: Partial<IBudget>): Promise<IBudget>;
  async getBudgets(filter?: Partial<IBudget>, options?: {...}): Promise<{...}>;
  async getBudgetWithDetails(id: string): Promise<IBudget | null>;
  
  // Budget workflow
  async updateBudgetStatus(id: string, status: IBudget['status'], userId: string, rejectionReason?: string): Promise<IBudget | null>;
  
  // Budget calculations
  async calculateBudgetTotals(budgetId: string): Promise<IBudget | null>;
  
  // Category operations
  async createBudgetCategory(data: Partial<IBudgetCategory>): Promise<IBudgetCategory>;
  
  // Subcategory operations
  async createBudgetSubcategory(data: Partial<IBudgetSubcategory>): Promise<IBudgetSubcategory>;
  
  // Item operations
  async createBudgetItem(data: Partial<IBudgetItem>): Promise<IBudgetItem>;
  
  // Import/Export
  async importBudgetItems(budgetId: string, items: Array<{...}>): Promise<IBudget | null>;
}
```

## Integration Points

### Accounting Module Integration

The Budget Management module integrates with the Accounting module to:
- Track budget allocations
- Compare actual expenses with budgeted amounts
- Generate financial reports

### User Authentication Integration

The module integrates with the authentication system to:
- Restrict access based on user roles
- Track who created and approved budgets
- Implement the approval workflow

## Error Handling

The Budget Management module implements comprehensive error handling:

1. **API Error Handling**: All API routes include try/catch blocks and return appropriate HTTP status codes
2. **Frontend Error Handling**: The UI displays error messages and provides recovery options
3. **Validation**: Input validation using Zod schemas prevents invalid data

## Performance Considerations

1. **Data Caching**: The Zustand store implements caching with a configurable duration
2. **Pagination**: Large datasets are paginated to improve performance
3. **Optimized Queries**: Database queries are optimized with proper indexing

## Security Considerations

1. **Authorization**: Role-based access control for budget operations
2. **Input Validation**: All user inputs are validated before processing
3. **Data Sanitization**: Data is sanitized to prevent injection attacks

## Testing

1. **Unit Tests**: Test individual components and functions
2. **Integration Tests**: Test the interaction between components
3. **End-to-End Tests**: Test the complete user flow

## Deployment

The Budget Management module is deployed as part of the TCM Enterprise Business Suite:

1. **Development**: Local development environment
2. **Staging**: Testing environment for QA
3. **Production**: Live environment for end users

## Troubleshooting

### Common Technical Issues

1. **MongoDB Connection Issues**:
   - Check database connection string
   - Verify network connectivity
   - Check MongoDB service status

2. **API Errors**:
   - Check server logs for detailed error messages
   - Verify request payload format
   - Check authentication tokens

3. **UI Rendering Issues**:
   - Clear browser cache
   - Check for JavaScript console errors
   - Verify component props and state

# Payroll Module Documentation

## Table of Contents
1. [Overview](#overview)
2. [Payroll Dashboard](#payroll-dashboard)
3. [Payroll Runs](#payroll-runs)
4. [Payslips](#payslips)
5. [Salary Structures](#salary-structures)
6. [Employee Salaries](#employee-salaries)
7. [Technical Implementation](#technical-implementation)
8. [API Reference](#api-reference)
9. [Data Models](#data-models)
10. [Services](#services)

## Overview

The Payroll module handles all aspects of employee compensation, from defining salary structures to processing payroll and generating payslips. It is designed to work seamlessly with the Employee module to ensure accurate and efficient payroll processing.

### Key Features
- Comprehensive payroll run management
- Payslip generation and distribution
- Salary structure management
- Employee salary records
- Tax calculation
- Reporting and exports
- Approval workflows

## Payroll Dashboard

The Payroll Dashboard provides quick access to all payroll features and functions. From here, you can navigate to different components of the payroll system.

### Components
- **Payroll Runs**: Create and manage payroll runs for specific periods
- **Payslips**: Generate, view, and distribute employee payslips
- **Salary Structures**: Define and manage salary templates
- **Employee Salaries**: Manage individual employee salary details
- **Allowances & Deductions**: Manage compensation components
- **Quick Actions**: Create new payroll runs and access other functions

### Navigation
- Access the Payroll Dashboard from the main sidebar: **Dashboard > Payroll**
- Each card on the dashboard links to a specific payroll function
- The dashboard provides a summary of recent payroll activities

## Payroll Runs

Payroll Runs are the core of the payroll process. They represent a specific pay period (e.g., monthly payroll) and include all the calculations and processing for employee salaries.

### Creating a Payroll Run
1. Navigate to **Dashboard > Payroll > Runs**
2. Click the **New Payroll Run** button
3. Complete the payroll run wizard:
   - **Setup**: Define pay period, name, description, and select departments
   - **Review**: Review the payroll run details
   - **Complete**: Finalize the payroll run creation
4. After creation, the payroll run will be in **Draft** status

### Payroll Run Wizard
1. **Setup**:
   - Name the payroll run (defaults to "Payroll for [Month] [Year]")
   - Add a description
   - Select month and year for the pay period
   - Define start and end dates
   - Choose departments to include
   - All fields are validated to ensure proper payroll setup

2. **Review**:
   - Review the payroll run details
   - Verify the pay period, departments, and other settings
   - Make any necessary adjustments before creation

3. **Complete**:
   - Confirmation of successful payroll run creation
   - Summary of the payroll run with key details
   - Options for next steps (process payroll, view details)

### Processing a Payroll Run
1. From the Payroll Runs page, click on a payroll run in **Draft** status
2. Click the **Process Payroll** button
3. The system will calculate salaries for all employees included in the run
4. Once processing is complete, the status will change to **Completed**
5. The system will display summary statistics

### Approving a Payroll Run
1. From the Payroll Runs page, click on a payroll run in **Completed** status
2. Review the payroll details and summary
3. Click the **Approve** button
4. Once approved, the status will change to **Approved**
5. Approved payroll runs can be used to generate payslips

### Payroll Run Details
The payroll run details page provides comprehensive information about a payroll run:
- Summary information (name, period, status, totals)
- Employee records with salary details
- Action buttons for processing, approving, generating payslips, and exporting
- Tabs for different views (employee records, department summary)

### Exporting Payroll Data
1. From the Payroll Run details page, click **Export to Excel** or **Export to PDF**
2. The system will generate the export file and download it to your device
3. Excel exports include summary and employee details
4. PDF exports include formatted reports with payroll information

## Payslips

Payslips provide detailed payment information for individual employees. They can be generated, viewed, printed, and distributed to employees.

### Generating Payslips
1. From a processed payroll run, click the **Generate Payslips** button
2. Alternatively, go to **Dashboard > Payroll > Payslips**
3. Select a payroll run from the dropdown
4. Click **Generate Payslips** in the Payslip Actions card
5. The system will create payslips for all employees in the payroll run

### Viewing and Managing Payslips
1. Go to **Dashboard > Payroll > Payslips**
2. Select a payroll run from the dropdown
3. Browse the list of payslips or use the search function to find specific employees
4. Click on the actions menu (three dots) for a payslip to:
   - **View**: Open the payslip viewer
   - **Download**: Download the payslip as PDF
   - **Email**: Send the payslip to the employee's email
   - **Print**: Open the payslip in print view

### Bulk Payslip Actions
1. Go to **Dashboard > Payroll > Payslips**
2. Select a payroll run from the dropdown
3. Use the buttons in the Payslip Actions card to:
   - **Generate Payslips**: Create payslips for all employees
   - **Email All**: Send payslips to all employees
   - **Download All**: Download all payslips as a ZIP file
   - **Print All**: Open all payslips in print view

### Payslip Viewer
The payslip viewer provides a detailed view of an employee's payslip:
- Employee information (name, ID, department, position)
- Pay period details
- Earnings breakdown (basic salary, allowances)
- Deductions breakdown (tax, pension, etc.)
- Summary with gross salary, total deductions, and net salary
- Action buttons for printing, downloading, and emailing

### Payslip Content
Each payslip includes:
- Company header with logo
- Employee details section
- Pay period information
- Earnings table with all components
- Deductions table with all components
- Summary section with totals
- Payment information
- Footer with notes and disclaimers

## Salary Structures

Salary Structures define templates for employee compensation packages. They include basic salary, allowances, and deductions that can be applied to multiple employees.

### Creating Salary Structures
1. Go to **Dashboard > Payroll > Salary Structures**
2. Click **New Salary Structure**
3. Fill in the structure details:
   - **Name**: A descriptive name for the structure
   - **Description**: Additional details about the structure
   - **Basic Salary**: The base amount for the structure
   - **Allowances**: Additional payments (housing, transport, etc.)
   - **Deductions**: Amounts to be deducted (pension, insurance, etc.)
4. Specify which roles or departments the structure applies to
5. Click **Save** to create the structure

### Bulk Import of Salary Structures
1. Go to **Dashboard > Payroll > Salary Structures**
2. Click **Bulk Upload**
3. Download the template file
4. Fill in the template with structure information
5. Upload the completed file
6. Review and confirm the import

## Employee Salaries

Employee Salaries link individual employees to salary structures and define their specific compensation details.

### Creating Employee Salary Records
1. Go to **Dashboard > Payroll > Employee Salaries**
2. Click **New Salary**
3. Select an employee from the dropdown
4. Choose a salary structure or define custom salary details
5. Set the effective date for the salary
6. Add any employee-specific allowances or deductions
7. Enter bank account and payment method details
8. Click **Save** to create the salary record

### Salary Revisions
1. Go to **Dashboard > Payroll > Employee Salaries**
2. Find the employee whose salary needs revision
3. Click **Revise Salary**
4. Enter the new salary details
5. Set the effective date for the revision
6. Provide a reason for the revision
7. Click **Save** to create the revision

## Technical Implementation

### System Architecture
The Payroll module is built on a modern web application architecture with the following components:
- **Frontend**: Next.js with React components and Tailwind CSS
- **Backend**: Next.js API routes with MongoDB database
- **Authentication**: Custom authentication system
- **State Management**: Zustand stores

### Key Components
- **PayrollRunWizard**: Guides users through creating a new payroll run
- **PayrollRunsTable**: Displays all payroll runs with filtering and sorting
- **PayrollRunDetails**: Shows detailed information about a specific payroll run
- **PayslipsTable**: Displays all payslips for a selected payroll run
- **PayslipViewer**: Renders a payslip with all details
- **BulkPayslipActions**: Provides actions for managing multiple payslips

## API Reference

### Payroll Runs
- `GET /api/payroll/runs` - Get all payroll runs
- `GET /api/payroll/runs/[id]` - Get a specific payroll run
- `POST /api/payroll/runs` - Create a new payroll run
- `PATCH /api/payroll/runs/[id]` - Update a payroll run
- `POST /api/payroll/runs/[id]/process` - Process a payroll run
- `POST /api/payroll/runs/[id]/approve` - Approve a payroll run
- `POST /api/payroll/runs/[id]/cancel` - Cancel a payroll run
- `GET /api/payroll/runs/[id]/export` - Export payroll run data

### Payslips
- `GET /api/payroll/payslips` - Get all payslips
- `GET /api/payroll/payslips/[id]` - Get a specific payslip
- `POST /api/payroll/runs/[id]/payslips` - Generate payslips for a payroll run
- `GET /api/payroll/payslips/[id]/download` - Download a payslip
- `POST /api/payroll/payslips/[id]/email` - Email a payslip
- `GET /api/payroll/payslips/[id]/print` - Print a payslip

### Salary Calculation
- `POST /api/payroll/calculate` - Calculate salary for an employee
- `GET /api/payroll/tax-brackets` - Get tax brackets

## Data Models

### PayrollRun Model
```javascript
{
  name: String,               // Name of the payroll run
  description: String,        // Description of the payroll run
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  status: String,             // draft, processing, completed, approved, paid, cancelled
  totalEmployees: Number,     // Total number of employees in the run
  processedEmployees: Number, // Number of employees processed
  totalGrossSalary: Number,   // Total gross salary amount
  totalDeductions: Number,    // Total deductions amount
  totalTax: Number,           // Total tax amount
  totalNetSalary: Number,     // Total net salary amount
  currency: String,           // Currency code (default: MWK)
  notes: String,              // Additional notes
  departments: [ObjectId],    // References to Department model
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId,        // Reference to User model
  approvedBy: ObjectId,       // Reference to User model
  approvedAt: Date,           // Approval timestamp
  processedAt: Date,          // Processing timestamp
  paidAt: Date                // Payment timestamp
}
```

### PayrollRecord Model
```javascript
{
  payrollRunId: ObjectId,     // Reference to PayrollRun model
  employeeId: ObjectId,       // Reference to Employee model
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  salaryStructureId: ObjectId, // Reference to SalaryStructure model
  currency: String,           // Currency code (default: MWK)
  components: {
    basic: Number,            // Basic salary amount
    allowances: [{            // Array of allowances
      name: String,           // Allowance name
      amount: Number,         // Allowance amount
      isTaxable: Boolean      // Whether the allowance is taxable
    }],
    deductions: [{            // Array of deductions
      name: String,           // Deduction name
      amount: Number,         // Deduction amount
      isStatutory: Boolean    // Whether the deduction is statutory
    }]
  },
  grossSalary: Number,        // Gross salary amount
  totalDeductions: Number,    // Total deductions amount
  totalTax: Number,           // Total tax amount
  netSalary: Number,          // Net salary amount
  status: String,             // draft, processed, paid
  bankAccount: String,        // Bank account number
  paymentMethod: String,      // Payment method
  paymentReference: String,   // Payment reference
  paymentDate: Date,          // Payment date
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId         // Reference to User model
}
```

### Payslip Model
```javascript
{
  payrollRunId: ObjectId,     // Reference to PayrollRun model
  payrollRecordId: ObjectId,  // Reference to PayrollRecord model
  employeeId: ObjectId,       // Reference to Employee model
  employeeName: String,       // Employee full name
  employeeNumber: String,     // Employee number
  department: String,         // Department name
  position: String,           // Position name
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  paymentDetails: {
    grossSalary: Number,      // Gross salary amount
    totalDeductions: Number,  // Total deductions amount
    totalTax: Number,         // Total tax amount
    netSalary: Number,        // Net salary amount
    components: Object,       // Salary components
    currency: String          // Currency code (default: MWK)
  },
  status: String,             // generated, emailed, downloaded
  emailHistory: [{            // Array of email history
    sentAt: Date,             // Email sent timestamp
    sentBy: ObjectId,         // Reference to User model
    sentTo: String,           // Email address
    status: String            // Email status
  }],
  downloadHistory: [{         // Array of download history
    downloadedAt: Date,       // Download timestamp
    downloadedBy: ObjectId    // Reference to User model
  }],
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId         // Reference to User model
}
```

## Services

### PayrollService
Handles payroll operations including creating and processing payroll runs.
- `createPayrollRun(data)` - Create a new payroll run
- `getPayrollRun(id)` - Get a specific payroll run
- `updatePayrollRun(id, data)` - Update a payroll run
- `processPayrollRun(id, options)` - Process a payroll run
- `approvePayrollRun(id, userId)` - Approve a payroll run
- `cancelPayrollRun(id, reason)` - Cancel a payroll run

### PayslipService
Handles payslip generation and management.
- `generatePayslips(payrollRunId)` - Generate payslips for a payroll run
- `getPayslip(id)` - Get a specific payslip
- `getPayslips(filters)` - Get payslips with filtering
- `downloadPayslip(id)` - Generate PDF for a payslip
- `emailPayslip(id)` - Email a payslip to an employee

### TaxCalculator
Handles tax calculations based on Malawi tax brackets.
- `calculateTax(grossSalary)` - Calculate tax based on gross salary
- `calculateNetSalary(grossSalary, deductions)` - Calculate net salary
- `calculateTaxBreakdown(grossSalary)` - Get detailed tax breakdown

# Employee Module Implementation Guide

## Overview

This guide provides detailed instructions for implementing the Employee module in the TCM Enterprise Business Suite. It covers the core functionality of the Employee module and its integration with other modules to create a comprehensive HR management system.

## Module Structure

The Employee module is organized into several interconnected components:

1. **Core Employee Management**: Basic employee data and operations
2. **Salary Management**: Employee compensation and salary structures
3. **Leave Management**: Employee time-off and absence tracking
4. **Attendance Management**: Employee attendance tracking
5. **Loan Management**: Employee loan processing and repayment tracking
6. **Task Management**: Employee task assignment and tracking
7. **Document Management**: Employee document storage and retrieval

## Implementation Sequence

The implementation should follow this sequence to ensure proper integration:

1. **Core Employee Management**: This is the foundation for all other components
2. **Salary Management**: Required for financial aspects of employee management
3. **Leave Management**: Critical for attendance tracking and payroll calculations
4. **Attendance Management**: Builds on leave management and affects payroll
5. **Loan Management**: Depends on payroll for salary deductions
6. **Task Management**: Depends on attendance and leave for availability
7. **Document Management**: Can be implemented in parallel with other components

## Core Employee Management

### Models

#### Employee Model

```typescript
// models/employee/Employee.ts
import mongoose, { Schema, Document } from 'mongoose';
import { IDepartment } from '../department/Department';
import { IPosition } from '../position/Position';

export interface IEmployee extends Document {
  employeeId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: Date;
  gender: 'male' | 'female' | 'other';
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
  nationalId?: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  department: IDepartment['_id'];
  position: IPosition['_id'];
  reportingManager?: IEmployee['_id'];
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern';
  employmentStatus: 'active' | 'on-leave' | 'terminated' | 'suspended';
  joinDate: Date;
  terminationDate?: Date;
  bankDetails: {
    bankName: string;
    accountNumber: string;
    branchCode: string;
  };
  emergencyContacts: [{
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  }];
  profileImage?: string;
  isActive: boolean;
  createdBy: string;
  updatedBy: string;
}

const EmployeeSchema = new Schema({
  employeeId: {
    type: String,
    required: true,
    unique: true
  },
  firstName: {
    type: String,
    required: true
  },
  middleName: {
    type: String
  },
  lastName: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  phone: {
    type: String,
    required: true
  },
  dateOfBirth: {
    type: Date,
    required: true
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: true
  },
  maritalStatus: {
    type: String,
    enum: ['single', 'married', 'divorced', 'widowed'],
    required: true
  },
  nationalId: {
    type: String
  },
  address: {
    street: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    postalCode: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true
    }
  },
  department: {
    type: Schema.Types.ObjectId,
    ref: 'Department',
    required: true
  },
  position: {
    type: Schema.Types.ObjectId,
    ref: 'Position',
    required: true
  },
  reportingManager: {
    type: Schema.Types.ObjectId,
    ref: 'Employee'
  },
  employmentType: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'intern'],
    required: true
  },
  employmentStatus: {
    type: String,
    enum: ['active', 'on-leave', 'terminated', 'suspended'],
    default: 'active'
  },
  joinDate: {
    type: Date,
    required: true
  },
  terminationDate: {
    type: Date
  },
  bankDetails: {
    bankName: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true
    },
    branchCode: {
      type: String,
      required: true
    }
  },
  emergencyContacts: [{
    name: {
      type: String,
      required: true
    },
    relationship: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    email: {
      type: String
    }
  }],
  profileImage: {
    type: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

// Create indexes for frequently queried fields
EmployeeSchema.index({ employeeId: 1 });
EmployeeSchema.index({ email: 1 });
EmployeeSchema.index({ department: 1 });
EmployeeSchema.index({ position: 1 });
EmployeeSchema.index({ employmentStatus: 1 });

export default mongoose.models.Employee || 
  mongoose.model<IEmployee>('Employee', EmployeeSchema);
```

### Services

#### EmployeeService

```typescript
// services/employee/EmployeeService.ts
import Employee, { IEmployee } from '@/models/employee/Employee';
import { FilterQuery } from 'mongoose';

export default class EmployeeService {
  async createEmployee(employeeData: Partial<IEmployee>, userId: string): Promise<IEmployee> {
    // Generate employee ID if not provided
    if (!employeeData.employeeId) {
      employeeData.employeeId = await this.generateEmployeeId();
    }
    
    // Set created by and updated by
    employeeData.createdBy = userId;
    employeeData.updatedBy = userId;
    
    // Create new employee
    const employee = new Employee(employeeData);
    return await employee.save();
  }
  
  async getEmployeeById(id: string): Promise<IEmployee | null> {
    return await Employee.findById(id)
      .populate('department')
      .populate('position')
      .populate('reportingManager')
      .exec();
  }
  
  async getEmployeeByEmployeeId(employeeId: string): Promise<IEmployee | null> {
    return await Employee.findOne({ employeeId })
      .populate('department')
      .populate('position')
      .populate('reportingManager')
      .exec();
  }
  
  async updateEmployee(id: string, employeeData: Partial<IEmployee>, userId: string): Promise<IEmployee | null> {
    // Set updated by
    employeeData.updatedBy = userId;
    
    // Update employee
    return await Employee.findByIdAndUpdate(
      id,
      { $set: employeeData },
      { new: true }
    )
    .populate('department')
    .populate('position')
    .populate('reportingManager')
    .exec();
  }
  
  async deleteEmployee(id: string): Promise<IEmployee | null> {
    return await Employee.findByIdAndDelete(id).exec();
  }
  
  async getEmployees(
    filters: FilterQuery<IEmployee> = {},
    page: number = 1,
    limit: number = 10,
    sortField: string = 'createdAt',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<{
    employees: IEmployee[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    // Calculate skip value for pagination
    const skip = (page - 1) * limit;
    
    // Create sort object
    const sort: Record<string, 1 | -1> = {};
    sort[sortField] = sortOrder === 'asc' ? 1 : -1;
    
    // Get employees with pagination
    const employees = await Employee.find(filters)
      .populate('department')
      .populate('position')
      .populate('reportingManager')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .exec();
    
    // Get total count
    const total = await Employee.countDocuments(filters);
    
    // Calculate total pages
    const totalPages = Math.ceil(total / limit);
    
    return {
      employees,
      total,
      page,
      limit,
      totalPages
    };
  }
  
  private async generateEmployeeId(): Promise<string> {
    // Get current year
    const currentYear = new Date().getFullYear().toString().substr(-2);
    
    // Get count of employees created this year
    const count = await Employee.countDocuments({
      employeeId: { $regex: `^EMP${currentYear}` }
    });
    
    // Generate employee ID (EMP + YY + 4-digit sequential number)
    return `EMP${currentYear}${(count + 1).toString().padStart(4, '0')}`;
  }
}
```

### API Routes

#### Employee API

```typescript
// app/api/employees/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/backend/auth/session';
import EmployeeService from '@/services/employee/EmployeeService';
import { hasPermission } from '@/lib/backend/auth/permissions';

const employeeService = new EmployeeService();

export async function GET(req: NextRequest) {
  try {
    // Get session
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check permissions
    if (!hasPermission(session, 'employee.view')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    // Build filters
    const filters: any = {};
    
    // Add search filter if provided
    const search = searchParams.get('search');
    if (search) {
      filters.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Add department filter if provided
    const department = searchParams.get('department');
    if (department) {
      filters.department = department;
    }
    
    // Add position filter if provided
    const position = searchParams.get('position');
    if (position) {
      filters.position = position;
    }
    
    // Add employment status filter if provided
    const employmentStatus = searchParams.get('employmentStatus');
    if (employmentStatus) {
      filters.employmentStatus = employmentStatus;
    }
    
    // Get employees
    const result = await employeeService.getEmployees(
      filters,
      page,
      limit,
      sortField as string,
      sortOrder as 'asc' | 'desc'
    );
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error fetching employees:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch employees' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get session
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check permissions
    if (!hasPermission(session, 'employee.create')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get request body
    const body = await req.json();
    
    // Create employee
    const employee = await employeeService.createEmployee(body, session.user.id);
    
    return NextResponse.json(employee, { status: 201 });
  } catch (error: any) {
    console.error('Error creating employee:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create employee' },
      { status: 500 }
    );
  }
}
```

## Integration with Other Modules

For detailed implementation guidelines on integrating the Employee module with other modules, refer to the [Employee Module Integration Guide](./EMPLOYEE_MODULE_INTEGRATION_GUIDE.md).

## Testing

### Unit Testing

```typescript
// __tests__/services/employee/EmployeeService.test.ts
import { describe, expect, it, beforeEach, jest } from '@jest/globals';
import EmployeeService from '@/services/employee/EmployeeService';
import Employee from '@/models/employee/Employee';

// Mock the Employee model
jest.mock('@/models/employee/Employee', () => ({
  findById: jest.fn(),
  findOne: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
  find: jest.fn(),
  countDocuments: jest.fn(),
  prototype: {
    save: jest.fn(),
  },
}));

describe('EmployeeService', () => {
  let employeeService: EmployeeService;
  
  beforeEach(() => {
    employeeService = new EmployeeService();
    jest.clearAllMocks();
  });
  
  describe('getEmployeeById', () => {
    it('should return an employee when found', async () => {
      // Arrange
      const mockEmployee = {
        _id: '123',
        firstName: 'John',
        lastName: 'Doe',
      };
      
      const mockPopulate = jest.fn().mockReturnThis();
      const mockExec = jest.fn().mockResolvedValue(mockEmployee);
      
      (Employee.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate,
        exec: mockExec,
      });
      
      // Act
      const result = await employeeService.getEmployeeById('123');
      
      // Assert
      expect(Employee.findById).toHaveBeenCalledWith('123');
      expect(mockPopulate).toHaveBeenCalledTimes(3);
      expect(mockExec).toHaveBeenCalled();
      expect(result).toEqual(mockEmployee);
    });
    
    it('should return null when employee not found', async () => {
      // Arrange
      const mockPopulate = jest.fn().mockReturnThis();
      const mockExec = jest.fn().mockResolvedValue(null);
      
      (Employee.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate,
        exec: mockExec,
      });
      
      // Act
      const result = await employeeService.getEmployeeById('123');
      
      // Assert
      expect(Employee.findById).toHaveBeenCalledWith('123');
      expect(mockPopulate).toHaveBeenCalledTimes(3);
      expect(mockExec).toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });
  
  // Add more tests for other methods
});
```

## Best Practices

1. **Code Organization**:
   - Keep related code together (models, services, API routes)
   - Use consistent naming conventions
   - Document complex logic with comments

2. **Error Handling**:
   - Use try-catch blocks for error handling
   - Return appropriate error messages
   - Log errors for debugging

3. **Performance Optimization**:
   - Use database indexes for frequently queried fields
   - Implement caching for expensive operations
   - Optimize database queries

4. **Security**:
   - Validate all input data
   - Implement proper authentication and authorization
   - Protect sensitive employee data

## Conclusion

By following this guide, you will be able to implement the Employee module in a consistent and efficient manner. This module serves as the foundation for the HR management system and integrates with other modules to provide a comprehensive solution.

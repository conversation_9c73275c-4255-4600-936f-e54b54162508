# Employee Module Integration Guide

## Introduction

This guide provides detailed instructions for implementing the integration of the Employee module with other relevant modules in the TCM Enterprise Business Suite. It is intended for developers working on the HR management system to ensure consistent implementation and proper alignment between modules.

## Architecture Overview

The Employee module serves as the central hub for all employee-related data and operations. It connects with various other modules to provide a comprehensive HR management system:

```
                    ┌─────────────┐
                    │  Attendance │
                    └──────┬──────┘
                           │
┌─────────────┐     ┌──────┴──────┐     ┌─────────────┐
│    Leave    │─────┤   Employee  ├─────┤    Tasks    │
└─────────────┘     └──────┬──────┘     └─────────────┘
                           │
┌─────────────┐     ┌──────┴──────┐     ┌─────────────┐
│    Loans    │─────┤   Payroll   ├─────┤ Accounting  │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Key Integration Points

### 1. Employee-Payroll Integration

#### Models
- **Employee**: Contains basic employee information
- **EmployeeSalary**: Links employee to their salary structure
- **SalaryStructure**: Defines salary components
- **SalaryRevision**: Tracks salary changes over time
- **Allowance**: Defines additional compensation
- **TaxBracket**: Defines tax calculation rules

#### Services
- **EmployeeService**: Manages employee data
- **SalaryService**: Manages employee compensation
- **TaxService**: Calculates taxes based on salary

#### Implementation Guidelines
1. **EmployeeSalary Model Enhancement**:
   ```typescript
   // models/payroll/EmployeeSalary.ts
   import mongoose, { Schema, Document } from 'mongoose';
   import { IEmployee } from '../employee/Employee';
   import { ISalaryStructure } from './SalaryStructure';

   export interface IEmployeeSalary extends Document {
     employee: IEmployee['_id'];
     salaryStructure: ISalaryStructure['_id'];
     basicSalary: number;
     effectiveDate: Date;
     endDate?: Date;
     isActive: boolean;
     createdBy: string;
     updatedBy: string;
   }

   const EmployeeSalarySchema = new Schema({
     employee: {
       type: Schema.Types.ObjectId,
       ref: 'Employee',
       required: true,
       index: true
     },
     salaryStructure: {
       type: Schema.Types.ObjectId,
       ref: 'SalaryStructure',
       required: true
     },
     basicSalary: {
       type: Number,
       required: true
     },
     effectiveDate: {
       type: Date,
       required: true
     },
     endDate: {
       type: Date
     },
     isActive: {
       type: Boolean,
       default: true
     },
     createdBy: {
       type: String,
       required: true
     },
     updatedBy: {
       type: String,
       required: true
     }
   }, {
     timestamps: true
   });

   export default mongoose.models.EmployeeSalary || 
     mongoose.model<IEmployeeSalary>('EmployeeSalary', EmployeeSalarySchema);
   ```

2. **SalaryService Implementation**:
   ```typescript
   // services/payroll/SalaryService.ts
   import EmployeeSalary, { IEmployeeSalary } from '@/models/payroll/EmployeeSalary';
   import SalaryStructure from '@/models/payroll/SalaryStructure';
   import TaxService from './TaxService';
   
   export default class SalaryService {
     private taxService: TaxService;
     
     constructor() {
       this.taxService = new TaxService();
     }
     
     async getEmployeeSalary(employeeId: string): Promise<IEmployeeSalary | null> {
       return await EmployeeSalary.findOne({ 
         employee: employeeId, 
         isActive: true 
       })
       .populate('salaryStructure')
       .exec();
     }
     
     async calculateNetSalary(employeeId: string): Promise<{
       grossSalary: number;
       deductions: Record<string, number>;
       netSalary: number;
     }> {
       const employeeSalary = await this.getEmployeeSalary(employeeId);
       if (!employeeSalary) {
         throw new Error('Employee salary not found');
       }
       
       // Calculate gross salary
       const grossSalary = employeeSalary.basicSalary;
       
       // Calculate tax deductions
       const taxDeduction = await this.taxService.calculatePAYE(grossSalary);
       
       // Calculate other deductions (to be implemented)
       const otherDeductions = 0;
       
       // Calculate net salary
       const netSalary = grossSalary - taxDeduction - otherDeductions;
       
       return {
         grossSalary,
         deductions: {
           tax: taxDeduction,
           other: otherDeductions
         },
         netSalary
       };
     }
     
     // Other salary-related methods
   }
   ```

### 2. Employee-Leave Integration

#### Models
- **Employee**: Contains employee information
- **Leave**: Tracks leave requests
- **LeaveBalance**: Tracks available leave
- **LeaveType**: Defines different types of leave

#### Services
- **EmployeeService**: Manages employee data
- **LeaveService**: Manages leave requests and balances

#### Implementation Guidelines
1. **LeaveBalance Model**:
   ```typescript
   // models/leave/LeaveBalance.ts
   import mongoose, { Schema, Document } from 'mongoose';
   import { IEmployee } from '../employee/Employee';
   import { ILeaveType } from './LeaveType';

   export interface ILeaveBalance extends Document {
     employee: IEmployee['_id'];
     leaveType: ILeaveType['_id'];
     year: number;
     totalDays: number;
     usedDays: number;
     pendingDays: number;
     remainingDays: number;
     carryOver: number;
     createdBy: string;
     updatedBy: string;
   }

   const LeaveBalanceSchema = new Schema({
     employee: {
       type: Schema.Types.ObjectId,
       ref: 'Employee',
       required: true
     },
     leaveType: {
       type: Schema.Types.ObjectId,
       ref: 'LeaveType',
       required: true
     },
     year: {
       type: Number,
       required: true
     },
     totalDays: {
       type: Number,
       required: true
     },
     usedDays: {
       type: Number,
       default: 0
     },
     pendingDays: {
       type: Number,
       default: 0
     },
     remainingDays: {
       type: Number,
       default: function(this: any) {
         return this.totalDays;
       }
     },
     carryOver: {
       type: Number,
       default: 0
     },
     createdBy: {
       type: String,
       required: true
     },
     updatedBy: {
       type: String,
       required: true
     }
   }, {
     timestamps: true
   });

   // Create a compound index for employee, leaveType, and year
   LeaveBalanceSchema.index({ employee: 1, leaveType: 1, year: 1 }, { unique: true });

   export default mongoose.models.LeaveBalance || 
     mongoose.model<ILeaveBalance>('LeaveBalance', LeaveBalanceSchema);
   ```

2. **LeaveType Model**:
   ```typescript
   // models/leave/LeaveType.ts
   import mongoose, { Schema, Document } from 'mongoose';

   export interface ILeaveType extends Document {
     name: string;
     code: string;
     description: string;
     defaultDays: number;
     isActive: boolean;
     isPaid: boolean;
     requiresApproval: boolean;
     maxConsecutiveDays: number;
     minNoticeInDays: number;
     allowCarryOver: boolean;
     maxCarryOverDays: number;
     createdBy: string;
     updatedBy: string;
   }

   const LeaveTypeSchema = new Schema({
     name: {
       type: String,
       required: true
     },
     code: {
       type: String,
       required: true,
       unique: true
     },
     description: {
       type: String
     },
     defaultDays: {
       type: Number,
       required: true
     },
     isActive: {
       type: Boolean,
       default: true
     },
     isPaid: {
       type: Boolean,
       default: true
     },
     requiresApproval: {
       type: Boolean,
       default: true
     },
     maxConsecutiveDays: {
       type: Number,
       default: 0 // 0 means no limit
     },
     minNoticeInDays: {
       type: Number,
       default: 0
     },
     allowCarryOver: {
       type: Boolean,
       default: false
     },
     maxCarryOverDays: {
       type: Number,
       default: 0
     },
     createdBy: {
       type: String,
       required: true
     },
     updatedBy: {
       type: String,
       required: true
     }
   }, {
     timestamps: true
   });

   export default mongoose.models.LeaveType || 
     mongoose.model<ILeaveType>('LeaveType', LeaveTypeSchema);
   ```

### 3. Employee-Loan Integration

#### Models
- **Employee**: Contains employee information
- **Loan**: Tracks loan details
- **LoanApplication**: Tracks loan requests
- **LoanRepayment**: Tracks loan repayments

#### Services
- **EmployeeService**: Manages employee data
- **LoanService**: Manages loan operations
- **PayrollService**: Handles salary deductions for loan repayments

#### Implementation Guidelines
1. **LoanApplication Model**:
   ```typescript
   // models/loan/LoanApplication.ts
   import mongoose, { Schema, Document } from 'mongoose';
   import { IEmployee } from '../employee/Employee';

   export interface ILoanApplication extends Document {
     employee: IEmployee['_id'];
     amount: number;
     purpose: string;
     requestDate: Date;
     status: 'pending' | 'approved' | 'rejected';
     approvedAmount?: number;
     approvedBy?: string;
     approvalDate?: Date;
     rejectionReason?: string;
     interestRate?: number;
     termInMonths?: number;
     attachments?: string[];
     createdBy: string;
     updatedBy: string;
   }

   const LoanApplicationSchema = new Schema({
     employee: {
       type: Schema.Types.ObjectId,
       ref: 'Employee',
       required: true
     },
     amount: {
       type: Number,
       required: true
     },
     purpose: {
       type: String,
       required: true
     },
     requestDate: {
       type: Date,
       default: Date.now
     },
     status: {
       type: String,
       enum: ['pending', 'approved', 'rejected'],
       default: 'pending'
     },
     approvedAmount: {
       type: Number
     },
     approvedBy: {
       type: String
     },
     approvalDate: {
       type: Date
     },
     rejectionReason: {
       type: String
     },
     interestRate: {
       type: Number
     },
     termInMonths: {
       type: Number
     },
     attachments: [{
       type: String
     }],
     createdBy: {
       type: String,
       required: true
     },
     updatedBy: {
       type: String,
       required: true
     }
   }, {
     timestamps: true
   });

   export default mongoose.models.LoanApplication || 
     mongoose.model<ILoanApplication>('LoanApplication', LoanApplicationSchema);
   ```

## Testing Guidelines

1. **Unit Testing**:
   - Test each service method in isolation
   - Mock dependencies to focus on the specific functionality
   - Verify calculations and business logic

2. **Integration Testing**:
   - Test the interaction between different services
   - Verify data flow between modules
   - Test API endpoints with realistic data

3. **End-to-End Testing**:
   - Test complete workflows from UI to database
   - Verify that all components work together correctly
   - Test edge cases and error handling

## Best Practices

1. **Code Organization**:
   - Keep related code together (models, services, API routes)
   - Use consistent naming conventions
   - Document complex logic with comments

2. **Error Handling**:
   - Use try-catch blocks for error handling
   - Return appropriate error messages
   - Log errors for debugging

3. **Performance Optimization**:
   - Use database indexes for frequently queried fields
   - Implement caching for expensive operations
   - Optimize database queries

4. **Security**:
   - Validate all input data
   - Implement proper authentication and authorization
   - Protect sensitive employee data

## Resources

- [Employee Module Development Tracker](../../EMPLOYEE_MODULE_DEVELOPMENT_TRACKER.md)
- [Employee Module Integration Tracker](../../EMPLOYEE_MODULE_INTEGRATION_TRACKER.md)
- [Payroll Module Development Tracker](../../PAYROLL_MODULE_DEVELOPMENT_TRACKER.md)

## Conclusion

By following this guide, you will be able to implement the integration of the Employee module with other relevant modules in a consistent and efficient manner. This integration is crucial for creating a comprehensive HR management system that meets the needs of the organization.

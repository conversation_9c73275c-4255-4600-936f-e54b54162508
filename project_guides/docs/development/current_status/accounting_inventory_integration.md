# Accounting-Inventory Integration

This document outlines the integration between the Accounting and Inventory modules in the TCM Enterprise Business Suite.

## Overview

The Accounting-Inventory integration enables automatic creation of journal entries in the accounting system when inventory transactions occur. This ensures that the financial impact of inventory changes is properly recorded in the accounting system.

## Key Features

1. **Automatic Journal Entry Creation**: When inventory transactions occur (purchases, sales, adjustments, etc.), corresponding journal entries are automatically created in the accounting system.

2. **Asset Lifecycle Financial Tracking**: Financial aspects of asset lifecycle (acquisition, depreciation, disposal) are automatically recorded in the accounting system.

3. **Bidirectional Traceability**: Links between inventory transactions and accounting journal entries are maintained, allowing users to trace from inventory to accounting and vice versa.

4. **Error Handling and Recovery**: Failed integrations are tracked and can be retried.

## Integration Points

### Inventory Transactions

| Transaction Type | Accounting Impact |
|------------------|-------------------|
| Purchase | Debit Inventory, Credit Accounts Payable |
| Sale | Debit Cost of Goods Sold, Credit Inventory |
| Adjustment (Increase) | Debit Inventory, Credit Inventory Adjustment |
| Adjustment (Decrease) | Debit Inventory Adjustment, Credit Inventory |
| Transfer | Memo entry only (no financial impact) |
| Return | Debit Inventory, Credit Accounts Payable |
| Write-off | Debit Inventory Write-off, Credit Inventory |

### Asset Lifecycle

| Event | Accounting Impact |
|-------|-------------------|
| Acquisition | Debit Fixed Assets, Credit Accounts Payable/Cash |
| Depreciation | Debit Depreciation Expense, Credit Accumulated Depreciation |
| Disposal | Debit Cash, Debit Accumulated Depreciation, Credit Fixed Assets, Credit/Debit Gain/Loss on Disposal |

## Implementation Details

### Services

1. **InventoryAccountingService**: Creates journal entries for inventory transactions and asset lifecycle events.

2. **InventoryAccountingLinkService**: Manages links between inventory items/transactions and accounting journal entries.

3. **EnhancedInventoryTransactionService**: Extends the base inventory transaction service with accounting integration.

4. **EnhancedAssetService**: Extends the base asset service with accounting integration.

### Models

1. **InventoryAccountingLink**: Stores links between inventory items/transactions and accounting journal entries.

### API Routes

1. **/api/integration/inventory-accounting**: Retrieves and manages inventory-accounting links.

2. **/api/inventory/enhanced-transaction**: Creates inventory transactions with accounting integration.

3. **/api/inventory/enhanced-asset**: Manages assets with accounting integration.

## Usage Examples

### Creating an Inventory Transaction with Accounting Integration

```javascript
// Example: Record a stock purchase with accounting integration
const response = await fetch('/api/inventory/enhanced-transaction', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    transactionType: 'purchase',
    stockId: '*********',
    stockName: 'Office Paper',
    quantity: 100,
    unitCost: 5.99,
    purchaseOrderNumber: 'PO-2023-001',
    location: 'Main Warehouse',
    notes: 'Regular monthly purchase'
  }),
});

const result = await response.json();
```

### Creating an Asset with Accounting Integration

```javascript
// Example: Create a new asset with accounting integration
const response = await fetch('/api/inventory/enhanced-asset', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'Office Laptop',
    category: 'computer_equipment',
    purchaseDate: '2023-06-15',
    purchasePrice: 1200,
    location: 'IT Department',
    serialNumber: 'LT-2023-001',
    manufacturer: 'Dell',
    model: 'XPS 15',
    depreciationMethod: 'straight-line',
    depreciationRate: 20,
    notes: 'Assigned to Finance Department'
  }),
});

const result = await response.json();
```

### Recording Asset Depreciation with Accounting Integration

```javascript
// Example: Record asset depreciation with accounting integration
const response = await fetch('/api/inventory/enhanced-asset', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    operation: 'depreciate',
    assetId: '*********',
    depreciationAmount: 100,
    depreciationDate: '2023-07-31',
    notes: 'Monthly depreciation'
  }),
});

const result = await response.json();
```

### Disposing an Asset with Accounting Integration

```javascript
// Example: Dispose an asset with accounting integration
const response = await fetch('/api/inventory/enhanced-asset', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    operation: 'dispose',
    assetId: '*********',
    disposalAmount: 300,
    disposalDate: '2023-08-15',
    disposalMethod: 'sale',
    notes: 'Sold to employee'
  }),
});

const result = await response.json();
```

### Viewing Inventory-Accounting Links

```javascript
// Example: Get links for an inventory item
const response = await fetch('/api/integration/inventory-accounting?itemId=*********&itemType=stock');
const links = await response.json();

// Example: Get links for a transaction
const response = await fetch('/api/integration/inventory-accounting?transactionId=*********');
const links = await response.json();

// Example: Get error links
const response = await fetch('/api/integration/inventory-accounting?errorOnly=true&page=1&limit=10');
const result = await response.json();
```

## Future Enhancements

1. **Batch Processing**: Implement batch processing for high-volume inventory transactions.

2. **Advanced Reporting**: Create reports that combine inventory and accounting data.

3. **Real-time Dashboards**: Develop dashboards showing the financial impact of inventory changes.

4. **Approval Workflows**: Implement approval workflows for high-value inventory transactions.

5. **Audit Trails**: Enhance audit trails for inventory-accounting integration.

## Conclusion

The Accounting-Inventory integration provides a seamless connection between inventory management and financial accounting, ensuring that inventory changes are properly reflected in the financial statements. This integration eliminates manual data entry, reduces errors, and provides real-time financial visibility into inventory operations.

# Bank Reconciliation Guide

## Overview

Bank reconciliation is the process of matching the transactions in your accounting records with the transactions in your bank statement. This process helps identify discrepancies and ensures that your financial records are accurate. This guide explains how to perform bank reconciliations in the TCM Enterprise Business Suite.

## Accessing the Bank Reconciliation Module

1. Log in to the TCM Enterprise Business Suite
2. Navigate to the Dashboard
3. Click on "Accounting" in the main navigation
4. Select "Banking & Treasury" from the accounting index page
5. Select the "Reconciliation" tab

## Understanding Bank Reconciliation

### Purpose of Bank Reconciliation

Bank reconciliation serves several important purposes:

1. **Detecting Errors**: Identifies errors in your accounting records or bank statements
2. **Preventing Fraud**: Helps detect unauthorized transactions
3. **Accurate Financial Reporting**: Ensures your financial statements reflect the actual cash position
4. **Cash Flow Management**: Provides an accurate picture of available funds

### Reconciliation Process Overview

The bank reconciliation process involves:

1. **Comparing Transactions**: Matching transactions in your accounting system with those on your bank statement
2. **Identifying Differences**: Finding transactions that appear in one place but not the other
3. **Making Adjustments**: Recording any necessary adjustments to your accounting records
4. **Finalizing Reconciliation**: Marking the reconciliation as complete

## Preparing for Reconciliation

### Prerequisites

Before starting a bank reconciliation, ensure you have:

1. **Bank Statement**: The most recent bank statement for the account
2. **Updated Records**: All transactions recorded in your accounting system
3. **Previous Reconciliation**: The ending balance from the previous reconciliation

### Importing Bank Statements

You can import bank statements to streamline the reconciliation process:

1. Navigate to the Bank Reconciliation page
2. Click "Import Statement"
3. Select the bank account
4. Choose the import method:
   - Upload a statement file (CSV, OFX, QFX, etc.)
   - Connect to online banking (if configured)
5. Follow the prompts to complete the import

## Performing Bank Reconciliation

### Starting a New Reconciliation

1. Navigate to the Bank Reconciliation page
2. Click "New Reconciliation"
3. Select the bank account to reconcile
4. Enter the statement details:
   - Statement Date: The ending date of the bank statement
   - Statement Ending Balance: The ending balance from the bank statement
5. Click "Start Reconciliation"

### Matching Transactions

1. The system will display unreconciled transactions from your accounting records and imported bank statement
2. For each transaction:
   - If the transaction appears in both your records and the bank statement, mark it as "Matched"
   - If the transaction appears only in your records, leave it as "Unmatched"
   - If the transaction appears only in the bank statement, you may need to record it in your system

### Automatic Matching

The system can automatically match transactions based on:

1. Amount
2. Date
3. Reference number
4. Description

To use automatic matching:

1. Click "Auto-Match" button
2. Review the suggested matches
3. Confirm or reject each match

### Handling Unmatched Transactions

#### Transactions in Your Records but Not on Bank Statement

These may be:

1. **Outstanding Checks**: Checks you've issued that haven't cleared the bank
2. **Deposits in Transit**: Deposits you've recorded that haven't been processed by the bank
3. **Errors**: Transactions recorded incorrectly in your system

#### Transactions on Bank Statement but Not in Your Records

These may be:

1. **Bank Fees**: Service charges or fees deducted by the bank
2. **Interest Income**: Interest earned on the account
3. **Electronic Payments**: Automatic payments or transfers
4. **Errors**: Transactions recorded incorrectly by the bank

### Recording Adjustments

For transactions that appear on the bank statement but not in your records:

1. Click "Add Transaction" button
2. Enter the transaction details:
   - Date
   - Description
   - Amount
   - Type (Deposit or Payment)
   - Account (for the other side of the entry)
3. Click "Save" to record the transaction

### Finalizing Reconciliation

1. Verify that the adjusted book balance matches the bank statement balance
2. Review any remaining unmatched transactions
3. Click "Complete Reconciliation"
4. Enter any notes about the reconciliation
5. Click "Finalize" to mark the reconciliation as complete

## Reconciliation Reports

### Reconciliation Summary

1. Navigate to the Bank Reconciliation page
2. Click "Reports" button
3. Select "Reconciliation Summary"
4. Choose the bank account and period
5. Generate the report

### Reconciliation Detail

1. Navigate to the Bank Reconciliation page
2. Click "Reports" button
3. Select "Reconciliation Detail"
4. Choose the bank account and period
5. Generate the report

### Unreconciled Items

1. Navigate to the Bank Reconciliation page
2. Click "Reports" button
3. Select "Unreconciled Items"
4. Choose the bank account
5. Generate the report

## Best Practices

1. **Regular Reconciliation**: Reconcile bank accounts at least monthly
2. **Timely Recording**: Record all transactions promptly
3. **Consistent Process**: Follow a consistent reconciliation process
4. **Documentation**: Maintain documentation for any adjustments made
5. **Review**: Have someone other than the preparer review the reconciliation
6. **Follow Up**: Investigate and resolve any discrepancies promptly

## Troubleshooting

### Common Issues

1. **Unmatched Transactions**: If you have many unmatched transactions, check for:
   - Timing differences
   - Recording errors
   - Missing transactions

2. **Balance Discrepancies**: If the reconciled balance doesn't match the bank statement:
   - Check your math
   - Look for transposed numbers
   - Verify the starting balance

3. **Import Errors**: If you encounter errors when importing bank statements:
   - Check the file format
   - Verify the account information
   - Try a different import method

### Getting Help

If you encounter issues that you cannot resolve, contact your system administrator or the TCM Enterprise Business Suite support team.

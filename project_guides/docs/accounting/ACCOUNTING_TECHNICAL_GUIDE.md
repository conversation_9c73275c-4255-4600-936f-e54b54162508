# TCM Enterprise Business Suite - Accounting Module Technical Guide

## Architecture Overview

The Accounting Module is built using a modular architecture that follows the principles of separation of concerns and domain-driven design. This document provides technical details for developers working on the module.

## Table of Contents

1. [Technology Stack](#technology-stack)
2. [Code Organization](#code-organization)
3. [Data Models](#data-models)
4. [Services](#services)
5. [API Routes](#api-routes)
6. [UI Components](#ui-components)
7. [Integration Points](#integration-points)
8. [Testing](#testing)
9. [Deployment](#deployment)

## Technology Stack

- **Frontend**: Next.js, React, Tailwind CSS, Zustand
- **Backend**: Next.js API Routes, Node.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Custom auth system
- **State Management**: Zustand
- **Form Handling**: React Hook Form, Zod
- **API Client**: Axios
- **Testing**: Jest, React Testing Library

## Code Organization

The Accounting Module follows a feature-based organization structure:

```
/models/accounting/          # MongoDB models
/lib/services/accounting/    # Business logic services
/lib/stores/accounting/      # Zustand state stores
/app/api/accounting/         # API routes
/app/dashboard/accounting/   # UI pages
/components/accounting/      # UI components
/types/accounting/           # TypeScript types
/utils/accounting/           # Utility functions
/hooks/accounting/           # Custom React hooks
```

## Data Models

### Core Models

#### Account

```typescript
// models/accounting/Account.ts
interface IAccount extends Document {
  code: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  subtype?: string;
  description?: string;
  parentAccount?: mongoose.Types.ObjectId;
  isParent: boolean;
  level: number;
  path: string;
  balance: number;
  defaultFor?: string;
  isActive: boolean;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### JournalEntry

```typescript
// models/accounting/JournalEntry.ts
interface IJournalEntry extends Document {
  date: Date;
  reference: string;
  description: string;
  items: Array<{
    accountId: mongoose.Types.ObjectId;
    description?: string;
    debit: number;
    credit: number;
    departmentId?: mongoose.Types.ObjectId;
    projectId?: mongoose.Types.ObjectId;
    costCenterId?: mongoose.Types.ObjectId;
  }>;
  status: 'draft' | 'posted' | 'voided';
  postingDate?: Date;
  metadata?: Record<string, any>;
  notes?: string;
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### Transaction

```typescript
// models/accounting/Transaction.ts
interface ITransaction extends Document {
  date: Date;
  description: string;
  amount: number;
  type: 'debit' | 'credit' | 'transfer';
  account: mongoose.Types.ObjectId;
  toAccount?: mongoose.Types.ObjectId;
  category?: string;
  subcategory?: string;
  reference?: string;
  status: 'pending' | 'completed' | 'voided';
  journalEntryId?: mongoose.Types.ObjectId;
  metadata?: Record<string, any>;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### RecurringTransaction

```typescript
// models/accounting/RecurringTransaction.ts
interface IRecurringTransaction extends Document {
  name: string;
  description?: string;
  amount: number;
  type: 'debit' | 'credit' | 'transfer';
  account: mongoose.Types.ObjectId;
  toAccount?: mongoose.Types.ObjectId;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  customFrequency?: number;
  startDate: Date;
  endDate?: Date;
  nextExecutionDate: Date;
  lastExecutionDate?: Date;
  occurrences?: number;
  executedOccurrences: number;
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### CostCenter

```typescript
// models/accounting/CostCenter.ts
interface ICostCenter extends Document {
  code: string;
  name: string;
  description?: string;
  manager?: mongoose.Types.ObjectId;
  parentCostCenter?: mongoose.Types.ObjectId;
  department?: mongoose.Types.ObjectId;
  budget?: number;
  status: 'active' | 'inactive';
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### FinancialStatement

```typescript
// models/accounting/FinancialStatement.ts
interface IFinancialStatement extends Document {
  type: 'balance_sheet' | 'income_statement' | 'cash_flow' | 'changes_in_equity' | 'custom';
  name: string;
  description?: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  sections: Array<{
    title: string;
    accounts: Array<mongoose.Types.ObjectId>;
    subtotal: boolean;
  }>;
  status: 'draft' | 'published';
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

#### ProjectBudget

```typescript
// models/accounting/ProjectBudget.ts
interface IProjectBudget extends Document {
  project: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  totalBudget: number;
  status: 'draft' | 'approved' | 'active' | 'closed';
  fiscalYear: string;
  categories: Array<{
    name: string;
    description?: string;
    amount: number;
  }>;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}
```

## Services

### Core Services

#### AccountService

```typescript
// lib/services/accounting/account-service.ts
class AccountService {
  async getAccounts(filters?: any): Promise<IAccount[]>;
  async getAccountById(id: string): Promise<IAccount>;
  async createAccount(data: any, userId: string): Promise<IAccount>;
  async updateAccount(id: string, data: any, userId: string): Promise<IAccount>;
  async deleteAccount(id: string): Promise<boolean>;
  async getAccountBalance(id: string, asOfDate?: Date): Promise<number>;
  async getChartOfAccounts(): Promise<any>;
}
```

#### JournalEntryService

```typescript
// lib/services/accounting/journal-entry-service.ts
class JournalEntryService {
  async getJournalEntries(filters?: any): Promise<IJournalEntry[]>;
  async getJournalEntryById(id: string): Promise<IJournalEntry>;
  async createJournalEntry(data: any, userId: string): Promise<IJournalEntry>;
  async updateJournalEntry(id: string, data: any, userId: string): Promise<IJournalEntry>;
  async deleteJournalEntry(id: string): Promise<boolean>;
  async postJournalEntry(id: string, userId: string): Promise<IJournalEntry>;
  async voidJournalEntry(id: string, userId: string): Promise<IJournalEntry>;
}
```

#### RecurringTransactionService

```typescript
// lib/services/accounting/recurring-transaction-service.ts
class RecurringTransactionService {
  async getRecurringTransactions(filters?: any): Promise<IRecurringTransaction[]>;
  async getRecurringTransactionById(id: string): Promise<IRecurringTransaction>;
  async createRecurringTransaction(data: any, userId: string): Promise<IRecurringTransaction>;
  async updateRecurringTransaction(id: string, data: any, userId: string): Promise<IRecurringTransaction>;
  async deleteRecurringTransaction(id: string): Promise<boolean>;
  async processRecurringTransaction(id: string, userId: string): Promise<any>;
  async processDueRecurringTransactions(asOfDate: Date, userId: string): Promise<any>;
}
```

#### FinancialStatementService

```typescript
// lib/services/accounting/financial-statement-service.ts
class FinancialStatementService {
  async getFinancialStatements(filters?: any): Promise<IFinancialStatement[]>;
  async getFinancialStatementById(id: string): Promise<IFinancialStatement>;
  async createFinancialStatement(data: any, userId: string): Promise<IFinancialStatement>;
  async updateFinancialStatement(id: string, data: any, userId: string): Promise<IFinancialStatement>;
  async deleteFinancialStatement(id: string): Promise<boolean>;
  async generateFinancialStatement(id: string, asOfDate?: Date): Promise<any>;
}
```

### Integration Services

#### PayrollIntegrationService

```typescript
// lib/services/accounting/payroll-integration-service.ts
class PayrollIntegrationService {
  async createPayrollAccountingEntries(payrollRunId: string, userId: string): Promise<any>;
  async analyzePayrollBudgetVariance(departmentId?: string, period?: any): Promise<any>;
}
```

#### InventoryIntegrationService

```typescript
// lib/services/accounting/inventory-integration-service.ts
class InventoryIntegrationService {
  async createPurchaseAccountingEntries(purchaseId: string, userId: string): Promise<any>;
  async createSaleAccountingEntries(saleId: string, userId: string): Promise<any>;
  async createAdjustmentAccountingEntries(adjustmentId: string, userId: string): Promise<any>;
}
```

#### ProjectIntegrationService

```typescript
// lib/services/accounting/project-integration-service.ts
class ProjectIntegrationService {
  async createExpenseAccountingEntries(expenseId: string, userId: string): Promise<any>;
  async createRevenueAccountingEntries(revenueId: string, userId: string): Promise<any>;
  async analyzeProjectBudgetVariance(projectId: string): Promise<any>;
}
```

## API Routes

### Core API Routes

- **Accounts**: `/api/accounting/accounts`
- **Journal Entries**: `/api/accounting/journal-entries`
- **Transactions**: `/api/accounting/transactions`
- **Recurring Transactions**: `/api/accounting/recurring-transactions`
- **Financial Statements**: `/api/accounting/financial-statements`
- **Cost Centers**: `/api/accounting/cost-centers`
- **Project Budgets**: `/api/accounting/project-budgets`

### Integration API Routes

- **Payroll Integration**: `/api/accounting/payroll`
- **Inventory Integration**: `/api/accounting/inventory`
- **Project Integration**: `/api/accounting/projects`

## UI Components

### Core Components

- **AccountList**: Display and manage accounts
- **JournalEntryForm**: Create and edit journal entries
- **TransactionList**: Display and manage transactions
- **RecurringTransactionForm**: Create and edit recurring transactions
- **FinancialStatementGenerator**: Generate financial statements
- **CostCenterList**: Display and manage cost centers
- **ProjectBudgetForm**: Create and edit project budgets

### Integration Components

- **PayrollIntegrationPanel**: Manage payroll integration
- **InventoryIntegrationPanel**: Manage inventory integration
- **ProjectIntegrationPanel**: Manage project integration

## Integration Points

### Payroll Module

- **Journal Entry Creation**: Automatically create journal entries from payroll runs
- **Budget Variance Analysis**: Compare actual payroll expenses to budgeted amounts

### Inventory Module

- **Purchase Accounting**: Create journal entries for inventory purchases
- **Sales Accounting**: Create journal entries for inventory sales
- **Adjustment Accounting**: Create journal entries for inventory adjustments

### Project Module

- **Expense Accounting**: Create journal entries for project expenses
- **Revenue Accounting**: Create journal entries for project revenues
- **Budget Variance Analysis**: Compare actual project expenses to budgeted amounts

## Testing

### Unit Tests

- **Model Tests**: Test model validation and methods
- **Service Tests**: Test service methods and business logic
- **API Route Tests**: Test API endpoints and responses

### Integration Tests

- **Module Integration Tests**: Test integration between modules
- **End-to-End Tests**: Test complete workflows

## Deployment

### Environment Variables

- **DATABASE_URL**: MongoDB connection string
- **JWT_SECRET**: Secret for JWT authentication
- **NEXT_PUBLIC_API_URL**: API base URL

### Build Process

```bash
# Install dependencies
npm install

# Build for production
npm run build

# Start production server
npm start
```

### Database Migrations

- **Initial Setup**: Create initial database schema
- **Data Migration**: Migrate data from legacy systems
- **Schema Updates**: Update schema for new features

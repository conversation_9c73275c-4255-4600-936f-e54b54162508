# TCM Enterprise Business Suite - Accounting API Reference

## Overview

This document provides detailed information about the Accounting Module API endpoints, request/response formats, and authentication requirements.

## Authentication

All API requests require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

## Response Format

All API responses follow a standard format:

```json
{
  "success": true|false,
  "message": "Optional message",
  "data": { ... },
  "error": "Error message if success is false",
  "errors": { "field": "error message" },
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## Error Codes

- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Missing or invalid authentication
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **500**: Internal Server Error - Server-side error

## Endpoints

### Accounts

#### GET /api/accounting/accounts

Get a list of accounts.

**Query Parameters:**
- `type`: Filter by account type (asset, liability, equity, revenue, expense)
- `isActive`: Filter by active status (true, false)
- `search`: Search by name or code
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "60f1a5b3e6b3f32b4c9e1234",
      "code": "1000",
      "name": "Cash",
      "type": "asset",
      "subtype": "current_asset",
      "balance": 10000,
      "isActive": true
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

#### GET /api/accounting/accounts/:id

Get a single account by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1234",
    "code": "1000",
    "name": "Cash",
    "type": "asset",
    "subtype": "current_asset",
    "description": "Cash on hand",
    "parentAccount": null,
    "isParent": false,
    "level": 1,
    "path": "1000",
    "balance": 10000,
    "defaultFor": "cash",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### POST /api/accounting/accounts

Create a new account.

**Request Body:**
```json
{
  "code": "1001",
  "name": "Checking Account",
  "type": "asset",
  "subtype": "current_asset",
  "description": "Main checking account",
  "parentAccount": null,
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account created successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1235",
    "code": "1001",
    "name": "Checking Account",
    "type": "asset",
    "subtype": "current_asset",
    "description": "Main checking account",
    "parentAccount": null,
    "isParent": false,
    "level": 1,
    "path": "1001",
    "balance": 0,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### PUT /api/accounting/accounts/:id

Update an existing account.

**Request Body:**
```json
{
  "name": "Main Checking Account",
  "description": "Updated description",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account updated successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1235",
    "code": "1001",
    "name": "Main Checking Account",
    "description": "Updated description",
    "isActive": true,
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

#### DELETE /api/accounting/accounts/:id

Delete an account.

**Response:**
```json
{
  "success": true,
  "message": "Account deleted successfully"
}
```

### Journal Entries

#### GET /api/accounting/journal-entries

Get a list of journal entries.

**Query Parameters:**
- `status`: Filter by status (draft, posted, voided)
- `startDate`: Filter by date range start
- `endDate`: Filter by date range end
- `search`: Search by reference or description
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "60f1a5b3e6b3f32b4c9e1236",
      "date": "2023-01-01T00:00:00.000Z",
      "reference": "JE-001",
      "description": "Initial deposit",
      "status": "posted",
      "totalDebit": 1000,
      "totalCredit": 1000
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "totalPages": 5
  }
}
```

#### GET /api/accounting/journal-entries/:id

Get a single journal entry by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1236",
    "date": "2023-01-01T00:00:00.000Z",
    "reference": "JE-001",
    "description": "Initial deposit",
    "items": [
      {
        "accountId": {
          "id": "60f1a5b3e6b3f32b4c9e1234",
          "code": "1000",
          "name": "Cash"
        },
        "description": "Cash deposit",
        "debit": 1000,
        "credit": 0
      },
      {
        "accountId": {
          "id": "60f1a5b3e6b3f32b4c9e1237",
          "code": "3000",
          "name": "Owner's Equity"
        },
        "description": "Initial investment",
        "debit": 0,
        "credit": 1000
      }
    ],
    "status": "posted",
    "postingDate": "2023-01-01T00:00:00.000Z",
    "notes": "Initial investment in the business",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### POST /api/accounting/journal-entries

Create a new journal entry.

**Request Body:**
```json
{
  "date": "2023-01-01",
  "reference": "JE-002",
  "description": "Office supplies purchase",
  "items": [
    {
      "accountId": "60f1a5b3e6b3f32b4c9e1238",
      "description": "Office supplies expense",
      "debit": 500,
      "credit": 0
    },
    {
      "accountId": "60f1a5b3e6b3f32b4c9e1234",
      "description": "Cash payment",
      "debit": 0,
      "credit": 500
    }
  ],
  "notes": "Purchased office supplies"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Journal entry created successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1239",
    "date": "2023-01-01T00:00:00.000Z",
    "reference": "JE-002",
    "description": "Office supplies purchase",
    "items": [
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1238",
        "description": "Office supplies expense",
        "debit": 500,
        "credit": 0
      },
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1234",
        "description": "Cash payment",
        "debit": 0,
        "credit": 500
      }
    ],
    "status": "draft",
    "notes": "Purchased office supplies",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### PUT /api/accounting/journal-entries/:id

Update an existing journal entry.

**Request Body:**
```json
{
  "description": "Updated description",
  "notes": "Updated notes"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Journal entry updated successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1239",
    "description": "Updated description",
    "notes": "Updated notes",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

#### POST /api/accounting/journal-entries/:id/post

Post a journal entry.

**Response:**
```json
{
  "success": true,
  "message": "Journal entry posted successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1239",
    "status": "posted",
    "postingDate": "2023-01-02T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### Recurring Transactions

#### GET /api/accounting/recurring-transactions

Get a list of recurring transactions.

**Query Parameters:**
- `status`: Filter by status (active, paused, completed, cancelled)
- `search`: Search by name or description
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "60f1a5b3e6b3f32b4c9e1240",
      "name": "Monthly Rent",
      "description": "Office rent payment",
      "amount": 2000,
      "frequency": "monthly",
      "nextExecutionDate": "2023-02-01T00:00:00.000Z",
      "status": "active"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 20,
    "totalPages": 2
  }
}
```

#### POST /api/accounting/recurring-transactions

Create a new recurring transaction.

**Request Body:**
```json
{
  "name": "Utility Bill",
  "description": "Monthly utility payment",
  "amount": 500,
  "type": "debit",
  "account": "60f1a5b3e6b3f32b4c9e1234",
  "frequency": "monthly",
  "startDate": "2023-01-15",
  "endDate": "2023-12-15"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Recurring transaction created successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1241",
    "name": "Utility Bill",
    "description": "Monthly utility payment",
    "amount": 500,
    "type": "debit",
    "account": "60f1a5b3e6b3f32b4c9e1234",
    "frequency": "monthly",
    "startDate": "2023-01-15T00:00:00.000Z",
    "endDate": "2023-12-15T00:00:00.000Z",
    "nextExecutionDate": "2023-01-15T00:00:00.000Z",
    "status": "active",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### POST /api/accounting/recurring-transactions/:id/process

Process a recurring transaction.

**Response:**
```json
{
  "success": true,
  "message": "Recurring transaction processed successfully",
  "data": {
    "transaction": {
      "id": "60f1a5b3e6b3f32b4c9e1242",
      "date": "2023-01-15T00:00:00.000Z",
      "description": "Monthly utility payment",
      "amount": 500,
      "type": "debit",
      "account": "60f1a5b3e6b3f32b4c9e1234",
      "status": "completed"
    },
    "recurringTransaction": {
      "id": "60f1a5b3e6b3f32b4c9e1241",
      "nextExecutionDate": "2023-02-15T00:00:00.000Z",
      "lastExecutionDate": "2023-01-15T00:00:00.000Z",
      "executedOccurrences": 1,
      "status": "active"
    }
  }
}
```

### Integration Endpoints

#### POST /api/accounting/payroll/create-entries

Create accounting entries from a payroll run.

**Request Body:**
```json
{
  "payrollRunId": "60f1a5b3e6b3f32b4c9e1243"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Accounting entries created successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1244",
    "reference": "Payroll-123",
    "description": "Payroll for Jan 2023",
    "items": [
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1245",
        "description": "Salary Expense",
        "debit": 10000,
        "credit": 0
      },
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1246",
        "description": "Tax Withholding",
        "debit": 0,
        "credit": 2000
      },
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1247",
        "description": "Net Salary Payable",
        "debit": 0,
        "credit": 8000
      }
    ],
    "status": "posted"
  }
}
```

#### POST /api/accounting/inventory/create-purchase-entries

Create accounting entries from an inventory purchase.

**Request Body:**
```json
{
  "purchaseId": "60f1a5b3e6b3f32b4c9e1248"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Purchase accounting entries created successfully",
  "data": {
    "id": "60f1a5b3e6b3f32b4c9e1249",
    "reference": "INV-PUR-123",
    "description": "Inventory Purchase - 123",
    "items": [
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1250",
        "description": "Inventory Asset",
        "debit": 5000,
        "credit": 0
      },
      {
        "accountId": "60f1a5b3e6b3f32b4c9e1251",
        "description": "Accounts Payable",
        "debit": 0,
        "credit": 5000
      }
    ],
    "status": "posted"
  }
}
```

#### POST /api/integration/project-accounting/:id

Sync a project with accounting.

**Response:**
```json
{
  "success": true,
  "message": "Project synced with accounting successfully",
  "data": {
    "expenses": {
      "processed": 2,
      "failed": 0,
      "results": [
        {
          "id": "60f1a5b3e6b3f32b4c9e1252",
          "status": "success"
        },
        {
          "id": "60f1a5b3e6b3f32b4c9e1253",
          "status": "success"
        }
      ]
    },
    "revenues": {
      "processed": 1,
      "failed": 0,
      "results": [
        {
          "id": "60f1a5b3e6b3f32b4c9e1254",
          "status": "success"
        }
      ]
    },
    "budgetVariance": {
      "project": {
        "id": "60f1a5b3e6b3f32b4c9e1255",
        "name": "Office Renovation"
      },
      "summary": {
        "totalBudget": 50000,
        "totalActualExpense": 45000,
        "totalVariance": 5000,
        "totalVariancePercent": 10,
        "status": "under_budget"
      }
    }
  }
}
```

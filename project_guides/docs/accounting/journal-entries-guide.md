# Journal Entries Guide

## Overview

Journal entries are the foundation of the accounting system in the TCM Enterprise Business Suite. They record all financial transactions and provide an audit trail for your organization's financial activities. This guide explains how to create, manage, and use journal entries effectively.

## Accessing the Journal Entries Module

1. Log in to the TCM Enterprise Business Suite
2. Navigate to the Dashboard
3. Click on "Accounting" in the main navigation
4. Select "Accounting Core" from the accounting index page
5. Select the "Journal Entry" tab

## Understanding Journal Entries

### Basic Concepts

A journal entry records a financial transaction in the accounting system. Each journal entry consists of:

- **Date**: When the transaction occurred
- **Reference**: A unique identifier for the transaction
- **Description**: A brief explanation of the transaction
- **Accounts**: The accounts affected by the transaction
- **Debits and Credits**: The amounts to be debited or credited to each account

### Double-Entry Accounting

The TCM Enterprise Business Suite uses double-entry accounting, which means:

- Every transaction affects at least two accounts
- The total debits must equal the total credits in each journal entry
- This ensures that the accounting equation (Assets = Liabilities + Equity) always remains in balance

### Types of Journal Entries

1. **Regular Journal Entries**: Record day-to-day transactions
2. **Adjusting Journal Entries**: Record accruals, deferrals, and other period-end adjustments
3. **Reversing Journal Entries**: Automatically reverse an entry on a future date
4. **Recurring Journal Entries**: Automatically create the same entry at regular intervals

## Creating Journal Entries

### Basic Journal Entry

1. Navigate to the Journal Entry page
2. Click "New Journal Entry"
3. Enter the basic information:
   - Date: The date of the transaction
   - Reference: A unique reference number (may be auto-generated)
   - Description: A brief explanation of the transaction
4. Add line items:
   - Account: Select an account from the Chart of Accounts
   - Description: Optional description for this line item
   - Debit: Enter the debit amount (if applicable)
   - Credit: Enter the credit amount (if applicable)
5. Add additional line items as needed
6. Verify that the total debits equal the total credits
7. Click "Save" to save as a draft or "Post" to post the journal entry

### Adjusting Journal Entry

1. Follow the steps for creating a basic journal entry
2. Mark the entry as an "Adjusting Entry"
3. This will help identify it as an adjustment during period-end reporting

### Reversing Journal Entry

1. Create a journal entry as usual
2. Check the "Create Reversing Entry" option
3. Specify the date for the reversal
4. Save or post the journal entry
5. The system will automatically create a reversing entry on the specified date

### Recurring Journal Entry

1. Create a journal entry as usual
2. Check the "Create Recurring Entry" option
3. Specify the recurrence pattern:
   - Frequency: Daily, Weekly, Monthly, Quarterly, Annually
   - Start Date: When to start the recurrence
   - End Date or Number of Occurrences: When to end the recurrence
4. Save or post the journal entry
5. The system will automatically create new entries according to the recurrence pattern

## Managing Journal Entries

### Viewing Journal Entries

1. Navigate to the Journal Entry page
2. Use the filters to find specific entries:
   - Date Range
   - Reference
   - Status (Draft, Posted, Voided)
   - Amount
   - Account
3. Click on an entry to view its details

### Editing Journal Entries

You can only edit journal entries that are in "Draft" status:

1. Navigate to the Journal Entry page
2. Find the draft entry you want to edit
3. Click the "Edit" button
4. Make the necessary changes
5. Click "Save" to update the entry

### Posting Journal Entries

Posting a journal entry makes it permanent and updates the account balances:

1. Navigate to the Journal Entry page
2. Find the draft entry you want to post
3. Click the "Post" button
4. Confirm the posting
5. The entry status will change to "Posted" and account balances will be updated

### Voiding Journal Entries

Instead of deleting posted entries, you should void them:

1. Navigate to the Journal Entry page
2. Find the posted entry you want to void
3. Click the "Void" button
4. Enter a reason for voiding
5. Confirm the void
6. The system will create a reversing entry to offset the original entry

### Importing Journal Entries

For bulk operations, you can import journal entries:

1. Navigate to the Journal Entry page
2. Click the "Import" button
3. Download the import template
4. Fill in the journal entry details
5. Upload the completed file
6. Review and post the imported entries

## Journal Entry Reports

### Journal Entry Listing

1. Navigate to the Journal Entry page
2. Click the "Reports" button
3. Select "Journal Entry Listing"
4. Specify the parameters:
   - Date Range
   - Account
   - User
   - Status
5. Generate the report

### Journal Entry Detail

1. Navigate to the Journal Entry page
2. Click the "Reports" button
3. Select "Journal Entry Detail"
4. Specify the parameters:
   - Journal Entry Reference
   - Date Range
5. Generate the report

## Best Practices

1. **Clear Descriptions**: Write clear, concise descriptions that explain the purpose of the entry
2. **Supporting Documentation**: Attach supporting documents to journal entries when possible
3. **Regular Review**: Regularly review journal entries for accuracy and completeness
4. **Consistent Coding**: Use consistent account coding for similar transactions
5. **Timely Recording**: Record transactions in a timely manner
6. **Proper Authorization**: Ensure journal entries are properly authorized before posting
7. **Audit Trail**: Maintain a clear audit trail for all journal entries

## Troubleshooting

### Common Issues

1. **Unbalanced Entry**: If debits and credits don't balance, review each line item for accuracy
2. **Posting Errors**: If you encounter errors when posting, check that all required fields are completed
3. **Missing Accounts**: If you need an account that doesn't exist, add it to the Chart of Accounts

### Getting Help

If you encounter issues that you cannot resolve, contact your system administrator or the TCM Enterprise Business Suite support team.

# TCM Enterprise Business Suite - Accounting Integration Guide

## Overview

This document provides detailed information about integrating the Accounting Module with other modules in the TCM Enterprise Business Suite. It covers integration points, data mapping, configuration options, and the new centralized integration dashboard.

## Table of Contents

1. [Integration Architecture](#integration-architecture)
2. [Integration Dashboard](#integration-dashboard)
3. [Payroll Integration](#payroll-integration)
4. [Inventory Integration](#inventory-integration)
5. [Project Integration](#project-integration)
6. [External System Integration](#external-system-integration)
7. [Custom Integration](#custom-integration)

## Integration Architecture

The Accounting Module uses a service-based integration approach, where dedicated integration services handle the communication between modules. This architecture provides several benefits:

- **Loose Coupling**: Modules can evolve independently without breaking integration
- **Consistent Interface**: Standard methods for creating accounting entries
- **Centralized Logic**: Business rules for accounting entries are centralized
- **Audit Trail**: All integrations are logged for audit purposes

### Integration Flow

1. **Event Trigger**: An event in a source module (e.g., payroll run completion) triggers the integration
2. **Service Call**: The source module calls the appropriate integration service
3. **Data Transformation**: The integration service transforms the source data into accounting entries
4. **Entry Creation**: The integration service creates journal entries in the accounting module
5. **Status Update**: The source module is updated with the accounting status

### Integration Services

- **ModuleIntegrationService**: Central service that handles all module integrations
- **PayrollIntegrationService**: Handles integration with the Payroll Module
- **InventoryIntegrationService**: Handles integration with the Inventory Module
- **ProjectIntegrationService**: Handles integration with the Project Module

## Integration Dashboard

The Integration Dashboard provides a centralized interface for managing all integrations between the Accounting Module and other modules. It allows you to monitor integration status, process pending integrations, and view integration history.

### Accessing the Dashboard

The Integration Dashboard is accessible from the Accounting Module's main navigation:

1. Navigate to Dashboard > Accounting > Integrations
2. The dashboard displays an overview of all module integrations

### Dashboard Features

#### Overview Tab

The Overview tab provides a high-level summary of all integrations:

- **Integration Status Cards**: Quick view of each module's integration status
- **Pending Integrations**: Count of pending integrations that need processing
- **Last Sync Time**: When each integration was last processed
- **Process All Button**: Process all pending integrations across all modules

#### Module-Specific Tabs

Each module has its own tab with detailed information:

- **Payroll Tab**: Details of payroll integration status and operations
- **Inventory Tab**: Details of inventory integration status and operations
- **Project Tab**: Details of project integration status and operations

### Processing Integrations

The dashboard provides two ways to process integrations:

1. **Process All**: Process all pending integrations across all modules
2. **Module-Specific Processing**: Process pending integrations for a specific module

When you click the "Process" button, the system will:

1. Find all pending transactions that need accounting entries
2. Create appropriate journal entries for each transaction
3. Update the transaction status to reflect the accounting integration
4. Provide a summary of the processing results

### API Endpoints

- **POST /api/accounting/integrations/process-all**: Process all pending integrations across all modules

## Payroll Integration

The Payroll Integration allows automatic creation of accounting entries from payroll runs and provides budget variance analysis.

### Integration Points

#### Journal Entry Creation

When a payroll run is completed, the system can automatically create journal entries for:

- Salary expenses (debit)
- Tax withholdings (credit)
- Net salary payable (credit)

#### Budget Variance Analysis

The system can analyze payroll expenses against budgeted amounts by:

- Department
- Period (month, quarter, year)
- Employee category

### Data Mapping

| Payroll Data | Accounting Entry |
|--------------|------------------|
| Gross Salary | Debit to Salary Expense Account |
| Tax Withholding | Credit to Tax Liability Account |
| Benefits | Debit to Benefits Expense Account |
| Net Salary | Credit to Payroll Liability Account |

### Configuration Options

- **Default Accounts**: Configure default accounts for salary expense, tax liability, etc.
- **Department Mapping**: Map departments to specific expense accounts
- **Automatic Processing**: Enable/disable automatic journal entry creation
- **Approval Workflow**: Configure approval process for payroll journal entries

### API Endpoints

- **POST /api/accounting/payroll/create-entries**: Create accounting entries from a payroll run
- **GET /api/accounting/payroll/budget-variance**: Get payroll budget variance analysis

### Example Integration

```javascript
// Example: Create accounting entries from a payroll run
const createPayrollEntries = async (payrollRunId) => {
  try {
    const response = await fetch('/api/accounting/payroll/create-entries', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ payrollRunId })
    });

    const result = await response.json();

    if (result.success) {
      console.log('Accounting entries created successfully:', result.data);
      return result.data;
    } else {
      console.error('Error creating accounting entries:', result.error);
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Error in payroll integration:', error);
    throw error;
  }
};
```

## Inventory Integration

The Inventory Integration allows automatic creation of accounting entries from inventory transactions and provides inventory valuation.

### Integration Points

#### Purchase Accounting

When inventory is purchased, the system can automatically create journal entries for:

- Inventory asset (debit)
- Accounts payable (credit)

#### Sales Accounting

When inventory is sold, the system can automatically create journal entries for:

- Accounts receivable (debit)
- Sales revenue (credit)
- Cost of goods sold (debit)
- Inventory asset (credit)

#### Adjustment Accounting

When inventory is adjusted, the system can automatically create journal entries for:

- Inventory asset (debit/credit)
- Inventory adjustment expense (debit/credit)

### Data Mapping

| Inventory Data | Accounting Entry |
|----------------|------------------|
| Purchase Amount | Debit to Inventory Asset Account |
| Purchase Amount | Credit to Accounts Payable Account |
| Sales Amount | Debit to Accounts Receivable Account |
| Sales Amount | Credit to Sales Revenue Account |
| Cost of Goods | Debit to Cost of Goods Sold Account |
| Cost of Goods | Credit to Inventory Asset Account |

### Configuration Options

- **Default Accounts**: Configure default accounts for inventory asset, COGS, etc.
- **Valuation Method**: Choose inventory valuation method (FIFO, LIFO, Average Cost)
- **Automatic Processing**: Enable/disable automatic journal entry creation
- **Cost Allocation**: Configure rules for allocating additional costs (freight, duties)

### API Endpoints

- **POST /api/accounting/inventory/create-purchase-entries**: Create accounting entries from an inventory purchase
- **POST /api/accounting/inventory/create-sale-entries**: Create accounting entries from an inventory sale
- **POST /api/accounting/inventory/create-adjustment-entries**: Create accounting entries from an inventory adjustment

### Example Integration

```javascript
// Example: Create accounting entries from an inventory sale
const createSaleEntries = async (saleId) => {
  try {
    const response = await fetch('/api/accounting/inventory/create-sale-entries', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ saleId })
    });

    const result = await response.json();

    if (result.success) {
      console.log('Sale accounting entries created successfully:', result.data);
      return result.data;
    } else {
      console.error('Error creating sale accounting entries:', result.error);
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Error in inventory integration:', error);
    throw error;
  }
};
```

## Project Integration

The Project Integration allows automatic creation of accounting entries from project transactions and provides project budget variance analysis.

### Integration Points

#### Expense Accounting

When project expenses are recorded, the system can automatically create journal entries for:

- Project expense (debit)
- Cash/bank (credit)

#### Revenue Accounting

When project revenue is recorded, the system can automatically create journal entries for:

- Accounts receivable (debit)
- Project revenue (credit)

#### Budget Variance Analysis

The system can analyze project expenses against budgeted amounts by:

- Project
- Category
- Period

### Data Mapping

| Project Data | Accounting Entry |
|--------------|------------------|
| Expense Amount | Debit to Project Expense Account |
| Expense Amount | Credit to Cash/Bank Account |
| Revenue Amount | Debit to Accounts Receivable Account |
| Revenue Amount | Credit to Project Revenue Account |

### Configuration Options

- **Default Accounts**: Configure default accounts for project expense, revenue, etc.
- **Project Mapping**: Map projects to specific expense/revenue accounts
- **Automatic Processing**: Enable/disable automatic journal entry creation
- **Revenue Recognition**: Configure rules for revenue recognition

### API Endpoints

- **POST /api/accounting/projects/create-expense-entries**: Create accounting entries from a project expense
- **POST /api/accounting/projects/create-revenue-entries**: Create accounting entries from project revenue
- **GET /api/accounting/projects/[id]/budget-variance**: Get project budget variance analysis
- **POST /api/integration/project-accounting/[id]**: Sync a project with accounting
- **POST /api/integration/project-accounting/batch-sync**: Sync all projects with accounting

### Example Integration

```javascript
// Example: Sync a project with accounting
const syncProjectWithAccounting = async (projectId) => {
  try {
    const response = await fetch(`/api/integration/project-accounting/${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();

    if (result.success) {
      console.log('Project synced with accounting successfully:', result.data);
      return result.data;
    } else {
      console.error('Error syncing project with accounting:', result.error);
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Error in project integration:', error);
    throw error;
  }
};
```

## External System Integration

The Accounting Module can integrate with external accounting systems like QuickBooks and Sage.

### Integration Methods

- **Data Export**: Export accounting data to CSV/Excel for import into external systems
- **API Integration**: Direct integration with external system APIs
- **Middleware**: Use middleware solutions for complex integrations

### Supported Systems

- **QuickBooks**: Export journal entries, chart of accounts
- **Sage**: Export journal entries, chart of accounts
- **Xero**: Export journal entries, chart of accounts
- **Custom**: Custom integration with other systems

### Configuration Options

- **Mapping Rules**: Configure mapping between internal and external accounts
- **Sync Frequency**: Configure how often data is synchronized
- **Conflict Resolution**: Configure rules for handling conflicts

## Custom Integration

Developers can create custom integrations with the Accounting Module using the provided API.

### Integration Steps

1. **Identify Integration Points**: Determine which accounting entries need to be created
2. **Create Integration Service**: Develop a service to handle the integration logic
3. **Implement API Calls**: Use the Accounting API to create journal entries
4. **Add Error Handling**: Implement proper error handling and logging
5. **Test Integration**: Thoroughly test the integration with various scenarios

### Best Practices

- **Use Transactions**: Ensure all database operations are wrapped in transactions
- **Implement Idempotency**: Make integration operations idempotent to prevent duplicates
- **Add Logging**: Log all integration activities for troubleshooting
- **Include Validation**: Validate data before creating accounting entries
- **Provide Feedback**: Return clear success/error messages to users

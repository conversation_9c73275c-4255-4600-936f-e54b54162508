# Chart of Accounts Guide

## Overview

The Chart of Accounts is the foundation of your accounting system in the TCM Enterprise Business Suite. It is a complete listing of every account in your accounting system, organized in a specific order. This guide explains how to set up, manage, and use the Chart of Accounts effectively.

## Accessing the Chart of Accounts

1. Log in to the TCM Enterprise Business Suite
2. Navigate to the Dashboard
3. Click on "Accounting" in the main navigation
4. Select "Accounting Core" from the accounting index page
5. Select the "Chart of Accounts" tab

## Account Structure

### Account Types

The Chart of Accounts is organized into five main account types:

1. **Assets (1000-1999)**: Resources owned by the organization that have economic value
   - Examples: Cash, Accounts Receivable, Equipment, Buildings

2. **Liabilities (2000-2999)**: Obligations that the organization owes to others
   - Examples: Accounts Payable, Loans Payable, Accrued Expenses

3. **Equity (3000-3999)**: The residual interest in the assets after deducting liabilities
   - Examples: Capital, Retained Earnings, Reserves

4. **Revenue (4000-4999)**: Income earned from the organization's activities
   - Examples: Sales Revenue, Service Revenue, Interest Income

5. **Expenses (5000-5999)**: Costs incurred in the course of operations
   - Examples: Salaries Expense, Rent Expense, Utilities Expense

### Account Numbering System

The TCM Enterprise Business Suite uses a four-digit account numbering system:

- First digit: Indicates the account type (1-5)
- Second digit: Indicates the account category within the type
- Third and fourth digits: Indicate the specific account

Example:
- 1000: Cash (Asset)
- 1100: Accounts Receivable (Asset)
- 2000: Accounts Payable (Liability)
- 4000: Sales Revenue (Revenue)
- 5000: Salaries Expense (Expense)

### Account Hierarchy

Accounts can be organized in a hierarchical structure with parent-child relationships:

- **Parent Accounts**: Summary accounts that group related child accounts
- **Child Accounts**: Detailed accounts that roll up to a parent account

Example:
- 1000: Cash (Parent)
  - 1010: Petty Cash (Child)
  - 1020: Operating Account (Child)
  - 1030: Savings Account (Child)

## Managing the Chart of Accounts

### Viewing the Chart of Accounts

1. Navigate to the Chart of Accounts page
2. The accounts are displayed in a hierarchical tree view
3. You can expand or collapse account groups to show or hide child accounts
4. Use the filter options to view specific account types or search for accounts

### Adding a New Account

1. Navigate to the Chart of Accounts page
2. Click the "Add Account" button
3. Fill in the account details:
   - Account Number: Following the numbering system
   - Account Name: A descriptive name
   - Account Type: Asset, Liability, Equity, Revenue, or Expense
   - Parent Account: If this is a child account
   - Description: Optional additional information
   - Status: Active or Inactive
   - Other attributes as needed
4. Click "Save" to create the account

### Editing an Existing Account

1. Navigate to the Chart of Accounts page
2. Find the account you want to edit
3. Click the "Edit" button (pencil icon) next to the account
4. Update the account details
5. Click "Save" to apply the changes

### Deactivating an Account

Instead of deleting accounts (which could affect historical data), you should deactivate them:

1. Navigate to the Chart of Accounts page
2. Find the account you want to deactivate
3. Click the "Edit" button next to the account
4. Change the Status to "Inactive"
5. Click "Save" to apply the change

### Importing and Exporting Accounts

For bulk operations, you can import or export accounts:

1. Navigate to the Chart of Accounts page
2. Click "Import" or "Export" button
3. For imports:
   - Download the template
   - Fill in the account details
   - Upload the completed file
4. For exports:
   - Choose the export format (CSV, Excel)
   - Select the accounts to export
   - Download the file

## Using the Chart of Accounts

### In Journal Entries

When creating journal entries, you'll select accounts from the Chart of Accounts:

1. Navigate to the Journal Entry page
2. Click "New Journal Entry"
3. For each line item, select an account from the Chart of Accounts
4. Enter the debit or credit amount
5. Save the journal entry

### In Financial Reports

The Chart of Accounts structure determines how information is presented in financial reports:

1. Navigate to the Financial Reporting module
2. Generate a report (e.g., Income Statement, Balance Sheet)
3. The report will organize information based on the Chart of Accounts structure

### In Budgeting

When creating budgets, you'll allocate amounts to accounts from the Chart of Accounts:

1. Navigate to the Budget Planning module
2. Create a new budget
3. Allocate budget amounts to specific accounts
4. Save the budget

## Best Practices

1. **Consistent Naming**: Use consistent naming conventions for all accounts
2. **Logical Structure**: Organize accounts in a logical hierarchy that reflects your organization's structure
3. **Appropriate Detail**: Include enough detail to provide useful information, but not so much that it becomes unwieldy
4. **Regular Review**: Periodically review the Chart of Accounts to ensure it still meets your organization's needs
5. **Documentation**: Maintain documentation about the purpose and use of each account
6. **Inactive Accounts**: Deactivate rather than delete accounts that are no longer needed

## Troubleshooting

### Common Issues

1. **Duplicate Accounts**: If you find duplicate accounts, decide which one to keep and deactivate the other
2. **Missing Accounts**: If you need an account that doesn't exist, add it following the numbering system
3. **Incorrect Account Type**: If an account has been assigned the wrong type, create a new account with the correct type and transfer the balances

### Getting Help

If you encounter issues that you cannot resolve, contact your system administrator or the TCM Enterprise Business Suite support team.

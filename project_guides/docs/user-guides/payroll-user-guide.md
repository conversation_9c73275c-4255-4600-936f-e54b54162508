# Payroll User Guide

## 🎉 **Enhanced with TCM Role Integration!**

**New Feature:** Payroll management now includes role-based salary validation, automatic salary suggestions, and TCM code-based salary bands for structured compensation management.

## Overview

The Payroll system manages all aspects of employee compensation, from salary structures to payroll processing. With the new role integration, you can ensure salary equity, automate validation, and maintain compliance with organizational standards.

## 🚀 **Getting Started**

### Accessing Payroll Management

1. **Login** to the TCM Enterprise Suite
2. **Navigate** to: **Payroll**
3. **Choose** your module:
   - **Salary Structures**: Define compensation frameworks
   - **Employee Salaries**: Manage individual employee compensation
   - **Salary Bands**: TCM role-based salary ranges 🆕
   - **Payroll Generation**: Process monthly payrolls
   - **Reports**: Payroll analytics and reporting

### Payroll Dashboard

The payroll dashboard provides:
- **Payroll Statistics**: Total employees, salary costs, pending approvals
- **Role-Based Analytics**: Salary distribution by TCM role 🆕
- **Recent Activities**: Latest salary changes and approvals
- **Quick Actions**: Fast access to common payroll tasks

---

## 💰 **Salary Band Management** 🆕

### Understanding TCM Salary Bands

**What are Salary Bands?**
- Predefined salary ranges for each TCM role code
- Ensure consistent compensation across similar roles
- Provide guidelines for salary negotiations and adjustments
- Support organizational salary equity

**TCM Salary Band Structure:**
- **TCM 1 (Executive)**: MWK 2,500,000 - 3,500,000
- **TCM 2 (Director)**: MWK 2,000,000 - 2,800,000
- **TCM 3 (Manager)**: MWK 1,500,000 - 2,200,000
- **TCM 4 (Senior Officer)**: MWK 1,200,000 - 1,800,000
- **TCM 5 (Officer)**: MWK 800,000 - 1,400,000
- **TCM 7 (Assistant)**: MWK 600,000 - 1,000,000
- **TCM 9 (Support)**: MWK 400,000 - 700,000
- **TCM 10-12 (Specialized Support)**: MWK 250,000 - 600,000

### Managing Salary Bands

**Viewing Salary Bands:**
1. **Navigate** to **Payroll → Salary Bands**
2. **Browse** all TCM salary bands
3. **Filter** by TCM code or salary range
4. **Search** by role name or code

**Salary Band Details:**
- TCM code and role name
- Minimum and maximum salary
- Step increment amounts
- Annual increment percentages
- Effective dates and status

**Updating Salary Bands:**
1. **Select** the salary band to edit
2. **Modify** salary ranges or increments
3. **Set** effective dates for changes
4. **Save** and approve updates

---

## 🏗 **Salary Structure Management**

### Creating Salary Structures

**Step 1: Basic Information**
1. **Navigate** to **Payroll → Salary Structures**
2. **Click** "Create Salary Structure"
3. **Fill** basic details:
   - Structure name (e.g., "TCM Officer Level")
   - Description
   - Currency (MWK)
   - Effective date

**Step 2: Role Assignment** 🆕
- **Select applicable roles** from TCM role list
- **Automatic validation** against role salary bands
- **Warning alerts** for mismatched salary ranges

**Step 3: Salary Components**
- **Basic Salary**: Base compensation amount
- **Allowances**: Housing, transport, medical, etc.
- **Deductions**: Tax, pension, insurance, etc.

**Step 4: Validation and Approval**
- **System validation** against role salary bands
- **Approval workflow** for exceptional cases
- **Activation** of salary structure

### Role-Based Salary Validation 🆕

**Automatic Validation:**
- System checks salary structure against applicable role bands
- Warnings for salaries outside role ranges
- Suggestions for appropriate salary levels

**Validation Process:**
1. **Select roles** for salary structure
2. **Enter basic salary** amount
3. **System validates** against role salary bands
4. **Review warnings** or confirmations
5. **Approve exceptions** if necessary

**Validation Results:**
- ✅ **Within Range**: Salary complies with role band
- ⚠️ **Warning**: Salary slightly outside range
- ❌ **Error**: Salary significantly outside range

---

## 👤 **Employee Salary Management**

### Assigning Salaries to Employees

**Individual Assignment:**
1. **Navigate** to **Payroll → Employee Salaries**
2. **Select** employee
3. **Choose** salary structure
4. **Role-based validation** automatically applied 🆕
5. **Review** salary components
6. **Save** assignment

**Role-Based Suggestions:** 🆕
- System suggests appropriate salary structures based on employee role
- Automatic salary range validation
- Recommended salary amounts within role bands

### Salary Revision Process

**Creating Salary Revisions:**
1. **Select** employee for salary revision
2. **Choose** new salary structure or amount
3. **Role validation** checks new salary against role band 🆕
4. **Document** revision reason
5. **Set** effective date
6. **Submit** for approval

**Approval Workflow:**
- **Automatic approval** for salaries within role bands
- **Manager approval** required for minor exceptions
- **HR Director approval** required for major exceptions

### Bulk Salary Operations

**Bulk Salary Updates:**
1. **Filter** employees by role, department, or criteria
2. **Select** multiple employees
3. **Apply** salary structure or percentage increase
4. **Role validation** for all selected employees 🆕
5. **Review** validation results
6. **Confirm** bulk update

**Role-Based Bulk Updates:** 🆕
- **Update by TCM code**: Apply changes to all employees in specific role
- **Department-wide updates**: Salary adjustments by department
- **Performance-based increases**: Merit increases within role bands

---

## 📊 **Payroll Processing**

### Monthly Payroll Generation

**Payroll Setup:**
1. **Navigate** to **Payroll → Payroll Generation**
2. **Create** new payroll run
3. **Select** payroll period
4. **Choose** employees or departments
5. **Configure** processing options

**Processing Steps:**
1. **Validate** employee salary assignments
2. **Calculate** gross salaries and allowances
3. **Apply** deductions and taxes
4. **Generate** payroll records
5. **Create** payslips
6. **Export** payroll data

**Role-Based Processing:** 🆕
- **Role validation** during payroll generation
- **Salary band compliance** checking
- **Exception reporting** for out-of-band salaries

### Payroll Validation and Approval

**Validation Checks:**
- Employee salary structure assignments
- Role-based salary compliance 🆕
- Tax and deduction calculations
- Bank account information

**Approval Process:**
1. **Review** payroll summary
2. **Check** validation results
3. **Resolve** any issues or exceptions
4. **Approve** payroll for processing
5. **Generate** final payroll reports

---

## 📈 **Reporting and Analytics**

### Standard Payroll Reports

**Payroll Summary:**
- Total payroll costs by period
- Employee count and average salaries
- Department-wise payroll breakdown
- Tax and deduction summaries

**Employee Salary Reports:**
- Individual salary statements
- Salary history and revisions
- Allowance and deduction details
- Year-to-date summaries

### Role-Based Analytics 🆕

**Salary Equity Analysis:**
- Salary distribution by TCM role
- Comparison within role bands
- Gender pay equity analysis
- Department salary comparisons

**Role-Based Reports:**
- **Salary Band Compliance**: Employees within/outside role bands
- **Role Distribution**: Employee count by TCM code
- **Salary Progression**: Career advancement tracking
- **Budget Analysis**: Payroll costs by role level

**Organizational Analytics:**
- **Hierarchy Analysis**: Salary structure by organizational level
- **Career Path Costing**: Salary progression modeling
- **Budget Planning**: Role-based salary projections
- **Compliance Monitoring**: Salary equity tracking

### Custom Reports

**Report Builder:**
1. **Select** data sources (employees, roles, salaries)
2. **Apply** filters (department, role, date range)
3. **Choose** metrics and calculations
4. **Format** output (Excel, PDF, CSV)
5. **Schedule** recurring reports

---

## 🔧 **Advanced Features**

### Salary Band Administration

**Band Management:**
- **Create** new salary bands for new roles
- **Update** existing bands for market adjustments
- **Archive** obsolete bands
- **Import/Export** salary band data

**Market Analysis:**
- **Compare** TCM salary bands with market rates
- **Adjust** bands based on economic factors
- **Document** band revision rationale
- **Implement** phased adjustments

### Integration with HR Modules

**Employee Management:**
- **Automatic** salary suggestions during employee registration
- **Role-based** salary validation in employee forms
- **Career progression** tracking with salary history

**Performance Management:**
- **Merit increases** within role salary bands
- **Performance-based** salary adjustments
- **Promotion** salary calculations

### Compliance and Audit

**Audit Trail:**
- **Track** all salary changes and approvals
- **Document** exception justifications
- **Monitor** role-based compliance
- **Generate** audit reports

**Compliance Monitoring:**
- **Salary equity** analysis by role and gender
- **Role band compliance** tracking
- **Exception reporting** and management
- **Regulatory compliance** documentation

---

## 🚨 **Troubleshooting**

### Common Issues

**"Salary outside role band" Warning:**
- **Cause**: Proposed salary exceeds role salary band
- **Solution**: Adjust salary or document exception justification

**"Role not assigned" Error:**
- **Cause**: Employee doesn't have a formal role assigned
- **Solution**: Assign appropriate TCM role to employee

**"Salary structure mismatch" Alert:**
- **Cause**: Salary structure not applicable to employee's role
- **Solution**: Select appropriate salary structure for role

### Validation Errors

**Role Band Validation:**
- Ensure salary falls within role's salary band
- Document justification for exceptions
- Obtain appropriate approvals for out-of-band salaries

**Salary Structure Validation:**
- Verify salary structure applies to employee's role
- Check effective dates for salary structures
- Ensure all required components are included

---

## 📚 **Best Practices**

### Salary Management

**Consistency:**
- **Apply** salary bands consistently across similar roles
- **Document** all exceptions and justifications
- **Regular** salary equity reviews

**Transparency:**
- **Clear** salary band communication
- **Documented** salary progression criteria
- **Fair** and equitable salary practices

### Role-Based Compensation

**Role Alignment:**
- **Ensure** employee roles match actual responsibilities
- **Regular** role and salary reviews
- **Career path** planning and communication

**Budget Management:**
- **Monitor** payroll costs by role and department
- **Plan** salary adjustments within budget constraints
- **Forecast** future payroll costs based on role projections

---

## 🎯 **Future Enhancements**

### Phase 1: Enhanced Integration
- **Direct role assignment** in payroll workflows
- **Real-time salary validation** during data entry
- **Automated promotion** salary calculations

### Phase 2: Advanced Analytics
- **Predictive salary modeling**
- **Market rate integration**
- **AI-powered salary recommendations**

### Phase 3: Complete Automation
- **Automated payroll processing**
- **Smart exception handling**
- **Integrated performance-salary management**

---

## 📞 **Support and Resources**

### Related Guides
- [Role Management User Guide](role-management-user-guide.md)
- [Employee Management User Guide](employee-management-user-guide.md)
- [HR System User Guide](hr-system-user-guide.md)
- [Bulk Import System Guide](bulk-import-system.md)

### Getting Help
- **Payroll Administrator**: Technical payroll issues
- **HR Department**: Policy and process questions
- **Finance Department**: Budget and compliance matters
- **System Administrator**: Technical system issues

---

**Welcome to the enhanced Payroll Management system with TCM Role Integration!** 🎉

This powerful system ensures fair, consistent, and compliant salary management aligned with your organizational structure and the Teachers Council of Malawi's professional standards.

# Employee Management User Guide

## 🎉 **Enhanced with TCM Role Integration!**

**New Feature Alert:** Employee management now includes integration with the TCM organizational role system, providing structured role assignment and salary validation capabilities.

## Overview

The Employee Management system is your central hub for managing all employee information, from basic profiles to complex organizational relationships. With the new role integration, you can now assign formal organizational roles to employees and benefit from automated salary validation.

## 🚀 **Getting Started**

### Accessing Employee Management

1. **Login** to the TCM Enterprise Suite
2. **Navigate** to: **HR → Employees**
3. **Choose** your action:
   - **View Employees**: Browse the employee directory
   - **Add Employee**: Register a new employee
   - **Bulk Import**: Import multiple employees
   - **Reports**: Generate employee reports

### Dashboard Overview

The employee dashboard provides:
- **Employee Statistics**: Total employees, active/inactive counts
- **Recent Activities**: Latest employee additions and updates
- **Quick Actions**: Fast access to common tasks
- **Role Distribution**: Overview of employees by organizational role

---

## 👤 **Managing Individual Employees**

### Adding a New Employee

**Step 1: Basic Information**
1. **Click** "Add Employee" or "Register Employee"
2. **Fill in required fields:**
   - **First Name** and **Last Name**
   - **Email Address** (must be unique)
   - **Employee ID** (auto-generated or manual)
   - **Phone Number**

**Step 2: Employment Details**
- **Department**: Select from existing departments
- **Position**: Enter job title or select from roles *(Role integration coming soon)*
- **Employment Type**: Full-time, Part-time, Contract, Intern, Temporary
- **Employment Status**: Active, Inactive, On-leave, Terminated
- **Hire Date**: Employee start date
- **Manager**: Select reporting manager (optional)

**Step 3: Personal Information**
- **Date of Birth**
- **Gender**
- **Marital Status**
- **Number of Children**
- **National ID**

**Step 4: Contact & Address**
- **Address, City, State, Country**
- **Village, Traditional Authority, District** (for Malawi-specific locations)
- **Emergency Contact Information**
- **Next of Kin Details**

**Step 5: Financial Information**
- **Salary** (with role-based validation)
- **Bank Name and Account Number**
- **Payment Method**

### Viewing Employee Details

**To view an employee:**
1. **Find** the employee in the list (use search if needed)
2. **Click** on the employee name
3. **Review** comprehensive employee information:
   - Personal and contact details
   - Employment information and history
   - Salary and financial details
   - Documents and attachments
   - Role assignment and organizational position

### Editing Employee Information

**To update employee details:**
1. **Navigate** to the employee's profile
2. **Click** "Edit Employee" button
3. **Modify** the necessary information
4. **Save** changes

**Important Notes:**
- Some changes may require approval (salary changes, role changes)
- System tracks all modifications with timestamps and user information
- Role changes may trigger salary validation warnings

### Employee Status Management

**Status Options:**
- **Active**: Currently employed and working
- **Inactive**: Temporarily not working (unpaid leave, suspension)
- **On-leave**: Temporarily absent (paid leave, medical leave)
- **Terminated**: Employment ended

**To change status:**
1. **Edit** the employee
2. **Select** new employment status
3. **Add termination date** if applicable
4. **Save** changes

---

## 🔍 **Searching and Filtering Employees**

### Search Capabilities

**Search by:**
- **Name**: First name, last name, or full name
- **Employee ID**: Unique identifier
- **Email**: Email address
- **Phone**: Phone number
- **Department**: Department name
- **Position/Role**: Job title or organizational role

### Advanced Filtering

**Filter employees by:**
- **Department**: View employees in specific departments
- **Employment Type**: Full-time, part-time, contract, etc.
- **Employment Status**: Active, inactive, on-leave, terminated
- **Role Level**: Filter by TCM code level (TCM 1, TCM 2, etc.)
- **Hire Date Range**: Employees hired within specific periods
- **Salary Range**: Employees within certain salary brackets

### Sorting Options

**Sort employee list by:**
- **Name** (A-Z or Z-A)
- **Hire Date** (newest or oldest first)
- **Department** (alphabetical)
- **Role Level** (TCM hierarchy)
- **Salary** (highest to lowest or vice versa)

---

## 👔 **Role Management Integration**

### Understanding Employee Roles

**Role vs Position:**
- **Position**: Job title or description (e.g., "Senior Accountant")
- **Role**: Formal organizational role with TCM code (e.g., "Accountant - TCM 5")

**Benefits of Role Assignment:**
- **Salary Validation**: Automatic checking against role salary bands
- **Organizational Clarity**: Clear reporting structure and hierarchy
- **Career Progression**: Defined advancement paths
- **Compliance**: Consistent compensation across similar roles

### Assigning Roles to Employees

**Current Status**: Role assignment is being implemented in the next development phase.

**Coming Soon:**
1. **Role Dropdown**: Select from 31 available TCM roles
2. **Automatic Validation**: System checks role-department compatibility
3. **Salary Suggestions**: Recommended salary ranges based on role
4. **Promotion Tracking**: History of role changes and progressions

### Role-Based Salary Validation

**How it Works:**
1. **Role Selection**: Choose appropriate TCM role for employee
2. **Salary Input**: Enter proposed salary
3. **Validation**: System checks against role salary band
4. **Alerts**: Warnings for out-of-range salaries
5. **Approval**: Override process for exceptional cases

**Salary Band Examples:**
- **TCM 1 (Registrar)**: MWK 2,500,000 - 3,500,000
- **TCM 3 (Manager)**: MWK 1,500,000 - 2,200,000
- **TCM 5 (Officer)**: MWK 800,000 - 1,400,000

---

## 📊 **Employee Reports and Analytics**

### Standard Reports

**Employee Directory:**
- Complete list with contact information
- Filterable by department, role, status
- Exportable to Excel/PDF

**Organizational Chart:**
- Visual representation of reporting structure
- Role-based hierarchy display
- Department-wise organization

**Employee Statistics:**
- Headcount by department and role
- Employment type distribution
- Salary analysis by role level
- Tenure and turnover analysis

### Role-Based Analytics

**New Capabilities:**
- **Role Distribution**: Number of employees per TCM role
- **Salary Equity Analysis**: Compensation comparison within roles
- **Organizational Gaps**: Unfilled or understaffed roles
- **Career Progression**: Role advancement tracking

### Generating Reports

1. **Navigate** to **HR → Reports** or **Employees → Reports**
2. **Select** report type
3. **Configure** filters and parameters
4. **Generate** report
5. **Export** in desired format (Excel, PDF, CSV)

---

## 📥 **Bulk Operations**

### Bulk Employee Import

**Preparation:**
1. **Download** employee import template
2. **Prepare** employee data with all required fields
3. **Validate** department names (must exist in system)
4. **Check** role assignments (when feature is available)

**Import Process:**
1. **Navigate** to **HR → Employees → Bulk Import**
2. **Upload** prepared Excel file
3. **Review** validation results
4. **Confirm** import for successful records
5. **Fix** any errors and re-import failed records

**Important Notes:**
- Departments must be imported before employees
- Email addresses must be unique
- Role assignments will be validated against available roles

### Bulk Updates

**For updating multiple employees:**
1. **Export** current employee data
2. **Modify** the exported file
3. **Import** the updated file
4. **Review** changes before confirming

### Bulk Delete

**To remove multiple employees:**
1. **Select** employees using checkboxes
2. **Click** "Bulk Delete" button
3. **Confirm** deletion (this action cannot be undone)
4. **Review** deletion results

**Warning**: Bulk delete permanently removes employee records. Use with caution.

---

## 🔧 **Advanced Features**

### Employee Documents

**Document Management:**
- **Upload** employee documents (contracts, certificates, etc.)
- **Organize** by document type
- **Version Control**: Track document updates
- **Access Control**: Restrict document visibility

### Employee History Tracking

**Tracked Changes:**
- **Role Changes**: Promotions, transfers, demotions
- **Salary Revisions**: Increases, adjustments, bonuses
- **Status Changes**: Active, inactive, leave, termination
- **Department Transfers**: Movement between departments

### Integration with Other Modules

**Current Integrations:**
- **Payroll**: Automatic salary processing
- **Leave Management**: Leave balance tracking
- **Department Management**: Organizational structure
- **Role Management**: Formal role assignments

**Planned Integrations:**
- **Performance Management**: Role-based evaluations
- **Training**: Role-specific training requirements
- **Recruitment**: Role-based hiring workflows

---

## 🚨 **Troubleshooting**

### Common Issues

**"Email already exists" Error:**
- **Cause**: Another employee has the same email
- **Solution**: Use unique email or update existing employee

**"Department not found" Error:**
- **Cause**: Department doesn't exist in system
- **Solution**: Create department first or check spelling

**"Invalid salary range" Warning:**
- **Cause**: Salary outside role's salary band
- **Solution**: Adjust salary or justify exception

### Data Validation Errors

**Required Field Missing:**
- Ensure all mandatory fields are completed
- Check for empty cells in bulk import files

**Invalid Date Format:**
- Use YYYY-MM-DD format for dates
- Ensure dates are logical (hire date before termination date)

**Invalid Employment Type:**
- Use only: full-time, part-time, contract, intern, temporary
- Check spelling and case sensitivity

---

## 📚 **Best Practices**

### Data Quality

1. **Consistency**: Use standardized formats for names, addresses
2. **Accuracy**: Double-check all employee information
3. **Completeness**: Fill all available fields when possible
4. **Regular Updates**: Keep employee information current

### Security and Privacy

1. **Access Control**: Limit access to sensitive employee data
2. **Data Protection**: Follow privacy regulations and policies
3. **Audit Trail**: Monitor who accesses and modifies employee data
4. **Backup**: Regular data backups and recovery procedures

### Role Assignment Guidelines

1. **Accuracy**: Ensure role matches actual job responsibilities
2. **Consistency**: Apply roles uniformly across similar positions
3. **Documentation**: Maintain records of role assignment rationale
4. **Review**: Periodically review and update role assignments

---

## 🎯 **Next Steps and Future Enhancements**

### Immediate Actions

1. **Explore** the enhanced employee management interface
2. **Review** current employee data for accuracy
3. **Plan** role assignments for existing employees
4. **Train** staff on new role-based features

### Coming Soon

1. **Role Assignment Interface**: Direct role selection in employee forms
2. **Salary Validation**: Real-time salary band checking
3. **Promotion Workflows**: Automated role progression processes
4. **Enhanced Reporting**: Advanced role-based analytics

### Long-term Roadmap

1. **Performance Integration**: Role-based performance management
2. **Career Planning**: Automated career path suggestions
3. **Succession Planning**: Role-based succession management
4. **AI-Powered Insights**: Predictive analytics for HR decisions

---

## 📞 **Support and Resources**

### Related Guides

- [Role Management User Guide](role-management-user-guide.md)
- [Bulk Import System Guide](bulk-import-system.md)
- [Department Management Guide](department-management-user-guide.md)
- [Payroll Integration Guide](payroll-user-guide.md)

### Getting Help

- **System Administrator**: Technical issues and system configuration
- **HR Department**: Process questions and policy clarification
- **User Training**: Schedule training sessions for new features
- **Documentation**: Access comprehensive system documentation

---

**Welcome to the enhanced Employee Management system with TCM Role Integration!** 🎉

This powerful combination provides structured organizational management, automated salary validation, and comprehensive employee lifecycle management tailored specifically for the Teachers Council of Malawi.

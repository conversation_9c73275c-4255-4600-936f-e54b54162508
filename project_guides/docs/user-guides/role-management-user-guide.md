# Role Management User Guide

## 🎉 **Welcome to TCM Role Management System!**

**Congratulations!** The TCM Enterprise Suite now includes a complete role management system with all 31 organizational roles successfully imported and ready for use.

## Overview

The Role Management system allows you to manage your organization's formal roles, assign employees to specific positions, and maintain a clear organizational structure. This system is specifically designed for the Teachers Council of Malawi (TCM) organizational hierarchy.

## 🏢 **TCM Organizational Structure**

Your organization now has the following role hierarchy available:

### **Executive Level**
- **TCM 1**: Registrar (Chief Executive Officer)

### **Director Level**
- **TCM 2**: Director of Registration and Licencing
- **TCM 2**: Director of Compliance Services

### **Manager Level**
- **TCM 3**: Finance Manager, HR Manager, Registration Manager, etc.

### **Officer Levels**
- **TCM 4**: Senior Officers (Registration, Monitoring & Enforcement)
- **TCM 5**: Officers (Accountant, ICT Officer, Investigation Officer, etc.)

### **Assistant & Support Levels**
- **TCM 7**: Assistant positions (Assistant Accountant, Administrative Assistant)
- **TCM 9-12**: Support staff (Receptionist, Drivers, Office Assistant)

---

## 🚀 **Getting Started**

### Accessing Role Management

1. **Login** to the TCM Enterprise Suite
2. **Navigate** to: **HR → Employee → Roles**
3. **View** the complete list of organizational roles

### Understanding the Role Interface

The role management interface provides:
- **Role List**: View all organizational roles
- **Search & Filter**: Find specific roles quickly
- **Role Details**: View comprehensive role information
- **Management Actions**: Create, edit, or manage roles

---

## 📋 **Managing Roles**

### Viewing Role Information

**To view role details:**
1. Go to **HR → Employee → Roles**
2. **Click** on any role name to view details
3. **Review** role information including:
   - Role name and TCM code
   - Department association
   - Role description
   - Status (Active/Inactive)
   - Creation and modification history

### Searching and Filtering Roles

**Search by:**
- **Role Name** (e.g., "Registrar", "Finance Manager")
- **TCM Code** (e.g., "TCM 1", "TCM 5")
- **Department** (e.g., "Management", "Finance Division")

**Filter by:**
- **Active Roles**: Currently available positions
- **Inactive Roles**: Discontinued or suspended positions
- **Department**: Roles within specific departments

### Creating New Roles

**To create a new role:**
1. Click **"Create Role"** button
2. **Fill in required information:**
   - **Name**: Role title (e.g., "Senior Accountant")
   - **Code**: Unique identifier (e.g., "TCM 6")
   - **Description**: Role responsibilities and duties
   - **Department**: Select from existing departments
   - **Status**: Active or Inactive
3. **Save** the role

**Best Practices:**
- Use clear, descriptive role names
- Follow TCM coding conventions (TCM X format)
- Provide detailed role descriptions
- Ensure department associations are correct

### Editing Existing Roles

**To edit a role:**
1. **Find** the role in the list
2. **Click** the **"Edit"** button (pencil icon)
3. **Modify** the information as needed
4. **Save** changes

**Note**: Be careful when editing roles that are already assigned to employees.

### Managing Role Status

**Activating/Deactivating Roles:**
- **Active**: Role is available for employee assignment
- **Inactive**: Role is hidden from assignment but preserved for historical records

**To change status:**
1. **Edit** the role
2. **Toggle** the "Is Active" setting
3. **Save** changes

---

## 👥 **Role-Employee Integration**

### Assigning Roles to Employees

**Current Status**: Role assignment to employees is being implemented in the next phase.

**Coming Soon:**
- Employee forms will include role dropdown selection
- Automatic salary suggestions based on role
- Role-based employee filtering and reporting

### Role-Based Salary Management

**Available Features:**
- **Salary Band Validation**: Roles are linked to salary ranges
- **Automatic Suggestions**: System suggests appropriate salaries based on role
- **Compliance Monitoring**: Ensures salary equity across similar roles

**How it Works:**
1. Each TCM code has associated salary bands
2. When assigning salaries, the system validates against role ranges
3. Out-of-range salaries trigger warnings for review

---

## 📊 **Reporting and Analytics**

### Role-Based Reports

**Available Reports:**
- **Organizational Chart**: Visual representation of role hierarchy
- **Role Distribution**: Number of employees per role
- **Department-Role Matrix**: Roles within each department
- **Salary Analysis**: Compensation analysis by role

### Accessing Reports

1. **Navigate** to **HR → Reports**
2. **Select** role-based report type
3. **Configure** filters (department, role level, etc.)
4. **Generate** and download reports

---

## 📥 **Bulk Operations**

### Bulk Import (Already Completed)

✅ **Status**: All 31 TCM roles have been successfully imported!

**For Future Imports:**
1. **Navigate** to **HR → Employee → Roles**
2. **Click** "Bulk Import"
3. **Download** template with proper headers
4. **Fill** template with role data
5. **Upload** and review results

### Bulk Export

**To export role data:**
1. **Go** to role management page
2. **Click** "Export" button
3. **Select** export format (Excel, CSV)
4. **Download** the file

### Bulk Updates

**For multiple role updates:**
1. **Export** current role data
2. **Modify** the exported file
3. **Import** the updated file
4. **Review** changes before confirming

---

## 🔧 **Advanced Features**

### Role Permissions (Future Enhancement)

**Planned Features:**
- Role-based system permissions
- Access control based on organizational level
- Workflow approvals based on role hierarchy

### Role Progression Tracking

**Coming Soon:**
- Career path mapping
- Promotion workflow automation
- Role history tracking for employees

### Integration with Other Modules

**Current Integrations:**
- **Department Management**: Roles linked to departments
- **Payroll System**: Salary band validation

**Planned Integrations:**
- **Performance Management**: Role-based evaluation criteria
- **Training System**: Role-specific training requirements
- **Recruitment**: Role-based job postings

---

## 🚨 **Troubleshooting**

### Common Issues

**"Role not found" Error:**
- **Cause**: Role may have been deactivated or deleted
- **Solution**: Check inactive roles or contact administrator

**"Department mismatch" Warning:**
- **Cause**: Role assigned to wrong department
- **Solution**: Edit role and correct department association

**"Salary out of range" Alert:**
- **Cause**: Proposed salary outside role's salary band
- **Solution**: Review salary bands or justify exception

### Getting Help

**For Technical Issues:**
- Contact your system administrator
- Check the troubleshooting guide
- Review system logs for error details

**For Process Questions:**
- Consult your HR department
- Review organizational policies
- Contact TCM management for clarification

---

## 📚 **Best Practices**

### Role Management Guidelines

1. **Consistency**: Use standardized naming conventions
2. **Accuracy**: Ensure role descriptions match actual responsibilities
3. **Regular Review**: Periodically review and update role information
4. **Documentation**: Maintain clear records of role changes

### Data Quality

1. **Validation**: Always validate role-department associations
2. **Completeness**: Ensure all required fields are filled
3. **Accuracy**: Double-check TCM codes and role names
4. **Backup**: Regular exports for data backup

### Security Considerations

1. **Access Control**: Only authorized users should manage roles
2. **Change Tracking**: Monitor who makes role changes
3. **Approval Process**: Implement approval workflows for role changes
4. **Audit Trail**: Maintain logs of all role modifications

---

## 🎯 **Next Steps**

### Immediate Actions

1. **Explore** the role management interface
2. **Review** all imported TCM roles
3. **Verify** role-department associations
4. **Plan** employee-role assignments

### Upcoming Features

1. **Employee-Role Linking**: Assign roles to employees
2. **Salary Band Implementation**: Complete salary validation system
3. **Advanced Reporting**: Enhanced role-based analytics
4. **Workflow Automation**: Role-based approval processes

### Training and Support

1. **User Training**: Schedule training sessions for HR staff
2. **Documentation Review**: Familiarize with all user guides
3. **System Testing**: Test role management workflows
4. **Feedback Collection**: Provide feedback for system improvements

---

## 📞 **Support and Resources**

### Documentation Links

- [Bulk Import System Guide](bulk-import-system.md)
- [Employee Management Guide](employee-management-user-guide.md)
- [Quick Reference Guide](../quick-reference/bulk-import-quick-guide.md)
- [Troubleshooting Guide](../../BULK_IMPORT_TROUBLESHOOTING.md)

### Contact Information

- **System Administrator**: [Contact Details]
- **HR Department**: [Contact Details]
- **Technical Support**: [Contact Details]

---

**Congratulations on successfully implementing the TCM Role Management System!** 🎉

This system provides the foundation for structured organizational management, role-based salary administration, and comprehensive HR analytics. Explore the features and take advantage of the powerful capabilities now available to your organization.

# Bulk Import System User Guide

## 🎉 **Latest Update: TCM Roles Successfully Imported!**

**Achievement Unlocked (December 2024):**
- ✅ **31 TCM Organizational Roles** imported successfully
- ✅ **Complete role hierarchy** from TCM 1 (Registrar) to TCM 12 (Office Assistant)
- ✅ **Department-role mapping** aligned with TCM organizational structure
- ✅ **Role-payroll integration** framework ready for salary management

## Overview

The TCM Enterprise Suite provides a powerful bulk import system that allows you to quickly import large amounts of employee, department, and role data using Excel or CSV files. This guide will walk you through the process step-by-step.

## 🔄 **Import Order & Dependencies**

### **IMPORTANT: Import Order Matters!**

**1. Import Departments FIRST** 📁
**2. Import Roles SECOND** 👔 ✅ **COMPLETED**
**3. Import Employees THIRD** 👥

**Why this order?**
- Employees must be assigned to existing departments
- Roles must reference existing departments for proper organizational structure
- The system validates that each employee's department exists in the database
- Role-based salary management requires roles to be defined first
- If a department doesn't exist, the employee/role import will fail for that record

**✅ TCM Roles Import Status:**
- **COMPLETED**: All 31 TCM organizational roles successfully imported
- **READY**: Role-based employee assignment and salary management available

---

## 👔 **Role Import Guide** ✅ **COMPLETED**

### 🎉 **TCM Roles Successfully Imported!**

**Status**: All 31 TCM organizational roles have been successfully imported into the system.

**What was imported:**
- **Executive Level**: TCM 1 (Registrar)
- **Director Level**: TCM 2 (Directors of Registration, Compliance)
- **Manager Level**: TCM 3 (Finance Manager, HR Manager, etc.)
- **Officer Levels**: TCM 4-5 (Senior Officers, Officers, Specialists)
- **Assistant Level**: TCM 7 (Assistant positions)
- **Support Levels**: TCM 9-12 (Support staff, drivers, office assistants)

**Benefits Now Available:**
- ✅ **Role-based employee assignment** - Assign employees to formal organizational roles
- ✅ **Salary band validation** - Automatic salary range validation based on role
- ✅ **Organizational reporting** - Role-based analytics and reporting
- ✅ **Promotion workflows** - Structured role progression tracking
- ✅ **Complete TCM hierarchy** - All 31 roles from Executive to Support levels
- ✅ **Department integration** - Roles properly mapped to organizational departments
- ✅ **Salary band framework** - TCM code-based compensation structure

### How Roles Import Works (For Future Reference)

**Step 1: Navigate to Roles Management**
- Go to: **HR → Employee → Roles**
- Click: **"Bulk Import"** button

**Step 2: Download Template**
- Click: **"Download Template"** tab
- Download the Excel template with proper headers

**Step 3: Prepare Role Data**
Required columns:
- ✅ **Name** (e.g., "Registrar", "Finance Manager")
- ✅ **Code** (e.g., "TCM 1", "TCM 3")
- ✅ **Description** (role responsibilities)
- ✅ **Department** (must match existing department names)

**Step 4: Upload and Import**
- Upload completed Excel file
- Review import results
- Verify all roles imported successfully

**Step 5: Verify Import**
- Check roles list for all imported roles
- Verify department associations
- Test role assignment to employees

### 🔗 **Role-Employee Integration Workflow**

**Now that roles are imported, here's how they integrate with employee management:**

**1. Employee Registration Enhancement:**
- Employee forms will include role dropdown selection
- Automatic department validation based on role
- Salary suggestions based on role salary bands

**2. Salary Validation Process:**
- System checks proposed salary against role salary band
- Warnings for out-of-range salaries
- Approval workflow for salary exceptions

**3. Organizational Reporting:**
- Employee distribution by role
- Salary equity analysis by role
- Organizational chart with role hierarchy

**4. Career Progression Tracking:**
- Role change history for employees
- Promotion workflow automation
- Career path visualization

---

## 📁 **Department Import Guide**

### Step 1: Prepare Your Department Data

**Required Fields:**
- **Name** - Department name (e.g., "Finance Division")
- **Description** - Brief description of the department

**Optional Fields:**
- **Department Code** - Unique identifier (e.g., "FIN001")
- **Budget Allocation** - Annual budget amount
- **Head of Department** - Department head title (e.g., "Director of Finance")
- **Location** - Physical location
- **Contact Email** - Department contact email
- **Contact Phone** - Department contact phone
- **Established Date** - When the department was created
- **Status** - Department status (active/inactive)

### Step 2: Download Department Template

1. Navigate to **HR → Departments**
2. Click **"Bulk Upload"** button
3. Click **"Download Template"** tab
4. Click **"Download Template"** button
5. Save the Excel file to your computer

### Step 3: Fill in Your Data

1. Open the downloaded template in Excel
2. Fill in your department data row by row
3. **Do not modify the column headers**
4. **Do not leave the Name field empty** (required)
5. Save the file when complete

### Step 4: Upload Department Data

1. Go back to **HR → Departments**
2. Click **"Bulk Upload"** button
3. In the **"Upload"** tab, click **"Upload a file"**
4. Select your completed Excel file
5. Click **"Upload and Process"**
6. Wait for the upload to complete
7. Review the results in the **"Results"** tab

### Step 5: Verify Department Import

- Check the **"Results"** tab for import statistics
- Verify all departments were imported successfully
- If any departments failed, fix the issues and re-import

---

## 👥 **Employee Import Guide**

### Step 1: Ensure Departments Exist

**⚠️ CRITICAL: Import departments first!**

Before importing employees, verify that all departments referenced in your employee data already exist in the system.

### Step 2: Prepare Your Employee Data

**Required Fields:**
- **First Name** - Employee's first name
- **Last Name** - Employee's last name
- **Email** - Unique email address
- **Position** - Job title/position
- **Employment Type** - full-time, part-time, contract, intern
- **Department** - Must match existing department name exactly
- **Hire Date** - Date of employment (YYYY-MM-DD format)

**Optional Fields:**
- **Phone** - Contact phone number
- **Date of Birth** - Employee's birth date
- **Gender** - Male/Female
- **Marital Status** - married, single, divorced, widowed
- **Number of Children** - Number of children
- **Employment Status** - Defaults to "active" if not specified
- **Salary** - Annual salary amount
- **Address, City, State, Country** - Location information
- **Village, Traditional Authority, District** - Local location data
- **National ID** - National identification number
- **Next of Kin** information (Name, Relationship, Phone, Address)
- **Emergency Contact** information (Name, Phone, Relationship)
- **Notes** - Additional information

### Step 3: Download Employee Template

1. Navigate to **HR → Employees**
2. Click **"Bulk Upload"** button
3. Click **"Download Template"** tab
4. Click **"Download Template"** button
5. Save the Excel file to your computer

### Step 4: Fill in Your Data

1. Open the downloaded template in Excel
2. Fill in your employee data row by row
3. **Ensure department names match exactly** (case-insensitive)
4. **Use proper date formats** (YYYY-MM-DD)
5. **Do not modify column headers**
6. Save the file when complete

### Step 5: Upload Employee Data

1. Go back to **HR → Employees**
2. Click **"Bulk Upload"** button
3. In the **"Upload"** tab, click **"Upload a file"**
4. Select your completed Excel file
5. Click **"Upload and Process"**
6. Wait for the upload to complete
7. Review the results in the **"Results"** tab

### Step 6: Review Import Results

The system will show detailed results including:
- **Total employees processed**
- **Successfully imported employees**
- **Failed imports with reasons**
- **Department mismatches** (if any)

---

## 🔍 **Department Validation**

### How Department Matching Works

The system uses **case-insensitive matching** for department names:

✅ **These all match "Finance Division":**
- "finance division"
- "FINANCE DIVISION"
- "Finance Division"
- "FiNaNcE dIvIsIoN"

❌ **These do NOT match:**
- "Finance Dept" (different name)
- "Finance Department" (different name)
- "Finance" (incomplete name)

### If Department Doesn't Exist

When an employee's department doesn't exist:
1. **Employee is skipped** (not imported)
2. **Error is logged** with details
3. **Available departments are shown** for reference
4. **You can fix and re-import** the failed records

---

## 📊 **Import Results & Feedback**

### Success Indicators
- ✅ **Green checkmarks** for successful imports
- **Total count** of successfully imported records
- **Employee/Department details** for successful imports

### Error Indicators
- ❌ **Red X marks** for failed imports
- **Detailed error messages** explaining why import failed
- **Row numbers** indicating which records failed
- **Suggested fixes** for common issues

### Department Mismatch Report
- **Employee name** and **invalid department**
- **List of available departments** to choose from
- **Row number** for easy reference in your Excel file

---

## 💡 **Best Practices**

### Before You Start
1. **Plan your import order**: Departments first, then employees
2. **Clean your data**: Remove duplicates and fix formatting issues
3. **Backup existing data**: Export current data before bulk import
4. **Test with small batches**: Try 5-10 records first

### Data Preparation Tips
1. **Use consistent naming**: Standardize department names
2. **Validate email addresses**: Ensure they're unique and properly formatted
3. **Check date formats**: Use YYYY-MM-DD format consistently
4. **Verify required fields**: Don't leave required fields empty

### During Import
1. **Don't close the browser**: Wait for import to complete
2. **Review results carefully**: Check all success/failure messages
3. **Fix errors immediately**: Address failed imports right away
4. **Verify data accuracy**: Spot-check imported records

### After Import
1. **Verify relationships**: Ensure employees are assigned to correct departments
2. **Check data integrity**: Verify all information imported correctly
3. **Update any missing information**: Add any data that couldn't be bulk imported
4. **Train users**: Show staff how to access and update their information

---

## 🚨 **Common Issues & Solutions**

### "Missing required columns" Error
**Problem**: Excel file is missing required column headers
**Solution**: Download fresh template and ensure all required columns are present

### "Department not found" Error
**Problem**: Employee's department doesn't exist in system
**Solution**: Import departments first, or fix department name in Excel file

### "Invalid employment type" Error
**Problem**: Employment type not recognized
**Solution**: Use only: full-time, part-time, contract, intern, temporary

### "Invalid date format" Error
**Problem**: Dates not in correct format
**Solution**: Use YYYY-MM-DD format (e.g., 2023-01-15)

### "Duplicate email address" Error
**Problem**: Email address already exists in system
**Solution**: Use unique email addresses for each employee

---

## 📞 **Getting Help**

If you encounter issues:
1. **Check this guide** for common solutions
2. **Review error messages** carefully - they often contain the solution
3. **Contact your system administrator** for technical issues
4. **Contact IT support** for system access problems

Remember: **Departments must be imported before employees!** This is the most important rule for successful bulk imports.

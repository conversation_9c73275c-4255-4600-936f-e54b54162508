# Payroll Module API Reference

This document provides detailed information about the API endpoints available in the Payroll Module.

## Tax Brackets

### Get Tax Brackets

**Endpoint:** `GET /api/payroll/tax-brackets`

**Description:** Get tax brackets with optional filtering.

**Query Parameters:**
- `country` (string, optional): Filter by country (default: 'Malawi')
- `currency` (string, optional): Filter by currency (default: 'MWK')
- `isActive` (boolean, optional): Filter by active status

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "60f1a5b3e6b3f32b4c9a1234",
      "country": "Malawi",
      "currency": "MWK",
      "effectiveDate": "2023-01-01T00:00:00.000Z",
      "expiryDate": null,
      "isActive": true,
      "brackets": [
        {
          "lowerLimit": 0,
          "upperLimit": 150000,
          "rate": 0,
          "isExempt": true
        },
        {
          "lowerLimit": 150000,
          "upperLimit": 500000,
          "rate": 25,
          "isExempt": false
        },
        {
          "lowerLimit": 500000,
          "upperLimit": 2550000,
          "rate": 30,
          "isExempt": false
        },
        {
          "lowerLimit": 2550000,
          "upperLimit": null,
          "rate": 35,
          "isExempt": false
        }
      ],
      "createdBy": {
        "_id": "60f1a5b3e6b3f32b4c9a1234",
        "name": "Admin User"
      },
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### Create Tax Bracket

**Endpoint:** `POST /api/payroll/tax-brackets`

**Description:** Create a new tax bracket.

**Request Body:**
```json
{
  "country": "Malawi",
  "currency": "MWK",
  "effectiveDate": "2023-01-01T00:00:00.000Z",
  "expiryDate": null,
  "isActive": true,
  "brackets": [
    {
      "lowerLimit": 0,
      "upperLimit": 150000,
      "rate": 0,
      "isExempt": true
    },
    {
      "lowerLimit": 150000,
      "upperLimit": 500000,
      "rate": 25,
      "isExempt": false
    },
    {
      "lowerLimit": 500000,
      "upperLimit": 2550000,
      "rate": 30,
      "isExempt": false
    },
    {
      "lowerLimit": 2550000,
      "upperLimit": null,
      "rate": 35,
      "isExempt": false
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tax bracket created successfully",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "country": "Malawi",
    "currency": "MWK",
    "effectiveDate": "2023-01-01T00:00:00.000Z",
    "expiryDate": null,
    "isActive": true,
    "brackets": [
      {
        "lowerLimit": 0,
        "upperLimit": 150000,
        "rate": 0,
        "isExempt": true
      },
      {
        "lowerLimit": 150000,
        "upperLimit": 500000,
        "rate": 25,
        "isExempt": false
      },
      {
        "lowerLimit": 500000,
        "upperLimit": 2550000,
        "rate": 30,
        "isExempt": false
      },
      {
        "lowerLimit": 2550000,
        "upperLimit": null,
        "rate": 35,
        "isExempt": false
      }
    ],
    "createdBy": "60f1a5b3e6b3f32b4c9a1234",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get Tax Bracket by ID

**Endpoint:** `GET /api/payroll/tax-brackets/:id`

**Description:** Get a tax bracket by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "country": "Malawi",
    "currency": "MWK",
    "effectiveDate": "2023-01-01T00:00:00.000Z",
    "expiryDate": null,
    "isActive": true,
    "brackets": [
      {
        "lowerLimit": 0,
        "upperLimit": 150000,
        "rate": 0,
        "isExempt": true
      },
      {
        "lowerLimit": 150000,
        "upperLimit": 500000,
        "rate": 25,
        "isExempt": false
      },
      {
        "lowerLimit": 500000,
        "upperLimit": 2550000,
        "rate": 30,
        "isExempt": false
      },
      {
        "lowerLimit": 2550000,
        "upperLimit": null,
        "rate": 35,
        "isExempt": false
      }
    ],
    "createdBy": {
      "_id": "60f1a5b3e6b3f32b4c9a1234",
      "name": "Admin User"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Update Tax Bracket

**Endpoint:** `PATCH /api/payroll/tax-brackets/:id`

**Description:** Update a tax bracket.

**Request Body:**
```json
{
  "isActive": false,
  "expiryDate": "2023-12-31T00:00:00.000Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tax bracket updated successfully",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "country": "Malawi",
    "currency": "MWK",
    "effectiveDate": "2023-01-01T00:00:00.000Z",
    "expiryDate": "2023-12-31T00:00:00.000Z",
    "isActive": false,
    "brackets": [
      {
        "lowerLimit": 0,
        "upperLimit": 150000,
        "rate": 0,
        "isExempt": true
      },
      {
        "lowerLimit": 150000,
        "upperLimit": 500000,
        "rate": 25,
        "isExempt": false
      },
      {
        "lowerLimit": 500000,
        "upperLimit": 2550000,
        "rate": 30,
        "isExempt": false
      },
      {
        "lowerLimit": 2550000,
        "upperLimit": null,
        "rate": 35,
        "isExempt": false
      }
    ],
    "createdBy": {
      "_id": "60f1a5b3e6b3f32b4c9a1234",
      "name": "Admin User"
    },
    "updatedBy": {
      "_id": "60f1a5b3e6b3f32b4c9a1234",
      "name": "Admin User"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Delete Tax Bracket

**Endpoint:** `DELETE /api/payroll/tax-brackets/:id`

**Description:** Delete a tax bracket.

**Response:**
```json
{
  "success": true,
  "message": "Tax bracket deleted successfully"
}
```

### Create Default Tax Brackets

**Endpoint:** `POST /api/payroll/tax-brackets/default`

**Description:** Create default tax brackets for Malawi.

**Response:**
```json
{
  "success": true,
  "message": "Default tax brackets created successfully",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "country": "Malawi",
    "currency": "MWK",
    "effectiveDate": "2023-01-01T00:00:00.000Z",
    "expiryDate": null,
    "isActive": true,
    "brackets": [
      {
        "lowerLimit": 0,
        "upperLimit": 150000,
        "rate": 0,
        "isExempt": true
      },
      {
        "lowerLimit": 150000,
        "upperLimit": 500000,
        "rate": 25,
        "isExempt": false
      },
      {
        "lowerLimit": 500000,
        "upperLimit": 2550000,
        "rate": 30,
        "isExempt": false
      },
      {
        "lowerLimit": 2550000,
        "upperLimit": null,
        "rate": 35,
        "isExempt": false
      }
    ],
    "createdBy": "60f1a5b3e6b3f32b4c9a1234",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Payroll Runs

### Get Payroll Runs

**Endpoint:** `GET /api/payroll/runs`

**Description:** Get payroll runs with optional filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10)
- `period` (string, optional): Filter by period (format: 'YYYY-MM')
- `status` (string, optional): Filter by status
- `fromDate` (date, optional): Filter by creation date (from)
- `toDate` (date, optional): Filter by creation date (to)
- `sortBy` (string, optional): Field to sort by (default: 'createdAt')
- `sortOrder` (string, optional): Sort order ('asc' or 'desc', default: 'desc')

**Response:**
```json
{
  "success": true,
  "data": {
    "docs": [
      {
        "_id": "60f1a5b3e6b3f32b4c9a1234",
        "name": "January 2023 Payroll",
        "description": "Monthly payroll for January 2023",
        "payPeriod": {
          "month": 1,
          "year": 2023,
          "startDate": "2023-01-01T00:00:00.000Z",
          "endDate": "2023-01-31T00:00:00.000Z"
        },
        "status": "completed",
        "totalEmployees": 50,
        "processedEmployees": 50,
        "totalGrossSalary": 5000000,
        "totalDeductions": 1000000,
        "totalTax": 800000,
        "totalNetSalary": 3200000,
        "currency": "MWK",
        "createdBy": {
          "_id": "60f1a5b3e6b3f32b4c9a1234",
          "name": "Admin User"
        },
        "processedAt": "2023-01-25T00:00:00.000Z",
        "createdAt": "2023-01-25T00:00:00.000Z",
        "updatedAt": "2023-01-25T00:00:00.000Z"
      }
    ],
    "totalDocs": 12,
    "page": 1,
    "totalPages": 2,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### Create Payroll Run

**Endpoint:** `POST /api/payroll/runs`

**Description:** Create a new payroll run.

**Request Body:**
```json
{
  "name": "January 2023 Payroll",
  "description": "Monthly payroll for January 2023",
  "payPeriod": {
    "month": 1,
    "year": 2023,
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-01-31T00:00:00.000Z"
  },
  "departments": ["60f1a5b3e6b3f32b4c9a1234", "60f1a5b3e6b3f32b4c9a5678"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll run created successfully",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "draft",
    "totalEmployees": 0,
    "processedEmployees": 0,
    "totalGrossSalary": 0,
    "totalDeductions": 0,
    "totalTax": 0,
    "totalNetSalary": 0,
    "currency": "MWK",
    "departments": ["60f1a5b3e6b3f32b4c9a1234", "60f1a5b3e6b3f32b4c9a5678"],
    "createdBy": "60f1a5b3e6b3f32b4c9a1234",
    "createdAt": "2023-01-25T00:00:00.000Z",
    "updatedAt": "2023-01-25T00:00:00.000Z"
  }
}
```

### Get Payroll Run by ID

**Endpoint:** `GET /api/payroll/runs/:id`

**Description:** Get a payroll run by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "completed",
    "totalEmployees": 50,
    "processedEmployees": 50,
    "totalGrossSalary": 5000000,
    "totalDeductions": 1000000,
    "totalTax": 800000,
    "totalNetSalary": 3200000,
    "currency": "MWK",
    "departments": [
      {
        "_id": "60f1a5b3e6b3f32b4c9a1234",
        "name": "Finance"
      },
      {
        "_id": "60f1a5b3e6b3f32b4c9a5678",
        "name": "HR"
      }
    ],
    "createdBy": {
      "_id": "60f1a5b3e6b3f32b4c9a1234",
      "name": "Admin User"
    },
    "processedAt": "2023-01-25T00:00:00.000Z",
    "createdAt": "2023-01-25T00:00:00.000Z",
    "updatedAt": "2023-01-25T00:00:00.000Z"
  }
}
```

### Process Payroll Run

**Endpoint:** `PATCH /api/payroll/runs/:id`

**Description:** Process a payroll run.

**Request Body:**
```json
{
  "action": "process"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll run process successful",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "completed",
    "totalEmployees": 50,
    "processedEmployees": 50,
    "totalGrossSalary": 5000000,
    "totalDeductions": 1000000,
    "totalTax": 800000,
    "totalNetSalary": 3200000,
    "currency": "MWK",
    "processedAt": "2023-01-25T00:00:00.000Z",
    "updatedAt": "2023-01-25T00:00:00.000Z"
  }
}
```

### Approve Payroll Run

**Endpoint:** `PATCH /api/payroll/runs/:id`

**Description:** Approve a payroll run.

**Request Body:**
```json
{
  "action": "approve"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll run approve successful",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "approved",
    "totalEmployees": 50,
    "processedEmployees": 50,
    "totalGrossSalary": 5000000,
    "totalDeductions": 1000000,
    "totalTax": 800000,
    "totalNetSalary": 3200000,
    "currency": "MWK",
    "approvedBy": "60f1a5b3e6b3f32b4c9a1234",
    "approvedAt": "2023-01-26T00:00:00.000Z",
    "processedAt": "2023-01-25T00:00:00.000Z",
    "updatedAt": "2023-01-26T00:00:00.000Z"
  }
}
```

### Mark Payroll Run as Paid

**Endpoint:** `PATCH /api/payroll/runs/:id`

**Description:** Mark a payroll run as paid.

**Request Body:**
```json
{
  "action": "pay"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll run pay successful",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "paid",
    "totalEmployees": 50,
    "processedEmployees": 50,
    "totalGrossSalary": 5000000,
    "totalDeductions": 1000000,
    "totalTax": 800000,
    "totalNetSalary": 3200000,
    "currency": "MWK",
    "paidAt": "2023-01-27T00:00:00.000Z",
    "approvedBy": "60f1a5b3e6b3f32b4c9a1234",
    "approvedAt": "2023-01-26T00:00:00.000Z",
    "processedAt": "2023-01-25T00:00:00.000Z",
    "updatedAt": "2023-01-27T00:00:00.000Z"
  }
}
```

### Cancel Payroll Run

**Endpoint:** `PATCH /api/payroll/runs/:id`

**Description:** Cancel a payroll run.

**Request Body:**
```json
{
  "action": "cancel",
  "reason": "Incorrect pay period"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll run cancel successful",
  "data": {
    "_id": "60f1a5b3e6b3f32b4c9a1234",
    "name": "January 2023 Payroll",
    "description": "Monthly payroll for January 2023",
    "payPeriod": {
      "month": 1,
      "year": 2023,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-01-31T00:00:00.000Z"
    },
    "status": "cancelled",
    "notes": "Incorrect pay period",
    "totalEmployees": 50,
    "processedEmployees": 50,
    "totalGrossSalary": 5000000,
    "totalDeductions": 1000000,
    "totalTax": 800000,
    "totalNetSalary": 3200000,
    "currency": "MWK",
    "updatedAt": "2023-01-25T00:00:00.000Z"
  }
}
```

## Salary Calculation

### Calculate Salary

**Endpoint:** `POST /api/payroll/calculate-salary`

**Description:** Calculate salary for an employee.

**Request Body:**
```json
{
  "employeeId": "60f1a5b3e6b3f32b4c9a1234",
  "payPeriod": {
    "month": 1,
    "year": 2023
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "employee": {
      "id": "60f1a5b3e6b3f32b4c9a1234",
      "name": "John Doe",
      "employeeNumber": "EMP001"
    },
    "components": [
      {
        "name": "Basic Salary",
        "type": "basic",
        "amount": 500000,
        "isTaxable": true
      },
      {
        "name": "Housing Allowance",
        "type": "allowance",
        "amount": 100000,
        "isTaxable": true
      },
      {
        "name": "Transport Allowance",
        "type": "allowance",
        "amount": 50000,
        "isTaxable": true
      },
      {
        "name": "PAYE Tax",
        "type": "tax",
        "amount": 125000,
        "isTaxable": false
      },
      {
        "name": "Pension Contribution",
        "type": "deduction",
        "amount": 25000,
        "isTaxable": false
      }
    ],
    "grossSalary": 650000,
    "totalDeductions": 150000,
    "totalTax": 125000,
    "netSalary": 500000,
    "currency": "MWK"
  }
}
```

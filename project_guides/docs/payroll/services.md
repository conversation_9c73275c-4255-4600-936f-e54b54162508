# Payroll Module Services

This document provides detailed information about the services used in the Payroll Module.

## TaxService

The `TaxService` calculates taxes based on Malawi regulations.

### Methods

#### calculatePAYE

Calculates PAYE tax based on Malawi tax brackets.

```typescript
async calculatePAYE(taxableAmount: number, country: string = 'Malawi', currency: string = 'MWK'): Promise<number>
```

**Parameters:**
- `taxableAmount`: The taxable amount
- `country`: The country code (default: 'Malawi')
- `currency`: The currency code (default: 'MWK')

**Returns:**
- The calculated tax amount

**Description:**
1. Gets the active tax bracket for the country and currency
2. Calculates tax based on the brackets
3. Returns the total tax amount

#### getActiveTaxBracket

Gets the active tax bracket for a country and currency.

```typescript
async getActiveTaxBracket(country: string, currency: string): Promise<ITaxBracket | null>
```

**Parameters:**
- `country`: The country code
- `currency`: The currency code

**Returns:**
- The active tax bracket or null if not found

**Description:**
1. Finds the most recent active tax bracket for the country and currency
2. Returns the tax bracket or null if not found

#### calculateTaxFromBrackets

Calculates tax from brackets.

```typescript
private calculateTaxFromBrackets(
  taxableAmount: number,
  brackets: Array<{
    lowerLimit: number;
    upperLimit?: number;
    rate: number;
    isExempt: boolean;
  }>
): number
```

**Parameters:**
- `taxableAmount`: The taxable amount
- `brackets`: The tax brackets

**Returns:**
- The calculated tax amount

**Description:**
1. Sorts brackets by lower limit
2. Calculates tax for each bracket
3. Returns the total tax amount

#### createDefaultMalawiTaxBrackets

Creates default Malawi tax brackets.

```typescript
async createDefaultMalawiTaxBrackets(userId: string): Promise<ITaxBracket>
```

**Parameters:**
- `userId`: The user ID creating the brackets

**Returns:**
- The created tax bracket

**Description:**
1. Checks if brackets already exist
2. Creates new tax brackets based on Malawi regulations
3. Returns the created tax bracket

#### calculatePensionContribution

Calculates pension contribution.

```typescript
calculatePensionContribution(pensionableAmount: number, rate: number = 5): number
```

**Parameters:**
- `pensionableAmount`: The pensionable amount
- `rate`: The contribution rate (default: 5%)

**Returns:**
- The calculated pension contribution

**Description:**
1. Calculates pension contribution based on the pensionable amount and rate
2. Returns the pension contribution amount

## SalaryCalculationService

The `SalaryCalculationService` calculates employee salaries.

### Methods

#### calculateSalary

Calculates salary for an employee.

```typescript
async calculateSalary(employeeId: string, payPeriod: { month: number; year: number }): Promise<{
  components: IPayrollComponent[];
  grossSalary: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
  currency: string;
}>
```

**Parameters:**
- `employeeId`: The employee ID
- `payPeriod`: The pay period (month and year)

**Returns:**
- An object containing the calculated salary components, gross salary, total deductions, total tax, net salary, and currency

**Description:**
1. Gets the employee salary
2. Gets the salary structure
3. Calculates the basic salary and allowances
4. Calculates the gross salary and taxable amount
5. Calculates the tax
6. Calculates the deductions
7. Calculates the net salary
8. Returns the calculated salary details

#### getEmployeeSalary

Gets the active employee salary.

```typescript
async getEmployeeSalary(employeeId: string): Promise<IEmployeeSalary | null>
```

**Parameters:**
- `employeeId`: The employee ID

**Returns:**
- The active employee salary or null if not found

**Description:**
1. Finds the active employee salary for the employee
2. Returns the employee salary or null if not found

#### calculateYearToDateTotals

Calculates year-to-date salary totals.

```typescript
async calculateYearToDateTotals(employeeId: string, year: number, upToMonth: number): Promise<{
  grossSalary: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
}>
```

**Parameters:**
- `employeeId`: The employee ID
- `year`: The year
- `upToMonth`: The month (1-12)

**Returns:**
- An object containing the year-to-date gross salary, total deductions, total tax, and net salary

**Description:**
1. Calculates the year-to-date totals for the employee
2. Returns the calculated totals

## PayrollService

The `PayrollService` manages payroll operations.

### Methods

#### createPayrollRun

Creates a new payroll run.

```typescript
async createPayrollRun(data: Partial<IPayrollRun>, userId: string): Promise<IPayrollRun>
```

**Parameters:**
- `data`: The payroll run data
- `userId`: The user ID creating the payroll run

**Returns:**
- The created payroll run

**Description:**
1. Validates the pay period
2. Checks if a payroll run already exists for the period
3. Creates a new payroll run
4. Returns the created payroll run

#### processPayrollRun

Processes a payroll run.

```typescript
async processPayrollRun(payrollRunId: string, userId: string): Promise<IPayrollRun>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `userId`: The user ID processing the payroll run

**Returns:**
- The processed payroll run

**Description:**
1. Gets the payroll run
2. Updates the status to processing
3. Gets the employees to process
4. Processes each employee
5. Updates the payroll run with the final totals
6. Returns the processed payroll run

#### approvePayrollRun

Approves a payroll run.

```typescript
async approvePayrollRun(payrollRunId: string, userId: string): Promise<IPayrollRun>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `userId`: The user ID approving the payroll run

**Returns:**
- The approved payroll run

**Description:**
1. Gets the payroll run
2. Updates the status to approved
3. Updates all payroll records for the run
4. Returns the approved payroll run

#### markPayrollRunAsPaid

Marks a payroll run as paid.

```typescript
async markPayrollRunAsPaid(payrollRunId: string, userId: string): Promise<IPayrollRun>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `userId`: The user ID marking the payroll run as paid

**Returns:**
- The paid payroll run

**Description:**
1. Gets the payroll run
2. Updates the status to paid
3. Updates all payroll records for the run
4. Returns the paid payroll run

#### cancelPayrollRun

Cancels a payroll run.

```typescript
async cancelPayrollRun(payrollRunId: string, userId: string, reason: string): Promise<IPayrollRun>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `userId`: The user ID cancelling the payroll run
- `reason`: The cancellation reason

**Returns:**
- The cancelled payroll run

**Description:**
1. Gets the payroll run
2. Updates the status to cancelled
3. Updates all payroll records for the run
4. Returns the cancelled payroll run

#### getPayrollRuns

Gets payroll runs with pagination.

```typescript
async getPayrollRuns(options: {
  page?: number;
  limit?: number;
  period?: string;
  status?: string;
  fromDate?: Date;
  toDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<{
  docs: IPayrollRun[];
  totalDocs: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}>
```

**Parameters:**
- `options`: The query options

**Returns:**
- An object containing the paginated payroll runs

**Description:**
1. Builds the query based on the options
2. Gets the paginated payroll runs
3. Returns the paginated payroll runs

#### getPayrollRunById

Gets a payroll run by ID.

```typescript
async getPayrollRunById(payrollRunId: string): Promise<IPayrollRun | null>
```

**Parameters:**
- `payrollRunId`: The payroll run ID

**Returns:**
- The payroll run or null if not found

**Description:**
1. Gets the payroll run by ID
2. Returns the payroll run or null if not found

#### getPayrollRecords

Gets payroll records for a payroll run.

```typescript
async getPayrollRecords(payrollRunId: string, options: {
  page?: number;
  limit?: number;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<{
  docs: IPayrollRecord[];
  totalDocs: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `options`: The query options

**Returns:**
- An object containing the paginated payroll records

**Description:**
1. Builds the query based on the options
2. Gets the paginated payroll records
3. Returns the paginated payroll records

## PayslipGenerationService

The `PayslipGenerationService` generates and manages payslips.

### Methods

#### generatePayslipsForPayrollRun

Generates payslips for a payroll run.

```typescript
async generatePayslipsForPayrollRun(payrollRunId: string, userId: string): Promise<IPaySlip[]>
```

**Parameters:**
- `payrollRunId`: The payroll run ID
- `userId`: The user ID generating the payslips

**Returns:**
- An array of generated payslips

**Description:**
1. Gets the payroll records for the run
2. Generates a payslip for each record
3. Returns the generated payslips

#### getPayslipById

Gets a payslip by ID.

```typescript
async getPayslipById(payslipId: string): Promise<IPaySlip | null>
```

**Parameters:**
- `payslipId`: The payslip ID

**Returns:**
- The payslip or null if not found

**Description:**
1. Gets the payslip by ID
2. Returns the payslip or null if not found

#### getPayslipsForEmployee

Gets payslips for an employee.

```typescript
async getPayslipsForEmployee(employeeId: string, options: {
  page?: number;
  limit?: number;
  year?: number;
  month?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<{
  docs: IPaySlip[];
  totalDocs: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}>
```

**Parameters:**
- `employeeId`: The employee ID
- `options`: The query options

**Returns:**
- An object containing the paginated payslips

**Description:**
1. Builds the query based on the options
2. Gets the paginated payslips
3. Returns the paginated payslips

#### markPayslipAsSent

Marks a payslip as sent.

```typescript
async markPayslipAsSent(payslipId: string, userId: string): Promise<IPaySlip | null>
```

**Parameters:**
- `payslipId`: The payslip ID
- `userId`: The user ID marking the payslip as sent

**Returns:**
- The updated payslip or null if not found

**Description:**
1. Updates the payslip status to sent
2. Returns the updated payslip or null if not found

#### markPayslipAsViewed

Marks a payslip as viewed.

```typescript
async markPayslipAsViewed(payslipId: string): Promise<IPaySlip | null>
```

**Parameters:**
- `payslipId`: The payslip ID

**Returns:**
- The updated payslip or null if not found

**Description:**
1. Updates the payslip status to viewed
2. Returns the updated payslip or null if not found

#### markPayslipAsDownloaded

Marks a payslip as downloaded.

```typescript
async markPayslipAsDownloaded(payslipId: string): Promise<IPaySlip | null>
```

**Parameters:**
- `payslipId`: The payslip ID

**Returns:**
- The updated payslip or null if not found

**Description:**
1. Updates the payslip status to downloaded
2. Returns the updated payslip or null if not found

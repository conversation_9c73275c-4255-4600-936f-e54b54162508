# Payroll Module Data Models

This document provides detailed information about the data models used in the Payroll Module.

## TaxBracket

The `TaxBracket` model defines tax brackets for PAYE calculations based on Malawi regulations.

### Schema

```typescript
interface ITaxBracket extends Document {
  country: string;
  currency: string;
  effectiveDate: Date;
  expiryDate?: Date;
  isActive: boolean;
  brackets: {
    lowerLimit: number;
    upperLimit?: number;
    rate: number;
    isExempt: boolean;
  }[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `country`: The country for which the tax brackets apply (default: 'Malawi')
- `currency`: The currency for which the tax brackets apply (default: 'MWK')
- `effectiveDate`: The date from which the tax brackets are effective
- `expiryDate`: The date until which the tax brackets are effective (optional)
- `isActive`: Whether the tax brackets are currently active
- `brackets`: An array of tax brackets with the following fields:
  - `lowerLimit`: The lower limit of the tax bracket
  - `upperLimit`: The upper limit of the tax bracket (optional, null for the highest bracket)
  - `rate`: The tax rate for the bracket (percentage)
  - `isExempt`: Whether the bracket is tax-exempt
- `createdBy`: The user who created the tax brackets
- `updatedBy`: The user who last updated the tax brackets (optional)
- `createdAt`: The date and time when the tax brackets were created
- `updatedAt`: The date and time when the tax brackets were last updated

### Indexes

- `{ country: 1, effectiveDate: -1 }`: For faster queries by country and effective date
- `{ isActive: 1 }`: For faster queries by active status

## SalaryStructure

The `SalaryStructure` model defines salary structures with components like basic salary, allowances, and deductions.

### Schema

```typescript
interface ISalaryComponent {
  name: string;
  type: 'basic' | 'allowance' | 'deduction' | 'tax' | 'benefit';
  amount?: number;
  percentage?: number;
  calculationBase?: string;
  isTaxable: boolean;
  isFixed: boolean;
  description?: string;
  order: number;
}

interface ISalaryStructure extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  effectiveDate: Date;
  expiryDate?: Date;
  currency: string;
  components: ISalaryComponent[];
  applicableRoles?: string[];
  applicableDepartments?: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `name`: The name of the salary structure
- `description`: A description of the salary structure (optional)
- `isActive`: Whether the salary structure is currently active
- `effectiveDate`: The date from which the salary structure is effective
- `expiryDate`: The date until which the salary structure is effective (optional)
- `currency`: The currency for the salary structure (default: 'MWK')
- `components`: An array of salary components with the following fields:
  - `name`: The name of the component
  - `type`: The type of the component ('basic', 'allowance', 'deduction', 'tax', 'benefit')
  - `amount`: The fixed amount for the component (optional)
  - `percentage`: The percentage for the component (optional)
  - `calculationBase`: The base for percentage calculations (optional)
  - `isTaxable`: Whether the component is taxable
  - `isFixed`: Whether the component has a fixed amount
  - `description`: A description of the component (optional)
  - `order`: The order of the component in the structure
- `applicableRoles`: An array of user roles to which the structure applies (optional)
- `applicableDepartments`: An array of department IDs to which the structure applies (optional)
- `createdBy`: The user who created the salary structure
- `updatedBy`: The user who last updated the salary structure (optional)
- `createdAt`: The date and time when the salary structure was created
- `updatedAt`: The date and time when the salary structure was last updated

### Indexes

- `{ name: 1 }`: For faster queries by name
- `{ isActive: 1 }`: For faster queries by active status
- `{ effectiveDate: -1 }`: For faster queries by effective date
- `{ 'applicableRoles': 1 }`: For faster queries by applicable roles
- `{ 'applicableDepartments': 1 }`: For faster queries by applicable departments

## PayrollRecord

The `PayrollRecord` model stores individual payroll records for employees.

### Schema

```typescript
interface IPayrollComponent {
  name: string;
  type: 'basic' | 'allowance' | 'deduction' | 'tax' | 'benefit';
  amount: number;
  isTaxable: boolean;
  description?: string;
}

interface IPayrollRecord extends Document {
  employeeId: mongoose.Types.ObjectId;
  payrollRunId: mongoose.Types.ObjectId;
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  salaryStructureId: mongoose.Types.ObjectId;
  currency: string;
  exchangeRate?: number;
  components: IPayrollComponent[];
  grossSalary: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
  status: 'draft' | 'approved' | 'paid' | 'cancelled';
  paymentDate?: Date;
  paymentReference?: string;
  paymentMethod?: string;
  bankAccount?: string;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `employeeId`: The ID of the employee
- `payrollRunId`: The ID of the payroll run
- `payPeriod`: The pay period details with the following fields:
  - `month`: The month of the pay period (1-12)
  - `year`: The year of the pay period
  - `startDate`: The start date of the pay period
  - `endDate`: The end date of the pay period
- `salaryStructureId`: The ID of the salary structure
- `currency`: The currency for the payroll record (default: 'MWK')
- `exchangeRate`: The exchange rate for currency conversion (optional)
- `components`: An array of payroll components with the following fields:
  - `name`: The name of the component
  - `type`: The type of the component ('basic', 'allowance', 'deduction', 'tax', 'benefit')
  - `amount`: The amount for the component
  - `isTaxable`: Whether the component is taxable
  - `description`: A description of the component (optional)
- `grossSalary`: The gross salary amount
- `totalDeductions`: The total deductions amount
- `totalTax`: The total tax amount
- `netSalary`: The net salary amount
- `status`: The status of the payroll record ('draft', 'approved', 'paid', 'cancelled')
- `paymentDate`: The date of payment (optional)
- `paymentReference`: The payment reference (optional)
- `paymentMethod`: The payment method (optional)
- `bankAccount`: The bank account for payment (optional)
- `notes`: Additional notes (optional)
- `createdBy`: The user who created the payroll record
- `updatedBy`: The user who last updated the payroll record (optional)
- `approvedBy`: The user who approved the payroll record (optional)
- `approvedAt`: The date and time when the payroll record was approved (optional)
- `createdAt`: The date and time when the payroll record was created
- `updatedAt`: The date and time when the payroll record was last updated

### Indexes

- `{ employeeId: 1, 'payPeriod.year': 1, 'payPeriod.month': 1 }`: For faster queries by employee and pay period
- `{ payrollRunId: 1 }`: For faster queries by payroll run
- `{ status: 1 }`: For faster queries by status
- `{ 'payPeriod.year': 1, 'payPeriod.month': 1 }`: For faster queries by pay period

## PayrollRun

The `PayrollRun` model manages payroll processing for a specific period.

### Schema

```typescript
interface IPayrollRun extends Document {
  name: string;
  description?: string;
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled';
  totalEmployees: number;
  processedEmployees: number;
  totalGrossSalary: number;
  totalDeductions: number;
  totalTax: number;
  totalNetSalary: number;
  currency: string;
  notes?: string;
  departments?: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  processedAt?: Date;
  paidAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `name`: The name of the payroll run
- `description`: A description of the payroll run (optional)
- `payPeriod`: The pay period details with the following fields:
  - `month`: The month of the pay period (1-12)
  - `year`: The year of the pay period
  - `startDate`: The start date of the pay period
  - `endDate`: The end date of the pay period
- `status`: The status of the payroll run ('draft', 'processing', 'completed', 'approved', 'paid', 'cancelled')
- `totalEmployees`: The total number of employees in the payroll run
- `processedEmployees`: The number of employees processed in the payroll run
- `totalGrossSalary`: The total gross salary amount
- `totalDeductions`: The total deductions amount
- `totalTax`: The total tax amount
- `totalNetSalary`: The total net salary amount
- `currency`: The currency for the payroll run (default: 'MWK')
- `notes`: Additional notes (optional)
- `departments`: An array of department IDs for filtering (optional)
- `createdBy`: The user who created the payroll run
- `updatedBy`: The user who last updated the payroll run (optional)
- `approvedBy`: The user who approved the payroll run (optional)
- `approvedAt`: The date and time when the payroll run was approved (optional)
- `processedAt`: The date and time when the payroll run was processed (optional)
- `paidAt`: The date and time when the payroll run was paid (optional)
- `createdAt`: The date and time when the payroll run was created
- `updatedAt`: The date and time when the payroll run was last updated

### Indexes

- `{ 'payPeriod.year': 1, 'payPeriod.month': 1 }`: For faster queries by pay period
- `{ status: 1 }`: For faster queries by status
- `{ departments: 1 }`: For faster queries by departments

## PaySlip

The `PaySlip` model stores generated payslips with detailed breakdowns.

### Schema

```typescript
interface IPaySlip extends Document {
  employeeId: mongoose.Types.ObjectId;
  payrollRecordId: mongoose.Types.ObjectId;
  payrollRunId: mongoose.Types.ObjectId;
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  employeeDetails: {
    name: string;
    employeeNumber: string;
    department?: string;
    position?: string;
    joinDate?: Date;
    bankAccount?: string;
    taxId?: string;
  };
  paymentDetails: {
    grossSalary: number;
    totalDeductions: number;
    totalTax: number;
    netSalary: number;
    currency: string;
    paymentMethod?: string;
    paymentDate?: Date;
    paymentReference?: string;
  };
  earningsBreakdown: Array<{
    name: string;
    amount: number;
    isTaxable: boolean;
  }>;
  deductionsBreakdown: Array<{
    name: string;
    amount: number;
  }>;
  taxBreakdown: Array<{
    name: string;
    amount: number;
  }>;
  ytdSummary: {
    grossSalary: number;
    totalDeductions: number;
    totalTax: number;
    netSalary: number;
  };
  status: 'generated' | 'sent' | 'viewed' | 'downloaded';
  generatedAt: Date;
  sentAt?: Date;
  viewedAt?: Date;
  downloadedAt?: Date;
  fileUrl?: string;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `employeeId`: The ID of the employee
- `payrollRecordId`: The ID of the payroll record
- `payrollRunId`: The ID of the payroll run
- `payPeriod`: The pay period details with the following fields:
  - `month`: The month of the pay period (1-12)
  - `year`: The year of the pay period
  - `startDate`: The start date of the pay period
  - `endDate`: The end date of the pay period
- `employeeDetails`: The employee details with the following fields:
  - `name`: The name of the employee
  - `employeeNumber`: The employee number
  - `department`: The department of the employee (optional)
  - `position`: The position of the employee (optional)
  - `joinDate`: The join date of the employee (optional)
  - `bankAccount`: The bank account of the employee (optional)
  - `taxId`: The tax ID of the employee (optional)
- `paymentDetails`: The payment details with the following fields:
  - `grossSalary`: The gross salary amount
  - `totalDeductions`: The total deductions amount
  - `totalTax`: The total tax amount
  - `netSalary`: The net salary amount
  - `currency`: The currency for the payment (default: 'MWK')
  - `paymentMethod`: The payment method (optional)
  - `paymentDate`: The date of payment (optional)
  - `paymentReference`: The payment reference (optional)
- `earningsBreakdown`: An array of earnings with the following fields:
  - `name`: The name of the earning
  - `amount`: The amount of the earning
  - `isTaxable`: Whether the earning is taxable
- `deductionsBreakdown`: An array of deductions with the following fields:
  - `name`: The name of the deduction
  - `amount`: The amount of the deduction
- `taxBreakdown`: An array of taxes with the following fields:
  - `name`: The name of the tax
  - `amount`: The amount of the tax
- `ytdSummary`: The year-to-date summary with the following fields:
  - `grossSalary`: The year-to-date gross salary amount
  - `totalDeductions`: The year-to-date total deductions amount
  - `totalTax`: The year-to-date total tax amount
  - `netSalary`: The year-to-date net salary amount
- `status`: The status of the payslip ('generated', 'sent', 'viewed', 'downloaded')
- `generatedAt`: The date and time when the payslip was generated
- `sentAt`: The date and time when the payslip was sent (optional)
- `viewedAt`: The date and time when the payslip was viewed (optional)
- `downloadedAt`: The date and time when the payslip was downloaded (optional)
- `fileUrl`: The URL of the payslip file (optional)
- `notes`: Additional notes (optional)
- `createdBy`: The user who created the payslip
- `updatedBy`: The user who last updated the payslip (optional)
- `createdAt`: The date and time when the payslip was created
- `updatedAt`: The date and time when the payslip was last updated

### Indexes

- `{ employeeId: 1, 'payPeriod.year': 1, 'payPeriod.month': 1 }`: For faster queries by employee and pay period
- `{ payrollRecordId: 1 }`: For faster queries by payroll record
- `{ payrollRunId: 1 }`: For faster queries by payroll run
- `{ status: 1 }`: For faster queries by status
- `{ 'employeeDetails.employeeNumber': 1 }`: For faster queries by employee number

## Allowance

The `Allowance` model defines allowance types that can be assigned to employees.

### Schema

```typescript
interface IAllowance extends Document {
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
  isTaxable: boolean;
  isPensionable: boolean;
  isFixed: boolean;
  defaultAmount?: number;
  defaultPercentage?: number;
  calculationBase?: string;
  applicableRoles?: string[];
  applicableDepartments?: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `name`: The name of the allowance
- `code`: The code of the allowance (unique)
- `description`: A description of the allowance (optional)
- `isActive`: Whether the allowance is currently active
- `isTaxable`: Whether the allowance is taxable
- `isPensionable`: Whether the allowance is pensionable
- `isFixed`: Whether the allowance has a fixed amount
- `defaultAmount`: The default amount for the allowance (optional)
- `defaultPercentage`: The default percentage for the allowance (optional)
- `calculationBase`: The base for percentage calculations (optional)
- `applicableRoles`: An array of user roles to which the allowance applies (optional)
- `applicableDepartments`: An array of department IDs to which the allowance applies (optional)
- `createdBy`: The user who created the allowance
- `updatedBy`: The user who last updated the allowance (optional)
- `createdAt`: The date and time when the allowance was created
- `updatedAt`: The date and time when the allowance was last updated

### Indexes

- `{ code: 1 }`: For faster queries by code (unique)
- `{ name: 1 }`: For faster queries by name
- `{ isActive: 1 }`: For faster queries by active status
- `{ isTaxable: 1 }`: For faster queries by taxable status
- `{ 'applicableRoles': 1 }`: For faster queries by applicable roles
- `{ 'applicableDepartments': 1 }`: For faster queries by applicable departments

## Deduction

The `Deduction` model defines deduction types that can be applied to employee salaries.

### Schema

```typescript
interface IDeduction extends Document {
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
  isStatutory: boolean;
  isFixed: boolean;
  defaultAmount?: number;
  defaultPercentage?: number;
  calculationBase?: string;
  applicableRoles?: string[];
  applicableDepartments?: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `name`: The name of the deduction
- `code`: The code of the deduction (unique)
- `description`: A description of the deduction (optional)
- `isActive`: Whether the deduction is currently active
- `isStatutory`: Whether the deduction is statutory (government-mandated)
- `isFixed`: Whether the deduction has a fixed amount
- `defaultAmount`: The default amount for the deduction (optional)
- `defaultPercentage`: The default percentage for the deduction (optional)
- `calculationBase`: The base for percentage calculations (optional)
- `applicableRoles`: An array of user roles to which the deduction applies (optional)
- `applicableDepartments`: An array of department IDs to which the deduction applies (optional)
- `createdBy`: The user who created the deduction
- `updatedBy`: The user who last updated the deduction (optional)
- `createdAt`: The date and time when the deduction was created
- `updatedAt`: The date and time when the deduction was last updated

### Indexes

- `{ code: 1 }`: For faster queries by code (unique)
- `{ name: 1 }`: For faster queries by name
- `{ isActive: 1 }`: For faster queries by active status
- `{ isStatutory: 1 }`: For faster queries by statutory status
- `{ 'applicableRoles': 1 }`: For faster queries by applicable roles
- `{ 'applicableDepartments': 1 }`: For faster queries by applicable departments

## EmployeeSalary

The `EmployeeSalary` model tracks employee salary information with allowances and deductions.

### Schema

```typescript
interface IEmployeeAllowance {
  allowanceId: mongoose.Types.ObjectId;
  name: string;
  amount?: number;
  percentage?: number;
  isTaxable: boolean;
  isPensionable: boolean;
}

interface IEmployeeDeduction {
  deductionId: mongoose.Types.ObjectId;
  name: string;
  amount?: number;
  percentage?: number;
  isStatutory: boolean;
}

interface IEmployeeSalary extends Document {
  employeeId: mongoose.Types.ObjectId;
  salaryStructureId: mongoose.Types.ObjectId;
  basicSalary: number;
  currency: string;
  effectiveDate: Date;
  endDate?: Date;
  isActive: boolean;
  allowances: IEmployeeAllowance[];
  deductions: IEmployeeDeduction[];
  bankName?: string;
  bankAccountNumber?: string;
  bankBranchCode?: string;
  paymentMethod: 'bank_transfer' | 'cash' | 'check' | 'mobile_money';
  taxId?: string;
  pensionScheme?: string;
  pensionNumber?: string;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `employeeId`: The ID of the employee
- `salaryStructureId`: The ID of the salary structure
- `basicSalary`: The basic salary amount
- `currency`: The currency for the salary (default: 'MWK')
- `effectiveDate`: The date from which the salary is effective
- `endDate`: The date until which the salary is effective (optional)
- `isActive`: Whether the salary is currently active
- `allowances`: An array of employee allowances with the following fields:
  - `allowanceId`: The ID of the allowance
  - `name`: The name of the allowance
  - `amount`: The amount for the allowance (optional)
  - `percentage`: The percentage for the allowance (optional)
  - `isTaxable`: Whether the allowance is taxable
  - `isPensionable`: Whether the allowance is pensionable
- `deductions`: An array of employee deductions with the following fields:
  - `deductionId`: The ID of the deduction
  - `name`: The name of the deduction
  - `amount`: The amount for the deduction (optional)
  - `percentage`: The percentage for the deduction (optional)
  - `isStatutory`: Whether the deduction is statutory
- `bankName`: The bank name for salary payment (optional)
- `bankAccountNumber`: The bank account number for salary payment (optional)
- `bankBranchCode`: The bank branch code for salary payment (optional)
- `paymentMethod`: The payment method ('bank_transfer', 'cash', 'check', 'mobile_money')
- `taxId`: The tax ID of the employee (optional)
- `pensionScheme`: The pension scheme of the employee (optional)
- `pensionNumber`: The pension number of the employee (optional)
- `notes`: Additional notes (optional)
- `createdBy`: The user who created the employee salary
- `updatedBy`: The user who last updated the employee salary (optional)
- `createdAt`: The date and time when the employee salary was created
- `updatedAt`: The date and time when the employee salary was last updated

### Indexes

- `{ employeeId: 1, isActive: 1 }`: For faster queries by employee and active status
- `{ salaryStructureId: 1 }`: For faster queries by salary structure
- `{ effectiveDate: -1 }`: For faster queries by effective date

## SalaryRevision

The `SalaryRevision` model tracks changes to employee salaries.

### Schema

```typescript
interface ISalaryRevision extends Document {
  employeeId: mongoose.Types.ObjectId;
  previousSalaryId: mongoose.Types.ObjectId;
  newSalaryId: mongoose.Types.ObjectId;
  revisionType: 'increment' | 'promotion' | 'adjustment' | 'demotion' | 'annual_review' | 'other';
  effectiveDate: Date;
  previousBasicSalary: number;
  newBasicSalary: number;
  percentageChange: number;
  amountChange: number;
  currency: string;
  reason: string;
  notes?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  rejectedBy?: mongoose.Types.ObjectId;
  rejectedAt?: Date;
  rejectionReason?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Fields

- `employeeId`: The ID of the employee
- `previousSalaryId`: The ID of the previous salary
- `newSalaryId`: The ID of the new salary
- `revisionType`: The type of revision ('increment', 'promotion', 'adjustment', 'demotion', 'annual_review', 'other')
- `effectiveDate`: The date from which the revision is effective
- `previousBasicSalary`: The previous basic salary amount
- `newBasicSalary`: The new basic salary amount
- `percentageChange`: The percentage change in the salary
- `amountChange`: The amount change in the salary
- `currency`: The currency for the salary (default: 'MWK')
- `reason`: The reason for the revision
- `notes`: Additional notes (optional)
- `approvalStatus`: The approval status of the revision ('pending', 'approved', 'rejected')
- `approvedBy`: The user who approved the revision (optional)
- `approvedAt`: The date and time when the revision was approved (optional)
- `rejectedBy`: The user who rejected the revision (optional)
- `rejectedAt`: The date and time when the revision was rejected (optional)
- `rejectionReason`: The reason for rejection (optional)
- `createdBy`: The user who created the revision
- `updatedBy`: The user who last updated the revision (optional)
- `createdAt`: The date and time when the revision was created
- `updatedAt`: The date and time when the revision was last updated

### Indexes

- `{ employeeId: 1 }`: For faster queries by employee
- `{ effectiveDate: -1 }`: For faster queries by effective date
- `{ approvalStatus: 1 }`: For faster queries by approval status
- `{ revisionType: 1 }`: For faster queries by revision type

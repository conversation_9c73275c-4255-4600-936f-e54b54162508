# Payroll Module Documentation

## Overview

The Payroll Module in the TCM Enterprise Business Suite provides comprehensive functionality for managing employee compensation, including salary structures, tax calculations, payroll processing, and payslip generation. The module is designed to comply with Malawi's tax regulations and integrate seamlessly with the Accounting Module.

## Key Features

### Salary Structure Management

- Define salary structures with components like basic salary, allowances, and deductions
- Create and manage allowance types with customizable calculation methods
- Define deduction types with statutory and non-statutory options
- Support for percentage-based and fixed-amount components

### Tax Calculation

- PAYE (Pay As You Earn) tax calculation based on Malawi's tax brackets
- Support for tax-exempt allowances and benefits
- Pension contribution calculation
- Configurable tax brackets for different periods

### Payroll Processing

- Create and manage payroll runs for specific periods
- Process payroll for all employees or selected departments
- Review and approve payroll runs before finalization
- Mark payroll runs as paid after disbursement

### Payslip Generation

- Generate detailed payslips for employees
- Include earnings, deductions, and tax breakdowns
- Track year-to-date totals for key metrics
- Support for digital distribution of payslips

### Employee Salary Management

- Manage employee salary information with allowances and deductions
- Track salary revisions with effective dates
- Support for different payment methods (bank transfer, cash, etc.)
- Maintain salary history for audit and reporting

## Data Models

### TaxBracket

Defines tax brackets for PAYE calculations based on Malawi regulations:

- Country and currency settings
- Effective date and expiry date for time-based applicability
- Tax brackets with lower and upper limits, rates, and exemption flags

### SalaryStructure

Defines salary structures with components:

- Name and description for identification
- Effective date and expiry date for time-based applicability
- Components like basic salary, allowances, and deductions
- Applicable roles and departments for targeted assignment

### PayrollRecord

Stores individual payroll records for employees:

- Employee and payroll run references
- Pay period details (month, year, start date, end date)
- Salary components with amounts and types
- Gross salary, total deductions, total tax, and net salary
- Status tracking (draft, approved, paid, cancelled)

### PayrollRun

Manages payroll processing for a specific period:

- Pay period details (month, year, start date, end date)
- Status tracking (draft, processing, completed, approved, paid, cancelled)
- Total counts and amounts for employees, gross salary, deductions, tax, and net salary
- Department filtering for targeted processing

### PaySlip

Stores generated payslips with detailed breakdowns:

- Employee and payroll record references
- Employee details (name, number, department, position, etc.)
- Payment details (gross, deductions, tax, net, etc.)
- Earnings, deductions, and tax breakdowns
- Year-to-date summary for key metrics
- Status tracking (generated, sent, viewed, downloaded)

### Allowance

Defines allowance types that can be assigned to employees:

- Name, code, and description for identification
- Taxable and pensionable flags for compliance
- Fixed or percentage-based calculation options
- Default amounts or percentages for quick assignment
- Applicable roles and departments for targeted assignment

### Deduction

Defines deduction types that can be applied to employee salaries:

- Name, code, and description for identification
- Statutory flag for government-mandated deductions
- Fixed or percentage-based calculation options
- Default amounts or percentages for quick assignment
- Applicable roles and departments for targeted assignment

### EmployeeSalary

Tracks employee salary information:

- Basic salary amount and currency
- Effective date and end date for time-based applicability
- Allowances and deductions with amounts or percentages
- Bank account and payment method details
- Tax and pension information

### SalaryRevision

Tracks changes to employee salaries:

- Previous and new salary references
- Revision type (increment, promotion, adjustment, etc.)
- Percentage and amount changes for analysis
- Reason and notes for documentation
- Approval status and approver details

## API Endpoints

### Tax Brackets

- `GET /api/payroll/tax-brackets`: Get tax brackets
- `POST /api/payroll/tax-brackets`: Create a new tax bracket
- `GET /api/payroll/tax-brackets/[id]`: Get a tax bracket by ID
- `PATCH /api/payroll/tax-brackets/[id]`: Update a tax bracket
- `DELETE /api/payroll/tax-brackets/[id]`: Delete a tax bracket
- `POST /api/payroll/tax-brackets/default`: Create default tax brackets for Malawi

### Salary Structures

- `GET /api/payroll/salary-structures`: Get salary structures
- `POST /api/payroll/salary-structures`: Create a new salary structure
- `GET /api/payroll/salary-structures/[id]`: Get a salary structure by ID
- `PATCH /api/payroll/salary-structures/[id]`: Update a salary structure
- `DELETE /api/payroll/salary-structures/[id]`: Delete a salary structure

### Payroll Runs

- `GET /api/payroll/runs`: Get payroll runs
- `POST /api/payroll/runs`: Create a new payroll run
- `GET /api/payroll/runs/[id]`: Get a payroll run by ID
- `PATCH /api/payroll/runs/[id]`: Perform operations on a payroll run (process, approve, pay, cancel)
- `GET /api/payroll/runs/[id]/records`: Get payroll records for a payroll run
- `GET /api/payroll/runs/[id]/payslips`: Get payslips for a payroll run
- `POST /api/payroll/runs/[id]/payslips`: Generate payslips for a payroll run

### Employee Salaries

- `GET /api/payroll/employee-salaries`: Get employee salaries
- `POST /api/payroll/employee-salaries`: Create a new employee salary
- `GET /api/payroll/employee-salaries/[id]`: Get an employee salary by ID
- `PATCH /api/payroll/employee-salaries/[id]`: Update an employee salary

### Employee Payroll Information

- `GET /api/payroll/employees/[id]/salary-history`: Get salary history for an employee
- `GET /api/payroll/employees/[id]/payslips`: Get payslips for an employee

### Payslips

- `GET /api/payroll/payslips/[id]`: Get a payslip by ID
- `PATCH /api/payroll/payslips/[id]`: Update payslip status (mark as sent, viewed, downloaded)
- `GET /api/payroll/payslips/[id]/download`: Download a payslip

### Allowances

- `GET /api/payroll/allowances`: Get allowances
- `POST /api/payroll/allowances`: Create a new allowance
- `GET /api/payroll/allowances/[id]`: Get an allowance by ID
- `PATCH /api/payroll/allowances/[id]`: Update an allowance
- `DELETE /api/payroll/allowances/[id]`: Delete an allowance

### Deductions

- `GET /api/payroll/deductions`: Get deductions
- `POST /api/payroll/deductions`: Create a new deduction
- `GET /api/payroll/deductions/[id]`: Get a deduction by ID
- `PATCH /api/payroll/deductions/[id]`: Update a deduction
- `DELETE /api/payroll/deductions/[id]`: Delete a deduction

### Salary Calculation

- `POST /api/payroll/calculate-salary`: Calculate salary for an employee

## Services

### TaxService

Calculates taxes based on Malawi regulations:

- PAYE tax calculation based on tax brackets
- Retrieval of active tax brackets for a country and currency
- Creation of default tax brackets for Malawi
- Pension contribution calculation

### SalaryCalculationService

Calculates employee salaries:

- Salary calculation with allowances, deductions, and taxes
- Retrieval of active employee salary information
- Year-to-date total calculation for reporting

### PayrollService

Manages payroll operations:

- Creation and management of payroll runs
- Processing of payroll runs for all or selected employees
- Approval and payment status updates for payroll runs
- Cancellation of payroll runs with reason tracking

### PayslipGenerationService

Generates and manages payslips:

- Generation of payslips for payroll runs
- Retrieval of payslips for employees
- Status updates for payslips (sent, viewed, downloaded)

## Integration with Accounting Module

The Payroll Module integrates with the Accounting Module to provide a comprehensive financial management system:

- Payroll transactions automatically create appropriate journal entries
- Expense allocation to correct general ledger accounts
- Tax liability tracking that integrates with accounts payable
- Salary payment processing that integrates with banking module
- Budget tracking for payroll expenses
- Financial reporting integration for payroll costs
- Audit trail for all payroll-related financial transactions

## Malawi PAYE Tax Calculation

The tax calculation is based on Malawi's PAYE tax brackets:

- 0 - 150,000 MWK: 0% (Tax-free threshold)
- 150,000 - 500,000 MWK: 25%
- 500,000 - 2,550,000 MWK: 30%
- Above 2,550,000 MWK: 35%

## Payroll Processing Workflow

1. Create a payroll run for a specific period
2. Process the payroll run to calculate salaries for all employees
3. Review and approve the payroll run
4. Generate payslips for the payroll run
5. Mark the payroll run as paid
6. Distribute payslips to employees

## Salary Revision Process

When an employee's salary is revised:

1. The current salary record is deactivated
2. A new salary record is created with the updated salary
3. A salary revision record is created to track the change
4. The revision includes details like percentage change, amount change, and reason

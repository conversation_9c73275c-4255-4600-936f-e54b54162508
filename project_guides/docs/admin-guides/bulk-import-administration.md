# Bulk Import System - Administrator Guide

## Overview

This guide is designed for system administrators who need to manage, troubleshoot, and optimize the bulk import system. It covers both operational procedures and technical management aspects.

## 🎯 **Administrator Responsibilities**

### Pre-Import Setup
1. **Verify system capacity** for large imports
2. **Ensure database connectivity** and performance
3. **Validate user permissions** and access rights
4. **Monitor system resources** during imports
5. **Backup critical data** before major imports

### During Import Operations
1. **Monitor system performance** and resource usage
2. **Assist users** with import issues and errors
3. **Validate import results** and data integrity
4. **Handle escalated technical issues**

### Post-Import Management
1. **Verify data accuracy** and completeness
2. **Update system indexes** if needed
3. **Clean up temporary files** and logs
4. **Generate import reports** for management

---

## 📋 **Import Process Management**

### **CRITICAL: Import Order Enforcement**

**🔴 ALWAYS ENFORCE THIS ORDER:**
1. **Departments FIRST** 📁
2. **Employees SECOND** 👥

**Why This Matters:**
- Employee records reference department IDs
- Invalid department references cause import failures
- Data integrity depends on proper relationships
- System performance is optimized for this order

### Pre-Import Checklist

**System Readiness:**
- [ ] Database connection stable
- [ ] Sufficient disk space (>1GB recommended)
- [ ] Memory usage below 70%
- [ ] No ongoing maintenance operations
- [ ] Backup completed within last 24 hours

**User Preparation:**
- [ ] Users trained on import process
- [ ] Templates downloaded and reviewed
- [ ] Data cleaned and validated
- [ ] Test import completed successfully
- [ ] Import order confirmed (departments first)

---

## 🔧 **System Configuration**

### File Upload Limits

**Current Settings:**
```javascript
MAX_FILE_SIZE = 5MB
SUPPORTED_FORMATS = ['.csv', '.xlsx', '.xls']
MAX_ROWS_PER_IMPORT = 1000 (recommended)
```

**To Modify Limits:**
1. Update environment variables
2. Restart application server
3. Test with sample files
4. Monitor performance impact

### Performance Tuning

**Database Connection Pool:**
```javascript
// Current settings
maxPoolSize: 10
serverSelectionTimeoutMS: 5000
socketTimeoutMS: 45000
```

**Memory Allocation:**
```bash
# For large imports, increase Node.js memory
node --max-old-space-size=4096 server.js
```

**Batch Processing:**
- Default batch size: 100 records
- Adjust based on system performance
- Monitor memory usage during processing

---

## 📊 **Monitoring & Analytics**

### Key Metrics to Track

**Performance Metrics:**
- Import processing time per 100 records
- Memory usage during imports
- Database query response times
- File upload success rates

**Business Metrics:**
- Total records imported per day/week/month
- Import success rates by department
- Common error types and frequencies
- User adoption and usage patterns

### Monitoring Dashboard

**Real-time Monitoring:**
1. **System Resources**
   - CPU usage during imports
   - Memory consumption
   - Database connection count
   - Disk I/O performance

2. **Import Statistics**
   - Active imports in progress
   - Recent import success/failure rates
   - Queue length (if background processing enabled)
   - Average processing time

### Log Analysis

**Important Log Locations:**
```bash
# Application logs
/var/log/tcm-enterprise/app.log

# Import-specific logs
/var/log/tcm-enterprise/imports/

# Database logs
/var/log/mongodb/mongod.log
```

**Key Log Patterns to Monitor:**
```bash
# Successful imports
grep "Bulk import completed successfully" app.log

# Failed imports
grep "Bulk import failed" app.log

# Department validation errors
grep "Department not found" app.log

# Memory warnings
grep "heap out of memory" app.log
```

---

## 🚨 **Troubleshooting Guide**

### Common Issues & Solutions

#### 1. "File Too Large" Errors

**Symptoms:**
- Users cannot upload files
- "File size exceeds limit" error messages

**Diagnosis:**
```bash
# Check current file size limit
grep MAX_FILE_SIZE .env

# Check actual file sizes
ls -lh /tmp/uploads/
```

**Solutions:**
- Increase file size limit in configuration
- Advise users to split large files
- Implement chunked upload for very large files

#### 2. Department Validation Failures

**Symptoms:**
- High number of "Department not found" errors
- Employees not being imported

**Diagnosis:**
```bash
# Check available departments
mongo tcm_enterprise --eval "db.departments.find({}, {name: 1})"

# Check import logs for department mismatches
grep "Department mismatch" /var/log/tcm-enterprise/imports/
```

**Solutions:**
- Verify departments were imported first
- Check department name spelling and formatting
- Provide users with current department list
- Update department names if needed

#### 3. Memory Issues

**Symptoms:**
- "JavaScript heap out of memory" errors
- Slow import processing
- Server becoming unresponsive

**Diagnosis:**
```bash
# Check memory usage
free -h
top -p $(pgrep node)

# Check Node.js memory limit
ps aux | grep node | grep max-old-space-size
```

**Solutions:**
- Increase Node.js memory limit
- Implement batch processing for large files
- Restart application server
- Consider background job processing

#### 4. Database Connection Issues

**Symptoms:**
- "MongoServerSelectionError" messages
- Import timeouts
- Connection refused errors

**Diagnosis:**
```bash
# Test database connectivity
mongo $MONGODB_URI --eval "db.runCommand('ping')"

# Check connection pool status
mongo tcm_enterprise --eval "db.runCommand('serverStatus').connections"
```

**Solutions:**
- Verify database server status
- Check network connectivity
- Increase connection timeout settings
- Restart database service if needed

---

## 🔐 **Security Management**

### Access Control

**Role-Based Permissions:**
- **HR_DIRECTOR**: Full import access for all modules
- **HR_MANAGER**: Employee and department imports
- **SYSTEM_ADMIN**: All imports + system configuration
- **SUPER_ADMIN**: All permissions + user management

**Permission Verification:**
```javascript
// Check user permissions before import
const hasPermission = await checkUserPermission(userId, 'BULK_IMPORT_EMPLOYEES')
```

### Data Security

**File Upload Security:**
1. **Virus Scanning**: Implement if handling external files
2. **File Type Validation**: Strict enforcement of allowed formats
3. **Content Scanning**: Check for malicious content
4. **Temporary File Cleanup**: Automatic deletion after processing

**Data Privacy:**
1. **PII Protection**: Ensure personal data is handled securely
2. **Audit Logging**: Track all import activities
3. **Data Retention**: Follow organizational data retention policies
4. **Access Logging**: Monitor who accesses import features

---

## 📈 **Performance Optimization**

### Database Optimization

**Index Management:**
```javascript
// Ensure these indexes exist for optimal performance
db.employees.createIndex({ "email": 1 }, { unique: true })
db.employees.createIndex({ "department": 1 })
db.employees.createIndex({ "employeeNumber": 1 }, { unique: true })
db.departments.createIndex({ "name": 1 }, { unique: true })
```

**Query Optimization:**
- Monitor slow queries during imports
- Use explain() to analyze query performance
- Consider compound indexes for complex queries

### Application Optimization

**Memory Management:**
```javascript
// Monitor memory usage
process.memoryUsage()

// Implement garbage collection hints
if (global.gc) {
  global.gc()
}
```

**Connection Pooling:**
```javascript
// Optimize MongoDB connection pool
mongoose.connect(uri, {
  maxPoolSize: 10,        // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferMaxEntries: 0     // Disable mongoose buffering
})
```

---

## 📋 **Maintenance Procedures**

### Daily Maintenance

**Morning Checklist:**
- [ ] Check system logs for overnight errors
- [ ] Verify database connectivity and performance
- [ ] Review import statistics from previous day
- [ ] Check disk space and memory usage
- [ ] Validate backup completion

**Evening Checklist:**
- [ ] Review day's import activities
- [ ] Check for any failed imports requiring attention
- [ ] Monitor system resource usage trends
- [ ] Plan for next day's large imports
- [ ] Update documentation if needed

### Weekly Maintenance

**System Health:**
- [ ] Analyze import performance trends
- [ ] Review error patterns and frequencies
- [ ] Check database index performance
- [ ] Update system documentation
- [ ] Plan capacity upgrades if needed

**User Support:**
- [ ] Review user feedback and issues
- [ ] Update user training materials
- [ ] Communicate system updates to users
- [ ] Schedule user training sessions if needed

### Monthly Maintenance

**Performance Review:**
- [ ] Analyze monthly import statistics
- [ ] Review system performance metrics
- [ ] Plan infrastructure improvements
- [ ] Update disaster recovery procedures
- [ ] Review security audit logs

---

## 📞 **Escalation Procedures**

### Level 1: User Support Issues
**Handle locally:**
- Template download problems
- Basic validation errors
- User training needs
- Simple data formatting issues

### Level 2: System Issues
**Escalate to technical team:**
- Performance degradation
- Database connectivity issues
- Memory or resource problems
- Application errors

### Level 3: Critical Issues
**Escalate to senior technical staff:**
- Data corruption or loss
- Security breaches
- System-wide failures
- Major performance issues affecting business operations

### Emergency Contacts

**Technical Team:**
- Primary: [Technical Lead Contact]
- Secondary: [Senior Developer Contact]
- Database: [DBA Contact]

**Business Team:**
- HR Director: [HR Director Contact]
- System Owner: [System Owner Contact]
- Management: [Management Contact]

---

## 📚 **Documentation & Training**

### User Training Materials

**Required Training Topics:**
1. Import order and dependencies
2. Template usage and data formatting
3. Error interpretation and resolution
4. Data validation best practices
5. Security and privacy considerations

**Training Schedule:**
- New user onboarding: Include bulk import training
- Quarterly refresher sessions
- Update training after system changes
- Document common user questions and solutions

### Documentation Maintenance

**Keep Updated:**
- User guides with latest features
- Technical documentation with system changes
- Troubleshooting guides with new solutions
- Performance benchmarks and optimization tips

---

## 🎯 **Success Metrics**

### Key Performance Indicators

**System Performance:**
- Import processing time: < 2 minutes per 100 records
- System uptime during imports: > 99.5%
- Memory usage: < 80% during peak imports
- Database response time: < 100ms average

**User Experience:**
- Import success rate: > 95%
- User satisfaction score: > 4.0/5.0
- Training completion rate: > 90%
- Support ticket resolution time: < 2 hours

**Business Impact:**
- Time saved vs manual entry: > 80%
- Data accuracy improvement: > 95%
- User adoption rate: > 85%
- Reduction in data entry errors: > 90%

Remember: **Departments must always be imported before employees!** This is the foundation of successful bulk import operations.

# Dashboard Statistics Implementation Guide

This document provides guidance on how to implement real data for dashboard statistics when you're ready to do so.

## Current Implementation

Currently, the dashboard displays static placeholder data for key metrics:

- Total Employees: 245
- New Hires: 12
- Turnover Rate: 3.2%
- Attendance Rate: 96.8%

These values are hardcoded in the `components/employee-overview.tsx` file.

## Using Stats Placeholders

For stats items that don't have data from the database, you can use the `StatsPlaceholder` component:

```tsx
import { StatsPlaceholder } from "@/components/ui/stats-placeholder"
import { Users } from "lucide-react"

// Example usage
<StatsPlaceholder 
  title="Total Employees" 
  icon={<Users className="h-6 w-6 text-primary" />} 
  color="primary" 
/>
```

The `StatsPlaceholder` component accepts the following props:

- `title`: The title of the stat (e.g., "Total Employees")
- `icon`: The icon to display (e.g., `<Users />`)
- `color`: The color theme (options: "primary", "green", "amber", "blue", "red")
- `className`: Optional additional CSS classes

## Implementing Real Data

When you're ready to implement real data for the dashboard statistics, follow these steps:

1. Use the API endpoint we've created at `app/api/dashboard/stats/route.ts`
2. Convert the `EmployeeOverview` component to a client component by adding the "use client" directive
3. Add state management for loading, error, and data states
4. Implement data fetching from the API endpoint
5. Update the UI to display real data

Here's a simplified example of how to implement real data:

```tsx
"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Users, TrendingUp, TrendingDown } from "lucide-react"
import { StatsPlaceholder } from "@/components/ui/stats-placeholder"

export function EmployeeOverview({ className }) {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true)
        const response = await fetch('/api/dashboard/stats')
        
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard statistics')
        }
        
        const data = await response.json()
        setStats(data)
      } catch (err) {
        console.error('Error fetching dashboard stats:', err)
        setError(err instanceof Error ? err.message : 'An unknown error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <>
        <StatsPlaceholder 
          title="Total Employees" 
          icon={<Users className="h-6 w-6 text-primary" />} 
          color="primary" 
          className={className} 
        />
        {/* Add more placeholders as needed */}
      </>
    )
  }

  if (error || !stats) {
    // Handle error state
    return <ErrorDisplay error={error} />
  }

  return (
    <>
      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-primary/20 to-primary/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                <h3 className="mt-1 text-3xl font-bold">{stats.employees.total}</h3>
                {/* Display trend indicator */}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                <Users className="h-6 w-6 text-primary" />
              </div>
            </div>
            {/* Display progress bar */}
          </div>
        </CardContent>
      </Card>
      {/* Add more stat cards as needed */}
    </>
  )
}
```

## API Endpoint

The API endpoint at `app/api/dashboard/stats/route.ts` provides the following data:

```typescript
{
  employees: {
    total: number;
    active: number;
    inactive: number;
    onLeave: number;
    terminated: number;
    changePercentage: number;
  },
  newHires: {
    count: number;
    inProbation: number;
    changePercentage: number;
  },
  turnover: {
    rate: string;
    changePercentage: number;
  },
  attendance: {
    rate: number;
    changePercentage: number;
  },
  departmentDistribution: Array<{ department: string; count: number }>,
  employmentTypeDistribution: Array<{ type: string; count: number }>
}
```

This data can be used to populate the dashboard statistics when you're ready to implement real data.

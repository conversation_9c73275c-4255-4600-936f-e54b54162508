# TCM Enterprise Business Suite Documentation

## Overview

The TCM Enterprise Business Suite is a comprehensive enterprise resource planning (ERP) system designed for businesses in Malawi. It provides a wide range of modules for managing various aspects of a business, including human resources, accounting, payroll, inventory, and more.

## Modules

### Human Resources Management

- [Employee Management](hr/employee-management.md)
- [Department Management](hr/department-management.md)
- [Role Management](user-guides/role-management-user-guide.md) 🆕 **TCM Roles Complete**
- [HR System User Guide](user-guides/hr-system-user-guide.md) 🆕 **Comprehensive Guide**
- [Employee Management User Guide](user-guides/employee-management-user-guide.md) 🆕 **Enhanced**
- [Bulk Import System](user-guides/bulk-import-system.md) ⭐ **Updated**
- [Recruitment](hr/recruitment.md)
- [Performance Management](hr/performance-management.md)
- [Training and Development](hr/training-development.md)
- [Leave Management](hr/leave-management.md)

### Accounting and Finance

- [General Ledger](accounting/general-ledger.md)
- [Accounts Payable](accounting/accounts-payable.md)
- [Accounts Receivable](accounting/accounts-receivable.md)
- [Banking and Treasury](accounting/banking/overview.md)
- [Financial Reporting](accounting/financial-reporting.md)

### Payroll

- [Overview](payroll/overview.md)
- [Data Models](payroll/models.md)
- [Services](payroll/services.md)
- [API Reference](payroll/api-reference.md)
- [Payroll User Guide](user-guides/payroll-user-guide.md) 🆕 **Role Integration**

### Inventory Management

- [Inventory Control](inventory/inventory-control.md)
- [Purchasing](inventory/purchasing.md)
- [Warehouse Management](inventory/warehouse-management.md)

### Sales and Customer Management

- [Sales Management](sales/sales-management.md)
- [Customer Relationship Management](sales/crm.md)
- [Quotations and Invoicing](sales/quotations-invoicing.md)

### System Administration

- [User Management](admin/user-management.md)
- [Role-Based Access Control](admin/rbac.md)
- [System Configuration](admin/system-configuration.md)
- [Bulk Import Administration](admin-guides/bulk-import-administration.md) ⭐ **New**
- [Audit Logs](admin/audit-logs.md)

## Technical Documentation

- [Architecture](technical/architecture.md)
- [Database Schema](technical/database-schema.md)
- [API Reference](technical/api-reference.md)
- [Bulk Import Technical Guide](technical/bulk-import-technical-guide.md) ⭐ **New**
- [Integration Points](technical/integration-points.md)
- [Deployment Guide](technical/deployment-guide.md)

## Implementation Status

- [Current Status](implementation-status.md)
- [Roadmap](roadmap.md)

## User Guides

### System Navigation
- [Getting Started](user-guides/getting-started.md)
- [Navigation](user-guides/navigation.md)
- [Common Tasks](user-guides/common-tasks.md)
- [Troubleshooting](user-guides/troubleshooting.md)

### HR & Employee Management 🆕
- [HR System User Guide](user-guides/hr-system-user-guide.md) 🆕 **Comprehensive HR Workflow**
- [Employee Management User Guide](user-guides/employee-management-user-guide.md) 🆕 **Enhanced with Roles**
- [Role Management User Guide](user-guides/role-management-user-guide.md) 🆕 **TCM Roles Complete**

### Payroll Management 🆕
- [Payroll User Guide](user-guides/payroll-user-guide.md) 🆕 **Role-Based Salary Management**

### Data Management
- [Bulk Import System](user-guides/bulk-import-system.md) ⭐ **Updated** - Complete guide for importing employees, departments, and roles

## Quick Reference

- [Bulk Import Quick Guide](quick-reference/bulk-import-quick-guide.md) ⭐ **New** - Essential steps and troubleshooting

## Support

- [FAQ](support/faq.md)
- [Contact Support](support/contact.md)
- [Known Issues](support/known-issues.md)

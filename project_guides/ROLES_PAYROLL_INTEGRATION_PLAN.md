# Roles-Payroll Integration Plan for TCM Enterprise Suite

## Overview

This document outlines the comprehensive integration plan between the Employee Roles system and the Payroll system, specifically designed for the Teachers Council of Malawi (TCM) organizational structure.

## Current State Analysis

### Roles System ✅ COMPLETE
- **Location**: `/dashboard/employee/roles`
- **Features**: Full CRUD operations, bulk import, department association
- **Model**: Role with name, code, description, department reference
- **API**: Complete REST endpoints with permissions
- **Bulk Import**: Excel/CSV support with validation

### Payroll System ✅ MOSTLY COMPLETE
- **Models**: SalaryStructure, EmployeeSalary, SalaryRevision
- **Integration Point**: SalaryStructure.applicableRoles (string array)
- **Gap**: Employee.position is string, not linked to Role model

### TCM Role Hierarchy
```
TCM 1  - Executive Level (Registrar)
TCM 2  - Director Level (Directors)
TCM 3  - Manager Level (Managers)
TCM 4  - Senior Officer Level
TCM 5  - Officer Level (Most positions)
TCM 7  - Assistant Level
TCM 9  - Support Level
TCM 10 - Specialized Support (Head Driver)
TCM 11 - General Support (Driver)
TCM 12 - Basic Support (Office Assistant)
```

## Integration Plan

### Phase 1: Core Integration (Priority: HIGH)

#### 1.1 Employee-Role Linking
**Objective**: Link employees to formal roles instead of free-text positions

**Changes Required**:
- Add `roleId` field to Employee model
- Update employee forms to use Role dropdown
- Create migration script for existing position data
- Update employee bulk import to support role assignment

**Files to Modify**:
- `models/Employee.ts` - Add roleId field
- `components/employee/employee-form.tsx` - Add role selector
- `app/api/hr/employees/bulk-import/route.ts` - Support role mapping

#### 1.2 Role-Based Salary Band System
**Objective**: Create standardized salary bands based on TCM codes

**Implementation**:
- Create SalaryBand model with TCM code mapping
- Define salary ranges for each TCM level
- Implement automatic salary structure suggestion

**New Models**:
```typescript
// models/payroll/SalaryBand.ts
interface ISalaryBand {
  tcmCode: string;           // TCM 1, TCM 2, etc.
  name: string;              // Executive, Director, Manager, etc.
  minSalary: number;
  maxSalary: number;
  currency: string;
  allowances: string[];      // Standard allowances for this band
  effectiveDate: Date;
  isActive: boolean;
}
```

#### 1.3 Automatic Salary Assignment
**Objective**: Auto-suggest salary structures when assigning roles

**Features**:
- Role selection triggers salary structure suggestions
- Validation to ensure salary aligns with role band
- Warning system for out-of-band assignments

### Phase 2: Advanced Features (Priority: MEDIUM)

#### 2.1 Role-Based Bulk Operations
- Bulk salary updates by role/TCM code
- Role-based payroll reports
- Department-role salary analysis
- Annual increment processing by role band

#### 2.2 Promotion Workflows
- Role promotion with automatic salary adjustment
- Approval workflows for role changes
- Salary revision tracking for promotions

#### 2.3 Reporting & Analytics
- Salary distribution by role
- Department-wise role analysis
- Budget planning by role bands
- Compliance reporting for salary equity

### Phase 3: Enhanced Features (Priority: LOW)

#### 3.1 Step Progression System
- Within-band step progressions
- Automatic annual increments
- Performance-based step adjustments

#### 3.2 Role-Based Benefits
- Role-specific allowances
- Position-based deductions
- Automatic benefit assignment

## Implementation Steps

### Step 1: Data Preparation
1. ✅ Create TCM roles CSV template (`format_excel/tcm_roles_import_template.csv`)
2. Import TCM roles using existing bulk import system
3. Validate role-department mappings

### Step 2: Employee Model Enhancement
1. Add roleId field to Employee model
2. Create migration script for existing employees
3. Update employee forms and APIs

### Step 3: Salary Band Implementation
1. Create SalaryBand model
2. Define TCM salary bands
3. Implement role-salary validation

### Step 4: Integration Testing
1. Test role assignment workflows
2. Validate salary structure suggestions
3. Test bulk operations

## Technical Considerations

### Database Changes
- Employee model: Add `roleId: ObjectId` field
- New SalaryBand model
- Indexes for performance optimization

### API Enhancements
- Role-based employee filtering
- Salary structure suggestions endpoint
- Role-salary validation endpoints

### UI/UX Updates
- Role selector in employee forms
- Salary band indicators
- Role-based dashboard widgets

## Benefits

### For HR Management
- Standardized role definitions
- Consistent salary structures
- Automated compliance checking
- Simplified bulk operations

### For Payroll Processing
- Role-based salary validation
- Automated salary suggestions
- Streamlined promotion processing
- Enhanced reporting capabilities

### For Organizational Management
- Clear role hierarchy
- Transparent salary bands
- Budget planning support
- Compliance monitoring

## Next Steps

1. **Immediate**: Import TCM roles using existing system
2. **Week 1**: Implement Employee-Role linking
3. **Week 2**: Create SalaryBand model and basic integration
4. **Week 3**: Implement automatic salary suggestions
5. **Week 4**: Testing and refinement

## Files Created/Modified

### New Files
- `format_excel/tcm_roles_import_template.csv` ✅
- `models/payroll/SalaryBand.ts` (Planned)
- `project_guides/ROLES_PAYROLL_INTEGRATION_PLAN.md` ✅

### Files to Modify
- `models/Employee.ts` - Add roleId field
- Employee forms and components
- Payroll-related APIs and services
- Bulk import systems

This integration will create a robust, standardized system that aligns with TCM's organizational structure while providing powerful payroll management capabilities.

# Employee and Payroll Module Guide

This guide explains how the Employee and Payroll modules work together in the TCM Enterprise Business Suite, including how to use each component and how they contribute to the overall HR management system.

## Table of Contents

1. [Overview](#overview)
2. [User Guide](#user-guide)
   - [Getting Started](#getting-started)
   - [Navigation](#navigation)
   - [User Roles and Permissions](#user-roles-and-permissions)
   - [Common Workflows](#common-workflows)
   - [Reports and Analytics](#reports-and-analytics)
3. [Employee Module](#employee-module)
   - [Employee Directory](#employee-directory)
   - [Adding New Employees](#adding-new-employees)
   - [Employee Details](#employee-details)
   - [Bulk Employee Upload](#bulk-employee-upload)
   - [Bulk Employee Delete](#bulk-employee-delete)
   - [Employee Fields](#employee-fields)
   - [Department Management](#department-management)
4. [Payroll Module](#payroll-module)
   - [Salary Management](#salary-management)
   - [Running Payroll](#running-payroll)
   - [Payslip Generation](#payslip-generation)
   - [Payroll History](#payroll-history)
   - [Salary Structures](#salary-structures)
   - [Allowances Management](#allowances-management)
   - [Deductions Management](#deductions-management)
   - [Tax Brackets](#tax-brackets)
   - [Payroll Reports](#payroll-reports)
   - [Accounting Integration](#accounting-integration)
5. [Integration Between Modules](#integration-between-modules)
   - [Data Flow](#data-flow)
   - [Salary Revisions](#salary-revisions)
6. [Technical Guide](#technical-guide)
   - [System Architecture](#system-architecture)
   - [Data Models and Relationships](#data-models-and-relationships)
   - [API Integration Points](#api-integration-points)
   - [Services and Business Logic](#services-and-business-logic)
   - [Bulk Import/Export](#bulk-import-export)
   - [Customization Points](#customization-points)
   - [Database Schema](#database-schema)
   - [Security Implementation](#security-implementation)
7. [Best Practices](#best-practices)
8. [Troubleshooting Common Issues](#troubleshooting-common-issues)
9. [Appendix](#appendix)
   - [Glossary](#glossary)
   - [API Reference](#api-reference)
   - [Configuration Options](#configuration-options)

## Overview

The Employee and Payroll modules form the core of the HR management system for the Teachers Council of Malawi. The Employee module manages employee information, while the Payroll module handles salary structures, payroll processing, and payment management. These modules are designed to work together seamlessly, with employee data flowing into the payroll system for accurate salary processing.

The system is built to handle the specific requirements of a government entity, including robust accounting features, compliance with government reporting standards, and integration with professional accounting systems. The modules provide comprehensive functionality for managing teacher certification, collecting membership fees, and processing payroll according to Malawi's tax regulations.

## User Guide

This section provides guidance for end users of the Employee and Payroll modules, including HR staff, finance personnel, and administrators.

### Getting Started

**System Requirements**:
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Screen resolution of at least 1280x800
- JavaScript enabled

**Accessing the System**:
1. Open your web browser and navigate to the system URL
2. Enter your username and password
3. Click "Sign In" to access the dashboard
4. For first-time login, you may be prompted to change your password

**Dashboard Overview**:
- The main dashboard provides an overview of key metrics and quick access to common functions
- The left sidebar contains navigation to all system modules
- The top bar includes user profile, notifications, and global search
- Quick action buttons allow for common tasks like adding employees or running payroll

**Initial Setup Checklist**:
1. Verify department structure is set up correctly
2. Ensure roles are defined for all employee types
3. Set up salary structures for different employee categories
4. Configure tax brackets according to current regulations
5. Define allowance and deduction types
6. Set up bank information for payment processing

### Navigation

**Main Navigation Areas**:
- **Sidebar**: Primary navigation organized by module
- **Breadcrumbs**: Shows your current location in the system
- **Tab Navigation**: Used within detail pages to organize information
- **Action Buttons**: Located at the top-right of content areas for primary actions
- **Navigation Overlay**: Provides visual feedback during page transitions

**Navigation Overlay**:
- A translucent overlay appears during page transitions
- Shows "Taking you to [page name]..." in bold green text
- Provides visual feedback that the system is responding to user actions
- Disappears automatically when the new page loads
- Improves user experience by reducing perceived loading time
- Prevents multiple clicks during page transitions

**Employee Module Navigation**:
- Dashboard > Employees: Employee directory
- Dashboard > Employees > Add Employee: Create new employee
- Dashboard > Employees > Bulk Upload: Import multiple employees
- Dashboard > Departments: Department management

**Payroll Module Navigation**:
- Dashboard > Payroll: Payroll dashboard
- Dashboard > Payroll > Employee Salaries: Salary management
- Dashboard > Payroll > Run Payroll: Process payroll
- Dashboard > Payroll > Payslips: Generate and manage payslips
- Dashboard > Payroll > History: View payroll history
- Dashboard > Payroll > Salary Structures: Manage salary templates
- Dashboard > Payroll > Allowances: Manage allowance types
- Dashboard > Payroll > Deductions: Manage deduction types
- Dashboard > Payroll > Tax Brackets: Configure tax brackets

### User Roles and Permissions

The system implements role-based access control with the following key roles:

**Super Admin**:
- Full access to all system functions
- Can manage user accounts and permissions
- Can configure system settings
- Can access all reports and data

**HR Director**:
- Full access to employee and payroll modules
- Can manage departments and roles
- Can run payroll and approve salary changes
- Can access HR and payroll reports

**HR Manager**:
- Can manage employee records
- Can view salary information
- Can participate in payroll processing
- Limited access to sensitive reports

**Finance Director**:
- Full access to payroll and accounting modules
- Can approve payroll runs
- Can manage banking integration
- Can access financial reports

**Finance Officer**:
- Can process payroll under supervision
- Can generate payslips
- Can prepare financial reports
- Limited approval authority

**Department Manager**:
- Can view employees in their department
- Can view department payroll summaries
- Cannot access individual salary details
- Limited to department-specific reports

### Common Workflows

**New Employee Onboarding**:
1. HR collects employee information and documentation
2. HR creates employee record in the system
3. HR assigns appropriate department and role
4. Finance creates salary record with appropriate structure
5. System generates employee number and credentials
6. HR completes onboarding checklist

**Monthly Payroll Processing**:
1. HR verifies employee status updates (new hires, terminations, leaves)
2. Finance initiates payroll run for the month
3. System calculates salaries based on current records
4. Finance reviews calculations and makes adjustments if needed
5. Finance Director approves the payroll run
6. System generates payslips
7. Finance processes payments through banking integration
8. System updates payroll history and employee records

**Salary Revision Process**:
1. HR initiates salary change request
2. System records current salary details
3. HR enters new salary information with effective date
4. Appropriate authority approves the change
5. System creates new salary record with revision history
6. Change takes effect on the specified date

**Department Transfer**:
1. HR initiates department transfer for employee
2. HR selects new department and effective date
3. System updates employee record
4. System notifies relevant department managers
5. Payroll calculations automatically use new department in future runs

### Reports and Analytics

**Standard Reports**:
- **Employee Reports**:
  - Employee Directory
  - Department Headcount
  - New Hires and Terminations
  - Employee Demographics

- **Payroll Reports**:
  - Monthly Payroll Summary
  - Department Salary Breakdown
  - Allowance and Deduction Summary
  - Tax Remittance Report
  - Bank Payment Report

**Custom Reports**:
1. Navigate to the Reports section
2. Select report type and parameters
3. Apply filters as needed
4. Generate report in desired format (PDF, Excel, CSV)
5. Save report configuration for future use

**Analytics Dashboard**:
- Headcount trends by department
- Salary distribution analysis
- Payroll cost trends
- Tax and deduction analysis
- Budget vs. actual salary expenditure

## Employee Module

The Employee module serves as the central repository for all employee information, providing tools for managing employee records, tracking employment history, and maintaining personal and professional details.

### Employee Directory

**Location**: Dashboard > Employees

**Purpose**: Provides a comprehensive view of all employees in the organization.

**Key Features**:
- Searchable employee list with filtering options
- Quick access to employee details
- Visual indicators for employee status (active, on leave, terminated)
- Department and position information
- Bulk selection and deletion functionality
- Checkbox selection for multiple employees

**How to Use**:
1. Navigate to the Employees page from the dashboard
2. Use the search bar to find specific employees
3. Click on an employee card to view their detailed profile
4. Use filter options to narrow down the employee list by department, status, or other criteria
5. For bulk operations:
   - Select multiple employees using the checkboxes
   - Use the "Delete Selected" button that appears when employees are selected
   - Confirm the deletion in the confirmation dialog

**Contribution to System**: The Employee Directory serves as the entry point for all employee-related operations, providing quick access to employee records for payroll processing, leave management, and other HR functions.

### Adding New Employees

**Location**: Dashboard > Employees > Add Employee

**Purpose**: Allows HR personnel to add new employees to the system.

**Key Features**:
- Multi-step form with progress tracking
- Validation for required fields
- Ability to save progress at each step
- Comprehensive data collection (personal, employment, financial information)

**How to Use**:
1. Click the "Add Employee" button on the Employees page
2. Complete each step of the form:
   - Personal Information (name, contact details, etc.)
   - Employment Details (position, department, hire date, etc.)
   - Financial Information (salary, bank details, etc.)
   - Additional Information (emergency contacts, etc.)
3. Review all information before final submission
4. Submit the form to create the employee record

**Contribution to System**: The employee creation process collects all necessary information for both HR management and payroll processing, ensuring that new employees can be immediately integrated into the payroll system.

### Employee Details

**Location**: Dashboard > Employees > [Employee Name]

**Purpose**: Provides detailed view and management of individual employee information.

**Key Features**:
- Comprehensive employee profile
- Tabs for different categories of information
- Edit functionality for updating employee details
- Access to salary history and revisions
- Employment status management

**How to Use**:
1. Click on an employee in the Employee Directory
2. View the employee's profile information
3. Use the tabs to navigate between different categories of information
4. Click "Edit" to update employee details
5. Use specialized actions for status changes, department transfers, etc.

**Contribution to System**: The Employee Details page serves as the central hub for managing all aspects of an employee's record, providing the foundation for accurate payroll processing and HR management.

### Bulk Employee Upload

**Location**: Dashboard > Employees > Bulk Upload

**Purpose**: Allows for the addition of multiple employees at once via CSV/Excel file.

**Key Features**:
- Template download for proper formatting
- File validation before processing
- Preview of data before final import
- Error handling and reporting
- Support for all employee fields including new fields
- Progress tracking during upload
- Detailed error reporting for failed records
- Support for employee roles and departments

**How to Use**:
1. Click the "Bulk Upload" button on the Employees page
2. Download the template file
3. Fill in employee information according to the template
   - Personal information (name, email, phone, etc.)
   - Employment details (position, department, hire date, etc.)
   - Location information (address, district, village, T/A)
   - Employee type and role information
   - Next of kin and emergency contact details
4. Upload the completed file (CSV or Excel, up to 5MB)
5. Wait for validation and processing
6. Review the results, including any errors
7. Confirm to complete the import

**Employee Role Bulk Upload**:
The template includes a dedicated column for employee roles, which are different from authentication roles:
- Employee roles represent job functions (e.g., Teacher, Administrator, Support Staff)
- The system fetches all available roles from the database to populate the template
- During import, role names are matched to existing roles in the database
- If a role doesn't exist, the system will create a warning but still import the employee
- Roles can be used for reporting, filtering, and department-specific operations

**Contribution to System**: Bulk upload functionality streamlines the onboarding process for multiple employees, particularly useful during company expansion or when migrating from another system. The inclusion of employee roles enhances organizational structure and reporting capabilities.

### Bulk Employee Delete

**Location**: Dashboard > Employees

**Purpose**: Allows for the deletion of multiple employees at once, improving data management efficiency.

**Key Features**:
- Checkbox selection for individual or all employees
- Selection counter showing number of selected employees
- Confirmation dialog to prevent accidental deletions
- Loading indicator during deletion process
- Success notification with count of deleted records
- Automatic refresh of employee list after deletion

**How to Use**:
1. Navigate to the Employees page
2. Select employees to delete using the checkboxes:
   - Use individual checkboxes next to each employee
   - Use the header checkbox to select/deselect all employees
3. Click the "Delete Selected" button that appears when employees are selected
4. Review the confirmation dialog showing the number of employees to be deleted
5. Confirm the deletion
6. Wait for the process to complete
7. View the success notification

**Security and Permissions**:
- Bulk deletion is restricted to users with appropriate permissions:
  - SUPER_ADMIN
  - SYSTEM_ADMIN
  - HR_DIRECTOR
  - HR_MANAGER
- The system logs all deletion operations for audit purposes
- Deleted employees are soft-deleted, preserving data for historical reporting

**Contribution to System**: The bulk delete functionality streamlines data management by allowing administrators to efficiently remove multiple employee records at once, particularly useful for cleaning up test data or handling organizational restructuring.

### Employee Fields

**Location**: Throughout the Employee module

**Purpose**: Captures comprehensive employee information for HR management and payroll processing.

**Key Fields**:
- **Personal Information**:
  - First Name, Last Name, Email (required)
  - Date of Birth, Gender
  - Marital Status (single, married, divorced, widowed)
  - Number of Children
  - National ID
  - Photo
- **Location Information**:
  - Address, City, State, Postal Code, Country
  - Village
  - Traditional Authority (T/A)
  - District
- **Employment Information**:
  - Position (required)
  - Department
  - Role (job function, e.g., Teacher, Administrator, Support Staff)
  - Employment Type (full-time, part-time, contract, intern, temporary, volunteer)
  - Employment Status (active, inactive, on-leave, terminated)
  - Hire Date (required)
  - Termination Date (if applicable)
  - Manager
- **Financial Information**:
  - Salary
  - Bank Name
  - Bank Account Number
- **Emergency Contact**:
  - Next of Kin (name, relationship, phone, address)
  - Emergency Contact Name
  - Emergency Contact Phone
  - Emergency Contact Relationship

**How to Use**:
1. Fill in required fields (marked with *) at minimum
2. Provide as much additional information as available
3. Update fields as employee information changes

**Contribution to System**: Comprehensive employee data provides the foundation for accurate HR management, payroll processing, and reporting.

### Department Management

**Location**: Dashboard > Departments

**Purpose**: Manages organizational structure through department creation and management.

**Key Features**:
- Department listing with search and filter
- Department creation and editing
- Department hierarchy management
- Bulk department upload

**How to Use**:
1. Navigate to the Departments page
2. View existing departments or create new ones
3. Assign employees to departments
4. Use bulk upload for adding multiple departments

**Contribution to System**: Departments provide organizational structure for employee management and reporting.

## Payroll Module

The Payroll module handles all aspects of employee compensation, from defining salary structures to processing payroll and generating payslips.

### Salary Management

**Location**: Dashboard > Payroll > Employee Salaries

**Purpose**: Manages employee salary information, including basic salary, allowances, and deductions.

**Key Features**:
- Employee salary listing with search and filter options
- Salary structure assignment
- Allowance and deduction management
- Salary revision history
- Bank account and payment method management

**How to Use**:
1. Navigate to the Employee Salaries page
2. Search for specific employees or use filters to narrow the list
3. Click "New Salary" to create a salary record for an employee
4. Select an employee and salary structure
5. Define basic salary, allowances, and deductions
6. Set effective date and payment details
7. Save the salary record

**Contribution to System**: Salary Management provides the foundation for payroll processing by defining how much each employee should be paid, including all components of their compensation package.

### Running Payroll

**Location**: Dashboard > Payroll > Runs

**Purpose**: Processes payroll for a specific period, calculating salaries for all eligible employees.

> **PREREQUISITE**: Employees must have an EmployeeSalary record created through the "Create Employee Salary" functionality before they can be properly processed in a payroll run. See the [Critical Dependency](#critical-dependency-employeesalary-required-for-payroll-run) section for details.

**Key Features**:
- Comprehensive step-by-step wizard interface
- Pay period definition with month and year selection
- Department and employee filtering
- Real-time salary calculation with tax and deductions
- Detailed review and approval process
- Summary statistics for the payroll run
- Batch processing capabilities
- Consistent use of Malawi Kwacha (MWK) as default currency
- Enhanced date selection with calendar interface
- Filters to show only employees with valid salary records
- Payroll run status tracking (draft, processing, completed, approved, paid, cancelled)
- Export functionality for payroll data in Excel and PDF formats
- Detailed payroll run details page with employee records

**How to Use**:
1. Navigate to **Dashboard > Payroll > Runs**
2. Click the **New Payroll Run** button
3. Complete the payroll run wizard:
   - **Setup**: Define pay period, name, description, and select departments
   - **Review**: Review the payroll run details
   - **Complete**: Finalize the payroll run creation
4. After creation, the payroll run will be in **Draft** status
5. Click on the payroll run to view details
6. Click the **Process Payroll** button to calculate salaries
7. Once processing is complete, the status will change to **Completed**
8. Click the **Approve** button to approve the payroll run
9. Generate payslips after approval

**Payroll Run Wizard**:
1. **Setup**:
   - Name the payroll run (defaults to "Payroll for [Month] [Year]")
   - Add a description
   - Select month and year for the pay period using dropdown menus
   - Define start and end dates using the enhanced date picker
   - Choose departments to include (or select all)
   - The system automatically calculates appropriate start and end dates based on the selected month and year
   - All fields are validated to ensure proper payroll setup

2. **Review**:
   - Review the payroll run details
   - Verify the pay period, departments, and other settings
   - Make any necessary adjustments before creation

3. **Complete**:
   - Confirmation of successful payroll run creation
   - Summary of the payroll run with key details
   - Options for next steps (process payroll, view details)

**Payroll Run Processing**:
1. From the Payroll Runs page, click on a payroll run in **Draft** status
2. Click the **Process Payroll** button
3. The system will calculate salaries for all employees included in the run:
   - Basic salary is taken from the employee's assigned salary structure or individual salary record
   - Allowances are calculated based on fixed amounts or percentages of basic salary
   - Deductions are calculated based on fixed amounts or percentages of basic salary
   - Tax is calculated using Malawi's progressive tax brackets
   - Net salary is calculated as gross salary minus deductions and tax
4. Once processing is complete, the status will change to **Completed**
5. The system will display summary statistics:
   - Total employees processed
   - Total gross salary
   - Total deductions
   - Total tax
   - Total net salary

**Payroll Run Approval**:
1. From the Payroll Runs page, click on a payroll run in **Completed** status
2. Review the payroll details and summary
3. Click the **Approve** button
4. Once approved, the status will change to **Approved**
5. Approved payroll runs can be used to generate payslips

**Payroll Run Details**:
The payroll run details page provides comprehensive information about a payroll run:
- Summary information (name, period, status, totals)
- Employee records with salary details
- Action buttons for processing, approving, generating payslips, and exporting
- Tabs for different views (employee records, department summary)

**Exporting Payroll Data**:
1. From the Payroll Run details page, click **Export to Excel** or **Export to PDF**
2. The system will generate the export file and download it to your device
3. Excel exports include:
   - Summary sheet with payroll run details and totals
   - Employee details sheet with individual salary breakdowns
4. PDF exports include:
   - Formatted report with payroll run details
   - Summary statistics
   - Employee salary table

**Contribution to System**: The Payroll Run process is the core functionality of the Payroll module, transforming salary definitions into actual payment records for a specific period. It ensures accurate and consistent salary calculations while providing transparency and detailed breakdowns for verification and compliance purposes.

### Payslip Generation

**Location**: Dashboard > Payroll > Payslips

**Purpose**: Creates and manages payslips for employees based on processed payroll runs.

**Key Features**:
- Automatic generation from payroll runs
- Detailed breakdown of earnings and deductions
- PDF generation for printing and distribution
- Email distribution capabilities
- Payslip history and archiving
- Bulk actions for multiple payslips
- Payslip viewer with print and download options
- Email tracking for payslip distribution
- Download history tracking
- Search and filter functionality

**How to Use**:
1. Navigate to **Dashboard > Payroll > Payslips**
2. Select a payroll run from the dropdown
3. Click **Generate Payslips** in the Payslip Actions card to create payslips for all employees in the run
4. Browse the list of payslips or use the search function to find specific employees
5. Use the actions menu for individual payslips:
   - **View**: Open the payslip viewer
   - **Download**: Download the payslip as PDF
   - **Email**: Send the payslip to the employee's email
   - **Print**: Open the payslip in print view
6. Use bulk actions for multiple payslips:
   - **Generate Payslips**: Create payslips for all employees
   - **Email All**: Send payslips to all employees
   - **Download All**: Download all payslips as a ZIP file
   - **Print All**: Open all payslips in print view

**Payslip Viewer**:
The payslip viewer provides a detailed view of an employee's payslip:
- Employee information (name, ID, department, position)
- Pay period details
- Earnings breakdown (basic salary, allowances)
- Deductions breakdown (tax, pension, etc.)
- Summary with gross salary, total deductions, and net salary
- Action buttons for printing, downloading, and emailing

**Payslip Content**:
Each payslip includes:
- Company header with logo
- Employee details section
- Pay period information
- Earnings table with all components
- Deductions table with all components
- Summary section with totals
- Payment information
- Footer with notes and disclaimers

**Contribution to System**: Payslip Generation provides the final output of the payroll process, delivering detailed payment information to employees and creating records for accounting and compliance purposes. The enhanced payslip management features ensure efficient distribution and tracking of payslips.

### Payroll History

**Location**: Dashboard > Payroll > History

**Purpose**: Maintains a record of all payroll runs and allows for historical reporting.

**Key Features**:
- Chronological listing of payroll runs
- Status tracking (draft, processing, completed, approved, paid)
- Summary statistics for each run
- Detailed drill-down capabilities
- Export functionality for reporting

**How to Use**:
1. Navigate to the Payroll History page
2. Browse through past payroll runs
3. Click on a specific run to view details
4. Use filter options to find specific payroll runs
5. Export data for external reporting

**Contribution to System**: Payroll History provides transparency and accountability for the payroll process, allowing for auditing, reporting, and historical analysis.

### Salary Structures

**Location**: Dashboard > Payroll > Salary Structures

**Purpose**: Defines templates for salary packages that can be applied to employees.

**Key Features**:
- Create and manage salary structure templates
- Define basic salary, allowances, and deductions
- Assign structures to specific roles or departments
- Set effective dates for structures
- Bulk upload of salary structures
- Bulk deletion of multiple salary structures
- Default currency set to Malawi Kwacha (MWK)
- Detailed view of salary structure components

**How to Use**:
1. Navigate to the Salary Structures page
2. Create a new structure or edit existing ones
3. Define components (basic salary, allowances, deductions)
4. Set effective date and applicable roles/departments
5. For bulk upload:
   - Click "Bulk Upload" button
   - Download the template
   - Fill in structure information
   - Upload the completed file
   - Review and confirm import
6. For bulk deletion:
   - Select multiple structures using the checkboxes
   - Click "Delete Selected" button that appears
   - Confirm deletion in the confirmation dialog

**Viewing Salary Structure Details**:
1. Click on a salary structure to view its details
2. The details view shows:
   - Basic information (name, description, effective date)
   - Currency (displayed as MWK for Malawi Kwacha)
   - Components breakdown (basic salary, allowances, deductions)
   - Applicable roles and departments
3. Use the tabs to navigate between:
   - Overview: Summary of salary components and totals
   - Components: Detailed list of all components with amounts/percentages
   - Applicable To: Roles and departments this structure applies to

**Contribution to System**: Salary structures standardize compensation packages across the organization and simplify salary assignment for new employees. The default Malawi Kwacha (MWK) currency ensures consistency with local financial practices.

### Allowances Management

**Location**: Dashboard > Payroll > Allowances

**Purpose**: Manages different types of allowances that can be added to employee compensation.

**Key Features**:
- Create and manage allowance types
- Define calculation methods (fixed or percentage-based)
- Set taxability status for each allowance
- Assign allowances to specific roles or departments
- Bulk upload of allowances

**How to Use**:
1. Navigate to the Allowances page
2. Create new allowance types or edit existing ones
3. Define calculation method and default amount/percentage
4. Set whether the allowance is taxable
5. For bulk upload:
   - Click "Bulk Upload" button
   - Download the template
   - Fill in allowance information
   - Upload the completed file
   - Review and confirm import

**Contribution to System**: Allowances provide flexibility in compensation packages and ensure consistent application of benefits across the organization.

### Deductions Management

**Location**: Dashboard > Payroll > Deductions

**Purpose**: Manages different types of deductions that can be applied to employee salaries.

**Key Features**:
- Create and manage deduction types
- Define calculation methods (fixed or percentage-based)
- Set statutory status for deductions
- Assign deductions to specific roles or departments
- Bulk upload of deductions

**How to Use**:
1. Navigate to the Deductions page
2. Create new deduction types or edit existing ones
3. Define calculation method and default amount/percentage
4. Set whether the deduction is statutory
5. For bulk upload:
   - Click "Bulk Upload" button
   - Download the template
   - Fill in deduction information
   - Upload the completed file
   - Review and confirm import

**Contribution to System**: Deductions ensure accurate calculation of net pay and compliance with statutory requirements.

### Tax Brackets

**Location**: Dashboard > Payroll > Tax Brackets

**Purpose**: Defines tax brackets for PAYE calculations based on Malawi regulations.

**Key Features**:
- Predefined tax brackets for Malawi
- Ability to customize brackets if regulations change
- Support for different currencies
- Effective date tracking for tax changes

**How to Use**:
1. Navigate to the Tax Brackets page
2. View existing tax brackets
3. Create or edit brackets if needed
4. Set effective dates for new tax regulations

**Contribution to System**: Tax brackets ensure accurate tax calculations in compliance with local regulations.

### Payroll Reports

**Location**: Dashboard > Payroll > Reports

**Purpose**: Generates comprehensive reports for payroll data analysis, compliance, and decision-making.

**Key Features**:
- Multiple report types (payroll summary, employee earnings, department summary, etc.)
- Flexible date range selection
- Department and employee filtering
- Format options (PDF, Excel, CSV)
- Report scheduling and automation
- Historical report storage and retrieval
- Detailed breakdowns of salary components
- Tax and deduction summaries
- Year-to-date (YTD) reporting

**How to Use**:
1. Navigate to the Payroll Reports page
2. Select the "Generate Report" tab or click the "Generate Report" button
3. Choose a report type from the available options:
   - Payroll Summary: Overview of all payroll data for a period
   - Employee Earnings: Detailed breakdown of individual employee earnings
   - Department Summary: Payroll data grouped by department
   - Tax Summary: Breakdown of tax calculations and remittances
   - Deduction Summary: Analysis of all deductions applied
   - Allowance Summary: Analysis of all allowances granted
   - YTD Summary: Year-to-date totals for all payroll components
4. Specify the date range for the report
5. Apply filters for departments and employees if needed
6. Select additional options like grouping and sorting
7. Choose the output format (PDF, Excel, CSV)
8. Generate the report
9. View, download, or share the generated report

**Report Management**:
- All generated reports are stored in the system for future reference
- Reports can be downloaded multiple times
- Reports can be deleted when no longer needed
- The system tracks report generation and download history

**Contribution to System**: Payroll Reports provide essential insights for financial planning, compliance reporting, and management decision-making. They enable detailed analysis of payroll expenses and help identify trends and anomalies.

### Accounting Integration

**Location**: Dashboard > Payroll > Accounting

**Purpose**: Integrates payroll processing with the accounting system to ensure financial data consistency and automate journal entries.

**Key Features**:
- Automatic journal entry creation for payroll runs
- Payroll payment processing with bank account integration
- Detailed transaction history
- Configurable account mapping
- Support for different accounting periods
- Audit trail for all financial transactions
- Integration with the general ledger
- Reconciliation tools

**How to Use**:
1. Navigate to the Payroll Accounting page
2. Select a processed payroll run from the dropdown
3. View the payroll summary information
4. Create journal entries:
   - Click "Create Journal Entries" to generate accounting entries
   - The system creates appropriate debits and credits for salary expenses, payables, and taxes
   - Review the created journal entries
5. Post journal entries:
   - Once journal entries are created, click "Post Journal Entries" to finalize them
   - Posted entries are locked and reflected in the general ledger
6. Process payments:
   - Select a bank account for payment
   - Specify the payment date
   - Click "Process Payment" to create payment journal entries
   - The system creates entries to debit salary payable and credit the bank account

**Account Mapping**:
The system uses the following default accounts for payroll transactions:
- Salary Expense (5100): For basic salary costs
- Allowances Expense (5110): For employee allowances
- Salary Payable (2100): For net salary owed to employees
- Tax Payable (2200): For taxes withheld from employees
- Deductions Payable (2300): For other deductions

**Contribution to System**: Accounting Integration ensures that payroll data is properly reflected in the organization's financial records, eliminating manual data entry and reducing errors. It provides a seamless flow of information between HR and finance departments, ensuring consistency and accuracy in financial reporting.

## Integration Between Modules

The Employee and Payroll modules are tightly integrated, with data flowing seamlessly between them to ensure accurate and efficient payroll processing.

### Critical Dependency: EmployeeSalary Required for Payroll Run

> **IMPORTANT**: For an employee to be properly included in a payroll run, they **MUST** first have an EmployeeSalary record created through the "Create Employee Salary" functionality.

```
┌─────────────────┐     ┌─────────────────────┐     ┌───────────────┐
│                 │     │                     │     │               │
│  Create         │     │  Create Employee    │     │  Run          │
│  Employee       ├────►│  Salary             ├────►│  Payroll      │
│  Record         │     │  Record             │     │               │
│                 │     │                     │     │               │
└─────────────────┘     └─────────────────────┘     └───────────────┘
```

#### Why This Dependency Exists:

1. **Basic vs. Detailed Information**:
   - The `Employee` model contains only basic information (name, position, etc.)
   - The `EmployeeSalary` model contains the detailed salary structure needed for payroll:
     - Basic salary amount
     - Allowances (housing, transport, etc.)
     - Deductions (pension, health insurance, etc.)
     - Tax settings
     - Payment method details

2. **Calculation Requirements**:
   - To run payroll correctly, the system needs to know:
     - Which allowances are taxable vs. non-taxable
     - Which components are pensionable
     - Whether deductions are statutory or voluntary
     - The exact breakdown of salary components
   - This detailed information only exists in the `EmployeeSalary` record

#### Correct Workflow:

1. **Create Employee Records** - Add employees to the system with their basic information
2. **Create Salary Structures** - Define standard salary templates for different roles
3. **Create Employee Salary Records** - Assign detailed salary information to each employee
4. **Run Payroll** - Process payroll using the detailed salary information

Without an EmployeeSalary record, an employee may appear in the list but cannot be properly processed in the payroll run.

### Data Flow

1. **Employee Creation to Salary Assignment**:
   - When a new employee is created in the Employee module, their basic information is stored in the Employee model
   - A salary record must then be created in the Payroll module, linking to the employee via their ID
   - The salary record includes detailed compensation information beyond the basic salary field in the Employee model

2. **Salary Updates to Payroll Processing**:
   - Changes to an employee's salary in the Salary Management component are tracked with effective dates
   - The Payroll Run process uses the active salary record for each employee based on the pay period dates
   - This ensures that salary changes are applied at the correct time in the payroll cycle

3. **Payroll Results to Employee Records**:
   - Processed payroll information, including payment history, is accessible from the Employee Details page
   - This provides a complete view of an employee's compensation history

### Salary Revisions

The system maintains a history of salary changes through the Salary Revision feature:

1. When a salary change is made, the previous salary record is deactivated
2. A new salary record is created with the updated information
3. A salary revision record links the two, recording the reason for the change and approval details
4. This creates an audit trail of all salary changes over time

## Technical Guide

### System Architecture

The Employee and Payroll modules are built on a modern web application architecture with the following components:

1. **Frontend**:
   - Next.js framework for server-side rendering and static generation
   - React for component-based UI development
   - Tailwind CSS for styling
   - ShadcN UI component library
   - React Hook Form for form handling
   - Zod for schema validation
   - React Query for data fetching and caching

2. **Backend**:
   - Next.js API routes for server-side logic
   - MongoDB for database storage
   - Mongoose for object modeling
   - NextAuth.js for authentication
   - Multer for file uploads
   - XLSX library for Excel processing

3. **Infrastructure**:
   - Deployed on Railway.com
   - Automatic deployment from master branch
   - Environment variables for configuration
   - MongoDB Atlas for database hosting

4. **Integration Points**:
   - Banking API integration for payment processing
   - PDF generation for payslips and reports
   - Email service for notifications
   - Excel import/export for bulk operations

### Data Models and Relationships

1. **Employee Model** (`models/Employee.ts`):
   - Primary model for storing employee information
   - Contains basic fields like name, contact details, position, etc.
   - Includes a simple `salary` field as a reference value
   - Referenced by the EmployeeSalary model via `employeeId`

2. **EmployeeSalary Model** (`models/payroll/EmployeeSalary.ts`):
   - Stores detailed salary information for employees
   - Links to Employee model via `employeeId` field
   - Contains fields for basic salary, currency, effective dates
   - Includes arrays for allowances and deductions
   - Tracks payment methods and bank details
   - Maintains active status to identify current salary record

3. **SalaryStructure Model** (`models/payroll/SalaryStructure.ts`):
   - Defines templates for salary packages
   - Contains components like basic salary, allowances, and deductions
   - Can be applied to specific roles or departments
   - Referenced by EmployeeSalary via `salaryStructureId`

4. **PayrollRun Model** (`models/payroll/PayrollRun.ts`):
   - Represents a payroll processing cycle for a specific period
   - Tracks status through various stages (draft, processing, completed, etc.)
   - Stores summary information like total amounts
   - Links to individual payroll records

5. **PaySlip Model** (`models/payroll/PaySlip.ts`):
   - Represents an individual employee's payment record for a period
   - Contains detailed breakdown of earnings and deductions
   - Includes payment details and status
   - Links to both Employee and PayrollRun models

### API Integration Points

1. **Employee Creation to Salary Assignment**:
   - `POST /api/employees`: Creates a new employee record
   - `POST /api/payroll/employee-salaries`: Creates a salary record for an employee

2. **Salary Updates and Revisions**:
   - `PATCH /api/payroll/employee-salaries/[id]`: Updates an existing salary record
   - `POST /api/employees/[id]/salary-revisions`: Creates a salary revision record

3. **Payroll Processing**:
   - `POST /api/payroll/runs`: Creates a new payroll run
   - `PATCH /api/payroll/runs/[id]`: Updates payroll run status
   - `POST /api/payroll/runs/[id]/process`: Processes a payroll run
   - `POST /api/payroll/runs/[id]/payslips`: Generates payslips for a payroll run

4. **Salary Calculation**:
   - `POST /api/payroll/calculate-salary`: Calculates salary for an employee
   - `GET /api/payroll/employees/[id]/salary-history`: Gets salary history for an employee

5. **Payroll Reporting**:
   - `GET /api/payroll/reports`: Lists all payroll reports with filtering options
   - `POST /api/payroll/reports`: Generates a new payroll report
   - `GET /api/payroll/reports/[id]`: Gets details of a specific report
   - `GET /api/payroll/reports/[id]/download`: Downloads a generated report
   - `DELETE /api/payroll/reports`: Deletes a report by ID

6. **Payroll Accounting Integration**:
   - `POST /api/payroll/accounting`: Handles various accounting actions:
     - `action=create_journal_entries`: Creates journal entries for a payroll run
     - `action=post_journal_entries`: Posts journal entries to the general ledger
     - `action=create_payment_journal_entries`: Creates payment journal entries

### Services and Business Logic

1. **SalaryService** (`services/payroll/SalaryService.ts`):
   - Creates and manages employee salaries
   - Handles salary revisions and history
   - Provides methods for retrieving active salary information

2. **PayrollService** (`services/payroll/PayrollService.ts`):
   - Manages payroll operations
   - Creates and processes payroll runs
   - Handles approval and payment workflows
   - Implements the multi-step wizard process for payroll runs
   - Manages state between wizard steps

3. **SalaryCalculationService**:
   - Calculates employee salaries with allowances, deductions, and taxes
   - Applies tax brackets and other calculations
   - Handles currency conversion if needed
   - Implements progressive tax calculation based on Malawi tax brackets
   - Calculates allowances and deductions based on fixed amounts or percentages

4. **PayslipGenerationService**:
   - Generates payslips from payroll records
   - Creates PDF documents for distribution
   - Manages payslip status and delivery

5. **PayrollRunWizardService**:
   - Manages the multi-step wizard for payroll processing
   - Handles state management between steps
   - Provides validation for each step
   - Coordinates the overall payroll run process
   - Implements the wizard UI components and navigation

6. **PayrollReportingService** (`services/payroll/payroll-reporting-service.ts`):
   - Generates various types of payroll reports (summary, employee earnings, department, tax, etc.)
   - Supports multiple output formats (PDF, Excel, CSV)
   - Manages report storage and retrieval
   - Provides filtering and customization options
   - Implements report templates and formatting
   - Handles report generation in the background
   - Manages report metadata and tracking

7. **PayrollAccountingService** (`services/payroll/payroll-accounting-service.ts`):
   - Creates journal entries for payroll runs
   - Posts journal entries to the general ledger
   - Processes payroll payments
   - Manages account mapping for payroll transactions
   - Handles fiscal year and period calculations
   - Integrates with the accounting module
   - Provides transaction history and audit trail

### Bulk Import/Export

The system provides comprehensive bulk import and export functionality for various entities:

1. **Employee Bulk Import**:
   - API Endpoint: `POST /api/employees/bulk-import`
   - Template Endpoint: `GET /api/employees/template`
   - Supports CSV and Excel formats
   - Handles all employee fields including new fields
   - Provides detailed error reporting

2. **Department Bulk Import**:
   - API Endpoint: `POST /api/hr/departments/bulk-import`
   - Template Endpoint: `GET /api/hr/departments/template`
   - Supports CSV and Excel formats
   - Validates department hierarchy

3. **Salary Structure Bulk Import**:
   - API Endpoint: `POST /api/payroll/salary-structures/bulk-import`
   - Template Endpoint: `GET /api/payroll/salary-structures/template`
   - Supports CSV and Excel formats
   - Validates structure components
   - Fetches real roles and departments from the database for template generation
   - Maps role and department names to IDs during import

4. **Allowance Bulk Import**:
   - API Endpoint: `POST /api/payroll/allowances/bulk-import`
   - Template Endpoint: `GET /api/payroll/allowances/template`
   - Supports CSV and Excel formats
   - Validates calculation methods

5. **Deduction Bulk Import**:
   - API Endpoint: `POST /api/payroll/deductions/bulk-import`
   - Template Endpoint: `GET /api/payroll/deductions/template`
   - Supports CSV and Excel formats
   - Validates statutory requirements

6. **Role Bulk Import**:
   - API Endpoint: `POST /api/hr/roles/bulk-import`
   - Template Endpoint: `GET /api/hr/roles/template`
   - Supports CSV and Excel formats
   - Validates role permissions

Each bulk import feature follows a consistent pattern:
- Template download with sample data
- File validation (type, size, format)
- Data validation
- Progress tracking during import
- Detailed error reporting
- Success summary

### Salary Structure Management Features

The salary structure management system includes several key features that have been enhanced for better usability and functionality:

#### Bulk Deletion

The bulk deletion feature allows administrators to efficiently manage salary structures by deleting multiple structures at once:

1. **User Interface**:
   - Checkboxes next to each salary structure in the list
   - "Delete Selected" button that appears when structures are selected
   - Confirmation dialog with count of selected structures
   - Loading indicator during deletion process

2. **API Implementation**:
   - Endpoint: `POST /api/payroll/salary-structures/bulk-delete`
   - Accepts an array of structure IDs to delete
   - Performs validation to ensure structures aren't in use
   - Returns success count and detailed error messages if needed

3. **Security**:
   - Limited to SUPER_ADMIN and SYSTEM_ADMIN roles
   - Requires authentication with proper session handling
   - Prevents deletion of structures currently assigned to employees

#### Default Currency Implementation

The system now consistently uses Malawi Kwacha (MWK) as the default currency throughout the application:

1. **Data Models**:
   - SalaryStructure model defaults to 'MWK' for the currency field
   - EmployeeSalary model defaults to 'MWK' for the currency field
   - All related models use 'MWK' as the default currency

2. **User Interface**:
   - Currency displays show "MK" symbol instead of "$" for Malawi Kwacha
   - Currency selection defaults to MWK in all forms
   - Currency store initializes with MWK regardless of browser locale

3. **Currency Conversion**:
   - All currency conversions use MWK as the base currency
   - Exchange rates are defined relative to MWK

#### Salary Structure Details View

The salary structure details view provides a comprehensive overview of a structure:

1. **Overview Tab**:
   - Summary cards for basic salary, allowances, and deductions
   - Detailed salary breakdown with all components
   - Calculation of gross and net salary

2. **Components Tab**:
   - Tabular view of all components with type, amount/percentage, and taxability
   - Color-coded badges for different component types
   - Proper currency display for all monetary values

3. **Applicable To Tab**:
   - Lists of roles and departments the structure applies to
   - Visual indicators for empty lists

### Salary Structure Bulk Import Process

The salary structure bulk import process is a key feature that allows HR and finance personnel to efficiently set up and manage salary structures. Here's a detailed explanation of how it works:

#### Template Generation

The template generation process is handled by the `GET /api/payroll/salary-structures/template` endpoint:

1. **Authentication and Authorization**:
   - The endpoint verifies that the user is authenticated and has the required permissions (SUPER_ADMIN, SYSTEM_ADMIN, FINANCE_DIRECTOR, or HR_DIRECTOR).

2. **Database Connection**:
   - The endpoint connects to the database to fetch real data.

3. **Fetching Real Data**:
   - It retrieves all active departments from the database:
     ```typescript
     const departments = await Department.find({ isActive: true }).select('name').lean()
     ```
   - It retrieves all active roles from the database:
     ```typescript
     const roles = await Role.find({ isActive: true }).select('name').lean()
     ```

4. **Creating the Template**:
   - It creates an Excel workbook with multiple sheets:
     - **Salary Structures**: The main sheet with sample data and headers
     - **Roles Reference**: A sheet listing all available roles from the database
     - **Departments Reference**: A sheet listing all available departments from the database
     - **Instructions**: A sheet with detailed instructions for filling out the template

5. **Reference Sheets**:
   - The Roles Reference sheet is populated with actual role names from the database
   - The Departments Reference sheet is populated with actual department names from the database

#### Bulk Import Processing

The bulk import process is handled by the `POST /api/payroll/salary-structures/bulk-import` endpoint:

1. **File Validation**:
   - It validates the uploaded file (type, size, etc.).

2. **Database Connection**:
   - It connects to the database to fetch real data.

3. **Fetching Reference Data**:
   - It fetches all departments and roles from the database and creates maps for quick lookup:
     ```typescript
     const departments = await Department.find({}).lean()
     const departmentMap = new Map()
     departments.forEach(dept => {
       departmentMap.set(dept.name.toLowerCase(), dept._id)
     })

     const roles = await Role.find({}).lean()
     const roleMap = new Map()
     roles.forEach(role => {
       roleMap.set(role.name.toLowerCase(), role._id)
     })
     ```

4. **Processing Each Row**:
   - For each row in the uploaded file, it:
     - Normalizes column names
     - Validates required fields
     - Processes dates and boolean fields
     - Processes applicable roles and departments

5. **Processing Applicable Roles and Departments**:
   - It splits comma-separated lists of role and department names
   - It looks up each name in the respective map to get the corresponding ID
   - It logs warnings for names that don't match any database records
   - It stores the IDs in the salary structure document

6. **Creating Salary Structures**:
   - It creates a new salary structure document for each valid row
   - It includes the mapped role and department IDs

7. **Error Handling and Reporting**:
   - It tracks successful and failed imports
   - It provides detailed error messages for failed imports
   - It logs all actions for auditing purposes

This implementation ensures that users can easily import salary structures with the correct roles and departments, and that the system properly validates and processes these fields using real data from the database.

### Payroll Run UI Components

The Payroll Run feature is built using several key UI components that work together to create a seamless user experience:

1. **PayrollRunWizard** (`components/payroll/payroll-run/payroll-run-wizard.tsx`):
   - Main container component for the payroll run process
   - Implements the step navigation and progress tracking
   - Manages the overall state of the payroll run
   - Handles transitions between steps
   - Provides consistent UI layout across all steps

2. **PayrollRunSetup** (`components/payroll/payroll-run/payroll-run-setup.tsx`):
   - Handles the first step of the wizard
   - Implements form validation using React Hook Form and Zod
   - Uses EnhancedDatePicker for date selection
   - Provides department selection functionality
   - Automatically updates dates when month/year changes

3. **PayrollRunEmployees** (`components/payroll/payroll-run/payroll-run-employees.tsx`):
   - Manages employee selection in the second step
   - Implements department-based grouping of employees
   - Provides search and filter functionality
   - Handles bulk selection of employees by department
   - Displays employee salary information

4. **PayrollRunCalculation** (`components/payroll/payroll-run/payroll-run-calculation.tsx`):
   - Handles the calculation process in the third step
   - Implements real-time progress tracking
   - Displays summary cards for totals
   - Provides detailed employee salary breakdown
   - Implements the employee details dialog

5. **PayrollRunReview** (`components/payroll/payroll-run/payroll-run-review.tsx`):
   - Presents final review in the fourth step
   - Displays comprehensive summary information
   - Allows for final verification before submission
   - Provides options for adjustments if needed

6. **PayrollRunComplete** (`components/payroll/payroll-run/payroll-run-complete.tsx`):
   - Shows completion confirmation in the final step
   - Displays summary of the processed payroll
   - Provides options for next steps
   - Includes links to generated payslips

7. **EnhancedDatePicker** (`components/ui/enhanced-date-picker.tsx`):
   - Custom date picker component used throughout the application
   - Provides both calendar selection and manual input
   - Includes month and year dropdown selectors
   - Supports both value/onChange and date/setDate prop patterns
   - Implements proper date validation and formatting
   - Handles different date formats and conversions

8. **CurrencyDisplay** (`components/ui/currency-display.tsx`):
   - Formats and displays currency values consistently
   - Uses Malawi Kwacha (MWK) as the default currency
   - Implements proper number formatting with commas
   - Handles different currency symbols

### Customization Points

The Employee and Payroll modules are designed to be customizable to accommodate changing business requirements. The following are key customization points:

1. **Tax Calculation Logic**:
   - Located in `services/payroll/TaxCalculationService.ts`
   - Can be modified to implement different tax regimes
   - Tax brackets are stored in the database and can be updated through the UI

2. **Salary Structure Components**:
   - New component types can be added to the SalaryStructure model
   - Calculation logic can be extended in the SalaryCalculationService

3. **Employee Fields**:
   - The Employee model can be extended with additional fields
   - UI components in `components/employees/employee-form` need to be updated
   - Bulk upload templates need to be updated in `app/api/employees/template/route.ts`

4. **Payslip Templates**:
   - Payslip layout and content can be customized in `components/payroll/payslip-template.tsx`
   - PDF generation options can be modified in `services/payroll/PayslipGenerationService.ts`

5. **Workflow Approvals**:
   - Approval workflows can be customized in `services/payroll/PayrollApprovalService.ts`
   - Additional approval steps can be added to the PayrollRun model

6. **Reports and Exports**:
   - Report templates are defined in `components/reports`
   - Export formats and data can be customized in `services/reports`

7. **Date Picker Customization**:
   - The EnhancedDatePicker component can be customized in `components/ui/enhanced-date-picker.tsx`
   - Date formatting and validation can be modified to support different locales
   - Calendar appearance can be customized through the component props

### Database Schema

The database schema is implemented using Mongoose schemas in the models directory. Key schema files include:

1. **Employee Schema** (`models/Employee.ts`):
   ```typescript
   const EmployeeSchema = new Schema({
     firstName: { type: String, required: true },
     lastName: { type: String, required: true },
     email: { type: String, required: true, unique: true },
     employeeNumber: { type: String, unique: true },
     position: { type: String, required: true },
     department: { type: Schema.Types.ObjectId, ref: 'Department' },
     hireDate: { type: Date, required: true },
     // Additional fields...
   }, { timestamps: true });
   ```

2. **EmployeeSalary Schema** (`models/payroll/EmployeeSalary.ts`):
   ```typescript
   const EmployeeSalarySchema = new Schema({
     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
     salaryStructure: { type: Schema.Types.ObjectId, ref: 'SalaryStructure' },
     basicSalary: { type: Number, required: true },
     currency: { type: String, default: 'MWK' },
     effectiveDate: { type: Date, required: true },
     expiryDate: { type: Date },
     isActive: { type: Boolean, default: true },
     allowances: [AllowanceSchema],
     deductions: [DeductionSchema],
     // Additional fields...
   }, { timestamps: true });
   ```

3. **PayrollRun Schema** (`models/payroll/PayrollRun.ts`):
   ```typescript
   const PayrollRunSchema = new Schema({
     name: { type: String, required: true },
     description: { type: String },
     period: {
       month: { type: Number, required: true },
       year: { type: Number, required: true },
       startDate: { type: Date, required: true },
       endDate: { type: Date, required: true }
     },
     status: {
       type: String,
       enum: ['draft', 'processing', 'completed', 'approved', 'paid'],
       default: 'draft'
     },
     departments: [{ type: Schema.Types.ObjectId, ref: 'Department' }],
     employees: [{ type: Schema.Types.ObjectId, ref: 'Employee' }],
     // Additional fields...
   }, { timestamps: true });
   ```

### Security Implementation

The system implements several security measures to protect sensitive payroll and employee data:

1. **Authentication**:
   - NextAuth.js for secure authentication
   - Password hashing and salting
   - Session management with secure cookies
   - Multi-factor authentication support

2. **Authorization**:
   - Role-based access control
   - Permission checking middleware
   - API route protection
   - UI component conditional rendering based on permissions

3. **Data Protection**:
   - Encryption of sensitive data in the database
   - Secure API endpoints with proper validation
   - HTTPS for all communications
   - Rate limiting to prevent brute force attacks

4. **Audit Trails**:
   - Comprehensive logging of all actions
   - User activity tracking
   - Change history for sensitive operations
   - Immutable records for financial transactions

5. **Implementation Details**:
   - Permission checks are implemented in `lib/backend/utils/permissions.ts`
   - API routes are protected with session validation
   - Sensitive operations require additional confirmation
   - Database queries are sanitized to prevent injection attacks

## Best Practices

1. **Employee Data Management**:
   - Ensure all required employee information is collected during the onboarding process
   - Regularly audit employee records for completeness and accuracy
   - Update employment status promptly for terminated or on-leave employees

2. **Salary Structure Management**:
   - Create standardized salary structures for different employee categories
   - Define clear allowance and deduction types with consistent naming
   - Review and update salary structures periodically to reflect market changes

3. **Payroll Processing**:
   - Establish a regular payroll schedule and communicate it to all stakeholders
   - Perform a thorough review of calculated amounts before finalizing payroll
   - Maintain proper documentation for all payroll runs
   - Process salary revisions before the payroll run to ensure they are included in the current period

4. **Security and Compliance**:
   - Restrict access to salary information to authorized personnel only
   - Ensure all payroll calculations comply with local tax regulations
   - Maintain records for the required retention period for audit purposes

## Troubleshooting Common Issues

1. **Employee Not Appearing in Payroll Run**:
   - Check if the employee has an active salary record
   - Verify the employee's status is "active"
   - Ensure the employee's hire date is before the payroll period
   - Check if the employee belongs to the selected departments

2. **Salary Calculation Discrepancies**:
   - Verify the salary structure and components are correctly defined
   - Check for overlapping salary records with conflicting effective dates
   - Ensure tax brackets are properly configured
   - Validate allowance and deduction calculations

3. **Payslip Generation Failures**:
   - Confirm the payroll run is in "completed" status
   - Check for missing employee information required for payslips
   - Verify the system has proper permissions to generate PDF files
   - Ensure email settings are configured correctly for distribution

4. **Salary Structure Display Issues**:
   - If currency symbols are not displaying correctly, check the CurrencyDisplay component
   - For date formatting errors, ensure dates are properly converted to Date objects before calling toLocaleDateString()
   - If components are not rendering properly, verify that each component has a unique key in list renderings
   - For authentication errors in bulk operations, ensure proper credentials are included in API requests

5. **EnhancedDatePicker Issues**:
   - If you encounter "onChange is not a function" errors, check that the component is receiving either onChange or setDate props
   - For date display issues, verify that the date value is a valid Date object
   - If the calendar doesn't show the correct month/year, check that the date passed to the component is valid
   - For manual input validation issues, ensure the input format matches "YYYY-MM-DD"
   - If the component doesn't update when the date changes, verify that the value/date prop is being updated correctly

6. **Navigation Overlay Issues**:
   - If the overlay doesn't appear during navigation, check that the NavigationProvider is properly wrapped around the application
   - If the overlay doesn't disappear after navigation, verify that the useEffect cleanup in the navigation context is working properly
   - For styling issues, check the CSS classes in the NavigationOverlay component
   - If the page name doesn't appear correctly, ensure the startNavigation function is being called with the correct page name
   - For animation issues, verify that framer-motion is properly installed and imported

## Appendix

### Glossary

- **Allowance**: Additional payment to an employee beyond their basic salary, which may be taxable or non-taxable.
- **Basic Salary**: The core component of an employee's compensation before allowances and deductions.
- **Bulk Delete**: Functionality that allows for the deletion of multiple records at once.
- **Bulk Upload**: Functionality that allows for the addition of multiple records at once via CSV/Excel file.
- **Deduction**: Amount subtracted from an employee's gross salary, which may be statutory or voluntary.
- **Effective Date**: The date from which a salary structure or change becomes active.
- **Employee Role**: Job function assigned to an employee (e.g., Teacher, Administrator), different from authentication roles.
- **Gross Salary**: Total salary before deductions, including basic salary and all allowances.
- **Navigation Overlay**: A translucent screen that appears during page transitions to provide visual feedback.
- **Net Pay**: Amount payable to an employee after all deductions have been made.
- **PAYE (Pay As You Earn)**: Income tax deducted from an employee's salary.
- **Payroll Run**: The process of calculating salaries for a specific period.
- **Payslip**: Document detailing an employee's salary calculation for a specific period.
- **Salary Revision**: A change to an employee's salary, recorded with effective date and reason.
- **Salary Structure**: Template defining the components of an employee's compensation package.
- **Statutory Deduction**: Mandatory deduction required by law, such as tax or pension contributions.

### API Reference

#### Employee APIs

- `GET /api/employees`: List all employees with pagination and filtering
- `POST /api/employees`: Create a new employee
- `GET /api/employees/[id]`: Get employee details by ID
- `PATCH /api/employees/[id]`: Update employee details
- `DELETE /api/employees/[id]`: Delete an employee (soft delete)
- `POST /api/employees/bulk-import`: Import multiple employees
- `GET /api/employees/template`: Download employee import template
- `POST /api/hr/employees/bulk-delete`: Delete multiple employees
- `POST /api/hr/employees/count`: Count employees based on filter criteria

#### Payroll APIs

- `GET /api/payroll/employee-salaries`: List all employee salaries
- `POST /api/payroll/employee-salaries`: Create a new salary record
- `GET /api/payroll/employee-salaries/[id]`: Get salary details by ID
- `PATCH /api/payroll/employee-salaries/[id]`: Update salary details
- `GET /api/payroll/runs`: List all payroll runs
- `POST /api/payroll/runs`: Create a new payroll run
- `GET /api/payroll/runs/[id]`: Get payroll run details
- `PATCH /api/payroll/runs/[id]`: Update payroll run status
- `POST /api/payroll/runs/[id]/process`: Process a payroll run
- `POST /api/payroll/runs/[id]/payslips`: Generate payslips
- `GET /api/payroll/payslips`: List all payslips
- `GET /api/payroll/payslips/[id]`: Get payslip details
- `GET /api/payroll/payslips/[id]/pdf`: Generate PDF for a payslip

#### Salary Structure APIs

- `GET /api/payroll/salary-structures`: List all salary structures
- `POST /api/payroll/salary-structures`: Create a new salary structure
- `GET /api/payroll/salary-structures/[id]`: Get structure details
- `PATCH /api/payroll/salary-structures/[id]`: Update structure
- `DELETE /api/payroll/salary-structures/[id]`: Delete a single structure
- `POST /api/payroll/salary-structures/bulk-delete`: Delete multiple structures
- `POST /api/payroll/salary-structures/bulk-import`: Import structures
- `GET /api/payroll/salary-structures/template`: Download template

### Configuration Options

The system can be configured through environment variables and database settings:

#### Environment Variables

- `MONGODB_URI`: MongoDB connection string
- `NEXTAUTH_URL`: Base URL for authentication
- `NEXTAUTH_SECRET`: Secret for JWT signing
- `SMTP_SERVER`, `SMTP_PORT`, `SMTP_USER`, `SMTP_PASSWORD`: Email configuration
- `DEFAULT_CURRENCY`: Default currency for salary calculations (default: MWK)
- `TAX_CALCULATION_METHOD`: Method for tax calculation (default: progressive)
- `PAYSLIP_TEMPLATE`: Template to use for payslips (default: standard)

#### Database Configuration

- Tax brackets are configured in the `TaxBracket` collection
- System settings are stored in the `SystemSettings` collection
- User roles and permissions are defined in the `Role` collection
- Currency exchange rates are stored in the `ExchangeRate` collection

# Project Guides Directory

This directory contains all the development guides, documentation, and reference materials for the TCM Enterprise Suite project.

## Directory Structure

### `/docs/` - Core Documentation
Contains the main project documentation including:
- System overview and architecture
- Module-specific documentation (Accounting, Payroll, HR, etc.)
- API references and technical guides
- User guides and implementation status

### `/development_scripts/` - Development Tools
Contains various scripts and tools used during development:
- TypeScript error fixing scripts
- Component scanners and analyzers
- Database migration scripts
- Build optimization tools
- Development utilities

### Root Level Markdown Files
Various module development trackers and implementation guides:
- Module development trackers for different components
- Implementation status documents
- Integration guides and summaries
- Technical specifications and requirements

## Important Notes

### Main Project Documents (In Root Directory)
The following critical documents remain in the project root for easy access:
- `TCM_ENTERPRISE_SUITE_BUSINESS_ARCHITECTURE_AND_SERVICES.md` - Complete system architecture
- `HR_PAYROLL_ACCOUNTING_UPDATE_TRACKER.md` - Core module status tracker
- `ENTITY_RECONCILIATION_PLAN.md` - HR/Employee entity separation plan

### Purpose of This Organization
This directory structure was created to:
1. **Clean up the project root** - Remove clutter from the main directory
2. **Organize documentation** - Group related guides and docs together
3. **Separate concerns** - Keep development tools separate from production code
4. **Improve navigation** - Make it easier to find relevant documentation
5. **Prevent confusion** - Avoid markdown files being treated as Next.js pages

## Usage Guidelines

### For Developers
- Check `/docs/` for system architecture and API documentation
- Use `/development_scripts/` for build tools and utilities
- Refer to root-level markdown files for specific module information

### For Documentation Updates
- Add new technical docs to `/docs/`
- Add development tools to `/development_scripts/`
- Update module trackers in the root level files

### For Deployment
- This entire directory is excluded from production builds
- Only the main project code and essential config files are deployed
- Documentation is available for reference but doesn't affect the application

## Key Documentation Files

### System Architecture
- `docs/system-overview.md` - High-level system overview
- `docs/master-implementation-plan.md` - Implementation roadmap

### Module Documentation
- `docs/payroll/` - Payroll module documentation
- `docs/accounting/` - Accounting module documentation
- `docs/development/` - Development status and guides

### Development Trackers
- Various `*_MODULE_DEVELOPMENT_TRACKER.md` files for module-specific progress
- Implementation summaries and integration guides
- Component error tracking and fixing guides

## Maintenance

This directory should be regularly maintained to:
- Remove outdated documentation
- Update development trackers with current status
- Archive completed implementation guides
- Add new documentation as features are developed

For questions about specific documentation or to contribute updates, refer to the main project documentation or contact the development team.

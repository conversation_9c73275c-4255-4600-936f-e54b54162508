// hooks/use-error-handler.ts
"use client"

import { useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"

export interface ErrorAction {
  label: string
  action: string
  type: 'button' | 'link' | 'retry'
  variant?: 'primary' | 'secondary' | 'destructive'
  url?: string
  data?: Record<string, any>
}

export interface StructuredError {
  id: string
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  code: string
  message: string
  userMessage: string
  details?: string
  suggestions?: string[]
  actions?: ErrorAction[]
  timestamp: string
  context?: Record<string, any>
}

export interface ErrorResponse {
  success: false
  error: StructuredError
}

interface UseErrorHandlerReturn {
  error: StructuredError | null
  isErrorOpen: boolean
  isActionLoading: boolean
  loadingAction: string | undefined
  showError: (error: StructuredError) => void
  hideError: () => void
  handleApiError: (response: Response) => Promise<void>
  handleError: (error: any, context?: Record<string, any>) => void
  clearError: () => void
  setActionLoading: (action: string | undefined) => void
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [error, setError] = useState<StructuredError | null>(null)
  const [isErrorOpen, setIsErrorOpen] = useState(false)
  const [isActionLoading, setIsActionLoading] = useState(false)
  const [loadingAction, setLoadingAction] = useState<string | undefined>(undefined)

  const showError = useCallback((error: StructuredError) => {
    setError(error)
    setIsErrorOpen(true)

    // Also show a toast for immediate feedback
    toast({
      title: "Error",
      description: error.userMessage,
      variant: "destructive"
    })
  }, [])

  const hideError = useCallback(() => {
    setIsErrorOpen(false)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
    setIsErrorOpen(false)
    setIsActionLoading(false)
    setLoadingAction(undefined)
  }, [])

  const setActionLoading = useCallback((action: string | undefined) => {
    setIsActionLoading(!!action)
    setLoadingAction(action)
  }, [])

  const handleApiError = useCallback(async (response: Response) => {
    try {
      const errorData: ErrorResponse = await response.json()

      if (!errorData.success && errorData.error) {
        showError(errorData.error)
      } else {
        // Fallback for non-structured errors
        handleError(new Error(`HTTP ${response.status}: ${response.statusText}`))
      }
    } catch (parseError) {
      // If we can't parse the error response, create a generic error
      handleError(new Error(`HTTP ${response.status}: ${response.statusText}`))
    }
  }, [showError])

  const handleError = useCallback((error: any, context?: Record<string, any>) => {
    // Create a structured error from a generic error
    const structuredError: StructuredError = {
      id: `ERR_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'SYSTEM',
      severity: 'MEDIUM',
      code: 'GENERIC_ERROR',
      message: error instanceof Error ? error.message : String(error),
      userMessage: error instanceof Error
        ? error.message
        : 'An unexpected error occurred. Please try again.',
      timestamp: new Date().toISOString(),
      context: {
        ...context,
        stack: error instanceof Error ? error.stack : undefined
      },
      suggestions: [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      actions: [
        {
          label: 'Retry',
          action: 'retry',
          type: 'button',
          variant: 'primary'
        },
        {
          label: 'View Details',
          action: 'view-details',
          type: 'link',
          variant: 'secondary',
          url: '/dashboard/error-details'
        }
      ]
    }

    showError(structuredError)
  }, [showError])

  return {
    error,
    isErrorOpen,
    isActionLoading,
    loadingAction,
    showError,
    hideError,
    handleApiError,
    handleError,
    clearError,
    setActionLoading
  }
}

// Utility function to check if a response contains a structured error
export function isErrorResponse(data: any): data is ErrorResponse {
  return data && data.success === false && data.error && typeof data.error === 'object'
}

// Utility function to extract error from API response
export async function extractErrorFromResponse(response: Response): Promise<StructuredError | null> {
  try {
    const data = await response.json()

    if (isErrorResponse(data)) {
      return data.error
    }

    return null
  } catch {
    return null
  }
}

// hooks/use-form-data-preloader.ts
"use client"

import { useState, useEffect, useCallback } from 'react'
import { localStorageService } from '@/lib/services/local-storage-service'

interface FormData {
  budgets: any[]
  budgetCategories: any[]
  budgetSubcategories: any[]
  fiscalYears: string[]
  incomeSources: Array<{ value: string; label: string }>
  statusOptions: Array<{ value: string; label: string }>
}

interface UseFormDataPreloaderReturn {
  formData: FormData
  isLoading: boolean
  isReady: boolean
  error: string | null
  refresh: () => Promise<void>
}

export function useFormDataPreloader(): UseFormDataPreloaderReturn {
  const [formData, setFormData] = useState<FormData>(() => {
    // Get initial data from cache immediately
    return localStorageService.getFormData()
  })

  const [isLoading, setIsLoading] = useState(true) // Start as loading
  const [isReady, setIsReady] = useState(false) // Start as not ready
  const [error, setError] = useState<string | null>(null)

  // Preload data in background
  const preloadData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      console.log('Preloading form data in background...')
      
      // This will either return cached data or fetch fresh data
      const data = await localStorageService.preloadFormData()
      
      setFormData(data)
      setIsReady(true)
      
      console.log('Form data preloaded successfully:', {
        budgets: data.budgets.length,
        fiscalYears: data.fiscalYears.length,
        sources: data.incomeSources.length
      })
      
    } catch (err) {
      console.error('Error preloading form data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load form data')
      
      // Even on error, ensure we have basic data
      setFormData(localStorageService.getFormData())
      setIsReady(true) // Still ready with fallback data
      
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Refresh data manually
  const refresh = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      console.log('Manually refreshing form data...')
      const data = await localStorageService.refreshFormData()
      
      setFormData(data)
      console.log('Form data refreshed successfully')
      
    } catch (err) {
      console.error('Error refreshing form data:', err)
      setError(err instanceof Error ? err.message : 'Failed to refresh form data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Start with cached data immediately
        const cachedData = localStorageService.getFormData()
        setFormData(cachedData)

        // Check if we have meaningful cached data
        const hasValidCache = cachedData.fiscalYears.length > 0 && cachedData.incomeSources.length > 0

        if (hasValidCache) {
          console.log('Valid cached data found, setting ready state')
          setIsReady(true)
          setIsLoading(false)
        }

        // Then preload fresh data in background (non-blocking)
        setTimeout(async () => {
          try {
            console.log('Starting background data refresh...')
            const freshData = await localStorageService.preloadFormData()
            setFormData(freshData)
            setIsReady(true)
            console.log('Background data refresh completed')
          } catch (error) {
            console.warn('Background refresh failed:', error)
            // Don't set error if we already have cached data
            if (!hasValidCache) {
              setError(error instanceof Error ? error.message : 'Failed to load form data')
            }
          } finally {
            setIsLoading(false)
          }
        }, 100)

      } catch (error) {
        console.error('Error initializing form data:', error)
        setError(error instanceof Error ? error.message : 'Failed to initialize form data')
        setIsLoading(false)

        // Still set ready with default data
        setIsReady(true)
      }
    }

    initializeData()
  }, [])

  // Auto-refresh every 30 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('Auto-refreshing form data...')
      preloadData()
    }, 30 * 60 * 1000) // 30 minutes

    return () => clearInterval(interval)
  }, [preloadData])

  return {
    formData,
    isLoading,
    isReady,
    error,
    refresh
  }
}

// Specialized hook for income form data
export function useIncomeFormData() {
  const { formData, isLoading, isReady, error, refresh } = useFormDataPreloader()
  
  return {
    // Direct access to form options
    fiscalYears: formData.fiscalYears,
    incomeSources: formData.incomeSources,
    statusOptions: formData.statusOptions,
    budgets: formData.budgets,
    
    // Current fiscal year helper
    currentFiscalYear: localStorageService.getCurrentFiscalYear(),
    
    // State
    isLoading,
    isReady,
    error,
    refresh,
    
    // Form draft helpers
    saveFormDraft: (data: any) => localStorageService.saveFormDraft(data),
    getFormDraft: () => localStorageService.getFormDraft(),
    clearFormDraft: () => localStorageService.clearFormDraft(),
  }
}

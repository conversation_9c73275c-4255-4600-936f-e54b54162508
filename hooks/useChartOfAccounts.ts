"use client";

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { isErrorResponse } from '@/hooks/use-error-handler';

// Types
export interface Account {
  _id: string;
  id: string;
  accountNumber: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  subtype?: string;
  description?: string;
  balance: number;
  isActive: boolean;
  parentAccount?: {
    _id: string;
    accountNumber: string;
    name: string;
  };
  costCenter?: {
    _id: string;
    code: string;
    name: string;
  };
  tags?: string[];
  fiscalYear?: string;
  reportingGroup?: string;
  isLocked?: boolean;
  lockReason?: string;
  lastReconciliationDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface ChartOfAccountsResponse {
  asset: Account[];
  liability: Account[];
  equity: Account[];
  revenue: Account[];
  expense: Account[];
}

export interface CreateAccountData {
  accountNumber: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  subtype?: string;
  description?: string;
  parentAccount?: string;
  costCenter?: string;
  tags?: string[];
  fiscalYear?: string;
  reportingGroup?: string;
  isActive?: boolean;
}

export interface UpdateAccountData extends Partial<CreateAccountData> {
  isLocked?: boolean;
  lockReason?: string;
}

// Transform backend data to frontend format
const transformAccount = (account: any): Account => ({
  ...account,
  id: account._id || account.id,
  code: account.accountNumber, // For backward compatibility with frontend
});

const transformChartOfAccounts = (data: any): Account[] => {
  const allAccounts: Account[] = [];
  
  // Flatten the hierarchical structure and transform accounts
  Object.values(data).forEach((accountGroup: any) => {
    if (Array.isArray(accountGroup)) {
      accountGroup.forEach((account: any) => {
        allAccounts.push(transformAccount(account));
      });
    }
  });
  
  return allAccounts;
};

// Add level calculation for hierarchical display
const addAccountLevels = (accounts: Account[]): Account[] => {
  const accountMap = new Map(accounts.map(acc => [acc.id, acc]));
  
  const calculateLevel = (account: Account, visited = new Set()): number => {
    if (visited.has(account.id)) return 0; // Prevent infinite loops
    visited.add(account.id);
    
    if (!account.parentAccount) return 0;
    
    const parent = accountMap.get(account.parentAccount._id);
    if (!parent) return 0;
    
    return calculateLevel(parent, visited) + 1;
  };
  
  return accounts.map(account => ({
    ...account,
    level: calculateLevel(account),
  }));
};

/**
 * Hook for managing Chart of Accounts
 */
export function useChartOfAccounts() {
  const queryClient = useQueryClient();
  
  // Get chart of accounts
  const {
    data: chartData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['chart-of-accounts'],
    queryFn: async (): Promise<Account[]> => {
      const response = await fetch('/api/accounting/accounts/chart-of-accounts');

      if (!response.ok) {
        const errorData = await response.json();

        // Check if it's a structured error response
        if (isErrorResponse(errorData)) {
          throw errorData.error;
        }

        throw new Error(errorData.error || 'Failed to fetch chart of accounts');
      }
      
      const data = await response.json();
      const flatAccounts = transformChartOfAccounts(data);
      return addAccountLevels(flatAccounts);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Get all accounts (alternative endpoint)
  const {
    data: allAccounts,
    isLoading: isLoadingAll,
    refetch: refetchAll,
  } = useQuery({
    queryKey: ['accounts'],
    queryFn: async (): Promise<Account[]> => {
      const response = await fetch('/api/accounting/accounts');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch accounts');
      }
      
      const accounts = await response.json();
      return accounts.map(transformAccount);
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });

  // Create account mutation
  const createAccount = useMutation({
    mutationFn: async (accountData: CreateAccountData): Promise<Account> => {
      const response = await fetch('/api/accounting/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(accountData),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check if it's a structured error response
        if (isErrorResponse(errorData)) {
          throw errorData.error;
        }

        throw new Error(errorData.error || 'Failed to create account');
      }

      const account = await response.json();
      return transformAccount(account);
    },
    onSuccess: (newAccount) => {
      // Invalidate and refetch chart of accounts
      queryClient.invalidateQueries({ queryKey: ['chart-of-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['accounts'] });
      
      toast({
        title: 'Success',
        description: `Account "${newAccount.name}" has been created successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create account',
        variant: 'destructive',
      });
    },
  });

  // Update account mutation
  const updateAccount = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateAccountData }): Promise<Account> => {
      const response = await fetch(`/api/accounting/accounts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update account');
      }

      const account = await response.json();
      return transformAccount(account);
    },
    onSuccess: (updatedAccount) => {
      // Invalidate and refetch chart of accounts
      queryClient.invalidateQueries({ queryKey: ['chart-of-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['accounts'] });
      queryClient.invalidateQueries({ queryKey: ['account', updatedAccount.id] });
      
      toast({
        title: 'Success',
        description: `Account "${updatedAccount.name}" has been updated successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update account',
        variant: 'destructive',
      });
    },
  });

  // Delete account mutation
  const deleteAccount = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/accounting/accounts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete account');
      }
    },
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch chart of accounts
      queryClient.invalidateQueries({ queryKey: ['chart-of-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['accounts'] });
      queryClient.removeQueries({ queryKey: ['account', deletedId] });
      
      toast({
        title: 'Success',
        description: 'Account has been deleted successfully.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete account',
        variant: 'destructive',
      });
    },
  });

  return {
    // Data
    accounts: chartData || [],
    allAccounts: allAccounts || [],
    
    // Loading states
    isLoading,
    isLoadingAll,
    isCreating: createAccount.isPending,
    isUpdating: updateAccount.isPending,
    isDeleting: deleteAccount.isPending,
    
    // Error states
    error,
    createError: createAccount.error,
    updateError: updateAccount.error,
    deleteError: deleteAccount.error,
    
    // Actions
    refetch,
    refetchAll,
    createAccount: createAccount.mutate,
    createAccountAsync: createAccount.mutateAsync,
    updateAccount: updateAccount.mutate,
    updateAccountAsync: updateAccount.mutateAsync,
    deleteAccount: deleteAccount.mutate,
    deleteAccountAsync: deleteAccount.mutateAsync,
  };
}

/**
 * Hook for getting a single account
 */
export function useAccount(id: string) {
  return useQuery({
    queryKey: ['account', id],
    queryFn: async (): Promise<Account> => {
      const response = await fetch(`/api/accounting/accounts/${id}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch account');
      }
      
      const account = await response.json();
      return transformAccount(account);
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

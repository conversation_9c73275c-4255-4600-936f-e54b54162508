// app/api/notifications/mark-all-read/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/notifications/mark-all-read
 * Mark all notifications as read for the current user
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Mark all notifications as read
    const count = await notificationService.markAllAsRead(user.id);

    return NextResponse.json({
      success: true,
      count,
      message: `Marked ${count} notifications as read`
    });
  } catch (error: unknown) {
    logger.error('Error in mark all notifications as read handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

// app/api/inventory/purchase-orders/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Define purchase order status type
type PurchaseOrderStatus = 'draft' | 'pending' | 'approved' | 'rejected' | 'ordered' | 'received' | 'partially_received' | 'cancelled';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * GET /api/inventory/purchase-orders
 * Get all purchase orders with pagination and filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || undefined;
    const supplierId = searchParams.get('supplierId') || undefined;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const sortField = searchParams.get('sortField') || 'orderDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build sort object
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'desc' ? -1 : 1
    };

    // Get purchase orders
    let result;

    if (search) {
      // Search purchase orders
      result = await purchaseOrderService.searchPurchaseOrders(search, {
        status: status as PurchaseOrderStatus,
        supplierId,
        startDate,
        endDate,
        sort,
        page,
        limit
      });
    } else if (status) {
      // Get purchase orders by status
      result = await purchaseOrderService.getByStatus(status as PurchaseOrderStatus, {
        supplierId,
        startDate,
        endDate,
        sort,
        page,
        limit
      });
    } else if (supplierId) {
      // Get purchase orders by supplier
      result = await purchaseOrderService.getBySupplier(supplierId, {
        status: status as PurchaseOrderStatus,
        sort,
        page,
        limit
      });
    } else {
      // Get all purchase orders
      const filter: Record<string, any> = {};

      // Add date range filter
      if (startDate || endDate) {
        filter.orderDate = {};

        if (startDate) {
          filter.orderDate.$gte = startDate;
        }

        if (endDate) {
          filter.orderDate.$lte = endDate;
        }
      }

      result = await purchaseOrderService.paginate(
        filter,
        page,
        limit,
        sort,
        ['supplierId', 'createdBy', 'approvedBy', 'rejectedBy']
      );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting purchase orders', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while getting purchase orders' }, { status: 500 });
  }
}

/**
 * POST /api/inventory/purchase-orders
 * Create a new purchase order
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.supplierId) {
      return NextResponse.json({ error: 'Supplier ID is required' }, { status: 400 });
    }

    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json({ error: 'At least one item is required' }, { status: 400 });
    }

    // Validate items
    for (const item of body.items) {
      if (!item.itemId || !item.name || !item.quantity || !item.unitPrice) {
        return NextResponse.json({
          error: 'Each item must have itemId, name, quantity, and unitPrice'
        }, { status: 400 });
      }
    }

    // Add created by
    body.createdBy = user.id;

    // Create purchase order
    const purchaseOrder = await purchaseOrderService.createPurchaseOrder(body);

    return NextResponse.json(purchaseOrder, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating purchase order', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while creating purchase order' }, { status: 500 });
  }
}

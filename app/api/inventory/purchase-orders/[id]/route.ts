import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * GET /api/inventory/purchase-orders/[id]
 * Get purchase order by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Get purchase order
    const purchaseOrder = await purchaseOrderService.getPurchaseOrderDetails(id);

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    return NextResponse.json(purchaseOrder);
  } catch (error: unknown) {
    logger.error('Error getting purchase order', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PUT /api/inventory/purchase-orders/[id]
 * Update purchase order
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Resolve the params promise
    const { id } = await params;

    // Check if purchase order exists
    const purchaseOrder = await purchaseOrderService.findById(id);

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Check if purchase order can be updated
    if (purchaseOrder.status !== 'draft' && purchaseOrder.status !== 'pending') {
      return NextResponse.json({
        error: `Cannot update purchase order with status: ${purchaseOrder.status}`
      }, { status: 400 });
    }

    // Update purchase order
    const updatedPurchaseOrder = await purchaseOrderService.updateById(id, body);

    return NextResponse.json(updatedPurchaseOrder);
  } catch (error: unknown) {
    logger.error('Error updating purchase order', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * DELETE /api/inventory/purchase-orders/[id]
 * Delete purchase order
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Check if purchase order exists
    const purchaseOrder = await purchaseOrderService.findById(id);

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Check if purchase order can be deleted
    if (purchaseOrder.status !== 'draft' && purchaseOrder.status !== 'pending' && purchaseOrder.status !== 'rejected') {
      return NextResponse.json({
        error: `Cannot delete purchase order with status: ${purchaseOrder.status}`
      }, { status: 400 });
    }

    // Delete purchase order
    await purchaseOrderService.deleteById(id);

    return NextResponse.json({ message: 'Purchase order deleted successfully' });
  } catch (error: unknown) {
    logger.error('Error deleting purchase order', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * PATCH /api/inventory/purchase-orders/[id]/payment
 * Update purchase order payment status
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate payment status
    if (!body.paymentStatus || !['unpaid', 'partially_paid', 'paid'].includes(body.paymentStatus)) {
      return NextResponse.json({ error: 'Invalid payment status' }, { status: 400 });
    }

    // Validate payment amount
    if ((body.paymentStatus === 'partially_paid' || body.paymentStatus === 'paid') && !body.paymentAmount) {
      return NextResponse.json({ error: 'Payment amount is required' }, { status: 400 });
    }

    // Get payment data
    const paymentData = {
      paymentAmount: body.paymentAmount,
      paymentDate: body.paymentDate ? new Date(body.paymentDate) : new Date(),
      paymentMethod: body.paymentMethod,
      paymentReference: body.paymentReference
    };

    // Resolve the params promise
    const { id } = await params;

    // Update purchase order payment status
    const updatedPurchaseOrder = await purchaseOrderService.updatePaymentStatus(
      id,
      body.paymentStatus,
      paymentData,
      user.id
    );

    if (!updatedPurchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    return NextResponse.json(updatedPurchaseOrder);
  } catch (error: unknown) {
    logger.error('Error updating purchase order payment status', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

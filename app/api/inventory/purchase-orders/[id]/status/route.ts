import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * PATCH /api/inventory/purchase-orders/[id]/status
 * Update purchase order status
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();

    // Validate status
    if (!body.status || !['draft', 'pending', 'approved', 'rejected', 'ordered', 'received', 'partially_received', 'cancelled'].includes(body.status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }

    // Check if user has required permissions based on status
    let hasPermission = false;

    if (body.status === 'approved' || body.status === 'rejected') {
      // Only certain roles can approve or reject
      hasPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER,
        UserRole.PROCUREMENT_MANAGER
      ]);
    } else if (body.status === 'ordered' || body.status === 'received' || body.status === 'partially_received') {
      // Only certain roles can mark as ordered or received
      hasPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.INVENTORY_MANAGER
      ]);
    } else {
      // Other statuses
      hasPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);
    }

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Update purchase order status
    const updatedPurchaseOrder = await purchaseOrderService.updateStatus(
      id,
      body.status,
      user.id,
      body.notes
    );

    if (!updatedPurchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    return NextResponse.json(updatedPurchaseOrder);
  } catch (error: unknown) {
    logger.error('Error updating purchase order status', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

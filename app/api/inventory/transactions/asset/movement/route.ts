import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { InventoryTransactionService } from '@/lib/backend/services/inventory/InventoryTransactionService';
import { AssetService } from '@/lib/backend/services/inventory/AssetService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const inventoryTransactionService = InventoryTransactionService;
const assetService = AssetService;

/**
 * POST /api/inventory/transactions/asset/movement
 * Record asset movement between locations
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);
    
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get request body
    const body = await req.json();
    
    // Validate required fields
    if (!body.assetId) {
      return NextResponse.json({ error: 'Asset ID is required' }, { status: 400 });
    }
    
    if (!body.assetName) {
      return NextResponse.json({ error: 'Asset name is required' }, { status: 400 });
    }
    
    if (!body.transactionType || !['transfer', 'adjustment'].includes(body.transactionType)) {
      return NextResponse.json({ error: 'Transaction type must be transfer or adjustment' }, { status: 400 });
    }
    
    if (body.transactionType === 'transfer') {
      if (!body.fromLocation) {
        return NextResponse.json({ error: 'From location is required' }, { status: 400 });
      }
      
      if (!body.toLocation) {
        return NextResponse.json({ error: 'To location is required' }, { status: 400 });
      }
    }
    
    // Get current asset
    const asset = await assetService.findById(body.assetId);
    
    if (!asset) {
      return NextResponse.json({ error: 'Asset not found' }, { status: 404 });
    }
    
    // Record transaction
    const transaction = await inventoryTransactionService.recordAssetMovement(
      body.assetId,
      body.assetName,
      body.transactionType,
      body.fromLocation || asset.location,
      body.toLocation,
      body.notes,
      user.id
    );
    
    // Update asset location
    const updatedAsset = await assetService.updateById(body.assetId, {
      location: body.toLocation || body.location
    });
    
    return NextResponse.json({
      transaction,
      asset: updatedAsset
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error recording asset movement', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

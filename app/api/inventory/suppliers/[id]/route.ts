import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/inventory/SupplierService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const supplierService = SupplierService;

/**
 * GET /api/inventory/suppliers/[id]
 * Get supplier by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Get supplier
    const supplier = await supplierService.getSupplierDetails(id);

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    return NextResponse.json(supplier);
  } catch (error: unknown) {
    logger.error('Error getting supplier', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PUT /api/inventory/suppliers/[id]
 * Update supplier
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Resolve the params promise
    const { id } = await params;

    // Check if supplier exists
    const supplier = await supplierService.findById(id);

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // Update supplier
    const updatedSupplier = await supplierService.updateById(id, body);

    return NextResponse.json(updatedSupplier);
  } catch (error: unknown) {
    logger.error('Error updating supplier', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * DELETE /api/inventory/suppliers/[id]
 * Delete supplier
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Check if supplier exists
    const supplier = await supplierService.findById(id);

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // Delete supplier
    await supplierService.deleteById(id);

    return NextResponse.json({ message: 'Supplier deleted successfully' });
  } catch (error: unknown) {
    logger.error('Error deleting supplier', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

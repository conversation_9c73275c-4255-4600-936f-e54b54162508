import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/inventory/SupplierService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const supplierService = SupplierService;

/**
 * PATCH /api/inventory/suppliers/[id]/status
 * Update supplier status
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate status
    if (!body.status || !['active', 'inactive', 'blacklisted'].includes(body.status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Update supplier status
    const updatedSupplier = await supplierService.updateStatus(
      id,
      body.status,
      user.id
    );

    if (!updatedSupplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    return NextResponse.json(updatedSupplier);
  } catch (error: unknown) {
    logger.error('Error updating supplier status', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

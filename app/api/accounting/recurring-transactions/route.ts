// app/api/accounting/recurring-transactions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { recurringTransactionService } from '@/lib/services/accounting/recurring-transaction-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for creating recurring transaction
const createRecurringTransactionSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters long'),
  description: z.string().min(3, 'Description must be at least 3 characters long'),
  amount: z.number().positive('Amount must be positive'),
  type: z.enum(['debit', 'credit', 'transfer']),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom']),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  dayOfWeek: z.number().min(0).max(6).optional(),
  monthOfYear: z.number().min(0).max(11).optional(),
  customInterval: z.number().min(1).optional(),
  account: z.string().min(1, 'Account is required'),
  transferToAccount: z.string().optional(),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  costCenter: z.string().optional(),
  project: z.string().optional(),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().positive().default(1),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  maxExecutions: z.number().positive().optional(),
  autoPost: z.boolean().default(false),
  notifyOnExecution: z.boolean().default(false),
  notificationEmail: z.string().email().optional()
});

/**
 * GET /api/accounting/recurring-transactions
 * List recurring transactions
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const frequency = searchParams.get('frequency');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'nextExecutionDate';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build query
    const query: Record<string, unknown> = {};

    if (status) {
      query.status = status;
    }

    if (frequency) {
      query.frequency = frequency;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: { [key: string]: 1 | -1 } = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get recurring transactions
    const result = await recurringTransactionService.paginate(
      query,
      page,
      limit,
      sort,
      ['account', 'transferToAccount', 'costCenter', 'project', 'createdBy', 'updatedBy']
    );

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting recurring transactions', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/recurring-transactions
 * Create a recurring transaction
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createRecurringTransactionSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Add user ID
    const data = {
      ...validationResult.data,
      startDate: new Date(validationResult.data.startDate),
      endDate: validationResult.data.endDate ? new Date(validationResult.data.endDate) : undefined,
      createdBy: user.id,
      updatedBy: user.id
    };

    // Create recurring transaction
    const recurringTransaction = await recurringTransactionService.createRecurringTransaction(data);

    return NextResponse.json({
      success: true,
      message: 'Recurring transaction created successfully',
      data: recurringTransaction
    });
  } catch (error: unknown) {
    logger.error('Error creating recurring transaction', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

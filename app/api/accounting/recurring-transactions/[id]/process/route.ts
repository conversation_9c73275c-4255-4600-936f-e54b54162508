// app/api/accounting/recurring-transactions/[id]/process/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { recurringTransactionService } from '@/lib/services/accounting/recurring-transaction-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  errorResponse,
  handleAuthError,
  handlePermissionError,
  handleNotFoundError
} from '@/lib/backend/utils/api-response';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/accounting/recurring-transactions/[id]/process
 * Process a recurring transaction
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;
    // Validate ID format
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return handleNotFoundError('Recurring transaction', id);
    }

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Process recurring transaction
    const result = await recurringTransactionService.processRecurringTransaction(
      id,
      user.id
    );

    // Return success response
    return successResponse(
      result,
      'Recurring transaction processed successfully'
    );
  } catch (error: unknown) {
    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return handleNotFoundError('Recurring transaction', id);
      }

      if (error.message.includes('Cannot process')) {
        return errorResponse(error.message, 400);
      }
    }

    // Handle general errors
    return errorResponse(
      error instanceof Error ? error.message : 'An unexpected error occurred',
      500
    );
  }
}

// app/api/accounting/recurring-transactions/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { recurringTransactionService } from '@/lib/services/accounting/recurring-transaction-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for updating recurring transaction
const updateRecurringTransactionSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters long').optional(),
  description: z.string().min(3, 'Description must be at least 3 characters long').optional(),
  amount: z.number().positive('Amount must be positive').optional(),
  type: z.enum(['debit', 'credit', 'transfer']).optional(),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom']).optional(),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ).optional(),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ).optional().nullable(),
  dayOfMonth: z.number().min(1).max(31).optional().nullable(),
  dayOfWeek: z.number().min(0).max(6).optional().nullable(),
  monthOfYear: z.number().min(0).max(11).optional().nullable(),
  customInterval: z.number().min(1).optional().nullable(),
  status: z.enum(['active', 'paused', 'completed', 'cancelled']).optional(),
  account: z.string().min(1, 'Account is required').optional(),
  transferToAccount: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  subcategory: z.string().optional().nullable(),
  costCenter: z.string().optional().nullable(),
  project: z.string().optional().nullable(),
  currency: z.string().optional(),
  exchangeRate: z.number().positive().optional(),
  notes: z.string().optional().nullable(),
  tags: z.array(z.string()).optional(),
  maxExecutions: z.number().positive().optional().nullable(),
  autoPost: z.boolean().optional(),
  notifyOnExecution: z.boolean().optional(),
  notificationEmail: z.string().email().optional().nullable()
});

/**
 * GET /api/accounting/recurring-transactions/[id]
 * Get a recurring transaction by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get recurring transaction
    const recurringTransaction = await recurringTransactionService.findById(
      id,
      ['account', 'transferToAccount', 'costCenter', 'project', 'createdBy', 'updatedBy', 'processHistory.journalEntryId', 'processHistory.processedBy']
    );

    if (!recurringTransaction) {
      return NextResponse.json(
        { error: 'Recurring transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: recurringTransaction
    });
  } catch (error: unknown) {
    logger.error('Error getting recurring transaction', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/recurring-transactions/[id]
 * Update a recurring transaction
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = updateRecurringTransactionSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Process dates
    const data = {
      ...validationResult.data,
      startDate: validationResult.data.startDate ? new Date(validationResult.data.startDate) : undefined,
      endDate: validationResult.data.endDate ? new Date(validationResult.data.endDate) : undefined
    };

    // Update recurring transaction
    const recurringTransaction = await recurringTransactionService.updateRecurringTransaction(
      id,
      data,
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Recurring transaction updated successfully',
      data: recurringTransaction
    });
  } catch (error: unknown) {
    logger.error('Error updating recurring transaction', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/recurring-transactions/[id]
 * Delete a recurring transaction
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;

    // Delete recurring transaction
    await recurringTransactionService.deleteById(id);

    return NextResponse.json({
      success: true,
      message: 'Recurring transaction deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting recurring transaction', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/recurring-transactions/batch-process/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { recurringTransactionService } from '@/lib/services/accounting/recurring-transaction-service';
import { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleZodError,
  handleBadRequestError
} from '@/lib/backend/utils/api-response';

// Validation schema for batch processing
const batchProcessSchema = z.object({
  date: z.string()
    .refine(value => !isNaN(Date.parse(value)), { message: 'Invalid date format' })
    .optional(),
  processAll: z.boolean().optional(),
  ids: z.array(z.string().min(1, 'ID cannot be empty')).optional()
}).refine(
  data => data.processAll === true || (data.ids && data.ids.length > 0),
  { message: 'Either processAll must be true or ids must be provided' }
);

/**
 * POST /api/accounting/recurring-transactions/batch-process
 * Process multiple recurring transactions
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = batchProcessSchema.safeParse(body);

    if (!validationResult.success) {
      return handleZodError(validationResult.error);
    }

    const { date, processAll, ids } = validationResult.data;

    // Process date if provided
    const processDate = date ? new Date(date) : new Date();

    // Process recurring transactions
    let result;

    if (processAll) {
      // Process all due recurring transactions
      result = await recurringTransactionService.processDueRecurringTransactions(
        processDate,
        user.id
      );
    } else if (ids && ids.length > 0) {
      // Process specific recurring transactions
      result = await recurringTransactionService.processMultipleRecurringTransactions(
        ids,
        user.id
      );
    } else {
      // This should never happen due to schema validation, but just in case
      return handleBadRequestError('Either processAll must be true or ids must be provided');
    }

    // Return success response
    return successResponse(
      result,
      `Successfully processed ${result.processed} recurring transactions with ${result.failed} failures`
    );
  } catch (error) {
    return handleApiError(error);
  }
}

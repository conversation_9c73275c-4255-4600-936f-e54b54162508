// app/api/accounting/accounts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Account, { IAccountModel } from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Interface for account update data
interface AccountUpdateData {
  _id: string;
  [key: string]: any;
}

/**
 * GET /api/accounting/accounts
 * Get all accounts with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');
    const parentAccount = searchParams.get('parentAccount');
    const costCenter = searchParams.get('costCenter');
    const fiscalYear = searchParams.get('fiscalYear');
    const reportingGroup = searchParams.get('reportingGroup');
    const limit = parseInt(searchParams.get('limit') || '100');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, unknown> = {};

    if (type) {
      query.type = type;
    }

    if (isActive !== null) {
      query.isActive = isActive === 'true';
    }

    if (parentAccount) {
      query.parentAccount = parentAccount;
    }

    if (costCenter) {
      query.costCenter = costCenter;
    }

    if (fiscalYear) {
      query.fiscalYear = fiscalYear;
    }

    if (reportingGroup) {
      query.reportingGroup = reportingGroup;
    }

    if (search) {
      query.$or = [
        { accountNumber: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get accounts
    const accounts = await Account.find(query)
      .sort({ accountNumber: 1 })
      .skip(skip)
      .limit(limit)
      .populate('parentAccount', 'accountNumber name')
      .populate('costCenter', 'code name');

    // Get total count
    const totalCount = await Account.countDocuments(query);

    return NextResponse.json({
      accounts,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    logger.error('Error getting accounts', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to get accounts' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/accounts
 * Create a new account
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.accountNumber || !body.name || !body.type) {
      return NextResponse.json(
        { error: 'Missing required fields: accountNumber, name, type' },
        { status: 400 }
      );
    }

    // Check if account number already exists
    const existingAccount = await Account.findOne({ accountNumber: body.accountNumber });
    if (existingAccount) {
      return NextResponse.json(
        { error: 'Account number already exists' },
        { status: 400 }
      );
    }

    // Create account
    const account = new Account({
      ...body,
      createdBy: user.id,
      updatedBy: user.id
    });

    // Save account
    await account.save();

    return NextResponse.json(account, { status: 201 });
  } catch (error) {
    logger.error('Error creating account', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/accounts
 * Update multiple accounts (batch update)
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request
    if (!body.accounts || !Array.isArray(body.accounts) || body.accounts.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request: accounts array is required' },
        { status: 400 }
      );
    }

    // Update accounts
    const updatePromises = body.accounts.map(async (accountData: AccountUpdateData) => {
      if (!accountData._id) {
        return { success: false, error: 'Account ID is required', data: accountData };
      }

      try {
        const updatedAccount = await Account.findByIdAndUpdate(
          accountData._id,
          {
            ...accountData,
            updatedBy: user.id
          },
          { new: true, runValidators: true }
        );

        if (!updatedAccount) {
          return { success: false, error: 'Account not found', data: accountData };
        }

        return { success: true, data: updatedAccount };
      } catch (error: unknown) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: accountData
        };
      }
    });

    const results = await Promise.all(updatePromises);

    return NextResponse.json({
      success: results.every(result => result.success),
      results
    });
  } catch (error) {
    logger.error('Error updating accounts', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to update accounts' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/accounting/accounts/[id]
 * Get an account by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: accountId } = await params;

    // Get account
    const account = await Account.findById(accountId)
      .populate('parentAccount', 'accountNumber name')
      .populate('costCenter', 'code name');

    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(account);
  } catch (error) {
    logger.error(`Error getting account`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to get account' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/accounts/[id]
 * Update an account
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: accountId } = await params;

    // Get request body
    const body = await request.json();

    // Check if account exists
    const account = await Account.findById(accountId);
    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 404 }
      );
    }

    // If account number is being changed, check if it already exists
    if (body.accountNumber && body.accountNumber !== account.accountNumber) {
      const existingAccount = await Account.findOne({ accountNumber: body.accountNumber });
      if (existingAccount) {
        return NextResponse.json(
          { error: 'Account number already exists' },
          { status: 400 }
        );
      }
    }

    // Update account
    const updatedAccount = await Account.findByIdAndUpdate(
      accountId,
      {
        ...body,
        updatedBy: user.id
      },
      { new: true, runValidators: true }
    ).populate('parentAccount', 'accountNumber name')
     .populate('costCenter', 'code name');

    return NextResponse.json(updatedAccount);
  } catch (error) {
    logger.error(`Error updating account`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to update account' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/accounts/[id]
 * Delete an account
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: accountId } = await params;

    // Check if account exists
    const account = await Account.findById(accountId);
    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 404 }
      );
    }

    // Check if account has child accounts
    const childAccounts = await Account.find({ parentAccount: accountId });
    if (childAccounts.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete account with child accounts' },
        { status: 400 }
      );
    }

    // Check if account has transactions
    // This would require checking Transaction model, but for simplicity we'll skip this check
    // In a real implementation, you would check if there are any transactions associated with this account

    // Delete account
    await Account.findByIdAndDelete(accountId);

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting account`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to delete account' },
      { status: 500 }
    );
  }
}

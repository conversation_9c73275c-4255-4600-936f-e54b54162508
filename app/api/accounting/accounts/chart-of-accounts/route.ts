// app/api/accounting/accounts/chart-of-accounts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Account, { IAccountModel } from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Interface for account objects in the chart of accounts
interface AccountWithChildren extends Omit<IAccountModel, 'children'> {
  children: AccountWithChildren[];
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  [key: string]: any; // Allow for additional properties
}

/**
 * GET /api/accounting/accounts/chart-of-accounts
 * Get the chart of accounts in a hierarchical structure
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const fiscalYear = searchParams.get('fiscalYear');

    // Build query
    const query: Record<string, unknown> = {};

    if (!includeInactive) {
      query.isActive = true;
    }

    if (fiscalYear) {
      query.fiscalYear = fiscalYear;
    }

    // Get all accounts
    const accounts = await Account.find(query)
      .sort({ accountNumber: 1 })
      .populate('parentAccount', 'accountNumber name')
      .populate('costCenter', 'code name');

    // Build chart of accounts hierarchy
    const accountMap = new Map<string, AccountWithChildren>();
    const rootAccounts: AccountWithChildren[] = [];

    // First pass: create all account objects
    accounts.forEach(account => {
      accountMap.set(account._id.toString(), {
        ...account.toObject(),
        children: []
      } as AccountWithChildren);
    });

    // Second pass: build the hierarchy
    accounts.forEach(account => {
      const accountObj = accountMap.get(account._id.toString());

      // Skip if account object wasn't created in the first pass
      if (!accountObj) return;

      if (account.parentAccount) {
        // Make sure parentAccount has _id property
        if (!account.parentAccount._id) {
          rootAccounts.push(accountObj);
          return;
        }

        const parentId = account.parentAccount._id.toString();
        const parentAccount = accountMap.get(parentId);

        if (parentAccount) {
          parentAccount.children.push(accountObj);
        } else {
          // If parent is not in the map (might be inactive or filtered out),
          // add this account as a root account
          rootAccounts.push(accountObj);
        }
      } else {
        rootAccounts.push(accountObj);
      }
    });

    // Group accounts by type
    const chartOfAccounts = {
      asset: rootAccounts.filter(account => account.type === 'asset'),
      liability: rootAccounts.filter(account => account.type === 'liability'),
      equity: rootAccounts.filter(account => account.type === 'equity'),
      revenue: rootAccounts.filter(account => account.type === 'revenue'),
      expense: rootAccounts.filter(account => account.type === 'expense')
    };

    return NextResponse.json(chartOfAccounts);
  } catch (error) {
    logger.error('Error getting chart of accounts', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to get chart of accounts' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/accounts/chart-of-accounts
 * Import a chart of accounts
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request
    if (!body.accounts || !Array.isArray(body.accounts) || body.accounts.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request: accounts array is required' },
        { status: 400 }
      );
    }

    // Process accounts
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Define interface for account data in request
    interface AccountImportData {
      accountNumber: string;
      name: string;
      type: string;
      parentAccountNumber?: string;
      [key: string]: any;
    }

    // First pass: create all accounts without parent references
    for (const accountData of body.accounts as AccountImportData[]) {
      try {
        // Check if account number already exists
        const existingAccount = await Account.findOne({ accountNumber: accountData.accountNumber });

        if (existingAccount) {
          // Update existing account
          await Account.findByIdAndUpdate(
            existingAccount._id,
            {
              ...accountData,
              parentAccount: null, // We'll set this in the second pass
              updatedBy: user.id
            },
            { runValidators: true }
          );
        } else {
          // Create new account
          const account = new Account({
            ...accountData,
            parentAccount: null, // We'll set this in the second pass
            createdBy: user.id,
            updatedBy: user.id
          });

          await account.save();
        }

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Error processing account ${accountData.accountNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Second pass: update parent references
    for (const accountData of body.accounts as AccountImportData[]) {
      if (accountData.parentAccountNumber) {
        try {
          // Find the account
          const account = await Account.findOne({ accountNumber: accountData.accountNumber });

          if (!account) {
            results.errors.push(`Account ${accountData.accountNumber} not found for parent update`);
            continue;
          }

          // Find the parent account
          const parentAccount = await Account.findOne({ accountNumber: accountData.parentAccountNumber });

          if (!parentAccount) {
            results.errors.push(`Parent account ${accountData.parentAccountNumber} not found for account ${accountData.accountNumber}`);
            continue;
          }

          // Update parent reference
          if (parentAccount._id) {
            account.parentAccount = parentAccount._id;
            await account.save();
          } else {
            results.errors.push(`Parent account ${accountData.parentAccountNumber} has invalid ID for account ${accountData.accountNumber}`);
          }
        } catch (error) {
          results.errors.push(`Error updating parent for account ${accountData.accountNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }

    return NextResponse.json({
      success: results.success > 0,
      message: `Imported ${results.success} accounts, ${results.failed} failed`,
      results
    });
  } catch (error) {
    logger.error('Error importing chart of accounts', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to import chart of accounts' },
      { status: 500 }
    );
  }
}

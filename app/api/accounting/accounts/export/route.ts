import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/accounts/export
 * Export chart of accounts in various formats
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to export accounts.',
        {
          endpoint: '/api/accounting/accounts/export',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Parse request body
    const body = await req.json();
    const { format, fileName, options = {} } = body;

    // Validate required fields
    if (!format || !fileName) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_REQUIRED_FIELDS',
        'Missing required fields: format, fileName',
        'Please provide both format and fileName for the export.',
        {
          userId: user.id,
          endpoint: '/api/accounting/accounts/export',
          method: 'POST',
          providedFields: Object.keys(body)
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Validate format
    if (!['excel', 'csv', 'pdf'].includes(format)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_FORMAT',
        'Invalid export format',
        'Supported formats are: excel, csv, pdf',
        {
          userId: user.id,
          endpoint: '/api/accounting/accounts/export',
          method: 'POST',
          providedFormat: format
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Build query based on options
    const query: any = {};
    
    // Filter by account types
    if (options.accountTypes && options.accountTypes.length > 0) {
      query.type = { $in: options.accountTypes };
    }

    // Filter by active status
    if (!options.includeInactive) {
      query.isActive = true;
    }

    // Filter by fiscal year
    if (options.fiscalYear) {
      query.fiscalYear = options.fiscalYear;
    }

    // Fetch accounts
    const accounts = await Account.find(query)
      .populate('parentAccount', 'accountNumber name')
      .populate('costCenter', 'code name')
      .sort({ accountNumber: 1 })
      .lean();

    logger.info('Exporting accounts', LogCategory.ACCOUNTING, {
      userId: user.id,
      format,
      accountCount: accounts.length,
      options
    });

    // Transform data for export
    const exportData = accounts.map(account => {
      const row: any = {
        'Account Number': account.accountNumber,
        'Account Name': account.name,
        'Account Type': account.type,
        'Subtype': account.subtype || '',
        'Description': account.description || '',
        'Active': account.isActive ? 'Yes' : 'No',
      };

      // Include hierarchy if requested
      if (options.includeHierarchy) {
        row['Parent Account'] = account.parentAccount 
          ? `${account.parentAccount.accountNumber} - ${account.parentAccount.name}`
          : '';
      }

      // Include balances if requested
      if (options.includeBalances) {
        row['Balance'] = account.balance || 0;
      }

      // Include additional fields
      if (account.costCenter) {
        row['Cost Center'] = `${account.costCenter.code} - ${account.costCenter.name}`;
      }

      row['Fiscal Year'] = account.fiscalYear || '';
      row['Created Date'] = account.createdAt ? new Date(account.createdAt).toLocaleDateString() : '';
      row['Updated Date'] = account.updatedAt ? new Date(account.updatedAt).toLocaleDateString() : '';

      return row;
    });

    // Generate file based on format
    let fileBuffer: Buffer;
    let contentType: string;
    let fileExtension: string;

    switch (format) {
      case 'excel':
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(exportData);
        
        // Auto-size columns
        const colWidths = Object.keys(exportData[0] || {}).map(key => ({
          wch: Math.max(key.length, 15)
        }));
        worksheet['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Chart of Accounts');
        fileBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        fileExtension = 'xlsx';
        break;

      case 'csv':
        const csvContent = [
          // Headers
          Object.keys(exportData[0] || {}).join(','),
          // Data rows
          ...exportData.map(row => 
            Object.values(row).map(value => 
              typeof value === 'string' && value.includes(',') 
                ? `"${value.replace(/"/g, '""')}"` 
                : value
            ).join(',')
          )
        ].join('\n');
        
        fileBuffer = Buffer.from(csvContent, 'utf-8');
        contentType = 'text/csv';
        fileExtension = 'csv';
        break;

      case 'pdf':
        // For PDF, we'll create a simple text-based PDF
        // In a real implementation, you might use a library like PDFKit
        const pdfContent = `Chart of Accounts Export\n\n${
          exportData.map(row => 
            Object.entries(row).map(([key, value]) => `${key}: ${value}`).join('\n')
          ).join('\n\n')
        }`;
        
        fileBuffer = Buffer.from(pdfContent, 'utf-8');
        contentType = 'application/pdf';
        fileExtension = 'pdf';
        break;

      default:
        throw new Error('Unsupported format');
    }

    // Create response with file
    const response = new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}.${fileExtension}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });

    logger.info('Accounts exported successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      format,
      fileName: `${fileName}.${fileExtension}`,
      accountCount: accounts.length
    });

    return response;

  } catch (error: any) {
    logger.error('Error exporting accounts', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'EXPORT_FAILED',
      'Failed to export accounts',
      'An error occurred while exporting the chart of accounts. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/accounts/export',
        method: 'POST',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

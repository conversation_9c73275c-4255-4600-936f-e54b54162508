import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/accounts/template
 * Download chart of accounts import template
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to download the template.',
        {
          endpoint: '/api/accounting/accounts/template',
          method: 'GET'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    logger.info('Downloading chart of accounts template', LogCategory.ACCOUNTING, {
      userId: user.id
    });

    // Create template data with sample rows and instructions
    const templateData = [
      {
        'Account Number': '1000',
        'Account Name': 'Assets',
        'Account Type': 'asset',
        'Subtype': 'Current Asset',
        'Description': 'All asset accounts',
        'Parent Account Number': '',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'main,category'
      },
      {
        'Account Number': '1100',
        'Account Name': 'Current Assets',
        'Account Type': 'asset',
        'Subtype': 'Current Asset',
        'Description': 'Short-term assets',
        'Parent Account Number': '1000',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'current'
      },
      {
        'Account Number': '1110',
        'Account Name': 'Cash and Cash Equivalents',
        'Account Type': 'asset',
        'Subtype': 'Current Asset',
        'Description': 'Cash and highly liquid investments',
        'Parent Account Number': '1100',
        'Cost Center Code': 'ADMIN',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'cash,liquid'
      },
      {
        'Account Number': '2000',
        'Account Name': 'Liabilities',
        'Account Type': 'liability',
        'Subtype': 'Current Liability',
        'Description': 'All liability accounts',
        'Parent Account Number': '',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'main,category'
      },
      {
        'Account Number': '3000',
        'Account Name': 'Equity',
        'Account Type': 'equity',
        'Subtype': 'Retained Earnings',
        'Description': 'All equity accounts',
        'Parent Account Number': '',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'main,category'
      },
      {
        'Account Number': '4000',
        'Account Name': 'Revenue',
        'Account Type': 'revenue',
        'Subtype': 'Operating Revenue',
        'Description': 'All revenue accounts',
        'Parent Account Number': '',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'main,category'
      },
      {
        'Account Number': '5000',
        'Account Name': 'Expenses',
        'Account Type': 'expense',
        'Subtype': 'Operating Expense',
        'Description': 'All expense accounts',
        'Parent Account Number': '',
        'Cost Center Code': '',
        'Fiscal Year': '2024',
        'Active': 'Yes',
        'Tags': 'main,category'
      }
    ];

    // Create instructions sheet data
    const instructionsData = [
      { Field: 'Account Number', Required: 'Yes', Description: 'Unique account number (alphanumeric, hyphens allowed)', Example: '1100, CASH-001' },
      { Field: 'Account Name', Required: 'Yes', Description: 'Descriptive name for the account', Example: 'Cash at Bank, Accounts Receivable' },
      { Field: 'Account Type', Required: 'Yes', Description: 'Must be one of: asset, liability, equity, revenue, expense', Example: 'asset' },
      { Field: 'Subtype', Required: 'No', Description: 'Additional categorization for the account', Example: 'Current Asset, Fixed Asset' },
      { Field: 'Description', Required: 'No', Description: 'Detailed description of the account purpose', Example: 'Cash and cash equivalents' },
      { Field: 'Parent Account Number', Required: 'No', Description: 'Account number of parent account for hierarchy', Example: '1000' },
      { Field: 'Cost Center Code', Required: 'No', Description: 'Code of associated cost center', Example: 'ADMIN, SALES' },
      { Field: 'Fiscal Year', Required: 'No', Description: 'Fiscal year for the account', Example: '2024' },
      { Field: 'Active', Required: 'No', Description: 'Whether account is active (Yes/No, default: Yes)', Example: 'Yes' },
      { Field: 'Tags', Required: 'No', Description: 'Comma-separated tags for categorization', Example: 'cash,liquid,current' }
    ];

    const validationRules = [
      { Rule: 'Account Numbers', Description: 'Must be unique across all accounts' },
      { Rule: 'Account Types', Description: 'Must be exactly one of: asset, liability, equity, revenue, expense' },
      { Rule: 'Parent Accounts', Description: 'Parent account must exist and cannot create circular references' },
      { Rule: 'Hierarchy Depth', Description: 'Maximum 4 levels of nesting allowed' },
      { Rule: 'Cost Centers', Description: 'Cost center code must exist in the system if provided' },
      { Rule: 'Active Status', Description: 'Must be "Yes", "No", "True", "False", or empty (defaults to Yes)' },
      { Rule: 'File Format', Description: 'Excel (.xlsx, .xls) or CSV (.csv) files supported' },
      { Rule: 'File Size', Description: 'Maximum file size is 10MB' }
    ];

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Add template sheet
    const templateSheet = XLSX.utils.json_to_sheet(templateData);
    
    // Set column widths for template sheet
    templateSheet['!cols'] = [
      { wch: 15 }, // Account Number
      { wch: 30 }, // Account Name
      { wch: 15 }, // Account Type
      { wch: 20 }, // Subtype
      { wch: 40 }, // Description
      { wch: 20 }, // Parent Account Number
      { wch: 15 }, // Cost Center Code
      { wch: 12 }, // Fiscal Year
      { wch: 8 },  // Active
      { wch: 20 }  // Tags
    ];

    XLSX.utils.book_append_sheet(workbook, templateSheet, 'Chart of Accounts');

    // Add instructions sheet
    const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
    instructionsSheet['!cols'] = [
      { wch: 25 }, // Field
      { wch: 10 }, // Required
      { wch: 50 }, // Description
      { wch: 30 }  // Example
    ];
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Add validation rules sheet
    const validationSheet = XLSX.utils.json_to_sheet(validationRules);
    validationSheet['!cols'] = [
      { wch: 20 }, // Rule
      { wch: 60 }  // Description
    ];
    XLSX.utils.book_append_sheet(workbook, validationSheet, 'Validation Rules');

    // Generate Excel file
    const fileBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Create response
    const response = new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="chart-of-accounts-template.xlsx"',
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });

    logger.info('Chart of accounts template downloaded', LogCategory.ACCOUNTING, {
      userId: user.id,
      fileName: 'chart-of-accounts-template.xlsx'
    });

    return response;

  } catch (error: any) {
    logger.error('Error generating chart of accounts template', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'TEMPLATE_GENERATION_FAILED',
      'Failed to generate template',
      'An error occurred while generating the import template. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/accounts/template',
        method: 'GET',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

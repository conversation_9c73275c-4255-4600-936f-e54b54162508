import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/fiscal-years
 * Get all fiscal years
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_MANAGER,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status'); // active, inactive, all
    const current = searchParams.get('current'); // true, false

    // Generate fiscal years (fallback if no database implementation)
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    
    // Determine current fiscal year (assuming July 1 - June 30)
    const currentFiscalStartYear = currentMonth >= 6 ? currentYear : currentYear - 1;
    
    const fiscalYears = [];
    for (let i = -3; i <= 3; i++) {
      const startYear = currentFiscalStartYear + i;
      const endYear = startYear + 1;
      const fiscalYear = {
        id: `${startYear}-${endYear}`,
        year: `${startYear}-${endYear}`,
        startDate: new Date(startYear, 6, 1), // July 1st
        endDate: new Date(endYear, 5, 30), // June 30th
        isActive: Math.abs(i) <= 2, // Keep 2 years before and after current as active
        isCurrent: i === 0,
        status: i === 0 ? 'current' : Math.abs(i) <= 1 ? 'active' : 'inactive'
      };
      fiscalYears.push(fiscalYear);
    }

    // Filter based on query parameters
    let filteredFiscalYears = fiscalYears;

    if (status === 'active') {
      filteredFiscalYears = fiscalYears.filter(fy => fy.isActive);
    } else if (status === 'inactive') {
      filteredFiscalYears = fiscalYears.filter(fy => !fy.isActive);
    }

    if (current === 'true') {
      filteredFiscalYears = fiscalYears.filter(fy => fy.isCurrent);
    }

    logger.info('Fiscal years fetched successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      count: filteredFiscalYears.length,
      filters: { status, current }
    });

    return NextResponse.json({
      fiscalYears: filteredFiscalYears,
      totalCount: filteredFiscalYears.length
    });

  } catch (error: unknown) {
    logger.error('Error fetching fiscal years', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/fiscal-years
 * Create a new fiscal year
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to create fiscal years' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { name, startDate, endDate, description } = body;

    // Validate required fields
    if (!name || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields: name, startDate, endDate' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Create fiscal year object
    const fiscalYear = {
      id: name.replace(/\s+/g, '-').toLowerCase(),
      year: name,
      name,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      description,
      isActive: true,
      isCurrent: false,
      status: 'active',
      createdBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    logger.info('Fiscal year created successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYearId: fiscalYear.id,
      name: fiscalYear.name
    });

    return NextResponse.json(fiscalYear, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating fiscal year', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/bank-accounts
 * Get all bank accounts
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status'); // active, inactive, all
    const type = searchParams.get('type'); // checking, savings, etc.

    // Default bank accounts (fallback if no database implementation)
    const bankAccounts = [
      {
        id: 'tcm_main_account',
        name: 'TCM Main Operating Account',
        accountNumber: '**********',
        bank: 'National Bank of Malawi',
        bankCode: 'NBM',
        accountType: 'checking',
        currency: 'MWK',
        isActive: true,
        isPrimary: true,
        balance: 0,
        description: 'Main operating account for Teachers Council of Malawi',
        swiftCode: 'NBMAMWMW',
        branchCode: '001',
        branchName: 'Lilongwe Main Branch'
      },
      {
        id: 'tcm_payroll_account',
        name: 'TCM Payroll Account',
        accountNumber: '**********',
        bank: 'Standard Bank Malawi',
        bankCode: 'SBM',
        accountType: 'checking',
        currency: 'MWK',
        isActive: true,
        isPrimary: false,
        balance: 0,
        description: 'Dedicated account for payroll transactions',
        swiftCode: 'SBICMWMW',
        branchCode: '002',
        branchName: 'Lilongwe City Centre'
      },
      {
        id: 'tcm_savings_account',
        name: 'TCM Reserve Fund',
        accountNumber: '**********',
        bank: 'FDH Bank',
        bankCode: 'FDH',
        accountType: 'savings',
        currency: 'MWK',
        isActive: true,
        isPrimary: false,
        balance: 0,
        description: 'Reserve fund for emergency expenses',
        swiftCode: 'FDHBMWMW',
        branchCode: '003',
        branchName: 'Area 3 Branch'
      },
      {
        id: 'tcm_usd_account',
        name: 'TCM USD Account',
        accountNumber: '**********',
        bank: 'National Bank of Malawi',
        bankCode: 'NBM',
        accountType: 'checking',
        currency: 'USD',
        isActive: true,
        isPrimary: false,
        balance: 0,
        description: 'USD account for international transactions',
        swiftCode: 'NBMAMWMW',
        branchCode: '001',
        branchName: 'Lilongwe Main Branch'
      }
    ];

    // Filter based on status
    let filteredBankAccounts = bankAccounts;
    if (status === 'active') {
      filteredBankAccounts = bankAccounts.filter(ba => ba.isActive);
    } else if (status === 'inactive') {
      filteredBankAccounts = bankAccounts.filter(ba => !ba.isActive);
    }

    // Filter based on type
    if (type) {
      filteredBankAccounts = filteredBankAccounts.filter(ba => ba.accountType === type);
    }

    logger.info('Bank accounts fetched successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      count: filteredBankAccounts.length,
      filters: { status, type }
    });

    return NextResponse.json({
      bankAccounts: filteredBankAccounts,
      totalCount: filteredBankAccounts.length
    });

  } catch (error: unknown) {
    logger.error('Error fetching bank accounts', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/bank-accounts
 * Create a new bank account
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to create bank accounts' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { 
      name, 
      accountNumber, 
      bank, 
      bankCode, 
      accountType, 
      currency, 
      description,
      swiftCode,
      branchCode,
      branchName
    } = body;

    // Validate required fields
    if (!name || !accountNumber || !bank || !accountType || !currency) {
      return NextResponse.json(
        { error: 'Missing required fields: name, accountNumber, bank, accountType, currency' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Create bank account object
    const bankAccount = {
      id: name.toLowerCase().replace(/\s+/g, '_'),
      name,
      accountNumber,
      bank,
      bankCode: bankCode || '',
      accountType,
      currency,
      isActive: true,
      isPrimary: false,
      balance: 0,
      description: description || '',
      swiftCode: swiftCode || '',
      branchCode: branchCode || '',
      branchName: branchName || '',
      createdBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    logger.info('Bank account created successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      bankAccountId: bankAccount.id,
      name: bankAccount.name,
      accountNumber: bankAccount.accountNumber
    });

    return NextResponse.json(bankAccount, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating bank account', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// app/api/accounting/assets/template/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import { AssetImportExportService } from '@/lib/services/accounting/asset-import-export-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Generate template
    const importExportService = new AssetImportExportService();
    const buffer = await importExportService.generateImportTemplate();

    // Return template file
    logger.info('Asset import template downloaded', LogCategory.EXPORT, {
      userId: user.id,
      userEmail: user.email
    });

    // Convert Buffer to Uint8Array which is compatible with Response
    const uint8Array = new Uint8Array(buffer);

    return new Response(uint8Array, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="asset-import-template.xlsx"',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error: unknown) {
    logger.error('Error generating asset import template', LogCategory.EXPORT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred generating template' },
      { status: 500 }
    );
  }
}

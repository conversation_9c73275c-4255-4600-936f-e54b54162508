// app/api/accounting/assets/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import { AssetImportExportService } from '@/lib/services/accounting/asset-import-export-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file' },
        { status: 400 }
      );
    }

    // Read file
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Parse file
    const workbook = XLSX.read(buffer, { type: 'array' });

    // Get first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[];

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for asset import', LogCategory.IMPORT, {
        userId: user.id
      });
      return NextResponse.json(
        { error: 'File is empty' },
        { status: 400 }
      );
    }

    // Process import data
    const importExportService = new AssetImportExportService();
    const result = await importExportService.processImportData(rows, user.id);

    // Log import results
    logger.info('Asset import completed', LogCategory.IMPORT, {
      userId: user.id,
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error importing assets', LogCategory.IMPORT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred during import' },
      { status: 500 }
    );
  }
}

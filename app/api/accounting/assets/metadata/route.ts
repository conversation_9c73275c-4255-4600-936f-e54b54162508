import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetService } from '@/lib/services/accounting/asset-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/assets/metadata
 * Get asset metadata (categories, locations)
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get categories and locations
    const [categories, locations] = await Promise.all([
      assetService.getAssetCategories(),
      assetService.getAssetLocations()
    ]);

    // Return metadata
    return NextResponse.json({
      categories,
      locations
    });
  } catch (error: unknown) {
    logger.error('Error fetching asset metadata:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

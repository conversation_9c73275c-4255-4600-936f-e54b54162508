// app/api/accounting/assets/maintenance/route.ts
import { NextRequest } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetIntegrationService } from '@/lib/services/accounting/asset-integration-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleBadRequestError
} from '@/lib/backend/utils/api-response';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for creating maintenance entries
const maintenanceSchema = z.object({
  maintenanceId: z.string().min(1, 'Maintenance ID is required')
    .refine(id => mongoose.Types.ObjectId.isValid(id), {
      message: 'Invalid maintenance ID format'
    })
});

/**
 * POST /api/accounting/assets/maintenance
 * Create accounting entries for asset maintenance
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.ASSET_MANAGER
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = maintenanceSchema.safeParse(body);

    if (!validationResult.success) {
      return handleBadRequestError(validationResult.error.errors[0].message);
    }

    // Create accounting entries
    const journalEntry = await assetIntegrationService.createAssetMaintenanceEntries(
      validationResult.data.maintenanceId,
      user.id
    );

    // Return success response
    return successResponse(
      journalEntry,
      'Asset maintenance accounting entries created successfully'
    );
  } catch (error: unknown) {
    // Handle specific errors
    if (error instanceof Error) {
      if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred'.includes('not found')) {
        return handleBadRequestError(error.message);
      }

      if (error.message.includes('not configured')) {
        return handleBadRequestError(error.message);
      }
    }

    // Handle general errors
    return handleApiError(error);
  }
}

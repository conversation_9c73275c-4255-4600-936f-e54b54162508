import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetService } from '@/lib/services/accounting/asset-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/assets/statistics
 * Get asset statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get asset statistics
    const statistics = await assetService.getAssetStatistics();

    // Return statistics
    return NextResponse.json(statistics);
  } catch (error: unknown) {
    logger.error('Error fetching asset statistics:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

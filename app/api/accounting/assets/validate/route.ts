// app/api/accounting/assets/validate/route.ts
import { NextRequest } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetCategoryRulesService } from '@/lib/services/accounting/asset-category-rules-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleBadRequestError
} from '@/lib/backend/utils/api-response';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for asset validation
const assetValidationSchema = z.object({
  category: z.string().min(1, 'Category ID is required')
    .refine(id => mongoose.Types.ObjectId.isValid(id), {
      message: 'Invalid category ID format'
    }),
  acquisitionCost: z.number().min(0, 'Acquisition cost must be non-negative'),
  usefulLifeYears: z.number().optional(),
  residualValue: z.number().optional(),
  depreciationMethod: z.string().optional()
});

/**
 * POST /api/accounting/assets/validate
 * Validate asset against category accounting rules
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.ASSET_MANAGER
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = assetValidationSchema.safeParse(body);

    if (!validationResult.success) {
      return handleBadRequestError(validationResult.error.errors[0].message);
    }

    // Validate asset against rules
    const result = await assetCategoryRulesService.validateAssetAgainstRules(validationResult.data);

    // Return success response
    return successResponse(
      result,
      result.valid
        ? 'Asset complies with category accounting rules'
        : 'Asset does not comply with category accounting rules'
    );
  } catch (error) {
    // Handle general errors
    return handleApiError(error);
  }
}

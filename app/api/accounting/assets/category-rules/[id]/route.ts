// app/api/accounting/assets/category-rules/[id]/route.ts
import { NextRequest } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetCategoryRulesService } from '@/lib/services/accounting/asset-category-rules-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleNotFoundError
} from '@/lib/backend/utils/api-response';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/assets/category-rules/[id]
 * Get accounting rules for an asset category
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Validate ID format
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return handleNotFoundError('Asset category', id);
    }

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.ASSET_MANAGER
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get category rules
    const rules = await assetCategoryRulesService.getCategoryRules(id);

    if (!rules) {
      return handleNotFoundError('Accounting rules for asset category', id);
    }

    // Return success response
    return successResponse(
      rules,
      'Asset category accounting rules retrieved successfully'
    );
  } catch (error) {
    // Handle general errors
    return handleApiError(error);
  }
}

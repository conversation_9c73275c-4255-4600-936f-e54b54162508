// app/api/accounting/assets/budget/compliance/route.ts
import { NextRequest } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetBudgetService } from '@/lib/services/accounting/asset-budget-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleBadRequestError
} from '@/lib/backend/utils/api-response';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for budget compliance check
const complianceCheckSchema = z.object({
  category: z.string().min(1, 'Category ID is required')
    .refine(id => mongoose.Types.ObjectId.isValid(id), {
      message: 'Invalid category ID format'
    }),
  department: z.string().optional()
    .refine(id => !id || mongoose.Types.ObjectId.isValid(id), {
      message: 'Invalid department ID format'
    }),
  acquisitionCost: z.number().min(0.01, 'Acquisition cost must be greater than 0'),
  acquisitionDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid acquisition date' }
  )
});

/**
 * POST /api/accounting/assets/budget/compliance
 * Check if asset acquisition is within budget
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.ASSET_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = complianceCheckSchema.safeParse(body);

    if (!validationResult.success) {
      return handleBadRequestError(validationResult.error.errors[0].message);
    }

    // Check budget compliance
    const complianceResult = await assetBudgetService.checkAssetBudgetCompliance({
      category: validationResult.data.category,
      department: validationResult.data.department,
      acquisitionCost: validationResult.data.acquisitionCost,
      acquisitionDate: new Date(validationResult.data.acquisitionDate)
    });

    // Return success response
    return successResponse(
      complianceResult,
      complianceResult.message
    );
  } catch (error) {
    // Handle general errors
    return handleApiError(error);
  }
}

// app/api/accounting/assets/budget/[id]/variance/route.ts
import { NextRequest } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetBudgetService } from '@/lib/services/accounting/asset-budget-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleNotFoundError
} from '@/lib/backend/utils/api-response';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/assets/budget/[id]/variance
 * Get asset budget variance analysis
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare assetBudgetId at function scope with default value
  let assetBudgetId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    assetBudgetId = id;

    // Validate ID format
    if (!assetBudgetId || !mongoose.Types.ObjectId.isValid(assetBudgetId)) {
      return handleNotFoundError('Asset budget', assetBudgetId);
    }

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.ASSET_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Get budget variance analysis
    const varianceAnalysis = await assetBudgetService.analyzeAssetBudgetVariance(
      assetBudgetId
    );

    // Return success response
    return successResponse(
      varianceAnalysis,
      'Asset budget variance analysis retrieved successfully'
    );
  } catch (error: unknown) {
    // Handle specific errors
    if (error instanceof Error) {
      if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred'.includes('not found')) {
        return handleNotFoundError('Asset budget with ID ' + assetBudgetId);
      }

      // Log the error with the budget ID if available
      console.error(`Error processing asset budget variance for ID: ${assetBudgetId}`, error);
    }

    // Handle general errors
    return handleApiError(error);
  }
}

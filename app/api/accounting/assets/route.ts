// app/api/accounting/assets/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { assetService } from '@/lib/services/accounting/asset-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';

/**
 * GET /api/accounting/assets
 * Get assets with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const location = searchParams.get('location');
    const department = searchParams.get('department');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const sortBy = searchParams.get('sortBy') || 'acquisitionDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get assets
    const result = await assetService.getAssets({
      page,
      limit,
      category: category || undefined,
      location: location || undefined,
      department: department || undefined,
      status: status || undefined,
      search: search || undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      sortBy,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching assets:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/assets
 * Create a new asset
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.category || !body.location || !body.acquisitionDate || !body.acquisitionCost) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create asset
    const asset = await assetService.createAsset(body);

    // Return created asset
    return NextResponse.json(
      {
        success: true,
        message: 'Asset created successfully',
        data: asset
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating asset:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/assets
 * Update an asset
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Set updated by
    body.updatedBy = user.id;

    // Update asset
    const asset = await assetService.updateAsset(body.id, body);

    if (!asset) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Return updated asset
    return NextResponse.json({
      success: true,
      message: 'Asset updated successfully',
      data: asset
    });
  } catch (error: unknown) {
    logger.error('Error updating asset:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

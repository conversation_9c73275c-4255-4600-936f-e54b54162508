import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { assetService } from '@/lib/services/accounting/asset-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * POST /api/accounting/assets/depreciation/batch
 * Run batch depreciation for all assets
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.date || !body.fiscalYear || !body.fiscalPeriod) {
      return NextResponse.json(
        { error: 'Date, fiscal year, and fiscal period are required' },
        { status: 400 }
      );
    }

    // Run batch depreciation
    const count = await assetService.runDepreciationBatch(
      new Date(body.date),
      body.fiscalYear,
      body.fiscalPeriod
    );

    // Return result
    return NextResponse.json({
      success: true,
      message: `Batch depreciation completed successfully for ${count} assets`,
      count
    });
  } catch (error: unknown) {
    logger.error('Error running batch depreciation:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

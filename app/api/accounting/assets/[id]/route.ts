// app/api/accounting/assets/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { assetService } from '@/lib/services/accounting/asset-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getServerSession } from '@/lib/backend/auth/session';

/**
 * GET /api/accounting/assets/[id]
 * Get an asset by ID
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id: assetId } = await params;

    // Get asset
    const asset = await assetService.getAssetById(assetId);

    if (!asset) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Return asset
    return NextResponse.json(asset);
  } catch (error: unknown) {
    logger.error(`Error fetching asset:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/assets/[id]
 * Delete an asset
 */
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: assetId } = await params;

    // Delete asset
    const asset = await assetService.deleteAsset(assetId);

    if (!asset) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Asset deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting asset:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/assets/[id]
 * Perform operations on an asset (depreciate, maintain, dispose)
 */
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: assetId } = await params;

    // Get request body
    const body = await request.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let asset;

    // Process based on action
    switch (body.action) {
      case 'depreciate':
        // Validate required fields
        if (!body.date || !body.fiscalYear || !body.fiscalPeriod) {
          return NextResponse.json(
            { error: 'Date, fiscal year, and fiscal period are required for depreciation' },
            { status: 400 }
          );
        }

        // Calculate depreciation if amount not provided
        let amount = body.amount;
        if (amount === undefined) {
          amount = await assetService.calculateDepreciation(assetId, new Date(body.date));
        }

        // Record depreciation
        asset = await assetService.recordDepreciation(assetId, {
          date: new Date(body.date),
          amount,
          fiscalYear: body.fiscalYear,
          fiscalPeriod: body.fiscalPeriod,
          notes: body.notes
        });
        break;

      case 'maintain':
        // Validate required fields
        if (!body.date || !body.description || body.cost === undefined) {
          return NextResponse.json(
            { error: 'Date, description, and cost are required for maintenance' },
            { status: 400 }
          );
        }

        // Record maintenance
        asset = await assetService.recordMaintenance(assetId, {
          date: new Date(body.date),
          description: body.description,
          cost: body.cost,
          provider: body.provider,
          receiptNumber: body.receiptNumber,
          notes: body.notes,
          performedBy: body.performedBy || user.id
        });
        break;

      case 'complete-maintenance':
        // Complete maintenance
        asset = await assetService.completeMaintenance(assetId);
        break;

      case 'dispose':
        // Validate required fields
        if (!body.disposalDate || !body.disposalReason || !body.disposalMethod) {
          return NextResponse.json(
            { error: 'Disposal date, reason, and method are required for disposal' },
            { status: 400 }
          );
        }

        // Dispose asset
        asset = await assetService.disposeAsset(assetId, {
          disposalDate: new Date(body.disposalDate),
          disposalAmount: body.disposalAmount,
          disposalReason: body.disposalReason,
          disposalMethod: body.disposalMethod,
          disposalReceiptNumber: body.disposalReceiptNumber
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: depreciate, maintain, complete-maintenance, dispose' },
          { status: 400 }
        );
    }

    if (!asset) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Return updated asset
    return NextResponse.json({
      success: true,
      message: `Asset ${body.action} operation completed successfully`,
      data: asset
    });
  } catch (error: unknown) {
    logger.error(`Error performing operation on asset:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

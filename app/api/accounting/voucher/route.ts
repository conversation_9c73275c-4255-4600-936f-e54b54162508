// app/api/accounting/voucher/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import { z } from 'zod';

export const runtime = 'nodejs';



// Define the voucher schema for validation
const voucherSchema = z.object({
  voucherNumber: z.string().optional(), // Will be generated if not provided
  voucherType: z.enum(['payment', 'receipt', 'journal']),
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }),
  reference: z.string().optional(),
  description: z.string().min(2, "Description is required"),
  totalAmount: z.number().positive("Amount must be positive"),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'posted', 'cancelled']).default('draft'),
  fiscalYear: z.string(),
  payee: z.string().optional(),
  paymentMethod: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(
    z.object({
      description: z.string(),
      account: z.string(),
      amount: z.number().positive("Amount must be positive"),
      isDebit: z.boolean().default(true),
    })
  ).optional(),
});

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const voucherType = searchParams.get('voucherType');
    const status = searchParams.get('status');
    const fiscalYear = searchParams.get('fiscalYear');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit') as string) : 10;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1;

    // Mock data for demonstration
    const mockVouchers = [
      {
        id: "PV-2025-0001",
        voucherNumber: "PV-2025-0001",
        voucherType: "payment",
        date: "2025-01-15",
        reference: "INV-2025-001",
        description: "Payment for office supplies",
        totalAmount: 125000,
        status: "posted",
        fiscalYear: "2025-2026",
        payee: "Office Supplies Ltd",
        paymentMethod: "Bank Transfer",
        createdAt: "2025-01-15T10:30:00Z",
        updatedAt: "2025-01-15T14:45:00Z",
        items: [
          {
            description: "Office stationery",
            account: "Office Supplies",
            amount: 125000,
            isDebit: true,
          }
        ]
      },
      {
        id: "RV-2025-0001",
        voucherNumber: "RV-2025-0001",
        voucherType: "receipt",
        date: "2025-01-20",
        reference: "CERT-2025-001",
        description: "Receipt for teacher certification fees",
        totalAmount: 750000,
        status: "posted",
        fiscalYear: "2025-2026",
        payee: "Various Teachers",
        paymentMethod: "Bank Deposit",
        createdAt: "2025-01-20T09:15:00Z",
        updatedAt: "2025-01-21T11:30:00Z",
        items: [
          {
            description: "Certification fees",
            account: "Fee Income",
            amount: 750000,
            isDebit: false,
          }
        ]
      },
      {
        id: "JV-2025-0001",
        voucherNumber: "JV-2025-0001",
        voucherType: "journal",
        date: "2025-01-25",
        reference: "ADJ-2025-001",
        description: "Adjustment for prepaid expenses",
        totalAmount: 250000,
        status: "pending_approval",
        fiscalYear: "2025-2026",
        createdAt: "2025-01-25T14:20:00Z",
        updatedAt: "2025-01-25T14:20:00Z",
        items: [
          {
            description: "Prepaid insurance",
            account: "Prepaid Expenses",
            amount: 250000,
            isDebit: true,
          },
          {
            description: "Insurance expense adjustment",
            account: "Insurance Expense",
            amount: 250000,
            isDebit: false,
          }
        ]
      },
    ];

    // If ID is provided, return a single voucher
    if (id) {
      const voucher = mockVouchers.find(v => v.id === id);

      if (!voucher) {
        return NextResponse.json(
          { error: 'Voucher not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(voucher);
    }

    // Filter vouchers based on query parameters
    let filteredVouchers = [...mockVouchers];

    if (voucherType) {
      filteredVouchers = filteredVouchers.filter(v => v.voucherType === voucherType);
    }

    if (status) {
      filteredVouchers = filteredVouchers.filter(v => v.status === status);
    }

    if (fiscalYear) {
      filteredVouchers = filteredVouchers.filter(v => v.fiscalYear === fiscalYear);
    }

    if (startDate) {
      const start = new Date(startDate);
      filteredVouchers = filteredVouchers.filter(v => new Date(v.date) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      filteredVouchers = filteredVouchers.filter(v => new Date(v.date) <= end);
    }

    // Paginate results
    const totalVouchers = filteredVouchers.length;
    const totalPages = Math.ceil(totalVouchers / limit);
    const offset = (page - 1) * limit;
    const paginatedVouchers = filteredVouchers.slice(offset, offset + limit);

    return NextResponse.json({
      vouchers: paginatedVouchers,
      pagination: {
        total: totalVouchers,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      }
    });
  } catch (error) {
    console.error('Error fetching vouchers:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // Validate the request data
    const validationResult = voucherSchema.safeParse(data);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Generate voucher number if not provided
    const voucherNumber = data.voucherNumber || generateVoucherNumber(data.voucherType);

    // Create voucher in database
    const voucher = new Voucher({
      voucherNumber,
      voucherType: validationResult.data.voucherType,
      date: new Date(validationResult.data.date),
      description: validationResult.data.description,
      totalAmount: validationResult.data.totalAmount,
      status: validationResult.data.status,
      fiscalYear: validationResult.data.fiscalYear,
      payee: validationResult.data.payee,
      paymentMethod: validationResult.data.paymentMethod,
      notes: validationResult.data.notes,
      createdBy: user._id?.toString() || user.id,
      updatedBy: user._id?.toString() || user.id,
    });

    await voucher.save();

    return NextResponse.json({
      success: true,
      message: 'Voucher created successfully',
      data: {
        _id: voucher._id,
        id: voucher._id,
        voucherNumber: voucher.voucherNumber,
        voucherType: voucher.voucherType,
        date: voucher.date,
        description: voucher.description,
        totalAmount: voucher.totalAmount,
        status: voucher.status,
        fiscalYear: voucher.fiscalYear,
        payee: voucher.payee,
        paymentMethod: voucher.paymentMethod,
        notes: voucher.notes,
        createdAt: voucher.createdAt,
        updatedAt: voucher.updatedAt,
        createdBy: voucher.createdBy,
        updatedBy: voucher.updatedBy,
      }
    });
  } catch (error) {
    console.error('Error creating voucher:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // Ensure ID is provided
    if (!data.id) {
      return NextResponse.json(
        { error: 'Voucher ID is required' },
        { status: 400 }
      );
    }

    // Validate the request data (excluding id and voucherNumber)
    const { id, voucherNumber, ...voucherData } = data;
    const validationResult = voucherSchema.partial().safeParse(voucherData);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Voucher updated successfully',
      data: {
        ...data,
        updatedAt: new Date().toISOString(),
        updatedBy: user._id?.toString() || user.id,
      }
    });
  } catch (error) {
    console.error('Error updating voucher:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Helper function to generate voucher number
function generateVoucherNumber(voucherType: string): string {
  const prefix = voucherType === 'payment' ? 'PV' :
                voucherType === 'receipt' ? 'RV' : 'JV';
  const year = new Date().getFullYear();
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  return `${prefix}-${year}-${randomNum}`;
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { templateService } from '@/lib/services/accounting/import-export/template-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/accounting/import-export/templates/initialize
 * Initialize default templates
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Create default templates
    await templateService.createDefaultTemplates(user.id);

    return NextResponse.json({
      success: true,
      message: 'Default templates initialized'
    });
  } catch (error) {
    logger.error('Error initializing default templates', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to initialize default templates' },
      { status: 500 }
    );
  }
}

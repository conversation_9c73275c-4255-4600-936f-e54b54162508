import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { templateService } from '@/lib/services/accounting/import-export/template-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/import-export/templates
 * Get templates
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type') as 'import' | 'export' | undefined;
    const entityType = searchParams.get('entityType') || undefined;
    const isDefault = searchParams.has('isDefault') ? searchParams.get('isDefault') === 'true' : undefined;
    const isSystem = searchParams.has('isSystem') ? searchParams.get('isSystem') === 'true' : undefined;

    // Get templates
    const templates = await templateService.getTemplates({
      type,
      entityType,
      isDefault,
      isSystem
    });

    return NextResponse.json(templates);
  } catch (error) {
    logger.error('Error getting templates', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/import-export/templates
 * Create a new template
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.type || !body.entityType || !body.format || !body.fields) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type, entityType, format, fields' },
        { status: 400 }
      );
    }

    // Create template
    const template = await templateService.createTemplate(body, user.id);

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    logger.error('Error creating template', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}

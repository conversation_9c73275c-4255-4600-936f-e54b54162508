import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { dataImportService, ImportEntityType, ImportFormat } from '@/lib/services/accounting/import-export/data-import-service';
import { templateService } from '@/lib/services/accounting/import-export/template-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/accounting/import-export/import
 * Import data from a file
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Parse form data
    const formData = await req.formData();

    // Get file
    const file = formData.get('file');
    if (!file || typeof file === 'string') {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Get form fields
    const format = formData.get('format') as ImportFormat;
    const entityType = formData.get('entityType') as ImportEntityType;
    const templateId = formData.get('templateId') as string;

    // Validate required fields
    if (!format || !entityType) {
      return NextResponse.json(
        { error: 'Missing required fields: format, entityType' },
        { status: 400 }
      );
    }

    // Validate format
    if (!['csv', 'excel', 'json'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Must be csv, excel, or json.' },
        { status: 400 }
      );
    }

    // Validate entity type
    if (!['account', 'transaction', 'customer', 'vendor', 'employee', 'budget', 'bank_account', 'bank_transaction'].includes(entityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type.' },
        { status: 400 }
      );
    }

    // Validate file format
    const fileName = file.name || '';
    if (!dataImportService.validateFileFormat(fileName, format)) {
      return NextResponse.json(
        { error: `Invalid file format. Expected ${format} file.` },
        { status: 400 }
      );
    }

    // Read file
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 400 }
      );
    }

    // Check if using template
    let importOptions = {
      format,
      entityType,
      file: buffer,
      fileName: fileName,
      headerRow: formData.get('headerRow') !== 'false',
      skipRows: parseInt(formData.get('skipRows') as string || '0'),
      mapping: formData.get('mapping') ? JSON.parse(formData.get('mapping') as string) : undefined,
      dateFormat: formData.get('dateFormat') as string,
      updateExisting: formData.get('updateExisting') === 'true',
      identifierField: formData.get('identifierField') as string,
      sheetName: formData.get('sheetName') as string,
      sheetIndex: formData.get('sheetIndex') ? parseInt(formData.get('sheetIndex') as string) : undefined,
      userId: user.id
    };

    if (templateId) {
      // Get template
      const template = await templateService.getTemplate(templateId);
      if (!template) {
        return NextResponse.json(
          { error: 'Template not found' },
          { status: 404 }
        );
      }

      // Apply template options
      importOptions = {
        ...importOptions,
        headerRow: template.options?.headerRow !== false,
        skipRows: template.options?.skipRows || importOptions.skipRows,
        mapping: template.mapping || importOptions.mapping,
        dateFormat: template.options?.dateFormat || importOptions.dateFormat,
        updateExisting: template.options?.updateExisting || importOptions.updateExisting,
        identifierField: template.options?.identifierField || importOptions.identifierField,
        sheetName: template.options?.sheetName || importOptions.sheetName
      };
    }

    // Import data
    const result = await dataImportService.importData(importOptions);

    // Log import
    logger.info(`Imported ${entityType} data from ${format}`, LogCategory.IMPORT, {
      userId: user.id,
      entityType,
      format,
      totalRecords: result.totalRecords,
      successCount: result.successCount,
      errorCount: result.errorCount
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error importing data', LogCategory.IMPORT, error);
    return NextResponse.json(
      { error: 'Failed to import data', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

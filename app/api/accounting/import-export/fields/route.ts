// app/api/accounting/import-export/fields/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { dataExportService, ExportEntityType } from '@/lib/services/accounting/import-export/data-export-service';
import { dataImportService, ImportEntityType } from '@/lib/services/accounting/import-export/data-import-service';
import { logger } from '@/lib/utils/logger';

/**
 * GET /api/accounting/import-export/fields
 * Get available fields for an entity type
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const entityType = searchParams.get('entityType');
    const type = searchParams.get('type');

    // Validate required parameters
    if (!entityType) {
      return NextResponse.json(
        { error: 'Missing required parameter: entityType' },
        { status: 400 }
      );
    }

    // Validate entity type
    if (!['account', 'transaction', 'customer', 'vendor', 'employee', 'budget', 'bank_account', 'bank_transaction'].includes(entityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type.' },
        { status: 400 }
      );
    }

    let fields: string[] = [];
    let requiredFields: string[] = [];

    // Get fields based on type
    if (type === 'import') {
      fields = dataExportService.getAvailableFields(entityType as ExportEntityType);
      requiredFields = dataImportService.getRequiredFields(entityType as ImportEntityType);
    } else {
      fields = dataExportService.getAvailableFields(entityType as ExportEntityType);
    }

    return NextResponse.json({
      fields,
      requiredFields
    });
  } catch (error) {
    logger.error('Error getting available fields', error);
    return NextResponse.json(
      { error: 'Failed to get available fields' },
      { status: 500 }
    );
  }
}

// app/api/accounting/project-budgets/[id]/actuals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectBudgetService } from '@/lib/services/accounting/project-budget-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * POST /api/accounting/project-budgets/[id]/actuals
 * Update actual amounts for a project budget
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: budgetId } = await params;
    id = budgetId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Update actuals
    const projectBudget = await projectBudgetService.updateActuals(id);

    return NextResponse.json({
      success: true,
      message: 'Project budget actuals updated successfully',
      data: projectBudget
    });
  } catch (error: unknown) {
    logger.error('Error updating project budget actuals', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/project-budgets/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectBudgetService } from '@/lib/services/accounting/project-budget-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for budget line item
const budgetLineItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  account: z.string().optional(),
  costCenter: z.string().optional(),
  plannedAmount: z.number().min(0, 'Planned amount cannot be negative'),
  actualAmount: z.number().default(0),
  variance: z.number().default(0),
  notes: z.string().optional()
});

// Validation schema for budget period
const budgetPeriodSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ),
  status: z.enum(['planned', 'active', 'completed']).default('planned'),
  lineItems: z.array(budgetLineItemSchema).min(1, 'At least one line item is required'),
  totalPlanned: z.number().optional(),
  totalActual: z.number().optional(),
  totalVariance: z.number().optional()
});

// Validation schema for updating project budget
const updateProjectBudgetSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters long').optional(),
  description: z.string().optional(),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ).optional(),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ).optional(),
  totalBudget: z.number().min(0, 'Total budget cannot be negative').optional(),
  currency: z.string().optional(),
  exchangeRate: z.number().positive().optional(),
  fiscalYear: z.string().min(4, 'Fiscal year is required').optional(),
  periods: z.array(budgetPeriodSchema).optional(),
  manager: z.string().optional(),
  department: z.string().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional()
});

/**
 * GET /api/accounting/project-budgets/[id]
 * Get a project budget by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get project budget
    const projectBudget = await projectBudgetService.findById(
      id,
      ['projectId', 'manager', 'department', 'approvedBy', 'periods.lineItems.account', 'periods.lineItems.costCenter']
    );

    if (!projectBudget) {
      return NextResponse.json(
        { error: 'Project budget not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: projectBudget
    });
  } catch (error: unknown) {
    logger.error('Error getting project budget', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/project-budgets/[id]
 * Update a project budget
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = updateProjectBudgetSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Process dates
    const data: Record<string, unknown> = { ...validationResult.data };

    if (data.startDate && typeof data.startDate === 'string') {
      data.startDate = new Date(data.startDate);
    }

    if (data.endDate && typeof data.endDate === 'string') {
      data.endDate = new Date(data.endDate);
    }

    if (data.periods && Array.isArray(data.periods)) {
      data.periods = (data.periods as any[]).map((period) => ({
        ...period,
        startDate: typeof period.startDate === 'string' ? new Date(period.startDate) : period.startDate,
        endDate: typeof period.endDate === 'string' ? new Date(period.endDate) : period.endDate
      }));
    }

    // Update project budget
    const projectBudget = await projectBudgetService.updateProjectBudget(
      id,
      data,
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Project budget updated successfully',
      data: projectBudget
    });
  } catch (error: unknown) {
    logger.error('Error updating project budget', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/project-budgets/[id]
 * Delete a project budget
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id: budgetId } = await params;
    id = budgetId;

    // Delete project budget
    await projectBudgetService.deleteById(id);

    return NextResponse.json({
      success: true,
      message: 'Project budget deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting project budget', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/project-budgets/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectBudgetService } from '@/lib/services/accounting/project-budget-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for status change
const statusChangeSchema = z.object({
  action: z.enum(['approve', 'activate', 'complete', 'cancel'])
});

/**
 * POST /api/accounting/project-budgets/[id]/status
 * Change the status of a project budget
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: budgetId } = await params;
    id = budgetId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = statusChangeSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { action } = validationResult.data;
    let projectBudget;

    // Perform the requested action
    switch (action) {
      case 'approve':
        projectBudget = await projectBudgetService.approveProjectBudget(id, user.id);
        break;
      case 'activate':
        projectBudget = await projectBudgetService.activateProjectBudget(id, user.id);
        break;
      case 'complete':
        projectBudget = await projectBudgetService.completeProjectBudget(id, user.id);
        break;
      case 'cancel':
        projectBudget = await projectBudgetService.cancelProjectBudget(id, user.id);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Project budget ${action}d successfully`,
      data: projectBudget
    });
  } catch (error: unknown) {
    logger.error('Error changing project budget status', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

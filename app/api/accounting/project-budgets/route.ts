// app/api/accounting/project-budgets/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectBudgetService } from '@/lib/services/accounting/project-budget-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for budget line item
const budgetLineItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  account: z.string().optional(),
  costCenter: z.string().optional(),
  plannedAmount: z.number().min(0, 'Planned amount cannot be negative'),
  actualAmount: z.number().default(0),
  variance: z.number().default(0),
  notes: z.string().optional()
});

// Validation schema for budget period
const budgetPeriodSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ),
  status: z.enum(['planned', 'active', 'completed']).default('planned'),
  lineItems: z.array(budgetLineItemSchema).min(1, 'At least one line item is required'),
  totalPlanned: z.number().optional(),
  totalActual: z.number().optional(),
  totalVariance: z.number().optional()
});

// Validation schema for creating project budget
const createProjectBudgetSchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
  name: z.string().min(3, 'Name must be at least 3 characters long'),
  description: z.string().optional(),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ),
  status: z.enum(['draft', 'approved', 'active', 'completed', 'cancelled']).default('draft'),
  totalBudget: z.number().min(0, 'Total budget cannot be negative'),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().positive().default(1),
  fiscalYear: z.string().min(4, 'Fiscal year is required'),
  periods: z.array(budgetPeriodSchema).min(1, 'At least one period is required'),
  manager: z.string().optional(),
  department: z.string().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional()
});

/**
 * GET /api/accounting/project-budgets
 * List project budgets
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const projectId = searchParams.get('projectId');
    const fiscalYear = searchParams.get('fiscalYear');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'startDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: Record<string, unknown> = {};

    if (status) {
      query.status = status;
    }

    if (projectId) {
      query.projectId = projectId;
    }

    if (fiscalYear) {
      query.fiscalYear = fiscalYear;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: { [key: string]: 1 | -1 } = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get project budgets
    const result = await projectBudgetService.paginate(
      query,
      page,
      limit,
      sort,
      ['projectId', 'manager', 'department', 'approvedBy']
    );

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting project budgets', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/project-budgets
 * Create a project budget
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createProjectBudgetSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Process dates
    const data = {
      ...validationResult.data,
      startDate: new Date(validationResult.data.startDate),
      endDate: new Date(validationResult.data.endDate),
      periods: validationResult.data.periods.map(period => ({
        ...period,
        startDate: new Date(period.startDate),
        endDate: new Date(period.endDate)
      })),
      createdBy: user.id,
      updatedBy: user.id
    };

    // Create project budget
    const projectBudget = await projectBudgetService.createProjectBudget(data);

    return NextResponse.json({
      success: true,
      message: 'Project budget created successfully',
      data: projectBudget
    });
  } catch (error: unknown) {
    logger.error('Error creating project budget', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

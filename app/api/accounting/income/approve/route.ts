import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { incomeApprovalService } from '@/lib/services/accounting/income-approval-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for approval request
const approvalRequestSchema = z.object({
  incomeId: z.string().min(1, 'Income ID is required'),
  action: z.enum(['approve', 'reject'], {
    required_error: 'Action must be either approve or reject'
  }),
  comments: z.string().optional()
});

// Required roles for income approval
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/income/approve
 * Process income approval or rejection
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = approvalRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { incomeId, action, comments } = validationResult.data;

    // Process the approval
    const updatedIncome = await incomeApprovalService.processApproval({
      incomeId,
      action,
      comments,
      approverId: user.id
    });

    logger.info('Income approval processed', LogCategory.ACCOUNTING, {
      incomeId,
      action,
      approverId: user.id,
      newStatus: updatedIncome.status
    });

    return NextResponse.json({
      success: true,
      message: `Income ${action}d successfully`,
      income: {
        id: updatedIncome._id,
        status: updatedIncome.status,
        approvalWorkflow: updatedIncome.approvalWorkflow
      }
    });

  } catch (error: any) {
    logger.error('Error processing income approval', LogCategory.ACCOUNTING, error);
    
    return NextResponse.json({
      error: error.message || 'Failed to process approval',
      details: error.stack
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/income/approve
 * Get pending approvals for the current user
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get pending approvals
    const result = await incomeApprovalService.getPendingApprovals(user.id, page, limit);

    return NextResponse.json({
      success: true,
      ...result
    });

  } catch (error: any) {
    logger.error('Error getting pending approvals', LogCategory.ACCOUNTING, error);
    
    return NextResponse.json({
      error: error.message || 'Failed to get pending approvals'
    }, { status: 500 });
  }
}

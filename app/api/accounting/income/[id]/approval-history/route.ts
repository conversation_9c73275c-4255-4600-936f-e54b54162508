import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { incomeApprovalService } from '@/lib/services/accounting/income-approval-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Required roles for viewing approval history
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * GET /api/accounting/income/[id]/approval-history
 * Get approval history for a specific income record
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: incomeId } = await params;
    if (!incomeId) {
      return NextResponse.json({ error: 'Income ID is required' }, { status: 400 });
    }

    // Get approval history
    const approvalHistory = await incomeApprovalService.getApprovalHistory(incomeId);

    return NextResponse.json({
      success: true,
      approvalHistory
    });

  } catch (error: any) {
    logger.error('Error getting approval history', LogCategory.ACCOUNTING, error);
    
    return NextResponse.json({
      error: error.message || 'Failed to get approval history'
    }, { status: 500 });
  }
}

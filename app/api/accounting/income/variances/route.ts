import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { 
  varianceAnalysisService,
  VarianceAnalysisSession,
  VarianceAnalysisRule
} from '@/lib/services/accounting/variance-analysis-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schemas
const incomeDataSchema = z.object({
  id: z.string(),
  amount: z.number().positive(),
  date: z.string().transform(str => new Date(str)),
  description: z.string().min(1),
  source: z.string().min(1),
  category: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetId: z.string().optional()
});

const budgetDataSchema = z.object({
  id: z.string(),
  categoryId: z.string(),
  allocatedAmount: z.number().positive(),
  period: z.string()
});

const analysisRuleSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  enabled: z.boolean(),
  priority: z.number().int().positive(),
  conditions: z.object({
    varianceThreshold: z.number().min(0).max(1),
    amountThreshold: z.number().min(0),
    timeWindow: z.number().int().min(1),
    frequencyThreshold: z.number().int().min(1),
    categories: z.array(z.string()).optional(),
    sources: z.array(z.string()).optional()
  }),
  actions: z.object({
    generateAlert: z.boolean(),
    requireInvestigation: z.boolean(),
    autoEscalate: z.boolean(),
    notifyStakeholders: z.boolean(),
    severity: z.enum(['low', 'medium', 'high', 'critical']),
    tags: z.array(z.string())
  })
});

const startAnalysisSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  analysisType: z.enum(['budget_comparison', 'trend_analysis', 'seasonal_analysis', 'anomaly_detection', 'comprehensive']),
  scope: z.object({
    dateRange: z.object({
      startDate: z.string().transform(str => new Date(str)),
      endDate: z.string().transform(str => new Date(str))
    }),
    budgetIds: z.array(z.string()).optional(),
    categoryIds: z.array(z.string()).optional(),
    sources: z.array(z.string()).optional(),
    amountRange: z.object({
      min: z.number(),
      max: z.number()
    }).optional()
  }),
  configuration: z.object({
    rules: z.array(z.string()),
    thresholds: z.object({
      variancePercentage: z.number().min(0).max(1),
      minimumAmount: z.number().min(0),
      confidenceLevel: z.number().min(0).max(1)
    }),
    includeForecasting: z.boolean(),
    includeSeasonalAdjustment: z.boolean(),
    generateRecommendations: z.boolean()
  }),
  incomeData: z.array(incomeDataSchema),
  budgetData: z.array(budgetDataSchema).optional(),
  analysisRules: z.array(analysisRuleSchema).optional()
});

const updateVarianceStatusSchema = z.object({
  varianceId: z.string(),
  status: z.enum(['detected', 'investigating', 'resolved', 'ignored', 'escalated']),
  notes: z.string().optional()
});

// Required roles for variance analysis operations
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/income/variances
 * Start variance analysis session
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = startAnalysisSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const {
      name,
      description,
      analysisType,
      scope,
      configuration,
      incomeData,
      budgetData,
      analysisRules
    } = validationResult.data;

    // Start analysis session
    const session = await varianceAnalysisService.startAnalysisSession({
      name,
      description,
      analysisType,
      scope,
      configuration,
      createdBy: user.id
    });

    // Perform variance analysis
    const results = await varianceAnalysisService.performVarianceAnalysis(
      session.id,
      incomeData,
      budgetData,
      analysisRules
    );

    // Update session with results
    const completedSession: VarianceAnalysisSession = {
      ...session,
      status: 'completed',
      progress: {
        totalItems: incomeData.length,
        processedItems: incomeData.length,
        variancesDetected: results.variances.length,
        highSeverityVariances: results.variances.filter(v => v.severity === 'high' || v.severity === 'critical').length,
        recommendationsGenerated: results.variances.reduce((sum, v) => sum + v.recommendations.length, 0)
      },
      completedAt: new Date(),
      results: {
        variances: results.variances,
        summary: results.summary,
        insights: results.insights
      }
    };

    logger.info('Variance analysis session completed', LogCategory.ACCOUNTING, {
      sessionId: session.id,
      userId: user.id,
      analysisType,
      totalVariances: results.variances.length,
      highSeverityVariances: results.variances.filter(v => v.severity === 'high' || v.severity === 'critical').length,
      totalInsights: results.insights.length
    });

    return NextResponse.json({
      success: true,
      session: completedSession,
      results
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error in variance analysis', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    // Handle specific error types
    if (errorMessage.includes('Insufficient data')) {
      return NextResponse.json({
        error: 'Insufficient data for variance analysis',
        details: errorMessage
      }, { status: 400 });
    }
    
    return NextResponse.json({
      error: 'Failed to perform variance analysis',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/income/variances
 * Get variance analysis sessions, variances, or statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');
    const type = searchParams.get('type');

    if (sessionId) {
      // Get specific session
      const session = await varianceAnalysisService.getAnalysisSession(sessionId);
      
      if (!session) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        session
      });

    } else if (type === 'configuration') {
      // Return configuration and default rules
      const configuration = {
        analysisTypes: [
          {
            value: 'budget_comparison',
            label: 'Budget Comparison',
            description: 'Compare actual income against budget allocations'
          },
          {
            value: 'trend_analysis',
            label: 'Trend Analysis',
            description: 'Analyze income trends and detect deviations'
          },
          {
            value: 'seasonal_analysis',
            label: 'Seasonal Analysis',
            description: 'Compare current income with seasonal patterns'
          },
          {
            value: 'anomaly_detection',
            label: 'Anomaly Detection',
            description: 'Detect statistical anomalies in income data'
          },
          {
            value: 'comprehensive',
            label: 'Comprehensive Analysis',
            description: 'Perform all types of variance analysis'
          }
        ],
        defaultAnalysisRules: [
          {
            id: 'budget_variance_critical',
            name: 'Critical Budget Variance',
            description: 'Detects critical variances from budget allocations',
            enabled: true,
            priority: 1,
            conditions: {
              varianceThreshold: 0.25,
              amountThreshold: 5000000,
              timeWindow: 30,
              frequencyThreshold: 1
            },
            actions: {
              generateAlert: true,
              requireInvestigation: true,
              autoEscalate: true,
              notifyStakeholders: true,
              severity: 'critical',
              tags: ['budget', 'critical', 'escalate']
            }
          },
          {
            id: 'trend_variance_high',
            name: 'High Trend Variance',
            description: 'Detects significant deviations from historical trends',
            enabled: true,
            priority: 2,
            conditions: {
              varianceThreshold: 0.20,
              amountThreshold: 2000000,
              timeWindow: 60,
              frequencyThreshold: 2
            },
            actions: {
              generateAlert: true,
              requireInvestigation: true,
              autoEscalate: false,
              notifyStakeholders: true,
              severity: 'high',
              tags: ['trend', 'high', 'investigate']
            }
          }
        ],
        severityLevels: [
          { value: 'low', label: 'Low', color: '#10B981' },
          { value: 'medium', label: 'Medium', color: '#F59E0B' },
          { value: 'high', label: 'High', color: '#EF4444' },
          { value: 'critical', label: 'Critical', color: '#DC2626' }
        ],
        thresholdRanges: {
          variancePercentage: { min: 0.05, max: 0.5, step: 0.01, default: 0.15 },
          minimumAmount: { min: 100000, max: 10000000, step: 100000, default: 1000000 },
          confidenceLevel: { min: 0.8, max: 0.99, step: 0.01, default: 0.95 }
        },
        recommendationTypes: [
          { value: 'immediate', label: 'Immediate Action', timeframe: '1-3 days' },
          { value: 'short_term', label: 'Short Term', timeframe: '1-4 weeks' },
          { value: 'long_term', label: 'Long Term', timeframe: '1-6 months' }
        ]
      };

      return NextResponse.json({
        success: true,
        configuration
      });

    } else {
      // Return empty response for now - would implement session listing
      return NextResponse.json({
        success: true,
        sessions: [],
        message: 'Session listing not yet implemented'
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting variance analysis data', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get variance analysis data',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * PATCH /api/accounting/income/variances
 * Update variance status
 */
export async function PATCH(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = updateVarianceStatusSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { varianceId, status, notes } = validationResult.data;

    // Update variance status
    const updatedVariance = await varianceAnalysisService.updateVarianceStatus(
      varianceId,
      status,
      user.id,
      notes
    );

    logger.info('Variance status updated', LogCategory.ACCOUNTING, {
      varianceId,
      status,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      variance: updatedVariance
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error updating variance status', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to update variance status',
      details: errorMessage
    }, { status: 500 });
  }
}

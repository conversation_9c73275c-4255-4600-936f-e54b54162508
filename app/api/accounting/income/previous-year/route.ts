// app/api/accounting/income/previous-year/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear') || '2024-2025';
    const budgetId = searchParams.get('budgetId');

    // Mock data for previous year income
    const mockData = {
      fiscalYear,
      totalIncome: **********,
      incomeData: [
        { source: 'government_subvention', value: **********, description: 'Government Subvention' },
        { source: 'registration_fees', value: **********, description: 'Registration Fees' },
        { source: 'licensing_fees', value: *********, description: 'Licensing Fees' },
        { source: 'donations', value: *********, description: 'Donations and Grants' },
        { source: 'other', value: *********, description: 'Other Income' }
      ],
      monthlyData: [
        { month: 'Jul', income: 433333333 },
        { month: 'Aug', income: 433333333 },
        { month: 'Sep', income: 433333333 },
        { month: 'Oct', income: 433333333 },
        { month: 'Nov', income: 433333333 },
        { month: 'Dec', income: 433333333 },
        { month: 'Jan', income: 433333333 },
        { month: 'Feb', income: 433333333 },
        { month: 'Mar', income: 433333333 },
        { month: 'Apr', income: 433333333 },
        { month: 'May', income: 433333333 },
        { month: 'Jun', income: 433333333 }
      ],
      quarterlyData: [
        { quarter: 'Q1', income: 1300000000 },
        { quarter: 'Q2', income: 1300000000 },
        { quarter: 'Q3', income: 1300000000 },
        { quarter: 'Q4', income: 1300000000 }
      ]
    };

    return NextResponse.json(mockData);
  } catch (error) {
    console.error('Error fetching previous year income:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching previous year income' },
      { status: 500 }
    );
  }
}

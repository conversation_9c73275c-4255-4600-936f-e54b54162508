import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { 
  duplicateDetectionService,
  DuplicateDetectionSession,
  DuplicateTransaction,
  DuplicateDetectionRule
} from '@/lib/services/accounting/duplicate-detection-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schemas
const duplicateTransactionSchema = z.object({
  id: z.string(),
  incomeId: z.string(),
  amount: z.number().positive(),
  date: z.string().transform(str => new Date(str)),
  description: z.string().min(1),
  reference: z.string().optional(),
  source: z.string().min(1),
  category: z.string().optional(),
  budgetCategory: z.string().optional(),
  createdAt: z.string().transform(str => new Date(str)),
  createdBy: z.string(),
  metadata: z.record(z.unknown()).optional()
});

const detectionRuleSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  enabled: z.boolean(),
  priority: z.number().int().positive(),
  criteria: z.object({
    amountTolerance: z.number().min(0).max(1),
    dateTolerance: z.number().int().min(0),
    descriptionSimilarity: z.number().min(0).max(1),
    referenceMatch: z.boolean(),
    sourceMatch: z.boolean(),
    categoryMatch: z.boolean(),
    customFields: z.array(z.object({
      field: z.string(),
      weight: z.number().min(0).max(1),
      tolerance: z.number().optional()
    })).optional()
  }),
  actions: z.object({
    autoMerge: z.boolean(),
    requireManualReview: z.boolean(),
    confidence: z.number().min(0).max(1),
    notifyUsers: z.boolean(),
    tags: z.array(z.string())
  })
});

const startDetectionSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  dateRange: z.object({
    startDate: z.string().transform(str => new Date(str)),
    endDate: z.string().transform(str => new Date(str))
  }),
  filters: z.object({
    sources: z.array(z.string()).optional(),
    categories: z.array(z.string()).optional(),
    amountRange: z.object({
      min: z.number(),
      max: z.number()
    }).optional(),
    budgetIds: z.array(z.string()).optional()
  }),
  rules: z.array(z.string()),
  transactions: z.array(duplicateTransactionSchema),
  detectionRules: z.array(detectionRuleSchema).optional()
});

const resolveDuplicateSchema = z.object({
  groupId: z.string(),
  action: z.enum(['merge', 'ignore', 'split']),
  mergeStrategy: z.enum(['keep_first', 'keep_latest', 'keep_highest_amount', 'manual']).optional(),
  transactionsToRemove: z.array(z.string()).optional(),
  reason: z.string().optional(),
  notes: z.string().optional()
});

const bulkResolveSchema = z.object({
  groupIds: z.array(z.string()),
  action: z.enum(['merge', 'ignore']),
  mergeStrategy: z.enum(['keep_first', 'keep_latest', 'keep_highest_amount', 'manual']).optional(),
  reason: z.string().optional(),
  notes: z.string().optional()
});

// Required roles for duplicate detection operations
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/income/duplicates
 * Start duplicate detection session or resolve duplicates
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    if (action === 'resolve') {
      // Resolve single duplicate group
      const validationResult = resolveDuplicateSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationResult.error.errors
        }, { status: 400 });
      }

      const { groupId, action: resolveAction, mergeStrategy, transactionsToRemove, reason, notes } = validationResult.data;

      let updatedGroup;
      switch (resolveAction) {
        case 'merge':
          updatedGroup = await duplicateDetectionService.mergeDuplicateGroup(
            groupId,
            mergeStrategy,
            user.id,
            notes
          );
          break;
        case 'ignore':
          updatedGroup = await duplicateDetectionService.ignoreDuplicateGroup(
            groupId,
            reason || 'Manual ignore',
            user.id,
            notes
          );
          break;
        case 'split':
          if (!transactionsToRemove || transactionsToRemove.length === 0) {
            return NextResponse.json({
              error: 'Transactions to remove are required for split action'
            }, { status: 400 });
          }
          updatedGroup = await duplicateDetectionService.splitDuplicateGroup(
            groupId,
            transactionsToRemove,
            user.id,
            notes
          );
          break;
      }

      logger.info('Duplicate group resolved', LogCategory.ACCOUNTING, {
        groupId,
        action: resolveAction,
        userId: user.id
      });

      return NextResponse.json({
        success: true,
        group: updatedGroup
      });

    } else if (action === 'bulk-resolve') {
      // Bulk resolve duplicate groups
      const validationResult = bulkResolveSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationResult.error.errors
        }, { status: 400 });
      }

      const { groupIds, action: bulkAction, mergeStrategy, reason, notes } = validationResult.data;

      const results = await duplicateDetectionService.bulkResolveDuplicateGroups(
        groupIds,
        bulkAction,
        user.id,
        { mergeStrategy, reason, notes }
      );

      logger.info('Bulk duplicate resolution completed', LogCategory.ACCOUNTING, {
        totalGroups: groupIds.length,
        resolved: results.resolved,
        failed: results.failed,
        userId: user.id
      });

      return NextResponse.json({
        success: true,
        results
      });

    } else {
      // Start detection session
      const validationResult = startDetectionSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationResult.error.errors
        }, { status: 400 });
      }

      const {
        name,
        description,
        dateRange,
        filters,
        rules,
        transactions,
        detectionRules
      } = validationResult.data;

      // Start detection session
      const session = await duplicateDetectionService.startDetectionSession({
        name,
        description,
        dateRange,
        filters,
        rules,
        createdBy: user.id
      });

      // Perform duplicate detection
      const results = await duplicateDetectionService.detectDuplicates(
        session.id,
        transactions,
        detectionRules
      );

      // Update session with results
      const completedSession: DuplicateDetectionSession = {
        ...session,
        status: 'completed',
        progress: {
          totalTransactions: transactions.length,
          processedTransactions: transactions.length,
          duplicateGroups: results.duplicateGroups.length,
          autoMerged: results.statistics.autoMerged,
          requiresReview: results.statistics.manualReview
        },
        completedAt: new Date(),
        results: {
          duplicateGroups: results.duplicateGroups,
          statistics: results.statistics
        }
      };

      logger.info('Duplicate detection session completed', LogCategory.ACCOUNTING, {
        sessionId: session.id,
        userId: user.id,
        totalTransactions: transactions.length,
        duplicateGroups: results.duplicateGroups.length,
        autoMerged: results.statistics.autoMerged,
        processingTime: results.statistics.processingTime
      });

      return NextResponse.json({
        success: true,
        session: completedSession,
        results
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error in duplicate detection', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    return NextResponse.json({
      error: 'Failed to process duplicate detection request',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/income/duplicates
 * Get duplicate detection sessions, groups, or statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');
    const type = searchParams.get('type');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (sessionId) {
      // Get specific session
      const session = await duplicateDetectionService.getDetectionSession(sessionId);
      
      if (!session) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        session
      });

    } else if (type === 'groups') {
      // Get duplicate groups for review
      const status = searchParams.get('status')?.split(',') as Array<'detected' | 'reviewing' | 'merged' | 'ignored' | 'resolved'> | undefined;
      const sources = searchParams.get('sources')?.split(',');
      const categories = searchParams.get('categories')?.split(',');
      const minConfidence = searchParams.get('minConfidence') ? parseFloat(searchParams.get('minConfidence')!) : undefined;
      const maxConfidence = searchParams.get('maxConfidence') ? parseFloat(searchParams.get('maxConfidence')!) : undefined;
      const minAmount = searchParams.get('minAmount') ? parseFloat(searchParams.get('minAmount')!) : undefined;
      const maxAmount = searchParams.get('maxAmount') ? parseFloat(searchParams.get('maxAmount')!) : undefined;

      const filters = {
        status,
        sources,
        categories,
        confidenceRange: minConfidence !== undefined || maxConfidence !== undefined ? {
          min: minConfidence || 0,
          max: maxConfidence || 1
        } : undefined,
        amountRange: minAmount !== undefined || maxAmount !== undefined ? {
          min: minAmount || 0,
          max: maxAmount || Number.MAX_SAFE_INTEGER
        } : undefined,
        dateRange: startDate && endDate ? {
          startDate: new Date(startDate),
          endDate: new Date(endDate)
        } : undefined
      };

      const groups = await duplicateDetectionService.getDuplicateGroupsForReview(filters);

      return NextResponse.json({
        success: true,
        groups
      });

    } else if (type === 'statistics' && startDate && endDate) {
      // Get detection statistics
      const sources = searchParams.get('sources')?.split(',');
      const categories = searchParams.get('categories')?.split(',');

      const statistics = await duplicateDetectionService.getDetectionStatistics(
        { startDate: new Date(startDate), endDate: new Date(endDate) },
        { sources, categories }
      );

      return NextResponse.json({
        success: true,
        statistics
      });

    } else {
      // Return configuration and default rules
      const configuration = {
        defaultDetectionRules: [
          {
            id: 'exact_duplicate',
            name: 'Exact Duplicate',
            description: 'Transactions with identical amount, date, and reference',
            enabled: true,
            priority: 1,
            criteria: {
              amountTolerance: 0,
              dateTolerance: 0,
              descriptionSimilarity: 1.0,
              referenceMatch: true,
              sourceMatch: false,
              categoryMatch: false
            },
            actions: {
              autoMerge: true,
              requireManualReview: false,
              confidence: 1.0,
              notifyUsers: true,
              tags: ['exact_duplicate', 'auto_merge']
            }
          },
          {
            id: 'near_duplicate',
            name: 'Near Duplicate',
            description: 'Transactions with very similar characteristics',
            enabled: true,
            priority: 2,
            criteria: {
              amountTolerance: 0.01,
              dateTolerance: 1,
              descriptionSimilarity: 0.9,
              referenceMatch: false,
              sourceMatch: true,
              categoryMatch: true
            },
            actions: {
              autoMerge: false,
              requireManualReview: true,
              confidence: 0.9,
              notifyUsers: true,
              tags: ['near_duplicate', 'manual_review']
            }
          }
        ],
        mergeStrategies: [
          { value: 'keep_first', label: 'Keep First Transaction' },
          { value: 'keep_latest', label: 'Keep Latest Transaction' },
          { value: 'keep_highest_amount', label: 'Keep Highest Amount' },
          { value: 'manual', label: 'Manual Selection' }
        ],
        confidenceLevels: [
          { value: 0.5, label: '50%' },
          { value: 0.75, label: '75%' },
          { value: 0.9, label: '90%' },
          { value: 0.95, label: '95%' },
          { value: 1.0, label: '100%' }
        ],
        toleranceRanges: {
          amount: { min: 0, max: 0.2, step: 0.01 },
          date: { min: 0, max: 30, step: 1 },
          description: { min: 0.5, max: 1.0, step: 0.05 }
        }
      };

      return NextResponse.json({
        success: true,
        configuration
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting duplicate detection data', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get duplicate detection data',
      details: errorMessage
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { advancedForecastingService, ForecastOptions } from '@/lib/services/accounting/advanced-forecasting-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for forecast request
const forecastRequestSchema = z.object({
  fiscalYear: z.string().min(1, 'Fiscal year is required'),
  budgetId: z.string().optional(),
  categoryId: z.string().optional(),
  options: z.object({
    forecastHorizon: z.number().min(1).max(24).default(6),
    confidenceLevel: z.number().min(0.8).max(0.99).default(0.95),
    includeSeasonality: z.boolean().default(true),
    detectAnomalies: z.boolean().default(true),
    anomalyThreshold: z.number().min(1).max(5).default(2.5),
    includeScenarios: z.boolean().default(true),
    modelType: z.enum(['auto', 'linear', 'exponential', 'seasonal']).default('auto'),
    minDataPoints: z.number().min(3).max(24).default(6)
  }).optional()
});

// Required roles for income forecasting
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/income/forecast
 * Generate advanced income forecast with multiple scenarios and analysis
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = forecastRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { fiscalYear, budgetId, categoryId, options } = validationResult.data;

    // Generate advanced forecast
    const forecastResult = await advancedForecastingService.generateIncomeForecast(
      fiscalYear,
      budgetId,
      categoryId,
      options as Partial<ForecastOptions>
    );

    logger.info('Advanced income forecast generated', LogCategory.ACCOUNTING, {
      fiscalYear,
      budgetId,
      categoryId,
      userId: user.id,
      dataPoints: forecastResult.metadata.dataPoints,
      forecastHorizon: forecastResult.metadata.forecastHorizon,
      modelType: forecastResult.model.type,
      modelAccuracy: forecastResult.model.accuracy.r2
    });

    return NextResponse.json({
      success: true,
      forecast: forecastResult
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error generating income forecast', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    // Handle specific error types
    if (errorMessage.includes('Insufficient data')) {
      return NextResponse.json({
        error: 'Insufficient historical data for forecasting',
        details: errorMessage
      }, { status: 400 });
    }
    
    if (errorMessage.includes('not found')) {
      return NextResponse.json({
        error: 'Budget or category not found',
        details: errorMessage
      }, { status: 404 });
    }
    
    return NextResponse.json({
      error: 'Failed to generate forecast',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/income/forecast
 * Get forecast configuration options and available models
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Return configuration options
    const configuration = {
      availableModels: [
        {
          type: 'auto',
          name: 'Automatic Model Selection',
          description: 'Automatically selects the best model based on data characteristics'
        },
        {
          type: 'linear',
          name: 'Linear Regression',
          description: 'Simple linear trend forecasting'
        },
        {
          type: 'exponential',
          name: 'Exponential Growth',
          description: 'Exponential growth/decay forecasting'
        },
        {
          type: 'seasonal',
          name: 'Seasonal Decomposition',
          description: 'Seasonal pattern-based forecasting'
        }
      ],
      defaultOptions: {
        forecastHorizon: 6,
        confidenceLevel: 0.95,
        includeSeasonality: true,
        detectAnomalies: true,
        anomalyThreshold: 2.5,
        includeScenarios: true,
        modelType: 'auto',
        minDataPoints: 6
      },
      confidenceLevels: [
        { value: 0.90, label: '90%' },
        { value: 0.95, label: '95%' },
        { value: 0.99, label: '99%' }
      ],
      forecastHorizons: [
        { value: 3, label: '3 months' },
        { value: 6, label: '6 months' },
        { value: 12, label: '12 months' },
        { value: 18, label: '18 months' },
        { value: 24, label: '24 months' }
      ],
      scenarios: [
        {
          type: 'optimistic',
          name: 'Optimistic',
          description: 'Favorable economic conditions (+15%)',
          adjustmentFactor: 1.15
        },
        {
          type: 'realistic',
          name: 'Realistic',
          description: 'Current trends and patterns (baseline)',
          adjustmentFactor: 1.0
        },
        {
          type: 'pessimistic',
          name: 'Pessimistic',
          description: 'Economic downturns and challenges (-20%)',
          adjustmentFactor: 0.8
        }
      ]
    };

    return NextResponse.json({
      success: true,
      configuration
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting forecast configuration', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get forecast configuration',
      details: errorMessage
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { 
  incomeReconciliationService,
  ReconciliationSession,
  ReconciliationTransaction,
  MatchingRule
} from '@/lib/services/accounting/income-reconciliation-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schemas
const reconciliationTransactionSchema = z.object({
  id: z.string(),
  source: z.enum(['internal', 'bank', 'government', 'external']),
  amount: z.number().positive(),
  date: z.string().transform(str => new Date(str)),
  description: z.string().min(1),
  reference: z.string().optional(),
  accountNumber: z.string().optional(),
  payerName: z.string().optional(),
  category: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
  originalData: z.record(z.unknown()).optional()
});

const matchingRuleSchema = z.object({
  id: z.string(),
  name: z.string(),
  priority: z.number().int().positive(),
  enabled: z.boolean(),
  conditions: z.object({
    amountTolerance: z.number().min(0).max(1).optional(),
    dateTolerance: z.number().int().min(0).optional(),
    descriptionSimilarity: z.number().min(0).max(1).optional(),
    requireExactReference: z.boolean().optional(),
    requireExactAmount: z.boolean().optional(),
    customFields: z.array(z.object({
      field: z.string(),
      operator: z.enum(['equals', 'contains', 'startsWith', 'endsWith', 'regex']),
      value: z.string(),
      caseSensitive: z.boolean().optional()
    })).optional()
  }),
  actions: z.object({
    autoMatch: z.boolean().optional(),
    requireManualReview: z.boolean().optional(),
    confidence: z.number().min(0).max(1).optional(),
    tags: z.array(z.string()).optional()
  })
});

const startReconciliationSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  source: z.string().min(1),
  dateRange: z.object({
    startDate: z.string().transform(str => new Date(str)),
    endDate: z.string().transform(str => new Date(str))
  }),
  settings: z.object({
    matchingRules: z.array(z.string()),
    autoApproveThreshold: z.number().min(0).max(1),
    requireManualReview: z.boolean(),
    enableDuplicateDetection: z.boolean()
  }),
  internalTransactions: z.array(reconciliationTransactionSchema),
  externalTransactions: z.array(reconciliationTransactionSchema),
  matchingRules: z.array(matchingRuleSchema).optional()
});

const updateMatchStatusSchema = z.object({
  matchId: z.string(),
  status: z.enum(['pending', 'approved', 'rejected', 'auto-matched']),
  notes: z.string().optional()
});

// Required roles for reconciliation operations
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/income/reconciliation
 * Start a new reconciliation session and perform matching
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = startReconciliationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const {
      name,
      description,
      source,
      dateRange,
      settings,
      internalTransactions,
      externalTransactions,
      matchingRules
    } = validationResult.data;

    // Start reconciliation session
    const session = await incomeReconciliationService.startReconciliationSession({
      name,
      description,
      source,
      dateRange,
      settings,
      createdBy: user.id
    });

    // Perform reconciliation
    const results = await incomeReconciliationService.performReconciliation(
      session.id,
      internalTransactions,
      externalTransactions,
      matchingRules
    );

    // Update session with results
    const completedSession: ReconciliationSession = {
      ...session,
      status: 'completed',
      statistics: results.statistics,
      completedAt: new Date(),
      results: {
        matches: results.matches,
        variances: results.variances,
        summary: {
          totalMatches: results.matches.length,
          totalVariances: results.variances.length,
          autoMatched: results.matches.filter(m => m.status === 'auto-matched').length,
          manualReview: results.matches.filter(m => m.status === 'pending').length,
          confidence: results.statistics.confidence
        }
      }
    };

    logger.info('Reconciliation session completed', LogCategory.ACCOUNTING, {
      sessionId: session.id,
      userId: user.id,
      source,
      totalMatches: results.matches.length,
      totalVariances: results.variances.length,
      autoMatched: results.statistics.autoMatched,
      confidence: results.statistics.confidence
    });

    return NextResponse.json({
      success: true,
      session: completedSession,
      results
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error in reconciliation', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    // Handle specific error types
    if (errorMessage.includes('Insufficient data')) {
      return NextResponse.json({
        error: 'Insufficient data for reconciliation',
        details: errorMessage
      }, { status: 400 });
    }
    
    return NextResponse.json({
      error: 'Failed to perform reconciliation',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/income/reconciliation
 * Get reconciliation sessions and statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const source = searchParams.get('source');

    if (sessionId) {
      // Get specific session
      const session = await incomeReconciliationService.getReconciliationSession(sessionId);
      
      if (!session) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        session
      });
    } else if (startDate && endDate) {
      // Get statistics for date range
      const statistics = await incomeReconciliationService.getReconciliationStatistics(
        new Date(startDate),
        new Date(endDate),
        source || undefined
      );

      return NextResponse.json({
        success: true,
        statistics
      });
    } else {
      // Return configuration and default rules
      const configuration = {
        defaultMatchingRules: [
          {
            id: 'exact_match',
            name: 'Exact Match',
            description: 'Matches transactions with identical amount, date, and reference',
            priority: 1,
            enabled: true,
            conditions: {
              amountTolerance: 0,
              dateTolerance: 0,
              requireExactReference: true,
              requireExactAmount: true
            },
            actions: {
              autoMatch: true,
              confidence: 1.0
            }
          },
          {
            id: 'amount_date_match',
            name: 'Amount and Date Match',
            description: 'Matches transactions with same amount and similar dates',
            priority: 2,
            enabled: true,
            conditions: {
              amountTolerance: 0.01,
              dateTolerance: 1,
              requireExactAmount: false
            },
            actions: {
              autoMatch: true,
              confidence: 0.95
            }
          },
          {
            id: 'fuzzy_description_match',
            name: 'Fuzzy Description Match',
            description: 'Matches transactions with similar descriptions',
            priority: 3,
            enabled: true,
            conditions: {
              amountTolerance: 0.05,
              dateTolerance: 3,
              descriptionSimilarity: 0.8
            },
            actions: {
              autoMatch: false,
              requireManualReview: true,
              confidence: 0.75
            }
          }
        ],
        supportedSources: ['internal', 'bank', 'government', 'external'],
        confidenceLevels: [
          { value: 0.5, label: '50%' },
          { value: 0.75, label: '75%' },
          { value: 0.9, label: '90%' },
          { value: 0.95, label: '95%' },
          { value: 1.0, label: '100%' }
        ],
        toleranceRanges: {
          amount: { min: 0, max: 0.2, step: 0.01 },
          date: { min: 0, max: 30, step: 1 },
          description: { min: 0.5, max: 1.0, step: 0.05 }
        }
      };

      return NextResponse.json({
        success: true,
        configuration
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting reconciliation data', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get reconciliation data',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * PATCH /api/accounting/income/reconciliation
 * Update match status or resolve variances
 */
export async function PATCH(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = updateMatchStatusSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { matchId, status, notes } = validationResult.data;

    // Update match status
    const updatedMatch = await incomeReconciliationService.updateMatchStatus(
      matchId,
      status,
      user.id,
      notes
    );

    logger.info('Match status updated', LogCategory.ACCOUNTING, {
      matchId,
      status,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      match: updatedMatch
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error updating match status', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to update match status',
      details: errorMessage
    }, { status: 500 });
  }
}

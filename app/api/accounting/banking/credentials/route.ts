import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// import { authOptions } from '@/lib/auth';
import { bankingIntegrationService } from '@/lib/services/banking-integration/banking-integration-service';
import { logger } from '@/lib/utils/logger';

/**
 * POST /api/accounting/banking/credentials
 * Create new banking credentials
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { provider, authMethod, clientId, clientSecret, apiKey, username, password, certificatePath, tokenEndpoint } = body;

    // Validate required fields
    if (!provider || !authMethod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate auth method specific fields
    switch (authMethod) {
      case 'oauth2':
        if (!clientId || !clientSecret || !tokenEndpoint) {
          return NextResponse.json(
            { error: 'Missing required fields for OAuth2 authentication' },
            { status: 400 }
          );
        }
        break;
      case 'api_key':
        if (!apiKey) {
          return NextResponse.json(
            { error: 'Missing API key' },
            { status: 400 }
          );
        }
        break;
      case 'basic_auth':
        if (!username || !password) {
          return NextResponse.json(
            { error: 'Missing username or password' },
            { status: 400 }
          );
        }
        break;
      case 'certificate':
        if (!certificatePath) {
          return NextResponse.json(
            { error: 'Missing certificate path' },
            { status: 400 }
          );
        }
        break;
    }

    // Create credentials
    const credentials = await bankingIntegrationService.createCredentials({
      provider,
      authMethod,
      clientId,
      clientSecret,
      apiKey,
      username,
      password,
      certificatePath,
      tokenEndpoint,
    });

    return NextResponse.json({
      success: true,
      credentials: {
        id: credentials.id,
        provider: credentials.provider,
        authMethod: credentials.authMethod,
        status: credentials.status,
        createdAt: credentials.createdAt,
      },
    });
  } catch (error: unknown) {
    logger.error('Error creating banking credentials:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

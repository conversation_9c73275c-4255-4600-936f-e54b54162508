// app/api/accounting/banking/accounts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET handler for bank accounts
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const accountNumber = searchParams.get('accountNumber');
    const accountName = searchParams.get('accountName');
    const bankName = searchParams.get('bankName');
    const currency = searchParams.get('currency');
    const accountType = searchParams.get('accountType');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // In a real implementation, this would query the database
    // For now, return mock data
    const mockBankAccounts = [
      {
        id: "1",
        accountName: "TCM Operations Account",
        accountNumber: "**********",
        bankName: "National Bank of Malawi",
        branchName: "Lilongwe Main Branch",
        branchCode: "001",
        swiftCode: "NBMAMWLI",
        currency: "MWK",
        openingBalance: 5000000,
        currentBalance: 5500000,
        availableBalance: 5300000,
        minimumBalance: 1000000,
        status: "active",
        accountType: "checking",
        openedDate: "2024-01-01T00:00:00Z",
        lastReconciliationDate: "2024-01-15T00:00:00Z",
        description: "Main operations account for day-to-day expenses",
        contactPerson: "John Banda",
        contactEmail: "<EMAIL>",
        contactPhone: "+265 999 123 456",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-15T00:00:00Z"
      },
      {
        id: "2",
        accountName: "TCM Payroll Account",
        accountNumber: "**********",
        bankName: "Standard Bank Malawi",
        branchName: "Blantyre Branch",
        branchCode: "002",
        swiftCode: "SBICMWMX",
        currency: "MWK",
        openingBalance: 2500000,
        currentBalance: 2750000,
        availableBalance: 2750000,
        status: "active",
        accountType: "current",
        openedDate: "2024-01-01T00:00:00Z",
        lastReconciliationDate: "2024-01-15T00:00:00Z",
        description: "Account for staff salaries and benefits",
        contactPerson: "Mary Phiri",
        contactEmail: "<EMAIL>",
        contactPhone: "+265 888 234 567",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-15T00:00:00Z"
      }
    ];

    // Log the request
    logger.info('Bank accounts retrieved', LogCategory.API, {
      userId: user.id,
      query: {
        accountId,
        accountNumber,
        accountName,
        bankName,
        currency,
        accountType,
        status,
        page,
        limit
      }
    });

    return NextResponse.json({
      success: true,
      data: mockBankAccounts,
      pagination: {
        page,
        limit,
        total: mockBankAccounts.length,
        totalPages: Math.ceil(mockBankAccounts.length / limit)
      }
    });
  } catch (error) {
    console.error('Error retrieving bank accounts:', error);
    logger.error('Error retrieving bank accounts', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new bank account
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();

    // Validate required fields
    const requiredFields = [
      'accountNumber',
      'accountName',
      'bankName',
      'branchName',
      'accountType',
      'currency',
      'openingBalance',
      'openedDate'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // In a real implementation, this would save to the database
    // For now, just return success with the data
    const newBankAccount = {
      id: `ACC-${Math.floor(1000 + Math.random() * 9000)}`,
      ...data,
      status: data.status || 'active',
      currentBalance: data.currentBalance || data.openingBalance,
      availableBalance: data.availableBalance || data.openingBalance,
      createdAt: new Date().toISOString(),
      createdBy: user.id || 'unknown',
    };

    // Log the creation
    logger.info('Bank account created', LogCategory.API, {
      userId: user.id,
      accountId: newBankAccount.id,
      accountName: data.accountName
    });

    return NextResponse.json({
      success: true,
      message: 'Bank account created successfully',
      data: newBankAccount
    });
  } catch (error) {
    console.error('Error creating bank account:', error);
    logger.error('Error creating bank account', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a bank account
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PUT(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json(
        { error: 'Bank account ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    const updatedBankAccount = {
      ...data,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id || 'unknown',
    };

    // Log the update
    logger.info('Bank account updated', LogCategory.API, {
      userId: user.id,
      accountId: id,
      accountName: data.accountName
    });

    return NextResponse.json({
      success: true,
      message: 'Bank account updated successfully',
      data: updatedBankAccount
    });
  } catch (error) {
    console.error('Error updating bank account:', error);
    logger.error('Error updating bank account', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

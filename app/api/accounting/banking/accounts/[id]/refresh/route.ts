// app/api/accounting/banking/accounts/[id]/refresh/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/accounting/banking/accounts/[id]/refresh
 * Refresh a bank account
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare accountId at function scope
  let accountId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    accountId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Refresh account
    const result = await bankingIntegrationService.refreshAccount(accountId);

    return NextResponse.json(result);
  } catch (error) {
    logger.error(`Error refreshing bank account: ${accountId}`, LogCategory.BANKING, error);
    return NextResponse.json(
      {
        success: false,
        message: `Failed to refresh account: ${error instanceof Error ? error.message : 'Unknown error'}`
      },
      { status: 500 }
    );
  }
}

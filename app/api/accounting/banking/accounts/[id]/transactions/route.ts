// app/api/accounting/banking/accounts/[id]/transactions/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/accounts/[id]/transactions
 * Get transactions for a bank account
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;

    // Get transactions
    const transactions = await bankingIntegrationService.getTransactions(id, startDate, endDate);

    return NextResponse.json(transactions);
  } catch (error) {
    logger.error(`Error getting bank account transactions`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank account transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/banking/accounts/[id]/transactions
 * Sync transactions for a bank account
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();
    const startDate = body.startDate ? new Date(body.startDate) : undefined;
    const endDate = body.endDate ? new Date(body.endDate) : undefined;

    // Sync transactions
    const transactions = await bankingIntegrationService.syncTransactions(id, startDate, endDate);

    return NextResponse.json({
      success: true,
      message: `Successfully synced ${transactions.length} transactions`,
      transactions
    });
  } catch (error) {
    logger.error(`Error syncing bank account transactions`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to sync bank account transactions' },
      { status: 500 }
    );
  }
}

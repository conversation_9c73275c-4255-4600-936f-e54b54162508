// app/api/accounting/banking/accounts/[id]/balance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/accounts/[id]/balance
 * Get balance for a bank account
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare accountId at function scope with default value
  let accountId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    accountId = id;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get balance
    const balance = await bankingIntegrationService.getAccountBalance(accountId);

    if (!balance) {
      return NextResponse.json(
        { error: 'Bank account not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(balance);
  } catch (error) {
    logger.error(`Error getting bank account balance: ${accountId}`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank account balance' },
      { status: 500 }
    );
  }
}

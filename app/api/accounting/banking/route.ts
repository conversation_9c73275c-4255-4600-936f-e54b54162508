// app/api/accounting/banking/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    // Mock data for bank accounts
    const bankAccounts = [
      {
        id: 1,
        accountNumber: '**********',
        accountName: 'TCM Main Operating Account',
        bankName: 'National Bank of Malawi',
        branchName: 'Lilongwe Main Branch',
        accountType: 'current',
        currency: 'MWK',
        currentBalance: ********,
        availableBalance: ********,
        status: 'active',
        openedDate: '2020-01-15',
        lastReconciliationDate: '2025-06-30',
      },
      {
        id: 2,
        accountNumber: '**********',
        accountName: 'TCM Payroll Account',
        bankName: 'Standard Bank Malawi',
        branchName: 'Capital City Branch',
        accountType: 'current',
        currency: 'MWK',
        currentBalance: 5000000,
        availableBalance: 5000000,
        status: 'active',
        openedDate: '2020-02-20',
        lastReconciliationDate: '2025-06-30',
      },
      {
        id: 3,
        accountNumber: '**********',
        accountName: 'TCM Reserve Fund',
        bankName: 'FDH Bank',
        branchName: 'Blantyre Branch',
        accountType: 'savings',
        currency: 'MWK',
        currentBalance: ********,
        availableBalance: ********,
        status: 'active',
        openedDate: '2021-05-10',
        lastReconciliationDate: '2025-06-30',
      },
      {
        id: 4,
        accountNumber: 'USD12345678',
        accountName: 'TCM Foreign Currency Account',
        bankName: 'National Bank of Malawi',
        branchName: 'Lilongwe Main Branch',
        accountType: 'current',
        currency: 'USD',
        currentBalance: 50000,
        availableBalance: 50000,
        status: 'active',
        openedDate: '2022-03-15',
        lastReconciliationDate: '2025-06-30',
      },
      {
        id: 5,
        accountNumber: 'FD12345678',
        accountName: 'TCM Fixed Deposit',
        bankName: 'Standard Bank Malawi',
        branchName: 'Capital City Branch',
        accountType: 'fixed_deposit',
        currency: 'MWK',
        currentBalance: ********,
        availableBalance: 0,
        status: 'active',
        openedDate: '2023-01-10',
        lastReconciliationDate: null,
      },
    ];

    // Filter bank accounts based on query parameters
    let filteredAccounts = [...bankAccounts];

    if (accountId) {
      filteredAccounts = filteredAccounts.filter(account => account.id.toString() === accountId);

      if (filteredAccounts.length === 0) {
        return NextResponse.json(
          { error: 'Bank account not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(filteredAccounts[0]);
    }

    if (status) {
      filteredAccounts = filteredAccounts.filter(account => account.status === status);
    }

    if (type) {
      filteredAccounts = filteredAccounts.filter(account => account.accountType === type);
    }

    // Calculate totals by currency
    const totals = filteredAccounts.reduce((acc, account) => {
      if (!acc[account.currency]) {
        acc[account.currency] = {
          currency: account.currency,
          currentBalance: 0,
          availableBalance: 0,
        };
      }

      acc[account.currency].currentBalance += account.currentBalance;
      acc[account.currency].availableBalance += account.availableBalance;

      return acc;
    }, {} as Record<string, { currency: string; currentBalance: number; availableBalance: number }>);

    return NextResponse.json({
      accounts: filteredAccounts,
      total: filteredAccounts.length,
      totals: Object.values(totals),
    });
  } catch (error) {
    console.error('Error fetching bank accounts:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // In a real implementation, this would save to the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Bank account created successfully',
      data: {
        id: 6, // Mock ID
        ...data,
        createdAt: new Date().toISOString(),
        createdBy: user.id || 'Unknown User',
      }
    });
  } catch (error) {
    console.error('Error creating bank account:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json(
        { error: 'Bank account ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Bank account updated successfully',
      data: {
        ...data,
        updatedAt: new Date().toISOString(),
        updatedBy: user.id || 'Unknown User',
      }
    });
  } catch (error) {
    console.error('Error updating bank account:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

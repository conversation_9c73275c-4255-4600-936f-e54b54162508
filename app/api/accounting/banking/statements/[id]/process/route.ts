// app/api/accounting/banking/statements/[id]/process/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { statementProcessingService } from '@/lib/services/banking/statement-processing-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * POST handler for processing a bank statement
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare statementId at function scope with a default value
  let statementId = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    statementId = id;
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { createTransactions, updateBalances, categorizeTransactions, matchExistingTransactions } = data;

    // Process statement
    const result = await statementProcessingService.processStatement(
      statementId,
      {
        createTransactions,
        updateBalances,
        categorizeTransactions,
        matchExistingTransactions
      },
      user.id
    );

    // Log the processing
    logger.info(`Statement processed: ${statementId}`, LogCategory.API, {
      userId: user.id,
      statementId: statementId,
      transactionsCreated: result.transactionsCreated,
      transactionsMatched: result.transactionsMatched,
      transactionsCategorized: result.transactionsCategorized,
      balanceUpdated: result.balanceUpdated
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      transactionsCreated: result.transactionsCreated,
      transactionsMatched: result.transactionsMatched,
      transactionsCategorized: result.transactionsCategorized,
      balanceUpdated: result.balanceUpdated
    });
  } catch (error: unknown) {
    logger.error(`Error processing statement: ${statementId}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to process statement',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

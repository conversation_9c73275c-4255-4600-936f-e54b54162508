import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { statementProcessingService } from '@/lib/services/banking/statement-processing-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * GET handler for getting bank statements
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortField = searchParams.get('sortField') || 'importDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Get statements
    const result = await statementProcessingService.getStatementsByAccount(
      accountId,
      {
        page,
        limit,
        sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
      }
    );

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error: unknown) {
    logger.error('Error getting bank statements', LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to get bank statements',
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

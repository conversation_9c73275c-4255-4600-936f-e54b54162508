// /api/accounting/banking/transactions
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Define a more specific user interface for our needs
interface User {
  _id: mongoose.Types.ObjectId;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
}

export async function GET(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const user = await getCurrentUser(req);

    if (!user) {
      logger.warn('Unauthorized access attempt to banking transactions', LogCategory.API, {
        path: req.nextUrl.pathname
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cast user to User type
    const typedUser = user as User;

    logger.info('Fetching bank transactions', LogCategory.API, {
      userId: typedUser._id.toString(),
      role: typedUser.role,
      path: req.nextUrl.pathname
    });

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const transactionId = searchParams.get('transactionId');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const isReconciled = searchParams.get('isReconciled');

    // Mock data for bank transactions
    const bankTransactions = [
      {
        id: 1,
        transactionId: 'TRX-2025-0001',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-07-05',
        type: 'deposit',
        amount: 5000000,
        runningBalance: ********,
        description: 'Government Subvention Q1 2025-2026',
        reference: 'GS-Q1-2025',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 2,
        transactionId: 'TRX-2025-0002',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-07-10',
        type: 'withdrawal',
        amount: -1500000,
        runningBalance: ********,
        description: 'Transfer to Payroll Account',
        reference: 'TRF-PAY-2025-07',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 3,
        transactionId: 'TRX-2025-0003',
        bankAccountId: 2,
        bankAccountName: 'TCM Payroll Account',
        date: '2025-07-10',
        type: 'deposit',
        amount: 1500000,
        runningBalance: 5000000,
        description: 'Transfer from Main Account',
        reference: 'TRF-PAY-2025-07',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 4,
        transactionId: 'TRX-2025-0004',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-07-15',
        type: 'withdrawal',
        amount: -250000,
        runningBalance: ********,
        description: 'Office Supplies Payment',
        reference: 'CHK-10001',
        checkNumber: '10001',
        payee: 'Office World Ltd',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 5,
        transactionId: 'TRX-2025-0005',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-07-20',
        type: 'deposit',
        amount: 2000000,
        runningBalance: ********,
        description: 'Teacher Registration Fees',
        reference: 'REG-BATCH-2025-07',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 6,
        transactionId: 'TRX-2025-0006',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-07-25',
        type: 'fee',
        amount: -5000,
        runningBalance: ********,
        description: 'Bank Service Charges',
        reference: 'BSC-2025-07',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 7,
        transactionId: 'TRX-2025-0007',
        bankAccountId: 3,
        bankAccountName: 'TCM Reserve Fund',
        date: '2025-07-31',
        type: 'interest',
        amount: 125000,
        runningBalance: ********,
        description: 'Interest Earned',
        reference: 'INT-2025-07',
        status: 'cleared',
        isReconciled: true,
        reconciledDate: '2025-07-31',
      },
      {
        id: 8,
        transactionId: 'TRX-2025-0008',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-08-05',
        type: 'withdrawal',
        amount: -350000,
        runningBalance: ********,
        description: 'Utility Payments',
        reference: 'CHK-10002',
        checkNumber: '10002',
        payee: 'Electricity Supply Corporation',
        status: 'cleared',
        isReconciled: false,
      },
      {
        id: 9,
        transactionId: 'TRX-2025-0009',
        bankAccountId: 1,
        bankAccountName: 'TCM Main Operating Account',
        date: '2025-08-10',
        type: 'withdrawal',
        amount: -1500000,
        runningBalance: ********,
        description: 'Transfer to Payroll Account',
        reference: 'TRF-PAY-2025-08',
        status: 'pending',
        isReconciled: false,
      },
      {
        id: 10,
        transactionId: 'TRX-2025-0010',
        bankAccountId: 4,
        bankAccountName: 'TCM Foreign Currency Account',
        date: '2025-08-15',
        type: 'deposit',
        amount: 10000,
        runningBalance: 50000,
        description: 'International Grant Funding',
        reference: 'GRANT-2025-001',
        status: 'cleared',
        isReconciled: false,
      },
    ];

    // Filter transactions based on query parameters
    let filteredTransactions = [...bankTransactions];

    if (transactionId) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        transaction.transactionId === transactionId);

      if (filteredTransactions.length === 0) {
        return NextResponse.json(
          { error: 'Transaction not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(filteredTransactions[0]);
    }

    if (accountId) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        transaction.bankAccountId.toString() === accountId);
    }

    if (type) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        transaction.type === type);
    }

    if (status) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        transaction.status === status);
    }

    if (isReconciled) {
      const reconciled = isReconciled === 'true';
      filteredTransactions = filteredTransactions.filter(transaction =>
        transaction.isReconciled === reconciled);
    }

    if (startDate) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        new Date(transaction.date) >= new Date(startDate));
    }

    if (endDate) {
      filteredTransactions = filteredTransactions.filter(transaction =>
        new Date(transaction.date) <= new Date(endDate));
    }

    // Calculate totals
    const totals = {
      deposits: filteredTransactions
        .filter(t => t.amount > 0)
        .reduce((sum, t) => sum + t.amount, 0),
      withdrawals: filteredTransactions
        .filter(t => t.amount < 0)
        .reduce((sum, t) => sum + Math.abs(t.amount), 0),
      net: filteredTransactions
        .reduce((sum, t) => sum + t.amount, 0),
    };

    return NextResponse.json({
      transactions: filteredTransactions,
      total: filteredTransactions.length,
      totals,
    });
  } catch (error: unknown) {
    logger.error('Error fetching bank transactions:', LogCategory.API, {
      error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const user = await getCurrentUser(req);

    if (!user) {
      logger.warn('Unauthorized access attempt to create banking transaction', LogCategory.API, {
        path: req.nextUrl.pathname
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cast user to User type
    const typedUser = user as User;

    logger.info('Creating bank transaction', LogCategory.API, {
      userId: typedUser._id.toString(),
      role: typedUser.role,
      path: req.nextUrl.pathname
    });

    const data = await req.json();

    // In a real implementation, this would save to the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Bank transaction created successfully',
      data: {
        id: 11, // Mock ID
        transactionId: `TRX-2025-${Math.floor(1000 + Math.random() * 9000)}`,
        ...data,
        createdAt: new Date().toISOString(),
        createdBy: `${typedUser.firstName} ${typedUser.lastName}`,
        createdById: typedUser._id.toString()
      }
    });
  } catch (error: unknown) {
    logger.error('Error creating bank transaction:', LogCategory.API, {
      error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const user = await getCurrentUser(req);

    if (!user) {
      logger.warn('Unauthorized access attempt to update banking transaction', LogCategory.API, {
        path: req.nextUrl.pathname
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cast user to User type
    const typedUser = user as User;

    logger.info('Updating bank transaction', LogCategory.API, {
      userId: typedUser._id.toString(),
      role: typedUser.role,
      path: req.nextUrl.pathname
    });

    const data = await req.json();
    const { id } = data;

    if (!id) {
      logger.warn('Missing transaction ID in update request', LogCategory.API, {
        userId: typedUser._id.toString(),
        path: req.nextUrl.pathname
      });
      return NextResponse.json(
        { error: 'Transaction ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Bank transaction updated successfully',
      data: {
        ...data,
        updatedAt: new Date().toISOString(),
        updatedBy: `${typedUser.firstName} ${typedUser.lastName}`,
        updatedById: typedUser._id.toString()
      }
    });
  } catch (error: unknown) {
    logger.error('Error updating bank transaction:', LogCategory.API, {
      error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { transactionExportService } from '@/lib/services/banking/transaction-export-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * POST handler for exporting transactions
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { format, accountId, startDate, endDate, filters, dateFormat, includeFields, excludeFields, sortBy, sortDirection, limit } = data;

    if (!format) {
      return NextResponse.json(
        { error: 'Format is required' },
        { status: 400 }
      );
    }

    // Export transactions
    const result = await transactionExportService.exportTransactions(
      {
        format,
        accountId,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        filters,
        dateFormat,
        includeFields,
        excludeFields,
        sortBy,
        sortDirection,
        limit
      },
      user.id
    );

    // Log the export
    logger.info('Transactions exported', LogCategory.API, {
      userId: user.id,
      exportId: result.exportId,
      totalTransactions: result.totalTransactions
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      exportId: result.exportId,
      fileUrl: result.fileUrl,
      totalTransactions: result.totalTransactions
    });
  } catch (error: unknown) {
    logger.error('Error exporting transactions', LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to export transactions',
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { transactionImportService } from '@/lib/services/banking/transaction-import-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * POST handler for importing transactions
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const accountId = formData.get('accountId') as string;
    const format = formData.get('format') as string;
    const dateFormat = formData.get('dateFormat') as string;
    const columnMappingStr = formData.get('columnMapping') as string;
    const skipFirstRow = formData.get('skipFirstRow') === 'true';
    const validateOnly = formData.get('validateOnly') === 'true';

    // Validate required fields
    if (!file || !accountId || !format || !columnMappingStr) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Parse column mapping
    let columnMapping;
    try {
      columnMapping = JSON.parse(columnMappingStr);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid column mapping format' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Import transactions
    const result = await transactionImportService.importTransactions(
      buffer,
      {
        format: format as any,
        accountId,
        dateFormat,
        columnMapping,
        skipFirstRow,
        validateOnly
      },
      user.id
    );

    // Log the import
    logger.info('Transactions imported', LogCategory.API, {
      userId: user.id,
      importId: result.importId,
      totalTransactions: result.totalTransactions,
      importedTransactions: result.importedTransactions
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      importId: result.importId,
      totalTransactions: result.totalTransactions,
      importedTransactions: result.importedTransactions,
      failedTransactions: result.failedTransactions,
      duplicateTransactions: result.duplicateTransactions,
      errors: result.errors
    });
  } catch (error: unknown) {
    logger.error('Error importing transactions', LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to import transactions',
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

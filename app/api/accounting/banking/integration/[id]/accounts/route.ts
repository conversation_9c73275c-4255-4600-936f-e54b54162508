// app/api/accounting/banking/integration/[id]/accounts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/integration/[id]/accounts
 * Get bank accounts for a banking integration
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare integrationId at function scope
  let integrationId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    integrationId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get accounts
    const accounts = await bankingIntegrationService.getAccounts(integrationId);

    return NextResponse.json(accounts);
  } catch (error) {
    logger.error(`Error getting bank accounts for integration: ${integrationId}`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank accounts' },
      { status: 500 }
    );
  }
}

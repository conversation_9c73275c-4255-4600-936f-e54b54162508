// app/api/accounting/banking/integration/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/integration/[id]
 * Get a banking integration by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get the integration
    const integration = await bankingIntegrationService.getIntegration(id);

    if (!integration) {
      return NextResponse.json(
        { error: 'Banking integration not found' },
        { status: 404 }
      );
    }

    // Return integration without sensitive data
    const result = integration.toObject();
    delete result.credentials.clientSecret;
    delete result.credentials.apiKey;
    delete result.credentials.password;
    delete result.credentials.accessToken;
    delete result.credentials.refreshToken;

    return NextResponse.json(result);
  } catch (error) {
    logger.error(`Error getting banking integration`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get banking integration' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/banking/integration/[id]
 * Update a banking integration
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Update the integration
    const integration = await bankingIntegrationService.updateIntegration(
      id,
      {
        name: body.name,
        credentials: body.credentials,
        settings: body.settings,
        status: body.status,
        userId: user.id
      }
    );

    if (!integration) {
      return NextResponse.json(
        { error: 'Banking integration not found' },
        { status: 404 }
      );
    }

    // Return updated integration without sensitive data
    const result = integration.toObject();
    delete result.credentials.clientSecret;
    delete result.credentials.apiKey;
    delete result.credentials.password;
    delete result.credentials.accessToken;
    delete result.credentials.refreshToken;

    return NextResponse.json(result);
  } catch (error) {
    logger.error(`Error updating banking integration`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to update banking integration' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/banking/integration/[id]
 * Delete a banking integration
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete the integration
    const result = await bankingIntegrationService.deleteIntegration(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Banking integration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting banking integration`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to delete banking integration' },
      { status: 500 }
    );
  }
}

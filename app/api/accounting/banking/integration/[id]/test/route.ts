// app/api/accounting/banking/integration/[id]/test/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/accounting/banking/integration/[id]/test
 * Test connection to a banking integration
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare integrationId at function scope with default value
  let integrationId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    integrationId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Test the connection
    const result = await bankingIntegrationService.testConnection(integrationId);

    return NextResponse.json(result);
  } catch (error) {
    logger.error(`Error testing banking integration connection: ${integrationId}`, LogCategory.BANKING, error);
    return NextResponse.json(
      {
        success: false,
        message: `Failed to test connection: ${error instanceof Error ? error.message : 'Unknown error'}`
      },
      { status: 500 }
    );
  }
}

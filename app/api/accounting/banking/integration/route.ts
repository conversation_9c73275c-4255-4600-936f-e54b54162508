import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import { bankingProviderRegistry } from '@/lib/services/accounting/banking/banking-provider-registry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/integration
 * Get all banking integrations
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get banking integrations
    const integrations = await bankingIntegrationService.getIntegrations();

    // Return integrations without sensitive data
    const sanitizedIntegrations = integrations.map(integration => {
      const result = integration.toObject();

      // Remove sensitive data
      delete result.credentials.clientSecret;
      delete result.credentials.apiKey;
      delete result.credentials.password;
      delete result.credentials.accessToken;
      delete result.credentials.refreshToken;

      return result;
    });

    return NextResponse.json(sanitizedIntegrations);
  } catch (error) {
    logger.error('Error getting banking integrations', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get banking integrations' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/banking/integration
 * Create a new banking integration
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.providerType || !body.settings?.baseUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if the provider type is valid
    const providerTypes = bankingProviderRegistry.getProviderTypes();
    if (!providerTypes.includes(body.providerType)) {
      return NextResponse.json(
        { error: `Invalid provider type. Valid types are: ${providerTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Create the integration
    const integration = await bankingIntegrationService.createIntegration({
      name: body.name,
      providerType: body.providerType,
      credentials: body.credentials || {},
      settings: body.settings,
      userId: user.id
    });

    // Return created integration without sensitive data
    const result = integration.toObject();
    delete result.credentials.clientSecret;
    delete result.credentials.apiKey;
    delete result.credentials.password;
    delete result.credentials.accessToken;
    delete result.credentials.refreshToken;

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    logger.error('Error creating banking integration', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to create banking integration' },
      { status: 500 }
    );
  }
}

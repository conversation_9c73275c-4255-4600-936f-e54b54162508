import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/accounting/banking/banking-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/banking/integration/callback
 * Handle OAuth callback from banking provider
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');
    const integrationId = searchParams.get('integration_id');

    // Check for errors
    if (error) {
      logger.error('Banking OAuth error', LogCategory.BANKING, {
        error,
        errorDescription
      });
      
      // Redirect to error page
      return NextResponse.redirect(new URL(`/accounting/banking/integration?error=${encodeURIComponent(errorDescription || error)}`, req.url));
    }

    // Check required parameters
    if (!code || !state || !integrationId) {
      logger.error('Missing required parameters', LogCategory.BANKING, {
        code,
        state,
        integrationId
      });
      
      // Redirect to error page
      return NextResponse.redirect(new URL('/accounting/banking/integration?error=Missing+required+parameters', req.url));
    }

    // Connect to database
    await connectToDatabase();

    // Complete authentication
    const result = await bankingIntegrationService.completeAuthentication(integrationId, code, state);

    if (!result.success) {
      logger.error('Failed to complete banking authentication', LogCategory.BANKING, {
        message: result.message
      });
      
      // Redirect to error page
      return NextResponse.redirect(new URL(`/accounting/banking/integration?error=${encodeURIComponent(result.message)}`, req.url));
    }

    // Redirect to success page
    return NextResponse.redirect(new URL('/accounting/banking/integration?success=true', req.url));
  } catch (error) {
    logger.error('Error handling banking OAuth callback', LogCategory.BANKING, error);
    
    // Redirect to error page
    return NextResponse.redirect(new URL('/accounting/banking/integration?error=An+unexpected+error+occurred', req.url));
  }
}

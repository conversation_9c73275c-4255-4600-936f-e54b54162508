import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingProviderRegistry } from '@/lib/services/accounting/banking/banking-provider-registry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/banking/integration/providers
 * Get all available banking providers
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get provider types
    const providerTypes = bankingProviderRegistry.getProviderTypes();

    // Create provider info objects
    const providers = providerTypes.map(type => {
      const provider = bankingProviderRegistry.getProvider(type);
      return {
        type,
        name: provider?.getName() || type,
        description: `Integration with ${provider?.getName() || type} banking system`
      };
    });

    return NextResponse.json(providers);
  } catch (error) {
    logger.error('Error getting banking providers', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get banking providers' },
      { status: 500 }
    );
  }
}

// app/api/accounting/banking/payments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET handler for payments
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const paymentId = searchParams.get('paymentId');
    const reference = searchParams.get('reference');
    const status = searchParams.get('status');
    const method = searchParams.get('method');
    const payee = searchParams.get('payee');
    const bankAccountId = searchParams.get('bankAccountId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // In a real implementation, this would query the database
    // For now, return mock data
    const mockPayments = [
      {
        id: "PAY-2025-0001",
        reference: "INV-2025-001",
        date: "2025-01-15T00:00:00.000Z",
        amount: 125000,
        currency: "MWK",
        payee: "Office Supplies Ltd",
        description: "Payment for office supplies",
        status: "completed",
        method: "bank_transfer",
        bankAccount: "TCM Operations Account",
        bankAccountId: "1",
        createdBy: "John Banda",
        createdAt: "2025-01-15T10:30:00.000Z",
        updatedAt: "2025-01-15T14:45:00.000Z"
      },
      {
        id: "PAY-2025-0002",
        reference: "INV-2025-002",
        date: "2025-01-20T00:00:00.000Z",
        amount: 350000,
        currency: "MWK",
        payee: "IT Solutions Inc",
        description: "Payment for IT services",
        status: "pending",
        method: "check",
        bankAccount: "TCM Operations Account",
        bankAccountId: "1",
        createdBy: "Mary Phiri",
        createdAt: "2025-01-20T09:15:00.000Z"
      }
    ];

    // Log the request
    logger.info('Payments retrieved', LogCategory.API, {
      userId: user.id,
      query: {
        paymentId,
        reference,
        status,
        method,
        payee,
        bankAccountId,
        startDate,
        endDate,
        page,
        limit
      }
    });

    return NextResponse.json({
      success: true,
      data: mockPayments,
      pagination: {
        page,
        limit,
        total: mockPayments.length,
        totalPages: Math.ceil(mockPayments.length / limit)
      }
    });
  } catch (error) {
    console.error('Error retrieving payments:', error);
    logger.error('Error retrieving payments', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new payment
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();

    // Validate required fields
    const requiredFields = ['reference', 'date', 'amount', 'payee', 'description', 'method', 'bankAccount'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // In a real implementation, this would save to the database
    // For now, just return success with the data
    const newPayment = {
      id: `PAY-2025-${Math.floor(1000 + Math.random() * 9000)}`,
      ...data,
      status: data.status || 'pending',
      currency: data.currency || 'MWK',
      createdAt: new Date().toISOString(),
      createdBy: user.id || 'Unknown User',
    };

    // Log the creation
    logger.info('Payment created', LogCategory.API, {
      userId: user.id,
      paymentId: newPayment.id,
      reference: data.reference
    });

    return NextResponse.json({
      success: true,
      message: 'Payment created successfully',
      data: newPayment
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    logger.error('Error creating payment', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a payment
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PUT(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    const updatedPayment = {
      ...data,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id || 'Unknown User',
    };

    // Log the update
    logger.info('Payment updated', LogCategory.API, {
      userId: user.id,
      paymentId: id,
      reference: data.reference
    });

    return NextResponse.json({
      success: true,
      message: 'Payment updated successfully',
      data: updatedPayment
    });
  } catch (error) {
    console.error('Error updating payment:', error);
    logger.error('Error updating payment', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { paymentProcessingService } from '@/lib/services/banking/payment-processing-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * POST handler for sending payment notifications
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { paymentIds } = data;

    if (!paymentIds || !Array.isArray(paymentIds) || paymentIds.length === 0) {
      return NextResponse.json(
        { error: 'Payment IDs are required' },
        { status: 400 }
      );
    }

    // Send notifications
    const success = await paymentProcessingService.sendPaymentNotifications(paymentIds);

    // Log the notification sending
    logger.info('Payment notifications sent', LogCategory.API, {
      userId: user.id,
      paymentIds,
      count: paymentIds.length
    });

    return NextResponse.json({
      success,
      message: `Successfully sent notifications for ${paymentIds.length} payments`
    });
  } catch (error: unknown) {
    logger.error('Error sending payment notifications', LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to send payment notifications',
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

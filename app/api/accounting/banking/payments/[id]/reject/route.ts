// app/api/accounting/banking/payments/[id]/reject/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { paymentProcessingService } from '@/lib/services/banking/payment-processing-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

/**
 * POST handler for rejecting a payment
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare paymentId at function scope with default value
  let paymentId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    paymentId = id;
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { reason } = data;

    if (!reason) {
      return NextResponse.json(
        { error: 'Rejection reason is required' },
        { status: 400 }
      );
    }

    // Reject payment
    const payment = await paymentProcessingService.rejectPayment(
      paymentId,
      user.id,
      reason
    );

    // Log the rejection
    logger.info(`Payment rejected: ${paymentId}`, LogCategory.API, {
      userId: user.id,
      paymentId: paymentId,
      reason
    });

    return NextResponse.json({
      success: true,
      message: 'Payment rejected successfully',
      data: payment
    });
  } catch (error) {
    logger.error(`Error rejecting payment: ${paymentId}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to reject payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// import { authOptions } from '@/lib/auth';
import { bankingIntegrationService } from '@/lib/services/banking-integration/banking-integration-service';
import { logger } from '@/lib/utils/logger';

/**
 * GET /api/accounting/banking/integrations
 * Get all banking integrations for a bank account
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const bankAccountId = searchParams.get('bankAccountId');

    if (!bankAccountId) {
      return NextResponse.json(
        { error: 'Bank account ID is required' },
        { status: 400 }
      );
    }

    // Get integrations
    const integrations = await bankingIntegrationService.getIntegrationsByBankAccount(bankAccountId);

    return NextResponse.json({
      success: true,
      integrations,
    });
  } catch (error: unknown) {
    logger.error('Error getting banking integrations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/banking/integrations
 * Create a new banking integration
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { name, provider, description, bankAccountId, settings, credentialsId } = body;

    // Validate required fields
    if (!name || !provider || !bankAccountId || !settings || !credentialsId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create integration
    const integration = await bankingIntegrationService.createIntegration({
      name,
      provider,
      description,
      bankAccountId,
      settings,
      credentialsId,
    });

    return NextResponse.json({
      success: true,
      integration,
    });
  } catch (error: unknown) {
    logger.error('Error creating banking integration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/banking/integrations/[id]/sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/banking-integration/banking-integration-service';
import { logger } from '@/lib/utils/logger';

/**
 * POST /api/accounting/banking/integrations/[id]/sync
 * Sync bank transactions from an integration
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare integrationId at function scope with default value
  let integrationId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    integrationId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { scheduleId, startDate, endDate } = body;

    // Parse dates if provided
    const parsedStartDate = startDate ? new Date(startDate) : undefined;
    const parsedEndDate = endDate ? new Date(endDate) : undefined;

    // Sync transactions
    const result = await bankingIntegrationService.syncBankTransactions(
      integrationId,
      scheduleId,
      parsedStartDate,
      parsedEndDate
    );

    return NextResponse.json({
      success: true,
      result,
    });
  } catch (error) {
    logger.error(`Error syncing bank transactions for integration ${integrationId}:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/banking/integrations/[id]/test-connection/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingIntegrationService } from '@/lib/services/banking-integration/banking-integration-service';
import { logger } from '@/lib/utils/logger';

/**
 * POST /api/accounting/banking/integrations/[id]/test-connection
 * Test a banking integration connection
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare integrationId at function scope with default value
  let integrationId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    integrationId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Test connection
    const status = await bankingIntegrationService.testConnection(integrationId);

    return NextResponse.json({
      success: true,
      status,
    });
  } catch (error: unknown) {
    logger.error(`Error testing banking integration connection:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

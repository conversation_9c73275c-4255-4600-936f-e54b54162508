// app/api/accounting/banking/integrations/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// import { authOptions } from '@/lib/auth';
import { bankingIntegrationService } from '@/lib/services/banking-integration/banking-integration-service';
import { logger } from '@/lib/utils/logger';

/**
 * GET /api/accounting/banking/integrations/[id]
 * Get a banking integration by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Get integration
    const integration = await bankingIntegrationService.getIntegration(id);
    if (!integration) {
      return NextResponse.json(
        { error: 'Banking integration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      integration,
    });
  } catch (error: unknown) {
    logger.error(`Error getting banking integration:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/banking/integrations/[id]
 * Update a banking integration
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();

    // Resolve the params promise
    const { id } = await params;

    // Update integration
    const integration = await bankingIntegrationService.updateIntegration(id, body);

    return NextResponse.json({
      success: true,
      integration,
    });
  } catch (error: unknown) {
    logger.error(`Error updating banking integration:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/banking/integrations/[id]
 * Delete a banking integration
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Delete integration
    const success = await bankingIntegrationService.deleteIntegration(id);

    return NextResponse.json({
      success,
    });
  } catch (error: unknown) {
    logger.error(`Error deleting banking integration:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetFundService } from '@/lib/services/accounting/budget-fund-service';

/**
 * GET /api/accounting/budget-fund
 * Get budget fund data for a fiscal year
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';

    logger.info('Getting budget fund data', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear
    });

    // Get budget fund summary
    const budgetFundData = await budgetFundService.getBudgetFundSummary(fiscalYear);

    if (!budgetFundData) {
      // Create budget fund if it doesn't exist
      const budgetFund = await budgetFundService.getOrCreateBudgetFund(fiscalYear, user.id);
      const newBudgetFundData = await budgetFundService.getBudgetFundSummary(fiscalYear);
      
      return NextResponse.json({
        success: true,
        budgetFund: newBudgetFundData?.budgetFund,
        summary: newBudgetFundData?.summary || {
          projected: { income: 0, expense: 0, net: 0 },
          expected: { income: 0, expense: 0, net: 0 },
          actual: { income: 0, expense: 0, net: 0 }
        },
        message: 'Budget fund created automatically'
      });
    }

    return NextResponse.json({
      success: true,
      budgetFund: budgetFundData.budgetFund,
      summary: budgetFundData.summary
    });

  } catch (error: unknown) {
    logger.error('Error getting budget fund data', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget-fund
 * Create a new budget fund
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { fiscalYear } = body;

    if (!fiscalYear) {
      return NextResponse.json(
        { error: 'Fiscal year is required' },
        { status: 400 }
      );
    }

    logger.info('Creating budget fund', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear
    });

    // Create budget fund
    const budgetFund = await budgetFundService.getOrCreateBudgetFund(fiscalYear, user.id);
    const budgetFundData = await budgetFundService.getBudgetFundSummary(fiscalYear);

    return NextResponse.json({
      success: true,
      message: 'Budget fund created successfully',
      budgetFund: budgetFundData?.budgetFund,
      summary: budgetFundData?.summary
    }, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating budget fund', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

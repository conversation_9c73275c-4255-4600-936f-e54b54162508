import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/income-sources
 * Get all income sources
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status'); // active, inactive, all

    // Default income sources for Teachers Council of Malawi
    const incomeSources = [
      {
        id: 'government_subvention',
        value: 'government_subvention',
        label: 'Government Subvention',
        description: 'Government funding and grants for teacher council operations',
        category: 'government',
        isActive: true,
        taxable: false,
        requiresDocumentation: true
      },
      {
        id: 'registration_fees',
        value: 'registration_fees',
        label: 'Teacher Registration Fees',
        description: 'Fees collected from teacher registration and certification',
        category: 'fees',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'licensing_fees',
        value: 'licensing_fees',
        label: 'Professional Licensing Fees',
        description: 'Fees for professional teaching licenses and renewals',
        category: 'fees',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'training_fees',
        value: 'training_fees',
        label: 'Training and Workshop Fees',
        description: 'Revenue from professional development training programs',
        category: 'services',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'certification_fees',
        value: 'certification_fees',
        label: 'Certification Fees',
        description: 'Fees for teacher certification and accreditation services',
        category: 'fees',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'consultation_fees',
        value: 'consultation_fees',
        label: 'Consultation Services',
        description: 'Revenue from educational consultation services',
        category: 'services',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'donations',
        value: 'donations',
        label: 'Donations and Grants',
        description: 'Donations and grants from organizations and individuals',
        category: 'donations',
        isActive: true,
        taxable: false,
        requiresDocumentation: true
      },
      {
        id: 'investment_income',
        value: 'investment_income',
        label: 'Investment Income',
        description: 'Income from investments and interest earnings',
        category: 'investments',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'partnership_revenue',
        value: 'partnership_revenue',
        label: 'Partnership Revenue',
        description: 'Revenue from partnerships with educational institutions',
        category: 'partnerships',
        isActive: true,
        taxable: true,
        requiresDocumentation: true
      },
      {
        id: 'other',
        value: 'other',
        label: 'Other Income',
        description: 'Miscellaneous income sources not categorized above',
        category: 'other',
        isActive: true,
        taxable: true,
        requiresDocumentation: false
      }
    ];

    // Filter based on status
    let filteredIncomeSources = incomeSources;
    if (status === 'active') {
      filteredIncomeSources = incomeSources.filter(source => source.isActive);
    } else if (status === 'inactive') {
      filteredIncomeSources = incomeSources.filter(source => !source.isActive);
    }

    logger.info('Income sources fetched successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      count: filteredIncomeSources.length,
      filters: { status }
    });

    return NextResponse.json({
      incomeSources: filteredIncomeSources,
      totalCount: filteredIncomeSources.length
    });

  } catch (error: unknown) {
    logger.error('Error fetching income sources', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/income-sources
 * Create a new income source
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to create income sources' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { label, description, category, taxable, requiresDocumentation } = body;

    // Validate required fields
    if (!label || !category) {
      return NextResponse.json(
        { error: 'Missing required fields: label, category' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Create income source object
    const incomeSource = {
      id: label.toLowerCase().replace(/\s+/g, '_'),
      value: label.toLowerCase().replace(/\s+/g, '_'),
      label,
      description: description || '',
      category,
      isActive: true,
      taxable: taxable || false,
      requiresDocumentation: requiresDocumentation || false,
      createdBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    logger.info('Income source created successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      incomeSourceId: incomeSource.id,
      label: incomeSource.label
    });

    return NextResponse.json(incomeSource, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating income source', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

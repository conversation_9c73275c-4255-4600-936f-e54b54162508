import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import ExternalSystem from '@/models/accounting/ExternalSystem';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/integrations/[id]
 * Get an external system by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get external system
    const externalSystem = await ExternalSystem.findById(id).select('-credentials.clientSecret -credentials.apiKey -credentials.password -credentials.accessToken -credentials.refreshToken -credentials.state -credentials.codeVerifier');

    if (!externalSystem) {
      return NextResponse.json(
        { error: 'External system not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(externalSystem);
  } catch (error) {
    logger.error('Error getting external system', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to get external system' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/integrations/[id]
 * Update an external system
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Get external system
    const externalSystem = await ExternalSystem.findById(id);

    if (!externalSystem) {
      return NextResponse.json(
        { error: 'External system not found' },
        { status: 404 }
      );
    }

    // Update fields
    if (body.name !== undefined) externalSystem.name = body.name;
    if (body.description !== undefined) externalSystem.description = body.description;
    if (body.baseUrl !== undefined) externalSystem.baseUrl = body.baseUrl;
    if (body.apiVersion !== undefined) externalSystem.apiVersion = body.apiVersion;
    if (body.isActive !== undefined) externalSystem.isActive = body.isActive;
    if (body.syncFrequency !== undefined) externalSystem.syncFrequency = body.syncFrequency;

    // Update credentials
    if (body.credentials) {
      if (body.credentials.clientId !== undefined) externalSystem.credentials.clientId = body.credentials.clientId;
      if (body.credentials.clientSecret !== undefined) externalSystem.credentials.clientSecret = body.credentials.clientSecret;
      if (body.credentials.apiKey !== undefined) externalSystem.credentials.apiKey = body.credentials.apiKey;
      if (body.credentials.username !== undefined) externalSystem.credentials.username = body.credentials.username;
      if (body.credentials.password !== undefined) externalSystem.credentials.password = body.credentials.password;
    }

    // Update sync settings
    if (body.syncSettings) {
      if (body.syncSettings.importChartOfAccounts !== undefined) externalSystem.syncSettings.importChartOfAccounts = body.syncSettings.importChartOfAccounts;
      if (body.syncSettings.importCustomers !== undefined) externalSystem.syncSettings.importCustomers = body.syncSettings.importCustomers;
      if (body.syncSettings.importVendors !== undefined) externalSystem.syncSettings.importVendors = body.syncSettings.importVendors;
      if (body.syncSettings.importTransactions !== undefined) externalSystem.syncSettings.importTransactions = body.syncSettings.importTransactions;
      if (body.syncSettings.importEmployees !== undefined) externalSystem.syncSettings.importEmployees = body.syncSettings.importEmployees;
      if (body.syncSettings.exportChartOfAccounts !== undefined) externalSystem.syncSettings.exportChartOfAccounts = body.syncSettings.exportChartOfAccounts;
      if (body.syncSettings.exportCustomers !== undefined) externalSystem.syncSettings.exportCustomers = body.syncSettings.exportCustomers;
      if (body.syncSettings.exportVendors !== undefined) externalSystem.syncSettings.exportVendors = body.syncSettings.exportVendors;
      if (body.syncSettings.exportTransactions !== undefined) externalSystem.syncSettings.exportTransactions = body.syncSettings.exportTransactions;
      if (body.syncSettings.exportEmployees !== undefined) externalSystem.syncSettings.exportEmployees = body.syncSettings.exportEmployees;
      if (body.syncSettings.startDate !== undefined) externalSystem.syncSettings.startDate = body.syncSettings.startDate;
      if (body.syncSettings.endDate !== undefined) externalSystem.syncSettings.endDate = body.syncSettings.endDate;
    }

    // Update mappings
    if (body.mappings) {
      if (body.mappings.accounts !== undefined) externalSystem.mappings.accounts = body.mappings.accounts;
      if (body.mappings.customers !== undefined) externalSystem.mappings.customers = body.mappings.customers;
      if (body.mappings.vendors !== undefined) externalSystem.mappings.vendors = body.mappings.vendors;
      if (body.mappings.employees !== undefined) externalSystem.mappings.employees = body.mappings.employees;
      if (body.mappings.transactions !== undefined) externalSystem.mappings.transactions = body.mappings.transactions;
    }

    // Save external system
    await externalSystem.save();

    // Return updated external system without sensitive data
    const result = externalSystem.toObject();
    delete result.credentials.clientSecret;
    delete result.credentials.apiKey;
    delete result.credentials.password;
    delete result.credentials.accessToken;
    delete result.credentials.refreshToken;
    delete result.credentials.state;
    delete result.credentials.codeVerifier;

    return NextResponse.json(result);
  } catch (error) {
    logger.error('Error updating external system', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to update external system' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/integrations/[id]
 * Delete an external system
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete external system
    const result = await ExternalSystem.findByIdAndDelete(id);

    if (!result) {
      return NextResponse.json(
        { error: 'External system not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Error deleting external system', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to delete external system' },
      { status: 500 }
    );
  }
}

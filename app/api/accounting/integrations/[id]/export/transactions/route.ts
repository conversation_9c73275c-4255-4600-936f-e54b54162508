import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import ExternalSystem from '@/models/accounting/ExternalSystem';
import { IntegrationFactory } from '@/lib/services/accounting/integration/integration-factory';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * POST /api/accounting/integrations/[id]/export/transactions
 * Export transactions to an external system
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {

  try {
    // Resolve the params promise
    const { id } = await params;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get external system
    const externalSystem = await ExternalSystem.findById(id);

    if (!externalSystem) {
      return NextResponse.json(
        { error: 'External system not found' },
        { status: 404 }
      );
    }

    // Get request body
    const body = await req.json();
    const startDate = body.startDate ? new Date(body.startDate) : undefined;
    const endDate = body.endDate ? new Date(body.endDate) : undefined;

    // Create integration service
    const integrationService = IntegrationFactory.createIntegrationService(externalSystem);

    // Export transactions
    const result = await integrationService.exportTransactions(user.id, startDate, endDate);

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error exporting transactions to external system', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      {
        success: false,
        message: `Failed to export transactions: ${error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'}`
      },
      { status: 500 }
    );
  }
}

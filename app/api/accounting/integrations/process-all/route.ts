// app/api/accounting/integrations/process-all/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { moduleIntegrationService } from '@/lib/services/accounting/module-integration-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError
} from '@/lib/backend/utils/api-response';

/**
 * POST /api/accounting/integrations/process-all
 * Process all pending integrations with accounting
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Process all pending integrations
    const result = await moduleIntegrationService.processAllPendingIntegrations(
      user.id
    );

    // Calculate total processed and failed
    const totalProcessed =
      result.data.payroll.processed +
      result.data.inventory.processed +
      result.data.project.processed;

    const totalFailed =
      result.data.payroll.failed +
      result.data.inventory.failed +
      result.data.project.failed;

    // Return success response
    return successResponse(
      result.data,
      `Successfully processed ${totalProcessed} integrations with ${totalFailed} failures`
    );
  } catch (error) {
    // Handle general errors
    return handleApiError(error);
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import ExternalSystem from '@/models/accounting/ExternalSystem';
import { IntegrationFactory } from '@/lib/services/accounting/integration/integration-factory';
import { QuickBooksIntegrationService } from '@/lib/services/accounting/integration/quickbooks-integration-service';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/integrations/quickbooks/callback
 * Handle OAuth callback from QuickBooks
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Check for errors
    if (error) {
      logger.error('QuickBooks OAuth error', LogCategory.INTEGRATION, {
        error,
        errorDescription
      });

      // Redirect to error page
      return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?error=' + encodeURIComponent(errorDescription || error), req.url));
    }

    // Check required parameters
    if (!code || !state) {
      logger.error('Missing required parameters', LogCategory.INTEGRATION, {
        code,
        state
      });

      // Redirect to error page
      return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?error=Missing+required+parameters', req.url));
    }

    // Connect to database
    await connectToDatabase();

    // Find external system with matching state
    const externalSystem = await ExternalSystem.findOne({
      type: 'quickbooks',
      'credentials.state': state
    });

    if (!externalSystem) {
      logger.error('Invalid state parameter', LogCategory.INTEGRATION, {
        state
      });

      // Redirect to error page
      return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?error=Invalid+state+parameter', req.url));
    }

    // Create integration service
    const integrationService = IntegrationFactory.createIntegrationService(externalSystem) as QuickBooksIntegrationService;

    // Complete authentication
    const result = await integrationService.completeAuthentication(user.id, code, state);

    if (!result.success) {
      logger.error('Failed to complete QuickBooks authentication', LogCategory.INTEGRATION, {
        message: result.message
      });

      // Redirect to error page
      return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?error=' + encodeURIComponent(result.message), req.url));
    }

    // Redirect to success page
    return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?success=true', req.url));
  } catch (error) {
    logger.error('Error handling QuickBooks OAuth callback', LogCategory.INTEGRATION, error);

    // Redirect to error page
    return NextResponse.redirect(new URL('/accounting/integrations/quickbooks?error=An+unexpected+error+occurred', req.url));
  }
}

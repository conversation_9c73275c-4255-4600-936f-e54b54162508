import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import ExternalSystem, { IExternalSystem } from '@/models/accounting/ExternalSystem';
import { IntegrationFactory } from '@/lib/services/accounting/integration/integration-factory';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/integrations
 * Get all external systems
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');

    // Build query
    const query: Record<string, unknown> = {};
    if (type) query.type = type;
    if (isActive !== null) query.isActive = isActive === 'true';

    // Get external systems
    const externalSystems = await ExternalSystem.find(query).select('-credentials.clientSecret -credentials.apiKey -credentials.password -credentials.accessToken -credentials.refreshToken -credentials.state -credentials.codeVerifier');

    return NextResponse.json(externalSystems);
  } catch (error) {
    logger.error('Error getting external systems', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to get external systems' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/integrations
 * Create a new external system
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.type || !body.baseUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create external system
    const externalSystem = new ExternalSystem({
      name: body.name,
      type: body.type,
      description: body.description,
      baseUrl: body.baseUrl,
      apiVersion: body.apiVersion,
      credentials: body.credentials || {},
      isActive: body.isActive !== undefined ? body.isActive : true,
      syncFrequency: body.syncFrequency || 'manual',
      syncSettings: body.syncSettings || {},
      mappings: body.mappings || {}
    });

    // Save external system
    await externalSystem.save();

    // Return created external system without sensitive data
    const result = externalSystem.toObject();
    delete result.credentials.clientSecret;
    delete result.credentials.apiKey;
    delete result.credentials.password;
    delete result.credentials.accessToken;
    delete result.credentials.refreshToken;
    delete result.credentials.state;
    delete result.credentials.codeVerifier;

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    logger.error('Error creating external system', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to create external system' },
      { status: 500 }
    );
  }
}

// app/api/accounting/reports/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import FinancialStatement from '@/models/accounting/FinancialStatement';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/reports/[id]
 * Get a financial report by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: reportId } = await params;
    id = reportId;

    // Get financial statement
    const report = await FinancialStatement.findById(id)
      .populate('createdBy', 'name')
      .populate('approvedBy', 'name')
      .populate('publishedBy', 'name');

    if (!report) {
      return NextResponse.json(
        { error: 'Financial report not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(report);
  } catch (error) {
    logger.error(`Error getting financial report: ${id}`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/reports/[id]
 * Update a financial report
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Resolve the params promise
    const { id: reportId } = await params;
    id = reportId;

    // Find financial statement
    const report = await FinancialStatement.findById(id);
    if (!report) {
      return NextResponse.json(
        { error: 'Financial report not found' },
        { status: 404 }
      );
    }

    // Check if report can be updated
    if (report.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft reports can be updated' },
        { status: 400 }
      );
    }

    // Update financial statement
    const updatedReport = await FinancialStatement.findByIdAndUpdate(
      id,
      {
        ...body,
        updatedBy: user.id
      },
      { new: true, runValidators: true }
    )
      .populate('createdBy', 'name')
      .populate('approvedBy', 'name')
      .populate('publishedBy', 'name');

    return NextResponse.json(updatedReport);
  } catch (error) {
    logger.error(`Error updating financial report: ${id}`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/reports/[id]
 * Delete a financial report
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: reportId } = await params;
    id = reportId;

    // Find financial statement
    const report = await FinancialStatement.findById(id);
    if (!report) {
      return NextResponse.json(
        { error: 'Financial report not found' },
        { status: 404 }
      );
    }

    // Check if report can be deleted
    if (report.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft reports can be deleted' },
        { status: 400 }
      );
    }

    // Delete financial statement
    await FinancialStatement.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Financial report deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting financial report: ${id}`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/reports/[id]/actions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import FinancialStatement from '@/models/accounting/FinancialStatement';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/accounting/reports/[id]/actions
 * Perform actions on a financial report (publish, archive, approve)
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: reportId } = await params;
    id = reportId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // Find financial statement
    const report = await FinancialStatement.findById(id);
    if (!report) {
      return NextResponse.json(
        { error: 'Financial report not found' },
        { status: 404 }
      );
    }

    // Process based on action
    switch (body.action) {
      case 'publish':
        // Check permissions
        const hasPublishPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasPublishPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to publish reports' },
            { status: 403 }
          );
        }

        // Check if report can be published
        if (report.status !== 'draft' && report.status !== 'approved') {
          return NextResponse.json(
            { error: 'Only draft or approved reports can be published' },
            { status: 400 }
          );
        }

        // Update report
        report.status = 'published';
        report.publishedBy = new mongoose.Types.ObjectId(user.id);
        report.publishedAt = new Date();
        await report.save();

        // Return updated report
        return NextResponse.json({
          success: true,
          message: 'Financial report published successfully',
          data: await FinancialStatement.findById(id)
            .populate('createdBy', 'name')
            .populate('approvedBy', 'name')
            .populate('publishedBy', 'name')
        });

      case 'archive':
        // Check permissions
        const hasArchivePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasArchivePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to archive reports' },
            { status: 403 }
          );
        }

        // Check if report can be archived
        if (report.status !== 'published') {
          return NextResponse.json(
            { error: 'Only published reports can be archived' },
            { status: 400 }
          );
        }

        // Update report
        report.status = 'archived';
        report.updatedBy = new mongoose.Types.ObjectId(user.id);
        await report.save();

        // Return updated report
        return NextResponse.json({
          success: true,
          message: 'Financial report archived successfully',
          data: await FinancialStatement.findById(id)
            .populate('createdBy', 'name')
            .populate('approvedBy', 'name')
            .populate('publishedBy', 'name')
        });

      case 'approve':
        // Check permissions
        const hasApprovePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasApprovePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve reports' },
            { status: 403 }
          );
        }

        // Check if report can be approved
        if (report.status !== 'draft') {
          return NextResponse.json(
            { error: 'Only draft reports can be approved' },
            { status: 400 }
          );
        }

        // Update report
        report.status = 'approved';
        report.approvedBy = new mongoose.Types.ObjectId(user.id);
        report.approvedAt = new Date();
        await report.save();

        // Return updated report
        return NextResponse.json({
          success: true,
          message: 'Financial report approved successfully',
          data: await FinancialStatement.findById(id)
            .populate('createdBy', 'name')
            .populate('approvedBy', 'name')
            .populate('publishedBy', 'name')
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: publish, archive, approve' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error(`Error performing action on financial report: ${id}`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/reports/[id]/export/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import FinancialStatement from '@/models/accounting/FinancialStatement';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * POST /api/accounting/reports/[id]/export
 * Export a financial report in various formats (PDF, Excel, CSV)
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: reportId } = await params;
    id = reportId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate format
    if (!body.format || !['pdf', 'excel', 'csv'].includes(body.format)) {
      return NextResponse.json(
        { error: 'Invalid format. Supported formats: pdf, excel, csv' },
        { status: 400 }
      );
    }

    // Find financial statement
    const report = await FinancialStatement.findById(id)
      .populate('createdBy', 'name')
      .populate('approvedBy', 'name')
      .populate('publishedBy', 'name');

    if (!report) {
      return NextResponse.json(
        { error: 'Financial report not found' },
        { status: 404 }
      );
    }

    // Check if report can be exported
    if (report.status === 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ])) {
      return NextResponse.json(
        { error: 'Draft reports can only be exported by authorized personnel' },
        { status: 403 }
      );
    }

    // Generate export data based on format
    let exportData = Buffer.from('No data available'); // Default value
    let contentType = 'application/octet-stream'; // Default content type
    let filename = 'report.txt'; // Default filename

    switch (body.format) {
      case 'pdf':
        // In a real implementation, this would generate a PDF file
        // For now, we'll return a placeholder response
        exportData = Buffer.from('PDF export not implemented yet');
        contentType = 'application/pdf';
        filename = `${report.name.replace(/\s+/g, '_')}.pdf`;
        break;

      case 'excel':
        // In a real implementation, this would generate an Excel file
        // For now, we'll return a placeholder response
        exportData = Buffer.from('Excel export not implemented yet');
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = `${report.name.replace(/\s+/g, '_')}.xlsx`;
        break;

      case 'csv':
        // In a real implementation, this would generate a CSV file
        // For now, we'll return a placeholder CSV with some data from the report
        let csvContent = 'Report Name,Type,Period,Fiscal Year,Start Date,End Date,Status\n';
        csvContent += `"${report.name}","${report.type}","${report.period}","${report.fiscalYear}","${report.startDate.toISOString()}","${report.endDate.toISOString()}","${report.status}"\n\n`;

        // Add data based on report type
        if (report.type === 'income_statement' && report.data && report.data.revenue && report.data.expenses) {
          csvContent += 'REVENUE\n';
          csvContent += 'Category,Item,Amount\n';

          report.data.revenue.categories.forEach((category: Record<string, any>) => {
            category.items.forEach((item: Record<string, any>) => {
              csvContent += `"${category.name}","${item.name}",${item.amount}\n`;
            });
          });

          csvContent += `Total Revenue,,${report.data.revenue.total}\n\n`;

          csvContent += 'EXPENSES\n';
          csvContent += 'Category,Item,Amount\n';

          report.data.expenses.categories.forEach((category: Record<string, any>) => {
            category.items.forEach((item: Record<string, any>) => {
              csvContent += `"${category.name}","${item.name}",${item.amount}\n`;
            });
          });

          csvContent += `Total Expenses,,${report.data.expenses.total}\n\n`;

          csvContent += `Net Income,,${report.data.netIncome}\n`;
        }

        exportData = Buffer.from(csvContent);
        contentType = 'text/csv';
        filename = `${report.name.replace(/\s+/g, '_')}.csv`;
        break;
    }

    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', contentType);
    headers.set('Content-Disposition', `attachment; filename="${filename}"`);

    // Return file
    return new NextResponse(exportData.toString(), {
      status: 200,
      headers
    });
  } catch (error: unknown) {
    logger.error(`Error exporting financial report: ${id}`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

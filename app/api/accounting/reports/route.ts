// app/api/accounting/reports/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type');
    const period = searchParams.get('period');
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';
    const id = searchParams.get('id');

    // Mock data for reports
    const reports = [
      {
        id: 1,
        name: 'Q1 Financial Report 2025-2026',
        type: 'quarterly',
        period: 'quarterly',
        fiscalYear: '2025-2026',
        startDate: '2025-07-01',
        endDate: '2025-09-30',
        status: 'published',
        format: 'pdf',
        createdAt: '2025-10-15',
        createdBy: '<PERSON>',
      },
      {
        id: 2,
        name: 'Q2 Financial Report 2025-2026',
        type: 'quarterly',
        period: 'quarterly',
        fiscalYear: '2025-2026',
        startDate: '2025-10-01',
        endDate: '2025-12-31',
        status: 'draft',
        format: 'pdf',
        createdAt: '2026-01-10',
        createdBy: 'John Doe',
      },
      {
        id: 3,
        name: 'Annual Financial Report 2024-2025',
        type: 'annual',
        period: 'annual',
        fiscalYear: '2024-2025',
        startDate: '2024-07-01',
        endDate: '2025-06-30',
        status: 'published',
        format: 'pdf',
        createdAt: '2025-07-30',
        createdBy: 'Jane Smith',
      },
      {
        id: 4,
        name: 'Tax Compliance Report 2024-2025',
        type: 'compliance',
        subtype: 'tax',
        period: 'annual',
        fiscalYear: '2024-2025',
        startDate: '2024-07-01',
        endDate: '2025-06-30',
        status: 'published',
        format: 'pdf',
        createdAt: '2025-08-15',
        createdBy: 'Jane Smith',
      },
      {
        id: 5,
        name: 'Custom Expenditure Analysis',
        type: 'custom',
        period: 'custom',
        fiscalYear: '2025-2026',
        startDate: '2025-07-01',
        endDate: '2025-12-31',
        status: 'draft',
        format: 'excel',
        createdAt: '2025-12-20',
        createdBy: 'John Doe',
      },
    ];

    // Filter reports based on query parameters
    let filteredReports = [...reports];

    if (id) {
      filteredReports = filteredReports.filter(report => report.id.toString() === id);

      if (filteredReports.length === 0) {
        return NextResponse.json(
          { error: 'Report not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(filteredReports[0]);
    }

    if (type) {
      filteredReports = filteredReports.filter(report => report.type === type);
    }

    if (period) {
      filteredReports = filteredReports.filter(report => report.period === period);
    }

    if (fiscalYear) {
      filteredReports = filteredReports.filter(report => report.fiscalYear === fiscalYear);
    }

    return NextResponse.json({
      reports: filteredReports,
      total: filteredReports.length,
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // In a real implementation, this would save to the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Report created successfully',
      data: {
        id: 6, // Mock ID
        ...data,
        createdAt: new Date().toISOString(),
        createdBy: `${user.firstName} ${user.lastName}` || 'Unknown User',
      }
    });
  } catch (error) {
    console.error('Error creating report:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json(
        { error: 'Report ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database
    // For now, just return success with the data
    return NextResponse.json({
      success: true,
      message: 'Report updated successfully',
      data: {
        ...data,
        updatedAt: new Date().toISOString(),
        updatedBy: `${user.firstName} ${user.lastName}` || 'Unknown User',
      }
    });
  } catch (error) {
    console.error('Error updating report:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

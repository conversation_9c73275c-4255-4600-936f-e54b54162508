import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { 
  advancedReportingService,
  ReportConfiguration,
  CustomReportBuilder,
  AnalyticsDashboard
} from '@/lib/services/accounting/advanced-reporting-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schemas
const generateReportSchema = z.object({
  configurationId: z.string(),
  overrides: z.object({
    dateRange: z.object({
      startDate: z.string().transform(str => new Date(str)),
      endDate: z.string().transform(str => new Date(str))
    }).optional(),
    filters: z.record(z.unknown()).optional()
  }).optional()
});

const reportConfigurationSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['income_summary', 'budget_analysis', 'variance_report', 'trend_analysis', 'custom_query', 'dashboard_widget']),
  category: z.enum(['financial', 'operational', 'compliance', 'strategic', 'executive']),
  scope: z.object({
    dateRange: z.object({
      startDate: z.string().transform(str => new Date(str)),
      endDate: z.string().transform(str => new Date(str)),
      period: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'])
    }),
    filters: z.object({
      budgetIds: z.array(z.string()).optional(),
      categoryIds: z.array(z.string()).optional(),
      sources: z.array(z.string()).optional(),
      amountRange: z.object({
        min: z.number(),
        max: z.number()
      }).optional(),
      status: z.array(z.string()).optional(),
      approvalStatus: z.array(z.string()).optional()
    }),
    groupBy: z.array(z.enum(['date', 'source', 'category', 'budget', 'amount_range', 'approval_status'])),
    aggregations: z.array(z.enum(['sum', 'average', 'count', 'min', 'max', 'variance', 'growth_rate']))
  }),
  visualization: z.object({
    chartType: z.enum(['line', 'bar', 'pie', 'area', 'scatter', 'heatmap', 'table', 'kpi_card']),
    layout: z.enum(['single', 'grid', 'dashboard', 'comparison']),
    styling: z.object({
      colors: z.array(z.string()),
      theme: z.enum(['light', 'dark', 'auto']),
      responsive: z.boolean()
    })
  }),
  scheduling: z.object({
    enabled: z.boolean(),
    frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly']),
    recipients: z.array(z.string()),
    format: z.enum(['pdf', 'excel', 'csv', 'email_summary'])
  }),
  permissions: z.object({
    viewRoles: z.array(z.string()),
    editRoles: z.array(z.string()),
    shareRoles: z.array(z.string())
  }),
  isTemplate: z.boolean().optional(),
  isPublic: z.boolean().optional()
});

// Required roles for advanced reporting operations
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT
];

/**
 * POST /api/accounting/reports/advanced
 * Generate advanced reports or create configurations
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    if (action === 'generate') {
      // Generate report
      const validationResult = generateReportSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationResult.error.errors
        }, { status: 400 });
      }

      const { configurationId, overrides } = validationResult.data;

      const reportData = await advancedReportingService.generateReport(
        configurationId,
        user.id,
        overrides
      );

      logger.info('Advanced report generated', LogCategory.ACCOUNTING, {
        configurationId,
        userId: user.id,
        dataPoints: reportData.dataPoints.length,
        executionTime: reportData.performance.executionTime
      });

      return NextResponse.json({
        success: true,
        report: reportData
      });

    } else if (action === 'schedule') {
      // Schedule report
      const { configurationId, scheduling } = body;
      
      const result = await advancedReportingService.scheduleReport(
        configurationId,
        scheduling,
        user.id
      );

      logger.info('Report scheduled', LogCategory.ACCOUNTING, {
        configurationId,
        userId: user.id,
        frequency: scheduling.frequency
      });

      return NextResponse.json({
        success: true,
        nextRun: result.nextRun
      });

    } else {
      // Create report configuration
      const validationResult = reportConfigurationSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationResult.error.errors
        }, { status: 400 });
      }

      const configurationData = {
        ...validationResult.data,
        id: new Date().getTime().toString(),
        createdBy: user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        isTemplate: validationResult.data.isTemplate || false,
        isPublic: validationResult.data.isPublic || false
      };

      logger.info('Advanced report configuration created', LogCategory.ACCOUNTING, {
        configurationId: configurationData.id,
        type: configurationData.type,
        userId: user.id
      });

      return NextResponse.json({
        success: true,
        configuration: configurationData
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error in advanced reports API', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    return NextResponse.json({
      error: 'Failed to process advanced reports request',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/reports/advanced
 * Get advanced report configurations and templates
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    const reportId = searchParams.get('reportId');
    const format = searchParams.get('format') as 'pdf' | 'excel' | 'csv' | 'json' | null;

    if (type === 'export' && reportId && format) {
      // Export report
      const exportData = await advancedReportingService.exportReport(
        reportId,
        format,
        user.id
      );

      logger.info('Advanced report exported', LogCategory.ACCOUNTING, {
        reportId,
        format,
        userId: user.id
      });

      return new NextResponse(exportData.data, {
        headers: {
          'Content-Type': exportData.mimeType,
          'Content-Disposition': `attachment; filename="${exportData.filename}"`
        }
      });

    } else if (type === 'templates') {
      // Get advanced report templates
      const templates = [
        {
          id: 'comprehensive_income_analysis',
          name: 'Comprehensive Income Analysis',
          description: 'Advanced income analysis with forecasting and variance detection',
          category: 'financial',
          configuration: {
            type: 'income_summary',
            scope: {
              groupBy: ['date', 'source', 'category'],
              aggregations: ['sum', 'average', 'count', 'growth_rate'],
              filters: {}
            },
            visualization: {
              chartType: 'line',
              layout: 'dashboard'
            }
          },
          isOfficial: true,
          tags: ['income', 'comprehensive', 'analysis', 'forecasting']
        },
        {
          id: 'budget_performance_dashboard',
          name: 'Budget Performance Dashboard',
          description: 'Real-time budget vs actual performance with variance alerts',
          category: 'financial',
          configuration: {
            type: 'budget_analysis',
            scope: {
              groupBy: ['budget', 'category'],
              aggregations: ['sum', 'variance'],
              filters: {}
            },
            visualization: {
              chartType: 'bar',
              layout: 'comparison'
            }
          },
          isOfficial: true,
          tags: ['budget', 'performance', 'variance', 'dashboard']
        },
        {
          id: 'executive_summary_report',
          name: 'Executive Summary Report',
          description: 'High-level financial overview for executive decision making',
          category: 'executive',
          configuration: {
            type: 'trend_analysis',
            scope: {
              groupBy: ['date'],
              aggregations: ['sum', 'growth_rate'],
              filters: {}
            },
            visualization: {
              chartType: 'kpi_card',
              layout: 'grid'
            }
          },
          isOfficial: true,
          tags: ['executive', 'summary', 'trends', 'kpi']
        }
      ];

      return NextResponse.json({
        success: true,
        templates
      });

    } else if (type === 'configuration') {
      // Get advanced reporting configuration
      const configuration = {
        reportTypes: [
          {
            value: 'income_summary',
            label: 'Income Summary',
            description: 'Comprehensive income analysis with trends and insights',
            features: ['trend_analysis', 'variance_detection', 'forecasting']
          },
          {
            value: 'budget_analysis',
            label: 'Budget Analysis',
            description: 'Budget vs actual performance with variance analysis',
            features: ['variance_analysis', 'performance_metrics', 'alerts']
          },
          {
            value: 'variance_report',
            label: 'Variance Report',
            description: 'Detailed variance analysis with root cause identification',
            features: ['anomaly_detection', 'root_cause_analysis', 'recommendations']
          },
          {
            value: 'trend_analysis',
            label: 'Trend Analysis',
            description: 'Historical trends with predictive analytics',
            features: ['pattern_recognition', 'forecasting', 'seasonal_analysis']
          },
          {
            value: 'custom_query',
            label: 'Custom Query',
            description: 'Build custom reports with advanced query capabilities',
            features: ['custom_fields', 'advanced_filters', 'calculated_metrics']
          }
        ],
        visualizations: [
          {
            type: 'line',
            label: 'Line Chart',
            description: 'Best for showing trends over time',
            useCases: ['time_series', 'trends', 'comparisons']
          },
          {
            type: 'bar',
            label: 'Bar Chart',
            description: 'Best for comparing categories',
            useCases: ['category_comparison', 'rankings', 'distributions']
          },
          {
            type: 'pie',
            label: 'Pie Chart',
            description: 'Best for showing proportions',
            useCases: ['composition', 'percentages', 'parts_of_whole']
          },
          {
            type: 'area',
            label: 'Area Chart',
            description: 'Best for showing cumulative values',
            useCases: ['cumulative_totals', 'stacked_values', 'volume']
          },
          {
            type: 'table',
            label: 'Data Table',
            description: 'Best for detailed data examination',
            useCases: ['detailed_data', 'drill_down', 'precise_values']
          },
          {
            type: 'kpi_card',
            label: 'KPI Card',
            description: 'Best for highlighting key metrics',
            useCases: ['key_metrics', 'dashboards', 'summaries']
          }
        ],
        aggregationFunctions: [
          {
            value: 'sum',
            label: 'Sum',
            description: 'Total of all values',
            applicableTypes: ['number', 'currency']
          },
          {
            value: 'average',
            label: 'Average',
            description: 'Mean of all values',
            applicableTypes: ['number', 'currency']
          },
          {
            value: 'count',
            label: 'Count',
            description: 'Number of records',
            applicableTypes: ['all']
          },
          {
            value: 'min',
            label: 'Minimum',
            description: 'Smallest value',
            applicableTypes: ['number', 'currency', 'date']
          },
          {
            value: 'max',
            label: 'Maximum',
            description: 'Largest value',
            applicableTypes: ['number', 'currency', 'date']
          },
          {
            value: 'variance',
            label: 'Variance',
            description: 'Statistical variance from expected',
            applicableTypes: ['number', 'currency']
          },
          {
            value: 'growth_rate',
            label: 'Growth Rate',
            description: 'Period-over-period growth percentage',
            applicableTypes: ['number', 'currency']
          }
        ],
        filterOperators: [
          { value: 'equals', label: 'Equals', types: ['string', 'number', 'date'] },
          { value: 'not_equals', label: 'Not Equals', types: ['string', 'number', 'date'] },
          { value: 'greater_than', label: 'Greater Than', types: ['number', 'date'] },
          { value: 'less_than', label: 'Less Than', types: ['number', 'date'] },
          { value: 'between', label: 'Between', types: ['number', 'date'] },
          { value: 'in', label: 'In List', types: ['string', 'number'] },
          { value: 'not_in', label: 'Not In List', types: ['string', 'number'] },
          { value: 'contains', label: 'Contains', types: ['string'] },
          { value: 'starts_with', label: 'Starts With', types: ['string'] }
        ],
        exportFormats: [
          {
            value: 'pdf',
            label: 'PDF Report',
            description: 'Professional formatted report',
            mimeType: 'application/pdf',
            features: ['formatting', 'charts', 'branding']
          },
          {
            value: 'excel',
            label: 'Excel Workbook',
            description: 'Spreadsheet with data and charts',
            mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            features: ['formulas', 'pivot_tables', 'charts']
          },
          {
            value: 'csv',
            label: 'CSV Data',
            description: 'Raw data for further analysis',
            mimeType: 'text/csv',
            features: ['raw_data', 'import_friendly']
          },
          {
            value: 'json',
            label: 'JSON Data',
            description: 'Structured data for API integration',
            mimeType: 'application/json',
            features: ['api_integration', 'structured_data']
          }
        ]
      };

      return NextResponse.json({
        success: true,
        configuration
      });

    } else {
      // Get user's advanced report configurations
      return NextResponse.json({
        success: true,
        configurations: [],
        message: 'Advanced report configurations listing not yet implemented'
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting advanced reports data', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get advanced reports data',
      details: errorMessage
    }, { status: 500 });
  }
}

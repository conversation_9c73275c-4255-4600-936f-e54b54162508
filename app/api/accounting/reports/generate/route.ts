// app/api/accounting/reports/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import FinancialStatement from '@/models/accounting/FinancialStatement';
import Account from '@/models/accounting/Account';
import Transaction from '@/models/accounting/Transaction';
import JournalEntry from '@/models/accounting/JournalEntry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/accounting/reports/generate
 * Generate a financial report based on specified parameters
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.type || !body.startDate || !body.endDate || !body.fiscalYear) {
      return NextResponse.json(
        { error: 'Missing required fields: type, startDate, endDate, fiscalYear' },
        { status: 400 }
      );
    }

    // Parse dates
    const startDate = new Date(body.startDate);
    const endDate = new Date(body.endDate);

    // Validate date range
    if (startDate > endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Generate report based on type
    let reportData;
    let reportName;

    switch (body.type) {
      case 'income_statement':
        reportData = await generateIncomeStatement(startDate, endDate, body);
        reportName = `Income Statement ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
        break;

      case 'balance_sheet':
        reportData = await generateBalanceSheet(endDate, body);
        reportName = `Balance Sheet as of ${endDate.toISOString().split('T')[0]}`;
        break;

      case 'cash_flow':
        reportData = await generateCashFlow(startDate, endDate, body);
        reportName = `Cash Flow Statement ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
        break;

      case 'custom':
        // Check if this is a trial balance report
        if (body.customOptions?.reportSubtype === 'trial_balance') {
          reportData = await generateTrialBalance(endDate, body);
          reportName = `Trial Balance as of ${endDate.toISOString().split('T')[0]}`;
        } else {
          reportData = await generateCustomReport(startDate, endDate, body);
          reportName = `Custom Report ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid report type. Supported types: income_statement, balance_sheet, cash_flow, custom' },
          { status: 400 }
        );
    }

    // Create financial statement
    const financialStatement = new FinancialStatement({
      name: reportName,
      type: body.type,
      period: body.period || 'custom',
      startDate,
      endDate,
      fiscalYear: body.fiscalYear,
      fiscalPeriod: body.fiscalPeriod,
      data: reportData,
      status: 'draft',
      format: body.detailed ? 'detailed' : 'summary',
      currency: body.currency || 'MWK',
      comparisonEnabled: body.compareWithPrevious || false,
      departments: body.departments,
      costCenters: body.costCenters,
      projects: body.projects,
      createdBy: user._id,
      updatedBy: user._id
    });

    // Save financial statement
    await financialStatement.save();

    return NextResponse.json({
      success: true,
      message: 'Financial report generated successfully',
      report: financialStatement
    });
  } catch (error: unknown) {
    logger.error('Error generating financial report', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Generate income statement data
 */
async function generateIncomeStatement(startDate: Date, endDate: Date, options: {
  detailed?: boolean;
  compareWithPrevious?: boolean;
  fiscalYear?: string;
  currency?: string;
  departments?: mongoose.Types.ObjectId[];
  costCenters?: mongoose.Types.ObjectId[];
  projects?: mongoose.Types.ObjectId[];
  includeZeroBalances?: boolean;
}) {
  // Get all revenue accounts
  const revenueAccounts = await Account.find({
    type: 'revenue',
    isActive: true
  });

  // Get all expense accounts
  const expenseAccounts = await Account.find({
    type: 'expense',
    isActive: true
  });

  // Get transactions for the period
  const transactions = await Transaction.find({
    date: { $gte: startDate, $lte: endDate },
    status: { $ne: 'voided' }
  }).populate('account', 'accountNumber name type');

  // Get journal entries for the period
  const journalEntries = await JournalEntry.find({
    date: { $gte: startDate, $lte: endDate },
    status: 'posted'
  }).populate('items.accountId', 'accountNumber name type');

  // Process revenue
  const revenueCategories: Array<{
    name: string;
    amount: number;
    items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }>;
  }> = [];
  let totalRevenue = 0;

  // Group revenue accounts by subtype
  const revenueBySubtype = revenueAccounts.reduce((acc: Record<string, any[]>, account) => {
    const subtype = account.subtype || 'Other Revenue';
    if (!acc[subtype]) {
      acc[subtype] = [];
    }
    acc[subtype].push(account);
    return acc;
  }, {});

  // Calculate revenue for each subtype
  for (const [subtype, accounts] of Object.entries(revenueBySubtype)) {
    const items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }> = [];
    let subtypeTotal = 0;

    for (const account of accounts) {
      let accountTotal = 0;

      // Add transaction amounts
      transactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'credit') {
            accountTotal += transaction.amount;
          } else if (transaction.type === 'debit') {
            accountTotal -= transaction.amount;
          }
        }
      });

      // Add journal entry amounts
      journalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; credit: number; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            accountTotal += item.credit - item.debit;
          }
        });
      });

      if (accountTotal !== 0 || options.includeZeroBalances) {
        items.push({
          name: account.name,
          amount: accountTotal,
          accountId: account._id
        });
        subtypeTotal += accountTotal;
      }
    }

    if (items.length > 0 || options.includeZeroBalances) {
      revenueCategories.push({
        name: subtype,
        amount: subtypeTotal,
        items
      });
      totalRevenue += subtypeTotal;
    }
  }

  // Process expenses
  const expenseCategories: Array<{
    name: string;
    amount: number;
    items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }>;
  }> = [];
  let totalExpenses = 0;

  // Group expense accounts by subtype
  const expenseBySubtype = expenseAccounts.reduce((acc: Record<string, any[]>, account) => {
    const subtype = account.subtype || 'Other Expenses';
    if (!acc[subtype]) {
      acc[subtype] = [];
    }
    acc[subtype].push(account);
    return acc;
  }, {});

  // Calculate expenses for each subtype
  for (const [subtype, accounts] of Object.entries(expenseBySubtype)) {
    const items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }> = [];
    let subtypeTotal = 0;

    for (const account of accounts) {
      let accountTotal = 0;

      // Add transaction amounts
      transactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'debit') {
            accountTotal += transaction.amount;
          } else if (transaction.type === 'credit') {
            accountTotal -= transaction.amount;
          }
        }
      });

      // Add journal entry amounts
      journalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; credit: number; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            accountTotal += item.debit - item.credit;
          }
        });
      });

      if (accountTotal !== 0 || options.includeZeroBalances) {
        items.push({
          name: account.name,
          amount: accountTotal,
          accountId: account._id
        });
        subtypeTotal += accountTotal;
      }
    }

    if (items.length > 0 || options.includeZeroBalances) {
      expenseCategories.push({
        name: subtype,
        amount: subtypeTotal,
        items
      });
      totalExpenses += subtypeTotal;
    }
  }

  // Calculate net income
  const netIncome = totalRevenue - totalExpenses;

  // Return income statement data
  return {
    revenue: {
      categories: revenueCategories,
      total: totalRevenue
    },
    expenses: {
      categories: expenseCategories,
      total: totalExpenses
    },
    netIncome
  };
}

/**
 * Generate balance sheet data
 */
async function generateBalanceSheet(asOfDate: Date, options: {
  detailed?: boolean;
  compareWithPrevious?: boolean;
  fiscalYear?: string;
  currency?: string;
  departments?: mongoose.Types.ObjectId[];
  costCenters?: mongoose.Types.ObjectId[];
  projects?: mongoose.Types.ObjectId[];
  includeZeroBalances?: boolean;
}) {
  // Get all asset accounts
  const assetAccounts = await Account.find({
    type: 'asset',
    isActive: true
  });

  // Get all liability accounts
  const liabilityAccounts = await Account.find({
    type: 'liability',
    isActive: true
  });

  // Get all equity accounts
  const equityAccounts = await Account.find({
    type: 'equity',
    isActive: true
  });

  // Get all transactions up to the specified date
  const transactions = await Transaction.find({
    date: { $lte: asOfDate },
    status: { $ne: 'voided' }
  }).populate('account', 'accountNumber name type');

  // Get all journal entries up to the specified date
  const journalEntries = await JournalEntry.find({
    date: { $lte: asOfDate },
    status: 'posted'
  }).populate('items.accountId', 'accountNumber name type');

  // Process assets
  const assetCategories: Array<{
    name: string;
    amount: number;
    items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }>;
  }> = [];
  let totalAssets = 0;

  // Group asset accounts by subtype
  const assetsBySubtype = assetAccounts.reduce((acc: Record<string, any[]>, account) => {
    const subtype = account.subtype || 'Other Assets';
    if (!acc[subtype]) {
      acc[subtype] = [];
    }
    acc[subtype].push(account);
    return acc;
  }, {});

  // Calculate assets for each subtype
  for (const [subtype, accounts] of Object.entries(assetsBySubtype)) {
    const items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }> = [];
    let subtypeTotal = 0;

    for (const account of accounts) {
      let accountTotal = 0;

      // Add transaction amounts
      transactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'debit') {
            accountTotal += transaction.amount;
          } else if (transaction.type === 'credit') {
            accountTotal -= transaction.amount;
          }
        }
      });

      // Add journal entry amounts
      journalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; credit: number; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            accountTotal += item.debit - item.credit;
          }
        });
      });

      if (accountTotal !== 0 || options.includeZeroBalances) {
        items.push({
          name: account.name,
          amount: accountTotal,
          accountId: account._id
        });
        subtypeTotal += accountTotal;
      }
    }

    if (items.length > 0 || options.includeZeroBalances) {
      assetCategories.push({
        name: subtype,
        amount: subtypeTotal,
        items
      });
      totalAssets += subtypeTotal;
    }
  }

  // Process liabilities
  const liabilityCategories: Array<{
    name: string;
    amount: number;
    items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }>;
  }> = [];
  let totalLiabilities = 0;

  // Group liability accounts by subtype
  const liabilitiesBySubtype = liabilityAccounts.reduce((acc: Record<string, any[]>, account) => {
    const subtype = account.subtype || 'Other Liabilities';
    if (!acc[subtype]) {
      acc[subtype] = [];
    }
    acc[subtype].push(account);
    return acc;
  }, {});

  // Calculate liabilities for each subtype
  for (const [subtype, accounts] of Object.entries(liabilitiesBySubtype)) {
    const items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId;
    }> = [];
    let subtypeTotal = 0;

    for (const account of accounts) {
      let accountTotal = 0;

      // Add transaction amounts
      transactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'credit') {
            accountTotal += transaction.amount;
          } else if (transaction.type === 'debit') {
            accountTotal -= transaction.amount;
          }
        }
      });

      // Add journal entry amounts
      journalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; credit: number; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            accountTotal += item.credit - item.debit;
          }
        });
      });

      if (accountTotal !== 0 || options.includeZeroBalances) {
        items.push({
          name: account.name,
          amount: accountTotal,
          accountId: account._id
        });
        subtypeTotal += accountTotal;
      }
    }

    if (items.length > 0 || options.includeZeroBalances) {
      liabilityCategories.push({
        name: subtype,
        amount: subtypeTotal,
        items
      });
      totalLiabilities += subtypeTotal;
    }
  }

  // Process equity
  const equityCategories: Array<{
    name: string;
    amount: number;
    items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId  | undefined;
    }>;
  }> = [];
  let totalEquity = 0;

  // Group equity accounts by subtype
  const equityBySubtype = equityAccounts.reduce((acc: Record<string, any[]>, account) => {
    const subtype = account.subtype || 'Other Equity';
    if (!acc[subtype]) {
      acc[subtype] = [];
    }
    acc[subtype].push(account);
    return acc;
  }, {});

  // Calculate equity for each subtype
  for (const [subtype, accounts] of Object.entries(equityBySubtype)) {
    const items: Array<{
      name: string;
      amount: number;
      accountId: mongoose.Types.ObjectId | undefined;
    }> = [];
    let subtypeTotal = 0;

    for (const account of accounts) {
      let accountTotal = 0;

      // Add transaction amounts
      transactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'credit') {
            accountTotal += transaction.amount;
          } else if (transaction.type === 'debit') {
            accountTotal -= transaction.amount;
          }
        }
      });

      // Add journal entry amounts
      journalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; credit: number; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            accountTotal += item.credit - item.debit;
          }
        });
      });

      if (accountTotal !== 0 || options.includeZeroBalances) {
        items.push({
          name: account.name,
          amount: accountTotal,
          accountId: account._id
        });
        subtypeTotal += accountTotal;
      }
    }

    if (items.length > 0 || options.includeZeroBalances) {
      equityCategories.push({
        name: subtype,
        amount: subtypeTotal,
        items
      });
      totalEquity += subtypeTotal;
    }
  }

  // Calculate retained earnings (if not already included)
  // This is a simplified approach - in a real system, you would calculate this from income and dividend accounts
  const retainedEarnings = totalAssets - totalLiabilities - totalEquity;

  if (retainedEarnings !== 0) {
    equityCategories.push({
      name: 'Retained Earnings',
      amount: retainedEarnings,
      items: [{
        name: 'Retained Earnings',
        amount: retainedEarnings,
        accountId: undefined
      }]
    });
    totalEquity += retainedEarnings;
  }

  // Return balance sheet data
  return {
    assets: {
      categories: assetCategories,
      total: totalAssets
    },
    liabilities: {
      categories: liabilityCategories,
      total: totalLiabilities
    },
    equity: {
      categories: equityCategories,
      total: totalEquity
    }
  };
}

/**
 * Generate cash flow statement data
 */
async function generateCashFlow(startDate: Date, endDate: Date, options: {
  detailed?: boolean;
  compareWithPrevious?: boolean;
  fiscalYear?: string;
  currency?: string;
  departments?: mongoose.Types.ObjectId[];
  costCenters?: mongoose.Types.ObjectId[];
  projects?: mongoose.Types.ObjectId[];
  includeZeroBalances?: boolean;
  method?: 'direct' | 'indirect';
}) {
  // Determine method (direct or indirect)
  const method = options.method || 'indirect';

  // Get all cash and cash equivalent accounts
  const cashAccounts = await Account.find({
    type: 'asset',
    subtype: { $in: ['Cash', 'Cash Equivalents'] },
    isActive: true
  });

  if (cashAccounts.length === 0) {
    throw new Error('No cash accounts found');
  }

  // Get cash account IDs
  const cashAccountIds = cashAccounts.map(account => account._id);

  // Get beginning cash balance (as of startDate)
  let beginningCashBalance = 0;

  // Get transactions before start date
  const beginningTransactions = await Transaction.find({
    date: { $lt: startDate },
    status: { $ne: 'voided' },
    account: { $in: cashAccountIds }
  });

  // Get journal entries before start date
  const beginningJournalEntries = await JournalEntry.find({
    date: { $lt: startDate },
    status: 'posted'
  });

  // Calculate beginning cash balance
  for (const account of cashAccounts) {
    let accountBalance = 0;

    // Add transaction amounts
    beginningTransactions.forEach(transaction => {
      if (transaction.account.toString() === account._id.toString()) {
        if (transaction.type === 'debit') {
          accountBalance += transaction.amount;
        } else if (transaction.type === 'credit') {
          accountBalance -= transaction.amount;
        }
      }
    });

    // Add journal entry amounts
    beginningJournalEntries.forEach(entry => {
      entry.items.forEach((item: { accountId: mongoose.Types.ObjectId | { toString(): string }; debit: number; credit: number }) => {
        if (item.accountId.toString() === account._id.toString()) {
          accountBalance += item.debit - item.credit;
        }
      });
    });

    beginningCashBalance += accountBalance;
  }

  // Get transactions during the period
  const periodTransactions = await Transaction.find({
    date: { $gte: startDate, $lte: endDate },
    status: { $ne: 'voided' }
  }).populate('account', 'accountNumber name type subtype');

  // Get journal entries during the period
  const periodJournalEntries = await JournalEntry.find({
    date: { $gte: startDate, $lte: endDate },
    status: 'posted'
  }).populate('items.accountId', 'accountNumber name type subtype');

  // Initialize cash flow categories
  const operatingItems: Array<{
    name: string;
    amount: number;
    accountId?: mongoose.Types.ObjectId;
  }> = [];
  const investingItems: Array<{
    name: string;
    amount: number;
    accountId?: mongoose.Types.ObjectId;
  }> = [];
  const financingItems: Array<{
    name: string;
    amount: number;
    accountId?: mongoose.Types.ObjectId;
  }> = [];

  let totalOperating = 0;
  let totalInvesting = 0;
  let totalFinancing = 0;

  if (method === 'direct') {
    // Direct method implementation
    // Get revenue accounts
    const revenueAccounts = await Account.find({
      type: 'revenue',
      isActive: true
    });

    // Get expense accounts
    const expenseAccounts = await Account.find({
      type: 'expense',
      isActive: true
    });

    // Calculate cash received from customers
    let cashFromCustomers = 0;
    revenueAccounts.forEach(account => {
      periodTransactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString() &&
            cashAccountIds.includes(transaction.relatedAccount)) {
          cashFromCustomers += transaction.amount;
        }
      });
    });

    operatingItems.push({
      name: 'Cash received from customers',
      amount: cashFromCustomers
    });
    totalOperating += cashFromCustomers;

    // Calculate cash paid to suppliers and employees
    let cashPaidToSuppliersAndEmployees = 0;
    expenseAccounts.forEach(account => {
      periodTransactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString() &&
            cashAccountIds.includes(transaction.relatedAccount)) {
          cashPaidToSuppliersAndEmployees += transaction.amount;
        }
      });
    });

    operatingItems.push({
      name: 'Cash paid to suppliers and employees',
      amount: -cashPaidToSuppliersAndEmployees
    });
    totalOperating -= cashPaidToSuppliersAndEmployees;

    // Add other operating cash flows
    // This would be more detailed in a real implementation
  } else {
    // Indirect method implementation
    // Start with net income
    // For simplicity, we'll calculate it here, but in a real system you would use the income statement
    const incomeStatement = await generateIncomeStatement(startDate, endDate, options);
    const netIncome = incomeStatement.netIncome;

    operatingItems.push({
      name: 'Net Income',
      amount: netIncome
    });
    totalOperating += netIncome;

    // Add adjustments for non-cash items
    // For example, depreciation
    const depreciationAccounts = await Account.find({
      type: 'expense',
      subtype: 'Depreciation',
      isActive: true
    });

    let depreciation = 0;
    depreciationAccounts.forEach(account => {
      periodTransactions.forEach(transaction => {
        if (transaction.account._id.toString() === account._id.toString()) {
          if (transaction.type === 'debit') {
            depreciation += transaction.amount;
          }
        }
      });

      periodJournalEntries.forEach(entry => {
        entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; debit: number }) => {
          if (item.accountId._id.toString() === account._id.toString()) {
            depreciation += item.debit;
          }
        });
      });
    });

    if (depreciation !== 0) {
      operatingItems.push({
        name: 'Depreciation',
        amount: depreciation
      });
      totalOperating += depreciation;
    }

    // Add changes in working capital
    // This would be more detailed in a real implementation
  }

  // Calculate investing activities
  // Get fixed asset accounts
  const fixedAssetAccounts = await Account.find({
    type: 'asset',
    subtype: { $in: ['Fixed Assets', 'Property', 'Equipment'] },
    isActive: true
  });

  // Calculate purchase of fixed assets
  let purchaseOfFixedAssets = 0;
  fixedAssetAccounts.forEach(account => {
    periodTransactions.forEach(transaction => {
      if (transaction.account._id.toString() === account._id.toString() &&
          cashAccountIds.includes(transaction.relatedAccount) &&
          transaction.type === 'debit') {
        purchaseOfFixedAssets += transaction.amount;
      }
    });
  });

  if (purchaseOfFixedAssets !== 0) {
    investingItems.push({
      name: 'Purchase of fixed assets',
      amount: -purchaseOfFixedAssets
    });
    totalInvesting -= purchaseOfFixedAssets;
  }

  // Calculate sale of fixed assets
  let saleOfFixedAssets = 0;
  fixedAssetAccounts.forEach(account => {
    periodTransactions.forEach(transaction => {
      if (transaction.account._id.toString() === account._id.toString() &&
          cashAccountIds.includes(transaction.relatedAccount) &&
          transaction.type === 'credit') {
        saleOfFixedAssets += transaction.amount;
      }
    });
  });

  if (saleOfFixedAssets !== 0) {
    investingItems.push({
      name: 'Sale of fixed assets',
      amount: saleOfFixedAssets
    });
    totalInvesting += saleOfFixedAssets;
  }

  // Calculate financing activities
  // Get loan accounts
  const loanAccounts = await Account.find({
    type: 'liability',
    subtype: { $in: ['Loans', 'Notes Payable'] },
    isActive: true
  });

  // Calculate proceeds from loans
  let proceedsFromLoans = 0;
  loanAccounts.forEach(account => {
    periodTransactions.forEach(transaction => {
      if (transaction.account._id.toString() === account._id.toString() &&
          cashAccountIds.includes(transaction.relatedAccount) &&
          transaction.type === 'credit') {
        proceedsFromLoans += transaction.amount;
      }
    });
  });

  if (proceedsFromLoans !== 0) {
    financingItems.push({
      name: 'Proceeds from loans',
      amount: proceedsFromLoans
    });
    totalFinancing += proceedsFromLoans;
  }

  // Calculate repayment of loans
  let repaymentOfLoans = 0;
  loanAccounts.forEach(account => {
    periodTransactions.forEach(transaction => {
      if (transaction.account._id.toString() === account._id.toString() &&
          cashAccountIds.includes(transaction.relatedAccount) &&
          transaction.type === 'debit') {
        repaymentOfLoans += transaction.amount;
      }
    });
  });

  if (repaymentOfLoans !== 0) {
    financingItems.push({
      name: 'Repayment of loans',
      amount: -repaymentOfLoans
    });
    totalFinancing -= repaymentOfLoans;
  }

  // Calculate net cash flow
  const netCashFlow = totalOperating + totalInvesting + totalFinancing;

  // Calculate ending cash balance
  const endingCashBalance = beginningCashBalance + netCashFlow;

  // Return cash flow statement data
  return {
    operatingActivities: {
      items: operatingItems,
      total: totalOperating
    },
    investingActivities: {
      items: investingItems,
      total: totalInvesting
    },
    financingActivities: {
      items: financingItems,
      total: totalFinancing
    },
    netCashFlow,
    beginningCashBalance,
    endingCashBalance
  };
}

/**
 * Generate custom report data
 * This is a placeholder implementation that should be replaced with actual custom report generation logic
 */
async function generateCustomReport(startDate: Date, endDate: Date, options: {
  detailed?: boolean;
  compareWithPrevious?: boolean;
  fiscalYear?: string;
  currency?: string;
  departments?: mongoose.Types.ObjectId[];
  costCenters?: mongoose.Types.ObjectId[];
  projects?: mongoose.Types.ObjectId[];
  includeZeroBalances?: boolean;
  customOptions?: Record<string, any>;
}) {
  // This is a placeholder implementation
  // In a real implementation, this would generate a custom report based on the options

  // For now, return a basic structure with some metadata
  return {
    title: "Custom Financial Report",
    period: {
      startDate,
      endDate
    },
    options: {
      ...options
    },
    sections: [],
    summary: {
      message: "Custom report generation not fully implemented"
    }
  };
}

/**
 * Generate trial balance data
 */
async function generateTrialBalance(asOfDate: Date, options: {
  detailed?: boolean;
  compareWithPrevious?: boolean;
  fiscalYear?: string;
  currency?: string;
  departments?: mongoose.Types.ObjectId[];
  costCenters?: mongoose.Types.ObjectId[];
  projects?: mongoose.Types.ObjectId[];
  includeZeroBalances?: boolean;
}) {
  // Get all active accounts
  const accounts = await Account.find({
    isActive: true
  }).sort({ code: 1 });

  // Get all transactions up to the specified date
  const transactions = await Transaction.find({
    date: { $lte: asOfDate },
    status: { $ne: 'voided' }
  }).populate('account', 'accountNumber name type');

  // Get all journal entries up to the specified date
  const journalEntries = await JournalEntry.find({
    date: { $lte: asOfDate },
    status: 'posted'
  }).populate('items.accountId', 'accountNumber name type');

  // Initialize trial balance data
  const accountBalances: Array<{
    accountId: mongoose.Types.ObjectId;
    code: string;
    name: string;
    type: string;
    subtype?: string;
    debit: number;
    credit: number;
  }> = [];
  let totalDebit = 0;
  let totalCredit = 0;

  // Calculate balance for each account
  for (const account of accounts) {
    let debitBalance = 0;
    let creditBalance = 0;

    // Add transaction amounts
    transactions.forEach(transaction => {
      if (transaction.account._id.toString() === account._id.toString()) {
        if (transaction.type === 'debit') {
          debitBalance += transaction.amount;
        } else if (transaction.type === 'credit') {
          creditBalance += transaction.amount;
        }
      }
    });

    // Add journal entry amounts
    journalEntries.forEach(entry => {
      entry.items.forEach((item: { accountId: { _id: mongoose.Types.ObjectId }; debit: number; credit: number }) => {
        if (item.accountId._id.toString() === account._id.toString()) {
          debitBalance += item.debit;
          creditBalance += item.credit;
        }
      });
    });

    // Calculate net balance
    let netDebit = 0;
    let netCredit = 0;

    // For asset and expense accounts, debit increases and credit decreases
    if (account.type === 'asset' || account.type === 'expense') {
      if (debitBalance > creditBalance) {
        netDebit = debitBalance - creditBalance;
      } else {
        netCredit = creditBalance - debitBalance;
      }
    }
    // For liability, equity, and revenue accounts, credit increases and debit decreases
    else if (account.type === 'liability' || account.type === 'equity' || account.type === 'revenue') {
      if (creditBalance > debitBalance) {
        netCredit = creditBalance - debitBalance;
      } else {
        netDebit = debitBalance - creditBalance;
      }
    }

    // Only include accounts with non-zero balances unless includeZeroBalances is true
    if (netDebit !== 0 || netCredit !== 0 || options.includeZeroBalances) {
      accountBalances.push({
        accountId: account._id,
        code: account.code,
        name: account.name,
        type: account.type,
        subtype: account.subtype,
        debit: netDebit,
        credit: netCredit
      });

      totalDebit += netDebit;
      totalCredit += netCredit;
    }
  }

  // Check if debits equal credits
  const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01; // Allow for small rounding errors

  // Return trial balance data
  return {
    accounts: accountBalances,
    totalDebit,
    totalCredit,
    isBalanced
  };
}

// app/api/accounting/expense/by-budget/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';
import Expense from '@/models/accounting/Expense';
import {Budget} from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';


// MongoDB query type definition
interface MongoBaseFilter {
  [key: string]: any;
}


export const runtime = 'nodejs';



/**
 * GET handler for retrieving expense transactions filtered by budget
 * @param req The request object
 * @param params The route parameters containing id
 * @returns A response with the expense data
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Resolve the params promise
    const { id } = await params;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Budget ID is already extracted from params

    // Validate budget ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid budget ID' },
        { status: 400 }
      );
    }

    // Check if budget exists
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const minAmount = searchParams.get('minAmount') ? parseFloat(searchParams.get('minAmount')!) : null;
    const maxAmount = searchParams.get('maxAmount') ? parseFloat(searchParams.get('maxAmount')!) : null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build filter
    const filter: Record<string, any> = {
      budget: new mongoose.Types.ObjectId(id),
      appliedToBudget: true
    };

    // Add budget category filter if provided
    if (categoryId && mongoose.Types.ObjectId.isValid(categoryId)) {
      filter.budgetCategory = new mongoose.Types.ObjectId(categoryId);
    }

    // Add budget subcategory filter if provided
    if (subcategoryId && mongoose.Types.ObjectId.isValid(subcategoryId)) {
      filter.budgetSubcategory = new mongoose.Types.ObjectId(subcategoryId);
    }

    // Add expense category filter if provided
    if (category) {
      filter.category = category;
    }

    // Add status filter if provided
    if (status) {
      filter.status = status;
    }

    // Add date range filters if provided
    if (startDate) {
      filter.date = { $gte: new Date(startDate) };
    }

    if (endDate) {
      if (filter.date) {
        filter.date.$lte = new Date(endDate);
      } else {
        filter.date = { $lte: new Date(endDate) };
      }
    }

    // Add amount range filters if provided
    if (minAmount !== null) {
      filter.amount = { $gte: minAmount };
    }

    if (maxAmount !== null) {
      if (filter.amount) {
        filter.amount.$lte = maxAmount;
      } else {
        filter.amount = { $lte: maxAmount };
      }
    }

    // Determine sort options
    const sortOptions: { [key: string]: 1 | -1 } = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await Expense.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);

    // Get expense transactions
    const expenses = await Expense.find(filter)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .populate('bankAccount', 'name accountNumber')
      .lean();

    // Get expense summary by category
    const categorySummary = await Expense.aggregate([
      { $match: filter },
      { $group: { _id: '$budgetCategory', total: { $sum: '$amount' } } },
      {
        $lookup: {
          from: 'budgetcategories',
          localField: '_id',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: { path: '$category', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: 1,
          categoryId: '$_id',
          categoryName: '$category.name',
          budgeted: '$category.budgetedAmount',
          actual: '$total'
        }
      }
    ]);

    // Calculate total expenses
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Get budget categories for this budget
    const budgetCategories = await BudgetCategory.find({
      budget: new mongoose.Types.ObjectId(id),
      type: 'expense'
    }).lean();

    // Get expense summary by expense category
    const expenseCategorySummary = await Expense.aggregate([
      { $match: filter },
      { $group: { _id: '$category', total: { $sum: '$amount' } } },
      {
        $project: {
          _id: 0,
          category: '$_id',
          total: 1
        }
      }
    ]);

    // Get BudgetExpenditure records for additional insights
    const budgetExpenditures = await BudgetExpenditure.find({
      budget: new mongoose.Types.ObjectId(id),
      status: { $in: ['approved', 'paid'] }
    })
      .populate('budgetCategory', 'name type budgetedAmount actualAmount')
      .populate('sourceExpenditure', 'reference description category')
      .sort({ date: -1 })
      .lean();

    // Calculate budget utilization
    const budgetUtilization = budgetCategories.map(category => {
      const categoryExpenses = expenses.filter(
        expense => expense.budgetCategory?._id?.toString() === category._id.toString()
      );

      const totalSpent = categoryExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      const utilization = category.budgetedAmount > 0 ? (totalSpent / category.budgetedAmount) * 100 : 0;

      return {
        categoryId: category._id,
        categoryName: category.name,
        budgetedAmount: category.budgetedAmount,
        actualAmount: totalSpent,
        utilization,
        variance: totalSpent - category.budgetedAmount,
        isOverBudget: totalSpent > category.budgetedAmount,
        expenseCount: categoryExpenses.length
      };
    });

    // Calculate overall budget performance (only expense categories)
    const totalBudgeted = budgetCategories
      .filter(cat => cat.type === 'expense')
      .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);
    const overallUtilization = totalBudgeted > 0 ? (totalExpenses / totalBudgeted) * 100 : 0;
    const overallVariance = totalExpenses - totalBudgeted;

    // Get monthly expense trends
    const monthlyTrends = await Expense.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Return enhanced expense data
    return NextResponse.json({
      success: true,
      expenses,
      budgetExpenditures,
      categorySummary,
      expenseCategorySummary,
      budgetUtilization,
      monthlyTrends,
      totalExpenses,
      budget: {
        id: budget._id,
        name: budget.name,
        fiscalYear: budget.fiscalYear,
        totalBudgeted,
        totalActualExpense: totalExpenses,
        utilization: overallUtilization,
        variance: overallVariance,
        isOverBudget: overallVariance > 0
      },
      categories: budgetCategories,
      summary: {
        totalExpenses,
        totalBudgeted,
        utilization: overallUtilization,
        variance: overallVariance,
        categoriesCount: budgetCategories.length,
        expensesCount: expenses.length
      },
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching expenses by budget', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

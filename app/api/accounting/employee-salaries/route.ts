import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/employee-salaries
 * Get employee salaries with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const employeeId = searchParams.get('employeeId');
    const isActive = searchParams.get('isActive');
    const salaryStructureId = searchParams.get('salaryStructureId');

    // Build query
    const query: Record<string, unknown> = {};

    if (employeeId) query.employeeId = new mongoose.Types.ObjectId(employeeId);
    if (isActive !== null) query.isActive = isActive === 'true';
    if (salaryStructureId) query.salaryStructureId = new mongoose.Types.ObjectId(salaryStructureId);

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const [employeeSalaries, total] = await Promise.all([
      EmployeeSalary.find(query)
        .sort({ effectiveDate: -1 })
        .skip(skip)
        .limit(limit)
        .populate('employeeId', 'firstName lastName employeeId email department position')
        .populate('salaryStructureId', 'name')
        .populate('taxConfigurationId', 'name')
        .populate('createdBy', 'name')
        .lean(),
      EmployeeSalary.countDocuments(query)
    ]);

    // Return results
    return NextResponse.json({
      employeeSalaries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching employee salaries:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/employee-salaries
 * Create a new employee salary
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.employeeId || !body.basicSalary || !body.effectiveDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // If isActive is true, deactivate other active salaries for the employee
    if (body.isActive) {
      await EmployeeSalary.updateMany(
        { employeeId: body.employeeId, isActive: true },
        { $set: { isActive: false, endDate: new Date() } }
      );
    }

    // Create employee salary
    const employeeSalary = new EmployeeSalary(body);
    await employeeSalary.save();

    // Return created employee salary
    return NextResponse.json(
      {
        success: true,
        message: 'Employee salary created successfully',
        data: employeeSalary
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating employee salary:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/employee-salaries
 * Update an employee salary
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Employee salary ID is required' },
        { status: 400 }
      );
    }

    // Find employee salary
    const employeeSalary = await EmployeeSalary.findById(body.id);
    if (!employeeSalary) {
      return NextResponse.json(
        { error: 'Employee salary not found' },
        { status: 404 }
      );
    }

    // Set updated by
    body.updatedBy = user.id;

    // If isActive is changing from false to true, deactivate other active salaries for the employee
    if (!employeeSalary.isActive && body.isActive) {
      await EmployeeSalary.updateMany(
        { employeeId: employeeSalary.employeeId, isActive: true },
        { $set: { isActive: false, endDate: new Date() } }
      );
    }

    // Update employee salary
    const updatedEmployeeSalary = await EmployeeSalary.findByIdAndUpdate(
      body.id,
      { $set: body },
      { new: true, runValidators: true }
    );

    // Return updated employee salary
    return NextResponse.json({
      success: true,
      message: 'Employee salary updated successfully',
      data: updatedEmployeeSalary
    });
  } catch (error: unknown) {
    logger.error('Error updating employee salary:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

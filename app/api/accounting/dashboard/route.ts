import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const from = searchParams.get('from');
    const to = searchParams.get('to');
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';

    // Mock data for now - in a real implementation, this would fetch from the database
    const dashboardData = {
      summary: {
        totalIncome: 5448434000,
        totalExpenses: 5448434000,
        budgetUtilization: 68.2,
        feeCollection: 1752075100,
      },
      incomeData: [
        { name: 'Government Subvention', value: 3696358900 },
        { name: 'Registration Fees', value: 210000000 },
        { name: 'Licensing Fees', value: 1542075100 },
        { name: 'Donations', value: 0 }
      ],
      expenseData: [
        { name: 'Administrative', value: 2579432681.25 },
        { name: 'Conferences', value: 299000000 },
        { name: 'Governance', value: 388160000 },
        { name: 'Human Resource', value: 1068341318.75 },
        { name: 'Inspection', value: 250000000 },
        { name: 'Institutional Dev', value: 605000000 },
        { name: 'Publicity', value: 110000000 },
        { name: 'Sensitization', value: 148500000 }
      ],
      budgetVsActualData: [
        { name: 'Administrative', budget: 2579432681.25, actual: 1850000000 },
        { name: 'Conferences', budget: 299000000, actual: 220000000 },
        { name: 'Governance', budget: 388160000, actual: 310000000 },
        { name: 'Human Resource', budget: 1068341318.75, actual: 980000000 },
        { name: 'Inspection', budget: 250000000, actual: 180000000 },
        { name: 'Institutional Dev', budget: 605000000, actual: 450000000 },
        { name: 'Publicity', budget: 110000000, actual: 95000000 },
        { name: 'Sensitization', budget: 148500000, actual: 120000000 }
      ],
      trendData: [
        { month: 'Jan', income: 450000000, expenses: 380000000 },
        { month: 'Feb', income: 470000000, expenses: 420000000 },
        { month: 'Mar', income: 520000000, expenses: 460000000 },
        { month: 'Apr', income: 480000000, expenses: 440000000 },
        { month: 'May', income: 510000000, expenses: 470000000 },
        { month: 'Jun', income: 530000000, expenses: 490000000 }
      ],
      recentTransactions: [
        { id: 1, date: '2025-06-15', type: 'income', description: 'Government Subvention', amount: 924089725 },
        { id: 2, date: '2025-06-12', type: 'expense', description: 'Administrative Expenses', amount: -156000000 },
        { id: 3, date: '2025-06-10', type: 'income', description: 'Licensing Fees', amount: 85000000 },
        { id: 4, date: '2025-06-05', type: 'expense', description: 'Payroll', amount: -54321543 }
      ]
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

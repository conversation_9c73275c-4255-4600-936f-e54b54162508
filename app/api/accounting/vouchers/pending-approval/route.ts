import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { VoucherApprovalService } from '@/lib/services/accounting/voucher-approval-service';

export const runtime = 'nodejs';

/**
 * Get pending voucher approvals for the current user
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has approval permissions
    const approvalRoles = ['HR_MANAGER', 'FINANCE_MANAGER', 'SUPER_ADMIN', 'SYSTEM_ADMIN'];
    if (!approvalRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: User does not have approval permissions' },
        { status: 403 }
      );
    }

    // Create approval service instance
    const approvalService = new VoucherApprovalService();

    // Get pending approvals for the user
    const pendingVouchers = await approvalService.getPendingApprovals(user.id, user.role);

    // Format response data
    const formattedVouchers = pendingVouchers.map(voucher => ({
      id: voucher._id,
      voucherNumber: voucher.voucherNumber,
      voucherType: voucher.voucherType,
      date: voucher.date,
      description: voucher.description,
      totalAmount: voucher.totalAmount,
      status: voucher.status,
      voucherCategory: voucher.voucherCategory,
      payee: voucher.payee,
      paymentMethod: voucher.paymentMethod,
      fiscalYear: voucher.fiscalYear,
      createdBy: voucher.createdBy,
      createdAt: voucher.createdAt,
      payrollRun: voucher.payrollRunId ? {
        id: voucher.payrollRunId._id,
        name: voucher.payrollRunId.name,
        payPeriod: voucher.payrollRunId.payPeriod
      } : null,
      approvalWorkflow: {
        currentLevel: voucher.approvalWorkflow?.currentLevel,
        workflowType: voucher.approvalWorkflow?.workflowType,
        currentApprover: voucher.approvalWorkflow?.currentApprover,
        approvalHistory: voucher.approvalWorkflow?.approvalHistory || []
      }
    }));

    // Group vouchers by priority/urgency
    const groupedVouchers = {
      urgent: formattedVouchers.filter(v => 
        v.voucherCategory === 'payroll' || v.totalAmount > 5000000
      ),
      normal: formattedVouchers.filter(v => 
        v.voucherCategory !== 'payroll' && v.totalAmount <= 5000000
      )
    };

    // Calculate summary statistics
    const summary = {
      totalPending: formattedVouchers.length,
      urgentCount: groupedVouchers.urgent.length,
      normalCount: groupedVouchers.normal.length,
      totalAmount: formattedVouchers.reduce((sum, v) => sum + v.totalAmount, 0),
      byCategory: {
        payroll: formattedVouchers.filter(v => v.voucherCategory === 'payroll').length,
        general: formattedVouchers.filter(v => v.voucherCategory === 'general').length,
        procurement: formattedVouchers.filter(v => v.voucherCategory === 'procurement').length,
        expense: formattedVouchers.filter(v => v.voucherCategory === 'expense').length,
      },
      byType: {
        payment: formattedVouchers.filter(v => v.voucherType === 'payment').length,
        receipt: formattedVouchers.filter(v => v.voucherType === 'receipt').length,
        journal: formattedVouchers.filter(v => v.voucherType === 'journal').length,
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        vouchers: formattedVouchers,
        groupedVouchers,
        summary,
        userRole: user.role,
        permissions: {
          canApprovePayroll: user.role === 'HR_MANAGER' || user.role === 'SUPER_ADMIN',
          canApproveFinance: ['FINANCE_MANAGER', 'SUPER_ADMIN'].includes(user.role),
          canApproveAll: user.role === 'SUPER_ADMIN'
        }
      }
    });

  } catch (error: any) {
    console.error('Error getting pending approvals:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Bulk approve vouchers
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has approval permissions
    const approvalRoles = ['HR_MANAGER', 'FINANCE_MANAGER', 'SUPER_ADMIN', 'SYSTEM_ADMIN'];
    if (!approvalRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: User does not have approval permissions' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { voucherIds, comments } = body;

    if (!Array.isArray(voucherIds) || voucherIds.length === 0) {
      return NextResponse.json(
        { error: 'Bad Request: voucherIds must be a non-empty array' },
        { status: 400 }
      );
    }

    // Limit bulk operations
    if (voucherIds.length > 50) {
      return NextResponse.json(
        { error: 'Bad Request: Cannot approve more than 50 vouchers at once' },
        { status: 400 }
      );
    }

    // Create approval service instance
    const approvalService = new VoucherApprovalService();

    // Process bulk approval
    const results = await approvalService.bulkApprove(voucherIds, user.id, comments);

    return NextResponse.json({
      success: true,
      message: `Bulk approval completed. ${results.successful.length} approved, ${results.failed.length} failed.`,
      data: {
        successful: results.successful,
        failed: results.failed,
        summary: {
          totalProcessed: voucherIds.length,
          successfulCount: results.successful.length,
          failedCount: results.failed.length,
          successRate: ((results.successful.length / voucherIds.length) * 100).toFixed(2) + '%'
        }
      }
    });

  } catch (error: any) {
    console.error('Error processing bulk approval:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { VoucherService } from '@/lib/services/accounting/voucher-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for export request
const exportRequestSchema = z.object({
  format: z.enum(['pdf', 'excel']),
  voucherType: z.enum(['payment', 'receipt', 'journal']).optional(),
  status: z.string().optional(),
  dateRange: z.object({
    startDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid start date format",
    }),
    endDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid end date format",
    }),
  }).optional(),
  includeItems: z.boolean().default(false),
});

/**
 * Export vouchers to PDF or Excel
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'SYSTEM_ADMIN', 'FINANCE_MANAGER', 'ACCOUNTANT'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to export vouchers' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = exportRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { format, voucherType, status, dateRange, includeItems } = validationResult.data;

    // Prepare export options
    const exportOptions = {
      format,
      voucherType,
      status,
      includeItems,
      dateRange: dateRange ? {
        startDate: new Date(dateRange.startDate),
        endDate: new Date(dateRange.endDate)
      } : undefined
    };

    // Create voucher service instance
    const voucherService = new VoucherService();

    // Generate export based on format
    let buffer: Buffer;
    let filename: string;
    let contentType: string;

    if (format === 'pdf') {
      buffer = await voucherService.exportToPdf(exportOptions);
      filename = `vouchers_export_${new Date().toISOString().split('T')[0]}.pdf`;
      contentType = 'application/pdf';
    } else {
      buffer = await voucherService.exportToExcel(exportOptions);
      filename = `vouchers_export_${new Date().toISOString().split('T')[0]}.xlsx`;
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }

    // Return file as download
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error: any) {
    console.error('Error exporting vouchers:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Get export options and statistics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'SYSTEM_ADMIN', 'FINANCE_MANAGER', 'ACCOUNTANT'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view voucher export options' },
        { status: 403 }
      );
    }

    // Create voucher service instance
    const voucherService = new VoucherService();

    // Get voucher statistics for export options
    const { vouchers } = await voucherService.getVouchers({}, { page: 1, limit: 1 });
    const totalVouchers = vouchers.length;

    // Get voucher counts by type and status
    const { vouchers: allVouchers } = await voucherService.getVouchers({}, { page: 1, limit: 10000 });
    
    const statistics = {
      totalVouchers: allVouchers.length,
      byType: {
        payment: allVouchers.filter(v => v.voucherType === 'payment').length,
        receipt: allVouchers.filter(v => v.voucherType === 'receipt').length,
        journal: allVouchers.filter(v => v.voucherType === 'journal').length,
      },
      byStatus: {
        draft: allVouchers.filter(v => v.status === 'draft').length,
        pending_approval: allVouchers.filter(v => v.status === 'pending_approval').length,
        approved: allVouchers.filter(v => v.status === 'approved').length,
        posted: allVouchers.filter(v => v.status === 'posted').length,
        rejected: allVouchers.filter(v => v.status === 'rejected').length,
        cancelled: allVouchers.filter(v => v.status === 'cancelled').length,
      },
      byCategory: {
        payroll: allVouchers.filter(v => v.voucherCategory === 'payroll').length,
        general: allVouchers.filter(v => v.voucherCategory === 'general').length,
        procurement: allVouchers.filter(v => v.voucherCategory === 'procurement').length,
        expense: allVouchers.filter(v => v.voucherCategory === 'expense').length,
      },
      totalAmount: allVouchers.reduce((sum, v) => sum + v.totalAmount, 0),
      dateRange: {
        earliest: allVouchers.length > 0 ? 
          new Date(Math.min(...allVouchers.map(v => new Date(v.date).getTime()))) : null,
        latest: allVouchers.length > 0 ? 
          new Date(Math.max(...allVouchers.map(v => new Date(v.date).getTime()))) : null,
      }
    };

    const exportOptions = {
      formats: ['pdf', 'excel'],
      voucherTypes: ['payment', 'receipt', 'journal'],
      statuses: ['draft', 'pending_approval', 'approved', 'posted', 'rejected', 'cancelled'],
      categories: ['payroll', 'general', 'procurement', 'expense'],
      includeItemsOption: true,
      maxExportLimit: 10000,
    };

    return NextResponse.json({
      success: true,
      data: {
        exportOptions,
        statistics
      }
    });

  } catch (error: any) {
    console.error('Error getting export options:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

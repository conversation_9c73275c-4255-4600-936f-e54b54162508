import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { VoucherApprovalService } from '@/lib/services/accounting/voucher-approval-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for approval request
const approvalRequestSchema = z.object({
  action: z.enum(['approve', 'reject']),
  comments: z.string().optional(),
});

/**
 * Process voucher approval or rejection
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve params
    const { id: voucherId } = await params;

    // Parse and validate request body
    const body = await request.json();
    const validationResult = approvalRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { action, comments } = validationResult.data;

    // Create approval service instance
    const approvalService = new VoucherApprovalService();

    // Process approval
    const updatedVoucher = await approvalService.processApproval({
      voucherId,
      approverId: user.id,
      action,
      comments
    });

    return NextResponse.json({
      success: true,
      message: `Voucher ${action}d successfully`,
      data: {
        voucher: {
          id: updatedVoucher._id,
          voucherNumber: updatedVoucher.voucherNumber,
          status: updatedVoucher.status,
          approvalWorkflow: updatedVoucher.approvalWorkflow,
          approvedBy: updatedVoucher.approvedBy,
          approvedAt: updatedVoucher.approvedAt
        }
      }
    });

  } catch (error: any) {
    console.error('Error processing voucher approval:', error);
    
    // Handle specific error types
    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }
    
    if (error.message.includes('not authorized') || error.message.includes('authorization')) {
      return NextResponse.json(
        { error: 'Forbidden: ' + error.message },
        { status: 403 }
      );
    }
    
    if (error.message.includes('not in pending approval')) {
      return NextResponse.json(
        { error: 'Bad Request: ' + error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Get approval workflow status for a voucher
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve params
    const { id: voucherId } = await params;

    // Create approval service instance
    const approvalService = new VoucherApprovalService();

    // Get approval workflow status
    const workflowStatus = await approvalService.getApprovalWorkflowStatus(voucherId);

    return NextResponse.json({
      success: true,
      data: workflowStatus
    });

  } catch (error: any) {
    console.error('Error getting approval workflow status:', error);
    
    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

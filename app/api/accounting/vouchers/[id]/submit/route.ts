import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/vouchers/[id]/submit
 * Submit a voucher for approval
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to submit vouchers for approval.',
        {
          endpoint: '/api/accounting/vouchers/[id]/submit',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Parse request body
    const body = await req.json().catch(() => ({}));
    const { comments } = body;

    // Get voucher
    const voucher = await Voucher.findById(id);

    if (!voucher) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'VOUCHER_NOT_FOUND',
        'Voucher not found',
        'The specified voucher could not be found.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          endpoint: '/api/accounting/vouchers/[id]/submit',
          method: 'POST'
        },
        404,
        ErrorSeverity.LOW
      );
    }

    // Check if voucher can be submitted
    const submittableStatuses = ['draft', 'rejected'];
    if (!submittableStatuses.includes(voucher.status)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'VOUCHER_CANNOT_BE_SUBMITTED',
        'Voucher cannot be submitted',
        `Vouchers with status '${voucher.status}' cannot be submitted for approval. Only draft or rejected vouchers can be submitted.`,
        {
          userId: user._id?.toString(),
          voucherId: id,
          voucherNumber: voucher.voucherNumber,
          currentStatus: voucher.status,
          submittableStatuses,
          endpoint: '/api/accounting/vouchers/[id]/submit',
          method: 'POST'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Check if user can submit this voucher
    const canSubmit = 
      voucher.createdBy?.toString() === user._id?.toString() ||
      voucher.createdBy?.toString() === user.id ||
      ['SUPER_ADMIN', 'SYSTEM_ADMIN', 'FINANCE_MANAGER'].includes(user.role);

    if (!canSubmit) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CANNOT_SUBMIT_VOUCHER',
        'Cannot submit voucher',
        'You can only submit vouchers that you created, or you must have appropriate permissions.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          voucherNumber: voucher.voucherNumber,
          voucherCreatedBy: voucher.createdBy?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/vouchers/[id]/submit',
          method: 'POST'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Validate voucher data before submission
    const validationErrors = [];

    if (!voucher.description || voucher.description.trim().length < 3) {
      validationErrors.push('Description must be at least 3 characters long');
    }

    if (!voucher.totalAmount || voucher.totalAmount <= 0) {
      validationErrors.push('Total amount must be greater than 0');
    }

    if (!voucher.date) {
      validationErrors.push('Voucher date is required');
    }

    if (!voucher.voucherType) {
      validationErrors.push('Voucher type is required');
    }

    if (validationErrors.length > 0) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'VOUCHER_VALIDATION_FAILED',
        'Voucher validation failed',
        'The voucher has validation errors that must be fixed before submission.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          voucherNumber: voucher.voucherNumber,
          validationErrors,
          endpoint: '/api/accounting/vouchers/[id]/submit',
          method: 'POST'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Store original status for audit
    const originalStatus = voucher.status;

    // Submit the voucher for approval
    voucher.status = 'pending_approval';
    voucher.updatedBy = user._id?.toString() || user.id;
    voucher.updatedAt = new Date();

    // Add submission comments to notes
    if (comments) {
      voucher.notes = voucher.notes 
        ? `${voucher.notes}\n\nSubmission Comments: ${comments}`
        : `Submission Comments: ${comments}`;
    }

    // Add audit trail
    voucher.notes = voucher.notes 
      ? `${voucher.notes}\n\nSubmitted for approval by ${user.name || user.email} on ${new Date().toISOString()} (Previous status: ${originalStatus})`
      : `Submitted for approval by ${user.name || user.email} on ${new Date().toISOString()} (Previous status: ${originalStatus})`;

    await voucher.save();

    logger.info('Voucher submitted for approval', LogCategory.ACCOUNTING, {
      userId: user._id?.toString(),
      voucherId: id,
      voucherNumber: voucher.voucherNumber,
      originalStatus,
      submittedAt: new Date().toISOString(),
      comments
    });

    // TODO: Trigger approval workflow notification
    // This could include:
    // - Sending email notifications to approvers
    // - Creating approval workflow records
    // - Setting up approval deadlines

    return NextResponse.json({
      success: true,
      message: `Voucher ${voucher.voucherNumber} has been submitted for approval`,
      data: {
        id: voucher._id,
        voucherNumber: voucher.voucherNumber,
        status: voucher.status,
        originalStatus,
        submittedAt: voucher.updatedAt,
        submittedBy: user.name || user.email,
        comments
      }
    });

  } catch (error: any) {
    logger.error('Error submitting voucher for approval', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUBMIT_VOUCHER_FAILED',
      'Failed to submit voucher',
      'An error occurred while submitting the voucher for approval. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/vouchers/[id]/submit',
        method: 'POST',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for voucher item
const voucherItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  account: z.string().min(1, 'Account is required'),
  debit: z.number().min(0, 'Debit amount cannot be negative'),
  credit: z.number().min(0, 'Credit amount cannot be negative'),
  costCenter: z.string().optional().nullable(),
  project: z.string().optional().nullable(),
  department: z.string().optional().nullable(),
  notes: z.string().optional().nullable()
});

// Validation schema for updating voucher
const updateVoucherSchema = z.object({
  date: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid date' }
  ).optional(),
  description: z.string().min(3, 'Description must be at least 3 characters long').optional(),
  reference: z.string().optional().nullable(),
  payee: z.string().optional().nullable(),
  paymentMethod: z.enum(['cash', 'check', 'bank_transfer', 'credit_card', 'other']).optional(),
  checkNumber: z.string().optional().nullable(),
  items: z.array(voucherItemSchema).min(1, 'At least one item is required').optional(),
  currency: z.string().optional(),
  exchangeRate: z.number().positive().optional(),
  notes: z.string().optional().nullable(),
  attachments: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'pending', 'approved', 'rejected', 'posted', 'voided']).optional()
});

/**
 * GET /api/accounting/vouchers/[id]
 * Get a voucher by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get voucher - skip payrollRunId population to avoid schema registration issues
    const voucher = await Voucher.findById(id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .populate('postedBy', 'firstName lastName email');

    if (!voucher) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: voucher
    });
  } catch (error: unknown) {
    logger.error('Error getting voucher', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/vouchers/[id]
 * Update a voucher
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get voucher
    const voucher = await Voucher.findById(id);

    if (!voucher) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }

    // Check if voucher can be updated
    if (voucher.status === 'posted' || voucher.status === 'voided') {
      return NextResponse.json(
        { error: `Vouchers with status '${voucher.status}' cannot be updated` },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = updateVoucherSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Validate accounts if items are provided
    if (validationResult.data.items) {
      const accountIds = validationResult.data.items.map(item => item.account);
      const accounts = await Account.find({ _id: { $in: accountIds } });
      
      if (accounts.length !== accountIds.length) {
        return NextResponse.json(
          { error: 'One or more accounts not found' },
          { status: 400 }
        );
      }

      // Validate debits and credits balance
      const totalDebit = validationResult.data.items.reduce((sum, item) => sum + item.debit, 0);
      const totalCredit = validationResult.data.items.reduce((sum, item) => sum + item.credit, 0);
      
      if (Math.abs(totalDebit - totalCredit) > 0.01) { // Allow for small rounding errors
        return NextResponse.json(
          { error: 'Debits and credits must balance' },
          { status: 400 }
        );
      }
    }

    // Update voucher fields
    if (validationResult.data.date) {
      voucher.date = new Date(validationResult.data.date);
    }
    
    if (validationResult.data.description !== undefined) {
      voucher.description = validationResult.data.description;
    }
    
    if (validationResult.data.reference !== undefined) {
      voucher.reference = validationResult.data.reference;
    }
    
    if (validationResult.data.payee !== undefined) {
      voucher.payee = validationResult.data.payee;
    }
    
    if (validationResult.data.paymentMethod !== undefined) {
      voucher.paymentMethod = validationResult.data.paymentMethod;
    }
    
    if (validationResult.data.checkNumber !== undefined) {
      voucher.checkNumber = validationResult.data.checkNumber;
    }
    
    if (validationResult.data.items) {
      voucher.items = validationResult.data.items.map(item => ({
        description: item.description,
        account: new mongoose.Types.ObjectId(item.account),
        debit: item.debit,
        credit: item.credit,
        costCenter: item.costCenter ? new mongoose.Types.ObjectId(item.costCenter) : undefined,
        project: item.project ? new mongoose.Types.ObjectId(item.project) : undefined,
        department: item.department ? new mongoose.Types.ObjectId(item.department) : undefined,
        notes: item.notes
      }));
    }
    
    if (validationResult.data.currency !== undefined) {
      voucher.currency = validationResult.data.currency;
    }
    
    if (validationResult.data.exchangeRate !== undefined) {
      voucher.exchangeRate = validationResult.data.exchangeRate;
    }
    
    if (validationResult.data.notes !== undefined) {
      voucher.notes = validationResult.data.notes;
    }
    
    if (validationResult.data.attachments !== undefined) {
      voucher.attachments = validationResult.data.attachments;
    }
    
    if (validationResult.data.tags !== undefined) {
      voucher.tags = validationResult.data.tags;
    }
    
    if (validationResult.data.status !== undefined) {
      // If changing to approved status, set approvedBy and approvedAt
      if (validationResult.data.status === 'approved' && voucher.status !== 'approved') {
        voucher.approvedBy = user._id?.toString() || user.id;
        voucher.approvedAt = new Date();
      }
      
      voucher.status = validationResult.data.status;
    }

    // Update updatedBy and updatedAt
    voucher.updatedBy = user._id?.toString() || user.id;
    voucher.updatedAt = new Date();

    // Save changes
    await voucher.save();

    return NextResponse.json({
      success: true,
      message: 'Voucher updated successfully',
      data: voucher
    });
  } catch (error: unknown) {
    logger.error('Error updating voucher', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/vouchers/[id]
 * Delete (void) a voucher
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get voucher
    const voucher = await Voucher.findById(id);

    if (!voucher) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }

    // Check if voucher can be voided
    if (voucher.status === 'voided') {
      return NextResponse.json(
        { error: 'Voucher is already voided' },
        { status: 400 }
      );
    }

    // Check if it's a hard delete request
    const body = await req.json().catch(() => ({}));
    const { deleteType = 'soft', reason } = body;

    if (deleteType === 'hard' && voucher.status === 'draft') {
      // Hard delete for draft vouchers
      await Voucher.findByIdAndDelete(id);

      return NextResponse.json({
        success: true,
        message: 'Voucher permanently deleted'
      });
    } else {
      // Soft delete (void) the voucher
      voucher.status = 'voided';
      voucher.updatedBy = user._id?.toString() || user.id;
      voucher.updatedAt = new Date();

      // Add deletion reason if provided
      if (reason) {
        voucher.notes = voucher.notes
          ? `${voucher.notes}\n\nDeletion Reason: ${reason}`
          : `Deletion Reason: ${reason}`;
      }

      await voucher.save();

      return NextResponse.json({
        success: true,
        message: 'Voucher voided successfully'
      });
    }
  } catch (error: unknown) {
    logger.error('Error voiding voucher', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

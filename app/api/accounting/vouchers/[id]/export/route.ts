import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/vouchers/[id]/export
 * Export a single voucher in PDF or Excel format
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to export vouchers.',
        {
          endpoint: '/api/accounting/vouchers/[id]/export',
          method: 'GET'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get format from query parameters
    const { searchParams } = new URL(req.url);
    const format = searchParams.get('format') || 'pdf';

    // Validate format
    if (!['pdf', 'excel'].includes(format)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_FORMAT',
        'Invalid export format',
        'Supported formats are: pdf, excel',
        {
          userId: user._id?.toString(),
          voucherId: id,
          providedFormat: format,
          endpoint: '/api/accounting/vouchers/[id]/export',
          method: 'GET'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Get voucher with populated references
    const voucher = await Voucher.findById(id)
      .populate('bankAccount', 'accountName accountNumber')
      .populate('expense', 'description amount')
      .populate('income', 'description amount')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .lean();

    if (!voucher) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'VOUCHER_NOT_FOUND',
        'Voucher not found',
        'The specified voucher could not be found.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          endpoint: '/api/accounting/vouchers/[id]/export',
          method: 'GET'
        },
        404,
        ErrorSeverity.LOW
      );
    }

    logger.info('Exporting voucher', LogCategory.ACCOUNTING, {
      userId: user._id?.toString(),
      voucherId: id,
      voucherNumber: voucher.voucherNumber,
      format
    });

    // Prepare voucher data for export
    const exportData = {
      'Voucher Number': voucher.voucherNumber,
      'Voucher Type': voucher.voucherType?.toUpperCase(),
      'Date': voucher.date ? new Date(voucher.date).toLocaleDateString() : '',
      'Description': voucher.description,
      'Total Amount': voucher.totalAmount,
      'Status': voucher.status?.toUpperCase(),
      'Fiscal Year': voucher.fiscalYear,
      'Payee': voucher.payee || '',
      'Payment Method': voucher.paymentMethod || '',
      'Reference': voucher.reference || '',
      'Notes': voucher.notes || '',
      'Created By': voucher.createdBy?.name || voucher.createdBy?.email || '',
      'Created Date': voucher.createdAt ? new Date(voucher.createdAt).toLocaleDateString() : '',
      'Updated By': voucher.updatedBy?.name || voucher.updatedBy?.email || '',
      'Updated Date': voucher.updatedAt ? new Date(voucher.updatedAt).toLocaleDateString() : '',
    };

    // Add bank account info if available
    if (voucher.bankAccount) {
      exportData['Bank Account'] = `${voucher.bankAccount.accountName} (${voucher.bankAccount.accountNumber})`;
    }

    // Generate file based on format
    let fileBuffer: Buffer;
    let contentType: string;
    let fileName: string;

    if (format === 'excel') {
      // Create Excel workbook
      const workbook = XLSX.utils.book_new();
      
      // Create voucher details sheet
      const voucherSheet = XLSX.utils.json_to_sheet([exportData]);
      
      // Set column widths
      voucherSheet['!cols'] = [
        { wch: 20 }, // Field names
        { wch: 40 }, // Values
      ];
      
      XLSX.utils.book_append_sheet(workbook, voucherSheet, 'Voucher Details');

      // TODO: Add voucher items sheet if items are available
      // if (voucher.items && voucher.items.length > 0) {
      //   const itemsSheet = XLSX.utils.json_to_sheet(voucher.items);
      //   XLSX.utils.book_append_sheet(workbook, itemsSheet, 'Voucher Items');
      // }

      fileBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileName = `voucher-${voucher.voucherNumber}.xlsx`;
    } else {
      // For PDF, create a simple text-based format
      // In a real implementation, you might use a library like PDFKit or Puppeteer
      const pdfContent = `
VOUCHER DETAILS
===============

Voucher Number: ${voucher.voucherNumber}
Voucher Type: ${voucher.voucherType?.toUpperCase()}
Date: ${voucher.date ? new Date(voucher.date).toLocaleDateString() : ''}
Status: ${voucher.status?.toUpperCase()}

FINANCIAL DETAILS
=================
Description: ${voucher.description}
Total Amount: ${new Intl.NumberFormat('en-MW', { style: 'currency', currency: 'MWK' }).format(voucher.totalAmount)}
Fiscal Year: ${voucher.fiscalYear}
Payee: ${voucher.payee || 'N/A'}
Payment Method: ${voucher.paymentMethod || 'N/A'}
Reference: ${voucher.reference || 'N/A'}

${voucher.bankAccount ? `Bank Account: ${voucher.bankAccount.accountName} (${voucher.bankAccount.accountNumber})` : ''}

ADDITIONAL INFORMATION
======================
Notes: ${voucher.notes || 'None'}

AUDIT TRAIL
===========
Created By: ${voucher.createdBy?.name || voucher.createdBy?.email || 'Unknown'}
Created Date: ${voucher.createdAt ? new Date(voucher.createdAt).toLocaleDateString() : ''}
Updated By: ${voucher.updatedBy?.name || voucher.updatedBy?.email || 'Unknown'}
Updated Date: ${voucher.updatedAt ? new Date(voucher.updatedAt).toLocaleDateString() : ''}

Generated on: ${new Date().toLocaleDateString()} by ${user.name || user.email}
      `.trim();
      
      fileBuffer = Buffer.from(pdfContent, 'utf-8');
      contentType = 'text/plain'; // In real implementation, this would be 'application/pdf'
      fileName = `voucher-${voucher.voucherNumber}.txt`; // In real implementation, this would be .pdf
    }

    // Create response with file
    const response = new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });

    logger.info('Voucher exported successfully', LogCategory.ACCOUNTING, {
      userId: user._id?.toString(),
      voucherId: id,
      voucherNumber: voucher.voucherNumber,
      format,
      fileName,
      fileSize: fileBuffer.length
    });

    return response;

  } catch (error: any) {
    logger.error('Error exporting voucher', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'EXPORT_VOUCHER_FAILED',
      'Failed to export voucher',
      'An error occurred while exporting the voucher. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/vouchers/[id]/export',
        method: 'GET',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/vouchers/[id]/void
 * Void a voucher
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to void vouchers.',
        {
          endpoint: '/api/accounting/vouchers/[id]/void',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to void vouchers',
        'You do not have permission to void vouchers.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/vouchers/[id]/void',
          method: 'POST'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Parse request body
    const body = await req.json().catch(() => ({}));
    const { reason } = body;

    // Get voucher
    const voucher = await Voucher.findById(id);

    if (!voucher) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'VOUCHER_NOT_FOUND',
        'Voucher not found',
        'The specified voucher could not be found.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          endpoint: '/api/accounting/vouchers/[id]/void',
          method: 'POST'
        },
        404,
        ErrorSeverity.LOW
      );
    }

    // Check if voucher can be voided
    if (voucher.status === 'voided') {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'VOUCHER_ALREADY_VOIDED',
        'Voucher is already voided',
        'This voucher has already been voided.',
        {
          userId: user._id?.toString(),
          voucherId: id,
          voucherNumber: voucher.voucherNumber,
          currentStatus: voucher.status,
          endpoint: '/api/accounting/vouchers/[id]/void',
          method: 'POST'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Check if voucher can be voided based on status
    const voidableStatuses = ['approved', 'posted', 'pending'];
    if (!voidableStatuses.includes(voucher.status)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'VOUCHER_CANNOT_BE_VOIDED',
        'Voucher cannot be voided',
        `Vouchers with status '${voucher.status}' cannot be voided. Only approved, posted, or pending vouchers can be voided.`,
        {
          userId: user._id?.toString(),
          voucherId: id,
          voucherNumber: voucher.voucherNumber,
          currentStatus: voucher.status,
          voidableStatuses,
          endpoint: '/api/accounting/vouchers/[id]/void',
          method: 'POST'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Store original status for audit
    const originalStatus = voucher.status;

    // Void the voucher
    voucher.status = 'voided';
    voucher.updatedBy = user._id?.toString() || user.id;
    voucher.updatedAt = new Date();

    // Add void reason to notes
    if (reason) {
      voucher.notes = voucher.notes 
        ? `${voucher.notes}\n\nVoid Reason: ${reason}`
        : `Void Reason: ${reason}`;
    }

    // Add audit trail
    voucher.notes = voucher.notes 
      ? `${voucher.notes}\n\nVoided by ${user.name || user.email} on ${new Date().toISOString()} (Previous status: ${originalStatus})`
      : `Voided by ${user.name || user.email} on ${new Date().toISOString()} (Previous status: ${originalStatus})`;

    await voucher.save();

    logger.info('Voucher voided successfully', LogCategory.ACCOUNTING, {
      userId: user._id?.toString(),
      voucherId: id,
      voucherNumber: voucher.voucherNumber,
      originalStatus,
      reason,
      voidedAt: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: `Voucher ${voucher.voucherNumber} has been voided successfully`,
      data: {
        id: voucher._id,
        voucherNumber: voucher.voucherNumber,
        status: voucher.status,
        originalStatus,
        voidedAt: voucher.updatedAt,
        voidedBy: user.name || user.email,
        reason
      }
    });

  } catch (error: any) {
    logger.error('Error voiding voucher', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'VOID_VOUCHER_FAILED',
      'Failed to void voucher',
      'An error occurred while voiding the voucher. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/vouchers/[id]/void',
        method: 'POST',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

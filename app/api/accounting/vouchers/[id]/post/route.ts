// app/api/accounting/vouchers/[id]/post/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import JournalEntry from '@/models/accounting/JournalEntry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Note: We're using 'any' for voucher items because the actual schema
// has additional properties like costCenter, project, and department
// that aren't defined in the IVoucherItem interface

export const runtime = 'nodejs';



/**
 * POST /api/accounting/vouchers/[id]/post
 * Post a voucher to the general ledger
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get voucher
    const voucher = await Voucher.findById(id);

    if (!voucher) {
      return NextResponse.json(
        { error: 'Voucher not found' },
        { status: 404 }
      );
    }

    // Check if voucher can be posted
    if (voucher.status !== 'approved') {
      return NextResponse.json(
        { error: 'Only approved vouchers can be posted' },
        { status: 400 }
      );
    }

    // Check if voucher is already posted
    if (voucher.journalEntryId) {
      return NextResponse.json(
        { error: 'Voucher is already posted' },
        { status: 400 }
      );
    }

    // Create journal entry
    const journalEntry = new JournalEntry({
      date: voucher.date,
      reference: voucher.voucherNumber,
      description: voucher.description,
      items: voucher.items.map((item: Record<string, any>) => ({
        accountId: item.account,
        description: item.description,
        debit: item.debit,
        credit: item.credit,
        costCenterId: item.costCenter,
        projectId: item.project,
        departmentId: item.department
      })),
      currency: voucher.currency,
      exchangeRate: voucher.exchangeRate,
      notes: voucher.notes,
      tags: voucher.tags,
      status: 'posted',
      createdBy: user._id?.toString() || user.id,
      updatedBy: user._id?.toString() || user.id
    });

    await journalEntry.save();

    // Update voucher with journal entry ID and status
    voucher.journalEntryId = journalEntry._id;
    voucher.status = 'posted';
    voucher.updatedBy = user._id?.toString() || user.id;
    voucher.updatedAt = new Date();
    await voucher.save();

    return NextResponse.json({
      success: true,
      message: 'Voucher posted successfully',
      data: {
        voucher,
        journalEntry
      }
    });
  } catch (error: unknown) {
    logger.error('Error posting voucher', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

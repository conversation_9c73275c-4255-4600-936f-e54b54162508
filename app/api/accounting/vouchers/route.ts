// app/api/accounting/vouchers/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Voucher from '@/models/accounting/Voucher';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for voucher item
const voucherItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  account: z.string().min(1, 'Account is required'),
  debit: z.number().min(0, 'Debit amount cannot be negative'),
  credit: z.number().min(0, 'Credit amount cannot be negative'),
  costCenter: z.string().optional(),
  project: z.string().optional(),
  department: z.string().optional(),
  notes: z.string().optional()
});

// Validation schema for creating voucher
const createVoucherSchema = z.object({
  voucherNumber: z.string().optional(),
  voucherType: z.enum(['payment', 'receipt', 'journal', 'adjustment']),
  date: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid date' }
  ),
  description: z.string().min(3, 'Description must be at least 3 characters long'),
  reference: z.string().optional(),
  payee: z.string().optional(),
  paymentMethod: z.enum(['cash', 'check', 'bank_transfer', 'credit_card', 'other']).optional(),
  checkNumber: z.string().optional(),
  items: z.array(voucherItemSchema).min(1, 'At least one item is required'),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().positive().default(1),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'pending', 'approved', 'rejected', 'posted', 'voided']).default('draft')
});

/**
 * GET /api/accounting/vouchers
 * List vouchers
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const status = searchParams.get('status');
    const voucherType = searchParams.get('voucherType');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: Record<string, any> = {};

    if (startDate || endDate) {
      query.date = {} as any;
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    if (status) {
      query.status = status;
    }

    if (voucherType) {
      query.voucherType = voucherType;
    }

    if (search) {
      query.$or = [
        { voucherNumber: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
        { payee: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: { [key: string]: 1 | -1 } = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get total count
    const totalCount = await Voucher.countDocuments(query);
    const totalPages = Math.ceil(totalCount / limit);

    // Get vouchers - skip payrollRunId population to avoid schema registration issues
    const vouchers = await Voucher.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    return NextResponse.json({
      success: true,
      data: vouchers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting vouchers', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/vouchers
 * Create a voucher
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createVoucherSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Validate accounts
    const accountIds = validationResult.data.items.map(item => item.account);
    const accounts = await Account.find({ _id: { $in: accountIds } });

    if (accounts.length !== accountIds.length) {
      return NextResponse.json(
        { error: 'One or more accounts not found' },
        { status: 400 }
      );
    }

    // Validate debits and credits balance
    const totalDebit = validationResult.data.items.reduce((sum, item) => sum + item.debit, 0);
    const totalCredit = validationResult.data.items.reduce((sum, item) => sum + item.credit, 0);

    if (Math.abs(totalDebit - totalCredit) > 0.01) { // Allow for small rounding errors
      return NextResponse.json(
        { error: 'Debits and credits must balance' },
        { status: 400 }
      );
    }

    // Generate voucher number if not provided
    let voucherNumber = validationResult.data.voucherNumber;
    if (!voucherNumber) {
      // Check if the model has the generateVoucherNumber method
      if (typeof (Voucher as any).generateVoucherNumber === 'function') {
        voucherNumber = await (Voucher as any).generateVoucherNumber(validationResult.data.voucherType);
      } else {
        // Fallback to a simple voucher number generation
        const prefix = validationResult.data.voucherType.substring(0, 3).toUpperCase();
        const timestamp = Date.now().toString().slice(-6);
        voucherNumber = `${prefix}-${timestamp}`;
      }
    }

    // Create voucher
    const voucher = new Voucher({
      voucherNumber,
      voucherType: validationResult.data.voucherType,
      date: new Date(validationResult.data.date),
      description: validationResult.data.description,
      reference: validationResult.data.reference,
      payee: validationResult.data.payee,
      paymentMethod: validationResult.data.paymentMethod,
      checkNumber: validationResult.data.checkNumber,
      items: validationResult.data.items.map(item => ({
        description: item.description,
        account: new mongoose.Types.ObjectId(item.account),
        debit: item.debit,
        credit: item.credit,
        costCenter: item.costCenter ? new mongoose.Types.ObjectId(item.costCenter) : undefined,
        project: item.project ? new mongoose.Types.ObjectId(item.project) : undefined,
        department: item.department ? new mongoose.Types.ObjectId(item.department) : undefined,
        notes: item.notes
      })),
      currency: validationResult.data.currency,
      exchangeRate: validationResult.data.exchangeRate,
      notes: validationResult.data.notes,
      attachments: validationResult.data.attachments,
      tags: validationResult.data.tags,
      status: validationResult.data.status,
      createdBy: user._id?.toString() || user.id,
      updatedBy: user._id?.toString() || user.id
    });

    await voucher.save();

    return NextResponse.json({
      success: true,
      message: 'Voucher created successfully',
      data: voucher
    });
  } catch (error: unknown) {
    logger.error('Error creating voucher', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

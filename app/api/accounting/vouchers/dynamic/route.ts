import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { DynamicVoucherService } from '@/lib/services/accounting/dynamic-voucher-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/vouchers/dynamic
 * Create a dynamic voucher with type-specific handling
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to create vouchers.',
        {
          userId: undefined,
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions
    const allowedRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.PAYROLL_SPECIALIST
    ];

    if (!hasRequiredPermissions(user, allowedRoles)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to create vouchers',
        'You do not have permission to create vouchers.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    await connectToDatabase();

    // Parse request body
    const body = await request.json();
    const {
      voucherTypeId,
      coreFields,
      dynamicFields
    } = body;

    // Validate required fields
    if (!voucherTypeId) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_VOUCHER_TYPE',
        'Voucher type is required',
        'Please specify a voucher type for the voucher.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    if (!coreFields || !coreFields.date || !coreFields.description || !coreFields.totalAmount) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_CORE_FIELDS',
        'Missing required core fields',
        'Please provide all required core fields: date, description, and totalAmount.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST',
          providedCoreFields: coreFields ? Object.keys(coreFields) : []
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Initialize dynamic voucher service
    const dynamicVoucherService = new DynamicVoucherService();

    // Validate voucher type exists
    const voucherTypeDefinition = await dynamicVoucherService.getVoucherTypeDefinition(voucherTypeId);
    if (!voucherTypeDefinition) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'VOUCHER_TYPE_NOT_FOUND',
        'Voucher type not found',
        `The specified voucher type '${voucherTypeId}' was not found or is not active.`,
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST',
          requestedVoucherTypeId: voucherTypeId
        },
        404,
        ErrorSeverity.MEDIUM
      );
    }

    // Prepare voucher request
    const voucherRequest = {
      voucherTypeId,
      coreFields: {
        date: new Date(coreFields.date),
        description: coreFields.description,
        totalAmount: Number(coreFields.totalAmount),
        fiscalYear: coreFields.fiscalYear || new Date().getFullYear().toString(),
        payee: coreFields.payee,
        paymentMethod: coreFields.paymentMethod,
        notes: coreFields.notes
      },
      dynamicFields: dynamicFields || {},
      userId: user._id?.toString() || ''
    };

    // Create dynamic voucher
    const voucher = await dynamicVoucherService.createDynamicVoucher(voucherRequest);

    // Get full voucher data for response
    const voucherRenderingData = await dynamicVoucherService.getVoucherForRendering(
      voucher._id.toString()
    );

    logger.info('Dynamic voucher created successfully', LogCategory.ACCOUNTING, {
      voucherId: voucher._id,
      voucherTypeId,
      userId: user._id?.toString(),
      totalAmount: voucherRequest.coreFields.totalAmount
    });

    return NextResponse.json({
      success: true,
      message: 'Voucher created successfully',
      data: {
        voucher: {
          id: voucher._id.toString(),
          voucherNumber: voucher.voucherNumber,
          voucherType: voucher.voucherType,
          voucherTypeId: voucher.voucherTypeId,
          date: voucher.date,
          description: voucher.description,
          totalAmount: voucher.totalAmount,
          status: voucher.status,
          fiscalYear: voucher.fiscalYear,
          payee: voucher.payee,
          paymentMethod: voucher.paymentMethod,
          notes: voucher.notes,
          dynamicFields: voucher.dynamicFields,
          createdAt: voucher.createdAt
        },
        typeDefinition: voucherRenderingData?.typeDefinition,
        displayConfiguration: voucherRenderingData?.displayConfiguration
      }
    });

  } catch (error: any) {
    logger.error('Error creating dynamic voucher', LogCategory.ACCOUNTING, error);

    // Handle specific validation errors
    if (error.message.includes('Required field missing')) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'FIELD_VALIDATION_ERROR',
        'Field validation failed',
        error.message,
        {
          userId: 'unknown',
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST',
          validationError: error.message
        },
        400,
        ErrorSeverity.LOW
      );
    }

    if (error.message.includes('must be a')) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'TYPE_VALIDATION_ERROR',
        'Data type validation failed',
        error.message,
        {
          userId: 'unknown',
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'POST',
          typeError: error.message
        },
        400,
        ErrorSeverity.LOW
      );
    }

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INTERNAL_SERVER_ERROR',
      'Failed to create voucher',
      'An unexpected error occurred while creating the voucher. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/vouchers/dynamic',
        method: 'POST',
        error: error.message,
        stack: error.stack
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * GET /api/accounting/vouchers/dynamic/[id]
 * Get a dynamic voucher with full rendering data
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to view vouchers.',
        {
          userId: undefined,
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'GET'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions
    const allowedRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.PAYROLL_SPECIALIST
    ];

    if (!hasRequiredPermissions(user, allowedRoles)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to view vouchers',
        'You do not have permission to view voucher details.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'GET'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    await connectToDatabase();

    // Get voucher ID from query parameters
    const { searchParams } = new URL(request.url);
    const voucherId = searchParams.get('id');

    if (!voucherId) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_VOUCHER_ID',
        'Voucher ID is required',
        'Please provide a voucher ID to retrieve voucher details.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'GET'
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Initialize dynamic voucher service
    const dynamicVoucherService = new DynamicVoucherService();

    // Get voucher with rendering data
    const voucherRenderingData = await dynamicVoucherService.getVoucherForRendering(voucherId);

    if (!voucherRenderingData) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'VOUCHER_NOT_FOUND',
        'Voucher not found',
        `The voucher with ID '${voucherId}' was not found.`,
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/vouchers/dynamic',
          method: 'GET',
          requestedVoucherId: voucherId
        },
        404,
        ErrorSeverity.MEDIUM
      );
    }

    logger.info('Retrieved dynamic voucher', LogCategory.ACCOUNTING, {
      voucherId,
      voucherTypeId: voucherRenderingData.voucher.voucherTypeId,
      userId: user._id?.toString()
    });

    return NextResponse.json({
      success: true,
      data: voucherRenderingData
    });

  } catch (error: any) {
    logger.error('Error getting dynamic voucher', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INTERNAL_SERVER_ERROR',
      'Failed to retrieve voucher',
      'An unexpected error occurred while retrieving the voucher. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/vouchers/dynamic',
        method: 'GET',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

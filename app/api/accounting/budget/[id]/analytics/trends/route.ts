// app/api/accounting/budget/[id]/analytics/trends/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetAnalyticsService } from '@/lib/services/accounting/budget-analytics-service';

/**
 * GET /api/accounting/budget/[id]/analytics/trends
 * Get budget trend analysis over time
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.ACCOUNTANT,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view budget trends' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') as 'monthly' | 'quarterly' || 'monthly';

    // Validate period parameter
    if (!['monthly', 'quarterly'].includes(period)) {
      return NextResponse.json(
        { error: 'Invalid period parameter. Must be "monthly" or "quarterly"' },
        { status: 400 }
      );
    }

    logger.info('Getting budget trend analysis', LogCategory.ACCOUNTING, {
      budgetId: id,
      period,
      userId: user.id
    });

    // Get trend analysis
    const trends = await budgetAnalyticsService.getBudgetTrendAnalysis(id, period);

    return NextResponse.json(trends);

  } catch (error) {
    logger.error('Error getting budget trend analysis', LogCategory.ACCOUNTING, error);
    
    if (error instanceof Error && error.message === 'Budget not found') {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while getting trend analysis' },
      { status: 500 }
    );
  }
}

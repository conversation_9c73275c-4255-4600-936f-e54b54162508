// app/api/accounting/budget/[id]/analytics/performance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetAnalyticsService } from '@/lib/services/accounting/budget-analytics-service';

/**
 * GET /api/accounting/budget/[id]/analytics/performance
 * Get comprehensive performance metrics for a budget
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.ACCOUNTANT,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view budget analytics' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    logger.info('Getting budget performance metrics', LogCategory.ACCOUNTING, {
      budgetId: id,
      userId: user.id
    });

    // Get performance metrics
    const performanceMetrics = await budgetAnalyticsService.getBudgetPerformanceMetrics(id);

    return NextResponse.json(performanceMetrics);

  } catch (error) {
    logger.error('Error getting budget performance metrics', LogCategory.ACCOUNTING, error);
    
    if (error instanceof Error && error.message === 'Budget not found') {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while getting performance metrics' },
      { status: 500 }
    );
  }
}

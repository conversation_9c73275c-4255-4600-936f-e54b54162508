// app/api/accounting/budget/[id]/performance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetPerformanceService } from '@/lib/services/accounting/budget-performance-service';

/**
 * GET /api/accounting/budget/[id]/performance
 * Get budget performance data
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const month = searchParams.get('month') ? parseInt(searchParams.get('month') as string) : undefined;
    const quarter = searchParams.get('quarter') ? parseInt(searchParams.get('quarter') as string) : undefined;
    const year = searchParams.get('year') ? parseInt(searchParams.get('year') as string) : undefined;

    // Resolve the params promise
    const { id } = await params;

    // Build period object
    const period: Record<string, number> = {};
    if (month) period.month = month;
    if (quarter) period.quarter = quarter;
    if (year) period.year = year;

    // Get budget performance data
    const performance = await budgetPerformanceService.getBudgetPerformance(id);

    // Log the request
    logger.info('Budget performance retrieved', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetId: id,
      period
    });

    return NextResponse.json({
      success: true,
      performance
    });
  } catch (error: unknown) {
    logger.error(`Error getting budget performance`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

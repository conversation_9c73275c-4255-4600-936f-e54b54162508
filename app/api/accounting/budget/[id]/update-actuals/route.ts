// app/api/accounting/budget/[id]/update-actuals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import { budgetService } from '@/lib/backend/services/accounting/budget-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for update actuals request
const updateActualsSchema = z.object({
  transactionId: z.string().optional(),
  type: z.enum(['income', 'expense']),
  operation: z.enum(['create', 'update', 'delete'])
});

/**
 * POST /api/accounting/budget/[id]/update-actuals
 * Update budget actual amounts when transactions are created, modified, or deleted
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = updateActualsSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get budget
    const budget = await budgetService.getBudgetById(id);

    // Update budget actual amounts
    await budget.updateActualAmounts();

    return NextResponse.json({
      success: true,
      message: 'Budget actual amounts updated successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error updating budget actual amounts`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/[id]/categories/[categoryId]/items/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { Budget, BudgetCategory, BudgetItem, BudgetSubcategory } from '@/models/accounting/Budget';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import BudgetService from '@/lib/services/accounting/budget-service';

export const runtime = 'nodejs';



// Create a budget service instance
const budgetService = new BudgetService();

/**
 * GET /api/accounting/budget/[id]/categories/[categoryId]/items
 * Get items for a specific budget category
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; categoryId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id, categoryId } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const subcategoryId = searchParams.get('subcategoryId');

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Find category
    const category = await BudgetCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify category belongs to this budget
    if (category.budget.toString() !== id) {
      return NextResponse.json(
        { error: 'Category does not belong to this budget' },
        { status: 400 }
      );
    }

    // Build filter
    const filter: Record<string, any> = {
      budget: id,
      parentCategory: categoryId
    };

    // Add subcategory filter if provided
    if (subcategoryId) {
      filter.parentSubcategory = subcategoryId;
    }

    // Get items
    const items = await BudgetItem.find(filter)
      .sort({ name: 1 })
      .lean();

    // Return items
    return NextResponse.json({
      success: true,
      items
    });
  } catch (error: unknown) {
    logger.error(`Error getting budget category items`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget/[id]/categories/[categoryId]/items
 * Add a new item to a budget category
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; categoryId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id, categoryId } = await params;

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || body.quantity === undefined || body.unitCost === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, quantity, and unitCost are required' },
        { status: 400 }
      );
    }

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Find category
    const category = await BudgetCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify category belongs to this budget
    if (category.budget.toString() !== id) {
      return NextResponse.json(
        { error: 'Category does not belong to this budget' },
        { status: 400 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Prepare item data
    const itemData: {
      name: string;
      description?: string;
      quantity: number;
      frequency: number;
      unitCost: number;
      parentCategory: string;
      parentSubcategory?: string;
      budget: string;
    } = {
      name: body.name,
      description: body.description,
      quantity: body.quantity,
      frequency: body.frequency || 1,
      unitCost: body.unitCost,
      parentCategory: categoryId,
      budget: id
    };

    // Add subcategory if provided
    if (body.subcategoryId) {
      // Verify subcategory exists and belongs to this category
      const subcategory = await BudgetSubcategory.findById(body.subcategoryId);
      if (!subcategory) {
        return NextResponse.json(
          { error: 'Subcategory not found' },
          { status: 404 }
        );
      }

      if (subcategory.parentCategory.toString() !== categoryId) {
        return NextResponse.json(
          { error: 'Subcategory does not belong to this category' },
          { status: 400 }
        );
      }

      itemData.parentSubcategory = body.subcategoryId;
    }

    // Create item
    const item = await budgetService.createBudgetItem(itemData);

    // Return created item
    return NextResponse.json({
      success: true,
      message: 'Item added successfully',
      item
    });
  } catch (error: unknown) {
    logger.error(`Error adding budget category item`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/budget/[id]/categories/[categoryId]/items
 * Delete an item from a budget category
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; categoryId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id, categoryId } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const itemId = searchParams.get('itemId');

    if (!itemId) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      );
    }

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Find category
    const category = await BudgetCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify category belongs to this budget
    if (category.budget.toString() !== id) {
      return NextResponse.json(
        { error: 'Category does not belong to this budget' },
        { status: 400 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Find the item
    const item = await BudgetItem.findById(itemId);
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      );
    }

    // Verify item belongs to this category
    if (item.parentCategory.toString() !== categoryId) {
      return NextResponse.json(
        { error: 'Item does not belong to this category' },
        { status: 400 }
      );
    }

    // Delete the item
    await BudgetItem.findByIdAndDelete(itemId);

    // Recalculate budget totals
    await budgetService.calculateBudgetTotals(id);

    return NextResponse.json({
      success: true,
      message: 'Item deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting budget category item`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
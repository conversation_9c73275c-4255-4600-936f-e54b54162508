// app/api/accounting/budget/[id]/real-time-tracker/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '../../../../../../lib/backend/auth/auth';
import { logger, LogCategory } from '../../../../../../lib/backend/utils/logger';
import { connectToDatabase } from '../../../../../../lib/backend/database';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



interface BudgetTrackerData {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  totalBudgeted: number;
  totalActual: number;
  totalRemaining: number;
  utilizationPercentage: number;
  categories: {
    id: string;
    name: string;
    type: 'income' | 'expense';
    budgeted: number;
    actual: number;
    remaining: number;
    utilizationPercentage: number;
    status: 'on-track' | 'warning' | 'exceeded' | 'under-utilized';
  }[];
  lastUpdated: Date;
  trends: {
    monthlyChange: number;
    weeklyChange: number;
    projectedEndOfYear: number;
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get budget ID from params
    const { id } = await params;
    
    if (!id) {
      return NextResponse.json({ error: 'Budget ID is required' }, { status: 400 });
    }

    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const db = mongoose.connection.db;

    if (!db) {
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    // Get budget details
    const budget = await db.collection('budgets').findOne({
      _id: new ObjectId(id)
    });

    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Get current date for calculations
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Calculate date ranges for trends
    const startOfMonth = new Date(currentYear, currentMonth, 1);
    const startOfLastMonth = new Date(currentYear, currentMonth - 1, 1);
    const endOfLastMonth = new Date(currentYear, currentMonth, 0);
    const startOfWeek = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

    // Get actual income and expense data
    const [incomeData, expenseData] = await Promise.all([
      // Get income data
      db.collection('incomes').aggregate([
        {
          $match: {
            budget: new ObjectId(id),
            date: { $gte: new Date(budget.fiscalYear.split('-')[0] + '-01-01') }
          }
        },
        {
          $group: {
            _id: '$budgetCategory',
            totalAmount: { $sum: '$amount' },
            currentMonthAmount: {
              $sum: {
                $cond: [
                  { $gte: ['$date', startOfMonth] },
                  '$amount',
                  0
                ]
              }
            },
            lastMonthAmount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $gte: ['$date', startOfLastMonth] },
                      { $lte: ['$date', endOfLastMonth] }
                    ]
                  },
                  '$amount',
                  0
                ]
              }
            },
            weeklyAmount: {
              $sum: {
                $cond: [
                  { $gte: ['$date', startOfWeek] },
                  '$amount',
                  0
                ]
              }
            }
          }
        }
      ]).toArray(),

      // Get expense data
      db.collection('expenses').aggregate([
        {
          $match: {
            budget: new ObjectId(id),
            date: { $gte: new Date(budget.fiscalYear.split('-')[0] + '-01-01') }
          }
        },
        {
          $group: {
            _id: '$budgetCategory',
            totalAmount: { $sum: '$amount' },
            currentMonthAmount: {
              $sum: {
                $cond: [
                  { $gte: ['$date', startOfMonth] },
                  '$amount',
                  0
                ]
              }
            },
            lastMonthAmount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $gte: ['$date', startOfLastMonth] },
                      { $lte: ['$date', endOfLastMonth] }
                    ]
                  },
                  '$amount',
                  0
                ]
              }
            },
            weeklyAmount: {
              $sum: {
                $cond: [
                  { $gte: ['$date', startOfWeek] },
                  '$amount',
                  0
                ]
              }
            }
          }
        }
      ]).toArray()
    ]);

    // Create lookup maps for actual amounts
    const incomeMap = new Map();
    const expenseMap = new Map();

    incomeData.forEach((item: any) => {
      incomeMap.set(item._id?.toString(), item);
    });

    expenseData.forEach((item: any) => {
      expenseMap.set(item._id?.toString(), item);
    });

    // Process budget categories
    const categories = budget.categories?.map((category: any) => {
      const categoryId = category._id?.toString() || category.id;
      const isIncome = category.type === 'income';
      const actualData = isIncome ? incomeMap.get(categoryId) : expenseMap.get(categoryId);
      
      const budgeted = category.budgeted || 0;
      const actual = actualData?.totalAmount || 0;
      const remaining = budgeted - actual;
      const utilizationPercentage = budgeted > 0 ? (actual / budgeted) * 100 : 0;

      // Determine status
      let status: 'on-track' | 'warning' | 'exceeded' | 'under-utilized';
      if (isIncome) {
        if (utilizationPercentage >= 90) status = 'on-track';
        else if (utilizationPercentage >= 70) status = 'warning';
        else status = 'under-utilized';
      } else {
        if (utilizationPercentage > 100) status = 'exceeded';
        else if (utilizationPercentage > 90) status = 'warning';
        else status = 'on-track';
      }

      return {
        id: categoryId,
        name: category.name,
        type: category.type,
        budgeted,
        actual,
        remaining,
        utilizationPercentage,
        status
      };
    }) || [];

    // Calculate totals separately for income and expenses
    const incomeCategories = categories.filter((cat: any) => cat.type === 'income');
    const expenseCategories = categories.filter((cat: any) => cat.type === 'expense');

    const totalBudgetedIncome = incomeCategories.reduce((sum: number, cat: any) => sum + (cat.budgeted || 0), 0);
    const totalActualIncome = incomeCategories.reduce((sum: number, cat: any) => sum + (cat.actual || 0), 0);
    const totalBudgetedExpense = expenseCategories.reduce((sum: number, cat: any) => sum + (cat.budgeted || 0), 0);
    const totalActualExpense = expenseCategories.reduce((sum: number, cat: any) => sum + (cat.actual || 0), 0);

    // Net position calculation (income - expenses)
    const netPosition = totalActualIncome - totalActualExpense;
    const incomeUtilization = totalBudgetedIncome > 0 ? (totalActualIncome / totalBudgetedIncome) * 100 : 0;
    const expenseUtilization = totalBudgetedExpense > 0 ? (totalActualExpense / totalBudgetedExpense) * 100 : 0;

    // Calculate trends for income and expenses separately
    const currentMonthIncome = incomeData.reduce((sum: number, item: any) => sum + (item.currentMonthAmount || 0), 0);
    const currentMonthExpense = expenseData.reduce((sum: number, item: any) => sum + (item.currentMonthAmount || 0), 0);
    const lastMonthIncome = incomeData.reduce((sum: number, item: any) => sum + (item.lastMonthAmount || 0), 0);
    const lastMonthExpense = expenseData.reduce((sum: number, item: any) => sum + (item.lastMonthAmount || 0), 0);

    const monthlyIncomeChange = lastMonthIncome > 0 ? ((currentMonthIncome - lastMonthIncome) / lastMonthIncome) * 100 : 0;
    const monthlyExpenseChange = lastMonthExpense > 0 ? ((currentMonthExpense - lastMonthExpense) / lastMonthExpense) * 100 : 0;

    // Simple projection based on current utilization rate
    const monthsIntoYear = currentMonth + 1;
    const projectedIncomeUtilization = monthsIntoYear > 0 ? (incomeUtilization / monthsIntoYear) * 12 : incomeUtilization;
    const projectedExpenseUtilization = monthsIntoYear > 0 ? (expenseUtilization / monthsIntoYear) * 12 : expenseUtilization;

    // Calculate legacy fields for backward compatibility
    const totalBudgeted = totalBudgetedIncome; // Budget = income only
    const totalActual = netPosition; // Net position (income - expenses)
    const totalRemaining = totalBudgeted - totalActualIncome;
    const utilizationPercentage = incomeUtilization;

    const trackerData: BudgetTrackerData = {
      budgetId: id,
      budgetName: budget.name,
      fiscalYear: budget.fiscalYear,
      totalBudgeted,
      totalActual,
      totalRemaining,
      utilizationPercentage,
      categories,
      lastUpdated: new Date(),
      trends: {
        monthlyChange: monthlyIncomeChange,
        weeklyChange: 0, // Calculate if needed
        projectedEndOfYear: Math.min(projectedIncomeUtilization, 200) // Cap at 200%
      }
    };

    logger.info('Real-time budget tracker data retrieved successfully', LogCategory.ANALYTICS, {
      budgetId: id,
      budgetName: budget.name,
      utilizationPercentage,
      totalBudgeted,
      totalActual,
      categoriesCount: categories.length
    });

    return NextResponse.json(trackerData);

  } catch (error) {
    logger.error('Error retrieving real-time budget tracker data', LogCategory.ANALYTICS, error);
    return NextResponse.json(
      { error: 'Failed to retrieve budget tracker data' },
      { status: 500 }
    );
  }
}

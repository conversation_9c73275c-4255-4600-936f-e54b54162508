import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import { Budget } from '@/models/accounting/Budget';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/budget/[id]/trends
 * Fetch monthly trends for a budget
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare idId at function scope
  let idId: string;
  
  try {
    // Resolve the params promise
    const { id } = await params;
    idId = id;
    // Get session
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get budget ID from params
    const budgetId = idId;

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Check if budget exists
    const budget = await Budget.findById(budgetId);
    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Get start and end dates from budget
    const startDate = new Date(budget.startDate);
    const endDate = new Date(budget.endDate);

    // Generate array of months between start and end dates
    const months: string[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      months.push(currentDate.toISOString().substring(0, 7)); // Format: YYYY-MM
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Aggregate income by month
    const incomeByMonth = await Income.aggregate([
      {
        $match: {
          budget: new mongoose.Types.ObjectId(budgetId),
          status: 'received',
          date: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: { $substr: ['$date', 0, 7] }, // Group by YYYY-MM
          total: { $sum: '$amount' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Aggregate expense by month
    const expenseByMonth = await Expense.aggregate([
      {
        $match: {
          budget: new mongoose.Types.ObjectId(budgetId),
          status: 'paid',
          date: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: { $substr: ['$date', 0, 7] }, // Group by YYYY-MM
          total: { $sum: '$amount' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Convert aggregation results to maps for easier lookup
    const incomeMap = new Map(incomeByMonth.map(item => [item._id, item.total]));
    const expenseMap = new Map(expenseByMonth.map(item => [item._id, item.total]));

    // Calculate monthly budget allocations (simple equal distribution)
    const monthCount = months.length;
    const monthlyBudgetedIncome = monthCount > 0 ? budget.totalIncome / monthCount : 0;
    const monthlyBudgetedExpense = monthCount > 0 ? budget.totalExpense / monthCount : 0;

    // Format data for response
    const formattedMonths = months.map(month => {
      const [year, monthNum] = month.split('-');
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return `${monthNames[parseInt(monthNum) - 1]} ${year}`;
    });

    const incomeData = months.map(month => incomeMap.get(month) || 0);
    const expenseData = months.map(month => expenseMap.get(month) || 0);
    const budgetedIncomeData = months.map(() => monthlyBudgetedIncome);
    const budgetedExpenseData = months.map(() => monthlyBudgetedExpense);

    // Return trends data
    return NextResponse.json({
      budgetId: budget._id.toString(),
      name: budget.name,
      months: formattedMonths,
      rawMonths: months,
      income: incomeData,
      expense: expenseData,
      budgetedIncome: budgetedIncomeData,
      budgetedExpense: budgetedExpenseData
    });
  } catch (error) {
    logger.error('Error fetching budget trends', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to fetch budget trends' },
      { status: 500 }
    );
  }
}

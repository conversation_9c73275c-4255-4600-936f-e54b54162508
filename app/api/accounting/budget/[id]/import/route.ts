// app/api/accounting/budget/[id]/import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { Budget } from '@/models/accounting/Budget';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import BudgetService from '@/lib/services/accounting/budget-service';

// Create a budget service instance
const budgetService = new BudgetService();

/**
 * POST /api/accounting/budget/[id]/import
 * Import budget items from CSV/Excel
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate items array
    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { error: 'Items array is required and must not be empty' },
        { status: 400 }
      );
    }

    // Validate each item
    for (const item of body.items) {
      if (!item.categoryName || !item.categoryType || !item.name ||
          item.quantity === undefined || item.unitCost === undefined) {
        return NextResponse.json(
          { error: 'Each item must have categoryName, categoryType, name, quantity, and unitCost' },
          { status: 400 }
        );
      }
    }

    // Get import options
    const options = {
      batchSize: body.batchSize || 100,
      skipTotalsCalculation: body.skipTotalsCalculation || false
    };

    // Import items
    const updatedBudget = await budgetService.importBudgetItems(id, body.items, options);

    // Return success
    return NextResponse.json({
      success: true,
      message: `Successfully imported ${body.items.length} items`,
      budget: updatedBudget
    });
  } catch (error: unknown) {
    logger.error(`Error importing budget items`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/[id]/actions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { budgetService } from '@/lib/services/accounting/budget-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

/**
 * POST /api/accounting/budget/[id]/actions
 * Perform actions on a budget (activate, close)
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'BUDGET_UNAUTHORIZED',
        'User authentication required',
        'You must be logged in to perform budget actions',
        { endpoint: req.url, method: req.method },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate action with zod
    const actionSchema = z.object({
      action: z.enum(['activate', 'close', 'approve', 'reject', 'submit']),
      rejectionReason: z.string().optional(),
    });

    const validationResult = actionSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'BUDGET_INVALID_ACTION',
        `Invalid action parameter: ${validationResult.error.errors[0].message}`,
        'The action you requested is not valid. Please check your request and try again.',
        {
          endpoint: req.url,
          method: req.method,
          userId: user.id,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        undefined,
        ['Check that the action is one of: activate, close, approve, reject, submit']
      );
    }

    // Resolve the params promise
    const { id: budgetId } = await params;

    // Get budget to check if it exists
    const budgetCheck = await budgetService.findById(budgetId);
    if (!budgetCheck) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'BUDGET_NOT_FOUND',
        `Budget with ID ${budgetId} not found`,
        'The budget you are trying to modify does not exist or may have been deleted.',
        {
          endpoint: req.url,
          method: req.method,
          userId: user.id,
          budgetId
        },
        404,
        ErrorSeverity.MEDIUM,
        undefined,
        ['Verify the budget ID is correct', 'Check if the budget was recently deleted']
      );
    }

    // Process based on action
    switch (body.action) {
      case 'activate':
        // Check permissions
        const hasActivatePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasActivatePermission) {
          return errorService.createApiResponse(
            ErrorType.FORBIDDEN,
            'BUDGET_ACTIVATION_FORBIDDEN',
            'Insufficient permissions to activate budget',
            'You do not have the required permissions to activate budgets. Contact your administrator.',
            {
              endpoint: req.url,
              method: req.method,
              userId: user.id,
              userRole: user.role,
              budgetId,
              requiredRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_MANAGER]
            },
            403,
            ErrorSeverity.MEDIUM
          );
        }

        // Check if budget can be activated
        if (budgetCheck.status !== 'draft' && budgetCheck.status !== 'approved') {
          return errorService.createApiResponse(
            ErrorType.BUSINESS_LOGIC,
            'BUDGET_INVALID_STATUS_FOR_ACTIVATION',
            `Cannot activate budget with status '${budgetCheck.status}'`,
            'Only draft or approved budgets can be activated. Please ensure the budget is in the correct status.',
            {
              endpoint: req.url,
              method: req.method,
              userId: user.id,
              budgetId,
              currentStatus: budgetCheck.status,
              allowedStatuses: ['draft', 'approved']
            },
            400,
            ErrorSeverity.LOW,
            undefined,
            ['Ensure the budget is approved before activation', 'Check the budget approval workflow']
          );
        }

        // Update budget using service
        const updatedBudget = await budgetService.updateBudgetStatus(
          budgetId,
          'active',
          user.id
        );

        // Return updated budget
        return NextResponse.json({
          success: true,
          message: 'Budget activated successfully',
          budget: updatedBudget
        });

      case 'close':
        // Check permissions
        const hasClosePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasClosePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to close budgets' },
            { status: 403 }
          );
        }

        // Check if budget can be closed
        if (budgetCheck.status !== 'active') {
          return NextResponse.json(
            { error: 'Only active budgets can be closed' },
            { status: 400 }
          );
        }

        // Update budget using service
        const closedBudget = await budgetService.updateBudgetStatus(
          budgetId,
          'closed',
          user.id
        );

        // Return updated budget
        return NextResponse.json({
          success: true,
          message: 'Budget closed successfully',
          budget: closedBudget
        });

      case 'approve':
        // Check permissions
        const hasApprovePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR
        ]);

        if (!hasApprovePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve budgets' },
            { status: 403 }
          );
        }

        // Check if budget can be approved
        if (budgetCheck.status !== 'draft' && budgetCheck.status !== 'pending_approval') {
          return NextResponse.json(
            { error: 'Only draft or pending approval budgets can be approved' },
            { status: 400 }
          );
        }

        // Update budget using service
        const approvedBudget = await budgetService.updateBudgetStatus(
          budgetId,
          'approved',
          user.id
        );

        // Return updated budget
        return NextResponse.json({
          success: true,
          message: 'Budget approved successfully',
          budget: approvedBudget
        });

      case 'reject':
        // Check permissions
        const hasRejectPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR
        ]);

        if (!hasRejectPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to reject budgets' },
            { status: 403 }
          );
        }

        // Check if budget can be rejected
        if (budgetCheck.status !== 'draft' && budgetCheck.status !== 'pending_approval') {
          return NextResponse.json(
            { error: 'Only draft or pending approval budgets can be rejected' },
            { status: 400 }
          );
        }

        // Validate rejection reason
        if (!body.rejectionReason) {
          return NextResponse.json(
            { error: 'Rejection reason is required' },
            { status: 400 }
          );
        }

        // Update budget using service
        const rejectedBudget = await budgetService.updateBudgetStatus(
          budgetId,
          'rejected',
          user.id,
          body.rejectionReason
        );

        // Return updated budget
        return NextResponse.json({
          success: true,
          message: 'Budget rejected successfully',
          budget: rejectedBudget
        });

      case 'submit':
        // Check permissions
        const hasSubmitPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_OFFICER
        ]);

        if (!hasSubmitPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to submit budgets' },
            { status: 403 }
          );
        }

        // Check if budget can be submitted
        if (budgetCheck.status !== 'draft') {
          return NextResponse.json(
            { error: 'Only draft budgets can be submitted for approval' },
            { status: 400 }
          );
        }

        // Update budget using service
        const submittedBudget = await budgetService.updateBudgetStatus(
          budgetId,
          'pending_approval',
          user.id
        );

        // Return updated budget
        return NextResponse.json({
          success: true,
          message: 'Budget submitted for approval successfully',
          budget: submittedBudget
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: activate, close, approve, reject, submit' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error(`Error performing action on budget`, LogCategory.API, error);
    return errorService.createApiResponse(
      ErrorType.INTERNAL_SERVER,
      'BUDGET_ACTION_FAILED',
      error instanceof Error ? error.message : 'An unknown error occurred while performing budget action',
      'An unexpected error occurred while processing your request. Please try again later.',
      {
        endpoint: req.url,
        method: req.method,
        error: error instanceof Error ? error.stack : String(error)
      },
      500,
      ErrorSeverity.HIGH,
      undefined,
      ['Try the action again', 'Contact support if the problem persists']
    );
  }
}

// app/api/accounting/budget/[id]/variance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import budgetVarianceService from '@/lib/services/accounting/budget-variance-service';

/**
 * GET /api/accounting/budget/[id]/variance
 * Get budget variance analysis
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Validate budget ID
    if (!id) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }

    // Get budget variance analysis
    const varianceAnalysis = await budgetVarianceService.analyzeBudgetVariance(id);

    // Log the request
    logger.info('Budget variance analysis retrieved', LogCategory.API, {
      userId: user.id,
      budgetId: id
    });

    return NextResponse.json(varianceAnalysis);
  } catch (error: unknown) {
    logger.error('Error getting budget variance analysis', LogCategory.API, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

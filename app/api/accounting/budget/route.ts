// app/api/accounting/budget/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';
import { BudgetService } from '@/lib/services/accounting/budget-service';

// Create a budget service instance
const budgetService = new BudgetService();

// Budget validation schema
const budgetSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, 'Fiscal year is required'),
  startDate: z.string().or(z.date()),
  endDate: z.string().or(z.date()),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed']).default('draft'),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear');
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    // If ID is provided, get a specific budget with details
    if (id) {
      const budget = await budgetService.getBudgetWithDetails(id);

      if (!budget) {
        return NextResponse.json(
          { error: 'Budget not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ budget });
    }

    // Build filter for listing budgets
    const filter: Record<string, any> = {};

    // Add fiscal year filter if provided
    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Add status filter if provided
    if (status) {
      filter.status = status;
    }

    // Get budgets with pagination
    const result = await budgetService.getBudgets(filter, {
      page,
      limit,
      sort: { createdAt: -1 },
      populate: [
        { path: 'createdBy', select: 'firstName lastName email' },
        { path: 'approvedBy', select: 'firstName lastName email' }
      ]
    });

    // Return budgets
    return NextResponse.json({
      budgets: result.budgets,
      pagination: {
        totalCount: result.totalCount,
        totalPages: result.totalPages,
        currentPage: result.currentPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching budget data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = budgetSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Add created by and convert dates
    const budgetData = {
      ...validationResult.data,
      createdBy: user.id,
      startDate: new Date(validationResult.data.startDate),
      endDate: new Date(validationResult.data.endDate)
    };

    // Create budget using the updated service
    const budget = await budgetService.createBudget(budgetData);

    return NextResponse.json(
      {
        success: true,
        message: 'Budget created successfully',
        budget
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating budget', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

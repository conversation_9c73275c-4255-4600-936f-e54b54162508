// app/api/accounting/budget/item/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { Budget, BudgetItem, BudgetSubcategory } from '@/models/accounting/Budget';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import BudgetService from '@/lib/services/accounting/budget-service';

// Create a budget service instance
const budgetService = new BudgetService();

/**
 * GET /api/accounting/budget/item/[id]
 * Get a specific budget item
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Find item
    const item = await BudgetItem.findById(id)
      .populate('parentCategory', 'name type')
      .populate('parentSubcategory', 'name')
      .populate('budget', 'name status')
      .lean();

    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      );
    }

    // Return item
    return NextResponse.json({
      success: true,
      item
    });
  } catch (error: unknown) {
    logger.error(`Error getting budget item`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/budget/item/[id]
 * Update a budget item
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Find item
    const item = await BudgetItem.findById(id);
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      );
    }

    // Find budget
    const budget = await Budget.findById(item.budget);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Update item fields
    if (body.name !== undefined) {
      item.name = body.name;
    }
    if (body.description !== undefined) {
      item.description = body.description;
    }
    if (body.quantity !== undefined) {
      item.quantity = body.quantity;
    }
    if (body.frequency !== undefined) {
      item.frequency = body.frequency;
    }
    if (body.unitCost !== undefined) {
      item.unitCost = body.unitCost;
    }

    // Calculate amount
    item.amount = item.quantity * item.frequency * item.unitCost;

    // Update subcategory if provided
    if (body.subcategoryId !== undefined) {
      if (body.subcategoryId) {
        // Verify subcategory exists and belongs to this category
        const subcategory = await BudgetSubcategory.findById(body.subcategoryId);
        if (!subcategory) {
          return NextResponse.json(
            { error: 'Subcategory not found' },
            { status: 404 }
          );
        }

        if (subcategory.parentCategory.toString() !== item.parentCategory.toString()) {
          return NextResponse.json(
            { error: 'Subcategory does not belong to this category' },
            { status: 400 }
          );
        }

        item.parentSubcategory = body.subcategoryId;
      } else {
        // Remove subcategory
        item.parentSubcategory = undefined;
      }
    }

    // Save item
    await item.save();

    // Recalculate budget totals
    await budgetService.calculateBudgetTotals(item.budget.toString());

    // Return updated item
    return NextResponse.json({
      success: true,
      message: 'Item updated successfully',
      item
    });
  } catch (error: unknown) {
    logger.error(`Error updating budget item`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/budget/item/[id]
 * Delete a budget item
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Find item
    const item = await BudgetItem.findById(id);
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      );
    }

    // Find budget
    const budget = await Budget.findById(item.budget);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Delete item
    await BudgetItem.findByIdAndDelete(id);

    // Recalculate budget totals
    await budgetService.calculateBudgetTotals(item.budget.toString());

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Item deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting budget item`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

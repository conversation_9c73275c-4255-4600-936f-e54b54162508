// app/api/accounting/budget/item/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';
import BudgetService from '@/lib/services/accounting/budget-service';

export const runtime = 'nodejs';



// Create a budget service instance
const budgetService = new BudgetService();

// Budget item validation schema
const itemSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  quantity: z.number().min(0, 'Quantity must be a positive number'),
  frequency: z.number().min(1, 'Frequency must be at least 1'),
  unitCost: z.number().min(0, 'Unit cost must be a positive number'),
  amount: z.number().optional(),
  parentCategory: z.string().min(1, 'Parent category ID is required'),
  parentSubcategory: z.string().optional(),
  budget: z.string().min(1, 'Budget ID is required'),
});

// Bulk import validation schema
const bulkImportSchema = z.object({
  budgetId: z.string().min(1, 'Budget ID is required'),
  items: z.array(z.object({
    categoryName: z.string().min(2, 'Category name must be at least 2 characters'),
    categoryType: z.enum(['income', 'expense']),
    subcategoryName: z.string().optional(),
    name: z.string().min(2, 'Item name must be at least 2 characters'),
    description: z.string().optional(),
    quantity: z.number().min(0, 'Quantity must be a positive number'),
    frequency: z.number().min(1, 'Frequency must be at least 1'),
    unitCost: z.number().min(0, 'Unit cost must be a positive number'),
  })).min(1, 'At least one item is required'),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');

    if (!budgetId) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }

    // Get BudgetItem model
    const BudgetItem = mongoose.models.BudgetItem || mongoose.model('BudgetItem');

    // Build filter
    const filter: Record<string, any> = { budget: budgetId };
    if (categoryId) {
      filter.parentCategory = categoryId;
    }
    if (subcategoryId) {
      filter.parentSubcategory = subcategoryId;
    }

    // Get items
    const items = await BudgetItem.find(filter)
      .sort({ name: 1 })
      .lean();

    // Return items
    return NextResponse.json({ items });
  } catch (error: unknown) {
    logger.error('Error fetching budget items', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const data = await req.json();

    // Check if this is a bulk import
    if (data.items && Array.isArray(data.items)) {
      // Validate bulk import data
      const validationResult = bulkImportSchema.safeParse(data);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: validationResult.error.errors[0].message },
          { status: 400 }
        );
      }

      // Import items
      const budget = await budgetService.importBudgetItems(
        validationResult.data.budgetId,
        validationResult.data.items
      );

      // Return updated budget
      return NextResponse.json({
        success: true,
        message: `${validationResult.data.items.length} budget items imported successfully`,
        budget
      }, { status: 201 });
    } else {
      // Validate single item data
      const validationResult = itemSchema.safeParse(data);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: validationResult.error.errors[0].message },
          { status: 400 }
        );
      }

      // Create item
      const item = await budgetService.createBudgetItem(validationResult.data);

      // Return created item
      return NextResponse.json({
        success: true,
        message: 'Budget item created successfully',
        item
      }, { status: 201 });
    }
  } catch (error: unknown) {
    logger.error('Error creating budget item', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

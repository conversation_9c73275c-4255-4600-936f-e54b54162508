// app/api/accounting/budget/impact/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget, BudgetCategory, BudgetSubcategory } from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/budget/impact
 * Calculate the impact of a transaction on a budget
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const transactionType = searchParams.get('transactionType') as 'income' | 'expense';
    const amount = parseFloat(searchParams.get('amount') || '0');
    const transactionId = searchParams.get('transactionId');

    // Validate required parameters
    if (!budgetId || !categoryId || !transactionType || isNaN(amount)) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Validate category ID
    if (!mongoose.Types.ObjectId.isValid(categoryId)) {
      return NextResponse.json({ error: 'Invalid category ID' }, { status: 400 });
    }

    // Validate subcategory ID if provided
    if (subcategoryId && !mongoose.Types.ObjectId.isValid(subcategoryId)) {
      return NextResponse.json({ error: 'Invalid subcategory ID' }, { status: 400 });
    }

    // Fetch budget
    const budget = await Budget.findById(budgetId);
    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Fetch category
    const category = await BudgetCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 });
    }

    // Fetch subcategory if provided
    let subcategory = null;
    if (subcategoryId) {
      subcategory = await BudgetSubcategory.findById(subcategoryId);
      if (!subcategory) {
        return NextResponse.json({ error: 'Subcategory not found' }, { status: 404 });
      }
    }

    // Calculate current actual amount
    let currentAmount = 0;
    if (transactionType === 'income') {
      const incomeQuery: Record<string, any> = {
        budgetCategory: new mongoose.Types.ObjectId(categoryId),
        status: 'received',
        appliedToBudget: true
      };

      if (subcategoryId) {
        incomeQuery.budgetSubcategory = new mongoose.Types.ObjectId(subcategoryId);
      }

      // Exclude current transaction if updating
      if (transactionId) {
        incomeQuery._id = { $ne: new mongoose.Types.ObjectId(transactionId) };
      }

      const incomeResult = await Income.aggregate([
        { $match: incomeQuery },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      currentAmount = incomeResult.length > 0 ? incomeResult[0].total : 0;
    } else {
      const expenseQuery: Record<string, any> = {
        budgetCategory: new mongoose.Types.ObjectId(categoryId),
        status: 'paid',
        appliedToBudget: true
      };

      if (subcategoryId) {
        expenseQuery.budgetSubcategory = new mongoose.Types.ObjectId(subcategoryId);
      }

      // Exclude current transaction if updating
      if (transactionId) {
        expenseQuery._id = { $ne: new mongoose.Types.ObjectId(transactionId) };
      }

      const expenseResult = await Expense.aggregate([
        { $match: expenseQuery },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      currentAmount = expenseResult.length > 0 ? expenseResult[0].total : 0;
    }

    // Calculate new amount
    const newAmount = currentAmount + amount;

    // Get budgeted amount
    const budgetedAmount = subcategory ? subcategory.total : category.total;

    // Calculate utilization percentages
    const currentUtilization = budgetedAmount > 0 ? (currentAmount / budgetedAmount) * 100 : 0;
    const newUtilization = budgetedAmount > 0 ? (newAmount / budgetedAmount) * 100 : 0;
    const utilizationChange = newUtilization - currentUtilization;

    // Check if over budget
    const isOverBudget = newAmount > budgetedAmount;

    // Return impact data
    return NextResponse.json({
      budgetId: budget.id,
      budgetName: budget.name,
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subcategory ? subcategory.id : null,
      subcategoryName: subcategory ? subcategory.name : null,
      currentAmount,
      newAmount,
      impact: amount,
      budgetedAmount,
      currentUtilization,
      newUtilization,
      utilizationChange,
      isOverBudget
    });
  } catch (error: unknown) {
    logger.error('Error calculating budget impact', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to calculate budget impact' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/forecasting/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetForecastingService } from '@/lib/services/accounting/budget-forecasting-service';
import { z } from 'zod';

// Request validation schema
const generateForecastSchema = z.object({
  budgetId: z.string().min(1, 'Budget ID is required'),
  forecastMonths: z.number().min(1).max(36).default(12),
  forecastType: z.enum(['linear', 'seasonal', 'growth', 'ai']).default('ai')
});

/**
 * POST /api/accounting/budget/forecasting/generate
 * Generate budget forecast using AI and statistical models
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.ACCOUNTANT,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to generate budget forecasts' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = generateForecastSchema.parse(body);

    logger.info('Generating budget forecast', LogCategory.ACCOUNTING, {
      budgetId: validatedData.budgetId,
      forecastMonths: validatedData.forecastMonths,
      forecastType: validatedData.forecastType,
      userId: user.id
    });

    // Generate forecast
    const forecast = await budgetForecastingService.generateBudgetForecast(
      validatedData.budgetId,
      validatedData.forecastMonths,
      validatedData.forecastType
    );

    logger.info('Budget forecast generated successfully', LogCategory.ACCOUNTING, {
      budgetId: validatedData.budgetId,
      forecastType: validatedData.forecastType,
      averageConfidence: forecast.averageConfidence,
      totalForecastedNet: forecast.totalForecastedNet
    });

    return NextResponse.json(forecast);

  } catch (error) {
    logger.error('Error generating budget forecast', LogCategory.ACCOUNTING, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Budget not found') {
        return NextResponse.json(
          { error: 'Budget not found' },
          { status: 404 }
        );
      }

      if (error.message.includes('Insufficient historical data')) {
        return NextResponse.json(
          { 
            error: 'Insufficient historical data for forecast generation',
            details: 'At least 2 months of historical data is required for accurate forecasting'
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error while generating forecast' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/transaction-link/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '../../../../../lib/backend/auth/auth';
import { logger, LogCategory } from '../../../../../lib/backend/utils/logger';
import { connectToDatabase } from '../../../../../lib/backend/database';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



interface BudgetLinkData {
  transactionId: string;
  transactionType: 'income' | 'expense';
  transactionAmount: number;
  transactionDate: Date;
  budgetId: string;
  budgetName: string;
  categoryId: string;
  categoryName: string;
  subcategoryId?: string;
  subcategoryName?: string;
  isLinked: boolean;
  linkStatus: 'linked' | 'pending' | 'failed' | 'unlinked';
  budgetImpact: {
    beforeAmount: number;
    afterAmount: number;
    utilizationBefore: number;
    utilizationAfter: number;
    remainingBudget: number;
    isOverBudget: boolean;
  };
  lastUpdated: Date;
}

// GET - Check transaction budget link status
export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const transactionId = searchParams.get('transactionId');
    const transactionType = searchParams.get('transactionType') as 'income' | 'expense';
    const budgetId = searchParams.get('budgetId');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const amount = parseFloat(searchParams.get('amount') || '0');

    if (!transactionId || !transactionType || !budgetId || !categoryId) {
      return NextResponse.json({ 
        error: 'Missing required parameters: transactionId, transactionType, budgetId, categoryId' 
      }, { status: 400 });
    }

    // Validate ObjectIds
    if (!ObjectId.isValid(budgetId) || !ObjectId.isValid(categoryId)) {
      return NextResponse.json({ error: 'Invalid budget or category ID' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const db = mongoose.connection.db;

    if (!db) {
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    // Get budget and category details
    const [budget, transaction] = await Promise.all([
      db.collection('budgets').findOne({ _id: new ObjectId(budgetId) }),
      db.collection(transactionType === 'income' ? 'incomes' : 'expenses').findOne({
        _id: new ObjectId(transactionId)
      })
    ]);

    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    // Find the category in the budget
    const category = budget.categories?.find((cat: any) => 
      cat._id?.toString() === categoryId || cat.id === categoryId
    );

    if (!category) {
      return NextResponse.json({ error: 'Category not found in budget' }, { status: 404 });
    }

    // Check if transaction is already linked
    const isLinked = transaction.budget?.toString() === budgetId &&
                    transaction.budgetCategory?.toString() === categoryId;

    // Calculate current category utilization
    const currentActual = await db.collection(transactionType === 'income' ? 'incomes' : 'expenses')
      .aggregate([
        {
          $match: {
            budget: new ObjectId(budgetId),
            budgetCategory: new ObjectId(categoryId),
            _id: { $ne: new ObjectId(transactionId) } // Exclude current transaction
          }
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' }
          }
        }
      ]).toArray();

    const beforeAmount = currentActual[0]?.totalAmount || 0;
    const afterAmount = beforeAmount + amount;
    const budgetedAmount = category.budgeted || 0;
    
    const utilizationBefore = budgetedAmount > 0 ? (beforeAmount / budgetedAmount) * 100 : 0;
    const utilizationAfter = budgetedAmount > 0 ? (afterAmount / budgetedAmount) * 100 : 0;
    const remainingBudget = budgetedAmount - afterAmount;
    const isOverBudget = afterAmount > budgetedAmount;

    // Find subcategory if specified
    let subcategoryName: string | undefined;
    if (subcategoryId && category.subcategories) {
      const subcategory = category.subcategories.find((sub: any) => 
        sub._id?.toString() === subcategoryId || sub.id === subcategoryId
      );
      subcategoryName = subcategory?.name;
    }

    const linkData: BudgetLinkData = {
      transactionId,
      transactionType,
      transactionAmount: amount,
      transactionDate: transaction.date,
      budgetId,
      budgetName: budget.name,
      categoryId,
      categoryName: category.name,
      subcategoryId: subcategoryId || undefined,
      subcategoryName,
      isLinked,
      linkStatus: isLinked ? 'linked' : 'unlinked',
      budgetImpact: {
        beforeAmount,
        afterAmount,
        utilizationBefore,
        utilizationAfter,
        remainingBudget,
        isOverBudget
      },
      lastUpdated: new Date()
    };

    return NextResponse.json(linkData);

  } catch (error) {
    logger.error('Error checking transaction budget link', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to check transaction budget link' },
      { status: 500 }
    );
  }
}

// POST - Link transaction to budget
export async function POST(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { 
      transactionId, 
      transactionType, 
      budgetId, 
      categoryId, 
      subcategoryId, 
      amount 
    } = body;

    if (!transactionId || !transactionType || !budgetId || !categoryId) {
      return NextResponse.json({ 
        error: 'Missing required fields: transactionId, transactionType, budgetId, categoryId' 
      }, { status: 400 });
    }

    // Validate ObjectIds
    if (!ObjectId.isValid(transactionId) || !ObjectId.isValid(budgetId) || !ObjectId.isValid(categoryId)) {
      return NextResponse.json({ error: 'Invalid transaction, budget, or category ID' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const db = mongoose.connection.db;

    if (!db) {
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    // Update the transaction with budget link
    const updateData: any = {
      budget: new ObjectId(budgetId),
      budgetCategory: new ObjectId(categoryId),
      updatedAt: new Date(),
      updatedBy: user.id
    };

    if (subcategoryId && ObjectId.isValid(subcategoryId)) {
      updateData.budgetSubcategory = new ObjectId(subcategoryId);
    }

    const result = await db.collection(transactionType === 'income' ? 'incomes' : 'expenses')
      .updateOne(
        { _id: new ObjectId(transactionId) },
        { $set: updateData }
      );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    logger.info('Transaction linked to budget successfully', LogCategory.ACCOUNTING, {
      transactionId,
      transactionType,
      budgetId,
      categoryId,
      subcategoryId,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Transaction linked to budget successfully'
    });

  } catch (error) {
    logger.error('Error linking transaction to budget', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to link transaction to budget' },
      { status: 500 }
    );
  }
}

// DELETE - Unlink transaction from budget
export async function DELETE(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { transactionId, budgetId, categoryId } = body;

    if (!transactionId) {
      return NextResponse.json({ 
        error: 'Missing required field: transactionId' 
      }, { status: 400 });
    }

    // Validate ObjectId
    if (!ObjectId.isValid(transactionId)) {
      return NextResponse.json({ error: 'Invalid transaction ID' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const db = mongoose.connection.db;

    if (!db) {
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    // First, find the transaction to determine its type
    const [incomeTransaction, expenseTransaction] = await Promise.all([
      db.collection('incomes').findOne({ _id: new ObjectId(transactionId) }),
      db.collection('expenses').findOne({ _id: new ObjectId(transactionId) })
    ]);

    const transaction = incomeTransaction || expenseTransaction;
    const transactionType = incomeTransaction ? 'incomes' : 'expenses';

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    // Remove budget link from transaction
    const result = await db.collection(transactionType)
      .updateOne(
        { _id: new ObjectId(transactionId) },
        {
          $unset: {
            budget: 1,
            budgetCategory: 1,
            budgetSubcategory: 1
          },
          $set: {
            updatedAt: new Date(),
            updatedBy: user.id
          }
        }
      );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    logger.info('Transaction unlinked from budget successfully', LogCategory.ACCOUNTING, {
      transactionId,
      transactionType,
      budgetId,
      categoryId,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Transaction unlinked from budget successfully'
    });

  } catch (error) {
    logger.error('Error unlinking transaction from budget', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to unlink transaction from budget' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Budget } from '@/models/accounting/Budget';
import { BudgetCategory, BudgetItem } from '@/models/accounting/Budget';
import { z } from 'zod';

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  budgetIds: z.array(z.string()).min(1, 'At least one budget ID is required'),
  deleteRelatedData: z.boolean().default(true),
  confirmDeletion: z.boolean().default(false)
});

/**
 * POST /api/accounting/budget/bulk-delete
 * Bulk delete budgets with safety checks and related data cleanup
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions - only high-level users can bulk delete
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk delete operations' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = bulkDeleteSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { budgetIds, deleteRelatedData, confirmDeletion } = validationResult.data;

    logger.info('Bulk delete budgets requested', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetIds,
      deleteRelatedData,
      confirmDeletion
    });

    // Safety check - require confirmation for bulk delete
    if (!confirmDeletion) {
      return NextResponse.json(
        { error: 'Confirmation required for bulk delete operation' },
        { status: 400 }
      );
    }

    // Find budgets to delete
    const budgetsToDelete = await Budget.find({
      _id: { $in: budgetIds }
    }).select('_id name status fiscalYear');

    if (budgetsToDelete.length === 0) {
      return NextResponse.json(
        { error: 'No budgets found with the provided IDs' },
        { status: 404 }
      );
    }

    // Check for active budgets - prevent deletion of active budgets
    const activeBudgets = budgetsToDelete.filter(budget => budget.status === 'active');
    if (activeBudgets.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete active budgets',
          activeBudgets: activeBudgets.map(b => ({ id: b._id, name: b.name }))
        },
        { status: 400 }
      );
    }

    const deletionResults = {
      budgetsDeleted: 0,
      categoriesDeleted: 0,
      itemsDeleted: 0,
      errors: [] as string[]
    };

    // Process each budget deletion
    for (const budget of budgetsToDelete) {
      try {
        if (deleteRelatedData) {
          // Delete related budget items first
          const itemsDeleted = await BudgetItem.deleteMany({ budget: budget._id });
          deletionResults.itemsDeleted += itemsDeleted.deletedCount || 0;

          // Delete related budget categories
          const categoriesDeleted = await BudgetCategory.deleteMany({ budget: budget._id });
          deletionResults.categoriesDeleted += categoriesDeleted.deletedCount || 0;
        }

        // Delete the budget
        await Budget.findByIdAndDelete(budget._id);
        deletionResults.budgetsDeleted++;

        logger.info('Budget deleted successfully', LogCategory.ACCOUNTING, {
          budgetId: budget._id,
          budgetName: budget.name,
          userId: user.id
        });

      } catch (error) {
        const errorMessage = `Failed to delete budget ${budget.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        deletionResults.errors.push(errorMessage);
        
        logger.error('Error deleting budget', LogCategory.ACCOUNTING, {
          budgetId: budget._id,
          budgetName: budget.name,
          error
        });
      }
    }

    // Return results
    const response = {
      success: deletionResults.budgetsDeleted > 0,
      message: `Successfully deleted ${deletionResults.budgetsDeleted} budget(s)`,
      results: deletionResults
    };

    if (deletionResults.errors.length > 0) {
      response.message += ` with ${deletionResults.errors.length} error(s)`;
    }

    return NextResponse.json(response);

  } catch (error) {
    logger.error('Error in bulk delete budgets', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Internal server error during bulk delete operation' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/accounting/budget/bulk-delete
 * Get information about budgets that can be deleted (safety check)
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get budget IDs from query params
    const { searchParams } = new URL(req.url);
    const budgetIdsParam = searchParams.get('budgetIds');
    
    if (!budgetIdsParam) {
      return NextResponse.json(
        { error: 'Budget IDs are required' },
        { status: 400 }
      );
    }

    const budgetIds = budgetIdsParam.split(',');

    // Get budget information for safety check
    const budgets = await Budget.find({
      _id: { $in: budgetIds }
    }).select('_id name status fiscalYear startDate endDate');

    // Get related data counts
    const budgetInfo = await Promise.all(
      budgets.map(async (budget) => {
        const categoriesCount = await BudgetCategory.countDocuments({ budget: budget._id });
        const itemsCount = await BudgetItem.countDocuments({ budget: budget._id });
        
        return {
          id: budget._id,
          name: budget.name,
          status: budget.status,
          fiscalYear: budget.fiscalYear,
          canDelete: budget.status !== 'active',
          relatedData: {
            categories: categoriesCount,
            items: itemsCount
          }
        };
      })
    );

    return NextResponse.json({
      budgets: budgetInfo,
      summary: {
        total: budgets.length,
        canDelete: budgetInfo.filter(b => b.canDelete).length,
        cannotDelete: budgetInfo.filter(b => !b.canDelete).length
      }
    });

  } catch (error) {
    logger.error('Error getting bulk delete info', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

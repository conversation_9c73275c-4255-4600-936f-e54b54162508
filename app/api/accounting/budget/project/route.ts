import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import projectBudgetIntegrationService from '@/lib/services/accounting/project-budget-integration-service';

/**
 * POST /api/accounting/budget/project
 * Link a project to a budget
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.projectId || !body.budgetId || !body.categoryId) {
      return NextResponse.json(
        { error: 'Project ID, budget ID, and category ID are required' },
        { status: 400 }
      );
    }

    // Link project to budget
    const project = await projectBudgetIntegrationService.linkProjectToBudget(
      body.projectId,
      body.budgetId,
      body.categoryId
    );

    // Log the action
    logger.info('Project linked to budget', LogCategory.ACCOUNTING, {
      userId: user.id,
      projectId: body.projectId,
      budgetId: body.budgetId,
      categoryId: body.categoryId
    });

    return NextResponse.json({
      success: true,
      project
    });
  } catch (error: unknown) {
    logger.error('Error linking project to budget', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

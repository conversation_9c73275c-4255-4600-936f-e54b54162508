// app/api/accounting/budget/validate-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Budget } from '@/models/accounting/Budget';
import { z } from 'zod';
import * as XLSX from 'xlsx';

// Validation schema for import validation request
const validateImportSchema = z.object({
  data: z.array(z.record(z.any())),
  importType: z.enum(['budgets', 'categories', 'items']),
  budgetId: z.string().optional(),
  validationLevel: z.enum(['basic', 'strict', 'comprehensive']).default('basic')
});

interface ValidationError {
  row: number;
  field: string;
  value: any;
  error: string;
  severity: 'error' | 'warning' | 'info';
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  summary: {
    totalRows: number;
    validRows: number;
    errorRows: number;
    warningRows: number;
  };
  processedData: any[];
}

/**
 * POST /api/accounting/budget/validate-import
 * Validate import data before processing
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = validateImportSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { data, importType, budgetId, validationLevel } = validationResult.data;

    logger.info('Import validation requested', LogCategory.ACCOUNTING, {
      userId: user.id,
      importType,
      rowCount: data.length,
      validationLevel
    });

    // Perform validation based on import type
    let validationResults: ValidationResult;

    switch (importType) {
      case 'budgets':
        validationResults = await validateBudgetData(data, validationLevel);
        break;
      case 'categories':
        validationResults = await validateCategoryData(data, budgetId, validationLevel);
        break;
      case 'items':
        validationResults = await validateItemData(data, budgetId, validationLevel);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid import type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      validation: validationResults
    });

  } catch (error) {
    logger.error('Error validating import data', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Internal server error during validation' },
      { status: 500 }
    );
  }
}

/**
 * Validate budget data
 */
async function validateBudgetData(data: any[], validationLevel: string): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const processedData: any[] = [];

  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNumber = i + 1;
    let hasErrors = false;

    // Required fields validation
    if (!row.name || typeof row.name !== 'string' || row.name.trim().length === 0) {
      errors.push({
        row: rowNumber,
        field: 'name',
        value: row.name,
        error: 'Budget name is required',
        severity: 'error'
      });
      hasErrors = true;
    }

    if (!row.fiscalYear || typeof row.fiscalYear !== 'string') {
      errors.push({
        row: rowNumber,
        field: 'fiscalYear',
        value: row.fiscalYear,
        error: 'Fiscal year is required',
        severity: 'error'
      });
      hasErrors = true;
    }

    // Date validation
    if (row.startDate) {
      const startDate = new Date(row.startDate);
      if (isNaN(startDate.getTime())) {
        errors.push({
          row: rowNumber,
          field: 'startDate',
          value: row.startDate,
          error: 'Invalid start date format',
          severity: 'error'
        });
        hasErrors = true;
      }
    }

    if (row.endDate) {
      const endDate = new Date(row.endDate);
      if (isNaN(endDate.getTime())) {
        errors.push({
          row: rowNumber,
          field: 'endDate',
          value: row.endDate,
          error: 'Invalid end date format',
          severity: 'error'
        });
        hasErrors = true;
      }
    }

    // Date range validation
    if (row.startDate && row.endDate) {
      const startDate = new Date(row.startDate);
      const endDate = new Date(row.endDate);
      if (startDate >= endDate) {
        errors.push({
          row: rowNumber,
          field: 'dateRange',
          value: `${row.startDate} - ${row.endDate}`,
          error: 'Start date must be before end date',
          severity: 'error'
        });
        hasErrors = true;
      }
    }

    // Status validation
    const validStatuses = ['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed'];
    if (row.status && !validStatuses.includes(row.status)) {
      warnings.push({
        row: rowNumber,
        field: 'status',
        value: row.status,
        error: `Invalid status. Will default to 'draft'. Valid statuses: ${validStatuses.join(', ')}`,
        severity: 'warning'
      });
    }

    // Comprehensive validation
    if (validationLevel === 'comprehensive') {
      // Check for duplicate names
      const existingBudget = await Budget.findOne({ 
        name: row.name,
        fiscalYear: row.fiscalYear 
      });
      
      if (existingBudget) {
        warnings.push({
          row: rowNumber,
          field: 'name',
          value: row.name,
          error: 'Budget with this name and fiscal year already exists',
          severity: 'warning'
        });
      }
    }

    // Process valid data
    if (!hasErrors) {
      processedData.push({
        name: row.name?.trim(),
        description: row.description?.trim() || '',
        fiscalYear: row.fiscalYear,
        startDate: row.startDate ? new Date(row.startDate) : undefined,
        endDate: row.endDate ? new Date(row.endDate) : undefined,
        status: validStatuses.includes(row.status) ? row.status : 'draft',
        totalIncome: parseFloat(row.totalIncome) || 0,
        totalExpense: parseFloat(row.totalExpense) || 0
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalRows: data.length,
      validRows: processedData.length,
      errorRows: errors.filter(e => e.severity === 'error').length,
      warningRows: warnings.length
    },
    processedData
  };
}

/**
 * Validate category data
 */
async function validateCategoryData(data: any[], budgetId: string | undefined, validationLevel: string): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const processedData: any[] = [];

  // Check if budget exists
  if (budgetId) {
    const budget = await Budget.findById(budgetId);
    if (!budget) {
      errors.push({
        row: 0,
        field: 'budgetId',
        value: budgetId,
        error: 'Budget not found',
        severity: 'error'
      });
      return {
        isValid: false,
        errors,
        warnings,
        summary: { totalRows: data.length, validRows: 0, errorRows: 1, warningRows: 0 },
        processedData: []
      };
    }
  }

  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNumber = i + 1;
    let hasErrors = false;

    // Required fields validation
    if (!row.name || typeof row.name !== 'string' || row.name.trim().length === 0) {
      errors.push({
        row: rowNumber,
        field: 'name',
        value: row.name,
        error: 'Category name is required',
        severity: 'error'
      });
      hasErrors = true;
    }

    // Type validation
    const validTypes = ['income', 'expense'];
    if (!row.type || !validTypes.includes(row.type)) {
      errors.push({
        row: rowNumber,
        field: 'type',
        value: row.type,
        error: `Category type is required. Valid types: ${validTypes.join(', ')}`,
        severity: 'error'
      });
      hasErrors = true;
    }

    // Process valid data
    if (!hasErrors) {
      processedData.push({
        name: row.name.trim(),
        description: row.description?.trim() || '',
        type: row.type,
        budgetId: budgetId || row.budgetId
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalRows: data.length,
      validRows: processedData.length,
      errorRows: errors.filter(e => e.severity === 'error').length,
      warningRows: warnings.length
    },
    processedData
  };
}

/**
 * Validate item data
 */
async function validateItemData(data: any[], budgetId: string | undefined, validationLevel: string): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const processedData: any[] = [];

  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNumber = i + 1;
    let hasErrors = false;

    // Required fields validation
    if (!row.name || typeof row.name !== 'string' || row.name.trim().length === 0) {
      errors.push({
        row: rowNumber,
        field: 'name',
        value: row.name,
        error: 'Item name is required',
        severity: 'error'
      });
      hasErrors = true;
    }

    // Numeric validation
    const quantity = parseFloat(row.quantity);
    if (isNaN(quantity) || quantity <= 0) {
      errors.push({
        row: rowNumber,
        field: 'quantity',
        value: row.quantity,
        error: 'Quantity must be a positive number',
        severity: 'error'
      });
      hasErrors = true;
    }

    const unitCost = parseFloat(row.unitCost);
    if (isNaN(unitCost) || unitCost < 0) {
      errors.push({
        row: rowNumber,
        field: 'unitCost',
        value: row.unitCost,
        error: 'Unit cost must be a non-negative number',
        severity: 'error'
      });
      hasErrors = true;
    }

    // Process valid data
    if (!hasErrors) {
      processedData.push({
        name: row.name.trim(),
        description: row.description?.trim() || '',
        quantity: quantity,
        unitCost: unitCost,
        frequency: parseFloat(row.frequency) || 1,
        categoryName: row.categoryName?.trim(),
        categoryType: row.categoryType
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalRows: data.length,
      validRows: processedData.length,
      errorRows: errors.filter(e => e.severity === 'error').length,
      warningRows: warnings.length
    },
    processedData
  };
}

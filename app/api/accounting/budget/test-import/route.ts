// app/api/accounting/budget/test-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/services/accounting/budget-service';

/**
 * POST /api/accounting/budget/test-import
 * Test budget import with detailed logging
 */
export async function POST(req: NextRequest) {
  try {
    console.log('=== BUDGET IMPORT TEST START ===');

    // Step 1: Check authentication
    console.log('Step 1: Checking authentication...');
    const user = await getCurrentUser(req);
    if (!user) {
      console.log('❌ Authentication failed - no user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('✅ User authenticated:', { id: user.id, email: user.email });

    // Step 2: Connect to database
    console.log('Step 2: Connecting to database...');
    await connectToDatabase();
    console.log('✅ Database connected');

    // Step 3: Parse request body
    console.log('Step 3: Parsing request body...');
    const body = await req.json();
    console.log('✅ Request body parsed:', body);

    // Step 4: Create test budget data
    console.log('Step 4: Creating test budget data...');
    const testBudget = {
      name: 'Test Budget ' + new Date().toISOString(),
      description: 'Test budget for debugging',
      fiscalYear: '2024-2025',
      startDate: new Date('2024-07-01'),
      endDate: new Date('2025-06-30'),
      status: 'draft' as const,
      createdBy: user.id
    };
    console.log('✅ Test budget data created:', testBudget);

    // Step 5: Create budget using service
    console.log('Step 5: Creating budget using service...');
    const budgetService = new BudgetService();
    const createdBudget = await budgetService.createBudget(testBudget);
    console.log('✅ Budget created successfully:', createdBudget);

    // Step 6: Test bulk creation with detailed results
    console.log('Step 6: Testing bulk creation with detailed results...');
    const testBudgets = [
      {
        name: 'Bulk Test Budget 1 ' + new Date().toISOString(),
        description: 'Bulk test budget 1',
        fiscalYear: '2024-2025',
        startDate: new Date('2024-07-01'),
        endDate: new Date('2025-06-30'),
        status: 'draft' as const,
        createdBy: user.id
      },
      {
        name: 'Bulk Test Budget 2 ' + new Date().toISOString(),
        description: 'Bulk test budget 2',
        fiscalYear: '2025-2026',
        startDate: new Date('2025-07-01'),
        endDate: new Date('2026-06-30'),
        status: 'draft' as const,
        createdBy: user.id
      },
      {
        name: 'Duplicate Test Budget',
        description: 'This budget might already exist to test skip functionality',
        fiscalYear: '2024-2025',
        startDate: new Date('2024-07-01'),
        endDate: new Date('2025-06-30'),
        status: 'draft' as const,
        createdBy: user.id
      }
    ];

    const bulkResult = await budgetService.createBudgetsWithDetails(testBudgets);
    console.log('✅ Bulk creation completed:', bulkResult);

    console.log('=== BUDGET IMPORT TEST SUCCESS ===');

    return NextResponse.json({
      success: true,
      message: 'Budget import test completed successfully',
      results: {
        singleBudget: createdBudget,
        bulkResult: bulkResult,
        user: { id: user.id, email: user.email }
      }
    });

  } catch (error: unknown) {
    console.error('❌ BUDGET IMPORT TEST FAILED:', error);
    
    // Detailed error logging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        details: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : null
      },
      { status: 500 }
    );
  }
}

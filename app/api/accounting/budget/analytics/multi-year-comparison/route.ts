// app/api/accounting/budget/analytics/multi-year-comparison/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetAnalyticsService } from '@/lib/services/accounting/budget-analytics-service';

/**
 * GET /api/accounting/budget/analytics/multi-year-comparison
 * Get multi-year budget comparison analysis
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.ACCOUNTANT,
      UserRole.BUDGET_ANALYST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view multi-year comparison' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const yearsParam = searchParams.get('years');

    if (!yearsParam) {
      return NextResponse.json(
        { error: 'Years parameter is required. Provide comma-separated fiscal years.' },
        { status: 400 }
      );
    }

    const fiscalYears = yearsParam.split(',').map(year => year.trim());

    // Validate fiscal years
    if (fiscalYears.length === 0) {
      return NextResponse.json(
        { error: 'At least one fiscal year must be provided' },
        { status: 400 }
      );
    }

    logger.info('Getting multi-year budget comparison', LogCategory.ACCOUNTING, {
      fiscalYears,
      userId: user.id
    });

    // Get multi-year comparison
    const comparison = await budgetAnalyticsService.getMultiYearComparison(fiscalYears);

    return NextResponse.json(comparison);

  } catch (error) {
    logger.error('Error getting multi-year budget comparison', LogCategory.ACCOUNTING, error);
    
    return NextResponse.json(
      { error: 'Internal server error while getting multi-year comparison' },
      { status: 500 }
    );
  }
}

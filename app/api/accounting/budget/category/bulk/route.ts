// app/api/accounting/budget/category/bulk/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { BudgetCategoryService } from '@/lib/services/accounting/budget-category-service';
import { z } from 'zod';

// Schema for bulk category import
const CategoryImportSchema = z.object({
  categories: z.array(
    z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string().optional(),
      type: z.enum(['income', 'expense']),
      budgetId: z.string().min(1, 'Budget ID is required'),
    })
  ),
});

/**
 * POST /api/accounting/budget/category/bulk
 * Bulk import budget categories
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();

    // Validate request body
    const validationResult = CategoryImportSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { categories } = validationResult.data;

    // Create categories
    const categoryService = new BudgetCategoryService();
    const createdCategories = await categoryService.createCategories(categories);

    return NextResponse.json({ categories: createdCategories }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error importing categories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Failed to import categories' },
      { status: 500 }
    );
  }
}

// app/api/accounting/budget/category/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';
import BudgetService from '@/lib/services/accounting/budget-service';

export const runtime = 'nodejs';



// Create a budget service instance
const budgetService = new BudgetService();

// Budget category validation schema
const categorySchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  type: z.enum(['income', 'expense']),
  budget: z.string().min(1, 'Budget ID is required'),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const type = searchParams.get('type');

    if (!budgetId) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }

    // Get BudgetCategory model
    const BudgetCategory = mongoose.models.BudgetCategory || mongoose.model('BudgetCategory');

    // Build filter
    const filter: Record<string, any> = { budget: budgetId };
    if (type) {
      filter.type = type;
    }

    // Get categories
    const categories = await BudgetCategory.find(filter)
      .sort({ name: 1 })
      .lean();

    // Return categories
    return NextResponse.json({ categories });
  } catch (error: unknown) {
    logger.error('Error fetching budget categories', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = categorySchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Create category
    const category = await budgetService.createBudgetCategory(validationResult.data);

    // Return created category
    return NextResponse.json({
      success: true,
      message: 'Budget category created successfully',
      category
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating budget category', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}


// app/api/accounting/budget/payroll/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import payrollBudgetIntegrationService from '@/lib/services/accounting/payroll-budget-integration-service';

/**
 * GET /api/accounting/budget/payroll
 * Get payroll budget variance analysis
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const departmentId = searchParams.get('departmentId') || undefined;
    const month = searchParams.get('month') ? parseInt(searchParams.get('month') as string) : undefined;
    const quarter = searchParams.get('quarter') ? parseInt(searchParams.get('quarter') as string) : undefined;
    const year = searchParams.get('year') ? parseInt(searchParams.get('year') as string) : undefined;

    // Build period object
    const period: Record<string, number> = {};
    if (month) period.month = month;
    if (quarter) period.quarter = quarter;
    if (year) period.year = year;

    // Get payroll budget variance analysis
    const analysis = await payrollBudgetIntegrationService.analyzePayrollBudgetVariance(
      departmentId,
      Object.keys(period).length > 0 ? period : undefined
    );

    // Log the request
    logger.info('Payroll budget variance analysis retrieved', LogCategory.ACCOUNTING, {
      userId: user.id,
      departmentId,
      period
    });

    return NextResponse.json({
      success: true,
      analysis
    });
  } catch (error: unknown) {
    logger.error('Error getting payroll budget variance analysis', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget/payroll
 * Link a payroll run to a budget
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.payrollRunId || !body.budgetId || !body.categoryId) {
      return NextResponse.json(
        { error: 'Payroll run ID, budget ID, and category ID are required' },
        { status: 400 }
      );
    }

    // Link payroll run to budget
    const payrollRun = await payrollBudgetIntegrationService.linkPayrollRunToBudget(
      body.payrollRunId,
      body.budgetId,
      body.categoryId
    );

    // Log the action
    logger.info('Payroll run linked to budget', LogCategory.ACCOUNTING, {
      userId: user.id,
      payrollRunId: body.payrollRunId,
      budgetId: body.budgetId,
      categoryId: body.categoryId
    });

    return NextResponse.json({
      success: true,
      payrollRun
    });
  } catch (error: unknown) {
    logger.error('Error linking payroll run to budget', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import budgetTemplateService from '@/lib/services/accounting/budget-template-service';

/**
 * GET /api/accounting/budget/templates
 * Get budget templates
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isDefault = searchParams.get('isDefault') === 'true';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build filter - using any type for MongoDB query operators
    const filter: any = {};
    if (searchParams.has('isDefault')) {
      filter.isDefault = isDefault;
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortBy]: sortOrder === 'asc' ? 1 : -1
    };

    // Get templates
    const result = await budgetTemplateService.getTemplates(
      filter,
      {
        page,
        limit,
        sort,
        populate: 'createdBy'
      }
    );

    // Log the request
    logger.info('Budget templates retrieved', LogCategory.API, {
      userId: user.id,
      count: result.templates.length,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting budget templates', LogCategory.API, error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget/templates
 * Create a new budget template
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Template name is required' },
        { status: 400 }
      );
    }

    // Check if creating from budget
    if (body.budgetId) {
      // Create template from budget
      const template = await budgetTemplateService.createTemplateFromBudget(
        body.budgetId,
        {
          name: body.name,
          description: body.description,
          isDefault: body.isDefault,
          createdBy: user.id
        }
      );

      // Log the creation
      logger.info('Budget template created from budget', LogCategory.API, {
        userId: user.id,
        templateId: template.id,
        budgetId: body.budgetId
      });

      return NextResponse.json({ template }, { status: 201 });
    } else {
      // Create new template
      const template = await budgetTemplateService.createTemplate({
        name: body.name,
        description: body.description,
        isDefault: body.isDefault || false,
        categories: [],
        createdBy: user.id
      });

      // Log the creation
      logger.info('Budget template created', LogCategory.API, {
        userId: user.id,
        templateId: template.id
      });

      return NextResponse.json({ template }, { status: 201 });
    }
  } catch (error: unknown) {
    logger.error('Error creating budget template', LogCategory.API, error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}


import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import budgetTemplateService from '@/lib/services/accounting/budget-template-service';

/**
 * GET /api/accounting/budget/templates/[id]
 * Get a specific budget template
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Validate template ID
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Get template with details
    const template = await budgetTemplateService.getTemplateWithDetails(id);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Log the request
    logger.info('Budget template retrieved', LogCategory.API, {
      userId: user.id,
      templateId: id
    });

    return NextResponse.json({ template });
  } catch (error: unknown) {
    logger.error('Error getting budget template', LogCategory.API, error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/budget/templates/[id]
 * Delete a budget template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Validate template ID
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Delete template
    const deleted = await budgetTemplateService.deleteTemplate(id);

    if (!deleted) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Log the deletion
    logger.info('Budget template deleted', LogCategory.API, {
      userId: user.id,
      templateId: id
    });

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error deleting budget template', LogCategory.API, error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget/templates/[id]/create-budget
 * Create a budget from a template
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Validate template ID
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.fiscalYear || !body.startDate || !body.endDate) {
      return NextResponse.json(
        { error: 'Name, fiscal year, start date, and end date are required' },
        { status: 400 }
      );
    }

    // Check if creating budget from template
    if (request.nextUrl.pathname.endsWith('/create-budget')) {
      // Create budget from template
      const budget = await budgetTemplateService.createBudgetFromTemplate(
        id,
        {
          name: body.name,
          description: body.description,
          fiscalYear: body.fiscalYear,
          startDate: new Date(body.startDate),
          endDate: new Date(body.endDate),
          createdBy: user.id
        }
      );

      // Log the creation
      logger.info('Budget created from template', LogCategory.API, {
        userId: user.id,
        templateId: id,
        budgetId: budget.id
      });

      return NextResponse.json({ budget }, { status: 201 });
    } else {
      return NextResponse.json(
        { error: 'Invalid endpoint' },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    logger.error('Error creating budget from template', LogCategory.API, error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

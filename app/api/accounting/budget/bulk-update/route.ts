// app/api/accounting/budget/bulk-update/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Budget } from '@/models/accounting/Budget';
import { z } from 'zod';

// Validation schema for bulk update request
const bulkUpdateSchema = z.object({
  updates: z.array(z.object({
    budgetId: z.string(),
    data: z.object({
      name: z.string().optional(),
      description: z.string().optional(),
      status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed']).optional(),
      fiscalYear: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      totalIncome: z.number().optional(),
      totalExpense: z.number().optional()
    })
  })).min(1, 'At least one update is required'),
  validateOnly: z.boolean().default(false)
});

/**
 * POST /api/accounting/budget/bulk-update
 * Bulk update budgets with validation and error handling
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk update operations' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = bulkUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { updates, validateOnly } = validationResult.data;

    logger.info('Bulk update budgets requested', LogCategory.ACCOUNTING, {
      userId: user.id,
      updateCount: updates.length,
      validateOnly
    });

    const updateResults = {
      successful: [] as any[],
      failed: [] as any[],
      validated: [] as any[]
    };

    // Process each update
    for (const update of updates) {
      try {
        const { budgetId, data: updateData } = update;

        // Find the budget
        const budget = await Budget.findById(budgetId);
        if (!budget) {
          updateResults.failed.push({
            budgetId,
            error: 'Budget not found'
          });
          continue;
        }

        // Validate date constraints if dates are being updated
        if (updateData.startDate && updateData.endDate) {
          const startDate = new Date(updateData.startDate);
          const endDate = new Date(updateData.endDate);
          
          if (startDate >= endDate) {
            updateResults.failed.push({
              budgetId,
              budgetName: budget.name,
              error: 'Start date must be before end date'
            });
            continue;
          }
        }

        // Check if budget can be updated (status constraints)
        if (updateData.status && budget.status === 'active' && updateData.status !== 'active' && updateData.status !== 'closed') {
          updateResults.failed.push({
            budgetId,
            budgetName: budget.name,
            error: 'Active budgets can only be updated to closed status'
          });
          continue;
        }

        // If validation only, add to validated list
        if (validateOnly) {
          updateResults.validated.push({
            budgetId,
            budgetName: budget.name,
            currentData: {
              name: budget.name,
              status: budget.status,
              fiscalYear: budget.fiscalYear
            },
            proposedChanges: updateData
          });
          continue;
        }

        // Apply updates
        Object.assign(budget, updateData);

        // Save the updated budget
        const updatedBudget = await budget.save();

        updateResults.successful.push({
          budgetId,
          budgetName: updatedBudget.name,
          updatedFields: Object.keys(updateData),
          newData: {
            name: updatedBudget.name,
            status: updatedBudget.status,
            fiscalYear: updatedBudget.fiscalYear
          }
        });

        logger.info('Budget updated successfully', LogCategory.ACCOUNTING, {
          budgetId,
          budgetName: updatedBudget.name,
          updatedFields: Object.keys(updateData),
          userId: user.id
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        updateResults.failed.push({
          budgetId: update.budgetId,
          error: errorMessage
        });

        logger.error('Error updating budget', LogCategory.ACCOUNTING, {
          budgetId: update.budgetId,
          error
        });
      }
    }

    // Prepare response
    const response = {
      success: validateOnly ? true : updateResults.successful.length > 0,
      message: validateOnly 
        ? `Validated ${updateResults.validated.length} budget update(s)`
        : `Successfully updated ${updateResults.successful.length} budget(s)`,
      results: updateResults,
      summary: {
        total: updates.length,
        successful: updateResults.successful.length,
        failed: updateResults.failed.length,
        validated: updateResults.validated.length
      }
    };

    if (!validateOnly && updateResults.failed.length > 0) {
      response.message += ` with ${updateResults.failed.length} error(s)`;
    }

    return NextResponse.json(response);

  } catch (error) {
    logger.error('Error in bulk update budgets', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Internal server error during bulk update operation' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/accounting/budget/bulk-update
 * Get current data for budgets to be updated (for validation/preview)
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get budget IDs from query params
    const { searchParams } = new URL(req.url);
    const budgetIdsParam = searchParams.get('budgetIds');
    
    if (!budgetIdsParam) {
      return NextResponse.json(
        { error: 'Budget IDs are required' },
        { status: 400 }
      );
    }

    const budgetIds = budgetIdsParam.split(',');

    // Get current budget data
    const budgets = await Budget.find({
      _id: { $in: budgetIds }
    }).select('_id name description status fiscalYear startDate endDate totalIncome totalExpense');

    const budgetData = budgets.map(budget => ({
      id: budget._id,
      name: budget.name,
      description: budget.description,
      status: budget.status,
      fiscalYear: budget.fiscalYear,
      startDate: budget.startDate,
      endDate: budget.endDate,
      totalIncome: budget.totalIncome,
      totalExpense: budget.totalExpense,
      canUpdate: budget.status !== 'closed'
    }));

    return NextResponse.json({
      budgets: budgetData,
      summary: {
        total: budgets.length,
        canUpdate: budgetData.filter(b => b.canUpdate).length,
        cannotUpdate: budgetData.filter(b => !b.canUpdate).length
      }
    });

  } catch (error) {
    logger.error('Error getting bulk update info', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

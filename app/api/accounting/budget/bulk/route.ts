// app/api/accounting/budget/bulk/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/services/accounting/budget-service';
import { z } from 'zod';

// Schema for bulk budget import
const BudgetImportSchema = z.object({
  budgets: z.array(
    z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string().optional(),
      fiscalYear: z.string().min(1, 'Fiscal year is required'),
      startDate: z.string().min(1, 'Start date is required'),
      endDate: z.string().min(1, 'End date is required'),
      status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed']).optional().default('draft'),
    })
  ),
});

/**
 * POST /api/accounting/budget/bulk
 * Bulk import budgets
 */
export async function POST(req: NextRequest) {
  try {
    console.log('=== API: Budget bulk import endpoint called ===');

    // Step 1: Check authentication
    console.log('Step 1: Checking authentication...');
    const user = await getCurrentUser(req);
    if (!user) {
      console.log('❌ API: Unauthorized - no user');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('✅ API: User authenticated:', { id: user.id, email: user.email });

    // Step 2: Connect to database
    console.log('Step 2: Connecting to database...');
    await connectToDatabase();
    console.log('✅ API: Database connected');

    // Step 3: Parse request body
    console.log('Step 3: Parsing request body...');
    let body;
    try {
      body = await req.json();
      console.log('✅ API: Request body parsed:', JSON.stringify(body, null, 2));
    } catch (parseError) {
      console.error('❌ API: Error parsing request body:', parseError);
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    // Step 4: Validate request body
    console.log('Step 4: Validating request data...');
    const validationResult = BudgetImportSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('❌ API: Validation error:', validationResult.error.format());
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { budgets } = validationResult.data;
    console.log('✅ API: Validated budgets count:', budgets.length);
    console.log('✅ API: Validated budgets:', JSON.stringify(budgets, null, 2));

    // Step 5: Prepare budgets with user ID and proper dates
    console.log('Step 5: Preparing budgets with user ID and dates...');
    const budgetsWithUser = budgets.map((budget, index) => {
      console.log(`Processing budget ${index + 1}:`, budget);

      let startDate, endDate;
      try {
        startDate = new Date(budget.startDate);
        endDate = new Date(budget.endDate);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          throw new Error(`Invalid date format for budget: ${budget.name}`);
        }

        if (startDate >= endDate) {
          throw new Error(`Start date must be before end date for budget: ${budget.name}`);
        }
      } catch (dateError) {
        console.error(`❌ Date validation error for budget ${budget.name}:`, dateError);
        throw dateError;
      }

      return {
        ...budget,
        createdBy: user.id,
        startDate,
        endDate
      };
    });
    console.log('✅ API: Budgets prepared with user ID and dates:', budgetsWithUser.length);

    // Step 6: Create budgets using service with detailed results
    console.log('Step 6: Creating budgets using service...');
    const budgetService = new BudgetService();
    const result = await budgetService.createBudgetsWithDetails(budgetsWithUser);
    console.log('✅ API: Budget creation completed:', result.summary);
    console.log('✅ API: Created budgets:', result.created.length);
    console.log('✅ API: Skipped budgets:', result.skipped);

    if (result.created.length === 0 && result.skipped.length === 0) {
      console.warn('⚠️ API: No budgets were processed - this might indicate an issue');
      return NextResponse.json(
        {
          error: 'No budgets were processed',
          details: 'All budgets may have failed validation or processing',
          attempted: budgetsWithUser.length,
          created: 0,
          skipped: 0
        },
        { status: 400 }
      );
    }

    console.log('=== API: Budget bulk import completed successfully ===');

    return NextResponse.json({
      budgets: result.created,
      skipped: result.skipped,
      summary: {
        ...result.summary,
        success: true,
        message: result.skipped.length > 0
          ? `Successfully created ${result.created.length} budgets. ${result.skipped.length} budgets were skipped.`
          : `Successfully created ${result.created.length} budgets.`
      }
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('API: Error importing budgets:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to import budgets' },
      { status: 500 }
    );
  }
}

// app/api/accounting/journal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import JournalEntry from '@/models/accounting/JournalEntry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/journal
 * Get journal entries with optional filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const fiscalYear = searchParams.get('fiscalYear');
    const fiscalPeriod = searchParams.get('fiscalPeriod');
    const status = searchParams.get('status');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const reference = searchParams.get('reference');
    const accountId = searchParams.get('accountId');

    // Build query
    const query: Record<string, unknown> = {};

    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (fiscalPeriod) query.fiscalPeriod = fiscalPeriod;
    if (status) query.status = status;
    if (reference) query.reference = { $regex: reference, $options: 'i' };
    if (accountId) query['items.accountId'] = accountId;

    // Date range query
    if (fromDate || toDate) {
      const dateQuery: Record<string, Date> = {};
      if (fromDate) dateQuery.$gte = new Date(fromDate);
      if (toDate) dateQuery.$lte = new Date(toDate);
      query.date = dateQuery;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const [entries, total] = await Promise.all([
      JournalEntry.find(query)
        .sort({ date: -1 })
        .skip(skip)
        .limit(limit)
        .populate('items.accountId', 'code name')
        .populate('createdBy', 'name')
        .populate('postedBy', 'name')
        .populate('approvedBy', 'name')
        .lean(),
      JournalEntry.countDocuments(query)
    ]);

    // Return results
    return NextResponse.json({
      entries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching journal entries:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/journal
 * Create a new journal entry
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.date || !body.reference || !body.description || !body.items || body.items.length < 2) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate debits and credits balance
    const totalDebits = body.items.reduce((sum: number, item: Record<string, any>) => sum + (parseFloat(item.debit) || 0), 0);
    const totalCredits = body.items.reduce((sum: number, item: Record<string, any>) => sum + (parseFloat(item.credit) || 0), 0);

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      return NextResponse.json(
        { error: 'Total debits must equal total credits' },
        { status: 400 }
      );
    }

    // Generate reference if not provided
    if (!body.reference) {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');

      // Get the count of journal entries for today
      const count = await JournalEntry.countDocuments({
        date: {
          $gte: new Date(date.setHours(0, 0, 0, 0)),
          $lt: new Date(date.setHours(23, 59, 59, 999))
        }
      });

      // Format: JE-YYYYMMDD-XXX
      body.reference = `JE-${year}${month}${day}-${(count + 1).toString().padStart(3, '0')}`;
    }

    // Set created by
    body.createdBy = user.id;

    // Create journal entry
    const journalEntry = new JournalEntry(body);
    await journalEntry.save();

    // Return created journal entry
    return NextResponse.json(
      {
        success: true,
        message: 'Journal entry created successfully',
        data: journalEntry
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating journal entry:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/journal
 * Update a journal entry
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Journal entry ID is required' },
        { status: 400 }
      );
    }

    // Find journal entry
    const journalEntry = await JournalEntry.findById(body.id);
    if (!journalEntry) {
      return NextResponse.json(
        { error: 'Journal entry not found' },
        { status: 404 }
      );
    }

    // Check if journal entry can be updated
    if (journalEntry.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft journal entries can be updated' },
        { status: 400 }
      );
    }

    // Validate debits and credits balance if items are provided
    if (body.items && body.items.length > 0) {
      const totalDebits = body.items.reduce((sum: number, item: Record<string, any>) => sum + (parseFloat(item.debit) || 0), 0);
      const totalCredits = body.items.reduce((sum: number, item: Record<string, any>) => sum + (parseFloat(item.credit) || 0), 0);

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        return NextResponse.json(
          { error: 'Total debits must equal total credits' },
          { status: 400 }
        );
      }
    }

    // Set updated by
    body.updatedBy = user.id;

    // Update journal entry
    const updatedJournalEntry = await JournalEntry.findByIdAndUpdate(
      body.id,
      { $set: body },
      { new: true, runValidators: true }
    );

    // Return updated journal entry
    return NextResponse.json({
      success: true,
      message: 'Journal entry updated successfully',
      data: updatedJournalEntry
    });
  } catch (error: unknown) {
    logger.error('Error updating journal entry:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/journal
 * Update journal entry status (post, approve, reject)
 */
export async function PATCH(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate ID and action
    if (!body.id || !body.action) {
      return NextResponse.json(
        { error: 'Journal entry ID and action are required' },
        { status: 400 }
      );
    }

    // Find journal entry
    const journalEntry = await JournalEntry.findById(body.id);
    if (!journalEntry) {
      return NextResponse.json(
        { error: 'Journal entry not found' },
        { status: 404 }
      );
    }

    // Process based on action
    switch (body.action) {
      case 'post':
        // Check permissions
        const hasPostPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER,
          UserRole.ACCOUNTANT
        ]);

        if (!hasPostPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to post journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be posted
        if (journalEntry.status !== 'draft') {
          return NextResponse.json(
            { error: 'Only draft journal entries can be posted' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'posted';
        journalEntry.postingDate = new Date();
        journalEntry.postedBy = user.id;
        break;

      case 'approve':
        // Check permissions
        const hasApprovePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasApprovePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be approved
        if (journalEntry.status !== 'posted') {
          return NextResponse.json(
            { error: 'Only posted journal entries can be approved' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'approved';
        journalEntry.approvalDate = new Date();
        journalEntry.approvedBy = user.id;
        break;

      case 'reject':
        // Check permissions
        const hasRejectPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasRejectPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to reject journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be rejected
        if (journalEntry.status !== 'posted') {
          return NextResponse.json(
            { error: 'Only posted journal entries can be rejected' },
            { status: 400 }
          );
        }

        // Validate rejection reason
        if (!body.rejectionReason) {
          return NextResponse.json(
            { error: 'Rejection reason is required' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'rejected';
        journalEntry.rejectionReason = body.rejectionReason;
        journalEntry.rejectedBy = user.id;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: post, approve, reject' },
          { status: 400 }
        );
    }

    // Save journal entry
    await journalEntry.save();

    // Return updated journal entry
    return NextResponse.json({
      success: true,
      message: `Journal entry ${body.action}ed successfully`,
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error updating journal entry status:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

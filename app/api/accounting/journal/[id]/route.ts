import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import JournalEntry from '@/models/accounting/JournalEntry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/accounting/journal/[id]
 * Get a journal entry by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get journal entry
    const journalEntry = await JournalEntry.findById(id)
      .populate('items.accountId', 'code name')
      .populate('createdBy', 'name')
      .populate('postedBy', 'name')
      .populate('approvedBy', 'name')
      .populate('rejectedBy', 'name');

    if (!journalEntry) {
      return NextResponse.json(
        { error: 'Journal entry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(journalEntry);
  } catch (error: unknown) {
    logger.error(`Error getting journal entry`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/journal/[id]
 * Delete a journal entry
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Find journal entry
    const journalEntry = await JournalEntry.findById(id);
    if (!journalEntry) {
      return NextResponse.json(
        { error: 'Journal entry not found' },
        { status: 404 }
      );
    }

    // Check if journal entry can be deleted
    if (journalEntry.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft journal entries can be deleted' },
        { status: 400 }
      );
    }

    // Delete journal entry
    await JournalEntry.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Journal entry deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting journal entry`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/journal/[id]/actions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import JournalEntry from '@/models/accounting/JournalEntry';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';



/**
 * POST /api/accounting/journal/[id]/actions
 * Perform actions on a journal entry (post, approve, reject, void)
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {

  try {
    // Resolve the params promise
    const { id } = await params;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // Find journal entry
    const journalEntry = await JournalEntry.findById(id);
    if (!journalEntry) {
      return NextResponse.json(
        { error: 'Journal entry not found' },
        { status: 404 }
      );
    }

    // Process based on action
    switch (body.action) {
      case 'post':
        // Check permissions
        const hasPostPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER,
          UserRole.ACCOUNTANT
        ]);

        if (!hasPostPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to post journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be posted
        if (journalEntry.status !== 'draft') {
          return NextResponse.json(
            { error: 'Only draft journal entries can be posted' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'posted';
        journalEntry.postingDate = new Date();
        journalEntry.postedBy = user.id;
        await journalEntry.save();

        // Return updated journal entry
        return NextResponse.json({
          success: true,
          message: 'Journal entry posted successfully',
          data: await JournalEntry.findById(id)
            .populate('items.accountId', 'code name')
            .populate('createdBy', 'name')
            .populate('postedBy', 'name')
        });

      case 'approve':
        // Check permissions
        const hasApprovePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasApprovePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be approved
        if (journalEntry.status !== 'posted') {
          return NextResponse.json(
            { error: 'Only posted journal entries can be approved' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'approved';
        journalEntry.approvalDate = new Date();
        journalEntry.approvedBy = user.id;
        await journalEntry.save();

        // Return updated journal entry
        return NextResponse.json({
          success: true,
          message: 'Journal entry approved successfully',
          data: await JournalEntry.findById(id)
            .populate('items.accountId', 'code name')
            .populate('createdBy', 'name')
            .populate('postedBy', 'name')
            .populate('approvedBy', 'name')
        });

      case 'reject':
        // Check permissions
        const hasRejectPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasRejectPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to reject journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be rejected
        if (journalEntry.status !== 'posted') {
          return NextResponse.json(
            { error: 'Only posted journal entries can be rejected' },
            { status: 400 }
          );
        }

        // Validate rejection reason
        if (!body.rejectionReason) {
          return NextResponse.json(
            { error: 'Rejection reason is required' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'rejected';
        journalEntry.rejectionReason = body.rejectionReason;
        journalEntry.rejectedBy = user.id;
        await journalEntry.save();

        // Return updated journal entry
        return NextResponse.json({
          success: true,
          message: 'Journal entry rejected successfully',
          data: await JournalEntry.findById(id)
            .populate('items.accountId', 'code name')
            .populate('createdBy', 'name')
            .populate('postedBy', 'name')
            .populate('rejectedBy', 'name')
        });

      case 'void':
        // Check permissions
        const hasVoidPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasVoidPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to void journal entries' },
            { status: 403 }
          );
        }

        // Check if journal entry can be voided
        if (journalEntry.status !== 'posted' && journalEntry.status !== 'approved') {
          return NextResponse.json(
            { error: 'Only posted or approved journal entries can be voided' },
            { status: 400 }
          );
        }

        // Validate void reason
        if (!body.voidReason) {
          return NextResponse.json(
            { error: 'Void reason is required' },
            { status: 400 }
          );
        }

        // Update journal entry
        journalEntry.status = 'voided';
        journalEntry.voidReason = body.voidReason;
        journalEntry.voidedBy = user.id;
        journalEntry.voidDate = new Date();
        await journalEntry.save();

        // Return updated journal entry
        return NextResponse.json({
          success: true,
          message: 'Journal entry voided successfully',
          data: await JournalEntry.findById(id)
            .populate('items.accountId', 'code name')
            .populate('createdBy', 'name')
            .populate('postedBy', 'name')
            .populate('approvedBy', 'name')
            .populate('voidedBy', 'name')
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: post, approve, reject, void' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error(`Error performing action on journal entry`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

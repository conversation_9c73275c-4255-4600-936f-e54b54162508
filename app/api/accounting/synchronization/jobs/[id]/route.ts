// app/api/accounting/synchronization/jobs/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { synchronizationScheduler } from '@/lib/services/accounting/synchronization/synchronization-scheduler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/synchronization/jobs/[id]
 * Get a synchronization job by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id: jobId } = await params;
    id = jobId;

    // Get job
    const job = await synchronizationScheduler.getJob(id);

    if (!job) {
      return NextResponse.json(
        { error: 'Synchronization job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(job);
  } catch (error) {
    logger.error(`Error getting synchronization job: ${id}`, LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to get synchronization job' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/synchronization/jobs/[id]
 * Update a synchronization job
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id: jobId } = await params;
    id = jobId;

    // Get request body
    const body = await req.json();

    // Update job
    const job = await synchronizationScheduler.updateJob(id, body, user._id?.toString() || user.id);

    if (!job) {
      return NextResponse.json(
        { error: 'Synchronization job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(job);
  } catch (error) {
    logger.error(`Error updating synchronization job: ${id}`, LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to update synchronization job' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/synchronization/jobs/[id]
 * Delete a synchronization job
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id: jobId } = await params;
    id = jobId;

    // Delete job
    const result = await synchronizationScheduler.deleteJob(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Synchronization job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting synchronization job: ${id}`, LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to delete synchronization job' },
      { status: 500 }
    );
  }
}

// app/api/accounting/synchronization/jobs/[id]/run/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { synchronizationScheduler } from '@/lib/services/accounting/synchronization/synchronization-scheduler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/accounting/synchronization/jobs/[id]/run
 * Run a synchronization job immediately
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: jobId } = await params;
    id = jobId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Run job
    const result = await synchronizationScheduler.runJobNow(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Synchronization job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Job execution triggered'
    });
  } catch (error) {
    logger.error(`Error running synchronization job: ${id}`, LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to run synchronization job' },
      { status: 500 }
    );
  }
}

// app/api/accounting/synchronization/jobs/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { synchronizationScheduler } from '@/lib/services/accounting/synchronization/synchronization-scheduler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/synchronization/jobs
 * Get all synchronization jobs
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const integrationId = searchParams.get('integrationId') || undefined;
    const integrationType = searchParams.get('integrationType') as 'accounting' | 'banking' | undefined;
    const operation = searchParams.get('operation') as 'import' | 'export' | undefined;
    const entityType = searchParams.get('entityType') || undefined;
    const isActive = searchParams.has('isActive') ? searchParams.get('isActive') === 'true' : undefined;

    // Get jobs
    const jobs = await synchronizationScheduler.getJobs({
      integrationId,
      integrationType,
      operation,
      entityType,
      isActive
    });

    return NextResponse.json(jobs);
  } catch (error) {
    logger.error('Error getting synchronization jobs', LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to get synchronization jobs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/synchronization/jobs
 * Create a new synchronization job
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.integrationId || !body.integrationType || !body.operation || !body.entityType || !body.schedule?.frequency) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create job
    const job = await synchronizationScheduler.createJob(body, user._id?.toString() || user.id);

    return NextResponse.json(job, { status: 201 });
  } catch (error) {
    logger.error('Error creating synchronization job', LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to create synchronization job' },
      { status: 500 }
    );
  }
}

// app/api/accounting/synchronization/scheduler/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { synchronizationScheduler } from '@/lib/services/accounting/synchronization/synchronization-scheduler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * POST /api/accounting/synchronization/scheduler
 * Start or stop the synchronization scheduler
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();

    // Check if action is provided
    if (!body.action || (body.action !== 'start' && body.action !== 'stop')) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "start" or "stop".' },
        { status: 400 }
      );
    }

    // Perform action
    if (body.action === 'start') {
      synchronizationScheduler.start();
      logger.info('Synchronization scheduler started', LogCategory.SYNC, {
        userId: user._id?.toString() || user.id
      });

      return NextResponse.json({
        success: true,
        message: 'Synchronization scheduler started'
      });
    } else {
      synchronizationScheduler.stop();
      logger.info('Synchronization scheduler stopped', LogCategory.SYNC, {
        userId: user._id?.toString() || user.id
      });

      return NextResponse.json({
        success: true,
        message: 'Synchronization scheduler stopped'
      });
    }
  } catch (error) {
    logger.error('Error managing synchronization scheduler', LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to manage synchronization scheduler' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { VoucherTypeDefinition } from '@/models/accounting/VoucherTypeDefinition';
import { DynamicVoucherService } from '@/lib/services/accounting/dynamic-voucher-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/voucher-types
 * Get all active voucher type definitions
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to access voucher types.',
        {
          userId: undefined,
          endpoint: '/api/accounting/voucher-types',
          method: 'GET'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions
    const allowedRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR
    ];

    if (!hasRequiredPermissions(user, allowedRoles)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to access voucher types',
        'You do not have permission to view voucher type definitions.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/voucher-types',
          method: 'GET'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const subCategory = searchParams.get('subCategory');
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Build query
    const query: any = {};
    if (category) {
      query.category = category;
    }
    if (subCategory) {
      query.subCategory = subCategory;
    }
    if (!includeInactive) {
      query.isActive = true;
    }

    // Get voucher types
    const voucherTypes = await VoucherTypeDefinition.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ category: 1, subCategory: 1, name: 1 })
      .lean();

    // Transform for response
    const transformedTypes = voucherTypes.map(type => ({
      id: type._id.toString(),
      typeId: type.typeId,
      name: type.name,
      description: type.description,
      category: type.category,
      subCategory: type.subCategory,
      configuration: type.configuration,
      customFieldsCount: type.customFields?.length || 0,
      hasIntegration: !!type.integrationSettings?.sourceModule,
      isActive: type.isActive,
      version: type.version,
      createdBy: type.createdBy,
      updatedBy: type.updatedBy,
      createdAt: type.createdAt,
      updatedAt: type.updatedAt
    }));

    // Group by category for easier frontend handling
    const groupedTypes = transformedTypes.reduce((acc, type) => {
      if (!acc[type.category]) {
        acc[type.category] = [];
      }
      acc[type.category].push(type);
      return acc;
    }, {} as Record<string, any[]>);

    logger.info('Retrieved voucher types', LogCategory.ACCOUNTING, {
      userId: user._id?.toString(),
      totalTypes: transformedTypes.length,
      categories: Object.keys(groupedTypes)
    });

    return NextResponse.json({
      success: true,
      data: {
        voucherTypes: transformedTypes,
        groupedTypes,
        summary: {
          totalTypes: transformedTypes.length,
          activeTypes: transformedTypes.filter(t => t.isActive).length,
          categories: Object.keys(groupedTypes).length
        }
      }
    });

  } catch (error: any) {
    logger.error('Error getting voucher types', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INTERNAL_SERVER_ERROR',
      'Failed to retrieve voucher types',
      'An unexpected error occurred while fetching voucher types. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/voucher-types',
        method: 'GET',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * POST /api/accounting/voucher-types
 * Create a new voucher type definition
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to create voucher types.',
        {
          userId: undefined,
          endpoint: '/api/accounting/voucher-types',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions - only admins and finance directors can create types
    const allowedRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ];

    if (!hasRequiredPermissions(user, allowedRoles)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to create voucher types',
        'Only system administrators and finance directors can create voucher type definitions.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/accounting/voucher-types',
          method: 'POST'
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    await connectToDatabase();

    // Parse request body
    const body = await request.json();
    const {
      typeId,
      name,
      description,
      category,
      subCategory,
      configuration,
      customFields,
      integrationSettings,
      displayConfiguration,
      workflowConfiguration
    } = body;

    // Validate required fields
    if (!typeId || !name || !description || !category || !subCategory) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_REQUIRED_FIELDS',
        'Missing required fields',
        'Please provide all required fields: typeId, name, description, category, and subCategory.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/voucher-types',
          method: 'POST',
          providedFields: Object.keys(body)
        },
        400,
        ErrorSeverity.LOW
      );
    }

    // Check if typeId already exists
    const existingType = await VoucherTypeDefinition.findOne({ typeId });
    if (existingType) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'VOUCHER_TYPE_EXISTS',
        'Voucher type already exists',
        `A voucher type with ID '${typeId}' already exists. Please use a different type ID.`,
        {
          userId: user._id?.toString(),
          endpoint: '/api/accounting/voucher-types',
          method: 'POST',
          conflictingTypeId: typeId
        },
        409,
        ErrorSeverity.MEDIUM
      );
    }

    // Create voucher type definition
    const voucherTypeData = {
      typeId,
      name,
      description,
      category,
      subCategory,
      configuration: configuration || {
        requiresApproval: true,
        approvalLevels: 1,
        autoNumbering: true,
        numberPrefix: 'V',
        allowManualEntry: true,
        requiresAttachments: false,
        defaultFiscalYear: true
      },
      customFields: customFields || [],
      integrationSettings: integrationSettings || {},
      displayConfiguration: displayConfiguration || {
        formLayout: 'two-column',
        fieldGroups: [],
        listViewFields: [],
        detailViewFields: [],
        exportFields: []
      },
      workflowConfiguration: workflowConfiguration || {},
      isActive: true,
      version: 1,
      createdBy: user._id,
      updatedBy: user._id
    };

    const voucherType = await VoucherTypeDefinition.create(voucherTypeData);

    logger.info('Voucher type created successfully', LogCategory.ACCOUNTING, {
      voucherTypeId: voucherType._id,
      typeId: voucherType.typeId,
      createdBy: user._id?.toString()
    });

    return NextResponse.json({
      success: true,
      message: 'Voucher type created successfully',
      data: {
        voucherType: {
          id: voucherType._id.toString(),
          typeId: voucherType.typeId,
          name: voucherType.name,
          description: voucherType.description,
          category: voucherType.category,
          subCategory: voucherType.subCategory,
          isActive: voucherType.isActive,
          version: voucherType.version,
          createdAt: voucherType.createdAt
        }
      }
    });

  } catch (error: any) {
    logger.error('Error creating voucher type', LogCategory.ACCOUNTING, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INTERNAL_SERVER_ERROR',
      'Failed to create voucher type',
      'An unexpected error occurred while creating the voucher type. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/accounting/voucher-types',
        method: 'POST',
        error: error.message
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import SalaryStructure from '@/models/accounting/SalaryStructure';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/salary-structures
 * Get salary structures with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Build query
    const query: Record<string, unknown> = {};

    if (status) query.status = status;
    if (search) query.name = { $regex: search, $options: 'i' };

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const [salaryStructures, total] = await Promise.all([
      SalaryStructure.find(query)
        .sort({ effectiveDate: -1 })
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name')
        .lean(),
      SalaryStructure.countDocuments(query)
    ]);

    // Return results
    return NextResponse.json({
      salaryStructures,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching salary structures:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/salary-structures
 * Create a new salary structure
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.effectiveDate || !body.components || !body.grades) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create salary structure
    const salaryStructure = new SalaryStructure(body);
    await salaryStructure.save();

    // Return created salary structure
    return NextResponse.json(
      {
        success: true,
        message: 'Salary structure created successfully',
        data: salaryStructure
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating salary structure:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/salary-structures
 * Update a salary structure
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Salary structure ID is required' },
        { status: 400 }
      );
    }

    // Find salary structure
    const salaryStructure = await SalaryStructure.findById(body.id);
    if (!salaryStructure) {
      return NextResponse.json(
        { error: 'Salary structure not found' },
        { status: 404 }
      );
    }

    // Check if salary structure can be updated
    if (salaryStructure.status === 'expired') {
      return NextResponse.json(
        { error: 'Expired salary structures cannot be updated' },
        { status: 400 }
      );
    }

    // Set updated by
    body.updatedBy = user.id;

    // Update salary structure
    const updatedSalaryStructure = await SalaryStructure.findByIdAndUpdate(
      body.id,
      { $set: body },
      { new: true, runValidators: true }
    );

    // Return updated salary structure
    return NextResponse.json({
      success: true,
      message: 'Salary structure updated successfully',
      data: updatedSalaryStructure
    });
  } catch (error: unknown) {
    logger.error('Error updating salary structure:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/expenditures/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Transaction from '@/models/accounting/Transaction';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import mongoose from 'mongoose';


// MongoDB query type definition
interface MongoBaseFilter {
  [key: string]: any;
}


export const runtime = 'nodejs';



/**
 * Generate a transaction number based on type
 * @param type Transaction type
 * @returns Generated transaction number
 */
async function generateTransactionNumber(type: string): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // Get prefix based on transaction type
  let prefix = '';
  switch (type) {
    case 'debit':
      prefix = 'DB';
      break;
    case 'credit':
      prefix = 'CR';
      break;
    case 'transfer':
      prefix = 'TR';
      break;
    default:
      prefix = 'TX';
  }

  // Get the latest transaction with this prefix and date
  const datePrefix = `${prefix}${year}${month}${day}`;
  const latestTransaction = await Transaction.findOne({
    transactionNumber: { $regex: `^${datePrefix}` }
  }).sort({ transactionNumber: -1 });

  let sequenceNumber = 1;
  if (latestTransaction) {
    // Extract sequence number from the latest transaction
    const latestSequence = latestTransaction.transactionNumber.slice(datePrefix.length);
    sequenceNumber = parseInt(latestSequence) + 1;
  }

  // Format sequence number with leading zeros
  const formattedSequence = sequenceNumber.toString().padStart(4, '0');

  return `${datePrefix}${formattedSequence}`;
}

// Validation schema for creating expenditure
const createExpenditureSchema = z.object({
  date: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid date' }
  ),
  description: z.string().min(3, 'Description must be at least 3 characters long'),
  amount: z.number().positive('Amount must be positive'),
  account: z.string().min(1, 'Account is required'),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  reference: z.string().optional(),
  payee: z.string().optional(),
  paymentMethod: z.enum(['cash', 'check', 'bank_transfer', 'credit_card', 'other']).optional(),
  checkNumber: z.string().optional(),
  project: z.string().optional(),
  costCenter: z.string().optional(),
  department: z.string().optional(),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().positive().default(1),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'voided']).default('pending')
});

/**
 * GET /api/accounting/expenditures
 * List expenditures
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const status = searchParams.get('status');
    const account = searchParams.get('account');
    const category = searchParams.get('category');
    const project = searchParams.get('project');
    const costCenter = searchParams.get('costCenter');
    const department = searchParams.get('department');
    const search = searchParams.get('search');
    const minAmount = searchParams.get('minAmount');
    const maxAmount = searchParams.get('maxAmount');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: Record<string, any> = {
      type: 'debit' // Expenditures are debit transactions
    };

    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    if (status) {
      query.status = status;
    }

    if (account) {
      query.account = new mongoose.Types.ObjectId(account);
    }

    if (category) {
      query.category = category;
    }

    if (project) {
      query.project = new mongoose.Types.ObjectId(project);
    }

    if (costCenter) {
      query.costCenter = new mongoose.Types.ObjectId(costCenter);
    }

    if (department) {
      query.department = new mongoose.Types.ObjectId(department);
    }

    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    if (search) {
      query.$or = [
        { description: { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
        { payee: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: { [key: string]: 1 | -1 } = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get total count
    const totalCount = await Transaction.countDocuments(query);
    const totalPages = Math.ceil(totalCount / limit);

    // Get expenditures
    const expenditures = await Transaction.find(query)
      .populate('account', 'name code type')
      .populate('project', 'name code')
      .populate('costCenter', 'name code')
      .populate('department', 'name')
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    return NextResponse.json({
      success: true,
      data: expenditures,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting expenditures', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/expenditures
 * Create an expenditure
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createExpenditureSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Check if account exists
    const account = await Account.findById(validationResult.data.account);
    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 400 }
      );
    }

    // Generate transaction number
    const transactionNumber = await generateTransactionNumber('debit');

    // Create expenditure
    const expenditure = new Transaction({
      transactionNumber,
      date: new Date(validationResult.data.date),
      description: validationResult.data.description,
      amount: validationResult.data.amount,
      type: 'debit', // Expenditures are debit transactions
      account: new mongoose.Types.ObjectId(validationResult.data.account),
      category: validationResult.data.category,
      subcategory: validationResult.data.subcategory,
      reference: validationResult.data.reference,
      payee: validationResult.data.payee,
      paymentMethod: validationResult.data.paymentMethod,
      checkNumber: validationResult.data.checkNumber,
      project: validationResult.data.project ? new mongoose.Types.ObjectId(validationResult.data.project) : undefined,
      costCenter: validationResult.data.costCenter ? new mongoose.Types.ObjectId(validationResult.data.costCenter) : undefined,
      department: validationResult.data.department ? new mongoose.Types.ObjectId(validationResult.data.department) : undefined,
      currency: validationResult.data.currency,
      exchangeRate: validationResult.data.exchangeRate,
      notes: validationResult.data.notes,
      attachments: validationResult.data.attachments,
      tags: validationResult.data.tags,
      status: validationResult.data.status,
      createdBy: user.id,
      updatedBy: user.id
    });

    await expenditure.save();

    return NextResponse.json({
      success: true,
      message: 'Expenditure created successfully',
      data: expenditure
    });
  } catch (error: unknown) {
    logger.error('Error creating expenditure', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

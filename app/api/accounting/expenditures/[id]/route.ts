import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Transaction from '@/models/accounting/Transaction';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Validation schema for updating expenditure
const updateExpenditureSchema = z.object({
  date: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid date' }
  ).optional(),
  description: z.string().min(3, 'Description must be at least 3 characters long').optional(),
  amount: z.number().positive('Amount must be positive').optional(),
  account: z.string().min(1, 'Account is required').optional(),
  category: z.string().optional().nullable(),
  subcategory: z.string().optional().nullable(),
  reference: z.string().optional().nullable(),
  payee: z.string().optional().nullable(),
  paymentMethod: z.enum(['cash', 'check', 'bank_transfer', 'credit_card', 'other']).optional(),
  checkNumber: z.string().optional().nullable(),
  project: z.string().optional().nullable(),
  costCenter: z.string().optional().nullable(),
  department: z.string().optional().nullable(),
  currency: z.string().optional(),
  exchangeRate: z.number().positive().optional(),
  notes: z.string().optional().nullable(),
  attachments: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'voided']).optional()
});

/**
 * GET /api/accounting/expenditures/[id]
 * Get an expenditure by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expenditure
    const expenditure = await Transaction.findOne({
      _id: id,
      type: 'debit' // Ensure it's an expenditure
    })
      .populate('account', 'name code type')
      .populate('project', 'name code')
      .populate('costCenter', 'name code')
      .populate('department', 'name')
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    if (!expenditure) {
      return NextResponse.json(
        { error: 'Expenditure not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: expenditure
    });
  } catch (error: unknown) {
    logger.error('Error getting expenditure', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/expenditures/[id]
 * Update an expenditure
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expenditure
    const expenditure = await Transaction.findOne({
      _id: id,
      type: 'debit' // Ensure it's an expenditure
    });

    if (!expenditure) {
      return NextResponse.json(
        { error: 'Expenditure not found' },
        { status: 404 }
      );
    }

    // Check if expenditure can be updated
    if (expenditure.status === 'voided') {
      return NextResponse.json(
        { error: 'Voided expenditures cannot be updated' },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = updateExpenditureSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Check if account exists if provided
    if (validationResult.data.account) {
      const account = await Account.findById(validationResult.data.account);
      if (!account) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 400 }
        );
      }
    }

    // Update expenditure fields
    if (validationResult.data.date) {
      expenditure.date = new Date(validationResult.data.date);
    }
    
    if (validationResult.data.description !== undefined) {
      expenditure.description = validationResult.data.description;
    }
    
    if (validationResult.data.amount !== undefined) {
      expenditure.amount = validationResult.data.amount;
    }
    
    if (validationResult.data.account !== undefined) {
      expenditure.account = new mongoose.Types.ObjectId(validationResult.data.account);
    }
    
    if (validationResult.data.category !== undefined) {
      expenditure.category = validationResult.data.category;
    }
    
    if (validationResult.data.subcategory !== undefined) {
      expenditure.subcategory = validationResult.data.subcategory;
    }
    
    if (validationResult.data.reference !== undefined) {
      expenditure.reference = validationResult.data.reference;
    }
    
    if (validationResult.data.payee !== undefined) {
      expenditure.payee = validationResult.data.payee;
    }
    
    if (validationResult.data.paymentMethod !== undefined) {
      expenditure.paymentMethod = validationResult.data.paymentMethod;
    }
    
    if (validationResult.data.checkNumber !== undefined) {
      expenditure.checkNumber = validationResult.data.checkNumber;
    }
    
    if (validationResult.data.project !== undefined) {
      expenditure.project = validationResult.data.project ? new mongoose.Types.ObjectId(validationResult.data.project) : null;
    }
    
    if (validationResult.data.costCenter !== undefined) {
      expenditure.costCenter = validationResult.data.costCenter ? new mongoose.Types.ObjectId(validationResult.data.costCenter) : null;
    }
    
    if (validationResult.data.department !== undefined) {
      expenditure.department = validationResult.data.department ? new mongoose.Types.ObjectId(validationResult.data.department) : null;
    }
    
    if (validationResult.data.currency !== undefined) {
      expenditure.currency = validationResult.data.currency;
    }
    
    if (validationResult.data.exchangeRate !== undefined) {
      expenditure.exchangeRate = validationResult.data.exchangeRate;
    }
    
    if (validationResult.data.notes !== undefined) {
      expenditure.notes = validationResult.data.notes;
    }
    
    if (validationResult.data.attachments !== undefined) {
      expenditure.attachments = validationResult.data.attachments;
    }
    
    if (validationResult.data.tags !== undefined) {
      expenditure.tags = validationResult.data.tags;
    }
    
    if (validationResult.data.status !== undefined) {
      expenditure.status = validationResult.data.status;
    }

    // Update updatedBy and updatedAt
    expenditure.updatedBy = user.id;
    expenditure.updatedAt = new Date();

    // Save changes
    await expenditure.save();

    return NextResponse.json({
      success: true,
      message: 'Expenditure updated successfully',
      data: expenditure
    });
  } catch (error: unknown) {
    logger.error('Error updating expenditure', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/expenditures/[id]
 * Delete (void) an expenditure
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expenditure
    const expenditure = await Transaction.findOne({
      _id: id,
      type: 'debit' // Ensure it's an expenditure
    });

    if (!expenditure) {
      return NextResponse.json(
        { error: 'Expenditure not found' },
        { status: 404 }
      );
    }

    // Void the expenditure instead of deleting it
    expenditure.status = 'voided';
    expenditure.updatedBy = user.id;
    expenditure.updatedAt = new Date();
    await expenditure.save();

    return NextResponse.json({
      success: true,
      message: 'Expenditure voided successfully'
    });
  } catch (error: unknown) {
    logger.error('Error voiding expenditure', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { 
  expenditureService,
  CreateExpenditureRequest,
  UpdateExpenditureRequest,
  ExpenditureFilters
} from '@/lib/services/accounting/expenditure-service';
import { logger, LogCategory } from '@/lib/backend/logger';
import { z } from 'zod';
import {
  ExpenditureCategory,
  ExpenditurePriority,
  PaymentMethod
} from '@/types/accounting/expenditure';

export const runtime = 'nodejs';

// Validation schemas
const createExpenditureSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(1000),
  category: z.nativeEnum(ExpenditureCategory),
  subcategory: z.string().min(1),
  amount: z.number().min(0),
  currency: z.string().optional(),
  exchangeRate: z.number().min(0).optional(),
  expenditureDate: z.string().transform(str => new Date(str)),
  dueDate: z.string().transform(str => new Date(str)).optional(),
  priority: z.nativeEnum(ExpenditurePriority).optional(),
  department: z.string().min(1),
  costCenter: z.string().optional(),
  vendor: z.object({
    vendorId: z.string().optional(),
    vendorName: z.string().min(1),
    vendorEmail: z.string().email().optional(),
    vendorPhone: z.string().optional(),
    vendorAddress: z.string().optional()
  }),
  budgetAllocations: z.array(z.object({
    budgetId: z.string(),
    allocatedAmount: z.number().min(0),
    percentage: z.number().min(0).max(100)
  })).min(1),
  taxInfo: z.object({
    taxType: z.enum(['VAT', 'withholding', 'excise', 'none']),
    taxRate: z.number().min(0).max(100),
    isExempt: z.boolean(),
    exemptionReason: z.string().optional()
  }).optional(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  tags: z.array(z.string()).optional(),
  notes: z.array(z.string()).optional(),
  isUrgent: z.boolean().optional(),
  requiresReceipt: z.boolean().optional(),
  isCapitalExpenditure: z.boolean().optional(),
  projectId: z.string().optional()
});

const updateExpenditureSchema = createExpenditureSchema.partial().omit(['budgetAllocations']).extend({
  budgetAllocations: z.array(z.object({
    budgetId: z.string(),
    allocatedAmount: z.number().min(0),
    percentage: z.number().min(0).max(100)
  })).optional()
});

// Required roles for expenditure operations
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.FINANCE_OFFICER,
  UserRole.ACCOUNTANT,
  UserRole.DEPARTMENT_HEAD
];

/**
 * POST /api/accounting/expenditures/advanced
 * Create a new expenditure
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    
    // Validate request data
    const validationResult = createExpenditureSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const request: CreateExpenditureRequest = {
      ...validationResult.data,
      requestedBy: user.id,
      requestedByName: user.email,
      requestedByEmail: user.email
    };

    const expenditure = await expenditureService.createExpenditure(request, user.id);

    logger.info('Advanced expenditure created via API', LogCategory.ACCOUNTING, {
      expenditureId: expenditure._id,
      expenditureNumber: expenditure.expenditureNumber,
      amount: expenditure.amount,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      expenditure: {
        id: expenditure._id,
        reference: expenditure.reference,
        description: expenditure.description,
        category: expenditure.category,
        subcategory: expenditure.subcategory,
        amount: expenditure.amount,
        status: expenditure.status,
        date: expenditure.date,
        vendor: expenditure.vendor,
        budget: expenditure.budget,
        budgetCategory: expenditure.budgetCategory,
        appliedToBudget: expenditure.appliedToBudget,
        department: expenditure.department,
        costCenter: expenditure.costCenter,
        paymentMethod: expenditure.paymentMethod,
        notes: expenditure.notes,
        approvalWorkflow: expenditure.approvalWorkflow,
        createdAt: expenditure.createdAt,
        updatedAt: expenditure.updatedAt
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Error creating advanced expenditure', LogCategory.ACCOUNTING, {
      error: errorMessage,
      stack: errorStack
    });
    
    return NextResponse.json({
      error: 'Failed to create expenditure',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * GET /api/accounting/expenditures/advanced
 * Get expenditures with filters and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');
    
    if (action === 'statistics') {
      // Get expenditure statistics
      const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
      const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
      
      const filters: Partial<ExpenditureFilters> = {};
      if (searchParams.get('department')) {
        filters.department = searchParams.get('department')!.split(',');
      }
      if (searchParams.get('category')) {
        filters.category = searchParams.get('category')!.split(',') as any;
      }

      const statistics = await expenditureService.getExpenditureStatistics(startDate, endDate, filters);

      return NextResponse.json({
        success: true,
        statistics
      });
    }
    
    // Parse query parameters for regular listing
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';
    
    // Parse filters
    const filters: ExpenditureFilters = {};
    
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status')!.split(',') as any;
    }
    
    if (searchParams.get('category')) {
      filters.category = searchParams.get('category')!.split(',') as any;
    }
    
    if (searchParams.get('priority')) {
      filters.priority = searchParams.get('priority')!.split(',') as any;
    }
    
    if (searchParams.get('department')) {
      filters.department = searchParams.get('department')!.split(',');
    }
    
    if (searchParams.get('requestedBy')) {
      filters.requestedBy = searchParams.get('requestedBy')!.split(',');
    }
    
    if (searchParams.get('vendorId')) {
      filters.vendorId = searchParams.get('vendorId')!.split(',');
    }
    
    if (searchParams.get('minAmount') || searchParams.get('maxAmount')) {
      filters.amountRange = {
        min: parseFloat(searchParams.get('minAmount') || '0'),
        max: parseFloat(searchParams.get('maxAmount') || '999999999')
      };
    }
    
    if (searchParams.get('startDate') || searchParams.get('endDate')) {
      filters.dateRange = {
        startDate: new Date(searchParams.get('startDate') || '2020-01-01'),
        endDate: new Date(searchParams.get('endDate') || new Date().toISOString())
      };
    }
    
    if (searchParams.get('isUrgent')) {
      filters.isUrgent = searchParams.get('isUrgent') === 'true';
    }
    
    if (searchParams.get('isCapitalExpenditure')) {
      filters.isCapitalExpenditure = searchParams.get('isCapitalExpenditure') === 'true';
    }
    
    if (searchParams.get('tags')) {
      filters.tags = searchParams.get('tags')!.split(',');
    }
    
    if (searchParams.get('search')) {
      filters.search = searchParams.get('search')!;
    }

    const result = await expenditureService.getExpenditures(
      filters,
      page,
      limit,
      sortBy,
      sortOrder
    );

    // Format expenditures for response
    const formattedExpenditures = result.expenditures.map(expenditure => ({
      id: expenditure._id,
      reference: expenditure.reference,
      description: expenditure.description,
      category: expenditure.category,
      subcategory: expenditure.subcategory,
      amount: expenditure.amount,
      status: expenditure.status,
      date: expenditure.date,
      vendor: expenditure.vendor,
      budget: expenditure.budget,
      budgetCategory: expenditure.budgetCategory,
      appliedToBudget: expenditure.appliedToBudget,
      department: expenditure.department,
      costCenter: expenditure.costCenter,
      paymentMethod: expenditure.paymentMethod,
      notes: expenditure.notes,
      approvalWorkflow: expenditure.approvalWorkflow,
      createdAt: expenditure.createdAt,
      updatedAt: expenditure.updatedAt
    }));

    return NextResponse.json({
      success: true,
      expenditures: formattedExpenditures,
      pagination: {
        page: result.page,
        limit,
        total: result.total,
        totalPages: result.totalPages
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Error getting advanced expenditures', LogCategory.ACCOUNTING, {
      error: errorMessage
    });
    
    return NextResponse.json({
      error: 'Failed to get expenditures',
      details: errorMessage
    }, { status: 500 });
  }
}

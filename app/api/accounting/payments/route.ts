// app/api/accounting/payments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { paymentGatewayService } from '@/lib/services/payment/payment-gateway-service';
import { logger } from '@/lib/utils/logger';

export const runtime = 'nodejs';



/**
 * POST /api/accounting/payments
 * Create a new payment transaction
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { gatewayId, amount, currency, description, paymentMethod, customerInfo, metadata } = body;

    // Validate required fields
    if (!gatewayId || !amount || !currency || !description || !paymentMethod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Create payment
    const payment = await paymentGatewayService.createPayment(
      gatewayId,
      amount,
      currency,
      description,
      paymentMethod,
      customerInfo,
      metadata
    );

    return NextResponse.json({
      success: true,
      payment,
    });
  } catch (error: unknown) {
    logger.error('Error creating payment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/accounting/payments
 * Get payment transactions
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const gatewayId = searchParams.get('gatewayId');
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100;
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0;

    if (!gatewayId) {
      return NextResponse.json(
        { error: 'Gateway ID is required' },
        { status: 400 }
      );
    }

    // Get transactions
    const transactions = await paymentGatewayService.getTransactionsByGateway(gatewayId, limit, offset);

    return NextResponse.json({
      success: true,
      transactions,
    });
  } catch (error: unknown) {
    logger.error('Error getting payment transactions:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/payments/webhooks/[gatewayId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { paymentGatewayService } from '@/lib/services/payment/payment-gateway-service';
import { logger } from '@/lib/utils/logger';

/**
 * POST /api/accounting/payments/webhooks/[gatewayId]
 * Handle payment gateway webhooks
 * Note: This endpoint is public and does not require authentication
 * as it's called by the payment gateway
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ gatewayId: string }> }
): Promise<NextResponse> {
  // Declare gatewayId at function scope to make it available in catch block
  let gatewayId = 'unknown';

  try {
    // Resolve the params promise
    const resolvedParams = await params;
    gatewayId = resolvedParams.gatewayId;

    // Get request body
    const body = await req.json();

    // Process webhook
    const result = await paymentGatewayService.processWebhook(gatewayId, body);

    if (!result.success) {
      logger.warn(`Webhook processing failed for gateway ${gatewayId}:`, { message: result.message });
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      transactionId: result.transactionId,
      status: result.status,
    });
  } catch (error) {
    logger.error(`Error processing webhook for gateway ${gatewayId}:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

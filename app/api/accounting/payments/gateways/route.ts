// app/api/accounting/payments/gateways/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { paymentGatewayService } from '@/lib/services/payment/payment-gateway-service';
import { logger } from '@/lib/utils/logger';

/**
 * GET /api/accounting/payments/gateways
 * Get all payment gateway integrations
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get gateways
    const gateways = await paymentGatewayService.getAllGateways();

    // Remove sensitive data
    const sanitizedGateways = gateways.map(gateway => ({
      ...gateway,
      credentials: {
        ...gateway.credentials,
        apiKey: gateway.credentials.apiKey ? '********' : undefined,
        secretKey: gateway.credentials.secretKey ? '********' : undefined,
        password: gateway.credentials.password ? '********' : undefined,
        webhookSecret: gateway.credentials.webhookSecret ? '********' : undefined,
      },
    }));

    return NextResponse.json({
      success: true,
      gateways: sanitizedGateways,
    });
  } catch (error: unknown) {
    logger.error('Error getting payment gateways:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/payments/gateways
 * Create a new payment gateway integration
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { name, provider, description, credentials, settings } = body;

    // Validate required fields
    if (!name || !provider || !credentials || !settings) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate credentials
    if (!credentials.environment) {
      return NextResponse.json(
        { error: 'Missing environment in credentials' },
        { status: 400 }
      );
    }

    // Validate settings
    if (!settings.baseUrl || !settings.paymentEndpoint || !settings.supportedCurrencies || !settings.defaultCurrency || !settings.supportedPaymentMethods) {
      return NextResponse.json(
        { error: 'Missing required fields in settings' },
        { status: 400 }
      );
    }

    // Create gateway
    const gateway = await paymentGatewayService.createGateway({
      name,
      provider,
      description,
      credentials,
      settings,
      status: 'active' // Set default status
    });

    // Remove sensitive data
    const sanitizedGateway = {
      ...gateway,
      credentials: {
        ...gateway.credentials,
        apiKey: gateway.credentials.apiKey ? '********' : undefined,
        secretKey: gateway.credentials.secretKey ? '********' : undefined,
        password: gateway.credentials.password ? '********' : undefined,
        webhookSecret: gateway.credentials.webhookSecret ? '********' : undefined,
      },
    };

    return NextResponse.json({
      success: true,
      gateway: sanitizedGateway,
    });
  } catch (error: unknown) {
    logger.error('Error creating payment gateway:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/payroll/create-entries/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
// Removed deprecated payroll-integration-service import - using unified payroll service
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for creating accounting entries
const createEntriesSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required')
});

/**
 * POST /api/accounting/payroll/create-entries
 * Create accounting entries from a payroll run
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createEntriesSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Create accounting entries
    const journalEntry = await payrollIntegrationService.createPayrollAccountingEntries(
      validationResult.data.payrollRunId,
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Accounting entries created successfully',
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error creating payroll accounting entries', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/payroll/budget-variance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { payrollIntegrationService } from '@/lib/services/accounting/payroll-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for budget variance query
const budgetVarianceQuerySchema = z.object({
  departmentId: z.string().optional(),
  year: z.string().transform(val => parseInt(val)).optional(),
  month: z.string().transform(val => parseInt(val)).optional(),
  quarter: z.string().transform(val => parseInt(val)).optional()
});

/**
 * GET /api/accounting/payroll/budget-variance
 * Get payroll budget variance analysis
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const departmentId = searchParams.get('departmentId') || undefined;
    const year = searchParams.get('year') || undefined;
    const month = searchParams.get('month') || undefined;
    const quarter = searchParams.get('quarter') || undefined;

    // Validate query parameters
    const validationResult = budgetVarianceQuerySchema.safeParse({
      departmentId,
      year,
      month,
      quarter
    });

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Prepare period object with the correct type
    let period: { year: number; month?: number; quarter?: number } | undefined = undefined;

    // Only create a period object if year is provided
    if (validationResult.data.year) {
      period = { year: validationResult.data.year };

      // Add month or quarter if provided
      if (validationResult.data.month) {
        period.month = validationResult.data.month;
      } else if (validationResult.data.quarter) {
        period.quarter = validationResult.data.quarter;
      }
    }

    // Get budget variance analysis
    const varianceAnalysis = await payrollIntegrationService.analyzePayrollBudgetVariance(
      validationResult.data.departmentId,
      period
    );

    return NextResponse.json({
      success: true,
      data: varianceAnalysis
    });
  } catch (error: unknown) {
    logger.error('Error getting payroll budget variance', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

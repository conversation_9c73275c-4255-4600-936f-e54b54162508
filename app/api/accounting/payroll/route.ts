// app/api/accounting/payroll/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/accounting/payroll
 * Get payroll runs with optional filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const period = searchParams.get('period');
    const status = searchParams.get('status');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get payroll runs
    const result = await payrollService.getPayrollRuns({
      page,
      limit,
      period: period || undefined,
      status: status || undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      sortBy,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching payroll runs:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/payroll
 * Create a new payroll run
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.period || !body.description || !body.startDate || !body.endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create payroll run
    const payrollRun = await payrollService.createPayrollRun(body);

    // Return created payroll run
    return NextResponse.json(
      {
        success: true,
        message: 'Payroll run created successfully',
        data: payrollRun
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating payroll run:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/payroll
 * Update a payroll run
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Payroll run ID is required' },
        { status: 400 }
      );
    }

    // Set updated by
    body.updatedBy = user.id;

    // Update payroll run
    const payrollRun = await payrollService.updatePayrollRun(body.id, body);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Return updated payroll run
    return NextResponse.json({
      success: true,
      message: 'Payroll run updated successfully',
      data: payrollRun
    });
  } catch (error: unknown) {
    logger.error('Error updating payroll run:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/accounting/payroll/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/accounting/payroll/[id]
 * Get a payroll run by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: payrollId } = await params;
    id = payrollId;

    // Get payroll run
    const payrollRun = await payrollService.getPayrollRunById(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Return payroll run
    return NextResponse.json(payrollRun);
  } catch (error) {
    logger.error(`Error fetching payroll run ${id}:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/payroll/[id]
 * Delete a payroll run
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: payrollId } = await params;
    id = payrollId;

    // Delete payroll run
    const payrollRun = await payrollService.deletePayrollRun(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Payroll run deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting payroll run ${id}:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/payroll/[id]
 * Perform operations on a payroll run (process, approve, pay, cancel)
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope to make it available in catch block
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let payrollRun;
    let requiredRoles: UserRole[];

    // Process based on action
    switch (body.action) {
      case 'process':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER,
          UserRole.ACCOUNTANT,
          UserRole.HR_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to process payroll run' },
            { status: 403 }
          );
        }

        // Process payroll run
        payrollRun = await payrollService.processPayrollRun(id, user.id);
        break;

      case 'approve':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve payroll run' },
            { status: 403 }
          );
        }

        // Approve payroll run
        payrollRun = await payrollService.approvePayrollRun(id, user.id);
        break;

      case 'pay':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to mark payroll run as paid' },
            { status: 403 }
          );
        }

        // Mark payroll run as paid
        payrollRun = await payrollService.payPayrollRun(id, user.id);
        break;

      case 'cancel':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to cancel payroll run' },
            { status: 403 }
          );
        }

        // Validate cancellation reason
        if (!body.reason) {
          return NextResponse.json(
            { error: 'Cancellation reason is required' },
            { status: 400 }
          );
        }

        // Cancel payroll run
        payrollRun = await payrollService.cancelPayrollRun(id, user.id, body.reason);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: process, approve, pay, cancel' },
          { status: 400 }
        );
    }

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Return updated payroll run
    return NextResponse.json({
      success: true,
      message: `Payroll run ${body.action} operation completed successfully`,
      data: payrollRun
    });
  } catch (error) {
    logger.error(`Error performing operation on payroll run ${id}:`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

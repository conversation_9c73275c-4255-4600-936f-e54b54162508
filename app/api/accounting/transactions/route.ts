// app/api/accounting/transactions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Transaction from '@/models/accounting/Transaction';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * Generate a transaction number based on type
 * @param type Transaction type
 * @returns Generated transaction number
 */
async function generateTransactionNumber(type: string): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // Get prefix based on transaction type
  let prefix = '';
  switch (type) {
    case 'debit':
      prefix = 'DB';
      break;
    case 'credit':
      prefix = 'CR';
      break;
    case 'transfer':
      prefix = 'TR';
      break;
    default:
      prefix = 'TX';
  }

  // Get the latest transaction with this prefix and date
  const datePrefix = `${prefix}${year}${month}${day}`;
  const latestTransaction = await Transaction.findOne({
    transactionNumber: { $regex: `^${datePrefix}` }
  }).sort({ transactionNumber: -1 });

  let sequenceNumber = 1;
  if (latestTransaction) {
    // Extract sequence number from the latest transaction
    const latestSequence = latestTransaction.transactionNumber.slice(datePrefix.length);
    sequenceNumber = parseInt(latestSequence) + 1;
  }

  // Format sequence number with leading zeros
  const formattedSequence = sequenceNumber.toString().padStart(4, '0');

  return `${datePrefix}${formattedSequence}`;
}

/**
 * GET /api/accounting/transactions
 * Get all transactions with optional filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const account = searchParams.get('account');
    const category = searchParams.get('category');
    const costCenter = searchParams.get('costCenter');
    const project = searchParams.get('project');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const fiscalYear = searchParams.get('fiscalYear');
    const fiscalPeriod = searchParams.get('fiscalPeriod');
    const search = searchParams.get('search');
    const minAmount = searchParams.get('minAmount');
    const maxAmount = searchParams.get('maxAmount');
    const reconciled = searchParams.get('reconciled');
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, any> = {};

    if (type) {
      query.type = type;
    }

    if (status) {
      query.status = status;
    }

    if (account) {
      query.account = account;
    }

    if (category) {
      query.category = category;
    }

    if (costCenter) {
      query.costCenter = costCenter;
    }

    if (project) {
      query.project = project;
    }

    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    if (fiscalYear) {
      query.fiscalYear = fiscalYear;
    }

    if (fiscalPeriod) {
      query.fiscalPeriod = fiscalPeriod;
    }

    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    if (reconciled !== null) {
      query.reconciled = reconciled === 'true';
    }

    if (search) {
      query.$or = [
        { transactionNumber: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }

    // Get transactions
    const transactions = await Transaction.find(query)
      .sort({ date: -1, transactionNumber: -1 })
      .skip(skip)
      .limit(limit)
      .populate('account', 'accountNumber name')
      .populate('costCenter', 'code name')
      .populate('project', 'code name')
      .populate('journalEntry', 'reference');

    // Get total count
    const totalCount = await Transaction.countDocuments(query);

    return NextResponse.json({
      transactions,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    logger.error('Error getting transactions', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to get transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/transactions
 * Create a new transaction
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.date || !body.description || !body.amount || !body.type || !body.account) {
      return NextResponse.json(
        { error: 'Missing required fields: date, description, amount, type, account' },
        { status: 400 }
      );
    }

    // Check if account exists
    const account = await Account.findById(body.account);
    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 400 }
      );
    }

    // Generate transaction number if not provided
    if (!body.transactionNumber) {
      body.transactionNumber = await generateTransactionNumber(body.type);
    }

    // Create transaction
    const transaction = new Transaction({
      ...body,
      createdBy: user._id?.toString() || user.id,
      updatedBy: user._id?.toString() || user.id
    });

    // Save transaction
    await transaction.save();

    // Create journal entry if autoPost is true
    if (body.autoPost) {
      await transaction.createJournalEntry(user._id?.toString() || user.id);
    }

    // Populate references
    await transaction.populate('account', 'accountNumber name');
    await transaction.populate('costCenter', 'code name');
    await transaction.populate('project', 'code name');
    await transaction.populate('journalEntry', 'reference');

    return NextResponse.json(transaction, { status: 201 });
  } catch (error) {
    logger.error('Error creating transaction', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to create transaction' },
      { status: 500 }
    );
  }
}

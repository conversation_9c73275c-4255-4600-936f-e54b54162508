// app/api/accounting/transactions/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Transaction from '@/models/accounting/Transaction';
import Account from '@/models/accounting/Account';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/accounting/transactions/[id]
 * Get a transaction by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;

    // Get transaction
    const transaction = await Transaction.findById(id)
      .populate('account', 'accountNumber name')
      .populate('costCenter', 'code name')
      .populate('project', 'code name')
      .populate('journalEntry', 'reference date description');

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(transaction);
  } catch (error) {
    logger.error(`Error getting transaction: ${id}`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to get transaction' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/transactions/[id]
 * Update a transaction
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;

    // Check if transaction exists
    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Check if transaction can be updated
    if (transaction.status === 'voided') {
      return NextResponse.json(
        { error: 'Cannot update a voided transaction' },
        { status: 400 }
      );
    }

    if (transaction.reconciled) {
      return NextResponse.json(
        { error: 'Cannot update a reconciled transaction' },
        { status: 400 }
      );
    }

    // If account is being changed, check if it exists
    if (body.account && body.account !== transaction.account.toString()) {
      const account = await Account.findById(body.account);
      if (!account) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 400 }
        );
      }
    }

    // Update transaction
    const updatedTransaction = await Transaction.findByIdAndUpdate(
      id,
      {
        ...body,
        updatedBy: user._id?.toString() || user.id
      },
      { new: true, runValidators: true }
    )
      .populate('account', 'accountNumber name')
      .populate('costCenter', 'code name')
      .populate('project', 'code name')
      .populate('journalEntry', 'reference');

    return NextResponse.json(updatedTransaction);
  } catch (error) {
    logger.error(`Error updating transaction: ${id}`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to update transaction' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/transactions/[id]
 * Delete a transaction
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;

    // Check if transaction exists
    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Check if transaction can be deleted
    if (transaction.status === 'completed' || transaction.status === 'reconciled') {
      return NextResponse.json(
        { error: 'Cannot delete a completed or reconciled transaction. Void it instead.' },
        { status: 400 }
      );
    }

    if (transaction.journalEntry) {
      return NextResponse.json(
        { error: 'Cannot delete a transaction with a journal entry. Void it instead.' },
        { status: 400 }
      );
    }

    // Delete transaction
    await Transaction.findByIdAndDelete(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting transaction: ${id}`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to delete transaction' },
      { status: 500 }
    );
  }
}

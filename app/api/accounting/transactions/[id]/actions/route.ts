// app/api/accounting/transactions/[id]/actions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import Transaction from '@/models/accounting/Transaction';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * POST /api/accounting/transactions/[id]/actions
 * Perform actions on a transaction (void, reconcile, create journal entry)
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: transactionId } = await params;
    id = transactionId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // Check if transaction exists
    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Perform action
    let result;
    switch (body.action) {
      case 'void':
        // Check if transaction can be voided
        if (transaction.status === 'voided') {
          return NextResponse.json(
            { error: 'Transaction is already voided' },
            { status: 400 }
          );
        }

        if (transaction.reconciled) {
          return NextResponse.json(
            { error: 'Cannot void a reconciled transaction' },
            { status: 400 }
          );
        }

        // Void transaction
        await transaction.void(user._id?.toString() || user.id);
        result = await Transaction.findById(id)
          .populate('account', 'accountNumber name')
          .populate('costCenter', 'code name')
          .populate('project', 'code name')
          .populate('journalEntry', 'reference');

        return NextResponse.json({
          success: true,
          message: 'Transaction voided successfully',
          transaction: result
        });

      case 'reconcile':
        // Check if transaction can be reconciled
        if (transaction.status === 'voided') {
          return NextResponse.json(
            { error: 'Cannot reconcile a voided transaction' },
            { status: 400 }
          );
        }

        if (transaction.reconciled) {
          return NextResponse.json(
            { error: 'Transaction is already reconciled' },
            { status: 400 }
          );
        }

        // Reconcile transaction
        await transaction.reconcile(user._id?.toString() || user.id);
        result = await Transaction.findById(id)
          .populate('account', 'accountNumber name')
          .populate('costCenter', 'code name')
          .populate('project', 'code name')
          .populate('journalEntry', 'reference');

        return NextResponse.json({
          success: true,
          message: 'Transaction reconciled successfully',
          transaction: result
        });

      case 'create_journal_entry':
        // Check if transaction can have a journal entry created
        if (transaction.status === 'voided') {
          return NextResponse.json(
            { error: 'Cannot create journal entry for a voided transaction' },
            { status: 400 }
          );
        }

        if (transaction.journalEntry) {
          return NextResponse.json(
            { error: 'Transaction already has a journal entry' },
            { status: 400 }
          );
        }

        // Create journal entry
        const journalEntry = await transaction.createJournalEntry(user._id?.toString() || user.id);
        result = await Transaction.findById(id)
          .populate('account', 'accountNumber name')
          .populate('costCenter', 'code name')
          .populate('project', 'code name')
          .populate('journalEntry', 'reference');

        return NextResponse.json({
          success: true,
          message: 'Journal entry created successfully',
          transaction: result,
          journalEntry
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: void, reconcile, create_journal_entry' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error(`Error performing action on transaction: ${id}`, LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: 'Failed to perform action on transaction' },
      { status: 500 }
    );
  }
}

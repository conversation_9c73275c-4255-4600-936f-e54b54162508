import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/payment-methods
 * Get all payment methods
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status'); // active, inactive, all

    // Default payment methods (fallback if no database implementation)
    const paymentMethods = [
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Direct bank transfer',
        type: 'electronic',
        isActive: true,
        requiresAccount: true,
        processingTime: '1-3 business days',
        fees: 0,
        currency: 'MWK'
      },
      {
        id: 'cash',
        name: 'Cash',
        description: 'Cash payment',
        type: 'physical',
        isActive: true,
        requiresAccount: false,
        processingTime: 'Immediate',
        fees: 0,
        currency: 'MWK'
      },
      {
        id: 'cheque',
        name: 'Cheque',
        description: 'Bank cheque',
        type: 'physical',
        isActive: true,
        requiresAccount: true,
        processingTime: '3-5 business days',
        fees: 0,
        currency: 'MWK'
      },
      {
        id: 'mobile_money',
        name: 'Mobile Money',
        description: 'Mobile money transfer (Airtel Money, TNM Mpamba)',
        type: 'electronic',
        isActive: true,
        requiresAccount: false,
        processingTime: 'Immediate',
        fees: 0,
        currency: 'MWK'
      },
      {
        id: 'wire_transfer',
        name: 'Wire Transfer',
        description: 'International wire transfer',
        type: 'electronic',
        isActive: true,
        requiresAccount: true,
        processingTime: '3-7 business days',
        fees: 0,
        currency: 'USD'
      },
      {
        id: 'eft',
        name: 'Electronic Funds Transfer (EFT)',
        description: 'Electronic funds transfer',
        type: 'electronic',
        isActive: true,
        requiresAccount: true,
        processingTime: '1-2 business days',
        fees: 0,
        currency: 'MWK'
      }
    ];

    // Filter based on status
    let filteredPaymentMethods = paymentMethods;
    if (status === 'active') {
      filteredPaymentMethods = paymentMethods.filter(pm => pm.isActive);
    } else if (status === 'inactive') {
      filteredPaymentMethods = paymentMethods.filter(pm => !pm.isActive);
    }

    logger.info('Payment methods fetched successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      count: filteredPaymentMethods.length,
      filters: { status }
    });

    return NextResponse.json({
      paymentMethods: filteredPaymentMethods,
      totalCount: filteredPaymentMethods.length
    });

  } catch (error: unknown) {
    logger.error('Error fetching payment methods', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/payment-methods
 * Create a new payment method
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to create payment methods' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { name, description, type, requiresAccount, processingTime, fees, currency } = body;

    // Validate required fields
    if (!name || !type) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Create payment method object
    const paymentMethod = {
      id: name.toLowerCase().replace(/\s+/g, '_'),
      name,
      description: description || '',
      type: type || 'electronic',
      isActive: true,
      requiresAccount: requiresAccount || false,
      processingTime: processingTime || 'Immediate',
      fees: fees || 0,
      currency: currency || 'MWK',
      createdBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    logger.info('Payment method created successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      paymentMethodId: paymentMethod.id,
      name: paymentMethod.name
    });

    return NextResponse.json(paymentMethod, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating payment method', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

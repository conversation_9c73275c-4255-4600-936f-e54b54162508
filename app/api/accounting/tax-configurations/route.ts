import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import TaxConfiguration from '@/models/accounting/TaxConfiguration';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/tax-configurations
 * Get tax configurations with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const country = searchParams.get('country');

    // Build query
    const query: Record<string, unknown> = {};

    if (status) query.status = status;
    if (country) query.country = country;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const [taxConfigurations, total] = await Promise.all([
      TaxConfiguration.find(query)
        .sort({ effectiveDate: -1 })
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name')
        .lean(),
      TaxConfiguration.countDocuments(query)
    ]);

    // Return results
    return NextResponse.json({
      taxConfigurations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching tax configurations:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/tax-configurations
 * Create a new tax configuration
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.country || !body.effectiveDate || !body.brackets) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create tax configuration
    const taxConfiguration = new TaxConfiguration(body);
    await taxConfiguration.save();

    // If status is active, deactivate other tax configurations
    if (body.status === 'active') {
      await TaxConfiguration.updateMany(
        { _id: { $ne: taxConfiguration._id }, status: 'active' },
        { $set: { status: 'expired' } }
      );
    }

    // Return created tax configuration
    return NextResponse.json(
      {
        success: true,
        message: 'Tax configuration created successfully',
        data: taxConfiguration
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating tax configuration:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/tax-configurations
 * Update a tax configuration
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Tax configuration ID is required' },
        { status: 400 }
      );
    }

    // Find tax configuration
    const taxConfiguration = await TaxConfiguration.findById(body.id);
    if (!taxConfiguration) {
      return NextResponse.json(
        { error: 'Tax configuration not found' },
        { status: 404 }
      );
    }

    // Check if tax configuration can be updated
    if (taxConfiguration.status === 'expired') {
      return NextResponse.json(
        { error: 'Expired tax configurations cannot be updated' },
        { status: 400 }
      );
    }

    // Set updated by
    body.updatedBy = user.id;

    // Update tax configuration
    const updatedTaxConfiguration = await TaxConfiguration.findByIdAndUpdate(
      body.id,
      { $set: body },
      { new: true, runValidators: true }
    );

    // If status is active, deactivate other tax configurations
    if (body.status === 'active') {
      await TaxConfiguration.updateMany(
        { _id: { $ne: updatedTaxConfiguration?._id }, status: 'active' },
        { $set: { status: 'expired' } }
      );
    }

    // Return updated tax configuration
    return NextResponse.json({
      success: true,
      message: 'Tax configuration updated successfully',
      data: updatedTaxConfiguration
    });
  } catch (error: unknown) {
    logger.error('Error updating tax configuration:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import PayrollProcessingBatch from '@/models/payroll/PayrollProcessingBatch';
import Employee from '@/models/Employee';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * Get the status of a payroll run processing batch
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise
  const { id } = await params;
  const payrollRunId = id;
  const { searchParams } = new URL(req.url);
  const batchId = searchParams.get('batchId');

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = user;

    logger.info('Getting payroll processing status', LogCategory.API, {
      userId: currentUser.id,
      payrollRunId,
      batchId
    });

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // If batch ID is provided, get batch status
    if (batchId) {
      const batch = await PayrollProcessingBatch.findById(batchId);

      if (!batch) {
        return NextResponse.json(
          { error: 'Processing batch not found' },
          { status: 404 }
        );
      }

      // Get count of processed records
      const processedRecords = await PayrollRecord.countDocuments({
        payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
        batchId: new mongoose.Types.ObjectId(batchId)
      });

      // Get detailed employee progress
      const employees = await getEmployeeProgress(payrollRunId, batchId);

      // Calculate time estimates
      const timeEstimates = calculateTimeEstimates(batch, processedRecords);

      return NextResponse.json({
        success: true,
        data: {
          batchId: batch._id,
          status: batch.status,
          totalEmployees: batch.totalEmployees,
          processedEmployees: processedRecords,
          currentEmployee: batch.currentEmployee,
          startedAt: batch.startedAt,
          completedAt: batch.completedAt,
          error: batch.error,
          employees,
          ...timeEstimates
        }
      });
    }

    // If no batch ID, get overall status from payroll run
    const totalRecords = await PayrollRecord.countDocuments({
      payrollRunId: new mongoose.Types.ObjectId(payrollRunId)
    });

    // Get the latest batch if any
    const latestBatch = await PayrollProcessingBatch.findOne({
      payrollRunId: new mongoose.Types.ObjectId(payrollRunId)
    }).sort({ createdAt: -1 });

    // Determine status based on payroll run and records
    let status = 'idle';
    let currentEmployee = '';

    if (payrollRun.status === 'processed' || payrollRun.status === 'completed') {
      status = 'completed';
    } else if (payrollRun.status === 'processing') {
      status = 'processing';
      if (latestBatch) {
        currentEmployee = latestBatch.currentEmployee || '';
      }
    } else if (payrollRun.status === 'error') {
      status = 'error';
    }

    return NextResponse.json({
      success: true,
      data: {
        status,
        totalEmployees: payrollRun.totalEmployees || 0,
        processedEmployees: totalRecords,
        currentEmployee,
        startedAt: payrollRun.processedAt,
        completedAt: payrollRun.updatedAt,
        totalGrossSalary: payrollRun.totalGrossSalary,
        totalDeductions: payrollRun.totalDeductions,
        totalTax: payrollRun.totalTax,
        totalNetSalary: payrollRun.totalNetSalary
      }
    });

  } catch (error) {
    logger.error(`Error getting payroll processing status for run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to get processing status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Get detailed employee progress for a payroll run
 */
async function getEmployeeProgress(payrollRunId: string, batchId?: string) {
  try {
    // Get all employees for this payroll run
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) return [];

    const query: any = { employmentStatus: 'active' };
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      query.departmentId = { $in: payrollRun.departments };
    }

    const employees = await Employee.find(query)
      .select('firstName lastName _id')
      .lean();

    // Get processed records
    const recordQuery: any = { payrollRunId: new mongoose.Types.ObjectId(payrollRunId) };
    if (batchId) {
      recordQuery.batchId = new mongoose.Types.ObjectId(batchId);
    }

    const processedRecords = await PayrollRecord.find(recordQuery)
      .select('employeeId status createdAt')
      .lean();

    const processedEmployeeIds = new Set(processedRecords.map(r => r.employeeId.toString()));

    // Get current processing employee from batch
    let currentEmployeeName = '';
    if (batchId) {
      const batch = await PayrollProcessingBatch.findById(batchId);
      currentEmployeeName = batch?.currentEmployee || '';
    }

    // Build employee progress list
    return employees.map((employee: any) => {
      const employeeName = `${employee.firstName} ${employee.lastName}`;
      const isProcessed = processedEmployeeIds.has(employee._id.toString());
      const isCurrent = employeeName === currentEmployeeName;

      return {
        id: employee._id.toString(),
        name: employeeName,
        status: isProcessed ? 'completed' : isCurrent ? 'processing' : 'pending'
      };
    });

  } catch (error) {
    logger.error('Error getting employee progress', LogCategory.API, error);
    return [];
  }
}

/**
 * Calculate time estimates for payroll processing
 */
function calculateTimeEstimates(batch: any, processedCount: number) {
  const now = new Date();
  const startTime = batch.startedAt;
  const elapsedTime = (now.getTime() - startTime.getTime()) / 1000; // in seconds

  if (processedCount === 0) {
    return {
      averageProcessingTime: 0,
      estimatedTimeRemaining: 0
    };
  }

  const averageProcessingTime = elapsedTime / processedCount;
  const remainingEmployees = batch.totalEmployees - processedCount;
  const estimatedTimeRemaining = remainingEmployees * averageProcessingTime;

  return {
    averageProcessingTime,
    estimatedTimeRemaining: Math.max(0, estimatedTimeRemaining)
  };
}

// app/api/payroll/runs/[id]/process/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import { optimizedPayrollProcessor } from '@/lib/services/payroll/optimized-payroll-processor';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import { Employee } from '@/models/Employee';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/payroll/runs/[id]/process
 * Process a payroll run
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Resolve the params promise
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block
  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = user;

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Get request body first to check if this is a resume operation
    const body = await req.json();
    const isResumeOperation = body.resume === true || payrollRun.status === 'processing';

    logger.info(`Processing payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      currentStatus: payrollRun.status,
      isResumeOperation,
      resumeFlag: body.resume,
      useBatch: body.useBatch
    });

    // Check if payroll run is in valid status for processing
    if (!['draft', 'processing'].includes(payrollRun.status)) {
      return NextResponse.json(
        { error: `Cannot process payroll run with status '${payrollRun.status}'. Only 'draft' and 'processing' statuses are allowed.` },
        { status: 400 }
      );
    }

    // For resume operations, allow processing status
    if (payrollRun.status === 'processing' && !isResumeOperation) {
      return NextResponse.json(
        { error: `Payroll run is already being processed. Use resume operation to continue.` },
        { status: 400 }
      );
    }

    // Check if payroll records already exist (only for new processing, not resume)
    const existingRecords = await PayrollRecord.countDocuments({ payrollRunId: payrollRun._id });
    if (existingRecords > 0 && payrollRun.status === 'draft') {
      return NextResponse.json(
        {
          message: 'Payroll records already exist for this run',
          count: existingRecords
        },
        { status: 409 }
      );
    }

    const useBatch = body.useBatch === true;
    const batchSize = body.batchSize || 50; // Default batch size

    // Get employees to process
    const query: Record<string, unknown> = { employmentStatus: 'active' };

    // Filter by departments if specified
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      query.departmentId = { $in: payrollRun.departments };
    }

    const employees = await Employee.find(query)
      .populate('departmentId');

    if (employees.length === 0) {
      return NextResponse.json(
        { error: 'No active employees found for processing' },
        { status: 400 }
      );
    }

    // Update payroll run status to processing (only if not already processing)
    if (payrollRun.status !== 'processing') {
      payrollRun.status = 'processing';
    }
    payrollRun.totalEmployees = employees.length;
    payrollRun.updatedBy = new mongoose.Types.ObjectId(currentUser.id as string);
    if (body.notes) {
      // For resume operations, append to existing notes
      if (isResumeOperation && payrollRun.notes) {
        payrollRun.notes = `${payrollRun.notes}\n\n${body.notes}`;
      } else {
        payrollRun.notes = body.notes;
      }
    }
    await payrollRun.save();

    // If batch processing is requested (use optimized processor)
    if (useBatch) {
      // Start optimized batch processing in the background
      processOptimizedBatch(id, currentUser.id, batchSize).catch(error => {
        logger.error(`Error in optimized batch processing for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
      });

      return NextResponse.json({
        success: true,
        message: isResumeOperation
          ? 'Payroll processing resumed successfully'
          : 'Optimized payroll run batch processing started',
        data: {
          totalEmployees: employees.length,
          batchSize,
          isResumeOperation,
          existingRecords,
          message: isResumeOperation
            ? `Processing resumed with ${existingRecords} existing records. Will skip processed employees.`
            : 'Processing started with enhanced performance optimizations'
        }
      });
    } else {
      // Process payroll run synchronously with optimized processor
      try {
        const result = await optimizedPayrollProcessor.processPayrollRunOptimized(
          id,
          currentUser.id,
          {
            batchSize: 5,
            maxConcurrency: 3,
            enableProgressTracking: false,
            skipExistingRecords: true
          }
        );

        return NextResponse.json({
          success: true,
          message: 'Payroll run processed successfully with optimizations',
          data: result
        });
      } catch (optimizedError) {
        // Fallback to unified processor if optimized fails
        logger.warn('Optimized processor failed, falling back to unified service', LogCategory.PAYROLL, optimizedError);

        const result = await unifiedPayrollService.processPayrollRun(id, {
          userId: currentUser.id,
          notes: body.notes
        });

        return NextResponse.json({
          success: true,
          message: 'Payroll run processed successfully (fallback)',
          data: result
        });
      }
    }
  } catch (error: unknown) {
    logger.error(`Error processing payroll run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to process payroll run', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Process a payroll run using the optimized processor
 * This function runs in the background with enhanced performance
 */
async function processOptimizedBatch(payrollRunId: string, userId: string, batchSize: number) {
  try {
    await connectToDatabase();

    logger.info(`Starting optimized batch processing for payroll run ${payrollRunId}`, LogCategory.PAYROLL, {
      payrollRunId,
      batchSize,
      userId
    });

    // Use the optimized processor with enhanced settings for batch processing
    const result = await optimizedPayrollProcessor.processPayrollRunOptimized(
      payrollRunId,
      userId,
      {
        batchSize: Math.max(batchSize, 10), // Ensure minimum batch size for efficiency
        maxConcurrency: Math.min(Math.max(Math.floor(batchSize / 2), 3), 8), // Dynamic concurrency
        enableProgressTracking: true,
        skipExistingRecords: true
      }
    );

    logger.info(`Optimized batch processing completed for payroll run ${payrollRunId}`, LogCategory.PAYROLL, {
      payrollRunId,
      totalEmployees: result.totalEmployees,
      processedEmployees: result.processedEmployees,
      failedEmployees: result.failedEmployees,
      processingTime: `${result.processingTime}ms`
    });

    // If processing completed but totals are zero, try to recalculate from database
    if (result.success && result.processedEmployees > 0) {
      const updatedPayrollRun = await PayrollRun.findById(payrollRunId);
      if (updatedPayrollRun &&
          updatedPayrollRun.totalGrossSalary === 0 &&
          updatedPayrollRun.totalDeductions === 0 &&
          updatedPayrollRun.totalTax === 0 &&
          updatedPayrollRun.totalNetSalary === 0) {

        logger.warn(`Payroll run ${payrollRunId} has zero totals after processing, attempting to recalculate`, LogCategory.PAYROLL);

        // Try to recalculate totals from database directly
        try {
          const payrollRecords = await PayrollRecord.find({
            payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
            status: { $ne: 'cancelled' }
          }).lean();

          if (payrollRecords.length > 0) {
            const totals = payrollRecords.reduce((acc, record) => ({
              totalGrossSalary: acc.totalGrossSalary + (record.grossSalary || 0),
              totalDeductions: acc.totalDeductions + (record.totalDeductions || 0),
              totalTax: acc.totalTax + (record.totalTax || 0),
              totalNetSalary: acc.totalNetSalary + (record.netSalary || 0)
            }), {
              totalGrossSalary: 0,
              totalDeductions: 0,
              totalTax: 0,
              totalNetSalary: 0
            });

            updatedPayrollRun.totalGrossSalary = totals.totalGrossSalary;
            updatedPayrollRun.totalDeductions = totals.totalDeductions;
            updatedPayrollRun.totalTax = totals.totalTax;
            updatedPayrollRun.totalNetSalary = totals.totalNetSalary;
            await updatedPayrollRun.save();

            logger.info(`Successfully recalculated totals for payroll run ${payrollRunId}`, LogCategory.PAYROLL, {
              recordsProcessed: payrollRecords.length,
              totals
            });
          }
        } catch (recalcError) {
          logger.error(`Failed to recalculate totals for payroll run ${payrollRunId}`, LogCategory.PAYROLL, recalcError);
        }
      }
    }

    return result;

  } catch (error: unknown) {
    logger.error(`Error in optimized batch processing for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
    throw error;
  }
}

// app/api/payroll/runs/[id]/debug/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import PayrollProcessingBatch from '@/models/payroll/PayrollProcessingBatch';
import Employee from '@/models/Employee';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);
    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Get existing payroll records
    const existingRecords = await PayrollRecord.find({ payrollRunId: payrollRun._id })
      .populate('employeeId', 'firstName lastName')
      .select('employeeId status createdAt grossSalary netSalary')
      .lean();

    // Get processing batches
    const processingBatches = await PayrollProcessingBatch.find({ payrollRunId: payrollRun._id })
      .sort({ createdAt: -1 })
      .lean();

    // Get total employees that should be processed
    const query: any = { employmentStatus: 'active' };
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      query.departmentId = { $in: payrollRun.departments };
    }

    const totalEmployees = await Employee.countDocuments(query);
    const activeEmployees = await Employee.find(query)
      .select('firstName lastName departmentId')
      .populate('departmentId', 'name')
      .lean();

    // Calculate processing statistics
    const processedEmployeeIds = new Set(existingRecords.map(r => r.employeeId._id.toString()));
    const unprocessedEmployees = activeEmployees.filter((emp: any) =>
      !processedEmployeeIds.has(emp._id.toString())
    );

    const debugInfo = {
      payrollRun: {
        id: payrollRun._id,
        name: payrollRun.name,
        status: payrollRun.status,
        totalEmployees: payrollRun.totalEmployees,
        processedEmployees: payrollRun.processedEmployees,
        createdAt: payrollRun.createdAt,
        updatedAt: payrollRun.updatedAt,
        processedAt: payrollRun.processedAt,
        notes: payrollRun.notes
      },
      statistics: {
        totalActiveEmployees: totalEmployees,
        existingRecords: existingRecords.length,
        unprocessedEmployees: unprocessedEmployees.length,
        processingBatches: processingBatches.length,
        completionPercentage: totalEmployees > 0 ? (existingRecords.length / totalEmployees) * 100 : 0
      },
      existingRecords: existingRecords.map(record => ({
        employeeId: record.employeeId._id,
        employeeName: `${record.employeeId.firstName} ${record.employeeId.lastName}`,
        status: record.status,
        grossSalary: record.grossSalary,
        netSalary: record.netSalary,
        createdAt: record.createdAt
      })),
      unprocessedEmployees: unprocessedEmployees.map(emp => ({
        employeeId: emp._id,
        employeeName: `${emp.firstName} ${emp.lastName}`,
        department: emp.departmentId?.name || 'No Department'
      })),
      processingBatches: processingBatches.map(batch => ({
        id: batch._id,
        status: batch.status,
        totalEmployees: batch.totalEmployees,
        processedEmployees: batch.processedEmployees,
        currentEmployee: batch.currentEmployee,
        startedAt: batch.startedAt,
        completedAt: batch.completedAt,
        error: batch.error
      })),
      recommendations: [] as Array<{type: string; message: string; action: string}>
    };

    // Add recommendations based on current state
    if (payrollRun.status === 'processing' && existingRecords.length === 0) {
      debugInfo.recommendations.push({
        type: 'warning',
        message: 'Payroll run is in processing status but no records exist. Consider resuming processing.',
        action: 'Resume Processing'
      });
    }

    if (payrollRun.status === 'processing' && unprocessedEmployees.length > 0) {
      debugInfo.recommendations.push({
        type: 'info',
        message: `${unprocessedEmployees.length} employees still need to be processed.`,
        action: 'Resume Processing'
      });
    }

    if (payrollRun.status === 'processing' && unprocessedEmployees.length === 0 && existingRecords.length > 0) {
      debugInfo.recommendations.push({
        type: 'success',
        message: 'All employees have been processed. Consider updating status to completed.',
        action: 'Update Status to Completed'
      });
    }

    if (processingBatches.length > 0) {
      const latestBatch = processingBatches[0];
      if (latestBatch.status === 'error') {
        debugInfo.recommendations.push({
          type: 'error',
          message: `Latest processing batch failed: ${latestBatch.error}`,
          action: 'Retry Processing'
        });
      }
    }

    logger.info(`Debug info requested for payroll run ${id}`, LogCategory.API, {
      payrollRunId: id,
      status: payrollRun.status,
      existingRecords: existingRecords.length,
      totalEmployees
    });

    return NextResponse.json({
      success: true,
      data: debugInfo
    });

  } catch (error) {
    logger.error(`Error getting debug info for payroll run ${(await params).id}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to get debug information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// app/api/payroll/runs/[id]/validate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import { Employee } from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * Validate employees for a payroll run
 * This checks if all employees have active salary structures
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise
  const { id } = await params;
  const payrollRunId = id;

  // Log the start of validation with the payroll run ID
  console.log(`Starting validation for payroll run: ${payrollRunId}`);

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = user;

    logger.info('Validating employees for payroll run', LogCategory.API, {
      userId: currentUser.id,
      payrollRunId
    });

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    console.log(`Attempting to find payroll run with ID: ${payrollRunId}`);
    let payrollRun;
    try {
      // Ensure valid ObjectId
      let objectId;
      try {
        objectId = new mongoose.Types.ObjectId(payrollRunId);
        console.log(`Created valid ObjectId: ${objectId}`);
      } catch (idError) {
        console.error(`Invalid ObjectId format: ${payrollRunId}`, idError);
        return NextResponse.json(
          { error: 'Invalid payroll run ID format', details: 'The provided ID is not a valid MongoDB ObjectId' },
          { status: 400 }
        );
      }

      payrollRun = await PayrollRun.findById(objectId);
      console.log(`Payroll run found:`, payrollRun ? {
        id: payrollRun._id,
        status: payrollRun.status,
        departments: payrollRun.departments
      } : 'null');
    } catch (findError: unknown) {
      console.error(`Error finding payroll run:`, findError);
      return NextResponse.json(
        { error: 'Error finding payroll run', details: findError instanceof Error ? findError instanceof Error ? findError instanceof Error ? findError instanceof Error ? findError.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
        { status: 500 }
      );
    }

    if (!payrollRun) {
      console.log(`Payroll run not found with ID: ${payrollRunId}`);
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Get employees to validate
    const query: Record<string, unknown> = { employmentStatus: 'active' };

    // Log the query for debugging
    logger.debug('Employee query', LogCategory.API, { query });

    // Filter by departments if specified in the payroll run
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      query.departmentId = { $in: payrollRun.departments.map((dept: string | mongoose.Types.ObjectId) =>
        typeof dept === 'string' ? new mongoose.Types.ObjectId(dept) : dept
      )};

      // Log the departments filter
      logger.debug('Departments filter', LogCategory.API, {
        departments: payrollRun.departments,
        query
      });
    }

    // Log before employee query
    console.log('Before employee query:', {
      modelExists: !!Employee,
      query
    });

    // Get employees
    let employees = [];
    try {
      console.log(`Executing Employee.find() with query:`, query);
      employees = await Employee.find(query)
        .populate('departmentId');

      console.log(`Employee query returned ${employees.length} results`);

      // Log employee query results
      if (employees.length > 0) {
        console.log(`First employee:`, {
          id: employees[0]._id,
          name: `${employees[0].firstName} ${employees[0].lastName}`,
          status: employees[0].employmentStatus
        });
      }

      logger.debug('Employee query results', LogCategory.API, {
        count: employees.length,
        firstEmployee: employees.length > 0 ? {
          id: employees[0]._id,
          name: `${employees[0].firstName} ${employees[0].lastName}`,
          status: employees[0].employmentStatus
        } : null
      });
    } catch (queryError) {
      console.error('Error querying employees:', queryError);
      logger.error('Error querying employees', LogCategory.API, queryError);
      throw queryError;
    }

    if (employees.length === 0) {
      return NextResponse.json(
        { error: 'No active employees found for validation' },
        { status: 400 }
      );
    }

    logger.info('Found employees for validation', LogCategory.API, {
      payrollRunId,
      employeeCount: employees.length
    });

    // Validate each employee has an active salary structure
    const invalidEmployees = [];
    const validEmployees = [];

    // Log EmployeeSalary model check
    console.log('EmployeeSalary model check:', {
      modelExists: !!EmployeeSalary
    });

    logger.debug('EmployeeSalary model check', LogCategory.API, {
      modelExists: !!EmployeeSalary
    });

    console.log(`Starting salary validation for ${employees.length} employees`);

    for (const employee of employees) {
      // Log employee being checked
      console.log(`Checking salary for employee: ${employee.firstName} ${employee.lastName} (${employee._id})`);

      logger.debug('Checking employee salary', LogCategory.API, {
        employeeId: employee._id,
        name: `${employee.firstName} ${employee.lastName}`
      });

      try {
        // Check if employee has an active salary
        const employeeId = employee._id.toString();
        console.log(`Searching for active salary with query:`, {
          employeeId,
          isActive: true
        });

        // Try different formats of the employee ID to ensure we find a match
        const employeeSalary = await EmployeeSalary.findOne({
          $or: [
            { employeeId: employee._id }, // ObjectId
            { employeeId: employeeId }, // String
            { employeeId: new mongoose.Types.ObjectId(employeeId) } // New ObjectId
          ],
          isActive: true
        });

        // Log salary check result
        console.log(`Salary check result for ${employee.firstName} ${employee.lastName}:`, {
          hasSalary: !!employeeSalary,
          salaryId: employeeSalary?._id,
          basicSalary: employeeSalary?.basicSalary
        });

        logger.debug('Employee salary check result', LogCategory.API, {
          employeeId: employee._id,
          hasSalary: !!employeeSalary,
          salaryId: employeeSalary?._id
        });

        if (!employeeSalary) {
          invalidEmployees.push({
            employeeId: employee._id,
            name: `${employee.firstName} ${employee.lastName}`,
            department: employee.departmentId ?
              (typeof employee.departmentId === 'object' ? employee.departmentId.name : employee.departmentId)
              : 'Unknown',
            position: employee.position || 'Unknown'
          });
        } else {
          validEmployees.push({
            employeeId: employee._id,
            name: `${employee.firstName} ${employee.lastName}`,
            department: employee.departmentId ?
              (typeof employee.departmentId === 'object' ? employee.departmentId.name : employee.departmentId)
              : 'Unknown',
            position: employee.position || 'Unknown',
            salary: employeeSalary.basicSalary
          });
        }
      } catch (salaryError) {
        logger.error('Error checking employee salary', LogCategory.API, {
          employeeId: employee._id,
          error: salaryError
        });

        // Add to invalid employees with error flag
        invalidEmployees.push({
          employeeId: employee._id,
          name: `${employee.firstName} ${employee.lastName}`,
          department: employee.departmentId ?
            (typeof employee.departmentId === 'object' ? employee.departmentId.name : employee.departmentId)
            : 'Unknown',
          position: employee.position || 'Unknown',
          error: true
        });
      }
    }

    // If there are invalid employees, return them
    if (invalidEmployees.length > 0) {
      console.log(`Validation failed: ${invalidEmployees.length} employees are missing active salary structures`);
      console.log(`Invalid employees:`, invalidEmployees.map(emp => emp.name));

      return NextResponse.json({
        success: false,
        error: `${invalidEmployees.length} employees are missing active salary structures`,
        totalEmployees: employees.length,
        validEmployees: validEmployees.length,
        invalidEmployees
      }, { status: 400 });
    }

    // All employees are valid
    console.log(`Validation successful: All ${employees.length} employees have active salary structures`);

    return NextResponse.json({
      success: true,
      message: 'All employees have active salary structures',
      totalEmployees: employees.length,
      validEmployees: validEmployees.length
    });

  } catch (error) {
    console.error(`Error validating employees for payroll run ${payrollRunId}:`, error);
    logger.error(`Error validating employees for payroll run ${payrollRunId}`, LogCategory.API, error);

    // Get detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    console.error(`Error details:`, {
      message: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Failed to validate employees',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

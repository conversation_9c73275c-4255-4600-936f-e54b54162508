import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollService } from '@/lib/services/payroll/unified-payroll-service';
import { errorService } from '@/lib/backend/services/error-service';

/**
 * GET /api/payroll/runs/[id]
 * Get a payroll run by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.ACCOUNTANT
            ]
          }
        }
      );
    }

    // Get payroll run
    const payrollRun = await payrollService.getPayrollRunById(payrollRunId);

    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user._id?.toString(),
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId }
        }
      );
    }

    // Log the payroll run data for debugging
    console.log('Payroll run data from API:', {
      id: payrollRun._id,
      totalGrossSalary: payrollRun.totalGrossSalary,
      totalDeductions: payrollRun.totalDeductions,
      totalTax: payrollRun.totalTax,
      totalNetSalary: payrollRun.totalNetSalary,
      typeOfGross: typeof payrollRun.totalGrossSalary,
      typeOfNet: typeof payrollRun.totalNetSalary
    });

    // Convert the Mongoose document to a plain JavaScript object
    const payrollRunData = payrollRun.toObject ? payrollRun.toObject() : JSON.parse(JSON.stringify(payrollRun));

    return NextResponse.json({
      success: true,
      data: payrollRunData
    });
  } catch (error) {
    logger.error(`Error getting payroll run ${payrollRunId}`, LogCategory.API, error);
    return errorService.createApiResponse(
      'SYSTEM',
      'PAYROLL_RUN_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to retrieve payroll run details. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      'HIGH'
    );
  }
}

/**
 * PATCH /api/payroll/runs/[id]
 * Perform operations on a payroll run (process, approve, pay, cancel)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let payrollRun;
    let requiredRoles: UserRole[];

    // Perform action based on the action type
    switch (body.action) {
      case 'process':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR,
          UserRole.HR_DIRECTOR,
          UserRole.FINANCE_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to process payroll run' },
            { status: 403 }
          );
        }

        // Process payroll run
        payrollRun = await payrollService.processPayrollRun(payrollRunId, {
          userId: user._id?.toString() || '',
          notes: body.notes
        });
        break;

      case 'approve':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR,
          UserRole.HR_DIRECTOR
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to approve payroll run' },
            { status: 403 }
          );
        }

        // Approve payroll run
        payrollRun = await payrollService.approvePayrollRun(payrollRunId, user._id?.toString() || '');
        break;

      case 'pay':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to mark payroll run as paid' },
            { status: 403 }
          );
        }

        // Mark payroll run as paid
        payrollRun = await payrollService.markPayrollRunAsPaid(payrollRunId, user._id?.toString() || '');
        break;

      case 'cancel':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to cancel payroll run' },
            { status: 403 }
          );
        }

        // Validate cancellation reason
        if (!body.reason) {
          return NextResponse.json(
            { error: 'Cancellation reason is required' },
            { status: 400 }
          );
        }

        // Cancel payroll run
        payrollRun = await payrollService.cancelPayrollRun(payrollRunId, user._id?.toString() || '', body.reason);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: process, approve, pay, cancel' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Payroll run ${body.action} successful`,
      data: payrollRun
    });
  } catch (error) {
    logger.error(`Error performing action on payroll run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to perform action on payroll run', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payroll/runs/[id]
 * Delete a payroll run and its associated records
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get payroll run to check status for permission validation
    let payrollRunStatus = 'unknown';
    try {
      const payrollRun = await payrollService.getPayrollRunById(payrollRunId);
      payrollRunStatus = payrollRun.status;
    } catch (error) {
      // If we can't get the payroll run, it might not exist
      logger.warn(`Could not retrieve payroll run for permission check: ${payrollRunId}`, LogCategory.API);
    }

    // Enhanced role-based permissions based on payroll run status
    const basicDeleteRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ];

    const criticalDeleteRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ];

    // For critical statuses (approved, paid), require higher permissions
    const requiredRoles = ['approved', 'paid'].includes(payrollRunStatus)
      ? criticalDeleteRoles
      : basicDeleteRoles;

    if (!hasRequiredPermissions(user, requiredRoles)) {
      const statusMessage = ['approved', 'paid'].includes(payrollRunStatus)
        ? `Deleting ${payrollRunStatus} payroll runs requires Super Admin or System Admin permissions`
        : 'Insufficient permissions to delete payroll run';

      return NextResponse.json(
        { error: `Forbidden: ${statusMessage}` },
        { status: 403 }
      );
    }

    // Log critical deletion attempts
    if (['approved', 'paid'].includes(payrollRunStatus)) {
      logger.warn(`Critical payroll run deletion attempted`, LogCategory.API, {
        payrollRunId,
        status: payrollRunStatus,
        userId: user._id?.toString(),
        userRole: user.role,
        timestamp: new Date().toISOString()
      });
    }

    // Delete payroll run and associated records
    const result = await payrollService.deletePayrollRunWithRecords(payrollRunId, user._id?.toString() || '');

    return NextResponse.json({
      success: true,
      message: 'Payroll run and associated records deleted successfully',
      data: result
    });
  } catch (error) {
    logger.error(`Error deleting payroll run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete payroll run', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

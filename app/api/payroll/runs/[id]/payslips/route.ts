import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { bulkPayslipService } from '@/lib/services/payroll/bulk-payslip-service';
import PaySlip from '@/models/payroll/PaySlip';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

/**
 * GET /api/payroll/runs/[id]/payslips
 * Get payslips for a payroll run
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise
  const { id } = await params;
  const payrollRunId = id;

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.ACCOUNTANT
            ]
          }
        }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || undefined;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Connect to database
    await connectToDatabase();

    // Build query
    const query: Record<string, unknown> = { payrollRunId };
    if (status) {
      query.status = status;
    }

    // Count total documents
    const totalDocs = await PaySlip.countDocuments(query);

    // Get payslips
    const payslips = await PaySlip.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName employeeNumber')
      .populate('createdBy', 'name');

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        docs: payslips,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error) {
    logger.error(`Error getting payslips for run ${payrollRunId}`, LogCategory.API, error instanceof Error ? error : new Error('Unknown error'));
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYSLIP_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to retrieve payslips for payroll run. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * POST /api/payroll/runs/[id]/payslips
 * Generate payslips for a payroll run
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise
  const { id } = await params;
  const payrollRunId = id;

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.HR_DIRECTOR,
              UserRole.FINANCE_MANAGER
            ]
          }
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    logger.info('Generating payslips for payroll run', LogCategory.PAYROLL, {
      payrollRunId,
      userId: user.id
    });

    // Check if payslips already exist for this run
    const existingPayslips = await PaySlip.countDocuments({ payrollRunId });
    logger.info('Checked for existing payslips', LogCategory.PAYROLL, {
      payrollRunId,
      existingCount: existingPayslips
    });

    if (existingPayslips > 0) {
      logger.warn('Payslips already exist for this payroll run', LogCategory.PAYROLL, {
        payrollRunId,
        existingCount: existingPayslips
      });
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'PAYSLIPS_ALREADY_EXIST',
        'Payslips already exist for this payroll run',
        'Payslips have already been generated for this payroll run. Please delete existing payslips before regenerating.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId, existingCount: existingPayslips }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    try {
      // Generate payslips with progress tracking
      logger.info('Starting bulk payslip generation', LogCategory.PAYROLL, {
        payrollRunId,
        userId: user.id
      });

      const result = await bulkPayslipService.generateAllPayslips(payrollRunId, user.id);

      logger.info('Payslip generation started successfully', LogCategory.PAYROLL, {
        payrollRunId,
        count: result.count,
        operationId: result.operationId
      });

      return NextResponse.json({
        success: true,
        message: 'Payslip generation started successfully',
        data: {
          count: result.count,
          operationId: result.operationId
        },
        operationId: result.operationId
      });
    } catch (error) {
      logger.error('Error in bulk payslip generation service', LogCategory.PAYROLL, error, {
        payrollRunId,
        userId: user.id
      });
      throw error;
    }
  } catch (error) {
    logger.error(`Error generating payslips for run ${payrollRunId}`, LogCategory.API, error instanceof Error ? error : new Error('Unknown error'));

    // Get detailed error message
    const errorMessage = error instanceof Error
      ? error.message
      : (typeof error === 'object' && error !== null && 'message' in error)
        ? String(error.message)
        : 'Unknown error';

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYSLIP_GENERATION_FAILED',
      errorMessage,
      'Failed to generate payslips for payroll run. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

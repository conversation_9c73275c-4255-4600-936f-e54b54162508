// app/api/payroll/runs/bulk-approve/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import PayrollRun from '@/models/payroll/PayrollRun'
import { payrollService } from '@/lib/services/payroll/unified-payroll-service'
import mongoose from 'mongoose'

export const runtime = 'nodejs';



// Required roles for bulk approval
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.HR_DIRECTOR
]

interface BulkApproveRequest {
  payrollRunIds: string[]
  notes?: string
  departments?: string[]
}

interface BulkApproveResult {
  success: boolean
  message: string
  data: {
    totalRuns: number
    approvedRuns: number
    failedRuns: number
    skippedRuns: number
    results: Array<{
      payrollRunId: string
      payrollRunName: string
      status: 'approved' | 'failed' | 'skipped'
      message?: string
      error?: string
    }>
  }
}

/**
 * POST /api/payroll/runs/bulk-approve
 * Approve multiple payroll runs in bulk
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Parse request body
    const body: BulkApproveRequest = await request.json()
    const { payrollRunIds, notes, departments } = body

    // Validate request
    if (!payrollRunIds || !Array.isArray(payrollRunIds) || payrollRunIds.length === 0) {
      return NextResponse.json(
        { error: 'Payroll run IDs are required and must be a non-empty array' },
        { status: 400 }
      )
    }

    // Validate payroll run IDs
    const invalidIds = payrollRunIds.filter(id => !mongoose.Types.ObjectId.isValid(id))
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid payroll run IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      )
    }

    // Get payroll runs to approve
    const query: any = {
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) },
      status: 'completed'
    }

    // Filter by departments if specified
    if (departments && departments.length > 0) {
      query.departments = { $in: departments }
    }

    const payrollRuns = await PayrollRun.find(query)

    if (payrollRuns.length === 0) {
      return NextResponse.json(
        { error: 'No eligible payroll runs found for approval (must be in completed status)' },
        { status: 404 }
      )
    }

    // Check for runs that are not in completed status
    const allRuns = await PayrollRun.find({
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) }
    })

    const nonCompletedRuns = allRuns.filter(run => run.status !== 'completed')
    const skippedRuns = nonCompletedRuns.map(run => ({
      payrollRunId: run._id.toString(),
      payrollRunName: run.name,
      status: 'skipped' as const,
      message: `Payroll run is in ${run.status} status, not completed`
    }))

    logger.info('Starting bulk payroll approval', LogCategory.PAYROLL, {
      userId: user.id,
      totalRuns: payrollRunIds.length,
      eligibleRuns: payrollRuns.length,
      skippedRuns: skippedRuns.length
    })

    // Process approvals
    const results = []
    let approvedRuns = 0
    let failedRuns = 0

    for (const run of payrollRuns) {
      try {
        // Approve the payroll run
        await payrollService.approvePayrollRun(run._id.toString(), user.id, notes)

        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'approved' as const,
          message: 'Payroll run approved successfully'
        })
        approvedRuns++

        logger.info(`Bulk approval: Approved payroll run ${run.name}`, LogCategory.PAYROLL, {
          payrollRunId: run._id.toString(),
          approvedBy: user.id
        })

      } catch (error) {
        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'failed' as const,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        failedRuns++

        logger.error(`Bulk approval: Failed to approve payroll run ${run.name}`, LogCategory.PAYROLL, error)
      }
    }

    const result: BulkApproveResult = {
      success: true,
      message: `Bulk approval completed: ${approvedRuns} approved, ${failedRuns} failed, ${skippedRuns.length} skipped`,
      data: {
        totalRuns: payrollRunIds.length,
        approvedRuns,
        failedRuns,
        skippedRuns: skippedRuns.length,
        results: [...results, ...skippedRuns]
      }
    }

    logger.info('Bulk payroll approval completed', LogCategory.PAYROLL, {
      userId: user.id,
      approvedRuns,
      failedRuns,
      skippedRuns: skippedRuns.length,
      totalRuns: payrollRunIds.length
    })

    return NextResponse.json(result)

  } catch (error) {
    logger.error('Error in bulk payroll approval', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to approve payroll runs in bulk', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/payroll/runs/bulk-approve
 * Get eligible payroll runs for bulk approval
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const departments = searchParams.get('departments')?.split(',').filter(Boolean)
    const limit = parseInt(searchParams.get('limit') || '50')

    // Build query for eligible runs
    const query: any = {
      status: 'completed'
    }

    // Filter by departments if specified
    if (departments && departments.length > 0) {
      query.departments = { $in: departments }
    }

    // Get eligible payroll runs
    const eligibleRuns = await PayrollRun.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('departments', 'name')
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean()

    const summary = {
      totalEligible: eligibleRuns.length,
      totalEmployees: eligibleRuns.reduce((sum, run) => sum + run.totalEmployees, 0),
      totalGrossSalary: eligibleRuns.reduce((sum, run) => sum + run.totalGrossSalary, 0),
      totalNetSalary: eligibleRuns.reduce((sum, run) => sum + run.totalNetSalary, 0)
    }

    return NextResponse.json({
      success: true,
      data: {
        eligibleRuns,
        summary
      }
    })

  } catch (error) {
    logger.error('Error getting eligible payroll runs for approval', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to get eligible payroll runs', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

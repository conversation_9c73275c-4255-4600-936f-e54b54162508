// app/api/payroll/runs/bulk-pay/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import PayrollRun from '@/models/payroll/PayrollRun'
import { payrollService } from '@/lib/services/payroll/unified-payroll-service'
import mongoose from 'mongoose'

export const runtime = 'nodejs';



// Required roles for bulk payment
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR
]

interface BulkPayRequest {
  payrollRunIds: string[]
  paymentMethod?: string
  paymentReference?: string
  notes?: string
  departments?: string[]
}

interface BulkPayResult {
  success: boolean
  message: string
  data: {
    totalRuns: number
    paidRuns: number
    failedRuns: number
    skippedRuns: number
    totalAmount: number
    results: Array<{
      payrollRunId: string
      payrollRunName: string
      status: 'paid' | 'failed' | 'skipped'
      amount?: number
      message?: string
      error?: string
    }>
  }
}

/**
 * POST /api/payroll/runs/bulk-pay
 * Mark multiple payroll runs as paid in bulk
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Parse request body
    const body: BulkPayRequest = await request.json()
    const { payrollRunIds, paymentMethod = 'Bank Transfer', paymentReference, notes, departments } = body

    // Validate request
    if (!payrollRunIds || !Array.isArray(payrollRunIds) || payrollRunIds.length === 0) {
      return NextResponse.json(
        { error: 'Payroll run IDs are required and must be a non-empty array' },
        { status: 400 }
      )
    }

    // Validate payroll run IDs
    const invalidIds = payrollRunIds.filter(id => !mongoose.Types.ObjectId.isValid(id))
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid payroll run IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      )
    }

    // Get payroll runs to mark as paid
    const query: any = {
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) },
      status: 'approved'
    }

    // Filter by departments if specified
    if (departments && departments.length > 0) {
      query.departments = { $in: departments }
    }

    const payrollRuns = await PayrollRun.find(query)

    if (payrollRuns.length === 0) {
      return NextResponse.json(
        { error: 'No eligible payroll runs found for payment (must be in approved status)' },
        { status: 404 }
      )
    }

    // Check for runs that are not in approved status
    const allRuns = await PayrollRun.find({
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) }
    })

    const nonApprovedRuns = allRuns.filter(run => run.status !== 'approved')
    const skippedRuns = nonApprovedRuns.map(run => ({
      payrollRunId: run._id.toString(),
      payrollRunName: run.name,
      status: 'skipped' as const,
      message: `Payroll run is in ${run.status} status, not approved`
    }))

    logger.info('Starting bulk payroll payment', LogCategory.PAYROLL, {
      userId: user.id,
      totalRuns: payrollRunIds.length,
      eligibleRuns: payrollRuns.length,
      skippedRuns: skippedRuns.length,
      paymentMethod,
      paymentReference
    })

    // Process payments
    const results = []
    let paidRuns = 0
    let failedRuns = 0
    let totalAmount = 0

    for (const run of payrollRuns) {
      try {
        // Mark the payroll run as paid
        await payrollService.markPayrollRunAsPaid(run._id.toString(), user.id, {
          paymentMethod,
          paymentReference: paymentReference || `BULK-${Date.now()}`,
          notes
        })

        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'paid' as const,
          amount: run.totalNetSalary,
          message: 'Payroll run marked as paid successfully'
        })
        paidRuns++
        totalAmount += run.totalNetSalary

        logger.info(`Bulk payment: Marked payroll run ${run.name} as paid`, LogCategory.PAYROLL, {
          payrollRunId: run._id.toString(),
          amount: run.totalNetSalary,
          paidBy: user.id
        })

      } catch (error) {
        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'failed' as const,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        failedRuns++

        logger.error(`Bulk payment: Failed to mark payroll run ${run.name} as paid`, LogCategory.PAYROLL, error)
      }
    }

    const result: BulkPayResult = {
      success: true,
      message: `Bulk payment completed: ${paidRuns} paid, ${failedRuns} failed, ${skippedRuns.length} skipped`,
      data: {
        totalRuns: payrollRunIds.length,
        paidRuns,
        failedRuns,
        skippedRuns: skippedRuns.length,
        totalAmount,
        results: [...results, ...skippedRuns]
      }
    }

    logger.info('Bulk payroll payment completed', LogCategory.PAYROLL, {
      userId: user.id,
      paidRuns,
      failedRuns,
      skippedRuns: skippedRuns.length,
      totalRuns: payrollRunIds.length,
      totalAmount
    })

    return NextResponse.json(result)

  } catch (error) {
    logger.error('Error in bulk payroll payment', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to process payroll payments in bulk', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/payroll/runs/bulk-pay
 * Get eligible payroll runs for bulk payment
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const departments = searchParams.get('departments')?.split(',').filter(Boolean)
    const limit = parseInt(searchParams.get('limit') || '50')

    // Build query for eligible runs
    const query: any = {
      status: 'approved'
    }

    // Filter by departments if specified
    if (departments && departments.length > 0) {
      query.departments = { $in: departments }
    }

    // Get eligible payroll runs
    const eligibleRuns = await PayrollRun.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .populate('departments', 'name')
      .sort({ approvedAt: -1 })
      .limit(limit)
      .lean()

    const summary = {
      totalEligible: eligibleRuns.length,
      totalEmployees: eligibleRuns.reduce((sum, run) => sum + run.totalEmployees, 0),
      totalGrossSalary: eligibleRuns.reduce((sum, run) => sum + run.totalGrossSalary, 0),
      totalNetSalary: eligibleRuns.reduce((sum, run) => sum + run.totalNetSalary, 0),
      totalTax: eligibleRuns.reduce((sum, run) => sum + run.totalTax, 0),
      totalDeductions: eligibleRuns.reduce((sum, run) => sum + run.totalDeductions, 0)
    }

    return NextResponse.json({
      success: true,
      data: {
        eligibleRuns,
        summary
      }
    })

  } catch (error) {
    logger.error('Error getting eligible payroll runs for payment', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to get eligible payroll runs', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// app/api/payroll/export/employee-data/route.ts
import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/backend/auth/auth"
import { connectToDatabase } from "@/lib/backend/database"
import { Employee } from "@/models/Employee"
import EmployeeSalary from "@/models/payroll/EmployeeSalary"
import SalaryBand from "@/models/payroll/SalaryBand"
import { hasRequiredPermissions } from "@/lib/backend/auth/permissions"
import { UserRole } from "@/types/user-roles"
import * as XLSX from 'xlsx'
import logger, { LogCategory } from "@/lib/backend/utils/logger"

// Required permissions for employee data export
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.PAYROLL_SPECIALIST
]

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    await connectToDatabase()

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const format = searchParams.get('format') || 'excel' // excel, csv
    const includePayroll = searchParams.get('includePayroll') === 'true'
    const includeSalaryBands = searchParams.get('includeSalaryBands') === 'true'
    const department = searchParams.get('department')
    const status = searchParams.get('status') // active, inactive, all
    const exportType = searchParams.get('exportType') || 'complete' // complete, basic, payroll_only

    // Build employee query
    let employeeQuery: any = {}

    if (department) {
      employeeQuery.department = department
    }

    if (status && status !== 'all') {
      employeeQuery.isActive = status === 'active'
    }

    // Get employees
    const employees = await Employee.find(employeeQuery)
      .select('-addedBy -lastModifiedBy -accessibleBy -blockedBy') // Exclude populated fields that might cause issues
      .sort({ lastName: 1, firstName: 1 })
      .setOptions({ strictPopulate: false }) // Allow population of non-existent fields
      .lean()

    if (!employees || employees.length === 0) {
      return NextResponse.json({ error: "No employees found for the specified criteria" }, { status: 404 })
    }

    // Get additional data if requested
    let employeeSalaries: SalaryData[] = []
    let salaryBands: BandData[] = []

    if (includePayroll || exportType === 'payroll_only') {
      const employeeIds = employees.map((emp: any) => emp._id)
      employeeSalaries = await EmployeeSalary.find({
        employeeId: { $in: employeeIds },
        isActive: true
      })
        .populate('employeeId', 'firstName lastName employeeId email department position')
        .setOptions({ strictPopulate: false }) // Allow population of non-existent fields
        .lean() as unknown as SalaryData[]
    }

    if (includeSalaryBands) {
      salaryBands = await SalaryBand.find({ isActive: true })
        .sort({ tcmCode: 1 })
        .setOptions({ strictPopulate: false }) // Allow population of non-existent fields
        .lean() as unknown as BandData[]
    }

    // Generate export based on format
    switch (format.toLowerCase()) {
      case 'excel':
        return await generateEmployeeDataExcel(employees as unknown as EmployeeData[], employeeSalaries, salaryBands, exportType)
      case 'csv':
        return await generateEmployeeDataCSV(employees as unknown as EmployeeData[], employeeSalaries, exportType)
      default:
        return NextResponse.json({ error: "Invalid format. Supported formats: excel, csv" }, { status: 400 })
    }

  } catch (error) {
    logger.error("Employee data export error", LogCategory.PAYROLL, error)

    // Check if it's a population error
    if (error instanceof Error && error.message.includes('strictPopulate')) {
      return NextResponse.json(
        { error: "Database schema error: " + error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: "Failed to export employee data" },
      { status: 500 }
    )
  }
}

interface EmployeeData {
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  department?: string;
  position: string;
  employmentType?: string;
  employmentStatus?: string;
  hireDate?: Date;
  dateOfBirth?: Date;
  gender?: string;
  maritalStatus?: string;
  nationalId?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  bankName?: string;
  bankAccountNumber?: string;
  bankBranch?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  isActive?: boolean;
  createdAt?: Date;
}

interface SalaryData {
  employeeId: {
    employeeId: string;
    firstName: string;
    lastName: string;
  };
  basicSalary: number;
  currency: string;
  effectiveDate: Date;
  endDate?: Date;
  allowances?: Array<{ name: string; amount?: number; percentage?: number }>;
  deductions?: Array<{ name: string; amount?: number; percentage?: number }>;
  totalAllowances?: number;
  totalDeductions?: number;
  grossSalary?: number;
  isActive: boolean;
  createdAt: Date;
}

interface BandData {
  tcmCode: string;
  name: string;
  description?: string;
  minSalary: number;
  maxSalary: number;
  currency: string;
  stepIncrement?: number;
  maxSteps?: number;
  annualIncrementPercentage?: number;
  standardAllowances?: Array<{ name: string; amount?: number; percentage?: number }>;
  standardDeductions?: Array<{ name: string; amount?: number; percentage?: number }>;
  effectiveDate: Date;
  isActive: boolean;
}

async function generateEmployeeDataExcel(employees: EmployeeData[], employeeSalaries: SalaryData[], salaryBands: BandData[], exportType: string) {
  const workbook = XLSX.utils.book_new()

  // Employee basic data sheet
  if (exportType === 'complete' || exportType === 'basic') {
    const employeeHeaders = [
      'Employee ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Department',
      'Position', 'Employment Type', 'Employment Status', 'Hire Date', 'Date of Birth',
      'Gender', 'Marital Status', 'National ID', 'Address', 'City', 'State', 'Country',
      'Bank Name', 'Bank Account', 'Bank Branch', 'Emergency Contact', 'Emergency Phone',
      'Is Active', 'Created Date'
    ]

    const employeeData = [
      employeeHeaders,
      ...employees.map((employee: EmployeeData) => [
        employee.employeeId || 'N/A',
        employee.firstName || '',
        employee.lastName || '',
        employee.email || '',
        employee.phone || '',
        employee.department || '',
        employee.position || '',
        employee.employmentType || '',
        employee.employmentStatus || '',
        employee.hireDate ? employee.hireDate.toISOString().split('T')[0] : '',
        employee.dateOfBirth ? employee.dateOfBirth.toISOString().split('T')[0] : '',
        employee.gender || '',
        employee.maritalStatus || '',
        employee.nationalId || '',
        employee.address || '',
        employee.city || '',
        employee.state || '',
        employee.country || 'Malawi',
        employee.bankName || '',
        employee.bankAccountNumber || '',
        employee.bankBranch || '',
        employee.emergencyContactName || '',
        employee.emergencyContactPhone || '',
        employee.isActive ? 'Yes' : 'No',
        employee.createdAt ? employee.createdAt.toISOString().split('T')[0] : ''
      ])
    ]

    const employeeSheet = XLSX.utils.aoa_to_sheet(employeeData)
    XLSX.utils.book_append_sheet(workbook, employeeSheet, 'Employees')
  }

  // Employee salary data sheet
  if ((exportType === 'complete' || exportType === 'payroll_only') && employeeSalaries.length > 0) {
    const salaryHeaders = [
      'Employee ID', 'Employee Name', 'Basic Salary', 'Currency', 'Effective Date',
      'End Date', 'Allowances', 'Deductions', 'Total Allowances', 'Total Deductions',
      'Gross Salary', 'Is Active', 'Created Date'
    ]

    const salaryData = [
      salaryHeaders,
      ...employeeSalaries.map((salary: SalaryData) => {
        const allowancesText = salary.allowances?.map((a) => `${a.name}: ${a.amount || a.percentage + '%'}`).join('; ') || 'None'
        const deductionsText = salary.deductions?.map((d) => `${d.name}: ${d.amount || d.percentage + '%'}`).join('; ') || 'None'

        return [
          salary.employeeId?.employeeId || 'N/A',
          `${salary.employeeId?.firstName || ''} ${salary.employeeId?.lastName || ''}`.trim(),
          salary.basicSalary || 0,
          salary.currency || 'MWK',
          salary.effectiveDate ? salary.effectiveDate.toISOString().split('T')[0] : '',
          salary.endDate ? salary.endDate.toISOString().split('T')[0] : '',
          allowancesText,
          deductionsText,
          salary.totalAllowances || 0,
          salary.totalDeductions || 0,
          salary.grossSalary || 0,
          salary.isActive ? 'Yes' : 'No',
          salary.createdAt ? salary.createdAt.toISOString().split('T')[0] : ''
        ]
      })
    ]

    const salarySheet = XLSX.utils.aoa_to_sheet(salaryData)
    XLSX.utils.book_append_sheet(workbook, salarySheet, 'Employee Salaries')
  }

  // Salary bands reference sheet
  if (salaryBands.length > 0) {
    const bandsHeaders = [
      'TCM Code', 'Band Name', 'Description', 'Min Salary', 'Max Salary', 'Currency',
      'Step Increment', 'Max Steps', 'Annual Increment %', 'Standard Allowances',
      'Standard Deductions', 'Effective Date', 'Is Active'
    ]

    const bandsData = [
      bandsHeaders,
      ...salaryBands.map((band: BandData) => {
        const allowancesText = band.standardAllowances?.map((a) => `${a.name}: ${a.amount || a.percentage + '%'}`).join('; ') || 'None'
        const deductionsText = band.standardDeductions?.map((d) => `${d.name}: ${d.amount || d.percentage + '%'}`).join('; ') || 'None'

        return [
          band.tcmCode || '',
          band.name || '',
          band.description || '',
          band.minSalary || 0,
          band.maxSalary || 0,
          band.currency || 'MWK',
          band.stepIncrement || 0,
          band.maxSteps || 0,
          band.annualIncrementPercentage || 0,
          allowancesText,
          deductionsText,
          band.effectiveDate ? band.effectiveDate.toISOString().split('T')[0] : '',
          band.isActive ? 'Yes' : 'No'
        ]
      })
    ]

    const bandsSheet = XLSX.utils.aoa_to_sheet(bandsData)
    XLSX.utils.book_append_sheet(workbook, bandsSheet, 'Salary Bands Reference')
  }

  // Summary sheet
  const summaryData = [
    ['Employee Data Export Summary'],
    ['Generated:', new Date().toLocaleDateString()],
    ['Export Type:', exportType],
    [],
    ['Total Employees:', employees.length],
    ['Active Employees:', employees.filter((e: EmployeeData) => e.isActive).length],
    ['Inactive Employees:', employees.filter((e: EmployeeData) => !e.isActive).length]
  ]

  if (employeeSalaries.length > 0) {
    summaryData.push(['Employees with Salary Data:', employeeSalaries.length])
  }

  if (salaryBands.length > 0) {
    summaryData.push(['Active Salary Bands:', salaryBands.length])
  }

  // Department breakdown
  const departmentCounts = employees.reduce((acc: any, emp: EmployeeData) => {
    const dept = emp.department || 'Unknown'
    acc[dept] = (acc[dept] || 0) + 1
    return acc
  }, {})

  summaryData.push([])
  summaryData.push(['Department Breakdown:'])
  Object.entries(departmentCounts).forEach(([dept, count]) => {
    summaryData.push([dept, count as number])
  })

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Export Summary')

  const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })
  const filename = `employee_data_export_${exportType}_${new Date().toISOString().split('T')[0]}.xlsx`

  return new NextResponse(buffer, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': buffer.byteLength.toString()
    }
  })
}

async function generateEmployeeDataCSV(employees: EmployeeData[], employeeSalaries: SalaryData[], exportType: string) {
  let csvContent = ''

  if (exportType === 'complete' || exportType === 'basic') {
    // Employee basic data CSV
    const headers = [
      'Employee ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Department',
      'Position', 'Employment Type', 'Employment Status', 'Hire Date', 'Date of Birth',
      'Gender', 'Marital Status', 'National ID', 'Address', 'City', 'State', 'Country',
      'Bank Name', 'Bank Account', 'Bank Branch', 'Emergency Contact', 'Emergency Phone',
      'Is Active', 'Created Date'
    ]

    csvContent += headers.join(',') + '\n'

    employees.forEach((employee: EmployeeData) => {
      const row = [
        employee.employeeId || '',
        employee.firstName || '',
        employee.lastName || '',
        employee.email || '',
        employee.phone || '',
        employee.department || '',
        employee.position || '',
        employee.employmentType || '',
        employee.employmentStatus || '',
        employee.hireDate ? employee.hireDate.toISOString().split('T')[0] : '',
        employee.dateOfBirth ? employee.dateOfBirth.toISOString().split('T')[0] : '',
        employee.gender || '',
        employee.maritalStatus || '',
        employee.nationalId || '',
        employee.address || '',
        employee.city || '',
        employee.state || '',
        employee.country || 'Malawi',
        employee.bankName || '',
        employee.bankAccountNumber || '',
        employee.bankBranch || '',
        employee.emergencyContactName || '',
        employee.emergencyContactPhone || '',
        employee.isActive ? 'Yes' : 'No',
        employee.createdAt ? employee.createdAt.toISOString().split('T')[0] : ''
      ]

      csvContent += row.map(field => `"${field}"`).join(',') + '\n'
    })
  }

  if ((exportType === 'complete' || exportType === 'payroll_only') && employeeSalaries.length > 0) {
    if (csvContent) csvContent += '\n\n'

    // Employee salary data CSV
    const salaryHeaders = [
      'Employee ID', 'Employee Name', 'Basic Salary', 'Currency', 'Effective Date',
      'End Date', 'Total Allowances', 'Total Deductions', 'Gross Salary', 'Is Active', 'Created Date'
    ]

    csvContent += salaryHeaders.join(',') + '\n'

    employeeSalaries.forEach((salary: SalaryData) => {
      const row = [
        salary.employeeId?.employeeId || '',
        `${salary.employeeId?.firstName || ''} ${salary.employeeId?.lastName || ''}`.trim(),
        salary.basicSalary || 0,
        salary.currency || 'MWK',
        salary.effectiveDate ? salary.effectiveDate.toISOString().split('T')[0] : '',
        salary.endDate ? salary.endDate.toISOString().split('T')[0] : '',
        salary.totalAllowances || 0,
        salary.totalDeductions || 0,
        salary.grossSalary || 0,
        salary.isActive ? 'Yes' : 'No',
        salary.createdAt ? salary.createdAt.toISOString().split('T')[0] : ''
      ]

      csvContent += row.map(field => `"${field}"`).join(',') + '\n'
    })
  }

  const filename = `employee_data_export_${exportType}_${new Date().toISOString().split('T')[0]}.csv`

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

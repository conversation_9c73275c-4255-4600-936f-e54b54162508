import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
// Removed deprecated payroll-accounting-service import - using unified payroll service
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for creating department allocation entries
const createDepartmentAllocationSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required')
});

// Validation schema for creating tax payment entries
const createTaxPaymentSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required'),
  bankAccountId: z.string().min(1, 'Bank account ID is required'),
  paymentDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid payment date' }
  )
});

/**
 * POST /api/payroll/accounting/department-allocation
 * Create department allocation entries or tax payment entries
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Determine action
    const action = body.action || 'create_department_allocation';

    switch (action) {
      case 'create_department_allocation':
        return handleCreateDepartmentAllocation(body, user.id);
      case 'create_tax_payment':
        return handleCreateTaxPayment(body, user.id);
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: create_department_allocation, create_tax_payment' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error('Error in payroll accounting department allocation API', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle creating department allocation entries
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleCreateDepartmentAllocation(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = createDepartmentAllocationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    
    const { payrollRunId } = validationResult.data;
    
    // Create department allocation entries
    const journalEntries = await payrollAccountingService.createDepartmentAllocationEntries(
      payrollRunId,
      userId
    );
    
    return NextResponse.json({
      success: true,
      message: 'Department allocation entries created successfully',
      data: journalEntries
    });
  } catch (error: unknown) {
    logger.error('Error creating department allocation entries', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle creating tax payment entries
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleCreateTaxPayment(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = createTaxPaymentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    
    const { payrollRunId, bankAccountId, paymentDate } = validationResult.data;
    
    // Create tax payment entries
    const journalEntry = await payrollAccountingService.createTaxPaymentJournalEntries(
      payrollRunId,
      bankAccountId,
      new Date(paymentDate),
      userId
    );
    
    return NextResponse.json({
      success: true,
      message: 'Tax payment entry created successfully',
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error creating tax payment entry', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

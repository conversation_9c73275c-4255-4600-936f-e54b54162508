//  app/api/payroll/salary-structures/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import SalaryStructure from '@/models/payroll/SalaryStructure'
import Department from '@/models/Department'
import Role from '@/models/Role'
import { connectToDatabase } from '@/lib/backend/database'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

// Define roles that can manage salary structures
const SALARY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.HR_DIRECTOR
]

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name' // Only name is truly required, others can have defaults
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Name': 'name',
  'GRADE': 'name', // TCM salary structure format
  'Grade': 'name',
  'Description': 'description',
  'Effective Date': 'effectiveDate',
  'Expiry Date': 'expiryDate',
  'Currency': 'currency',
  'SALARY IN MK': 'basicComponentAmount', // TCM salary structure format
  'Salary in MK': 'basicComponentAmount',
  'Salary': 'basicComponentAmount',
  'Basic Salary': 'basicComponentAmount',
  'Is Active': 'isActive',
  'Applicable Roles': 'applicableRoles',
  'Applicable Departments': 'applicableDepartments',
  'Basic Component Name': 'basicComponentName',
  'Basic Component Amount': 'basicComponentAmount',
  'Basic Component Is Taxable': 'basicComponentIsTaxable'
}

/**
 * POST handler for bulk importing salary structures
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Connect to database
    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file' },
        { status: 400 }
      )
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing salary structure bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[]

    // Validate and process data
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [] as Array<{ row: number, error: string }>,
      skipped: [] as Array<{ row: number, reason: string }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for salary structure import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json(
        { error: 'File is empty' },
        { status: 400 }
      )
    }

    // Get all departments for reference
    const departments = await Department.find({}).lean()
    const departmentMap = new Map()
    departments.forEach(dept => {
      departmentMap.set(dept.name.toLowerCase(), dept._id)
    })

    // Get all roles for reference
    const roles = await Role.find({}).lean()
    const roleMap = new Map()
    roles.forEach(role => {
      roleMap.set(role.name.toLowerCase(), role._id)
    })

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      // Normalize column names based on mapping
      for (const [key, value] of Object.entries(row)) {
        const normalizedKey = COLUMN_DISPLAY_MAPPING[key] || key.toLowerCase()
        normalizedRow[normalizedKey] = value
      }

      try {
        // Log the row being processed for debugging
        logger.debug('Processing row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow
        })

        // Validate required fields
        for (const field of REQUIRED_COLUMNS) {
          if (!normalizedRow[field]) {
            throw new Error(`Missing required field: ${field}`)
          }
        }

        // Process dates - provide default if not specified
        let effectiveDate: Date
        if (normalizedRow.effectiveDate) {
          try {
            effectiveDate = new Date(normalizedRow.effectiveDate)
            if (isNaN(effectiveDate.getTime())) {
              throw new Error('Invalid date format')
            }
          } catch (error) {
            throw new Error(`Invalid effective date: ${normalizedRow.effectiveDate}`)
          }
        } else {
          // Default to current date if not specified
          effectiveDate = new Date()
        }

        let expiryDate: Date | undefined = undefined
        if (normalizedRow.expiryDate) {
          try {
            expiryDate = new Date(normalizedRow.expiryDate)
            if (isNaN(expiryDate.getTime())) {
              throw new Error('Invalid date format')
            }
          } catch (error) {
            throw new Error(`Invalid expiry date: ${normalizedRow.expiryDate}`)
          }
        }

        // Process isActive field
        let isActive = true
        if (normalizedRow.isActive !== null && normalizedRow.isActive !== undefined) {
          const isActiveStr = normalizedRow.isActive.toString().toLowerCase()
          isActive = isActiveStr === 'true' || isActiveStr === 'yes' || isActiveStr === '1'
        }

        // Process applicable roles
        let applicableRoles: string[] = []
        if (normalizedRow.applicableRoles) {
          const roleNames = normalizedRow.applicableRoles.split(',').map((r: string) => r.trim().toLowerCase())
          for (const roleName of roleNames) {
            const roleId = roleMap.get(roleName)
            if (roleId) {
              applicableRoles.push(roleId)
            } else {
              logger.warn(`Role not found: ${roleName}`, LogCategory.IMPORT, {
                rowIndex: i + 1,
                userId: user.id
              })
            }
          }
        }

        // Process applicable departments
        let applicableDepartments: string[] = []
        if (normalizedRow.applicableDepartments) {
          const deptNames = normalizedRow.applicableDepartments.split(',').map((d: string) => d.trim().toLowerCase())
          for (const deptName of deptNames) {
            const deptId = departmentMap.get(deptName)
            if (deptId) {
              applicableDepartments.push(deptId)
            } else {
              logger.warn(`Department not found: ${deptName}`, LogCategory.IMPORT, {
                rowIndex: i + 1,
                userId: user.id
              })
            }
          }
        }

        // Process basic component
        const basicComponentName = normalizedRow.basicComponentName || 'Basic Salary'
        let basicComponentAmount: number | undefined = undefined
        if (normalizedRow.basicComponentAmount) {
          basicComponentAmount = parseFloat(normalizedRow.basicComponentAmount)
          if (isNaN(basicComponentAmount)) {
            throw new Error(`Invalid basic component amount: ${normalizedRow.basicComponentAmount}`)
          }
        }

        let basicComponentIsTaxable = true
        if (normalizedRow.basicComponentIsTaxable !== null && normalizedRow.basicComponentIsTaxable !== undefined) {
          const isTaxableStr = normalizedRow.basicComponentIsTaxable.toString().toLowerCase()
          basicComponentIsTaxable = isTaxableStr === 'true' || isTaxableStr === 'yes' || isTaxableStr === '1'
        }

        // Create salary structure data
        const salaryStructureData = {
          name: normalizedRow.name,
          description: normalizedRow.description || undefined,
          isActive: isActive,
          effectiveDate: effectiveDate,
          expiryDate: expiryDate,
          currency: normalizedRow.currency || 'MWK',
          applicableRoles: applicableRoles.length > 0 ? applicableRoles : undefined,
          applicableDepartments: applicableDepartments.length > 0 ? applicableDepartments : undefined,
          components: [
            {
              name: basicComponentName,
              type: 'basic',
              amount: basicComponentAmount,
              isTaxable: basicComponentIsTaxable,
              isFixed: true,
              order: 0
            }
          ],
          createdBy: user.id
        }

        // Check if salary structure with same name already exists
        const existingStructure = await SalaryStructure.findOne({ name: salaryStructureData.name })
        if (existingStructure) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: `Salary structure with name ${salaryStructureData.name} already exists`
          })
          logger.debug('Skipping duplicate salary structure', LogCategory.IMPORT, {
            rowIndex: i + 1,
            name: salaryStructureData.name
          })
          continue
        }

        // Create salary structure
        await SalaryStructure.create(salaryStructureData)
        result.successCount++

        logger.info('Salary structure created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          name: salaryStructureData.name,
          userId: user.id
        })
      } catch (error: unknown) {
        result.errorCount++

        // Log the error
        logger.error('Error processing salary structure row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Log the final result
    logger.info('Salary structure bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    })

    return NextResponse.json({
      status: 'success',
      data: result
    })
  } catch (error: unknown) {
    console.error('Error in salary structure bulk import:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    )
  }
}

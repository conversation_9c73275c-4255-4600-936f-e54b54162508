import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Employee } from '@/models/Employee';

/**
 * GET /api/payroll/employee-count
 * Get employee count for payroll processing
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const departmentIds = searchParams.getAll('departmentId');

    // Build query
    const query: Record<string, unknown> = { employmentStatus: 'active' };

    // Add department filter if specified
    if (departmentIds && departmentIds.length > 0) {
      query.departmentId = { $in: departmentIds };
    }

    // Check if Employee model is defined
    if (!Employee || typeof Employee.countDocuments !== 'function') {
      logger.error('Employee model is not properly defined', LogCategory.API);
      return NextResponse.json(
        {
          error: 'Internal server error',
          details: 'Employee model is not properly defined'
        },
        { status: 500 }
      );
    }

    try {
      // Count employees
      const count = await Employee.countDocuments(query);

      // Log the result
      logger.info(`Employee count: ${count}`, LogCategory.API, { query });

      // Return count
      return NextResponse.json({
        success: true,
        data: {
          count
        }
      });
    } catch (countError: unknown) {
      logger.error('Error counting employees', LogCategory.API, countError);
      return NextResponse.json(
        {
          error: 'Failed to count employees',
          details: countError instanceof Error ? countError instanceof Error ? countError instanceof Error ? countError.message : 'An error occurred' : 'An error occurred' : 'An error occurred'
        },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    logger.error('Error getting employee count', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get employee count', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import TaxBracket from '@/models/payroll/TaxBracket';

/**
 * GET /api/payroll/tax-brackets
 * Get tax brackets
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const country = searchParams.get('country') || 'Malawi';
    const currency = searchParams.get('currency') || 'MWK';
    const isActive = searchParams.get('isActive') === 'true';

    // Connect to database
    await connectToDatabase();

    // Build query
    const query: Record<string, unknown> = { country, currency };
    if (isActive) {
      query.isActive = true;
    }

    // Get tax brackets
    const taxBrackets = await TaxBracket.find(query)
      .sort({ effectiveDate: -1 })
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    return NextResponse.json({
      success: true,
      data: taxBrackets
    });
  } catch (error: unknown) {
    logger.error('Error getting tax brackets', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get tax brackets', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payroll/tax-brackets
 * Create a new tax bracket
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.country || !body.currency || !body.effectiveDate || !body.brackets || !Array.isArray(body.brackets)) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if a tax bracket with the same country, currency, and effective date already exists
    const existingTaxBracket = await TaxBracket.findOne({
      country: body.country,
      currency: body.currency,
      effectiveDate: new Date(body.effectiveDate)
    });

    if (existingTaxBracket) {
      return NextResponse.json(
        { error: 'A tax bracket with the same country, currency, and effective date already exists' },
        { status: 400 }
      );
    }

    // Create tax bracket
    const taxBracket = new TaxBracket({
      ...body,
      createdBy: user.id
    });

    await taxBracket.save();

    // If this is a new active bracket, deactivate other brackets
    if (body.isActive) {
      await TaxBracket.updateMany(
        {
          _id: { $ne: taxBracket._id },
          country: body.country,
          currency: body.currency,
          isActive: true
        },
        { isActive: false, updatedBy: user.id }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Tax bracket created successfully',
      data: taxBracket
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating tax bracket', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create tax bracket', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

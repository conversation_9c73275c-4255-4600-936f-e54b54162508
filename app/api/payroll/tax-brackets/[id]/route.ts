// app/api/payroll/tax-brackets/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import TaxBracket from '@/models/payroll/TaxBracket';

/**
 * GET /api/payroll/tax-brackets/[id]
 * Get a tax bracket by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const bracketId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get tax bracket
    const taxBracket = await TaxBracket.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    if (!taxBracket) {
      return NextResponse.json(
        { error: 'Tax bracket not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: taxBracket
    });
  } catch (error) {
    logger.error(`Error getting tax bracket ${bracketId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get tax bracket', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payroll/tax-brackets/[id]
 * Update a tax bracket
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const bracketId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Connect to database
    await connectToDatabase();

    // Get tax bracket
    const taxBracket = await TaxBracket.findById(id);

    if (!taxBracket) {
      return NextResponse.json(
        { error: 'Tax bracket not found' },
        { status: 404 }
      );
    }

    // Update tax bracket
    Object.assign(taxBracket, {
      ...body,
      updatedBy: user.id
    });

    await taxBracket.save();

    // If this is a new active bracket, deactivate other brackets
    if (body.isActive) {
      await TaxBracket.updateMany(
        {
          _id: { $ne: taxBracket._id },
          country: taxBracket.country,
          currency: taxBracket.currency,
          isActive: true
        },
        { isActive: false, updatedBy: user.id }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Tax bracket updated successfully',
      data: taxBracket
    });
  } catch (error) {
    logger.error(`Error updating tax bracket ${bracketId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update tax bracket', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payroll/tax-brackets/[id]
 * Delete a tax bracket
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const bracketId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get tax bracket
    const taxBracket = await TaxBracket.findById(id);

    if (!taxBracket) {
      return NextResponse.json(
        { error: 'Tax bracket not found' },
        { status: 404 }
      );
    }

    // Check if this is an active bracket
    if (taxBracket.isActive) {
      return NextResponse.json(
        { error: 'Cannot delete an active tax bracket' },
        { status: 400 }
      );
    }

    // Delete tax bracket
    await taxBracket.deleteOne();

    return NextResponse.json({
      success: true,
      message: 'Tax bracket deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting tax bracket ${bracketId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete tax bracket', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

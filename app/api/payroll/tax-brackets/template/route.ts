// app/api/payroll/tax-brackets/template/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import * as XLSX from 'xlsx'

export const runtime = 'nodejs';

// Define roles that can download tax bracket templates
const TAX_BRACKET_TEMPLATE_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.HR_DIRECTOR
]

/**
 * GET handler for downloading tax bracket import template
 * @param request - Next.js request
 * @returns Excel file response
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, TAX_BRACKET_TEMPLATE_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Create a new workbook
    const workbook = XLSX.utils.book_new()

    // Define the headers for tax bracket import
    const headers = [
      'Tax Bracket Name',
      'Minimum Income',
      'Maximum Income',
      'Tax Rate (%)',
      'Fixed Amount',
      'Country',
      'Effective Date',
      'End Date',
      'Description',
      'Is Active'
    ]

    // Create sample data for tax brackets (Malawi PAYE tax brackets)
    const sampleData = [
      [
        'Tax Free Band',
        0,
        100000,
        0,
        0,
        'Malawi',
        '2024-01-01',
        '',
        'Tax-free income band for annual income up to MWK 100,000',
        'true'
      ],
      [
        'Standard Rate Band',
        100001,
        500000,
        25,
        0,
        'Malawi',
        '2024-01-01',
        '',
        'Standard tax rate for income between MWK 100,001 and MWK 500,000',
        'true'
      ],
      [
        'Higher Rate Band',
        500001,
        1500000,
        30,
        100000,
        'Malawi',
        '2024-01-01',
        '',
        'Higher tax rate for income between MWK 500,001 and MWK 1,500,000',
        'true'
      ],
      [
        'Top Rate Band',
        1500001,
        '',
        35,
        400000,
        'Malawi',
        '2024-01-01',
        '',
        'Top tax rate for income above MWK 1,500,000',
        'true'
      ]
    ]

    // Create the main data sheet
    const mainData = [headers, ...sampleData]
    const mainSheet = XLSX.utils.aoa_to_sheet(mainData)

    // Set column widths for better readability
    mainSheet['!cols'] = [
      { width: 20 }, // Tax Bracket Name
      { width: 15 }, // Minimum Income
      { width: 15 }, // Maximum Income
      { width: 12 }, // Tax Rate (%)
      { width: 12 }, // Fixed Amount
      { width: 10 }, // Country
      { width: 12 }, // Effective Date
      { width: 12 }, // End Date
      { width: 30 }, // Description
      { width: 10 }  // Is Active
    ]

    // Add the main sheet to workbook
    XLSX.utils.book_append_sheet(workbook, mainSheet, 'Tax Brackets')

    // Create instructions sheet
    const instructions = [
      ['Tax Bracket Import Instructions'],
      [''],
      ['Required Fields:'],
      ['• Tax Bracket Name: Unique name for the tax bracket'],
      ['• Minimum Income: Minimum income threshold for this bracket (MWK)'],
      ['• Tax Rate (%): Tax rate as percentage (0-100)'],
      [''],
      ['Optional Fields:'],
      ['• Maximum Income: Maximum income threshold (leave empty for unlimited)'],
      ['• Fixed Amount: Fixed tax amount to add (default: 0)'],
      ['• Country: Country code (default: Malawi)'],
      ['• Effective Date: When bracket becomes effective (YYYY-MM-DD, default: today)'],
      ['• End Date: When bracket expires (YYYY-MM-DD, leave empty for no expiry)'],
      ['• Description: Description of the tax bracket'],
      ['• Is Active: Whether bracket is active (true/false, default: true)'],
      [''],
      ['Data Format Guidelines:'],
      ['• Income amounts: Numeric values only (no commas or currency symbols)'],
      ['• Tax rates: Percentage values (e.g., 25 for 25%)'],
      ['• Dates: YYYY-MM-DD format (e.g., 2024-01-01)'],
      ['• Boolean values: true/false, yes/no, or 1/0'],
      ['• Maximum Income: Leave empty for unlimited upper bound'],
      [''],
      ['Important Notes:'],
      ['• Tax brackets should not overlap in income ranges'],
      ['• Minimum income of next bracket should be Maximum income + 1 of previous bracket'],
      ['• Each bracket name must be unique for the same effective date'],
      ['• Tax rates are applied progressively (marginal tax system)'],
      ['• Fixed amounts are added to the calculated tax for the bracket'],
      ['• Effective dates allow for historical tax bracket management'],
      [''],
      ['Malawi PAYE Tax System:'],
      ['• Tax-free threshold: MWK 100,000 annually'],
      ['• Progressive tax rates apply to income above thresholds'],
      ['• Standard rate: 25% on income MWK 100,001 - 500,000'],
      ['• Higher rate: 30% on income MWK 500,001 - 1,500,000'],
      ['• Top rate: 35% on income above MWK 1,500,000'],
      ['• Fixed amounts represent cumulative tax from lower brackets']
    ]

    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions)
    instructionsSheet['!cols'] = [{ width: 60 }]
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions')

    // Create validation rules sheet
    const validationRules = [
      ['Field Validation Rules'],
      [''],
      ['Tax Bracket Name:'],
      ['• Required field'],
      ['• Must be unique for the same effective date'],
      ['• Maximum 100 characters'],
      ['• Cannot contain special characters: < > " \' &'],
      [''],
      ['Minimum Income:'],
      ['• Required field'],
      ['• Must be a positive number or zero'],
      ['• Cannot be greater than Maximum Income'],
      [''],
      ['Maximum Income:'],
      ['• Optional field (leave empty for unlimited)'],
      ['• Must be greater than Minimum Income'],
      ['• Must be a positive number'],
      [''],
      ['Tax Rate (%):'],
      ['• Required field'],
      ['• Must be between 0 and 100'],
      ['• Can include decimal places (e.g., 25.5)'],
      [''],
      ['Fixed Amount:'],
      ['• Optional field (default: 0)'],
      ['• Must be a positive number or zero'],
      ['• Represents cumulative tax from lower brackets'],
      [''],
      ['Country:'],
      ['• Optional field (default: Malawi)'],
      ['• Standard country names or codes'],
      [''],
      ['Effective Date:'],
      ['• Optional field (default: current date)'],
      ['• Must be in YYYY-MM-DD format'],
      ['• Cannot be in the past for new brackets'],
      [''],
      ['End Date:'],
      ['• Optional field (no expiry if empty)'],
      ['• Must be in YYYY-MM-DD format'],
      ['• Must be after Effective Date'],
      [''],
      ['Is Active:'],
      ['• Optional field (default: true)'],
      ['• Accepted values: true/false, yes/no, 1/0']
    ]

    const validationSheet = XLSX.utils.aoa_to_sheet(validationRules)
    validationSheet['!cols'] = [{ width: 50 }]
    XLSX.utils.book_append_sheet(workbook, validationSheet, 'Validation Rules')

    // Create examples sheet with different scenarios
    const exampleHeaders = [
      'Scenario',
      'Tax Bracket Name',
      'Minimum Income',
      'Maximum Income',
      'Tax Rate (%)',
      'Fixed Amount',
      'Country',
      'Effective Date',
      'End Date',
      'Description',
      'Is Active'
    ]

    const examples = [
      [
        'Basic Tax Bracket',
        'Standard Rate',
        100001,
        500000,
        25,
        0,
        'Malawi',
        '2024-01-01',
        '',
        'Standard tax rate bracket',
        'true'
      ],
      [
        'Unlimited Upper Bound',
        'Top Rate',
        1500001,
        '',
        35,
        400000,
        'Malawi',
        '2024-01-01',
        '',
        'Highest tax bracket with no upper limit',
        'true'
      ],
      [
        'Temporary Bracket',
        'Special Rate',
        750000,
        1000000,
        28,
        162500,
        'Malawi',
        '2024-01-01',
        '2024-12-31',
        'Temporary special rate for 2024',
        'true'
      ],
      [
        'Inactive Bracket',
        'Old Rate',
        200000,
        600000,
        20,
        20000,
        'Malawi',
        '2023-01-01',
        '2023-12-31',
        'Previous year tax bracket',
        'false'
      ]
    ]

    const exampleData = [exampleHeaders, ...examples]
    const exampleSheet = XLSX.utils.aoa_to_sheet(exampleData)
    exampleSheet['!cols'] = [
      { width: 18 }, // Scenario
      { width: 20 }, // Tax Bracket Name
      { width: 15 }, // Minimum Income
      { width: 15 }, // Maximum Income
      { width: 12 }, // Tax Rate (%)
      { width: 12 }, // Fixed Amount
      { width: 10 }, // Country
      { width: 12 }, // Effective Date
      { width: 12 }, // End Date
      { width: 25 }, // Description
      { width: 10 }  // Is Active
    ]
    XLSX.utils.book_append_sheet(workbook, exampleSheet, 'Examples')

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true
    })

    // Create response with proper headers
    const response = new NextResponse(excelBuffer)
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response.headers.set('Content-Disposition', 'attachment; filename="tax_brackets_import_template.xlsx"')
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')

    return response

  } catch (error: unknown) {
    console.error('Error generating tax bracket template:', error)
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    )
  }
}

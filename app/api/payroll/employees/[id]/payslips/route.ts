// app/api/payroll/employees/[id]/payslips/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payslipGenerationService } from '@/lib/services/payroll/payslip-generation-service';
import { Employee } from '@/models/Employee';

/**
 * GET /api/payroll/employees/[id]/payslips
 * Get payslips for an employee
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare idId at function scope
  let idId: string = '';

  try {
    // Resolve the params promise
    const { id } = await params;
    idId = id;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(idId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const year = searchParams.get('year') ? parseInt(searchParams.get('year') || '') : undefined;
    const month = searchParams.get('month') ? parseInt(searchParams.get('month') || '') : undefined;
    const sortBy = searchParams.get('sortBy') || 'payPeriod.year payPeriod.month';
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

    // Get payslips
    const result = await payslipGenerationService.getPayslipsForEmployee(idId, {
      page,
      limit,
      year,
      month,
      sortBy,
      sortOrder
    });

    return NextResponse.json({
      success: true,
      data: {
        employee: {
          id: employee._id,
          name: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber
        },
        ...result
      }
    });
  } catch (error: unknown) {
    logger.error(`Error getting payslips for employee ${idId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get payslips', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

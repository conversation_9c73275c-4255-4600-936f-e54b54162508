// app/api/payroll/salary-revisions/bulk-update/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import SalaryRevision from '@/models/payroll/SalaryRevision'
import EmployeeSalary from '@/models/payroll/EmployeeSalary'
import { Employee } from '@/models/Employee'
import Department from '@/models/Department'
import mongoose from 'mongoose'

export const runtime = 'nodejs'

// Required roles for bulk salary revision updates
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_DIRECTOR,
  UserRole.PAYROLL_MANAGER
]

interface BulkUpdateRequest {
  updateType: 'percentage' | 'fixed_amount' | 'department_wide' | 'role_based'
  filters: {
    departments?: string[]
    roles?: string[]
    employeeIds?: string[]
    salaryRange?: {
      min: number
      max: number
    }
  }
  changes: {
    percentageIncrease?: number
    fixedAmount?: number
    newBasicSalary?: number
  }
  effectiveDate: string
  reason: string
  notes?: string
  revisionType: 'increment' | 'promotion' | 'adjustment' | 'demotion' | 'annual_review' | 'other'
  autoApprove?: boolean
}

interface BulkUpdateResult {
  totalEmployees: number
  successCount: number
  errorCount: number
  skippedCount: number
  errors: Array<{
    employeeId: string
    employeeName: string
    error: string
  }>
  skipped: Array<{
    employeeId: string
    employeeName: string
    reason: string
  }>
  results: Array<{
    employeeId: string
    employeeName: string
    department: string
    previousSalary: number
    newSalary: number
    change: number
    changeType: 'percentage' | 'fixed'
    status: 'success' | 'error' | 'skipped'
    revisionId?: string
  }>
}

/**
 * POST /api/payroll/salary-revisions/bulk-update
 * Apply bulk salary updates to multiple employees
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Parse request body
    const body: BulkUpdateRequest = await request.json()
    const { updateType, filters, changes, effectiveDate, reason, notes, revisionType, autoApprove = false } = body

    // Validate request
    if (!updateType || !effectiveDate || !reason || !revisionType) {
      return NextResponse.json(
        { error: 'Missing required fields: updateType, effectiveDate, reason, revisionType' },
        { status: 400 }
      )
    }

    // Validate effective date
    const parsedEffectiveDate = new Date(effectiveDate)
    if (isNaN(parsedEffectiveDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid effective date format' },
        { status: 400 }
      )
    }

    // Validate changes based on update type
    if (updateType === 'percentage' && !changes.percentageIncrease) {
      return NextResponse.json(
        { error: 'Percentage increase is required for percentage update type' },
        { status: 400 }
      )
    }

    if (updateType === 'fixed_amount' && !changes.fixedAmount) {
      return NextResponse.json(
        { error: 'Fixed amount is required for fixed amount update type' },
        { status: 400 }
      )
    }

    logger.info('Starting bulk salary revision update', LogCategory.PAYROLL, {
      userId: user.id,
      updateType,
      filters,
      changes,
      effectiveDate: parsedEffectiveDate
    })

    // Build employee query based on filters
    const employeeQuery: any = {}

    if (filters.departments && filters.departments.length > 0) {
      employeeQuery.department = { $in: filters.departments.map(id => new mongoose.Types.ObjectId(id)) }
    }

    if (filters.roles && filters.roles.length > 0) {
      employeeQuery.role = { $in: filters.roles }
    }

    if (filters.employeeIds && filters.employeeIds.length > 0) {
      employeeQuery._id = { $in: filters.employeeIds.map(id => new mongoose.Types.ObjectId(id)) }
    }

    // Get eligible employees
    const employees = await Employee.find(employeeQuery)
      .populate('department', 'name')
      .lean()

    if (employees.length === 0) {
      return NextResponse.json(
        { error: 'No employees found matching the specified filters' },
        { status: 404 }
      )
    }

    // Initialize result tracking
    const result: BulkUpdateResult = {
      totalEmployees: employees.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      skipped: [],
      results: []
    }

    // Process each employee
    for (const employee of employees) {
      try {
        const employeeName = `${employee.firstName} ${employee.lastName}`

        // Get current active salary
        const currentSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: true
        })

        if (!currentSalary) {
          result.skippedCount++
          result.skipped.push({
            employeeId: employee._id.toString(),
            employeeName,
            reason: 'No active salary found'
          })
          continue
        }

        // Apply salary range filter if specified
        if (filters.salaryRange) {
          const { min, max } = filters.salaryRange
          if (currentSalary.basicSalary < min || currentSalary.basicSalary > max) {
            result.skippedCount++
            result.skipped.push({
              employeeId: employee._id.toString(),
              employeeName,
              reason: `Salary ${currentSalary.basicSalary} outside range ${min}-${max}`
            })
            continue
          }
        }

        // Check if revision already exists for this date
        const existingRevision = await SalaryRevision.findOne({
          employeeId: employee._id,
          effectiveDate: parsedEffectiveDate,
          approvalStatus: { $ne: 'rejected' }
        })

        if (existingRevision) {
          result.skippedCount++
          result.skipped.push({
            employeeId: employee._id.toString(),
            employeeName,
            reason: 'Salary revision already exists for this effective date'
          })
          continue
        }

        // Calculate new salary based on update type
        let newBasicSalary: number
        let changeAmount: number
        let changeType: 'percentage' | 'fixed'

        switch (updateType) {
          case 'percentage':
            changeAmount = (currentSalary.basicSalary * (changes.percentageIncrease! / 100))
            newBasicSalary = currentSalary.basicSalary + changeAmount
            changeType = 'percentage'
            break

          case 'fixed_amount':
            changeAmount = changes.fixedAmount!
            newBasicSalary = currentSalary.basicSalary + changeAmount
            changeType = 'fixed'
            break

          case 'department_wide':
            if (changes.percentageIncrease) {
              changeAmount = (currentSalary.basicSalary * (changes.percentageIncrease / 100))
              newBasicSalary = currentSalary.basicSalary + changeAmount
              changeType = 'percentage'
            } else if (changes.fixedAmount) {
              changeAmount = changes.fixedAmount
              newBasicSalary = currentSalary.basicSalary + changeAmount
              changeType = 'fixed'
            } else {
              throw new Error('Department-wide update requires either percentage or fixed amount')
            }
            break

          case 'role_based':
            if (changes.percentageIncrease) {
              changeAmount = (currentSalary.basicSalary * (changes.percentageIncrease / 100))
              newBasicSalary = currentSalary.basicSalary + changeAmount
              changeType = 'percentage'
            } else if (changes.fixedAmount) {
              changeAmount = changes.fixedAmount
              newBasicSalary = currentSalary.basicSalary + changeAmount
              changeType = 'fixed'
            } else {
              throw new Error('Role-based update requires either percentage or fixed amount')
            }
            break

          default:
            throw new Error(`Invalid update type: ${updateType}`)
        }

        // Validate new salary
        if (newBasicSalary <= 0) {
          result.errorCount++
          result.errors.push({
            employeeId: employee._id.toString(),
            employeeName,
            error: 'Calculated new salary is invalid (less than or equal to 0)'
          })
          continue
        }

        // Create new salary record
        const newSalary = new EmployeeSalary({
          ...currentSalary.toObject(),
          _id: undefined,
          basicSalary: newBasicSalary,
          effectiveDate: parsedEffectiveDate,
          isActive: false, // Will be activated when revision is approved
          createdBy: user.id,
          createdAt: undefined,
          updatedAt: undefined
        })

        await newSalary.save()

        // Calculate percentage change
        const percentageChange = ((changeAmount / currentSalary.basicSalary) * 100)

        // Create salary revision record
        const salaryRevision = new SalaryRevision({
          employeeId: employee._id,
          previousSalaryId: currentSalary._id,
          newSalaryId: newSalary._id,
          revisionType,
          effectiveDate: parsedEffectiveDate,
          previousBasicSalary: currentSalary.basicSalary,
          newBasicSalary,
          percentageChange,
          amountChange: changeAmount,
          currency: currentSalary.currency || 'MWK',
          reason,
          notes: notes || `Bulk ${updateType} update`,
          approvalStatus: autoApprove ? 'approved' : 'pending',
          approvedBy: autoApprove ? new mongoose.Types.ObjectId(user.id) : undefined,
          approvedAt: autoApprove ? new Date() : undefined,
          createdBy: new mongoose.Types.ObjectId(user.id)
        })

        await salaryRevision.save()

        // If auto-approved, activate the new salary
        if (autoApprove) {
          // Deactivate current salary
          currentSalary.isActive = false
          currentSalary.endDate = parsedEffectiveDate
          currentSalary.updatedBy = new mongoose.Types.ObjectId(user.id)
          await currentSalary.save()

          // Activate new salary
          newSalary.isActive = true
          await newSalary.save()
        }

        // Track successful update
        result.successCount++
        result.results.push({
          employeeId: employee._id.toString(),
          employeeName,
          department: employee.department?.name || 'Unknown',
          previousSalary: currentSalary.basicSalary,
          newSalary: newBasicSalary,
          change: Math.abs(changeAmount),
          changeType,
          status: 'success',
          revisionId: salaryRevision._id.toString()
        })

        logger.info('Bulk salary revision created', LogCategory.PAYROLL, {
          employeeId: employee._id.toString(),
          previousSalary: currentSalary.basicSalary,
          newSalary: newBasicSalary,
          changeAmount,
          revisionId: salaryRevision._id.toString(),
          userId: user.id
        })

      } catch (error) {
        result.errorCount++
        result.errors.push({
          employeeId: employee._id.toString(),
          employeeName: `${employee.firstName} ${employee.lastName}`,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        })

        logger.error('Error processing bulk salary revision', LogCategory.PAYROLL, {
          employeeId: employee._id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error',
          userId: user.id
        })
      }
    }

    // Log final results
    logger.info('Bulk salary revision update completed', LogCategory.PAYROLL, {
      totalEmployees: result.totalEmployees,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      updateType,
      userId: user.id
    })

    return NextResponse.json({
      success: true,
      message: `Bulk update completed: ${result.successCount} revisions created, ${result.errorCount} errors, ${result.skippedCount} skipped`,
      data: result
    })

  } catch (error) {
    logger.error('Error in bulk salary revision update', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to process bulk update', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

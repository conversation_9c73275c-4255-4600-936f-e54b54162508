// app/api/payroll/salary-revisions/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import SalaryRevision from '@/models/payroll/SalaryRevision'
import EmployeeSalary from '@/models/payroll/EmployeeSalary'
import { Employee } from '@/models/Employee'
import Department from '@/models/Department'
import * as XLSX from 'xlsx'
import mongoose from 'mongoose'

export const runtime = 'nodejs'

// Required roles for bulk salary revision import
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_DIRECTOR,
  UserRole.PAYROLL_MANAGER
]

// Expected columns in the import file
const REQUIRED_COLUMNS = [
  'employeeId',
  'revisionType',
  'newBasicSalary',
  'effectiveDate',
  'reason'
]

const OPTIONAL_COLUMNS = [
  'employeeEmail',
  'employeeName',
  'department',
  'percentageIncrease',
  'fixedIncrease',
  'notes',
  'currency'
]

interface SalaryRevisionImportRow {
  employeeId?: string
  employeeEmail?: string
  employeeName?: string
  department?: string
  revisionType: 'increment' | 'promotion' | 'adjustment' | 'demotion' | 'annual_review' | 'other'
  newBasicSalary: number
  percentageIncrease?: number
  fixedIncrease?: number
  effectiveDate: string
  reason: string
  notes?: string
  currency?: string
}

interface BulkImportResult {
  totalRows: number
  successCount: number
  errorCount: number
  skippedCount: number
  errors: Array<{
    row: number
    error: string
    employeeInfo?: string
  }>
  skipped: Array<{
    row: number
    reason: string
    employeeInfo?: string
  }>
  results: Array<{
    row: number
    employeeId: string
    employeeName: string
    previousSalary: number
    newSalary: number
    change: number
    changeType: 'percentage' | 'fixed'
    status: 'success' | 'error' | 'skipped'
    message?: string
  }>
}

/**
 * POST /api/payroll/salary-revisions/bulk-import
 * Import salary revisions in bulk from Excel file
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      return NextResponse.json({ error: 'Only Excel files (.xlsx, .xls) are supported' }, { status: 400 })
    }

    logger.info('Starting salary revision bulk import', LogCategory.IMPORT, {
      userId: user.id,
      fileName: file.name,
      fileSize: file.size
    })

    // Read and parse Excel file
    const buffer = Buffer.from(await file.arrayBuffer())
    const workbook = XLSX.read(buffer, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false,
      blankrows: false
    }) as Record<string, any>[]

    if (rows.length === 0) {
      return NextResponse.json({ error: 'File is empty or has no data' }, { status: 400 })
    }

    // Initialize result tracking
    const result: BulkImportResult = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      skipped: [],
      results: []
    }

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      const rowNumber = i + 1

      try {
        // Check if row has meaningful data
        const hasData = Object.values(row).some(value => 
          value !== null && value !== undefined && value !== ''
        )

        if (!hasData) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: 'Empty row - no data found'
          })
          continue
        }

        // Validate required fields
        const missingFields = REQUIRED_COLUMNS.filter(field => 
          !row[field] || row[field].toString().trim() === ''
        )

        if (missingFields.length > 0) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: `Missing required fields: ${missingFields.join(', ')}`,
            employeeInfo: row.employeeName || row.employeeEmail || row.employeeId || 'Unknown'
          })
          continue
        }

        // Find employee
        let employee
        if (row.employeeId) {
          employee = await Employee.findById(row.employeeId)
        } else if (row.employeeEmail) {
          employee = await Employee.findOne({ email: row.employeeEmail.trim().toLowerCase() })
        }

        if (!employee) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Employee not found',
            employeeInfo: row.employeeName || row.employeeEmail || row.employeeId || 'Unknown'
          })
          continue
        }

        // Get current active salary
        const currentSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: true
        })

        if (!currentSalary) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'No active salary found for employee',
            employeeInfo: `${employee.firstName} ${employee.lastName} (${employee.email})`
          })
          continue
        }

        // Parse and validate salary data
        const newBasicSalary = parseFloat(row.newBasicSalary)
        if (isNaN(newBasicSalary) || newBasicSalary <= 0) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Invalid new basic salary amount',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Parse effective date
        const effectiveDate = new Date(row.effectiveDate)
        if (isNaN(effectiveDate.getTime())) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Invalid effective date format',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Check if revision already exists for this date
        const existingRevision = await SalaryRevision.findOne({
          employeeId: employee._id,
          effectiveDate: effectiveDate,
          approvalStatus: { $ne: 'rejected' }
        })

        if (existingRevision) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: 'Salary revision already exists for this effective date',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Calculate changes
        const previousSalary = currentSalary.basicSalary
        const amountChange = newBasicSalary - previousSalary
        const percentageChange = ((amountChange / previousSalary) * 100)

        // Validate revision type
        const validRevisionTypes = ['increment', 'promotion', 'adjustment', 'demotion', 'annual_review', 'other']
        if (!validRevisionTypes.includes(row.revisionType)) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: `Invalid revision type. Must be one of: ${validRevisionTypes.join(', ')}`,
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Create new salary record
        const newSalary = new EmployeeSalary({
          ...currentSalary.toObject(),
          _id: undefined,
          basicSalary: newBasicSalary,
          effectiveDate: effectiveDate,
          isActive: false, // Will be activated when revision is approved
          createdBy: user.id,
          createdAt: undefined,
          updatedAt: undefined
        })

        await newSalary.save()

        // Create salary revision record
        const salaryRevision = new SalaryRevision({
          employeeId: employee._id,
          previousSalaryId: currentSalary._id,
          newSalaryId: newSalary._id,
          revisionType: row.revisionType,
          effectiveDate: effectiveDate,
          previousBasicSalary: previousSalary,
          newBasicSalary: newBasicSalary,
          percentageChange: percentageChange,
          amountChange: amountChange,
          currency: row.currency || currentSalary.currency || 'MWK',
          reason: row.reason.trim(),
          notes: row.notes?.trim() || undefined,
          approvalStatus: 'pending',
          createdBy: user.id
        })

        await salaryRevision.save()

        // Track successful import
        result.successCount++
        result.results.push({
          row: rowNumber,
          employeeId: employee._id.toString(),
          employeeName: `${employee.firstName} ${employee.lastName}`,
          previousSalary: previousSalary,
          newSalary: newBasicSalary,
          change: Math.abs(amountChange),
          changeType: row.percentageIncrease ? 'percentage' : 'fixed',
          status: 'success',
          message: 'Salary revision created successfully (pending approval)'
        })

        logger.info('Salary revision created successfully', LogCategory.IMPORT, {
          rowIndex: rowNumber,
          employeeId: employee._id.toString(),
          previousSalary,
          newSalary: newBasicSalary,
          userId: user.id
        })

      } catch (error) {
        result.errorCount++
        result.errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          employeeInfo: row.employeeName || row.employeeEmail || 'Unknown'
        })

        logger.error('Error processing salary revision row', LogCategory.IMPORT, {
          rowIndex: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error',
          userId: user.id
        })
      }
    }

    // Log final results
    logger.info('Salary revision bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    })

    return NextResponse.json({
      success: true,
      message: `Bulk import completed: ${result.successCount} created, ${result.errorCount} errors, ${result.skippedCount} skipped`,
      data: result
    })

  } catch (error) {
    logger.error('Error in salary revision bulk import', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to process bulk import', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// app/api/payroll/employee-salaries/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import EmployeeSalary from '@/models/payroll/EmployeeSalary'
import { connectToDatabase } from '@/lib/backend/database'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

export const runtime = 'nodejs';

// Define roles that can bulk delete employee salaries
const EMPLOYEE_SALARY_DELETE_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER
]

/**
 * POST handler for bulk deleting employee salaries
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, EMPLOYEE_SALARY_DELETE_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Get request body
    const body = await request.json()
    const { ids } = body

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No employee salary IDs provided' },
        { status: 400 }
      )
    }

    // Validate that all IDs are valid MongoDB ObjectIds
    const validIds = ids.filter(id => {
      return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/)
    })

    if (validIds.length !== ids.length) {
      return NextResponse.json(
        { error: 'Invalid employee salary IDs provided' },
        { status: 400 }
      )
    }

    logger.info('Starting bulk delete of employee salaries', LogCategory.API, {
      userId: user.id,
      salaryIds: validIds,
      count: validIds.length
    })

    // Check if any of the salaries exist and get their details for logging
    const existingSalaries = await EmployeeSalary.find({
      _id: { $in: validIds }
    })
    .populate('employeeId', 'firstName lastName employeeNumber')
    .lean()

    if (existingSalaries.length === 0) {
      return NextResponse.json(
        { error: 'No employee salaries found with the provided IDs' },
        { status: 404 }
      )
    }

    // Check if any of these salaries are currently active and warn about potential impact
    const activeSalaries = existingSalaries.filter(salary => salary.isActive)
    
    if (activeSalaries.length > 0) {
      logger.warn('Deleting active employee salaries', LogCategory.API, {
        userId: user.id,
        activeSalariesCount: activeSalaries.length,
        activeSalaryIds: activeSalaries.map(s => s._id),
        affectedEmployees: activeSalaries.map(s => ({
          id: (s.employeeId as any)?._id,
          name: `${(s.employeeId as any)?.firstName} ${(s.employeeId as any)?.lastName}`,
          employeeNumber: (s.employeeId as any)?.employeeNumber
        }))
      })
    }

    // Perform the bulk delete
    const deleteResult = await EmployeeSalary.deleteMany({
      _id: { $in: validIds }
    })

    // Log the successful deletion
    logger.info('Successfully bulk deleted employee salaries', LogCategory.API, {
      userId: user.id,
      requestedCount: validIds.length,
      deletedCount: deleteResult.deletedCount,
      activeSalariesDeleted: activeSalaries.length,
      deletedSalaries: existingSalaries.map(salary => ({
        id: salary._id,
        employeeName: `${(salary.employeeId as any)?.firstName} ${(salary.employeeId as any)?.lastName}`,
        employeeNumber: (salary.employeeId as any)?.employeeNumber,
        basicSalary: salary.basicSalary,
        isActive: salary.isActive,
        effectiveDate: salary.effectiveDate
      }))
    })

    // Prepare response data
    const responseData = {
      success: true,
      deletedCount: deleteResult.deletedCount,
      requestedCount: validIds.length,
      activeSalariesDeleted: activeSalaries.length,
      deletedSalaries: existingSalaries.map(salary => ({
        id: salary._id,
        employeeName: `${(salary.employeeId as any)?.firstName} ${(salary.employeeId as any)?.lastName}`,
        employeeNumber: (salary.employeeId as any)?.employeeNumber,
        basicSalary: salary.basicSalary,
        currency: salary.currency,
        isActive: salary.isActive,
        effectiveDate: salary.effectiveDate
      }))
    }

    return NextResponse.json(responseData)

  } catch (error: unknown) {
    console.error('Error in employee salary bulk delete:', error)
    
    logger.error('Error in employee salary bulk delete', LogCategory.API, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred during bulk delete',
        success: false
      }, 
      { status: 500 }
    )
  }
}

// app/api/payroll/employee-salaries/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import SalaryRevision from '@/models/payroll/SalaryRevision';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/payroll/employee-salaries/[id]
 * Get an employee salary by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare salaryId at function scope
  let salaryId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;
    salaryId = id;

    // Get employee salary
    const employeeSalary = await EmployeeSalary.findById(id)
      .populate('employeeId', 'firstName lastName employeeNumber')
      .populate('salaryStructureId', 'name')
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    if (!employeeSalary) {
      return NextResponse.json(
        { error: 'Employee salary not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: employeeSalary
    });
  } catch (error) {
    logger.error(`Error getting employee salary ${salaryId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get employee salary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payroll/employee-salaries/[id]
 * Update an employee salary
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare salaryId at function scope
  let salaryId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;
    salaryId = id;

    // Get employee salary
    const employeeSalary = await EmployeeSalary.findById(id);

    if (!employeeSalary) {
      return NextResponse.json(
        { error: 'Employee salary not found' },
        { status: 404 }
      );
    }

    // Check if this is a salary revision
    const isSalaryRevision = body.basicSalary && body.basicSalary !== employeeSalary.basicSalary;

    // If this is a salary revision, create a new salary record instead of updating
    if (isSalaryRevision && body.revisionType && body.effectiveDate) {
      // Create a new salary record
      const newSalary = new EmployeeSalary({
        ...employeeSalary.toObject(),
        _id: undefined, // Remove _id to create a new record
        basicSalary: body.basicSalary,
        effectiveDate: new Date(body.effectiveDate),
        isActive: true,
        createdBy: user.id,
        createdAt: undefined,
        updatedAt: undefined
      });

      // Apply other updates from the body
      Object.keys(body).forEach(key => {
        if (key !== 'revisionType' && key !== 'reason') {
          newSalary[key] = body[key];
        }
      });

      await newSalary.save();

      // Deactivate the old salary
      employeeSalary.isActive = false;
      employeeSalary.endDate = new Date(body.effectiveDate);
      employeeSalary.updatedBy = new mongoose.Types.ObjectId(user.id);
      await employeeSalary.save();

      // Create salary revision record
      const percentageChange = ((body.basicSalary - employeeSalary.basicSalary) / employeeSalary.basicSalary) * 100;
      const amountChange = body.basicSalary - employeeSalary.basicSalary;

      const salaryRevision = new SalaryRevision({
        employeeId: employeeSalary.employeeId,
        previousSalaryId: employeeSalary._id,
        newSalaryId: newSalary._id,
        revisionType: body.revisionType,
        effectiveDate: new Date(body.effectiveDate),
        previousBasicSalary: employeeSalary.basicSalary,
        newBasicSalary: body.basicSalary,
        percentageChange,
        amountChange,
        currency: employeeSalary.currency,
        reason: body.reason || 'Salary revision',
        approvalStatus: 'approved', // Auto-approve since it's created by an authorized user
        approvedBy: user.id,
        approvedAt: new Date(),
        createdBy: user.id
      });

      await salaryRevision.save();

      return NextResponse.json({
        success: true,
        message: 'Salary revision created successfully',
        data: {
          newSalary,
          salaryRevision
        }
      });
    } else {
      // Regular update (not a salary revision)
      // If activating this salary, deactivate other active salaries for this employee
      if (body.isActive === true && !employeeSalary.isActive) {
        await EmployeeSalary.updateMany(
          {
            employeeId: employeeSalary.employeeId,
            _id: { $ne: employeeSalary._id },
            isActive: true
          },
          {
            isActive: false,
            endDate: new Date(),
            updatedBy: new mongoose.Types.ObjectId(user.id)
          }
        );
      }

      // Update employee salary
      Object.assign(employeeSalary, {
        ...body,
        updatedBy: user.id
      });

      await employeeSalary.save();

      return NextResponse.json({
        success: true,
        message: 'Employee salary updated successfully',
        data: employeeSalary
      });
    }
  } catch (error) {
    logger.error(`Error updating employee salary ${salaryId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update employee salary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payroll/employee-salaries/[id]
 * Delete an employee salary
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare salaryId at function scope
  let salaryId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;
    salaryId = id;

    // Check if employee salary exists
    const employeeSalary = await EmployeeSalary.findById(id);
    if (!employeeSalary) {
      return NextResponse.json(
        { error: 'Employee salary not found' },
        { status: 404 }
      );
    }

    // Delete employee salary
    await EmployeeSalary.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Employee salary deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting employee salary ${salaryId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete employee salary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
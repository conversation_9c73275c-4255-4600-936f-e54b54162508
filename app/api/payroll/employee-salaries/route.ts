import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import { Employee } from '@/models/Employee';
import SalaryStructure from '@/models/payroll/SalaryStructure';
import Allowance from '@/models/payroll/Allowance';
import Deduction from '@/models/payroll/Deduction';

export const runtime = 'nodejs';



/**
 * GET /api/payroll/employee-salaries
 * Get employee salaries
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const employeeId = searchParams.get('employeeId') || undefined;
    const isActive = searchParams.get('isActive') === 'true';
    const sortBy = searchParams.get('sortBy') || 'effectiveDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Connect to database
    await connectToDatabase();

    // Build query
    // Using a more specific type for MongoDB queries
    interface EmployeeSalaryQuery {
      employeeId?: string;
      isActive?: boolean;
    }

    const query: EmployeeSalaryQuery = {};
    if (employeeId) {
      query.employeeId = employeeId;
    }
    if (isActive) {
      query.isActive = true;
    }

    // Count total documents
    const totalDocs = await EmployeeSalary.countDocuments(query);

    // If no documents found, return empty array with success status
    if (totalDocs === 0) {
      return NextResponse.json({
        success: true,
        data: {
          docs: [],
          totalDocs: 0,
          page,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      });
    }

    // Get employee salaries
    const employeeSalaries = await EmployeeSalary.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName employeeNumber')
      .populate('salaryStructureId', 'name')
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        docs: employeeSalaries,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting employee salaries', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get employee salaries', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payroll/employee-salaries
 * Create a new employee salary
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.employeeId || !body.salaryStructureId || !body.basicSalary || !body.effectiveDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    try {
      const employee = await Employee.findById(body.employeeId);
      if (!employee) {
        return NextResponse.json(
          { error: 'Employee not found' },
          { status: 404 }
        );
      }
    } catch (error: unknown) {
      logger.error('Error finding employee', LogCategory.API, error);
      return NextResponse.json(
        { error: 'Failed to find employee', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // Check if salary structure exists
    const salaryStructure = await SalaryStructure.findById(body.salaryStructureId);
    if (!salaryStructure) {
      return NextResponse.json(
        { error: 'Salary structure not found' },
        { status: 404 }
      );
    }

    // Process allowances
    if (body.allowances && Array.isArray(body.allowances)) {
      for (let i = 0; i < body.allowances.length; i++) {
        const allowance = body.allowances[i];
        if (allowance.allowanceId) {
          // Check if allowance exists
          const allowanceDoc = await Allowance.findById(allowance.allowanceId);
          if (!allowanceDoc) {
            return NextResponse.json(
              { error: `Allowance with ID ${allowance.allowanceId} not found` },
              { status: 404 }
            );
          }
          // Set name from allowance document if not provided
          if (!allowance.name) {
            allowance.name = allowanceDoc.name;
          }
          // Set taxable and pensionable flags from allowance document if not provided
          if (allowance.isTaxable === undefined) {
            allowance.isTaxable = allowanceDoc.isTaxable;
          }
          if (allowance.isPensionable === undefined) {
            allowance.isPensionable = allowanceDoc.isPensionable;
          }
        }
      }
    }

    // Process deductions
    if (body.deductions && Array.isArray(body.deductions)) {
      for (let i = 0; i < body.deductions.length; i++) {
        const deduction = body.deductions[i];
        if (deduction.deductionId) {
          // Check if deduction exists
          const deductionDoc = await Deduction.findById(deduction.deductionId);
          if (!deductionDoc) {
            return NextResponse.json(
              { error: `Deduction with ID ${deduction.deductionId} not found` },
              { status: 404 }
            );
          }
          // Set name from deduction document if not provided
          if (!deduction.name) {
            deduction.name = deductionDoc.name;
          }
          // Set statutory flag from deduction document if not provided
          if (deduction.isStatutory === undefined) {
            deduction.isStatutory = deductionDoc.isStatutory;
          }
        }
      }
    }

    // If this is an active salary, deactivate other active salaries for this employee
    if (body.isActive !== false) {
      await EmployeeSalary.updateMany(
        { employeeId: body.employeeId, isActive: true },
        {
          isActive: false,
          endDate: new Date(body.effectiveDate),
          updatedBy: user.id
        }
      );
    }

    // Create employee salary
    const employeeSalary = new EmployeeSalary({
      ...body,
      isActive: body.isActive !== false, // Default to true if not explicitly set to false
      createdBy: user.id
    });

    await employeeSalary.save();

    return NextResponse.json({
      success: true,
      message: 'Employee salary created successfully',
      data: employeeSalary
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating employee salary', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create employee salary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

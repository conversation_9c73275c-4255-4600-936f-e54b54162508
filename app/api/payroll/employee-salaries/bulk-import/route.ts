// app/api/payroll/employee-salaries/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import EmployeeSalary from '@/models/payroll/EmployeeSalary'
import { Employee } from '@/models/Employee'
import SalaryStructure from '@/models/payroll/SalaryStructure'
import Allowance from '@/models/payroll/Allowance'
import Deduction from '@/models/payroll/Deduction'
import { connectToDatabase } from '@/lib/backend/database'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import mongoose from 'mongoose'

export const runtime = 'nodejs';

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'employeeIdentifier', // email, employeeId, or employeeNumber
  'basicSalary',
  'effectiveDate'
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Employee Email': 'employeeEmail',
  'Employee ID': 'employeeId',
  'Employee Number': 'employeeNumber',
  'Employee Name': 'employeeName', // For reference only
  'Position': 'position', // For reference only
  'Department': 'department', // For reference only
  'Current Salary': 'currentSalary', // For reference only
  'Has Existing Salary Record': 'hasExistingSalaryRecord', // For validation
  'Employee Identifier': 'employeeIdentifier', // Flexible field for any employee identifier
  'Basic Salary': 'basicSalary',
  'Currency': 'currency',
  'Salary Structure': 'salaryStructureName',
  'Effective Date': 'effectiveDate',
  'End Date': 'endDate',
  'Bank Name': 'bankName',
  'Bank Account Number': 'bankAccountNumber',
  'Bank Branch Code': 'bankBranchCode',
  'Payment Method': 'paymentMethod',
  'Tax ID': 'taxId',
  'Pension Scheme': 'pensionScheme',
  'Pension Number': 'pensionNumber',
  'Notes': 'notes',
  'Is Active': 'isActive',
  // Allowances (up to 5 for template)
  'Allowance 1 Name': 'allowance1Name',
  'Allowance 1 Amount': 'allowance1Amount',
  'Allowance 1 Percentage': 'allowance1Percentage',
  'Allowance 1 Is Taxable': 'allowance1IsTaxable',
  'Allowance 2 Name': 'allowance2Name',
  'Allowance 2 Amount': 'allowance2Amount',
  'Allowance 2 Percentage': 'allowance2Percentage',
  'Allowance 2 Is Taxable': 'allowance2IsTaxable',
  'Allowance 3 Name': 'allowance3Name',
  'Allowance 3 Amount': 'allowance3Amount',
  'Allowance 3 Percentage': 'allowance3Percentage',
  'Allowance 3 Is Taxable': 'allowance3IsTaxable',
  // Deductions (up to 5 for template)
  'Deduction 1 Name': 'deduction1Name',
  'Deduction 1 Amount': 'deduction1Amount',
  'Deduction 1 Percentage': 'deduction1Percentage',
  'Deduction 2 Name': 'deduction2Name',
  'Deduction 2 Amount': 'deduction2Amount',
  'Deduction 2 Percentage': 'deduction2Percentage',
  'Deduction 3 Name': 'deduction3Name',
  'Deduction 3 Amount': 'deduction3Amount',
  'Deduction 3 Percentage': 'deduction3Percentage'
}

// Define roles that can manage employee salaries
const EMPLOYEE_SALARY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER
]

/**
 * POST handler for bulk importing employee salaries
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, EMPLOYEE_SALARY_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 })
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing employee salary bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[]

    // Validate and process data
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [] as Array<{ row: number, error: string }>,
      warnings: [] as Array<{ row: number, warning: string }>,
      skipped: [] as Array<{
        row: number,
        reason: string,
        employeeName?: string,
        employeeEmail?: string,
        employeeId?: string,
        existingSalary?: number
      }>,
      imported: [] as Array<{
        row: number,
        employeeName: string,
        employeeEmail: string,
        basicSalary: number
      }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for employee salary import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json({ error: 'File is empty' }, { status: 400 })
    }

    // Log the first row for debugging
    logger.debug('First row of import file', LogCategory.IMPORT, {
      firstRow: rows[0],
      keys: Object.keys(rows[0])
    })

    // Create a mapping between the columns in the file and our expected fields
    const availableColumns = Object.keys(rows[0])
    const columnMap = new Map<string, string>()

    // First, try to map using the display name mapping
    Object.keys(rows[0]).forEach(key => {
      // Check if this column name is in our display mapping
      if (key in COLUMN_DISPLAY_MAPPING) {
        const mappedField = COLUMN_DISPLAY_MAPPING[key]
        columnMap.set(mappedField, key)
      }
    })

    logger.debug('Column mapping', LogCategory.IMPORT, {
      columnMap: Object.fromEntries(columnMap),
      availableColumns
    })

    // Cache for lookups to improve performance
    const employeeCache = new Map<string, any>()
    const salaryStructureCache = new Map<string, any>()
    const allowanceCache = new Map<string, any>()
    const deductionCache = new Map<string, any>()

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      // Map columns from the file to our expected fields using the Map
      for (const [field, column] of columnMap.entries()) {
        normalizedRow[field] = row[column]
      }

      // Direct mapping from display names to expected field names for any unmapped fields
      Object.keys(row).forEach(key => {
        // If this is a display name in our mapping and we haven't already mapped it
        if (key in COLUMN_DISPLAY_MAPPING && !(COLUMN_DISPLAY_MAPPING[key] in normalizedRow)) {
          const mappedField = COLUMN_DISPLAY_MAPPING[key]
          normalizedRow[mappedField] = row[key]

          // Trim string values
          if (typeof normalizedRow[mappedField] === 'string') {
            normalizedRow[mappedField] = normalizedRow[mappedField].trim()
          }
        }
      })

      try {
        // Log the row being processed for debugging
        logger.debug('Processing employee salary row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow
        })

        // Enhanced employee validation with email priority
        const employeeEmail = normalizedRow.employeeEmail?.trim()
        const employeeId = normalizedRow.employeeId?.trim()
        const employeeNumber = normalizedRow.employeeNumber?.trim()

        // Skip rows with no employee data or placeholder text
        if (!employeeEmail && !employeeId && !employeeNumber) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: 'No employee identifier provided',
            employeeName: normalizedRow.employeeName || 'Unknown'
          })
          continue
        }

        // Skip placeholder/example rows
        if (employeeEmail?.includes('example.com') ||
            employeeEmail?.includes('sample') ||
            normalizedRow.employeeName?.toLowerCase().includes('no active employees')) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: 'Placeholder/example data detected',
            employeeName: normalizedRow.employeeName || employeeEmail || 'Unknown'
          })
          continue
        }

        // Primary validation: Use email as the main identifier
        let employeeIdentifier = employeeEmail || employeeId || employeeNumber

        if (!normalizedRow.basicSalary) {
          throw new Error('Basic salary is required')
        }

        if (!normalizedRow.effectiveDate) {
          throw new Error('Effective date is required')
        }

        // Find employee with enhanced validation
        let employee = employeeCache.get(employeeIdentifier)
        if (!employee) {
          // Prioritize email lookup, then fallback to other identifiers
          const searchCriteria = []
          if (employeeEmail) searchCriteria.push({ email: employeeEmail })
          if (employeeId) searchCriteria.push({ employeeId: employeeId })
          if (employeeNumber) searchCriteria.push({ employeeNumber: employeeNumber })

          employee = await Employee.findOne({
            $or: searchCriteria,
            employmentStatus: 'active', // Only active employees
            isBlocked: { $ne: true } // Exclude blocked employees
          })

          if (!employee) {
            // Filter out non-existent employees (don't throw error, just skip)
            result.skippedCount++
            result.skipped.push({
              row: i + 1,
              reason: `Employee not found in database`,
              employeeName: normalizedRow.employeeName || employeeIdentifier,
              employeeEmail: employeeEmail,
              employeeId: employeeId
            })
            continue
          }

          employeeCache.set(employeeIdentifier, employee)
        }

        // Check if employee already has an active salary record
        const existingActiveSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: true
        })

        // Skip employees who already have salary records (unless explicitly overriding)
        if (existingActiveSalary && normalizedRow.isActive !== 'false') {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: 'Employee already has an active salary record',
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeEmail: employee.email,
            employeeId: employee.employeeId,
            existingSalary: existingActiveSalary.basicSalary
          })
          continue
        }

        // Validate basic salary
        const basicSalary = Number(normalizedRow.basicSalary)
        if (isNaN(basicSalary) || basicSalary <= 0) {
          throw new Error('Basic salary must be a positive number')
        }

        // Validate and parse effective date
        const effectiveDate = new Date(normalizedRow.effectiveDate)
        if (isNaN(effectiveDate.getTime())) {
          throw new Error('Invalid effective date format')
        }

        // Parse end date if provided
        let endDate: Date | undefined
        if (normalizedRow.endDate) {
          endDate = new Date(normalizedRow.endDate)
          if (isNaN(endDate.getTime())) {
            throw new Error('Invalid end date format')
          }
        }

        // Find salary structure if provided
        let salaryStructureId: mongoose.Types.ObjectId | undefined
        if (normalizedRow.salaryStructureName) {
          let salaryStructure = salaryStructureCache.get(normalizedRow.salaryStructureName)
          if (!salaryStructure) {
            salaryStructure = await SalaryStructure.findOne({
              name: normalizedRow.salaryStructureName,
              isActive: true
            })

            if (!salaryStructure) {
              result.warnings.push({
                row: i + 1,
                warning: `Salary structure '${normalizedRow.salaryStructureName}' not found, proceeding without it`
              })
            } else {
              salaryStructureCache.set(normalizedRow.salaryStructureName, salaryStructure)
              salaryStructureId = salaryStructure._id
            }
          } else {
            salaryStructureId = salaryStructure._id
          }
        }

        // If no salary structure provided, try to find a default one
        if (!salaryStructureId) {
          const defaultStructure = await SalaryStructure.findOne({
            isActive: true,
            $or: [
              { name: /default/i },
              { name: /standard/i }
            ]
          })

          if (defaultStructure) {
            salaryStructureId = defaultStructure._id
            result.warnings.push({
              row: i + 1,
              warning: `Using default salary structure: ${defaultStructure.name}`
            })
          }
        }

        // If still no salary structure, this is required
        if (!salaryStructureId) {
          throw new Error('Salary structure is required but none found')
        }

        // Process allowances
        const allowances: any[] = []
        for (let j = 1; j <= 3; j++) {
          const allowanceName = normalizedRow[`allowance${j}Name`]?.trim()
          if (allowanceName) {
            const allowanceAmount = normalizedRow[`allowance${j}Amount`] ? Number(normalizedRow[`allowance${j}Amount`]) : undefined
            const allowancePercentage = normalizedRow[`allowance${j}Percentage`] ? Number(normalizedRow[`allowance${j}Percentage`]) : undefined
            const allowanceIsTaxable = normalizedRow[`allowance${j}IsTaxable`] === 'true' || normalizedRow[`allowance${j}IsTaxable`] === 'yes' || normalizedRow[`allowance${j}IsTaxable`] === '1'

            // Validate that either amount or percentage is provided
            if (!allowanceAmount && !allowancePercentage) {
              throw new Error(`Allowance ${j} "${allowanceName}" must have either amount or percentage. If you don't want to add this allowance, leave the name field blank.`)
            }

            // Try to find the allowance in the system
            let allowanceDoc = allowanceCache.get(allowanceName)
            if (!allowanceDoc) {
              allowanceDoc = await Allowance.findOne({ name: allowanceName, isActive: true })
              if (allowanceDoc) {
                allowanceCache.set(allowanceName, allowanceDoc)
              }
            }

            allowances.push({
              name: allowanceName,
              allowanceId: allowanceDoc?._id,
              amount: allowanceAmount,
              percentage: allowancePercentage,
              isTaxable: allowanceIsTaxable,
              isPensionable: allowanceDoc?.isPensionable || false
            })
          }
        }

        // Process deductions
        const deductions: any[] = []
        for (let j = 1; j <= 3; j++) {
          const deductionName = normalizedRow[`deduction${j}Name`]?.trim()
          if (deductionName) {
            const deductionAmount = normalizedRow[`deduction${j}Amount`] ? Number(normalizedRow[`deduction${j}Amount`]) : undefined
            const deductionPercentage = normalizedRow[`deduction${j}Percentage`] ? Number(normalizedRow[`deduction${j}Percentage`]) : undefined

            // Validate that either amount or percentage is provided
            if (!deductionAmount && !deductionPercentage) {
              throw new Error(`Deduction ${j} "${deductionName}" must have either amount or percentage. If you don't want to add this deduction, leave the name field blank.`)
            }

            // Try to find the deduction in the system
            let deductionDoc = deductionCache.get(deductionName)
            if (!deductionDoc) {
              deductionDoc = await Deduction.findOne({ name: deductionName, isActive: true })
              if (deductionDoc) {
                deductionCache.set(deductionName, deductionDoc)
              }
            }

            deductions.push({
              name: deductionName,
              deductionId: deductionDoc?._id,
              amount: deductionAmount,
              percentage: deductionPercentage,
              isStatutory: deductionDoc?.isStatutory || false
            })
          }
        }

        // Validate payment method
        const paymentMethod = normalizedRow.paymentMethod || 'bank_transfer'
        const validPaymentMethods = ['bank_transfer', 'cash', 'check', 'mobile_money']
        if (!validPaymentMethods.includes(paymentMethod)) {
          throw new Error(`Invalid payment method: ${paymentMethod}. Must be one of: ${validPaymentMethods.join(', ')}`)
        }

        const isActive = normalizedRow.isActive !== 'false' && normalizedRow.isActive !== 'no' && normalizedRow.isActive !== '0'

        // If this is an active salary and employee has existing active salary, deactivate the old one
        // (This should rarely happen since we skip employees with existing salaries above)
        if (isActive) {
          const existingActiveSalary = await EmployeeSalary.findOne({
            employeeId: employee._id,
            isActive: true
          })

          if (existingActiveSalary) {
            await EmployeeSalary.updateOne(
              { _id: existingActiveSalary._id },
              {
                isActive: false,
                endDate: effectiveDate,
                updatedBy: user.id
              }
            )
          }
        }

        // Create employee salary data
        const employeeSalaryData = {
          employeeId: employee._id,
          salaryStructureId,
          basicSalary,
          currency: normalizedRow.currency || 'MWK',
          effectiveDate,
          endDate,
          isActive,
          allowances,
          deductions,
          bankName: normalizedRow.bankName || undefined,
          bankAccountNumber: normalizedRow.bankAccountNumber || undefined,
          bankBranchCode: normalizedRow.bankBranchCode || undefined,
          paymentMethod,
          taxId: normalizedRow.taxId || undefined,
          pensionScheme: normalizedRow.pensionScheme || undefined,
          pensionNumber: normalizedRow.pensionNumber || undefined,
          notes: normalizedRow.notes || undefined,
          createdBy: user.id
        }

        // Create employee salary
        await EmployeeSalary.create(employeeSalaryData)
        result.successCount++

        // Add to imported list
        result.imported.push({
          row: i + 1,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeEmail: employee.email,
          basicSalary
        })

        logger.info('Employee salary created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          employeeId: employee.employeeId || employee.employeeNumber,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          basicSalary,
          userId: user.id
        })

      } catch (error: unknown) {
        result.errorCount++

        // Log the error
        logger.error('Error processing employee salary row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Log the final result
    logger.info('Employee salary bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    })

    return NextResponse.json({
      status: 'success',
      data: result
    })
  } catch (error: unknown) {
    console.error('Error in employee salary bulk import:', error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 })
  }
}

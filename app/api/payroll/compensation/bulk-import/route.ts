// app/api/payroll/compensation/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import { Employee } from '@/models/Employee'
import Department from '@/models/Department'
import Compensation from '@/models/payroll/Compensation'
import PayrollRun from '@/models/payroll/PayrollRun'
import Allowance from '@/models/payroll/Allowance'
import * as XLSX from 'xlsx'
import mongoose from 'mongoose'

export const runtime = 'nodejs'

// Required roles for compensation bulk import
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_DIRECTOR,
  UserRole.PAYROLL_MANAGER,
  UserRole.FINANCE_MANAGER
]

// Expected columns in the import file (Excel headers)
const REQUIRED_COLUMNS = [
  'Employee ID',
  'Compensation Type',
  'Amount',
  'Effective Date'
]

const OPTIONAL_COLUMNS = [
  'Employee Email',
  'Employee Name',
  'Department',
  'Description',
  'Notes',
  'Currency',
  'Payroll Run ID',
  'Is Recurring',
  'Frequency',
  'End Date',
  'Taxable',
  'Pensionable'
]

// Field mapping from Excel headers to object properties
const FIELD_MAPPING = {
  'Employee ID': 'employeeId',
  'Employee Email': 'employeeEmail',
  'Employee Name': 'employeeName',
  'Department': 'department',
  'Compensation Type': 'compensationType',
  'Amount': 'amount',
  'Effective Date': 'effectiveDate',
  'Description': 'description',
  'Notes': 'notes',
  'Currency': 'currency',
  'Payroll Run ID': 'payrollRunId',
  'Is Recurring': 'isRecurring',
  'Frequency': 'frequency',
  'End Date': 'endDate',
  'Taxable': 'taxable',
  'Pensionable': 'pensionable'
}

interface CompensationImportRow {
  employeeId?: string
  employeeEmail?: string
  employeeName?: string
  department?: string
  compensationType: 'performance_bonus' | 'holiday_bonus' | 'overtime' | 'special_allowance' | 'one_time_deduction' | 'retroactive_adjustment'
  amount: number
  effectiveDate: string
  description?: string
  notes?: string
  currency?: string
  payrollRunId?: string
  isRecurring?: boolean
  frequency?: 'monthly' | 'quarterly' | 'annually' | 'one_time'
  endDate?: string
  taxable?: boolean
  pensionable?: boolean
}

interface BulkImportResult {
  totalRows: number
  successCount: number
  errorCount: number
  skippedCount: number
  errors: Array<{
    row: number
    error: string
    employeeInfo?: string
  }>
  skipped: Array<{
    row: number
    reason: string
    employeeInfo?: string
  }>
  results: Array<{
    row: number
    employeeId: string
    employeeName: string
    compensationType: string
    amount: number
    status: 'success' | 'error' | 'skipped'
    message?: string
    recordId?: string
  }>
}

/**
 * Map Excel row data to expected field names
 */
function mapRowFields(row: Record<string, any>): Record<string, any> {
  const mappedRow: Record<string, any> = {}

  // Map each field from Excel header to expected property name
  Object.entries(FIELD_MAPPING).forEach(([excelHeader, propertyName]) => {
    if (row[excelHeader] !== undefined) {
      mappedRow[propertyName] = row[excelHeader]
    }
  })

  return mappedRow
}

/**
 * POST /api/payroll/compensation/bulk-import
 * Import compensation adjustments (bonuses, overtime, etc.) in bulk from Excel file
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      return NextResponse.json({ error: 'Only Excel files (.xlsx, .xls) are supported' }, { status: 400 })
    }

    logger.info('Starting compensation bulk import', LogCategory.IMPORT, {
      userId: user.id,
      fileName: file.name,
      fileSize: file.size
    })

    // Read and parse Excel file
    const buffer = Buffer.from(await file.arrayBuffer())
    const workbook = XLSX.read(buffer, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false,
      blankrows: false
    }) as Record<string, any>[]

    if (rows.length === 0) {
      return NextResponse.json({ error: 'File is empty or has no data' }, { status: 400 })
    }

    // Initialize result tracking
    const result: BulkImportResult = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      skipped: [],
      results: []
    }

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const originalRow = rows[i]
      const rowNumber = i + 1

      try {
        // Check if row has meaningful data
        const hasData = Object.values(originalRow).some(value =>
          value !== null && value !== undefined && value !== ''
        )

        if (!hasData) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: 'Empty row - no data found'
          })
          continue
        }

        // Map Excel headers to expected field names
        const row = mapRowFields(originalRow)

        // Validate required fields using mapped field names
        const missingFields = REQUIRED_COLUMNS.filter(excelHeader => {
          const fieldName = FIELD_MAPPING[excelHeader]
          return !row[fieldName] || row[fieldName].toString().trim() === ''
        })

        if (missingFields.length > 0) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: `Missing required fields: ${missingFields.join(', ')}`,
            employeeInfo: row.employeeName || row.employeeEmail || row.employeeId || 'Unknown'
          })
          continue
        }

        // Find employee by email (primary) or ID (fallback) - only active employees
        let employee
        if (row.employeeEmail) {
          employee = await Employee.findOne({
            email: row.employeeEmail.trim().toLowerCase(),
            employmentStatus: 'active'
          }).populate('departmentId', 'name')
        } else if (row.employeeId) {
          employee = await Employee.findOne({
            $or: [
              { employeeId: row.employeeId },
              { _id: row.employeeId }
            ],
            employmentStatus: 'active'
          }).populate('departmentId', 'name')
        }

        if (!employee) {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: `Employee not found or inactive: ${row.employeeEmail || row.employeeId || 'Unknown'}`,
            employeeInfo: `${row.employeeName || 'Unknown'} (${row.employeeEmail || row.employeeId || 'No identifier'})`
          })
          continue
        }

        // Skip rows with empty amounts (user didn't want to process this compensation)
        if (!row.amount || row.amount.toString().trim() === '') {
          result.skippedCount++
          result.skipped.push({
            row: rowNumber,
            reason: 'Amount is empty - skipping this compensation type',
            employeeInfo: `${employee.firstName} ${employee.lastName} - ${row.compensationType || 'Unknown type'}`
          })
          continue
        }

        // Parse and validate compensation data
        const amount = parseFloat(row.amount)
        if (isNaN(amount) || amount <= 0) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Invalid amount - must be a positive number',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Parse effective date
        const effectiveDate = new Date(row.effectiveDate)
        if (isNaN(effectiveDate.getTime())) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Invalid effective date format',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Validate compensation type against allowances database and standard types
        const allowances = await Allowance.find({ isActive: true }).select('code').lean()
        const allowanceCodes = allowances.map(a => a.code)
        const standardTypes = ['performance_bonus', 'holiday_bonus', 'overtime', 'special_allowance', 'one_time_deduction', 'retroactive_adjustment']
        const validCompensationTypes = [...allowanceCodes, ...standardTypes]

        if (!validCompensationTypes.includes(row.compensationType)) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: `Invalid compensation type. Must be one of: ${validCompensationTypes.join(', ')}`,
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Parse optional fields
        const endDate = row.endDate ? new Date(row.endDate) : undefined
        if (row.endDate && isNaN(endDate!.getTime())) {
          result.errorCount++
          result.errors.push({
            row: rowNumber,
            error: 'Invalid end date format',
            employeeInfo: `${employee.firstName} ${employee.lastName}`
          })
          continue
        }

        // Find or validate payroll run if specified
        let payrollRun = null
        if (row.payrollRunId) {
          payrollRun = await PayrollRun.findById(row.payrollRunId)
          if (!payrollRun) {
            result.errorCount++
            result.errors.push({
              row: rowNumber,
              error: 'Specified payroll run not found',
              employeeInfo: `${employee.firstName} ${employee.lastName}`
            })
            continue
          }
        }

        // Create compensation record data
        const compensationData = {
          employeeId: employee._id,
          compensationType: row.compensationType,
          amount: amount,
          effectiveDate: effectiveDate,
          description: row.description?.trim() || `${row.compensationType.replace('_', ' ')} for ${employee.firstName} ${employee.lastName}`,
          notes: row.notes?.trim() || undefined,
          currency: row.currency || 'MWK',
          payrollRunId: payrollRun?._id || undefined,
          isRecurring: row.isRecurring === 'true' || row.isRecurring === true || false,
          frequency: row.frequency || 'one_time',
          endDate: endDate || undefined,
          taxable: row.taxable === 'true' || row.taxable === true || true, // Default to taxable
          pensionable: row.pensionable === 'true' || row.pensionable === true || false,
          status: 'pending',
          createdBy: user.id
        }

        // Create compensation record
        const compensationRecord = await Compensation.create(compensationData)

        // Track successful import
        result.successCount++
        result.results.push({
          row: rowNumber,
          employeeId: employee._id.toString(),
          employeeName: `${employee.firstName} ${employee.lastName}`,
          compensationType: row.compensationType,
          amount: amount,
          status: 'success',
          message: 'Compensation record created successfully',
          recordId: compensationRecord._id.toString()
        })

        logger.info('Compensation record created successfully', LogCategory.IMPORT, {
          rowIndex: rowNumber,
          employeeId: employee._id.toString(),
          compensationType: row.compensationType,
          amount: amount,
          userId: user.id
        })

      } catch (error) {
        result.errorCount++
        result.errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          employeeInfo: row.employeeName || row.employeeEmail || 'Unknown'
        })

        logger.error('Error processing compensation row', LogCategory.IMPORT, {
          rowIndex: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error',
          userId: user.id
        })
      }
    }

    // Log final results
    logger.info('Compensation bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    })

    return NextResponse.json({
      success: true,
      message: `Bulk import completed: ${result.successCount} created, ${result.errorCount} errors, ${result.skippedCount} skipped`,
      data: result
    })

  } catch (error) {
    logger.error('Error in compensation bulk import', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to process bulk import', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

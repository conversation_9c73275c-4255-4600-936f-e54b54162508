import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payslipProgressService } from '@/lib/services/payroll/payslip-progress-service';
import { errorService } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/payroll/payslips/progress/[operationId]
 * Get progress for a payslip operation
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ operationId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get operation ID
    const { operationId } = await params;

    // Validate operation ID
    if (!operationId) {
      return errorService.createApiResponse(
        'VALIDATION',
        'MISSING_OPERATION_ID',
        'Operation ID is required',
        'Please provide a valid operation ID.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        400,
        'MEDIUM'
      );
    }

    // Get progress
    const progress = payslipProgressService.getProgress(operationId);

    if (!progress) {
      return errorService.createApiResponse(
        'VALIDATION',
        'OPERATION_NOT_FOUND',
        'Operation not found',
        'The requested operation was not found or has expired.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { operationId }
        },
        404,
        'LOW'
      );
    }

    // Calculate progress percentage
    const progressPercentage = progress.total > 0 
      ? Math.round((progress.processed / progress.total) * 100)
      : 0;

    // Format response
    const response = {
      operationId: progress.operationId,
      type: progress.type,
      payrollRunId: progress.payrollRunId,
      status: progress.status,
      progress: {
        total: progress.total,
        processed: progress.processed,
        failed: progress.failed,
        skipped: progress.skipped,
        percentage: progressPercentage,
        successful: progress.processed - progress.failed - progress.skipped
      },
      currentEmployee: progress.currentEmployee,
      currentEmployeeId: progress.currentEmployeeId,
      timing: {
        startedAt: progress.startedAt,
        completedAt: progress.completedAt,
        estimatedTimeRemaining: progress.estimatedTimeRemaining,
        processingRate: progress.processingRate
      },
      errors: progress.errors.slice(-10), // Return last 10 errors
      summary: {
        isComplete: progress.status === 'completed' || progress.status === 'failed' || progress.status === 'cancelled',
        hasErrors: progress.failed > 0,
        successRate: progress.total > 0 ? Math.round(((progress.processed - progress.failed - progress.skipped) / progress.total) * 100) : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error: unknown) {
    const operationId = await params.then(p => p.operationId).catch(() => 'unknown');
    logger.error(`Error getting progress for operation ${operationId}`, LogCategory.API, error);
    
    return errorService.createApiResponse(
      'SYSTEM',
      'PROGRESS_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to retrieve operation progress. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { operationId }
      },
      500,
      'HIGH'
    );
  }
}

/**
 * DELETE /api/payroll/payslips/progress/[operationId]
 * Cancel a payslip operation
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ operationId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions (only higher-level roles can cancel operations)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get operation ID
    const { operationId } = await params;

    // Validate operation ID
    if (!operationId) {
      return errorService.createApiResponse(
        'VALIDATION',
        'MISSING_OPERATION_ID',
        'Operation ID is required',
        'Please provide a valid operation ID.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        400,
        'MEDIUM'
      );
    }

    // Get current progress
    const progress = payslipProgressService.getProgress(operationId);

    if (!progress) {
      return errorService.createApiResponse(
        'VALIDATION',
        'OPERATION_NOT_FOUND',
        'Operation not found',
        'The requested operation was not found or has expired.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { operationId }
        },
        404,
        'LOW'
      );
    }

    // Check if operation can be cancelled
    if (progress.status !== 'processing') {
      return errorService.createApiResponse(
        'VALIDATION',
        'OPERATION_NOT_CANCELLABLE',
        'Operation cannot be cancelled',
        `Operation is already ${progress.status} and cannot be cancelled.`,
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { operationId, currentStatus: progress.status }
        },
        400,
        'MEDIUM'
      );
    }

    // Cancel the operation
    payslipProgressService.cancelOperation(operationId);

    logger.info(`Operation ${operationId} cancelled by user ${user.id}`, LogCategory.PAYROLL, {
      operationId,
      userId: user.id,
      operationType: progress.type,
      payrollRunId: progress.payrollRunId,
      processedBeforeCancel: progress.processed
    });

    return NextResponse.json({
      success: true,
      message: 'Operation cancelled successfully',
      data: {
        operationId,
        status: 'cancelled',
        processedBeforeCancel: progress.processed,
        totalPlanned: progress.total
      }
    });

  } catch (error: unknown) {
    const operationId = await params.then(p => p.operationId).catch(() => 'unknown');
    logger.error(`Error cancelling operation ${operationId}`, LogCategory.API, error);
    
    return errorService.createApiResponse(
      'SYSTEM',
      'OPERATION_CANCEL_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to cancel operation. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { operationId }
      },
      500,
      'HIGH'
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import PaySlip from '@/models/payroll/PaySlip';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Define roles that can bulk delete payslips
const PAYSLIP_DELETE_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.PAYROLL_OFFICER
];

interface DeleteFilter {
  payrollRunId?: string;
  department?: string;
  status?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
  searchTerm?: string;
}

interface BulkDeleteRequest {
  ids?: string[];
  filter?: DeleteFilter;
}

interface BulkDeleteResult {
  deletedCount: number;
  errors: Array<{ id: string; error: string }>;
}

/**
 * POST /api/payroll/payslips/bulk-delete
 * Bulk delete payslips
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, PAYSLIP_DELETE_ROLES);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body: BulkDeleteRequest = await req.json();

    logger.info('Payslip bulk delete request', LogCategory.API, { body });

    // Validate request
    if (!body.ids && !body.filter) {
      return NextResponse.json({
        error: 'Either payslip IDs or filter criteria must be provided'
      }, { status: 400 });
    }

    // If IDs are provided, validate them
    if (body.ids && (!Array.isArray(body.ids) || body.ids.length === 0)) {
      return NextResponse.json({
        error: 'Payslip IDs must be a non-empty array'
      }, { status: 400 });
    }

    // If filter is provided, validate it
    if (body.filter) {
      // At least one filter criteria must be provided
      if (!body.filter.payrollRunId && !body.filter.department && !body.filter.status && 
          !body.filter.dateRange && !body.filter.searchTerm) {
        return NextResponse.json({
          error: 'At least one filter criteria must be provided'
        }, { status: 400 });
      }

      // If date range is provided, validate it
      if (body.filter.dateRange) {
        if (!body.filter.dateRange.from || !body.filter.dateRange.to) {
          return NextResponse.json({
            error: 'Date range must include both from and to dates'
          }, { status: 400 });
        }

        // Convert string dates to Date objects
        body.filter.dateRange.from = new Date(body.filter.dateRange.from);
        body.filter.dateRange.to = new Date(body.filter.dateRange.to);

        // Validate date range
        if (isNaN(body.filter.dateRange.from.getTime()) || isNaN(body.filter.dateRange.to.getTime())) {
          return NextResponse.json({
            error: 'Invalid date format in date range'
          }, { status: 400 });
        }
      }
    }

    // Perform bulk delete
    const result = await bulkDeletePayslips(body);

    // Return result
    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount,
      errors: result.errors
    });
  } catch (error: unknown) {
    logger.error('Error in payslip bulk delete handler', LogCategory.API, error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'An error occurred' 
    }, { status: 500 });
  }
}

/**
 * Bulk delete payslips based on IDs or filter criteria
 */
async function bulkDeletePayslips(request: BulkDeleteRequest): Promise<BulkDeleteResult> {
  const result: BulkDeleteResult = {
    deletedCount: 0,
    errors: []
  };

  try {
    let payslipsToDelete: string[] = [];

    if (request.ids) {
      // Delete by IDs
      payslipsToDelete = request.ids;
    } else if (request.filter) {
      // Build query based on filter
      const query: Record<string, unknown> = {};

      if (request.filter.payrollRunId) {
        query.payrollRunId = request.filter.payrollRunId;
      }

      if (request.filter.department) {
        query['employeeDetails.department'] = request.filter.department;
      }

      if (request.filter.status) {
        query.status = request.filter.status;
      }

      if (request.filter.dateRange) {
        query['payPeriod.startDate'] = {
          $gte: request.filter.dateRange.from,
          $lte: request.filter.dateRange.to
        };
      }

      if (request.filter.searchTerm) {
        query.$or = [
          { 'employeeDetails.name': { $regex: request.filter.searchTerm, $options: 'i' } },
          { 'employeeDetails.employeeNumber': { $regex: request.filter.searchTerm, $options: 'i' } }
        ];
      }

      // Find payslips matching the filter
      const payslips = await PaySlip.find(query).select('_id');
      payslipsToDelete = payslips.map(p => p._id.toString());
    }

    // Delete payslips one by one to handle errors gracefully
    for (const payslipId of payslipsToDelete) {
      try {
        // Check if payslip exists
        const payslip = await PaySlip.findById(payslipId);
        if (!payslip) {
          result.errors.push({
            id: payslipId,
            error: 'Payslip not found'
          });
          continue;
        }

        // Delete the payslip
        await PaySlip.findByIdAndDelete(payslipId);

        // Update the corresponding payroll record to mark payslip as not generated
        if (payslip.payrollRecordId) {
          await PayrollRecord.findByIdAndUpdate(
            payslip.payrollRecordId,
            { 
              $unset: { payslipId: 1 },
              $set: { payslipGenerated: false }
            }
          );
        }

        result.deletedCount++;

        logger.info(`Payslip ${payslipId} deleted successfully`, LogCategory.PAYROLL);
      } catch (error) {
        logger.error(`Error deleting payslip ${payslipId}`, LogCategory.PAYROLL, error);
        result.errors.push({
          id: payslipId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    logger.info(`Bulk delete completed: ${result.deletedCount} payslips deleted, ${result.errors.length} errors`, LogCategory.PAYROLL);

    return result;
  } catch (error) {
    logger.error('Error in bulk delete payslips', LogCategory.PAYROLL, error);
    throw error;
  }
}

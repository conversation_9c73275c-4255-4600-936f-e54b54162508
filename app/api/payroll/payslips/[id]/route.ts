import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payslipGenerationService } from '@/lib/services/payroll/payslip-generation-service';

/**
 * GET /api/payroll/payslips/[id]
 * Get a payslip by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get payslip
    const payslip = await payslipGenerationService.getPayslipById(id);

    if (!payslip) {
      return NextResponse.json(
        { error: 'Payslip not found' },
        { status: 404 }
      );
    }

    // Mark payslip as viewed
    await payslipGenerationService.markPayslipAsViewed(id);

    return NextResponse.json({
      success: true,
      data: payslip
    });
  } catch (error: unknown) {
    logger.error(`Error getting payslip`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get payslip', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payroll/payslips/[id]
 * Update payslip status (mark as sent, viewed, downloaded)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let payslip;
    let requiredRoles: UserRole[];

    // Perform action based on the action type
    switch (body.action) {
      case 'markAsSent':
        // Check permissions
        requiredRoles = [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_DIRECTOR,
          UserRole.HR_DIRECTOR,
          UserRole.FINANCE_MANAGER,
          UserRole.HR_MANAGER
        ];

        if (!hasRequiredPermissions(user, requiredRoles)) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to mark payslip as sent' },
            { status: 403 }
          );
        }

        // Mark payslip as sent
        payslip = await payslipGenerationService.markPayslipAsSent(id, user.id);
        break;

      case 'markAsViewed':
        // Mark payslip as viewed
        payslip = await payslipGenerationService.markPayslipAsViewed(id);
        break;

      case 'markAsDownloaded':
        // Mark payslip as downloaded
        payslip = await payslipGenerationService.markPayslipAsDownloaded(id);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: markAsSent, markAsViewed, markAsDownloaded' },
          { status: 400 }
        );
    }

    if (!payslip) {
      return NextResponse.json(
        { error: 'Payslip not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Payslip ${body.action} successful`,
      data: payslip
    });
  } catch (error: unknown) {
    logger.error(`Error updating payslip`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update payslip', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

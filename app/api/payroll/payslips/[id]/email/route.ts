// app/api/payroll/payslips/[id]/email/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { payslipGenerationService } from '@/lib/services/payroll/payslip-generation-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import PaySlip from '@/models/payroll/PaySlip';
// import Employee from '@/models/Employee';

/**
 * POST handler for emailing a payslip
 * @param request - Next.js request
 * @param params - URL parameters
 * @returns Next.js response
 */
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Get session
    const user = await getCurrentUser(request);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await context.params;

    // Get payslip
    const payslip = await PaySlip.findById(id)
      .populate('employeeId', 'firstName lastName email');

    if (!payslip) {
      return NextResponse.json(
        { error: 'Payslip not found' },
        { status: 404 }
      );
    }

    // Get employee email
    const employee = payslip.employeeId as { email?: string };
    if (!employee.email) {
      return NextResponse.json(
        { error: 'Employee email not found' },
        { status: 400 }
      );
    }

    // Generate PDF payslip
    const pdfBuffer = await payslipGenerationService.generatePayslipPdf(id);

    if (!pdfBuffer) {
      return NextResponse.json(
        { error: 'Failed to generate payslip PDF' },
        { status: 500 }
      );
    }

    // Send email with payslip
    // In a real implementation, this would use an email service
    // For now, just log the action and mark the payslip as emailed
    logger.info(`Emailing payslip ${id} to ${employee.email}`, LogCategory.PAYROLL);

    // Mark payslip as emailed
    await PaySlip.findByIdAndUpdate(id, {
      $push: {
        'emailHistory': {
          sentAt: new Date(),
          sentBy: user.id,
          sentTo: employee.email,
          status: 'sent'
        }
      }
    });

    return NextResponse.json({
      message: `Payslip has been sent to ${employee.email}`,
      success: true
    });
  } catch (error: unknown) {
    logger.error('Error emailing payslip', LogCategory.PAYROLL, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to email payslip' },
      { status: 500 }
    );
  }
}

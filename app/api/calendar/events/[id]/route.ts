
// app/api/calendar/events/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/lib/backend/services/calendar/CalendarService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/calendar/events/[id]
 * Get a specific calendar event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get event
    const event = await calendarService.getById(id, [
      { path: 'organizer', select: 'name email avatar' },
      { path: 'attendees.userId', select: 'name email avatar' },
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ]);

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if user is organizer or attendee
    const isOrganizer = event.organizer._id.toString() === user.id;
    const isAttendee = event.attendees.some(a => a.userId._id.toString() === user.id);

    if (!isOrganizer && !isAttendee && event.isPrivate) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json(event);
  } catch (error: unknown) {
    logger.error('Error in calendar event GET by ID handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PUT /api/calendar/events/[id]
 * Update a calendar event
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get event to check permissions
    const existingEvent = await calendarService.getById(id);

    if (!existingEvent) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if user is the organizer
    if (existingEvent.organizer.toString() !== user.id) {
      return NextResponse.json({
        error: 'Only the event organizer can update the event'
      }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Set updater
    body.updatedBy = user.id;

    // Update event
    const event = await calendarService.updateById(id, body);

    return NextResponse.json(event);
  } catch (error: unknown) {
    logger.error('Error in calendar event PUT handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * DELETE /api/calendar/events/[id]
 * Delete a calendar event
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get event to check permissions
    const existingEvent = await calendarService.getById(id);

    if (!existingEvent) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if user is the organizer
    if (existingEvent.organizer.toString() !== user.id) {
      return NextResponse.json({
        error: 'Only the event organizer can delete the event'
      }, { status: 403 });
    }

    // Delete event
    const result = await calendarService.deleteById(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in calendar event DELETE handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

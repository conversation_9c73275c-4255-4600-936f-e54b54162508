import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/lib/backend/services/calendar/CalendarService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * PUT /api/calendar/events/[id]/attendees
 * Update attendee status for a calendar event
 */
export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await context.params;

    // Get request body
    const body = await request.json();

    // Validate status
    if (!body.status || !['accepted', 'declined', 'tentative'].includes(body.status)) {
      return NextResponse.json({
        error: 'Valid status is required (accepted, declined, tentative)'
      }, { status: 400 });
    }

    // Update attendee status
    const event = await calendarService.updateAttendeeStatus(
      id,
      user.id,
      body.status,
      body.notes
    );

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    return NextResponse.json(event);
  } catch (error: unknown) {
    logger.error('Error in calendar event attendee PUT handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

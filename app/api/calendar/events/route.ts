// app/api/calendar/events/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/lib/backend/services/calendar/CalendarService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/calendar/events
 * Get calendar events for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    // Validate required parameters
    if (!startDateParam || !endDateParam) {
      return NextResponse.json({
        error: 'Start date and end date are required'
      }, { status: 400 });
    }

    // Parse dates
    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json({
        error: 'Invalid date format'
      }, { status: 400 });
    }

    // Get events
    const events = await calendarService.getUserEvents(
      user.id,
      startDate,
      endDate
    );

    return NextResponse.json(events);
  } catch (error: unknown) {
    logger.error('Error in calendar events GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/calendar/events
 * Create a new calendar event
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.startTime || !body.endTime) {
      return NextResponse.json({
        error: 'Title, start time, and end time are required'
      }, { status: 400 });
    }

    // Set creator and organizer
    body.createdBy = user.id;
    body.organizer = user.id;

    // Create event
    const event = await calendarService.createEvent(body);

    return NextResponse.json(event, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in calendar events POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

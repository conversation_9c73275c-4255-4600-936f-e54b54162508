// app/api/attendance/check-leave/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { leaveAttendanceService } from '@/services/attendance/LeaveAttendanceService';

/**
 * GET /api/attendance/check-leave
 * Check if an employee is on leave for a specific date
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const employeeId = searchParams.get('employeeId');
    const date = searchParams.get('date');

    // Validate required parameters
    if (!employeeId || !date) {
      return NextResponse.json(
        { error: 'Missing required parameters: employeeId, date' },
        { status: 400 }
      );
    }

    // Check if employee is on leave
    const leave = await leaveAttendanceService.isEmployeeOnLeave(
      employeeId,
      new Date(date)
    );

    return NextResponse.json({
      onLeave: !!leave,
      leave
    });
  } catch (error: unknown) {
    logger.error('Error checking if employee is on leave', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/assessment/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { assessmentService } from '@/lib/backend/services/assessment/AssessmentService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/assessment
 * Get assessments with optional filtering
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const isTemplate = searchParams.get('isTemplate') === 'true';
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: Record<string, any> = {};

    if (type) {
      query.type = type;
    }

    if (category) {
      query.category = category;
    }

    if (status) {
      query.status = status;
    }

    if (searchParams.has('isTemplate')) {
      query.isTemplate = isTemplate;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { assessmentId: { $regex: search, $options: 'i' } }
      ];
    }

    // Get assessments
    const result = await assessmentService.getAll({
      query,
      page,
      limit,
      populate: [
        { path: 'createdBy', select: 'name email' }
      ],
      sort: { [sortBy]: sortOrder === 'asc' ? 1 : -1 }
    });

    return NextResponse.json({
      data: result.data,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    });
  } catch (error: unknown) {
    logger.error('Error in assessment GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/assessment
 * Create a new assessment
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.RECRUITER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.type || !body.category || !body.questions || !body.questions.length) {
      return NextResponse.json({
        error: 'Title, type, category, and questions are required'
      }, { status: 400 });
    }

    // Set creator
    body.createdBy = user._id?.toString() || user.id;

    // Create assessment
    const assessment = await assessmentService.createAssessment(body);

    return NextResponse.json(assessment, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in assessment POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

// app/api/assessment/submission/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { assessmentSubmissionService } from '@/lib/backend/services/assessment/AssessmentSubmissionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/assessment/submission
 * Get assessment submissions for the current user
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const assessmentId = searchParams.get('assessmentId');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: Record<string, any> = {
      userId: user._id?.toString() || user.id
    };

    if (status) {
      query.status = status;
    }

    if (assessmentId) {
      query.assessmentId = assessmentId;
    }

    // Get submissions
    const result = await assessmentSubmissionService.getAll({
      query,
      page,
      limit,
      populate: [
        { path: 'assessmentId', select: 'title type category' }
      ],
      sort: { [sortBy]: sortOrder === 'asc' ? 1 : -1 }
    });

    return NextResponse.json({
      data: result.data,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    });
  } catch (error: unknown) {
    logger.error('Error in assessment submission GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/assessment/submission
 * Create a new assessment submission
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.assessmentId) {
      return NextResponse.json({
        error: 'Assessment ID is required'
      }, { status: 400 });
    }

    // Set user ID
    body.userId = user._id?.toString() || user.id;

    // Create submission
    const submission = await assessmentSubmissionService.createSubmission(body);

    return NextResponse.json(submission, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in assessment submission POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

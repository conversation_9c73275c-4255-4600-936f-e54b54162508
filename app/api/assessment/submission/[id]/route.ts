// app/api/assessment/submission/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { assessmentSubmissionService } from '@/lib/backend/services/assessment/AssessmentSubmissionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/assessment/submission/[id]
 * Get a specific assessment submission
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get submission
    const submission = await assessmentSubmissionService.getById(id, [
      { path: 'assessmentId', select: 'title type category questions' },
      { path: 'userId', select: 'name email' },
      { path: 'gradedBy', select: 'name email' }
    ]);

    if (!submission) {
      return NextResponse.json({ error: 'Submission not found' }, { status: 404 });
    }

    // Check if user is the owner or has permission to view
    const isOwner = submission.userId._id.toString() === (user._id?.toString() || user.id);
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.RECRUITER
    ]);

    if (!isOwner && !hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json(submission);
  } catch (error: unknown) {
    logger.error('Error in assessment submission GET by ID handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PUT /api/assessment/submission/[id]
 * Submit answers for an assessment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();

    // Validate answers
    if (!body.answers || !Array.isArray(body.answers) || body.answers.length === 0) {
      return NextResponse.json({
        error: 'Answers are required'
      }, { status: 400 });
    }

    // Submit answers
    const submission = await assessmentSubmissionService.submitAnswers(
      id,
      body.answers,
      user._id?.toString() || user.id
    );

    if (!submission) {
      return NextResponse.json({ error: 'Submission not found' }, { status: 404 });
    }

    return NextResponse.json(submission);
  } catch (error: unknown) {
    logger.error('Error in assessment submission PUT handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { paymentService } from '@/lib/services/banking/payment-service';
import { PaymentStatus, PaymentType } from '@/models/accounting/Payment';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/banking/payments
 * Get all payments
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status') as PaymentStatus  | undefined;
    const type = searchParams.get('type') as PaymentType | null;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit') || '100');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get payments
    const payments = await paymentService.getPayments(
      status || undefined,
      type || undefined,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      skip
    );

    return NextResponse.json(payments);
  } catch (error) {
    logger.error('Error getting payments', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get payments' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/banking/payments
 * Create a new payment
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager' &&
        user.role !== 'accountant') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.paymentType || !body.amount || !body.date || !body.description) {
      return NextResponse.json(
        { error: 'Missing required fields: paymentType, amount, date, description' },
        { status: 400 }
      );
    }

    // Create payment
    const payment = await paymentService.createPayment({
      ...body,
      date: new Date(body.date),
      createdBy: user.id
    });

    return NextResponse.json(payment, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating payment', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to create payment', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

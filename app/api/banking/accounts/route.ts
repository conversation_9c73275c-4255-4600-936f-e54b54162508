import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { bankingService } from '@/lib/services/banking/banking-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/banking/accounts
 * Get all bank accounts
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const isActive = searchParams.get('isActive');

    // Get bank accounts
    const accounts = await bankingService.getBankAccounts(
      isActive === 'true' ? true : isActive === 'false' ? false : undefined
    );

    return NextResponse.json(accounts);
  } catch (error) {
    logger.error('Error getting bank accounts', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank accounts' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/banking/accounts
 * Create a new bank account
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.accountNumber || !body.name || !body.bankName || !body.accountType) {
      return NextResponse.json(
        { error: 'Missing required fields: accountNumber, name, bankName, accountType' },
        { status: 400 }
      );
    }

    // Create bank account
    const account = await bankingService.createBankAccount({
      ...body,
      createdBy: user.id,
      updatedBy: user.id
    });

    return NextResponse.json(account, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating bank account', LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to create bank account', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

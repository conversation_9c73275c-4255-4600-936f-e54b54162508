// app/api/banking/accounts/[id]/reconciliation/start/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { reconciliationService } from '@/lib/services/banking/reconciliation-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/banking/accounts/[id]/reconciliation/start
 * Start a new reconciliation
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager' &&
        user.role !== 'accountant') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.statementBalance || !body.statementDate) {
      return NextResponse.json(
        { error: 'Missing required fields: statementBalance, statementDate' },
        { status: 400 }
      );
    }

    // Start reconciliation
    const reconciliationData = await reconciliationService.startReconciliation(
      id,
      body.statementBalance,
      new Date(body.statementDate)
    );

    return NextResponse.json(reconciliationData);
  } catch (error: unknown) {
    logger.error(`Error starting reconciliation`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to start reconciliation', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

// app/api/employees/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { Employee } from '@/models/Employee'
import { connectToDatabase } from '@/lib/backend/database'
import * as XLSX from 'xlsx'
import crypto from 'crypto'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import mongoose from 'mongoose'

export const runtime = 'nodejs';

// Import Department model
import Department from '@/models/Department';

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'firstName',
  'lastName',
  'email',
  'position', // Position is a required field
  'employmentType',
  // 'employmentStatus', // Made optional since we default to 'active'
  'hireDate'
]

// Define the important but optional columns
const IMPORTANT_COLUMNS = [
  'employmentStatus', // Important but optional (defaults to 'active')
  'district', // District is important but optional
  'village',
  'traditionalAuthority',
  'maritalStatus',
  'numberOfChildren'
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'First Name': 'firstName',
  'Last Name': 'lastName',
  'Email': 'email',
  'Position': 'position',
  'Employment Type': 'employmentType',
  'Status': 'employmentStatus', // Note: Template uses "Status" for employmentStatus
  'Employment Status': 'employmentStatus', // Also handle "Employment Status" column name
  'Hire Date': 'hireDate',
  'Phone': 'phone',
  'Date of Birth': 'dateOfBirth',
  'Gender': 'gender',
  'Marital Status': 'maritalStatus',
  'Number of Children': 'numberOfChildren',
  'Address': 'address',
  'City': 'city',
  'State': 'state',
  'Postal Code': 'postalCode',
  'Country': 'country',
  'Village': 'village',
  'Traditional Authority': 'traditionalAuthority',
  'District': 'district',
  'National ID': 'nationalId',
  'Department': 'department', // Add department mapping
  'Salary': 'salary',
  'Bank Name': 'bankName',
  'Bank Account Number': 'bankAccountNumber',
  'Next of Kin Name': 'nextOfKinName',
  'Next of Kin Relationship': 'nextOfKinRelationship',
  'Next of Kin Phone': 'nextOfKinPhone',
  'Next of Kin Address': 'nextOfKinAddress',
  'Emergency Contact Name': 'emergencyContactName',
  'Emergency Contact Phone': 'emergencyContactPhone',
  'Emergency Contact Relationship': 'emergencyContactRelationship',
  'Notes': 'notes'
}

// Define the valid employment types
const VALID_EMPLOYMENT_TYPES = ['full-time', 'part-time', 'contract', 'intern', 'temporary', 'volunteer']

// Define the valid employment statuses
const VALID_EMPLOYMENT_STATUSES = ['active', 'inactive', 'on-leave', 'terminated']

// Define the valid marital statuses
const VALID_MARITAL_STATUSES = ['single', 'married', 'divorced', 'widowed']

// Function to generate a unique employee ID
const generateEmployeeId = async (): Promise<string> => {
  // Get the current year
  const year = new Date().getFullYear().toString().slice(-2)

  // Generate a random 6-digit number
  const randomPart = crypto.randomInt(100000, 999999).toString()

  // Combine to create an employee ID like "EMP-23-123456"
  const employeeId = `EMP-${year}-${randomPart}`

  // Check if this ID already exists
  const existingEmployee = await Employee.findOne({ employeeId })

  // If it exists, generate a new one recursively
  if (existingEmployee) {
    return generateEmployeeId()
  }

  return employeeId
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_STAFF
    ])

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Fetch all valid departments for validation and create efficient lookup maps
    const validDepartments = await Department.find({ status: { $ne: 'inactive' } })
      .select('_id name')
      .lean();

    // Create efficient lookup maps for department validation
    const departmentNameMap = new Map<string, string>(); // lowercase name -> actual name
    const departmentIdMap = new Map<string, mongoose.Types.ObjectId>(); // lowercase name -> _id

    validDepartments.forEach(dept => {
      const lowerName = dept.name.toLowerCase();
      departmentNameMap.set(lowerName, dept.name);
      departmentIdMap.set(lowerName, dept._id);
    });

    const departmentNames = validDepartments.map(dept => dept.name);
    logger.info('Valid departments for employee import', LogCategory.IMPORT, {
      departmentCount: validDepartments.length,
      departments: departmentNames,
      userId: user.id
    });

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 })
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing employee bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[]

    // Validate and process data with enhanced result structure
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      departmentMismatchCount: 0,
      errors: [] as Array<{ row: number, error: string, employeeName?: string, department?: string }>,
      skippedEmployees: [] as Array<{ row: number, employeeName: string, department: string, reason: string }>,
      departmentMismatches: [] as Array<{ row: number, employeeName: string, department: string, availableDepartments: string[] }>,
      successfulEmployees: [] as Array<{ row: number, employeeName: string, department: string, employeeId: string }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for employee import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json({ error: 'File is empty' }, { status: 400 })
    }

    // Log the first row for debugging
    logger.debug('First row of import file', LogCategory.IMPORT, {
      firstRow: rows[0],
      keys: Object.keys(rows[0] || {})
    })

    // Create a normalized map of column names (case-insensitive)
    const firstRow = rows[0] as Record<string, any>
    const columnMap = new Map<string, string>()

    // Log all available columns for debugging
    logger.debug('Available columns in import file', LogCategory.IMPORT, {
      columns: Object.keys(firstRow),
      firstRowData: JSON.stringify(firstRow)
    });

    // Check for position and district columns specifically
    const hasPositionColumn = Object.keys(firstRow).some(key =>
      key === 'Position' || key === 'position' ||
      COLUMN_DISPLAY_MAPPING[key] === 'position'
    );

    const hasDistrictColumn = Object.keys(firstRow).some(key =>
      key === 'District' || key === 'district' ||
      COLUMN_DISPLAY_MAPPING[key] === 'district'
    );

    logger.debug('Position and district columns check', LogCategory.IMPORT, {
      hasPositionColumn,
      hasDistrictColumn
    });

    // First, try to map using the display name mapping
    Object.keys(firstRow).forEach(key => {
      // Check if this column name is in our display mapping
      if (key in COLUMN_DISPLAY_MAPPING) {
        const mappedField = COLUMN_DISPLAY_MAPPING[key]
        columnMap.set(mappedField, key)

        // Log the mapping for critical fields like position and district
        if (mappedField === 'position' || mappedField === 'district') {
          logger.debug(`Mapped ${mappedField} field`, LogCategory.IMPORT, {
            originalColumn: key,
            mappedField
          })
        }
      }
    })

    // Then, try case-insensitive matching for any remaining unmapped columns
    Object.keys(firstRow).forEach(key => {
      const normalizedKey = key.toLowerCase().trim()

      // Check required columns
      REQUIRED_COLUMNS.forEach(requiredCol => {
        if (!columnMap.has(requiredCol) && requiredCol.toLowerCase() === normalizedKey) {
          columnMap.set(requiredCol, key)

          // Log if we found position through case-insensitive matching
          if (requiredCol === 'position') {
            logger.debug('Found position through case-insensitive matching', LogCategory.IMPORT, {
              originalColumn: key,
              normalizedKey
            })
          }
        }
      })

      // Also check important columns
      IMPORTANT_COLUMNS.forEach(importantCol => {
        if (!columnMap.has(importantCol) && importantCol.toLowerCase() === normalizedKey) {
          columnMap.set(importantCol, key)

          // Log if we found district through case-insensitive matching
          if (importantCol === 'district') {
            logger.debug('Found district through case-insensitive matching', LogCategory.IMPORT, {
              originalColumn: key,
              normalizedKey
            })
          }
        }
      })
    })

    // Log the column mapping for debugging
    logger.debug('Column mapping', LogCategory.IMPORT, {
      columnMap: Object.fromEntries(columnMap),
      availableColumns: Object.keys(firstRow)
    })

    // Check for missing columns
    const missingColumns = REQUIRED_COLUMNS.filter(col => !columnMap.has(col))

    if (missingColumns.length > 0) {
      logger.warn('Missing required columns in import file', LogCategory.IMPORT, {
        missingColumns,
        availableColumns: Object.keys(firstRow),
        userId: user.id
      })

      return NextResponse.json({
        error: `Missing required columns: ${missingColumns.join(', ')}`,
        availableColumns: Object.keys(firstRow)
      }, { status: 400 })
    }

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i] as Record<string, any>

      // Create a normalized row with expected field names
      const normalizedRow: Record<string, any> = {}

      // Special handling for Status and Employment Status fields which map to employmentStatus
      if ('Status' in row && !columnMap.has('employmentStatus')) {
        columnMap.set('employmentStatus', 'Status')
      }
      if ('Employment Status' in row && !columnMap.has('employmentStatus')) {
        columnMap.set('employmentStatus', 'Employment Status')
      }

      // Map the actual column values to the expected field names
      for (const [expectedCol, actualCol] of columnMap.entries()) {
        normalizedRow[expectedCol] = row[actualCol]

        // Trim string values
        if (typeof normalizedRow[expectedCol] === 'string') {
          normalizedRow[expectedCol] = normalizedRow[expectedCol].trim()
        }
      }

      // Direct mapping from display names to expected field names for any unmapped fields
      Object.keys(row).forEach(key => {
        // If this is a display name in our mapping and we haven't already mapped it
        if (key in COLUMN_DISPLAY_MAPPING && !(COLUMN_DISPLAY_MAPPING[key] in normalizedRow)) {
          const mappedField = COLUMN_DISPLAY_MAPPING[key]
          normalizedRow[mappedField] = row[key]

          // Log mapping for critical fields
          if (mappedField === 'position' || mappedField === 'district') {
            logger.debug(`Direct mapped ${mappedField} field`, LogCategory.IMPORT, {
              originalColumn: key,
              mappedField,
              value: row[key]
            })
          }

          // Trim string values
          if (typeof normalizedRow[mappedField] === 'string') {
            normalizedRow[mappedField] = normalizedRow[mappedField].trim()
          }
        }
      })

      // Copy any additional fields that aren't in the required columns
      Object.keys(row).forEach(key => {
        if (!Array.from(columnMap.values()).includes(key) && !(key in COLUMN_DISPLAY_MAPPING)) {
          normalizedRow[key] = row[key]
        }
      })

      // Direct mapping for position and district fields (case-insensitive)
      Object.keys(row).forEach(key => {
        const lowerKey = key.toLowerCase();
        if (lowerKey === 'position' && !normalizedRow.position) {
          normalizedRow.position = row[key];
          logger.debug('Direct mapped position field (case-insensitive)', LogCategory.IMPORT, {
            originalKey: key,
            value: row[key]
          });
        }
        if (lowerKey === 'district' && !normalizedRow.district) {
          normalizedRow.district = row[key];
          logger.debug('Direct mapped district field (case-insensitive)', LogCategory.IMPORT, {
            originalKey: key,
            value: row[key]
          });
        }
      });

      try {
        // Log the row being processed for debugging
        logger.debug('Processing row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow,
          position: normalizedRow.position,
          district: normalizedRow.district
        })

        // Validate required fields
        for (const field of REQUIRED_COLUMNS) {
          if (!normalizedRow[field]) {
            throw new Error(`Missing required field: ${field}`)
          }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(normalizedRow.email)) {
          throw new Error('Invalid email format')
        }

        // Check if email already exists
        const existingEmployee = await Employee.findOne({ email: normalizedRow.email })
        if (existingEmployee) {
          throw new Error(`Employee with email ${normalizedRow.email} already exists`)
        }

        // Format hire date
        let hireDate
        try {
          // If it's a date object from Excel
          if (typeof normalizedRow.hireDate === 'number') {
            // Excel dates are stored as days since 1900-01-01
            const excelEpoch = new Date(1900, 0, 1)
            hireDate = new Date(excelEpoch.getTime() + (normalizedRow.hireDate - 1) * 24 * 60 * 60 * 1000)
          } else {
            // Try to parse as string
            hireDate = new Date(normalizedRow.hireDate)
          }

          // Check if date is valid
          if (isNaN(hireDate.getTime())) {
            throw new Error('Invalid hire date format')
          }
        } catch (error) {
          throw new Error('Invalid hire date format')
        }

        // Validate and normalize employment type
        let employmentType = String(normalizedRow.employmentType).toLowerCase().trim()

        // Map common variations to valid values
        const employmentTypeMap: Record<string, string> = {
          'fulltime': 'full-time',
          'parttime': 'part-time',
          'full time': 'full-time',
          'part time': 'part-time',
          'temp': 'temporary',
          'internship': 'intern'
        }

        if (employmentTypeMap[employmentType]) {
          employmentType = employmentTypeMap[employmentType]
        }

        if (!VALID_EMPLOYMENT_TYPES.includes(employmentType)) {
          throw new Error(`Invalid employment type: "${normalizedRow.employmentType}". Must be one of: ${VALID_EMPLOYMENT_TYPES.join(', ')}`)
        }

        // Store the normalized value back
        normalizedRow.employmentType = employmentType

        // Validate and normalize employment status with default fallback
        let employmentStatus = 'active'; // Default to active

        if (normalizedRow.employmentStatus && normalizedRow.employmentStatus.toString().trim() !== '') {
          employmentStatus = String(normalizedRow.employmentStatus).toLowerCase().trim();
        }

        // Map common variations to valid values
        const employmentStatusMap: Record<string, string> = {
          'active': 'active',
          'inactive': 'inactive',
          'on leave': 'on-leave',
          'onleave': 'on-leave',
          'terminated': 'terminated'
        }

        if (employmentStatusMap[employmentStatus]) {
          employmentStatus = employmentStatusMap[employmentStatus]
        }

        if (!VALID_EMPLOYMENT_STATUSES.includes(employmentStatus)) {
          // If invalid status provided, default to active and log warning
          logger.warn('Invalid employment status provided, defaulting to active', LogCategory.IMPORT, {
            rowIndex: i + 1,
            providedStatus: normalizedRow.employmentStatus,
            defaultedTo: 'active'
          });
          employmentStatus = 'active';
        }

        // Store the normalized value back
        normalizedRow.employmentStatus = employmentStatus

        // Validate and normalize marital status if provided
        if (normalizedRow.maritalStatus) {
          let maritalStatus = String(normalizedRow.maritalStatus).toLowerCase().trim()

          // Map common variations to valid values
          const maritalStatusMap: Record<string, string> = {
            'single': 'single',
            'married': 'married',
            'divorced': 'divorced',
            'widowed': 'widowed',
            'widow': 'widowed',
            'separated': 'divorced' // Map separated to divorced as it's not a valid option
          }

          if (maritalStatusMap[maritalStatus]) {
            maritalStatus = maritalStatusMap[maritalStatus]
          }

          if (!VALID_MARITAL_STATUSES.includes(maritalStatus)) {
            throw new Error(`Invalid marital status: "${normalizedRow.maritalStatus}". Must be one of: ${VALID_MARITAL_STATUSES.join(', ')}`)
          }

          // Store the normalized value back
          normalizedRow.maritalStatus = maritalStatus
        }

        // Validate number of children if provided
        if (normalizedRow.numberOfChildren !== undefined && normalizedRow.numberOfChildren !== null) {
          const numChildren = Number(normalizedRow.numberOfChildren)
          if (isNaN(numChildren) || numChildren < 0) {
            throw new Error('Number of children must be a non-negative number')
          }
        }

        // Enhanced department validation with efficient lookup
        let departmentId = undefined;
        let actualDepartmentName = undefined;

        if (normalizedRow.department && normalizedRow.department.trim() !== '') {
          const departmentName = normalizedRow.department.trim();
          const lowerDepartmentName = departmentName.toLowerCase();

          // Check if department exists using efficient map lookup
          if (departmentNameMap.has(lowerDepartmentName)) {
            // Department exists - get the actual name and ID
            actualDepartmentName = departmentNameMap.get(lowerDepartmentName);
            departmentId = departmentIdMap.get(lowerDepartmentName);

            logger.debug('Department validation successful', LogCategory.IMPORT, {
              rowIndex: i + 1,
              inputDepartment: departmentName,
              actualDepartment: actualDepartmentName,
              departmentId: departmentId?.toString()
            });
          } else {
            // Department doesn't exist - this employee will be skipped
            const employeeName = `${normalizedRow.firstName} ${normalizedRow.lastName}`;

            logger.warn('Department mismatch - skipping employee', LogCategory.IMPORT, {
              rowIndex: i + 1,
              employeeName,
              department: departmentName,
              availableDepartments: departmentNames
            });

            // Add to department mismatches
            result.departmentMismatches.push({
              row: i + 1,
              employeeName,
              department: departmentName,
              availableDepartments: departmentNames
            });

            result.departmentMismatchCount++;
            result.skippedCount++;

            // Skip this employee
            continue;
          }
        }

        // Generate a unique employee ID
        const employeeId = await generateEmployeeId()

        // Create employee object
        // Always generate a unique employeeNumber to avoid duplicate key errors
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
        const uniqueEmployeeNumber = `EMP-${timestamp}-${random}-${i}`; // Add row index for extra uniqueness

        // Department ID and name are already set above during validation

        // Process next of kin fields
        const nextOfKin: Record<string, string> = {};
        if (normalizedRow.nextOfKinName) nextOfKin.name = normalizedRow.nextOfKinName;
        if (normalizedRow.nextOfKinRelationship) nextOfKin.relationship = normalizedRow.nextOfKinRelationship;
        if (normalizedRow.nextOfKinPhone) nextOfKin.phone = normalizedRow.nextOfKinPhone;
        if (normalizedRow.nextOfKinAddress) nextOfKin.address = normalizedRow.nextOfKinAddress;

        // Ensure position and district are properly set
        if (!normalizedRow.position) {
          logger.warn('Position field is missing or empty', LogCategory.IMPORT, {
            rowIndex: i + 1,
            email: normalizedRow.email
          })
        }

        if (!normalizedRow.district) {
          logger.warn('District field is missing or empty', LogCategory.IMPORT, {
            rowIndex: i + 1,
            email: normalizedRow.email
          })
        }

        // Create a complete employee data object with all fields explicitly defined
        const employeeData = {
          employeeId,
          employeeNumber: uniqueEmployeeNumber, // Always use a unique number, don't use employeeId
          firstName: normalizedRow.firstName,
          lastName: normalizedRow.lastName,
          email: normalizedRow.email,
          position: String(normalizedRow.position || ''), // Explicitly convert to string and ensure not undefined
          employmentType: normalizedRow.employmentType, // Already normalized
          employmentStatus: normalizedRow.employmentStatus, // Already normalized
          hireDate,
          // Optional fields
          phone: normalizedRow.phone || undefined,
          dateOfBirth: normalizedRow.dateOfBirth ? new Date(normalizedRow.dateOfBirth) : undefined,
          gender: normalizedRow.gender || undefined,
          // New fields
          maritalStatus: normalizedRow.maritalStatus || undefined, // Already normalized
          numberOfChildren: normalizedRow.numberOfChildren !== undefined ? Number(normalizedRow.numberOfChildren) : undefined,
          village: normalizedRow.village || undefined,
          traditionalAuthority: normalizedRow.traditionalAuthority || undefined,
          district: String(normalizedRow.district || ''), // Explicitly convert to string and ensure not undefined
          // Existing fields
          address: normalizedRow.address || undefined,
          city: normalizedRow.city || undefined,
          state: normalizedRow.state || undefined,
          postalCode: normalizedRow.postalCode || undefined,
          country: normalizedRow.country || undefined,
          nationalId: normalizedRow.nationalId || undefined,
          departmentId: departmentId || undefined, // Use the validated department ID
          department: actualDepartmentName || normalizedRow.department || undefined, // Store actual department name
          managerId: normalizedRow.managerId || undefined,
          salary: normalizedRow.salary ? Number(normalizedRow.salary) : undefined,
          bankName: normalizedRow.bankName || undefined,
          bankAccountNumber: normalizedRow.bankAccountNumber || undefined,
          // Next of kin - only add if at least one field is provided
          nextOfKin: Object.keys(nextOfKin).length > 0 ? nextOfKin : undefined,
          // Emergency contact
          emergencyContactName: normalizedRow.emergencyContactName || undefined,
          emergencyContactPhone: normalizedRow.emergencyContactPhone || undefined,
          emergencyContactRelationship: normalizedRow.emergencyContactRelationship || undefined,
          notes: normalizedRow.notes || undefined,
          addedBy: user._id,
          lastModifiedBy: user._id,
          // Default values for required fields
          isBlocked: false,
          accessibleBy: []
        }

        // Log employee data before creation
        logger.debug('Creating employee', LogCategory.IMPORT, {
          rowIndex: i + 1,
          employeeId,
          email: employeeData.email
        })

        // Log the employee data before creation for debugging
        logger.debug('Employee data before creation', LogCategory.IMPORT, {
          rowIndex: i + 1,
          employeeData: JSON.stringify(employeeData)
        })

        // Log the exact position and district values before creation
        logger.debug('Position and district values before creation', LogCategory.IMPORT, {
          rowIndex: i + 1,
          position: employeeData.position,
          district: employeeData.district,
          position_type: typeof employeeData.position,
          district_type: typeof employeeData.district
        });

        // Create employee with explicit position and district fields
        // Create a new object to ensure all fields are properly included
        const employeeToCreate = {
          ...employeeData,
          position: String(employeeData.position || ''), // Ensure position is a string
          district: String(employeeData.district || '') // Ensure district is a string
        };

        // Log the final object to be saved
        logger.debug('Final employee object to be saved', LogCategory.IMPORT, {
          rowIndex: i + 1,
          position: employeeToCreate.position,
          district: employeeToCreate.district,
          allFields: Object.keys(employeeToCreate).join(', ')
        });

        // Create employee - use a direct document creation approach
        const createdEmployee = new Employee(employeeToCreate);
        await createdEmployee.save();

        // Verify that all fields were saved correctly
        logger.debug('Created employee data', LogCategory.IMPORT, {
          rowIndex: i + 1,
          position: createdEmployee.position,
          district: createdEmployee.district,
          employeeId: createdEmployee.employeeId,
          toObject: JSON.stringify(createdEmployee.toObject())
        });

        // Double-check by finding the employee again with explicit field selection
        const savedEmployee = await Employee.findOne(
          { email: employeeData.email },
          'employeeId firstName lastName email position district' // Explicitly select fields we want to verify
        ).lean() as any; // Use 'any' type to avoid TypeScript errors

        logger.debug('Retrieved employee after save', LogCategory.IMPORT, {
          rowIndex: i + 1,
          position: savedEmployee?.position,
          district: savedEmployee?.district,
          employeeId: savedEmployee?.employeeId,
          allFields: savedEmployee ? Object.keys(savedEmployee).join(', ') : 'none'
        });

        // Verify position and district were saved correctly
        if (!savedEmployee?.position) {
          logger.warn('Position field was not saved correctly', LogCategory.IMPORT, {
            rowIndex: i + 1,
            email: employeeData.email,
            originalPosition: employeeData.position
          });
        }

        if (!savedEmployee?.district) {
          logger.warn('District field was not saved correctly', LogCategory.IMPORT, {
            rowIndex: i + 1,
            email: employeeData.email,
            originalDistrict: employeeData.district
          });
        }

        result.successCount++

        // Add to successful employees list
        const employeeName = `${normalizedRow.firstName} ${normalizedRow.lastName}`;
        result.successfulEmployees.push({
          row: i + 1,
          employeeName,
          department: actualDepartmentName || normalizedRow.department || 'No Department',
          employeeId
        });

        logger.info('Employee created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          employeeId,
          email: employeeData.email,
          department: actualDepartmentName || normalizedRow.department,
          userId: user.id
        })
      } catch (error: unknown) {
        result.errorCount++

        // Get employee name for error reporting
        const employeeName = normalizedRow?.firstName && normalizedRow?.lastName
          ? `${normalizedRow.firstName} ${normalizedRow.lastName}`
          : 'Unknown Employee';

        // Log the error
        logger.error('Error processing employee row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          employeeName,
          department: normalizedRow?.department,
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error',
          employeeName,
          department: normalizedRow?.department
        })
      }
    }

    // Log the final result with enhanced statistics
    logger.info('Employee bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      departmentMismatchCount: result.departmentMismatchCount,
      departmentMismatches: result.departmentMismatches.length,
      successfulEmployees: result.successfulEmployees.length,
      userId: user.id
    })

    return NextResponse.json(result)
  } catch (error: unknown) {
    logger.error('Error processing bulk import', LogCategory.IMPORT, {
      error: error instanceof Error ? error.message : 'An unknown error occurred',
      stack: error instanceof Error ? error.stack : undefined
    })

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An error occurred during bulk import',
      details: process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

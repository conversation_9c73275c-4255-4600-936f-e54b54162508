// app/api/auth/register/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { registerUser, generateUserToken, setTokenCookie } from '@/lib/backend/auth/auth';
import { UserRole, UserStatus } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

export async function POST(req: NextRequest) {
  logger.info('Registration API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Parse request body
    let email, password, firstName, lastName, role;
    try {
      const body = await req.json();
      email = body.email;
      password = body.password;
      firstName = body.firstName;
      lastName = body.lastName;
      role = body.role;

      logger.debug('Registration request parsed', LogCategory.API, {
        email: email || 'not provided',
        firstName: firstName || 'not provided',
        lastName: lastName || 'not provided',
        hasPassword: !!password,
        role: role || 'not provided (will use default)'
      });
    } catch (parseError: unknown) {
      logger.error('Failed to parse registration request body', LogCategory.API,
        parseError instanceof Error ? parseError : new Error(typeof parseError === 'object' && parseError !== null && 'message' in parseError ? String(parseError.message) : 'Unknown error')
      );

      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_REQUEST_FORMAT',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      logger.warn('Registration validation failed: Missing required fields', LogCategory.API, {
        hasEmail: !!email,
        hasPassword: !!password,
        hasFirstName: !!firstName,
        hasLastName: !!lastName
      });

      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_REQUIRED_FIELDS',
        'Missing required fields',
        'Please provide all required fields: first name, last name, email, and password.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method,
          missingFields: {
            email: !email,
            password: !password,
            firstName: !firstName,
            lastName: !lastName
          }
        },
        400,
        ErrorSeverity.MEDIUM,
        'Required fields validation failed',
        [
          'Ensure all required fields are filled out',
          'Check that the form data is being sent correctly'
        ]
      );
    }

    // Set all users to active by default
    const userStatus = UserStatus.ACTIVE;
    logger.debug('Determined user status for registration', LogCategory.API, {
      role: role || UserRole.EMPLOYEE,
      status: userStatus
    });

    // Register user
    logger.debug('Attempting to register user', LogCategory.API, {
      email,
      firstName,
      lastName,
      role: role || UserRole.EMPLOYEE,
      status: userStatus
    });

    const user = await registerUser(
      email,
      password,
      firstName,
      lastName,
      role || UserRole.EMPLOYEE,
      userStatus // All users are active by default
    );

    // Generate token
    logger.debug('Generating authentication token for new user', LogCategory.API, {
      userId: user.id
    });
    const token = generateUserToken(user);

    // Create response
    logger.debug('Creating registration response', LogCategory.API, {
      userId: user.id,
      email: user.email
    });

    const response = NextResponse.json(
      {
        status: 'success',
        message: 'User registered successfully',
        data: {
          user: {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status
          }
        }
      },
      { status: 201 }
    );

    // Set token cookie
    logger.debug('Setting authentication token cookie', LogCategory.API, {
      userId: user.id
    });
    setTokenCookie(response, token);

    logger.info('Registration successful', LogCategory.API, {
      userId: user.id,
      email: user.email,
      role: user.role,
      status: user.status
    });

    return response;
  } catch (error: unknown) {
    logger.error('Registration API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    const errorMessage = error instanceof Error ? error.message : 'An error occurred during registration';

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        return errorService.createApiResponse(
          ErrorType.CONFLICT,
          'EMAIL_ALREADY_EXISTS',
          'Email address already in use',
          'This email address is already registered. Please use a different email address or try logging in.',
          {
            endpoint: req.nextUrl.pathname,
            method: req.method,
            email: 'redacted',
            timestamp: new Date().toISOString()
          },
          409,
          ErrorSeverity.MEDIUM,
          'Duplicate email address in registration attempt',
          [
            'Use a different email address',
            'Check if you already have an account with this email',
            'Contact support if you believe this is an error'
          ],
          [
            {
              label: 'Try Different Email',
              action: 'retry',
              type: 'retry',
              variant: 'primary'
            }
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.DATABASE,
      'REGISTRATION_FAILED',
      'User registration failed',
      'Unable to create user account. This may be due to a temporary server issue.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: {
          errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try again in a few moments',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      [
        {
          label: 'Retry Registration',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        }
      ]
    );
  }
}


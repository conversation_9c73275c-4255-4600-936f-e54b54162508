import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';

export async function GET(req: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      status: 'success',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          department: user.department,
          position: user.position,
          avatar: user.avatar,
          lastLogin: user.lastLogin
        }
      }
    });
  } catch (error: unknown) {
    console.error('Error getting current user:', error);
    
    return NextResponse.json(
      { error: 'An error occurred while fetching user data' },
      { status: 500 }
    );
  }
}


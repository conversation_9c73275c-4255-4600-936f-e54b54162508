import { NextRequest, NextResponse } from 'next/server';
import { createPasswordResetToken } from '@/lib/backend/auth/auth';

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Please provide an email address' },
        { status: 400 }
      );
    }

    // Create password reset token
    const resetToken = await createPasswordResetToken(email);

    // In a real application, you would send an email with the reset link
    // For development, we'll just return the token in the response
    // DO NOT do this in production!
    return NextResponse.json({
      status: 'success',
      message: 'Password reset token generated successfully',
      data: {
        resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined
      }
    });
  } catch (error: unknown) {
    console.error('Error generating password reset token:', error);
    
    // Don't reveal if the email exists or not for security reasons
    return NextResponse.json({
      status: 'success',
      message: 'If a user with that email exists, a password reset link has been sent'
    });
  }
}


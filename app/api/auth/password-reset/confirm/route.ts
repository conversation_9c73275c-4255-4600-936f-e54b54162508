// app/api/auth/password-reset/confirm/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { resetPassword } from '@/lib/backend/auth/auth';

export async function POST(req: NextRequest) {
  try {
    const { token, newPassword } = await req.json();

    // Validate input
    if (!token || !newPassword) {
      return NextResponse.json(
        { error: 'Please provide token and new password' },
        { status: 400 }
      );
    }

    // Reset password
    await resetPassword(token, newPassword);

    return NextResponse.json({
      status: 'success',
      message: 'Password reset successfully'
    });
  } catch (error: unknown) {
    console.error('Error resetting password:', error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while resetting password' },
      { status: 500 }
    );
  }
}


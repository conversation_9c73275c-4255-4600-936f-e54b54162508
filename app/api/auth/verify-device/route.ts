// app/api/auth/verify-device/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { sessionService } from '@/lib/backend/services/auth/SessionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function POST(req: NextRequest) {
  logger.info('Device verification request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Device verification failed: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Parse request body
    let sessionId;
    try {
      const body = await req.json();
      sessionId = body.sessionId;

      logger.debug('Device verification request parsed', LogCategory.API, {
        sessionId: sessionId || 'not provided'
      });
    } catch (parseError: unknown) {
      logger.error('Failed to parse device verification request body', LogCategory.API,
        parseError instanceof Error ? parseError : new Error(typeof parseError === 'object' && parseError !== null && 'message' in parseError ? String(parseError.message) : 'Unknown error')
      );

      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    // Validate input
    if (!sessionId) {
      logger.warn('Device verification validation failed: Missing session ID', LogCategory.API);
      return NextResponse.json(
        { error: 'Please provide session ID' },
        { status: 400 }
      );
    }

    // Trust the device
    const success = await sessionService.trustDevice(sessionId);
    if (!success) {
      logger.warn('Device verification failed: Invalid session ID', LogCategory.API, { sessionId });
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      );
    }

    // Return success response
    logger.info('Device verification successful', LogCategory.API, {
      userId: user.id,
      sessionId
    });

    return NextResponse.json({
      status: 'success',
      message: 'Device verified successfully'
    });
  } catch (error: unknown) {
    logger.error('Device verification API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred during device verification' },
      { status: 500 }
    );
  }
}

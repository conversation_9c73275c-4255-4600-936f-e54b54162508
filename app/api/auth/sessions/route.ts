// app/api/auth/sessions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { sessionService } from '@/lib/backend/services/auth/SessionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * Get all active sessions for the current user
 */
export async function GET(req: NextRequest) {
  logger.info('User sessions API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);

    if (!user) {
      logger.warn('Sessions API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get active sessions
    const sessions = await sessionService.getUserActiveSessions((user._id as mongoose.Types.ObjectId).toString());

    // Get current session token
    const currentToken = req.cookies.get('token')?.value;
    const currentSession = sessions.find(session => session.token === currentToken);

    // Format sessions for response
    const formattedSessions = sessions.map(session => ({
      id: (session._id as mongoose.Types.ObjectId).toString(),
      deviceName: session.deviceName,
      deviceType: session.deviceType,
      browser: session.browser,
      operatingSystem: session.operatingSystem,
      ipAddress: session.ipAddress,
      location: session.location,
      isTrusted: session.isTrusted,
      isCurrentSession: session.token === currentToken,
      lastActive: session.lastActive,
      loginTime: session.loginTime
    }));

    logger.info('Sessions API: Retrieved user sessions', LogCategory.API, {
      userId: (user._id as mongoose.Types.ObjectId).toString(),
      sessionCount: sessions.length
    });

    return NextResponse.json({
      status: 'success',
      data: {
        sessions: formattedSessions,
        currentSessionId: currentSession ? (currentSession._id as mongoose.Types.ObjectId).toString() : undefined
      }
    });
  } catch (error: unknown) {
    logger.error('Sessions API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while fetching sessions' },
      { status: 500 }
    );
  }
}

/**
 * End a specific session or all other sessions
 */
export async function POST(req: NextRequest) {
  logger.info('End session API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);

    if (!user) {
      logger.warn('End session API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { action, sessionId } = body;

    // Get current session
    const currentToken = req.cookies.get('token')?.value;
    if (!currentToken) {
      logger.warn('End session API: No current session token', LogCategory.API);
      return NextResponse.json(
        { error: 'No active session' },
        { status: 400 }
      );
    }

    const currentSession = await sessionService.getSessionByToken(currentToken);
    if (!currentSession) {
      logger.warn('End session API: Current session not found', LogCategory.API);
      return NextResponse.json(
        { error: 'Current session not found' },
        { status: 400 }
      );
    }

    // Handle different actions
    if (action === 'end_all_other_sessions') {
      // End all other sessions
      const count = await sessionService.endAllUserSessions(
        (user._id as mongoose.Types.ObjectId).toString(),
        (currentSession._id as mongoose.Types.ObjectId).toString()
      );

      logger.info('End session API: Ended all other sessions', LogCategory.API, {
        userId: user.id,
        count
      });

      return NextResponse.json({
        status: 'success',
        message: `Ended ${count} other sessions`,
        data: { count }
      });
    } else if (action === 'end_session' && sessionId) {
      // Check if trying to end current session
      if (sessionId === (currentSession._id as mongoose.Types.ObjectId).toString()) {
        logger.warn('End session API: Cannot end current session', LogCategory.API);
        return NextResponse.json(
          { error: 'Cannot end current session. Use logout instead.' },
          { status: 400 }
        );
      }

      // Get the session to end
      const sessionToEnd = await sessionService.getUserActiveSessions((user._id as mongoose.Types.ObjectId).toString())
        .then(sessions => sessions.find(s => (s._id as mongoose.Types.ObjectId).toString() === sessionId));

      if (!sessionToEnd) {
        logger.warn('End session API: Session not found', LogCategory.API, { sessionId });
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        );
      }

      // End the session
      await sessionService.endSession(sessionToEnd.token);

      logger.info('End session API: Ended specific session', LogCategory.API, {
        userId: user.id,
        sessionId
      });

      return NextResponse.json({
        status: 'success',
        message: 'Session ended successfully'
      });
    } else {
      logger.warn('End session API: Invalid action', LogCategory.API, { action });
      return NextResponse.json(
        { error: 'Invalid action. Use "end_all_other_sessions" or "end_session"' },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    logger.error('End session API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while ending session' },
      { status: 500 }
    );
  }
}

// app/api/auth/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser, updateUserStatus, hasRole } from '@/lib/backend/auth/auth';
import { UserRole, UserStatus } from '@/types/user-roles';

export async function POST(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);

    if (!currentUser) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user has permission to update user status
    const adminRoles = [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER];
    if (!hasRole(currentUser, adminRoles)) {
      return NextResponse.json(
        { error: 'You do not have permission to update user status' },
        { status: 403 }
      );
    }

    const { userId, status } = await req.json();

    // Validate input
    if (!userId || !status) {
      return NextResponse.json(
        { error: 'Please provide userId and status' },
        { status: 400 }
      );
    }

    // Validate status
    if (!Object.values(UserStatus).includes(status as UserStatus)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update user status
    const user = await updateUserStatus(userId, status as UserStatus);

    return NextResponse.json({
      status: 'success',
      message: 'User status updated successfully',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status
        }
      }
    });
  } catch (error: unknown) {
    console.error('Error updating user status:', error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while updating user status' },
      { status: 500 }
    );
  }
}


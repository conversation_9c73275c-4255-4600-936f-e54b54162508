// app/api/auth/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { loginUser, setTokenCookie } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
// import { sessionService } from '@/lib/backend/services/auth/SessionService';

export async function POST(req: NextRequest) {
  logger.info('Login API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Parse request body
    let email, password;
    try {
      const body = await req.json();
      email = body.email;
      password = body.password;

      logger.debug('Login request parsed', LogCategory.API, {
        email: email || 'not provided',
        hasPassword: !!password
      });
    } catch (parseError: unknown) {
      logger.error('Failed to parse login request body', LogCategory.API,
        parseError instanceof Error ? parseError : new Error(typeof parseError === 'object' && parseError !== null && 'message' in parseError ? String(parseError.message) : 'Unknown error')
      );

      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    // Validate input
    if (!email || !password) {
      logger.warn('Login validation failed: Missing credentials', LogCategory.API, {
        hasEmail: !!email,
        hasPassword: !!password
      });

      return NextResponse.json(
        { error: 'Please provide email and password' },
        { status: 400 }
      );
    }

    // Login user
    logger.debug('Attempting to login user', LogCategory.API, { email });
    const { user, token, requiresDeviceVerification, sessionId } = await loginUser(email, password, req);

    // Create response
    logger.debug('Creating login response', LogCategory.API, {
      userId: user.id,
      email: user.email,
      requiresDeviceVerification
    });

    const response = NextResponse.json({
      status: 'default',
      message: 'Logged in successfully',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          avatar: user.avatar
        },
        requiresDeviceVerification,
        sessionId
      }
    });

    // Set token cookie
    logger.debug('Setting authentication token cookie', LogCategory.API, {
      userId: user.id
    });
    setTokenCookie(response, token);

    logger.info('Login successful', LogCategory.API, {
      userId: user.id,
      email: user.email,
      role: user.role
    });

    return response;
  } catch (error: unknown) {
    logger.error('Login API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    // Determine appropriate status code based on error
    let statusCode = 401; // Default to unauthorized
    let errorMessage = 'An error occurred during login';

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('provide') || error.message.includes('invalid format')) {
        statusCode = 400; // Bad request for validation errors
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}


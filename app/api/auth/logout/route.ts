// app/api/auth/logout/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { clearTokenCookie } from '@/lib/backend/auth/auth';
import { sessionService } from '@/lib/backend/services/auth/SessionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function POST(req: NextRequest) {
  logger.info('Logout API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get token from cookies
    const token = req.cookies.get('token')?.value;

    // End session if token exists
    if (token) {
      logger.debug('Ending user session', LogCategory.API);
      await sessionService.endSession(token);
    }

    // Create response
    const response = NextResponse.json({
      status: 'default',
      message: 'Logged out successfully'
    });

    // Clear token cookie
    logger.debug('Clearing authentication token cookie', LogCategory.API);
    clearTokenCookie(response);

    logger.info('Logout successful', LogCategory.API);

    return response;
  } catch (error: unknown) {
    logger.error('Logout API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    // Even if there's an error, we should still clear the cookie
    const response = NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred during logout' },
      { status: 500 }
    );

    clearTokenCookie(response);

    return response;
  }
}


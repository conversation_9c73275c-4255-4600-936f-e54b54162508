// app/api/auth/clear-all/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { clearTokenCookie } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import UserSession from '@/models/UserSession';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function POST(req: NextRequest) {
  logger.info('Clear all sessions API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Connect to database
    await connectToDatabase();

    // Deactivate all active sessions
    const result = await UserSession.updateMany(
      { isActive: true },
      { 
        isActive: false, 
        logoutTime: new Date() 
      }
    );

    logger.info('All sessions cleared', LogCategory.API, {
      sessionsCleared: result.modifiedCount
    });

    // Create response
    const response = NextResponse.json({
      status: 'success',
      message: `Cleared ${result.modifiedCount} active sessions`,
      sessionsCleared: result.modifiedCount
    });

    // Clear token cookie
    clearTokenCookie(response);

    return response;
  } catch (error: unknown) {
    logger.error('Clear all sessions API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    // Even if there's an error, we should still clear the cookie
    const response = NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while clearing sessions' },
      { status: 500 }
    );

    clearTokenCookie(response);

    return response;
  }
}

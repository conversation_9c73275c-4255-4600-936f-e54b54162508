// app/api/debug/pdf-test/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PdfTestService } from '@/lib/services/payroll/pdf-test-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/debug/pdf-test
 * Test PDF generation functionality
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    logger.info('PDF test endpoint called', LogCategory.API);

    const results = {
      basicPdfTest: await PdfTestService.testBasicPdfGeneration(),
      pdfGeneratorTest: await PdfTestService.testPdfGeneratorClass(),
      timestamp: new Date().toISOString()
    };

    logger.info('PDF tests completed', LogCategory.API, results);

    return NextResponse.json({
      success: true,
      message: 'PDF tests completed',
      data: results
    });

  } catch (error: unknown) {
    logger.error('Error in PDF test endpoint', LogCategory.API, error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'PDF test failed'
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import LoanApplication from '@/models/loan/LoanApplication';
import Employee from '@/models/Employee';
// import Department from '@/models/Department';

/**
 * GET /api/loans/applications/approvals
 * Get loan applications that need approval
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'pending';
    const departmentId = searchParams.get('departmentId');

    // Build query
    const query: Record<string, unknown> = {};
    
    if (status !== 'all') {
      query.status = status;
    }

    // Filter by department if specified
    if (departmentId) {
      // Get employees in department
      const employees = await Employee.find({ departmentId });
      const employeeIds = employees.map(emp => emp._id);
      
      query.employeeId = { $in: employeeIds };
    }

    // Get loan applications
    const loanApplications = await LoanApplication.find(query)
      .sort({ requestDate: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName employeeNumber position avatar departmentId')
      .populate('approvedBy', 'name')
      .lean();

    // Get total count
    const total = await LoanApplication.countDocuments(query);

    // Format response
    const formattedApplications = loanApplications.map(application => ({
      id: application._id,
      applicationId: application.applicationId,
      employee: {
        id: application.employeeId._id,
        name: `${application.employeeId.firstName} ${application.employeeId.lastName}`,
        position: application.employeeId.position,
        avatar: application.employeeId.avatar
      },
      amount: application.amount,
      purpose: application.purpose,
      requestDate: application.requestDate,
      status: application.status,
      approvedAmount: application.approvedAmount,
      approvedBy: application.approvedBy ? {
        id: application.approvedBy._id,
        name: application.approvedBy.name
      } : undefined,
      approvalDate: application.approvalDate,
      rejectionReason: application.rejectionReason,
      interestRate: application.interestRate,
      termInMonths: application.termInMonths,
      startDate: application.startDate,
      endDate: application.endDate,
      paymentFrequency: application.paymentFrequency,
      notes: application.notes,
      attachments: application.attachments
    }));

    return NextResponse.json({
      data: formattedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting loan applications for approval', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

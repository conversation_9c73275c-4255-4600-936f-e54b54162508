import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { loanService } from '@/services/loan/LoanService';
import LoanApplication from '@/models/loan/LoanApplication';
import { accountingService } from '@/services/accounting/AccountingService';

/**
 * POST /api/loans/applications/[id]/approve
 * Approve a loan application
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.approvedAmount || !body.interestRate || !body.termInMonths || !body.startDate || !body.paymentFrequency) {
      return NextResponse.json(
        { error: 'Missing required fields: approvedAmount, interestRate, termInMonths, startDate, paymentFrequency' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get loan application
    const loanApplication = await LoanApplication.findById(id)
      .populate('employeeId', 'firstName lastName');

    if (!loanApplication) {
      return NextResponse.json(
        { error: 'Loan application not found' },
        { status: 404 }
      );
    }

    // Check if loan application is already approved or rejected
    if (loanApplication.status !== 'pending') {
      return NextResponse.json(
        { error: `Loan application is already ${loanApplication.status}` },
        { status: 400 }
      );
    }

    // Update loan application status
    const updatedLoanApplication = await loanService.updateLoanApplicationStatus(
      id,
      'approved',
      body,
      user.id
    );

    // Create accounting entry for loan disbursement
    try {
      // Get the created loan
      const loan = await updatedLoanApplication.populate('loanId');

      // Create accounting entry
      await accountingService.createJournalEntry({
        date: new Date(),
        reference: `LOAN-${(loan as any).loanId?.loanId}`,
        description: `Loan disbursement for ${(loanApplication as any).employeeId?.firstName} ${(loanApplication as any).employeeId?.lastName}`,
        entries: [
          {
            accountId: process.env.LOANS_RECEIVABLE_ACCOUNT_ID || '12000', // Loans Receivable
            debit: body.approvedAmount,
            credit: 0,
            description: 'Employee loan receivable',
            metadata: {
              employeeId: (loanApplication as any).employeeId?._id,
              loanId: (loan as any).loanId?._id,
              loanApplicationId: loanApplication._id
            }
          },
          {
            accountId: process.env.CASH_ACCOUNT_ID || '10100', // Cash
            debit: 0,
            credit: body.approvedAmount,
            description: 'Cash disbursement for employee loan',
            metadata: {
              employeeId: (loanApplication as any).employeeId?._id,
              loanId: (loan as any).loanId?._id,
              loanApplicationId: loanApplication._id
            }
          }
        ],
        createdBy: user.id,
        status: 'posted',
        postingDate: new Date()
      });

      // Update loan with disbursement information
      await (loan as any).loanId?.updateOne({
        disbursedAmount: body.approvedAmount,
        disbursementDate: new Date(),
        disbursementMethod: 'bank_transfer',
        disbursementReference: `LOAN-${(loan as any).loanId?.loanId}`,
        status: 'active'
      });
    } catch (error) {
      logger.error('Error creating accounting entry for loan disbursement', LogCategory.ACCOUNTING, error);
      // Don't fail the request if accounting entry fails
    }

    return NextResponse.json({
      success: true,
      message: 'Loan application approved successfully',
      data: updatedLoanApplication
    });
  } catch (error: unknown) {
    logger.error('Error approving loan application', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { loanService } from '@/services/loan/LoanService';
import LoanApplication from '@/models/loan/LoanApplication';

/**
 * POST /api/loans/applications/[id]/reject
 * Reject a loan application
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.reason) {
      return NextResponse.json(
        { error: 'Missing required field: reason' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get loan application
    const loanApplication = await LoanApplication.findById(id);

    if (!loanApplication) {
      return NextResponse.json(
        { error: 'Loan application not found' },
        { status: 404 }
      );
    }

    // Check if loan application is already approved or rejected
    if (loanApplication.status !== 'pending') {
      return NextResponse.json(
        { error: `Loan application is already ${loanApplication.status}` },
        { status: 400 }
      );
    }

    // Update loan application status
    const updatedLoanApplication = await loanService.updateLoanApplicationStatus(
      id,
      'rejected',
      { rejectionReason: body.reason },
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Loan application rejected successfully',
      data: updatedLoanApplication
    });
  } catch (error: unknown) {
    logger.error('Error rejecting loan application', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

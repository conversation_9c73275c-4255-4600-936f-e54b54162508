import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
// import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { loanService } from '@/services/loan/LoanService';
import LoanApplication from '@/models/loan/LoanApplication';
// import Employee from '@/models/Employee';

/**
 * GET /api/loans/applications
 * Get loan applications for the current user
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());

    // Get employee ID from session
    const employeeId = user.id;

    // Build query
    const query: Record<string, unknown> = {
      employeeId,
      requestDate: {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1)
      }
    };

    if (status && status !== 'all') {
      query.status = status;
    }

    // Get loan applications
    const loanApplications = await LoanApplication.find(query)
      .sort({ requestDate: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('approvedBy', 'name')
      .lean();

    // Get total count
    const total = await LoanApplication.countDocuments(query);

    return NextResponse.json({
      data: loanApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting loan applications', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/loans/applications
 * Create a new loan application
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.amount || !body.purpose || !body.interestRate || !body.termInMonths || !body.startDate || !body.paymentFrequency) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, purpose, interestRate, termInMonths, startDate, paymentFrequency' },
        { status: 400 }
      );
    }

    // Set employee ID to current user
    body.employeeId = user.id;

    // Create loan application
    const loanApplication = await loanService.createLoanApplication(body, user.id);

    return NextResponse.json(
      {
        success: true,
        message: 'Loan application submitted successfully',
        data: loanApplication
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating loan application', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

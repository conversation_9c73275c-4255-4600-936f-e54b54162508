import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { loanService } from '@/services/loan/LoanService';
import LoanRepayment from '@/models/loan/LoanRepayment';
// import Loan from '@/models/loan/Loan';
import { accountingService } from '@/services/accounting/AccountingService';

/**
 * POST /api/loans/repayments/[id]/record
 * Record a loan repayment
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.amount || !body.paymentMethod || !body.paymentReference) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, paymentMethod, paymentReference' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get repayment
    const repayment = await LoanRepayment.findById(id)
      .populate({
        path: 'loanId',
        populate: {
          path: 'employeeId',
          select: 'firstName lastName'
        }
      });

    if (!repayment) {
      return NextResponse.json(
        { error: 'Loan repayment not found' },
        { status: 404 }
      );
    }

    // Check if repayment is already paid
    if (repayment.status === 'paid') {
      return NextResponse.json(
        { error: 'Loan repayment is already paid' },
        { status: 400 }
      );
    }

    // Record repayment
    const updatedRepayment = await loanService.recordRepayment(
      repayment.loanId._id.toString(),
      repayment.paymentNumber,
      body.amount,
      body.paymentMethod,
      body.paymentReference,
      user.id
    );

    // Create accounting entry for loan repayment
    try {
      // Calculate principal and interest amounts
      const principalAmount = Math.min(body.amount, repayment.principalAmount);
      const interestAmount = Math.max(0, body.amount - principalAmount);

      // Create accounting entry
      await accountingService.createJournalEntry({
        date: new Date(),
        reference: `LOAN-REPAY-${repayment.loanId.loanId}-${repayment.paymentNumber}`,
        description: `Loan repayment for ${repayment.loanId.employeeId.firstName} ${repayment.loanId.employeeId.lastName}`,
        entries: [
          {
            accountId: process.env.CASH_ACCOUNT_ID || '10100', // Cash
            debit: body.amount,
            credit: 0,
            description: 'Cash receipt for loan repayment',
            metadata: {
              employeeId: repayment.employeeId,
              loanId: repayment.loanId._id,
              repaymentId: repayment._id,
              paymentNumber: repayment.paymentNumber
            }
          },
          {
            accountId: process.env.LOANS_RECEIVABLE_ACCOUNT_ID || '12000', // Loans Receivable
            debit: 0,
            credit: principalAmount,
            description: 'Principal portion of loan repayment',
            metadata: {
              employeeId: repayment.employeeId,
              loanId: repayment.loanId._id,
              repaymentId: repayment._id,
              paymentNumber: repayment.paymentNumber
            }
          }
        ],
        createdBy: user.id,
        status: 'posted',
        postingDate: new Date()
      });

      // If there's interest, add an interest income entry
      if (interestAmount > 0) {
        await accountingService.createJournalEntry({
          date: new Date(),
          reference: `LOAN-INT-${repayment.loanId.loanId}-${repayment.paymentNumber}`,
          description: `Loan interest income for ${repayment.loanId.employeeId.firstName} ${repayment.loanId.employeeId.lastName}`,
          entries: [
            {
              accountId: process.env.INTEREST_INCOME_ACCOUNT_ID || '40100', // Interest Income
              debit: 0,
              credit: interestAmount,
              description: 'Interest portion of loan repayment',
              metadata: {
                employeeId: repayment.employeeId,
                loanId: repayment.loanId._id,
                repaymentId: repayment._id,
                paymentNumber: repayment.paymentNumber
              }
            }
          ],
          createdBy: user.id,
          status: 'posted',
          postingDate: new Date()
        });
      }
    } catch (error) {
      logger.error('Error creating accounting entry for loan repayment', LogCategory.ACCOUNTING, error);
      // Don't fail the request if accounting entry fails
    }

    return NextResponse.json({
      success: true,
      message: 'Loan repayment recorded successfully',
      data: updatedRepayment
    });
  } catch (error: unknown) {
    logger.error('Error recording loan repayment', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

//  app/api/loans/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Loan from '@/models/loan/Loan';

/**
 * GET /api/loans/[id]
 * Get loan details
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get loan
    const loan = await Loan.findById(id)
      .populate('employeeId', 'firstName lastName employeeNumber position')
      .populate('applicationId')
      .populate('createdBy', 'name')
      .lean();

    if (!loan) {
      return NextResponse.json(
        { error: 'Loan not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to view this loan
    const isOwnLoan = (loan as any).employeeId?._id?.toString() === user.id;
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!isOwnLoan && !hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    return NextResponse.json(loan);
  } catch (error: unknown) {
    logger.error('Error getting loan details', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

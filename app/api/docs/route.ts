import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';



/**
 * GET handler for documentation content
 * @param request - Next.js request
 * @returns Next.js response with markdown content
 */
export async function GET(request: NextRequest) {
  try {
    // Get the section from query params
    const searchParams = request.nextUrl.searchParams;
    const section = searchParams.get('section') || 'getting-started';

    // Validate section name to prevent directory traversal
    if (!/^[a-z0-9-]+$/.test(section)) {
      return NextResponse.json(
        { error: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Define the docs directory path
    const docsDir = path.join(process.cwd(), 'project_guides', 'docs');

    // Try to read the requested markdown file
    let filePath = path.join(docsDir, `${section}.md`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      logger.warn(`Documentation file not found: ${filePath}`, LogCategory.API);
      filePath = path.join(docsDir, 'not-found.md');

      // If even the not-found file doesn't exist, return a default message
      if (!fs.existsSync(filePath)) {
        return NextResponse.json(
          {
            content: '# Documentation Not Found\n\nThe requested documentation section could not be found.'
          },
          { status: 404 }
        );
      }
    }

    // Read the markdown file
    const content = fs.readFileSync(filePath, 'utf8');

    // Return the content
    return NextResponse.json({ content });
  } catch (error) {
    logger.error('Error serving documentation', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to load documentation' },
      { status: 500 }
    );
  }
}

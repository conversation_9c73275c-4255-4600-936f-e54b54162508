// app/api/documents/employee/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentService } from '@/services/document/DocumentService';
import { documentUploadService } from '@/services/document/DocumentUploadService';
import { parseFormData } from '@/lib/backend/utils/form-parser';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/documents/employee
 * Get employee documents
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const employeeId = searchParams.get('employeeId') || undefined;
    const categoryId = searchParams.get('categoryId') || undefined;
    const isActive = searchParams.get('isActive') === 'true';
    const isExpired = searchParams.get('isExpired') === 'true';
    const isVerified = searchParams.get('isVerified') === 'true';
    const search = searchParams.get('search') || undefined;
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Check if user has permission to view other employees' documents
    if (employeeId && employeeId !== user.id) {
      const hasPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Forbidden' },
          { status: 403 }
        );
      }
    }

    // Get documents
    const result = await documentService.getEmployeeDocuments({
      employeeId: employeeId || user.id,
      categoryId,
      isActive,
      isExpired,
      isVerified,
      search,
      page,
      limit,
      sortField,
      sortOrder: sortOrder as 'asc' | 'desc'
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting employee documents', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/documents/employee
 * Upload a new employee document
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Parse form data
    const formData = await req.formData();
    const { fields, files } = await parseFormData(formData);

    // Get file
    const file = files.file;
    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!fields.title || !fields.categoryId) {
      return NextResponse.json(
        { error: 'Missing required fields: title, categoryId' },
        { status: 400 }
      );
    }

    // Check if user has permission to upload documents for other employees
    const employeeId = fields.employeeId || user.id;
    if (employeeId !== user.id) {
      const hasPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Forbidden' },
          { status: 403 }
        );
      }
    }

    // Upload file
    const uploadResult = await documentUploadService.uploadDocument(
      file.data,
      file.name,
      fields.categoryId
    );

    // Create document
    const document = await documentService.createEmployeeDocument({
      title: fields.title,
      documentNumber: fields.documentNumber,
      employeeId,
      categoryId: new mongoose.Types.ObjectId(fields.categoryId),
      description: fields.description,
      filePath: uploadResult.filePath,
      fileName: uploadResult.fileName,
      fileType: uploadResult.fileType,
      fileSize: uploadResult.fileSize,
      issuedDate: fields.issuedDate ? new Date(fields.issuedDate) : undefined,
      expiryDate: fields.expiryDate ? new Date(fields.expiryDate) : undefined,
      tags: fields.tags ? fields.tags.split(',').map(tag => tag.trim()) : undefined,
      isPublic: fields.isPublic === 'true',
      createdBy: user.id
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Document uploaded successfully',
        data: document
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error uploading employee document', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

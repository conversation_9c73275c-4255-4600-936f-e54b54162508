// app/api/project/report/template/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ReportingService } from '@/lib/backend/services/project/ReportingService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const reportingService = new ReportingService();

/**
 * GET /api/project/report/template
 * Get report templates with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const type = searchParams.get('type');
    const isSystem = searchParams.get('isSystem');
    const isActive = searchParams.get('isActive');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build filter
    // Use a more specific type for MongoDB queries
    interface TemplateFilter {
      category?: string;
      type?: string;
      isSystem?: boolean;
      isActive?: boolean;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: TemplateFilter = {};

    // Add category filter
    if (category) {
      filter.category = category;
    }

    // Add type filter
    if (type) {
      filter.type = type;
    }

    // Add isSystem filter
    if (isSystem !== null) {
      filter.isSystem = isSystem === 'true';
    }

    // Add isActive filter
    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get templates
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await reportingService.getReportTemplates(filter as any, {
      sort,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/report/template', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/report/template
 * Create a new report template
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create template
    const template = await reportingService.createReportTemplate(body);

    return NextResponse.json(template, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/report/template', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

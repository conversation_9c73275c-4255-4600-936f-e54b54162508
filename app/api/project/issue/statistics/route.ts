// app/api/project/issue/statistics/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { IssueService } from '@/lib/backend/services/project/IssueService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const issueService = new IssueService();

/**
 * GET /api/project/issue/statistics
 * Get issue statistics for a project
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');

    // Project ID is required
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Generate statistics
    const statistics = await issueService.generateIssueStatistics(projectId);

    return NextResponse.json(statistics);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/issue/statistics', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

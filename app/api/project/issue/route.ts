// app/api/project/issue/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IssueService } from '@/lib/backend/services/project/IssueService';

import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const issueService = new IssueService();

/**
 * GET /api/project/issue
 * Get issues with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const priority = searchParams.get('priority');
    const projectId = searchParams.get('project');
    const assignedTo = searchParams.get('assignedTo');
    const reportedBy = searchParams.get('reportedBy');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single issue
    if (id) {
      const issue = await issueService.getIssueById(id);

      if (!issue) {
        return NextResponse.json(
          { error: 'Issue not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(issue);
    }

    // If projectId is provided, get issues for a project
    if (projectId) {
      const result = await issueService.getIssuesByProject(
        projectId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          type: type ? (type.includes(',') ? type.split(',') as any : type) : undefined,
          priority: priority ? (priority.includes(',') ? priority.split(',') as any : priority) : undefined,
          assignedTo: assignedTo || undefined,
          reportedBy: reportedBy || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface IssueFilter {
      isDeleted: boolean;
      status?: string | { $in: string[] };
      type?: string | { $in: string[] };
      priority?: string | { $in: string[] };
      assignedTo?: string;
      reportedBy?: string;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: IssueFilter = { isDeleted: false };

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add type filter
    if (type) {
      (filter as any).type = type.includes(',') ? { $in: type.split(',') } : type;
    }

    // Add priority filter
    if (priority) {
      (filter as any).priority = priority.includes(',') ? { $in: priority.split(',') } : priority;
    }

    // Add assignedTo filter
    if (assignedTo) {
      filter.assignedTo = assignedTo;
    }

    // Add reportedBy filter
    if (reportedBy) {
      filter.reportedBy = reportedBy;
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get issues
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await issueService.getIssues(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'reportedBy', 'assignedTo', 'createdBy', 'updatedBy', 'closedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/issue', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/issue
 * Create a new issue
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by and reported by
    body.createdBy = user.id;
    body.reportedBy = user.id;

    // Create issue
    const issue = await issueService.createIssue(body);

    return NextResponse.json(issue, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/issue', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

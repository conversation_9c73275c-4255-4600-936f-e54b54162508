// app/api/project/issue/[id]/history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IssueService } from '@/lib/backend/services/project/IssueService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const issueService = new IssueService();

/**
 * GET /api/project/issue/[id]/history
 * Get history for an issue
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const sortField = searchParams.get('sortField') || 'changedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Resolve the params promise
    const { id } = await context.params;

    // Get history
    const history = await issueService.getHistory(
      id,
      {
        sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
        page,
        limit
      }
    );

    return NextResponse.json(history);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/issue/[id]/history', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

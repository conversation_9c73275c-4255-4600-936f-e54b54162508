// app/api/project/issue/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IssueService } from '@/lib/backend/services/project/IssueService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const issueService = new IssueService();

/**
 * GET /api/project/issue/[id]
 * Get an issue by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get issue
    const issue = await issueService.getIssueById(id);

    if (!issue) {
      return NextResponse.json(
        { error: 'Issue not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(issue);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/issue/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/issue/[id]
 * Update an issue
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get issue
    const issue = await issueService.getIssueById(id);

    if (!issue) {
      return NextResponse.json(
        { error: 'Issue not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isReporter = issue.reportedBy.toString() === user.id;
    const isAssignee = issue.assignedTo && issue.assignedTo.toString() === user.id;
    const hasPermission = isReporter || isAssignee || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update issue
    const updatedIssue = await issueService.updateById(id, body);

    return NextResponse.json(updatedIssue);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/issue/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/issue/[id]
 * Delete an issue (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get issue
    const issue = await issueService.getIssueById(id);

    if (!issue) {
      return NextResponse.json(
        { error: 'Issue not found' },
        { status: 404 }
      );
    }

    // Soft delete issue
    const result = await issueService.updateById(id, {
      isDeleted: true,
      updatedBy: user.id
    });

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/issue/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/issue/[id]
 * Perform operations on an issue (status, assignment, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get issue
    const issue = await issueService.getIssueById(id);

    if (!issue) {
      return NextResponse.json(
        { error: 'Issue not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        // Check permissions for status update
        const isAssignee = issue.assignedTo && issue.assignedTo.toString() === user.id;
        const hasStatusPermission = isAssignee || hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.TEAM_LEADER
        ]);

        if (!hasStatusPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update status' },
            { status: 403 }
          );
        }

        result = await issueService.updateStatus(
          id,
          body.status,
          user.id,
          body.resolution,
          body.comment
        );
        break;

      case 'assign':
        // Check permissions for assignment
        const hasAssignPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.TEAM_LEADER
        ]);

        if (!hasAssignPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to assign issue' },
            { status: 403 }
          );
        }

        result = await issueService.assignIssue(
          id,
          body.assigneeId,
          user.id,
          body.comment
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/issue/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/analytics/overview/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ReportingService } from '@/lib/backend/services/project/ReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const reportingService = new ReportingService();

/**
 * GET /api/project/analytics/overview/[id]
 * Get project overview analytics
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Generate project overview
    const overview = await reportingService.generateProjectOverview(id);

    return NextResponse.json(overview);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/analytics/overview/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

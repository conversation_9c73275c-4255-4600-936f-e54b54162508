// app/api/project/export/[type]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ExportService } from '@/lib/backend/services/project/ExportService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const exportService = new ExportService();

/**
 * GET /api/project/export/[type]
 * Export project data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ type: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { type } = await params;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');
    const reportId = searchParams.get('report');
    const format = (searchParams.get('format') || 'csv') as 'csv' | 'excel';
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Validate parameters
    if (!projectId && !reportId && type !== 'portfolio') {
      return NextResponse.json(
        { error: 'Project ID or Report ID is required' },
        { status: 400 }
      );
    }

    let result: string | Buffer;
    let fileName: string;
    let contentType: string;

    // Export data based on type
    switch (type) {
      case 'project':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportProject(projectId, format, includeDetails);
        fileName = `project_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'tasks':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportTasks(projectId, format);
        fileName = `tasks_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'resources':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportResources(projectId, format);
        fileName = `resources_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'time':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportTimeEntries(projectId, format);
        fileName = `time_entries_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'budget':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportBudget(projectId, format);
        fileName = `budget_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'risks':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportRisks(projectId, format);
        fileName = `risks_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'issues':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportIssues(projectId, format);
        fileName = `issues_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'documents':
        if (!projectId) {
          return NextResponse.json(
            { error: 'Project ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportDocuments(projectId, format);
        fileName = `documents_${projectId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'report':
        if (!reportId) {
          return NextResponse.json(
            { error: 'Report ID is required' },
            { status: 400 }
          );
        }
        result = await exportService.exportReport(reportId, format);
        fileName = `report_${reportId}_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'portfolio':
        // Get filter parameters
        const status = searchParams.get('status');
        const clientId = searchParams.get('client');
        const managerId = searchParams.get('manager');

        // Build filter
        interface PortfolioFilter {
          status?: string | { $in: string[] };
          client?: string;
          manager?: string;
          [key: string]: any; // Allow additional properties for MongoDB queries
        }

        const filter: PortfolioFilter = {};

        // Add status filter
        if (status) {
          (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
        }

        // Add client filter
        if (clientId) {
          filter.client = clientId;
        }

        // Add manager filter
        if (managerId) {
          filter.manager = managerId;
        }

        // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
        result = await exportService.exportPortfolio(filter as any, format);
        fileName = `portfolio_${new Date().toISOString().split('T')[0]}`;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid export type' },
          { status: 400 }
        );
    }

    // Set content type based on format
    if (format === 'csv') {
      contentType = 'text/csv';
      fileName += '.csv';
    } else {
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileName += '.xlsx';
    }

    // Create response with appropriate headers
    const headers = {
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Cache-Control': 'no-cache'
    };

    // Handle different result types
    if (result instanceof Buffer) {
      // For Buffer results, create a Blob with the appropriate content type
      const blob = new Blob([new Uint8Array(result)], { type: contentType });
      return new Response(blob, { headers });
    } else if (typeof result === 'string') {
      // For string results
      return new Response(result, { headers });
    } else {
      // Fallback for any other type (shouldn't happen)
      return NextResponse.json(
        { error: 'Unexpected result type' },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/export/[type]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

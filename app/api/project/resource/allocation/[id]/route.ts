// app/api/project/resource/allocation/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ResourceAllocationService } from '@/lib/backend/services/project/ResourceAllocationService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const resourceAllocationService = new ResourceAllocationService();

/**
 * GET /api/project/resource/allocation/[id]
 * Get a resource allocation by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get allocation
    const allocation = await resourceAllocationService.getAllocationById(id);

    if (!allocation) {
      return NextResponse.json(
        { error: 'Resource allocation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(allocation);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/resource/allocation/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/resource/allocation/[id]
 * Update a resource allocation
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Check availability if requested and dates are changing
    if (body.checkAvailability && (body.startDate || body.endDate)) {
      const allocation = await resourceAllocationService.getAllocationById(id);

      if (!allocation) {
        return NextResponse.json(
          { error: 'Resource allocation not found' },
          { status: 404 }
        );
      }

      const startDate = body.startDate ? new Date(body.startDate) : allocation.startDate;
      const endDate = body.endDate ? new Date(body.endDate) : allocation.endDate;

      const availability = await resourceAllocationService.checkAvailability(
        body.resource || allocation.resource.toString(),
        startDate,
        endDate,
        id
      );

      if (!availability.available) {
        return NextResponse.json(
          {
            error: 'Resource is not available for the requested period',
            availability
          },
          { status: 409 }
        );
      }
    }

    // Update allocation
    const allocation = await resourceAllocationService.updateById(id, body);

    if (!allocation) {
      return NextResponse.json(
        { error: 'Resource allocation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(allocation);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/resource/allocation/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/resource/allocation/[id]
 * Delete a resource allocation
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete allocation
    const result = await resourceAllocationService.deleteById(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Resource allocation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/resource/allocation/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/resource/allocation/[id]
 * Perform operations on a resource allocation (status, utilized hours, tasks, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        result = await resourceAllocationService.updateStatus(
          id,
          body.status,
          user.id
        );
        break;

      case 'utilizedHours':
        result = await resourceAllocationService.updateUtilizedHours(
          id,
          body.utilizedHours,
          user.id
        );
        break;

      case 'addTask':
        result = await resourceAllocationService.addTask(
          id,
          body.taskId,
          user.id
        );
        break;

      case 'removeTask':
        result = await resourceAllocationService.removeTask(
          id,
          body.taskId,
          user.id
        );
        break;

      case 'checkAvailability':
        const allocation = await resourceAllocationService.getAllocationById(id);

        if (!allocation) {
          return NextResponse.json(
            { error: 'Resource allocation not found' },
            { status: 404 }
          );
        }

        const startDate = body.startDate ? new Date(body.startDate) : allocation.startDate;
        const endDate = body.endDate ? new Date(body.endDate) : allocation.endDate;

        const availability = await resourceAllocationService.checkAvailability(
          allocation.resource.toString(),
          startDate,
          endDate,
          id
        );

        return NextResponse.json(availability);

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    if (!result) {
      return NextResponse.json(
        { error: 'Resource allocation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/resource/allocation/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

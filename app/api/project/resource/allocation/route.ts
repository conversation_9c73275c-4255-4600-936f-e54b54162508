// app/api/project/resource/allocation/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ResourceAllocationService } from '@/lib/backend/services/project/ResourceAllocationService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const resourceAllocationService = new ResourceAllocationService();

/**
 * GET /api/project/resource/allocation
 * Get resource allocations with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const projectId = searchParams.get('project');
    const resourceId = searchParams.get('resource');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'startDate';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const id = searchParams.get('id');

    // If ID is provided, get a single allocation
    if (id) {
      const allocation = await resourceAllocationService.getAllocationById(id);

      if (!allocation) {
        return NextResponse.json(
          { error: 'Resource allocation not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(allocation);
    }

    // If projectId is provided, get allocations for a project
    if (projectId) {
      const result = await resourceAllocationService.getAllocationsByProject(
        projectId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If resourceId is provided, get allocations for a resource
    if (resourceId) {
      const result = await resourceAllocationService.getAllocationsByResource(
        resourceId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface AllocationFilter {
      status?: string | { $in: string[] };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: AllocationFilter = {};

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get allocations
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await resourceAllocationService.getAllocations(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'resource', 'tasks', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/resource/allocation', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/resource/allocation
 * Create a new resource allocation
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Check availability if requested
    if (body.checkAvailability) {
      const availability = await resourceAllocationService.checkAvailability(
        body.resource,
        new Date(body.startDate),
        new Date(body.endDate)
      );

      if (!availability.available) {
        return NextResponse.json(
          {
            error: 'Resource is not available for the requested period',
            availability
          },
          { status: 409 }
        );
      }
    }

    // Create allocation
    const allocation = await resourceAllocationService.createAllocation(body);

    return NextResponse.json(allocation, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/resource/allocation', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/resource/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ResourceService } from '@/lib/backend/services/project/ResourceService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const resourceService = new ResourceService();

/**
 * GET /api/project/resource/[id]
 * Get a resource by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get resource
    const resource = await resourceService.getResourceById(id);

    if (!resource) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(resource);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/resource/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/resource/[id]
 * Update a resource
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update resource
    const resource = await resourceService.updateById(id, body);

    if (!resource) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(resource);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/resource/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/resource/[id]
 * Delete a resource
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete resource
    const result = await resourceService.deleteById(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/resource/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/resource/[id]
 * Perform operations on a resource (status, skills, availability, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        result = await resourceService.updateStatus(
          id,
          body.status,
          user.id
        );
        break;

      case 'addSkill':
        result = await resourceService.addSkill(
          id,
          body.skill,
          user.id
        );
        break;

      case 'removeSkill':
        result = await resourceService.removeSkill(
          id,
          body.skillName,
          user.id
        );
        break;

      case 'addAvailability':
        result = await resourceService.addAvailability(
          id,
          body.availability,
          user.id
        );
        break;

      case 'calculateUtilization':
        result = await resourceService.calculateUtilization(
          id,
          new Date(body.startDate),
          new Date(body.endDate)
        );
        return NextResponse.json(result);

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    if (!result) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/resource/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/resource/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ResourceService } from '@/lib/backend/services/project/ResourceService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const resourceService = new ResourceService();

/**
 * GET /api/project/resource
 * Get resources with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const departmentId = searchParams.get('department');
    const skill = searchParams.get('skill');
    const proficiency = searchParams.get('proficiency');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single resource
    if (id) {
      const resource = await resourceService.getResourceById(id);

      if (!resource) {
        return NextResponse.json(
          { error: 'Resource not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(resource);
    }

    // If departmentId is provided, get resources for a department
    if (departmentId) {
      const result = await resourceService.getResourcesByDepartment(
        departmentId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If skill is provided, get resources with that skill
    if (skill) {
      const result = await resourceService.getResourcesBySkill(
        skill,
        {
          proficiency: proficiency ? (proficiency.includes(',') ? proficiency.split(',') as any : proficiency) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface ResourceFilter {
      status?: string | { $in: string[] };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: ResourceFilter = {};

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get resources
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await resourceService.getResources(filter as any, {
      sort,
      page,
      limit,
      populate: ['user', 'department', 'allocations', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/resource', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/resource
 * Create a new resource
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create resource
    const resource = await resourceService.createResource(body);

    return NextResponse.json(resource, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/resource', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

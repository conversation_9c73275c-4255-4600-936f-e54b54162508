// app/api/project/resource/capacity/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { enhancedResourceService } from '@/services/project/EnhancedResourceService';
import { addMonths } from 'date-fns';

/**
 * GET /api/project/resource/capacity
 * Get resource capacity plan
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const departmentId = searchParams.get('departmentId');
    const projectId = searchParams.get('projectId');
    const resourceIds = searchParams.get('resourceIds')?.split(',');
    const viewMode = searchParams.get('viewMode') as 'day' | 'week' | 'month' | undefined;

    // Set default dates if not provided
    const startDate = startDateParam ? new Date(startDateParam) : new Date();
    const endDate = endDateParam ? new Date(endDateParam) : addMonths(startDate, 3);

    // Get capacity plan
    const capacityPlan = await enhancedResourceService.getResourceCapacityPlan({
      startDate,
      endDate,
      departmentId: departmentId || undefined,
      projectId: projectId || undefined,
      resourceIds: resourceIds || undefined,
      viewMode: viewMode || 'week'
    });

    return NextResponse.json(capacityPlan);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/resource/capacity', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/resource/capacity/conflicts
 * Check for resource allocation conflicts
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.resourceId || !body.startDate || !body.endDate || body.hoursPerDay === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: resourceId, startDate, endDate, hoursPerDay' },
        { status: 400 }
      );
    }

    // Check for conflicts
    const conflicts = await enhancedResourceService.getResourceAllocationConflicts(
      body.resourceId,
      new Date(body.startDate),
      new Date(body.endDate),
      body.hoursPerDay,
      body.excludeAllocationId
    );

    return NextResponse.json(conflicts);
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/resource/capacity/conflicts', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

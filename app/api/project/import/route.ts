// app/api/project/import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ImportService } from '@/lib/backend/services/project/ImportService';
import { ExportService } from '@/lib/backend/services/project/ExportService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const importService = new ImportService();
const exportService = new ExportService();

/**
 * GET /api/project/import
 * Get available import templates
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const templateType = searchParams.get('template');
    const download = searchParams.get('download') === 'true';
    const projectId = searchParams.get('projectId');
    const exportTemplate = searchParams.get('export') === 'true';
    const customTemplates = searchParams.get('customTemplates') === 'true';
    const customType = searchParams.get('customType');

    // If requesting custom templates
    if (customTemplates) {
      const templates = exportService.getCustomTemplates(customType || undefined);
      return NextResponse.json({ templates });
    }

    // If template type is provided and download is true, download template
    if (templateType && download) {
      try {
        const { content, filename } = importService.downloadTemplate(templateType);

        // Create response with appropriate headers
        const response = new NextResponse(content, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Cache-Control': 'no-cache'
          }
        });

        return response;
      } catch (error: unknown) {
        return NextResponse.json(
          { error: error instanceof Error ? error.message : 'Error downloading template' },
          { status: 404 }
        );
      }
    }

    // If exporting project data to template
    if (templateType && exportTemplate && projectId) {
      try {
        // Check permissions
        const hasPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER
        ]);

        if (!hasPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions' },
            { status: 403 }
          );
        }

        const content = await exportService.exportToTemplate(projectId, templateType);

        // Create response with appropriate headers
        const response = new NextResponse(content, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="${templateType}_export.csv"`,
            'Cache-Control': 'no-cache'
          }
        });

        return response;
      } catch (error: unknown) {
        return NextResponse.json(
          { error: error instanceof Error ? error.message : 'Error exporting to template' },
          { status: 500 }
        );
      }
    }

    // Get available template types
    const templateTypes = importService.getTemplateTypes();

    return NextResponse.json({ templates: templateTypes });
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/import', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/import
 * Import data from template
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if this is a custom template creation request
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      const body = await request.json();

      if (body.action === 'createCustomTemplate') {
        // Validate required fields
        if (!body.name || !body.type || !body.content) {
          return NextResponse.json(
            { error: 'Name, type, and content are required' },
            { status: 400 }
          );
        }

        // Create custom template
        const result = await exportService.createCustomTemplate(
          body.name,
          body.content,
          body.type
        );

        return NextResponse.json({ success: result });
      } else if (body.action === 'deleteCustomTemplate') {
        // Validate required fields
        if (!body.name || !body.type) {
          return NextResponse.json(
            { error: 'Name and type are required' },
            { status: 400 }
          );
        }

        // Delete custom template
        const result = exportService.deleteCustomTemplate(
          body.name,
          body.type
        );

        return NextResponse.json({ success: result });
      }
    }

    // Handle file import
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string;
    const batchSize = formData.get('batchSize') as string;

    // Validate file and type
    if (!file || !type) {
      return NextResponse.json(
        { error: 'File and type are required' },
        { status: 400 }
      );
    }

    // Read file content
    const content = await file.text();

    // Import data based on type
    let result;
    switch (type) {
      case 'project':
        result = await importService.importProjects(content, user.id);
        break;

      case 'tasks':
        result = await importService.importTasks(content, user.id);
        break;

      case 'resources':
        result = await importService.importResources(content, user.id);
        break;

      // Add other import types here

      default:
        return NextResponse.json(
          { error: 'Invalid import type' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/import', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

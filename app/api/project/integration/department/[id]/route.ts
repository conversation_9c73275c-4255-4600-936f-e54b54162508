// app/api/project/integration/department/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IntegrationService } from '@/lib/backend/services/project/IntegrationService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const integrationService = new IntegrationService();

/**
 * GET /api/project/integration/department/[id]
 * Get department projects
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get department projects
    const projects = await integrationService.getDepartmentProjects(id);

    return NextResponse.json(projects);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/integration/department/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/integration/user/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IntegrationService } from '@/lib/backend/services/project/IntegrationService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const integrationService = new IntegrationService();

/**
 * GET /api/project/integration/user/[id]
 * Get user projects, tasks, and time entries
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type') || 'projects';
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : undefined;

    let result;

    // Get data based on type
    switch (type) {
      case 'projects':
        result = await integrationService.getUserProjects(id);
        break;

      case 'tasks':
        result = await integrationService.getUserTasks(id);
        break;

      case 'time':
        result = await integrationService.getUserTimeEntries(id, startDate, endDate);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid type' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/integration/user/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

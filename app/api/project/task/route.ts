// app/api/project/task/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const taskService = new TaskService();

/**
 * GET /api/project/task
 * Get tasks with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const projectId = searchParams.get('project');
    const parentTaskId = searchParams.get('parentTask');
    const assignedToId = searchParams.get('assignedTo');
    const dueDate = searchParams.get('dueDate');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single task
    if (id) {
      const task = await taskService.getTaskById(id);

      if (!task) {
        return NextResponse.json(
          { error: 'Task not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(task);
    }

    // If projectId is provided, get tasks for a project
    if (projectId) {
      const result = await taskService.getTasksByProject(
        projectId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          assignedTo: assignedToId || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If parentTaskId is provided, get subtasks
    if (parentTaskId) {
      const result = await taskService.getSubtasks(
        parentTaskId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If assignedToId is provided, get tasks assigned to a user
    if (assignedToId) {
      const result = await taskService.getTasksByAssignee(
        assignedToId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          project: projectId || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface TaskFilter {
      status?: string | { $in: string[] } | { $ne: string };
      priority?: string | { $in: string[] };
      dueDate?: {
        $gte?: Date;
        $lt?: Date;
      };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: TaskFilter = {};

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add priority filter
    if (priority) {
      (filter as any).priority = priority.includes(',') ? { $in: priority.split(',') } : priority;
    }

    // Add due date filter
    if (dueDate) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (dueDate === 'today') {
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        filter.dueDate = { $gte: today, $lt: tomorrow };
      } else if (dueDate === 'overdue') {
        filter.dueDate = { $lt: today };
        (filter as any).status = { $ne: 'completed' };
      } else if (dueDate === 'week') {
        const nextWeek = new Date(today);
        nextWeek.setDate(nextWeek.getDate() + 7);
        filter.dueDate = { $gte: today, $lt: nextWeek };
      } else if (dueDate === 'month') {
        const nextMonth = new Date(today);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        filter.dueDate = { $gte: today, $lt: nextMonth };
      }
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get tasks
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await taskService.getTasks(filter as any, {
      sort,
      page,
      limit,
      populate: ['assignedBy', 'assignedTo', 'project', 'parentTask', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/task', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/task
 * Create a new task
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by and assigned by
    body.createdBy = user.id;
    body.assignedBy = user.id;

    // Create task
    const task = await taskService.createTask(body);

    return NextResponse.json(task, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/task', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

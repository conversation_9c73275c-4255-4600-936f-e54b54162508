// app/api/project/budget/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/backend/services/project/BudgetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const budgetService = new BudgetService();

/**
 * GET /api/project/budget/[id]
 * Get a budget by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget
    const budget = await budgetService.getBudgetById(id);

    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(budget);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/budget/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/budget/[id]
 * Update a budget
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget
    const budget = await budgetService.getBudgetById(id);

    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be updated
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ])) {
      return NextResponse.json(
        { error: `Budget cannot be updated in ${budget.status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update budget
    const updatedBudget = await budgetService.updateById(id, body);

    return NextResponse.json(updatedBudget);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/budget/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/budget/[id]
 * Delete a budget
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget
    const budget = await budgetService.getBudgetById(id);

    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be deleted
    if (budget.status !== 'draft') {
      return NextResponse.json(
        { error: `Budget cannot be deleted in ${budget.status} status` },
        { status: 403 }
      );
    }

    // Delete budget
    const result = await budgetService.deleteById(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/budget/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/budget/[id]
 * Perform operations on a budget (status, calculate, report, forecast, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get budget
    const budget = await budgetService.getBudgetById(id);

    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        // Check permissions for status update
        const hasStatusPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasStatusPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update status' },
            { status: 403 }
          );
        }

        result = await budgetService.updateStatus(
          id,
          body.status,
          user.id,
          body.rejectionReason
        );
        break;

      case 'calculate':
        // Check permissions for calculation
        const hasCalcPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_SPECIALIST
        ]);

        if (!hasCalcPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to calculate budget' },
            { status: 403 }
          );
        }

        result = await budgetService.calculateBudgetTotals(id);
        break;

      case 'updateFromExpenses':
        // Check permissions for updating from expenses
        const hasUpdatePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_SPECIALIST
        ]);

        if (!hasUpdatePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update from expenses' },
            { status: 403 }
          );
        }

        result = await budgetService.updateActualAmountsFromExpenses(id);
        break;

      case 'varianceReport':
        // Check permissions for variance report
        const hasReportPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_SPECIALIST,
          UserRole.EMPLOYEE
        ]);

        if (!hasReportPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to generate variance report' },
            { status: 403 }
          );
        }

        result = await budgetService.generateBudgetVarianceReport(id);
        break;

      case 'forecast':
        // Check permissions for forecast
        const hasForecastPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_SPECIALIST
        ]);

        if (!hasForecastPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to generate forecast' },
            { status: 403 }
          );
        }

        result = await budgetService.generateBudgetForecast(
          id,
          new Date(body.forecastDate || new Date())
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/budget/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

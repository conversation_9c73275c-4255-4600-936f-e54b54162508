// app/api/project/budget/category/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/backend/services/project/BudgetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const budgetService = new BudgetService();

/**
 * GET /api/project/budget/category
 * Get budget categories with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const isActive = searchParams.get('isActive');
    const parentId = searchParams.get('parent');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '100', 10);
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build filter
    // Use a more specific type for MongoDB queries
    interface CategoryFilter {
      isActive?: boolean;
      parentCategory?: string | { $exists: boolean };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: CategoryFilter = {};

    // Add isActive filter
    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    // Add parent filter
    if (parentId) {
      filter.parentCategory = parentId;
    } else if (parentId === 'null') {
      filter.parentCategory = { $exists: false };
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get categories
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await budgetService.getCategories(filter as any, {
      sort,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/budget/category', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/budget/category
 * Create a new budget category
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create category
    const category = await budgetService.createCategory(body);

    return NextResponse.json(category, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/budget/category', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/budget/item/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/backend/services/project/BudgetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { BudgetItem } from '@/models/project/ProjectBudget';

// Initialize services
const budgetService = new BudgetService();

/**
 * GET /api/project/budget/item/[id]
 * Get a budget item by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget item
    const budgetItem = await BudgetItem.findById(id)
      .populate('budget')
      .populate('category')
      .populate('createdBy')
      .populate('updatedBy');

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'Budget item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(budgetItem);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/budget/item/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/budget/item/[id]
 * Update a budget item
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget item
    const budgetItem = await BudgetItem.findById(id).populate('budget');

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'Budget item not found' },
        { status: 404 }
      );
    }

    // Check if budget is in a state that allows updates
    if (budgetItem.budget && (budgetItem.budget as any).status !== 'draft' &&
        !hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.FINANCE_MANAGER
        ])) {
      return NextResponse.json(
        { error: `Budget items cannot be updated when budget is in ${(budgetItem.budget as any).status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update budget item
    const updatedBudgetItem = await budgetService.updateBudgetItem(id, body);

    return NextResponse.json(updatedBudgetItem);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/budget/item/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/budget/item/[id]
 * Delete a budget item
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get budget item
    const budgetItem = await BudgetItem.findById(id).populate('budget');

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'Budget item not found' },
        { status: 404 }
      );
    }

    // Check if budget is in a state that allows deletion
    if (budgetItem.budget && (budgetItem.budget as any).status !== 'draft' &&
        !hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN
        ])) {
      return NextResponse.json(
        { error: `Budget items cannot be deleted when budget is in ${(budgetItem.budget as any).status} status` },
        { status: 403 }
      );
    }

    // Delete budget item
    await BudgetItem.findByIdAndDelete(id);

    // Recalculate budget totals
    if (budgetItem.budget) {
      await budgetService.calculateBudgetTotals((budgetItem.budget as any)._id.toString());
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/budget/item/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

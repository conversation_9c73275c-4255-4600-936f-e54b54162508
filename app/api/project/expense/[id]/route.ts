// app/api/project/expense/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ExpenseService } from '@/lib/backend/services/project/ExpenseService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const expenseService = new ExpenseService();

/**
 * GET /api/project/expense/[id]
 * Get an expense by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expense
    const expense = await expenseService.getExpenseById(id);

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(expense);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/expense/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/expense/[id]
 * Update an expense
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expense
    const expense = await expenseService.getExpenseById(id);

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = expense.createdBy.toString() === user.id;
    const hasPermission = isOwner || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if expense can be updated
    if (expense.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ])) {
      return NextResponse.json(
        { error: `Expense cannot be updated in ${expense.status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update expense
    const updatedExpense = await expenseService.updateById(id, body);

    return NextResponse.json(updatedExpense);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/expense/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/expense/[id]
 * Delete an expense
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get expense
    const expense = await expenseService.getExpenseById(id);

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = expense.createdBy.toString() === user.id;
    const hasPermission = isOwner || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if expense can be deleted
    if (expense.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: `Expense cannot be deleted in ${expense.status} status` },
        { status: 403 }
      );
    }

    // Delete expense
    const result = await expenseService.deleteById(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/expense/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/expense/[id]
 * Perform operations on an expense (status, payment, attachment, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get expense
    const expense = await expenseService.getExpenseById(id);

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        // Check permissions for status update
        const hasStatusPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasStatusPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update status' },
            { status: 403 }
          );
        }

        result = await expenseService.updateStatus(
          id,
          body.status,
          user.id,
          body.rejectionReason
        );
        break;

      case 'payment':
        // Check permissions for payment update
        const hasPaymentPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.FINANCE_MANAGER,
          UserRole.FINANCE_SPECIALIST,
          UserRole.ACCOUNTANT
        ]);

        if (!hasPaymentPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update payment status' },
            { status: 403 }
          );
        }

        result = await expenseService.updatePaymentStatus(
          id,
          body.paymentStatus,
          user.id,
          body.paymentDate ? new Date(body.paymentDate) : undefined
        );
        break;

      case 'attachment':
        // Check permissions for adding attachment
        const isOwner = expense.createdBy.toString() === user.id;
        const hasAttachmentPermission = isOwner || hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.FINANCE_MANAGER
        ]);

        if (!hasAttachmentPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to add attachment' },
            { status: 403 }
          );
        }

        result = await expenseService.addAttachment(
          id,
          {
            name: body.name,
            fileUrl: body.fileUrl,
            fileType: body.fileType,
            fileSize: body.fileSize
          },
          user.id
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/expense/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

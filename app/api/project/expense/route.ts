// app/api/project/expense/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ExpenseService } from '@/lib/backend/services/project/ExpenseService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const expenseService = new ExpenseService();

/**
 * GET /api/project/expense
 * Get expenses with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const paymentStatus = searchParams.get('paymentStatus');
    const projectId = searchParams.get('project');
    const budgetId = searchParams.get('budget');
    const categoryId = searchParams.get('category');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const billable = searchParams.get('billable');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single expense
    if (id) {
      const expense = await expenseService.getExpenseById(id);

      if (!expense) {
        return NextResponse.json(
          { error: 'Expense not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(expense);
    }

    // If projectId is provided, get expenses for a project
    if (projectId) {
      const result = await expenseService.getExpensesByProject(
        projectId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          paymentStatus: paymentStatus ? (paymentStatus.includes(',') ? paymentStatus.split(',') as any : paymentStatus) : undefined,
          categoryId: categoryId || undefined,
          budgetId: budgetId || undefined,
          billable: billable ? billable === 'true' : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If budgetId is provided, get expenses for a budget
    if (budgetId) {
      const result = await expenseService.getExpensesByBudget(
        budgetId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          paymentStatus: paymentStatus ? (paymentStatus.includes(',') ? paymentStatus.split(',') as any : paymentStatus) : undefined,
          categoryId: categoryId || undefined,
          billable: billable ? billable === 'true' : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface ExpenseFilter {
      status?: string | { $in: string[] };
      paymentStatus?: string | { $in: string[] };
      category?: string;
      date?: {
        $gte?: Date;
        $lte?: Date;
      };
      billable?: boolean;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: ExpenseFilter = {};

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add payment status filter
    if (paymentStatus) {
      (filter as any).paymentStatus = paymentStatus.includes(',') ? { $in: paymentStatus.split(',') } : paymentStatus;
    }

    // Add category filter
    if (categoryId) {
      filter.category = categoryId;
    }

    // Add date range filter
    if (startDate || endDate) {
      filter.date = {};

      if (startDate) {
        filter.date.$gte = new Date(startDate);
      }

      if (endDate) {
        filter.date.$lte = new Date(endDate);
      }
    }

    // Add billable filter
    if (billable !== null) {
      filter.billable = billable === 'true';
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get expenses
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await expenseService.getExpenses(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'budget', 'budgetItem', 'category', 'approvedBy', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/expense', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/expense
 * Create a new expense
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create expense
    const expense = await expenseService.createExpense(body);

    return NextResponse.json(expense, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/expense', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

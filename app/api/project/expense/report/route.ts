// app/api/project/expense/report/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ExpenseService } from '@/lib/backend/services/project/ExpenseService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const expenseService = new ExpenseService();

/**
 * GET /api/project/expense/report
 * Generate expense reports
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_SPECIALIST,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const groupBy = searchParams.get('groupBy') as 'category' | 'budget' | 'date' | 'paymentStatus' || 'category';

    // Project ID is required
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Start date and end date are required
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    // Generate expense report
    const report = await expenseService.generateExpenseReport(
      projectId,
      new Date(startDate),
      new Date(endDate),
      groupBy
    );

    return NextResponse.json(report);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/expense/report', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

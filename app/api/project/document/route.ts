// app/api/project/document/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { DocumentService } from '@/lib/backend/services/project/DocumentService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const documentService = new DocumentService();

/**
 * GET /api/project/document
 * Get documents with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const projectId = searchParams.get('project');
    const categoryId = searchParams.get('category');
    const isTemplate = searchParams.get('isTemplate');
    const isPrivate = searchParams.get('isPrivate');
    const search = searchParams.get('search');
    const tags = searchParams.get('tags');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'updatedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single document
    if (id) {
      const document = await documentService.getDocumentById(id);

      if (!document) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(document);
    }

    // If search is provided, search documents
    if (search) {
      interface SearchOptions {
        projectId?: string;
        limit: number;
        page: number;
        status?: string | string[];
        category?: string;
        isTemplate?: boolean;
        [key: string]: any;
      }

      const options: SearchOptions = {
        projectId: projectId || undefined,
        limit,
        page
      };

      // Add status filter
      if (status) {
        (options as any).status = status.includes(',') ? status.split(',') as any : status;
      }

      // Add category filter
      if (categoryId) {
        options.category = categoryId;
      }

      // Add isTemplate filter
      if (isTemplate !== null) {
        options.isTemplate = isTemplate === 'true';
      }

      const result = await documentService.searchDocuments(search, options as any);

      return NextResponse.json(result);
    }

    // If projectId is provided, get documents for a project
    if (projectId) {
      interface ProjectOptions {
        limit: number;
        page: number;
        sort: Record<string, number>;
        status?: string | string[];
        category?: string;
        isTemplate?: boolean;
        isPrivate?: boolean;
        tags?: string[];
        [key: string]: any;
      }

      const options: ProjectOptions = {
        limit,
        page,
        sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 }
      };

      // Add status filter
      if (status) {
        (options as any).status = status.includes(',') ? status.split(',') as any : status;
      }

      // Add category filter
      if (categoryId) {
        options.category = categoryId;
      }

      // Add isTemplate filter
      if (isTemplate !== null) {
        options.isTemplate = isTemplate === 'true';
      }

      // Add isPrivate filter
      if (isPrivate !== null) {
        options.isPrivate = isPrivate === 'true';
      }

      // Add tags filter
      if (tags) {
        options.tags = tags.split(',');
      }

      const result = await documentService.getDocumentsByProject(projectId, options as any);

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface DocumentFilter {
      isDeleted: boolean;
      status?: string | { $in: string[] };
      category?: string;
      isTemplate?: boolean;
      isPrivate?: boolean;
      tags?: { $in: string[] };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: DocumentFilter = { isDeleted: false };

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add category filter
    if (categoryId) {
      filter.category = categoryId;
    }

    // Add isTemplate filter
    if (isTemplate !== null) {
      filter.isTemplate = isTemplate === 'true';
    }

    // Add isPrivate filter
    if (isPrivate !== null) {
      filter.isPrivate = isPrivate === 'true';
    }

    // Add tags filter
    if (tags) {
      filter.tags = { $in: tags.split(',') };
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get documents
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await documentService.getDocuments(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'category', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/document', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/document
 * Create a new document
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();
    const { fileData, ...documentData } = body;

    // Validate file data
    if (!fileData || !fileData.fileUrl || !fileData.fileName || !fileData.fileSize || !fileData.fileType) {
      return NextResponse.json(
        { error: 'File data is required' },
        { status: 400 }
      );
    }

    // Add created by
    documentData.createdBy = user.id;

    // Create document
    const document = await documentService.createDocument(documentData, fileData);

    return NextResponse.json(document, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/document', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/document/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { DocumentService } from '@/lib/backend/services/project/DocumentService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const documentService = new DocumentService();

/**
 * GET /api/project/document/[id]
 * Get a document by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get document
    const document = await documentService.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(document);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/document/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/document/[id]
 * Update a document
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get document
    const document = await documentService.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isCreator = document.createdBy.toString() === user.id;
    const hasPermission = isCreator || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if document can be updated
    if (!['draft', 'under_review'].includes(document.status)) {
      return NextResponse.json(
        { error: `Document cannot be updated in ${document.status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update document
    const updatedDocument = await documentService.updateById(id, body);

    return NextResponse.json(updatedDocument);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/document/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/document/[id]
 * Delete a document (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get document
    const document = await documentService.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Soft delete document
    const result = await documentService.updateById(id, {
      isDeleted: true,
      updatedBy: user.id
    });

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/document/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/document/[id]
 * Perform operations on a document (version, approval, publish, archive, comment)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get document
    const document = await documentService.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'version':
        // Check permissions for adding version
        const isCreator = document.createdBy.toString() === user.id;
        const hasVersionPermission = isCreator || hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.TEAM_LEADER
        ]);

        if (!hasVersionPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to add version' },
            { status: 403 }
          );
        }

        result = await documentService.addVersion(
          id,
          body.fileData,
          user.id
        );
        break;

      case 'requestApproval':
        // Check permissions for requesting approval
        const hasApprovalRequestPermission = document.createdBy.toString() === user.id ||
          hasRequiredPermissions(user, [
            UserRole.SUPER_ADMIN,
            UserRole.SYSTEM_ADMIN,
            UserRole.PROJECT_MANAGER,
            UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER
          ]);

        if (!hasApprovalRequestPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to request approval' },
            { status: 403 }
          );
        }

        result = await documentService.requestApproval(
          id,
          body.approverIds,
          user.id
        );
        break;

      case 'processApproval':
        // Check if user is an approver
        const isApprover = document.approvals?.some(
          approval => approval.approver.toString() === user.id && approval.status === 'pending'
        );

        if (!isApprover) {
          return NextResponse.json(
            { error: 'Forbidden: User is not an approver for this document' },
            { status: 403 }
          );
        }

        result = await documentService.processApproval(
          id,
          body.approved,
          user.id,
          body.comments
        );
        break;

      case 'publish':
        // Check permissions for publishing
        const hasPublishPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!hasPublishPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to publish document' },
            { status: 403 }
          );
        }

        result = await documentService.publishDocument(
          id,
          user.id
        );
        break;

      case 'archive':
        // Check permissions for archiving
        const hasArchivePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!hasArchivePermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to archive document' },
            { status: 403 }
          );
        }

        result = await documentService.archiveDocument(
          id,
          user.id
        );
        break;

      case 'comment':
        // Anyone can comment
        result = await documentService.addComment(
          id,
          body.content,
          user.id
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/document/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

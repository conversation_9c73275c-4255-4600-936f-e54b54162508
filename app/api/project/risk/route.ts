// app/api/project/risk/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { RiskService } from '@/lib/backend/services/project/RiskService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const riskService = new RiskService();

/**
 * GET /api/project/risk
 * Get risks with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const projectId = searchParams.get('project');
    const owner = searchParams.get('owner');
    const isActive = searchParams.get('isActive');
    const minSeverity = searchParams.get('minSeverity');
    const maxSeverity = searchParams.get('maxSeverity');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'severity';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single risk
    if (id) {
      const risk = await riskService.getRiskById(id);

      if (!risk) {
        return NextResponse.json(
          { error: 'Risk not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(risk);
    }

    // If projectId is provided, get risks for a project
    if (projectId) {
      interface ProjectOptions {
        status?: string | string[];
        category?: string | string[];
        owner?: string;
        isActive?: boolean;
        sort: Record<string, number>;
        page: number;
        limit: number;
        severity?: {
          min?: number;
          max?: number;
        };
        [key: string]: any;
      }

      const options: ProjectOptions = {
        status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
        category: category ? (category.includes(',') ? category.split(',') as any : category) : undefined,
        owner: owner || undefined,
        isActive: isActive ? isActive === 'true' : undefined,
        sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
        page,
        limit
      };

      // Add severity filter if provided
      if (minSeverity || maxSeverity) {
        (options as any).severity = {};

        if (minSeverity) {
          (options as any).severity.min = parseInt(minSeverity, 10);
        }

        if (maxSeverity) {
          (options as any).severity.max = parseInt(maxSeverity, 10);
        }
      }

      const result = await riskService.getRisksByProject(projectId, options as any);

      return NextResponse.json(result);
    }

    // Build filter
    // Use a more specific type for MongoDB queries
    interface RiskFilter {
      isDeleted: boolean;
      status?: string | { $in: string[] };
      category?: string | { $in: string[] };
      owner?: string;
      isActive?: boolean;
      severity?: {
        $gte?: number;
        $lte?: number;
      };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: RiskFilter = { isDeleted: false };

    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add category filter
    if (category) {
      (filter as any).category = category.includes(',') ? { $in: category.split(',') } : category;
    }

    // Add owner filter
    if (owner) {
      filter.owner = owner;
    }

    // Add isActive filter
    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    // Add severity filter
    if (minSeverity || maxSeverity) {
      filter.severity = {};

      if (minSeverity) {
        filter.severity.$gte = parseInt(minSeverity, 10);
      }

      if (maxSeverity) {
        filter.severity.$lte = parseInt(maxSeverity, 10);
      }
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get risks
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await riskService.getRisks(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'owner', 'identifiedBy', 'lastReviewedBy', 'closedBy', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/risk', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/risk
 * Create a new risk
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by and identified by
    body.createdBy = user.id;
    body.identifiedBy = user.id;

    // Create risk
    const risk = await riskService.createRisk(body);

    return NextResponse.json(risk, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/risk', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/risk/statistics/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { RiskService } from '@/lib/backend/services/project/RiskService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const riskService = new RiskService();

/**
 * GET /api/project/risk/statistics
 * Get risk statistics for a project
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');

    // Project ID is required
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Generate statistics
    const statistics = await riskService.generateRiskStatistics(projectId);

    return NextResponse.json(statistics);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/risk/statistics', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

// app/api/project/risk/[id]/history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { RiskService } from '@/lib/backend/services/project/RiskService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const riskService = new RiskService();

/**
 * GET /api/project/risk/[id]/history
 * Get history for a risk
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get history
    const history = await riskService.getHistory(id);

    return NextResponse.json(history);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/risk/[id]/history', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

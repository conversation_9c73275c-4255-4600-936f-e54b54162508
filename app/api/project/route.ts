// app/api/project/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ProjectService } from '@/lib/backend/services/project/ProjectService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Initialize services
const projectService = new ProjectService();

/**
 * GET /api/project
 * Get projects with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const managerId = searchParams.get('manager');
    const departmentId = searchParams.get('department');
    const teamMemberId = searchParams.get('teamMember');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single project
    if (id) {
      const project = await projectService.getProjectById(id);

      if (!project) {
        return NextResponse.json(
          { error: 'Project not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(project);
    }

    // Build filter - using any type for MongoDB query operators
    const filter: any = {};

    // Add status filter
    if (status) {
      filter.status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add priority filter
    if (priority) {
      filter.priority = priority.includes(',') ? { $in: priority.split(',') } : priority;
    }

    // Add manager filter
    if (managerId) {
      filter.manager = new mongoose.Types.ObjectId(managerId);
    }

    // Add department filter
    if (departmentId) {
      filter.department = new mongoose.Types.ObjectId(departmentId);
    }

    // Add team member filter
    if (teamMemberId) {
      filter.team = new mongoose.Types.ObjectId(teamMemberId);
    }

    // Add date range filter
    if (startDate || endDate) {
      if (startDate && endDate) {
        filter.startDate = { $gte: new Date(startDate) };
        filter.endDate = { $lte: new Date(endDate) };
      } else if (startDate) {
        filter.startDate = { $gte: new Date(startDate) };
      } else if (endDate) {
        filter.endDate = { $lte: new Date(endDate) };
      }
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get projects
    const result = await projectService.getProjects(filter, {
      sort,
      page,
      limit,
      populate: ['manager', 'department', 'team', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * POST /api/project
 * Create a new project
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create project
    const project = await projectService.createProject(body);

    return NextResponse.json(project, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

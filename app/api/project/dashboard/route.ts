// app/api/project/dashboard/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { ReportingService } from '@/lib/backend/services/project/ReportingService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const reportingService = new ReportingService();

/**
 * GET /api/project/dashboard
 * Get dashboards with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');
    const isDefault = searchParams.get('isDefault');
    const isSystem = searchParams.get('isSystem');
    const isShared = searchParams.get('isShared');
    const isActive = searchParams.get('isActive');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build filter
    // Use a more specific type for MongoDB queries
    interface DashboardFilter {
      project?: string;
      isDefault?: boolean;
      isSystem?: boolean;
      isShared?: boolean;
      isActive?: boolean;
      $or?: Array<Record<string, any>>;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }

    const filter: DashboardFilter = {};

    // Add project filter
    if (projectId) {
      filter.project = projectId;
    }

    // Add isDefault filter
    if (isDefault !== null) {
      filter.isDefault = isDefault === 'true';
    }

    // Add isSystem filter
    if (isSystem !== null) {
      filter.isSystem = isSystem === 'true';
    }

    // Add isShared filter
    if (isShared !== null) {
      filter.isShared = isShared === 'true';
    }

    // Add isActive filter
    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    // Add filter for dashboards shared with the user
    if (!hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ])) {
      filter.$or = [
        { createdBy: user.id },
        { isShared: true },
        { sharedWith: user.id }
      ];
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get dashboards
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await reportingService.getDashboards(filter as any, {
      sort,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/dashboard', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/dashboard
 * Create a new dashboard
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create dashboard
    const dashboard = await reportingService.createDashboard(body);

    return NextResponse.json(dashboard, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/dashboard', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

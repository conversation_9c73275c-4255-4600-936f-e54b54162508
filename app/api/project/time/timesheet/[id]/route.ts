// app/api/project/time/timesheet/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { TimesheetService } from '@/lib/backend/services/project/TimesheetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const timesheetService = new TimesheetService();

/**
 * GET /api/project/time/timesheet/[id]
 * Get a timesheet by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get timesheet
    const timesheet = await timesheetService.getTimesheetById(id);

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(timesheet);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/time/timesheet/[id]', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * PUT /api/project/time/timesheet/[id]
 * Update a timesheet
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get timesheet
    const timesheet = await timesheetService.getTimesheetById(id);

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = timesheet.createdBy.toString() === user.id;
    const hasPermission = isOwner || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if timesheet can be updated
    if (timesheet.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ])) {
      return NextResponse.json(
        { error: `Timesheet cannot be updated in ${timesheet.status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Update timesheet
    const updatedTimesheet = await timesheetService.updateById(id, body);

    return NextResponse.json(updatedTimesheet);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/time/timesheet/[id]', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * DELETE /api/project/time/timesheet/[id]
 * Delete a timesheet
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get timesheet
    const timesheet = await timesheetService.getTimesheetById(id);

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if timesheet can be deleted
    if (timesheet.status !== 'draft') {
      return NextResponse.json(
        { error: `Timesheet cannot be deleted in ${timesheet.status} status` },
        { status: 403 }
      );
    }

    // Delete timesheet
    await timesheetService.deleteById(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/time/timesheet/[id]', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * PATCH /api/project/time/timesheet/[id]
 * Perform operations on a timesheet (status, submit, calculate, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get timesheet
    const timesheet = await timesheetService.getTimesheetById(id);

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        // Check permissions for status update
        const hasStatusPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!hasStatusPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update status' },
            { status: 403 }
          );
        }

        result = await timesheetService.updateStatus(
          id,
          body.status,
          user.id,
          body.rejectionReason
        );
        break;

      case 'submit':
        // Check if user is the owner or has permissions
        const isOwner = timesheet.createdBy.toString() === user.id;
        const hasSubmitPermission = isOwner || hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!hasSubmitPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to submit timesheet' },
            { status: 403 }
          );
        }

        result = await timesheetService.submitTimesheet(
          id,
          user.id
        );
        break;

      case 'calculate':
        // Check permissions for calculation
        const hasCalcPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD,
          UserRole.TEAM_LEADER,
          UserRole.EMPLOYEE
        ]);

        if (!hasCalcPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to calculate hours' },
            { status: 403 }
          );
        }

        result = await timesheetService.calculateTimesheetHours(id);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/time/timesheet/[id]', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

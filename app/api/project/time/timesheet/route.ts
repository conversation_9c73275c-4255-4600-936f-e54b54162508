// app/api/project/time/timesheet/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { TimesheetService } from '@/lib/backend/services/project/TimesheetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const timesheetService = new TimesheetService();

/**
 * GET /api/project/time/timesheet
 * Get timesheets with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const resourceId = searchParams.get('resource');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'startDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single timesheet
    if (id) {
      const timesheet = await timesheetService.getTimesheetById(id);

      if (!timesheet) {
        return NextResponse.json(
          { error: 'Timesheet not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(timesheet);
    }

    // If resourceId is provided, get timesheets for a resource
    if (resourceId) {
      const result = await timesheetService.getTimesheetsByResource(
        resourceId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter - using any type for MongoDB query operators
    const filter: any = {};

    // Add status filter
    if (status) {
      filter.status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add date range filter
    if (startDate) {
      filter.startDate = { $gte: new Date(startDate) };
    }

    if (endDate) {
      filter.endDate = { $lte: new Date(endDate) };
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get timesheets
    const result = await timesheetService.getTimesheets(filter, {
      sort,
      page,
      limit,
      populate: ['resource', 'approvedBy', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/time/timesheet', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * POST /api/project/time/timesheet
 * Create a new timesheet
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Add created by
    body.createdBy = user.id;

    // Handle different operations
    if (operation === 'generate') {
      // Generate timesheet
      const timesheet = await timesheetService.generateTimesheet(
        body.resourceId,
        new Date(body.startDate),
        new Date(body.endDate),
        user.id
      );

      return NextResponse.json(timesheet, { status: 201 });
    } else {
      // Create timesheet
      const timesheet = await timesheetService.createTimesheet(body);

      return NextResponse.json(timesheet, { status: 201 });
    }
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/time/timesheet', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

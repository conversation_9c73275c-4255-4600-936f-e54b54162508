// app/api/project/time/entry/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { TimeEntryService } from '@/lib/backend/services/project/TimeEntryService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const timeEntryService = new TimeEntryService();

/**
 * GET /api/project/time/entry
 * Get time entries with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const projectId = searchParams.get('project');
    const resourceId = searchParams.get('resource');
    const taskId = searchParams.get('task');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const billable = searchParams.get('billable');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');

    // If ID is provided, get a single time entry
    if (id) {
      const timeEntry = await timeEntryService.getTimeEntryById(id);

      if (!timeEntry) {
        return NextResponse.json(
          { error: 'Time entry not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(timeEntry);
    }

    // If projectId is provided, get time entries for a project
    if (projectId) {
      const result = await timeEntryService.getTimeEntriesByProject(
        projectId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          resourceId: resourceId || undefined,
          taskId: taskId || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If resourceId is provided, get time entries for a resource
    if (resourceId) {
      const result = await timeEntryService.getTimeEntriesByResource(
        resourceId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          projectId: projectId || undefined,
          taskId: taskId || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // If taskId is provided, get time entries for a task
    if (taskId) {
      const result = await timeEntryService.getTimeEntriesByTask(
        taskId,
        {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          resourceId: resourceId || undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );

      return NextResponse.json(result);
    }

    // Build filter - using any type for MongoDB query operators
    const filter: any = {};

    // Add status filter
    if (status) {
      filter.status = status.includes(',') ? { $in: status.split(',') } : status;
    }

    // Add date range filter
    if (startDate || endDate) {
      filter.date = {};

      if (startDate) {
        filter.date.$gte = new Date(startDate);
      }

      if (endDate) {
        filter.date.$lte = new Date(endDate);
      }
    }

    // Add billable filter
    if (billable !== null) {
      filter.billable = billable === 'true';
    }

    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };

    // Get time entries
    const result = await timeEntryService.getTimeEntries(filter, {
      sort,
      page,
      limit,
      populate: ['project', 'task', 'resource', 'approvedBy', 'createdBy', 'updatedBy']
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/time/entry', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * POST /api/project/time/entry
 * Create a new time entry
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Add created by
    body.createdBy = user.id;

    // Create time entry
    const timeEntry = await timeEntryService.createTimeEntry(body);

    return NextResponse.json(timeEntry, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/time/entry', LogCategory.PROJECT, error);

    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    const statusCode = error && typeof error === 'object' && 'status' in error
      ? (error as any).status
      : 500;

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

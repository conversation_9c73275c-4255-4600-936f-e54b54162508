// app/api/project/time/entry/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { TimeEntryService } from '@/lib/backend/services/project/TimeEntryService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const timeEntryService = new TimeEntryService();

/**
 * GET /api/project/time/entry/[id]
 * Get a time entry by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get time entry
    const timeEntry = await timeEntryService.getTimeEntryById(id);

    if (!timeEntry) {
      return NextResponse.json(
        { error: 'Time entry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(timeEntry);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/time/entry/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PUT /api/project/time/entry/[id]
 * Update a time entry
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get time entry
    const timeEntry = await timeEntryService.getTimeEntryById(id);

    if (!timeEntry) {
      return NextResponse.json(
        { error: 'Time entry not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = timeEntry.createdBy.toString() === user.id;
    const hasPermission = isOwner || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if time entry can be updated
    if (timeEntry.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ])) {
      return NextResponse.json(
        { error: `Time entry cannot be updated in ${timeEntry.status} status` },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Add updated by
    body.updatedBy = user.id;

    // Validate time entry
    if (body.startTime && body.endTime) {
      if (new Date(body.startTime) >= new Date(body.endTime)) {
        return NextResponse.json(
          { error: 'Start time must be before end time' },
          { status: 400 }
        );
      }

      // Calculate duration in minutes
      const startTime = new Date(body.startTime);
      const endTime = new Date(body.endTime);
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationMinutes = Math.round(durationMs / (1000 * 60));

      body.duration = durationMinutes;
    }

    // Update time entry
    const updatedTimeEntry = await timeEntryService.updateById(id, body);

    return NextResponse.json(updatedTimeEntry);
  } catch (error: unknown) {
    logger.error('Error in PUT /api/project/time/entry/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * DELETE /api/project/time/entry/[id]
 * Delete a time entry
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get time entry
    const timeEntry = await timeEntryService.getTimeEntryById(id);

    if (!timeEntry) {
      return NextResponse.json(
        { error: 'Time entry not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = timeEntry.createdBy.toString() === user.id;
    const hasPermission = isOwner || hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if time entry can be deleted
    if (timeEntry.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER
    ])) {
      return NextResponse.json(
        { error: `Time entry cannot be deleted in ${timeEntry.status} status` },
        { status: 403 }
      );
    }

    // Delete time entry
    await timeEntryService.deleteById(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error('Error in DELETE /api/project/time/entry/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * PATCH /api/project/time/entry/[id]
 * Perform operations on a time entry (status, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();
    const { operation } = body;

    // Get time entry
    const timeEntry = await timeEntryService.getTimeEntryById(id);

    if (!timeEntry) {
      return NextResponse.json(
        { error: 'Time entry not found' },
        { status: 404 }
      );
    }

    let result;

    // Perform operation based on type
    switch (operation) {
      case 'status':
        // Check permissions for status update
        const hasStatusPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROJECT_MANAGER,
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!hasStatusPermission) {
          return NextResponse.json(
            { error: 'Forbidden: Insufficient permissions to update status' },
            { status: 403 }
          );
        }

        result = await timeEntryService.updateStatus(
          id,
          body.status,
          user.id,
          body.rejectionReason
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid operation' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in PATCH /api/project/time/entry/[id]', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

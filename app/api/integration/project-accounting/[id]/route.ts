// app/api/integration/project-accounting/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectAccountingIntegrationService } from '@/lib/services/integration/project-accounting-integration-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError,
  handleNotFoundError
} from '@/lib/backend/utils/api-response';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/integration/project-accounting/[id]
 * Sync a project with accounting
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Validate ID format
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return handleNotFoundError('Project', id);
    }

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Sync project with accounting
    const result = await projectAccountingIntegrationService.syncProjectWithAccounting(
      id,
      user.id
    );

    if (!result.success) {
      return handleApiError(new Error(result.message));
    }

    // Return success response
    return successResponse(
      result.data,
      result.message
    );
  } catch (error: unknown) {
    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        const { id } = await params;
        return handleNotFoundError('Project', id);
      }
    }

    // Handle general errors
    return handleApiError(error);
  }
}

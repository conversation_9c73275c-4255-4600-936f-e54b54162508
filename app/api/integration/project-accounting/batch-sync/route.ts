// app/api/integration/project-accounting/batch-sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { projectAccountingIntegrationService } from '@/lib/services/integration/project-accounting-integration-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  successResponse,
  handleApiError,
  handleAuthError,
  handlePermissionError
} from '@/lib/backend/utils/api-response';

/**
 * POST /api/integration/project-accounting/batch-sync
 * Sync all projects with accounting
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return handleAuthError();
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return handlePermissionError();
    }

    // Connect to database
    await connectToDatabase();

    // Sync all projects with accounting
    const result = await projectAccountingIntegrationService.syncAllProjectsWithAccounting(
      user.id
    );

    if (!result.success) {
      return handleApiError(new Error(result.message));
    }

    // Return success response
    return successResponse(
      result.data,
      result.message
    );
  } catch (error: unknown) {
    // Handle general errors
    return handleApiError(error);
  }
}

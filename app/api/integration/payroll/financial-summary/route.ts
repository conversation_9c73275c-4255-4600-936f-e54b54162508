import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollFinancialIntegrationService } from '@/lib/services/integration/payroll-financial-integration';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/integration/payroll/financial-summary
 * Get payroll financial summary for a period
 * Query parameters:
 * - startDate: start date for the summary (required)
 * - endDate: end date for the summary (required)
 * - departmentId: filter by department (optional)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const departmentId = searchParams.get('departmentId');

    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    logger.info('Getting payroll financial summary', LogCategory.INTEGRATION, {
      startDate,
      endDate,
      departmentId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create financial integration service and get summary
    const financialIntegrationService = new PayrollFinancialIntegrationService();
    const summary = await financialIntegrationService.getPayrollFinancialSummary(
      startDate,
      endDate,
      departmentId || undefined
    );

    return NextResponse.json({
      success: true,
      data: summary
    });

  } catch (error) {
    logger.error('Failed to get payroll financial summary', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get payroll financial summary',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

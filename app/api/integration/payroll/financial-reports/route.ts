import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollFinancialIntegrationService } from '@/lib/services/integration/payroll-financial-integration';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/integration/payroll/financial-reports
 * Generate comprehensive payroll financial reports
 * Query parameters:
 * - startDate: start date for the report (required)
 * - endDate: end date for the report (required)
 * - departmentId: filter by department (optional)
 * - includeComparison: include comparison data (optional)
 * - comparisonStartDate: comparison period start date (optional)
 * - comparisonEndDate: comparison period end date (optional)
 * - includeForecasting: include forecasting data (optional)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const departmentId = searchParams.get('departmentId');
    const includeComparison = searchParams.get('includeComparison') === 'true';
    const comparisonStartDateParam = searchParams.get('comparisonStartDate');
    const comparisonEndDateParam = searchParams.get('comparisonEndDate');
    const includeForecasting = searchParams.get('includeForecasting') === 'true';

    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    logger.info('Generating payroll financial reports', LogCategory.INTEGRATION, {
      startDate,
      endDate,
      departmentId,
      includeComparison,
      includeForecasting,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Prepare options
    const options: any = {
      departmentId,
      includeComparison,
      includeForecasting
    };

    if (includeComparison && comparisonStartDateParam && comparisonEndDateParam) {
      options.comparisonStartDate = new Date(comparisonStartDateParam);
      options.comparisonEndDate = new Date(comparisonEndDateParam);
    }

    // Create financial integration service and generate report
    const financialIntegrationService = new PayrollFinancialIntegrationService();
    const report = await financialIntegrationService.generatePayrollFinancialReport(
      startDate,
      endDate,
      options
    );

    return NextResponse.json({
      success: true,
      data: report
    });

  } catch (error) {
    logger.error('Failed to generate payroll financial reports', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate payroll financial reports',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/integration/payroll/financial-reports
 * Update financial statements with payroll data
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { payrollRunId } = body;

    if (!payrollRunId) {
      return NextResponse.json(
        { error: 'Payroll run ID is required' },
        { status: 400 }
      );
    }

    logger.info('Updating financial statements with payroll data', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create financial integration service and update statements
    const financialIntegrationService = new PayrollFinancialIntegrationService();
    await financialIntegrationService.updateFinancialStatements(payrollRunId, (user._id as mongoose.Types.ObjectId).toString());

    logger.info('Financial statements updated successfully', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    return NextResponse.json({
      success: true,
      message: 'Financial statements updated successfully',
      payrollRunId
    });

  } catch (error) {
    logger.error('Failed to update financial statements', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update financial statements',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

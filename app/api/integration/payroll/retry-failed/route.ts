import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollAccountingAutomationService } from '@/lib/services/integration/payroll-accounting-automation';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/integration/payroll/retry-failed
 * Retry failed payroll-accounting integration for a specific payroll run
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { payrollRunId } = body;

    if (!payrollRunId) {
      return NextResponse.json(
        { error: 'Payroll run ID is required' },
        { status: 400 }
      );
    }

    logger.info('Retrying failed payroll integration', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create automation service and retry integration
    const automationService = new PayrollAccountingAutomationService();
    await automationService.retryIntegration(payrollRunId, (user._id as mongoose.Types.ObjectId).toString());

    logger.info('Payroll integration retry completed successfully', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    return NextResponse.json({
      success: true,
      message: 'Payroll integration retry completed successfully',
      payrollRunId
    });

  } catch (error) {
    logger.error('Payroll integration retry failed', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to retry payroll integration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollAccountingAutomationService } from '@/lib/services/integration/payroll-accounting-automation';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/integration/payroll/integration-status
 * Get integration status for payroll runs
 * Query parameters:
 * - payrollRunIds: comma-separated list of payroll run IDs (optional)
 * - limit: number of records to return (default: 50)
 * - offset: number of records to skip (default: 0)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const payrollRunIdsParam = searchParams.get('payrollRunIds');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Parse payroll run IDs if provided
    let payrollRunIds: string[] | undefined;
    if (payrollRunIdsParam) {
      payrollRunIds = payrollRunIdsParam.split(',').map(id => id.trim()).filter(id => id);
    }

    logger.info('Getting payroll integration status', LogCategory.INTEGRATION, {
      payrollRunIds,
      limit,
      offset,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create automation service and get status
    const automationService = new PayrollAccountingAutomationService();
    const integrationStatus = await automationService.getIntegrationStatus(payrollRunIds);

    // Apply pagination
    const paginatedResults = integrationStatus.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      data: paginatedResults,
      pagination: {
        total: integrationStatus.length,
        limit,
        offset,
        hasMore: offset + limit < integrationStatus.length
      }
    });

  } catch (error) {
    logger.error('Failed to get payroll integration status', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get integration status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

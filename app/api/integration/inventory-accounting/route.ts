import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { inventoryAccountingLinkService } from '@/services/integration/InventoryAccountingLinkService';

/**
 * GET /api/integration/inventory-accounting
 * Get inventory-accounting links
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const itemId = searchParams.get('itemId');
    const itemType = searchParams.get('itemType');
    const transactionId = searchParams.get('transactionId');
    const journalEntryId = searchParams.get('journalEntryId');
    const errorOnly = searchParams.get('errorOnly') === 'true';
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');

    // Get links based on parameters
    if (itemId && itemType) {
      // Get links for inventory item
      const links = await inventoryAccountingLinkService.getLinksForInventoryItem(
        itemId,
        itemType as any
      );

      return NextResponse.json(links);
    } else if (transactionId) {
      // Get links for inventory transaction
      const links = await inventoryAccountingLinkService.getLinksForInventoryTransaction(
        transactionId
      );

      return NextResponse.json(links);
    } else if (journalEntryId) {
      // Get links for journal entry
      const links = await inventoryAccountingLinkService.getLinksForJournalEntry(
        journalEntryId
      );

      return NextResponse.json(links);
    } else if (errorOnly) {
      // Get error links
      const result = await inventoryAccountingLinkService.getErrorLinks({
        page,
        limit,
        itemType: itemType as any,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined
      });

      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    logger.error('Error getting inventory-accounting links', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/integration/inventory-accounting/retry
 * Retry failed inventory-accounting link
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.linkId) {
      return NextResponse.json(
        { error: 'Missing required field: linkId' },
        { status: 400 }
      );
    }

    // Retry error link
    const link = await inventoryAccountingLinkService.retryErrorLink(
      body.linkId,
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Link retry initiated',
      data: link
    });
  } catch (error: unknown) {
    logger.error('Error retrying inventory-accounting link', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

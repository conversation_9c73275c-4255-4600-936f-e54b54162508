// app/api/integration/payroll-voucher/create/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollVoucherIntegrationService } from '@/lib/services/integration/payroll-voucher-integration';
import { UserRole } from '@/types/user-roles';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { successResponse } from '@/lib/backend/utils/api-response';
import { z } from 'zod';
import mongoose from 'mongoose';
import { IUser } from '@/models/User';

export const runtime = 'nodejs';

// Interface for formatted payroll run response
interface FormattedPayrollRun {
  id: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled';
  totalEmployees: number;
  totalGrossSalary: number;
  totalDeductions: number;
  totalTax: number;
  totalNetSalary: number;
  currency: string;
  createdBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvedBy?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
  };
  approvedAt?: Date;
  voucherStatus: 'not_created' | 'created' | 'pending_approval' | 'approved' | 'rejected' | 'posted';
}

// Interface for summary statistics
interface PayrollRunsSummary {
  totalPayrollRuns: number;
  totalAmount: number;
  totalEmployees: number;
  oldestRun: FormattedPayrollRun | null;
}

// Validation schema for voucher creation request
const createVoucherRequestSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required'),
  autoApprove: z.boolean().default(false),
});

/**
 * Create voucher from approved payroll run
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  let user: IUser | null = null;
  let payrollRunId: string | undefined = undefined;

  try {
    // Check authentication
    user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to create vouchers from payroll runs.',
        {
          userId: undefined,
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM
      );
    }

    // Check permissions - only HR, Finance, and Admin can create vouchers from payroll
    const allowedRoles = [UserRole.HR_MANAGER, UserRole.FINANCE_MANAGER, UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.ACCOUNTANT];
    if (!allowedRoles.includes(user.role as UserRole)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to create vouchers from payroll',
        'You do not have the required permissions to create vouchers from payroll runs. This action requires HR Manager, Finance Manager, or Admin privileges.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST',
          additionalData: { requiredRoles: allowedRoles }
        },
        403,
        ErrorSeverity.MEDIUM,
        `User role: ${user.role}. Required roles: ${allowedRoles.join(', ')}`,
        [
          'Contact your system administrator to request appropriate permissions',
          'Ensure you have HR Manager, Finance Manager, or Admin role assigned'
        ]
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = createVoucherRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_REQUEST_DATA',
        'Request validation failed',
        'The request data is invalid. Please check the required fields and try again.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST',
          additionalData: { validationErrors: validationResult.error.errors }
        },
        400,
        ErrorSeverity.MEDIUM,
        `Validation errors: ${validationResult.error.errors.map((e: any) => `${e.path.join('.')}: ${e.message}`).join(', ')}`,
        [
          'Ensure payrollRunId is provided and valid',
          'Check that autoApprove is a boolean value'
        ]
      );
    }

    const { payrollRunId: validatedPayrollRunId, autoApprove } = validationResult.data;
    payrollRunId = validatedPayrollRunId;

    // Create integration service instance
    const integrationService = new PayrollVoucherIntegrationService();

    // Create voucher from payroll run
    const voucher = await integrationService.createVoucherFromPayrollRun(payrollRunId, user._id?.toString() || '');

    // If auto-approve is requested and user has sufficient permissions
    if (autoApprove && ['SUPER_ADMIN', 'SYSTEM_ADMIN'].includes(user.role)) {
      try {
        const { VoucherApprovalService } = await import('@/lib/services/accounting/voucher-approval-service');
        const approvalService = new VoucherApprovalService();

        await approvalService.processApproval({
          voucherId: voucher._id?.toString() || '',
          approverId: user._id?.toString() || '',
          action: 'approve',
          comments: 'Auto-approved during voucher creation'
        });
      } catch (approvalError) {
        console.warn('Auto-approval failed, voucher created but requires manual approval:', approvalError);
      }
    }

    return successResponse(
      {
        voucher: {
          id: voucher._id,
          voucherNumber: voucher.voucherNumber,
          voucherType: voucher.voucherType,
          status: voucher.status,
          totalAmount: voucher.totalAmount,
          description: voucher.description,
          voucherCategory: voucher.voucherCategory,
          payrollRunId: voucher.payrollRunId,
          createdAt: voucher.createdAt,
          approvalWorkflow: voucher.approvalWorkflow
        }
      },
      'Voucher created successfully from payroll run',
      201
    );

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error creating voucher from payroll run:', error);

    // Handle specific error types with structured error responses
    if (errorMessage.includes('not found')) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'PAYROLL_RUN_NOT_FOUND',
        'Payroll run not found',
        'The specified payroll run could not be found. It may have been deleted or the ID is incorrect.',
        {
          userId: user?._id?.toString(),
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST',
          additionalData: { payrollRunId }
        },
        404,
        ErrorSeverity.MEDIUM,
        errorMessage,
        [
          'Verify the payroll run ID is correct',
          'Check if the payroll run still exists',
          'Ensure you have access to this payroll run'
        ]
      );
    }

    if (errorMessage.includes('must be approved')) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'PAYROLL_NOT_APPROVED',
        'Payroll run must be approved before creating voucher',
        'The payroll run must be approved before a voucher can be created from it.',
        {
          userId: user?._id?.toString(),
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST',
          additionalData: { payrollRunId }
        },
        400,
        ErrorSeverity.MEDIUM,
        errorMessage,
        [
          'Approve the payroll run first',
          'Contact HR or Finance to approve the payroll run',
          'Check the payroll run status'
        ]
      );
    }

    if (errorMessage.includes('already exists')) {
      return errorService.createApiResponse(
        ErrorType.CONFLICT,
        'VOUCHER_ALREADY_EXISTS',
        'Voucher already exists for this payroll run',
        'A voucher has already been created for this payroll run. Each payroll run can only have one voucher.',
        {
          userId: user?._id?.toString(),
          endpoint: '/api/integration/payroll-voucher/create',
          method: 'POST',
          additionalData: { payrollRunId }
        },
        409,
        ErrorSeverity.MEDIUM,
        errorMessage,
        [
          'Check existing vouchers for this payroll run',
          'Use the existing voucher instead of creating a new one'
        ]
      );
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'VOUCHER_CREATION_FAILED',
      'Failed to create voucher from payroll run',
      'An unexpected error occurred while creating the voucher. Please try again or contact support if the problem persists.',
      {
        userId: user?._id?.toString(),
        endpoint: '/api/integration/payroll-voucher/create',
        method: 'POST',
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH,
      errorMessage,
      [
        'Try again in a few moments',
        'Check if the payroll run data is valid',
        'Contact system administrator if the problem persists'
      ]
    );
  }
}

/**
 * Get payroll runs ready for voucher creation
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const allowedRoles = [UserRole.HR_MANAGER, UserRole.FINANCE_MANAGER, UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.ACCOUNTANT];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view payroll runs' },
        { status: 403 }
      );
    }

    // Create integration service instance
    const integrationService = new PayrollVoucherIntegrationService();

    // Get payroll runs ready for voucher creation
    const payrollRuns = await integrationService.getPayrollRunsReadyForVouchers();

    // Format response data
    const formattedPayrollRuns: FormattedPayrollRun[] = payrollRuns.map(run => ({
      id: run._id,
      name: run.name,
      description: run.description,
      payPeriod: run.payPeriod,
      status: run.status,
      totalEmployees: run.totalEmployees,
      totalGrossSalary: run.totalGrossSalary,
      totalDeductions: run.totalDeductions,
      totalTax: run.totalTax,
      totalNetSalary: run.totalNetSalary,
      currency: run.currency,
      createdBy: run.createdBy,
      approvedBy: run.approvedBy,
      approvedAt: run.approvedAt,
      voucherStatus: run.voucherStatus || 'not_created'
    }));

    // Calculate summary statistics
    const summary: PayrollRunsSummary = {
      totalPayrollRuns: formattedPayrollRuns.length,
      totalAmount: formattedPayrollRuns.reduce((sum, run) => sum + run.totalNetSalary, 0),
      totalEmployees: formattedPayrollRuns.reduce((sum, run) => sum + run.totalEmployees, 0),
      oldestRun: formattedPayrollRuns.length > 0 ?
        formattedPayrollRuns
          .filter(run => run.approvedAt) // Filter out runs without approvedAt
          .reduce((oldest, run) => {
            if (!oldest.approvedAt || !run.approvedAt) return oldest;
            return new Date(run.approvedAt) < new Date(oldest.approvedAt) ? run : oldest;
          }, formattedPayrollRuns.find(run => run.approvedAt) || formattedPayrollRuns[0])
        : null
    };

    return NextResponse.json({
      success: true,
      data: {
        payrollRuns: formattedPayrollRuns,
        summary,
        userPermissions: {
          canCreateVouchers: true,
          canAutoApprove: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN].includes(user.role)
        }
      }
    });

  } catch (error: any) {
    console.error('Error getting payroll runs ready for vouchers:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

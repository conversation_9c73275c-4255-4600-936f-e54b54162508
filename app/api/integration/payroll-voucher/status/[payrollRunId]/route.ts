import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollVoucherIntegrationService } from '@/lib/services/integration/payroll-voucher-integration';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * Get voucher status for a specific payroll run
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ payrollRunId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const allowedRoles = [UserRole.HR_MANAGER, UserRole.FINANCE_MANAGER, UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.ACCOUNTANT];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view payroll-voucher status' },
        { status: 403 }
      );
    }

    // Resolve params
    const { payrollRunId } = await params;

    // Create integration service instance
    const integrationService = new PayrollVoucherIntegrationService();

    // Get voucher status for payroll run
    const statusInfo = await integrationService.getVoucherStatusForPayrollRun(payrollRunId);

    // Determine next actions based on current status
    const nextActions = [];
    
    if (statusInfo.payrollRun.voucherStatus === 'not_created' && statusInfo.payrollRun.status === 'approved') {
      nextActions.push({
        action: 'create_voucher',
        label: 'Create Voucher',
        description: 'Create a payment voucher for this payroll run',
        available: true
      });
    }
    
    if (statusInfo.voucher && statusInfo.voucher.status === 'pending_approval') {
      // Check if user can approve based on role and approval level
      const canApprove = 
        (user.role === 'HR_MANAGER' && statusInfo.voucher.currentApprover?.role === 'HR_MANAGER') ||
        (user.role === 'FINANCE_MANAGER' && statusInfo.voucher.currentApprover?.role === 'FINANCE_MANAGER') ||
        user.role === 'SUPER_ADMIN';
        
      nextActions.push({
        action: 'approve_voucher',
        label: 'Approve Voucher',
        description: 'Approve the voucher for payment processing',
        available: canApprove
      });
      
      nextActions.push({
        action: 'reject_voucher',
        label: 'Reject Voucher',
        description: 'Reject the voucher with comments',
        available: canApprove
      });
    }
    
    if (statusInfo.voucher && statusInfo.voucher.status === 'approved') {
      const canPost = ['FINANCE_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT'].includes(user.role);
      nextActions.push({
        action: 'post_voucher',
        label: 'Post Voucher',
        description: 'Post the voucher and mark payroll as paid',
        available: canPost
      });
    }

    // Calculate workflow progress
    let workflowProgress = 0;
    if (statusInfo.payrollRun.status === 'approved') workflowProgress = 25;
    if (statusInfo.payrollRun.voucherStatus === 'created') workflowProgress = 50;
    if (statusInfo.voucher?.status === 'approved') workflowProgress = 75;
    if (statusInfo.voucher?.status === 'posted') workflowProgress = 100;

    return NextResponse.json({
      success: true,
      data: {
        payrollRun: statusInfo.payrollRun,
        voucher: statusInfo.voucher,
        workflowProgress,
        nextActions,
        timeline: [
          {
            step: 'Payroll Approved',
            status: statusInfo.payrollRun.status === 'approved' ? 'completed' : 'pending',
            date: statusInfo.payrollRun.status === 'approved' ? new Date() : null
          },
          {
            step: 'Voucher Created',
            status: statusInfo.payrollRun.voucherStatus === 'created' ? 'completed' : 
                   statusInfo.payrollRun.status === 'approved' ? 'pending' : 'disabled',
            date: statusInfo.voucher?.createdAt || null
          },
          {
            step: 'Voucher Approved',
            status: statusInfo.voucher?.status === 'approved' ? 'completed' :
                   statusInfo.voucher?.status === 'pending_approval' ? 'pending' : 'disabled',
            date: statusInfo.voucher?.approvedAt || null
          },
          {
            step: 'Payment Posted',
            status: statusInfo.voucher?.status === 'posted' ? 'completed' :
                   statusInfo.voucher?.status === 'approved' ? 'pending' : 'disabled',
            date: statusInfo.voucher?.status === 'posted' ? new Date() : null
          }
        ],
        userPermissions: {
          canCreateVoucher: ['HR_MANAGER', 'FINANCE_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT'].includes(user.role),
          canApproveVoucher: ['HR_MANAGER', 'FINANCE_MANAGER', 'SUPER_ADMIN'].includes(user.role),
          canPostVoucher: ['FINANCE_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT'].includes(user.role),
          canViewDetails: true
        }
      }
    });

  } catch (error: any) {
    console.error('Error getting payroll-voucher status:', error);
    
    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Update voucher status (for posting)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ payrollRunId: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions for posting
    const allowedRoles = [UserRole.FINANCE_MANAGER, UserRole.SUPER_ADMIN, UserRole.ACCOUNTANT];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to post vouchers' },
        { status: 403 }
      );
    }

    // Resolve params
    const { payrollRunId } = await params;

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (action !== 'post_voucher') {
      return NextResponse.json(
        { error: 'Bad Request: Only post_voucher action is supported' },
        { status: 400 }
      );
    }

    // Create integration service instance
    const integrationService = new PayrollVoucherIntegrationService();

    // Get current status to find voucher ID
    const statusInfo = await integrationService.getVoucherStatusForPayrollRun(payrollRunId);
    
    if (!statusInfo.voucher) {
      return NextResponse.json(
        { error: 'Bad Request: No voucher found for this payroll run' },
        { status: 400 }
      );
    }

    if (statusInfo.voucher.status !== 'approved') {
      return NextResponse.json(
        { error: 'Bad Request: Voucher must be approved before posting' },
        { status: 400 }
      );
    }

    // Process voucher posting
    await integrationService.processVoucherPosting(statusInfo.voucher.id, user.id);

    return NextResponse.json({
      success: true,
      message: 'Voucher posted successfully. Payroll marked as paid.',
      data: {
        payrollRunId,
        voucherId: statusInfo.voucher.id,
        postedBy: user.id,
        postedAt: new Date()
      }
    });

  } catch (error: any) {
    console.error('Error posting voucher:', error);
    
    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Payroll run or voucher not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/procurement/PurchaseOrderService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

const purchaseOrderService = new PurchaseOrderService();
const auditService = new AuditDeletionService();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'PURCHASE_ORDER_GET_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view purchase orders.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'PURCHASE_ORDER_GET_FORBIDDEN',
        'Insufficient permissions to view purchase orders',
        'You do not have permission to view purchase order details.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Get purchase order
    const order = await purchaseOrderService.findById(id);

    if (!order) {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: order
    });

  } catch (error) {
    logger.error('Error fetching purchase order', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PURCHASE_ORDER_GET_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch purchase order',
      'Unable to retrieve the purchase order. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        orderId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Contact support if the problem persists'
      ]
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'PURCHASE_ORDER_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update purchase orders.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'PURCHASE_ORDER_UPDATE_FORBIDDEN',
        'Insufficient permissions to update purchase orders',
        'You do not have permission to update purchase order records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse request body
    const body = await request.json();

    // Update purchase order
    const updatedOrder = await purchaseOrderService.updatePurchaseOrder(id, {
      ...body,
      updatedBy: user.id
    });

    if (!updatedOrder) {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    logger.info('Purchase order updated', LogCategory.PROCUREMENT, {
      orderId: id,
      orderNumber: updatedOrder.orderNumber,
      updatedBy: user.id
    });

    return NextResponse.json({
      success: true,
      data: updatedOrder,
      message: 'Purchase order updated successfully'
    });

  } catch (error) {
    logger.error('Error updating purchase order', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PURCHASE_ORDER_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update purchase order',
      'Unable to update the purchase order. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        orderId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check the data format and try again',
        'Ensure all required fields are provided',
        'Contact support if the problem persists'
      ]
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'PURCHASE_ORDER_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete purchase orders.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'PURCHASE_ORDER_DELETE_FORBIDDEN',
        'Insufficient permissions to delete purchase orders',
        'You do not have permission to delete purchase order records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          orderId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse request body for audit information
    let deletionReason = 'Manual deletion';
    let context = {};
    
    try {
      const body = await request.json();
      deletionReason = body.deletionReason || deletionReason;
      context = body.context || {};
    } catch {
      // Body is optional for individual deletes
    }

    // Get purchase order details for audit
    const order = await purchaseOrderService.findById(id);
    if (!order) {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    // Check if order can be deleted (business rules)
    if (['confirmed', 'partially_received', 'received'].includes(order.status)) {
      return NextResponse.json(
        { error: `Cannot delete purchase order with status: ${order.status}` },
        { status: 400 }
      );
    }

    // Create audit record before deletion
    const auditRecord = await auditService.createDeletionRecord({
      entityType: 'PurchaseOrder',
      entityId: id,
      entityData: {
        orderNumber: order.orderNumber,
        supplierId: order.supplierId,
        orderDate: order.orderDate,
        expectedDeliveryDate: order.expectedDeliveryDate,
        status: order.status,
        items: order.items,
        subtotal: order.subtotal,
        tax: order.tax,
        total: order.total,
        paymentTerms: order.paymentTerms,
        shippingTerms: order.shippingTerms,
        notes: order.notes
      },
      deletionReason,
      deletedBy: user.id,
      context: {
        department: 'Procurement',
        fiscalYear: new Date().getFullYear().toString(),
        ...context
      }
    });

    // Delete purchase order
    const deletedOrder = await purchaseOrderService.deletePurchaseOrder(id);

    logger.info('Purchase order deleted with audit trail', LogCategory.PROCUREMENT, {
      orderId: id,
      orderNumber: deletedOrder.orderNumber,
      deletedBy: user.id,
      auditRecordId: auditRecord._id,
      reason: deletionReason
    });

    return NextResponse.json({
      success: true,
      message: 'Purchase order deleted successfully with audit trail'
    });

  } catch (error) {
    logger.error('Error deleting purchase order', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PURCHASE_ORDER_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete purchase order',
      'Unable to delete the purchase order. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        orderId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if the purchase order has active dependencies',
        'Ensure the order is in deletable status (draft, sent, cancelled)',
        'Contact support if the problem persists'
      ]
    );
  }
}

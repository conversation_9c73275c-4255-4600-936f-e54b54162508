import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { contractService, CreateContractData, ContractFilters } from '@/lib/backend/services/procurement/ContractService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for contract creation
const createContractSchema = z.object({
  supplierId: z.string().min(1, 'Supplier ID is required'),
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description too long'),
  contractType: z.enum(['service', 'supply', 'maintenance', 'lease', 'consulting', 'construction']),
  value: z.number().min(0, 'Value must be positive'),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  startDate: z.string().transform(val => new Date(val)),
  endDate: z.string().transform(val => new Date(val)),
  autoRenewal: z.boolean().optional(),
  renewalTerms: z.string().optional(),
  terms: z.array(z.string()),
  paymentTerms: z.string().min(1, 'Payment terms are required'),
  deliveryTerms: z.string().optional(),
  penaltyClause: z.string().optional(),
  warrantyTerms: z.string().optional(),
  performanceMetrics: z.array(z.object({
    metric: z.string(),
    target: z.string(),
    measurement: z.string(),
    frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'annually'])
  })).optional(),
  budgetCategory: z.string().optional(),
  costCenter: z.string().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountRate: z.number().min(0).max(100).optional(),
  complianceRequirements: z.array(z.string()).optional(),
  legalReviewRequired: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  riskLevel: z.enum(['low', 'medium', 'high']).optional(),
  notes: z.string().optional()
});

// Validation schema for contract search filters
const contractFiltersSchema = z.object({
  supplierId: z.string().optional(),
  contractType: z.string().optional(),
  status: z.string().optional(),
  startDateFrom: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  startDateTo: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  endDateFrom: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  endDateTo: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  valueMin: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  valueMax: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  priority: z.string().optional(),
  riskLevel: z.string().optional(),
  tags: z.string().transform(val => val ? val.split(',') : undefined).optional(),
  search: z.string().optional(),
  page: z.string().transform(val => val ? parseInt(val) : 1).optional(),
  limit: z.string().transform(val => val ? parseInt(val) : 20).optional()
});

// Response type for successful contract operations
interface ContractResponse {
  success: true;
  data: unknown;
  message?: string;
}

// Response type for contract list operations
interface ContractListResponse {
  success: true;
  data: unknown[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * GET /api/procurement/contracts
 * Get contracts with optional filtering and pagination
 */
export async function GET(request: NextRequest): Promise<NextResponse<ContractListResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_FORBIDDEN',
        'Insufficient permissions to view contracts',
        'You do not have permission to view contract information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to access contracts without proper permissions',
        [
          'Contact your administrator to request contract access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate filters
    const validationResult = contractFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_INVALID_FILTERS',
        'Invalid filter parameters',
        'The search filters provided are not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        validationResult.error.errors[0].message,
        [
          'Check the format of date filters (YYYY-MM-DD)',
          'Ensure numeric values are valid numbers',
          'Verify boolean filters use "true" or "false"'
        ]
      );
    }

    const {
      supplierId,
      contractType,
      status,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      valueMin,
      valueMax,
      priority,
      riskLevel,
      tags,
      search,
      page = 1,
      limit = 20
    } = validationResult.data;

    // Build filters object
    const filters: ContractFilters = {
      supplierId,
      contractType,
      status,
      priority,
      riskLevel,
      tags,
      search
    };

    if (startDateFrom || startDateTo) {
      filters.startDate = {
        from: startDateFrom,
        to: startDateTo
      };
    }

    if (endDateFrom || endDateTo) {
      filters.endDate = {
        from: endDateFrom,
        to: endDateTo
      };
    }

    if (valueMin !== undefined || valueMax !== undefined) {
      filters.value = {
        min: valueMin,
        max: valueMax
      };
    }

    // Get contracts
    const result = await contractService.searchContracts(filters, page, limit);

    return NextResponse.json({
      success: true,
      data: result.contracts,
      pagination: result.pagination
    } as ContractListResponse);

  } catch (error: unknown) {
    logger.error('Error getting contracts', LogCategory.PROCUREMENT, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CONTRACT_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch contracts',
      'Unable to retrieve contract information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * POST /api/procurement/contracts
 * Create a new contract
 */
export async function POST(request: NextRequest): Promise<NextResponse<ContractResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to create contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_CREATE_FORBIDDEN',
        'Insufficient permissions to create contracts',
        'You do not have permission to create contract records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to create contract without proper permissions',
        [
          'Contact your administrator to request contract creation permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = createContractSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_VALIDATION_ERROR',
        'Invalid contract data',
        'The contract information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that values are in the correct format',
          'Verify date formats are correct',
          'Ensure arrays contain valid items'
        ]
      );
    }

    const contractData: CreateContractData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Validate date range
    if (contractData.startDate >= contractData.endDate) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CONTRACT_INVALID_DATES',
        'Invalid contract dates',
        'The contract start date must be before the end date.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          startDate: contractData.startDate.toISOString(),
          endDate: contractData.endDate.toISOString()
        },
        400,
        ErrorSeverity.LOW,
        'Start date is not before end date',
        [
          'Adjust the start date to be before the end date',
          'Verify the date formats are correct',
          'Check the contract duration requirements'
        ]
      );
    }

    // Create contract
    const contract = await contractService.createContract(contractData);

    return NextResponse.json({
      success: true,
      message: 'Contract created successfully',
      data: contract
    } as ContractResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating contract', LogCategory.PROCUREMENT, error);

    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Supplier') && error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'CONTRACT_SUPPLIER_NOT_FOUND',
          'Supplier not found',
          'The specified supplier could not be found. Please verify the supplier ID.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the supplier ID is correct',
            'Ensure the supplier is active in the system',
            'Contact procurement team if needed'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CONTRACT_CREATE_ERROR',
      error instanceof Error ? error.message : 'Failed to create contract',
      'Unable to create the contract record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try creating the contract again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/procurement/SupplierService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

const supplierService = new SupplierService();
const auditService = new AuditDeletionService();

// Validation schema for supplier update
const updateSupplierSchema = z.object({
  name: z.string().min(1, 'Supplier name is required').max(200, 'Name too long').optional(),
  contactPerson: z.string().optional(),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  website: z.string().url().optional(),
  category: z.array(z.string()).min(1, 'At least one category is required').optional(),
  taxId: z.string().optional(),
  paymentTerms: z.string().optional(),
  bankName: z.string().optional(),
  accountNumber: z.string().optional(),
  status: z.enum(['active', 'inactive', 'blacklisted']).optional(),
  rating: z.number().min(1).max(5).optional(),
  notes: z.string().max(2000, 'Notes too long').optional(),
  attachments: z.array(z.string()).optional()
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_GET_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view supplier details.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_GET_FORBIDDEN',
        'Insufficient permissions to view supplier',
        'You do not have permission to view supplier details.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    const supplier = await supplierService.findById(id);
    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: supplier
    });

  } catch (error) {
    logger.error('Error getting supplier', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_GET_ERROR',
      error instanceof Error ? error.message : 'Failed to get supplier',
      'Unable to retrieve supplier information.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        supplierId: id
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_UPDATE_FORBIDDEN',
        'Insufficient permissions to update supplier',
        'You do not have permission to update supplier records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Check if supplier exists
    const existingSupplier = await supplierService.findById(id);
    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // Parse and validate request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    const validationResult = updateSupplierSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid supplier data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Update supplier
    const updatedSupplier = await supplierService.updateSupplier(id, validationResult.data);

    logger.info('Supplier updated', LogCategory.PROCUREMENT, {
      supplierId: updatedSupplier.supplierId,
      supplierName: updatedSupplier.name,
      updatedBy: user.id
    });

    return NextResponse.json({
      success: true,
      data: updatedSupplier,
      message: 'Supplier updated successfully'
    });

  } catch (error) {
    logger.error('Error updating supplier', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update supplier',
      'Unable to update supplier information.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        supplierId: id
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only managers and admins can delete
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_DELETE_FORBIDDEN',
        'Insufficient permissions to delete supplier',
        'You do not have permission to delete supplier records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Get supplier details for audit
    const supplier = await supplierService.findById(id);
    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // Parse request body for audit information
    let deletionReason = 'Manual deletion';
    let context = {};

    try {
      const body = await request.json();
      deletionReason = body.deletionReason || deletionReason;
      context = body.context || {};
    } catch {
      // Body is optional for individual deletes
    }

    // Create audit record before deletion
    const auditRecord = await auditService.createDeletionRecord({
      entityType: 'Supplier',
      entityId: id,
      entityData: {
        supplierId: supplier.supplierId,
        name: supplier.name,
        category: supplier.category,
        status: supplier.status,
        contactPerson: supplier.contactPerson,
        email: supplier.email,
        phone: supplier.phone,
        address: supplier.address,
        city: supplier.city,
        country: supplier.country
      },
      deletionReason,
      deletedBy: user.id,
      context: {
        department: 'Procurement',
        fiscalYear: new Date().getFullYear().toString(),
        bulkOperation: false,
        ...context
      }
    });

    // Delete the supplier
    await supplierService.deleteSupplier(id);

    logger.info('Supplier deleted', LogCategory.PROCUREMENT, {
      supplierId: supplier.supplierId,
      supplierName: supplier.name,
      deletedBy: user.id,
      auditRecordId: auditRecord._id
    });

    return NextResponse.json({
      success: true,
      message: 'Supplier deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting supplier', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete supplier',
      'Unable to delete supplier.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        supplierId: id
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

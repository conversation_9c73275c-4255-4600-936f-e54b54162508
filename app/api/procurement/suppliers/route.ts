import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService, ISupplier } from '@/lib/backend/services/procurement/SupplierService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Initialize service
const supplierService = new SupplierService();

// Validation schema for supplier creation (matching existing ISupplier interface)
const createSupplierSchema = z.object({
  name: z.string().min(1, 'Supplier name is required').max(200, 'Name too long'),
  supplierId: z.string().optional(), // Will be auto-generated if not provided
  contactPerson: z.string().optional(),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  website: z.string().url().optional(),
  category: z.array(z.string()).min(1, 'At least one category is required'),
  taxId: z.string().optional(),
  paymentTerms: z.string().optional(),
  bankName: z.string().optional(),
  accountNumber: z.string().optional(),
  status: z.enum(['active', 'inactive', 'blacklisted']).default('active'),
  rating: z.number().min(1).max(5).optional(),
  notes: z.string().max(2000, 'Notes too long').optional(),
  attachments: z.array(z.string()).optional()
});

// Validation schema for supplier search filters
const supplierFiltersSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  category: z.string().optional(),
  page: z.string().transform(val => val ? parseInt(val) : 1).optional(),
  limit: z.string().transform(val => val ? parseInt(val) : 20).optional()
});

// Response type for successful supplier operations
interface SupplierResponse {
  success: true;
  data: unknown;
  message?: string;
}

// Response type for supplier list operations
interface SupplierListResponse {
  success: true;
  data: unknown[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * GET /api/procurement/suppliers
 * Get suppliers with optional filtering and pagination
 */
export async function GET(request: NextRequest): Promise<NextResponse<SupplierListResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_FORBIDDEN',
        'Insufficient permissions to view suppliers',
        'You do not have permission to view supplier information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to access suppliers without proper permissions',
        [
          'Contact your administrator to request supplier access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate filters
    const validationResult = supplierFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'SUPPLIER_INVALID_FILTERS',
        'Invalid filter parameters',
        'The search filters provided are not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        validationResult.error.errors[0].message,
        [
          'Check the format of filter parameters',
          'Ensure numeric values are valid numbers',
          'Verify boolean filters use "true" or "false"'
        ]
      );
    }

    const { page = 1, limit = 20, search, status, category } = validationResult.data;

    // Get suppliers based on search query or filters
    let result;
    if (search) {
      result = await supplierService.searchSuppliers(search, {
        status: status as ISupplier['status'],
        category,
        page,
        limit
      });
    } else if (category) {
      result = await supplierService.getByCategory(category, {
        status: status as ISupplier['status'],
        page,
        limit
      });
    } else if (status === 'active') {
      result = await supplierService.getActiveSuppliers({
        page,
        limit
      });
    } else {
      // Get all suppliers with pagination
      const filter: any = {};
      if (status) {
        filter.status = status;
      }
      result = await supplierService.paginate(filter, page, limit, { name: 1 }, ['createdBy']);
    }

    return NextResponse.json({
      success: true,
      data: result.docs,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.totalDocs,
        pages: result.totalPages
      }
    } as SupplierListResponse);

  } catch (error: unknown) {
    logger.error('Error getting suppliers', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch suppliers',
      'Unable to retrieve supplier information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * POST /api/procurement/suppliers
 * Create a new supplier
 */
export async function POST(request: NextRequest): Promise<NextResponse<SupplierResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_CREATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to create suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_CREATE_FORBIDDEN',
        'Insufficient permissions to create suppliers',
        'You do not have permission to create supplier records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to create supplier without proper permissions',
        [
          'Contact your administrator to request supplier creation permissions',
          'Ensure you are logged in with a manager-level account'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'SUPPLIER_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify array and object structures are correct'
        ]
      );
    }

    // Validate request data
    const validationResult = createSupplierSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'SUPPLIER_VALIDATION_ERROR',
        'Invalid supplier data',
        'The supplier information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that email addresses are valid',
          'Verify phone numbers are in correct format',
          'Ensure arrays contain valid items'
        ]
      );
    }

    const supplierData: Partial<ISupplier> = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create supplier
    const supplier = await supplierService.createSupplier(supplierData);

    return NextResponse.json({
      success: true,
      message: 'Supplier created successfully',
      data: supplier
    } as SupplierResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating supplier', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Supplier code already exists')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'SUPPLIER_CODE_EXISTS',
          'Supplier code already exists',
          'A supplier with this code already exists. Please choose a different code.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.LOW,
          error.message,
          [
            'Choose a unique supplier code',
            'Check existing suppliers for code conflicts',
            'Use a descriptive and unique identifier'
          ]
        );
      }

      if (error.message.includes('Email already exists')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'SUPPLIER_EMAIL_EXISTS',
          'Email already exists',
          'A supplier with this email address already exists. Please use a different email.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.LOW,
          error.message,
          [
            'Use a different email address',
            'Check if the supplier already exists in the system',
            'Contact the existing supplier to update their information'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_CREATE_ERROR',
      error instanceof Error ? error.message : 'Failed to create supplier',
      'Unable to create the supplier record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try creating the supplier again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

// app/api/procurement/supplier/route.ts
import { NextRequest, NextResponse } from 'next/server';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  category?: string;
  status?: string;
}


import { getCurrentUser } from '@/lib/backend/auth/auth';
import { SupplierService } from '@/lib/backend/services/procurement/SupplierService';
import { SupplierImportExportService } from '@/lib/backend/services/procurement/SupplierImportExportService';
import { SupplierReportingService } from '@/lib/backend/services/procurement/SupplierReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Initialize services
const supplierService = new SupplierService();
const importExportService = new SupplierImportExportService();
const reportingService = new SupplierReportingService();

/**
 * GET handler for suppliers
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get supplier by ID
      const supplier = await supplierService.getSupplierDetails(id);

      if (!supplier) {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }

      return NextResponse.json(supplier);
    } else if (searchParams.get('performance') === 'true' && id) {
      // Get supplier performance
      const performance = await supplierService.getSupplierPerformance(id);

      return NextResponse.json(performance);
    } else if (format) {
      // Export suppliers
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      // Add filters
      if (category) {
        filter.category = category;
      }

      if (status) {
        filter.status = status;
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'suppliers.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'suppliers.csv';
      } else if (format === 'pdf' && id) {
        buffer = await reportingService.generateSupplierPdf(id);
        contentType = 'application/pdf';
        filename = `supplier-${id}.pdf`;
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      return new NextResponse(new Uint8Array(buffer), {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          category: category || undefined,
          status: status || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'performance') {
        // Generate performance report
        const reportData = await reportingService.generatePerformanceReport({
          category: category || undefined,
          minOrders: parseInt(searchParams.get('minOrders') || '1')
        });

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
      }
    } else if (searchParams.get('active') === 'true') {
      // Get active suppliers
      // Build options
      interface SupplierOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        category?: string;
        [key: string]: any;
      }

      const options: SupplierOptions = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (category) {
        options.category = category;
      }

      // Get suppliers
      // Cast to any to bypass TypeScript's type checking
      const result = await supplierService.getActiveSuppliers(options as any);

      return NextResponse.json(result);
    } else if (category) {
      // Get suppliers by category
      // Build options
      interface CategoryOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        [key: string]: any;
      }

      const options: CategoryOptions = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      // Get suppliers
      // Cast to any to bypass TypeScript's type checking
      const result = await supplierService.getByCategory(category, options as any);

      return NextResponse.json(result);
    } else if (search) {
      // Search suppliers
      // Build options
      interface SearchOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        category?: string;
        [key: string]: any;
      }

      const options: SearchOptions = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (category) {
        options.category = category;
      }

      // Search suppliers
      // Cast to any to bypass TypeScript's type checking
      const result = await supplierService.searchSuppliers(search, options as any);

      return NextResponse.json(result);
    } else {
      // Get all suppliers with pagination
      // Build filter
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      // Get suppliers
      const result = await supplierService.paginate(filter, page, limit, { name: 1 }, ['createdBy']);

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in suppliers GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for suppliers
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // Create supplier
    const supplier = await supplierService.createSupplier(body);

    return NextResponse.json(supplier, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in suppliers POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for suppliers
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if status update
    if (body.id && body.status) {
      // Update status
      const updatedSupplier = await supplierService.updateStatus(
        body.id,
        body.status,
        user.id,
        body.notes
      );

      return NextResponse.json(updatedSupplier);
    } else if (body.id) {
      // Update supplier
      const updatedSupplier = await supplierService.updateById(body.id, body);

      return NextResponse.json(updatedSupplier);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in suppliers PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}


// app/api/procurement/categories/[id]/delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { AuditDeletionContext } from '@/lib/services/audit/audit-deletion-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';



/**
 * DELETE /api/procurement/categories/[id]/delete
 * Delete a procurement category with audit trail
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Get current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete procurement categories',
        { endpoint: `/api/procurement/categories/${id}/delete`, method: 'DELETE' },
        401
      );
    }

    // Check permissions
    if (!['admin', 'procurement_manager', 'super_admin'].includes(currentUser.role)) {
      return errorService.createApiResponse(
        ErrorType.AUTHORIZATION,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to delete procurement categories',
        'You do not have permission to delete procurement categories',
        { 
          userId: currentUser.id,
          userRole: currentUser.role,
          endpoint: `/api/procurement/categories/${id}/delete`,
          method: 'DELETE'
        },
        403
      );
    }

    // Parse request body
    const body = await request.json();
    const { deletionReason, context } = body;

    // Validate deletion reason
    if (!deletionReason || typeof deletionReason !== 'string' || deletionReason.trim().length < 10) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_DELETION_REASON',
        'Deletion reason is required and must be at least 10 characters',
        'Please provide a detailed reason for deleting this procurement category (minimum 10 characters)',
        { 
          userId: currentUser.id,
          endpoint: `/api/procurement/categories/${id}/delete`,
          method: 'DELETE'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Deletion reason validation failed for audit compliance requirements',
        [
          'Provide a clear and detailed reason for the deletion',
          'Ensure the reason is at least 10 characters long',
          'Include business justification for the deletion'
        ]
      );
    }

    // Prepare audit context
    const auditContext: AuditDeletionContext = {
      deletedBy: currentUser.id,
      deletionReason: deletionReason.trim(),
      deletionType: 'single',
      userInfo: {
        id: currentUser.id,
        name: currentUser.name || 'Unknown User',
        email: currentUser.email || '<EMAIL>',
        role: currentUser.role
      },
      context: {
        fiscalYear: context?.fiscalYear,
        department: context?.department || 'Procurement',
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: request.headers.get('x-session-id')
      }
    };

    logger.info('Processing category deletion request', LogCategory.PROCUREMENT, {
      categoryId: id,
      userId: currentUser.id,
      deletionReason: deletionReason.trim()
    });

    // Perform audit-compliant deletion
    const result = await procurementCategoryService.deleteCategory(id, auditContext);

    logger.info('Category deletion completed successfully', LogCategory.PROCUREMENT, {
      categoryId: id,
      userId: currentUser.id,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated
    });

    return NextResponse.json({
      success: true,
      message: 'Procurement category deleted successfully with audit trail',
      data: {
        deletedCount: result.deletedCount,
        auditRecordsCreated: result.auditRecordsCreated,
        auditCompliance: result.details?.auditCompliance,
        deletionId: result.auditRecordIds?.[0]
      }
    });

  } catch (error) {
    logger.error('Error deleting procurement category', LogCategory.PROCUREMENT, error);

    // Handle structured errors
    if (error instanceof Error && error.message.includes('Unable to delete')) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CATEGORY_DELETE_CONSTRAINT',
        error.message,
        error.message,
        {
          categoryId: id,
          endpoint: `/api/procurement/categories/${id}/delete`,
          method: 'DELETE'
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Unknown error occurred',
      'An unexpected error occurred while deleting the procurement category. Please try again.',
      {
        categoryId: id,
        endpoint: `/api/procurement/categories/${id}/delete`,
        method: 'DELETE'
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if the category still exists',
        'Contact support if the problem persists'
      ]
    );
  }
}

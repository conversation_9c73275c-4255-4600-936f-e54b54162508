// app/api/procurement/categories/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import * as XLSX from 'xlsx';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name'
];

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'name': 'name',
  'Name': 'name',
  'Code': 'code',
  'Description': 'description',
  'Category Type': 'categoryType',
  'Parent Category': 'parentCategory',
  'Budget Category': 'budgetCategory',
  'Approval Limit': 'approvalLimit',
  'Required Approvers': 'requiredApprovers',
  'Default Suppliers': 'defaultSuppliers',
  'Restricted Suppliers': 'restrictedSuppliers',
  'Compliance Requirements': 'complianceRequirements',
  'Risk Level': 'riskLevel',
  'Lead Time': 'leadTime',
  'Quality Standards': 'qualityStandards',
  'Environmental Impact': 'environmentalImpact',
  'Tags': 'tags',
  'Notes': 'notes',
  'Status': 'status'
};

// Define roles that can manage categories
const CATEGORY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.PROCUREMENT_MANAGER,
  UserRole.PROCUREMENT_OFFICER
];

interface ImportResult {
  totalRows: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: Array<{ row: number; error: string }>;
  imported: Array<{ row: number; name: string; code?: string }>;
  skipped: Array<{ row: number; name: string; reason: string }>;
}

/**
 * POST handler for bulk importing procurement categories
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, CATEGORY_ADMIN_ROLES);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Debug: Check model registration
    logger.debug('Model registration check', LogCategory.IMPORT, {
      registeredModels: Object.keys(mongoose.models),
      categoryModelExists: !!mongoose.models.ProcurementCategory,
      userId: user.id
    });

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 });
    }

    // Read file
    const buffer = await file.arrayBuffer();

    logger.info('Processing procurement category bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    });

    const workbook = XLSX.read(buffer, { type: 'array' });

    // Get first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // Convert to JSON
    const rawData = XLSX.utils.sheet_to_json(worksheet, { defval: null });

    if (!rawData || rawData.length === 0) {
      return NextResponse.json({ error: 'No data found in the file' }, { status: 400 });
    }

    logger.info('File parsed successfully', LogCategory.IMPORT, {
      rowCount: rawData.length,
      userId: user.id
    });

    // Initialize result
    const result: ImportResult = {
      totalRows: rawData.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      imported: [],
      skipped: []
    };

    // Get existing categories for validation
    const existingCategories = await procurementCategoryService.getAllCategories();
    const existingCategoryNames = new Set(existingCategories.map(cat => cat.name.toLowerCase()));
    const existingCategoryCodes = new Set(existingCategories.map(cat => cat.code?.toLowerCase()).filter(Boolean));

    // Process each row
    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i] as Record<string, any>;

      try {
        // Normalize column names
        const normalizedRow: Record<string, any> = {};
        for (const [key, value] of Object.entries(row)) {
          const normalizedKey = COLUMN_DISPLAY_MAPPING[key] || key.toLowerCase();
          normalizedRow[normalizedKey] = value;
        }

        // Validate required fields
        if (!normalizedRow.name || typeof normalizedRow.name !== 'string' || normalizedRow.name.trim() === '') {
          result.errorCount++;
          result.errors.push({
            row: i + 1,
            error: 'Name is required and cannot be empty'
          });
          continue;
        }

        const categoryName = normalizedRow.name.trim();

        // Check for duplicates
        if (existingCategoryNames.has(categoryName.toLowerCase())) {
          result.skippedCount++;
          result.skipped.push({
            row: i + 1,
            name: categoryName,
            reason: 'Category with this name already exists'
          });
          continue;
        }

        // Generate code if not provided
        let categoryCode = normalizedRow.code?.trim();
        if (!categoryCode) {
          categoryCode = categoryName.toUpperCase().replace(/[^A-Z0-9]/g, '_').substring(0, 20);
        }

        // Check for code duplicates
        if (existingCategoryCodes.has(categoryCode.toLowerCase())) {
          // Generate unique code
          let counter = 1;
          let uniqueCode = `${categoryCode}_${counter}`;
          while (existingCategoryCodes.has(uniqueCode.toLowerCase())) {
            counter++;
            uniqueCode = `${categoryCode}_${counter}`;
          }
          categoryCode = uniqueCode;
        }

        // Prepare category data
        const categoryData: any = {
          name: categoryName,
          code: categoryCode,
          description: normalizedRow.description?.trim() || '',
          categoryType: normalizedRow.categoryType || 'general',
          approvalLimit: normalizedRow.approvalLimit ? parseFloat(normalizedRow.approvalLimit) : 0,
          riskLevel: normalizedRow.riskLevel || 'low',
          leadTime: normalizedRow.leadTime ? parseInt(normalizedRow.leadTime) : 0,
          qualityStandards: normalizedRow.qualityStandards?.split(',').map((s: string) => s.trim()).filter(Boolean) || [],
          environmentalImpact: normalizedRow.environmentalImpact || 'low',
          tags: normalizedRow.tags?.split(',').map((s: string) => s.trim()).filter(Boolean) || [],
          notes: normalizedRow.notes?.trim() || '',
          status: normalizedRow.status || 'active',
          isActive: true,
          createdBy: user.id
        };

        // Handle compliance requirements
        if (normalizedRow.complianceRequirements) {
          categoryData.complianceRequirements = normalizedRow.complianceRequirements
            .split(',')
            .map((s: string) => s.trim())
            .filter(Boolean);
        }

        // Create category
        const newCategory = await procurementCategoryService.createCategory(categoryData);

        // Add to existing sets to prevent duplicates in subsequent rows
        existingCategoryNames.add(categoryName.toLowerCase());
        existingCategoryCodes.add(categoryCode.toLowerCase());

        result.successCount++;
        result.imported.push({
          row: i + 1,
          name: categoryName,
          code: categoryCode
        });

        logger.debug('Category imported successfully', LogCategory.IMPORT, {
          row: i + 1,
          categoryId: newCategory.id,
          name: categoryName,
          code: categoryCode,
          userId: user.id
        });

      } catch (error: unknown) {
        result.errorCount++;

        // Log the error
        logger.error('Error processing category row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          row: row,
          userId: user.id
        });

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Log the final result
    logger.info('Procurement category bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    });

    return NextResponse.json({
      status: 'success',
      data: result
    });

  } catch (error: unknown) {
    logger.error('Error in procurement category bulk import', LogCategory.IMPORT, error);

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An unexpected error occurred during import'
    }, { status: 500 });
  }
}

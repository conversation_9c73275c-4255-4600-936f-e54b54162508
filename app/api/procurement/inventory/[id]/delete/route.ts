// app/api/procurement/inventory/[id]/delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { AuditDeletionContext } from '@/lib/services/audit/audit-deletion-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';



/**
 * DELETE /api/procurement/inventory/[id]/delete
 * Delete a procurement inventory item with audit trail
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Get current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete inventory items',
        { endpoint: `/api/procurement/inventory/${id}/delete`, method: 'DELETE' },
        401
      );
    }

    // Check permissions
    if (!['admin', 'procurement_manager', 'inventory_manager', 'super_admin'].includes(currentUser.role)) {
      return errorService.createApiResponse(
        ErrorType.AUTHORIZATION,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to delete inventory items',
        'You do not have permission to delete inventory items',
        { 
          userId: currentUser.id,
          userRole: currentUser.role,
          endpoint: `/api/procurement/inventory/${id}/delete`,
          method: 'DELETE'
        },
        403
      );
    }

    // Parse request body
    const body = await request.json();
    const { deletionReason, context } = body;

    // Validate deletion reason
    if (!deletionReason || typeof deletionReason !== 'string' || deletionReason.trim().length < 15) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_DELETION_REASON',
        'Deletion reason is required and must be at least 15 characters',
        'Please provide a detailed reason for deleting this inventory item (minimum 15 characters)',
        { 
          userId: currentUser.id,
          endpoint: `/api/procurement/inventory/${id}/delete`,
          method: 'DELETE'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Deletion reason validation failed for audit compliance requirements',
        [
          'Provide a clear and detailed reason for the deletion',
          'Ensure the reason is at least 15 characters long',
          'Include stock disposition if item has inventory',
          'Include business justification for the deletion'
        ]
      );
    }

    // Prepare audit context
    const auditContext: AuditDeletionContext = {
      deletedBy: currentUser.id,
      deletionReason: deletionReason.trim(),
      deletionType: 'single',
      userInfo: {
        id: currentUser.id,
        name: currentUser.name || 'Unknown User',
        email: currentUser.email || '<EMAIL>',
        role: currentUser.role
      },
      context: {
        fiscalYear: context?.fiscalYear,
        department: context?.department || 'Procurement',
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: request.headers.get('x-session-id')
      }
    };

    logger.info('Processing inventory item deletion request', LogCategory.PROCUREMENT, {
      itemId: id,
      userId: currentUser.id,
      deletionReason: deletionReason.trim()
    });

    // Perform audit-compliant deletion
    const result = await procurementInventoryService.deleteInventoryItem(id, auditContext);

    logger.info('Inventory item deletion completed successfully', LogCategory.PROCUREMENT, {
      itemId: id,
      userId: currentUser.id,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated
    });

    return NextResponse.json({
      success: true,
      message: 'Inventory item deleted successfully with audit trail',
      data: {
        deletedCount: result.deletedCount,
        auditRecordsCreated: result.auditRecordsCreated,
        auditCompliance: result.details?.auditCompliance,
        deletionId: result.auditRecordIds?.[0]
      }
    });

  } catch (error) {
    logger.error('Error deleting inventory item', LogCategory.PROCUREMENT, error);

    // Handle structured errors
    if (error instanceof Error && error.message.includes('Unable to delete')) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'INVENTORY_DELETE_CONSTRAINT',
        error.message,
        error.message,
        {
          itemId: id,
          endpoint: `/api/procurement/inventory/${id}/delete`,
          method: 'DELETE'
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INVENTORY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Unknown error occurred',
      'An unexpected error occurred while deleting the inventory item. Please try again.',
      {
        itemId: id,
        endpoint: `/api/procurement/inventory/${id}/delete`,
        method: 'DELETE'
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if the item still exists',
        'Ensure no pending orders reference this item',
        'Contact support if the problem persists'
      ]
    );
  }
}

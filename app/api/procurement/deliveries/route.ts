import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { deliveryService, CreateDeliveryData, DeliveryFilters } from '@/lib/backend/services/procurement/DeliveryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for delivery creation
const createDeliverySchema = z.object({
  purchaseOrderId: z.string().min(1, 'Purchase order ID is required'),
  supplierId: z.string().min(1, 'Supplier ID is required'),
  contractId: z.string().optional(),
  expectedDate: z.string().transform(val => new Date(val)),
  promisedDate: z.string().transform(val => new Date(val)).optional(),
  deliveryType: z.enum(['full', 'partial', 'split', 'emergency']).optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  trackingNumber: z.string().optional(),
  carrier: z.string().optional(),
  shippingMethod: z.enum(['standard', 'express', 'overnight', 'pickup', 'direct']).optional(),
  shippingCost: z.number().min(0).optional(),
  deliveryAddress: z.object({
    street: z.string().min(1, 'Street address is required'),
    city: z.string().min(1, 'City is required'),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().default('Malawi'),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180)
    }).optional()
  }),
  contactPerson: z.string().min(1, 'Contact person is required'),
  contactPhone: z.string().min(1, 'Contact phone is required'),
  contactEmail: z.string().email().optional(),
  items: z.array(z.object({
    purchaseOrderItemId: z.string().min(1, 'Purchase order item ID is required'),
    itemName: z.string().min(1, 'Item name is required'),
    itemCode: z.string().optional(),
    itemDescription: z.string().optional(),
    quantityOrdered: z.number().min(0, 'Quantity ordered must be positive'),
    quantityDelivered: z.number().min(0, 'Quantity delivered must be positive'),
    unitPrice: z.number().min(0, 'Unit price must be positive'),
    condition: z.enum(['good', 'damaged', 'defective', 'incomplete', 'expired']).optional(),
    expiryDate: z.string().transform(val => new Date(val)).optional(),
    batchNumber: z.string().optional(),
    serialNumbers: z.array(z.string()).optional(),
    notes: z.string().optional()
  })).min(1, 'At least one delivery item is required'),
  currency: z.string().length(3).optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional()
});

// Validation schema for delivery search filters
const deliveryFiltersSchema = z.object({
  supplierId: z.string().optional(),
  purchaseOrderId: z.string().optional(),
  contractId: z.string().optional(),
  status: z.string().optional(),
  priority: z.string().optional(),
  deliveryType: z.string().optional(),
  trackingNumber: z.string().optional(),
  carrier: z.string().optional(),
  expectedDateFrom: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  expectedDateTo: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  actualDateFrom: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  actualDateTo: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  overdue: z.string().transform(val => val === 'true').optional(),
  pendingReceipt: z.string().transform(val => val === 'true').optional(),
  hasIssues: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
  page: z.string().transform(val => val ? parseInt(val) : 1).optional(),
  limit: z.string().transform(val => val ? parseInt(val) : 20).optional()
});

// Response type for successful delivery operations
interface DeliveryResponse {
  success: true;
  data: unknown;
  message?: string;
}

// Response type for delivery list operations
interface DeliveryListResponse {
  success: true;
  data: unknown[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * GET /api/procurement/deliveries
 * Get deliveries with optional filtering and pagination
 */
export async function GET(request: NextRequest): Promise<NextResponse<DeliveryListResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view deliveries.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER,
      UserRole.WAREHOUSE_STAFF,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_FORBIDDEN',
        'Insufficient permissions to view deliveries',
        'You do not have permission to view delivery information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to access deliveries without proper permissions',
        [
          'Contact your administrator to request delivery access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate filters
    const validationResult = deliveryFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_FILTERS',
        'Invalid filter parameters',
        'The search filters provided are not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        validationResult.error.errors[0].message,
        [
          'Check the format of date filters (YYYY-MM-DD)',
          'Ensure numeric values are valid numbers',
          'Verify boolean filters use "true" or "false"'
        ]
      );
    }

    const {
      supplierId,
      purchaseOrderId,
      contractId,
      status,
      priority,
      deliveryType,
      trackingNumber,
      carrier,
      expectedDateFrom,
      expectedDateTo,
      actualDateFrom,
      actualDateTo,
      overdue,
      pendingReceipt,
      hasIssues,
      search,
      page = 1,
      limit = 20
    } = validationResult.data;

    // Build filters object
    const filters: DeliveryFilters = {
      supplierId,
      purchaseOrderId,
      contractId,
      status,
      priority,
      deliveryType,
      trackingNumber,
      carrier,
      overdue,
      pendingReceipt,
      hasIssues,
      search
    };

    if (expectedDateFrom || expectedDateTo) {
      filters.expectedDate = {
        from: expectedDateFrom,
        to: expectedDateTo
      };
    }

    if (actualDateFrom || actualDateTo) {
      filters.actualDate = {
        from: actualDateFrom,
        to: actualDateTo
      };
    }

    // Get deliveries
    const result = await deliveryService.searchDeliveries(filters, page, limit);

    return NextResponse.json({
      success: true,
      data: result.deliveries,
      pagination: result.pagination
    } as DeliveryListResponse);

  } catch (error: unknown) {
    logger.error('Error getting deliveries', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch deliveries',
      'Unable to retrieve delivery information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * POST /api/procurement/deliveries
 * Create a new delivery
 */
export async function POST(request: NextRequest): Promise<NextResponse<DeliveryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to create deliveries.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_CREATE_FORBIDDEN',
        'Insufficient permissions to create deliveries',
        'You do not have permission to create delivery records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to create delivery without proper permissions',
        [
          'Contact your administrator to request delivery creation permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = createDeliverySchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_VALIDATION_ERROR',
        'Invalid delivery data',
        'The delivery information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that quantities are positive numbers',
          'Verify date formats are correct',
          'Ensure at least one delivery item is included'
        ]
      );
    }

    const deliveryData: CreateDeliveryData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Validate date logic
    if (deliveryData.promisedDate && deliveryData.expectedDate > deliveryData.promisedDate) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'DELIVERY_INVALID_DATES',
        'Invalid delivery dates',
        'The expected delivery date cannot be after the promised delivery date.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          expectedDate: deliveryData.expectedDate.toISOString(),
          promisedDate: deliveryData.promisedDate.toISOString()
        },
        400,
        ErrorSeverity.LOW,
        'Expected date is after promised date',
        [
          'Adjust the expected delivery date to be before or equal to the promised date',
          'Contact the supplier to confirm delivery dates'
        ]
      );
    }

    // Validate delivery items quantities
    const invalidItems = deliveryData.items.filter(item =>
      item.quantityDelivered > item.quantityOrdered
    );

    if (invalidItems.length > 0) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'DELIVERY_INVALID_QUANTITIES',
        'Invalid delivery quantities',
        'Some delivery quantities exceed the ordered quantities. Please check and adjust.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          invalidItems: invalidItems.map(item => ({
            itemName: item.itemName,
            quantityOrdered: item.quantityOrdered,
            quantityDelivered: item.quantityDelivered
          }))
        },
        400,
        ErrorSeverity.MEDIUM,
        `${invalidItems.length} items have delivery quantities exceeding ordered quantities`,
        [
          'Adjust delivery quantities to not exceed ordered quantities',
          'Create separate deliveries for additional quantities if needed',
          'Verify the purchase order details'
        ]
      );
    }

    // Create delivery
    const delivery = await deliveryService.createDelivery(deliveryData);

    return NextResponse.json({
      success: true,
      message: 'Delivery created successfully',
      data: delivery
    } as DeliveryResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating delivery', LogCategory.PROCUREMENT, error);

    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Purchase order') && error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'DELIVERY_PO_NOT_FOUND',
          'Purchase order not found',
          'The specified purchase order could not be found. Please verify the purchase order ID.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the purchase order ID is correct',
            'Ensure the purchase order exists and is active',
            'Contact procurement team if needed'
          ]
        );
      }

      if (error.message.includes('Supplier') && error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'DELIVERY_SUPPLIER_NOT_FOUND',
          'Supplier not found',
          'The specified supplier could not be found. Please verify the supplier ID.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the supplier ID is correct',
            'Ensure the supplier is active in the system',
            'Contact procurement team if needed'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_CREATE_ERROR',
      error instanceof Error ? error.message : 'Failed to create delivery',
      'Unable to create the delivery record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try creating the delivery again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

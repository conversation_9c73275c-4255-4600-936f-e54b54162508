import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { deliveryService, QualityInspectionData } from '@/lib/backend/services/procurement/DeliveryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for quality inspection
const qualityInspectionSchema = z.object({
  inspectionDate: z.string().transform(val => new Date(val)).optional(),
  inspectionType: z.enum(['visual', 'functional', 'compliance', 'full']),
  overallRating: z.enum(['excellent', 'good', 'fair', 'poor', 'failed']),
  criteria: z.array(z.object({
    criterion: z.string().min(1, 'Criterion name is required'),
    rating: z.enum(['pass', 'fail', 'conditional']),
    notes: z.string().optional()
  })).min(1, 'At least one inspection criterion is required'),
  defectsFound: z.array(z.object({
    type: z.enum(['cosmetic', 'functional', 'safety', 'compliance']),
    severity: z.enum(['minor', 'major', 'critical']),
    description: z.string().min(1, 'Defect description is required'),
    quantity: z.number().min(0, 'Defect quantity must be non-negative')
  })).optional(),
  recommendations: z.array(z.string()).optional(),
  certificatesRequired: z.boolean().optional(),
  certificatesReceived: z.boolean().optional(),
  inspectionNotes: z.string().min(1, 'Inspection notes are required')
});

// Response type for successful inspection operations
interface InspectionResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * POST /api/procurement/deliveries/[id]/inspection
 * Conduct quality inspection for a delivery
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<InspectionResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'INSPECTION_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to conduct quality inspections.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER,
      UserRole.WAREHOUSE_STAFF,
      UserRole.QUALITY_INSPECTOR
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSPECTION_FORBIDDEN',
        'Insufficient permissions to conduct quality inspections',
        'You do not have permission to conduct quality inspections.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to conduct quality inspection without proper permissions',
        [
          'Contact your administrator to request quality inspection permissions',
          'Ensure you are logged in with the correct account',
          'Verify you have quality inspector or warehouse access'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INSPECTION_INVALID_DELIVERY_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INSPECTION_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = qualityInspectionSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INSPECTION_VALIDATION_ERROR',
        'Invalid quality inspection data',
        'The quality inspection information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that inspection criteria are properly defined',
          'Verify defect information is complete',
          'Include detailed inspection notes'
        ]
      );
    }

    const inspectionData: QualityInspectionData = {
      ...validationResult.data,
      inspectedBy: user.id
    };

    // Validate inspection logic
    const failedCriteria = inspectionData.criteria.filter(c => c.rating === 'fail');
    const hasCriticalDefects = inspectionData.defectsFound?.some(d => d.severity === 'critical') || false;

    // Check consistency between overall rating and criteria/defects
    if (inspectionData.overallRating === 'excellent' && (failedCriteria.length > 0 || hasCriticalDefects)) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'INSPECTION_INCONSISTENT_RATING',
        'Inconsistent inspection rating',
        'The overall rating of "excellent" is inconsistent with failed criteria or critical defects.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          overallRating: inspectionData.overallRating,
          failedCriteria: failedCriteria.length,
          hasCriticalDefects
        },
        400,
        ErrorSeverity.MEDIUM,
        'Overall rating conflicts with inspection findings',
        [
          'Review the overall rating based on inspection criteria',
          'Ensure failed criteria are reflected in the overall rating',
          'Consider critical defects when assigning the overall rating'
        ]
      );
    }

    if (inspectionData.overallRating === 'failed' && failedCriteria.length === 0 && !hasCriticalDefects) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'INSPECTION_INCONSISTENT_FAILED_RATING',
        'Inconsistent failed rating',
        'The overall rating of "failed" should have corresponding failed criteria or critical defects.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          overallRating: inspectionData.overallRating,
          failedCriteria: failedCriteria.length,
          hasCriticalDefects
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed rating without supporting evidence',
        [
          'Add failed criteria to support the failed rating',
          'Include critical defects if the inspection failed',
          'Provide detailed notes explaining the failure'
        ]
      );
    }

    // Validate certificate requirements
    if (inspectionData.certificatesRequired && !inspectionData.certificatesReceived) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'INSPECTION_MISSING_CERTIFICATES',
        'Required certificates not received',
        'This inspection requires certificates, but they have not been received.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          certificatesRequired: inspectionData.certificatesRequired,
          certificatesReceived: inspectionData.certificatesReceived
        },
        400,
        ErrorSeverity.MEDIUM,
        'Certificates required but not received',
        [
          'Obtain required certificates before completing inspection',
          'Contact the supplier for missing certificates',
          'Mark certificates as not required if they are not needed'
        ]
      );
    }

    // Conduct quality inspection
    const delivery = await deliveryService.conductQualityInspection(id, inspectionData);

    return NextResponse.json({
      success: true,
      message: 'Quality inspection completed successfully',
      data: delivery
    } as InspectionResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error conducting quality inspection', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'INSPECTION_DELIVERY_NOT_FOUND',
          'Delivery not found',
          'The delivery you are trying to inspect could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the delivery ID is correct',
            'Check if the delivery exists in the system',
            'Contact support if needed'
          ]
        );
      }

      if (error.message.includes('cannot be inspected')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'INSPECTION_NOT_ALLOWED',
          'Quality inspection not allowed',
          'This delivery cannot be inspected at this time. Goods must be received first.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Ensure goods receipt has been recorded first',
            'Check the delivery status',
            'Contact the warehouse team if needed'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INSPECTION_CONDUCT_ERROR',
      error instanceof Error ? error.message : 'Failed to conduct quality inspection',
      'Unable to complete the quality inspection. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try conducting the inspection again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

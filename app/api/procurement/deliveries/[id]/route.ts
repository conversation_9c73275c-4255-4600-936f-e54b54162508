import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { deliveryService, UpdateDeliveryData } from '@/lib/backend/services/procurement/DeliveryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for delivery updates
const updateDeliverySchema = z.object({
  expectedDate: z.string().transform(val => new Date(val)).optional(),
  promisedDate: z.string().transform(val => new Date(val)).optional(),
  actualDate: z.string().transform(val => new Date(val)).optional(),
  deliveryType: z.enum(['full', 'partial', 'split', 'emergency']).optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  trackingNumber: z.string().optional(),
  carrier: z.string().optional(),
  shippingMethod: z.enum(['standard', 'express', 'overnight', 'pickup', 'direct']).optional(),
  shippingCost: z.number().min(0).optional(),
  deliveryAddress: z.object({
    street: z.string().min(1),
    city: z.string().min(1),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180)
    }).optional()
  }).optional(),
  contactPerson: z.string().min(1).optional(),
  contactPhone: z.string().min(1).optional(),
  contactEmail: z.string().email().optional(),
  status: z.enum(['scheduled', 'in_transit', 'delivered', 'partially_delivered', 'delayed', 'cancelled', 'returned']).optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional()
});

// Response type for successful delivery operations
interface DeliveryResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * GET /api/procurement/deliveries/[id]
 * Get a specific delivery by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DeliveryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view delivery details.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER,
      UserRole.WAREHOUSE_STAFF,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_VIEW_FORBIDDEN',
        'Insufficient permissions to view delivery details',
        'You do not have permission to view delivery information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to view delivery without proper permissions',
        [
          'Contact your administrator to request delivery access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Get delivery
    const delivery = await deliveryService.findById(id);
    if (!delivery) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'DELIVERY_NOT_FOUND',
        'Delivery not found',
        'The requested delivery could not be found. It may have been deleted or the ID is incorrect.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Delivery with ID ${id} not found in database`,
        [
          'Verify the delivery ID is correct',
          'Check if the delivery was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    return NextResponse.json({
      success: true,
      data: delivery
    } as DeliveryResponse);

  } catch (error: unknown) {
    logger.error('Error getting delivery', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch delivery',
      'Unable to retrieve delivery information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * PUT /api/procurement/deliveries/[id]
 * Update a specific delivery
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DeliveryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update deliveries.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_UPDATE_FORBIDDEN',
        'Insufficient permissions to update deliveries',
        'You do not have permission to update delivery information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to update delivery without proper permissions',
        [
          'Contact your administrator to request delivery update permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all fields are properly formatted',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = updateDeliverySchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_VALIDATION_ERROR',
        'Invalid delivery update data',
        'The delivery information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Check that all fields are properly formatted',
          'Verify date formats are correct',
          'Ensure numeric values are valid'
        ]
      );
    }

    const updateData: UpdateDeliveryData = {
      ...validationResult.data,
      updatedBy: user.id
    };

    // Validate date logic if both dates are provided
    if (updateData.expectedDate && updateData.promisedDate && 
        updateData.expectedDate > updateData.promisedDate) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'DELIVERY_INVALID_DATES',
        'Invalid delivery dates',
        'The expected delivery date cannot be after the promised delivery date.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          expectedDate: updateData.expectedDate.toISOString(),
          promisedDate: updateData.promisedDate.toISOString()
        },
        400,
        ErrorSeverity.LOW,
        'Expected date is after promised date',
        [
          'Adjust the expected delivery date to be before or equal to the promised date',
          'Contact the supplier to confirm delivery dates'
        ]
      );
    }

    // Update delivery
    const delivery = await deliveryService.updateDelivery(id, updateData);

    return NextResponse.json({
      success: true,
      message: 'Delivery updated successfully',
      data: delivery
    } as DeliveryResponse);

  } catch (error: unknown) {
    logger.error('Error updating delivery', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'DELIVERY_NOT_FOUND',
          'Delivery not found',
          'The delivery you are trying to update could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the delivery ID is correct',
            'Check if the delivery was recently deleted',
            'Contact support if needed'
          ]
        );
      }

      if (error.message.includes('Invalid status transition')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'DELIVERY_INVALID_STATUS_TRANSITION',
          'Invalid status transition',
          'The status change you are trying to make is not allowed for this delivery.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Check the current delivery status',
            'Verify the status transition is valid',
            'Contact support if you need help with status changes'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update delivery',
      'Unable to update the delivery record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try updating the delivery again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * DELETE /api/procurement/deliveries/[id]
 * Delete a specific delivery
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<{ success: true; message: string } | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete deliveries.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only high-level users can delete deliveries
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.WAREHOUSE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_DELETE_FORBIDDEN',
        'Insufficient permissions to delete deliveries',
        'You do not have permission to delete delivery records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to delete delivery without proper permissions',
        [
          'Contact your administrator to request delivery deletion permissions',
          'Consider updating the delivery status instead of deleting',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_INVALID_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Check if delivery exists and validate deletion rules
    const existingDelivery = await deliveryService.findById(id);
    if (!existingDelivery) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'DELIVERY_NOT_FOUND',
        'Delivery not found',
        'The delivery you are trying to delete could not be found.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Delivery with ID ${id} not found in database`,
        [
          'Verify the delivery ID is correct',
          'Check if the delivery was already deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    // Check if delivery can be deleted (business rules)
    if (!['scheduled', 'cancelled'].includes(existingDelivery.status)) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'DELIVERY_DELETE_NOT_ALLOWED',
        'Delivery cannot be deleted',
        'Only scheduled or cancelled deliveries can be deleted. For other statuses, consider updating the status instead.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id,
          currentStatus: existingDelivery.status
        },
        400,
        ErrorSeverity.MEDIUM,
        `Cannot delete delivery with status: ${existingDelivery.status}`,
        [
          'Update the delivery status to cancelled instead',
          'Contact your administrator if deletion is necessary',
          'Consider archiving the delivery instead of deleting'
        ]
      );
    }

    // Check if delivery has been received (additional business rule)
    if (existingDelivery.receivedBy) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'DELIVERY_DELETE_RECEIVED',
        'Cannot delete received delivery',
        'This delivery has already been received and cannot be deleted. This is to maintain audit trail integrity.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id,
          receivedBy: existingDelivery.receivedBy.toString(),
          receivedDate: existingDelivery.receivedDate?.toISOString()
        },
        400,
        ErrorSeverity.HIGH,
        'Attempted to delete delivery that has been received',
        [
          'Contact your administrator if this delivery needs to be removed',
          'Consider updating the delivery status instead',
          'Review audit trail requirements with your compliance team'
        ]
      );
    }

    // Delete delivery
    await deliveryService.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Delivery deleted successfully'
    });

  } catch (error: unknown) {
    logger.error('Error deleting delivery', LogCategory.PROCUREMENT, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete delivery',
      'Unable to delete the delivery record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try deleting the delivery again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

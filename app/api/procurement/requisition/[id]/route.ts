import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { RequisitionService } from '@/lib/backend/services/procurement/RequisitionService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

const requisitionService = new RequisitionService();
const auditService = new AuditDeletionService();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'REQUISITION_GET_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view requisitions.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Get requisition
    const requisition = await requisitionService.getRequisitionDetails(id);

    if (!requisition) {
      return NextResponse.json(
        { error: 'Requisition not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isProcurementStaff = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    const isFinanceStaff = hasRequiredPermissions(user, [
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER
    ]);

    const isDepartmentHead = hasRequiredPermissions(user, [
      UserRole.DEPARTMENT_HEAD
    ]);

    const isRequester = requisition.requestedBy.toString() === user.id;

    if (!isProcurementStaff && !isFinanceStaff && !isRequester && !isDepartmentHead) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_GET_FORBIDDEN',
        'Insufficient permissions to view this requisition',
        'You do not have permission to view this requisition.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Transform the requisition to handle null populated fields
    const transformedRequisition = {
      ...requisition.toObject(),
      departmentId: requisition.departmentId || null,
      requestedBy: requisition.requestedBy || null,
      approvedBy: requisition.approvedBy || null,
      purchaseOrderId: requisition.purchaseOrderId || null,
      budgetId: requisition.budgetId || null,
      createdBy: requisition.createdBy || null
    };

    return NextResponse.json({
      success: true,
      data: transformedRequisition
    });

  } catch (error) {
    logger.error('Error fetching requisition', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'REQUISITION_GET_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch requisition',
      'Unable to retrieve the requisition. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        requisitionId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Contact support if the problem persists'
      ]
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'REQUISITION_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update requisitions.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_UPDATE_FORBIDDEN',
        'Insufficient permissions to update requisitions',
        'You do not have permission to update requisition records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse request body
    const body = await request.json();

    // Get existing requisition to check permissions
    const existingRequisition = await requisitionService.getRequisitionDetails(id);
    if (!existingRequisition) {
      return NextResponse.json(
        { error: 'Requisition not found' },
        { status: 404 }
      );
    }

    // Check if user can update this specific requisition
    const isProcurementStaff = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    const isRequester = existingRequisition.requestedBy.toString() === user.id;
    const isDraft = existingRequisition.status === 'draft';

    if (!isProcurementStaff && (!isRequester || !isDraft)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_UPDATE_FORBIDDEN',
        'Cannot update this requisition',
        'You can only update your own draft requisitions.',
        {
          userId: user.id,
          requisitionId: id,
          requisitionStatus: existingRequisition.status,
          isRequester
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Update requisition
    const updatedRequisition = await requisitionService.updateRequisition(id, {
      ...body,
      updatedBy: user.id
    });

    logger.info('Requisition updated', LogCategory.PROCUREMENT, {
      requisitionId: id,
      updatedBy: user.id
    });

    return NextResponse.json({
      success: true,
      data: updatedRequisition,
      message: 'Requisition updated successfully'
    });

  } catch (error) {
    logger.error('Error updating requisition', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'REQUISITION_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update requisition',
      'Unable to update the requisition. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        requisitionId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check the data format and try again',
        'Ensure all required fields are provided',
        'Contact support if the problem persists'
      ]
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { id } = await params;

    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'REQUISITION_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete requisitions.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_DELETE_FORBIDDEN',
        'Insufficient permissions to delete requisitions',
        'You do not have permission to delete requisition records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          requisitionId: id
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse request body for audit information
    let deletionReason = 'Manual deletion';
    let context = {};
    
    try {
      const body = await request.json();
      deletionReason = body.deletionReason || deletionReason;
      context = body.context || {};
    } catch {
      // Body is optional for individual deletes
    }

    // Get requisition details for audit
    const requisition = await requisitionService.getRequisitionDetails(id);
    if (!requisition) {
      return NextResponse.json(
        { error: 'Requisition not found' },
        { status: 404 }
      );
    }

    // Check if requisition can be deleted (business rules)
    if (['approved', 'completed'].includes(requisition.status)) {
      return NextResponse.json(
        { error: `Cannot delete requisition with status: ${requisition.status}` },
        { status: 400 }
      );
    }

    // Create audit record before deletion
    const auditRecord = await auditService.createDeletionRecord({
      entityType: 'Requisition',
      entityId: id,
      entityData: {
        requisitionNumber: requisition.requisitionNumber,
        title: requisition.title,
        departmentId: requisition.departmentId,
        requestedBy: requisition.requestedBy,
        status: requisition.status,
        priority: requisition.priority,
        items: requisition.items,
        totalAmount: requisition.totalAmount,
        description: requisition.description,
        justification: requisition.justification
      },
      deletionReason,
      deletedBy: user.id,
      context: {
        department: 'Procurement',
        fiscalYear: new Date().getFullYear().toString(),
        ...context
      }
    });

    // Delete requisition
    const deletedRequisition = await requisitionService.deleteRequisition(id);

    logger.info('Requisition deleted with audit trail', LogCategory.PROCUREMENT, {
      requisitionId: id,
      requisitionNumber: deletedRequisition.requisitionNumber,
      deletedBy: user.id,
      auditRecordId: auditRecord._id,
      reason: deletionReason
    });

    return NextResponse.json({
      success: true,
      message: 'Requisition deleted successfully with audit trail'
    });

  } catch (error) {
    logger.error('Error deleting requisition', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'REQUISITION_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete requisition',
      'Unable to delete the requisition. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        requisitionId: id,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if the requisition has active dependencies',
        'Ensure the requisition is in deletable status (draft, rejected)',
        'Contact support if the problem persists'
      ]
    );
  }
}

// app/api/procurement/requisition/route.ts
import { NextRequest, NextResponse } from 'next/server';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  startDate?: Date;
  endDate?: Date;
  amount?: { $gte?: number; $lte?: number };
}


import { getCurrentUser } from '@/lib/backend/auth/auth';
import { RequisitionService } from '@/lib/backend/services/procurement/RequisitionService';
import { RequisitionImportExportService } from '@/lib/backend/services/procurement/RequisitionImportExportService';
import { RequisitionReportingService } from '@/lib/backend/services/procurement/RequisitionReportingService';
import { procurementBudgetIntegrationService } from '@/lib/services/procurement/budget-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Initialize services
const requisitionService = new RequisitionService();
const importExportService = new RequisitionImportExportService();
const reportingService = new RequisitionReportingService();

/**
 * GET handler for requisitions
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const departmentId = searchParams.get('departmentId');
    const employeeId = searchParams.get('employeeId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get requisition by ID
      const requisition = await requisitionService.getRequisitionDetails(id);

      if (!requisition) {
        return NextResponse.json({ error: 'Requisition not found' }, { status: 404 });
      }

      // Check if user has permission to view this requisition
      const isProcurementStaff = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      const isFinanceStaff = hasRequiredPermissions(user, [
        UserRole.FINANCE_MANAGER,
        UserRole.FINANCE_OFFICER
      ]);

      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);

      const isRequester = requisition.requestedBy.toString() === user.id;

      if (!isProcurementStaff && !isFinanceStaff && !isRequester && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      return NextResponse.json(requisition);
    } else if (format) {
      // Export requisitions
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      // Add filters
      if (departmentId) {
        filter.departmentId = departmentId;
      }

      if (employeeId) {
        filter.requestedBy = employeeId;
      }

      if (status) {
        filter.status = status;
      }

      if (startDate && endDate) {
        filter.startDate = new Date(startDate);
        filter.endDate = new Date(endDate);
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'requisitions.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'requisitions.csv';
      } else if (format === 'pdf' && id) {
        buffer = await reportingService.generateRequisitionPdf(id);
        contentType = 'application/pdf';
        filename = `requisition-${id}.pdf`;
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      return new NextResponse(new Uint8Array(buffer), {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          departmentId: departmentId || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'department' && departmentId) {
        // Generate department report
        const reportData = await reportingService.generateDepartmentReport(
          departmentId
        );

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
      }
    } else if (searchParams.get('pending') === 'true') {
      // Get pending requisitions
      // Check if user has permission to view pending requisitions
      const hasPendingPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasPendingPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build options
      interface PendingOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        departmentId?: string;
        priority?: string[];
        [key: string]: any;
      }

      const options: PendingOptions = {
        page,
        limit,
        sort: { priority: -1, date: 1 }
      };

      if (departmentId) {
        options.departmentId = departmentId;
      }

      const priority = searchParams.get('priority');
      if (priority) {
        options.priority = priority.split(',');
      }

      // Get pending requisitions
      // Cast to any to bypass TypeScript's type checking
      const result = await requisitionService.getPendingRequisitions(options as any);

      // Transform the result to handle null populated fields
      const transformedResult = {
        ...result,
        docs: result.docs.map((req: any) => ({
          ...req.toObject(),
          departmentId: req.departmentId || null,
          requestedBy: req.requestedBy || null,
          approvedBy: req.approvedBy || null,
          purchaseOrderId: req.purchaseOrderId || null,
          budgetId: req.budgetId || null,
          createdBy: req.createdBy || null
        }))
      };

      return NextResponse.json(transformedResult);
    } else if (searchParams.get('approved') === 'true') {
      // Get approved requisitions
      // Check if user has permission to view approved requisitions
      const hasApprovedPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      if (!hasApprovedPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build options
      interface ApprovedOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        departmentId?: string;
        [key: string]: any;
      }

      const options: ApprovedOptions = {
        page,
        limit,
        sort: { approvalDate: 1 }
      };

      if (departmentId) {
        options.departmentId = departmentId;
      }

      // Get approved requisitions
      // Cast to any to bypass TypeScript's type checking
      const result = await requisitionService.getApprovedRequisitions(options as any);

      // Transform the result to handle null populated fields
      const transformedResult = {
        ...result,
        docs: result.docs.map((req: any) => ({
          ...req.toObject(),
          departmentId: req.departmentId || null,
          requestedBy: req.requestedBy || null,
          approvedBy: req.approvedBy || null,
          purchaseOrderId: req.purchaseOrderId || null,
          budgetId: req.budgetId || null,
          createdBy: req.createdBy || null
        }))
      };

      return NextResponse.json(transformedResult);
    } else if (departmentId) {
      // Get requisitions by department
      // Check if user has permission to view department requisitions
      const isProcurementStaff = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      const isFinanceStaff = hasRequiredPermissions(user, [
        UserRole.FINANCE_MANAGER,
        UserRole.FINANCE_OFFICER
      ]);

      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!isProcurementStaff && !isFinanceStaff && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build options
      interface DepartmentOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        startDate?: Date;
        endDate?: Date;
        [key: string]: any;
      }

      const options: DepartmentOptions = {
        page,
        limit,
        sort: { date: -1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }

      // Get requisitions
      // Cast to any to bypass TypeScript's type checking
      const result = await requisitionService.getByDepartment(departmentId, options as any);

      // Transform the result to handle null populated fields
      const transformedResult = {
        ...result,
        docs: result.docs.map((req: any) => ({
          ...req.toObject(),
          departmentId: req.departmentId || null,
          requestedBy: req.requestedBy || null,
          approvedBy: req.approvedBy || null,
          purchaseOrderId: req.purchaseOrderId || null,
          budgetId: req.budgetId || null,
          createdBy: req.createdBy || null
        }))
      };

      return NextResponse.json(transformedResult);
    } else if (employeeId) {
      // Get requisitions by employee
      // Check if user has permission to view employee requisitions
      const isProcurementStaff = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      const isFinanceStaff = hasRequiredPermissions(user, [
        UserRole.FINANCE_MANAGER,
        UserRole.FINANCE_OFFICER
      ]);

      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);

      const isOwnRequisitions = employeeId === user.id;

      if (!isProcurementStaff && !isFinanceStaff && !isOwnRequisitions && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build options
      interface EmployeeOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        startDate?: Date;
        endDate?: Date;
        [key: string]: any;
      }

      const options: EmployeeOptions = {
        page,
        limit,
        sort: { date: -1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }

      // Get requisitions
      // Cast to any to bypass TypeScript's type checking
      const result = await requisitionService.getByEmployee(employeeId, options as any);

      // Transform the result to handle null populated fields
      const transformedResult = {
        ...result,
        docs: result.docs.map((req: any) => ({
          ...req.toObject(),
          departmentId: req.departmentId || null,
          requestedBy: req.requestedBy || null,
          approvedBy: req.approvedBy || null,
          purchaseOrderId: req.purchaseOrderId || null,
          budgetId: req.budgetId || null,
          createdBy: req.createdBy || null
        }))
      };

      return NextResponse.json(transformedResult);
    } else {
      // Get all requisitions with pagination
      // Check if user has permission to view all requisitions
      const hasViewAllPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER,
        UserRole.FINANCE_OFFICER
      ]);

      if (!hasViewAllPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      // Get requisitions
      const result = await requisitionService.paginate(filter, page, limit, { date: -1 }, ['requestedBy', 'departmentId', 'approvedBy', 'purchaseOrderId', 'budgetId', 'createdBy']);

      // Transform the result to handle null populated fields
      const transformedResult = {
        ...result,
        docs: result.docs.map((req: any) => ({
          ...req.toObject(),
          departmentId: req.departmentId || null,
          requestedBy: req.requestedBy || null,
          approvedBy: req.approvedBy || null,
          purchaseOrderId: req.purchaseOrderId || null,
          budgetId: req.budgetId || null,
          createdBy: req.createdBy || null
        }))
      };

      return NextResponse.json(transformedResult);
    }
  } catch (error: unknown) {
    logger.error('Error in requisitions GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for requisitions
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // If employee is creating requisition for themselves, set requestedBy
    if (!body.requestedBy && user.id) {
      body.requestedBy = user.id;
    }

    // Handle empty budget ID - convert empty string to null
    if (body.budgetId === '' || body.budgetId === 'no-budget') {
      body.budgetId = null;
    }

    // Handle empty department ID
    if (body.departmentId === '') {
      body.departmentId = null;
    }

    // Budget validation for requisitions with budget information
    if (body.budgetId && body.totalAmount && body.totalAmount > 0) {
      try {
        logger.info('Performing budget validation for requisition', LogCategory.PROCUREMENT, {
          budgetId: body.budgetId,
          totalAmount: body.totalAmount,
          userId: user.id
        });

        const budgetCheck = await procurementBudgetIntegrationService.checkBudgetAvailability(
          'temp-' + Date.now(), // Temporary ID for checking
          body.totalAmount,
          body.budgetCode,
          body.categoryCode
        );

        if (!budgetCheck.available) {
          logger.warn('Budget validation failed for requisition', LogCategory.PROCUREMENT, {
            budgetCheck,
            requestedAmount: body.totalAmount,
            userId: user.id
          });

          return NextResponse.json({
            error: 'Insufficient budget allocation',
            message: budgetCheck.message,
            budgetDetails: {
              available: budgetCheck.availableAmount,
              requested: budgetCheck.requestedAmount,
              budgetId: budgetCheck.budgetId,
              categoryId: budgetCheck.categoryId
            }
          }, { status: 400 });
        }

        // Store budget validation result for later use
        body.budgetValidation = {
          budgetId: budgetCheck.budgetId,
          categoryId: budgetCheck.categoryId,
          validated: true,
          validatedAt: new Date(),
          availableAmount: budgetCheck.availableAmount
        };

        logger.info('Budget validation successful for requisition', LogCategory.PROCUREMENT, {
          budgetCheck,
          userId: user.id
        });

      } catch (error) {
        logger.error('Error during budget validation', LogCategory.PROCUREMENT, error);
        return NextResponse.json({
          error: 'Budget validation failed',
          message: 'Unable to validate budget availability. Please try again or contact support.',
          details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    // Create requisition
    const requisition = await requisitionService.createRequisition(body);

    logger.info('Requisition created successfully', LogCategory.PROCUREMENT, {
      requisitionId: requisition._id,
      totalAmount: requisition.totalAmount,
      budgetValidated: !!body.budgetValidation,
      userId: user.id
    });

    return NextResponse.json(requisition, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in requisitions POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for requisitions
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Check if status update
    if (body.id && body.status) {
      // Get requisition
      const requisition = await requisitionService.findById(body.id);

      if (!requisition) {
        return NextResponse.json({ error: 'Requisition not found' }, { status: 404 });
      }

      // Check permissions for status update
      if (body.status === 'submitted') {
        // Only the requester, department head, or procurement staff can submit
        const isProcurementStaff = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROCUREMENT_MANAGER,
          UserRole.PROCUREMENT_OFFICER
        ]);

        const isDepartmentHead = hasRequiredPermissions(user, [
          UserRole.DEPARTMENT_HEAD
        ]);

        const isRequester = requisition.requestedBy.toString() === user.id;

        if (!isProcurementStaff && !isDepartmentHead && !isRequester) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }
      } else if (body.status === 'approved' || body.status === 'rejected') {
        // Only department head or procurement staff can approve/reject
        const isProcurementStaff = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROCUREMENT_MANAGER,
          UserRole.PROCUREMENT_OFFICER
        ]);

        const isDepartmentHead = hasRequiredPermissions(user, [
          UserRole.DEPARTMENT_HEAD
        ]);

        if (!isProcurementStaff && !isDepartmentHead) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }
      } else if (body.status === 'ordered' || body.status === 'completed' || body.status === 'cancelled') {
        // Only procurement staff can set to ordered/completed/cancelled
        const isProcurementStaff = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.PROCUREMENT_MANAGER,
          UserRole.PROCUREMENT_OFFICER
        ]);

        if (!isProcurementStaff) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }
      }

      // Budget operations for status changes
      if (body.status === 'approved') {
        // Reserve budget when requisition is approved
        const requisition = await requisitionService.findById(body.id);

        if (requisition && requisition.budgetValidation && requisition.totalAmount) {
          try {
            logger.info('Reserving budget for approved requisition', LogCategory.PROCUREMENT, {
              requisitionId: body.id,
              budgetId: requisition.budgetValidation.budgetId,
              categoryId: requisition.budgetValidation.categoryId,
              amount: requisition.totalAmount,
              userId: user.id
            });

            await procurementBudgetIntegrationService.reserveBudget(
              body.id,
              requisition.budgetValidation.budgetId,
              requisition.budgetValidation.categoryId,
              requisition.totalAmount
            );

            logger.info('Budget reserved successfully for approved requisition', LogCategory.PROCUREMENT, {
              requisitionId: body.id,
              amount: requisition.totalAmount,
              userId: user.id
            });
          } catch (error) {
            logger.error('Failed to reserve budget for approved requisition', LogCategory.PROCUREMENT, error);
            return NextResponse.json({
              error: 'Budget reservation failed',
              message: 'Unable to reserve budget for this requisition. The approval cannot proceed.',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 400 });
          }
        }
      } else if (body.status === 'cancelled' || body.status === 'rejected') {
        // Release budget when requisition is cancelled or rejected
        try {
          logger.info('Releasing budget for cancelled/rejected requisition', LogCategory.PROCUREMENT, {
            requisitionId: body.id,
            status: body.status,
            userId: user.id
          });

          await procurementBudgetIntegrationService.releaseBudget(body.id);

          logger.info('Budget released successfully for cancelled/rejected requisition', LogCategory.PROCUREMENT, {
            requisitionId: body.id,
            status: body.status,
            userId: user.id
          });
        } catch (error) {
          logger.warn('Failed to release budget for cancelled/rejected requisition', LogCategory.PROCUREMENT, {
            requisitionId: body.id,
            status: body.status,
            error: error instanceof Error ? error.message : 'Unknown error',
            userId: user.id
          });
          // Don't fail the status update if budget release fails - it may not have been reserved
        }
      }

      // Update status
      const updatedRequisition = await requisitionService.updateStatus(
        body.id,
        body.status,
        user.id,
        {
          rejectionReason: body.rejectionReason,
          purchaseOrderId: body.purchaseOrderId,
          notes: body.notes
        }
      );

      logger.info('Requisition status updated successfully', LogCategory.PROCUREMENT, {
        requisitionId: body.id,
        oldStatus: requisition?.status,
        newStatus: body.status,
        userId: user.id
      });

      return NextResponse.json(updatedRequisition);
    } else if (body.id) {
      // Update requisition
      // Get requisition
      const requisition = await requisitionService.findById(body.id);

      if (!requisition) {
        return NextResponse.json({ error: 'Requisition not found' }, { status: 404 });
      }

      // Check permissions for update
      const isProcurementStaff = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      const isRequester = requisition.requestedBy.toString() === user.id;

      if (!isProcurementStaff && !isRequester) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Only allow updates to draft requisitions
      if (requisition.status !== 'draft') {
        return NextResponse.json({
          error: `Requisition with status '${requisition.status}' cannot be updated`
        }, { status: 400 });
      }

      // Update requisition
      const updatedRequisition = await requisitionService.updateById(body.id, body);

      return NextResponse.json(updatedRequisition);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in requisitions PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}


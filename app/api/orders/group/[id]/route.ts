// app/api/orders/group/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { groupOrderService } from '@/lib/backend/services/orders/GroupOrderService';

/**
 * GET /api/orders/group/[id]
 * Get a group order by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get group order
    const groupOrder = await groupOrderService.findById(id);
    if (!groupOrder) {
      return NextResponse.json(
        { error: 'Group order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: groupOrder
    });
  } catch (error) {
    logger.error(`Error fetching group order:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to fetch group order.' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/orders/group/[id]
 * Update a group order
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Update group order
    const groupOrder = await groupOrderService.updateById(id, body);
    if (!groupOrder) {
      return NextResponse.json(
        { error: 'Group order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Group order updated successfully',
      data: groupOrder
    });
  } catch (error) {
    logger.error(`Error updating group order:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update group order.' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/orders/group/[id]
 * Delete a group order
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete group order
    const result = await groupOrderService.deleteById(id);
    if (!result) {
      return NextResponse.json(
        { error: 'Group order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Group order deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting group order:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete group order.' },
      { status: 500 }
    );
  }
}

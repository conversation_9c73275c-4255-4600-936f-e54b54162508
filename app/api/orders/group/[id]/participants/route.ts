import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { groupOrderService } from '@/lib/backend/services/orders/GroupOrderService';

/**
 * POST /api/orders/group/[id]/participants
 * Add a participant to a group order
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();
    const { participantId } = body;

    if (!participantId) {
      return NextResponse.json(
        { error: 'Participant ID is required' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Add participant to group order
    const groupOrder = await groupOrderService.addParticipant(id, participantId);

    return NextResponse.json({
      success: true,
      message: 'Participant added to group order successfully',
      data: groupOrder
    });
  } catch (error) {
    logger.error(`Error adding participant to group order:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to add participant to group order.' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/orders/group/[id]/participants
 * Remove a participant from a group order
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();
    const { participantId } = body;

    if (!participantId) {
      return NextResponse.json(
        { error: 'Participant ID is required' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Remove participant from group order
    const groupOrder = await groupOrderService.removeParticipant(id, participantId);

    return NextResponse.json({
      success: true,
      message: 'Participant removed from group order successfully',
      data: groupOrder
    });
  } catch (error) {
    logger.error(`Error removing participant from group order:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to remove participant from group order.' },
      { status: 500 }
    );
  }
}

// app/api/security/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { securityService } from '@/lib/services/security/security-service';
import { LoginStatus } from '@/models/security/LoginLog';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/security/login
 * Record a login attempt
 */
export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.userId || !body.ipAddress || !body.status) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, ipAddress, status' },
        { status: 400 }
      );
    }

    // Validate status
    if (!Object.values(LoginStatus).includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be success, failed, blocked, or suspicious.' },
        { status: 400 }
      );
    }

    // Get user agent
    const userAgent = req.headers.get('user-agent') || '';

    // Parse device information
    const deviceInfo = securityService.parseUserAgent(userAgent);

    // Generate device ID
    const deviceId = securityService.generateDeviceId(userAgent, body.ipAddress);

    // Record login attempt
    const loginLog = await securityService.recordLoginAttempt({
      userId: body.userId,
      ipAddress: body.ipAddress,
      status: body.status,
      deviceInfo: {
        type: deviceInfo.type,
        browser: deviceInfo.browser,
        os: deviceInfo.os,
        deviceId
      },
      location: body.location,
      failureReason: body.failureReason,
      sessionId: body.sessionId
    });

    return NextResponse.json(loginLog);
  } catch (error) {
    logger.error('Error recording login attempt', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to record login attempt' },
      { status: 500 }
    );
  }
}

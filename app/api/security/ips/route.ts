// app/api/security/ips/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import { BlockType } from '@/models/security/BlockedIP';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/security/ips
 * Get blocked IPs
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get blocked IPs
    const blockedIPs = await securityService.getBlockedIPs(limit, skip);

    return NextResponse.json(blockedIPs);
  } catch (error) {
    logger.error('Error getting blocked IPs', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to get blocked IPs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/security/ips
 * Block an IP address
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.ipAddress || !body.blockType || !body.reason) {
      return NextResponse.json(
        { error: 'Missing required fields: ipAddress, blockType, reason' },
        { status: 400 }
      );
    }

    // Validate block type
    if (!Object.values(BlockType).includes(body.blockType)) {
      return NextResponse.json(
        { error: 'Invalid block type. Must be temporary or permanent.' },
        { status: 400 }
      );
    }

    // Validate duration for temporary blocks
    if (body.blockType === BlockType.TEMPORARY && !body.duration) {
      return NextResponse.json(
        { error: 'Duration is required for temporary IP blocks' },
        { status: 400 }
      );
    }

    // Block IP
    const blockedIP = await securityService.blockIP(
      body.ipAddress,
      body.blockType,
      body.reason,
      body.duration || null,
      user.id,
      body.notes
    );

    return NextResponse.json(blockedIP);
  } catch (error: unknown) {
    logger.error('Error blocking IP address', LogCategory.SECURITY, error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to block IP address';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// app/api/security/ips/[ip]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * DELETE /api/security/ips/[ip]
 * Unblock an IP address
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ ip: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'hr_specialist') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { ip } = await params;

    // Unblock IP
    const result = await securityService.unblockIP(
      ip,
      user.id
    );

    if (!result) {
      return NextResponse.json(
        { error: 'IP address not found or already unblocked' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error unblocking IP address`, LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to unblock IP address' },
      { status: 500 }
    );
  }
}

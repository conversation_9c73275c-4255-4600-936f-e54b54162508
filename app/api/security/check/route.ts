import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/security/check
 * Check security status for a user, IP, or device
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.type) {
      return NextResponse.json(
        { error: 'Missing required field: type' },
        { status: 400 }
      );
    }

    // Perform check based on type
    switch (body.type) {
      case 'user':
        // Validate required fields for user check
        if (!body.userId) {
          return NextResponse.json(
            { error: 'Missing required field: userId' },
            { status: 400 }
          );
        }

        // Check if user is restricted
        const userRestriction = await securityService.isUserRestricted(body.userId);

        return NextResponse.json({
          isRestricted: !!userRestriction,
          details: userRestriction
        });

      case 'ip':
        // Validate required fields for IP check
        if (!body.ipAddress) {
          return NextResponse.json(
            { error: 'Missing required field: ipAddress' },
            { status: 400 }
          );
        }

        // Check if IP is blocked
        const ipBlock = await securityService.isIPBlocked(body.ipAddress);

        return NextResponse.json({
          isBlocked: !!ipBlock,
          details: ipBlock
        });

      case 'device':
        // Validate required fields for device check
        if (!body.userId || !body.deviceId) {
          return NextResponse.json(
            { error: 'Missing required fields: userId, deviceId' },
            { status: 400 }
          );
        }

        // Check if device is blocked
        const isDeviceBlocked = await securityService.isDeviceBlocked(body.userId, body.deviceId);

        return NextResponse.json({
          isBlocked: isDeviceBlocked
        });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Must be user, ip, or device.' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error('Error checking security status', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to check security status' },
      { status: 500 }
    );
  }
}

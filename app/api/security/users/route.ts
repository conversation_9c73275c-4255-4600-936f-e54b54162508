// app/api/security/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import { UserSecurityStatus } from '@/models/security/UserSecurity';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/security/users
 * Get restricted users
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'hr_specialist') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status') as UserSecurityStatus | undefined;
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get restricted users
    const users = await securityService.getRestrictedUsers(status, limit, skip);

    return NextResponse.json(users);
  } catch (error) {
    logger.error('Error getting restricted users', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to get restricted users' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/security/users
 * Perform security action on a user
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'hr_specialist') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.userId || !body.action) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, action' },
        { status: 400 }
      );
    }

    // Perform action based on action type
    switch (body.action) {
      case 'block':
        // Validate required fields for block action
        if (body.reason === undefined) {
          return NextResponse.json(
            { error: 'Missing required field: reason' },
            { status: 400 }
          );
        }

        // Block user
        const blockedUser = await securityService.blockUser(
          body.userId,
          body.duration || null,
          body.reason,
          user.id
        );

        return NextResponse.json(blockedUser);

      case 'ban':
        // Validate required fields for ban action
        if (body.reason === undefined) {
          return NextResponse.json(
            { error: 'Missing required field: reason' },
            { status: 400 }
          );
        }

        // Ban user
        const bannedUser = await securityService.banUser(
          body.userId,
          body.reason,
          user.id
        );

        return NextResponse.json(bannedUser);

      case 'suspend':
        // Validate required fields for suspend action
        if (body.reason === undefined) {
          return NextResponse.json(
            { error: 'Missing required field: reason' },
            { status: 400 }
          );
        }

        // Suspend user
        const suspendedUser = await securityService.suspendUser(
          body.userId,
          body.reason,
          user.id
        );

        return NextResponse.json(suspendedUser);

      case 'activate':
        // Activate user
        const activatedUser = await securityService.activateUser(
          body.userId,
          user.id
        );

        return NextResponse.json(activatedUser);

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be block, ban, suspend, or activate.' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error('Error performing security action on user', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to perform security action on user' },
      { status: 500 }
    );
  }
}

// app/api/security/users/[id]/devices/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/security/users/[id]/devices
 * Block or unblock a device for a user
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'hr_specialist') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.deviceId || !body.action) {
      return NextResponse.json(
        { error: 'Missing required fields: deviceId, action' },
        { status: 400 }
      );
    }

    // Perform action based on action type
    switch (body.action) {
      case 'block':
        // Resolve the params promise
        const { id } = await params;

        // Block device
        const blockedDevice = await securityService.blockDevice(
          id,
          body.deviceId,
          user.id
        );

        return NextResponse.json(blockedDevice);

      case 'unblock':
        // Resolve the params promise
        const { id: userId } = await params;

        // Unblock device
        const unblockedDevice = await securityService.unblockDevice(
          userId,
          body.deviceId,
          user.id
        );

        return NextResponse.json(unblockedDevice);

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be block or unblock.' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error(`Error managing device for user`, LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to manage device', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

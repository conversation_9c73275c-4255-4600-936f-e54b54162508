// app/api/security/users/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { securityService } from '@/lib/services/security/security-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/security/users/[id]
 * Get user security information
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Check if user has admin role or is requesting their own security info
    if (id !== user.id &&
        user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'hr_specialist') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get user security information
    const userSecurity = await securityService.getUserSecurity(id);

    if (!userSecurity) {
      return NextResponse.json(
        { error: 'User security information not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(userSecurity);
  } catch (error) {
    logger.error(`Error getting user security information`, LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to get user security information' },
      { status: 500 }
    );
  }
}

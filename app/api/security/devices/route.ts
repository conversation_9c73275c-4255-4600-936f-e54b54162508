import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import UserSecurity from '@/models/security/UserSecurity';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/security/devices
 * Get all users with devices
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '100');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get all users with devices
    const users = await UserSecurity.find(
      { 'devices.0': { $exists: true } }, // Only users with at least one device
      {
        userId: 1,
        status: 1,
        devices: 1
      }
    )
    .sort({ 'devices.lastLogin': -1 })
    .skip(skip)
    .limit(limit);

    return NextResponse.json(users);
  } catch (error) {
    logger.error('Error getting user devices', LogCategory.SECURITY, error);
    return NextResponse.json(
      { error: 'Failed to get user devices' },
      { status: 500 }
    );
  }
}

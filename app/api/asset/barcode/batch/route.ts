import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetBarcodeService } from '@/services/asset/AssetBarcodeService';

/**
 * POST /api/asset/barcode/batch
 * Generate batch of asset labels
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetIds || !Array.isArray(body.assetIds) || body.assetIds.length === 0) {
      return NextResponse.json(
        { error: 'Asset IDs array is required' },
        { status: 400 }
      );
    }

    // Set options
    const options = {
      includeQR: body.includeQR !== undefined ? body.includeQR : true,
      includeBarcode: body.includeBarcode !== undefined ? body.includeBarcode : true,
      includeDetails: body.includeDetails !== undefined ? body.includeDetails : true,
      width: body.width || 400,
      height: body.height || 200,
      columns: body.columns || 2
    };

    // Generate batch labels
    const labelsHTML = await assetBarcodeService.generateBatchLabels(body.assetIds, options);

    // Return result
    return NextResponse.json({
      success: true,
      assetCount: body.assetIds.length,
      data: labelsHTML
    });
  } catch (error: unknown) {
    logger.error('Error generating batch labels:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

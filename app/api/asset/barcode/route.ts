// app/api/asset/barcode/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
// import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
// import { UserRole } from '@/types/user-roles';
import { assetBarcodeService } from '@/services/asset/AssetBarcodeService';

/**
 * GET /api/asset/barcode
 * Generate barcode or QR code for an asset
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const assetId = searchParams.get('assetId');
    const type = searchParams.get('type') || 'qr';
    const size = searchParams.get('size') ? parseInt(searchParams.get('size')!) : undefined;
    const includeDetails = searchParams.get('includeDetails') === 'true';
    const format = searchParams.get('format') || 'CODE128';

    // Validate required parameters
    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    let result;

    // Generate code based on type
    if (type === 'qr') {
      result = await assetBarcodeService.generateQRCode(assetId, {
        size,
        includeDetails,
        errorCorrectionLevel: 'M'
      });
    } else if (type === 'barcode') {
      result = await assetBarcodeService.generateBarcode(assetId, {
        format: format as any,
        width: 2,
        height: size || 100,
        displayValue: true
      });
    } else if (type === 'label') {
      result = await assetBarcodeService.generateAssetLabel(assetId, {
        includeQR: true,
        includeBarcode: true,
        includeDetails,
        width: size || 400,
        height: Math.floor((size || 400) / 2)
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid type. Supported types: qr, barcode, label' },
        { status: 400 }
      );
    }

    // Return result
    return NextResponse.json({
      assetId,
      type,
      data: result
    });
  } catch (error: unknown) {
    logger.error('Error generating asset code:', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/barcode
 * Lookup asset by barcode or QR code
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.code) {
      return NextResponse.json(
        { error: 'Code is required' },
        { status: 400 }
      );
    }

    // Lookup asset
    const asset = await assetBarcodeService.lookupAssetByCode(body.code);

    // Return asset
    return NextResponse.json({
      success: true,
      data: asset
    });
  } catch (error: unknown) {
    logger.error('Error looking up asset by code:', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

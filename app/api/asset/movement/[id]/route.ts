// app/api/asset/movement/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetMovementService } from '@/services/asset/AssetMovementService';

/**
 * GET /api/asset/movement/[id]
 * Get asset movement by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get movement
    const movement = await assetMovementService.getMovementById(id);

    if (!movement) {
      return NextResponse.json(
        { error: 'Asset movement not found' },
        { status: 404 }
      );
    }

    // Return movement
    return NextResponse.json(movement);
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error fetching asset movement ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/asset/movement/[id]
 * Perform operations on an asset movement (approve, complete, cancel)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let movement;

    // Resolve the params promise
    const { id } = await params;

    // Process based on action
    switch (body.action) {
      case 'approve':
        movement = await assetMovementService.approveMovement(id, user.id);
        break;

      case 'complete':
        // Validate required fields
        if (!body.receivedBy) {
          return NextResponse.json(
            { error: 'Received by is required for completion' },
            { status: 400 }
          );
        }

        movement = await assetMovementService.completeMovement(id, {
          receivedBy: body.receivedBy || user.id,
          receiveDate: body.receiveDate ? new Date(body.receiveDate) : new Date(),
          notes: body.notes
        });
        break;

      case 'cancel':
        // TODO: Implement cancel movement functionality
        return NextResponse.json(
          { error: 'Cancel movement functionality not implemented yet' },
          { status: 501 }
        );

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: approve, complete, cancel' },
          { status: 400 }
        );
    }

    if (!movement) {
      return NextResponse.json(
        { error: 'Asset movement not found' },
        { status: 404 }
      );
    }

    // Return updated movement
    return NextResponse.json({
      success: true,
      message: `Asset movement ${body.action} operation completed successfully`,
      data: movement
    });
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error performing operation on asset movement ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetMovementService } from '@/services/asset/AssetMovementService';

/**
 * GET /api/asset/movement
 * Get asset movements with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const assetId = searchParams.get('assetId');
    const sourceLocation = searchParams.get('sourceLocation');
    const destinationLocation = searchParams.get('destinationLocation');
    const status = searchParams.get('status');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const requestedBy = searchParams.get('requestedBy');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');

    // Get asset movements
    const result = await assetMovementService.getMovements({
      page,
      limit,
      assetId: assetId || undefined,
      sourceLocation: sourceLocation || undefined,
      destinationLocation: destinationLocation || undefined,
      status: status ? status.split(',') : undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      requestedBy: requestedBy || undefined,
      sortBy: sortBy || undefined,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching asset movements:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/movement
 * Create a new asset movement request
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetId || !body.sourceLocation || !body.destinationLocation || !body.reason) {
      return NextResponse.json(
        { error: 'Missing required fields: assetId, sourceLocation, destinationLocation, reason' },
        { status: 400 }
      );
    }

    // Set created by and requested by if not provided
    body.createdBy = user.id;
    body.requestedBy = body.requestedBy || user.id;
    
    // Set movement date if not provided
    body.movementDate = body.movementDate || new Date();

    // Create movement request
    const movement = await assetMovementService.createMovementRequest(body);

    // Return created movement
    return NextResponse.json(
      {
        success: true,
        message: 'Asset movement request created successfully',
        data: movement
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating asset movement:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/asset/reservation/availability/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { assetReservationService } from '@/services/asset/AssetReservationService';
import { addMonths } from 'date-fns';

/**
 * GET /api/asset/reservation/availability
 * Get asset availability for reservation
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const assetId = searchParams.get('assetId');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');

    // Validate required parameters
    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Set default dates if not provided
    const start = fromDate ? new Date(fromDate) : new Date();
    const end = toDate ? new Date(toDate) : addMonths(start, 3);

    // Get asset availability
    const availability = await assetReservationService.getAssetAvailability(
      assetId,
      start,
      end
    );

    // Return availability
    return NextResponse.json(availability);
  } catch (error: unknown) {
    logger.error('Error fetching asset availability:', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

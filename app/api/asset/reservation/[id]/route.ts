// app/api/asset/reservation/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetReservationService } from '@/services/asset/AssetReservationService';

/**
 * GET /api/asset/reservation/[id]
 * Get reservation by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get reservation
    const reservation = await assetReservationService.getReservationById(id);

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Return reservation
    return NextResponse.json(reservation);
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error fetching reservation ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/asset/reservation/[id]
 * Perform operations on a reservation (approve, reject, checkout, checkin, cancel)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let reservation;

    // Resolve the params promise
    const { id } = await params;

    // Process based on action
    switch (body.action) {
      case 'approve':
        reservation = await assetReservationService.approveReservation(id, user.id);
        break;

      case 'reject':
        // Validate required fields
        if (!body.rejectionReason) {
          return NextResponse.json(
            { error: 'Rejection reason is required' },
            { status: 400 }
          );
        }

        reservation = await assetReservationService.rejectReservation(id, {
          rejectedBy: user.id,
          rejectionReason: body.rejectionReason
        });
        break;

      case 'checkout':
        reservation = await assetReservationService.checkoutAsset(id, {
          checkedOutBy: user.id,
          checkoutDate: body.checkoutDate ? new Date(body.checkoutDate) : undefined,
          location: body.location,
          notes: body.notes
        });
        break;

      case 'checkin':
        reservation = await assetReservationService.checkinAsset(id, {
          checkedInBy: user.id,
          checkinDate: body.checkinDate ? new Date(body.checkinDate) : undefined,
          notes: body.notes
        });
        break;

      case 'cancel':
        reservation = await assetReservationService.cancelReservation(id, user.id);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: approve, reject, checkout, checkin, cancel' },
          { status: 400 }
        );
    }

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Return updated reservation
    return NextResponse.json({
      success: true,
      message: `Reservation ${body.action} operation completed successfully`,
      data: reservation
    });
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error performing operation on reservation ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

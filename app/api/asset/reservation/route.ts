import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

import { assetReservationService } from '@/services/asset/AssetReservationService';

/**
 * GET /api/asset/reservation
 * Get asset reservations with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const assetId = searchParams.get('assetId');
    const requestedBy = searchParams.get('requestedBy');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');

    // Get reservations
    const result = await assetReservationService.getReservations({
      page,
      limit,
      assetId: assetId || undefined,
      requestedBy: requestedBy || undefined,
      status: status ? status.split(',') : undefined,
      priority: priority ? priority.split(',') : undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      sortBy: sortBy || undefined,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching reservations:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/reservation
 * Create a new reservation request
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetId || !body.purpose || !body.startDate || !body.endDate) {
      return NextResponse.json(
        { error: 'Missing required fields: assetId, purpose, startDate, endDate' },
        { status: 400 }
      );
    }

    // Set created by and requested by if not provided
    body.createdBy = user.id;
    body.requestedBy = body.requestedBy || user.id;
    
    // Parse dates
    body.startDate = new Date(body.startDate);
    body.endDate = new Date(body.endDate);
    
    // Validate dates
    if (body.startDate >= body.endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Create reservation
    const reservation = await assetReservationService.createReservation(body);

    // Return created reservation
    return NextResponse.json(
      {
        success: true,
        message: 'Reservation request created successfully',
        data: reservation
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating reservation:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

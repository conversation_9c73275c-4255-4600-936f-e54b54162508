import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetDepreciationService } from '@/services/asset/AssetDepreciationService';

/**
 * GET /api/asset/depreciation
 * Calculate depreciation for an asset
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const assetId = searchParams.get('assetId');
    const date = searchParams.get('date');
    
    // Validate required parameters
    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Calculate depreciation
    const depreciationAmount = await assetDepreciationService.calculateDepreciation(
      assetId,
      date ? new Date(date) : undefined
    );

    // Return result
    return NextResponse.json({
      assetId,
      date: date || new Date().toISOString(),
      depreciationAmount
    });
  } catch (error: unknown) {
    logger.error('Error calculating depreciation:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/depreciation
 * Record depreciation for an asset
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetId || !body.fiscalYear || !body.fiscalPeriod) {
      return NextResponse.json(
        { error: 'Missing required fields: assetId, fiscalYear, fiscalPeriod' },
        { status: 400 }
      );
    }

    // Set defaults
    const date = body.date ? new Date(body.date) : new Date();
    let amount = body.amount;

    // Calculate amount if not provided
    if (amount === undefined) {
      amount = await assetDepreciationService.calculateDepreciation(body.assetId, date);
    }

    // Record depreciation
    const result = await assetDepreciationService.recordDepreciation(body.assetId, {
      date,
      amount,
      fiscalYear: body.fiscalYear,
      fiscalPeriod: body.fiscalPeriod,
      notes: body.notes,
      recordedBy: user.id
    });

    // Return result
    return NextResponse.json({
      success: true,
      message: 'Depreciation recorded successfully',
      data: result
    });
  } catch (error: unknown) {
    logger.error('Error recording depreciation:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/asset/maintenance/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetMaintenanceService } from '@/services/asset/AssetMaintenanceService';

/**
 * GET /api/asset/maintenance/[id]
 * Get maintenance record by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get maintenance record
    const maintenance = await assetMaintenanceService.getMaintenanceById(id);

    if (!maintenance) {
      return NextResponse.json(
        { error: 'Maintenance record not found' },
        { status: 404 }
      );
    }

    // Return maintenance record
    return NextResponse.json(maintenance);
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error fetching maintenance record ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/asset/maintenance/[id]
 * Perform operations on a maintenance record (start, complete, cancel)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER,
      UserRole.MAINTENANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate action
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let maintenance;

    // Process based on action
    switch (body.action) {
      case 'start':
        maintenance = await assetMaintenanceService.startMaintenance(id, user.id);
        break;

      case 'complete':
        maintenance = await assetMaintenanceService.completeMaintenance(id, {
          completedBy: user.id,
          completionDate: body.completionDate ? new Date(body.completionDate) : new Date(),
          findings: body.findings,
          recommendations: body.recommendations,
          cost: body.cost,
          nextMaintenanceDate: body.nextMaintenanceDate ? new Date(body.nextMaintenanceDate) : undefined,
          invoiceNumber: body.invoiceNumber,
          downtime: body.downtime,
          notes: body.notes
        });
        break;

      case 'cancel':
        // TODO: Implement cancel maintenance functionality
        return NextResponse.json(
          { error: 'Cancel maintenance functionality not implemented yet' },
          { status: 501 }
        );

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: start, complete, cancel' },
          { status: 400 }
        );
    }

    if (!maintenance) {
      return NextResponse.json(
        { error: 'Maintenance record not found' },
        { status: 404 }
      );
    }

    // Return updated maintenance record
    return NextResponse.json({
      success: true,
      message: `Maintenance ${body.action} operation completed successfully`,
      data: maintenance
    });
  } catch (error) {
    let errorId = 'unknown';
    try {
      const { id } = await params;
      errorId = id;
    } catch (e) {
      // If params can't be resolved, use a default error ID
    }
    logger.error(`Error performing operation on maintenance record ${errorId}:`, LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetMaintenanceService } from '@/services/asset/AssetMaintenanceService';

/**
 * GET /api/asset/maintenance
 * Get asset maintenance records with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const assetId = searchParams.get('assetId');
    const maintenanceType = searchParams.get('maintenanceType');
    const status = searchParams.get('status');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const assignedTo = searchParams.get('assignedTo');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');

    // Get maintenance records
    const result = await assetMaintenanceService.getMaintenanceRecords({
      page,
      limit,
      assetId: assetId || undefined,
      maintenanceType: maintenanceType ? maintenanceType.split(',') : undefined,
      status: status ? status.split(',') : undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      assignedTo: assignedTo || undefined,
      sortBy: sortBy || undefined,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching maintenance records:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/maintenance
 * Schedule a new maintenance
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER,
      UserRole.MAINTENANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetId || !body.maintenanceType || !body.description || !body.scheduledDate || !body.location) {
      return NextResponse.json(
        { error: 'Missing required fields: assetId, maintenanceType, description, scheduledDate, location' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Schedule maintenance
    const maintenance = await assetMaintenanceService.scheduleMaintenance(body);

    // Return scheduled maintenance
    return NextResponse.json(
      {
        success: true,
        message: 'Maintenance scheduled successfully',
        data: maintenance
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error scheduling maintenance:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// app/api/asset/disposal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { assetDisposalService, DisposalMethod } from '@/services/asset/AssetDisposalService';

/**
 * GET /api/asset/disposal
 * Get disposed assets with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const disposalMethod = searchParams.get('disposalMethod');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');
    const disposedBy = searchParams.get('disposedBy');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');

    // Get disposed assets
    const result = await assetDisposalService.getDisposedAssets({
      page,
      limit,
      disposalMethod: disposalMethod
        ? disposalMethod.split(',').filter(method =>
            ['sale', 'donation', 'scrapped', 'recycled', 'trade-in', 'theft', 'lost', 'destroyed'].includes(method)
          ) as DisposalMethod[]
        : undefined,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      disposedBy: disposedBy || undefined,
      sortBy: sortBy || undefined,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    // Return results
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error fetching disposed assets:', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset/disposal
 * Dispose an asset
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.assetId || !body.disposalMethod || !body.disposalReason) {
      return NextResponse.json(
        { error: 'Missing required fields: assetId, disposalMethod, disposalReason' },
        { status: 400 }
      );
    }

    // Set disposal date if not provided
    body.disposalDate = body.disposalDate ? new Date(body.disposalDate) : new Date();

    // Set disposed by
    body.disposedBy = user.id;

    // Dispose asset
    const result = await assetDisposalService.disposeAsset(body.assetId, body);

    // Return result
    return NextResponse.json({
      success: true,
      message: 'Asset disposed successfully',
      data: result
    });
  } catch (error: unknown) {
    logger.error('Error disposing asset:', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

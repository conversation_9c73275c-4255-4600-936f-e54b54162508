// app/api/users/[id]/reset-password/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import User from '@/models/User';
import { connectToDatabase } from '@/lib/backend/database';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export const runtime = 'nodejs';



/**
 * POST handler for admin password reset
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  logger.info('Admin password reset API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'USER_NOT_AUTHENTICATED',
        'User authentication failed',
        'Your session has expired. Please refresh the page and log in again.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check if user has permission to reset passwords
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'User lacks required permissions to reset passwords',
        'You do not have permission to reset user passwords. Contact your administrator for access.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        403,
        ErrorSeverity.HIGH,
        'Only administrators with appropriate permissions can reset user passwords'
      );
    }

    const { id } = await params;
    const body = await req.json();
    const { resetType, newPassword, generateTemporary } = body;

    // Connect to database
    await connectToDatabase();

    // Find target user
    const targetUser = await User.findById(id);
    if (!targetUser) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'USER_NOT_FOUND',
        'User not found',
        'The user you are trying to reset password for could not be found.',
        {
          userId: user.id,
          requestedUserId: id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        404,
        ErrorSeverity.MEDIUM
      );
    }

    let finalPassword: string;
    let isTemporary = false;

    if (resetType === 'custom' && newPassword) {
      // Admin provides custom password
      if (newPassword.length < 8) {
        return errorService.createApiResponse(
          ErrorType.VALIDATION,
          'PASSWORD_TOO_SHORT',
          'Password does not meet requirements',
          'Password must be at least 8 characters long.',
          {
            userId: user.id,
            requestedUserId: id,
            endpoint: req.nextUrl.pathname,
            method: req.method
          },
          400,
          ErrorSeverity.MEDIUM
        );
      }
      finalPassword = newPassword;
    } else if (resetType === 'temporary' || generateTemporary) {
      // Generate temporary password
      finalPassword = crypto.randomBytes(8).toString('hex').substring(0, 12);
      isTemporary = true;
    } else {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_RESET_TYPE',
        'Invalid password reset type',
        'Please specify either a custom password or request a temporary password.',
        {
          userId: user.id,
          requestedUserId: id,
          resetType,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(finalPassword, salt);

    // Update user password
    await User.findByIdAndUpdate(id, {
      password: hashedPassword,
      // Clear any existing password reset tokens
      passwordResetToken: undefined,
      passwordResetExpires: undefined,
      // Reset failed login attempts
      failedLoginAttempts: 0,
      lastFailedLogin: undefined,
      accountLockTime: undefined
    });

    logger.info(`Password reset for user ${id} by admin ${user.id}`, LogCategory.AUTH, {
      targetUserId: id,
      adminUserId: user.id,
      resetType,
      isTemporary
    });

    // Return response (include temporary password only in development)
    const response: any = {
      status: 'success',
      message: 'Password reset successfully',
      data: {
        isTemporary,
        resetType,
        resetBy: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email
        },
        resetAt: new Date().toISOString()
      }
    };

    // Only include the temporary password in development mode
    if (isTemporary && process.env.NODE_ENV === 'development') {
      response.data.temporaryPassword = finalPassword;
    }

    return NextResponse.json(response);

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while resetting password';
    logger.error('Admin password reset API error', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage),
      { path: req.nextUrl.pathname }
    );

    return errorService.createApiResponse(
      ErrorType.DATABASE,
      'PASSWORD_RESET_FAILED',
      'Failed to reset user password',
      'Unable to reset the user password. This may be due to a database issue.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: {
          errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try again in a few moments',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      [
        {
          label: 'Retry',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        }
      ]
    );
  }
}

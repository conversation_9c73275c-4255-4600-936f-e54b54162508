// app/api/leave/requests/approvals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Leave from '@/models/leave/Leave';
import Employee from '@/models/Employee';
import Department from '@/models/Department';

/**
 * GET /api/leave/requests/approvals
 * Get leave requests that need approval
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'pending';
    const departmentId = searchParams.get('departmentId');

    // Build query
    const query: Record<string, unknown> = {};

    if (status !== 'all') {
      query.status = status;
    }

    // If user is department head, only show requests from their department
    if (hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]) &&
        !hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_DIRECTOR,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ])) {
      // Get department where user is head
      const department = await Department.findOne({ head: user.id });

      if (!department) {
        return NextResponse.json({
          data: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0
          }
        });
      }

      // Get employees in department
      const employees = await Employee.find({ departmentId: department._id });
      const employeeIds = employees.map(emp => emp._id);

      query.employeeId = { $in: employeeIds };
    } else if (departmentId) {
      // Filter by department if specified
      const employees = await Employee.find({ departmentId });
      const employeeIds = employees.map(emp => emp._id);

      query.employeeId = { $in: employeeIds };
    }

    // Get leave requests
    const leaveRequests = await Leave.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName employeeNumber position avatar')
      .populate('leaveTypeId', 'name code color')
      .populate('approvedBy', 'name')
      .lean();

    // Get total count
    const total = await Leave.countDocuments(query);

    // Format response
    const formattedRequests = leaveRequests.map(request => ({
      id: request._id,
      leaveId: request.leaveId,
      employee: {
        id: request.employeeId._id,
        name: `${request.employeeId.firstName} ${request.employeeId.lastName}`,
        position: request.employeeId.position,
        avatar: request.employeeId.avatar
      },
      leaveType: {
        id: request.leaveTypeId._id,
        name: request.leaveTypeId.name,
        color: request.leaveTypeId.color
      },
      startDate: request.startDate,
      endDate: request.endDate,
      duration: request.duration,
      reason: request.reason,
      status: request.status,
      requestDate: request.createdAt,
      reviewDate: request.approvalDate || request.rejectionDate,
      reviewedBy: request.approvedBy ? {
        id: request.approvedBy._id,
        name: request.approvedBy.name
      } : undefined,
      rejectionReason: request.rejectionReason,
      notes: request.notes,
      attachments: request.attachments
    }));

    return NextResponse.json({
      data: formattedRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting leave requests for approval', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

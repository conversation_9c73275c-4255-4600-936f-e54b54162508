import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveAttendanceService } from '@/services/attendance/LeaveAttendanceService';
import Leave from '@/models/leave/Leave';

/**
 * GET /api/leave/[id]/attendance
 * Get attendance records for a leave
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Check if leave exists
    const leave = await Leave.findById(id)
      .populate('employeeId', 'firstName lastName');

    if (!leave) {
      return NextResponse.json(
        { error: 'Leave not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to view this leave's attendance records
    const isOwnLeave = leave.employeeId._id.toString() === user.id;
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!isOwnLeave && !hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get attendance records for leave
    const attendanceRecords = await leaveAttendanceService.getAttendanceRecordsForLeave(id);

    return NextResponse.json({
      leave: {
        id: leave._id,
        employee: {
          id: leave.employeeId._id,
          name: `${leave.employeeId.firstName} ${leave.employeeId.lastName}`
        },
        startDate: leave.startDate,
        endDate: leave.endDate,
        status: leave.status
      },
      attendanceRecords
    });
  } catch (error: unknown) {
    logger.error('Error getting attendance records for leave', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

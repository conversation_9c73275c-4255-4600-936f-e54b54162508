import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import LeaveType from '@/models/leave/LeaveType';

/**
 * GET /api/leave/types
 * Get leave types
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const active = searchParams.get('active');

    // Build query
    const query: Record<string, unknown> = {};
    if (active === 'true') {
      query.isActive = true;
    }

    // Get leave types
    const leaveTypes = await LeaveType.find(query)
      .sort({ name: 1 })
      .populate('applicableDepartments', 'name')
      .lean();

    return NextResponse.json(leaveTypes);
  } catch (error: unknown) {
    logger.error('Error getting leave types', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/leave/types
 * Create a new leave type
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.code || body.defaultDays === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, code, defaultDays' },
        { status: 400 }
      );
    }

    // Check if leave type with same code already exists
    const existingLeaveType = await LeaveType.findOne({ code: body.code });
    if (existingLeaveType) {
      return NextResponse.json(
        { error: `Leave type with code ${body.code} already exists` },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create leave type
    const leaveType = new LeaveType(body);
    await leaveType.save();

    return NextResponse.json(
      {
        success: true,
        message: 'Leave type created successfully',
        data: leaveType
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating leave type', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

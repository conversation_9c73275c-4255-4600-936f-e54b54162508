// app/api/leave/calendar/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';
import Leave from '@/models/leave/Leave';
import Employee from '@/models/Employee';

export const runtime = 'nodejs';



// Define MongoDB query interface for leave calendar
interface LeaveCalendarQuery {
  $or: Array<{
    startDate?: { $gte?: Date; $lte?: Date };
    endDate?: { $gte?: Date; $lte?: Date };
    $and?: Array<{ startDate?: { $lte?: Date }; endDate?: { $gte?: Date } }>;
  }>;
  status: { $in: string[] };
  employeeId?: { $in: mongoose.Types.ObjectId[] };
}

/**
 * GET /api/leave/calendar
 * Get leave calendar data
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const departmentId = searchParams.get('departmentId');

    // Validate required parameters
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required parameters: startDate, endDate' },
        { status: 400 }
      );
    }

    // Build query
    const query: LeaveCalendarQuery = {
      $or: [
        {
          startDate: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        },
        {
          endDate: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        },
        {
          $and: [
            { startDate: { $lte: new Date(startDate) } },
            { endDate: { $gte: new Date(endDate) } }
          ]
        }
      ],
      status: { $in: ['approved', 'pending'] }
    };

    // Filter by department if specified
    if (departmentId) {
      // Get employees in department
      const employees = await Employee.find({ departmentId });
      const employeeIds = employees.map(emp => emp._id);

      query.employeeId = { $in: employeeIds };
    }

    // Get leave requests
    const leaveRequests = await Leave.find(query)
      .populate('employeeId', 'firstName lastName position avatar')
      .populate('leaveTypeId', 'name code color')
      .lean();

    // Format response
    const formattedEvents = leaveRequests.map(leave => ({
      id: leave._id,
      employeeId: leave.employeeId._id,
      employeeName: `${leave.employeeId.firstName} ${leave.employeeId.lastName}`,
      employeeAvatar: leave.employeeId.avatar,
      leaveType: leave.leaveTypeId.name,
      leaveTypeColor: leave.leaveTypeId.color,
      startDate: leave.startDate,
      endDate: leave.endDate,
      status: leave.status
    }));

    return NextResponse.json(formattedEvents);
  } catch (error: unknown) {
    logger.error('Error getting leave calendar data', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

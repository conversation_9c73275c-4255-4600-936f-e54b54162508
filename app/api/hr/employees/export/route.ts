// app/api/hr/employees/export/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { EmployeeImportExportService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: any;
  departmentId?: string | mongoose.Types.ObjectId;
  employmentStatus?: string;
  hireDate?: { $gte?: Date; $lte?: Date };
  $or?: Array<Record<string, any>>;
}


export const runtime = 'nodejs';



// Initialize services
const importExportService = new EmployeeImportExportService();

/**
 * GET handler for employee exports
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can export employees
    const hasExportPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasExportPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const format = searchParams.get('format') || 'excel';
    const departmentId = searchParams.get('departmentId');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const sortBy = searchParams.get('sortBy') || 'lastName';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    logger.info('Employee export request', LogCategory.API, {
      format,
      departmentId,
      status,
      search,
      dateFrom,
      dateTo,
      sortBy,
      sortOrder
    });

    // Build filter

    const filter: MongoFilter = {};

    // Add department filter
    if (departmentId) {
      // Check if it's a department name or ID
      if (mongoose.Types.ObjectId.isValid(departmentId)) {
        filter.departmentId = new mongoose.Types.ObjectId(departmentId);
      } else {
        // Department heads can only export their own department
        const isDepartmentHead = hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]);
        const isHR = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ]);

        if (isDepartmentHead && !isHR && user.department !== departmentId) {
          return NextResponse.json({ error: 'Forbidden: You can only export employees from your department' }, { status: 403 });
        }

        filter.departmentId = departmentId;
      }
    }

    // Add status filter
    if (status) {
      filter.employmentStatus = status;
    }

    // Add date range filter
    if (dateFrom || dateTo) {
      filter.hireDate = {};

      if (dateFrom) {
        filter.hireDate.$gte = new Date(dateFrom);
      }

      if (dateTo) {
        filter.hireDate.$lte = new Date(dateTo);
      }
    }

    // Add search filter
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } }
      ];
    }

    // Export employees
    const exportData = await importExportService.exportData(format, filter);

    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `employees_export_${timestamp}.${format === 'excel' ? 'xlsx' : format}`;

    // Return file
    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(exportData.data);

    return new NextResponse(uint8Array, {
      headers: {
        'Content-Type': exportData.contentType,
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    });
  } catch (error: unknown) {
    logger.error('Error in employee export handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while exporting employees' }, { status: 500 });
  }
}

/**
 * POST handler for employee exports with more complex filters
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can export employees
    const hasExportPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasExportPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();
    const { format = 'excel', filter = {}, options = {} } = body;

    logger.info('Employee export POST request', LogCategory.API, { format, filter, options });

    // Department heads can only export their own department
    const isDepartmentHead = hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]);
    const isHR = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (isDepartmentHead && !isHR && filter.departmentId && user.department !== filter.departmentId) {
      return NextResponse.json({ error: 'Forbidden: You can only export employees from your department' }, { status: 403 });
    }

    // Export employees
    const exportData = await importExportService.exportData(format, filter);

    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = options.filename || `employees_export_${timestamp}.${format === 'excel' ? 'xlsx' : format}`;

    // Return file
    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(exportData.data);

    return new NextResponse(uint8Array, {
      headers: {
        'Content-Type': exportData.contentType,
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    });
  } catch (error: unknown) {
    logger.error('Error in employee export POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while exporting employees' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

// Initialize services
const employeeService = new EmployeeService();

/**
 * POST handler for bulk deleting employees
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Get current user
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check permissions - only certain roles can delete employees
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);
    
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get request body
    const body = await request.json();
    
    logger.info('Employee bulk delete request', LogCategory.API, { body });
    
    // Validate request
    if (!body.ids && !body.filter) {
      return NextResponse.json({
        error: 'Either employee IDs or filter criteria must be provided'
      }, { status: 400 });
    }
    
    // If IDs are provided, validate them
    if (body.ids && (!Array.isArray(body.ids) || body.ids.length === 0)) {
      return NextResponse.json({
        error: 'Employee IDs must be a non-empty array'
      }, { status: 400 });
    }
    
    // If filter is provided, validate it
    if (body.filter) {
      // At least one filter criteria must be provided
      if (!body.filter.department && !body.filter.status && !body.filter.dateRange && !body.filter.searchTerm) {
        return NextResponse.json({
          error: 'At least one filter criteria must be provided'
        }, { status: 400 });
      }
      
      // If date range is provided, validate it
      if (body.filter.dateRange) {
        if (!body.filter.dateRange.from || !body.filter.dateRange.to) {
          return NextResponse.json({
            error: 'Date range must include both from and to dates'
          }, { status: 400 });
        }
        
        // Convert string dates to Date objects
        body.filter.dateRange.from = new Date(body.filter.dateRange.from);
        body.filter.dateRange.to = new Date(body.filter.dateRange.to);
        
        // Validate date range
        if (isNaN(body.filter.dateRange.from.getTime()) || isNaN(body.filter.dateRange.to.getTime())) {
          return NextResponse.json({
            error: 'Invalid date format in date range'
          }, { status: 400 });
        }
      }
    }
    
    // Perform bulk delete
    const result = await employeeService.bulkDelete({
      ids: body.ids,
      filter: body.filter
    });
    
    // Return result
    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount,
      errors: result.errors
    });
  } catch (error: unknown) {
    logger.error('Error in employee bulk delete handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Role from '@/models/Role';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Define roles that can manage roles
const ROLE_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

/**
 * GET /api/hr/roles
 * Get roles with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const active = searchParams.get('active');
    const departmentId = searchParams.get('department');

    // Build query
    const query: Record<string, unknown> = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (active === 'true') {
      query.isActive = true;
    } else if (active === 'false') {
      query.isActive = false;
    }

    if (departmentId) {
      query.department = new mongoose.Types.ObjectId(departmentId);
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get roles
    const roles = await Role.find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit)
      .populate('department', 'name')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .lean();

    // Get total count
    const totalDocs = await Role.countDocuments(query);
    const totalPages = Math.ceil(totalDocs / limit);

    return NextResponse.json({
      success: true,
      data: {
        docs: roles,
        totalDocs,
        limit,
        page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting roles', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get roles', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/hr/roles
 * Create a new role
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ROLE_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if role with same code already exists
    const existingRole = await Role.findOne({ code: body.code.toLowerCase() });
    if (existingRole) {
      return NextResponse.json(
        { error: 'Role with this code already exists' },
        { status: 400 }
      );
    }

    // Process the department field
    const roleData = {
      ...body,
      createdBy: user.id
    };

    // If department is "all_departments", set it to null/undefined
    if (roleData.department === "all_departments") {
      delete roleData.department;
    }

    // Create role
    const role = new Role(roleData);

    await role.save();

    return NextResponse.json({
      success: true,
      data: role
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating role', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create role', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

// app/api/hr/departments/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import Department from '@/models/Department';
import { Employee } from '@/models/Employee';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';



// Define roles that can manage departments
const DEPARTMENT_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

// Define roles that can view departments - Allow all authenticated users
const DEPARTMENT_VIEWER_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.HR_SPECIALIST,
  UserRole.DEPARTMENT_HEAD,
  UserRole.TEAM_LEADER,
  UserRole.RECRUITER,
  UserRole.EMPLOYEE,
  UserRole.CONTRACTOR,
  UserRole.INTERN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.ACCOUNTANT,
  UserRole.PAYROLL_SPECIALIST
];

/**
 * GET handler for a specific department
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  logger.info('Department GET by ID request', LogCategory.API, {
    path: request.nextUrl.pathname
  });

  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_VIEWER_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get department
    const department = await Department.findById(id)
      .populate({
        path: 'head',
        select: 'firstName lastName email',
        strictPopulate: false
      })
      .lean();

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // Get employee count for this department
    const employeeCount = await Employee.countDocuments({ departmentId: id });

    // Add employee count to department data
    const departmentWithStats = {
      ...department,
      employeeCount
    };

    return NextResponse.json({
      status: 'success',
      data: departmentWithStats
    });
  } catch (error: unknown) {
    console.error('Error fetching department:', error);
    const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * PATCH handler for updating a department
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  logger.info('Department PATCH request', LogCategory.API, {
    path: request.nextUrl.pathname
  });

  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await request.json();

    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // If name is being updated, check if it's unique
    if (body.name && body.name !== department.name) {
      const existingDepartment = await Department.findOne({ name: body.name });
      if (existingDepartment) {
        return NextResponse.json({ error: 'Department with this name already exists' }, { status: 400 });
      }
    }

    // Update department
    const updatedDepartment = await Department.findByIdAndUpdate(
      id,
      { $set: body },
      { new: true }
    ).populate({
      path: 'head',
      select: 'firstName lastName email',
      strictPopulate: false
    });

    return NextResponse.json({
      status: 'success',
      data: updatedDepartment
    });
  } catch (error: unknown) {
    console.error('Error updating department:', error);
    const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * DELETE handler for removing a department
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  logger.info('Department DELETE request', LogCategory.API, {
    path: request.nextUrl.pathname
  });

  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // Check if there are employees in this department
    const employeeCount = await Employee.countDocuments({ departmentId: id });
    if (employeeCount > 0) {
      return NextResponse.json({
        error: 'Cannot delete department with employees. Reassign employees first.'
      }, { status: 400 });
    }

    // Delete department
    await Department.findByIdAndDelete(id);

    return NextResponse.json({
      status: 'success',
      message: 'Department deleted successfully'
    });
  } catch (error: unknown) {
    console.error('Error deleting department:', error);
    const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

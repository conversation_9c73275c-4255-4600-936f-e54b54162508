// app/api/hr/departments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import Department from '@/models/Department';
import Employee from '@/models/Employee';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: any;
  name?: string | { $regex: string, $options: string };
}


export const runtime = 'nodejs';



// Define roles that can manage departments
const DEPARTMENT_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];

// Define roles that can view departments - Allow all authenticated users
const DEPARTMENT_VIEWER_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.HR_SPECIALIST,
  UserRole.DEPARTMENT_HEAD,
  UserRole.TEAM_LEADER,
  UserRole.RECRUITER,
  UserRole.EMPLOYEE,
  UserRole.CONTRACTOR,
  UserRole.INTERN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.ACCOUNTANT,
  UserRole.PAYROLL_SPECIALIST
];

/**
 * GET handler for departments
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  logger.info('Departments API request received', LogCategory.API, {
    path: request.nextUrl.pathname,
    method: request.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_VIEWER_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const skip = (page - 1) * limit;

    // Build filter

    const filter: MongoFilter = {};

    // Add search filter
    if (search) {
      filter.name = { $regex: search, $options: 'i' };
    }

    // Get total count
    const total = await Department.countDocuments(filter);

    // Get departments
    const departments = await Department.find(filter)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit)
      // Disable strict population to avoid errors with missing fields
      .populate({
        path: 'head',
        select: 'firstName lastName email',
        strictPopulate: false
      })
      .lean();

    // Calculate employee count for each department dynamically
    const departmentsWithEmployeeCount = await Promise.all(
      departments.map(async (department) => {
        const employeeCount = await Employee.countDocuments({
          department: department.name
        });

        return {
          ...department,
          employeeCount
        };
      })
    );

    return NextResponse.json({
      status: 'success',
      data: {
        docs: departmentsWithEmployeeCount,
        totalDocs: total,
        limit,
        totalPages: Math.ceil(total / limit),
        page,
        hasPrevPage: page > 1,
        hasNextPage: page < Math.ceil(total / limit),
        prevPage: page > 1 ? page - 1 : null,
        nextPage: page < Math.ceil(total / limit) ? page + 1 : null
      }
    });
  } catch (error: unknown) {
    console.error('Error fetching departments:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while fetching departments';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * POST handler for creating a new department
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  logger.info('Departments POST request received', LogCategory.API, {
    path: request.nextUrl.pathname,
    method: request.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json({ error: 'Department name is required' }, { status: 400 });
    }

    // Check if department with the same name already exists
    const existingDepartment = await Department.findOne({ name: body.name });
    if (existingDepartment) {
      return NextResponse.json({ error: 'Department with this name already exists' }, { status: 400 });
    }

    // Create department
    const department = await Department.create(body);

    return NextResponse.json({
      status: 'success',
      data: department
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error creating department:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while creating department';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

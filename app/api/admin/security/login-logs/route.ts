import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET handler for login logs
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admins and system admins can access login logs
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);
    
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');
    const ipAddress = searchParams.get('ipAddress');
    const country = searchParams.get('country');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    // In a real implementation, this would query the database
    // For now, return mock data
    const mockLoginLogs = [
      {
        id: "1",
        userId: "user123",
        username: "john.doe",
        email: "<EMAIL>",
        ipAddress: "***********",
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        deviceInfo: {
          type: "desktop",
          browser: "Chrome",
          os: "Windows",
          deviceId: "device123"
        },
        location: {
          country: "Malawi",
          region: "Central Region",
          city: "Lilongwe"
        },
        status: "success",
        timestamp: "2025-01-15T10:30:00.000Z",
        sessionId: "session123",
        sessionDuration: 3600
      },
      {
        id: "2",
        userId: "user456",
        username: "jane.smith",
        email: "<EMAIL>",
        ipAddress: "***********",
        userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
        deviceInfo: {
          type: "desktop",
          browser: "Safari",
          os: "macOS",
          deviceId: "device456"
        },
        location: {
          country: "Malawi",
          region: "Southern Region",
          city: "Blantyre"
        },
        status: "failed",
        failureReason: "Invalid password",
        timestamp: "2025-01-15T11:45:00.000Z"
      }
    ];

    // Log the request
    logger.info('Login logs retrieved', LogCategory.API, {
      userId: user.id,
      query: {
        userId,
        status,
        ipAddress,
        country,
        startDate,
        endDate,
        page,
        limit
      }
    });

    return NextResponse.json({
      success: true,
      data: mockLoginLogs,
      pagination: {
        page,
        limit,
        total: mockLoginLogs.length,
        totalPages: Math.ceil(mockLoginLogs.length / limit)
      }
    });
  } catch (error) {
    console.error('Error retrieving login logs:', error);
    logger.error('Error retrieving login logs', LogCategory.API, { error });
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

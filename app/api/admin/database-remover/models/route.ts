// app/api/admin/database-remover/models/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { databaseRemoverService } from '@/lib/services/admin/database-remover-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/admin/database-remover/models
 * Get all available database models with their counts and metadata
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin can access database remover
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      logger.warn('Unauthorized database remover access attempt', LogCategory.SECURITY, {
        userId: user.id,
        userRole: user.role,
        endpoint: '/api/admin/database-remover/models'
      });
      return NextResponse.json(
        { error: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    logger.info('Database models requested', LogCategory.ADMIN, {
      userId: user.id,
      userRole: user.role
    });

    // Get all models with current counts
    const models = await databaseRemoverService.getAllModels();

    // Group models by category for better organization
    const categorizedModels = {
      system: models.filter(m => ['User', 'AuditLog', 'DeletedItems'].includes(m.name)),
      hr: models.filter(m => ['Employee', 'Department'].includes(m.name)),
      payroll: models.filter(m => m.name.toLowerCase().includes('payroll') || m.name.includes('Salary')),
      accounting: models.filter(m => ['Income', 'Expense', 'Budget'].includes(m.name)),
      other: models.filter(m => !['User', 'AuditLog', 'DeletedItems', 'Employee', 'Department', 'Income', 'Expense', 'Budget'].includes(m.name) && !m.name.toLowerCase().includes('payroll') && !m.name.includes('Salary'))
    };

    // Calculate totals
    const totalRecords = models.reduce((sum, model) => sum + model.count, 0);
    const totalModels = models.length;
    const criticalModels = models.filter(m => m.dangerLevel === 'critical').length;

    return NextResponse.json({
      success: true,
      data: {
        models: categorizedModels,
        summary: {
          totalModels,
          totalRecords,
          criticalModels,
          lastUpdated: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    logger.error('Database models API error', LogCategory.ADMIN, error);
    return NextResponse.json(
      { error: 'Failed to fetch database models' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/database-remover/models
 * Refresh model counts
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    logger.info('Database model counts refresh requested', LogCategory.ADMIN, {
      userId: user.id
    });

    // Update all model counts
    await databaseRemoverService.updateModelCounts();

    // Get updated models
    const models = await databaseRemoverService.getAllModels();

    return NextResponse.json({
      success: true,
      message: 'Model counts refreshed successfully',
      data: {
        models,
        refreshedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Database model refresh error', LogCategory.ADMIN, error);
    return NextResponse.json(
      { error: 'Failed to refresh model counts' },
      { status: 500 }
    );
  }
}

// app/api/admin/database-remover/models/[modelName]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { databaseRemoverService } from '@/lib/services/admin/database-remover-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schemas
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('50')
});

const bulkDeleteSchema = z.object({
  itemIds: z.array(z.string()).min(1, 'At least one item ID is required'),
  deletionReason: z.string().min(10, 'Deletion reason must be at least 10 characters'),
  confirmDeletion: z.boolean(),
  deleteRelatedData: z.boolean().optional().default(false)
});

const deleteAllSchema = z.object({
  deletionReason: z.string().min(10, 'Deletion reason must be at least 10 characters'),
  confirmDeletion: z.boolean(),
  confirmModelName: z.string()
});

/**
 * GET /api/admin/database-remover/models/[modelName]
 * Get data for a specific model with pagination
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ modelName: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    const { modelName } = await params;
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Validate query parameters
    const validationResult = querySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { page, limit } = validationResult.data;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    logger.info('Model data requested', LogCategory.ADMIN, {
      userId: user.id,
      modelName,
      page: pageNum,
      limit: limitNum
    });

    // Get model metadata
    const model = await databaseRemoverService.getModelByName(modelName);
    if (!model) {
      return NextResponse.json(
        { error: `Model ${modelName} not found` },
        { status: 404 }
      );
    }

    // Check if user has permission for this specific model
    if (!model.allowedRoles.includes(user.role as UserRole)) {
      return NextResponse.json(
        { error: `Insufficient permissions to access ${model.displayName}` },
        { status: 403 }
      );
    }

    // Get paginated data
    const data = await databaseRemoverService.getModelData(modelName, pageNum, limitNum);

    return NextResponse.json({
      success: true,
      data: {
        model,
        items: data.items,
        pagination: {
          page: pageNum,
          limit: limitNum,
          totalCount: data.totalCount,
          totalPages: data.totalPages,
          hasNext: pageNum < data.totalPages,
          hasPrev: pageNum > 1
        }
      }
    });

  } catch (error) {
    logger.error('Model data API error', LogCategory.ADMIN, error);
    return NextResponse.json(
      { error: 'Failed to fetch model data' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/database-remover/models/[modelName]
 * Bulk delete items from a specific model
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ modelName: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    const { modelName } = await params;
    const body = await req.json();

    // Check if this is a delete-all operation
    if (body.deleteAll) {
      // Validate delete-all request
      const validationResult = deleteAllSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: validationResult.error.errors[0].message },
          { status: 400 }
        );
      }

      const { deletionReason, confirmDeletion, confirmModelName } = validationResult.data;

      // Additional safety check for delete-all
      if (confirmModelName !== modelName) {
        return NextResponse.json(
          { error: 'Model name confirmation does not match' },
          { status: 400 }
        );
      }

      if (!confirmDeletion) {
        return NextResponse.json(
          { error: 'Deletion confirmation required for delete-all operation' },
          { status: 400 }
        );
      }

      logger.warn('Delete-all operation requested', LogCategory.ADMIN, {
        userId: user.id,
        modelName,
        reason: deletionReason
      });

      // Perform delete-all operation
      const result = await databaseRemoverService.deleteAllModelData(
        modelName,
        {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email,
          role: user.role
        },
        deletionReason
      );

      return NextResponse.json({
        success: result.success,
        message: result.success 
          ? `Successfully deleted all data from ${modelName}` 
          : 'Delete-all operation failed',
        data: result
      });

    } else {
      // Validate bulk delete request
      const validationResult = bulkDeleteSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: validationResult.error.errors[0].message },
          { status: 400 }
        );
      }

      const { itemIds, deletionReason, confirmDeletion, deleteRelatedData } = validationResult.data;

      logger.info('Bulk delete requested', LogCategory.ADMIN, {
        userId: user.id,
        modelName,
        itemCount: itemIds.length,
        reason: deletionReason
      });

      // Perform bulk deletion
      const result = await databaseRemoverService.bulkDeleteItems({
        modelName,
        itemIds,
        deletionReason,
        userInfo: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email,
          role: user.role
        },
        confirmDeletion,
        deleteRelatedData
      });

      return NextResponse.json({
        success: result.success,
        message: result.success 
          ? `Successfully deleted ${result.deletedCount} items from ${modelName}` 
          : 'Bulk deletion failed',
        data: result
      });
    }

  } catch (error) {
    logger.error('Model deletion API error', LogCategory.ADMIN, error);
    return NextResponse.json(
      { error: 'Failed to delete model data' },
      { status: 500 }
    );
  }
}

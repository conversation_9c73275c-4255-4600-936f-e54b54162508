// app/api/admin/sessions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { sessionService } from '@/lib/backend/services/auth/SessionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';

export async function GET(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin sessions API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin sessions API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin sessions API: Unauthorized access attempt', LogCategory.API, {
        userId: user._id?.toString() || user.id,
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const skip = parseInt(url.searchParams.get('skip') || '0');

    // Get all active sessions
    logger.debug('Getting all active sessions', LogCategory.API, { limit, skip });
    const { sessions, total } = await sessionService.getAllActiveSessions(limit, skip);

    // Return sessions
    logger.info(`Retrieved ${sessions.length} active sessions`, LogCategory.API);
    return NextResponse.json({
      status: 'success',
      data: {
        sessions,
        total,
        limit,
        skip
      }
    });
  } catch (error: unknown) {
    logger.error('Admin sessions API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while retrieving sessions' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin terminate session API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin terminate session API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin terminate session API: Unauthorized access attempt', LogCategory.API, {
        userId: user._id?.toString() || user.id,
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    let sessionId;
    try {
      const body = await req.json();
      sessionId = body.sessionId;

      logger.debug('Admin terminate session request parsed', LogCategory.API, {
        sessionId: sessionId || 'not provided'
      });
    } catch (parseError: unknown) {
      logger.error('Failed to parse admin terminate session request body', LogCategory.API,
        parseError instanceof Error ? parseError : new Error(typeof parseError === 'object' && parseError !== null && 'message' in parseError ? String(parseError.message) : 'Unknown error')
      );

      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    // Validate input
    if (!sessionId) {
      logger.warn('Admin terminate session validation failed: Missing session ID', LogCategory.API);
      return NextResponse.json(
        { error: 'Please provide session ID' },
        { status: 400 }
      );
    }

    // End the session
    const success = await sessionService.endSession(sessionId);
    if (!success) {
      logger.warn('Admin terminate session failed: Invalid session ID', LogCategory.API, { sessionId });
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      );
    }

    // Return success response
    logger.info('Admin terminate session successful', LogCategory.API, {
      userId: user._id?.toString() || user.id,
      sessionId
    });

    return NextResponse.json({
      status: 'success',
      message: 'Session terminated successfully'
    });
  } catch (error: unknown) {
    logger.error('Admin terminate session API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while terminating session' },
      { status: 500 }
    );
  }
}

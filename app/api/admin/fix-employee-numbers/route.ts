// app/api/admin/fix-employee-numbers/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { connectToDatabase } from '@/lib/backend/database'
import { Employee } from '@/models/Employee'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

/**
 * API route to fix employee numbers
 * This sets the employeeNumber field to match the employeeId for all employees
 * Only accessible by super_admin and system_admin
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions - only super_admin and system_admin can run this
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Find all employees without employeeNumber or with null employeeNumber
    const employees = await Employee.find({
      $or: [
        { employeeNumber: { $exists: false } },
        { employeeNumber: null },
        { employeeNumber: "" }
      ]
    }).sort({ createdAt: 1 }) // Sort by creation date to process oldest records first

    logger.info('Fixing employee numbers', LogCategory.SYSTEM, {
      totalEmployees: employees.length,
      userId: user._id?.toString() || user.id
    })

    // Update each employee
    const result = {
      totalEmployees: employees.length,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ employeeId: string, error: string }>
    }

    // Generate a unique prefix for this batch
    const timestamp = Date.now().toString().slice(-6)
    const prefix = `EMP-FIX-${timestamp}-`

    // Process each employee
    for (let i = 0; i < employees.length; i++) {
      const employee = employees[i]
      try {
        // Generate a unique employee number
        // If employeeId exists, use it; otherwise create a new unique number
        let newEmployeeNumber = employee.employeeId

        // If employeeId is null or empty, generate a completely new number
        if (!newEmployeeNumber || newEmployeeNumber.trim() === '') {
          newEmployeeNumber = `${prefix}${(i + 1).toString().padStart(4, '0')}`
        }

        // Check if this employeeNumber already exists
        const existingWithNumber = await Employee.findOne({
          employeeNumber: newEmployeeNumber,
          _id: { $ne: employee._id } // Exclude the current employee
        })

        // If it exists, create a truly unique number
        if (existingWithNumber) {
          // Add a random suffix to make it unique
          const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
          newEmployeeNumber = `${prefix}${(i + 1).toString().padStart(4, '0')}-${random}`

          // Double-check that this new number is unique
          const doubleCheck = await Employee.findOne({ employeeNumber: newEmployeeNumber })
          if (doubleCheck) {
            // If still not unique, use timestamp for absolute uniqueness
            newEmployeeNumber = `${prefix}${Date.now()}-${random}`
          }
        }

        // Update the employee
        employee.employeeNumber = newEmployeeNumber
        await employee.save()
        result.successCount++

        logger.info('Fixed employee number', LogCategory.SYSTEM, {
          employeeId: employee.employeeId,
          employeeNumber: employee.employeeNumber,
          userId: user._id?.toString() || user.id
        })
      } catch (error: unknown) {
        result.errorCount++
        result.errors.push({
          employeeId: employee.employeeId || 'unknown',
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        })

        logger.error('Error fixing employee number', LogCategory.SYSTEM, {
          employeeId: employee.employeeId || 'unknown',
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          userId: user._id?.toString() || user.id
        })
      }
    }

    // Log the final result
    logger.info('Employee number fix completed', LogCategory.SYSTEM, {
      totalEmployees: result.totalEmployees,
      successCount: result.successCount,
      errorCount: result.errorCount,
      userId: user._id?.toString() || user.id
    })

    return NextResponse.json(result)
  } catch (error: unknown) {
    logger.error('Error fixing employee numbers', LogCategory.SYSTEM, {
      error: error instanceof Error ? error.message : 'An unknown error occurred',
      stack: error instanceof Error ? error.stack : undefined
    })

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An error occurred',
      details: process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

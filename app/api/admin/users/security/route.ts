// app/api/admin/users/security/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { userSecurityService } from '@/lib/backend/services/auth/UserSecurityService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole, UserStatus } from '@/types/user-roles';

export async function GET(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin users security API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin users security API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin users security API: Unauthorized access attempt', LogCategory.API, {
        userId: user._id?.toString() || user.id,
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const search = url.searchParams.get('search') || undefined;
    const statusParam = url.searchParams.get('status');

    // Parse status filter
    let statusFilter: UserStatus[] | undefined;
    if (statusParam) {
      statusFilter = statusParam.split(',').map(s => s.trim() as UserStatus);
    }

    // Get all users with security information
    logger.debug('Getting all users security information', LogCategory.API, { limit, skip, search, statusFilter });
    const { users, total } = await userSecurityService.getAllUsersSecurityInfo(limit, skip, {
      status: statusFilter,
      search
    });

    // Return users
    logger.info(`Retrieved ${users.length} users security information`, LogCategory.API);
    return NextResponse.json({
      status: 'success',
      data: {
        users,
        total,
        limit,
        skip
      }
    });
  } catch (error: unknown) {
    logger.error('Admin users security API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while retrieving users security information' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin user security action API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin user security action API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin user security action API: Unauthorized access attempt', LogCategory.API, {
        userId: user._id?.toString() || user.id,
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { action, userId, reason } = body;

    // Validate input
    if (!action || !userId) {
      logger.warn('Admin user security action API: Missing required parameters', LogCategory.API, {
        action,
        userId
      });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Perform action
    let result;
    switch (action) {
      case 'block':
        result = await userSecurityService.blockUser(userId, reason || 'Blocked by administrator', user._id?.toString() || user.id);
        break;
      case 'ban':
        result = await userSecurityService.banUser(userId, reason || 'Banned by administrator', user._id?.toString() || user.id);
        break;
      case 'revoke':
        result = await userSecurityService.revokeUser(userId, reason || 'Access revoked by administrator', user._id?.toString() || user.id);
        break;
      case 'reactivate':
        result = await userSecurityService.reactivateUser(userId, reason || 'Reactivated by administrator', user._id?.toString() || user.id);
        break;
      case 'updateSettings':
        const { allowMultipleDevices, trustedDevicesOnly, securityNotes } = body;
        result = await userSecurityService.updateSecuritySettings(
          userId,
          {
            allowMultipleDevices,
            trustedDevicesOnly,
            securityNotes
          },
          user._id?.toString() || user.id
        );
        break;
      default:
        logger.warn('Admin user security action API: Invalid action', LogCategory.API, { action });
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Return result
    logger.info(`User security action '${action}' performed successfully`, LogCategory.API, {
      userId,
      adminId: user._id?.toString() || user.id
    });
    return NextResponse.json({
      status: 'success',
      message: `User ${action} action performed successfully`,
      data: {
        user: result
      }
    });
  } catch (error: unknown) {
    logger.error('Admin user security action API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while performing user security action' },
      { status: 500 }
    );
  }
}

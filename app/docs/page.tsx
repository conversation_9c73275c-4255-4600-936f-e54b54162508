"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowRight, Book, Building, Calculator, CheckSquare, Clock, Code, DollarSign, FileText, Landmark, Layers, Package, Search, Settings, Shield, UserCircle, UserPlus, Users, Wallet } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"

export default function DocsPage() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="flex flex-col min-h-screen overflow-hidden">
      {/* Hero Section */}
      <section className="w-full py-24 md:py-32 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                Documentation
              </span>
            </div>
            <h1 className="text-3xl md:text-5xl font-bold tracking-tighter text-white">
              TCM Enterprise <span className="text-emerald-400">Documentation</span>
            </h1>
            <p className="max-w-[700px] text-zinc-400 md:text-xl/relaxed">
              Comprehensive guides and documentation for the TCM Enterprise Business Suite.
            </p>
            <div className="w-full max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-zinc-500" />
                <Input
                  type="search"
                  placeholder="Search documentation..."
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-zinc-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col gap-8">
            {/* Breadcrumbs */}
            <Breadcrumb className="mb-4">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Documentation Tabs */}
            <Tabs defaultValue="guides" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                <TabsTrigger value="guides" className="text-sm md:text-base">Guides</TabsTrigger>
                <TabsTrigger value="api" className="text-sm md:text-base">API Reference</TabsTrigger>
                <TabsTrigger value="components" className="text-sm md:text-base">Components</TabsTrigger>
                <TabsTrigger value="development" className="text-sm md:text-base">Development</TabsTrigger>
              </TabsList>

              {/* Guides Tab Content */}
              <TabsContent value="guides" className="space-y-8">
                {/* Core Modules Section */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-zinc-900 mb-4">Core Business Modules</h2>
                  <p className="text-zinc-600 mb-6">
                    Comprehensive documentation for the three primary modules of the TCM Enterprise Suite.
                  </p>
                  <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
                    <Link href="/docs/employees">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200">
                        <CardHeader>
                          <Users className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Employee Management</CardTitle>
                          <CardDescription>Core entity management for organizational staff</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Master data management, employee directory, lifecycle, and organizational structure.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• User Guide - Daily operations</div>
                            <div>• Management Guide - Strategic oversight</div>
                            <div>• Developer Docs - Technical implementation</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Explore Module <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/hr">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200">
                        <CardHeader>
                          <UserPlus className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Human Resources</CardTitle>
                          <CardDescription>HR processes and strategic workforce management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Recruitment, performance management, training, policies, and compliance.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• User Guide - HR workflows</div>
                            <div>• Management Guide - Strategic HR</div>
                            <div>• Developer Docs - System integration</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Explore Module <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/accounting">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200">
                        <CardHeader>
                          <Landmark className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Accounting & Finance</CardTitle>
                          <CardDescription>Complete financial management system</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            General ledger, banking, payments, reporting, and Malawian tax compliance.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• User Guide - Daily transactions</div>
                            <div>• Management Guide - Financial oversight</div>
                            <div>• Developer Docs - API integration</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Explore Module <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>
                  </div>
                </div>

                {/* System Guides Section */}
                <div>
                  <h2 className="text-2xl font-bold text-zinc-900 mb-4">System Guides</h2>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Link href="/docs/getting-started">
                      <Card className="h-full transition-all hover:shadow-md">
                        <CardHeader>
                          <Book className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Getting Started</CardTitle>
                          <CardDescription>Learn the basics and get up and running quickly</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600">
                            An introduction to the system, installation, and basic configuration.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Read Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/developer">
                      <Card className="h-full transition-all hover:shadow-md border-blue-200">
                        <CardHeader>
                          <Code className="h-8 w-8 text-blue-500 mb-2" />
                          <CardTitle>Developer Documentation</CardTitle>
                          <CardDescription>Complete development workflow and project guidelines</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600">
                            Comprehensive guides for developers working on the TCM Enterprise project.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                            View Docs <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/authentication">
                      <Card className="h-full transition-all hover:shadow-md">
                        <CardHeader>
                          <Shield className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Authentication</CardTitle>
                          <CardDescription>User management and authentication</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600">
                            Learn about user roles, authentication flows, and security features.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Read Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/settings">
                      <Card className="h-full transition-all hover:shadow-md">
                        <CardHeader>
                          <Settings className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>System Settings</CardTitle>
                          <CardDescription>Configuring system settings</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600">
                            Learn how to configure and customize the system settings to your needs.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Read Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/bulk-import">
                      <Card className="h-full transition-all hover:shadow-md">
                        <CardHeader>
                          <FileText className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle>Bulk Import</CardTitle>
                          <CardDescription>Importing data in bulk</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600">
                            Guide to importing employee data in bulk using CSV or Excel files.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Read Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>
                  </div>
                </div>

                {/* New HR & Role Management Guides Section */}
                <div>
                  <h2 className="text-2xl font-bold text-zinc-900 mb-4">HR & Role Management 🆕</h2>
                  <p className="text-zinc-600 mb-6">
                    Comprehensive guides for the new TCM role management system and enhanced HR workflows.
                  </p>
                  <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
                    <Link href="/docs/role-management">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200 bg-emerald-50">
                        <CardHeader>
                          <Users className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            Role Management
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">🎉 Complete</span>
                          </CardTitle>
                          <CardDescription>TCM organizational role system management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Complete guide for managing the 31 TCM organizational roles with salary integration.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• TCM role hierarchy (TCM 1-12)</div>
                            <div>• Role-based salary validation</div>
                            <div>• Department integration</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Explore Roles <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/hr-system">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200 bg-emerald-50">
                        <CardHeader>
                          <Building className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            HR System Guide
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">🆕 New</span>
                          </CardTitle>
                          <CardDescription>Comprehensive HR workflow management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Complete HR management from organizational setup to employee lifecycle.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• Integrated HR workflows</div>
                            <div>• Role-based processes</div>
                            <div>• Organizational management</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            View Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/payroll-system">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200 bg-emerald-50">
                        <CardHeader>
                          <DollarSign className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            Payroll System
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">🆕 Enhanced</span>
                          </CardTitle>
                          <CardDescription>Role-based salary management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Payroll management with TCM role integration, salary band validation, and bulk operations.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• TCM salary bands</div>
                            <div>• Role-based validation</div>
                            <div>• Bulk operations</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Learn More <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>
                  </div>
                </div>

                {/* Enhanced Payroll Processing Section */}
                <div>
                  <h2 className="text-2xl font-bold text-zinc-900 mb-4">Enhanced Payroll Processing 🚀</h2>
                  <p className="text-zinc-600 mb-6">
                    Comprehensive documentation for the enhanced payroll run system with real-time tracking and accounting integration.
                  </p>
                  <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
                    <Link href="/docs/payroll-runs">
                      <Card className="h-full transition-all hover:shadow-md border-emerald-200 bg-emerald-50">
                        <CardHeader>
                          <Calculator className="h-8 w-8 text-emerald-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            Payroll Runs Guide
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">🆕 Enhanced</span>
                          </CardTitle>
                          <CardDescription>Complete payroll run processing documentation</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Comprehensive guide for the enhanced payroll run system with real-time progress tracking,
                            visual feedback, and accounting integration.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• Enhanced processing with real-time tracking</div>
                            <div>• Visual feedback and progress indicators</div>
                            <div>• Accounting system integration</div>
                            <div>• Error handling and recovery</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                            Explore Payroll Runs <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/user-guides/payroll-bulk-operations">
                      <Card className="h-full transition-all hover:shadow-md border-blue-200 bg-blue-50">
                        <CardHeader>
                          <UserCircle className="h-8 w-8 text-blue-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            Bulk Operations
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">✨ Advanced</span>
                          </CardTitle>
                          <CardDescription>Bulk payroll processing operations</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            Step-by-step instructions for processing, approving, and paying multiple payroll runs efficiently.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• Bulk processing workflows</div>
                            <div>• Approval and payment processes</div>
                            <div>• Best practices and troubleshooting</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                            Read User Guide <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>

                    <Link href="/docs/technical/payroll-bulk-operations">
                      <Card className="h-full transition-all hover:shadow-md border-purple-200 bg-purple-50">
                        <CardHeader>
                          <Code className="h-8 w-8 text-purple-500 mb-2" />
                          <CardTitle className="flex items-center gap-2">
                            Technical Guide
                            <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">🔧 Dev</span>
                          </CardTitle>
                          <CardDescription>Technical documentation for developers</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-zinc-600 mb-3">
                            API documentation, enhanced processing architecture, real-time tracking implementation,
                            and system integration details.
                          </p>
                          <div className="space-y-1 text-xs text-zinc-500">
                            <div>• Enhanced processing APIs</div>
                            <div>• Real-time tracking implementation</div>
                            <div>• Accounting integration</div>
                            <div>• Performance optimization</div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                            View Technical Docs <ArrowRight className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    </Link>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>
    </div>
  )
}
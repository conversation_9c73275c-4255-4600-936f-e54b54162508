// app/docs/employee-payroll/page.tsx
"use client"

import Link from "next/link"
import { ArrowRight, Book, FileText, Users, Wallet, RefreshCw, Building, Code, Upload, Download } from "lucide-react"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function EmployeePayrollGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Employee & Payroll Guide
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Comprehensive documentation for managing employees and processing payroll
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-6">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/employee-payroll">Employee & Payroll</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="mb-8">
                <h2 className="text-2xl font-bold tracking-tight mb-4">Employee & Payroll Documentation</h2>
                <p className="text-muted-foreground mb-6">
                  This comprehensive guide covers all aspects of employee management and payroll processing in the TCM Enterprise Business Suite.
                  Choose from the user guide for day-to-day operations or the technical guide for implementation details.
                </p>
              </div>

              <div className="grid gap-6 md:grid-cols-3">
                <Link href="/docs/employee-payroll/user-guide">
                  <Card className="h-full transition-all hover:shadow-md">
                    <CardHeader>
                      <Book className="h-8 w-8 text-emerald-500 mb-2" />
                      <CardTitle>User Guide</CardTitle>
                      <CardDescription>For day-to-day operations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600">
                        Learn how to use the employee and payroll modules for everyday tasks including employee management,
                        bulk operations, payroll processing, and reporting.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                        Read User Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/employee-payroll/technical-guide">
                  <Card className="h-full transition-all hover:shadow-md">
                    <CardHeader>
                      <FileText className="h-8 w-8 text-emerald-500 mb-2" />
                      <CardTitle>Technical Guide</CardTitle>
                      <CardDescription>For implementation and customization</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600">
                        Technical documentation covering API endpoints, data models, integration points,
                        and implementation details for developers and system administrators.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                        Read Technical Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/employee-payroll/recent-updates">
                  <Card className="h-full transition-all hover:shadow-md">
                    <CardHeader>
                      <RefreshCw className="h-8 w-8 text-emerald-500 mb-2" />
                      <CardTitle>Recent Updates</CardTitle>
                      <CardDescription>Latest enhancements and changes</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600">
                        Summary of recent updates to the Employee and Payroll modules, including new features,
                        improvements, and bug fixes.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                        View Recent Updates <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>

              <div className="mt-12">
                <h3 className="text-xl font-semibold mb-4">Specific Module Guides</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  <Link href="/docs/employee-payroll/salary-bands-user-guide">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Building className="h-6 w-6 text-blue-500 mb-2" />
                        <CardTitle className="text-lg">Salary Bands User Guide</CardTitle>
                        <CardDescription>For HR and Payroll teams</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Complete guide for managing TCM 1-12 salary bands, bulk import operations, and compensation structures.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                          View User Guide <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>

                  <Link href="/docs/employee-payroll/salary-bands-technical-guide">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Code className="h-6 w-6 text-purple-500 mb-2" />
                        <CardTitle className="text-lg">Salary Bands Technical Guide</CardTitle>
                        <CardDescription>For developers and system admins</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Technical documentation covering API endpoints, database schema, security implementation, and integration points.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                          View Technical Guide <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>

                  <Link href="/docs/bulk-import">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Upload className="h-6 w-6 text-green-500 mb-2" />
                        <CardTitle className="text-lg">Bulk Import Guide</CardTitle>
                        <CardDescription>For all bulk operations</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Comprehensive guide for bulk import operations across all modules including employees, departments, and salary bands.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-green-600">
                          View Bulk Import Guide <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>

                  <Link href="/docs/employee-payroll/export-user-guide">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Download className="h-6 w-6 text-orange-500 mb-2" />
                        <CardTitle className="text-lg">Export Operations Guide</CardTitle>
                        <CardDescription>For data export and reporting</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Complete guide for exporting payroll data including payslips, reports, and bank transfer files.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-orange-600">
                          View Export Guide <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>
                </div>
              </div>

              <div className="mt-12">
                <h3 className="text-xl font-semibold mb-4">Related Documentation</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  <Link href="/docs/employee-management">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Users className="h-6 w-6 text-emerald-500 mb-2" />
                        <CardTitle className="text-lg">Employee Management</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Basic employee management documentation
                        </p>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/docs/development/current_status/payroll">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Wallet className="h-6 w-6 text-emerald-500 mb-2" />
                        <CardTitle className="text-lg">Payroll Development Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Current implementation status of the payroll module
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

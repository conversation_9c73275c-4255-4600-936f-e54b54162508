"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Code,
  Database,
  Server,
  Shield,
  Zap,
  FileText,
  GitBranch,
  Settings,
  AlertTriangle,
  CheckCircle,
  Terminal,
  Layers,
  Lock,
  Monitor
} from "lucide-react"

export default function SalaryBandsTechnicalGuide() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Code className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Salary Bands Technical Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Technical documentation for developers and system administrators managing salary band functionality.
        </p>
      </div>

      {/* Quick Navigation */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Navigation</CardTitle>
          <CardDescription>Jump to specific technical sections</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start" asChild>
              <a href="#architecture">
                <Layers className="h-4 w-4 mr-2" />
                Architecture
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#api-endpoints">
                <Server className="h-4 w-4 mr-2" />
                API Endpoints
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#database-schema">
                <Database className="h-4 w-4 mr-2" />
                Database Schema
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#security">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Architecture Overview */}
      <section id="architecture">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="h-5 w-5 mr-2" />
              System Architecture
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Component Structure</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-600">Frontend Components</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <code>SalaryBandManager</code> - Main management interface</li>
                    <li>• <code>SalaryBandForm</code> - Create/edit form with tabs</li>
                    <li>• <code>SalaryBandDetails</code> - Detailed view component</li>
                    <li>• <code>BulkSalaryBandUpload</code> - Bulk import interface</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">Backend Services</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <code>SalaryBand</code> model with Mongoose schema</li>
                    <li>• <code>RoleSalaryIntegrationService</code> for TCM mapping</li>
                    <li>• Bulk import API with Excel processing</li>
                    <li>• Template generation service</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Data Flow</h4>
              <div className="bg-gray-50 border rounded-lg p-4">
                <div className="space-y-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">1</Badge>
                    <span>User interacts with <code>SalaryBandManager</code> component</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">2</Badge>
                    <span>Frontend makes API calls to <code>/api/payroll/salary-bands/*</code></span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">3</Badge>
                    <span>API validates permissions and processes requests</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">4</Badge>
                    <span>Data persisted to MongoDB via <code>SalaryBand</code> model</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">5</Badge>
                    <span>Integration with employee and payroll systems</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Technology Stack</h4>
              <div className="grid md:grid-cols-3 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Frontend</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Next.js 14 with App Router</li>
                    <li>• React Hook Form with Zod validation</li>
                    <li>• Tailwind CSS + shadcn/ui</li>
                    <li>• TypeScript for type safety</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Backend</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Next.js API Routes</li>
                    <li>• MongoDB with Mongoose ODM</li>
                    <li>• XLSX library for Excel processing</li>
                    <li>• JWT authentication</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Infrastructure</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Vercel deployment</li>
                    <li>• MongoDB Atlas database</li>
                    <li>• File upload handling</li>
                    <li>• Logging and monitoring</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* API Endpoints */}
      <section id="api-endpoints">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              API Endpoints
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Core CRUD Operations</h4>
              <div className="space-y-3">
                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">GET /api/payroll/salary-bands</code>
                    <Badge variant="secondary">Implemented</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Retrieve salary bands with pagination, filtering, and search capabilities.
                    Supports query parameters for active status and search terms.
                  </p>
                </div>

                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">POST /api/payroll/salary-bands</code>
                    <Badge variant="secondary">Implemented</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Create new salary band with validation for TCM code uniqueness,
                    salary range logic, and required fields.
                  </p>
                </div>

                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">GET /api/payroll/salary-bands/[id]</code>
                    <Badge variant="secondary">Implemented</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Retrieve specific salary band details including allowances,
                    deductions, and metadata.
                  </p>
                </div>

                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">PATCH /api/payroll/salary-bands/[id]</code>
                    <Badge variant="secondary">Implemented</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Update existing salary band with partial data support
                    and validation of business rules.
                  </p>
                </div>

                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">DELETE /api/payroll/salary-bands/[id]</code>
                    <Badge variant="secondary">Implemented</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Delete salary band with checks for employee assignments
                    and cascade handling.
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Bulk Operations</h4>
              <div className="space-y-3">
                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">POST /api/payroll/salary-bands/bulk-import</code>
                    <Badge variant="default">New</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Bulk import salary bands from Excel files with comprehensive
                    validation and detailed error reporting.
                  </p>
                  <div className="mt-2">
                    <h6 className="text-xs font-medium mb-1">Request Format:</h6>
                    <code className="text-xs bg-gray-100 p-1 rounded">multipart/form-data</code>
                    <p className="text-xs text-muted-foreground mt-1">
                      Accepts Excel files (.xlsx, .xls) up to 10MB
                    </p>
                  </div>
                </div>

                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono">GET /api/payroll/salary-bands/template</code>
                    <Badge variant="default">New</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Generate Excel template with sample data, instructions,
                    validation rules, and examples for bulk import.
                  </p>
                  <div className="mt-2">
                    <h6 className="text-xs font-medium mb-1">Response Format:</h6>
                    <code className="text-xs bg-gray-100 p-1 rounded">application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</code>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Request/Response Examples</h4>
              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">Create Salary Band Request</h5>
                  <pre className="bg-gray-900 text-gray-100 p-3 rounded-lg text-xs overflow-x-auto">
{`{
  "tcmCode": "TCM 1",
  "name": "Registrar",
  "description": "Chief Executive Officer level",
  "minSalary": 2500000,
  "maxSalary": 3500000,
  "currency": "MWK",
  "stepIncrement": 100000,
  "maxSteps": 10,
  "annualIncrementPercentage": 5,
  "effectiveDate": "2024-01-01T00:00:00.000Z",
  "isActive": true,
  "standardAllowances": [
    {
      "name": "Housing Allowance",
      "percentage": 30,
      "isTaxable": true
    }
  ],
  "standardDeductions": [
    {
      "name": "PAYE",
      "percentage": 30
    }
  ]
}`}
                  </pre>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Bulk Import Response</h5>
                  <pre className="bg-gray-900 text-gray-100 p-3 rounded-lg text-xs overflow-x-auto">
{`{
  "success": true,
  "message": "Bulk import completed. 10 salary bands imported successfully, 2 failed.",
  "data": {
    "processed": 12,
    "successful": 10,
    "failed": 2,
    "results": [
      {
        "row": 1,
        "tcmCode": "TCM 1",
        "name": "Registrar",
        "status": "success",
        "message": "Salary band created successfully"
      },
      {
        "row": 11,
        "tcmCode": "TCM 1",
        "name": "Duplicate",
        "status": "error",
        "message": "TCM code already exists"
      }
    ]
  }
}`}
                  </pre>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Database Schema */}
      <section id="database-schema">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Database Schema
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">SalaryBand Model</h4>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                <pre>
{`interface ISalaryBand extends Document {
  tcmCode: string;              // TCM 1, TCM 2, etc.
  name: string;                 // Executive, Director, Manager, etc.
  description?: string;         // Description of the band
  minSalary: number;           // Minimum salary for this band
  maxSalary: number;           // Maximum salary for this band
  currency: string;            // Currency (default: MWK)

  // Standard components for this band
  standardAllowances: {
    name: string;
    amount?: number;
    percentage?: number;
    isTaxable: boolean;
  }[];

  standardDeductions: {
    name: string;
    amount?: number;
    percentage?: number;
  }[];

  // Progression settings
  stepIncrement?: number;       // Amount for step progression
  maxSteps?: number;           // Maximum steps in this band
  annualIncrementPercentage?: number; // Annual increment percentage

  // Effective dates
  effectiveDate: Date;
  expiryDate?: Date;
  isActive: boolean;

  // Approval and audit
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;

  // Metadata
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}`}
                </pre>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Schema Validation Rules</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Field Constraints</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <code>tcmCode</code>: Unique, format "TCM X"</li>
                    <li>• <code>name</code>: Required, trimmed</li>
                    <li>• <code>minSalary</code>: Required, positive number</li>
                    <li>• <code>maxSalary</code>: Required, ≥ minSalary</li>
                    <li>• <code>currency</code>: Default "MWK"</li>
                    <li>• <code>effectiveDate</code>: Required</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Business Rules</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Expiry date must be after effective date</li>
                    <li>• TCM code format validation</li>
                    <li>• Allowance/deduction structure validation</li>
                    <li>• Step increment and max steps relationship</li>
                    <li>• Annual increment percentage (0-100%)</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Database Indexes</h4>
              <div className="bg-gray-50 border rounded-lg p-4">
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Primary Indexes</h5>
                    <ul className="space-y-1">
                      <li>• <code>tcmCode</code> (unique)</li>
                      <li>• <code>isActive</code></li>
                      <li>• <code>effectiveDate</code> (desc)</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Compound Indexes</h5>
                    <ul className="space-y-1">
                      <li>• <code>isActive + effectiveDate</code></li>
                      <li>• <code>createdBy + createdAt</code></li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Text Indexes</h5>
                    <ul className="space-y-1">
                      <li>• <code>name</code> (text search)</li>
                      <li>• <code>description</code> (text search)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Security */}
      <section id="security">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Security Implementation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Role-Based Access Control</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">Authorized Roles</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <Badge variant="outline">SUPER_ADMIN</Badge> - Full access</li>
                    <li>• <Badge variant="outline">SYSTEM_ADMIN</Badge> - Full access</li>
                    <li>• <Badge variant="outline">HR_DIRECTOR</Badge> - Full access</li>
                    <li>• <Badge variant="outline">HR_MANAGER</Badge> - Full access</li>
                    <li>• <Badge variant="outline">PAYROLL_MANAGER</Badge> - Full access</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-red-600">Restricted Access</h5>
                  <ul className="text-sm space-y-1">
                    <li>• All other roles have no access</li>
                    <li>• API returns 403 Forbidden</li>
                    <li>• UI components hidden from navigation</li>
                    <li>• Bulk operations require elevated permissions</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Data Validation & Sanitization</h4>
              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">Input Validation</h5>
                  <div className="bg-gray-50 border rounded-lg p-3">
                    <ul className="text-sm space-y-1">
                      <li>• <strong>TCM Code:</strong> Regex validation for "TCM X" format</li>
                      <li>• <strong>Salary Amounts:</strong> Positive number validation</li>
                      <li>• <strong>Dates:</strong> Valid date format and logical constraints</li>
                      <li>• <strong>File Uploads:</strong> Type, size, and content validation</li>
                      <li>• <strong>Percentages:</strong> Range validation (0-100%)</li>
                    </ul>
                  </div>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Business Logic Validation</h5>
                  <div className="bg-gray-50 border rounded-lg p-3">
                    <ul className="text-sm space-y-1">
                      <li>• Maximum salary ≥ minimum salary</li>
                      <li>• Expiry date &gt; effective date (if provided)</li>
                      <li>• TCM code uniqueness across active bands</li>
                      <li>• Allowance/deduction structure integrity</li>
                      <li>• Step progression logical constraints</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Audit Logging</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Logged Events</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Salary band creation/modification</li>
                    <li>• Bulk import operations</li>
                    <li>• Template downloads</li>
                    <li>• Permission violations</li>
                    <li>• Data validation failures</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Log Information</h5>
                  <ul className="text-sm space-y-1">
                    <li>• User ID and role</li>
                    <li>• Timestamp and action</li>
                    <li>• Affected salary band IDs</li>
                    <li>• Request metadata</li>
                    <li>• Error details (if applicable)</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Performance & Optimization */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="h-5 w-5 mr-2" />
              Performance & Optimization
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Database Optimization</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Query Optimization</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Indexed queries for TCM code lookups</li>
                    <li>• Compound indexes for filtered searches</li>
                    <li>• Pagination for large datasets</li>
                    <li>• Projection to limit returned fields</li>
                    <li>• Aggregation pipelines for statistics</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Caching Strategy</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Active salary bands cached in memory</li>
                    <li>• Template generation cached</li>
                    <li>• User permissions cached per session</li>
                    <li>• Database connection pooling</li>
                    <li>• CDN for static assets</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">File Processing Optimization</h4>
              <div className="space-y-3">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Excel Processing</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Streaming Excel reader for large files</li>
                    <li>• Batch processing for bulk operations</li>
                    <li>• Memory-efficient data structures</li>
                    <li>• Progress tracking for user feedback</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Error Handling</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Graceful degradation for partial failures</li>
                    <li>• Detailed error reporting per row</li>
                    <li>• Transaction rollback for critical errors</li>
                    <li>• Retry mechanisms for transient failures</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Deployment & Configuration */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Deployment & Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Environment Variables</h4>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm">
                <pre>
{`# Database Configuration
MONGODB_URI=mongodb+srv://...
DATABASE_NAME=tcm_enterprise

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=7d

# File Upload Limits
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=.xlsx,.xls

# Logging
LOG_LEVEL=info
LOG_CATEGORY=PAYROLL

# Feature Flags
ENABLE_BULK_IMPORT=true
ENABLE_TEMPLATE_GENERATION=true`}
                </pre>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Deployment Checklist</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Pre-Deployment</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Database migrations completed</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Environment variables configured</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> API endpoints tested</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Bulk import functionality verified</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Permission system validated</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Post-Deployment</h5>
                  <ul className="text-sm space-y-1">
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Health checks passing</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Database connectivity verified</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> File upload limits working</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Template generation functional</li>
                    <li>• <CheckCircle className="h-3 w-3 inline mr-1" /> Monitoring and logging active</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Troubleshooting */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Terminal className="h-5 w-5 mr-2" />
              Troubleshooting Guide
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Common Issues</h4>
              <div className="space-y-4">
                <div className="border-l-4 border-red-500 pl-4">
                  <h5 className="font-medium text-red-700">Database Connection Errors</h5>
                  <p className="text-sm text-muted-foreground mb-2">
                    <strong>Symptoms:</strong> API returns 500 errors, "Cannot connect to database"
                  </p>
                  <div className="text-sm">
                    <strong>Solutions:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Verify MONGODB_URI environment variable</li>
                      <li>Check database server status and connectivity</li>
                      <li>Validate database credentials and permissions</li>
                      <li>Review connection pool settings</li>
                    </ul>
                  </div>
                </div>

                <div className="border-l-4 border-yellow-500 pl-4">
                  <h5 className="font-medium text-yellow-700">File Upload Failures</h5>
                  <p className="text-sm text-muted-foreground mb-2">
                    <strong>Symptoms:</strong> Bulk import fails, "File too large" or "Invalid file type"
                  </p>
                  <div className="text-sm">
                    <strong>Solutions:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Check MAX_FILE_SIZE configuration</li>
                      <li>Verify ALLOWED_FILE_TYPES settings</li>
                      <li>Review Vercel function size limits</li>
                      <li>Implement file streaming for large uploads</li>
                    </ul>
                  </div>
                </div>

                <div className="border-l-4 border-blue-500 pl-4">
                  <h5 className="font-medium text-blue-700">Permission Denied Errors</h5>
                  <p className="text-sm text-muted-foreground mb-2">
                    <strong>Symptoms:</strong> 403 Forbidden responses, features not visible
                  </p>
                  <div className="text-sm">
                    <strong>Solutions:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Verify user role assignments in database</li>
                      <li>Check JWT token validity and claims</li>
                      <li>Review hasRequiredPermissions function</li>
                      <li>Validate role constants and mappings</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Debugging Tools</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Development</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Next.js development server logs</li>
                    <li>• Browser developer tools</li>
                    <li>• MongoDB Compass for database inspection</li>
                    <li>• Postman for API testing</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Production</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Vercel function logs</li>
                    <li>• MongoDB Atlas monitoring</li>
                    <li>• Application performance monitoring</li>
                    <li>• Error tracking and alerting</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Integration Points */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GitBranch className="h-5 w-5 mr-2" />
              Integration Points
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            <div>
              <h4 className="font-semibold mb-3">System Integrations</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Employee Management</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Automatic salary band assignment</li>
                    <li>• TCM code validation during employee creation</li>
                    <li>• Salary calculation based on band ranges</li>
                    <li>• Career progression tracking</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Payroll Processing</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Standard allowances application</li>
                    <li>• Standard deductions calculation</li>
                    <li>• Step increment processing</li>
                    <li>• Annual increment calculations</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3">API Integration Examples</h4>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                <pre>
{`// Get salary band for employee assignment
const getSalaryBandByTCM = async (tcmCode: string) => {
  const response = await fetch(\`/api/payroll/salary-bands?tcmCode=\${tcmCode}\`);
  return response.json();
};

// Calculate salary with band constraints
const validateSalaryInBand = (salary: number, bandId: string) => {
  // Implementation to check if salary falls within band range
};

// Apply standard allowances from band
const applyStandardAllowances = (baseSalary: number, bandId: string) => {
  // Implementation to calculate allowances based on band settings
};`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}

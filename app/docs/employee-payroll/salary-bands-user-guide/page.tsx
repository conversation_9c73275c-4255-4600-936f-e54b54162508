"use client"

import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Building,
  DollarSign,
  Users,
  Download,
  Upload,
  CheckCircle,
  AlertTriangle,
  FileText,
  Percent,
  TrendingUp,
  Calendar,
  Eye,
  Edit,
  Trash,
  Plus
} from "lucide-react"

export default function SalaryBandsUserGuide() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Building className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Salary Bands User Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Complete guide for managing TCM salary bands, compensation structures, and bulk import operations.
        </p>
      </div>

      {/* Quick Navigation */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Navigation</CardTitle>
          <CardDescription>Jump to specific sections</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start" asChild>
              <a href="#overview">
                <Eye className="h-4 w-4 mr-2" />
                Overview
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#managing-bands">
                <Edit className="h-4 w-4 mr-2" />
                Managing Bands
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#bulk-import">
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#troubleshooting">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Troubleshooting
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Overview Section */}
      <section id="overview">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              What are Salary Bands?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Salary bands are predefined compensation structures that define salary ranges, allowances,
              and deductions for different job levels within the Teachers Council of Malawi (TCM).
              Each band corresponds to a specific TCM code (TCM 1 through TCM 12) and includes:
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Salary Information
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Minimum and maximum salary ranges</li>
                  <li>• Step increments for progression</li>
                  <li>• Annual increment percentages</li>
                  <li>• Currency specifications</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Percent className="h-4 w-4 mr-2" />
                  Compensation Components
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Standard allowances (housing, transport, etc.)</li>
                  <li>• Standard deductions (PAYE, pension, etc.)</li>
                  <li>• Taxable/non-taxable designations</li>
                  <li>• Fixed amounts or percentage-based</li>
                </ul>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">TCM Hierarchy Overview</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                <div><Badge variant="outline">TCM 1</Badge> Registrar</div>
                <div><Badge variant="outline">TCM 2</Badge> Director</div>
                <div><Badge variant="outline">TCM 3</Badge> Deputy Director</div>
                <div><Badge variant="outline">TCM 4</Badge> Assistant Director</div>
                <div><Badge variant="outline">TCM 5</Badge> Principal Officer</div>
                <div><Badge variant="outline">TCM 6</Badge> Senior Officer</div>
                <div><Badge variant="outline">TCM 7</Badge> Officer</div>
                <div><Badge variant="outline">TCM 8</Badge> Assistant Officer</div>
                <div><Badge variant="outline">TCM 9</Badge> Senior Clerk</div>
                <div><Badge variant="outline">TCM 10</Badge> Clerk</div>
                <div><Badge variant="outline">TCM 11</Badge> Assistant Clerk</div>
                <div><Badge variant="outline">TCM 12</Badge> Office Assistant</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Accessing Salary Bands */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Accessing Salary Bands
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Required Permissions
              </h4>
              <p className="text-sm text-yellow-700">
                Only users with HR Director, HR Manager, Payroll Manager, Super Admin, or System Admin roles
                can access salary band management features.
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold">Navigation Steps:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Log in to the TCM Enterprise Suite</li>
                <li>Navigate to <strong>Payroll</strong> in the main sidebar</li>
                <li>Click on <strong>Salary Bands</strong> in the payroll submenu</li>
                <li>You'll see the salary bands management interface</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Managing Salary Bands */}
      <section id="managing-bands">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Edit className="h-5 w-5 mr-2" />
              Managing Salary Bands
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            {/* Creating a New Salary Band */}
            <div>
              <h4 className="font-semibold mb-3">Creating a New Salary Band</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Click the <strong>"New Band"</strong> dropdown button</li>
                <li>Select <strong>"Create Custom Band"</strong> for individual creation</li>
                <li>Or select <strong>"Create Default TCM Bands"</strong> for all 12 standard bands</li>
                <li>Fill in the required information across four tabs:</li>
              </ol>

              <div className="mt-4 grid md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Basic Info Tab</h5>
                  <ul className="text-xs space-y-1">
                    <li>• TCM Code (required)</li>
                    <li>• Band Name (required)</li>
                    <li>• Description (optional)</li>
                    <li>• Effective and expiry dates</li>
                    <li>• Active status</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Salary Range Tab</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Minimum salary (required)</li>
                    <li>• Maximum salary (required)</li>
                    <li>• Currency selection</li>
                    <li>• Step increment amount</li>
                    <li>• Maximum steps and annual increment %</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Allowances Tab</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Add up to multiple allowances</li>
                    <li>• Set fixed amounts or percentages</li>
                    <li>• Mark as taxable/non-taxable</li>
                    <li>• Common: Housing, Transport, Communication</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Deductions Tab</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Add multiple deductions</li>
                    <li>• Set fixed amounts or percentages</li>
                    <li>• Common: PAYE, Pension, Professional fees</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            {/* Viewing and Editing */}
            <div>
              <h4 className="font-semibold mb-3">Viewing and Editing Salary Bands</h4>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Eye className="h-4 w-4 mt-1 text-blue-600" />
                  <div>
                    <p className="font-medium">View Details</p>
                    <p className="text-sm text-muted-foreground">
                      Click the three-dot menu next to any salary band and select "View Details"
                      to see comprehensive information including compensation breakdown and metadata.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Edit className="h-4 w-4 mt-1 text-green-600" />
                  <div>
                    <p className="font-medium">Edit Salary Band</p>
                    <p className="text-sm text-muted-foreground">
                      Use the "Edit" option from the dropdown menu to modify salary ranges,
                      allowances, deductions, or other band settings.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Trash className="h-4 w-4 mt-1 text-red-600" />
                  <div>
                    <p className="font-medium">Delete Salary Band</p>
                    <p className="text-sm text-muted-foreground">
                      Delete individual bands or use bulk selection to delete multiple bands.
                      <strong>Warning:</strong> This may affect employees currently assigned to these bands.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Filtering and Search */}
            <div>
              <h4 className="font-semibold mb-3">Filtering and Search</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Search Options</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Search by TCM code (e.g., "TCM 1")</li>
                    <li>• Search by band name (e.g., "Director")</li>
                    <li>• Search by description keywords</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">Filter Options</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Show/hide inactive bands</li>
                    <li>• View summary statistics</li>
                    <li>• Bulk selection for operations</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Bulk Import Section */}
      <section id="bulk-import">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="h-5 w-5 mr-2" />
              Bulk Import Salary Bands
            </CardTitle>
            <CardDescription>
              Import multiple salary bands at once using Excel files
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                When to Use Bulk Import
              </h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Setting up all 12 TCM salary bands initially</li>
                <li>• Annual salary band updates across the organization</li>
                <li>• Importing salary bands from external systems</li>
                <li>• Making bulk changes to multiple bands simultaneously</li>
              </ul>
            </div>

            {/* Step-by-Step Process */}
            <div>
              <h4 className="font-semibold mb-4">Step-by-Step Bulk Import Process</h4>

              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
                  <div className="flex-1">
                    <h5 className="font-medium">Access Bulk Import</h5>
                    <p className="text-sm text-muted-foreground">
                      Click the "New Band" dropdown and select "Bulk Import" to open the import dialog.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
                  <div className="flex-1">
                    <h5 className="font-medium">Download Template</h5>
                    <p className="text-sm text-muted-foreground">
                      Click "Download Template" to get the Excel file with proper formatting,
                      sample data, and detailed instructions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
                  <div className="flex-1">
                    <h5 className="font-medium">Fill in Your Data</h5>
                    <p className="text-sm text-muted-foreground">
                      Complete the Excel template with your salary band information.
                      Review the Instructions, Validation Rules, and Examples sheets.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</div>
                  <div className="flex-1">
                    <h5 className="font-medium">Upload and Process</h5>
                    <p className="text-sm text-muted-foreground">
                      Select your completed file and click "Upload & Process".
                      Monitor the progress and review detailed results.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Template Guide */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Excel Template Guide
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              The salary band template includes multiple sheets to help you import data correctly:
            </p>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-3">
                <h5 className="font-medium mb-2 text-blue-600">Salary Bands Data Sheet</h5>
                <p className="text-sm text-muted-foreground">
                  Main sheet where you enter your salary band data. Includes sample TCM bands
                  with realistic salary ranges and compensation structures.
                </p>
              </div>

              <div className="border rounded-lg p-3">
                <h5 className="font-medium mb-2 text-green-600">Instructions Sheet</h5>
                <p className="text-sm text-muted-foreground">
                  Comprehensive guide covering required fields, optional fields, data formats,
                  and the complete TCM hierarchy with role descriptions.
                </p>
              </div>

              <div className="border rounded-lg p-3">
                <h5 className="font-medium mb-2 text-orange-600">Validation Rules Sheet</h5>
                <p className="text-sm text-muted-foreground">
                  Field-by-field validation requirements including data types, formats,
                  and business rules for each column in the template.
                </p>
              </div>

              <div className="border rounded-lg p-3">
                <h5 className="font-medium mb-2 text-purple-600">Examples Sheet</h5>
                <p className="text-sm text-muted-foreground">
                  Different scenarios and calculation examples showing how allowances,
                  deductions, and salary progressions work in practice.
                </p>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">Required Fields</h4>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <ul className="space-y-1">
                    <li>• <strong>TCM Code:</strong> Format "TCM X" (e.g., TCM 1)</li>
                    <li>• <strong>Band Name:</strong> Descriptive name</li>
                    <li>• <strong>Minimum Salary:</strong> Numeric value</li>
                    <li>• <strong>Maximum Salary:</strong> Must be ≥ minimum</li>
                  </ul>
                </div>
                <div>
                  <ul className="space-y-1">
                    <li>• <strong>Effective Date:</strong> YYYY-MM-DD format</li>
                    <li>• <strong>Currency:</strong> Defaults to MWK</li>
                    <li>• <strong>Is Active:</strong> TRUE/FALSE</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Troubleshooting Section */}
      <section id="troubleshooting">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Troubleshooting Common Issues
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div className="space-y-4">
              <div className="border-l-4 border-red-500 pl-4">
                <h5 className="font-medium text-red-700">Error: "TCM code already exists"</h5>
                <p className="text-sm text-muted-foreground">
                  <strong>Cause:</strong> You're trying to create a salary band with a TCM code that already exists.
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Solution:</strong> Check existing salary bands or use a different TCM code.
                  Each TCM code must be unique across all active salary bands.
                </p>
              </div>

              <div className="border-l-4 border-yellow-500 pl-4">
                <h5 className="font-medium text-yellow-700">Error: "Maximum salary must be greater than minimum salary"</h5>
                <p className="text-sm text-muted-foreground">
                  <strong>Cause:</strong> The maximum salary value is less than or equal to the minimum salary.
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Solution:</strong> Ensure the maximum salary is always greater than the minimum salary.
                  Review your salary range data for accuracy.
                </p>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <h5 className="font-medium text-blue-700">Error: "Invalid TCM code format"</h5>
                <p className="text-sm text-muted-foreground">
                  <strong>Cause:</strong> TCM code doesn't follow the required "TCM X" format.
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Solution:</strong> Use the exact format "TCM 1", "TCM 2", etc.
                  Include the space between "TCM" and the number.
                </p>
              </div>

              <div className="border-l-4 border-purple-500 pl-4">
                <h5 className="font-medium text-purple-700">Issue: "Bulk import partially failed"</h5>
                <p className="text-sm text-muted-foreground">
                  <strong>Cause:</strong> Some rows in your Excel file have validation errors.
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Solution:</strong> Review the detailed results table to see which rows failed and why.
                  Fix the errors in your Excel file and re-import only the failed rows.
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Data Validation Tips</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Before Import</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Double-check all TCM codes for uniqueness</li>
                    <li>• Verify salary ranges are logical</li>
                    <li>• Ensure date formats are YYYY-MM-DD</li>
                    <li>• Check that required fields are filled</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">After Import</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Review the import results carefully</li>
                    <li>• Verify salary bands appear correctly</li>
                    <li>• Test with a small batch first</li>
                    <li>• Keep backup of original data</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Best Practices */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Salary Band Management
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Review and update salary bands annually</li>
                  <li>• Maintain consistent progression between TCM levels</li>
                  <li>• Document rationale for salary range decisions</li>
                  <li>• Consider market rates and internal equity</li>
                  <li>• Set realistic step increments for career progression</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Timing and Planning
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Plan salary band updates during budget cycles</li>
                  <li>• Use effective dates to manage transitions</li>
                  <li>• Communicate changes to affected employees</li>
                  <li>• Coordinate with HR and Finance departments</li>
                  <li>• Test changes in a staging environment first</li>
                </ul>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Data Quality Guidelines</h4>
              <div className="bg-gray-50 border rounded-lg p-4">
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Accuracy</h5>
                    <ul className="space-y-1">
                      <li>• Verify all salary amounts</li>
                      <li>• Check allowance calculations</li>
                      <li>• Validate deduction percentages</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Consistency</h5>
                    <ul className="space-y-1">
                      <li>• Use standard naming conventions</li>
                      <li>• Apply uniform date formats</li>
                      <li>• Maintain logical progression</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Completeness</h5>
                    <ul className="space-y-1">
                      <li>• Fill all required fields</li>
                      <li>• Include relevant descriptions</li>
                      <li>• Set appropriate effective dates</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Support and Resources */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle>Support and Resources</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Getting Help</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Contact your system administrator for technical issues</li>
                  <li>• Reach out to HR department for policy questions</li>
                  <li>• Consult Finance team for salary range guidance</li>
                  <li>• Use the built-in help tooltips and validation messages</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Additional Resources</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <a href="/docs/employee-payroll/technical-guide" className="text-blue-600 hover:underline">Technical Guide</a> for advanced features</li>
                  <li>• <a href="/docs/bulk-import" className="text-blue-600 hover:underline">Bulk Import Guide</a> for general import procedures</li>
                  <li>• <a href="/docs/employee-payroll" className="text-blue-600 hover:underline">Payroll Documentation</a> for related features</li>
                  <li>• Template files with sample data and instructions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}

"use client"

import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Download,
  FileText,
  Users,
  CreditCard,
  Building,
  Calendar,
  Filter,
  CheckCircle,
  AlertTriangle,
  Eye,
  Settings,
  Zap,
  BarChart3
} from "lucide-react"

export default function ExportUserGuide() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Download className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Export Operations User Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Complete guide for exporting payroll data, generating reports, and creating bank transfer files.
        </p>
      </div>

      {/* Quick Navigation */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Navigation</CardTitle>
          <CardDescription>Jump to specific export types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start" asChild>
              <a href="#payslips-export">
                <FileText className="h-4 w-4 mr-2" />
                Payslips Export
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#reports-export">
                <BarChart3 className="h-4 w-4 mr-2" />
                Reports Export
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#bank-transfers">
                <Building className="h-4 w-4 mr-2" />
                Bank Transfers
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="#troubleshooting">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Troubleshooting
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Overview Section */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Export Operations Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              The TCM Enterprise Suite provides comprehensive export capabilities for payroll data,
              enabling you to generate reports, distribute payslips, and create bank transfer files
              for automated payments.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Available Export Types
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Bulk Payslips:</strong> Export multiple payslips in Excel, PDF, or ZIP formats</li>
                  <li>• <strong>Payroll Reports:</strong> Generate comprehensive payroll analysis reports</li>
                  <li>• <strong>Bank Transfer Files:</strong> Create files for automated salary payments</li>
                  <li>• <strong>Employee Data:</strong> Export employee information (coming soon)</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Settings className="h-4 w-4 mr-2" />
                  Export Formats
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Excel:</strong> Comprehensive workbooks with multiple sheets</li>
                  <li>• <strong>PDF:</strong> Professional documents ready for printing</li>
                  <li>• <strong>CSV:</strong> Data files for external system integration</li>
                  <li>• <strong>ZIP:</strong> Archives with individual files</li>
                </ul>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                <Eye className="h-4 w-4 mr-2" />
                Required Permissions
              </h4>
              <p className="text-sm text-blue-700">
                Export operations require elevated permissions. Only users with Finance Director,
                Finance Manager, Payroll Specialist, HR Director, HR Manager, Super Admin, or
                System Admin roles can access export functionality.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Accessing Export Manager */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Accessing Export Manager
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <h4 className="font-semibold">Navigation Steps:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Log in to the TCM Enterprise Suite</li>
                <li>Navigate to <strong>Payroll</strong> in the main sidebar</li>
                <li>Click on <strong>Export Data</strong> in the payroll submenu</li>
                <li>You'll see the Export Manager interface with different export types</li>
              </ol>
            </div>

            <div className="bg-gray-50 border rounded-lg p-4">
              <h4 className="font-semibold mb-2">Export Manager Interface</h4>
              <p className="text-sm text-muted-foreground">
                The Export Manager provides a tabbed interface where you can switch between different
                export types. Each tab contains specific configuration options and export formats
                relevant to that data type.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Bulk Payslips Export */}
      <section id="payslips-export">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Bulk Payslips Export
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Export Formats Available</h4>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2 text-green-600">Excel Workbook</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Summary sheet with overview</li>
                    <li>• Detailed payslips sheet</li>
                    <li>• Professional formatting</li>
                    <li>• Easy to analyze and share</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2 text-red-600">PDF Document</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Single PDF with all payslips</li>
                    <li>• Print-ready format</li>
                    <li>• Professional layout</li>
                    <li>• Suitable for distribution</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2 text-blue-600">ZIP Archive</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Individual PDF per employee</li>
                    <li>• Organized file structure</li>
                    <li>• Easy individual distribution</li>
                    <li>• Maintains confidentiality</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Filtering Options</h4>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Calendar className="h-4 w-4 mt-1 text-blue-600" />
                  <div>
                    <p className="font-medium">Payroll Run Selection</p>
                    <p className="text-sm text-muted-foreground">
                      Choose a specific payroll run or use date range filtering to export
                      payslips from multiple runs within a period.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Building className="h-4 w-4 mt-1 text-green-600" />
                  <div>
                    <p className="font-medium">Department Filtering</p>
                    <p className="text-sm text-muted-foreground">
                      Filter payslips by department to export only specific organizational units.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Users className="h-4 w-4 mt-1 text-purple-600" />
                  <div>
                    <p className="font-medium">Employee Selection</p>
                    <p className="text-sm text-muted-foreground">
                      Select specific employees or use "Select All" to include all employees
                      matching your filter criteria.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Step-by-Step Process</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Select your preferred export format (Excel, PDF, or ZIP)</li>
                <li>Choose filtering options (payroll run, date range, department)</li>
                <li>Select specific employees or use "Select All"</li>
                <li>Click "Export Payslips" to start the process</li>
                <li>Monitor the progress in the Recent Exports panel</li>
                <li>Download the file when the export completes</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Reports Export */}
      <section id="reports-export">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Payroll Reports Export
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div>
              <h4 className="font-semibold mb-3">Available Report Types</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Payroll Summary</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Total employees and payslips</li>
                    <li>• Gross and net salary totals</li>
                    <li>• Allowances and deductions summary</li>
                    <li>• Tax calculations overview</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Tax Summary</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Tax calculations by department</li>
                    <li>• PAYE breakdown and totals</li>
                    <li>• Tax bracket analysis</li>
                    <li>• Average tax rates</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Department Summary</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Payroll costs by department</li>
                    <li>• Employee count per department</li>
                    <li>• Average salaries by department</li>
                    <li>• Budget analysis</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-3">
                  <h5 className="font-medium mb-2">Employee Summary</h5>
                  <ul className="text-xs space-y-1">
                    <li>• Individual employee totals</li>
                    <li>• Salary progression tracking</li>
                    <li>• Allowances and deductions breakdown</li>
                    <li>• Year-to-date calculations</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Report Configuration</h4>
              <div className="space-y-3">
                <div>
                  <p className="font-medium">Required Settings:</p>
                  <ul className="text-sm space-y-1 mt-2">
                    <li>• <strong>Report Type:</strong> Choose from the four available report types</li>
                    <li>• <strong>Export Format:</strong> Excel, PDF, or CSV</li>
                    <li>• <strong>Date Range:</strong> Start and end dates for the report period</li>
                  </ul>
                </div>

                <div>
                  <p className="font-medium">Optional Filters:</p>
                  <ul className="text-sm space-y-1 mt-2">
                    <li>• <strong>Department:</strong> Filter by specific department</li>
                    <li>• <strong>Payroll Run:</strong> Focus on a specific payroll run</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Bank Transfer Files */}
      <section id="bank-transfers">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Bank Transfer Files
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Coming Soon
              </h4>
              <p className="text-sm text-yellow-700">
                Bank transfer file generation is currently under development. This feature will support
                multiple Malawian bank formats including NBS Malawi, FMB Bank, and Standard Bank.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Planned Features</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Supported Banks</h5>
                  <ul className="text-sm space-y-1">
                    <li>• NBS Malawi format</li>
                    <li>• FMB Bank format</li>
                    <li>• Standard Bank format</li>
                    <li>• MT940 international format</li>
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium mb-2">File Formats</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Excel workbooks</li>
                    <li>• CSV files</li>
                    <li>• TXT files</li>
                    <li>• Bank-specific formats</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Troubleshooting */}
      <section id="troubleshooting">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Troubleshooting Common Issues
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">

            <div className="space-y-4">
              <div className="border-l-4 border-red-500 pl-4">
                <h5 className="font-medium text-red-700">Export fails with "No data found"</h5>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Cause:</strong> No payslips or data match your filter criteria.
                </p>
                <div className="text-sm">
                  <strong>Solutions:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Check if the selected payroll run has processed payslips</li>
                    <li>Verify date range includes periods with payroll data</li>
                    <li>Ensure selected department has employees with payslips</li>
                    <li>Try removing filters to see if data exists</li>
                  </ul>
                </div>
              </div>

              <div className="border-l-4 border-yellow-500 pl-4">
                <h5 className="font-medium text-yellow-700">Export takes too long or times out</h5>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Cause:</strong> Large dataset or server processing limitations.
                </p>
                <div className="text-sm">
                  <strong>Solutions:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Reduce the date range to smaller periods</li>
                    <li>Filter by department to reduce data volume</li>
                    <li>Select fewer employees for the export</li>
                    <li>Try during off-peak hours for better performance</li>
                  </ul>
                </div>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <h5 className="font-medium text-blue-700">Downloaded file is corrupted or won't open</h5>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Cause:</strong> Download interruption or browser issues.
                </p>
                <div className="text-sm">
                  <strong>Solutions:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Try downloading again with a stable internet connection</li>
                    <li>Clear browser cache and cookies</li>
                    <li>Use a different browser or incognito mode</li>
                    <li>Check if you have the required software to open the file</li>
                  </ul>
                </div>
              </div>

              <div className="border-l-4 border-purple-500 pl-4">
                <h5 className="font-medium text-purple-700">Permission denied error</h5>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Cause:</strong> Insufficient user permissions for export operations.
                </p>
                <div className="text-sm">
                  <strong>Solutions:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Contact your system administrator to verify your role</li>
                    <li>Ensure you have Finance, HR, or Admin permissions</li>
                    <li>Log out and log back in to refresh permissions</li>
                    <li>Check if your account has been recently updated</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Best Practices */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  Export Planning
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Plan exports during off-peak hours for better performance</li>
                  <li>• Use specific date ranges rather than very broad periods</li>
                  <li>• Filter by department for focused analysis</li>
                  <li>• Test with small datasets before large exports</li>
                  <li>• Keep export criteria documented for recurring reports</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  File Management
                </h4>
                <ul className="space-y-2 text-sm">
                  <li>• Use descriptive names when saving exported files</li>
                  <li>• Organize exports by date and type</li>
                  <li>• Keep backup copies of important reports</li>
                  <li>• Verify file integrity before sharing</li>
                  <li>• Follow data protection policies for sensitive information</li>
                </ul>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-3">Format Selection Guidelines</h4>
              <div className="bg-gray-50 border rounded-lg p-4">
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Use Excel When:</h5>
                    <ul className="space-y-1">
                      <li>• You need to analyze data</li>
                      <li>• Creating charts and graphs</li>
                      <li>• Sharing with stakeholders</li>
                      <li>• Performing calculations</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Use PDF When:</h5>
                    <ul className="space-y-1">
                      <li>• Printing documents</li>
                      <li>• Official record keeping</li>
                      <li>• Sharing final reports</li>
                      <li>• Maintaining formatting</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium mb-2">Use ZIP When:</h5>
                    <ul className="space-y-1">
                      <li>• Individual payslip distribution</li>
                      <li>• Large number of employees</li>
                      <li>• Maintaining confidentiality</li>
                      <li>• Email distribution</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Support and Resources */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle>Support and Resources</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Getting Help</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Contact your system administrator for technical issues</li>
                  <li>• Reach out to Finance team for report interpretation</li>
                  <li>• Consult HR department for payroll data questions</li>
                  <li>• Use the export job history to track previous exports</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Additional Resources</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <a href="/docs/employee-payroll" className="text-blue-600 hover:underline">Payroll Documentation</a> for general payroll features</li>
                  <li>• <a href="/docs/bulk-import" className="text-blue-600 hover:underline">Bulk Import Guide</a> for data import procedures</li>
                  <li>• <a href="/docs/employee-payroll/salary-bands-user-guide" className="text-blue-600 hover:underline">Salary Bands Guide</a> for compensation structures</li>
                  <li>• Export templates and sample files for reference</li>
                </ul>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">Export Status Monitoring</h4>
              <p className="text-sm text-green-700">
                Use the "Recent Exports" panel on the right side of the Export Manager to monitor
                your export jobs. You can see the status, download completed exports, and track
                any errors that may occur during the export process.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}

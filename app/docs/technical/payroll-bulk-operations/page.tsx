"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  Code, 
  Database, 
  Server, 
  Shield, 
  Zap,
  GitBranch,
  Settings,
  Monitor,
  AlertTriangle,
  CheckCircle,
  Info
} from "lucide-react"

export default function PayrollBulkOperationsTechnicalGuide() {
  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">Payroll Bulk Operations</h1>
          <p className="text-xl text-muted-foreground">
            Technical documentation for developers and system administrators
          </p>
          <div className="flex justify-center gap-2">
            <Badge variant="secondary">Technical Guide</Badge>
            <Badge variant="outline">API Documentation</Badge>
            <Badge variant="outline">Architecture</Badge>
          </div>
        </div>

        {/* Architecture Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              System Architecture
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              The Payroll Bulk Operations system is built on a microservices architecture with background processing, 
              real-time progress tracking, and comprehensive error handling.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg text-center">
                <Server className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-semibold">API Layer</h3>
                <p className="text-sm text-muted-foreground">RESTful endpoints with role-based access control</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <Database className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-semibold">Data Layer</h3>
                <p className="text-sm text-muted-foreground">MongoDB with batch processing models</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <Monitor className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-semibold">UI Layer</h3>
                <p className="text-sm text-muted-foreground">React components with real-time updates</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Documentation */}
        <Tabs defaultValue="endpoints" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
            <TabsTrigger value="models">Data Models</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="endpoints" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>API Endpoints</CardTitle>
                <CardDescription>Complete API reference for bulk operations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Bulk Process */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-blue-600">POST /api/payroll/runs/bulk-process</h3>
                  <p className="text-sm text-muted-foreground">Process multiple payroll runs in bulk</p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Request Body:</h4>
                    <pre className="text-xs overflow-x-auto">
{`{
  "payrollRunIds": ["id1", "id2", "id3"],
  "batchSize": 50,
  "useBatch": true,
  "notes": "Bulk processed on 2024-01-15",
  "departments": ["dept1", "dept2"] // optional
}`}
                    </pre>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Response:</h4>
                    <pre className="text-xs overflow-x-auto">
{`{
  "success": true,
  "message": "Bulk payroll processing started for 3 runs",
  "data": {
    "totalRuns": 3,
    "processedRuns": 0,
    "failedRuns": 0,
    "skippedRuns": 1,
    "batchId": "batch_id_here",
    "results": [...]
  }
}`}
                    </pre>
                  </div>
                </div>

                {/* Bulk Approve */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-green-600">POST /api/payroll/runs/bulk-approve</h3>
                  <p className="text-sm text-muted-foreground">Approve multiple completed payroll runs</p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Request Body:</h4>
                    <pre className="text-xs overflow-x-auto">
{`{
  "payrollRunIds": ["id1", "id2", "id3"],
  "notes": "Bulk approved on 2024-01-15",
  "departments": ["dept1", "dept2"] // optional
}`}
                    </pre>
                  </div>
                </div>

                {/* Bulk Pay */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-purple-600">POST /api/payroll/runs/bulk-pay</h3>
                  <p className="text-sm text-muted-foreground">Mark multiple approved payroll runs as paid</p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Request Body:</h4>
                    <pre className="text-xs overflow-x-auto">
{`{
  "payrollRunIds": ["id1", "id2", "id3"],
  "paymentMethod": "Bank Transfer",
  "paymentReference": "BULK-*************",
  "notes": "Bulk payment processed",
  "departments": ["dept1", "dept2"] // optional
}`}
                    </pre>
                  </div>
                </div>

                {/* GET Endpoints */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">GET Endpoints</h3>
                  <ul className="space-y-2 text-sm">
                    <li><code className="bg-gray-100 px-2 py-1 rounded">GET /api/payroll/runs/bulk-approve</code> - Get eligible runs for approval</li>
                    <li><code className="bg-gray-100 px-2 py-1 rounded">GET /api/payroll/runs/bulk-pay</code> - Get eligible runs for payment</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="models" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Data Models</CardTitle>
                <CardDescription>Database schemas and interfaces</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* PayrollRun Model */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">PayrollRun Model</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
{`interface IPayrollRun {
  _id: ObjectId
  name: string
  description?: string
  payPeriod: {
    month: number
    year: number
    startDate: Date
    endDate: Date
  }
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string
  departments?: ObjectId[]
  paymentMethod?: string
  paymentReference?: string
  createdBy: ObjectId
  approvedBy?: ObjectId
  approvedAt?: Date
  paidAt?: Date
  processedAt?: Date
  notes?: string
  createdAt: Date
  updatedAt: Date
}`}
                    </pre>
                  </div>
                </div>

                {/* PayrollProcessingBatch Model */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">PayrollProcessingBatch Model</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
{`interface IPayrollProcessingBatch {
  _id: ObjectId
  payrollRunId: ObjectId
  status: 'pending' | 'processing' | 'completed' | 'error'
  totalEmployees: number
  processedEmployees: number
  currentEmployee: string
  startedAt: Date
  completedAt?: Date
  error?: string
  createdBy: ObjectId
  updatedBy: ObjectId
  createdAt: Date
  updatedAt: Date
}`}
                    </pre>
                  </div>
                </div>

                {/* Request/Response Interfaces */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">TypeScript Interfaces</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
{`interface BulkOperationResult {
  success: boolean
  message: string
  data: {
    totalRuns: number
    processedRuns?: number
    approvedRuns?: number
    paidRuns?: number
    failedRuns: number
    skippedRuns: number
    totalAmount?: number
    batchId?: string
    results: Array<{
      payrollRunId: string
      payrollRunName: string
      status: string
      message?: string
      error?: string
      amount?: number
    }>
  }
}`}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Implementation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Authentication */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Authentication & Authorization</h3>
                  <div className="space-y-2">
                    <p className="text-sm">All endpoints require valid JWT authentication and role-based permissions:</p>
                    <ul className="text-sm space-y-1 ml-4">
                      <li>• <strong>Bulk Processing:</strong> SUPER_ADMIN, SYSTEM_ADMIN, FINANCE_DIRECTOR, HR_DIRECTOR, FINANCE_MANAGER</li>
                      <li>• <strong>Bulk Approval:</strong> SUPER_ADMIN, SYSTEM_ADMIN, FINANCE_DIRECTOR, HR_DIRECTOR</li>
                      <li>• <strong>Bulk Payment:</strong> SUPER_ADMIN, SYSTEM_ADMIN, FINANCE_DIRECTOR</li>
                    </ul>
                  </div>
                </div>

                {/* Input Validation */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Input Validation</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
{`// Validation checks performed:
- Valid MongoDB ObjectIds for payrollRunIds
- Non-empty array of payroll run IDs
- Status validation (draft → completed → approved → paid)
- Department filtering validation
- Batch size limits (max 100)
- Payment method validation
- Notes length limits`}
                    </pre>
                  </div>
                </div>

                {/* Audit Trail */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Audit Trail</h3>
                  <p className="text-sm">Comprehensive logging includes:</p>
                  <ul className="text-sm space-y-1 ml-4">
                    <li>• User ID and timestamp for all operations</li>
                    <li>• Detailed operation results and errors</li>
                    <li>• Status changes with reasons</li>
                    <li>• Performance metrics and processing times</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Performance & Scalability
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Background Processing */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Background Processing</h3>
                  <p className="text-sm">
                    Bulk operations use asynchronous background processing to handle large datasets without blocking the UI:
                  </p>
                  <ul className="text-sm space-y-1 ml-4">
                    <li>• Configurable batch sizes (default: 50 runs)</li>
                    <li>• Progress tracking with real-time updates</li>
                    <li>• Error isolation - failed runs don't stop the batch</li>
                    <li>• Automatic retry mechanisms for transient failures</li>
                  </ul>
                </div>

                {/* Database Optimization */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Database Optimization</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
{`// Optimized queries:
- Indexed fields: status, departments, createdAt
- Bulk operations use $in queries for efficiency
- Pagination for large result sets
- Selective field projection to reduce data transfer
- Connection pooling for concurrent operations`}
                    </pre>
                  </div>
                </div>

                {/* Monitoring */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Monitoring & Metrics</h3>
                  <p className="text-sm">Built-in monitoring includes:</p>
                  <ul className="text-sm space-y-1 ml-4">
                    <li>• Processing time metrics</li>
                    <li>• Success/failure rates</li>
                    <li>• Resource utilization tracking</li>
                    <li>• Error rate monitoring</li>
                    <li>• User activity analytics</li>
                  </ul>
                </div>

                {/* Scalability Considerations */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Scalability Considerations</h3>
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>Performance Guidelines</AlertTitle>
                    <AlertDescription>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Recommended batch size: 25-50 payroll runs</li>
                        <li>Maximum concurrent operations: 5 per user</li>
                        <li>Processing time: ~2-5 seconds per payroll run</li>
                        <li>Memory usage: ~10MB per 1000 employees</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Implementation Details */}
        <Card>
          <CardHeader>
            <CardTitle>Implementation Details</CardTitle>
            <CardDescription>Key technical implementation aspects</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* File Structure */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">File Structure</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-xs overflow-x-auto">
{`app/
├── api/payroll/runs/
│   ├── bulk-process/route.ts
│   ├── bulk-approve/route.ts
│   └── bulk-pay/route.ts
├── (dashboard)/dashboard/payroll/
│   └── bulk-operations/page.tsx
components/dashboard-sidebar.tsx (navigation)
lib/services/payroll/payroll-service.ts (enhanced)
models/payroll/
├── PayrollRun.ts
└── PayrollProcessingBatch.ts`}
                </pre>
              </div>
            </div>

            {/* Error Handling */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Error Handling Strategy</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold text-red-600">API Level</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Input validation errors</li>
                    <li>• Authentication failures</li>
                    <li>• Database connection issues</li>
                    <li>• Rate limiting</li>
                  </ul>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold text-yellow-600">Processing Level</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Individual run failures</li>
                    <li>• Status validation errors</li>
                    <li>• Calculation errors</li>
                    <li>• Timeout handling</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Testing Strategy */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Testing Strategy</h3>
              <p className="text-sm">Comprehensive testing approach:</p>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Unit tests for individual functions</li>
                <li>• Integration tests for API endpoints</li>
                <li>• Load testing for bulk operations</li>
                <li>• Security testing for authorization</li>
                <li>• End-to-end testing for user workflows</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Deployment & Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Deployment & Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Environment Variables */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Environment Configuration</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-xs overflow-x-auto">
{`# Required environment variables:
MONGODB_URI=mongodb://localhost:27017/tcm_suite
JWT_SECRET=your_jwt_secret_here
NEXT_PUBLIC_API_URL=http://localhost:3000

# Optional performance tuning:
BULK_OPERATION_BATCH_SIZE=50
BULK_OPERATION_TIMEOUT=300000
MAX_CONCURRENT_OPERATIONS=5`}
                </pre>
              </div>
            </div>

            {/* Database Indexes */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Required Database Indexes</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-xs overflow-x-auto">
{`// MongoDB indexes for optimal performance:
db.payrollruns.createIndex({ "status": 1 })
db.payrollruns.createIndex({ "departments": 1 })
db.payrollruns.createIndex({ "createdAt": -1 })
db.payrollruns.createIndex({ "payPeriod.year": 1, "payPeriod.month": 1 })
db.payrollprocessingbatches.createIndex({ "payrollRunId": 1 })
db.payrollprocessingbatches.createIndex({ "status": 1, "createdAt": -1 })`}
                </pre>
              </div>
            </div>

            {/* Monitoring Setup */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Monitoring Setup</h3>
              <Alert>
                <Monitor className="h-4 w-4" />
                <AlertTitle>Recommended Monitoring</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    <li>API response times and error rates</li>
                    <li>Database query performance</li>
                    <li>Background job completion rates</li>
                    <li>Memory and CPU usage during bulk operations</li>
                    <li>User activity and feature adoption</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>For additional technical support or questions about implementation details, contact the development team.</p>
        </div>
      </div>
    </div>
  )
}

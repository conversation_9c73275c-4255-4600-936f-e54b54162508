import { Metadata } from 'next';
import { DocSideNav } from '@/components/docs/doc-sidenav';
import { PaymentProcessingDoc } from '@/components/docs/content/accounting/payment-processing';

export const metadata: Metadata = {
  title: 'Payment Processing Documentation | TCM Enterprise Business Suite',
  description: 'Documentation for the Payment Processing System in the Banking & Treasury Management module',
};

export default function PaymentProcessingDocPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Payment Processing System
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Comprehensive guide to the Payment Processing System in the Banking & Treasury Management module
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <PaymentProcessingDoc
                backLink="/docs/accounting/banking"
                backLabel="Back to Banking & Treasury"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

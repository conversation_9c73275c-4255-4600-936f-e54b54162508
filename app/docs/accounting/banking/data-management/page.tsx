import { Metadata } from 'next';
import { DocSideNav } from '@/components/docs/doc-sidenav';
import { BankingDataManagementDoc } from '@/components/docs/content/accounting/banking-data-management';

export const metadata: Metadata = {
  title: 'Banking Data Management Documentation | TCM Enterprise Business Suite',
  description: 'Documentation for the Banking Data Management features in the Banking & Treasury Management module',
};

export default function BankingDataManagementDocPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Banking Data Management
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Comprehensive guide to the Banking Data Management features in the Banking & Treasury Management module
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <BankingDataManagementDoc
                backLink="/docs/accounting/banking"
                backLabel="Back to Banking & Treasury"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

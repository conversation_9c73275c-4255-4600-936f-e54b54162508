// app/docs/accounting/chart-of-accounts/page.tsx
import { Metadata } from 'next';
import fs from 'fs';
import path from 'path';
import { DocSideNav } from '@/components/docs/doc-side-nav';
import { MarkdownContent } from '@/components/docs/markdown-content';
import { BookOpen, List } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Chart of Accounts Guide | TCM Enterprise Business Suite',
  description: 'Learn how to set up, manage, and use the Chart of Accounts in the TCM Enterprise Business Suite',
};

export default function ChartOfAccountsGuidePage() {
  // Read the markdown file with error handling
  let markdownContent = '';
  try {
    const markdownPath = path.join(process.cwd(), 'project_guides', 'docs', 'accounting', 'chart-of-accounts-guide.md');
    markdownContent = fs.readFileSync(markdownPath, 'utf8');
  } catch (error) {
    console.error('Error reading chart-of-accounts-guide.md:', error);
    markdownContent = `# Chart of Accounts Guide

## Overview
This guide will help you understand and manage the Chart of Accounts in the TCM Enterprise Business Suite.

## Getting Started
The Chart of Accounts is the foundation of your accounting system. It provides a structured way to organize and track your financial transactions.

*Note: Detailed documentation is being updated. Please check back later for complete information.*`;
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-green-500 to-emerald-700">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center text-white">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Chart of Accounts Guide
              </h1>
              <p className="mx-auto max-w-[700px] text-zinc-200 md:text-xl">
                Learn how to set up, manage, and use the Chart of Accounts in the TCM Enterprise Business Suite
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-6">
                <BookOpen className="h-5 w-5 text-emerald-600" />
                <a href="/accounting/ledger/chart-of-accounts" className="text-emerald-600 hover:underline">
                  Go to Chart of Accounts Module
                </a>
              </div>
              <MarkdownContent markdown={markdownContent} />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

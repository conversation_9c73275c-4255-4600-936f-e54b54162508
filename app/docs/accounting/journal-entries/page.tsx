// app/docs/accounting/journal-entries/page.tsx
import { Metadata } from 'next';
import fs from 'fs';
import path from 'path';
import { DocSideNav } from '@/components/docs/doc-side-nav';
import { MarkdownContent } from '@/components/docs/markdown-content';
import { BookOpen, FileText } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Journal Entries Guide | TCM Enterprise Business Suite',
  description: 'Learn how to create, manage, and use journal entries in the TCM Enterprise Business Suite',
};

export default function JournalEntriesGuidePage() {
  // Read the markdown file with error handling
  let markdownContent = '';
  try {
    const markdownPath = path.join(process.cwd(), 'project_guides', 'docs', 'accounting', 'journal-entries-guide.md');
    markdownContent = fs.readFileSync(markdownPath, 'utf8');
  } catch (error) {
    console.error('Error reading journal-entries-guide.md:', error);
    markdownContent = `# Journal Entries Guide

## Overview
This guide covers creating and managing journal entries in the TCM Enterprise Business Suite.

## Key Features
- Create manual journal entries
- Automated journal entries from transactions
- Journal entry approval workflow
- Audit trail and history

*Note: Detailed documentation is being updated. Please check back later for complete information.*`;
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-green-500 to-emerald-700">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center text-white">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Journal Entries Guide
              </h1>
              <p className="mx-auto max-w-[700px] text-zinc-200 md:text-xl">
                Learn how to create, manage, and use journal entries in the TCM Enterprise Business Suite
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-6">
                <FileText className="h-5 w-5 text-emerald-600" />
                <a href="/accounting/ledger/journal-entry" className="text-emerald-600 hover:underline">
                  Go to Journal Entry Module
                </a>
              </div>
              <MarkdownContent markdown={markdownContent} />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

"use client"

import Link from "next/link"
import { DocContent } from "@/components/docs/markdown-renderer"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function AccountingDeveloperDocsPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-purple-900 to-purple-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                Developer Documentation
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Accounting API & Integration Guide
            </h1>
            <p className="max-w-[700px] text-purple-100 md:text-xl/relaxed">
              Technical documentation for developers integrating with the TCM Enterprise accounting system
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <DocContent
                title="Accounting API & Integration Guide"
                backLink="/docs/accounting"
                backLabel="Back to Accounting & Finance"
              >
                <h1>Accounting API & Integration Guide</h1>
                <p className="lead">
                  This technical guide provides comprehensive documentation for developers who need to integrate
                  with the TCM Enterprise accounting system. Learn about API endpoints, data models, authentication,
                  and best practices for building robust financial integrations.
                </p>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
                  <h3 className="text-lg font-semibold text-purple-800 mb-2">Prerequisites</h3>
                  <ul className="text-purple-700 space-y-1">
                    <li><strong>API Access:</strong> Valid API credentials and appropriate permissions</li>
                    <li><strong>Technical Knowledge:</strong> REST APIs, JSON, HTTP methods</li>
                    <li><strong>Accounting Basics:</strong> Understanding of double-entry bookkeeping</li>
                    <li><strong>Security:</strong> Knowledge of OAuth 2.0 and API security best practices</li>
                  </ul>
                </div>

                <h2>API Overview</h2>

                <h3>Base URL and Versioning</h3>
                <p>
                  All API endpoints are versioned and follow RESTful conventions. The current API version is v1.
                </p>

                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2">Base URL Structure:</h4>
                  <pre className="text-sm bg-slate-100 p-3 rounded overflow-x-auto">
{`https://your-domain.com/api/v1/accounting/
├── accounts/          # Chart of accounts management
├── transactions/      # Journal entries and transactions
├── banking/          # Bank accounts and reconciliation
├── reports/          # Financial reports and analytics
├── budgets/          # Budget management
└── settings/         # Accounting configuration`}
                  </pre>
                </div>

                <h3>Authentication</h3>
                <p>
                  The API uses OAuth 2.0 with JWT tokens for authentication. All requests must include a valid bearer token.
                </p>

                <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2 text-slate-900">Authentication Flow:</h4>
                  <pre className="text-sm bg-slate-100 p-3 rounded overflow-x-auto text-slate-800">
{`// 1. Obtain access token
POST /api/auth/token
{
  "client_id": "your_client_id",
  "client_secret": "your_client_secret",
  "grant_type": "client_credentials",
  "scope": "accounting:read accounting:write"
}

// 2. Use token in requests
GET /api/v1/accounting/accounts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`}
                  </pre>
                </div>

                <h2>Core API Endpoints</h2>

                <h3>Chart of Accounts</h3>
                <p>
                  Manage the chart of accounts structure, including account creation, updates, and hierarchy management.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
                  <div className="border border-slate-200 rounded-lg p-4 bg-slate-50">
                    <h4 className="font-semibold mb-3 text-blue-700">Account Management</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">GET</span>
                        <code className="text-slate-700">/accounts</code>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">POST</span>
                        <code className="text-slate-700">/accounts</code>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">PUT</span>
                        <code className="text-slate-700">/accounts/{id}</code>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">DELETE</span>
                        <code className="text-slate-700">/accounts/{id}</code>
                      </div>
                    </div>
                  </div>

                  <div className="border border-slate-200 rounded-lg p-4 bg-slate-50">
                    <h4 className="font-semibold mb-3 text-green-700">Account Hierarchy</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">GET</span>
                        <code className="text-slate-700">/accounts/tree</code>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">GET</span>
                        <code className="text-slate-700">/accounts/{id}/children</code>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">POST</span>
                        <code className="text-slate-700">/accounts/{id}/move</code>
                      </div>
                    </div>
                  </div>
                </div>

                <h4>Account Data Model</h4>
                <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 my-4">
                  <pre className="text-sm overflow-x-auto text-slate-800">
{`interface Account {
  id: string;
  code: string;              // Account code (e.g., "1000")
  name: string;              // Account name (e.g., "Cash")
  type: AccountType;         // ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
  subType: string;           // Detailed classification
  parentId?: string;         // Parent account for hierarchy
  isActive: boolean;         // Account status
  description?: string;      // Account description
  balance: number;           // Current balance
  debitBalance: number;      // Total debits
  creditBalance: number;     // Total credits
  currency: string;          // Account currency (default: MWK)
  taxCode?: string;          // Associated tax code
  createdAt: Date;
  updatedAt: Date;
}`}
                  </pre>
                </div>

                <h3>Transactions and Journal Entries</h3>
                <p>
                  Create, update, and manage financial transactions using double-entry bookkeeping principles.
                </p>

                <h4>Transaction Endpoints</h4>
                <div className="space-y-3 my-4">
                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">POST</Badge>
                      <code className="text-sm">/transactions</code>
                    </div>
                    <p className="text-sm text-slate-600 mb-2">Create a new journal entry with multiple line items</p>
                    <div className="text-xs text-slate-500">
                      <strong>Body:</strong> Transaction object with journal lines
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/transactions</code>
                    </div>
                    <p className="text-sm text-slate-600 mb-2">Retrieve transactions with filtering and pagination</p>
                    <div className="text-xs text-slate-500">
                      <strong>Query:</strong> date_from, date_to, account_id, status, page, limit
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">PATCH</Badge>
                      <code className="text-sm">/transactions/{id}/post</code>
                    </div>
                    <p className="text-sm text-slate-600 mb-2">Post a draft transaction to the general ledger</p>
                    <div className="text-xs text-slate-500">
                      <strong>Action:</strong> Changes status from DRAFT to POSTED
                    </div>
                  </div>
                </div>

                <h4>Transaction Data Model</h4>
                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <pre className="text-sm overflow-x-auto">
{`interface Transaction {
  id: string;
  reference: string;         // Transaction reference number
  description: string;       // Transaction description
  date: Date;               // Transaction date
  status: TransactionStatus; // DRAFT, POSTED, REVERSED
  totalAmount: number;       // Total transaction amount
  currency: string;          // Transaction currency
  exchangeRate?: number;     // For foreign currency transactions
  journalLines: JournalLine[]; // Array of journal line items
  attachments?: string[];    // Supporting document URLs
  createdBy: string;         // User who created the transaction
  postedBy?: string;         // User who posted the transaction
  postedAt?: Date;           // When transaction was posted
  createdAt: Date;
  updatedAt: Date;
}

interface JournalLine {
  id: string;
  accountId: string;         // Reference to account
  description?: string;      // Line item description
  debitAmount: number;       // Debit amount (0 if credit)
  creditAmount: number;      // Credit amount (0 if debit)
  taxCode?: string;          // Associated tax code
  costCenter?: string;       // Cost center allocation
  project?: string;          // Project allocation
}`}
                  </pre>
                </div>

                <h3>Banking and Reconciliation</h3>
                <p>
                  Manage bank accounts, import bank statements, and perform automated reconciliation.
                </p>

                <h4>Banking Endpoints</h4>
                <div className="space-y-3 my-4">
                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/banking/accounts</code>
                    </div>
                    <p className="text-sm text-slate-600">Retrieve all bank accounts with current balances</p>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">POST</Badge>
                      <code className="text-sm">/banking/statements/import</code>
                    </div>
                    <p className="text-sm text-slate-600">Import bank statement data for reconciliation</p>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-purple-100 text-purple-800">POST</Badge>
                      <code className="text-sm">/banking/reconcile</code>
                    </div>
                    <p className="text-sm text-slate-600">Perform automated bank reconciliation</p>
                  </div>
                </div>

                <h3>Financial Reports</h3>
                <p>
                  Generate standard financial reports and custom analytics programmatically.
                </p>

                <h4>Report Endpoints</h4>
                <div className="space-y-3 my-4">
                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/reports/income-statement</code>
                    </div>
                    <p className="text-sm text-slate-600">Generate profit and loss statement</p>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/reports/balance-sheet</code>
                    </div>
                    <p className="text-sm text-slate-600">Generate balance sheet report</p>
                  </div>

                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/reports/trial-balance</code>
                    </div>
                    <p className="text-sm text-slate-600">Generate trial balance report</p>
                  </div>
                </div>

                <h2>Integration Patterns</h2>

                <h3>Payroll Integration</h3>
                <p>
                  Seamlessly integrate payroll data with the accounting system for automated journal entry creation.
                </p>

                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2">Payroll Integration Flow:</h4>
                  <pre className="text-sm overflow-x-auto">
{`// 1. Process payroll run
POST /api/v1/payroll/runs/{id}/process

// 2. Create accounting entries
POST /api/v1/accounting/transactions
{
  "reference": "PAYROLL-2024-01",
  "description": "January 2024 Payroll",
  "date": "2024-01-31",
  "journalLines": [
    {
      "accountId": "salary-expense",
      "debitAmount": 150000,
      "description": "Gross salaries"
    },
    {
      "accountId": "paye-payable",
      "creditAmount": 25000,
      "description": "PAYE tax withheld"
    },
    {
      "accountId": "bank-account",
      "creditAmount": 125000,
      "description": "Net pay to employees"
    }
  ]
}`}
                  </pre>
                </div>

                <h3>Third-Party System Integration</h3>
                <p>
                  Connect external systems like CRM, inventory management, or e-commerce platforms.
                </p>

                <h4>Webhook Support</h4>
                <p>
                  Configure webhooks to receive real-time notifications of accounting events.
                </p>

                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2">Webhook Configuration:</h4>
                  <pre className="text-sm overflow-x-auto">
{`POST /api/v1/webhooks
{
  "url": "https://your-system.com/webhooks/accounting",
  "events": [
    "transaction.created",
    "transaction.posted",
    "account.created",
    "reconciliation.completed"
  ],
  "secret": "your-webhook-secret"
}`}
                  </pre>
                </div>

                <h2>Security and Best Practices</h2>

                <h3>API Security</h3>
                <ul>
                  <li><strong>Authentication:</strong> Always use OAuth 2.0 with appropriate scopes</li>
                  <li><strong>HTTPS:</strong> All API calls must use HTTPS encryption</li>
                  <li><strong>Rate Limiting:</strong> Respect API rate limits (1000 requests/hour)</li>
                  <li><strong>Token Management:</strong> Implement proper token refresh mechanisms</li>
                </ul>

                <h3>Data Integrity</h3>
                <ul>
                  <li><strong>Validation:</strong> Always validate data before submission</li>
                  <li><strong>Idempotency:</strong> Use idempotency keys for critical operations</li>
                  <li><strong>Error Handling:</strong> Implement comprehensive error handling</li>
                  <li><strong>Audit Trail:</strong> Maintain logs of all API interactions</li>
                </ul>

                <h3>Performance Optimization</h3>
                <ul>
                  <li><strong>Pagination:</strong> Use pagination for large data sets</li>
                  <li><strong>Filtering:</strong> Apply appropriate filters to reduce payload size</li>
                  <li><strong>Caching:</strong> Implement caching for frequently accessed data</li>
                  <li><strong>Batch Operations:</strong> Use batch endpoints for bulk operations</li>
                </ul>

                <h2>Error Handling</h2>

                <h3>HTTP Status Codes</h3>
                <div className="space-y-2 my-4">
                  <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <code>200 OK</code>
                    <span className="text-sm">Request successful</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                    <code>201 Created</code>
                    <span className="text-sm">Resource created successfully</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                    <code>400 Bad Request</code>
                    <span className="text-sm">Invalid request data</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <code>401 Unauthorized</code>
                    <span className="text-sm">Authentication required</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <code>403 Forbidden</code>
                    <span className="text-sm">Insufficient permissions</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <code>422 Unprocessable Entity</code>
                    <span className="text-sm">Validation errors</span>
                  </div>
                </div>

                <h3>Error Response Format</h3>
                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <pre className="text-sm overflow-x-auto">
{`{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Transaction does not balance",
    "details": {
      "totalDebits": 1000,
      "totalCredits": 950,
      "difference": 50
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123456789"
  }
}`}
                  </pre>
                </div>

                <h2>SDK and Code Examples</h2>

                <h3>JavaScript/TypeScript SDK</h3>
                <div className="bg-slate-50 border rounded-lg p-4 my-4">
                  <pre className="text-sm overflow-x-auto">
{`import { AccountingAPI } from '@tcm/accounting-sdk';

const api = new AccountingAPI({
  baseURL: 'https://your-domain.com/api/v1',
  clientId: 'your_client_id',
  clientSecret: 'your_client_secret'
});

// Create a journal entry
const transaction = await api.transactions.create({
  reference: 'INV-001',
  description: 'Customer payment',
  date: new Date(),
  journalLines: [
    {
      accountId: 'bank-account',
      debitAmount: 1000,
      description: 'Payment received'
    },
    {
      accountId: 'accounts-receivable',
      creditAmount: 1000,
      description: 'Customer payment'
    }
  ]
});

// Post the transaction
await api.transactions.post(transaction.id);`}
                  </pre>
                </div>

                <h2>Testing and Development</h2>

                <h3>Sandbox Environment</h3>
                <p>
                  Use the sandbox environment for development and testing without affecting production data.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 my-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Sandbox Details:</h4>
                  <ul className="text-blue-700 space-y-1">
                    <li><strong>Base URL:</strong> https://sandbox.your-domain.com/api/v1</li>
                    <li><strong>Test Data:</strong> Pre-populated with sample accounts and transactions</li>
                    <li><strong>Reset:</strong> Data resets daily at midnight UTC</li>
                    <li><strong>Rate Limits:</strong> Same as production for realistic testing</li>
                  </ul>
                </div>

                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Support and Resources</h2>
                <p className="mb-4">
                  Additional resources for developers working with the accounting API:
                </p>
                <ul className="list-disc pl-6 space-y-2 mb-6">
                  <li><Link href="/docs/accounting/user-guide" className="text-emerald-600 hover:text-emerald-700">User Guide</Link> for business context</li>
                  <li><Link href="/docs/accounting/management-guide" className="text-emerald-600 hover:text-emerald-700">Management Guide</Link> for strategic insights</li>
                  <li><Link href="/docs/api/accounting" className="text-emerald-600 hover:text-emerald-700">Complete API Reference</Link> for detailed endpoint documentation</li>
                  <li><strong>Developer Support:</strong> <EMAIL></li>
                </ul>
              </DocContent>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

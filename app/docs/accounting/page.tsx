"use client"

import Link from "next/link"
import { ArrowRight, Landmark, BookOpen, Settings, Code, CreditCard, TrendingUp, FileText, Building, Calculator, PieChart } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"

export default function AccountingModulePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                Core Module
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Accounting & Finance
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Complete financial management system - general ledger, banking, payments, reporting, and Malawian tax compliance
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-8">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/accounting">Accounting & Finance</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Module Overview */}
              <div className="mb-12">
                <h2 className="text-3xl font-bold text-zinc-900 mb-4">Accounting & Finance Module</h2>
                <p className="text-lg text-zinc-600 mb-6">
                  The Accounting & Finance module provides comprehensive financial management capabilities including
                  general ledger, banking, payments, financial reporting, and compliance with Malawian tax regulations.
                </p>

                <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-6 mb-8">
                  <h3 className="text-lg font-semibold text-emerald-800 mb-2">Malawian Compliance</h3>
                  <p className="text-emerald-700">
                    Built-in compliance with <strong>Malawi Revenue Authority (MRA)</strong> requirements,
                    including PAYE tax calculations, VAT handling, and statutory reporting formats.
                  </p>
                </div>
              </div>

              {/* Documentation Categories */}
              <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3 mb-12">
                <Link href="/docs/accounting/user-guide">
                  <Card className="h-full transition-all hover:shadow-md border-blue-200">
                    <CardHeader>
                      <BookOpen className="h-8 w-8 text-blue-500 mb-2" />
                      <CardTitle>User Guide</CardTitle>
                      <CardDescription>Daily financial operations and transactions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Step-by-step guides for daily accounting tasks and financial operations.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Journal entries and transactions</div>
                        <div>• Banking and reconciliation</div>
                        <div>• Invoice and payment processing</div>
                        <div>• Financial reporting</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                        Read User Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/accounting/management-guide">
                  <Card className="h-full transition-all hover:shadow-md border-purple-200">
                    <CardHeader>
                      <Settings className="h-8 w-8 text-purple-500 mb-2" />
                      <CardTitle>Management Guide</CardTitle>
                      <CardDescription>Financial oversight and strategic management</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Strategic guidance for financial managers and executives.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Financial planning and budgeting</div>
                        <div>• Management reporting and KPIs</div>
                        <div>• Compliance and audit management</div>
                        <div>• Cash flow and treasury management</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                        Read Management Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/accounting/developer-docs">
                  <Card className="h-full transition-all hover:shadow-md border-orange-200">
                    <CardHeader>
                      <Code className="h-8 w-8 text-orange-500 mb-2" />
                      <CardTitle>Developer Docs</CardTitle>
                      <CardDescription>Technical implementation and API integration</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Technical documentation for accounting system integrations.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Accounting API endpoints</div>
                        <div>• Integration with Payroll module</div>
                        <div>• Banking API connections</div>
                        <div>• Custom report development</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-orange-600">
                        Read Developer Docs <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>

              {/* Core Features */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-zinc-900 mb-6">Core Accounting Functions</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <FileText className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">General Ledger</h4>
                      <p className="text-sm text-zinc-600">Chart of accounts, journal entries, trial balance, and financial statements</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Building className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Banking & Treasury</h4>
                      <p className="text-sm text-zinc-600">Bank account management, reconciliation, and cash flow forecasting</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <CreditCard className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Accounts Payable & Receivable</h4>
                      <p className="text-sm text-zinc-600">Vendor management, customer invoicing, and payment processing</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <PieChart className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Financial Reporting</h4>
                      <p className="text-sm text-zinc-600">Standard reports, management dashboards, and regulatory compliance</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="bg-zinc-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-zinc-900 mb-4">Quick Links</h3>
                <div className="grid gap-3 md:grid-cols-2">
                  <Link href="/docs/api/accounting" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Code className="h-4 w-4 mr-2" />
                    Accounting API Reference
                  </Link>
                  <Link href="/docs/api/payroll" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Calculator className="h-4 w-4 mr-2" />
                    Payroll Integration Guide
                  </Link>
                  <Link href="/docs/employees" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <FileText className="h-4 w-4 mr-2" />
                    Employee Module Integration
                  </Link>
                  <Link href="/docs/settings" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Settings className="h-4 w-4 mr-2" />
                    Chart of Accounts Setup
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
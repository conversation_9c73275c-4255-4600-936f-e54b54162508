"use client"

import Link from "next/link"
import { DocContent } from "@/components/docs/markdown-renderer"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function AccountingUserGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Accounting & Finance User Guide
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Step-by-step instructions for daily accounting tasks and financial operations
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <DocContent
                title="Accounting & Finance User Guide"
                backLink="/docs/accounting"
                backLabel="Back to Accounting & Finance"
              >
                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Overview</h2>
                <p className="mb-4">
                  This guide covers the essential daily tasks for managing financial operations in the TCM Enterprise Suite.
                  Learn how to process transactions, manage accounts, handle banking operations, and generate financial reports.
                </p>

                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Getting Started</h2>
                <p className="mb-4">
                  The Accounting & Finance module is your central hub for all financial operations.
                  Access it from the main dashboard by clicking on "Accounting" in the navigation menu.
                </p>

                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Before You Begin</h2>
                <ul className="list-disc pl-6 space-y-2 mb-6">
                  <li>Ensure you have the necessary accounting permissions</li>
                  <li>Understand your organization's chart of accounts</li>
                  <li>Have supporting documents ready for transactions</li>
                  <li>Know your organization's approval workflows</li>
                </ul>

                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Core Concepts</h2>

                <h3 className="text-xl font-semibold mt-4 mb-3">Chart of Accounts</h3>
                <p className="mb-4">
                  The chart of accounts is the foundation of your accounting system. It's organized into:
                </p>
                <ul className="list-disc pl-6 space-y-2 mb-6">
                  <li><strong>Assets</strong>: What the organization owns (cash, equipment, etc.)</li>
                  <li><strong>Liabilities</strong>: What the organization owes (loans, payables, etc.)</li>
                  <li><strong>Equity</strong>: Owner's equity and retained earnings</li>
                  <li><strong>Revenue</strong>: Income from operations and other sources</li>
                  <li><strong>Expenses</strong>: Costs of doing business</li>
                </ul>

                <h3>Double-Entry Bookkeeping</h3>
                <p>
                  Every transaction affects at least two accounts, and the total debits must equal total credits.
                  The system automatically enforces this principle.
                </p>

                <h3>Malawian Tax Compliance</h3>
                <p>
                  The system includes built-in compliance with Malawi Revenue Authority (MRA) requirements:
                </p>
                <ul>
                  <li>PAYE tax calculations and reporting</li>
                  <li>VAT handling and returns</li>
                  <li>Withholding tax management</li>
                  <li>Statutory reporting formats</li>
                </ul>

                <h2>Daily Workflows</h2>

                <h3>1. Recording Journal Entries</h3>
                <p>Journal entries are the foundation of all accounting transactions:</p>

                <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2 text-slate-900">Step-by-Step Process:</h4>
                  <ol className="space-y-2 text-slate-700">
                    <li>Navigate to <strong>Accounting → Journal Entries</strong></li>
                    <li>Click <strong>New Journal Entry</strong></li>
                    <li>Enter transaction details:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Date of transaction</li>
                        <li>Reference number (invoice, receipt, etc.)</li>
                        <li>Description of transaction</li>
                      </ul>
                    </li>
                    <li>Add journal lines:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Select account from chart of accounts</li>
                        <li>Enter debit or credit amount</li>
                        <li>Add line description if needed</li>
                      </ul>
                    </li>
                    <li>Verify that total debits equal total credits</li>
                    <li>Attach supporting documents</li>
                    <li>Save as draft or post immediately</li>
                  </ol>
                </div>

                <h3>2. Processing Payments</h3>
                <p>Handle both incoming and outgoing payments efficiently:</p>

                <h4>Recording Customer Payments</h4>
                <ol>
                  <li>Go to <strong>Accounting → Payments → Receive Payment</strong></li>
                  <li>Select the customer</li>
                  <li>Choose payment method (cash, check, bank transfer)</li>
                  <li>Enter payment amount and date</li>
                  <li>Apply payment to outstanding invoices</li>
                  <li>Record any discounts or adjustments</li>
                  <li>Save and post the payment</li>
                </ol>

                <h4>Making Vendor Payments</h4>
                <ol>
                  <li>Navigate to <strong>Accounting → Payments → Make Payment</strong></li>
                  <li>Select the vendor</li>
                  <li>Choose payment method and bank account</li>
                  <li>Select bills to pay</li>
                  <li>Review payment details</li>
                  <li>Generate payment voucher</li>
                  <li>Process payment through banking system</li>
                </ol>

                <h3>3. Bank Reconciliation</h3>
                <p>Regular bank reconciliation ensures accuracy of your cash records:</p>

                <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2 text-slate-900">Monthly Reconciliation Process:</h4>
                  <ol className="space-y-2 text-slate-700">
                    <li>Download bank statement from your bank</li>
                    <li>Go to <strong>Accounting → Banking → Bank Reconciliation</strong></li>
                    <li>Select the bank account to reconcile</li>
                    <li>Import or manually enter bank statement transactions</li>
                    <li>Match bank transactions with book transactions:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Green checkmarks indicate matched transactions</li>
                        <li>Red flags indicate discrepancies</li>
                        <li>Yellow highlights indicate potential matches</li>
                      </ul>
                    </li>
                    <li>Investigate and resolve any discrepancies</li>
                    <li>Record any bank fees or interest</li>
                    <li>Complete reconciliation when differences are zero</li>
                  </ol>
                </div>

                <h3>4. Generating Financial Reports</h3>
                <p>Access comprehensive financial reporting:</p>

                <h4>Standard Reports</h4>
                <ul>
                  <li><strong>Income Statement</strong>: Revenue and expenses for a period</li>
                  <li><strong>Balance Sheet</strong>: Assets, liabilities, and equity at a point in time</li>
                  <li><strong>Cash Flow Statement</strong>: Cash inflows and outflows</li>
                  <li><strong>Trial Balance</strong>: All account balances to verify accuracy</li>
                </ul>

                <h4>Management Reports</h4>
                <ul>
                  <li><strong>Aged Receivables</strong>: Outstanding customer balances by age</li>
                  <li><strong>Aged Payables</strong>: Outstanding vendor balances by age</li>
                  <li><strong>Budget vs. Actual</strong>: Performance against budget</li>
                  <li><strong>Department P&L</strong>: Profit and loss by department</li>
                </ul>

                <h2>Best Practices</h2>

                <h3>Data Accuracy</h3>
                <ul>
                  <li>Always verify transaction details before posting</li>
                  <li>Use consistent account coding</li>
                  <li>Attach supporting documents to all transactions</li>
                  <li>Review and approve transactions promptly</li>
                </ul>

                <h3>Month-End Procedures</h3>
                <ul>
                  <li>Complete all bank reconciliations</li>
                  <li>Record all accruals and deferrals</li>
                  <li>Review account balances for reasonableness</li>
                  <li>Generate and review financial statements</li>
                  <li>Close the accounting period</li>
                </ul>

                <h3>Compliance Management</h3>
                <ul>
                  <li>Stay current with MRA tax requirements</li>
                  <li>Maintain proper documentation for audits</li>
                  <li>File tax returns and payments on time</li>
                  <li>Keep backup copies of all financial data</li>
                </ul>

                <h2>Troubleshooting</h2>

                <h3>Common Issues</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Journal Entry Won't Balance</h4>
                    <p>Check that total debits equal total credits. Review each line item for accuracy.</p>
                  </div>

                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Bank Reconciliation Differences</h4>
                    <p>Look for timing differences, bank fees, or data entry errors. Check for duplicate entries.</p>
                  </div>

                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Report Shows Unexpected Results</h4>
                    <p>Verify the date range and account filters. Check for unposted transactions or period closures.</p>
                  </div>
                </div>

                <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Next Steps</h2>
                <p className="mb-4">
                  Once you're comfortable with basic accounting operations, explore these advanced areas:
                </p>
                <ul className="list-disc pl-6 space-y-2 mb-6">
                  <li><Link href="/docs/accounting/management-guide" className="text-emerald-600 hover:text-emerald-700">Financial Management and Planning</Link></li>
                  <li><Link href="/docs/accounting/developer-docs" className="text-emerald-600 hover:text-emerald-700">API Integration and Automation</Link></li>
                  <li><Link href="/docs/api/payroll" className="text-emerald-600 hover:text-emerald-700">Payroll Integration</Link></li>
                </ul>
              </DocContent>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

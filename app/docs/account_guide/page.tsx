// app/docs/account_guide/page.tsx
"use client"

import { DocContent } from "@/components/docs/markdown-renderer"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BarChart4,
  PieChart,
  TrendingUp,
  FileText,
  BookOpen,
  Landmark,
  CreditCard,
  LinkIcon,
  ArrowRight,
  Banknote,
  Receipt,
  Building,
  FileSpreadsheet,
  Calculator
} from "lucide-react"
import Link from "next/link"

export default function AccountingGuidePage() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-emerald-50 to-white">
        <DocsPatternBackground />
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none text-emerald-800">
                Accounting System Guide
              </h1>
              <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl">
                Comprehensive guide to the financial management features of the TCM Enterprise Business Suite
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="md:hidden">
              <DocsMobileNav />
            </div>
            <div className="flex-1">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="modules">Core Modules</TabsTrigger>
                  <TabsTrigger value="integration">Integration</TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                  <DocContent title="Accounting System Overview">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Introduction</h2>
                    <p className="mb-4">
                      The TCM Enterprise Business Suite Accounting System is a comprehensive financial management solution
                      designed specifically for the Teachers Council of Malawi. It provides tools for managing all aspects
                      of financial operations, including budgeting, income tracking, expenditure management, banking, and
                      financial reporting.
                    </p>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Key Features</h2>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li><strong>Financial Dashboard:</strong> Overview of financial health and key metrics</li>
                      <li><strong>Banking & Treasury:</strong> Manage bank accounts, reconciliations, and cash flow</li>
                      <li><strong>Budget & Planning:</strong> Create and manage budget plans for income and expenditure</li>
                      <li><strong>Income Management:</strong> Track and manage income sources</li>
                      <li><strong>Expenditure Management:</strong> Track and manage expenses across departments</li>
                      <li><strong>Voucher Management:</strong> Create and manage payment, receipt, and journal vouchers</li>
                      <li><strong>Accounting Core:</strong> Manage chart of accounts, general ledger, and journal entries</li>
                      <li><strong>Financial Reporting:</strong> Generate quarterly and annual financial reports</li>
                      <li><strong>External Integrations:</strong> Connect with QuickBooks, Sage, and other accounting systems</li>
                    </ul>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">System Architecture</h2>
                    <p className="mb-4">
                      The Accounting System is built on a modular architecture that integrates with other modules in the TCM
                      Enterprise Business Suite. It uses a shared database and common services to ensure data consistency
                      and seamless operation across the entire system.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Data Flow</CardTitle>
                          <CardDescription>How data moves through the accounting system</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>The accounting system follows a structured data flow:</p>
                          <ol className="list-decimal pl-6 space-y-1 mt-2">
                            <li>Source documents (invoices, receipts, etc.) are entered into the system</li>
                            <li>Transactions are recorded in journals</li>
                            <li>Journal entries are posted to the general ledger</li>
                            <li>Financial statements are generated from the general ledger</li>
                            <li>Reports are created for management and compliance purposes</li>
                          </ol>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle>Integration Points</CardTitle>
                          <CardDescription>How accounting connects with other modules</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>The accounting system integrates with:</p>
                          <ul className="list-disc pl-6 space-y-1 mt-2">
                            <li>Payroll - for salary processing and journal entries</li>
                            <li>Employee - for expense claims and advances</li>
                            <li>Inventory - for asset valuation and stock management</li>
                            <li>Banking - for reconciliation and payment processing</li>
                            <li>External systems - for data import/export</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  </DocContent>
                </TabsContent>

                {/* Modules Tab */}
                <TabsContent value="modules" className="space-y-6">
                  <DocContent title="Core Accounting Modules">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Financial Dashboard</h2>
                    <p className="mb-4">
                      The Financial Dashboard provides a high-level overview of the organization's financial health. It displays
                      key metrics, charts, and indicators that help management make informed decisions.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <BarChart4 className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/dashboard" className="text-emerald-600 hover:underline">
                        Access Financial Dashboard
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Budget & Planning</h2>
                    <p className="mb-4">
                      The Budget & Planning module allows you to create and manage budget plans for income and expenditure.
                      It provides tools for budget allocation, monitoring, and revision.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <PieChart className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/budget/planning" className="text-emerald-600 hover:underline">
                        Access Budget Planning
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Income Management</h2>
                    <p className="mb-4">
                      The Income Management module helps track and manage all income sources, including government subventions,
                      fees, and donations. It provides tools for income forecasting and analysis.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <TrendingUp className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/income/overview" className="text-emerald-600 hover:underline">
                        Access Income Management
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Expenditure Management</h2>
                    <p className="mb-4">
                      The Expenditure Management module helps track and manage expenses across departments. It provides
                      tools for expense categorization, approval workflows, and analysis.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <Banknote className="h-5 w-5 text-emerald-600" />
                      <Link href="/dashboard/accounting/expenditures" className="text-emerald-600 hover:underline">
                        Access Expenditure Management
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Voucher Management</h2>
                    <p className="mb-4">
                      The Voucher Management module allows you to create and manage payment, receipt, and journal vouchers.
                      It provides tools for voucher approval, printing, and tracking.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <Receipt className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/vouchers/payment" className="text-emerald-600 hover:underline">
                        Access Voucher Management
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Accounting Core</h2>
                    <p className="mb-4">
                      The Accounting Core module provides tools for managing the chart of accounts, general ledger, and
                      journal entries. It forms the foundation of the accounting system.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <BookOpen className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/ledger/chart-of-accounts" className="text-emerald-600 hover:underline">
                        Access Accounting Core
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Banking & Treasury</h2>
                    <p className="mb-4">
                      The Banking & Treasury module provides tools for managing bank accounts, reconciliations, and cash flow.
                      It helps ensure accurate financial records and optimal cash management.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <Landmark className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/banking" className="text-emerald-600 hover:underline">
                        Access Banking & Treasury
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Financial Reporting</h2>
                    <p className="mb-4">
                      The Financial Reporting module allows you to generate quarterly and annual financial reports for
                      management and compliance purposes. It provides tools for customizing reports and exporting data.
                    </p>
                    <div className="flex items-center gap-2 mb-6">
                      <FileText className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/reports" className="text-emerald-600 hover:underline">
                        Access Financial Reporting
                      </Link>
                    </div>

                    <h2 className="text-2xl font-semibold mt-8 mb-4 text-emerald-700">Accounting Guides</h2>
                    <p className="mb-4">
                      Detailed guides are available for key accounting features to help you understand and use the system effectively.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileSpreadsheet className="h-5 w-5 text-emerald-600" />
                            <span>Financial Reporting</span>
                          </CardTitle>
                          <CardDescription>Generate and analyze financial reports</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="mb-3">Learn how to generate, view, and analyze financial reports including income statements, balance sheets, cash flow statements, and trial balances.</p>
                          <Link href="/docs/accounting/financial-reporting" className="text-emerald-600 hover:underline flex items-center gap-1">
                            View Guide <ArrowRight className="h-4 w-4" />
                          </Link>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <BookOpen className="h-5 w-5 text-emerald-600" />
                            <span>Chart of Accounts</span>
                          </CardTitle>
                          <CardDescription>Manage your accounting structure</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="mb-3">Learn how to set up, manage, and use the Chart of Accounts to organize your financial data effectively.</p>
                          <Link href="/docs/accounting/chart-of-accounts" className="text-emerald-600 hover:underline flex items-center gap-1">
                            View Guide <ArrowRight className="h-4 w-4" />
                          </Link>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-emerald-600" />
                            <span>Journal Entries</span>
                          </CardTitle>
                          <CardDescription>Record financial transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="mb-3">Learn how to create, manage, and use journal entries to record financial transactions accurately.</p>
                          <Link href="/docs/accounting/journal-entries" className="text-emerald-600 hover:underline flex items-center gap-1">
                            View Guide <ArrowRight className="h-4 w-4" />
                          </Link>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Landmark className="h-5 w-5 text-emerald-600" />
                            <span>Bank Reconciliation</span>
                          </CardTitle>
                          <CardDescription>Match bank statements with your records</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="mb-3">Learn how to perform bank reconciliations to ensure your financial records match your bank statements.</p>
                          <Link href="/docs/accounting/bank-reconciliation" className="text-emerald-600 hover:underline flex items-center gap-1">
                            View Guide <ArrowRight className="h-4 w-4" />
                          </Link>
                        </CardContent>
                      </Card>
                    </div>
                  </DocContent>
                </TabsContent>

                {/* Integration Tab */}
                <TabsContent value="integration" className="space-y-6">
                  <DocContent title="System Integration">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Integration with Other Modules</h2>
                    <p className="mb-4">
                      The Accounting System integrates seamlessly with other modules in the TCM Enterprise Business Suite,
                      creating a cohesive financial management ecosystem.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Payroll Integration</CardTitle>
                          <CardDescription>Connecting accounting with payroll operations</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>The accounting system integrates with the Payroll module to:</p>
                          <ul className="list-disc pl-6 space-y-1 mt-2">
                            <li>Generate journal entries for salary payments</li>
                            <li>Track payroll liabilities and expenses</li>
                            <li>Process tax payments and deductions</li>
                            <li>Manage employee advances and loans</li>
                            <li>Generate payroll reports for financial statements</li>
                          </ul>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle>Inventory Integration</CardTitle>
                          <CardDescription>Connecting accounting with inventory management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>The accounting system integrates with the Inventory module to:</p>
                          <ul className="list-disc pl-6 space-y-1 mt-2">
                            <li>Track inventory asset values</li>
                            <li>Record inventory purchases and sales</li>
                            <li>Manage depreciation of assets</li>
                            <li>Handle inventory write-offs and adjustments</li>
                            <li>Generate inventory valuation reports</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">External System Integration</h2>
                    <p className="mb-4">
                      The Accounting System can connect with external accounting systems such as QuickBooks, Sage, and Xero
                      for data import/export and synchronization.
                    </p>

                    <div className="flex items-center gap-2 mb-6">
                      <LinkIcon className="h-5 w-5 text-emerald-600" />
                      <Link href="/accounting/integrations" className="text-emerald-600 hover:underline">
                        Access Integration Settings
                      </Link>
                    </div>

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Integration Process</h3>
                    <ol className="list-decimal pl-6 space-y-2 mb-6">
                      <li>Navigate to the Accounting  Integrations page</li>
                      <li>Select the desired integration (QuickBooks, Sage, etc.)</li>
                      <li>Follow the authentication process to connect to the external system</li>
                      <li>Configure the data mapping between systems</li>
                      <li>Set up synchronization schedules and options</li>
                      <li>Test the integration to ensure proper data flow</li>
                    </ol>
                  </DocContent>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

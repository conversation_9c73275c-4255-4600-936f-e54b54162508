import { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, FileText, Calendar, DollarSign, Users, CreditCard } from "lucide-react"

export const metadata: Metadata = {
  title: "Payroll Module Documentation",
  description: "Documentation for the Payroll module in TCM Enterprise Business Suite",
}

export default function PayrollModuleDocsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Module Documentation"
        description="Comprehensive guide for the Payroll module"
      >
        <Link href="/docs">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Documentation
          </Button>
        </Link>
      </DashboardHeader>

      <Tabs defaultValue="user-guide" className="space-y-4">
        <TabsList>
          <TabsTrigger value="user-guide">User Guide</TabsTrigger>
          <TabsTrigger value="technical-guide">Technical Guide</TabsTrigger>
        </TabsList>

        <TabsContent value="user-guide" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Module User Guide</CardTitle>
              <CardDescription>
                This guide explains how to use the Payroll module in the TCM Enterprise Business Suite.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Overview</h3>
                <p>
                  The Payroll module handles all aspects of employee compensation, from defining salary structures to processing payroll and generating payslips. It is designed to work seamlessly with the Employee module to ensure accurate and efficient payroll processing.
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Payroll Dashboard</h3>
                <p>
                  The Payroll Dashboard provides quick access to all payroll features and functions. From here, you can navigate to different components of the payroll system.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="border rounded-md p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-primary" />
                      <h4 className="font-medium">Payroll Runs</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Create and manage payroll runs for specific periods. Process employee salaries, generate payslips, and track payroll history.
                    </p>
                  </div>
                  <div className="border rounded-md p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      <h4 className="font-medium">Payslips</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Generate, view, and distribute employee payslips. Download payslips in PDF format or email them directly to employees.
                    </p>
                  </div>
                  <div className="border rounded-md p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-primary" />
                      <h4 className="font-medium">Salary Structures</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Define and manage salary templates for different positions and departments. Set up basic salary, allowances, and deductions.
                    </p>
                  </div>
                  <div className="border rounded-md p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-primary" />
                      <h4 className="font-medium">Employee Salaries</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Manage individual employee salary details and history. Assign salary structures and track salary revisions.
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Payroll Runs</h3>
                <p>
                  Payroll Runs are the core of the payroll process. They represent a specific pay period (e.g., monthly payroll) and include all the calculations and processing for employee salaries.
                </p>

                <div className="space-y-2">
                  <h4 className="font-medium">Creating a Payroll Run</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Navigate to <strong>Dashboard &gt; Payroll &gt; Runs</strong></li>
                    <li>Click the <strong>New Payroll Run</strong> button</li>
                    <li>Complete the payroll run wizard:
                      <ul className="list-disc list-inside ml-6 mt-1">
                        <li><strong>Setup</strong>: Define the pay period, name, and description</li>
                        <li><strong>Review</strong>: Review the payroll run details</li>
                        <li><strong>Complete</strong>: Finalize the payroll run creation</li>
                      </ul>
                    </li>
                    <li>After creation, the payroll run will be in <strong>Draft</strong> status</li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Processing a Payroll Run</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>From the Payroll Runs page, click on a payroll run in <strong>Draft</strong> status</li>
                    <li>Click the <strong>Process Payroll</strong> button</li>
                    <li>The system will calculate salaries for all employees included in the run</li>
                    <li>Once processing is complete, the status will change to <strong>Completed</strong></li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Approving a Payroll Run</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>From the Payroll Runs page, click on a payroll run in <strong>Completed</strong> status</li>
                    <li>Review the payroll details and summary</li>
                    <li>Click the <strong>Approve</strong> button</li>
                    <li>Once approved, the status will change to <strong>Approved</strong></li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Exporting Payroll Data</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>From the Payroll Run details page, click <strong>Export to Excel</strong> or <strong>Export to PDF</strong></li>
                    <li>The system will generate the export file and download it to your device</li>
                    <li>Excel exports include detailed breakdowns and summary information</li>
                    <li>PDF exports are formatted for printing and sharing</li>
                  </ol>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Payslips</h3>
                <p>
                  Payslips provide detailed payment information for individual employees. They can be generated, viewed, printed, and distributed to employees.
                </p>

                <div className="space-y-2">
                  <h4 className="font-medium">Generating Payslips</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>From a processed payroll run, click the <strong>Generate Payslips</strong> button</li>
                    <li>Alternatively, go to <strong>Dashboard &gt; Payroll &gt; Payslips</strong></li>
                    <li>Select a payroll run from the dropdown</li>
                    <li>Click <strong>Generate Payslips</strong> in the Payslip Actions card</li>
                    <li>The system will create payslips for all employees in the payroll run</li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Viewing and Managing Payslips</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Payslips</strong></li>
                    <li>Select a payroll run from the dropdown</li>
                    <li>Browse the list of payslips or use the search function to find specific employees</li>
                    <li>Click on the actions menu (three dots) for a payslip to:
                      <ul className="list-disc list-inside ml-6 mt-1">
                        <li><strong>View</strong>: Open the payslip viewer</li>
                        <li><strong>Download</strong>: Download the payslip as PDF</li>
                        <li><strong>Email</strong>: Send the payslip to the employee's email</li>
                        <li><strong>Print</strong>: Open the payslip in print view</li>
                      </ul>
                    </li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Bulk Payslip Actions</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Payslips</strong></li>
                    <li>Select a payroll run from the dropdown</li>
                    <li>Use the buttons in the Payslip Actions card to:
                      <ul className="list-disc list-inside ml-6 mt-1">
                        <li><strong>Generate Payslips</strong>: Create payslips for all employees</li>
                        <li><strong>Email All</strong>: Send payslips to all employees</li>
                        <li><strong>Download All</strong>: Download all payslips as a ZIP file</li>
                        <li><strong>Print All</strong>: Open all payslips in print view</li>
                      </ul>
                    </li>
                  </ol>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Salary Structures</h3>
                <p>
                  Salary Structures define templates for employee compensation packages. They include basic salary, allowances, and deductions that can be applied to multiple employees.
                </p>

                <div className="space-y-2">
                  <h4 className="font-medium">Creating Salary Structures</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Salary Structures</strong></li>
                    <li>Click <strong>New Salary Structure</strong></li>
                    <li>Fill in the structure details:
                      <ul className="list-disc list-inside ml-6 mt-1">
                        <li><strong>Name</strong>: A descriptive name for the structure</li>
                        <li><strong>Description</strong>: Additional details about the structure</li>
                        <li><strong>Basic Salary</strong>: The base amount for the structure</li>
                        <li><strong>Allowances</strong>: Additional payments (housing, transport, etc.)</li>
                        <li><strong>Deductions</strong>: Amounts to be deducted (pension, insurance, etc.)</li>
                      </ul>
                    </li>
                    <li>Specify which roles or departments the structure applies to</li>
                    <li>Click <strong>Save</strong> to create the structure</li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Bulk Import of Salary Structures</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Salary Structures</strong></li>
                    <li>Click <strong>Bulk Upload</strong></li>
                    <li>Download the template file</li>
                    <li>Fill in the template with structure information</li>
                    <li>Upload the completed file</li>
                    <li>Review and confirm the import</li>
                  </ol>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Employee Salaries</h3>
                <p>
                  Employee Salaries link individual employees to salary structures and define their specific compensation details.
                </p>

                <div className="space-y-2">
                  <h4 className="font-medium">Creating Employee Salary Records</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Employee Salaries</strong></li>
                    <li>Click <strong>New Salary</strong></li>
                    <li>Select an employee from the dropdown</li>
                    <li>Choose a salary structure or define custom salary details</li>
                    <li>Set the effective date for the salary</li>
                    <li>Add any employee-specific allowances or deductions</li>
                    <li>Enter bank account and payment method details</li>
                    <li>Click <strong>Save</strong> to create the salary record</li>
                  </ol>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Salary Revisions</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Go to <strong>Dashboard &gt; Payroll &gt; Employee Salaries</strong></li>
                    <li>Find the employee whose salary needs revision</li>
                    <li>Click <strong>Revise Salary</strong></li>
                    <li>Enter the new salary details</li>
                    <li>Set the effective date for the revision</li>
                    <li>Provide a reason for the revision</li>
                    <li>Click <strong>Save</strong> to create the revision</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technical-guide" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Module Technical Guide</CardTitle>
              <CardDescription>
                Technical documentation for developers and administrators working with the Payroll module.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">System Architecture</h3>
                <p>
                  The Payroll module is built on a modern web application architecture with the following components:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4 mt-2">
                  <li><strong>Frontend</strong>: Next.js with React components and Tailwind CSS</li>
                  <li><strong>Backend</strong>: Next.js API routes with MongoDB database</li>
                  <li><strong>Authentication</strong>: Custom authentication system</li>
                  <li><strong>State Management</strong>: Zustand stores</li>
                </ul>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Data Models</h3>

                <div className="space-y-2">
                  <h4 className="font-medium">PayrollRun Model</h4>
                  <p className="text-sm text-muted-foreground">
                    Represents a payroll processing cycle for a specific period.
                  </p>
                  <div className="border rounded-md p-4 mt-2">
                    <pre className="text-xs overflow-auto">
{`{
  name: String,               // Name of the payroll run
  description: String,        // Description of the payroll run
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  status: String,             // draft, processing, completed, approved, paid, cancelled
  totalEmployees: Number,     // Total number of employees in the run
  processedEmployees: Number, // Number of employees processed
  totalGrossSalary: Number,   // Total gross salary amount
  totalDeductions: Number,    // Total deductions amount
  totalTax: Number,           // Total tax amount
  totalNetSalary: Number,     // Total net salary amount
  currency: String,           // Currency code (default: MWK)
  notes: String,              // Additional notes
  departments: [ObjectId],    // References to Department model
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId,        // Reference to User model
  approvedBy: ObjectId,       // Reference to User model
  approvedAt: Date,           // Approval timestamp
  processedAt: Date,          // Processing timestamp
  paidAt: Date                // Payment timestamp
}`}
                    </pre>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">PayrollRecord Model</h4>
                  <p className="text-sm text-muted-foreground">
                    Represents an individual employee's payroll record for a specific payroll run.
                  </p>
                  <div className="border rounded-md p-4 mt-2">
                    <pre className="text-xs overflow-auto">
{`{
  payrollRunId: ObjectId,     // Reference to PayrollRun model
  employeeId: ObjectId,       // Reference to Employee model
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  salaryStructureId: ObjectId, // Reference to SalaryStructure model
  currency: String,           // Currency code (default: MWK)
  components: {
    basic: Number,            // Basic salary amount
    allowances: [{            // Array of allowances
      name: String,           // Allowance name
      amount: Number,         // Allowance amount
      isTaxable: Boolean      // Whether the allowance is taxable
    }],
    deductions: [{            // Array of deductions
      name: String,           // Deduction name
      amount: Number,         // Deduction amount
      isStatutory: Boolean    // Whether the deduction is statutory
    }]
  },
  grossSalary: Number,        // Gross salary amount
  totalDeductions: Number,    // Total deductions amount
  totalTax: Number,           // Total tax amount
  netSalary: Number,          // Net salary amount
  status: String,             // draft, processed, paid
  bankAccount: String,        // Bank account number
  paymentMethod: String,      // Payment method
  paymentReference: String,   // Payment reference
  paymentDate: Date,          // Payment date
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId         // Reference to User model
}`}
                    </pre>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Payslip Model</h4>
                  <p className="text-sm text-muted-foreground">
                    Represents a generated payslip for an employee.
                  </p>
                  <div className="border rounded-md p-4 mt-2">
                    <pre className="text-xs overflow-auto">
{`{
  payrollRunId: ObjectId,     // Reference to PayrollRun model
  payrollRecordId: ObjectId,  // Reference to PayrollRecord model
  employeeId: ObjectId,       // Reference to Employee model
  employeeName: String,       // Employee full name
  employeeNumber: String,     // Employee number
  department: String,         // Department name
  position: String,           // Position name
  payPeriod: {
    month: Number,            // Month (1-12)
    year: Number,             // Year
    startDate: Date,          // Start date of the pay period
    endDate: Date             // End date of the pay period
  },
  paymentDetails: {
    grossSalary: Number,      // Gross salary amount
    totalDeductions: Number,  // Total deductions amount
    totalTax: Number,         // Total tax amount
    netSalary: Number,        // Net salary amount
    components: Object,       // Salary components
    currency: String          // Currency code (default: MWK)
  },
  status: String,             // generated, emailed, downloaded
  emailHistory: [{            // Array of email history
    sentAt: Date,             // Email sent timestamp
    sentBy: ObjectId,         // Reference to User model
    sentTo: String,           // Email address
    status: String            // Email status
  }],
  downloadHistory: [{         // Array of download history
    downloadedAt: Date,       // Download timestamp
    downloadedBy: ObjectId    // Reference to User model
  }],
  createdBy: ObjectId,        // Reference to User model
  updatedBy: ObjectId         // Reference to User model
}`}
                    </pre>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">API Endpoints</h3>

                <div className="space-y-2">
                  <h4 className="font-medium">Payroll Runs</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><code>GET /api/payroll/runs</code> - Get all payroll runs</li>
                    <li><code>GET /api/payroll/runs/[id]</code> - Get a specific payroll run</li>
                    <li><code>POST /api/payroll/runs</code> - Create a new payroll run</li>
                    <li><code>PATCH /api/payroll/runs/[id]</code> - Update a payroll run</li>
                    <li><code>POST /api/payroll/runs/[id]/process</code> - Process a payroll run</li>
                    <li><code>POST /api/payroll/runs/[id]/approve</code> - Approve a payroll run</li>
                    <li><code>POST /api/payroll/runs/[id]/cancel</code> - Cancel a payroll run</li>
                    <li><code>GET /api/payroll/runs/[id]/export</code> - Export payroll run data</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Payslips</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><code>GET /api/payroll/payslips</code> - Get all payslips</li>
                    <li><code>GET /api/payroll/payslips/[id]</code> - Get a specific payslip</li>
                    <li><code>POST /api/payroll/runs/[id]/payslips</code> - Generate payslips for a payroll run</li>
                    <li><code>GET /api/payroll/payslips/[id]/download</code> - Download a payslip</li>
                    <li><code>POST /api/payroll/payslips/[id]/email</code> - Email a payslip</li>
                    <li><code>GET /api/payroll/payslips/[id]/print</code> - Print a payslip</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Salary Calculation</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><code>POST /api/payroll/calculate</code> - Calculate salary for an employee</li>
                    <li><code>GET /api/payroll/tax-brackets</code> - Get tax brackets</li>
                  </ul>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Services</h3>

                <div className="space-y-2">
                  <h4 className="font-medium">PayrollService</h4>
                  <p className="text-sm text-muted-foreground">
                    Handles payroll operations including creating and processing payroll runs.
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4 mt-2">
                    <li><code>createPayrollRun(data)</code> - Create a new payroll run</li>
                    <li><code>getPayrollRun(id)</code> - Get a specific payroll run</li>
                    <li><code>updatePayrollRun(id, data)</code> - Update a payroll run</li>
                    <li><code>processPayrollRun(id, options)</code> - Process a payroll run</li>
                    <li><code>approvePayrollRun(id, userId)</code> - Approve a payroll run</li>
                    <li><code>cancelPayrollRun(id, reason)</code> - Cancel a payroll run</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">PayslipService</h4>
                  <p className="text-sm text-muted-foreground">
                    Handles payslip generation and management.
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4 mt-2">
                    <li><code>generatePayslips(payrollRunId)</code> - Generate payslips for a payroll run</li>
                    <li><code>getPayslip(id)</code> - Get a specific payslip</li>
                    <li><code>getPayslips(filters)</code> - Get payslips with filtering</li>
                    <li><code>downloadPayslip(id)</code> - Generate PDF for a payslip</li>
                    <li><code>emailPayslip(id)</code> - Email a payslip to an employee</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">TaxCalculator</h4>
                  <p className="text-sm text-muted-foreground">
                    Handles tax calculations based on Malawi tax brackets.
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4 mt-2">
                    <li><code>calculateTax(grossSalary)</code> - Calculate tax based on gross salary</li>
                    <li><code>calculateNetSalary(grossSalary, deductions)</code> - Calculate net salary</li>
                    <li><code>calculateTaxBreakdown(grossSalary)</code> - Get detailed tax breakdown</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

"use client"

import Link from "next/link"
import { ArrowLeft, Terminal, Database, Code, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function HRAPIDocsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/api">API Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/api/hr">HR API</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/api">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to API Documentation
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Terminal className="h-8 w-8 text-purple-500" />
          <h1 className="text-3xl font-bold text-zinc-900">HR API Documentation</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Complete API reference for HR management operations including employee management, 
          analytics, reporting, and organizational structure.
        </p>
      </div>

      {/* API Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-12">
        <Card className="border-purple-200">
          <CardHeader className="pb-3">
            <Database className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">Employee Management</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">CRUD operations for employee records</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader className="pb-3">
            <Code className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">Analytics</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">HR metrics and workforce analytics</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader className="pb-3">
            <Zap className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">Bulk Operations</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Batch processing and bulk updates</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader className="pb-3">
            <Terminal className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">Reporting</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Report generation and export</p>
          </CardContent>
        </Card>
      </div>

      {/* API Endpoints */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">API Endpoints</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employee Management</CardTitle>
              <CardDescription>Core employee CRUD operations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/employees</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve all employees with filtering and pagination</p>
                  <div className="text-xs text-zinc-500 space-y-1">
                    <div><strong>Query Parameters:</strong></div>
                    <div>• page (number): Page number for pagination</div>
                    <div>• limit (number): Number of records per page</div>
                    <div>• department (string): Filter by department ID</div>
                    <div>• status (string): Filter by employment status</div>
                    <div>• search (string): Search by name or employee ID</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/employees/:id</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve a specific employee by ID</p>
                  <div className="text-xs text-zinc-500">
                    <div><strong>Path Parameters:</strong> id (string) - Employee ID</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/employees</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Create a new employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div><strong>Required Fields:</strong> firstName, lastName, email, department</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-mono">PUT</span>
                    <code className="text-sm">/api/hr/employees/:id</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Update an existing employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports partial updates with validation</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-mono">DELETE</span>
                    <code className="text-sm">/api/hr/employees/:id</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Soft delete an employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Maintains audit trail and referential integrity</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Analytics & Reporting</CardTitle>
              <CardDescription>HR metrics and workforce analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/analytics/overview</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get comprehensive HR analytics overview</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns: headcount, turnover, demographics, trends</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/analytics/departments</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Department-wise analytics and metrics</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: dateRange, metrics (headcount, budget, performance)</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/reports/generate</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Generate custom HR reports</p>
                  <div className="text-xs text-zinc-500">
                    <div>Body: reportType, filters, format (PDF, Excel, CSV)</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>Batch processing and bulk updates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/employees/bulk-import</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk import employees from file</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports: Excel, CSV with validation and error reporting</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/employees/bulk-update</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk update multiple employee records</p>
                  <div className="text-xs text-zinc-500">
                    <div>Body: Array of employee updates with validation</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/employees/export</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Export employee data in various formats</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: format, filters, fields to include</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Authentication */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Authentication</h2>
        <Card>
          <CardHeader>
            <CardTitle>API Authentication</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-zinc-600">
                All HR API endpoints require authentication using JWT tokens.
              </p>
              <div className="bg-zinc-50 rounded-lg p-4">
                <pre className="text-xs text-zinc-700">
{`Authorization: Bearer <your-jwt-token>
Content-Type: application/json`}
                </pre>
              </div>
              <div className="space-y-2 text-sm text-zinc-600">
                <div><strong>Required Permissions:</strong></div>
                <div>• hr:read - Read access to HR data</div>
                <div>• hr:write - Create and update HR records</div>
                <div>• hr:delete - Delete HR records</div>
                <div>• hr:analytics - Access to analytics and reporting</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Response Examples */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Response Examples</h2>
        <Card>
          <CardHeader>
            <CardTitle>Employee Response</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-zinc-50 rounded-lg p-4">
              <pre className="text-xs text-zinc-700 overflow-x-auto">
{`{
  "success": true,
  "data": {
    "_id": "64a7b8c9d1e2f3a4b5c6d7e8",
    "employeeId": "TCM-0001",
    "personalInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890"
    },
    "employment": {
      "department": {
        "_id": "64a7b8c9d1e2f3a4b5c6d7e9",
        "name": "Engineering",
        "code": "ENG"
      },
      "position": "Software Engineer",
      "status": "active",
      "startDate": "2024-01-15T00:00:00.000Z"
    },
    "compensation": {
      "baseSalary": 75000,
      "currency": "USD"
    },
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "version": "1.0"
  }
}`}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Handling */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Error Handling</h2>
        <Card>
          <CardHeader>
            <CardTitle>Error Response Format</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-zinc-50 rounded-lg p-4">
                <pre className="text-xs text-zinc-700">
{`{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid employee data",
    "details": [
      {
        "field": "email",
        "message": "Email already exists"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}`}
                </pre>
              </div>
              <div className="space-y-2 text-sm text-zinc-600">
                <div><strong>Common Error Codes:</strong></div>
                <div>• 400 - Bad Request (validation errors)</div>
                <div>• 401 - Unauthorized (invalid token)</div>
                <div>• 403 - Forbidden (insufficient permissions)</div>
                <div>• 404 - Not Found (resource not found)</div>
                <div>• 500 - Internal Server Error</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/hr/developer-docs">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">HR Developer Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Complete technical documentation</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/api/authentication">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Authentication API</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Authentication and authorization</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/api/employees">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Employee API</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Detailed employee API reference</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

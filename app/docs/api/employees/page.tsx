"use client"

import { EmployeesApiDoc } from "@/components/docs/content/api/employees-api"
import DocsPatternBackground from "@/components/docs/pattern-background"

export default function EmployeesApiPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Employees API
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              API reference for employee management operations
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <EmployeesApiDoc />
        </div>
      </section>
    </div>
  )
}

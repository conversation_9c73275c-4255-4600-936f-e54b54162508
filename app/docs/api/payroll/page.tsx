"use client"

import Link from "next/link"
import { ArrowLeft, Terminal, Calculator, FileText, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function PayrollAPIDocsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/api">API Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/api/payroll">Payroll API</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/api">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to API Documentation
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Calculator className="h-8 w-8 text-emerald-500" />
          <h1 className="text-3xl font-bold text-zinc-900">Payroll API Documentation</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Complete API reference for payroll management operations including payroll runs, 
          salary management, compensation, and accounting integration.
        </p>
      </div>

      {/* API Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-12">
        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <Calculator className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Payroll Runs</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Create and manage payroll processing</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <FileText className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Salary Management</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Employee salary and compensation</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <Zap className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Integration</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Accounting and HR integration</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <Terminal className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Bulk Operations</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Batch processing and imports</p>
          </CardContent>
        </Card>
      </div>

      {/* API Endpoints */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">API Endpoints</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Runs</CardTitle>
              <CardDescription>Payroll processing and management</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/runs</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve all payroll runs with filtering</p>
                  <div className="text-xs text-zinc-500 space-y-1">
                    <div><strong>Query Parameters:</strong></div>
                    <div>• status (string): Filter by run status (draft, processing, completed)</div>
                    <div>• period (string): Filter by pay period</div>
                    <div>• department (string): Filter by department</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/runs</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Create a new payroll run</p>
                  <div className="text-xs text-zinc-500">
                    <div><strong>Required:</strong> payPeriod, employees, runType</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/runs/:id/process</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Process a payroll run</p>
                  <div className="text-xs text-zinc-500">
                    <div>Calculates salaries, deductions, and generates payslips</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/runs/:id/status</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get payroll run processing status</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns progress, completed employees, and any errors</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Employee Salaries</CardTitle>
              <CardDescription>Salary and compensation management</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/employee-salaries</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve employee salary records</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: employee, department, effectiveDate</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/employee-salaries</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Create employee salary record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Required: employee, baseSalary, effectiveDate</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/employee-salaries/bulk-upload</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk upload employee salaries</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports Excel/CSV with validation and error reporting</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Compensation Management</CardTitle>
              <CardDescription>Allowances and deductions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/compensation</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve employee compensation records</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: employee, type (allowance/deduction), status</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/compensation/bulk-import</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk import compensation data</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports allowances and deductions with validation</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/allowances</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get available allowance types</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns allowance types with taxable status</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/deductions</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get available deduction types</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns deduction types and calculation methods</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payslips & Reports</CardTitle>
              <CardDescription>Payslip generation and reporting</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/payslips</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve payslips with filtering</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: payrollRun, employee, period, format</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/payroll/payslips/bulk-download</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk download payslips</p>
                  <div className="text-xs text-zinc-500">
                    <div>Generate ZIP file with multiple payslips</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/payroll/reports/summary</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get payroll summary reports</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query: period, department, format (PDF, Excel)</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Accounting Integration</CardTitle>
              <CardDescription>Payroll-accounting integration endpoints</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/integration/payroll/integration-status</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get payroll-accounting integration status</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns integration health and recent activity</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/integration/payroll/process-completion</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Process payroll completion for accounting</p>
                  <div className="text-xs text-zinc-500">
                    <div>Triggers automatic journal entry creation</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/integration/payroll/budget-variance</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Get budget variance analysis</p>
                  <div className="text-xs text-zinc-500">
                    <div>Returns budget impact and variance metrics</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Response Examples */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Response Examples</h2>
        <Card>
          <CardHeader>
            <CardTitle>Payroll Run Response</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-zinc-50 rounded-lg p-4">
              <pre className="text-xs text-zinc-700 overflow-x-auto">
{`{
  "success": true,
  "data": {
    "_id": "64a7b8c9d1e2f3a4b5c6d7e8",
    "runNumber": "PR-2024-001",
    "payPeriod": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-01-31T23:59:59.999Z",
      "type": "monthly"
    },
    "status": "completed",
    "totalEmployees": 150,
    "processedEmployees": 150,
    "totalGrossPay": 750000,
    "totalDeductions": 125000,
    "totalNetPay": 625000,
    "summary": {
      "departments": [
        {
          "department": "Engineering",
          "employeeCount": 50,
          "totalPay": 300000
        }
      ]
    },
    "integrationStatus": {
      "accounting": "completed",
      "lastSyncAt": "2024-01-31T15:30:00.000Z"
    },
    "createdAt": "2024-01-31T10:00:00.000Z",
    "completedAt": "2024-01-31T15:30:00.000Z"
  }
}`}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Authentication */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Authentication & Permissions</h2>
        <Card>
          <CardHeader>
            <CardTitle>Required Permissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2 text-sm text-zinc-600">
                <div><strong>Payroll Permissions:</strong></div>
                <div>• payroll:read - Read access to payroll data</div>
                <div>• payroll:write - Create and update payroll records</div>
                <div>• payroll:process - Process payroll runs</div>
                <div>• payroll:reports - Generate payroll reports</div>
                <div>• payroll:integration - Access integration endpoints</div>
              </div>
              <div className="bg-zinc-50 rounded-lg p-4">
                <pre className="text-xs text-zinc-700">
{`Authorization: Bearer <your-jwt-token>
Content-Type: application/json`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/payroll-system">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Payroll System Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Complete payroll system documentation</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/api/accounting">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Accounting API</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Accounting integration endpoints</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/bulk-import">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Bulk Operations</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Bulk import and export operations</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

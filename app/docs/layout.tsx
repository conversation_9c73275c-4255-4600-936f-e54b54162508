"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Book, ChevronDown, ChevronRight, Code, FileText, Layers, Server, Settings, Users } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

interface DocsLayoutProps {
  children: React.ReactNode
}

export default function DocsLayout({ children }: DocsLayoutProps) {
  const pathname = usePathname()
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return null
  }

  return (
    <div className="flex min-h-screen flex-col">
      {children}

      {/* Documentation Footer */}
      <footer className="w-full py-12 bg-zinc-900 text-white">
        <div className="container px-4 md:px-6">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <h3 className="text-lg font-medium mb-4">Documentation</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/docs/getting-started" className="text-zinc-400 hover:text-white transition-colors">
                    Getting Started
                  </Link>
                </li>
                <li>
                  <Link href="/docs/authentication" className="text-zinc-400 hover:text-white transition-colors">
                    Authentication
                  </Link>
                </li>
                <li>
                  <Link href="/docs/employee-management" className="text-zinc-400 hover:text-white transition-colors">
                    Employee Management
                  </Link>
                </li>
                <li>
                  <Link href="/docs/department-management" className="text-zinc-400 hover:text-white transition-colors">
                    Department Management
                  </Link>
                </li>
                <li>
                  <Link href="/docs/bulk-import" className="text-zinc-400 hover:text-white transition-colors">
                    Bulk Import
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">API Reference</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/docs/api/authentication" className="text-zinc-400 hover:text-white transition-colors">
                    Authentication API
                  </Link>
                </li>
                <li>
                  <Link href="/docs/api/employees" className="text-zinc-400 hover:text-white transition-colors">
                    Employees API
                  </Link>
                </li>
                <li>
                  <Link href="/docs/api/departments" className="text-zinc-400 hover:text-white transition-colors">
                    Departments API
                  </Link>
                </li>
                <li>
                  <Link href="/docs/api/finance" className="text-zinc-400 hover:text-white transition-colors">
                    Finance API
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Components</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/docs/components/ui" className="text-zinc-400 hover:text-white transition-colors">
                    UI Components
                  </Link>
                </li>
                <li>
                  <Link href="/docs/components/forms" className="text-zinc-400 hover:text-white transition-colors">
                    Form Components
                  </Link>
                </li>
                <li>
                  <Link href="/docs/components/tables" className="text-zinc-400 hover:text-white transition-colors">
                    Table Components
                  </Link>
                </li>
                <li>
                  <Link href="/docs/components/charts" className="text-zinc-400 hover:text-white transition-colors">
                    Chart Components
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-zinc-400 hover:text-white transition-colors">
                    Main Dashboard
                  </Link>
                </li>
                <li>
                  <Link href="/login" className="text-zinc-400 hover:text-white transition-colors">
                    Login
                  </Link>
                </li>
                <li>
                  <Link href="https://github.com/Winstonmhango/hrimpackhrmanager" className="text-zinc-400 hover:text-white transition-colors">
                    GitHub Repository
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <Separator className="my-8 bg-zinc-800" />
          <div className="text-center text-zinc-500 text-sm">
            &copy; {new Date().getFullYear()} HR Impact Management System. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  )
}

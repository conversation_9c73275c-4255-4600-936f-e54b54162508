"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Users, 
  DollarSign, 
  Clock,
  Zap,
  Shield,
  FileText,
  ArrowRight
} from "lucide-react"

export default function PayrollBulkOperationsUserGuide() {
  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">Payroll Bulk Operations</h1>
          <p className="text-xl text-muted-foreground">
            Complete user guide for processing, approving, and paying multiple payroll runs efficiently
          </p>
          <div className="flex justify-center gap-2">
            <Badge variant="secondary">User Guide</Badge>
            <Badge variant="outline">Payroll Module</Badge>
            <Badge variant="outline">Bulk Operations</Badge>
          </div>
        </div>

        {/* Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              What are Bulk Operations?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Payroll Bulk Operations allow you to process, approve, and pay multiple payroll runs simultaneously, 
              saving significant time and reducing manual work. Instead of handling each payroll run individually, 
              you can select multiple runs and perform actions on all of them at once.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-semibold">Bulk Process</h3>
                <p className="text-sm text-muted-foreground">Process multiple draft payroll runs</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-semibold">Bulk Approve</h3>
                <p className="text-sm text-muted-foreground">Approve multiple completed runs</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-semibold">Bulk Pay</h3>
                <p className="text-sm text-muted-foreground">Mark multiple approved runs as paid</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Access Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Access Requirements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Permission Required</AlertTitle>
              <AlertDescription>
                Bulk operations require specific role permissions. Contact your system administrator if you cannot access these features.
              </AlertDescription>
            </Alert>
            
            <div className="mt-4 space-y-3">
              <div>
                <h4 className="font-semibold">Bulk Processing</h4>
                <p className="text-sm text-muted-foreground">Super Admin, System Admin, Finance Director, HR Director, Finance Manager</p>
              </div>
              <div>
                <h4 className="font-semibold">Bulk Approval</h4>
                <p className="text-sm text-muted-foreground">Super Admin, System Admin, Finance Director, HR Director</p>
              </div>
              <div>
                <h4 className="font-semibold">Bulk Payment</h4>
                <p className="text-sm text-muted-foreground">Super Admin, System Admin, Finance Director</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>How to access and use bulk operations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">1</div>
                <div>
                  <h4 className="font-semibold">Navigate to Bulk Operations</h4>
                  <p className="text-sm text-muted-foreground">
                    Go to <strong>Payroll → Bulk Operations</strong> in the sidebar menu
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">2</div>
                <div>
                  <h4 className="font-semibold">Choose Operation Type</h4>
                  <p className="text-sm text-muted-foreground">
                    Select from three tabs: <strong>Process Runs</strong>, <strong>Approve Runs</strong>, or <strong>Pay Runs</strong>
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">3</div>
                <div>
                  <h4 className="font-semibold">Select Payroll Runs</h4>
                  <p className="text-sm text-muted-foreground">
                    Use checkboxes to select individual runs or "Select All" for bulk selection
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">4</div>
                <div>
                  <h4 className="font-semibold">Execute Operation</h4>
                  <p className="text-sm text-muted-foreground">
                    Click the action button to start the bulk operation
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Operations */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Detailed Operation Guides</h2>
          
          {/* Bulk Processing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                Bulk Processing
              </CardTitle>
              <CardDescription>Process multiple draft payroll runs simultaneously</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>What it does</AlertTitle>
                <AlertDescription>
                  Converts draft payroll runs to completed status by calculating salaries for all employees in the selected runs.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-3">
                <h4 className="font-semibold">Step-by-Step Process:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Navigate to the <strong>Process Runs</strong> tab</li>
                  <li>Review available draft payroll runs in the table</li>
                  <li>Select runs using checkboxes (only draft status runs are eligible)</li>
                  <li>Click <strong>"Process Selected"</strong> button</li>
                  <li>Monitor progress through the background processing system</li>
                  <li>Review results showing processed, failed, and skipped runs</li>
                </ol>
              </div>
              
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Important Notes</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Only payroll runs in "draft" status can be processed</li>
                    <li>Processing runs in other statuses will be skipped</li>
                    <li>Background processing may take time for large employee counts</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Bulk Approval */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Bulk Approval
              </CardTitle>
              <CardDescription>Approve multiple completed payroll runs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>What it does</AlertTitle>
                <AlertDescription>
                  Approves completed payroll runs, making them ready for payment processing.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-3">
                <h4 className="font-semibold">Step-by-Step Process:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Navigate to the <strong>Approve Runs</strong> tab</li>
                  <li>Review completed payroll runs awaiting approval</li>
                  <li>Select runs using checkboxes (only completed status runs are eligible)</li>
                  <li>Click <strong>"Approve Selected"</strong> button</li>
                  <li>Confirm the approval action</li>
                  <li>Review results showing approved, failed, and skipped runs</li>
                </ol>
              </div>
              
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Best Practices</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Review payroll calculations before bulk approval</li>
                    <li>Verify employee counts and total amounts</li>
                    <li>Ensure all required approvals are in place</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Bulk Payment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-purple-600" />
                Bulk Payment
              </CardTitle>
              <CardDescription>Mark multiple approved payroll runs as paid</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>What it does</AlertTitle>
                <AlertDescription>
                  Marks approved payroll runs as paid, indicating that payments have been processed and sent to employees.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-3">
                <h4 className="font-semibold">Step-by-Step Process:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Navigate to the <strong>Pay Runs</strong> tab</li>
                  <li>Review approved payroll runs ready for payment</li>
                  <li>Check the total payment amount in the confirmation box</li>
                  <li>Select runs using checkboxes (only approved status runs are eligible)</li>
                  <li>Click <strong>"Pay Selected"</strong> button</li>
                  <li>Confirm the payment action and total amount</li>
                  <li>Review results showing paid, failed, and skipped runs</li>
                </ol>
              </div>
              
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Critical Warning</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Ensure actual payments are processed before marking as paid</li>
                    <li>Verify total amounts match your payment system</li>
                    <li>This action indicates payments have been sent to employees</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>

        {/* Understanding Results */}
        <Card>
          <CardHeader>
            <CardTitle>Understanding Results</CardTitle>
            <CardDescription>How to interpret bulk operation outcomes</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold text-green-600">Processed/Approved/Paid</h4>
                <p className="text-sm text-muted-foreground">Successfully completed operations</p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold text-yellow-600">Skipped</h4>
                <p className="text-sm text-muted-foreground">Items not eligible for the operation (wrong status)</p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold text-red-600">Failed</h4>
                <p className="text-sm text-muted-foreground">Operations that encountered errors</p>
              </div>
            </div>
            
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Result Details</AlertTitle>
              <AlertDescription>
                Each operation provides detailed results showing exactly what happened to each payroll run, 
                including specific error messages for failed operations and reasons for skipped items.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Troubleshooting */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting</CardTitle>
            <CardDescription>Common issues and solutions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">No eligible runs showing</h4>
                <p className="text-sm text-muted-foreground">
                  Check that payroll runs are in the correct status for the operation you want to perform.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Operation button is disabled</h4>
                <p className="text-sm text-muted-foreground">
                  Ensure you have selected at least one payroll run and have the required permissions.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Some runs were skipped</h4>
                <p className="text-sm text-muted-foreground">
                  Review the results to see why runs were skipped - usually due to incorrect status.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Processing takes too long</h4>
                <p className="text-sm text-muted-foreground">
                  Large payroll runs with many employees may take time. Check the progress indicators and wait for completion.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle>Best Practices</CardTitle>
            <CardDescription>Tips for efficient bulk operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold">Before Operations</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Review payroll run details before bulk processing</li>
                  <li>• Verify employee data is complete and accurate</li>
                  <li>• Check department and salary structure assignments</li>
                  <li>• Ensure sufficient system resources for large operations</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold">During Operations</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Monitor progress indicators for long-running operations</li>
                  <li>• Don't navigate away during processing</li>
                  <li>• Review results carefully before proceeding</li>
                  <li>• Address any failed operations before continuing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>For technical support or additional questions, contact your system administrator.</p>
        </div>
      </div>
    </div>
  )
}

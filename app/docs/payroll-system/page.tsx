import { Metadata } from 'next'
import { ArrowLeft, DollarSign, Users, BarChart3, CheckCircle, AlertTriangle } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'Payroll User Guide | TCM Enterprise Suite',
  description: 'Comprehensive payroll management guide with TCM role integration, salary bands, and role-based validation.',
}

export default function PayrollSystemPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          {/* Back Link */}
          <Link
            href="/docs"
            className="inline-flex items-center text-sm font-medium text-emerald-600 hover:text-emerald-700 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Documentation
          </Link>

          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-zinc-900">Payroll User Guide</h1>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">
                  🎉 Enhanced with TCM Role Integration
                </Badge>
                <Badge variant="outline">Role-Based Salary Management</Badge>
              </div>
            </div>
          </div>

          <p className="text-lg text-zinc-600 mb-8">
            Payroll management now includes role-based salary validation, automatic salary suggestions, and TCM code-based salary bands for structured compensation management.
          </p>
        </div>

        {/* Enhancement Banner */}
        <Card className="mb-8 border-emerald-200 bg-emerald-50">
          <CardHeader>
            <CardTitle className="text-emerald-800 flex items-center gap-2">
              🎉 Enhanced with TCM Role Integration!
            </CardTitle>
            <CardDescription className="text-emerald-700">
              <strong>New Feature:</strong> Payroll management now includes role-based salary validation, automatic salary suggestions, and TCM code-based salary bands for structured compensation management.
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Getting Started */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🚀 Getting Started</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Accessing Payroll Management</CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal list-inside space-y-2 text-sm text-zinc-600">
                  <li>Login to the TCM Enterprise Suite</li>
                  <li>Navigate to: <strong>Payroll</strong></li>
                  <li>Choose your module:
                    <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                      <li>Salary Structures: Define compensation frameworks</li>
                      <li>Employee Salaries: Manage individual compensation</li>
                      <li>Salary Bands: TCM role-based ranges 🆕</li>
                      <li>Payroll Generation: Process monthly payrolls</li>
                      <li>Reports: Payroll analytics and reporting</li>
                    </ul>
                  </li>
                </ol>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Payroll Dashboard</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Payroll Statistics:</strong> Total employees, salary costs, pending approvals</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Role-Based Analytics:</strong> Salary distribution by TCM role 🆕</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Recent Activities:</strong> Latest salary changes and approvals</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Quick Actions:</strong> Fast access to common payroll tasks</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* TCM Salary Bands */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">💰 Salary Band Management 🆕</h2>
          
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Understanding TCM Salary Bands</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">What are Salary Bands?</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-zinc-600">
                    <li>Predefined salary ranges for each TCM role code</li>
                    <li>Ensure consistent compensation across similar roles</li>
                    <li>Provide guidelines for salary negotiations and adjustments</li>
                    <li>Support organizational salary equity</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">TCM Salary Band Structure</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="p-3 bg-emerald-50 rounded-lg">
                    <div className="font-semibold text-emerald-800">TCM 1 (Executive)</div>
                    <div className="text-sm text-emerald-600">MWK 2,500,000 - 3,500,000</div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-semibold text-blue-800">TCM 2 (Director)</div>
                    <div className="text-sm text-blue-600">MWK 2,000,000 - 2,800,000</div>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="font-semibold text-purple-800">TCM 3 (Manager)</div>
                    <div className="text-sm text-purple-600">MWK 1,500,000 - 2,200,000</div>
                  </div>
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <div className="font-semibold text-orange-800">TCM 4 (Senior Officer)</div>
                    <div className="text-sm text-orange-600">MWK 1,200,000 - 1,800,000</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="p-3 bg-teal-50 rounded-lg">
                    <div className="font-semibold text-teal-800">TCM 5 (Officer)</div>
                    <div className="text-sm text-teal-600">MWK 800,000 - 1,400,000</div>
                  </div>
                  <div className="p-3 bg-indigo-50 rounded-lg">
                    <div className="font-semibold text-indigo-800">TCM 7 (Assistant)</div>
                    <div className="text-sm text-indigo-600">MWK 600,000 - 1,000,000</div>
                  </div>
                  <div className="p-3 bg-pink-50 rounded-lg">
                    <div className="font-semibold text-pink-800">TCM 9 (Support)</div>
                    <div className="text-sm text-pink-600">MWK 400,000 - 700,000</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-semibold text-gray-800">TCM 10-12 (Specialized)</div>
                    <div className="text-sm text-gray-600">MWK 250,000 - 600,000</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Salary Structure Management */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🏗 Salary Structure Management</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Creating Salary Structures</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold mb-1">Step 1: Basic Information</h4>
                    <ul className="list-disc list-inside text-sm text-zinc-600">
                      <li>Structure name (e.g., "TCM Officer Level")</li>
                      <li>Description and currency (MWK)</li>
                      <li>Effective date</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Step 2: Role Assignment 🆕</h4>
                    <ul className="list-disc list-inside text-sm text-zinc-600">
                      <li>Select applicable roles from TCM role list</li>
                      <li>Automatic validation against role salary bands</li>
                      <li>Warning alerts for mismatched salary ranges</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Role-Based Salary Validation 🆕</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold mb-1">Validation Results</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-emerald-500" />
                        <span><strong>Within Range:</strong> Salary complies with role band</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        <span><strong>Warning:</strong> Salary slightly outside range</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span><strong>Error:</strong> Salary significantly outside range</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Employee Salary Management */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">👤 Employee Salary Management</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Assigning Salaries to Employees</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold mb-1">Individual Assignment</h4>
                    <ol className="list-decimal list-inside text-sm text-zinc-600 space-y-1">
                      <li>Navigate to Payroll → Employee Salaries</li>
                      <li>Select employee</li>
                      <li>Choose salary structure</li>
                      <li>Role-based validation automatically applied 🆕</li>
                      <li>Review salary components and save</li>
                    </ol>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Role-Based Suggestions 🆕</h4>
                    <ul className="list-disc list-inside text-sm text-zinc-600">
                      <li>System suggests appropriate salary structures</li>
                      <li>Automatic salary range validation</li>
                      <li>Recommended amounts within role bands</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Salary Revision Process</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold mb-1">Creating Revisions</h4>
                    <ol className="list-decimal list-inside text-sm text-zinc-600 space-y-1">
                      <li>Select employee for salary revision</li>
                      <li>Choose new salary structure or amount</li>
                      <li>Role validation checks new salary 🆕</li>
                      <li>Document revision reason</li>
                      <li>Set effective date and submit for approval</li>
                    </ol>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Approval Workflow</h4>
                    <ul className="list-disc list-inside text-sm text-zinc-600">
                      <li>Automatic approval for salaries within role bands</li>
                      <li>Manager approval for minor exceptions</li>
                      <li>HR Director approval for major exceptions</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Reporting and Analytics */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">📈 Reporting and Analytics</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Standard Payroll Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><strong>Payroll Summary:</strong> Total costs by period, employee count</li>
                  <li><strong>Employee Salary Reports:</strong> Individual statements and history</li>
                  <li><strong>Department Breakdown:</strong> Payroll costs by department</li>
                  <li><strong>Tax and Deductions:</strong> Summary of all deductions</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Role-Based Analytics 🆕</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><strong>Salary Equity Analysis:</strong> Distribution by TCM role</li>
                  <li><strong>Role Distribution:</strong> Employee count by TCM code</li>
                  <li><strong>Salary Progression:</strong> Career advancement tracking</li>
                  <li><strong>Budget Analysis:</strong> Payroll costs by role level</li>
                  <li><strong>Compliance Monitoring:</strong> Salary equity tracking</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <Link
            href="/dashboard/payroll"
            className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
          >
            <DollarSign className="h-4 w-4 mr-2" />
            Access Payroll System
          </Link>
          <Link
            href="/docs/role-management"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Role Management Guide
          </Link>
          <Link
            href="/docs/hr-system"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            HR System Guide
          </Link>
          <Link
            href="/docs/employees"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Employee Management
          </Link>
        </div>

        {/* Support Section */}
        <Card className="mt-8 border-zinc-200">
          <CardHeader>
            <CardTitle className="text-xl">📞 Support and Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-3">Related Guides</h4>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><Link href="/docs/role-management" className="text-emerald-600 hover:text-emerald-700">Role Management User Guide</Link></li>
                  <li><Link href="/docs/employees" className="text-emerald-600 hover:text-emerald-700">Employee Management User Guide</Link></li>
                  <li><Link href="/docs/hr-system" className="text-emerald-600 hover:text-emerald-700">HR System User Guide</Link></li>
                  <li><Link href="/docs/bulk-import" className="text-emerald-600 hover:text-emerald-700">Bulk Import System Guide</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Getting Help</h4>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><strong>Payroll Administrator:</strong> Technical payroll issues</li>
                  <li><strong>HR Department:</strong> Policy and process questions</li>
                  <li><strong>Finance Department:</strong> Budget and compliance matters</li>
                  <li><strong>System Administrator:</strong> Technical system issues</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// app/docs/payroll-runs/recent-updates/page.tsx
"use client"

import Link from "next/link"
import { ArrowRight, RefreshCw, Zap, CheckCircle, AlertTriangle, DollarSign, FileText, Monitor, Shield, Calendar } from "lucide-react"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function PayrollRunsRecentUpdatesPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-blue-900 to-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Payroll Runs Recent Updates
            </h1>
            <p className="max-w-[700px] text-blue-100 md:text-xl/relaxed">
              Latest enhancements, features, and improvements to the payroll run system
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1 max-w-4xl">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-6">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs">Payroll Runs</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs/recent-updates">Recent Updates</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="mb-8">
                <h2 className="text-2xl font-bold tracking-tight mb-4">Recent Updates & Enhancements</h2>
                <p className="text-muted-foreground mb-6">
                  Stay up-to-date with the latest improvements to the payroll run system. This page documents 
                  all recent enhancements, new features, bug fixes, and system optimizations.
                </p>
              </div>

              {/* Latest Release */}
              <Card className="mb-8 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Latest Release: Enhanced Processing v2.0
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">🚀 Major Update</Badge>
                  </CardTitle>
                  <CardDescription>Released: December 2024</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Major overhaul of the payroll processing system with real-time progress tracking, 
                      enhanced visual feedback, and improved error handling capabilities.
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-semibold text-sm">🎯 Key Features</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Real-time progress tracking</li>
                          <li>• Visual employee status indicators</li>
                          <li>• Enhanced error handling</li>
                          <li>• Resumable processing</li>
                        </ul>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-semibold text-sm">🔧 Technical Improvements</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Optimized processing algorithms</li>
                          <li>• Better memory management</li>
                          <li>• Enhanced API responses</li>
                          <li>• Improved error recovery</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Update Timeline */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-emerald-600" />
                    Update Timeline
                  </CardTitle>
                  <CardDescription>Chronological list of recent updates and improvements</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* December 2024 */}
                    <div className="border-l-4 border-blue-500 pl-6">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold">December 2024</h4>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">v2.0</Badge>
                      </div>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4 bg-blue-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Zap className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">Enhanced Processing System</span>
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">🆕 New</Badge>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Implemented real-time progress tracking with visual feedback</li>
                            <li>• Added employee status indicators (pending, processing, completed, error)</li>
                            <li>• Enhanced processing modal with live updates</li>
                            <li>• Improved user experience with animated progress bars</li>
                          </ul>
                        </div>

                        <div className="border rounded-lg p-4 bg-green-50">
                          <div className="flex items-center gap-2 mb-2">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            <span className="font-medium">Accounting Integration</span>
                            <Badge variant="outline" className="bg-green-100 text-green-800">🔗 Integration</Badge>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Added PayrollAccountingPanel for seamless integration</li>
                            <li>• Implemented journal entry creation and posting</li>
                            <li>• Bank account management for payment processing</li>
                            <li>• Fixed API endpoint compatibility issues</li>
                          </ul>
                        </div>

                        <div className="border rounded-lg p-4 bg-purple-50">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-purple-600" />
                            <span className="font-medium">PDF Generation Improvements</span>
                            <Badge variant="outline" className="bg-purple-100 text-purple-800">🔧 Fix</Badge>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Implemented robust PDF generation with fallback system</li>
                            <li>• Added error handling for PDF creation failures</li>
                            <li>• Improved payslip template rendering</li>
                            <li>• Enhanced PDF download and email distribution</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* November 2024 */}
                    <div className="border-l-4 border-green-500 pl-6">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold">November 2024</h4>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">v1.8</Badge>
                      </div>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Shield className="h-4 w-4 text-orange-600" />
                            <span className="font-medium">Error Handling Enhancements</span>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Implemented comprehensive error service</li>
                            <li>• Added detailed error logging and tracking</li>
                            <li>• Enhanced error recovery mechanisms</li>
                            <li>• Improved error messaging for users</li>
                          </ul>
                        </div>

                        <div className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Monitor className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">UI/UX Improvements</span>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Redesigned payroll run management interface</li>
                            <li>• Added status badges and visual indicators</li>
                            <li>• Improved responsive design for mobile devices</li>
                            <li>• Enhanced loading states and feedback</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* October 2024 */}
                    <div className="border-l-4 border-purple-500 pl-6">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold">October 2024</h4>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">v1.7</Badge>
                      </div>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="font-medium">Performance Optimizations</span>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Optimized payroll calculation algorithms</li>
                            <li>• Improved database query performance</li>
                            <li>• Enhanced memory usage for large employee datasets</li>
                            <li>• Reduced processing time by 40%</li>
                          </ul>
                        </div>

                        <div className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <RefreshCw className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">Resumable Processing</span>
                          </div>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Added ability to resume interrupted payroll processing</li>
                            <li>• Implemented processing state persistence</li>
                            <li>• Enhanced recovery from system interruptions</li>
                            <li>• Improved handling of network connectivity issues</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Breaking Changes */}
              <Card className="mb-8 border-red-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    Breaking Changes & Migration Notes
                  </CardTitle>
                  <CardDescription>Important changes that may affect existing implementations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h4 className="font-semibold text-red-900 mb-2">API Response Structure Changes (v2.0)</h4>
                      <p className="text-sm text-red-800 mb-2">
                        The payroll runs API response structure has been updated to improve consistency:
                      </p>
                      <div className="bg-white rounded p-3 text-xs">
                        <div className="mb-2">
                          <strong>Before:</strong>
                          <pre className="text-red-600">{`{ success: true, data: { docs: [...], pagination: {...} } }`}</pre>
                        </div>
                        <div>
                          <strong>After:</strong>
                          <pre className="text-green-600">{`{ success: true, data: { payrollRuns: [...], pagination: {...} } }`}</pre>
                        </div>
                      </div>
                      <p className="text-sm text-red-800 mt-2">
                        <strong>Migration:</strong> Update client code to use <code>data.payrollRuns</code> instead of <code>data.docs</code>
                      </p>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-semibold text-yellow-900 mb-2">Status Value Changes (v2.0)</h4>
                      <p className="text-sm text-yellow-800 mb-2">
                        Payroll run status values have been standardized:
                      </p>
                      <ul className="text-sm text-yellow-800 space-y-1">
                        <li>• <code>processed</code> → <code>completed</code></li>
                        <li>• All other status values remain unchanged</li>
                      </ul>
                      <p className="text-sm text-yellow-800 mt-2">
                        <strong>Migration:</strong> Update any hardcoded status checks to use the new values
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Features */}
              <Card className="mb-8 border-emerald-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <RefreshCw className="h-5 w-5 text-emerald-600" />
                    Upcoming Features
                  </CardTitle>
                  <CardDescription>Planned enhancements and new features</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4 bg-emerald-50">
                      <h4 className="font-semibold mb-2">Q1 2025 - Advanced Analytics</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Payroll cost analysis and trending</li>
                        <li>• Department-wise payroll comparisons</li>
                        <li>• Predictive payroll budgeting</li>
                        <li>• Advanced reporting dashboard</li>
                      </ul>
                    </div>

                    <div className="border rounded-lg p-4 bg-blue-50">
                      <h4 className="font-semibold mb-2">Q2 2025 - Mobile Application</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Mobile app for payroll approvals</li>
                        <li>• Push notifications for payroll status</li>
                        <li>• Mobile payslip access for employees</li>
                        <li>• Offline payroll review capabilities</li>
                      </ul>
                    </div>

                    <div className="border rounded-lg p-4 bg-purple-50">
                      <h4 className="font-semibold mb-2">Q3 2025 - AI-Powered Features</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Automated anomaly detection in payroll</li>
                        <li>• Smart payroll scheduling recommendations</li>
                        <li>• AI-assisted error resolution</li>
                        <li>• Predictive processing time estimates</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between items-center pt-8 border-t">
                <Button variant="outline" asChild>
                  <Link href="/docs/payroll-runs/technical-guide">
                    ← Technical Guide
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/docs/payroll-runs">
                    Back to Payroll Runs →
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

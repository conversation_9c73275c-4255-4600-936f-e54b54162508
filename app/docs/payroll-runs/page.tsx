// app/docs/payroll-runs/page.tsx
"use client"

import Link from "next/link"
import { ArrowRight, Book, FileText, Calculator, Zap, Users, Settings, Code, RefreshCw, DollarSign, CheckCircle } from "lucide-react"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function PayrollRunsGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                Payroll Processing
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Payroll Runs Documentation
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Complete guide to payroll run processing with enhanced features, real-time tracking, and accounting integration
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-6">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs">Payroll Runs</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="mb-8">
                <h2 className="text-2xl font-bold tracking-tight mb-4">Payroll Runs Documentation</h2>
                <p className="text-muted-foreground mb-6">
                  Comprehensive documentation for the enhanced payroll run system featuring real-time progress tracking, 
                  visual feedback, accounting integration, and robust error handling. Choose from user guides for 
                  day-to-day operations or technical guides for implementation details.
                </p>
                
                {/* Latest Updates Banner */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-8">
                  <div className="flex items-center gap-3 mb-3">
                    <Zap className="h-6 w-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-blue-900">Latest Enhancements</h3>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">🆕 Updated</Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Enhanced Processing with real-time progress tracking</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Visual feedback with employee status indicators</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Robust PDF generation with fallback system</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Accounting system integration</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Resumable payroll processing</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Enhanced error handling and recovery</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Documentation Cards */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
                <Link href="/docs/payroll-runs/user-guide">
                  <Card className="h-full transition-all hover:shadow-md border-emerald-200">
                    <CardHeader>
                      <Book className="h-8 w-8 text-emerald-500 mb-2" />
                      <CardTitle>User Guide</CardTitle>
                      <CardDescription>For HR teams and payroll administrators</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Complete step-by-step guide for creating, processing, and managing payroll runs with 
                        the enhanced processing features and visual feedback system.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Payroll run creation and setup</div>
                        <div>• Enhanced processing workflow</div>
                        <div>• Approval and payment processes</div>
                        <div>• Troubleshooting and best practices</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                        Read User Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/payroll-runs/technical-guide">
                  <Card className="h-full transition-all hover:shadow-md border-purple-200">
                    <CardHeader>
                      <Code className="h-8 w-8 text-purple-500 mb-2" />
                      <CardTitle>Technical Guide</CardTitle>
                      <CardDescription>For developers and system administrators</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Technical documentation covering API endpoints, enhanced processing architecture, 
                        real-time progress tracking implementation, and system integration details.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• API endpoints and schemas</div>
                        <div>• Enhanced processing architecture</div>
                        <div>• Real-time progress tracking</div>
                        <div>• Error handling and recovery</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                        View Technical Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/payroll-runs/recent-updates">
                  <Card className="h-full transition-all hover:shadow-md border-blue-200">
                    <CardHeader>
                      <RefreshCw className="h-8 w-8 text-blue-500 mb-2" />
                      <CardTitle>Recent Updates</CardTitle>
                      <CardDescription>Latest features and improvements</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Detailed changelog of recent enhancements including enhanced processing, 
                        visual feedback improvements, and accounting integration features.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Enhanced processing features</div>
                        <div>• Visual feedback improvements</div>
                        <div>• Accounting integration</div>
                        <div>• Bug fixes and optimizations</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                        View Updates <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>

              {/* Specialized Guides */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold mb-6">Specialized Guides</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  <Link href="/docs/payroll-runs/enhanced-processing">
                    <Card className="h-full transition-all hover:shadow-md border-blue-200 bg-blue-50">
                      <CardHeader>
                        <Zap className="h-6 w-6 text-blue-500 mb-2" />
                        <CardTitle className="text-lg flex items-center gap-2">
                          Enhanced Processing
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">🚀 New</Badge>
                        </CardTitle>
                        <CardDescription>Real-time progress tracking and visual feedback</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Complete guide to the enhanced processing system with real-time progress tracking, 
                          visual employee status indicators, and improved user experience.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                          Learn Enhanced Processing <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>

                  <Link href="/docs/payroll-runs/accounting-integration">
                    <Card className="h-full transition-all hover:shadow-md border-green-200 bg-green-50">
                      <CardHeader>
                        <DollarSign className="h-6 w-6 text-green-500 mb-2" />
                        <CardTitle className="text-lg flex items-center gap-2">
                          Accounting Integration
                          <Badge variant="secondary" className="bg-green-100 text-green-800">🔗 Integration</Badge>
                        </CardTitle>
                        <CardDescription>Payroll to accounting system workflow</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Comprehensive guide for integrating payroll runs with the accounting system, 
                          including journal entries, bank accounts, and payment processing.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-green-600">
                          View Integration Guide <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>

                  <Link href="/docs/payroll-runs/troubleshooting">
                    <Card className="h-full transition-all hover:shadow-md border-orange-200 bg-orange-50">
                      <CardHeader>
                        <Settings className="h-6 w-6 text-orange-500 mb-2" />
                        <CardTitle className="text-lg">Troubleshooting Guide</CardTitle>
                        <CardDescription>Common issues and solutions</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Comprehensive troubleshooting guide covering common issues, error resolution, 
                          PDF generation problems, and system recovery procedures.
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="gap-1 text-orange-600">
                          View Troubleshooting <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>
                </div>
              </div>

              {/* Related Documentation */}
              <div>
                <h3 className="text-xl font-semibold mb-6">Related Documentation</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  <Link href="/docs/employee-payroll">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Users className="h-6 w-6 text-emerald-500 mb-2" />
                        <CardTitle className="text-lg">Employee & Payroll</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          General employee and payroll management documentation
                        </p>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/docs/accounting">
                    <Card className="h-full transition-all hover:shadow-md">
                      <CardHeader>
                        <Calculator className="h-6 w-6 text-emerald-500 mb-2" />
                        <CardTitle className="text-lg">Accounting System</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-zinc-600">
                          Accounting system documentation and integration guides
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

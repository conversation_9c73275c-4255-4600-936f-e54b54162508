// app/docs/payroll-runs/user-guide/page.tsx
"use client"

import Link from "next/link"
import { ArrowRight, Calendar, Users, Calculator, CheckCircle, Clock, DollarSign, AlertTriangle, Play, Zap, FileText, CreditCard, RefreshCw } from "lucide-react"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function PayrollRunsUserGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Payroll Runs User Guide
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Complete step-by-step guide for HR teams and payroll administrators
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1 max-w-4xl">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-6">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs">Payroll Runs</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs/user-guide">User Guide</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="mb-8">
                <h2 className="text-2xl font-bold tracking-tight mb-4">Payroll Runs User Guide</h2>
                <p className="text-muted-foreground mb-6">
                  This comprehensive guide covers everything you need to know about creating, processing, and managing
                  payroll runs in the TCM Enterprise Suite. Follow these step-by-step instructions to efficiently
                  handle payroll processing with the enhanced features.
                </p>
              </div>

              {/* Quick Start Section */}
              <Card className="mb-8 border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Quick Start Guide
                  </CardTitle>
                  <CardDescription>Get started with payroll runs in 5 simple steps</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <span className="text-blue-600 font-semibold">1</span>
                      </div>
                      <h5 className="font-medium text-sm">Create Run</h5>
                      <p className="text-xs text-muted-foreground">Set up pay period</p>
                    </div>
                    <div className="text-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <span className="text-blue-600 font-semibold">2</span>
                      </div>
                      <h5 className="font-medium text-sm">Select Employees</h5>
                      <p className="text-xs text-muted-foreground">Choose who to include</p>
                    </div>
                    <div className="text-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <span className="text-blue-600 font-semibold">3</span>
                      </div>
                      <h5 className="font-medium text-sm">Process</h5>
                      <p className="text-xs text-muted-foreground">Calculate salaries</p>
                    </div>
                    <div className="text-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <span className="text-blue-600 font-semibold">4</span>
                      </div>
                      <h5 className="font-medium text-sm">Review & Approve</h5>
                      <p className="text-xs text-muted-foreground">Verify calculations</p>
                    </div>
                    <div className="text-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <span className="text-blue-600 font-semibold">5</span>
                      </div>
                      <h5 className="font-medium text-sm">Pay</h5>
                      <p className="text-xs text-muted-foreground">Process payments</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payroll Run States */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <RefreshCw className="h-5 w-5 text-emerald-600" />
                    Understanding Payroll Run States
                  </CardTitle>
                  <CardDescription>Learn about the different stages of payroll processing</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
                          <span className="text-sm text-muted-foreground">Initial State</span>
                        </div>
                        <p className="text-sm">Payroll run is created but not yet processed. Can be edited or deleted.</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 border rounded-lg border-blue-200 bg-blue-50">
                      <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <Calculator className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>
                          <span className="text-sm text-muted-foreground">Enhanced Processing</span>
                        </div>
                        <p className="text-sm">System is calculating salaries with real-time progress tracking and visual feedback.</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 border rounded-lg border-green-200 bg-green-50">
                      <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
                          <span className="text-sm text-muted-foreground">Ready for Review</span>
                        </div>
                        <p className="text-sm">Calculations complete. Ready for review and approval.</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 border rounded-lg border-purple-200 bg-purple-50">
                      <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="bg-purple-100 text-purple-800">Approved</Badge>
                          <span className="text-sm text-muted-foreground">Ready for Payment</span>
                        </div>
                        <p className="text-sm">Approved by authorized personnel. Ready for payment processing.</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 border rounded-lg border-emerald-200 bg-emerald-50">
                      <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center">
                        <CreditCard className="h-6 w-6 text-emerald-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">Paid</Badge>
                          <span className="text-sm text-muted-foreground">Final State</span>
                        </div>
                        <p className="text-sm">Payments processed and employees have been paid. Payroll run is complete.</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Processing Features */}
              <Card className="mb-8 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Enhanced Processing Features
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">🆕 New</Badge>
                  </CardTitle>
                  <CardDescription>Real-time progress tracking and visual feedback</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">Real-time Progress Tracking</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <h5 className="font-medium text-sm">Visual Indicators</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• Live progress bar with percentage completion</li>
                            <li>• Current employee being processed highlighted</li>
                            <li>• Completed employees moved to top with checkmarks</li>
                            <li>• Processing time estimates</li>
                          </ul>
                        </div>
                        <div className="space-y-2">
                          <h5 className="font-medium text-sm">Employee Status</h5>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                              <Clock className="h-4 w-4 text-gray-500" />
                              <span>⏳ Pending - Waiting to be processed</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Calculator className="h-4 w-4 text-blue-500" />
                              <span>⚡ Processing - Currently calculating</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span>✓ Completed - Successfully processed</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                              <span>✗ Error - Processing failed</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">Resumable Processing</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        If processing is interrupted, you can resume from where it left off. The system automatically
                        saves progress and allows you to continue processing incomplete payroll runs.
                      </p>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          <span className="font-medium text-yellow-800">Important Note</span>
                        </div>
                        <p className="text-sm text-yellow-700">
                          Incomplete payroll runs will appear in a separate table below the main interface,
                          allowing you to easily continue processing from the current stage.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Step-by-Step Instructions */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5 text-emerald-600" />
                    Step-by-Step Instructions
                  </CardTitle>
                  <CardDescription>Detailed walkthrough of the payroll run process</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Step 1 */}
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <Calendar className="h-4 w-4" />
                        Step 1: Create Payroll Run
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>1. Navigate to <strong>Payroll → Payroll Runs</strong></p>
                        <p>2. Click <strong>"Create New Payroll Run"</strong></p>
                        <p>3. Fill in the required information:</p>
                        <ul className="ml-4 space-y-1">
                          <li>• <strong>Name:</strong> Descriptive name for the payroll run</li>
                          <li>• <strong>Pay Period:</strong> Select month and year</li>
                          <li>• <strong>Date Range:</strong> Set start and end dates</li>
                          <li>• <strong>Departments:</strong> Choose specific departments (optional)</li>
                        </ul>
                        <p>4. Click <strong>"Create Payroll Run"</strong></p>
                      </div>
                    </div>

                    {/* Step 2 */}
                    <div className="border-l-4 border-green-500 pl-6">
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <Users className="h-4 w-4" />
                        Step 2: Review and Select Employees
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>1. Review the list of eligible employees</p>
                        <p>2. Verify employee details and current salaries</p>
                        <p>3. Include/exclude specific employees if needed</p>
                        <p>4. Ensure all employees have valid salary structures</p>
                        <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-2">
                          <p className="text-blue-800 font-medium">💡 Pro Tip:</p>
                          <p className="text-blue-700">Employees without salary structures will be highlighted.
                          Set up their salaries before proceeding.</p>
                        </div>
                      </div>
                    </div>

                    {/* Step 3 */}
                    <div className="border-l-4 border-purple-500 pl-6">
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <Zap className="h-4 w-4" />
                        Step 3: Process Payroll (Enhanced)
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>1. Click <strong>"Process Payroll"</strong> to start calculations</p>
                        <p>2. Watch the enhanced processing interface:</p>
                        <ul className="ml-4 space-y-1">
                          <li>• Real-time progress bar shows completion percentage</li>
                          <li>• Current employee being processed is highlighted</li>
                          <li>• Completed employees move to top with green checkmarks</li>
                          <li>• Any errors are clearly displayed with details</li>
                        </ul>
                        <p>3. Processing includes:</p>
                        <ul className="ml-4 space-y-1">
                          <li>• Basic salary + allowances = gross salary</li>
                          <li>• PAYE tax calculation</li>
                          <li>• Statutory and voluntary deductions</li>
                          <li>• Final net salary calculation</li>
                        </ul>
                        <div className="bg-green-50 border border-green-200 rounded p-3 mt-2">
                          <p className="text-green-800 font-medium">✨ Enhanced Feature:</p>
                          <p className="text-green-700">If processing is interrupted, you can resume from where it left off.
                          The system saves progress automatically.</p>
                        </div>
                      </div>
                    </div>

                    {/* Step 4 */}
                    <div className="border-l-4 border-orange-500 pl-6">
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4" />
                        Step 4: Review and Approve
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>1. Review the payroll summary and totals</p>
                        <p>2. Check individual employee calculations</p>
                        <p>3. Verify gross salary, deductions, and net pay</p>
                        <p>4. Generate and review payslips</p>
                        <p>5. Make any necessary adjustments</p>
                        <p>6. Click <strong>"Approve Payroll"</strong> when satisfied</p>
                      </div>
                    </div>

                    {/* Step 5 */}
                    <div className="border-l-4 border-emerald-500 pl-6">
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <CreditCard className="h-4 w-4" />
                        Step 5: Process Payment
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>1. Navigate to the approved payroll run</p>
                        <p>2. Click <strong>"Mark as Paid"</strong></p>
                        <p>3. Generate bank transfer files if needed</p>
                        <p>4. Process actual payments through your banking system</p>
                        <p>5. Distribute payslips to employees</p>
                        <p>6. Archive the completed payroll run</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Best Practices */}
              <Card className="mb-8 border-green-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Best Practices
                  </CardTitle>
                  <CardDescription>Tips for efficient payroll processing</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">Before Processing</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li>• Ensure all employee salaries are up to date</li>
                        <li>• Verify department assignments</li>
                        <li>• Check for any pending salary adjustments</li>
                        <li>• Review tax rates and deduction rules</li>
                        <li>• Backup your data before major processing</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-3">During Processing</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li>• Monitor the enhanced progress interface</li>
                        <li>• Don't close the browser during processing</li>
                        <li>• Address any errors immediately</li>
                        <li>• Use the resume feature if interrupted</li>
                        <li>• Keep track of processing time for planning</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Troubleshooting Section */}
              <Card className="mb-8 border-yellow-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    Common Issues & Solutions
                  </CardTitle>
                  <CardDescription>Quick fixes for common problems</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4">
                      <h5 className="font-medium mb-2">Processing Stuck or Slow</h5>
                      <p className="text-sm text-muted-foreground mb-2">
                        If payroll processing appears stuck or is running very slowly:
                      </p>
                      <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                        <li>• Check your internet connection</li>
                        <li>• Refresh the page and use the resume feature</li>
                        <li>• Process smaller batches of employees</li>
                        <li>• Contact system administrator if issues persist</li>
                      </ul>
                    </div>

                    <div className="border rounded-lg p-4">
                      <h5 className="font-medium mb-2">Employee Missing from List</h5>
                      <p className="text-sm text-muted-foreground mb-2">
                        If an employee doesn't appear in the payroll run:
                      </p>
                      <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                        <li>• Verify employee status is "Active"</li>
                        <li>• Check if employee has a valid salary structure</li>
                        <li>• Ensure employee is assigned to selected departments</li>
                        <li>• Verify employment dates fall within pay period</li>
                      </ul>
                    </div>

                    <div className="border rounded-lg p-4">
                      <h5 className="font-medium mb-2">Calculation Errors</h5>
                      <p className="text-sm text-muted-foreground mb-2">
                        If salary calculations appear incorrect:
                      </p>
                      <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                        <li>• Verify employee's current salary structure</li>
                        <li>• Check tax rates and deduction rules</li>
                        <li>• Review allowances and deductions setup</li>
                        <li>• Reprocess individual employee if needed</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between items-center pt-8 border-t">
                <Button variant="outline" asChild>
                  <Link href="/docs/payroll-runs">
                    ← Back to Payroll Runs
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/docs/payroll-runs/technical-guide">
                    Technical Guide →
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

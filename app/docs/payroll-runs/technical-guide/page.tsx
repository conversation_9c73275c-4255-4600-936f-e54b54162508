// app/docs/payroll-runs/technical-guide/page.tsx
"use client"

import Link from "next/link"
import { ArrowRight, Code, Database, Server, Zap, Shield, Monitor, GitBranch, AlertTriangle, CheckCircle, Settings } from "lucide-react"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function PayrollRunsTechnicalGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-purple-900 to-purple-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Payroll Runs Technical Guide
            </h1>
            <p className="max-w-[700px] text-purple-100 md:text-xl/relaxed">
              Technical documentation for developers and system administrators
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1 max-w-4xl">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-6">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs">Payroll Runs</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/payroll-runs/technical-guide">Technical Guide</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="mb-8">
                <h2 className="text-2xl font-bold tracking-tight mb-4">Payroll Runs Technical Guide</h2>
                <p className="text-muted-foreground mb-6">
                  This technical documentation covers the architecture, API endpoints, enhanced processing implementation, 
                  and system integration details for the payroll runs module. Designed for developers and system administrators.
                </p>
              </div>

              {/* Architecture Overview */}
              <Card className="mb-8 border-purple-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GitBranch className="h-5 w-5 text-purple-600" />
                    System Architecture
                  </CardTitle>
                  <CardDescription>Enhanced payroll processing architecture</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">Core Components</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-2 flex items-center gap-2">
                            <Server className="h-4 w-4 text-blue-500" />
                            Backend Services
                          </h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• PayrollService - Core processing logic</li>
                            <li>• TaxService - PAYE calculations</li>
                            <li>• EmployeeService - Employee data management</li>
                            <li>• ErrorService - Enhanced error handling</li>
                          </ul>
                        </div>
                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-2 flex items-center gap-2">
                            <Monitor className="h-4 w-4 text-green-500" />
                            Frontend Components
                          </h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• PayrollRunForm - Creation interface</li>
                            <li>• EnhancedProcessingModal - Real-time UI</li>
                            <li>• PayrollRunTable - Management interface</li>
                            <li>• PayrollAccountingPanel - Integration</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">Enhanced Processing Flow</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="space-y-3 text-sm">
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">1</div>
                            <span><strong>Initialization:</strong> Validate employees and setup processing queue</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">2</div>
                            <span><strong>Real-time Processing:</strong> Process employees with live progress updates</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">3</div>
                            <span><strong>Visual Feedback:</strong> Update UI with employee status and progress</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">4</div>
                            <span><strong>Error Handling:</strong> Capture and display errors with recovery options</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">5</div>
                            <span><strong>Completion:</strong> Finalize processing and update payroll run status</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* API Endpoints */}
              <Card className="mb-8 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5 text-blue-600" />
                    API Endpoints
                  </CardTitle>
                  <CardDescription>Core payroll run API endpoints</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">Payroll Run Management</h4>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                            <code className="text-sm">/api/payroll/runs</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Retrieve payroll runs with filtering and pagination</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Query Parameters:</strong> status, period, page, limit, sortBy, sortOrder
                          </div>
                        </div>

                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">POST</Badge>
                            <code className="text-sm">/api/payroll/runs</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Create a new payroll run</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Body:</strong> name, payPeriod, departments, notes
                          </div>
                        </div>

                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-purple-100 text-purple-800">PATCH</Badge>
                            <code className="text-sm">/api/payroll/runs/[id]</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Update payroll run status (process, approve, pay, cancel)</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Body:</strong> action, reason (for cancel)
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">Enhanced Processing</h4>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">POST</Badge>
                            <code className="text-sm">/api/payroll/runs/[id]/process-enhanced</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Process payroll with enhanced real-time feedback</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Response:</strong> Real-time processing updates via Server-Sent Events
                          </div>
                        </div>

                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                            <code className="text-sm">/api/payroll/runs/[id]/progress</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Get current processing progress and status</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Response:</strong> progress percentage, current employee, completed count
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">Accounting Integration</h4>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">POST</Badge>
                            <code className="text-sm">/api/payroll/accounting</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Create journal entries and process payments</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Actions:</strong> create_journal_entries, post_journal_entries, create_payment_journal_entries
                          </div>
                        </div>

                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                            <code className="text-sm">/api/accounting/banking/accounts</code>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">Retrieve bank accounts for payment processing</p>
                          <div className="text-xs text-muted-foreground">
                            <strong>Response:</strong> Bank account details with available balances
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Data Models */}
              <Card className="mb-8 border-green-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5 text-green-600" />
                    Data Models
                  </CardTitle>
                  <CardDescription>Core data structures and schemas</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">PayrollRun Model</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <pre className="text-sm overflow-x-auto">
{`interface IPayrollRun {
  _id: ObjectId
  name: string
  description?: string
  payPeriod: {
    month: number        // 1-12
    year: number
    startDate: Date
    endDate: Date
  }
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string     // Default: 'MWK'
  departments?: ObjectId[]
  createdBy: ObjectId
  approvedBy?: ObjectId
  approvedAt?: Date
  processedAt?: Date
  paidAt?: Date
  journalEntryId?: string
  paymentJournalEntryId?: string
  createdAt: Date
  updatedAt: Date
}`}
                        </pre>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">PayrollRecord Model</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <pre className="text-sm overflow-x-auto">
{`interface IPayrollRecord {
  _id: ObjectId
  payrollRunId: ObjectId
  employeeId: ObjectId
  basicSalary: number
  allowances: Array<{
    type: string
    amount: number
  }>
  deductions: Array<{
    type: string
    amount: number
  }>
  grossSalary: number
  taxableIncome: number
  tax: number
  netSalary: number
  status: 'draft' | 'approved' | 'paid' | 'cancelled'
  payslipGenerated: boolean
  payslipPath?: string
  createdAt: Date
  updatedAt: Date
}`}
                        </pre>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Processing Implementation */}
              <Card className="mb-8 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Enhanced Processing Implementation
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">🆕 New</Badge>
                  </CardTitle>
                  <CardDescription>Real-time progress tracking and visual feedback</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">Frontend Implementation</h4>
                      <div className="space-y-3">
                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-2">Real-time Progress Tracking</h5>
                          <div className="bg-gray-50 rounded p-3">
                            <pre className="text-xs overflow-x-auto">
{`// Enhanced processing with real-time updates
const processPayrollEnhanced = async () => {
  setIsProcessing(true)
  setProcessingProgress({ current: 0, total: employees.length })
  
  for (let i = 0; i < employees.length; i++) {
    const employee = employees[i]
    
    // Update current employee being processed
    setCurrentEmployee(employee)
    
    try {
      await processEmployeePayroll(employee.id)
      
      // Move completed employee to top with checkmark
      setCompletedEmployees(prev => [employee, ...prev])
      setProcessingProgress(prev => ({ 
        ...prev, 
        current: i + 1 
      }))
      
    } catch (error) {
      setErrorEmployees(prev => [...prev, { employee, error }])
    }
  }
  
  setIsProcessing(false)
}`}
                            </pre>
                          </div>
                        </div>

                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-2">Visual Status Indicators</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                                <span>⏳ Pending - Waiting to be processed</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
                                <span>⚡ Processing - Currently calculating</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                <span>✓ Completed - Successfully processed</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                <span>✗ Error - Processing failed</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-semibold mb-3">Backend Processing Logic</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <pre className="text-xs overflow-x-auto">
{`// Enhanced payroll processing service
class PayrollService {
  async processPayrollRunEnhanced(payrollRunId: string, userId: string) {
    const payrollRun = await PayrollRun.findById(payrollRunId)
    const employees = await this.getEligibleEmployees(payrollRun)
    
    // Update status to processing
    await PayrollRun.findByIdAndUpdate(payrollRunId, {
      status: 'processing',
      processedEmployees: 0,
      totalEmployees: employees.length
    })
    
    let processedCount = 0
    const errors = []
    
    for (const employee of employees) {
      try {
        // Process individual employee
        await this.processEmployeePayroll(employee, payrollRun)
        processedCount++
        
        // Update progress in real-time
        await PayrollRun.findByIdAndUpdate(payrollRunId, {
          processedEmployees: processedCount
        })
        
        // Emit progress event for real-time UI updates
        this.emitProgressUpdate(payrollRunId, {
          current: processedCount,
          total: employees.length,
          currentEmployee: employee
        })
        
      } catch (error) {
        errors.push({ employee: employee.id, error: error.message })
      }
    }
    
    // Finalize processing
    const finalStatus = errors.length === 0 ? 'completed' : 'completed_with_errors'
    await PayrollRun.findByIdAndUpdate(payrollRunId, {
      status: finalStatus,
      processedAt: new Date(),
      errors: errors
    })
    
    return { processedCount, errors }
  }
}`}
                        </pre>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between items-center pt-8 border-t">
                <Button variant="outline" asChild>
                  <Link href="/docs/payroll-runs/user-guide">
                    ← User Guide
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/docs/payroll-runs/recent-updates">
                    Recent Updates →
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

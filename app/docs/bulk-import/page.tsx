"use client"

import { BulkImportDoc } from "@/components/docs/content/bulk-import"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function BulkImportPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Bulk Import
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Learn how to import multiple employees and data from external systems
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              <BulkImportDoc
                backLink="/docs"
                backLabel="Back to Documentation Home"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

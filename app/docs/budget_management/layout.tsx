"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  ChevronRight, 
  Home, 
  FileText, 
  BookOpen, 
  Code, 
  HelpCircle, 
  Video, 
  MessageSquare 
} from 'lucide-react';

export default function BudgetManagementLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const sidebarLinks = [
    {
      title: 'Overview',
      href: '/docs/budget_management',
      icon: <Home className="h-4 w-4 mr-2" />,
    },
    {
      title: 'User Guide',
      href: '/docs/budget_management/user_guide',
      icon: <BookOpen className="h-4 w-4 mr-2" />,
    },
    {
      title: 'Technical Guide',
      href: '/docs/budget_management/technical_guide',
      icon: <Code className="h-4 w-4 mr-2" />,
    },
    {
      title: 'API Reference',
      href: '/docs/budget_management/api_reference',
      icon: <FileText className="h-4 w-4 mr-2" />,
    },
    {
      title: 'Video Tutorials',
      href: '/docs/budget_management/tutorials',
      icon: <Video className="h-4 w-4 mr-2" />,
    },
    {
      title: 'FAQ',
      href: '/docs/budget_management/faq',
      icon: <HelpCircle className="h-4 w-4 mr-2" />,
    },
    {
      title: 'Support',
      href: '/docs/budget_management/support',
      icon: <MessageSquare className="h-4 w-4 mr-2" />,
    },
  ];

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <div className="hidden md:flex w-64 flex-col border-r bg-background">
        <div className="p-6">
          <h2 className="text-xl font-bold">Budget Management</h2>
          <p className="text-sm text-muted-foreground mt-1">Documentation</p>
        </div>
        <nav className="flex-1 px-4 pb-4">
          <ul className="space-y-1">
            {sidebarLinks.map((link) => (
              <li key={link.href}>
                <Link
                  href={link.href}
                  className={`flex items-center px-3 py-2 text-sm rounded-md ${
                    pathname === link.href
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-muted-foreground hover:bg-muted'
                  }`}
                >
                  {link.icon}
                  {link.title}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t">
          <p className="text-xs text-muted-foreground">
            Documentation Version: 1.0.0
          </p>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto">
        {/* Breadcrumb */}
        <div className="border-b bg-background">
          <div className="container mx-auto py-2 px-4 md:px-6">
            <div className="flex items-center text-sm text-muted-foreground">
              <Link href="/docs" className="hover:text-foreground">
                Documentation
              </Link>
              <ChevronRight className="h-4 w-4 mx-1" />
              <Link href="/docs/budget_management" className="hover:text-foreground">
                Budget Management
              </Link>
              {pathname !== '/docs/budget_management' && (
                <>
                  <ChevronRight className="h-4 w-4 mx-1" />
                  <span className="text-foreground">
                    {pathname.split('/').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="container mx-auto py-6 px-4 md:px-6">
          {children}
        </main>
      </div>
    </div>
  );
}

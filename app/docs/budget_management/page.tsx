"use client";

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Hardcoded content for now to fix build issues
const userGuideContent = `# Budget Management User Guide

## Introduction

The Budget Management module in the TCM Enterprise Business Suite provides a comprehensive solution for creating, managing, and tracking organizational budgets.`;

const technicalGuideContent = `# Budget Management Technical Guide

## Architecture Overview

The Budget Management module is built using a modern, component-based architecture that follows the principles of separation of concerns and modularity.`;

export default function BudgetManagementDocs() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Budget Management Documentation</h1>

      <Alert className="mb-6">
        <InfoIcon className="h-4 w-4" />
        <AlertTitle>Documentation Version</AlertTitle>
        <AlertDescription>
          This documentation is for the Budget Management module version 1.0.0.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="user-guide" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="user-guide">User Guide</TabsTrigger>
          <TabsTrigger value="technical-guide">Technical Guide</TabsTrigger>
        </TabsList>

        <TabsContent value="user-guide" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>User Guide</CardTitle>
              <CardDescription>
                A comprehensive guide for end users of the Budget Management module
              </CardDescription>
            </CardHeader>
            <CardContent className="prose max-w-none dark:prose-invert">
              <div className="markdown-content">
                <Markdown remarkPlugins={[remarkGfm]}>
                  {userGuideContent}
                </Markdown>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technical-guide" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Technical Guide</CardTitle>
              <CardDescription>
                Technical documentation for developers and system administrators
              </CardDescription>
            </CardHeader>
            <CardContent className="prose max-w-none dark:prose-invert">
              <div className="markdown-content">
                <Markdown remarkPlugins={[remarkGfm]}>
                  {technicalGuideContent}
                </Markdown>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">Additional Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Video Tutorials</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Watch step-by-step video tutorials on how to use the Budget Management module.</p>
              <a href="#" className="text-primary hover:underline mt-2 inline-block">
                View Tutorials
              </a>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>FAQ</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Find answers to frequently asked questions about the Budget Management module.</p>
              <a href="#" className="text-primary hover:underline mt-2 inline-block">
                View FAQ
              </a>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Support</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Get help from our support team if you encounter any issues.</p>
              <a href="#" className="text-primary hover:underline mt-2 inline-block">
                Contact Support
              </a>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="mt-8 text-center text-sm text-muted-foreground">
        <p>Last updated: {new Date().toLocaleDateString()}</p>
        <p>© {new Date().getFullYear()} TCM Enterprise Business Suite. All rights reserved.</p>
      </div>
    </div>
  );
}

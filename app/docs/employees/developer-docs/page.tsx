"use client"

import Link from "next/link"
import { ArrowLeft, Code, Database, Settings, FileText, Terminal, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function EmployeeDeveloperDocsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/employee-management">Employee Management</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/employees/developer-docs">Developer Documentation</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/employee-management">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Employee Management
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Code className="h-8 w-8 text-purple-500" />
          <h1 className="text-3xl font-bold text-zinc-900">Employee Developer Documentation</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Complete technical documentation for developers including API endpoints, data models, 
          integration patterns, and customization options.
        </p>
      </div>

      {/* Quick Reference */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
        <Card className="border-purple-200">
          <CardHeader>
            <Terminal className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">API Endpoints</CardTitle>
            <CardDescription>RESTful API for employee operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• GET /api/employees</div>
              <div>• POST /api/employees</div>
              <div>• PUT /api/employees/:id</div>
              <div>• DELETE /api/employees/:id</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader>
            <Database className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">Data Models</CardTitle>
            <CardDescription>Employee data structure and schemas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Employee schema</div>
              <div>• Department relationships</div>
              <div>• Role and permission models</div>
              <div>• Audit trail structure</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader>
            <Zap className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">Integration</CardTitle>
            <CardDescription>Integration patterns and webhooks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Webhook configurations</div>
              <div>• Third-party integrations</div>
              <div>• Event-driven architecture</div>
              <div>• Real-time updates</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* API Documentation */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">API Reference</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Terminal className="h-5 w-5 text-purple-500" />
                Employee API Endpoints
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/employees</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve all employees with optional filtering and pagination</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query Parameters: page, limit, department, status, search</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/employees</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Create a new employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Required: firstName, lastName, email, department</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-mono">PUT</span>
                    <code className="text-sm">/api/employees/:id</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Update an existing employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports partial updates and validation</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-mono">DELETE</span>
                    <code className="text-sm">/api/employees/:id</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Soft delete an employee record</p>
                  <div className="text-xs text-zinc-500">
                    <div>Maintains audit trail and referential integrity</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-purple-500" />
                Data Models & Schemas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Employee Schema</h4>
                  <div className="bg-zinc-50 rounded-lg p-4">
                    <pre className="text-xs text-zinc-700 overflow-x-auto">
{`{
  "_id": "ObjectId",
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "phone": "string",
  "department": "ObjectId",
  "position": "string",
  "employeeId": "string",
  "startDate": "Date",
  "status": "active | inactive | terminated",
  "salary": {
    "amount": "number",
    "currency": "string",
    "effectiveDate": "Date"
  },
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string",
    "country": "string"
  },
  "emergencyContact": {
    "name": "string",
    "relationship": "string",
    "phone": "string"
  },
  "createdAt": "Date",
  "updatedAt": "Date",
  "createdBy": "ObjectId",
  "updatedBy": "ObjectId"
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Validation Rules</h4>
                  <div className="space-y-2 text-sm text-zinc-600">
                    <div>• Email must be unique across all employees</div>
                    <div>• Employee ID must follow company format (TCM-XXXX)</div>
                    <div>• Department must exist in departments collection</div>
                    <div>• Phone numbers validated for format and uniqueness</div>
                    <div>• Salary amount must be positive number</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-500" />
                Authentication & Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Authentication</h4>
                  <div className="space-y-2 text-sm text-zinc-600">
                    <div>• JWT token-based authentication required</div>
                    <div>• Include Authorization header: Bearer {`<token>`}</div>
                    <div>• Tokens expire after 24 hours</div>
                    <div>• Refresh tokens available for extended sessions</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Permission Levels</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h5 className="font-medium text-zinc-800 mb-1">Read Permissions</h5>
                      <div className="space-y-1 text-xs text-zinc-600">
                        <div>• View employee list</div>
                        <div>• View employee details</div>
                        <div>• Export employee data</div>
                      </div>
                    </div>
                    <div>
                      <h5 className="font-medium text-zinc-800 mb-1">Write Permissions</h5>
                      <div className="space-y-1 text-xs text-zinc-600">
                        <div>• Create new employees</div>
                        <div>• Update employee records</div>
                        <div>• Delete/deactivate employees</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Integration Examples */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Integration Examples</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>JavaScript/TypeScript Example</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-zinc-50 rounded-lg p-4">
                <pre className="text-xs text-zinc-700 overflow-x-auto">
{`// Fetch employees with filtering
const fetchEmployees = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(\`/api/employees?\${params}\`, {
    headers: {
      'Authorization': \`Bearer \${token}\`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Create new employee
const createEmployee = async (employeeData) => {
  const response = await fetch('/api/employees', {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${token}\`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(employeeData)
  });
  return response.json();
};`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/api/employees">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Complete API Reference</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Full API documentation with examples</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/employees/user-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">User Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">End-user documentation and tutorials</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/employees/management-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Management Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Administrative and management features</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

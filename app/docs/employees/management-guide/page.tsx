"use client"

import Link from "next/link"
import { ArrowLeft, Users, BarChart3, <PERSON><PERSON>s, FileText, Download, Upload, Shield } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function EmployeeManagementGuidePage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/employee-management">Employee Management</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/employees/management-guide">Management Guide</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/employee-management">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Employee Management
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-8 w-8 text-blue-500" />
          <h1 className="text-3xl font-bold text-zinc-900">Employee Management Guide</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Strategic oversight and administrative management for employee operations. 
          Advanced features for managers and administrators.
        </p>
      </div>

      {/* Management Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
        <Card className="border-blue-200">
          <CardHeader>
            <BarChart3 className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Analytics & Reporting</CardTitle>
            <CardDescription>Employee analytics and comprehensive reporting</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Employee headcount analytics</div>
              <div>• Department distribution reports</div>
              <div>• Performance metrics tracking</div>
              <div>• Compliance reporting</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader>
            <Users className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Organizational Structure</CardTitle>
            <CardDescription>Manage departments and hierarchies</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Department management</div>
              <div>• Role and position hierarchy</div>
              <div>• Reporting structure setup</div>
              <div>• Organizational charts</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader>
            <Shield className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Compliance & Audit</CardTitle>
            <CardDescription>Ensure regulatory compliance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Audit trail management</div>
              <div>• Compliance monitoring</div>
              <div>• Data retention policies</div>
              <div>• Security protocols</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Management Features */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Key Management Features</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5 text-blue-500" />
                Bulk Operations Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Efficiently manage large-scale employee operations with bulk import, export, and update capabilities.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Bulk Import Features</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Excel/CSV file processing</div>
                    <div>• Data validation and error handling</div>
                    <div>• Duplicate detection and resolution</div>
                    <div>• Progress tracking and reporting</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Data Management</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Bulk updates and modifications</div>
                    <div>• Data export and backup</div>
                    <div>• Archive and retention management</div>
                    <div>• Data quality monitoring</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-500" />
                Advanced Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Gain insights into your workforce with comprehensive analytics and reporting tools.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Workforce Analytics</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Employee demographics analysis</div>
                    <div>• Department distribution metrics</div>
                    <div>• Turnover and retention rates</div>
                    <div>• Performance trend analysis</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Reporting Capabilities</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Custom report generation</div>
                    <div>• Scheduled automated reports</div>
                    <div>• Interactive dashboards</div>
                    <div>• Export to multiple formats</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-500" />
                System Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Configure system settings and customize the employee management experience.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Access Control</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Role-based permissions</div>
                    <div>• Department access controls</div>
                    <div>• Data visibility settings</div>
                    <div>• Security policy configuration</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Customization Options</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Custom field configuration</div>
                    <div>• Workflow customization</div>
                    <div>• Notification settings</div>
                    <div>• Integration configurations</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Best Practices */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Management Best Practices</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border-emerald-200">
            <CardHeader>
              <CardTitle className="text-lg text-emerald-700">Data Quality Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Regular data validation and cleanup</div>
                <div>• Standardized data entry procedures</div>
                <div>• Duplicate prevention strategies</div>
                <div>• Data integrity monitoring</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-emerald-200">
            <CardHeader>
              <CardTitle className="text-lg text-emerald-700">Security & Compliance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Regular access review and updates</div>
                <div>• Audit trail monitoring</div>
                <div>• Compliance reporting schedules</div>
                <div>• Data protection protocols</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/employees/user-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">User Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Daily employee management tasks and procedures</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/employees/developer-docs">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Developer Documentation</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Technical implementation and API reference</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/bulk-import">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Bulk Import Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Comprehensive bulk operations documentation</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

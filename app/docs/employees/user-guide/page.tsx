"use client"

import Link from "next/link"
import { ArrowLeft, Users, UserPlus, Search, Edit, FileText, Download, Upload, Eye } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { Separator } from "@/components/ui/separator"

export default function EmployeeUserGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-blue-900 to-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full">
                User Guide
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Employee Management User Guide
            </h1>
            <p className="max-w-[700px] text-blue-100 md:text-xl/relaxed">
              Step-by-step instructions for daily employee management tasks and workflows
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1 max-w-4xl">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-8">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/employees">Employee Management</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/employees/user-guide">User Guide</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Back Link */}
              <Link
                href="/docs/employees"
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 mb-8"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Employee Management
              </Link>

              {/* Content */}
              <div className="prose prose-zinc max-w-none">
                <h1>Employee Management User Guide</h1>
                <p className="lead">
                  This guide covers the essential daily tasks for managing employees in the TCM Enterprise Suite. 
                  Learn how to register new employees, search the directory, update profiles, and manage employee documents.
                </p>

                <h2>Getting Started</h2>
                <p>
                  The Employee Management module is your central hub for all employee-related information. 
                  Access it from the main dashboard by clicking on "Employees" in the navigation menu.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">Before You Begin</h3>
                  <ul className="text-blue-700 space-y-1">
                    <li>Ensure you have the necessary permissions to manage employees</li>
                    <li>Have employee information and documents ready</li>
                    <li>Understand your organization's employee numbering system</li>
                  </ul>
                </div>

                <h2>Core Concepts</h2>
                
                <h3>Employee vs User</h3>
                <p>
                  It's important to understand the distinction between <strong>Employees</strong> and <strong>Users</strong>:
                </p>
                <ul>
                  <li><strong>Employees</strong>: People who work for your organization (staff members)</li>
                  <li><strong>Users</strong>: System accounts with login credentials and permissions</li>
                </ul>
                <p>
                  An employee may or may not have a user account, depending on whether they need system access.
                </p>

                <h3>Employee Lifecycle</h3>
                <p>The typical employee lifecycle in the system includes:</p>
                <ol>
                  <li><strong>Registration</strong>: Adding new employee to the system</li>
                  <li><strong>Onboarding</strong>: Completing initial setup and documentation</li>
                  <li><strong>Active Management</strong>: Ongoing updates and maintenance</li>
                  <li><strong>Transitions</strong>: Promotions, transfers, role changes</li>
                  <li><strong>Separation</strong>: Resignation, retirement, or termination</li>
                </ol>

                <h2>Daily Workflows</h2>

                <h3>1. Registering a New Employee</h3>
                <p>Follow these steps to add a new employee to the system:</p>
                
                <div className="bg-zinc-50 border rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2">Step-by-Step Process:</h4>
                  <ol className="space-y-2">
                    <li>Navigate to <strong>Employees → Register New Employee</strong></li>
                    <li>Fill in the required personal information:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Full name (first, middle, last)</li>
                        <li>National ID number</li>
                        <li>Date of birth</li>
                        <li>Gender</li>
                        <li>Contact information (phone, email, address)</li>
                      </ul>
                    </li>
                    <li>Add Malawian-specific location details:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Village/Area</li>
                        <li>Traditional Authority</li>
                        <li>District</li>
                      </ul>
                    </li>
                    <li>Enter employment details:
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>Employee number (auto-generated or manual)</li>
                        <li>Department</li>
                        <li>Position/Job title</li>
                        <li>Start date</li>
                        <li>Employment type (permanent, contract, temporary)</li>
                      </ul>
                    </li>
                    <li>Add emergency contact information</li>
                    <li>Upload required documents (if available)</li>
                    <li>Review all information and click <strong>Save Employee</strong></li>
                  </ol>
                </div>

                <h3>2. Searching and Finding Employees</h3>
                <p>The employee directory provides powerful search capabilities:</p>

                <h4>Basic Search</h4>
                <ul>
                  <li>Use the search bar to find employees by name, employee number, or department</li>
                  <li>Search is case-insensitive and supports partial matches</li>
                </ul>

                <h4>Advanced Filtering</h4>
                <ul>
                  <li><strong>Department Filter</strong>: View employees by specific department</li>
                  <li><strong>Status Filter</strong>: Active, inactive, or on leave employees</li>
                  <li><strong>Employment Type</strong>: Permanent, contract, or temporary staff</li>
                  <li><strong>Date Range</strong>: Filter by hire date or other date criteria</li>
                </ul>

                <h3>3. Updating Employee Information</h3>
                <p>Keep employee records current with regular updates:</p>

                <div className="bg-zinc-50 border rounded-lg p-4 my-4">
                  <h4 className="font-semibold mb-2">Common Updates:</h4>
                  <ul className="space-y-1">
                    <li><strong>Contact Information</strong>: Phone numbers, email addresses, physical addresses</li>
                    <li><strong>Emergency Contacts</strong>: Update contact persons and their information</li>
                    <li><strong>Position Changes</strong>: Promotions, transfers, or role modifications</li>
                    <li><strong>Department Transfers</strong>: Moving employees between departments</li>
                    <li><strong>Personal Information</strong>: Marital status, dependents, etc.</li>
                  </ul>
                </div>

                <h3>4. Document Management</h3>
                <p>Manage employee documents efficiently:</p>

                <h4>Uploading Documents</h4>
                <ol>
                  <li>Open the employee's profile</li>
                  <li>Navigate to the <strong>Documents</strong> tab</li>
                  <li>Click <strong>Upload Document</strong></li>
                  <li>Select document type (CV, certificates, contracts, etc.)</li>
                  <li>Choose file and add description</li>
                  <li>Click <strong>Upload</strong></li>
                </ol>

                <h4>Document Types</h4>
                <ul>
                  <li>Personal identification documents</li>
                  <li>Educational certificates and qualifications</li>
                  <li>Employment contracts and agreements</li>
                  <li>Professional certifications</li>
                  <li>Performance reviews and evaluations</li>
                </ul>

                <h2>Best Practices</h2>

                <h3>Data Quality</h3>
                <ul>
                  <li>Always verify employee information before saving</li>
                  <li>Use consistent naming conventions</li>
                  <li>Ensure contact information is current and accurate</li>
                  <li>Regularly audit and clean up employee data</li>
                </ul>

                <h3>Security and Privacy</h3>
                <ul>
                  <li>Only access employee information you need for your role</li>
                  <li>Keep sensitive information confidential</li>
                  <li>Log out of the system when not in use</li>
                  <li>Report any data discrepancies immediately</li>
                </ul>

                <h3>Efficiency Tips</h3>
                <ul>
                  <li>Use bulk import for adding multiple employees</li>
                  <li>Set up saved search filters for common queries</li>
                  <li>Use keyboard shortcuts for faster navigation</li>
                  <li>Keep frequently used documents easily accessible</li>
                </ul>

                <h2>Troubleshooting</h2>

                <h3>Common Issues</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Employee Number Already Exists</h4>
                    <p>If you get this error, check if the employee is already in the system or use the auto-generate option.</p>
                  </div>
                  
                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Document Upload Fails</h4>
                    <p>Ensure the file size is under 10MB and in an accepted format (PDF, DOC, JPG, PNG).</p>
                  </div>
                  
                  <div className="border-l-4 border-yellow-400 pl-4">
                    <h4 className="font-semibold">Search Returns No Results</h4>
                    <p>Try using partial names or check your filter settings. The employee might be in a different status.</p>
                  </div>
                </div>

                <h2>Next Steps</h2>
                <p>
                  Once you're comfortable with basic employee management, explore these related areas:
                </p>
                <ul>
                  <li><Link href="/docs/hr/user-guide" className="text-blue-600 hover:text-blue-700">HR Processes and Workflows</Link></li>
                  <li><Link href="/docs/bulk-import" className="text-blue-600 hover:text-blue-700">Bulk Import Guide</Link></li>
                  <li><Link href="/docs/employees/management-guide" className="text-blue-600 hover:text-blue-700">Employee Management Guide</Link></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

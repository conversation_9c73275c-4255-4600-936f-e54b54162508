"use client"

import Link from "next/link"
import { ArrowRight, Users, BookOpen, Settings, Code, FileText, UserCheck, Building, Search, Download } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"

export default function EmployeesModulePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                Core Module
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Employee Management
            </h1>
            <p className="max-w-[700px] text-emerald-100 md:text-xl/relaxed">
              Core entity management for organizational staff - master data, directory, lifecycle, and organizational structure
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-8">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/employees">Employee Management</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Module Overview */}
              <div className="mb-12">
                <h2 className="text-3xl font-bold text-zinc-900 mb-4">Employee Management Module</h2>
                <p className="text-lg text-zinc-600 mb-6">
                  The Employee Management module serves as the core entity system for managing organizational staff.
                  It handles employee master data, directory services, lifecycle management, and organizational structure.
                </p>

                <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-6 mb-8">
                  <h3 className="text-lg font-semibold text-emerald-800 mb-2">Key Distinction</h3>
                  <p className="text-emerald-700">
                    <strong>Employee Management</strong> focuses on the core "person" entities who work for the organization,
                    while <strong>HR Management</strong> handles the processes and policies that govern how employees are managed.
                  </p>
                </div>
              </div>

              {/* Documentation Categories */}
              <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 mb-12">
                <Link href="/docs/employees/user-guide">
                  <Card className="h-full transition-all hover:shadow-md border-blue-200">
                    <CardHeader>
                      <BookOpen className="h-8 w-8 text-blue-500 mb-2" />
                      <CardTitle>User Guide</CardTitle>
                      <CardDescription>Daily operations and workflows</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Step-by-step guides for daily employee management tasks.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Employee registration and onboarding</div>
                        <div>• Directory search and management</div>
                        <div>• Profile updates and maintenance</div>
                        <div>• Document management</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                        Read User Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/employees/management-guide">
                  <Card className="h-full transition-all hover:shadow-md border-purple-200">
                    <CardHeader>
                      <Settings className="h-8 w-8 text-purple-500 mb-2" />
                      <CardTitle>Management Guide</CardTitle>
                      <CardDescription>Strategic oversight and administration</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Management-level guidance for employee administration and strategic planning.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Organizational structure design</div>
                        <div>• Employee lifecycle management</div>
                        <div>• Reporting and analytics</div>
                        <div>• Compliance and audit trails</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                        Read Management Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>


              </div>

              {/* Core Features */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-zinc-900 mb-6">Core Features</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <UserCheck className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Employee Master Data</h4>
                      <p className="text-sm text-zinc-600">Comprehensive employee profiles with Malawian-specific fields</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Search className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Employee Directory</h4>
                      <p className="text-sm text-zinc-600">Searchable directory with advanced filtering and organizational charts</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Building className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Organizational Structure</h4>
                      <p className="text-sm text-zinc-600">Department hierarchy, positions, and reporting relationships</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <FileText className="h-6 w-6 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Document Management</h4>
                      <p className="text-sm text-zinc-600">Digital storage of contracts, certifications, and employee documents</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="bg-zinc-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-zinc-900 mb-4">Quick Links</h3>
                <div className="grid gap-3 md:grid-cols-2">
                  <Link href="/docs/api/employees" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Code className="h-4 w-4 mr-2" />
                    Employee API Reference
                  </Link>
                  <Link href="/docs/bulk-import" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Download className="h-4 w-4 mr-2" />
                    Bulk Import Guide
                  </Link>
                  <Link href="/docs/hr" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <Users className="h-4 w-4 mr-2" />
                    HR Module Documentation
                  </Link>
                  <Link href="/docs/accounting" className="flex items-center text-sm text-emerald-600 hover:text-emerald-700">
                    <FileText className="h-4 w-4 mr-2" />
                    Accounting Module Documentation
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

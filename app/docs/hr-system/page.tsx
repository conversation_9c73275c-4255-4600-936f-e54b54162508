import { Metadata } from 'next'
import { ArrowLeft, Users, Building, DollarSign, BarChart3, Workflow, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'HR System User Guide | TCM Enterprise Suite',
  description: 'Comprehensive HR management guide with TCM role integration, covering complete workflows from organizational setup to employee management.',
}

export default function HRSystemPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          {/* Back Link */}
          <Link
            href="/docs"
            className="inline-flex items-center text-sm font-medium text-emerald-600 hover:text-emerald-700 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Documentation
          </Link>

          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <Building className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-zinc-900">HR System User Guide</h1>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">
                  🎉 Complete HR Management
                </Badge>
                <Badge variant="outline">TCM Role Integration</Badge>
              </div>
            </div>
          </div>

          <p className="text-lg text-zinc-600 mb-8">
            Complete HR management with TCM role integration. This guide covers the comprehensive HR workflow from organizational structure setup to employee management.
          </p>
        </div>

        {/* Achievement Banner */}
        <Card className="mb-8 border-emerald-200 bg-emerald-50">
          <CardHeader>
            <CardTitle className="text-emerald-800 flex items-center gap-2">
              🎉 Complete HR Management with TCM Role Integration
            </CardTitle>
            <CardDescription className="text-emerald-700">
              <strong>Welcome to the comprehensive HR management system!</strong> This guide covers the complete HR workflow from organizational structure setup to employee management, now enhanced with the TCM role system.
            </CardDescription>
          </CardHeader>
        </Card>

        {/* System Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🏢 System Overview</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-emerald-500" />
                  What's New
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>31 Organizational Roles</strong> imported and active</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Role-Department Mapping</strong> aligned with TCM structure</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Salary Band Integration</strong> for role-based compensation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Organizational Hierarchy</strong> from TCM 1 to TCM 12</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Core HR Modules</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <strong>1. Department Management</strong>
                    <p className="text-zinc-600">Create and manage organizational departments</p>
                  </div>
                  <div>
                    <strong>2. Role Management 🆕</strong>
                    <p className="text-zinc-600">Manage formal organizational roles with TCM codes</p>
                  </div>
                  <div>
                    <strong>3. Employee Management</strong>
                    <p className="text-zinc-600">Complete employee lifecycle with role integration</p>
                  </div>
                  <div>
                    <strong>4. Integrated Reporting</strong>
                    <p className="text-zinc-600">Organizational charts and role-based analytics</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Getting Started Workflow */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🚀 Getting Started Workflow</h2>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="border-emerald-200">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <CardTitle className="text-lg">Department Setup ✅</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• Create organizational departments</li>
                  <li>• Define department hierarchies</li>
                  <li>• Assign department heads and budgets</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-emerald-200">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <CardTitle className="text-lg">Role Import ✅</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• Import TCM organizational roles</li>
                  <li>• Verify role-department associations</li>
                  <li>• Activate role management features</li>
                </ul>
                <Badge variant="secondary" className="mt-2 bg-emerald-100 text-emerald-700">
                  COMPLETED
                </Badge>
              </CardContent>
            </Card>

            <Card className="border-orange-200">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <CardTitle className="text-lg">Employee Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• Import or register employees</li>
                  <li>• Assign roles to employees</li>
                  <li>• Validate salary assignments</li>
                </ul>
                <Badge variant="outline" className="mt-2">
                  In Progress
                </Badge>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* User Access Levels */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">👥 User Access Levels</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-emerald-700">HR Director/Manager</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• Full access to all HR modules</li>
                  <li>• Role and department management</li>
                  <li>• Salary band configuration</li>
                  <li>• Advanced reporting</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-blue-700">HR Specialist</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• Employee management</li>
                  <li>• Basic reporting</li>
                  <li>• Role assignment</li>
                  <li>• Data entry and updates</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-purple-700">Department Head</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• View department employees</li>
                  <li>• Basic employee information</li>
                  <li>• Department-specific reports</li>
                  <li>• Role recommendations</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-gray-700">Employee</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>• View own profile</li>
                  <li>• Update personal information</li>
                  <li>• Access organizational directory</li>
                  <li>• View role information</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Integrated Workflows */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🔄 Integrated Workflows</h2>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Workflow className="h-5 w-5 text-emerald-500" />
                Complete HR Workflow
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">1. Organizational Setup</h4>
                  <div className="flex items-center gap-2 text-sm text-zinc-600">
                    <span className="px-2 py-1 bg-emerald-100 rounded">Departments</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-blue-100 rounded">Roles</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-purple-100 rounded">Salary Bands</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-orange-100 rounded">Employee Registration</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">2. Employee Onboarding</h4>
                  <div className="flex items-center gap-2 text-sm text-zinc-600">
                    <span className="px-2 py-1 bg-emerald-100 rounded">Registration</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-blue-100 rounded">Role Assignment</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-purple-100 rounded">Salary Validation</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-orange-100 rounded">Department Assignment</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">3. Employee Management</h4>
                  <div className="flex items-center gap-2 text-sm text-zinc-600">
                    <span className="px-2 py-1 bg-emerald-100 rounded">Profile Updates</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-blue-100 rounded">Role Changes</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-purple-100 rounded">Salary Adjustments</span>
                    <span>→</span>
                    <span className="px-2 py-1 bg-orange-100 rounded">Performance Tracking</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Key Features */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 mb-4">🎯 Key Features</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-emerald-700">TCM Role System</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>31 Organizational Roles</strong> - Complete hierarchy</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Role Management Interface</strong> - Full CRUD operations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Department Integration</strong> - Role-department mapping</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Bulk Operations</strong> - Import/export capabilities</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-emerald-700">Salary Management Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Salary Band System</strong> - TCM code-based ranges</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Automatic Validation</strong> - Role-based checking</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Exception Handling</strong> - Out-of-band procedures</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    <span><strong>Compliance Monitoring</strong> - Salary equity tracking</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <Link
            href="/dashboard/employee/roles"
            className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
          >
            <Users className="h-4 w-4 mr-2" />
            Access Role Management
          </Link>
          <Link
            href="/docs/role-management"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Role Management Guide
          </Link>
          <Link
            href="/docs/employees"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Employee Management
          </Link>
          <Link
            href="/docs/bulk-import"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Bulk Import Guide
          </Link>
        </div>

        {/* Support Section */}
        <Card className="mt-8 border-zinc-200">
          <CardHeader>
            <CardTitle className="text-xl">📞 Support and Training</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-3">User Resources</h4>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><Link href="/docs/role-management" className="text-emerald-600 hover:text-emerald-700">Role Management User Guide</Link></li>
                  <li><Link href="/docs/employees" className="text-emerald-600 hover:text-emerald-700">Employee Management User Guide</Link></li>
                  <li><Link href="/docs/bulk-import" className="text-emerald-600 hover:text-emerald-700">Bulk Import System Guide</Link></li>
                  <li><Link href="/docs/development" className="text-emerald-600 hover:text-emerald-700">Quick Reference Guides</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Getting Help</h4>
                <ul className="space-y-2 text-sm text-zinc-600">
                  <li><strong>Technical Support:</strong> System administrator for technical issues</li>
                  <li><strong>Process Support:</strong> HR department for policy questions</li>
                  <li><strong>Training:</strong> User training sessions and documentation</li>
                  <li><strong>Updates:</strong> Regular system updates and improvements</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

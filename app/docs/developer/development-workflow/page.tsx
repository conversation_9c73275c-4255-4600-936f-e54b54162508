"use client"

import Link from "next/link"
import { ArrowLeft, GitBranch, Workflow, Clock, Users, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function DevelopmentWorkflowPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-indigo-900 to-indigo-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full">
                Development Workflow
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Development Workflow
            </h1>
            <p className="max-w-[700px] text-indigo-100 md:text-xl/relaxed">
              Complete branching strategy, commit standards, and development process documentation
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumb className="mb-8">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs/developer">Developer Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>Development Workflow</BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Navigation */}
            <div className="flex items-center gap-2 mb-8">
              <Button variant="outline" size="sm" asChild>
                <Link href="/docs/developer">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Developer Docs
                </Link>
              </Button>
            </div>

            {/* Overview Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Workflow className="h-8 w-8 text-indigo-600" />
                🌟 Workflow Overview
              </h2>

              <Card>
                <CardContent className="p-6">
                  <p className="text-lg text-zinc-600 mb-6">
                    This document outlines the development workflow, branching strategy, and deployment process for the Teachers Council of Malawi (TCM) Enterprise Business Suite.
                  </p>

                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <GitBranch className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <h3 className="font-semibold text-blue-900">Branching Strategy</h3>
                      <p className="text-sm text-blue-700">Structured git workflow</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <h3 className="font-semibold text-green-900">Quality Gates</h3>
                      <p className="text-sm text-green-700">Automated quality checks</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <h3 className="font-semibold text-purple-900">Team Collaboration</h3>
                      <p className="text-sm text-purple-700">Code review process</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Branching Strategy Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <GitBranch className="h-8 w-8 text-green-600" />
                🌳 Branching Strategy
              </h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Branch Structure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>main (production)</div>
                        <div>├── development (staging)</div>
                        <div>│   ├── feature/employee-management-v2</div>
                        <div>│   ├── feature/hr-workflows</div>
                        <div>│   ├── feature/accounting-reports</div>
                        <div>│   ├── bugfix/login-validation</div>
                        <div>│   └── hotfix/security-patch</div>
                        <div>└── hotfix/critical-production-fix (emergency only)</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-red-700">main Branch 🚀</CardTitle>
                      <CardDescription>Production-ready code</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div><strong>Purpose:</strong> Production-ready code</div>
                        <div><strong>Deployment:</strong> Automatically deploys to production (Vercel & Railway)</div>
                        <div><strong>Protection:</strong> Requires PR approval and successful CI/CD</div>
                        <div><strong>Direct commits:</strong> <Badge variant="destructive">❌ Forbidden</Badge></div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-blue-700">development Branch 🧪</CardTitle>
                      <CardDescription>Integration and testing environment</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div><strong>Purpose:</strong> Integration and testing environment</div>
                        <div><strong>Deployment:</strong> Automatically deploys to staging environments</div>
                        <div><strong>Source:</strong> Merges from feature/bugfix branches</div>
                        <div><strong>Target:</strong> Merges to main after manual approval</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-green-700">Feature Branches ✨</CardTitle>
                      <CardDescription>New feature development</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div><strong>Naming:</strong> <code className="bg-gray-100 px-1 rounded">feature/description-of-feature</code></div>
                        <div><strong>Purpose:</strong> New feature development</div>
                        <div><strong>Source:</strong> Created from <code className="bg-gray-100 px-1 rounded">development</code></div>
                        <div><strong>Target:</strong> Merged back to <code className="bg-gray-100 px-1 rounded">development</code></div>
                        <div><strong>Lifespan:</strong> Deleted after successful merge</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-orange-700">Hotfix Branches 🚨</CardTitle>
                      <CardDescription>Critical production fixes</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div><strong>Naming:</strong> <code className="bg-gray-100 px-1 rounded">hotfix/critical-issue-description</code></div>
                        <div><strong>Purpose:</strong> Critical production fixes</div>
                        <div><strong>Source:</strong> Created from <code className="bg-gray-100 px-1 rounded">main</code></div>
                        <div><strong>Target:</strong> Merged to both <code className="bg-gray-100 px-1 rounded">main</code> and <code className="bg-gray-100 px-1 rounded">development</code></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Development Process Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Clock className="h-8 w-8 text-blue-600" />
                🔄 Development Process
              </h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Starting New Feature Development</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <h4 className="font-semibold mb-2">1. Sync with latest development</h4>
                          <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                            <div>git checkout development</div>
                            <div>git pull origin development</div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">2. Create feature branch</h4>
                          <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                            <div>git checkout -b feature/employee-profile-enhancement</div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">3. Develop and commit</h4>
                          <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                            <div>git add .</div>
                            <div>git commit -m "feat: add employee profile photo upload"</div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">4. Push to remote</h4>
                          <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                            <div>git push origin feature/employee-profile-enhancement</div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">5. Create Pull Request</h4>
                        <ul className="text-sm space-y-1">
                          <li>• Target: <code className="bg-gray-100 px-1 rounded">development</code> branch</li>
                          <li>• Include: Feature description, testing notes, screenshots</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Code Review Process Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Users className="h-8 w-8 text-purple-600" />
                👥 Code Review Process
              </h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Pull Request Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-semibold mb-2">Required Elements</h4>
                        <div className="space-y-1 text-sm">
                          <div>✅ <strong>Description:</strong> Clear explanation of changes</div>
                          <div>✅ <strong>Testing:</strong> Evidence of testing (screenshots, test results)</div>
                          <div>✅ <strong>Documentation:</strong> Updated docs if needed</div>
                          <div>✅ <strong>No conflicts:</strong> Resolved merge conflicts</div>
                          <div>✅ <strong>CI/CD passing:</strong> All automated checks pass</div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Review Checklist</h4>
                        <div className="space-y-1 text-sm">
                          <div>☐ Code follows project standards</div>
                          <div>☐ No security vulnerabilities</div>
                          <div>☐ Performance considerations addressed</div>
                          <div>☐ Accessibility requirements met</div>
                          <div>☐ Mobile responsiveness verified</div>
                          <div>☐ Error handling implemented</div>
                          <div>☐ Tests added/updated</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Deployment Process Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🚀 Deployment Process</h2>

              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-blue-700">Development Deployment (Automatic)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="bg-blue-50 rounded p-3 font-mono text-sm">
                          feature/branch → development → staging environments
                        </div>
                        <div className="space-y-2 text-sm">
                          <div><strong>Trigger:</strong> Merge to <code className="bg-gray-100 px-1 rounded">development</code></div>
                          <div><strong>Environments:</strong></div>
                          <ul className="ml-4 space-y-1">
                            <li>• Vercel: <code className="bg-gray-100 px-1 rounded">tcm-enterprise-dev.vercel.app</code></li>
                            <li>• Railway: Development instance</li>
                          </ul>
                          <div><strong>Purpose:</strong> Testing and validation</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-red-700">Production Deployment (Manual)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="bg-red-50 rounded p-3 font-mono text-sm">
                          development → main → production environments
                        </div>
                        <div className="space-y-2 text-sm">
                          <div><strong>Trigger:</strong> Merge to <code className="bg-gray-100 px-1 rounded">main</code></div>
                          <div><strong>Environments:</strong></div>
                          <ul className="ml-4 space-y-1">
                            <li>• Vercel: <code className="bg-gray-100 px-1 rounded">tcm-enterprise.vercel.app</code></li>
                            <li>• Railway: Production instance</li>
                          </ul>
                          <div><strong>Requirements:</strong> 2 approvals, all checks pass</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Quality Gates Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <CheckCircle className="h-8 w-8 text-green-600" />
                📊 Quality Gates
              </h2>

              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Before Merging to Development</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-1 text-sm">
                        <div>☐ All tests pass</div>
                        <div>☐ Code coverage &gt; 80%</div>
                        <div>☐ No critical security vulnerabilities</div>
                        <div>☐ Performance benchmarks met</div>
                        <div>☐ Accessibility standards met</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Before Merging to Main</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-1 text-sm">
                        <div>☐ Staging deployment successful</div>
                        <div>☐ User acceptance testing completed</div>
                        <div>☐ Security review passed</div>
                        <div>☐ Performance testing passed</div>
                        <div>☐ Documentation updated</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Emergency Procedures */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <AlertCircle className="h-8 w-8 text-red-600" />
                🚨 Emergency Procedures
              </h2>

              <div className="space-y-6">
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <strong>Critical Production Issues:</strong> Follow the hotfix process for immediate production fixes.
                  </AlertDescription>
                </Alert>

                <Card>
                  <CardHeader>
                    <CardTitle>Critical Production Issues</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li><strong>Create hotfix branch</strong> from <code className="bg-gray-100 px-1 rounded">main</code></li>
                        <li><strong>Implement fix</strong> with minimal changes</li>
                        <li><strong>Test thoroughly</strong> on local environment</li>
                        <li><strong>Create emergency PR</strong> to <code className="bg-gray-100 px-1 rounded">main</code></li>
                        <li><strong>Deploy immediately</strong> after approval</li>
                        <li><strong>Merge back</strong> to <code className="bg-gray-100 px-1 rounded">development</code></li>
                      </ol>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Rollback Procedure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li><strong>Identify</strong> last known good deployment</li>
                        <li><strong>Revert</strong> to previous version via platform</li>
                        <li><strong>Create hotfix</strong> for permanent solution</li>
                        <li><strong>Document</strong> incident and resolution</li>
                      </ol>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Footer Navigation */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between">
                <Button variant="outline" asChild>
                  <Link href="/docs/developer">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Developer Docs
                  </Link>
                </Button>
                <div className="text-sm text-muted-foreground">
                  <Link href="/docs/developer/developer-guide" className="text-blue-600 hover:underline">← Developer Guide</Link>
                  {" | "}
                  <Link href="/docs/developer/quick-reference" className="text-blue-600 hover:underline">Quick Reference →</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

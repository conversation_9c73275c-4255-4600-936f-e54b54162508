"use client"

import Link from "next/link"
import { ArrowLeft, Shield, <PERSON>tings, Key, GitBranch, Server, Monitor } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function RepositorySetupPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-purple-900 to-purple-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full">
                Repository Setup
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Repository Setup Guide
            </h1>
            <p className="max-w-[700px] text-purple-100 md:text-xl/relaxed">
              GitHub configuration, branch protection, and deployment environment setup
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumb className="mb-8">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs/developer">Developer Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>Repository Setup</BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Navigation */}
            <div className="flex items-center gap-2 mb-8">
              <Button variant="outline" size="sm" asChild>
                <Link href="/docs/developer">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Developer Docs
                </Link>
              </Button>
            </div>

            {/* Initial Setup Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <GitBranch className="h-8 w-8 text-purple-600" />
                🚀 Initial Repository Setup
              </h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>1. Create Development Branch</CardTitle>
                    <CardDescription>Set up the development branch for staging deployments</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div className="text-green-400"># From main branch, create and push development branch</div>
                        <div>git checkout main</div>
                        <div>git pull origin main</div>
                        <div>git checkout -b development</div>
                        <div>git push -u origin development</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>2. Set Development as Default Branch (Temporarily)</CardTitle>
                    <CardDescription>Ensure new PRs target development by default</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li>Go to GitHub repository settings</li>
                        <li>Navigate to "Branches" section</li>
                        <li>Change default branch to <code className="bg-gray-100 px-1 rounded">development</code></li>
                        <li>This ensures new PRs target development by default</li>
                      </ol>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Branch Protection Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Shield className="h-8 w-8 text-red-600" />
                🔒 Branch Protection Rules
              </h2>

              <div className="space-y-6">
                <Alert className="border-red-200 bg-red-50">
                  <Shield className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <strong>Critical:</strong> Configure branch protection before allowing team access to prevent accidental direct commits to main.
                  </AlertDescription>
                </Alert>

                <Card>
                  <CardHeader>
                    <CardTitle>Main Branch Protection</CardTitle>
                    <CardDescription>Navigate to: Settings → Branches → Add rule</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Branch name pattern: <code className="bg-gray-100 px-2 py-1 rounded">main</code></h4>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Settings to enable:</h4>
                        <div className="grid gap-2 md:grid-cols-2">
                          <div className="space-y-1 text-sm">
                            <div>✅ Require a pull request before merging</div>
                            <div>✅ Require approvals (2 reviewers)</div>
                            <div>✅ Dismiss stale PR approvals when new commits are pushed</div>
                            <div>✅ Require review from code owners</div>
                            <div>✅ Require status checks to pass before merging</div>
                            <div>✅ Require branches to be up to date before merging</div>
                          </div>
                          <div className="space-y-1 text-sm">
                            <div>✅ Require conversation resolution before merging</div>
                            <div>✅ Restrict pushes that create files larger than 100MB</div>
                            <div>✅ Require signed commits</div>
                            <div>✅ Require linear history</div>
                            <div>✅ Include administrators (enforce rules for admins)</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Required status checks:</h4>
                        <div className="space-y-1 text-sm">
                          <div>• <code className="bg-blue-100 px-1 rounded">Lint, Type Check & Test</code></div>
                          <div>• <code className="bg-blue-100 px-1 rounded">Security Scan</code></div>
                          <div>• <code className="bg-blue-100 px-1 rounded">Accessibility Testing</code></div>
                          <div>• <code className="bg-blue-100 px-1 rounded">Performance Testing</code></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Development Branch Protection</CardTitle>
                    <CardDescription>Branch name pattern: development</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Settings to enable:</h4>
                        <div className="space-y-1 text-sm">
                          <div>✅ Require a pull request before merging</div>
                          <div>✅ Require approvals (1 reviewer)</div>
                          <div>✅ Require status checks to pass before merging</div>
                          <div>✅ Require branches to be up to date before merging</div>
                          <div>✅ Allow force pushes (for emergency fixes)</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Required status checks:</h4>
                        <div className="space-y-1 text-sm">
                          <div>• <code className="bg-blue-100 px-1 rounded">Lint, Type Check & Test</code></div>
                          <div>• <code className="bg-blue-100 px-1 rounded">Security Scan</code></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* GitHub Secrets Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Key className="h-8 w-8 text-yellow-600" />
                🔐 GitHub Secrets Configuration
              </h2>

              <div className="space-y-6">
                <Alert className="border-yellow-200 bg-yellow-50">
                  <Key className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    Navigate to: <strong>Settings → Secrets and variables → Actions</strong> to configure these secrets.
                  </AlertDescription>
                </Alert>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Vercel Configuration</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 font-mono text-sm">
                        <div className="bg-gray-100 p-2 rounded">VERCEL_TOKEN=your_vercel_token</div>
                        <div className="bg-gray-100 p-2 rounded">VERCEL_ORG_ID=your_vercel_org_id</div>
                        <div className="bg-gray-100 p-2 rounded">VERCEL_PROJECT_ID=your_vercel_project_id</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Railway Configuration</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 font-mono text-sm">
                        <div className="bg-gray-100 p-2 rounded">RAILWAY_TOKEN=your_railway_token</div>
                        <div className="bg-gray-100 p-2 rounded">RAILWAY_SERVICE_ID_DEV=your_dev_service_id</div>
                        <div className="bg-gray-100 p-2 rounded">RAILWAY_SERVICE_ID_PROD=your_prod_service_id</div>
                        <div className="bg-gray-100 p-2 rounded">RAILWAY_DEV_URL=https://your-dev-app.railway.app</div>
                        <div className="bg-gray-100 p-2 rounded">RAILWAY_PROD_URL=https://your-prod-app.railway.app</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Environment URLs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 font-mono text-sm">
                        <div className="bg-gray-100 p-2 rounded">DEV_API_URL=https://tcm-enterprise-dev.vercel.app/api</div>
                        <div className="bg-gray-100 p-2 rounded">PROD_API_URL=https://tcm-enterprise.vercel.app/api</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Security & Monitoring</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 font-mono text-sm">
                        <div className="bg-gray-100 p-2 rounded">SNYK_TOKEN=your_snyk_token</div>
                        <div className="bg-gray-100 p-2 rounded">LHCI_GITHUB_APP_TOKEN=your_lighthouse_token</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Environment Setup Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Settings className="h-8 w-8 text-blue-600" />
                🌍 Environment Secrets
              </h2>

              <div className="space-y-6">
                <p className="text-zinc-600">Create two environments with specific reviewers and deployment branches:</p>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-green-700">Development Environment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm">Configuration:</h4>
                          <ul className="text-sm space-y-1">
                            <li>• All development-specific secrets</li>
                            <li>• Reviewers: Development team</li>
                            <li>• Deployment branches: <code className="bg-gray-100 px-1 rounded">development</code></li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-red-700">Production Environment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm">Configuration:</h4>
                          <ul className="text-sm space-y-1">
                            <li>• All production-specific secrets</li>
                            <li>• Reviewers: Lead developers + Project manager</li>
                            <li>• Deployment branches: <code className="bg-gray-100 px-1 rounded">main</code></li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Deployment Setup Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Server className="h-8 w-8 text-green-600" />
                🚀 Deployment Setup
              </h2>

              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Vercel Configuration</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-sm mb-2">Development Environment</h4>
                          <ul className="text-sm space-y-1">
                            <li>1. Create new Vercel project for development</li>
                            <li>2. Connect to <code className="bg-gray-100 px-1 rounded">development</code> branch</li>
                            <li>3. Set environment variables for development</li>
                            <li>4. Configure custom domain: <code className="bg-gray-100 px-1 rounded">tcm-enterprise-dev.vercel.app</code></li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-sm mb-2">Production Environment</h4>
                          <ul className="text-sm space-y-1">
                            <li>1. Use existing Vercel project</li>
                            <li>2. Connect to <code className="bg-gray-100 px-1 rounded">main</code> branch</li>
                            <li>3. Set environment variables for production</li>
                            <li>4. Configure custom domain: <code className="bg-gray-100 px-1 rounded">tcm-enterprise.vercel.app</code></li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Railway Configuration</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-sm mb-2">Development Environment</h4>
                          <ul className="text-sm space-y-1">
                            <li>1. Create new Railway service for development</li>
                            <li>2. Connect to <code className="bg-gray-100 px-1 rounded">development</code> branch</li>
                            <li>3. Set environment variables for development</li>
                            <li>4. Configure custom domain if needed</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-sm mb-2">Production Environment</h4>
                          <ul className="text-sm space-y-1">
                            <li>1. Use existing Railway service</li>
                            <li>2. Connect to <code className="bg-gray-100 px-1 rounded">main</code> branch</li>
                            <li>3. Set environment variables for production</li>
                            <li>4. Configure custom domain if needed</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Setup Checklist */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Monitor className="h-8 w-8 text-indigo-600" />
                ✅ Setup Checklist
              </h2>

              <Card>
                <CardHeader>
                  <CardTitle>Complete Setup Verification</CardTitle>
                  <CardDescription>Ensure all components are properly configured</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-semibold mb-2">Repository Configuration</h4>
                      <div className="space-y-1 text-sm">
                        <div>☐ Development branch created and protected</div>
                        <div>☐ Main branch protection rules configured</div>
                        <div>☐ GitHub secrets configured</div>
                        <div>☐ Environment secrets configured</div>
                        <div>☐ Labels created</div>
                        <div>☐ PR and issue templates created</div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Deployment Configuration</h4>
                      <div className="space-y-1 text-sm">
                        <div>☐ Vercel environments configured</div>
                        <div>☐ Railway environments configured</div>
                        <div>☐ Git hooks configured</div>
                        <div>☐ VS Code settings configured</div>
                        <div>☐ CI/CD workflows tested</div>
                        <div>☐ Team access configured</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Footer Navigation */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between">
                <Button variant="outline" asChild>
                  <Link href="/docs/developer">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Developer Docs
                  </Link>
                </Button>
                <div className="text-sm text-muted-foreground">
                  Next: <Link href="/docs/developer/development-workflow" className="text-blue-600 hover:underline">Development Workflow</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

"use client"

import Link from "next/link"
import { ArrowRight, Code, BookOpen, Settings, GitBranch, Terminal, Layers, Zap } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"

export default function DeveloperDocsPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-blue-900 to-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full">
                Developer Documentation
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Developer Documentation
            </h1>
            <p className="max-w-[700px] text-blue-100 md:text-xl/relaxed">
              Complete development workflow, project structure, and technical guidelines for contributing to the TCM Enterprise Business Suite
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-8">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/developer">Developer Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Module Overview */}
              <div className="mb-12">
                <h2 className="text-3xl font-bold text-zinc-900 mb-4">Developer Documentation</h2>
                <p className="text-lg text-zinc-600 mb-6">
                  Comprehensive development workflow, project structure, and technical guidelines for contributing to the TCM Enterprise Business Suite. 
                  This documentation covers everything from getting started to advanced development practices.
                </p>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                  <div className="flex items-start gap-3">
                    <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-800 mb-2">New Developer? Start Here!</h3>
                      <p className="text-blue-700 mb-3">
                        Follow our comprehensive developer guide to get up and running quickly with the TCM Enterprise development workflow.
                      </p>
                      <Button size="sm" asChild>
                        <Link href="/docs/developer/developer-guide">
                          <BookOpen className="h-4 w-4 mr-2" />
                          Get Started
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Documentation Categories */}
              <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 mb-12">
                <Link href="/docs/developer/developer-guide">
                  <Card className="h-full transition-all hover:shadow-md border-emerald-200">
                    <CardHeader>
                      <BookOpen className="h-8 w-8 text-emerald-500 mb-2" />
                      <CardTitle>Complete Developer Guide</CardTitle>
                      <CardDescription>Everything you need to know to start contributing</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Comprehensive 750+ line guide covering setup, workflow, testing, and best practices.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Getting started and environment setup</div>
                        <div>• Branching strategy and git workflow</div>
                        <div>• Code review and testing guidelines</div>
                        <div>• Real-world examples and scenarios</div>
                        <div>• Troubleshooting and emergency procedures</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-emerald-600">
                        Read Developer Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/developer/quick-reference">
                  <Card className="h-full transition-all hover:shadow-md border-green-200">
                    <CardHeader>
                      <Terminal className="h-8 w-8 text-green-500 mb-2" />
                      <CardTitle>Quick Reference Card</CardTitle>
                      <CardDescription>Printable reference for daily commands</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Compact reference card with the most commonly used commands and workflows.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Daily git commands and workflows</div>
                        <div>• Branch types and naming conventions</div>
                        <div>• Testing and build commands</div>
                        <div>• Troubleshooting quick fixes</div>
                        <div>• Emergency procedures</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-green-600">
                        View Quick Reference <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/developer/repository-setup">
                  <Card className="h-full transition-all hover:shadow-md border-purple-200">
                    <CardHeader>
                      <Layers className="h-8 w-8 text-purple-500 mb-2" />
                      <CardTitle>Repository Setup</CardTitle>
                      <CardDescription>GitHub configuration and deployment setup</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Complete guide for setting up branch protection, CI/CD, and deployment environments.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Branch protection rules</div>
                        <div>• GitHub Actions setup</div>
                        <div>• Vercel and Railway configuration</div>
                        <div>• Security and monitoring setup</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                        Read Setup Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/developer/development-workflow">
                  <Card className="h-full transition-all hover:shadow-md border-indigo-200">
                    <CardHeader>
                      <GitBranch className="h-8 w-8 text-indigo-500 mb-2" />
                      <CardTitle>Development Workflow</CardTitle>
                      <CardDescription>Detailed branching strategy and process documentation</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        In-depth documentation of our branching strategy, commit standards, and release process.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Branching strategy and naming</div>
                        <div>• Commit message standards</div>
                        <div>• Code review process</div>
                        <div>• Release management</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-indigo-600">
                        Read Workflow Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>

              {/* Core Development Features */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-zinc-900 mb-6">Development Workflow Features</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <GitBranch className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Branching Strategy</h4>
                      <p className="text-sm text-zinc-600">Structured git workflow with main, development, and feature branches</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Settings className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Automated CI/CD</h4>
                      <p className="text-sm text-zinc-600">GitHub Actions workflows for testing, building, and deployment</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Terminal className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Quality Gates</h4>
                      <p className="text-sm text-zinc-600">Automated testing, linting, and security scanning</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Code className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Code Standards</h4>
                      <p className="text-sm text-zinc-600">Conventional commits, TypeScript, and code review requirements</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="bg-zinc-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-zinc-900 mb-4">Quick Links</h3>
                <div className="grid gap-3 md:grid-cols-2">
                  <Link href="/docs/developer/developer-guide" className="flex items-center text-sm text-blue-600 hover:text-blue-700">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Complete Developer Guide
                  </Link>
                  <Link href="/docs/developer/quick-reference" className="flex items-center text-sm text-blue-600 hover:text-blue-700">
                    <Terminal className="h-4 w-4 mr-2" />
                    Quick Reference Card
                  </Link>
                  <Link href="/docs/developer/repository-setup" className="flex items-center text-sm text-blue-600 hover:text-blue-700">
                    <Layers className="h-4 w-4 mr-2" />
                    Repository Setup Guide
                  </Link>
                  <Link href="/docs/developer/development-workflow" className="flex items-center text-sm text-blue-600 hover:text-blue-700">
                    <GitBranch className="h-4 w-4 mr-2" />
                    Development Workflow
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

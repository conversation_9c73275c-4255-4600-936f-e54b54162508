"use client"

import Link from "next/link"
import { ArrowLeft, Terminal, GitBranch, TestTube, AlertTriangle, Globe, Phone, Printer } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function QuickReferencePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-green-900 to-green-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                Quick Reference
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Developer Quick Reference Card
            </h1>
            <p className="max-w-[700px] text-green-100 md:text-xl/relaxed">
              Essential commands and workflows for daily development - print and keep handy!
            </p>
            <Button variant="secondary" className="mt-4" onClick={() => window.print()}>
              <Printer className="h-4 w-4 mr-2" />
              Print This Page
            </Button>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="max-w-6xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumb className="mb-8">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs/developer">Developer Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>Quick Reference</BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Navigation */}
            <div className="flex items-center gap-2 mb-8">
              <Button variant="outline" size="sm" asChild>
                <Link href="/docs/developer">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Developer Docs
                </Link>
              </Button>
            </div>

            {/* Daily Commands Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <Terminal className="h-8 w-8 text-green-600" />
                🚀 Daily Commands
              </h2>

              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Starting Work</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>git checkout development && git pull origin development</div>
                        <div>git checkout -b feature/your-feature-name</div>
                        <div>npm run dev</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Before Committing</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>npm run test:full</div>
                        <div>git add .</div>
                        <div>git commit -m "feat(scope): description"</div>
                        <div>git push origin feature/your-feature-name</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Emergency Hotfix</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>git checkout main && git pull origin main</div>
                        <div>git checkout -b hotfix/critical-issue</div>
                        <div className="text-red-400"># ... fix issue ...</div>
                        <div>npm run test:full</div>
                        <div>git commit -m "hotfix(scope): description"</div>
                        <div>git push origin hotfix/critical-issue</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Testing Commands</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>npm run lint              <span className="text-gray-400"># Check code quality</span></div>
                        <div>npm run lint:fix          <span className="text-gray-400"># Auto-fix issues</span></div>
                        <div>npm run type-check        <span className="text-gray-400"># TypeScript validation</span></div>
                        <div>npm run test              <span className="text-gray-400"># Run tests</span></div>
                        <div>npm run test:full         <span className="text-gray-400"># Complete test suite</span></div>
                        <div>npm run build             <span className="text-gray-400"># Production build</span></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Branch Types Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <GitBranch className="h-8 w-8 text-blue-600" />
                🌳 Branch Types
              </h2>

              <Card>
                <CardContent className="p-6">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 font-semibold">Branch</th>
                          <th className="text-left py-2 font-semibold">Purpose</th>
                          <th className="text-left py-2 font-semibold">Target</th>
                          <th className="text-left py-2 font-semibold">Protection</th>
                        </tr>
                      </thead>
                      <tbody className="text-sm">
                        <tr className="border-b">
                          <td className="py-2"><code className="bg-red-100 text-red-800 px-2 py-1 rounded">main</code></td>
                          <td className="py-2">Production</td>
                          <td className="py-2">-</td>
                          <td className="py-2">2 approvals</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2"><code className="bg-blue-100 text-blue-800 px-2 py-1 rounded">development</code></td>
                          <td className="py-2">Staging</td>
                          <td className="py-2">main</td>
                          <td className="py-2">1 approval</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2"><code className="bg-green-100 text-green-800 px-2 py-1 rounded">feature/*</code></td>
                          <td className="py-2">New features</td>
                          <td className="py-2">development</td>
                          <td className="py-2">CI checks</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2"><code className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">bugfix/*</code></td>
                          <td className="py-2">Bug fixes</td>
                          <td className="py-2">development</td>
                          <td className="py-2">CI checks</td>
                        </tr>
                        <tr>
                          <td className="py-2"><code className="bg-orange-100 text-orange-800 px-2 py-1 rounded">hotfix/*</code></td>
                          <td className="py-2">Critical fixes</td>
                          <td className="py-2">main + development</td>
                          <td className="py-2">Emergency</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Commit Types Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">📝 Commit Types</h2>

              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Commit Type Reference</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <code className="bg-green-100 text-green-800 px-2 py-1 rounded">feat</code>
                        <span className="text-sm text-zinc-600">New feature</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-red-100 text-red-800 px-2 py-1 rounded">fix</code>
                        <span className="text-sm text-zinc-600">Bug fix</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-blue-100 text-blue-800 px-2 py-1 rounded">docs</code>
                        <span className="text-sm text-zinc-600">Documentation</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-purple-100 text-purple-800 px-2 py-1 rounded">style</code>
                        <span className="text-sm text-zinc-600">Code formatting</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">refactor</code>
                        <span className="text-sm text-zinc-600">Code refactoring</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">test</code>
                        <span className="text-sm text-zinc-600">Add/update tests</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded">chore</code>
                        <span className="text-sm text-zinc-600">Maintenance</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Example Commit Messages</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 font-mono text-xs">
                      <div className="bg-gray-50 p-2 rounded">feat(auth): add 2FA</div>
                      <div className="bg-gray-50 p-2 rounded">fix(dashboard): chart rendering</div>
                      <div className="bg-gray-50 p-2 rounded">docs(api): update endpoints</div>
                      <div className="bg-gray-50 p-2 rounded">style(components): format buttons</div>
                      <div className="bg-gray-50 p-2 rounded">refactor(utils): optimize dates</div>
                      <div className="bg-gray-50 p-2 rounded">test(auth): add login tests</div>
                      <div className="bg-gray-50 p-2 rounded">chore(deps): update packages</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Troubleshooting Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6 flex items-center gap-2">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                🔧 Troubleshooting
              </h2>

              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Common Issues</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm mb-1">ChunkLoadError</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-2 font-mono text-xs">
                          rm -rf .next && npm run dev
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm mb-1">TypeScript Errors</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-2 font-mono text-xs">
                          <div>npm run type-check</div>
                          <div>npm run ts-check-fix</div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Dependency Issues</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-2 font-mono text-xs">
                          <div>rm -rf node_modules package-lock.json</div>
                          <div>npm install</div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Git Conflicts</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-2 font-mono text-xs">
                          <div>git fetch origin</div>
                          <div>git merge origin/development</div>
                          <div className="text-yellow-400"># Resolve conflicts in VS Code</div>
                          <div>npm run test:full</div>
                          <div>git commit -m "merge: resolve conflicts"</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Environment URLs & Contacts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm mb-2 flex items-center gap-1">
                          <Globe className="h-4 w-4" />
                          Environment URLs
                        </h4>
                        <div className="space-y-1 text-sm">
                          <div><strong>Local:</strong> <code>http://localhost:3000</code></div>
                          <div><strong>Staging:</strong> <code>tcm-enterprise-dev.vercel.app</code></div>
                          <div><strong>Production:</strong> <code>tcm-enterprise.vercel.app</code></div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm mb-2 flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          Emergency Contacts
                        </h4>
                        <div className="space-y-1 text-sm">
                          <div><strong>Lead Developer</strong> - Architecture & critical issues</div>
                          <div><strong>DevOps Engineer</strong> - Deployment & infrastructure</div>
                          <div><strong>Project Manager</strong> - Process & priorities</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Workflow Summary */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🔄 Complete Workflow Summary</h2>

              <Card>
                <CardHeader>
                  <CardTitle>14-Step Development Process</CardTitle>
                  <CardDescription>From feature start to production deployment</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">1</Badge>
                        <span>git checkout development && git pull origin development</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">2</Badge>
                        <span>git checkout -b feature/your-feature</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">3</Badge>
                        <span># ... develop your feature ...</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">4</Badge>
                        <span>npm run test:full</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">5</Badge>
                        <span>git commit -m "feat(scope): description"</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">6</Badge>
                        <span>git push origin feature/your-feature</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">7</Badge>
                        <span>Create PR via GitHub UI</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">8</Badge>
                        <span>Wait for review and CI checks</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">9</Badge>
                        <span>Merge to development</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">10</Badge>
                        <span>Deploy to staging automatically</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">11</Badge>
                        <span>Test on staging environment</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">12</Badge>
                        <span>Create PR from development to main</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">13</Badge>
                        <span>Get approvals and merge to main</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">14</Badge>
                        <span>Deploy to production automatically</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Print Footer */}
            <div className="border-t pt-6 print:block">
              <div className="text-center text-sm text-muted-foreground">
                <p><strong>TCM Enterprise Suite - Developer Quick Reference Card</strong></p>
                <p>Print this page and keep it handy! • Last updated: January 2024</p>
              </div>
            </div>

            {/* Footer Navigation */}
            <div className="border-t pt-6 print:hidden">
              <div className="flex items-center justify-between">
                <Button variant="outline" asChild>
                  <Link href="/docs/developer">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Developer Docs
                  </Link>
                </Button>
                <div className="text-sm text-muted-foreground">
                  Next: <Link href="/docs/developer/repository-setup" className="text-blue-600 hover:underline">Repository Setup</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

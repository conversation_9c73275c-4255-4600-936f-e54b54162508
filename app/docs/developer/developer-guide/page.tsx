"use client"

import Link from "next/link"
import { ArrowLeft, BookOpen, Code, GitBranch, Terminal, Users, Zap, CheckCircle, AlertCircle, Info } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocsMobileNav } from "@/components/docs/mobile-nav"

export default function DeveloperGuidePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-blue-900 to-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full">
                Developer Documentation
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Complete Developer Guide
            </h1>
            <p className="max-w-[700px] text-blue-100 md:text-xl/relaxed">
              Everything you need to know to start contributing to the TCM Enterprise Business Suite
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumb className="mb-8">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs/developer">Developer Documentation</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>Developer Guide</BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Navigation */}
            <div className="flex items-center gap-2 mb-8">
              <Button variant="outline" size="sm" asChild>
                <Link href="/docs/developer">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Developer Docs
                </Link>
              </Button>
            </div>

            {/* Quick Start Alert */}
            <Alert className="mb-8 border-emerald-200 bg-emerald-50">
              <Zap className="h-4 w-4 text-emerald-600" />
              <AlertDescription className="text-emerald-800">
                <strong>New Developer?</strong> This comprehensive guide will walk you through everything you need to know to start contributing to the TCM Enterprise project.
              </AlertDescription>
            </Alert>

            {/* Getting Started Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🚀 Getting Started</h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-emerald-500" />
                      Prerequisites
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-semibold mb-2">Required Software</h4>
                        <ul className="space-y-1 text-sm text-zinc-600">
                          <li>• Node.js 18+ - <a href="https://nodejs.org/" className="text-blue-600 hover:underline">Download here</a></li>
                          <li>• Git 2.30+ - <a href="https://git-scm.com/" className="text-blue-600 hover:underline">Download here</a></li>
                          <li>• VS Code (recommended) - <a href="https://code.visualstudio.com/" className="text-blue-600 hover:underline">Download here</a></li>
                          <li>• GitHub account with repository access</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Initial Setup Commands</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Clone repository</div>
                            <div><NAME_EMAIL>:winstonmhango23/lasttcmsuit.git</div>
                            <div>cd lasttcmsuit</div>
                            <div className="text-green-400"># Install dependencies</div>
                            <div>npm install</div>
                            <div className="text-green-400"># Start development</div>
                            <div>npm run dev</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Branching Strategy Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🌳 Understanding Our Branching Strategy</h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <GitBranch className="h-5 w-5 text-blue-500" />
                      Branch Types
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border-l-4 border-red-500 pl-4">
                        <h4 className="font-semibold text-red-700">main Branch 🚀</h4>
                        <p className="text-sm text-zinc-600">Production code only. Requires 2 approvals, all CI checks must pass. ❌ NEVER commit directly.</p>
                      </div>
                      <div className="border-l-4 border-blue-500 pl-4">
                        <h4 className="font-semibold text-blue-700">development Branch 🧪</h4>
                        <p className="text-sm text-zinc-600">Integration and testing. Requires 1 approval, basic CI checks. ✅ Most PRs go here.</p>
                      </div>
                      <div className="border-l-4 border-green-500 pl-4">
                        <h4 className="font-semibold text-green-700">feature/* Branches ✨</h4>
                        <p className="text-sm text-zinc-600">New feature development. Created from development, merged back to development.</p>
                      </div>
                      <div className="border-l-4 border-yellow-500 pl-4">
                        <h4 className="font-semibold text-yellow-700">bugfix/* Branches 🐛</h4>
                        <p className="text-sm text-zinc-600">Non-critical bug fixes. Examples: bugfix/login-form-validation</p>
                      </div>
                      <div className="border-l-4 border-orange-500 pl-4">
                        <h4 className="font-semibold text-orange-700">hotfix/* Branches 🚨</h4>
                        <p className="text-sm text-zinc-600">Critical production fixes only. Merges to both main and development.</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Branch Structure Visualization</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div>main (production) ← Manual merge only</div>
                        <div>├── development (staging) ← Auto-deploy to staging</div>
                        <div>│   ├── feature/employee-management-v2</div>
                        <div>│   ├── feature/hr-workflows</div>
                        <div>│   ├── feature/accounting-reports</div>
                        <div>│   ├── bugfix/login-validation</div>
                        <div>│   └── hotfix/security-patch</div>
                        <div>└── hotfix/critical-production-fix (emergency only)</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Daily Workflow Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🔄 Daily Development Workflow</h2>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Terminal className="h-5 w-5 text-green-500" />
                      Starting a New Feature
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Step 1: Sync with Development</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Always start with latest development</div>
                            <div>git checkout development</div>
                            <div>git pull origin development</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Step 2: Create Feature Branch</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Create and switch to feature branch</div>
                            <div>git checkout -b feature/employee-search-functionality</div>
                            <div className="text-green-400"># Push to remote to track</div>
                            <div>git push -u origin feature/employee-search-functionality</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Step 3: Develop Your Feature</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Make your changes</div>
                            <div>code .</div>
                            <div className="text-green-400"># Test your changes</div>
                            <div>npm run dev</div>
                            <div>npm run test</div>
                            <div>npm run lint</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Step 4: Commit Your Changes</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Stage your changes</div>
                            <div>git add .</div>
                            <div className="text-green-400"># Commit with conventional format</div>
                            <div>git commit -m "feat(employees): add advanced search functionality</div>
                            <div></div>
                            <div>- Add search by name, department, and role</div>
                            <div>- Implement debounced search input</div>
                            <div>- Add search result highlighting</div>
                            <div>- Include pagination for large result sets"</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Step 5: Push and Create PR</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div className="space-y-1">
                            <div className="text-green-400"># Push your changes</div>
                            <div>git push origin feature/employee-search-functionality</div>
                            <div className="text-green-400"># Go to GitHub and create Pull Request</div>
                            <div># Target: development branch</div>
                            <div># Fill out the PR template completely</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Testing Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">🧪 Testing Your Code</h2>

              <div className="space-y-6">
                <Alert className="border-blue-200 bg-blue-50">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    <strong>Before Every Commit:</strong> Always run the full test suite to ensure your changes don't break existing functionality.
                  </AlertDescription>
                </Alert>

                <Card>
                  <CardHeader>
                    <CardTitle>Essential Test Commands</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-semibold mb-2">Full Test Suite</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm">
                          <div>npm run test:full</div>
                        </div>
                        <p className="text-xs text-zinc-600 mt-1">Runs linting, type checking, tests, and build verification</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Individual Commands</h4>
                        <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-sm space-y-1">
                          <div>npm run lint</div>
                          <div>npm run type-check</div>
                          <div>npm run test</div>
                          <div>npm run build</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Commit Standards Section */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-zinc-900 mb-6">📝 Commit Message Standards</h2>

              <Card>
                <CardHeader>
                  <CardTitle>Conventional Commit Format</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-gray-50 rounded p-3 font-mono text-sm">
                      &lt;type&gt;(&lt;scope&gt;): &lt;description&gt;<br/><br/>
                      [optional body]<br/><br/>
                      [optional footer]
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-semibold mb-2">Commit Types</h4>
                        <ul className="space-y-1 text-sm">
                          <li><code className="bg-gray-100 px-1 rounded">feat</code> - New feature</li>
                          <li><code className="bg-gray-100 px-1 rounded">fix</code> - Bug fix</li>
                          <li><code className="bg-gray-100 px-1 rounded">docs</code> - Documentation changes</li>
                          <li><code className="bg-gray-100 px-1 rounded">style</code> - Code style changes</li>
                          <li><code className="bg-gray-100 px-1 rounded">refactor</code> - Code refactoring</li>
                          <li><code className="bg-gray-100 px-1 rounded">test</code> - Adding tests</li>
                          <li><code className="bg-gray-100 px-1 rounded">chore</code> - Maintenance tasks</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Good Examples</h4>
                        <div className="space-y-1 text-xs font-mono">
                          <div>feat(auth): add two-factor authentication</div>
                          <div>fix(dashboard): resolve sidebar navigation</div>
                          <div>docs(api): update employee endpoints</div>
                          <div>style(components): format button components</div>
                          <div>refactor(utils): optimize date formatting</div>
                          <div>test(auth): add login form tests</div>
                          <div>chore(deps): update dependencies</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Footer Navigation */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between">
                <Button variant="outline" asChild>
                  <Link href="/docs/developer">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Developer Docs
                  </Link>
                </Button>
                <div className="text-sm text-muted-foreground">
                  Next: <Link href="/docs/developer/quick-reference" className="text-blue-600 hover:underline">Quick Reference</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

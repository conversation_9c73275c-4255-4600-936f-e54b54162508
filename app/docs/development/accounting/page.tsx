"use client"

import { <PERSON>Content } from "@/components/docs/markdown-renderer"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CodeBlock } from "@/components/docs/code-block"

export default function AccountingDevelopmentGuidePage() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <section className="w-full py-12 md: py-24 lg:py-32 bg-gradient-to-b from-emerald-50 to-white">
        <DocsPatternBackground className="absolute inset-0 z-0" />
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none text-emerald-800">
                Accounting System - Developer Guide
              </h1>
              <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl">
                Technical documentation for developers working on the TCM Enterprise Business Suite Accounting Module
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="md:hidden">
              <DocsMobileNav />
            </div>
            <div className="flex-1">
              <Tabs defaultValue="architecture" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="architecture">Architecture</TabsTrigger>
                  <TabsTrigger value="models">Data Models</TabsTrigger>
                  <TabsTrigger value="api">API Routes</TabsTrigger>
                  <TabsTrigger value="components">Components</TabsTrigger>
                </TabsList>
                
                {/* Architecture Tab */}
                <TabsContent value="architecture" className="space-y-6">
                  <DocContent title="Accounting System Architecture">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">System Overview</h2>
                    <p className="mb-4">
                      The Accounting Module follows a layered architecture pattern with clear separation of concerns:
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Frontend Layer</CardTitle>
                          <CardDescription>User interface components and state management</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ul className="list-disc pl-6 space-y-1">
                            <li>React components for UI rendering</li>
                            <li>Zustand stores for state management</li>
                            <li>Form validation using React Hook Form</li>
                            <li>Data visualization with Recharts</li>
                          </ul>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle>API Layer</CardTitle>
                          <CardDescription>RESTful API endpoints for data access</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ul className="list-disc pl-6 space-y-1">
                            <li>Next.js API routes for handling requests</li>
                            <li>Authentication and permission checks</li>
                            <li>Request validation and error handling</li>
                            <li>Response formatting and status codes</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Service Layer</CardTitle>
                          <CardDescription>Business logic and data processing</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ul className="list-disc pl-6 space-y-1">
                            <li>Service classes for business logic</li>
                            <li>Transaction management</li>
                            <li>Data validation and transformation</li>
                            <li>Integration with external systems</li>
                          </ul>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle>Data Layer</CardTitle>
                          <CardDescription>Data models and database access</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ul className="list-disc pl-6 space-y-1">
                            <li>Mongoose schemas for MongoDB</li>
                            <li>Data models with validation</li>
                            <li>Indexes for query optimization</li>
                            <li>Relationships between models</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </div>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Directory Structure</h2>
                    <CodeBlock language="text" code={`
/app/(dashboard)/accounting/  # Next.js pages for accounting module
/app/api/accounting/          # API routes for accounting module
/components/accounting/       # React components for accounting UI
/lib/services/accounting/     # Service classes for business logic
/models/accounting/           # Mongoose models for accounting data
/lib/frontend/                # Zustand stores for state management
                    `} />

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">State Management</h2>
                    <p className="mb-4">
                      The accounting module uses Zustand for state management. Each major feature has its own store
                      for managing related state. Stores implement data caching with a 1-hour duration to improve performance.
                    </p>
                    
                    <CodeBlock language="typescript" code={`
// Example Zustand store structure
interface AccountingStore {
  // State
  accounts: Account[];
  selectedAccount: Account  | undefined;
  isLoading: boolean;
  error: string  | undefined;

  // Cache
  accountsCache: Record<string, { data: Account[]; timestamp: number }>;
  accountCache: Record<string, { data: Account; timestamp: number }>;
  cacheDuration: number;

  // Actions
  fetchAccounts: () => Promise<void>;
  fetchAccount: (id: string) => Promise<void>;
  createAccount: (data: AccountCreateData) => Promise<void>;
  updateAccount: (id: string, data: AccountUpdateData) => Promise<void>;
  deleteAccount: (id: string) => Promise<void>;
}
                    `} />
                  </DocContent>
                </TabsContent>
                
                {/* Data Models Tab */}
                <TabsContent value="models" className="space-y-6">
                  <DocContent title="Accounting Data Models">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Core Models</h2>
                    <p className="mb-4">
                      The accounting module uses the following core data models:
                    </p>
                    
                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Account Model</h3>
                    <p className="mb-4">
                      The Account model represents entries in the chart of accounts.
                    </p>
                    <CodeBlock language="typescript" code={`
interface IAccountModel extends Document {
  accountNumber: string;
  name: string;
  type: string;
  subtype?: string;
  description?: string;
  balance: number;
  isActive: boolean;
  parentAccount?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
                    `} />

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Transaction Model</h3>
                    <p className="mb-4">
                      The Transaction model represents financial transactions.
                    </p>
                    <CodeBlock language="typescript" code={`
interface ITransactionModel extends Document {
  reference: string;
  date: Date;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  category: string;
  account: mongoose.Types.ObjectId;
  status: 'pending' | 'completed' | 'cancelled';
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
                    `} />

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Journal Model</h3>
                    <p className="mb-4">
                      The Journal model represents journal entries.
                    </p>
                    <CodeBlock language="typescript" code={`
interface IJournalModel extends Document {
  reference: string;
  date: Date;
  description: string;
  entries: Array<{
    account: mongoose.Types.ObjectId;
    description?: string;
    debit: number;
    credit: number;
  }>;
  status: 'draft' | 'posted' | 'voided';
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
                    `} />

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Relationships</h2>
                    <p className="mb-4">
                      The accounting models are related to each other in the following ways:
                    </p>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li>Accounts can have parent accounts (hierarchical structure)</li>
                      <li>Transactions reference accounts</li>
                      <li>Journal entries reference accounts</li>
                      <li>Vouchers reference transactions and journal entries</li>
                      <li>Budget items reference accounts</li>
                    </ul>
                  </DocContent>
                </TabsContent>
                
                {/* API Routes Tab */}
                <TabsContent value="api" className="space-y-6">
                  <DocContent title="Accounting API Routes">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">API Structure</h2>
                    <p className="mb-4">
                      The accounting module exposes the following API endpoints:
                    </p>
                    
                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Dashboard API</h3>
                    <CodeBlock language="text" code={`
GET /api/accounting/dashboard
- Returns financial dashboard data
- Query parameters: from, to, fiscalYear
                    `} />

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Accounts API</h3>
                    <CodeBlock language="text" code={`
GET /api/accounting/accounts
- Returns list of accounts
- Query parameters: type, isActive, search

GET /api/accounting/accounts/:id
- Returns a specific account

POST /api/accounting/accounts
- Creates a new account
- Body: account data

PATCH /api/accounting/accounts/:id
- Updates an existing account
- Body: account data

DELETE /api/accounting/accounts/:id
- Deletes an account
                    `} />

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Transactions API</h3>
                    <CodeBlock language="text" code={`
GET /api/accounting/transactions
- Returns list of transactions
- Query parameters: type, status, from, to, account

GET /api/accounting/transactions/:id
- Returns a specific transaction

POST /api/accounting/transactions
- Creates a new transaction
- Body: transaction data

PATCH /api/accounting/transactions/:id
- Updates an existing transaction
- Body: transaction data

DELETE /api/accounting/transactions/:id
- Deletes a transaction
                    `} />

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Journal API</h3>
                    <CodeBlock language="text" code={`
GET /api/accounting/journal
- Returns list of journal entries
- Query parameters: status, from, to

GET /api/accounting/journal/:id
- Returns a specific journal entry

POST /api/accounting/journal
- Creates a new journal entry
- Body: journal entry data

PATCH /api/accounting/journal/:id
- Updates an existing journal entry
- Body: journal entry data

POST /api/accounting/journal/:id/post
- Posts a journal entry

POST /api/accounting/journal/:id/void
- Voids a journal entry
                    `} />
                  </DocContent>
                </TabsContent>
                
                {/* Components Tab */}
                <TabsContent value="components" className="space-y-6">
                  <DocContent title="Accounting UI Components">
                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Component Structure</h2>
                    <p className="mb-4">
                      The accounting module uses a component-based architecture with reusable UI components:
                    </p>
                    
                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Layout Components</h3>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li><strong>accounting-nav.tsx</strong> - Navigation sidebar for accounting module</li>
                      <li><strong>accounting-dashboard-page.tsx</strong> - Main dashboard page wrapper</li>
                    </ul>

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Form Components</h3>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li><strong>bank-account-form.tsx</strong> - Form for creating/editing bank accounts</li>
                      <li><strong>voucher-form.tsx</strong> - Form for creating/editing vouchers</li>
                      <li><strong>journal-entry-form.tsx</strong> - Form for creating/editing journal entries</li>
                    </ul>

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Table Components</h3>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li><strong>data-table.tsx</strong> - Reusable table component with sorting and filtering</li>
                      <li><strong>expense-table.tsx</strong> - Table for displaying expenses</li>
                    </ul>

                    <h3 className="text-xl font-semibold mt-6 mb-3 text-emerald-600">Chart Components</h3>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li><strong>financial-dashboard.tsx</strong> - Dashboard with charts and metrics</li>
                    </ul>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Component Best Practices</h2>
                    <ul className="list-disc pl-6 space-y-2 mb-6">
                      <li>Use TypeScript interfaces for component props</li>
                      <li>Implement proper error handling and loading states</li>
                      <li>Create reusable components for common patterns</li>
                      <li>Use React Hook Form for form validation</li>
                      <li>Implement responsive design for all components</li>
                      <li>Use Zustand stores for state management</li>
                      <li>Implement data caching for improved performance</li>
                    </ul>

                    <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Example Component</h2>
                    <CodeBlock language="tsx" code={`
// Example of a reusable accounting component
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';

interface AccountBalanceCardProps {
  title: string;
  balance: number;
  previousBalance?: number;
  currency?: string;
  isLoading?: boolean;
}

export function AccountBalanceCard({
  title,
  balance,
  previousBalance,
  currency = 'MWK',
  isLoading = false
}: AccountBalanceCardProps) {
  const percentChange = previousBalance
    ? ((balance - previousBalance) / previousBalance) * 100
    : 0;
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-6 w-24 bg-muted animate-pulse rounded" />
        ) : (
          <>
            <div className="text-2xl font-bold">
              {formatCurrency(balance, currency)}
            </div>
            {previousBalance !== undefined && (
              <p className={\`text-xs \${percentChange >= 0 ? 'text-green-500' : 'text-red-500'}\`}>
                {percentChange >= 0 ? '↑' : '↓'} {Math.abs(percentChange).toFixed(1)}%
              </p>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
                    `} />
                  </DocContent>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

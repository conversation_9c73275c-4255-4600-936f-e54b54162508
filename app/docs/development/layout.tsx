"use client"

import { DocSideNav } from "@/components/docs/navigation"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import DocsPatternBackground from "@/components/docs/pattern-background"

interface DevelopmentLayoutProps {
  children: React.ReactNode
}

export default function DevelopmentLayout({ children }: DevelopmentLayoutProps) {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-12 md:py-16 bg-gradient-to-b from-emerald-900 to-emerald-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                Development
              </span>
            </div>
            <h1 className="text-3xl md:text-5xl font-bold tracking-tighter text-white">
              Development <span className="text-emerald-400">Status</span>
            </h1>
            <p className="max-w-[700px] text-zinc-400 md:text-xl/relaxed">
              Track the implementation status of various modules in the TCM Enterprise Business Suite
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {children}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

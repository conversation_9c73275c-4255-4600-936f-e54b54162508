"use client"

import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { DocContent } from "@/components/docs/markdown-renderer"

export default function DevelopmentPage() {
  return (
    <div className="container px-4 md:px-6 py-8">
      <div className="flex flex-col gap-8">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/docs">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Documentation
            </Link>
          </Button>
        </div>

        <DocContent title="Development Status">
          <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Overview</h2>
          <p className="mb-4">
            This section tracks the development status of various modules in the TCM Enterprise Business Suite.
            Each module has two pages: one for pending implementation items and another for completed items.
          </p>

          <h2 className="text-2xl font-semibold mt-6 mb-4 text-emerald-700">Module Status</h2>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-8">
            {/* Accounting Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Accounting Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the accounting system, including financial dashboard, budget planning, income management, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/accounting">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_accounting">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Payroll Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Payroll Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the payroll system, including salary processing, tax management, accounting integration, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/payroll">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_payroll">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Inventory Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Inventory Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the inventory system, including stock management, equipment tracking, asset management, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/inventory">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_inventory">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Employee Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Employee Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the employee system, including salary management, leave management, loan management, tax management, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/employee">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_employee">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Attendance Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Attendance Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the attendance system, including time tracking, shift management, leave integration, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/attendance">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_attendance">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Task Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Task Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the task management system, including task tracking, collaboration, notifications, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/task">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_task">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Recruitment Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Recruitment Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the recruitment system, including job management, candidate tracking, interview scheduling, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/recruitment">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_recruitment">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* CRM Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">CRM Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the customer relationship management system, including customer management, deal tracking, lead management, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/crm">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_crm">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Document Management Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Document Management Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the document management system, including document repository, versioning, workflows, security, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/document_management">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_document_management">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Knowledge Base & Help Desk Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Knowledge Base & Help Desk Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the knowledge base and help desk system, including article management, ticketing, self-service portal, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/knowledge_base_help_desk">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_knowledge_base_help_desk">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* E-commerce Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">E-commerce Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the e-commerce system, including product management, storefront, shopping cart, order processing, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/ecommerce">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_ecommerce">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Learning Management System Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Learning Management System Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the learning management system, including course management, assessments, certifications, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/lms">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_lms">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Business Intelligence & Analytics Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Business Intelligence & Analytics Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the business intelligence system, including data warehouse, dashboards, reports, analytics, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/business_intelligence">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_business_intelligence">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Vendor/Supplier Management Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Vendor/Supplier Management Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the vendor management system, including supplier data, procurement, contracts, performance tracking, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/vendor_management">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_vendor_management">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Asset Management Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Asset Management Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the asset management system, including asset registry, maintenance, depreciation, tracking, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/asset_management">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_asset_management">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Compliance & Audit Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Compliance & Audit Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the compliance and audit system, including policy management, risk assessment, audit processes, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/compliance_audit">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_compliance_audit">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Communication & Messaging Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Communication & Messaging Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the communication system, including messaging, notifications, announcements, chat, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/communication_messaging">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_communication_messaging">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Project Management Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Project Management Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the project management system, including project planning, task management, resource allocation, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/project_management">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed_project_management">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Assessment Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Assessment Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the assessment system, including skills assessments, personality tests, knowledge tests, and performance evaluations.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/assessment">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed/assessment_onboarding">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Onboarding Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Onboarding Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the onboarding system, including task management, documentation, training, and feedback during the onboarding period.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/onboarding">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed/assessment_onboarding">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Loan Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Loan Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the loan management system, including loan applications, approvals, repayments, and integration with payroll.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/LOAN_MODULE_DEVELOPMENT_TRACKER.md">
                    <span>Pending Items</span>
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/docs/development/current_status/completed/CORE_MODULES_IMPLEMENTATION_STATUS.md">
                    <span>Completed Items</span>
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Security Module */}
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">Security Module</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  Track the implementation status of the security system, including user authentication, device tracking, and more.
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" disabled>
                  <span>Pending Items</span>
                </Button>
                <Button variant="outline" size="sm" disabled>
                  <span>Completed Items</span>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </DocContent>
      </div>
    </div>
  )
}

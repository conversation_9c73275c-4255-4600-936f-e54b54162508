import { Metadata } from 'next'
import { ArrowLeft, Users, Building, DollarSign, BarChart3 } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'Role Management User Guide | TCM Enterprise Suite',
  description: 'Complete guide for managing TCM organizational roles, role-based salary management, and organizational structure.',
}

export default function RoleManagementPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          {/* Back Link */}
          <Link
            href="/docs"
            className="inline-flex items-center text-sm font-medium text-emerald-600 hover:text-emerald-700 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Documentation
          </Link>

          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <Users className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-zinc-900">Role Management User Guide</h1>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">
                  🎉 TCM Roles Complete
                </Badge>
                <Badge variant="outline">User Guide</Badge>
              </div>
            </div>
          </div>

          <p className="text-lg text-zinc-600 mb-8">
            Complete guide for managing the TCM organizational role system with all 31 roles successfully imported and ready for use.
          </p>
        </div>

        {/* Success Banner */}
        <Card className="mb-8 border-emerald-200 bg-emerald-50">
          <CardHeader>
            <CardTitle className="text-emerald-800 flex items-center gap-2">
              🎉 Welcome to TCM Role Management System!
            </CardTitle>
            <CardDescription className="text-emerald-700">
              <strong>Congratulations!</strong> The TCM Enterprise Suite now includes a complete role management system with all 31 organizational roles successfully imported and ready for use.
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Quick Navigation */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <Building className="h-8 w-8 text-emerald-500 mb-2" />
              <CardTitle className="text-lg">TCM Structure</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-zinc-600 mb-3">
                Complete organizational hierarchy from TCM 1 (Executive) to TCM 12 (Support)
              </p>
              <div className="space-y-1 text-xs text-zinc-500">
                <div>• Executive Level (TCM 1)</div>
                <div>• Director Level (TCM 2)</div>
                <div>• Manager Level (TCM 3)</div>
                <div>• Officer Levels (TCM 4-5)</div>
                <div>• Support Levels (TCM 7-12)</div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <Users className="h-8 w-8 text-emerald-500 mb-2" />
              <CardTitle className="text-lg">Role Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-zinc-600 mb-3">
                Full CRUD operations for organizational roles with advanced features
              </p>
              <div className="space-y-1 text-xs text-zinc-500">
                <div>• View and search roles</div>
                <div>• Create and edit roles</div>
                <div>• Bulk import/export</div>
                <div>• Role status management</div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <DollarSign className="h-8 w-8 text-emerald-500 mb-2" />
              <CardTitle className="text-lg">Salary Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-zinc-600 mb-3">
                Role-based salary validation and automatic suggestions
              </p>
              <div className="space-y-1 text-xs text-zinc-500">
                <div>• Salary band validation</div>
                <div>• Automatic suggestions</div>
                <div>• Compliance monitoring</div>
                <div>• Exception handling</div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <BarChart3 className="h-8 w-8 text-emerald-500 mb-2" />
              <CardTitle className="text-lg">Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-zinc-600 mb-3">
                Role-based reporting and organizational analytics
              </p>
              <div className="space-y-1 text-xs text-zinc-500">
                <div>• Organizational charts</div>
                <div>• Role distribution</div>
                <div>• Salary analysis</div>
                <div>• Department reports</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Sections */}
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Getting Started */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-emerald-700">🚀 Getting Started</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Accessing Role Management</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm text-zinc-600">
                  <li>Login to the TCM Enterprise Suite</li>
                  <li>Navigate to: <strong>HR → Employee → Roles</strong></li>
                  <li>View the complete list of organizational roles</li>
                </ol>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Interface Overview</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-zinc-600">
                  <li>Role List: View all organizational roles</li>
                  <li>Search & Filter: Find specific roles quickly</li>
                  <li>Role Details: View comprehensive information</li>
                  <li>Management Actions: Create, edit, manage roles</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* TCM Organizational Structure */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-emerald-700">🏢 TCM Organizational Structure</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Role Hierarchy Available</h4>
                <div className="space-y-2 text-sm">
                  <div className="p-2 bg-emerald-50 rounded">
                    <strong>Executive Level</strong><br />
                    <span className="text-zinc-600">TCM 1: Registrar (Chief Executive Officer)</span>
                  </div>
                  <div className="p-2 bg-blue-50 rounded">
                    <strong>Director Level</strong><br />
                    <span className="text-zinc-600">TCM 2: Directors of Registration, Compliance</span>
                  </div>
                  <div className="p-2 bg-purple-50 rounded">
                    <strong>Manager Level</strong><br />
                    <span className="text-zinc-600">TCM 3: Finance Manager, HR Manager, etc.</span>
                  </div>
                  <div className="p-2 bg-orange-50 rounded">
                    <strong>Officer Levels</strong><br />
                    <span className="text-zinc-600">TCM 4-5: Senior Officers, Officers, Specialists</span>
                  </div>
                  <div className="p-2 bg-gray-50 rounded">
                    <strong>Support Levels</strong><br />
                    <span className="text-zinc-600">TCM 7-12: Assistants, Support Staff</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Role Management Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-emerald-700">📋 Managing Roles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Viewing Role Information</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm text-zinc-600">
                  <li>Go to HR → Employee → Roles</li>
                  <li>Click on any role name to view details</li>
                  <li>Review role information including TCM code, department, description</li>
                </ol>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Search and Filter Options</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-zinc-600">
                  <li><strong>Search by:</strong> Role Name, TCM Code, Department</li>
                  <li><strong>Filter by:</strong> Active/Inactive status, Department</li>
                  <li><strong>Sort by:</strong> Name, TCM Code, Department</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Role-Employee Integration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-emerald-700">👥 Role-Employee Integration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Current Status</h4>
                <p className="text-sm text-zinc-600 mb-2">
                  Role assignment interface is being implemented in the next development phase.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Coming Soon</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-zinc-600">
                  <li>Role Dropdown: Select from 31 available TCM roles</li>
                  <li>Automatic Validation: Role-department compatibility</li>
                  <li>Salary Suggestions: Recommended ranges based on role</li>
                  <li>Promotion Tracking: History of role changes</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Role-Based Salary Validation</h4>
                <div className="text-sm text-zinc-600 space-y-1">
                  <div>• <strong>TCM 1 (Registrar):</strong> MWK 2,500,000 - 3,500,000</div>
                  <div>• <strong>TCM 3 (Manager):</strong> MWK 1,500,000 - 2,200,000</div>
                  <div>• <strong>TCM 5 (Officer):</strong> MWK 800,000 - 1,400,000</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <Link
            href="/dashboard/employee/roles"
            className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
          >
            <Users className="h-4 w-4 mr-2" />
            Access Role Management
          </Link>
          <Link
            href="/docs/hr"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            HR System Guide
          </Link>
          <Link
            href="/docs/bulk-import"
            className="inline-flex items-center px-4 py-2 border border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            Bulk Import Guide
          </Link>
        </div>

        {/* Support Section */}
        <Card className="mt-8 border-zinc-200">
          <CardHeader>
            <CardTitle className="text-xl">📞 Support and Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-2">Related Guides</h4>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li><Link href="/docs/bulk-import" className="text-emerald-600 hover:text-emerald-700">Bulk Import System Guide</Link></li>
                  <li><Link href="/docs/employees" className="text-emerald-600 hover:text-emerald-700">Employee Management Guide</Link></li>
                  <li><Link href="/docs/hr" className="text-emerald-600 hover:text-emerald-700">HR System Guide</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Getting Help</h4>
                <ul className="space-y-1 text-sm text-zinc-600">
                  <li>System Administrator: Technical issues</li>
                  <li>HR Department: Process questions</li>
                  <li>User Training: Schedule training sessions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

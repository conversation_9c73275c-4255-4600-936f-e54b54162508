"use client"

import Link from "next/link"
import { ArrowLeft, Code, Database, Settings, Terminal, Zap, FileText } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function HRDeveloperDocsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr">HR System</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr/developer-docs">Developer Documentation</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/hr">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to HR System
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Code className="h-8 w-8 text-purple-500" />
          <h1 className="text-3xl font-bold text-zinc-900">HR System Developer Documentation</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Complete technical documentation for HR system development including API endpoints, 
          data models, integration patterns, and customization options.
        </p>
      </div>

      {/* Quick Reference */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
        <Card className="border-purple-200">
          <CardHeader>
            <Terminal className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">HR API Endpoints</CardTitle>
            <CardDescription>RESTful API for HR operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Employee management APIs</div>
              <div>• Department and role APIs</div>
              <div>• Analytics and reporting APIs</div>
              <div>• Bulk operations APIs</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader>
            <Database className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">Data Architecture</CardTitle>
            <CardDescription>HR data models and relationships</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Employee data schema</div>
              <div>• Organizational structure models</div>
              <div>• Relationship mappings</div>
              <div>• Audit and history tracking</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader>
            <Zap className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-lg">Integration</CardTitle>
            <CardDescription>System integration and webhooks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Payroll system integration</div>
              <div>• Authentication integration</div>
              <div>• Third-party HR tools</div>
              <div>• Real-time event handling</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* API Documentation */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">API Reference</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Terminal className="h-5 w-5 text-purple-500" />
                HR Management API Endpoints
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/employees</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Retrieve employees with advanced filtering and analytics</p>
                  <div className="text-xs text-zinc-500">
                    <div>Query Parameters: department, role, status, analytics, dateRange</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/employees/bulk</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Bulk employee operations with validation and error handling</p>
                  <div className="text-xs text-zinc-500">
                    <div>Supports: create, update, delete operations in batch</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">GET</span>
                    <code className="text-sm">/api/hr/analytics</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">HR analytics and metrics with customizable parameters</p>
                  <div className="text-xs text-zinc-500">
                    <div>Metrics: headcount, turnover, demographics, performance</div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">POST</span>
                    <code className="text-sm">/api/hr/reports/generate</code>
                  </div>
                  <p className="text-sm text-zinc-600 mb-2">Generate custom HR reports with flexible parameters</p>
                  <div className="text-xs text-zinc-500">
                    <div>Formats: PDF, Excel, CSV with custom templates</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-purple-500" />
                HR Data Models & Schemas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Extended Employee Schema</h4>
                  <div className="bg-zinc-50 rounded-lg p-4">
                    <pre className="text-xs text-zinc-700 overflow-x-auto">
{`{
  "_id": "ObjectId",
  "employeeId": "string", // TCM-XXXX format
  "personalInfo": {
    "firstName": "string",
    "lastName": "string",
    "middleName": "string",
    "email": "string",
    "phone": "string",
    "dateOfBirth": "Date",
    "gender": "string",
    "nationality": "string"
  },
  "employment": {
    "department": "ObjectId",
    "position": "string",
    "employmentType": "full-time | part-time | contract",
    "startDate": "Date",
    "endDate": "Date",
    "status": "active | inactive | terminated",
    "probationEndDate": "Date",
    "manager": "ObjectId"
  },
  "compensation": {
    "baseSalary": "number",
    "currency": "string",
    "payGrade": "string",
    "effectiveDate": "Date",
    "salaryHistory": [
      {
        "amount": "number",
        "effectiveDate": "Date",
        "reason": "string"
      }
    ]
  },
  "hrMetrics": {
    "performanceRating": "number",
    "lastReviewDate": "Date",
    "nextReviewDate": "Date",
    "trainingCompleted": ["string"],
    "certifications": ["string"]
  },
  "auditTrail": {
    "createdAt": "Date",
    "updatedAt": "Date",
    "createdBy": "ObjectId",
    "updatedBy": "ObjectId",
    "changeHistory": [
      {
        "field": "string",
        "oldValue": "any",
        "newValue": "any",
        "changedAt": "Date",
        "changedBy": "ObjectId"
      }
    ]
  }
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Department Schema</h4>
                  <div className="bg-zinc-50 rounded-lg p-4">
                    <pre className="text-xs text-zinc-700 overflow-x-auto">
{`{
  "_id": "ObjectId",
  "name": "string",
  "code": "string",
  "description": "string",
  "parentDepartment": "ObjectId",
  "manager": "ObjectId",
  "budget": {
    "annual": "number",
    "currency": "string",
    "fiscalYear": "string"
  },
  "metrics": {
    "employeeCount": "number",
    "budgetUtilization": "number",
    "turnoverRate": "number"
  },
  "isActive": "boolean",
  "createdAt": "Date",
  "updatedAt": "Date"
}`}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-500" />
                Authentication & Authorization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">HR-Specific Permissions</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h5 className="font-medium text-zinc-800 mb-1">Employee Management</h5>
                      <div className="space-y-1 text-xs text-zinc-600">
                        <div>• hr:employees:read - View employee data</div>
                        <div>• hr:employees:write - Create/update employees</div>
                        <div>• hr:employees:delete - Remove employees</div>
                        <div>• hr:employees:sensitive - Access sensitive data</div>
                      </div>
                    </div>
                    <div>
                      <h5 className="font-medium text-zinc-800 mb-1">Analytics & Reporting</h5>
                      <div className="space-y-1 text-xs text-zinc-600">
                        <div>• hr:analytics:read - View HR analytics</div>
                        <div>• hr:reports:generate - Create reports</div>
                        <div>• hr:reports:export - Export data</div>
                        <div>• hr:admin:config - System configuration</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Role-Based Access Control</h4>
                  <div className="space-y-2 text-sm text-zinc-600">
                    <div>• HR Manager: Full access to all HR functions</div>
                    <div>• HR Specialist: Employee management and basic reporting</div>
                    <div>• Department Manager: Access to own department employees</div>
                    <div>• Employee: Read-only access to own profile</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Integration Patterns */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Integration Patterns</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-500" />
                Payroll System Integration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-zinc-600">
                  Seamless integration between HR and Payroll systems for automated data synchronization.
                </p>
                <div className="bg-zinc-50 rounded-lg p-4">
                  <pre className="text-xs text-zinc-700 overflow-x-auto">
{`// Employee data sync to payroll
const syncEmployeeToPayroll = async (employeeId) => {
  const employee = await Employee.findById(employeeId);
  
  const payrollData = {
    employeeId: employee.employeeId,
    name: \`\${employee.personalInfo.firstName} \${employee.personalInfo.lastName}\`,
    department: employee.employment.department,
    baseSalary: employee.compensation.baseSalary,
    effectiveDate: employee.compensation.effectiveDate,
    status: employee.employment.status
  };
  
  await fetch('/api/payroll/employees/sync', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payrollData)
  });
};

// Webhook for real-time updates
app.post('/api/hr/webhooks/employee-updated', async (req, res) => {
  const { employeeId, changes } = req.body;
  
  // Sync relevant changes to payroll
  if (changes.includes('compensation') || changes.includes('status')) {
    await syncEmployeeToPayroll(employeeId);
  }
  
  res.status(200).json({ success: true });
});`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Event-Driven Architecture</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-zinc-600">
                  HR system events that trigger actions in other modules.
                </p>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold text-zinc-900 mb-2">HR Events</h4>
                    <div className="space-y-1 text-sm text-zinc-600">
                      <div>• employee.created</div>
                      <div>• employee.updated</div>
                      <div>• employee.terminated</div>
                      <div>• department.restructured</div>
                      <div>• salary.adjusted</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-zinc-900 mb-2">Triggered Actions</h4>
                    <div className="space-y-1 text-sm text-zinc-600">
                      <div>• Payroll record creation/update</div>
                      <div>• Access permission updates</div>
                      <div>• Notification sending</div>
                      <div>• Audit log creation</div>
                      <div>• Report regeneration</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Customization Options */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Customization & Extensions</h2>
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-500" />
                Custom Fields
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-zinc-600">
                  Add custom fields to employee records for organization-specific data.
                </p>
                <div className="space-y-2 text-xs text-zinc-600">
                  <div>• Dynamic field configuration</div>
                  <div>• Validation rule setup</div>
                  <div>• Custom form generation</div>
                  <div>• Reporting integration</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-500" />
                Custom Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-zinc-600">
                  Create custom reports with flexible data selection and formatting.
                </p>
                <div className="space-y-2 text-xs text-zinc-600">
                  <div>• Report template designer</div>
                  <div>• Custom data queries</div>
                  <div>• Multiple output formats</div>
                  <div>• Scheduled report generation</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/api/hr">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Complete HR API Reference</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Full API documentation with examples</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/hr/user-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">User Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">End-user documentation and procedures</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/hr/management-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Management Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Strategic HR management features</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

"use client"

import Link from "next/link"
import { ArrowRight, UserPlus, BookOpen, Settings, Code, Users, Target, GraduationCap, FileCheck, TrendingUp } from "lucide-react"
import { DocsMobileNav } from "@/components/docs/mobile-nav"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import DocsPatternBackground from "@/components/docs/pattern-background"
import { DocSideNav } from "@/components/docs/navigation"

export default function HRModulePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-b from-purple-900 to-purple-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <DocsPatternBackground />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex items-center justify-between mb-4">
            <DocsMobileNav />
            <div className="ml-auto"></div>
          </div>
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-block">
              <span className="px-3 py-1 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                Core Module
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tighter text-white">
              Human Resources
            </h1>
            <p className="max-w-[700px] text-purple-100 md:text-xl/relaxed">
              HR processes and strategic workforce management - recruitment, performance, training, policies, and compliance
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="hidden md:block">
              <DocSideNav />
            </div>
            <div className="flex-1">
              {/* Breadcrumbs */}
              <Breadcrumb className="mb-8">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/docs/hr">Human Resources</BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Module Overview */}
              <div className="mb-12">
                <h2 className="text-3xl font-bold text-zinc-900 mb-4">Human Resources Module</h2>
                <p className="text-lg text-zinc-600 mb-6">
                  The Human Resources module manages HR processes, policies, and strategic workforce management. 
                  It handles recruitment, performance management, training, policy administration, and compliance.
                </p>
                
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-8">
                  <h3 className="text-lg font-semibold text-purple-800 mb-2">Key Distinction</h3>
                  <p className="text-purple-700">
                    <strong>Human Resources</strong> focuses on the processes and policies that govern how employees are managed, 
                    while <strong>Employee Management</strong> handles the core "person" entities and their master data.
                  </p>
                </div>
              </div>

              {/* Documentation Categories */}
              <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3 mb-12">
                <Link href="/docs/hr/user-guide">
                  <Card className="h-full transition-all hover:shadow-md border-blue-200">
                    <CardHeader>
                      <BookOpen className="h-8 w-8 text-blue-500 mb-2" />
                      <CardTitle>User Guide</CardTitle>
                      <CardDescription>HR workflows and daily operations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Step-by-step guides for HR professionals and managers.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Recruitment and hiring workflows</div>
                        <div>• Performance review processes</div>
                        <div>• Training program management</div>
                        <div>• Policy administration</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-blue-600">
                        Read User Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/hr/management-guide">
                  <Card className="h-full transition-all hover:shadow-md border-purple-200">
                    <CardHeader>
                      <Settings className="h-8 w-8 text-purple-500 mb-2" />
                      <CardTitle>Management Guide</CardTitle>
                      <CardDescription>Strategic HR management and oversight</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Strategic guidance for HR leaders and senior management.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• Workforce planning and strategy</div>
                        <div>• HR analytics and reporting</div>
                        <div>• Compliance management</div>
                        <div>• Organizational development</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-purple-600">
                        Read Management Guide <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>

                <Link href="/docs/hr/developer-docs">
                  <Card className="h-full transition-all hover:shadow-md border-orange-200">
                    <CardHeader>
                      <Code className="h-8 w-8 text-orange-500 mb-2" />
                      <CardTitle>Developer Docs</CardTitle>
                      <CardDescription>Technical implementation and integrations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-zinc-600 mb-3">
                        Technical documentation for HR system integrations.
                      </p>
                      <div className="space-y-1 text-xs text-zinc-500">
                        <div>• HR API endpoints and workflows</div>
                        <div>• Integration with Employee module</div>
                        <div>• Custom workflow development</div>
                        <div>• Third-party integrations</div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="ghost" size="sm" className="gap-1 text-orange-600">
                        Read Developer Docs <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>

              {/* Core Features */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-zinc-900 mb-6">Core HR Functions</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <UserPlus className="h-6 w-6 text-purple-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Recruitment & Hiring</h4>
                      <p className="text-sm text-zinc-600">Job postings, candidate tracking, interview scheduling, and hiring workflows</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <Target className="h-6 w-6 text-purple-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Performance Management</h4>
                      <p className="text-sm text-zinc-600">Reviews, goal setting, 360-degree feedback, and development planning</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <GraduationCap className="h-6 w-6 text-purple-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Training & Development</h4>
                      <p className="text-sm text-zinc-600">Learning programs, skill development, and certification tracking</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 bg-zinc-50 rounded-lg">
                    <FileCheck className="h-6 w-6 text-purple-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-zinc-900">Policies & Compliance</h4>
                      <p className="text-sm text-zinc-600">Policy management, compliance tracking, and regulatory reporting</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* HR Workflows */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-zinc-900 mb-6">Key HR Workflows</h3>
                <div className="space-y-4">
                  <div className="border border-zinc-200 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-zinc-900 mb-2">Recruitment Process</h4>
                    <p className="text-zinc-600 mb-3">End-to-end recruitment from job posting to employee onboarding</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Job Posting</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Application Review</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Interview Scheduling</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Decision Making</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Offer Management</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Onboarding</span>
                    </div>
                  </div>
                  
                  <div className="border border-zinc-200 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-zinc-900 mb-2">Performance Review Cycle</h4>
                    <p className="text-zinc-600 mb-3">Structured performance management with goal setting and feedback</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">Goal Setting</span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">Regular Check-ins</span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">360 Feedback</span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">Performance Review</span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">Development Planning</span>
                    </div>
                  </div>
                  
                  <div className="border border-zinc-200 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-zinc-900 mb-2">Training Program Management</h4>
                    <p className="text-zinc-600 mb-3">Comprehensive learning and development program administration</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Needs Assessment</span>
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Program Design</span>
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Enrollment</span>
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Progress Tracking</span>
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Certification</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="bg-zinc-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-zinc-900 mb-4">Quick Links</h3>
                <div className="grid gap-3 md:grid-cols-2">
                  <Link href="/docs/api/hr" className="flex items-center text-sm text-purple-600 hover:text-purple-700">
                    <Code className="h-4 w-4 mr-2" />
                    HR API Reference
                  </Link>
                  <Link href="/docs/employees" className="flex items-center text-sm text-purple-600 hover:text-purple-700">
                    <Users className="h-4 w-4 mr-2" />
                    Employee Management Module
                  </Link>
                  <Link href="/docs/accounting" className="flex items-center text-sm text-purple-600 hover:text-purple-700">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Accounting Module Integration
                  </Link>
                  <Link href="/docs/settings" className="flex items-center text-sm text-purple-600 hover:text-purple-700">
                    <Settings className="h-4 w-4 mr-2" />
                    System Configuration
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

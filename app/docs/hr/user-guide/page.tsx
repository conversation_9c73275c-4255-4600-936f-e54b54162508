"use client"

import Link from "next/link"
import { ArrowLeft, Users, UserPlus, FileText, Search, Download, Upload } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function HRUserGuidePage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr">HR System</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr/user-guide">User Guide</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/hr">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to HR System
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Users className="h-8 w-8 text-emerald-500" />
          <h1 className="text-3xl font-bold text-zinc-900">HR System User Guide</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Step-by-step instructions for daily HR management tasks including employee onboarding, 
          record management, and administrative operations.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-12">
        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <UserPlus className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Add New Employee</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Create comprehensive employee profiles</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <Search className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Search & Filter</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Find employees quickly with advanced filters</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <Upload className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Bulk Import</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Import multiple employees from Excel/CSV</p>
          </CardContent>
        </Card>

        <Card className="border-emerald-200">
          <CardHeader className="pb-3">
            <FileText className="h-6 w-6 text-emerald-500 mb-2" />
            <CardTitle className="text-sm">Generate Reports</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-zinc-600">Create comprehensive HR reports</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Sections */}
      <div className="space-y-8 mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-emerald-500" />
              Employee Onboarding
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-zinc-600 mb-4">
              Complete guide to adding new employees to the system with all required information.
            </p>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Step 1: Basic Information</h4>
                <div className="space-y-1 text-sm text-zinc-600 ml-4">
                  <div>• Navigate to HR Dashboard → Add Employee</div>
                  <div>• Fill in personal details (name, email, phone)</div>
                  <div>• Assign unique employee ID (auto-generated or manual)</div>
                  <div>• Select employment start date</div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Step 2: Employment Details</h4>
                <div className="space-y-1 text-sm text-zinc-600 ml-4">
                  <div>• Choose department and position</div>
                  <div>• Set employment type (full-time, part-time, contract)</div>
                  <div>• Configure salary and compensation details</div>
                  <div>• Assign reporting manager if applicable</div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Step 3: Additional Information</h4>
                <div className="space-y-1 text-sm text-zinc-600 ml-4">
                  <div>• Add address and contact information</div>
                  <div>• Include emergency contact details</div>
                  <div>• Upload profile photo and documents</div>
                  <div>• Set access permissions and system roles</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5 text-emerald-500" />
              Finding and Managing Employee Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-zinc-600 mb-4">
              Efficiently locate and manage employee information using search and filter tools.
            </p>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Search Options</h4>
                <div className="space-y-1 text-sm text-zinc-600">
                  <div>• Quick search by name or employee ID</div>
                  <div>• Advanced search with multiple criteria</div>
                  <div>• Filter by department, position, or status</div>
                  <div>• Sort results by various fields</div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Record Management</h4>
                <div className="space-y-1 text-sm text-zinc-600">
                  <div>• View detailed employee profiles</div>
                  <div>• Edit and update information</div>
                  <div>• Track employment history</div>
                  <div>• Manage status changes</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-emerald-500" />
              Bulk Operations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-zinc-600 mb-4">
              Efficiently manage multiple employees using bulk import and export features.
            </p>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Bulk Import Process</h4>
                <div className="space-y-1 text-sm text-zinc-600 ml-4">
                  <div>• Download the employee import template</div>
                  <div>• Fill in employee data following the format</div>
                  <div>• Upload the completed file through the import wizard</div>
                  <div>• Review validation results and fix any errors</div>
                  <div>• Confirm import to add employees to the system</div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Data Export Options</h4>
                <div className="space-y-1 text-sm text-zinc-600 ml-4">
                  <div>• Export all employees or filtered results</div>
                  <div>• Choose from multiple file formats (Excel, CSV, PDF)</div>
                  <div>• Select specific fields to include in export</div>
                  <div>• Schedule automated exports for regular reporting</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-emerald-500" />
              Reporting and Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-zinc-600 mb-4">
              Generate comprehensive reports and analyze HR metrics for better decision making.
            </p>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Standard Reports</h4>
                <div className="space-y-1 text-sm text-zinc-600">
                  <div>• Employee directory and contact list</div>
                  <div>• Department headcount and distribution</div>
                  <div>• New hires and terminations report</div>
                  <div>• Employee anniversary and birthday lists</div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-zinc-900 mb-2">Analytics Dashboard</h4>
                <div className="space-y-1 text-sm text-zinc-600">
                  <div>• Workforce demographics overview</div>
                  <div>• Turnover and retention metrics</div>
                  <div>• Department growth trends</div>
                  <div>• Custom analytics and insights</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Best Practices */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Best Practices</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-700">Data Quality</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Always verify employee information before saving</div>
                <div>• Use consistent formatting for names and addresses</div>
                <div>• Regularly update contact information</div>
                <div>• Maintain accurate employment status records</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-700">Security & Privacy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Follow data protection guidelines</div>
                <div>• Limit access to sensitive information</div>
                <div>• Regular backup of employee data</div>
                <div>• Report any data discrepancies immediately</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/hr/management-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Management Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Advanced HR management features and analytics</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/hr/developer-docs">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Developer Documentation</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Technical implementation and API reference</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/bulk-import">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Bulk Import Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Detailed bulk operations documentation</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

"use client"

import Link from "next/link"
import { ArrowLeft, BarChart3, Settings, Shield, Users, FileText, TrendingUp } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"

export default function HRManagementGuidePage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs">Documentation</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr">HR System</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/docs/hr/management-guide">Management Guide</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Link href="/docs/hr">
        <Button variant="ghost" size="sm" className="mb-6 gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to HR System
        </Button>
      </Link>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-8 w-8 text-blue-500" />
          <h1 className="text-3xl font-bold text-zinc-900">HR Management Guide</h1>
        </div>
        <p className="text-lg text-zinc-600 max-w-3xl">
          Strategic HR management features for administrators and managers. 
          Advanced analytics, compliance tools, and organizational oversight capabilities.
        </p>
      </div>

      {/* Management Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
        <Card className="border-blue-200">
          <CardHeader>
            <BarChart3 className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Workforce Analytics</CardTitle>
            <CardDescription>Comprehensive HR metrics and insights</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Employee demographics analysis</div>
              <div>• Turnover and retention metrics</div>
              <div>• Performance trend tracking</div>
              <div>• Compensation analytics</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader>
            <Users className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Organizational Management</CardTitle>
            <CardDescription>Structure and hierarchy oversight</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Department structure management</div>
              <div>• Role and position hierarchy</div>
              <div>• Reporting relationships</div>
              <div>• Organizational chart visualization</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader>
            <Shield className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-lg">Compliance & Governance</CardTitle>
            <CardDescription>Regulatory compliance and audit</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-zinc-600">
              <div>• Audit trail management</div>
              <div>• Compliance reporting</div>
              <div>• Data governance policies</div>
              <div>• Security protocols</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Management Features */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Strategic HR Management</h2>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-500" />
                Advanced Analytics & Reporting
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Gain deep insights into your workforce with comprehensive analytics and strategic reporting tools.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Workforce Metrics</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Employee headcount trends and forecasting</div>
                    <div>• Department growth and distribution analysis</div>
                    <div>• Turnover rates and retention analytics</div>
                    <div>• Time-to-hire and recruitment efficiency</div>
                    <div>• Employee satisfaction and engagement scores</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Strategic Reporting</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Executive dashboard with KPIs</div>
                    <div>• Custom report builder and scheduling</div>
                    <div>• Comparative analysis and benchmarking</div>
                    <div>• Predictive analytics and trend forecasting</div>
                    <div>• Interactive data visualization tools</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                Organizational Structure Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Design and manage your organizational structure with advanced hierarchy and relationship tools.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Structure Design</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Department creation and management</div>
                    <div>• Position hierarchy definition</div>
                    <div>• Reporting relationship mapping</div>
                    <div>• Matrix organization support</div>
                    <div>• Organizational chart generation</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Change Management</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Restructuring workflow management</div>
                    <div>• Position transfer and promotion tracking</div>
                    <div>• Historical structure versioning</div>
                    <div>• Impact analysis for organizational changes</div>
                    <div>• Approval workflows for structure changes</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-500" />
                Compliance & Risk Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Ensure regulatory compliance and manage HR-related risks with comprehensive governance tools.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Compliance Monitoring</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Regulatory requirement tracking</div>
                    <div>• Automated compliance reporting</div>
                    <div>• Policy adherence monitoring</div>
                    <div>• Documentation management</div>
                    <div>• Compliance dashboard and alerts</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Audit & Governance</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Complete audit trail maintenance</div>
                    <div>• Data access and modification logging</div>
                    <div>• Privacy and security compliance</div>
                    <div>• Risk assessment and mitigation</div>
                    <div>• Governance policy enforcement</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                Strategic Planning & Forecasting
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-600 mb-4">
                Plan for the future with advanced forecasting and strategic workforce planning tools.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Workforce Planning</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Headcount planning and budgeting</div>
                    <div>• Skills gap analysis and planning</div>
                    <div>• Succession planning management</div>
                    <div>• Recruitment pipeline forecasting</div>
                    <div>• Capacity planning and optimization</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-2">Predictive Analytics</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Turnover prediction modeling</div>
                    <div>• Performance trend forecasting</div>
                    <div>• Cost projection and budgeting</div>
                    <div>• Scenario planning and modeling</div>
                    <div>• ROI analysis for HR initiatives</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* System Administration */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">System Administration</h2>
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-500" />
                Configuration Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-1">System Settings</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Global HR policy configuration</div>
                    <div>• Workflow and approval process setup</div>
                    <div>• Notification and alert management</div>
                    <div>• Integration settings and API configuration</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-1">User Management</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Role-based access control</div>
                    <div>• Permission matrix management</div>
                    <div>• User provisioning and deprovisioning</div>
                    <div>• Security policy enforcement</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                Data Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-1">Data Quality</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Data validation and cleansing tools</div>
                    <div>• Duplicate detection and resolution</div>
                    <div>• Data integrity monitoring</div>
                    <div>• Quality metrics and reporting</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-zinc-900 mb-1">Backup & Recovery</h4>
                  <div className="space-y-1 text-sm text-zinc-600">
                    <div>• Automated backup scheduling</div>
                    <div>• Disaster recovery planning</div>
                    <div>• Data archival and retention</div>
                    <div>• Recovery testing and validation</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Best Practices */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-zinc-900 mb-6">Management Best Practices</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border-emerald-200">
            <CardHeader>
              <CardTitle className="text-lg text-emerald-700">Strategic Planning</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Regular workforce planning reviews</div>
                <div>• Data-driven decision making</div>
                <div>• Continuous process improvement</div>
                <div>• Stakeholder engagement and communication</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-emerald-200">
            <CardHeader>
              <CardTitle className="text-lg text-emerald-700">Operational Excellence</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-zinc-600">
                <div>• Regular system performance monitoring</div>
                <div>• User training and support programs</div>
                <div>• Change management protocols</div>
                <div>• Continuous security assessment</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Resources */}
      <div>
        <h2 className="text-2xl font-bold text-zinc-900 mb-4">Related Resources</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/docs/hr/user-guide">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">User Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Daily HR operations and user procedures</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/hr/developer-docs">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Developer Documentation</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Technical implementation and customization</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/docs/analytics">
            <Card className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Analytics Guide</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-zinc-600">Advanced analytics and reporting features</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { LoginForm } from "../components/auth/login-form";
import { BookOpen } from "lucide-react";

export default function Home() {
  const router = useRouter();

  // Check if user is already authenticated (using localStorage in this example)
  useEffect(() => {
    // In a real app, you would check the authentication state from your auth store
    // For now, we'll just redirect to the dashboard
    // This is just for development purposes
    // router.push("/dashboard");
  }, [router]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            TCM Enterprise Business Suite
          </h1>
          <p className="text-sm text-muted-foreground">
            Enter your credentials to sign in to your account
          </p>
        </div>
        <LoginForm />
        <div className="flex justify-center mt-4">
          <Link
            href="/docs"
            className="flex items-center text-sm text-muted-foreground hover:text-primary"
          >
            <BookOpen className="h-4 w-4 mr-1" />
            Documentation
          </Link>
        </div>
      </div>
    </div>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { InventoryPage } from '@/components/inventory/inventory-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Inventory Dashboard | TCM Enterprise Business Suite',
  description: 'Track and manage company assets, equipment, and supplies',
};

export default async function InventoryDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return <InventoryPage />;
}

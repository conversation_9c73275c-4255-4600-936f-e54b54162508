import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { StockPage } from '@/components/inventory/stock/stock-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Stock Management | TCM Enterprise Business Suite',
  description: 'Manage inventory stock items and levels',
};

export default async function StockManagementPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Stock Management"
        text="Manage inventory stock items and levels"
      />
      <div className="flex-1 space-y-6">
        <StockPage />
      </div>
    </DashboardShell>
  );
}

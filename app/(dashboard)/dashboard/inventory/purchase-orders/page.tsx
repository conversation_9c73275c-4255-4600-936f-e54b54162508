import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PurchaseOrdersPage } from '@/components/inventory/purchase-orders/purchase-orders-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Purchase Orders | TCM Enterprise Business Suite',
  description: 'Manage inventory purchase orders and requisitions',
};

export default async function PurchaseOrdersManagementPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Purchase Orders"
        text="Manage inventory purchase orders and requisitions"
      />
      <div className="flex-1 space-y-6">
        <PurchaseOrdersPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

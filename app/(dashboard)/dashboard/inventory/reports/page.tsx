import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { InventoryReportsPage } from '@/components/inventory/reports/inventory-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Inventory Reports | TCM Enterprise Business Suite',
  description: 'Generate and view inventory reports and analytics',
};

export default async function InventoryReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Inventory Reports"
        text="Generate and view inventory reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <InventoryReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

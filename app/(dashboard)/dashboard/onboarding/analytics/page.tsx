import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { OnboardingProgressDashboard } from '@/components/onboarding/onboarding-progress-dashboard';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Onboarding Analytics | TCM Enterprise Business Suite',
  description: 'View analytics and reports for employee onboarding',
};

export default async function AnalyticsPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Onboarding Analytics"
        text="View analytics and reports for employee onboarding"
      />
      <div className="flex-1 space-y-6">
        <OnboardingProgressDashboard userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { OnboardingTemplatesPage } from '@/components/onboarding/onboarding-templates-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Onboarding Templates | TCM Enterprise Business Suite',
  description: 'Manage onboarding templates and workflows',
};

export default async function TemplatesPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Onboarding Templates"
        text="Manage onboarding templates and workflows"
      />
      <div className="flex-1 space-y-6">
        <OnboardingTemplatesPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

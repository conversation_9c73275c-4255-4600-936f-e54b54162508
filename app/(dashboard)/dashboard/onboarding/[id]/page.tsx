// app/(dashboard)/dashboard/onboarding/[id]/page.tsx
"use client"

import { OnboardingDetail } from "@/components/onboarding/onboarding-detail"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { useParams } from "next/navigation"

export default function OnboardingDetailPage() {
  const params = useParams();
  const id = params?.id as string;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Onboarding Details"
        text="View and manage employee onboarding process"
      />
      <div className="flex-1 space-y-4">
        <OnboardingDetail id={id} />
      </div>
    </DashboardShell>
  )
}

"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>Header } from "@/components/dashboard-header"
import { Settings } from "lucide-react"
import { NotificationSettings } from "@/components/notification/notification-settings"

export default function NotificationSettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Notification Settings"
        text="Manage your notification preferences"
      >
        <div className="flex items-center">
          <Settings className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="space-y-4">
        <NotificationSettings />
      </div>
    </DashboardShell>
  )
}

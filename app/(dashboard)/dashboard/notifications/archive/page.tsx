// app/(dashboard)/dashboard/notifications/archive/page.tsx
"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Archive } from "lucide-react"
import { ArchivedNotifications } from "@/components/notification/archived-notifications"

export default function ArchivedNotificationsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Archived Notifications"
        text="View and manage your archived notifications"
      >
        <div className="flex items-center">
          <Archive className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="space-y-4">
        <ArchivedNotifications />
      </div>
    </DashboardShell>
  )
}

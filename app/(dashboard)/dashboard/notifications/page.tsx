// app/(dashboard)/dashboard/notifications/page.tsx
"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Bell } from "lucide-react"
import { NotificationCenter } from "@/components/notification/notification-center"

export default function Notifications() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="All Notifications"
        text="View and manage all your notifications"
      >
        <div className="flex items-center">
          <Bell className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="space-y-4">
        <NotificationCenter />
      </div>
    </DashboardShell>
  )
}

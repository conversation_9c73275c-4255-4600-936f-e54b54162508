"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/dashboard-header"
import { Clock } from "lucide-react"
import { UnreadNotifications } from "@/components/notification/unread-notifications"

export default function UnreadNotificationsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Unread Notifications"
        text="View and manage your unread notifications"
      >
        <div className="flex items-center">
          <Clock className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="space-y-4">
        <UnreadNotifications />
      </div>
    </DashboardShell>
  )
}

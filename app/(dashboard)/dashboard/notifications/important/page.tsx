"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON><PERSON>eader } from "@/components/dashboard-header"
import { AlertCircle } from "lucide-react"
import { ImportantNotifications } from "@/components/notification/important-notifications"

export default function ImportantNotificationsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Important Notifications"
        text="View and manage your important notifications"
      >
        <div className="flex items-center">
          <AlertCircle className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="space-y-4">
        <ImportantNotifications />
      </div>
    </DashboardShell>
  )
}

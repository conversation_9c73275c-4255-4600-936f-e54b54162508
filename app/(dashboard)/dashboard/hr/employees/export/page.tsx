import { Metadata } from 'next';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { ExportEmployeesForm } from '@/components/hr/employees/export-employees-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Export Employees',
  description: 'Export employee data in various formats',
};

export default function ExportEmployeesPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Export Employees"
        text="Export employee data in various formats with customizable filters"
      />

      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Export Options</CardTitle>
            <CardDescription>
              Choose your export format and filter options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ExportEmployeesForm />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Export Guide</CardTitle>
            <CardDescription>
              Learn how to use the export features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Available Export Formats</h3>
              <ul className="mt-2 list-disc pl-6 text-sm">
                <li className="mt-1">
                  <strong>Excel .xlsx)</strong> - Comprehensive multi-sheet workbook with all employee data, formatted with colors and styling.
                </li>
                <li className="mt-1">
                  <strong>PDF Document</strong> - Professional PDF report organized by department, ideal for printing or sharing.
                </li>
                <li className="mt-1">
                  <strong>CSV File</strong> - Simple comma-separated values format, compatible with most spreadsheet applications and data analysis tools.
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium">Filter Options</h3>
              <ul className="mt-2 list-disc pl-6 text-sm">
                <li className="mt-1">
                  <strong>Department</strong> - Filter employees by specific department.
                </li>
                <li className="mt-1">
                  <strong>Employment Status</strong> - Filter by active, inactive, on-leave, or terminated status.
                </li>
                <li className="mt-1">
                  <strong>Search</strong> - Find specific employees by name, email, or employee ID.
                </li>
                <li className="mt-1">
                  <strong>Hire Date Range</strong> - Filter employees hired within a specific date range.
                </li>
                <li className="mt-1">
                  <strong>Include Inactive</strong> - Option to include or exclude inactive and terminated employees.
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium">Tips for Effective Exports</h3>
              <ul className="mt-2 list-disc pl-6 text-sm">
                <li className="mt-1">
                  Use Excel format for the most comprehensive data view with multiple sheets.
                </li>
                <li className="mt-1">
                  PDF format is best for formal reports and presentations.
                </li>
                <li className="mt-1">
                  CSV format is ideal for importing into other systems or data analysis tools.
                </li>
                <li className="mt-1">
                  Narrow your export with filters to focus on specific employee groups.
                </li>
                <li className="mt-1">
                  Department heads can only export employees from their own department.
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { EmployeeNav } from '@/components/hr/employees/employee-nav';
import { SimpleBulkDeleteForm } from '@/components/hr/employees/simple-bulk-delete-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Bulk Delete Employees',
  description: 'Delete multiple employee records at once',
};

export default function BulkDeleteEmployeesPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Bulk Delete Employees"
        text="Delete multiple employee records at once"
      >
        <EmployeeNav />
      </DashboardHeader>

      <div className="grid gap-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Warning</AlertTitle>
          <AlertDescription>
            This action permanently deletes employee records. This cannot be undone.
            Make sure you have a backup or export of any data you want to keep.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Bulk Delete Options</CardTitle>
            <CardDescription>
              Delete employees by entering their IDs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleBulkDeleteForm />
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

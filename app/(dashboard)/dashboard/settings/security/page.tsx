import { ActiveSessions } from "@/components/auth/active-sessions";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "Security Settings",
  description: "Manage your account security settings and active sessions",
};

export default function SecuritySettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Security Settings"
        text="Manage your account security settings and active sessions"
      />
      <div className="grid gap-8">
        <ActiveSessions />
      </div>
    </DashboardShell>
  );
}

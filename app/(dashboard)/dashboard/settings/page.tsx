import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { ArrowRight, Settings, Building2, Users, Bell, Layers, Shield } from "lucide-react";

export const metadata = {
  title: "Settings",
  description: "Manage your account and application settings",
};

export default function SettingsIndexPage() {
  const settingsCategories = [
    {
      title: "General Settings",
      description: "Manage your general application settings",
      href: "/settings/general",
      icon: Settings,
    },
    {
      title: "Company Settings",
      description: "Manage your company information and settings",
      href: "/settings/company",
      icon: Building2,
    },
    {
      title: "User Management",
      description: "Manage user accounts and permissions",
      href: "/settings/users",
      icon: Users,
    },
    {
      title: "Notification Settings",
      description: "Manage your notification preferences",
      href: "/settings/notifications",
      icon: Bell,
    },
    {
      title: "Integration Settings",
      description: "Manage your application integrations",
      href: "/settings/integrations",
      icon: Layers,
    },
    {
      title: "Security Settings",
      description: "Manage your account security settings and active sessions",
      href: "/settings/security",
      icon: Shield,
    },
  ];

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Settings"
        text="Manage your account and application settings"
      />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {settingsCategories.map((category) => (
          <Link href={category.href} key={category.href} className="block">
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">{category.title}</CardTitle>
                <category.icon className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">{category.description}</CardDescription>
                <div className="mt-4 flex items-center text-sm text-primary">
                  <span>View settings</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </DashboardShell>
  );
}

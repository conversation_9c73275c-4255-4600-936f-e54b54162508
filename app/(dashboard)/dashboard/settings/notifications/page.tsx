import { NotificationSettings } from "@/components/settings/notification-settings";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "Notification Settings",
  description: "Manage your notification preferences",
};

export default function NotificationSettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Notification Settings"
        text="Manage your notification preferences"
      />
      <div className="grid gap-8">
        <NotificationSettings />
      </div>
    </DashboardShell>
  );
}

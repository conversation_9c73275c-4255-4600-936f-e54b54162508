import { UserProfilePage } from "@/components/settings/user-profile-page";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "User Profile",
  description: "View and manage user profile information",
};

interface UserDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function UserDetailPage({ params }: UserDetailPageProps) {
  const { id } = await params;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="User Profile"
        text="View and manage user profile information"
      />
      <div className="grid gap-8">
        <UserProfilePage userId={id} />
      </div>
    </DashboardShell>
  );
}

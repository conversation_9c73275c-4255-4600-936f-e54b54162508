import { UsersPage } from "@/components/settings/users-page";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "User Management",
  description: "Manage user accounts and permissions",
};

export default function UsersSettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="User Management"
        text="Manage user accounts and permissions"
      />
      <div className="grid gap-8">
        <UsersPage />
      </div>
    </DashboardShell>
  );
}

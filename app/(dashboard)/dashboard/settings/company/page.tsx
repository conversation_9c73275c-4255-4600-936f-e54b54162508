import { CompanySettings } from "@/components/settings/company-settings";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "Company Settings",
  description: "Manage your company information and settings",
};

export default function CompanySettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Company Settings"
        text="Manage your company information and settings"
      />
      <div className="grid gap-8">
        <CompanySettings />
      </div>
    </DashboardShell>
  );
}

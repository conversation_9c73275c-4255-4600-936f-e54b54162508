"use client";

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { DashboardShell } from '@/components/dashboard-shell';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  ArrowLeft,
  Building,
  Send,
  Edit,
  Download,
  Copy,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  FileText,
  FileSpreadsheet,
  ChevronDown,
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from '@/components/ui/use-toast';
import { useErrorHandler } from '@/lib/frontend/hooks/useErrorHandler';
import { ErrorOverlay } from '@/components/errors/error-overlay';
import { useAuth } from '@/lib/frontend/hooks/useAuth';
import { UserRole } from '@/types/user-roles';
import { VoucherDownloadService } from '@/lib/services/accounting/voucher-download-service';
import { formatAmountInWords } from '@/lib/utils/number-to-words';

interface VoucherUser {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface VoucherDetails {
  _id: string;
  voucherNumber: string;
  voucherType: 'payment' | 'receipt' | 'journal';
  date: string;
  reference?: string;
  description: string;
  totalAmount: number;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'posted' | 'cancelled';
  fiscalYear: string;
  payee?: string;
  paymentMethod?: string;
  notes?: string;
  voucherCategory: 'payroll' | 'general' | 'procurement' | 'expense';
  isAutoGenerated: boolean;
  sourceModule?: 'payroll' | 'procurement' | 'manual';
  createdBy: VoucherUser;
  updatedBy?: VoucherUser;
  approvedBy?: VoucherUser;
  postedBy?: VoucherUser;
  approvedAt?: string;
  postedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function VoucherDetailsPage() {
  const params = useParams();
  const voucherId = params?.id as string;

  const [voucher, setVoucher] = useState<VoucherDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [approving, setApproving] = useState(false);
  const [exportingPdf, setExportingPdf] = useState(false);
  const [exportingExcel, setExportingExcel] = useState(false);
  const { error, isErrorOpen, handleApiError, hideError } = useErrorHandler();
  const { user } = useAuth();

  // Fetch voucher details
  const fetchVoucherDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/accounting/vouchers/${voucherId}`);

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const result = await response.json();
      if (result.success) {
        setVoucher(result.data);
      }
    } catch (error) {
      console.error('Error fetching voucher details:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch voucher details. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Submit voucher for approval
  const handleSubmitForApproval = async () => {
    if (!voucher) return;

    try {
      setSubmitting(true);
      const response = await fetch(`/api/accounting/vouchers/${voucherId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: result.message || 'Voucher submitted for approval successfully.',
        });

        // Refresh voucher data
        await fetchVoucherDetails();
      }
    } catch (error) {
      console.error('Error submitting voucher:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit voucher for approval. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Approve voucher
  const handleApproveVoucher = async () => {
    if (!voucher) return;

    try {
      setApproving(true);
      const response = await fetch(`/api/accounting/vouchers/${voucherId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          comments: 'Approved via voucher details page'
        }),
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: result.message || 'Voucher approved successfully.',
        });

        // Refresh voucher data
        await fetchVoucherDetails();
      }
    } catch (error) {
      console.error('Error approving voucher:', error);
      toast({
        title: 'Error',
        description: 'Failed to approve voucher. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setApproving(false);
    }
  };

  // Reject voucher
  const handleRejectVoucher = async () => {
    if (!voucher) return;

    try {
      setApproving(true);
      const response = await fetch(`/api/accounting/vouchers/${voucherId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          comments: 'Rejected via voucher details page'
        }),
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: result.message || 'Voucher rejected successfully.',
        });

        // Refresh voucher data
        await fetchVoucherDetails();
      }
    } catch (error) {
      console.error('Error rejecting voucher:', error);
      toast({
        title: 'Error',
        description: 'Failed to reject voucher. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setApproving(false);
    }
  };

  // Export voucher as PDF
  const handleExportPdf = async () => {
    if (!voucher) return;

    try {
      setExportingPdf(true);

      // Transform voucher data for the download service
      const transformedVoucher = {
        _id: voucher._id,
        voucherNumber: voucher.voucherNumber,
        voucherType: voucher.voucherType,
        date: voucher.date,
        description: voucher.description,
        totalAmount: voucher.totalAmount,
        status: voucher.status,
        fiscalYear: voucher.fiscalYear,
        payee: voucher.payee,
        paymentMethod: voucher.paymentMethod,
        reference: voucher.reference,
        createdBy: voucher.createdBy,
        approvedBy: voucher.approvedBy,
        postedBy: voucher.postedBy,
        createdAt: voucher.createdAt,
        updatedAt: voucher.updatedAt,
        approvedAt: voucher.approvedAt,
        postedAt: voucher.postedAt,
      };

      await VoucherDownloadService.downloadAsPDF(transformedVoucher);

      toast({
        title: 'Success',
        description: `Voucher ${voucher.voucherNumber} exported as PDF successfully.`,
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast({
        title: 'Error',
        description: 'Failed to export voucher as PDF. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setExportingPdf(false);
    }
  };

  // Export voucher as Excel
  const handleExportExcel = async () => {
    if (!voucher) return;

    try {
      setExportingExcel(true);

      // Transform voucher data for the download service
      const transformedVoucher = {
        _id: voucher._id,
        voucherNumber: voucher.voucherNumber,
        voucherType: voucher.voucherType,
        date: voucher.date,
        description: voucher.description,
        totalAmount: voucher.totalAmount,
        status: voucher.status,
        fiscalYear: voucher.fiscalYear,
        payee: voucher.payee,
        paymentMethod: voucher.paymentMethod,
        reference: voucher.reference,
        createdBy: voucher.createdBy,
        approvedBy: voucher.approvedBy,
        postedBy: voucher.postedBy,
        createdAt: voucher.createdAt,
        updatedAt: voucher.updatedAt,
        approvedAt: voucher.approvedAt,
        postedAt: voucher.postedAt,
      };

      VoucherDownloadService.downloadAsExcel(transformedVoucher);

      toast({
        title: 'Success',
        description: `Voucher ${voucher.voucherNumber} exported as Excel successfully.`,
      });
    } catch (error) {
      console.error('Error exporting Excel:', error);
      toast({
        title: 'Error',
        description: 'Failed to export voucher as Excel. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setExportingExcel(false);
    }
  };

  // Copy voucher ID to clipboard
  const handleCopyId = () => {
    navigator.clipboard.writeText(voucherId);
    toast({
      title: 'Copied',
      description: 'Voucher ID copied to clipboard.',
    });
  };

  useEffect(() => {
    if (voucherId) {
      fetchVoucherDetails();
    }
  }, [voucherId]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'draft': return 'outline';
      case 'pending_approval': return 'secondary';
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'posted': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  // Get user display name
  const getUserDisplayName = (user: VoucherUser) => {
    return `${user.firstName} ${user.lastName}`;
  };

  // Check if voucher can be submitted for approval
  const canSubmitForApproval = voucher && ['draft', 'rejected'].includes(voucher.status);

  // Check if current user can approve vouchers
  const canApproveVoucher = () => {
    if (!user || !voucher) return false;

    // Only show approve button if voucher is pending approval
    if (voucher.status !== 'pending_approval') return false;

    // Define approval roles based on user requirements
    const approvalRoles = [
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_MANAGER,
      UserRole.SUPER_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_DIRECTOR,
      UserRole.SYSTEM_ADMIN
    ];

    return approvalRoles.includes(user.role);
  };

  if (loading) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading voucher details...</p>
          </div>
        </div>
      </DashboardShell>
    );
  }

  if (!voucher) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Voucher Not Found</h3>
            <p className="text-muted-foreground mb-4">
              The voucher you're looking for could not be found.
            </p>
            <Button asChild>
              <Link href="/dashboard/accounting/vouchers/management">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Vouchers
              </Link>
            </Button>
          </div>
        </div>
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <ErrorOverlay
        error={error}
        isOpen={isErrorOpen}
        onClose={hideError}
      />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/accounting/vouchers/management">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Vouchers
                </Link>
              </Button>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">
              Voucher {voucher.voucherNumber}
            </h1>
            <p className="text-muted-foreground">
              View and manage voucher details
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleCopyId}>
              <Copy className="h-4 w-4 mr-2" />
              Copy ID
            </Button>
            
            {voucher.status === 'draft' && (
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/accounting/vouchers/${voucherId}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={exportingPdf || exportingExcel}>
                  {exportingPdf || exportingExcel ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleExportPdf} disabled={exportingPdf || exportingExcel}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportExcel} disabled={exportingPdf || exportingExcel}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export as Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Status and Submit for Approval */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-3">
            <Badge variant={getStatusBadgeVariant(voucher.status)} className="text-sm">
              {voucher.status.replace('_', ' ').toUpperCase()}
            </Badge>
            <span className="text-sm text-muted-foreground">
              Last updated {formatDate(voucher.updatedAt)}
            </span>
          </div>

          <div className="flex items-center gap-2">
            {canSubmitForApproval && (
              <Button
                onClick={handleSubmitForApproval}
                disabled={submitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send for Approval
                  </>
                )}
              </Button>
            )}

            {canApproveVoucher() && (
              <>
                <Button
                  onClick={handleApproveVoucher}
                  disabled={approving}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {approving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleRejectVoucher}
                  disabled={approving}
                  variant="destructive"
                >
                  {approving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Voucher Document */}
        <Card className="max-w-4xl mx-auto">
          <CardContent className="p-8">
            {/* Header with Logo */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <Image
                  src="/images/logo.png"
                  alt="Teachers Council of Malawi Logo"
                  width={96}
                  height={96}
                  className="object-contain"
                  priority
                />
              </div>
              <h1 className="text-2xl font-bold text-primary">Teachers Council of Malawi</h1>
              <p className="text-muted-foreground">Professional Development & Regulation</p>
              <div className="mt-4">
                <h2 className="text-xl font-semibold">PAYMENT VOUCHER</h2>
                <p className="text-sm text-muted-foreground">Voucher No: {voucher.voucherNumber}</p>
              </div>
            </div>

            {/* Voucher Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Date:</label>
                    <p className="font-medium">{new Date(voucher.date).toLocaleDateString('en-MW', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Fiscal Year:</label>
                    <p className="font-medium">{voucher.fiscalYear}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Pay To:</label>
                  <p className="font-medium text-lg">{voucher.payee || 'N/A'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Amount:</label>
                  <p className="font-bold text-2xl text-primary">{formatCurrency(voucher.totalAmount)}</p>
                  <p className="text-sm text-muted-foreground mt-1 italic">
                    ({formatAmountInWords(voucher.totalAmount)})
                  </p>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Type:</label>
                    <Badge variant="outline" className="capitalize">
                      {voucher.voucherType}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status:</label>
                    <Badge variant={getStatusBadgeVariant(voucher.status)}>
                      {voucher.status === 'pending_approval'
                        ? 'Pending Approval'
                        : voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                    </Badge>
                  </div>
                </div>

                {voucher.paymentMethod && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Payment Method:</label>
                    <p className="font-medium capitalize">{voucher.paymentMethod.replace('_', ' ')}</p>
                  </div>
                )}

                {voucher.reference && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Reference:</label>
                    <p className="font-mono text-sm">{voucher.reference}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="mb-8">
              <label className="text-sm font-medium text-muted-foreground">Description/Purpose:</label>
              <div className="mt-2 p-4 border rounded-md bg-muted/20">
                <p className="text-sm leading-relaxed">{voucher.description}</p>
              </div>
            </div>

            {/* Notes */}
            {voucher.notes && (
              <div className="mb-8">
                <label className="text-sm font-medium text-muted-foreground">Additional Notes:</label>
                <div className="mt-2 p-4 border rounded-md bg-muted/20">
                  <p className="text-sm leading-relaxed whitespace-pre-wrap">{voucher.notes}</p>
                </div>
              </div>
            )}

            {/* Signature Section */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8 pt-8 border-t">
              <div className="text-center">
                <div className="border-b border-gray-300 mb-2 h-12"></div>
                <p className="text-sm font-medium">Prepared By</p>
                <p className="text-xs text-muted-foreground">Finance Department</p>
              </div>

              <div className="text-center">
                <div className="border-b border-gray-300 mb-2 h-12"></div>
                <p className="text-sm font-medium">Checked By</p>
                <p className="text-xs text-muted-foreground">Accountant</p>
              </div>

              <div className="text-center">
                <div className="border-b border-gray-300 mb-2 h-12"></div>
                <p className="text-sm font-medium">Approved By</p>
                <p className="text-xs text-muted-foreground">Finance Manager</p>
              </div>

              <div className="text-center">
                <div className="border-b border-gray-300 mb-2 h-12"></div>
                <p className="text-sm font-medium">Received By</p>
                <p className="text-xs text-muted-foreground">Payee Signature</p>
              </div>
            </div>

            {/* Footer Information */}
            <div className="border-t pt-6 mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-xs text-muted-foreground">
                {/* Left Footer */}
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Created By:</span> {getUserDisplayName(voucher.createdBy)}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {voucher.createdBy.email}
                  </div>
                  <div>
                    <span className="font-medium">Created Date:</span> {formatDate(voucher.createdAt)}
                  </div>
                  {voucher.updatedBy && (
                    <div>
                      <span className="font-medium">Last Updated By:</span> {getUserDisplayName(voucher.updatedBy)}
                    </div>
                  )}
                </div>

                {/* Right Footer */}
                <div className="space-y-2">
                  {voucher.approvedBy && (
                    <>
                      <div>
                        <span className="font-medium">Approved By:</span> {getUserDisplayName(voucher.approvedBy)}
                      </div>
                      {voucher.approvedAt && (
                        <div>
                          <span className="font-medium">Approved Date:</span> {formatDate(voucher.approvedAt)}
                        </div>
                      )}
                    </>
                  )}
                  <div>
                    <span className="font-medium">Category:</span> {voucher.voucherCategory.charAt(0).toUpperCase() + voucher.voucherCategory.slice(1)}
                  </div>
                  {voucher.sourceModule && (
                    <div>
                      <span className="font-medium">Source:</span> {voucher.sourceModule.charAt(0).toUpperCase() + voucher.sourceModule.slice(1)}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Document ID:</span> {voucher._id}
                  </div>
                </div>
              </div>

              {/* Organization Footer */}
              <div className="text-center mt-6 pt-4 border-t">
                <p className="text-xs text-muted-foreground">
                  Teachers Council of Malawi | P.O. Box 30579, Lilongwe 3, Malawi
                </p>
                <p className="text-xs text-muted-foreground">
                  Tel: +265 999 638 224/986976755 | Email: <EMAIL> | Website: www.teacherscouncil.mw
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

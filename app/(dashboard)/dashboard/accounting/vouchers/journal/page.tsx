import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { VoucherForm } from "@/components/accounting/vouchers/voucher-form";
import { CalendarDateRangePicker } from "@/components/date-range-picker";

export const metadata = {
  title: 'Journal Vouchers',
  description: 'Create and manage journal vouchers for the Teachers Council of Malawi.',
};

export default function JournalVoucherRoute() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Journal Vouchers</h1>
            <p className="text-muted-foreground">Create and manage journal vouchers for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button size="sm" className="gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>New Journal</span>
            </Button>
          </div>
        </div>

        {/* Voucher Content */}
        <Tabs defaultValue="new" className="w-full">
          <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
            <TabsList>
              <TabsTrigger value="new">New Journal Entry</TabsTrigger>
              <TabsTrigger value="list">Journal List</TabsTrigger>
            </TabsList>
            <CalendarDateRangePicker />
          </div>

          <TabsContent value="new" className="mt-4">
            <VoucherForm voucherType="journal" />
          </TabsContent>

          <TabsContent value="list" className="mt-4">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium mb-2">Journal Voucher List</h3>
              <p className="text-muted-foreground mb-4">
                The journal voucher list view is under development and will be available soon.
              </p>
              <Button variant="outline">Switch to New Journal</Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

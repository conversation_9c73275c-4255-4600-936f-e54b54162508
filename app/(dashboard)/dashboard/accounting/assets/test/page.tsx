'use client';

import { useEffect, useState } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

export default function AssetTestPage() {
  const [assets, setAssets] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchAssets = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/accounting/assets?limit=5');

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch assets');
        }

        const data = await response.json();

        setAssets(data.assets || []);
      } catch (error: unknown) {
        console.error('Error fetching assets:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to fetch assets',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssets();
  }, [toast]);

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Asset API Test"
        text="Testing the asset API with super-admin role"
      >
        <Button onClick={() => window.location.reload()}>
          Refresh
        </Button>
      </DashboardHeader>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>API Response</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <p>Loading assets...</p>
            ) : error ? (
              <div className="p-4 bg-red-50 text-red-800 rounded-md">
                <h3 className="font-bold">Error</h3>
                <p>{error}</p>
              </div>
            ) : assets.length === 0 ? (
              <p>No assets found.</p>
            ) : (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">Found {assets.length} assets</p>
                <pre className="p-4 bg-slate-100 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(assets, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

// app/(dashboard)/dashboard/accounting/assets/register/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Building,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash,
  Eye,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Tag,
  MapPin,
  FileText,
  Clock,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import { Asset, AssetFilter, useAssetStore } from '@/lib/store/useAssetStore';
import { AssetForm } from '@/components/accounting/assets/asset-form';
import { AssetDetailsDialog } from '@/components/accounting/assets/asset-details-dialog';
import { MaintenanceForm } from '@/components/accounting/assets/maintenance-form';
import { DepreciationForm } from '@/components/accounting/assets/depreciation-form';
import { DisposalForm } from '@/components/accounting/assets/disposal-form';
import { EmptyState } from '@/components/empty-state';
import { BulkAssetUpload } from '@/components/accounting/assets/bulk-asset-upload';

// Status badge colors
const statusColors = {
  active: 'bg-green-100 text-green-800',
  'under-maintenance': 'bg-yellow-100 text-yellow-800',
  disposed: 'bg-red-100 text-red-800',
  lost: 'bg-gray-100 text-gray-800',
  'written-off': 'bg-purple-100 text-purple-800',
};

export default function AssetRegisterPage() {
  const { toast } = useToast();
  const {
    assets,
    isLoading,
    error,
    pagination,
    filter,
    fetchAssets,
    setFilter,
    resetFilter,
    setPage,
    deleteAsset
  } = useAssetStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isAddAssetOpen, setIsAddAssetOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // Form states
  const [isMaintenanceFormOpen, setIsMaintenanceFormOpen] = useState(false);
  const [isCompletingMaintenance, setIsCompletingMaintenance] = useState(false);
  const [isDepreciationFormOpen, setIsDepreciationFormOpen] = useState(false);
  const [isDisposalFormOpen, setIsDisposalFormOpen] = useState(false);

  // Fetch assets on component mount
  useEffect(() => {
    fetchAssets();
  }, [fetchAssets]);

  // Apply filters when they change
  useEffect(() => {
    const newFilter: Partial<AssetFilter> = {};

    if (categoryFilter !== 'all') {
      newFilter.category = categoryFilter;
    }

    if (statusFilter !== 'all') {
      newFilter.status = statusFilter;
    }

    if (searchTerm) {
      newFilter.search = searchTerm;
    }

    setFilter(newFilter);
    fetchAssets(1, pagination.limit, newFilter);
  }, [categoryFilter, statusFilter, searchTerm, setFilter, fetchAssets, pagination.limit]);

  // Handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Map tab value to category filter
    if (value === 'all') {
      setCategoryFilter('all');
    } else if (value === 'it') {
      setCategoryFilter('IT Equipment');
    } else if (value === 'office') {
      setCategoryFilter('Office Equipment');
    } else if (value === 'vehicles') {
      setCategoryFilter('Vehicles');
    } else if (value === 'furniture') {
      setCategoryFilter('Furniture');
    } else if (value === 'real-estate') {
      setCategoryFilter('Real Estate');
    }
  };

  // Handle view asset details
  const handleViewAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsDetailsOpen(true);
  };

  // Handle edit asset
  const handleEditAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsAddAssetOpen(true);
  };

  // Handle delete asset
  const handleDeleteAsset = async (id: string) => {
    if (confirm('Are you sure you want to delete this asset? This action cannot be undone.')) {
      const success = await deleteAsset(id);
      if (success) {
        setIsDetailsOpen(false);
      }
    }
  };

  // Handle record maintenance
  const handleRecordMaintenance = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsCompletingMaintenance(false);
    setIsMaintenanceFormOpen(true);
  };

  // Handle complete maintenance
  const handleCompleteMaintenance = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsCompletingMaintenance(true);
    setIsMaintenanceFormOpen(true);
  };

  // Handle record depreciation
  const handleRecordDepreciation = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsDepreciationFormOpen(true);
  };

  // Handle dispose asset
  const handleDisposeAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsDisposalFormOpen(true);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setPage(page);
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Asset Register"
        text="Track and manage all fixed assets, their values, and depreciation"
      >
        <div className="flex space-x-2">
          <Button onClick={() => {
            setSelectedAsset(null);
            setIsAddAssetOpen(true);
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Asset
          </Button>
          <BulkAssetUpload onSuccess={() => fetchAssets()} />
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="all">All Assets</TabsTrigger>
            <TabsTrigger value="it">IT Equipment</TabsTrigger>
            <TabsTrigger value="office">Office Equipment</TabsTrigger>
            <TabsTrigger value="vehicles">Vehicles</TabsTrigger>
            <TabsTrigger value="furniture">Furniture</TabsTrigger>
            <TabsTrigger value="real-estate">Real Estate</TabsTrigger>
          </TabsList>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search assets..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="under-maintenance">Under Maintenance</SelectItem>
              <SelectItem value="disposed">Disposed</SelectItem>
              <SelectItem value="lost">Lost</SelectItem>
              <SelectItem value="written-off">Written Off</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={() => {
            setSearchTerm('');
            setCategoryFilter('all');
            setStatusFilter('all');
            resetFilter();
            fetchAssets();
          }}>
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <TabsContent value={activeTab} className="space-y-4">
          {error && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <p className="text-red-500">{error}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : assets.length === 0 ? (
            <EmptyState
              title="No assets found"
              description="There are no assets matching your filters. Try adjusting your search or add a new asset."
              icon={<Building className="h-10 w-10 text-muted-foreground" />}
              action={
                <Button onClick={() => {
                  setSelectedAsset(null);
                  setIsAddAssetOpen(true);
                }}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Asset
                </Button>
              }
            />
          ) : (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Asset ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Acquisition Date</TableHead>
                      <TableHead>Current Value</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {assets.map(asset => (
                      <TableRow key={asset._id}>
                        <TableCell className="font-medium">{asset.assetId}</TableCell>
                        <TableCell>{asset.name}</TableCell>
                        <TableCell>{asset.category}</TableCell>
                        <TableCell>{asset.location}</TableCell>
                        <TableCell>{format(new Date(asset.acquisitionDate), 'dd MMM yyyy')}</TableCell>
                        <TableCell>{asset.currency} {asset.currentBookValue.toLocaleString()}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={statusColors[asset.status]}>
                            {asset.status.charAt(0).toUpperCase() + asset.status.slice(1).replace('-', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewAsset(asset)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditAsset(asset)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleRecordDepreciation(asset)}>
                                <DollarSign className="mr-2 h-4 w-4" />
                                Record Depreciation
                              </DropdownMenuItem>
                              {asset.status === 'active' && (
                                <DropdownMenuItem onClick={() => handleRecordMaintenance(asset)}>
                                  <Clock className="mr-2 h-4 w-4" />
                                  Record Maintenance
                                </DropdownMenuItem>
                              )}
                              {asset.status === 'under-maintenance' && (
                                <DropdownMenuItem onClick={() => handleCompleteMaintenance(asset)}>
                                  <Clock className="mr-2 h-4 w-4" />
                                  Complete Maintenance
                                </DropdownMenuItem>
                              )}
                              {asset.status === 'active' && (
                                <DropdownMenuItem onClick={() => handleDisposeAsset(asset)}>
                                  <Tag className="mr-2 h-4 w-4" />
                                  Dispose Asset
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteAsset(asset._id)}
                                className="text-red-600"
                              >
                                <Trash className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex items-center justify-between p-4">
                <div className="text-sm text-muted-foreground">
                  Showing {assets.length} of {pagination.total} assets
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                  >
                    Previous
                  </Button>
                  <div className="text-sm">
                    Page {pagination.page} of {pagination.totalPages}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.totalPages}
                  >
                    Next
                  </Button>
                </div>
              </CardFooter>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Add/Edit Asset Form */}
      <AssetForm
        isOpen={isAddAssetOpen}
        onClose={() => setIsAddAssetOpen(false)}
        asset={selectedAsset}
        isEditing={!!selectedAsset}
      />

      {/* Asset Details Dialog */}
      {selectedAsset && (
        <AssetDetailsDialog
          isOpen={isDetailsOpen}
          onClose={() => setIsDetailsOpen(false)}
          asset={selectedAsset}
          onEdit={handleEditAsset}
          onDelete={handleDeleteAsset}
        />
      )}

      {/* Maintenance Form */}
      {selectedAsset && (
        <MaintenanceForm
          isOpen={isMaintenanceFormOpen}
          onClose={() => setIsMaintenanceFormOpen(false)}
          asset={selectedAsset}
          isCompleting={isCompletingMaintenance}
        />
      )}

      {/* Depreciation Form */}
      {selectedAsset && (
        <DepreciationForm
          isOpen={isDepreciationFormOpen}
          onClose={() => setIsDepreciationFormOpen(false)}
          asset={selectedAsset}
        />
      )}

      {/* Disposal Form */}
      {selectedAsset && (
        <DisposalForm
          isOpen={isDisposalFormOpen}
          onClose={() => setIsDisposalFormOpen(false)}
          asset={selectedAsset}
        />
      )}
    </DashboardShell>
  );
}

'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { ImportDataForm } from '@/components/accounting/import-export/import-data-form';
import { ExportDataForm } from '@/components/accounting/import-export/export-data-form';
import { TemplateManager } from '@/components/accounting/import-export/template-manager';
import { Button } from '@/components/ui/button';
import { Download, Upload, FileText, RefreshCw } from 'lucide-react';

/**
 * Import/Export Page
 * This page provides a UI for importing and exporting data
 */
export default function ImportExportPage() {
  const [activeTab, setActiveTab] = useState('import');

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading="Data Import & Export"
        subheading="Import and export data to and from the system"
      />

      <Tabs defaultValue="import" className="mt-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
          <TabsTrigger value="import">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </TabsTrigger>
          <TabsTrigger value="export">
            <Download className="mr-2 h-4 w-4" />
            Export
          </TabsTrigger>
          <TabsTrigger value="templates">
            <FileText className="mr-2 h-4 w-4" />
            Templates
          </TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Upload className="mr-2 h-5 w-5" />
                Import Data
              </CardTitle>
              <CardDescription>
                Import data from CSV, Excel, or JSON files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImportDataForm />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Download className="mr-2 h-5 w-5" />
                Export Data
              </CardTitle>
              <CardDescription>
                Export data to CSV, Excel, or JSON files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ExportDataForm />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-xl flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Import/Export Templates
                </CardTitle>
                <CardDescription>
                  Manage templates for importing and exporting data
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={() => {
                fetch('/api/accounting/import-export/templates/initialize', {
                  method: 'POST'
                }).then(() => {
                  window.location.reload();
                });
              }}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Initialize Default Templates
              </Button>
            </CardHeader>
            <CardContent>
              <TemplateManager type={activeTab === 'import' ? 'import' : 'export'} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

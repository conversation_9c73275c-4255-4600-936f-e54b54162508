// app/(dashboard)/accounting/index/page.tsx
'use client';

import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarC<PERSON>4, Pie<PERSON><PERSON>, TrendingUp, TrendingDown, Receipt, Wallet, Building, FileText, BookOpen, Landmark, Link as LinkIcon, ArrowRight, CreditCard, Shield } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/lib/frontend/hooks/useAuth';
import { UserRole } from '@/types/user-roles';
import { useEffect, useState } from 'react';

export default function AccountingIndexPage() {
  const { user } = useAuth();
  const [filteredModules, setFilteredModules] = useState<any[]>([]);

  const accountingModules = [
    {
      title: "Financial Dashboard",
      description: "Overview of financial health and key metrics for the Teachers Council of Malawi",
      icon: Bar<PERSON>hart4,
      href: "/dashboard/accounting/dashboard",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST]
    },
    {
      title: "Budget & Planning",
      description: "Create and manage budget plans for income and expenditure",
      icon: PieChart,
      href: "/dashboard/accounting/budget/planning",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Income Management",
      description: "Track and manage income sources including government subventions and fees",
      icon: TrendingUp,
      href: "/dashboard/accounting/income/overview",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Expenditure Management",
      description: "Track and manage expenses across all departments and categories",
      icon: TrendingDown,
      href: "/dashboard/accounting/expenditures",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Voucher Management",
      description: "Create and manage payment, receipt, and journal vouchers",
      icon: Receipt,
      href: "/dashboard/accounting/vouchers/payment",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Payroll & Benefits",
      description: "Process salaries, pensions, and manage employee benefits",
      icon: Wallet,
      href: "/dashboard/accounting/payroll/processing",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST]
    },
    {
      title: "Asset Management",
      description: "Track and manage fixed assets, depreciation, and maintenance",
      icon: Building,
      href: "/dashboard/accounting/assets/register",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Financial Reporting",
      description: "Generate quarterly and annual financial reports for compliance",
      icon: FileText,
      href: "/dashboard/accounting/reports",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Accounting Core",
      description: "Manage chart of accounts, general ledger, and journal entries",
      icon: BookOpen,
      href: "/dashboard/accounting/ledger/chart-of-accounts",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Banking & Treasury",
      description: "Manage bank accounts, reconciliation, and cash flow",
      icon: Landmark,
      href: "/dashboard/accounting/banking",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Payment Management",
      description: "Configure payment gateways and process electronic payments",
      icon: CreditCard,
      href: "/dashboard/accounting/payments",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    },
    {
      title: "Security Management",
      description: "Manage access control, audit logs, and security settings",
      icon: Shield,
      href: "/dashboard/accounting/security",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR]
    },
    {
      title: "Integrations",
      description: "Connect with QuickBooks, Sage, and other accounting systems",
      icon: LinkIcon,
      href: "/dashboard/accounting/integrations",
      roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
    }
  ];

  // Filter modules based on user role
  useEffect(() => {
    if (user?.role) {
      const filtered = accountingModules.filter(module =>
        module.roles.includes(user.role)
      );
      setFilteredModules(filtered);
    } else {
      setFilteredModules(accountingModules);
    }
  }, [user?.role]);

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Accounting System"
        text="Comprehensive financial management system for the Teachers Council of Malawi"
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredModules.map((module) => (
          <Link href={module.href} key={module.href} className="block">
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">{module.title}</CardTitle>
                <module.icon className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">{module.description}</CardDescription>
                <div className="mt-4 flex items-center text-sm text-primary">
                  <span>Access module</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </DashboardShell>
  );
}

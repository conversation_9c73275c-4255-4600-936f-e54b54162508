'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardShell } from "@/components/dashboard-shell";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, X, Loader2 } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { ProgressiveIncomeForm } from "@/components/accounting/income/progressive-income-form";
import { useIncomeStore } from "@/lib/stores/enhanced-income-store";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface EditIncomePageProps {
  params: Promise<{ id: string }>;
}

export default function EditIncomePage({ params }: EditIncomePageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingIncome, setIsLoadingIncome] = useState(true);
  const [income, setIncome] = useState<any>(null);
  const [incomeId, setIncomeId] = useState<string>('');
  
  // Use enhanced income store for form data
  const { updateIncome, getIncomeById } = useIncomeStore();

  // Get the income ID from params
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setIncomeId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  // Load income data
  useEffect(() => {
    const loadIncome = async () => {
      if (!incomeId) return;
      
      try {
        setIsLoadingIncome(true);
        
        // Try to get income from store first
        const incomeData = await getIncomeById(incomeId);
        
        if (!incomeData) {
          toast({
            title: "Error",
            description: "Income record not found.",
            variant: "destructive",
          });
          router.push('/dashboard/accounting/income/overview');
          return;
        }
        
        setIncome(incomeData);
      } catch (error) {
        console.error('Error loading income:', error);
        toast({
          title: "Error",
          description: "Failed to load income record.",
          variant: "destructive",
        });
        router.push('/dashboard/accounting/income/overview');
      } finally {
        setIsLoadingIncome(false);
      }
    };

    loadIncome();
  }, [incomeId, getIncomeById, toast, router]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      console.log('Form submission started:', data);
      setIsLoading(true);

      // Use store method to update income
      await updateIncome(incomeId, data);
      console.log('Income updated successfully');

      toast({
        title: "Success",
        description: "Income record updated successfully.",
      });

      // Navigate back to overview page
      router.push('/dashboard/accounting/income/overview');
      
    } catch (error) {
      console.error('Error updating income:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update income record.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form cancellation
  const handleCancel = () => {
    router.push('/dashboard/accounting/income/overview');
  };

  // Loading state
  if (isLoadingIncome) {
    return (
      <DashboardShell>
        <div className="flex flex-col gap-8">
          {/* Page Header Skeleton */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard/accounting/income/overview">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Income Overview
                  </Link>
                </Button>
              </div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>

          {/* Form Container Skeleton */}
          <div className="max-w-4xl mx-auto w-full">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin mr-2" />
                  <span>Loading income record...</span>
                </div>
              </CardHeader>
            </Card>
          </div>
        </div>
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/dashboard/accounting/income/overview">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Income Overview
                </Link>
              </Button>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Income Record</h1>
            <p className="text-muted-foreground">
              Update income transaction details for the Teachers Council of Malawi
            </p>
            {income && (
              <div className="mt-2">
                <Badge variant="outline" className="text-xs">
                  Reference: {income.reference}
                </Badge>
                <Badge variant="outline" className="text-xs ml-2">
                  Amount: MWK {income.amount?.toLocaleString()}
                </Badge>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Edit Mode
            </Badge>
            <Badge variant="default" className="text-xs">
              Dedicated Page
            </Badge>
          </div>
        </div>

        {/* Form Container */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Update Income Transaction</span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ProgressiveIncomeForm
                income={income}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </div>

        {/* Help Section */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edit Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Important Notes</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Changes will update the existing record</li>
                    <li>• Budget allocations may be affected</li>
                    <li>• Reference numbers should remain unique</li>
                    <li>• Status changes may trigger workflows</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Audit Trail</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• All changes are logged</li>
                    <li>• Original creation date preserved</li>
                    <li>• Update timestamp will be recorded</li>
                    <li>• User information tracked</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  );
}

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  CheckCircle,
  AlertTriangle,
  Zap
} from 'lucide-react';
import { OptimizedIncomeForm } from '@/components/accounting/income/optimized-income-form';
import { useToast } from '@/hooks/use-toast';

// Mock data for demonstration - in production, this would come from API
const MOCK_INCOME_DATA = [
  {
    id: '1',
    date: '2024-12-01',
    source: 'government_subvention',
    amount: 500000,
    reference: 'GOV-2024-001',
    status: 'received',
    fiscalYear: '2024-2025'
  },
  {
    id: '2',
    date: '2024-12-02',
    source: 'registration_fees',
    amount: 75000,
    reference: 'REG-2024-045',
    status: 'approved',
    fiscalYear: '2024-2025'
  },
  {
    id: '3',
    date: '2024-12-03',
    source: 'licensing_fees',
    amount: 120000,
    reference: 'LIC-2024-023',
    status: 'pending_approval',
    fiscalYear: '2024-2025'
  }
];

const SUMMARY_STATS = {
  totalIncome: 695000,
  monthlyIncome: 695000,
  pendingApproval: 120000,
  averageTransaction: 231667
};

export default function OptimizedIncomePage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showForm, setShowForm] = useState(false);
  const [selectedIncome, setSelectedIncome] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const { toast } = useToast();

  // Handle form submission
  const handleFormSubmit = useCallback(async (formData: any) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Form submitted:', formData);
      
      toast({
        title: selectedIncome ? 'Income Updated' : 'Income Created',
        description: selectedIncome 
          ? 'The income record has been updated successfully' 
          : 'New income record has been created successfully',
      });
      
      // Reset form state
      setShowForm(false);
      setSelectedIncome(null);
      
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: 'Error',
        description: 'Failed to save income record. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedIncome, toast]);

  // Handle form cancel
  const handleFormCancel = useCallback(() => {
    setShowForm(false);
    setSelectedIncome(null);
  }, []);

  // Handle new income
  const handleNewIncome = useCallback(() => {
    setSelectedIncome(null);
    setShowForm(true);
  }, []);

  // Handle edit income
  const handleEditIncome = useCallback((income: any) => {
    setSelectedIncome(income);
    setShowForm(true);
  }, []);

  // Get status badge variant
  const getStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'received': return 'default';
      case 'approved': return 'secondary';
      case 'pending_approval': return 'outline';
      case 'draft': return 'outline';
      default: return 'outline';
    }
  }, []);

  // Memoized summary cards
  const summaryCards = useMemo(() => (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Income</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">MWK {SUMMARY_STATS.totalIncome.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            +12.5% from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">This Month</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">MWK {SUMMARY_STATS.monthlyIncome.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            3 transactions recorded
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">MWK {SUMMARY_STATS.pendingApproval.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            1 item awaiting approval
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Transaction</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">MWK {SUMMARY_STATS.averageTransaction.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            Based on recent activity
          </p>
        </CardContent>
      </Card>
    </div>
  ), []);

  // Memoized income list
  const incomeList = useMemo(() => (
    <div className="space-y-4">
      {MOCK_INCOME_DATA.map((income) => (
        <Card key={income.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">{income.reference}</h3>
                  <Badge variant={getStatusVariant(income.status)}>
                    {income.status.replace('_', ' ')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {income.source.replace('_', ' ')} • {income.date}
                </p>
              </div>
              <div className="text-right space-y-1">
                <div className="text-lg font-bold">
                  MWK {income.amount.toLocaleString()}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditIncome(income)}
                >
                  Edit
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  ), [getStatusVariant, handleEditIncome]);

  // Show form if requested
  if (showForm) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              Optimized Performance
            </Badge>
            <h1 className="text-2xl font-bold">
              {selectedIncome ? 'Edit Income' : 'New Income'}
            </h1>
          </div>
        </div>

        <OptimizedIncomeForm
          income={selectedIncome}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <Badge variant="outline" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              Optimized Performance
            </Badge>
            <h1 className="text-2xl font-bold">Income Management</h1>
          </div>
          <p className="text-muted-foreground">
            Manage income records with optimized performance
          </p>
        </div>
        <Button onClick={handleNewIncome}>
          <Plus className="mr-2 h-4 w-4" />
          New Income
        </Button>
      </div>

      {/* Performance Alert */}
      <Alert className="mb-6">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          This page uses performance optimizations including debounced inputs, 
          throttled API calls, and memoized components to prevent browser freezing.
        </AlertDescription>
      </Alert>

      {/* Summary Cards */}
      {summaryCards}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="recent">Recent Income</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Income Records</CardTitle>
            </CardHeader>
            <CardContent>
              {incomeList}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Income Records</CardTitle>
            </CardHeader>
            <CardContent>
              {incomeList}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Income Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Analytics dashboard coming soon...</p>
                <p className="text-sm">This will include charts and detailed analysis</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

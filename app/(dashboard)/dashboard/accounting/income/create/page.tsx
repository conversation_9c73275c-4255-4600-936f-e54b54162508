'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, X } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { ProgressiveIncomeForm } from "@/components/accounting/income/progressive-income-form";
import { useIncomeStore } from "@/lib/stores/enhanced-income-store";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function CreateIncomePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // Use enhanced income store for form data
  const { createIncome } = useIncomeStore();

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      console.log('Form submission started:', data);
      setIsLoading(true);

      // Use store method to create income
      const result = await createIncome(data);
      console.log('Income created successfully:', result);

      toast({
        title: "Success",
        description: "Income record created successfully.",
      });

      // Navigate back to overview page
      router.push('/dashboard/accounting/income/overview');
      
    } catch (error) {
      console.error('Error creating income:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create income record.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form cancellation
  const handleCancel = () => {
    router.push('/dashboard/accounting/income/overview');
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/dashboard/accounting/income/overview">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Income Overview
                </Link>
              </Button>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Income Record</h1>
            <p className="text-muted-foreground">
              Record a new income transaction. Select existing budget and category, then income will be saved to Income DB, Budget totals, and Category items.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Progressive Form
            </Badge>
            <Badge variant="default" className="text-xs">
              Multi-Save
            </Badge>
          </div>
        </div>

        {/* Form Container */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Income Transaction Details</span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ProgressiveIncomeForm
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </div>

        {/* Help Section */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Form Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Required Fields</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Date of income transaction</li>
                    <li>• Budget selection (from existing budgets)</li>
                    <li>• Budget category (auto-filtered by income type)</li>
                    <li>• Amount in Malawi Kwacha (MWK)</li>
                    <li>• Reference number or identifier</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Multi-Save Operations</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Saved to Income database</li>
                    <li>• Added to Budget total income</li>
                    <li>• Added to Budget Category items</li>
                    <li>• Draft = Projected, Approved = Expected, Received = Actual</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  );
}

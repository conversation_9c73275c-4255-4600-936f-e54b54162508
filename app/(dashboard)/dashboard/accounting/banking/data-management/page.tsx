import { Metadata } from 'next';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { TransactionExporter } from '@/components/accounting/banking/transaction-exporter';
import { TransactionImporter } from '@/components/accounting/banking/transaction-importer';
import { StatementProcessor } from '@/components/accounting/banking/statement-processor';
import { getBankAccounts } from '@/lib/backend/services/banking/bank-account-service';

export const metadata: Metadata = {
  title: 'Banking Data Management | TCM Enterprise Business Suite',
  description: 'Import, export, and process banking data',
};

export default async function BankingDataManagementPage() {
  // Get bank accounts
  const bankAccounts = await getBankAccounts();

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Banking Data Management</h1>
        <p className="text-muted-foreground mt-2">
          Import, export, and process banking data
        </p>
      </div>

      <Tabs defaultValue="import" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="import">Import Transactions</TabsTrigger>
          <TabsTrigger value="export">Export Transactions</TabsTrigger>
          <TabsTrigger value="process">Process Statements</TabsTrigger>
        </TabsList>
        <TabsContent value="import">
          <TransactionImporter bankAccounts={bankAccounts} />
        </TabsContent>
        <TabsContent value="export">
          <TransactionExporter bankAccounts={bankAccounts} />
        </TabsContent>
        <TabsContent value="process">
          <div className="space-y-4">
            {bankAccounts.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No bank accounts found. Please create a bank account first.
              </div>
            ) : (
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Select Bank Account</label>
                  <select
                    className="w-full border rounded px-3 py-2"
                    defaultValue={bankAccounts[0]?.id}
                    id="bankAccountSelector"
                  >
                    {bankAccounts.map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.accountNumber})
                      </option>
                    ))}
                  </select>
                </div>
                <StatementProcessor bankAccountId={bankAccounts[0]?.id} />
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

import { Metadata } from 'next';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BatchPaymentProcessor } from '@/components/accounting/banking/batch-payment-processor';
import { PaymentApprovalQueue } from '@/components/accounting/banking/payment-approval-queue';

export const metadata: Metadata = {
  title: 'Payment Processing | TCM Enterprise Business Suite',
  description: 'Process payments and manage payment approvals',
};

export default function PaymentProcessingPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Payment Processing</h1>
        <p className="text-muted-foreground mt-2">
          Process payments and manage payment approvals
        </p>
      </div>

      <Tabs defaultValue="batch-processing" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="batch-processing">Batch Processing</TabsTrigger>
          <TabsTrigger value="approval-queue">Approval Queue</TabsTrigger>
        </TabsList>
        <TabsContent value="batch-processing">
          <BatchPaymentProcessor />
        </TabsContent>
        <TabsContent value="approval-queue">
          <PaymentApprovalQueue />
        </TabsContent>
      </Tabs>
    </div>
  );
}

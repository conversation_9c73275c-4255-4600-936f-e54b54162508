import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

import { BankingIntegrationManager } from '@/components/accounting/banking/integration/banking-integration-manager';
import { BankAccount } from '@/types/banking';

// Sample bank accounts
const sampleBankAccounts: BankAccount[] = [
  {
    id: "1",
    name: "TCM Operations Account",
    accountNumber: "**********",
    bank: "National Bank of Malawi",
    currency: "MWK",
    type: "checking",
    balance: 5000000,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
  {
    id: "2",
    name: "TCM Payroll Account",
    accountNumber: "**********",
    bank: "Standard Bank",
    currency: "MWK",
    type: "checking",
    balance: 2000000,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
  {
    id: "3",
    name: "TCM Reserve Account",
    accountNumber: "**********",
    bank: "FDH Bank",
    currency: "MWK",
    type: "savings",
    balance: ********,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
];

export const metadata = {
  title: 'Banking Integrations',
  description: 'Manage banking API integrations for the Teachers Council of Malawi.',
};

export default function BankingIntegrationsPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Banking Integrations</h1>
            <p className="text-muted-foreground">Connect to banking APIs for automated data feeds and electronic payments.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting/banking">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Banking</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Banking Integrations Content */}
        <BankingIntegrationManager bankAccounts={sampleBankAccounts} />
      </div>
    </DashboardShell>
  );
}

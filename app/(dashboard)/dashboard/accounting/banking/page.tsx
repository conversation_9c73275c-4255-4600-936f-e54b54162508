import { BankingPageClient } from '@/components/accounting/banking/banking-page-client';
import { BankAccount } from '@/types/banking';
import { BankTransaction } from '@/types/reconciliation';

export const metadata = {
  title: 'Banking & Treasury Management',
  description: 'Manage bank accounts, reconciliations, and cash flow for the Teachers Council of Malawi.',
};

// Sample bank accounts data
const bankAccounts: BankAccount[] = [
  {
    id: "1",
    name: "TCM Operations Account",
    accountNumber: "**********",
    bank: "National Bank of Malawi",
    currency: "MWK",
    type: "checking",
    balance: 5000000,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
  {
    id: "2",
    name: "TCM Payroll Account",
    accountNumber: "**********",
    bank: "Standard Bank",
    currency: "MWK",
    type: "checking",
    balance: 2000000,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
  {
    id: "3",
    name: "TCM Reserve Account",
    accountNumber: "**********",
    bank: "FDH Bank",
    currency: "MWK",
    type: "savings",
    balance: ********,
    status: "active",
    openDate: new Date(2020, 0, 1),
  },
];

// Sample transactions data
const sampleTransactions: BankTransaction[] = Array.from({ length: 20 }, (_, i) => ({
  id: `TX-${i + 1}`,
  date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date in the last 30 days
  description: `Transaction ${i + 1}`,
  reference: `REF-${Math.floor(Math.random() * 10000)}`,
  amount: Math.round((Math.random() * 2000 - 1000) * 100) / 100, // Random amount between -1000 and 1000
  category: "operations",
  reconciled: Math.random() > 0.7, // 30% chance of being reconciled
  bankStatement: false,
}));

export default function BankingTreasuryPage() {
  return <BankingPageClient bankAccounts={bankAccounts} sampleTransactions={sampleTransactions} />;
}

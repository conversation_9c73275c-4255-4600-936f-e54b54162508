// app/(dashboard)/dashboard/accounting/integrations/[id]/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft, RefreshCw, Settings, Database,
  FileText, Download, Upload, History, Play
} from 'lucide-react';
import { IntegrationDetails } from '@/components/accounting/integrations/integration-details';
import { IntegrationSettings } from '@/components/accounting/integrations/integration-settings';
import { IntegrationDataMapping } from '@/components/accounting/integrations/integration-data-mapping';
import { IntegrationSyncHistory } from '@/components/accounting/integrations/integration-sync-history';
import { IntegrationImportExport } from '@/components/accounting/integrations/integration-import-export';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';

// Define the Integration interface
interface Integration {
  id: string;
  name: string;
  type: string;
  provider: string;
  isAuthenticated: boolean;
  status: 'active' | 'inactive' | 'error';
  lastSyncDate?: string;
  connectionDetails?: {
    apiKey?: string;
    apiUrl?: string;
    username?: string;
    [key: string]: any;
  };
  settings?: {
    [key: string]: any;
  };
  mappings?: {
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
  version?: string;
  apiEndpoint?: string;
  authType?: string;
  lastSyncAt?: string;
  nextSyncAt?: string;
  stats?: {
    recordsSynced?: number;
    successfulSyncs?: number;
    failedSyncs?: number;
  };
}

/**
 * Integration Detail Page
 * This page provides a UI for managing a specific integration
 */
export default function IntegrationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [integration, setIntegration] = useState<Integration | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const integrationId = params?.id as string;

  useEffect(() => {
    const fetchIntegration = async () => {
      try {
        const response = await fetch(`/api/accounting/integrations/${integrationId}`);
        if (!response.ok) throw new Error('Failed to fetch integration');

        const data = await response.json();
        setIntegration(data);
        setIsAuthenticated(data.isAuthenticated || false);
      } catch (error: unknown) {
        console.error('Error fetching integration:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        toast({
          title: 'Error',
          description: `Failed to load integration details: ${errorMessage}`,
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchIntegration();
  }, [integrationId, toast]);

  const handleAuthenticate = async () => {
    try {
      const response = await fetch(`/api/accounting/integrations/${integrationId}/authenticate`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Authentication failed');

      const data = await response.json();

      if (data.authUrl) {
        // Open the authentication URL in a new window
        window.open(data.authUrl, '_blank', 'width=800,height=600');
      } else if (data.success) {
        setIsAuthenticated(true);
        toast({
          title: 'Success',
          description: 'Integration authenticated successfully',
        });
      }
    } catch (error: unknown) {
      console.error('Error authenticating:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to authenticate integration: ${errorMessage}`,
        variant: 'destructive',
      });
    }
  };

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      const response = await fetch(`/api/accounting/integrations/${integrationId}/sync`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Sync failed');

      toast({
        title: 'Success',
        description: 'Synchronization started successfully',
      });
    } catch (error: unknown) {
      console.error('Error syncing:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to start synchronization: ${errorMessage}`,
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <Skeleton className="h-12 w-[250px]" />
        <Skeleton className="h-4 w-[350px] mt-2" />
        <Skeleton className="h-[400px] w-full mt-6" />
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading={integration?.name || 'Integration Details'}
        subheading={`Manage ${integration?.type || ''} integration settings and data synchronization`}
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            {!isAuthenticated ? (
              <Button size="sm" onClick={handleAuthenticate}>
                <Settings className="mr-2 h-4 w-4" />
                Authenticate
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleSync}
                disabled={isSyncing}
              >
                <Play className="mr-2 h-4 w-4" />
                {isSyncing ? 'Syncing...' : 'Sync Now'}
              </Button>
            )}
          </div>
        }
      />

      <Tabs defaultValue="details" className="mt-6">
        <TabsList className="grid w-full grid-cols-5 lg:w-[800px]">
          <TabsTrigger value="details">
            <FileText className="mr-2 h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="mapping">
            <Database className="mr-2 h-4 w-4" />
            Data Mapping
          </TabsTrigger>
          <TabsTrigger value="sync">
            <History className="mr-2 h-4 w-4" />
            Sync History
          </TabsTrigger>
          <TabsTrigger value="import-export">
            <Download className="mr-2 h-4 w-4" />
            Import/Export
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Details</CardTitle>
              <CardDescription>
                View details about this integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationDetails integration={integration as Integration} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Configure connection settings for this integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationSettings
                integration={integration as Integration}
                isAuthenticated={isAuthenticated}
                onAuthenticate={handleAuthenticate}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mapping" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Mapping</CardTitle>
              <CardDescription>
                Configure how data is mapped between systems
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationDataMapping
                integration={integration as Integration}
                isAuthenticated={isAuthenticated}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Synchronization History</CardTitle>
              <CardDescription>
                View history of synchronization jobs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationSyncHistory
                integrationId={integrationId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import-export" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Import & Export</CardTitle>
              <CardDescription>
                Manually import or export data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationImportExport
                integration={integration as Integration}
                isAuthenticated={isAuthenticated}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" disabled={!isAuthenticated}>
                <Upload className="mr-2 h-4 w-4" />
                Export to {integration?.type}
              </Button>
              <Button variant="outline" disabled={!isAuthenticated}>
                <Download className="mr-2 h-4 w-4" />
                Import from {integration?.type}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { Button } from '@/components/ui/button';
import { RefreshCw, Plus } from 'lucide-react';
import { IntegrationsList } from '@/components/accounting/integrations/integrations-list';
import { AddIntegrationDialog } from '@/components/accounting/integrations/add-integration-dialog';

/**
 * Integrations Page
 * This page provides a UI for managing external accounting system integrations
 */
export default function IntegrationsPage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading="Accounting Integrations"
        subheading="Connect with external accounting systems"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button size="sm" onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Integration
            </Button>
          </div>
        }
      />

      <Card>
        <CardHeader>
          <CardTitle>Available Integrations</CardTitle>
          <CardDescription>
            Manage connections to external accounting systems like QuickBooks, Xero, and Sage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <IntegrationsList />
        </CardContent>
      </Card>

      <AddIntegrationDialog 
        open={isAddDialogOpen} 
        onOpenChange={setIsAddDialogOpen} 
      />
    </div>
  );
}

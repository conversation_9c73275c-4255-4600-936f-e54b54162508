// app/(dashboard)/dashboard/accounting/expenditure/overview/page.tsx
"use client"

import React, { useState } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  BarChart3,
  Plus,
  FileText,
  TrendingUp,
  DollarSign,
  AlertTriangle,
  Settings,
  Download,
  Upload,
  Filter,
  Search,
  Calendar,
  Building
} from 'lucide-react';

// Import our expenditure components
import { ExpenditureDashboard } from '@/components/accounting/expenditures/expenditure-dashboard';
import { ExpenditureForm } from '@/components/accounting/expenditures/expenditure-form';
import { ExpenditureTable } from '@/components/accounting/expenditures/expenditure-table';
import { ExpenditureMetrics } from '@/components/accounting/expenditures/expenditure-metrics';
import { AIAnalyticsDashboard } from '@/components/accounting/expenditures/ai-analytics-dashboard';

// Import hooks
import {
  useExpenditures,
  useExpenditureStatistics,
  useExpenditureManagement
} from '@/lib/hooks/accounting/use-expenditure-management';

export default function ExpenditureOverviewRoute() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedExpenditureId, setSelectedExpenditureId] = useState<string | null>(null);
  const [showViewDialog, setShowViewDialog] = useState(false);

  const { formatCurrency } = useExpenditureManagement();

  // Get expenditure statistics for the overview
  const {
    data: statisticsData,
    isLoading: isLoadingStatistics,
    error: statisticsError
  } = useExpenditureStatistics();

  // Get recent expenditures
  const {
    data: expendituresData,
    isLoading: isLoadingExpenditures,
    error: expendituresError
  } = useExpenditures({}, 1, 10, 'createdAt', 'desc');

  const statistics = statisticsData?.statistics;
  const expenditures = expendituresData?.expenditures || [];

  // Handle create expenditure
  const handleCreateExpenditure = () => {
    setShowCreateForm(true);
  };

  // Handle edit expenditure
  const handleEditExpenditure = (expenditureId: string) => {
    setSelectedExpenditureId(expenditureId);
    setShowEditForm(true);
  };

  // Handle view expenditure
  const handleViewExpenditure = (expenditureId: string) => {
    setSelectedExpenditureId(expenditureId);
    setShowViewDialog(true);
  };

  // Handle form submission
  const handleFormSubmit = (data: any) => {
    console.log('Form submitted:', data);
    setShowCreateForm(false);
    setShowEditForm(false);
    setSelectedExpenditureId(null);
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setShowCreateForm(false);
    setShowEditForm(false);
    setSelectedExpenditureId(null);
  };

  return (
    <DashboardShell>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Expenditure Management</h1>
            <p className="text-muted-foreground">
              Manage organizational spending, track budgets, and process vendor payments
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-1" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
            <Button onClick={handleCreateExpenditure}>
              <Plus className="h-4 w-4 mr-1" />
              New Expenditure
            </Button>
          </div>
        </div>

        {/* Quick Stats Overview */}
        {!isLoadingStatistics && statistics && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenditures</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(statistics.totalAmount)}</div>
                <p className="text-xs text-muted-foreground">
                  {statistics.totalExpenditures} transactions this month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{statistics.pendingApprovals}</div>
                <p className="text-xs text-muted-foreground">
                  Awaiting approval
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Urgent Items</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{statistics.urgentCount}</div>
                <p className="text-xs text-muted-foreground">
                  Require immediate attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Amount</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(statistics.averageAmount)}</div>
                <p className="text-xs text-muted-foreground">
                  Per transaction
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Display */}
        {(statisticsError || expendituresError) && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Loading Data</AlertTitle>
            <AlertDescription>
              {statisticsError?.message || expendituresError?.message || 'Failed to load expenditure data'}
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard" className="gap-1">
              <BarChart3 className="h-4 w-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="expenditures" className="gap-1">
              <FileText className="h-4 w-4" />
              Expenditures
            </TabsTrigger>
            <TabsTrigger value="analytics" className="gap-1">
              <TrendingUp className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="ai-insights" className="gap-1">
              <Building className="h-4 w-4" />
              AI Insights
            </TabsTrigger>
            <TabsTrigger value="settings" className="gap-1">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-4">
            <ExpenditureDashboard
              onCreateExpenditure={handleCreateExpenditure}
              onViewExpenditure={handleViewExpenditure}
              onEditExpenditure={handleEditExpenditure}
            />
          </TabsContent>

          {/* Expenditures Tab */}
          <TabsContent value="expenditures" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Expenditures</CardTitle>
                <CardDescription>
                  View and manage all expenditure requests and transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ExpenditureTable
                  expenditures={expenditures}
                  isLoading={isLoadingExpenditures}
                  onViewExpenditure={handleViewExpenditure}
                  onEditExpenditure={handleEditExpenditure}
                  onDeleteExpenditure={(expenditureId) => {
                    // Handle delete expenditure
                    console.log('Delete expenditure:', expenditureId);
                    // TODO: Implement delete confirmation dialog
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4">
              <ExpenditureMetrics
                statistics={statistics}
                isLoading={isLoadingStatistics}
              />
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Analytics</CardTitle>
                  <CardDescription>
                    Detailed expenditure analysis and insights
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4" />
                    <p>Advanced analytics coming soon...</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="ai-insights" className="space-y-4">
            <AIAnalyticsDashboard
              expenditureData={expenditures}
              onInsightAction={(action, data) => {
                console.log('AI Insight Action:', action, data);
                // Handle AI insight actions (view, resolve, implement, etc.)
              }}
            />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Expenditure Settings</CardTitle>
                <CardDescription>
                  Configure expenditure management preferences and workflows
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4" />
                  <p>Settings configuration coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Create Expenditure Dialog */}
        <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Expenditure</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new expenditure request
              </DialogDescription>
            </DialogHeader>
            <ExpenditureForm
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Expenditure Dialog */}
        <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Expenditure</DialogTitle>
              <DialogDescription>
                Update the expenditure details
              </DialogDescription>
            </DialogHeader>
            <ExpenditureForm
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              isEditing={true}
              initialData={selectedExpenditureId ? { id: selectedExpenditureId } : undefined}
            />
          </DialogContent>
        </Dialog>

        {/* View Expenditure Dialog */}
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Expenditure Details</DialogTitle>
              <DialogDescription>
                View expenditure information and status
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="text-center py-12 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4" />
                <p>Expenditure details view coming soon...</p>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

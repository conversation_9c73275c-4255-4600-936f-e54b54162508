// app/(dashboard)/dashboard/accounting/expenditure/page.tsx
"use client"

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ExpenditureRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the correct expenditures page (plural)
    router.replace('/dashboard/accounting/expenditures');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecting to expenditures...</p>
      </div>
    </div>
  );
}

import { DashboardShell } from '@/components/dashboard-shell';
import { ExpenseCategoriesChart } from '@/components/accounting/expenditure/expense-categories-chart';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata = {
  title: 'Expense Categories',
  description: 'Analyze and manage expense categories for the Teachers Council of Malawi.',
};

export default function ExpenseCategoriesRoute() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Expense Categories</h1>
            <p className="text-muted-foreground">Analyze and manage expense categories for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/expenditures">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Expenditures</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Categories Content */}
        <ExpenseCategoriesChart 
          title="Expense Categories Analysis" 
          description="Detailed breakdown and analysis of expenses by category."
        />
      </div>
    </DashboardShell>
  );
}

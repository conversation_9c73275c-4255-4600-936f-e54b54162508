import { DashboardShell } from '@/components/dashboard-shell';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

export const metadata = {
  title: 'Expense Approvals',
  description: 'Manage expense approval workflows for the Teachers Council of Malawi.',
};

// Sample data for pending approvals
const pendingApprovals = [
  {
    id: "EXP-2025-0003",
    date: new Date("2025-01-25"),
    category: "Professional Services",
    amount: 500000,
    reference: "CONS-2025-001",
    description: "Consulting services for IT infrastructure",
    submittedBy: "<PERSON>",
    submittedOn: new Date("2025-01-24"),
  },
  {
    id: "EXP-2025-0008",
    date: new Date("2025-02-15"),
    category: "Equipment",
    amount: 1800000,
    reference: "EQP-2025-001",
    description: "New computers for data processing department",
    submittedBy: "Mary Phiri",
    submittedOn: new Date("2025-02-14"),
  },
  {
    id: "EXP-2025-0011",
    date: new Date("2025-03-05"),
    category: "Travel and Transportation",
    amount: 350000,
    reference: "TRV-2025-002",
    description: "Travel expenses for regional teacher certification workshops",
    submittedBy: "James Mwanza",
    submittedOn: new Date("2025-03-04"),
  },
];

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

export default function ExpenseApprovalsRoute() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Expense Approvals</h1>
            <p className="text-muted-foreground">Manage expense approval workflows for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/expenditures">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Expenditure</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Approval Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
              <Clock className="h-4 w-4 text-amber-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                Expenses awaiting your approval
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                Expenses approved in the last 24 hours
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rejected Today</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1</div>
              <p className="text-xs text-muted-foreground">
                Expenses rejected in the last 24 hours
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Value</CardTitle>
              <AlertCircle className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                Expenses over MWK 1,000,000
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pending Approvals */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Approvals</CardTitle>
            <CardDescription>
              Expenses that require your approval
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Submitted By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingApprovals.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell>{format(expense.date, "PPP")}</TableCell>
                      <TableCell>{expense.reference}</TableCell>
                      <TableCell>{expense.category}</TableCell>
                      <TableCell className="max-w-[200px] truncate" title={expense.description}>
                        {expense.description}
                      </TableCell>
                      <TableCell>{formatCurrency(expense.amount)}</TableCell>
                      <TableCell>
                        <div>
                          <div>{expense.submittedBy}</div>
                          <div className="text-xs text-muted-foreground">
                            {format(expense.submittedOn, "PPP")}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" className="h-8 gap-1">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Approve</span>
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 gap-1">
                            <XCircle className="h-4 w-4 text-red-500" />
                            <span>Reject</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Recent Approval History */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Approval History</CardTitle>
            <CardDescription>
              Recent expense approval actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Approved/Rejected By</TableHead>
                    <TableHead>Comments</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>{format(new Date("2025-03-04"), "PPP")}</TableCell>
                    <TableCell>TRV-2025-001</TableCell>
                    <TableCell>{formatCurrency(250000)}</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500">Approved</Badge>
                    </TableCell>
                    <TableCell>Sarah Gondwe</TableCell>
                    <TableCell>Approved as per travel policy</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>{format(new Date("2025-03-03"), "PPP")}</TableCell>
                    <TableCell>SUP-2025-005</TableCell>
                    <TableCell>{formatCurrency(75000)}</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500">Approved</Badge>
                    </TableCell>
                    <TableCell>Sarah Gondwe</TableCell>
                    <TableCell>Within budget allocation</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>{format(new Date("2025-03-03"), "PPP")}</TableCell>
                    <TableCell>MKT-2025-002</TableCell>
                    <TableCell>{formatCurrency(450000)}</TableCell>
                    <TableCell>
                      <Badge variant="destructive">Rejected</Badge>
                    </TableCell>
                    <TableCell>Sarah Gondwe</TableCell>
                    <TableCell>Exceeds marketing budget. Please revise.</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

// app/(dashboard)/dashboard/accounting/synchronization/[id]/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft, RefreshCw, Settings, Clock,
  FileText, Play, Pause, RotateCcw, AlertTriangle
} from 'lucide-react';
import { SyncJobDetails } from '@/components/accounting/synchronization/sync-job-details';
import { SyncJobSettings } from '@/components/accounting/synchronization/sync-job-settings';
import { SyncJobSchedule } from '@/components/accounting/synchronization/sync-job-schedule';
import { SyncJobHistory } from '@/components/accounting/synchronization/sync-job-history';
import { SyncJobLogs } from '@/components/accounting/synchronization/sync-job-logs';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';

// Define the SyncJobData interface for our component
interface SyncJobData {
  id: string;
  name: string;
  source: string;
  destination: string;
  status: 'idle' | 'running' | 'paused' | 'error' | 'completed';
  lastRun?: string;
  nextRun?: string;
  errorMessage?: string;
  // Required properties from SyncJob interface
  dataType: string;
  direction: 'import' | 'export' | 'bidirectional';
  createdAt: string;
  updatedAt: string;
  // Add other properties as needed
  [key: string]: any; // Allow for additional properties
}

/**
 * Synchronization Job Detail Page
 * This page provides a UI for managing a specific synchronization job
 */
export default function SyncJobDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [syncJob, setSyncJob] = useState<SyncJobData | null>(null);
  const [loading, setLoading] = useState(true);
  // activeTab state is used in the onValueChange prop of the Tabs component
  const [activeTab, setActiveTab] = useState('details');
  const [isRunning, setIsRunning] = useState(false);

  const syncJobId = params?.id as string;

  useEffect(() => {
    const fetchSyncJob = async () => {
      try {
        const response = await fetch(`/api/accounting/synchronization/jobs/${syncJobId}`);
        if (!response.ok) throw new Error('Failed to fetch synchronization job');

        const data = await response.json();
        setSyncJob(data);
        setIsRunning(data.status === 'running');
      } catch (error) {
        console.error('Error fetching sync job:', error);
        toast({
          title: 'Error',
          description: `Failed to load synchronization job details: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSyncJob();
  }, [syncJobId, toast]);

  const handleRunJob = async () => {
    try {
      const response = await fetch(`/api/accounting/synchronization/jobs/${syncJobId}/run`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Failed to run job');

      setIsRunning(true);
      toast({
        title: 'Success',
        description: 'Synchronization job started successfully',
      });
    } catch (error) {
      console.error('Error running job:', error);
      toast({
        title: 'Error',
        description: `Failed to start synchronization job: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  const handlePauseJob = async () => {
    try {
      const response = await fetch(`/api/accounting/synchronization/jobs/${syncJobId}/pause`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Failed to pause job');

      setIsRunning(false);
      toast({
        title: 'Success',
        description: 'Synchronization job paused successfully',
      });
    } catch (error) {
      console.error('Error pausing job:', error);
      toast({
        title: 'Error',
        description: `Failed to pause synchronization job: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  const handleResetJob = async () => {
    try {
      const response = await fetch(`/api/accounting/synchronization/jobs/${syncJobId}/reset`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Failed to reset job');

      toast({
        title: 'Success',
        description: 'Synchronization job reset successfully',
      });
    } catch (error) {
      console.error('Error resetting job:', error);
      toast({
        title: 'Error',
        description: `Failed to reset synchronization job: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <Skeleton className="h-12 w-[250px]" />
        <Skeleton className="h-4 w-[350px] mt-2" />
        <Skeleton className="h-[400px] w-full mt-6" />
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading={syncJob?.name || 'Synchronization Job Details'}
        subheading={`Manage synchronization job for ${syncJob?.source || ''} to ${syncJob?.destination || ''}`}
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            {isRunning ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handlePauseJob}
              >
                <Pause className="mr-2 h-4 w-4" />
                Pause Job
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleRunJob}
              >
                <Play className="mr-2 h-4 w-4" />
                Run Now
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetJob}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset Job
            </Button>
          </div>
        }
      />

      <div className="flex items-center space-x-2 mb-4">
        <Badge variant={syncJob?.status === 'running' ? 'default' : syncJob?.status === 'paused' ? 'outline' : syncJob?.status === 'error' ? 'destructive' : 'secondary'}>
          {syncJob?.status === 'running' ? 'Running' :
           syncJob?.status === 'paused' ? 'Paused' :
           syncJob?.status === 'error' ? 'Error' :
           syncJob?.status === 'completed' ? 'Completed' : 'Idle'}
        </Badge>
        {syncJob?.lastRun && (
          <span className="text-sm text-muted-foreground flex items-center">
            <Clock className="mr-1 h-3 w-3" />
            Last run: {new Date(syncJob.lastRun).toLocaleString()}
          </span>
        )}
        {syncJob?.nextRun && (
          <span className="text-sm text-muted-foreground flex items-center ml-4">
            <Clock className="mr-1 h-3 w-3" />
            Next run: {new Date(syncJob.nextRun).toLocaleString()}
          </span>
        )}
        {syncJob?.status === 'error' && (
          <span className="text-sm text-destructive flex items-center ml-4">
            <AlertTriangle className="mr-1 h-3 w-3" />
            {syncJob.errorMessage || 'An error occurred during synchronization'}
          </span>
        )}
      </div>

      <Tabs defaultValue="details" className="mt-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 lg:w-[800px]">
          <TabsTrigger value="details">
            <FileText className="mr-2 h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="schedule">
            <Clock className="mr-2 h-4 w-4" />
            Schedule
          </TabsTrigger>
          <TabsTrigger value="history">
            <RefreshCw className="mr-2 h-4 w-4" />
            History
          </TabsTrigger>
          <TabsTrigger value="logs">
            <FileText className="mr-2 h-4 w-4" />
            Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Details</CardTitle>
              <CardDescription>
                View details about this synchronization job
              </CardDescription>
            </CardHeader>
            <CardContent>
              {syncJob && <SyncJobDetails syncJob={syncJob as any} />}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Settings</CardTitle>
              <CardDescription>
                Configure settings for this synchronization job
              </CardDescription>
            </CardHeader>
            <CardContent>
              {syncJob && (
                <SyncJobSettings
                  syncJob={syncJob as any}
                  onUpdate={(updatedJob) => setSyncJob(updatedJob as SyncJobData)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Schedule</CardTitle>
              <CardDescription>
                Configure when this job should run
              </CardDescription>
            </CardHeader>
            <CardContent>
              {syncJob && (
                <SyncJobSchedule
                  syncJob={syncJob as any}
                  onUpdate={(updatedJob) => setSyncJob(updatedJob as SyncJobData)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job History</CardTitle>
              <CardDescription>
                View history of job executions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SyncJobHistory
                syncJobId={syncJobId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Logs</CardTitle>
              <CardDescription>
                View detailed logs for this job
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SyncJobLogs
                syncJobId={syncJobId}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { Button } from '@/components/ui/button';
import { RefreshCw, Plus } from 'lucide-react';
import { SynchronizationJobsList } from '@/components/accounting/synchronization/synchronization-jobs-list';
import { AddSyncJobDialog } from '@/components/accounting/synchronization/add-sync-job-dialog';

/**
 * Synchronization Page
 * This page provides a UI for managing data synchronization jobs
 */
export default function SynchronizationPage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading="Data Synchronization"
        subheading="Manage data synchronization with external systems"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button size="sm" onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Sync Job
            </Button>
          </div>
        }
      />

      <Card>
        <CardHeader>
          <CardTitle>Synchronization Jobs</CardTitle>
          <CardDescription>
            Manage automated data synchronization between TCM Enterprise and external systems
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SynchronizationJobsList />
        </CardContent>
      </Card>

      <AddSyncJobDialog 
        open={isAddDialogOpen} 
        onOpenChange={setIsAddDialogOpen} 
      />
    </div>
  );
}

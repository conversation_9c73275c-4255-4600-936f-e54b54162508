// app/(dashboard)/dashboard/accounting/reports/compliance/page.tsx
"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportList } from '@/components/accounting/reports/financial-report-list';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FinancialReportGenerator } from '@/components/accounting/reports/financial-report-generator';
import { useState } from 'react';

// Define a custom interface for compliance reports
interface ComplianceReport {
  id: string;
  name: string;
  type: string;
  subtype: string;
  period: string;
  startDate: string;
  endDate: string;
  fiscalYear: string;
  status: string;
  format: string;
  fileUrl?: string;
  sections: string[];
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  publishedBy?: string;
}



// Sample compliance reports data
const sampleComplianceReports: ComplianceReport[] = [
  {
    id: "CR-2025-TAX",
    name: "Tax Compliance Report 2024-2025",
    type: "compliance",
    subtype: "tax",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/tax-2024-2025.pdf",
    sections: ["income_statement", "notes", "audit_opinion"],
    createdAt: "2025-08-15",
    createdBy: "Jane Smith",
    publishedAt: "2025-08-20",
    publishedBy: "Robert Johnson"
  },
  {
    id: "CR-2025-AUDIT",
    name: "Audit Compliance Report 2024-2025",
    type: "compliance",
    subtype: "audit",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/audit-2024-2025.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "notes", "audit_opinion"],
    createdAt: "2025-07-25",
    createdBy: "Robert Johnson",
    publishedAt: "2025-08-05",
    publishedBy: "Jane Smith"
  },
  {
    id: "CR-2025-REGULATORY",
    name: "Regulatory Compliance Report 2024-2025",
    type: "compliance",
    subtype: "regulatory",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/regulatory-2024-2025.pdf",
    sections: ["notes", "audit_opinion"],
    createdAt: "2025-09-10",
    createdBy: "Jane Smith",
    publishedAt: "2025-09-15",
    publishedBy: "Robert Johnson"
  },
  {
    id: "CR-2026-TAX-DRAFT",
    name: "Tax Compliance Report 2025-2026 (Draft)",
    type: "compliance",
    subtype: "tax",
    period: "annual",
    startDate: "2025-07-01",
    endDate: "2026-06-30",
    fiscalYear: "2025-2026",
    status: "draft",
    format: "pdf",
    sections: ["income_statement", "notes"],
    createdAt: "2026-07-10",
    createdBy: "John Doe"
  }
];

export default function ComplianceReportsPage() {
  const [reports, setReports] = useState<ComplianceReport[]>(sampleComplianceReports);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);

  // Handle report generation
  const handleReportGenerated = (newReport: unknown) => {
    // Ensure the report is of compliance type
    const typedReport = newReport as Partial<ComplianceReport>;
    const complianceReport: ComplianceReport = {
      ...typedReport as ComplianceReport,
      type: "compliance",
      subtype: (typedReport.subtype as string) || "regulatory"
    };

    setReports([complianceReport, ...reports]);
    setShowGenerateDialog(false);
  };

  // Handle report deletion
  const handleDeleteReport = (reportToDelete: unknown) => {
    const typedReport = reportToDelete as ComplianceReport;
    setReports(reports.filter(report => report.id !== typedReport.id));
  };

  // Handle report publishing
  const handlePublishReport = (reportToPublish: unknown) => {
    const typedReport = reportToPublish as ComplianceReport;
    setReports(reports.map(report => {
      if (report.id === typedReport.id) {
        return {
          ...report,
          status: "published",
          publishedAt: new Date().toISOString(),
          publishedBy: "Current User"
        };
      }
      return report;
    }));
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Compliance Reports</h1>
            <p className="text-muted-foreground">Generate and view compliance reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              className="gap-1"
              onClick={() => setShowGenerateDialog(true)}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Generate Report</span>
            </Button>
          </div>
        </div>

        {/* Reports Content */}
        <FinancialReportList
          reports={reports}
          title="Compliance Reports"
          type="compliance"
          onDeleteReport={handleDeleteReport}
          onPublishReport={handlePublishReport}
        />

        {/* Generate Report Dialog */}
        <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Generate Compliance Report</DialogTitle>
              <DialogDescription>
                Create a new compliance report. Fill in the details below to generate the report.
              </DialogDescription>
            </DialogHeader>
            <FinancialReportGenerator
              onReportGenerated={handleReportGenerated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

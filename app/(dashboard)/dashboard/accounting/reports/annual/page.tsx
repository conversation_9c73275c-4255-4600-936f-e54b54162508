// app/(dashboard)/dashboard/accounting/reports/annual/page.tsx
"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportList } from '@/components/accounting/reports/financial-report-list';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FinancialReportGenerator } from '@/components/accounting/reports/financial-report-generator';
import { useState } from 'react';

// Define a custom interface for annual reports
interface AnnualReport {
  id: string;
  name: string;
  type: string;
  period: string;
  startDate: string;
  endDate: string;
  fiscalYear: string;
  status: string;
  format: string;
  fileUrl?: string;
  sections: string[];
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  publishedBy?: string;
  summary: {
    totalRevenue: number;
    totalExpenses: number;
    netIncome: number;
    budgetVariance: number;
    cashBalance: number;
  };
}



// Sample annual reports data
const sampleAnnualReports: AnnualReport[] = [
  {
    id: "AR-2025",
    name: "Annual Financial Report 2024-2025",
    type: "annual",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/annual-2024-2025.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance", "notes", "audit_opinion"],
    createdAt: "2025-07-15",
    createdBy: "Jane Smith",
    publishedAt: "2025-07-25",
    publishedBy: "Robert Johnson",
    summary: {
      totalRevenue: 42500000,
      totalExpenses: 36200000,
      netIncome: 6300000,
      budgetVariance: 1200000,
      cashBalance: 6800000
    }
  },
  {
    id: "AR-2024",
    name: "Annual Financial Report 2023-2024",
    type: "annual",
    period: "annual",
    startDate: "2023-07-01",
    endDate: "2024-06-30",
    fiscalYear: "2023-2024",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/annual-2023-2024.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance", "notes", "audit_opinion"],
    createdAt: "2024-07-20",
    createdBy: "John Doe",
    publishedAt: "2024-07-30",
    publishedBy: "Robert Johnson",
    summary: {
      totalRevenue: 38700000,
      totalExpenses: 33500000,
      netIncome: 5200000,
      budgetVariance: -800000,
      cashBalance: 5100000
    }
  },
  {
    id: "AR-2026-DRAFT",
    name: "Annual Financial Report 2025-2026 (Draft)",
    type: "annual",
    period: "annual",
    startDate: "2025-07-01",
    endDate: "2026-06-30",
    fiscalYear: "2025-2026",
    status: "draft",
    format: "pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance", "notes"],
    createdAt: "2026-06-15",
    createdBy: "Jane Smith",
    summary: {
      totalRevenue: 45800000,
      totalExpenses: 38900000,
      netIncome: 6900000,
      budgetVariance: 1500000,
      cashBalance: 8200000
    }
  }
];

export default function AnnualReportsPage() {
  const [reports, setReports] = useState<AnnualReport[]>(sampleAnnualReports);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);

  // Handle report generation
  const handleReportGenerated = (newReport: unknown) => {
    // Ensure the report is of annual type
    const typedReport = newReport as Partial<AnnualReport>;
    const annualReport: AnnualReport = {
      ...typedReport as AnnualReport,
      type: "annual",
      period: "annual"
    };

    setReports([annualReport, ...reports]);
    setShowGenerateDialog(false);
  };

  // Handle report deletion
  const handleDeleteReport = (reportToDelete: unknown) => {
    const typedReport = reportToDelete as AnnualReport;
    setReports(reports.filter(report => report.id !== typedReport.id));
  };

  // Handle report publishing
  const handlePublishReport = (reportToPublish: unknown) => {
    const typedReport = reportToPublish as AnnualReport;
    setReports(reports.map(report => {
      if (report.id === typedReport.id) {
        return {
          ...report,
          status: "published",
          publishedAt: new Date().toISOString(),
          publishedBy: "Current User"
        };
      }
      return report;
    }));
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Annual Financial Reports</h1>
            <p className="text-muted-foreground">Generate and view annual financial reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              className="gap-1"
              onClick={() => setShowGenerateDialog(true)}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Generate Report</span>
            </Button>
          </div>
        </div>

        {/* Reports Content */}
        <FinancialReportList
          reports={reports}
          title="Annual Financial Reports"
          type="annual"
          onDeleteReport={handleDeleteReport}
          onPublishReport={handlePublishReport}
        />

        {/* Generate Report Dialog */}
        <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Generate Annual Report</DialogTitle>
              <DialogDescription>
                Create a new annual financial report. Fill in the details below to generate the report.
              </DialogDescription>
            </DialogHeader>
            <FinancialReportGenerator
              onReportGenerated={handleReportGenerated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

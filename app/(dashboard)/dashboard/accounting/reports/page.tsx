import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportingModule } from '@/components/accounting/reports/financial-reporting';

// Import the ReportTemplate type
import { ReportTemplate } from '@/types/financial-reporting';

// Sample report templates
const sampleTemplates: ReportTemplate[] = [
  {
    id: "template-1",
    name: "Income Statement",
    description: "Standard income statement template",
    type: "income_statement",
    structure: {
      sections: [
        {
          title: "Revenue",
          accounts: ["4000", "4100", "4200", "4900"],
        },
        {
          title: "Expenses",
          accounts: ["5000", "5100", "5200", "5300", "5400", "5900"],
        },
        {
          title: "Net Income",
          formula: "Revenue - Expenses",
        },
      ],
    },
    isDefault: true,
    createdBy: "System",
    createdAt: new Date(2024, 0, 1),
  },
  {
    id: "template-2",
    name: "Balance Sheet",
    description: "Standard balance sheet template",
    type: "balance_sheet",
    structure: {
      sections: [
        {
          title: "Assets",
          accounts: ["1000", "1100", "1200", "1300", "1900"],
        },
        {
          title: "Liabilities",
          accounts: ["2000", "2100", "2200", "2900"],
        },
        {
          title: "Equity",
          accounts: ["3000", "3100", "3900"],
        },
      ],
    },
    isDefault: true,
    createdBy: "System",
    createdAt: new Date(2024, 0, 1),
  },
  {
    id: "template-3",
    name: "Cash Flow Statement",
    description: "Standard cash flow statement template",
    type: "cash_flow",
    structure: {
      sections: [
        {
          title: "Operating Activities",
          accounts: ["4000", "4100", "4200", "5000", "5100", "5200"],
        },
        {
          title: "Investing Activities",
          accounts: ["1200", "1300"],
        },
        {
          title: "Financing Activities",
          accounts: ["2100", "3100"],
        },
        {
          title: "Net Cash Flow",
          formula: "Operating Activities + Investing Activities + Financing Activities",
        },
      ],
    },
    isDefault: true,
    createdBy: "System",
    createdAt: new Date(2024, 0, 1),
  },
  {
    id: "template-4",
    name: "Budget Variance Report",
    description: "Budget vs. actual comparison report",
    type: "budget_variance",
    structure: {
      sections: [
        {
          title: "Revenue",
          accounts: ["4000", "4100", "4200", "4900"],
        },
        {
          title: "Expenses",
          accounts: ["5000", "5100", "5200", "5300", "5400", "5900"],
        },
        {
          title: "Net Income",
          formula: "Revenue - Expenses",
        },
      ],
    },
    isDefault: true,
    createdBy: "System",
    createdAt: new Date(2024, 0, 1),
  },
  {
    id: "template-5",
    name: "Custom Report",
    description: "Custom financial report template",
    type: "custom",
    structure: {
      sections: [
        {
          title: "Custom Section 1",
          accounts: ["1000", "2000", "3000"],
        },
        {
          title: "Custom Section 2",
          accounts: ["4000", "5000"],
        },
        {
          title: "Total",
          formula: "Custom Section 1 + Custom Section 2",
        },
      ],
    },
    isDefault: false,
    createdBy: "System",
    createdAt: new Date(2024, 0, 1),
  },
];

export const metadata = {
  title: 'Financial Reporting',
  description: 'Generate and manage financial reports for the Teachers Council of Malawi.',
};

export default function FinancialReportingPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Financial Reporting</h1>
            <p className="text-muted-foreground">Generate and manage financial reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Financial Reporting Content */}
        <FinancialReportingModule initialTemplates={sampleTemplates} />
      </div>
    </DashboardShell>
  );
}

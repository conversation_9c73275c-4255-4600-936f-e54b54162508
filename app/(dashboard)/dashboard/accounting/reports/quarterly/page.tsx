// app/(dashboard)/dashboard/accounting/reports/quarterly/page.tsx
"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportList } from '@/components/accounting/reports/financial-report-list';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FinancialReportGenerator } from '@/components/accounting/reports/financial-report-generator';
import { useState } from 'react';

// Define a custom interface for quarterly reports
interface QuarterlyReport {
  id: string;
  name: string;
  type: string;
  period: string;
  startDate: string;
  endDate: string;
  fiscalYear: string;
  status: string;
  format: string;
  fileUrl?: string;
  sections: string[];
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  publishedBy?: string;
  summary: {
    totalRevenue: number;
    totalExpenses: number;
    netIncome: number;
    budgetVariance: number;
    cashBalance: number;
  };
}



// Sample quarterly reports data
const sampleQuarterlyReports: QuarterlyReport[] = [
  {
    id: "QR-2025-Q1",
    name: "Q1 Financial Report 2025-2026",
    type: "quarterly",
    period: "quarterly",
    startDate: "2025-07-01",
    endDate: "2025-09-30",
    fiscalYear: "2025-2026",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/q1-2025-2026.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance"],
    createdAt: "2025-10-15",
    createdBy: "John Doe",
    publishedAt: "2025-10-20",
    publishedBy: "Jane Smith",
    summary: {
      totalRevenue: 10500000,
      totalExpenses: 8500000,
      netIncome: 2000000,
      budgetVariance: 500000,
      cashBalance: 7500000
    }
  },
  {
    id: "QR-2025-Q2",
    name: "Q2 Financial Report 2025-2026",
    type: "quarterly",
    period: "quarterly",
    startDate: "2025-10-01",
    endDate: "2025-12-31",
    fiscalYear: "2025-2026",
    status: "draft",
    format: "pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance"],
    createdAt: "2026-01-10",
    createdBy: "John Doe",
    summary: {
      totalRevenue: 11200000,
      totalExpenses: 9100000,
      netIncome: 2100000,
      budgetVariance: 300000,
      cashBalance: 9600000
    }
  },
  {
    id: "QR-2024-Q4",
    name: "Q4 Financial Report 2024-2025",
    type: "quarterly",
    period: "quarterly",
    startDate: "2025-04-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/q4-2024-2025.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance"],
    createdAt: "2025-07-15",
    createdBy: "Jane Smith",
    publishedAt: "2025-07-25",
    publishedBy: "Robert Johnson",
    summary: {
      totalRevenue: 12500000,
      totalExpenses: 10200000,
      netIncome: 2300000,
      budgetVariance: -200000,
      cashBalance: 6800000
    }
  },
  {
    id: "QR-2024-Q3",
    name: "Q3 Financial Report 2024-2025",
    type: "quarterly",
    period: "quarterly",
    startDate: "2025-01-01",
    endDate: "2025-03-31",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/q3-2024-2025.pdf",
    sections: ["income_statement", "balance_sheet", "cash_flow", "budget_variance"],
    createdAt: "2025-04-12",
    createdBy: "Jane Smith",
    publishedAt: "2025-04-20",
    publishedBy: "Robert Johnson",
    summary: {
      totalRevenue: 9800000,
      totalExpenses: 8200000,
      netIncome: 1600000,
      budgetVariance: 100000,
      cashBalance: 5500000
    }
  }
];

export default function QuarterlyReportsPage() {
  const [reports, setReports] = useState<QuarterlyReport[]>(sampleQuarterlyReports);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);

  // Handle report generation
  const handleReportGenerated = (newReport: unknown) => {
    // Ensure the report is of quarterly type
    const typedReport = newReport as Partial<QuarterlyReport>;
    const quarterlyReport: QuarterlyReport = {
      ...typedReport as QuarterlyReport,
      type: "quarterly",
      period: "quarterly"
    };

    setReports([quarterlyReport, ...reports]);
    setShowGenerateDialog(false);
  };

  // Handle report deletion
  const handleDeleteReport = (reportToDelete: unknown) => {
    const typedReport = reportToDelete as QuarterlyReport;
    setReports(reports.filter(report => report.id !== typedReport.id));
  };

  // Handle report publishing
  const handlePublishReport = (reportToPublish: unknown) => {
    const typedReport = reportToPublish as QuarterlyReport;
    setReports(reports.map(report => {
      if (report.id === typedReport.id) {
        return {
          ...report,
          status: "published",
          publishedAt: new Date().toISOString(),
          publishedBy: "Current User"
        };
      }
      return report;
    }));
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quarterly Financial Reports</h1>
            <p className="text-muted-foreground">Generate and view quarterly financial reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              className="gap-1"
              onClick={() => setShowGenerateDialog(true)}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Generate Report</span>
            </Button>
          </div>
        </div>

        {/* Reports Content */}
        <FinancialReportList
          reports={reports}
          title="Quarterly Financial Reports"
          type="quarterly"
          onDeleteReport={handleDeleteReport}
          onPublishReport={handlePublishReport}
        />

        {/* Generate Report Dialog */}
        <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Generate Quarterly Report</DialogTitle>
              <DialogDescription>
                Create a new quarterly financial report. Fill in the details below to generate the report.
              </DialogDescription>
            </DialogHeader>
            <FinancialReportGenerator
              onReportGenerated={handleReportGenerated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

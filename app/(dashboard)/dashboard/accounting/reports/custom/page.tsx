// app/(dashboard)/dashboard/accounting/reports/custom/page.tsx
"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportList } from '@/components/accounting/reports/financial-report-list';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FinancialReportGenerator } from '@/components/accounting/reports/financial-report-generator';
import { useState } from 'react';

// Define a custom interface for custom reports
interface CustomReport {
  id: string;
  name: string;
  type: string;
  period: string;
  startDate: string;
  endDate: string;
  fiscalYear: string;
  status: string;
  format: string;
  fileUrl?: string;
  sections: string[];
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  publishedBy?: string;
  summary: {
    totalRevenue?: number;
    totalExpenses?: number;
    budgetVariance?: number;
    netIncome?: number;
    cashBalance?: number;
  };
}



// Sample custom reports data
const sampleCustomReports: CustomReport[] = [
  {
    id: "CUS-2025-EXP",
    name: "Expenditure Analysis Q1-Q2 2025-2026",
    type: "custom",
    period: "custom",
    startDate: "2025-07-01",
    endDate: "2025-12-31",
    fiscalYear: "2025-2026",
    status: "draft",
    format: "excel",
    sections: ["budget_variance"],
    createdAt: "2025-12-20",
    createdBy: "John Doe",
    summary: {
      totalExpenses: 17600000,
      budgetVariance: 800000
    }
  },
  {
    id: "CUS-2025-CERT",
    name: "Certification Revenue Analysis 2024-2025",
    type: "custom",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/cert-revenue-2024-2025.pdf",
    sections: ["income_statement"],
    createdAt: "2025-08-05",
    createdBy: "Jane Smith",
    publishedAt: "2025-08-10",
    publishedBy: "Robert Johnson",
    summary: {
      totalRevenue: 15200000
    }
  },
  {
    id: "CUS-2025-DEPT",
    name: "Departmental Budget Performance 2024-2025",
    type: "custom",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "excel",
    fileUrl: "/reports/dept-budget-2024-2025.xlsx",
    sections: ["budget_variance"],
    createdAt: "2025-07-25",
    createdBy: "John Doe",
    publishedAt: "2025-07-30",
    publishedBy: "Jane Smith",
    summary: {
      totalExpenses: 36200000,
      budgetVariance: 1200000
    }
  }
];

export default function CustomReportsPage() {
  const [reports, setReports] = useState<CustomReport[]>(sampleCustomReports);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);

  // Handle report generation
  const handleReportGenerated = (newReport: unknown) => {
    // Ensure the report is of custom type
    const typedReport = newReport as Partial<CustomReport>;
    const customReport: CustomReport = {
      ...typedReport as CustomReport,
      type: "custom"
    };

    setReports([customReport, ...reports]);
    setShowGenerateDialog(false);
  };

  // Handle report deletion
  const handleDeleteReport = (reportToDelete: unknown) => {
    const typedReport = reportToDelete as CustomReport;
    setReports(reports.filter(report => report.id !== typedReport.id));
  };

  // Handle report publishing
  const handlePublishReport = (reportToPublish: unknown) => {
    const typedReport = reportToPublish as CustomReport;
    setReports(reports.map(report => {
      if (report.id === typedReport.id) {
        return {
          ...report,
          status: "published",
          publishedAt: new Date().toISOString(),
          publishedBy: "Current User"
        };
      }
      return report;
    }));
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Custom Reports</h1>
            <p className="text-muted-foreground">Generate and view custom financial reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              className="gap-1"
              onClick={() => setShowGenerateDialog(true)}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Generate Report</span>
            </Button>
          </div>
        </div>

        {/* Reports Content */}
        <FinancialReportList
          reports={reports}
          title="Custom Reports"
          type="custom"
          onDeleteReport={handleDeleteReport}
          onPublishReport={handlePublishReport}
        />

        {/* Generate Report Dialog */}
        <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Generate Custom Report</DialogTitle>
              <DialogDescription>
                Create a new custom financial report. Fill in the details below to generate the report.
              </DialogDescription>
            </DialogHeader>
            <FinancialReportGenerator
              onReportGenerated={handleReportGenerated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

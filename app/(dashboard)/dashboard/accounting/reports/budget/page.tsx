// app/(dashboard)/dashboard/accounting/reports/budget/page.tsx
"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { FinancialReportList } from '@/components/accounting/reports/financial-report-list';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FinancialReportGenerator } from '@/components/accounting/reports/financial-report-generator';
import { useState } from 'react';

// Define a custom interface for budget reports
interface BudgetReport {
  id: string;
  name: string;
  type: string;
  subtype: string;
  period: string;
  startDate: string;
  endDate: string;
  fiscalYear: string;
  status: string;
  format: string;
  fileUrl?: string;
  sections: string[];
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  publishedBy?: string;
  summary: {
    totalExpenses: number;
    budgetVariance: number;
  };
}



// Sample budget reports data
const sampleBudgetReports: BudgetReport[] = [
  {
    id: "BR-2025-Q1",
    name: "Q1 Budget Variance Report 2025-2026",
    type: "custom",
    subtype: "budget",
    period: "quarterly",
    startDate: "2025-07-01",
    endDate: "2025-09-30",
    fiscalYear: "2025-2026",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/budget-q1-2025-2026.pdf",
    sections: ["budget_variance"],
    createdAt: "2025-10-10",
    createdBy: "John Doe",
    publishedAt: "2025-10-15",
    publishedBy: "Jane Smith",
    summary: {
      totalExpenses: 8500000,
      budgetVariance: 500000
    }
  },
  {
    id: "BR-2025-Q2",
    name: "Q2 Budget Variance Report 2025-2026",
    type: "custom",
    subtype: "budget",
    period: "quarterly",
    startDate: "2025-10-01",
    endDate: "2025-12-31",
    fiscalYear: "2025-2026",
    status: "draft",
    format: "pdf",
    sections: ["budget_variance"],
    createdAt: "2026-01-05",
    createdBy: "John Doe",
    summary: {
      totalExpenses: 9100000,
      budgetVariance: 300000
    }
  },
  {
    id: "BR-2025-ANNUAL",
    name: "Annual Budget Variance Report 2024-2025",
    type: "custom",
    subtype: "budget",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "pdf",
    fileUrl: "/reports/budget-annual-2024-2025.pdf",
    sections: ["budget_variance"],
    createdAt: "2025-07-20",
    createdBy: "Jane Smith",
    publishedAt: "2025-07-25",
    publishedBy: "Robert Johnson",
    summary: {
      totalExpenses: 36200000,
      budgetVariance: 1200000
    }
  },
  {
    id: "BR-2025-DEPT",
    name: "Departmental Budget Report 2024-2025",
    type: "custom",
    subtype: "budget",
    period: "annual",
    startDate: "2024-07-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "published",
    format: "excel",
    fileUrl: "/reports/budget-dept-2024-2025.xlsx",
    sections: ["budget_variance"],
    createdAt: "2025-08-05",
    createdBy: "Robert Johnson",
    publishedAt: "2025-08-10",
    publishedBy: "Jane Smith",
    summary: {
      totalExpenses: 36200000,
      budgetVariance: 1200000
    }
  }
];

export default function BudgetReportsPage() {
  const [reports, setReports] = useState<BudgetReport[]>(sampleBudgetReports);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);

  // Handle report generation
  const handleReportGenerated = (newReport: unknown) => {
    // Ensure the report is of budget type
    const typedReport = newReport as Partial<BudgetReport>;
    const budgetReport: BudgetReport = {
      ...typedReport as BudgetReport,
      type: "custom",
      subtype: "budget"
    };

    setReports([budgetReport, ...reports]);
    setShowGenerateDialog(false);
  };

  // Handle report deletion
  const handleDeleteReport = (reportToDelete: unknown) => {
    const typedReport = reportToDelete as BudgetReport;
    setReports(reports.filter(report => report.id !== typedReport.id));
  };

  // Handle report publishing
  const handlePublishReport = (reportToPublish: unknown) => {
    const typedReport = reportToPublish as BudgetReport;
    setReports(reports.map(report => {
      if (report.id === typedReport.id) {
        return {
          ...report,
          status: "published",
          publishedAt: new Date().toISOString(),
          publishedBy: "Current User"
        };
      }
      return report;
    }));
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Budget Reports</h1>
            <p className="text-muted-foreground">Generate and view budget reports for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              className="gap-1"
              onClick={() => setShowGenerateDialog(true)}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Generate Report</span>
            </Button>
          </div>
        </div>

        {/* Reports Content */}
        <FinancialReportList
          reports={reports}
          title="Budget Reports"
          type="custom"
          onDeleteReport={handleDeleteReport}
          onPublishReport={handlePublishReport}
        />

        {/* Generate Report Dialog */}
        <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Generate Budget Report</DialogTitle>
              <DialogDescription>
                Create a new budget report. Fill in the details below to generate the report.
              </DialogDescription>
            </DialogHeader>
            <FinancialReportGenerator
              onReportGenerated={handleReportGenerated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  );
}

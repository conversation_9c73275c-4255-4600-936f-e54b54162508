// app/(dashboard)/dashboard/accounting/security/page.tsx
'use client';

import { useState } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Shield,
  Users,
  Lock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Search,
  Clock,
  RefreshCw,
  UserCog,
  LogIn,
  LogOut,
  Edit,
  Trash,
  MoreHorizontal,
  Filter
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';

// Sample audit log data
const auditLogs = [
  {
    id: '1',
    timestamp: new Date('2024-05-14T09:23:45'),
    user: 'John Banda',
    action: 'login',
    details: 'Successful login from ************',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '2',
    timestamp: new Date('2024-05-14T10:15:22'),
    user: 'Mary Phiri',
    action: 'create',
    details: 'Created new journal entry JE-2024-0125',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '3',
    timestamp: new Date('2024-05-14T11:05:17'),
    user: 'James Mwanza',
    action: 'update',
    details: 'Updated budget allocation for IT Department',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '4',
    timestamp: new Date('2024-05-14T11:30:45'),
    user: 'Sarah Gondwe',
    action: 'login',
    details: 'Failed login attempt - incorrect password',
    ipAddress: '************',
    status: 'failure'
  },
  {
    id: '5',
    timestamp: new Date('2024-05-14T12:10:33'),
    user: 'Sarah Gondwe',
    action: 'login',
    details: 'Successful login after previous failure',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '6',
    timestamp: new Date('2024-05-14T13:45:12'),
    user: 'John Banda',
    action: 'approve',
    details: 'Approved payment voucher PV-2024-0089',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '7',
    timestamp: new Date('2024-05-14T14:22:05'),
    user: 'Peter Mbewe',
    action: 'delete',
    details: 'Attempted to delete journal entry JE-2024-0120 without permission',
    ipAddress: '************',
    status: 'failure'
  },
  {
    id: '8',
    timestamp: new Date('2024-05-14T15:05:38'),
    user: 'Mary Phiri',
    action: 'export',
    details: 'Exported financial report for Q1 2024',
    ipAddress: '************',
    status: 'success'
  },
  {
    id: '9',
    timestamp: new Date('2024-05-14T15:30:22'),
    user: 'System',
    action: 'backup',
    details: 'Automated daily backup completed',
    ipAddress: 'localhost',
    status: 'default'
  },
  {
    id: '10',
    timestamp: new Date('2024-05-14T16:15:47'),
    user: 'James Mwanza',
    action: 'logout',
    details: 'User logged out',
    ipAddress: '************',
    status: 'default'
  }
];

// Sample user access data
const userAccess = [
  {
    id: '1',
    name: 'John Banda',
    email: '<EMAIL>',
    role: 'Finance Manager',
    lastLogin: new Date('2024-05-14T16:30:22'),
    status: 'active',
    twoFactorEnabled: true
  },
  {
    id: '2',
    name: 'Mary Phiri',
    email: '<EMAIL>',
    role: 'Accountant',
    lastLogin: new Date('2024-05-14T15:45:10'),
    status: 'active',
    twoFactorEnabled: true
  },
  {
    id: '3',
    name: 'James Mwanza',
    email: '<EMAIL>',
    role: 'Finance Director',
    lastLogin: new Date('2024-05-14T16:15:47'),
    status: 'active',
    twoFactorEnabled: true
  },
  {
    id: '4',
    name: 'Sarah Gondwe',
    email: '<EMAIL>',
    role: 'Accountant',
    lastLogin: new Date('2024-05-14T12:10:33'),
    status: 'active',
    twoFactorEnabled: false
  },
  {
    id: '5',
    name: 'Peter Mbewe',
    email: '<EMAIL>',
    role: 'Accounting Clerk',
    lastLogin: new Date('2024-05-14T14:22:05'),
    status: 'active',
    twoFactorEnabled: false
  },
  {
    id: '6',
    name: 'Grace Nyirenda',
    email: '<EMAIL>',
    role: 'Payroll Specialist',
    lastLogin: new Date('2024-05-13T17:05:22'),
    status: 'active',
    twoFactorEnabled: true
  },
  {
    id: '7',
    name: 'David Chirwa',
    email: '<EMAIL>',
    role: 'Accountant',
    lastLogin: new Date('2024-05-12T09:30:15'),
    status: 'inactive',
    twoFactorEnabled: false
  }
];

// Status badge variants mapping
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'success':
    case 'active':
      return 'success';
    case 'failure':
    case 'locked':
      return 'destructive';
    case 'warning':
      return 'secondary';
    case 'default':
    case 'inactive':
    default:
      return 'outline';
  }
};

export default function SecurityManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Filter audit logs based on search term and filters
  const filteredLogs = auditLogs.filter(log => {
    const matchesSearch =
      log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.ipAddress.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesAction = actionFilter === 'all' || log.action === actionFilter;
    const matchesStatus = statusFilter === 'all' || log.status === statusFilter;

    return matchesSearch && matchesAction && matchesStatus;
  });

  // Get unique actions for filter
  const actions = ['all', ...new Set(auditLogs.map(log => log.action))];

  // Get unique statuses for filter
  const statuses = ['all', ...new Set(auditLogs.map(log => log.status))];

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Security Management"
        text="Manage access control, audit logs, and security settings"
      />

      <Tabs defaultValue="audit-logs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
          <TabsTrigger value="access-control">Access Control</TabsTrigger>
          <TabsTrigger value="security-settings">Security Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="audit-logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Logs</CardTitle>
              <CardDescription>
                Track all user activities and system events
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search logs..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <Select value={actionFilter} onValueChange={setActionFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Action" />
                  </SelectTrigger>
                  <SelectContent>
                    {actions.map(action => (
                      <SelectItem key={action} value={action}>
                        {action === 'all' ? 'All Actions' : action.charAt(0).toUpperCase() + action.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {status === 'all' ? 'All Statuses' : status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="icon">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map(log => (
                    <TableRow key={log.id}>
                      <TableCell>{format(log.timestamp, 'dd MMM yyyy HH:mm:ss')}</TableCell>
                      <TableCell>{log.user}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {log.action.charAt(0).toUpperCase() + log.action.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{log.details}</TableCell>
                      <TableCell>{log.ipAddress}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusVariant(log.status)}>
                          {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access-control" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Access Control</CardTitle>
              <CardDescription>
                Manage user access and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="relative w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search users..."
                    className="pl-8"
                  />
                </div>
                <Button>
                  <UserCog className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>2FA</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {userAccess.map(user => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell>{format(user.lastLogin, 'dd MMM yyyy HH:mm')}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusVariant(user.status)}>
                          {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.twoFactorEnabled ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Lock className="mr-2 h-4 w-4" />
                              Reset Password
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {user.status === 'active' ? (
                              <DropdownMenuItem>
                                <XCircle className="mr-2 h-4 w-4" />
                                Deactivate User
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Activate User
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  );
}

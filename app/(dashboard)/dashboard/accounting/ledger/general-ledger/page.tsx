import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { GeneralLedgerView } from '@/components/accounting/ledger/general-ledger-view';

export const metadata = {
  title: 'General Ledger',
  description: 'View and manage the general ledger for the Teachers Council of Malawi.',
};

export default function GeneralLedgerPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">General Ledger</h1>
            <p className="text-muted-foreground">View and manage the general ledger for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting/ledger">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Ledger</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button size="sm" className="gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>New Entry</span>
            </Button>
          </div>
        </div>

        {/* General Ledger Content */}
        <GeneralLedgerView />
      </div>
    </DashboardShell>
  );
}

import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { ChartOfAccountsManager } from '@/components/accounting/ledger/chart-of-accounts-manager';
import { GeneralLedgerView } from '@/components/accounting/ledger/general-ledger-view';
import { JournalEntryForm } from '@/components/accounting/ledger/journal-entry-form';
import { TrialBalanceGenerator } from '@/components/accounting/ledger/trial-balance-generator';

export const metadata = {
  title: 'Ledger Management',
  description: 'Manage the general ledger and chart of accounts for the Teachers Council of Malawi.',
};

export default function LedgerPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* <PERSON> Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Ledger Management</h1>
            <p className="text-muted-foreground">Manage the general ledger and chart of accounts for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button size="sm" className="gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>New Journal Entry</span>
            </Button>
          </div>
        </div>

        {/* Ledger Content */}
        <Tabs defaultValue="general-ledger" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general-ledger">General Ledger</TabsTrigger>
            <TabsTrigger value="chart-of-accounts">Chart of Accounts</TabsTrigger>
            <TabsTrigger value="journal-entry">Journal Entry</TabsTrigger>
            <TabsTrigger value="trial-balance">Trial Balance</TabsTrigger>
          </TabsList>

          <TabsContent value="general-ledger" className="mt-6">
            <GeneralLedgerView />
          </TabsContent>

          <TabsContent value="chart-of-accounts" className="mt-6">
            <ChartOfAccountsManager />
          </TabsContent>

          <TabsContent value="journal-entry" className="mt-6">
            <JournalEntryForm />
          </TabsContent>

          <TabsContent value="trial-balance" className="mt-6">
            <TrialBalanceGenerator />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { JournalEntryForm } from '@/components/accounting/ledger/journal-entry-form';

export const metadata = {
  title: 'New Journal Entry',
  description: 'Create a new journal entry for the Teachers Council of Malawi.',
};

export default function NewJournalEntryPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">New Journal Entry</h1>
            <p className="text-muted-foreground">Create a new journal entry for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting/ledger/journal-entries">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Journal Entries</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Journal Entry Form */}
        <JournalEntryForm />
      </div>
    </DashboardShell>
  );
}

import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

import { PaymentGatewayManager } from '@/components/accounting/payments/payment-gateway-manager';
import { PaymentTransactionList } from '@/components/accounting/payments/payment-transaction-list';

// Sample payment transactions
const sampleTransactions = Array.from({ length: 20 }, (_, i) => ({
  id: `PT-${i + 1}`,
  gatewayId: `PG-1`,
  externalId: `EXT-${Math.floor(Math.random() * 1000000)}`,
  amount: Math.round(Math.random() * 10000) / 100,
  currency: 'MWK',
  description: `Payment for ${['Membership', 'Registration', 'Certification', 'Renewal'][Math.floor(Math.random() * 4)]}`,
  paymentMethod: ['airtel_money', 'tnm_mpamba', 'visa', 'mastercard'][Math.floor(Math.random() * 4)],
  status: ['pending', 'processing', 'completed', 'failed', 'refunded'][Math.floor(Math.random() * 5)] as any,
  customerInfo: {
    name: `Customer ${i + 1}`,
    email: `customer${i + 1}@example.com`,
    phone: `+265 ${Math.floor(Math.random() * ********)}`,
  },
  createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  updatedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  completedAt: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
}));

export const metadata = {
  title: 'Payment Management',
  description: 'Manage payment gateways and transactions for the Teachers Council of Malawi.',
};

export default function PaymentsPage() {
  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Management</h1>
            <p className="text-muted-foreground">Manage payment gateways and transactions for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Payments Content */}
        <Tabs defaultValue="gateways">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="gateways">Payment Gateways</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
          </TabsList>

          <TabsContent value="gateways" className="mt-6">
            <PaymentGatewayManager />
          </TabsContent>

          <TabsContent value="transactions" className="mt-6">
            <PaymentTransactionList initialTransactions={sampleTransactions} />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

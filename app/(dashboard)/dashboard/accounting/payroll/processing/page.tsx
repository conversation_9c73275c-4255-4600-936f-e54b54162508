// app/(dashboard)/dashboard/accounting/payroll/processing/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Wallet,
  Calendar,
  DollarSign,
  Users,
  Plus,
  Loader2
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { PayrollRunForm } from '@/components/accounting/forms/payroll-run-form';
import { EmptyState } from '@/components/empty-state';
import {
  EmployeePayrollRecordsTable
} from '@/components/accounting/payroll/employee-payroll-records-table';
import { PayrollHistoryTable } from '@/components/accounting/payroll/payroll-history-table';
import { AccountingIntegration } from '@/components/accounting/payroll/accounting-integration';

// Interface for payroll run
interface PayrollRun {
  _id: string;
  name: string;
  payPeriod: {
    month: number;
    year: number;
    startDate: string;
    endDate: string;
  };
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled';
  totalEmployees: number;
  processedEmployees: number;
  totalGrossSalary: number;
  totalDeductions: number;
  totalTax: number;
  totalNetSalary: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  approvedBy?: {
    _id: string;
    name: string;
  };
}

// Interface for payroll record
interface PayrollRecord {
  _id: string;
  employeeId: {
    _id: string;
    firstName: string;
    lastName: string;
    employeeNumber: string;
    departmentId?: {
      _id: string;
      name: string;
    };
    positionId?: {
      _id: string;
      name: string;
    };
  };
  payrollRunId: string;
  grossSalary: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
  status: string;
  components: {
    basic: number;
    allowances: Array<{
      name: string;
      amount: number;
      type: string;
    }>;
    deductions: Array<{
      name: string;
      amount: number;
      type: string;
    }>;
  };
  currency: string;
}

// Status badge colors
const statusColors: Record<string, string> = {
  draft: 'bg-gray-100 text-gray-800',
  processing: 'bg-blue-100 text-blue-800',
  approved: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  cancelled: 'bg-red-100 text-red-800'
};

export default function PayrollProcessingPage() {
  const [isNewPayrollOpen, setIsNewPayrollOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [payrollRuns, setPayrollRuns] = useState<PayrollRun[]>([]);
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([]);
  const [selectedPayrollId, setSelectedPayrollId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch payroll runs
  useEffect(() => {
    const fetchPayrollRuns = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/payroll/runs');
        if (!response.ok) {
          throw new Error('Failed to fetch payroll runs');
        }

        const data = await response.json();
        if (data.success && data.data && data.data.docs) {
          setPayrollRuns(data.data.docs);

          // Set the first payroll run as selected if available
          if (data.data.docs.length > 0 && !selectedPayrollId) {
            setSelectedPayrollId(data.data.docs[0]._id);
          }
        } else {
          setPayrollRuns([]);
        }
      } catch (error: unknown) {
        console.error('Error fetching payroll runs:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        toast({
          title: 'Error',
          description: `Failed to fetch payroll runs: ${errorMessage}`,
          variant: 'destructive',
        });
        setPayrollRuns([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPayrollRuns();
  }, []);

  // Fetch payroll records when selectedPayrollId changes
  useEffect(() => {
    const fetchPayrollRecords = async () => {
      if (!selectedPayrollId) return;

      setIsLoading(true);
      try {
        const response = await fetch(`/api/payroll/runs/${selectedPayrollId}/records`);
        if (!response.ok) {
          throw new Error('Failed to fetch payroll records');
        }

        const data = await response.json();
        if (data.success && data.data && data.data.docs) {
          setPayrollRecords(data.data.docs);
        } else {
          setPayrollRecords([]);
        }
      } catch (error: unknown) {
        console.error('Error fetching payroll records:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        toast({
          title: 'Error',
          description: `Failed to fetch payroll records: ${errorMessage}`,
          variant: 'destructive',
        });
        setPayrollRecords([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPayrollRecords();
  }, [selectedPayrollId]);

  // Get the selected payroll run with null fallback
  const selectedPayroll = payrollRuns.find(run => run._id === selectedPayrollId) || null;

  // Handle creating a new payroll run
  const handleCreatePayrollRun = async (data: unknown) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/payroll/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payroll run');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: 'Payroll run created successfully',
      });

      // Close the dialog
      setIsNewPayrollOpen(false);

      // Refresh the payroll runs
      const updatedResponse = await fetch('/api/payroll/runs');
      const updatedData = await updatedResponse.json();

      if (updatedData.success && updatedData.data && updatedData.data.docs) {
        setPayrollRuns(updatedData.data.docs);

        // Set the newly created payroll run as selected
        if (result.data && result.data._id) {
          setSelectedPayrollId(result.data._id);
        }
      }
    } catch (error: unknown) {
      console.error('Error creating payroll run:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create payroll run',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle processing a payroll run
  const handleProcessPayrollRun = async (payrollRunId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'process'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process payroll run');
      }

      toast({
        title: 'Success',
        description: 'Payroll run is being processed',
      });

      // Refresh the payroll runs
      const updatedResponse = await fetch('/api/payroll/runs');
      const updatedData = await updatedResponse.json();

      if (updatedData.success && updatedData.data && updatedData.data.docs) {
        setPayrollRuns(updatedData.data.docs);
      }

      // Refresh the payroll records
      if (selectedPayrollId) {
        const recordsResponse = await fetch(`/api/payroll/runs/${selectedPayrollId}/records`);
        const recordsData = await recordsResponse.json();

        if (recordsData.success && recordsData.data && recordsData.data.docs) {
          setPayrollRecords(recordsData.data.docs);
        }
      }
    } catch (error: unknown) {
      console.error('Error processing payroll run:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to process payroll run',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle approving a payroll run
  const handleApprovePayrollRun = async (payrollRunId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve payroll run');
      }

      toast({
        title: 'Success',
        description: 'Payroll run approved successfully',
      });

      // Refresh the data
      await refreshPayrollData();
    } catch (error: unknown) {
      console.error('Error approving payroll run:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to approve payroll run',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle marking a payroll run as paid
  const handlePayPayrollRun = async (payrollRunId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'pay'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark payroll run as paid');
      }

      toast({
        title: 'Success',
        description: 'Payroll run marked as paid successfully',
      });

      // Refresh the data
      await refreshPayrollData();
    } catch (error: unknown) {
      console.error('Error marking payroll run as paid:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to mark payroll run as paid',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancelling a payroll run
  const handleCancelPayrollRun = async (payrollRunId: string, reason: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cancel',
          reason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel payroll run');
      }

      toast({
        title: 'Success',
        description: 'Payroll run cancelled successfully',
      });

      // Refresh the data
      await refreshPayrollData();
    } catch (error: unknown) {
      console.error('Error cancelling payroll run:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel payroll run',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to refresh payroll data
  const refreshPayrollData = async () => {
    try {
      // Refresh the payroll runs
      const updatedResponse = await fetch('/api/payroll/runs');
      const updatedData = await updatedResponse.json();

      if (updatedData.success && updatedData.data && updatedData.data.docs) {
        setPayrollRuns(updatedData.data.docs);
      }

      // Refresh the payroll records if a payroll run is selected
      if (selectedPayrollId) {
        const recordsResponse = await fetch(`/api/payroll/runs/${selectedPayrollId}/records`);
        const recordsData = await recordsResponse.json();

        if (recordsData.success && recordsData.data && recordsData.data.docs) {
          setPayrollRecords(recordsData.data.docs);
        }
      }
    } catch (error: unknown) {
      console.error('Error refreshing payroll data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to refresh payroll data: ${errorMessage}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Processing"
        text="Process salaries, manage allowances, and handle deductions"
      >
        <Button onClick={() => setIsNewPayrollOpen(true)} disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Plus className="mr-2 h-4 w-4" />
              New Payroll Run
            </>
          )}
        </Button>
      </DashboardHeader>

      {/* Payroll Run Form */}
      <PayrollRunForm
        isOpen={isNewPayrollOpen}
        onCancel={() => setIsNewPayrollOpen(false)}
        onSubmit={handleCreatePayrollRun}
      />

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payroll</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {selectedPayroll ? (
                  `MWK ${selectedPayroll.totalNetSalary.toLocaleString()}`
                ) : (
                  'MWK 0'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedPayroll ? (
                  `${new Date(selectedPayroll.payPeriod.year, selectedPayroll.payPeriod.month - 1).toLocaleString('default', { month: 'long' })} ${selectedPayroll.payPeriod.year}`
                ) : (
                  'No payroll selected'
                )}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {selectedPayroll ? selectedPayroll.totalEmployees : 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedPayroll && selectedPayroll.processedEmployees > 0 ? (
                  `${selectedPayroll.processedEmployees} processed`
                ) : (
                  `Active employees`
                )}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {selectedPayroll && selectedPayroll.totalEmployees > 0 ? (
                  `MWK ${Math.round(selectedPayroll.totalNetSalary / selectedPayroll.totalEmployees).toLocaleString()}`
                ) : (
                  'MWK 0'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Per employee average
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                {selectedPayroll ? (
                  <Badge variant="outline" className={statusColors[selectedPayroll.status]}>
                    {selectedPayroll.status.charAt(0).toUpperCase() + selectedPayroll.status.slice(1)}
                  </Badge>
                ) : (
                  'No payroll'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedPayroll ? (
                  `Last updated: ${new Date(selectedPayroll.updatedAt).toLocaleDateString()}`
                ) : (
                  'Create a new payroll run'
                )}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="current" className="space-y-4 mt-6">
        <TabsList>
          <TabsTrigger value="current">Current Payroll</TabsTrigger>
          <TabsTrigger value="history">Payroll History</TabsTrigger>
          <TabsTrigger value="accounting">Accounting Integration</TabsTrigger>
        </TabsList>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : payrollRuns.length === 0 ? (
          <EmptyState
            title="No payroll runs found"
            description="There are no payroll runs in the system. Create a new payroll run to get started."
            icon={<Calendar className="h-10 w-10 text-muted-foreground" />}
            action={
              <Button onClick={() => setIsNewPayrollOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                New Payroll Run
              </Button>
            }
          />
        ) : (
          <>
            <TabsContent value="current" className="space-y-4">
              <EmployeePayrollRecordsTable
                payrollRun={selectedPayroll as PayrollRun | undefined}
                payrollRecords={payrollRecords}
                isLoading={isLoading}
                onProcessPayrollRun={handleProcessPayrollRun}
                onApprovePayrollRun={handleApprovePayrollRun}
                onPayPayrollRun={handlePayPayrollRun}
                onCancelPayrollRun={handleCancelPayrollRun}
              />
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <PayrollHistoryTable
                payrollRuns={payrollRuns}
                isLoading={isLoading}
                onSelectPayrollRun={setSelectedPayrollId}
                selectedPayrollId={selectedPayrollId}
                onProcessPayrollRun={handleProcessPayrollRun}
                onApprovePayrollRun={handleApprovePayrollRun}
                onPayPayrollRun={handlePayPayrollRun}
                onCancelPayrollRun={handleCancelPayrollRun}
              />
            </TabsContent>

            <TabsContent value="accounting" className="space-y-4">
              <AccountingIntegration selectedPayroll={selectedPayroll as PayrollRun | undefined} />
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* Payroll cancel dialog is handled by the EmployeePayrollRecordsTable component */}
    </DashboardShell>
  );
}
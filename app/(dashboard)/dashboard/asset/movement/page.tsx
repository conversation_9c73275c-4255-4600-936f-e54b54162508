"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DownloadIcon, FilterIcon, PlusIcon, SearchIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { toast } from "@/components/ui/use-toast"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"

// Define the asset movement type
interface AssetMovement {
  id: string
  movementId: string
  asset: {
    id: string
    name: string
    category: string
  }
  sourceLocation: string
  destinationLocation: string
  movementDate: string
  status: "pending" | "in-transit" | "completed" | "cancelled"
  requestedBy: {
    id: string
    name: string
  }
  createdAt: string
}

export default function AssetMovementPage() {
  const [movements, setMovements] = useState<AssetMovement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const router = useRouter()

  // Define table columns
  const columns: ColumnDef<AssetMovement>[] = [
    {
      accessorKey: "movementId",
      header: "Movement ID",
    },
    {
      accessorKey: "asset.name",
      header: "Asset",
    },
    {
      accessorKey: "sourceLocation",
      header: "Source",
    },
    {
      accessorKey: "destinationLocation",
      header: "Destination",
    },
    {
      accessorKey: "movementDate",
      header: "Date",
      cell: ({ row }) => {
        return format(new Date(row.original.movementDate), "PPP")
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status
        let badgeVariant: "default" | "secondary" | "outline" | "destructive" = "default"

        switch (status) {
          case "pending":
            badgeVariant = "secondary"
            break
          case "in-transit":
            badgeVariant = "default"
            break
          case "completed":
            badgeVariant = "outline"
            break
          case "cancelled":
            badgeVariant = "destructive"
            break
        }

        return <Badge variant={badgeVariant}>{status}</Badge>
      },
    },
    {
      accessorKey: "requestedBy.name",
      header: "Requested By",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const movement = row.original

        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/dashboard/asset/movement/${movement.id}`)}
            >
              View
            </Button>
            {movement.status === "pending" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleApprove(movement.id)}
              >
                Approve
              </Button>
            )}
            {movement.status === "in-transit" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleComplete(movement.id)}
              >
                Complete
              </Button>
            )}
          </div>
        )
      },
    },
  ]

  // Fetch asset movements
  useEffect(() => {
    const fetchMovements = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/asset/movement?status=${activeTab === "all" ? "" : activeTab}`)
        if (!response.ok) {
          throw new Error("Failed to fetch asset movements")
        }
        const data = await response.json()
        setMovements(data.movements)
      } catch (error) {
        console.error("Error fetching asset movements:", error)
        toast({
          title: "Error",
          description: `Failed to fetch asset movements: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchMovements()
  }, [activeTab])

  // Handle approve movement
  const handleApprove = async (id: string) => {
    try {
      const response = await fetch(`/api/asset/movement/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "approve",
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to approve movement")
      }

      toast({
        title: "Success",
        description: "Movement approved successfully",
      })

      // Refresh movements
      const updatedMovements = movements.map((movement) =>
        movement.id === id ? { ...movement, status: "in-transit" as const } : movement
      )
      setMovements(updatedMovements)
    } catch (error) {
      console.error("Error approving movement:", error)
      toast({
        title: "Error",
        description: `Failed to approve movement: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  // Handle complete movement
  const handleComplete = async (id: string) => {
    try {
      const response = await fetch(`/api/asset/movement/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "complete",
          receivedBy: "current-user-id", // This would be the current user's ID
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to complete movement")
      }

      toast({
        title: "Success",
        description: "Movement completed successfully",
      })

      // Refresh movements
      const updatedMovements = movements.map((movement) =>
        movement.id === id ? { ...movement, status: "completed" as const } : movement
      )
      setMovements(updatedMovements)
    } catch (error) {
      console.error("Error completing movement:", error)
      toast({
        title: "Error",
        description: `Failed to complete movement: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Asset Movement"
        text="Track and manage asset movements between locations"
      >
        <Button onClick={() => router.push("/dashboard/asset/movement/new")}>
          <PlusIcon className="mr-2 h-4 w-4" />
          New Movement
        </Button>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 items-center mb-6">
        <div className="relative w-full md:w-64">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search movements..."
            className="w-full pl-8"
          />
        </div>
        <Button variant="outline" size="sm" className="ml-auto">
          <FilterIcon className="mr-2 h-4 w-4" />
          Filters
        </Button>
        <Button variant="outline" size="sm">
          <DownloadIcon className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Movements</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="in-transit">In Transit</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Asset Movements</CardTitle>
              <CardDescription>
                All asset movement requests and their current status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={movements}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Movements</CardTitle>
              <CardDescription>
                Asset movement requests awaiting approval
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={movements.filter(m => m.status === "pending")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="in-transit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>In Transit</CardTitle>
              <CardDescription>
                Assets currently in transit between locations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={movements.filter(m => m.status === "in-transit")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completed Movements</CardTitle>
              <CardDescription>
                Successfully completed asset movements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={movements.filter(m => m.status === "completed")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

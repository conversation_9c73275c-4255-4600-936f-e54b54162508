// app/(dashboard)/dashboard/asset/movement/new/page.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AssetMovementForm } from "@/components/asset/movement/asset-movement-form"
import { toast } from "@/components/ui/use-toast"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Skeleton } from "@/components/ui/skeleton"

interface Asset {
  id: string
  name: string
}

interface AssetData {
  _id: string
  name: string
  assetId: string
  [key: string]: any
}

interface MovementFormValues {
  assetId: string
  sourceLocation: string
  destinationLocation: string
  movementDate: Date
  reason: string
  notes?: string
}

export default function NewAssetMovementPage() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [locations, setLocations] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch assets and locations
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Fetch assets
        const assetsResponse = await fetch('/api/accounting/assets')
        if (!assetsResponse.ok) {
          throw new Error("Failed to fetch assets")
        }
        const assetsData = await assetsResponse.json()

        // Transform assets data
        const formattedAssets = assetsData.assets.map((asset: AssetData) => ({
          id: asset._id,
          name: `${asset.name} (${asset.assetId})`,
        }))

        setAssets(formattedAssets)

        // Fetch locations
        const locationsResponse = await fetch('/api/accounting/assets/metadata')
        if (!locationsResponse.ok) {
          throw new Error("Failed to fetch locations")
        }
        const locationsData = await locationsResponse.json()

        setLocations(locationsData.locations || [])
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: `Failed to fetch required data: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle form submission
  const handleSubmit = async (values: MovementFormValues) => {
    try {
      const response = await fetch('/api/asset/movement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      })

      if (!response.ok) {
        throw new Error("Failed to create asset movement")
      }

      return await response.json()
    } catch (error) {
      console.error("Error creating asset movement:", error)
      throw error
    }
  }

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="New Asset Movement"
          text="Create a new asset movement request"
        />
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-10 w-full" />
              <div className="flex justify-end space-x-4">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="New Asset Movement"
        text="Create a new asset movement request"
      />
      <Card>
        <CardHeader>
          <CardTitle>Asset Movement Request</CardTitle>
          <CardDescription>
            Fill out the form below to request an asset movement between locations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AssetMovementForm
            assets={assets}
            locations={locations}
            onSubmit={handleSubmit}
          />
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

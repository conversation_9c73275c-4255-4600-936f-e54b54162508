// app/(dashboard)/dashboard/asset/maintenance/new/page.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AssetMaintenanceForm } from "@/components/asset/maintenance/asset-maintenance-form"
import { toast } from "@/components/ui/use-toast"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Skeleton } from "@/components/ui/skeleton"

interface Asset {
  id: string
  name: string
}

interface User {
  id: string
  name: string
}

interface AssetData {
  _id: string
  name: string
  assetId: string
  [key: string]: any
}

interface UserData {
  _id: string
  firstName: string
  lastName: string
  [key: string]: any
}

interface MaintenanceFormValues {
  assetId: string
  maintenanceType: "preventive" | "corrective" | "inspection" | "calibration" | "other"
  scheduledDate: Date
  location: string
  description: string
  assignedTo?: string
  provider?: string
  providerContact?: string
  cost?: number
  notes?: string
}

export default function NewAssetMaintenancePage() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [locations, setLocations] = useState<string[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch assets, locations, and users
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Fetch assets
        const assetsResponse = await fetch('/api/accounting/assets')
        if (!assetsResponse.ok) {
          throw new Error("Failed to fetch assets")
        }
        const assetsData = await assetsResponse.json()

        // Transform assets data
        const formattedAssets = assetsData.assets.map((asset: AssetData) => ({
          id: asset._id,
          name: `${asset.name} (${asset.assetId})`,
        }))

        setAssets(formattedAssets)

        // Fetch locations
        const locationsResponse = await fetch('/api/accounting/assets/metadata')
        if (!locationsResponse.ok) {
          throw new Error("Failed to fetch locations")
        }
        const locationsData = await locationsResponse.json()

        setLocations(locationsData.locations || [])

        // Fetch users
        const usersResponse = await fetch('/api/users?role=maintenance')
        if (!usersResponse.ok) {
          // If specific endpoint fails, use a more general one
          const fallbackResponse = await fetch('/api/users')
          if (!fallbackResponse.ok) {
            throw new Error("Failed to fetch users")
          }
          const fallbackData = await fallbackResponse.json()

          // Transform users data
          const formattedUsers = fallbackData.users.map((user: UserData) => ({
            id: user._id,
            name: `${user.firstName} ${user.lastName}`,
          }))

          setUsers(formattedUsers)
        } else {
          const usersData = await usersResponse.json()

          // Transform users data
          const formattedUsers = usersData.users.map((user: UserData) => ({
            id: user._id,
            name: `${user.firstName} ${user.lastName}`,
          }))

          setUsers(formattedUsers)
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: `Failed to fetch required data: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })

        // Set some mock data if API calls fail
        setAssets([
          { id: "asset1", name: "Laptop (LAP-001)" },
          { id: "asset2", name: "Printer (PRN-001)" },
          { id: "asset3", name: "Server (SRV-001)" },
        ])

        setLocations([
          "Main Office",
          "Branch Office",
          "Warehouse",
          "IT Department",
        ])

        setUsers([
          { id: "user1", name: "John Doe" },
          { id: "user2", name: "Jane Smith" },
        ])
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle form submission
  const handleSubmit = async (values: MaintenanceFormValues) => {
    try {
      const response = await fetch('/api/asset/maintenance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      })

      if (!response.ok) {
        throw new Error("Failed to schedule maintenance")
      }

      return await response.json()
    } catch (error) {
      console.error("Error scheduling maintenance:", error)
      throw error
    }
  }

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Schedule Maintenance"
          text="Schedule a new asset maintenance activity"
        />
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-10 w-full" />
              <div className="flex justify-end space-x-4">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Schedule Maintenance"
        text="Schedule a new asset maintenance activity"
      />
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Details</CardTitle>
          <CardDescription>
            Fill out the form below to schedule maintenance for an asset
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AssetMaintenanceForm
            assets={assets}
            locations={locations}
            users={users}
            onSubmit={handleSubmit}
          />
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

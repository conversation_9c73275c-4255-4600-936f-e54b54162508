// app/(dashboard)/dashboard/asset/maintenance/page.tsx
"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { DownloadIcon, FilterIcon, PlusIcon, SearchIcon, WrenchIcon, CheckIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { toast } from "@/components/ui/use-toast"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"

// Define the asset maintenance type
interface AssetMaintenance {
  id: string
  maintenanceId: string
  asset: {
    id: string
    name: string
    category: string
  }
  maintenanceType: "preventive" | "corrective" | "inspection" | "calibration" | "other"
  description: string
  scheduledDate: string
  completionDate?: string
  status: "scheduled" | "in-progress" | "completed" | "cancelled"
  location: string
  assignedTo?: {
    id: string
    name: string
  }
  provider?: string
  cost?: number
  createdAt: string
}

export default function AssetMaintenancePage() {
  const [maintenanceRecords, setMaintenanceRecords] = useState<AssetMaintenance[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const router = useRouter()

  // Define table columns
  const columns: ColumnDef<AssetMaintenance>[] = [
    {
      accessorKey: "maintenanceId",
      header: "ID",
    },
    {
      accessorKey: "asset.name",
      header: "Asset",
    },
    {
      accessorKey: "maintenanceType",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.maintenanceType
        return type.charAt(0).toUpperCase() + type.slice(1)
      },
    },
    {
      accessorKey: "scheduledDate",
      header: "Scheduled Date",
      cell: ({ row }) => {
        return format(new Date(row.original.scheduledDate), "PPP")
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status
        let badgeVariant: "default" | "secondary" | "outline" | "destructive" = "default"

        switch (status) {
          case "scheduled":
            badgeVariant = "secondary"
            break
          case "in-progress":
            badgeVariant = "default"
            break
          case "completed":
            badgeVariant = "outline"
            break
          case "cancelled":
            badgeVariant = "destructive"
            break
        }

        return <Badge variant={badgeVariant}>{status}</Badge>
      },
    },
    {
      accessorKey: "location",
      header: "Location",
    },
    {
      accessorKey: "assignedTo.name",
      header: "Assigned To",
      cell: ({ row }) => {
        return row.original.assignedTo?.name || "Unassigned"
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const maintenance = row.original

        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/dashboard/asset/maintenance/${maintenance.id}`)}
            >
              View
            </Button>
            {maintenance.status === "scheduled" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStart(maintenance.id)}
              >
                <WrenchIcon className="mr-2 h-4 w-4" />
                Start
              </Button>
            )}
            {maintenance.status === "in-progress" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleComplete(maintenance.id)}
              >
                <CheckIcon className="mr-2 h-4 w-4" />
                Complete
              </Button>
            )}
          </div>
        )
      },
    },
  ]

  // Fetch maintenance records
  useEffect(() => {
    const fetchMaintenanceRecords = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/asset/maintenance?status=${activeTab === "all" ? "" : activeTab}`)
        if (!response.ok) {
          throw new Error("Failed to fetch maintenance records")
        }
        const data = await response.json()
        setMaintenanceRecords(data.records)
      } catch (error) {
        console.error("Error fetching maintenance records:", error)
        toast({
          title: "Error",
          description: `Failed to fetch maintenance records: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchMaintenanceRecords()
  }, [activeTab])

  // Handle start maintenance
  const handleStart = async (id: string) => {
    try {
      const response = await fetch(`/api/asset/maintenance/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "start",
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to start maintenance")
      }

      toast({
        title: "Success",
        description: "Maintenance started successfully",
      })

      // Refresh maintenance records
      const updatedRecords = maintenanceRecords.map((record) =>
        record.id === id ? { ...record, status: "in-progress" as const } : record
      )
      setMaintenanceRecords(updatedRecords)
    } catch (error) {
      console.error("Error starting maintenance:", error)
      toast({
        title: "Error",
        description: `Failed to start maintenance: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  // Handle complete maintenance
  const handleComplete = async (id: string) => {
    try {
      const response = await fetch(`/api/asset/maintenance/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "complete",
          completionDate: new Date().toISOString(),
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to complete maintenance")
      }

      toast({
        title: "Success",
        description: "Maintenance completed successfully",
      })

      // Refresh maintenance records
      const updatedRecords = maintenanceRecords.map((record) =>
        record.id === id ? { ...record, status: "completed" as const, completionDate: new Date().toISOString() } : record
      )
      setMaintenanceRecords(updatedRecords)
    } catch (error) {
      console.error("Error completing maintenance:", error)
      toast({
        title: "Error",
        description: `Failed to complete maintenance: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Asset Maintenance"
        text="Schedule and track asset maintenance activities"
      >
        <Button onClick={() => router.push("/dashboard/asset/maintenance/new")}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Schedule Maintenance
        </Button>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 items-center mb-6">
        <div className="relative w-full md:w-64">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search maintenance..."
            className="w-full pl-8"
          />
        </div>
        <Button variant="outline" size="sm" className="ml-auto">
          <FilterIcon className="mr-2 h-4 w-4" />
          Filters
        </Button>
        <Button variant="outline" size="sm">
          <DownloadIcon className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="in-progress">In Progress</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Records</CardTitle>
              <CardDescription>
                All scheduled and completed maintenance activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={maintenanceRecords}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Maintenance</CardTitle>
              <CardDescription>
                Upcoming maintenance activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={maintenanceRecords.filter(m => m.status === "scheduled")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="in-progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>In Progress</CardTitle>
              <CardDescription>
                Maintenance activities currently in progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={maintenanceRecords.filter(m => m.status === "in-progress")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completed Maintenance</CardTitle>
              <CardDescription>
                Successfully completed maintenance activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={maintenanceRecords.filter(m => m.status === "completed")}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

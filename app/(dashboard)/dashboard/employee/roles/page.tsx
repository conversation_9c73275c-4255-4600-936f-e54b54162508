"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>Header } from "@/components/dashboard-header"
import { RolesManager } from "@/components/employee/roles/roles-manager"

export default function RolesPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Role Management" 
        text="Create and manage employee roles for your organization"
      />
      <RolesManager />
    </DashboardShell>
  )
}

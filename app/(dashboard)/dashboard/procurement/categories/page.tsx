'use client';

import { useState, useEffect } from 'react';
import { DashboardHeader } from '@/components/dashboard-header';
import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useProcurementStore } from '@/lib/stores/procurement-store';
import { Plus, Search, Filter, Layers, TreePine, Settings, AlertCircle, CheckCircle, XCircle, Edit, Trash, Upload, Trash2 } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { ProcurementCategory } from '@/lib/stores/procurement-store';
import { toast } from '@/components/ui/use-toast';
import { CategoryForm, type CategoryFormData } from '@/components/procurement/forms/category-form';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { ErrorOverlay } from '@/components/errors/error-overlay';
import { BulkCategoryUpload } from '@/components/procurement/categories/bulk-category-upload';
import { BulkCategoryDelete } from '@/components/procurement/categories/bulk-category-delete';

export default function CategoriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRiskLevel, setSelectedRiskLevel] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProcurementCategory | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState<ProcurementCategory | null>(null);
  const [deletionReason, setDeletionReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  // Bulk operations state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isBulkUploadDialogOpen, setIsBulkUploadDialogOpen] = useState(false);

  // Error handling
  const { error, isErrorOpen, handleApiError, hideError } = useErrorHandler();

  const {
    categories,
    categoryHierarchy,
    isLoadingCategories,
    categoriesError,
    categoryFilters,
    categoriesPagination,
    fetchCategories,
    fetchCategoryHierarchy,
    createCategory,
    updateCategory,
    deleteCategory,
    setCategoryFilters,
    clearCategoriesError
  } = useProcurementStore();

  // Handler functions
  const handleEditCategory = (category: ProcurementCategory) => {
    setEditingCategory(category);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCategory = (category: ProcurementCategory) => {
    setDeletingCategory(category);
    setIsDeleteDialogOpen(true);
    setDeletionReason('');
  };

  // Bulk operations handlers
  const handleSelectCategory = (categoryId: string, selected: boolean) => {
    setSelectedCategories(prev =>
      selected
        ? [...prev, categoryId]
        : prev.filter(id => id !== categoryId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedCategories(selected ? categories.map(cat => cat._id) : []);
  };

  const handleBulkDelete = () => {
    if (selectedCategories.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select categories to delete',
        variant: 'destructive',
      });
      return;
    }
    setIsBulkDeleteDialogOpen(true);
  };

  const handleBulkUpload = () => {
    setIsBulkUploadDialogOpen(true);
  };

  const handleBulkOperationSuccess = () => {
    setSelectedCategories([]);
    fetchCategories();
    fetchCategoryHierarchy();
  };

  const handleConfirmDelete = async () => {
    if (!deletingCategory || !deletionReason.trim()) {
      toast({
        title: 'Error',
        description: 'Please provide a reason for deletion',
        variant: 'destructive',
      });
      return;
    }

    if (deletionReason.trim().length < 10) {
      toast({
        title: 'Error',
        description: 'Deletion reason must be at least 10 characters',
        variant: 'destructive',
      });
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/procurement/categories/${deletingCategory._id}/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deletionReason: deletionReason.trim(),
          context: {
            fiscalYear: new Date().getFullYear().toString(),
            department: 'Procurement'
          }
        }),
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const result = await response.json();

      setIsDeleteDialogOpen(false);
      setDeletingCategory(null);
      setDeletionReason('');

      fetchCategories();
      fetchCategoryHierarchy();

      toast({
        title: 'Success',
        description: `Category deleted successfully. Audit record created: ${result.data.deletionId}`,
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete category. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };





  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
    fetchCategoryHierarchy();
  }, [fetchCategories, fetchCategoryHierarchy]);

  // Handle search and filters
  useEffect(() => {
    const filters = {
      search: searchTerm || undefined,
      riskLevel: selectedRiskLevel || undefined,
      isActive: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined,
    };
    
    setCategoryFilters(filters);
    fetchCategories(1, 20, filters);
  }, [searchTerm, selectedRiskLevel, selectedStatus, setCategoryFilters, fetchCategories]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearCategoriesError();
    };
  }, [clearCategoriesError]);

  const handleCreateCategory = () => {
    setIsCreateDialogOpen(true);
  };

  const handleCategorySubmit = async (data: CategoryFormData) => {
    try {
      await createCategory(data);
      setIsCreateDialogOpen(false);
      fetchCategories();
      fetchCategoryHierarchy();
      toast({
        title: 'Success',
        description: 'Category created successfully',
      });
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: 'Failed to create category',
        variant: 'destructive',
      });
    }
  };

  const handleEditSubmit = async (data: CategoryFormData) => {
    if (!editingCategory) return;

    try {
      await updateCategory(editingCategory._id, data);
      setIsEditDialogOpen(false);
      setEditingCategory(null);
      fetchCategories();
      fetchCategoryHierarchy();
      toast({
        title: 'Success',
        description: 'Category updated successfully',
      });
    } catch (error) {
      console.error('Error updating category:', error);
      toast({
        title: 'Error',
        description: 'Failed to update category',
        variant: 'destructive',
      });
    }
  };

  const handleRefresh = () => {
    fetchCategories();
    fetchCategoryHierarchy();
    toast({
      title: 'Refreshed',
      description: 'Category data has been refreshed',
    });
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Category Management"
        text="Manage procurement categories, hierarchies, and approval workflows."
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <Settings className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleBulkUpload}>
            <Upload className="mr-2 h-4 w-4" />
            Bulk Import
          </Button>
          <Button onClick={handleCreateCategory}>
            <Plus className="mr-2 h-4 w-4" />
            New Category
          </Button>
        </div>
      </DashboardHeader>

      {/* Error Display */}
      {categoriesError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span>{categoriesError}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="list" className="space-y-6">
        <TabsList>
          <TabsTrigger value="list">Category List</TabsTrigger>
          <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search categories..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={selectedRiskLevel} onValueChange={setSelectedRiskLevel}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Risk Level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Risk Levels</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Categories Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="h-5 w-5" />
                Categories ({categories.length})
              </CardTitle>
              <CardDescription>
                Manage procurement categories and their configurations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Bulk Actions Bar */}
              {selectedCategories.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-md mb-4">
                  <div className="text-sm">
                    {selectedCategories.length} {selectedCategories.length === 1 ? 'category' : 'categories'} selected
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="h-8 gap-1"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete Selected
                  </Button>
                </div>
              )}

              {/* Categories Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40px]">
                        <Checkbox
                          checked={categories.length > 0 && selectedCategories.length === categories.length}
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all"
                        />
                      </TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Level</TableHead>
                      <TableHead>Risk Level</TableHead>
                      <TableHead>Approval Limit</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Quotation Required</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingCategories ? (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          Loading categories...
                        </TableCell>
                      </TableRow>
                    ) : categories.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No categories found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      categories.map((category) => (
                        <TableRow key={category._id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedCategories.includes(category._id)}
                              onCheckedChange={(checked) => handleSelectCategory(category._id, !!checked)}
                              aria-label={`Select ${category.name}`}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{category.code}</div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{category.name}</div>
                              <div className="text-sm text-muted-foreground">{category.path}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{category.level}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              category.riskLevel === 'high' || category.riskLevel === 'critical'
                                ? 'destructive'
                                : category.riskLevel === 'medium'
                                ? 'secondary'
                                : 'default'
                            }>
                              {category.riskLevel}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {category.approvalLimit ? `$${category.approvalLimit.toLocaleString()}` : 'No limit'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {category.isActive ? (
                                <>
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                  <span className="text-green-700">Active</span>
                                </>
                              ) : (
                                <>
                                  <XCircle className="h-4 w-4 text-red-500" />
                                  <span className="text-red-700">Inactive</span>
                                </>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {category.requiresQuotation ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-gray-400" />
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditCategory(category)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteCategory(category)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hierarchy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TreePine className="h-5 w-5" />
                Category Hierarchy
              </CardTitle>
              <CardDescription>
                View and manage the hierarchical structure of procurement categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingCategories ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">Loading hierarchy...</div>
                </div>
              ) : categoryHierarchy.length > 0 ? (
                <div className="space-y-2">
                  {/* Hierarchy tree will be implemented here */}
                  <div className="text-muted-foreground">
                    Hierarchy tree component will be implemented here
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No categories found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
                <Layers className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{categories.length}</div>
                <p className="text-xs text-muted-foreground">
                  Active procurement categories
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categories.filter(c => c.isActive).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Risk</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categories.filter(c => c.riskLevel === 'high' || c.riskLevel === 'critical').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Require special attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Quotation Required</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categories.filter(c => c.requiresQuotation).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Need quotations
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create Category Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Category</DialogTitle>
            <DialogDescription>
              Add a new procurement category to the system
            </DialogDescription>
          </DialogHeader>
          <CategoryForm
            onSubmit={handleCategorySubmit}
            isLoading={isLoadingCategories}
            parentCategories={categories.map(cat => ({
              _id: cat._id,
              name: cat.name,
              level: cat.level
            }))}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update category details and settings
            </DialogDescription>
          </DialogHeader>
          <CategoryForm
            initialData={editingCategory ? {
              name: editingCategory.name,
              code: editingCategory.code,
              description: editingCategory.description || '',
              parentCategoryId: editingCategory.parentCategory?._id || 'none',
              level: editingCategory.level,
              budgetCategoryId: editingCategory.budgetCategory?._id || 'none',
              defaultCurrency: 'MWK',
              approvalLimits: [],
              requiresQuotes: editingCategory.requiresQuotation || false,
              minimumQuotes: editingCategory.minimumQuotations || 3,
              requiresTender: false,
              tenderThreshold: 0,
              complianceRequirements: editingCategory.complianceRequirements || [],
              mandatoryDocuments: editingCategory.requiredDocuments || [],
              preferredSuppliers: editingCategory.defaultSuppliers?.map((s: any) => s._id || s) || [],
              restrictedSuppliers: editingCategory.restrictedSuppliers?.map((s: any) => s._id || s) || [],
              requiresSupplierApproval: false,
              isActive: editingCategory.isActive,
              allowSubcategories: true,
              tags: editingCategory.tags || [],
              notes: editingCategory.notes || '',
            } : undefined}
            onSubmit={handleEditSubmit}
            isLoading={isLoadingCategories}
            parentCategories={categories.filter(cat => cat._id !== editingCategory?._id).map(cat => ({
              _id: cat._id,
              name: cat.name,
              level: cat.level
            }))}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Category Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              This action will create an audit trail record. The category will be permanently removed from the system.
            </DialogDescription>
          </DialogHeader>

          {deletingCategory && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700 mb-2">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Category to be deleted:</span>
                </div>
                <div className="text-sm">
                  <p><strong>Name:</strong> {deletingCategory.name}</p>
                  <p><strong>Code:</strong> {deletingCategory.code}</p>
                  <p><strong>Level:</strong> {deletingCategory.level}</p>
                  {deletingCategory.description && (
                    <p><strong>Description:</strong> {deletingCategory.description}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="deletionReason">
                  Deletion Reason <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="deletionReason"
                  placeholder="Please provide a detailed reason for deleting this category (minimum 10 characters)..."
                  value={deletionReason}
                  onChange={(e) => setDeletionReason(e.target.value)}
                  rows={3}
                  className="resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  This reason will be recorded in the audit trail for compliance purposes.
                </p>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setDeletingCategory(null);
                    setDeletionReason('');
                  }}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDelete}
                  disabled={isDeleting || !deletionReason.trim() || deletionReason.trim().length < 10}
                >
                  {isDeleting ? 'Deleting...' : 'Delete Category'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bulk Upload Dialog */}
      <Dialog open={isBulkUploadDialogOpen} onOpenChange={setIsBulkUploadDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Import Categories</DialogTitle>
            <DialogDescription>
              Import multiple categories from a CSV or Excel file
            </DialogDescription>
          </DialogHeader>
          <BulkCategoryUpload
            onSuccess={() => {
              handleBulkOperationSuccess();
              setIsBulkUploadDialogOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <BulkCategoryDelete
        selectedCategories={categories.filter(cat => selectedCategories.includes(cat._id))}
        isOpen={isBulkDeleteDialogOpen}
        onOpenChange={setIsBulkDeleteDialogOpen}
        onSuccess={handleBulkOperationSuccess}
        onCancel={() => setSelectedCategories([])}
      />

      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          onAction={(action, data) => {
            if (action === 'retry') {
              // Retry the last operation
              fetchCategories();
            }
          }}
        />
      )}
    </DashboardShell>
  );
}

// app/(dashboard)/dashboard/procurement/requisitions/[id]/page.tsx
import { Metadata } from "next"
import { RequisitionDetail } from "@/components/procurement/requisition-detail"

export const metadata: Metadata = {
  title: "Requisition Details",
  description: "View detailed information about a purchase requisition",
}

interface RequisitionDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function RequisitionDetailPage({ params }: RequisitionDetailPageProps) {
  const { id } = await params
  
  return <RequisitionDetail requisitionId={id} />
}

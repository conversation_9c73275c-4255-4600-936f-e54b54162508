// app/(dashboard)/dashboard/procurement/requisitions/[id]/edit/page.tsx
import { Metadata } from "next"
import { RequisitionEditForm } from "@/components/procurement/forms/requisition-edit-form"

export const metadata: Metadata = {
  title: "Edit Requisition",
  description: "Edit an existing purchase requisition",
}

interface RequisitionEditPageProps {
  params: Promise<{ id: string }>
}

export default async function RequisitionEditPage({ params }: RequisitionEditPageProps) {
  const { id } = await params
  
  return <RequisitionEditForm requisitionId={id} />
}

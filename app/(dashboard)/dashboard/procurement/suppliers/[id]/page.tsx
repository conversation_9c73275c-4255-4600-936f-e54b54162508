import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { SupplierDetails } from '@/components/procurement/suppliers/supplier-details';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Supplier Details | TCM Enterprise Business Suite',
  description: 'View and manage supplier information, contracts, and documents',
};

export default async function SupplierDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN, 
    UserRole.PROCUREMENT_MANAGER,
    UserRole.PROCUREMENT_OFFICER,
    UserRole.FINANCE_DIRECTOR, 
    UserRole.FINANCE_MANAGER,
    UserRole.DEPARTMENT_HEAD
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Supplier Details" 
        text="View and manage supplier information, contracts, and documents" 
      />
      <SupplierDetails supplierId={resolvedParams.id} />
    </DashboardShell>
  );
}

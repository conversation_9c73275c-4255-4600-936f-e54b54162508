import { Metadata } from 'next';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { SimpleBulkDeleteForm } from '@/components/departments/simple-bulk-delete-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Bulk Delete Departments',
  description: 'Delete multiple department records at once',
};

export default function BulkDeleteDepartmentsPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Bulk Delete Departments" 
        text="Delete multiple department records at once"
      />

      <div className="grid gap-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Warning</AlertTitle>
          <AlertDescription>
            This action permanently deletes department records. This cannot be undone.
            Departments with assigned employees cannot be deleted - you must reassign employees first.
            Make sure you have a backup or export of any data you want to keep.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Bulk Delete Options</CardTitle>
            <CardDescription>
              Delete departments by entering their IDs. Note: Departments with employees cannot be deleted.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleBulkDeleteForm />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Important Notes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Before Deleting Departments:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>Ensure all employees are reassigned to other departments</li>
                <li>Export any important department data for backup</li>
                <li>Verify that no active projects or budgets are tied to these departments</li>
                <li>Check that no roles or permissions are specifically assigned to these departments</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">What Gets Deleted:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>Department record and all associated metadata</li>
                <li>Department budget information</li>
                <li>Department contact information</li>
                <li>Department organizational structure data</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">What Is Protected:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>Employee records (must be reassigned first)</li>
                <li>Historical payroll data</li>
                <li>Audit logs and system records</li>
                <li>Related financial transactions</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

import { DashboardShell } from "@/components/dashboard-shell"
import { DepartmentDetails } from "@/components/departments/department-details"

export const metadata = {
  title: "Department Details | HR Impact Management",
  description: "View and manage department details",
}

export default async function DepartmentPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params

  return (
    <DashboardShell>
      <DepartmentDetails departmentId={resolvedParams.id} />
    </DashboardShell>
  )
}

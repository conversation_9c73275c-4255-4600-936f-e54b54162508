"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON><PERSON>eader } from "@/components/dashboard-header"
import { BarChart3 } from "lucide-react"
import { CalendarUsageStats } from "@/components/calendar/calendar-usage-stats"

export default function CalendarAnalyticsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Calendar Analytics"
        text="Analyze your calendar usage and team scheduling patterns"
      >
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="flex-1 space-y-4">
        <CalendarUsageStats />
      </div>
    </DashboardShell>
  )
}

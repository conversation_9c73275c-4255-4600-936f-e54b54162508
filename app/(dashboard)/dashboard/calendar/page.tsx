// app/(dashboard)/dashboard/calendar/page.tsx
"use client"

import { CalendarView } from "@/components/calendar/calendar-view"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Calendar as CalendarIcon } from "lucide-react"

export default function CalendarPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Calendar Overview"
        text="Manage events, appointments, and schedules"
      >
        <div className="flex items-center">
          <CalendarIcon className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="flex-1 space-y-4">
        <CalendarView />
      </div>
    </DashboardShell>
  )
}

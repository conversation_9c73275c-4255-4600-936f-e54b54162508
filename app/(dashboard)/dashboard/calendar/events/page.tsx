"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>Header } from "@/components/dashboard-header"
import { ListTodo } from "lucide-react"
import { MyEventsView } from "@/components/calendar/my-events-view"

export default function MyEventsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="My Events"
        text="View and manage your calendar events"
      >
        <div className="flex items-center">
          <ListTodo className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="flex-1 space-y-4">
        <MyEventsView />
      </div>
    </DashboardShell>
  )
}

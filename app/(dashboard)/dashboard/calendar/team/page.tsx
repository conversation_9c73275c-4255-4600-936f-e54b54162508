// app/(dashboard)/dashboard/calendar/team/page.tsx
"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Users } from "lucide-react"
import { TeamScheduleView } from "@/components/calendar/team-schedule-view"

export default function TeamSchedulePage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Team Schedule"
        text="View and manage your team's calendar and availability"
      >
        <div className="flex items-center">
          <Users className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="flex-1 space-y-4">
        <TeamScheduleView />
      </div>
    </DashboardShell>
  )
}

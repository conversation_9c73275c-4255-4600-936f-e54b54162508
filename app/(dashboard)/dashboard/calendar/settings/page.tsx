"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>Header } from "@/components/dashboard-header"
import { Settings } from "lucide-react"
import { CalendarSettings } from "@/components/calendar/calendar-settings"

export default function CalendarSettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Calendar Settings"
        text="Configure your calendar preferences and integrations"
      >
        <div className="flex items-center">
          <Settings className="h-6 w-6" />
        </div>
      </DashboardHeader>
      <div className="flex-1 space-y-4">
        <CalendarSettings />
      </div>
    </DashboardShell>
  )
}

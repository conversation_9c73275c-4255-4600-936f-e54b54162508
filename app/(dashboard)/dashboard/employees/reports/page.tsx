import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { EmployeeReportsPage } from '@/components/employees/reports/employee-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Employee Reports | TCM Enterprise Business Suite',
  description: 'Generate and view employee reports and analytics',
};

export default async function EmployeeReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Employee Reports"
        text="Generate and view employee reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <EmployeeReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

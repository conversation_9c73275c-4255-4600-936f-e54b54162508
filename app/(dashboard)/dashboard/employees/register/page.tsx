import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { UserRegistrationPage } from "@/components/employees/user-registration-page";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Register New Employee | TCM Enterprise Business Suite',
  description: 'Add a new employee to the system with any role',
};

export default async function EmployeeRegistrationPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only specific roles can register new employees
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return <UserRegistrationPage />;
}

// app/(dashboard)/dashboard/documents/page.tsx
"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { Plus, ArrowLeft } from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"

import {
  DocumentUploader,
  DocumentList,
  DocumentViewer,
  DocumentCategoryManager
} from "@/components/document-management"

interface DocumentCategory {
  _id: string
  name: string
  code: string
  color?: string
  icon?: string
  expiryRequired: boolean
  allowedFileTypes: string[]
  maxFileSize?: number
}

// Rename to avoid conflict with DOM Document type
interface DocumentItem {
  _id: string
  title: string
  [key: string]: unknown
}

export default function DocumentsPage() {
  const [activeTab, setActiveTab] = useState("my-documents")
  const [categories, setCategories] = useState<DocumentCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [showUploader, setShowUploader] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<DocumentItem | null>(null)

  const fetchCategories = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/documents/categories?isActive=true")

      if (!response.ok) {
        throw new Error("Failed to fetch categories")
      }

      const data = await response.json()
      setCategories(data.categories)
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  const handleDocumentUploaded = () => {
    setShowUploader(false)
    toast.success("Document uploaded successfully")
  }

  const handleViewDocument = (document: DocumentItem) => {
    setSelectedDocument(document)
  }

  const handleBackFromViewer = () => {
    setSelectedDocument(null)
  }

  const handleCategoryChange = () => {
    fetchCategories()
  }

  return (
    <DashboardShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Document Management</h1>
            <p className="text-muted-foreground">
              Manage employee documents and categories
            </p>
          </div>

          {activeTab === "my-documents" && !selectedDocument && !showUploader && (
            <Button onClick={() => setShowUploader(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          )}

          {(selectedDocument || showUploader) && (
            <Button variant="outline" onClick={() => {
              setSelectedDocument(null)
              setShowUploader(false)
            }}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Documents
            </Button>
          )}
        </div>

      {!selectedDocument && !showUploader ? (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="my-documents">My Documents</TabsTrigger>
            <TabsTrigger value="all-documents">All Documents</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          <TabsContent value="my-documents" className="space-y-4">
            <DocumentList
              categories={categories}
              onView={(document: any) => handleViewDocument(document as DocumentItem)}
            />
          </TabsContent>

          <TabsContent value="all-documents" className="space-y-4">
            <DocumentList
              categories={categories}
              onView={(document: any) => handleViewDocument(document as DocumentItem)}
              onVerify={(document: any) => toast.success(`Document "${document.title}" verified`)}
            />
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <DocumentCategoryManager onCategoryChange={handleCategoryChange} />
          </TabsContent>
        </Tabs>
      ) : showUploader ? (
        <DocumentUploader
          categories={categories}
          onSuccess={handleDocumentUploaded}
          onCancel={() => setShowUploader(false)}
        />
      ) : selectedDocument ? (
        <DocumentViewer
          documentId={selectedDocument._id}
          onBack={handleBackFromViewer}
          onEdit={(document: any) => toast.info(`Edit document "${document.title}" (not implemented)`)}
          onVerify={(document: any) => toast.success(`Document "${document.title}" verified`)}
        />
      ) : null}

      <Card>
        <CardHeader>
          <CardTitle>Document Management System</CardTitle>
          <CardDescription>
            Important information about the document management system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">Document Categories</h3>
            <p className="text-sm text-muted-foreground">
              Document categories help organize documents by type. Each category can have specific settings
              like required expiry dates, allowed file types, and maximum file sizes.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Document Verification</h3>
            <p className="text-sm text-muted-foreground">
              Documents can be verified by authorized personnel to confirm their authenticity and validity.
              Verified documents are marked with a verification badge.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Document Expiry</h3>
            <p className="text-sm text-muted-foreground">
              Some documents have expiry dates. The system will automatically mark documents as expired
              when they reach their expiry date. You'll receive notifications for documents nearing expiry.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Document Security</h3>
            <p className="text-sm text-muted-foreground">
              By default, documents are private to the employee they belong to and authorized personnel.
              Documents can be marked as public to make them visible to all employees.
            </p>
          </div>
        </CardContent>
      </Card>
      </div>
    </DashboardShell>
  )
}

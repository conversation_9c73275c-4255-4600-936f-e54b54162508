// app/(dashboard)/dashboard/admin/fix-employee-numbers/page.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { DashboardShell } from '@/components/dashboard-shell'
import { DashboardHeader } from '@/components/dashboard-header'
import { Loader2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

export default function FixEmployeeNumbersPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    totalEmployees: number
    successCount: number
    errorCount: number
    errors: Array<{ employeeId: string, error: string }>
  } | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleFixEmployeeNumbers = async () => {
    try {
      setIsLoading(true)
      setError(null)
      setResult(null)

      const response = await fetch('/api/admin/fix-employee-numbers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fix employee numbers')
      }

      setResult(data)
      toast({
        title: 'Employee numbers fixed',
        description: `Successfully fixed ${data.successCount} out of ${data.totalEmployees} employee records.`,
        variant: 'default'
      })
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage)
      toast({
        title: 'Error',
        description: errorMessage || 'Failed to fix employee numbers',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Fix Employee Numbers"
        text="Update employee records to ensure employeeNumber field is set correctly"
      />

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Fix Employee Numbers</CardTitle>
            <CardDescription>
              This utility will update all employee records that have missing or null employeeNumber values.
              The employeeNumber field will be set to match the employeeId for each employee.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This operation is needed to fix database consistency issues. Only run this if you are experiencing
              problems with employee records or if instructed by a system administrator.
            </p>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {result && (
              <Alert variant={result.errorCount > 0 ? 'destructive' : 'default'} className="mb-4">
                <AlertTitle>Results</AlertTitle>
                <AlertDescription>
                  <p>Total employees processed: {result.totalEmployees}</p>
                  <p>Successfully updated: {result.successCount}</p>
                  <p>Errors: {result.errorCount}</p>

                  {result.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="font-semibold">Error details:</p>
                      <ul className="list-disc pl-5 text-sm">
                        {result.errors.map((err, index) => (
                          <li key={index}>
                            Employee ID {err.employeeId}: {err.error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleFixEmployeeNumbers}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Fixing...
                </>
              ) : (
                'Fix Employee Numbers'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardShell>
  )
}

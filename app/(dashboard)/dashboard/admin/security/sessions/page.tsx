import { Metadata } from "next";
import { SessionsPage } from "@/components/admin/sessions-page";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata: Metadata = {
  title: "Active Sessions",
  description: "Monitor and manage all active user sessions across the system",
};

export default function AdminSessionsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Active Sessions"
        text="Monitor and manage all active user sessions across the system"
      />
      <div className="grid gap-8">
        <SessionsPage />
      </div>
    </DashboardShell>
  );
}

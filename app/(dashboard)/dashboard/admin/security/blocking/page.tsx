import { <PERSON>ada<PERSON> } from "next";
import { BlockingManagement } from "@/components/admin/blocking-management";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata: Metadata = {
  title: "Blocking Management",
  description: "Manage blocked IP addresses, devices, and countries",
};

export default function AdminBlockingPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Blocking Management"
        text="Manage blocked IP addresses, devices, and countries"
      />
      <div className="grid gap-8">
        <BlockingManagement />
      </div>
    </DashboardShell>
  );
}

// app/(dashboard)/dashboard/admin/security/page.tsx
import { Metada<PERSON> } from "next";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { ArrowRight, MonitorSmartphone, Shield, Ban, FileText, AlertTriangle } from "lucide-react";

export const metadata: Metadata = {
  title: "Security Management",
  description: "Manage user security, sessions, and access control",
};

export default function AdminSecurityPage() {
  const securityCategories = [
    {
      title: "Active Sessions",
      description: "Monitor and manage all active user sessions across the system",
      href: "/dashboard/admin/security/sessions",
      icon: MonitorSmartphone,
    },
    {
      title: "User Access Control",
      description: "Manage user security settings and access permissions",
      href: "/dashboard/admin/security/users",
      icon: Shield,
    },
    {
      title: "Blocking Management",
      description: "Manage blocked IP addresses, devices, and countries",
      href: "/dashboard/admin/security/blocking",
      icon: Ban,
    },
    {
      title: "Login Logs",
      description: "Monitor and analyze user login activity across the system",
      href: "/dashboard/admin/security/logs",
      icon: FileText,
    },
    {
      title: "Security Alerts",
      description: "View and manage security alerts and notifications",
      href: "/dashboard/admin/security/alerts",
      icon: AlertTriangle,
    },
  ];

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Security Management"
        text="Monitor and manage user sessions, access control, and security settings"
      />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {securityCategories.map((category) => (
          <Link href={category.href} key={category.href} className="block">
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-medium">{category.title}</CardTitle>
                <category.icon className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">{category.description}</CardDescription>
                <div className="mt-4 flex items-center text-sm text-primary">
                  <span>View details</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </DashboardShell>
  );
}

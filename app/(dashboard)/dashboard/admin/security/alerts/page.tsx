// app/(dashboard)/dashboard/admin/security/alerts/page.tsx
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Clock } from "lucide-react";

export const metadata = {
  title: "Security Alerts",
  description: "View and manage security alerts and notifications",
};

export default function SecurityAlertsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Security Alerts"
        text="View and manage security alerts and notifications"
      />
      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Security Alerts Dashboard</CardTitle>
            <CardDescription>
              This feature is coming soon. It will provide real-time security alerts and notifications.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-8 text-center">
            <AlertTriangle className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Security Alerts Dashboard</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              The Security Alerts Dashboard is currently under development. This feature will allow you to monitor and respond to security threats in real-time.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row">
              <Button variant="outline" className="gap-2">
                <Bell className="h-4 w-4" />
                <span>Notification Settings</span>
              </Button>
              <Button variant="outline" className="gap-2">
                <Clock className="h-4 w-4" />
                <span>View Planned Features</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

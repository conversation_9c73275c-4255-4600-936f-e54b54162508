import { UserSecurityManagement } from "@/components/admin/user-security-management";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata = {
  title: "User Access Control",
  description: "Manage user security settings and access permissions",
};

export default function AdminUserSecurityPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="User Access Control"
        text="Manage user security settings and access permissions"
      />
      <div className="grid gap-8">
        <UserSecurityManagement />
      </div>
    </DashboardShell>
  );
}

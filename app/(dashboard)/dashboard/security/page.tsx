'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/page-header';
import { Button } from '@/components/ui/button';
import { Shield, Users, Globe, Smartphone, RefreshCw } from 'lucide-react';
import { RestrictedUsersList } from '@/components/security/restricted-users-list';
import { BlockedIPList } from '@/components/security/blocked-ip-list';
import { LoginLogsList } from '@/components/security/login-logs-list';
import { DeviceManagement } from '@/components/security/device-management';

/**
 * Security Management Page
 * This page provides a UI for managing security settings
 */
export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState('users');

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <PageHeader
        heading="Security Management"
        subheading="Manage user access, blocked IPs, and device security"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        }
      />

      <Tabs defaultValue="users" className="mt-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            User Access
          </TabsTrigger>
          <TabsTrigger value="ips">
            <Globe className="mr-2 h-4 w-4" />
            IP Blocking
          </TabsTrigger>
          <TabsTrigger value="devices">
            <Smartphone className="mr-2 h-4 w-4" />
            Devices
          </TabsTrigger>
          <TabsTrigger value="logs">
            <Shield className="mr-2 h-4 w-4" />
            Login Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Users className="mr-2 h-5 w-5" />
                User Access Management
              </CardTitle>
              <CardDescription>
                Block, ban, or suspend users who violate security policies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RestrictedUsersList />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ips" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Globe className="mr-2 h-5 w-5" />
                IP Address Blocking
              </CardTitle>
              <CardDescription>
                Block suspicious or malicious IP addresses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BlockedIPList />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Smartphone className="mr-2 h-5 w-5" />
                Device Management
              </CardTitle>
              <CardDescription>
                Manage user devices and block suspicious devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DeviceManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Login Activity Logs
              </CardTitle>
              <CardDescription>
                View login attempts and security events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LoginLogsList />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

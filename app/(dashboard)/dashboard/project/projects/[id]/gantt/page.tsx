"use client"

import { notFound, useParams } from 'next/navigation';
import { GanttChart } from '@/components/project/gantt/GanttChart';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ArrowLeft,
  Calendar,
  Download,
  Filter,
  Plus,
  Settings
} from 'lucide-react';
import Link from 'next/link';

export default function ProjectGanttPage() {
  const params = useParams();
  const id = params?.id as string;
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/project/projects/${id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Project Timeline</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Task
          </Button>
        </div>
      </div>

      <GanttChart
        projectId={id}
        onTaskClick={(taskId) => {
          console.log(`Task clicked: ${taskId}`);
        }}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Critical Path</CardTitle>
            <CardDescription>
              Tasks that directly affect the project completion date
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              The critical path analysis will be displayed here, showing tasks that must be completed on time to ensure the project finishes on schedule.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Timeline Statistics</CardTitle>
            <CardDescription>
              Key metrics about project timeline
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Project Duration</p>
                <p className="text-2xl font-bold">120 days</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Completion</p>
                <p className="text-2xl font-bold">35%</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Tasks on Schedule</p>
                <p className="text-2xl font-bold">24/32</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Delayed Tasks</p>
                <p className="text-2xl font-bold text-red-500">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  Clock,
  DollarSign,
  Edit,
  FileText,
  Flag,
  MoreHorizontal,
  Pencil,
  Plus,
  Share,
  Trash2,
  Users
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { ProjectOverview } from '@/components/project/detail/ProjectOverview';
import { ProjectTasks } from '@/components/project/detail/ProjectTasks';
import { ProjectResources } from '@/components/project/detail/ProjectResources';
import { ProjectBudget } from '@/components/project/detail/ProjectBudget';
import { ProjectDocuments } from '@/components/project/detail/ProjectDocuments';
import { ProjectRisks } from '@/components/project/detail/ProjectRisks';
import { ProjectIssues } from '@/components/project/detail/ProjectIssues';

export const metadata: Metadata = {
  title: 'Project Details | TCM Enterprise Business Suite',
  description: 'View and manage project details',
};

// This would normally come from an API call
const getProject = async (id: string) => {
  // Mock data for demonstration
  return {
    id,
    name: 'Enterprise Resource Planning Implementation',
    code: 'ERP-2023',
    description: 'Implementation of a new ERP system to streamline business processes and improve operational efficiency.',
    status: 'active',
    priority: 'high',
    startDate: '2023-06-01',
    endDate: '2023-12-31',
    progress: 65,
    manager: {
      id: '101',
      name: 'John Smith',
      email: '<EMAIL>',
      avatar: ''
    },
    client: {
      id: '201',
      name: 'Acme Corporation',
      contactName: 'Jane Doe',
      contactEmail: '<EMAIL>'
    },
    budget: {
      planned: 150000,
      actual: 98000,
      currency: 'MWK',
      variance: -52000
    },
    tasks: {
      total: 120,
      completed: 78,
      inProgress: 30,
      notStarted: 12
    },
    team: {
      total: 8,
      members: [
        { id: '301', name: 'Jane Doe', role: 'Software Developer', avatar: '' },
        { id: '302', name: 'Robert Johnson', role: 'UX Designer', avatar: '' },
        { id: '303', name: 'Sarah Williams', role: 'Business Analyst', avatar: '' },
        { id: '304', name: 'Michael Brown', role: 'DevOps Engineer', avatar: '' }
      ]
    },
    risks: {
      total: 5,
      high: 2,
      medium: 2,
      low: 1
    },
    issues: {
      total: 3,
      open: 2,
      resolved: 1
    },
    documents: {
      total: 15
    }
  };
};

// Render status badge
const renderStatusBadge = (status: string) => {
  switch (status) {
    case 'planning':
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Planning</Badge>;
    case 'active':
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
    case 'on_hold':
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">On Hold</Badge>;
    case 'completed':
      return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">Completed</Badge>;
    case 'cancelled':
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Cancelled</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

// Render priority badge
const renderPriorityBadge = (priority: string) => {
  switch (priority) {
    case 'low':
      return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Low</Badge>;
    case 'medium':
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Medium</Badge>;
    case 'high':
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">High</Badge>;
    case 'urgent':
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Urgent</Badge>;
    default:
      return <Badge variant="outline">{priority}</Badge>;
  }
};

export default async function ProjectDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const project = await getProject(resolvedParams.id);

  if (!project) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{project.name}</h2>
          <p className="text-gray-500">{project.code}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive" size="sm">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Description</h3>
                <p className="mt-1">{project.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <div className="mt-1 flex items-center space-x-2">
                    {renderStatusBadge(project.status)}
                    {renderPriorityBadge(project.priority)}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Timeline</h3>
                  <div className="mt-1 flex items-center text-sm">
                    <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                    <span>
                      {new Date(project.startDate).toLocaleDateString()} - {new Date(project.endDate).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Project Manager</h3>
                  <div className="mt-1 flex items-center">
                    <Avatar className="h-6 w-6 mr-2">
                      <AvatarImage src={project.manager.avatar} alt={project.manager.name} />
                      <AvatarFallback>{project.manager.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{project.manager.name}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Client</h3>
                  <div className="mt-1 text-sm">
                    <div>{project.client.name}</div>
                    <div className="text-xs text-gray-500">{project.client.contactName}</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Progress</h3>
                <div className="mt-1 space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>{project.progress}% Complete</span>
                    <span>{project.tasks.completed}/{project.tasks.total} Tasks</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Project Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">Timeline</div>
                    <div className="text-xs text-gray-500">6 months</div>
                  </div>
                </div>
                <div className="text-sm font-medium">
                  {Math.round((new Date().getTime() - new Date(project.startDate).getTime()) /
                  (new Date(project.endDate).getTime() - new Date(project.startDate).getTime()) * 100)}% elapsed
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">Budget</div>
                    <div className="text-xs text-gray-500">{project.budget.currency} {project.budget.planned.toLocaleString()}</div>
                  </div>
                </div>
                <div className="text-sm font-medium">
                  {Math.round((project.budget.actual / project.budget.planned) * 100)}% spent
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-8 w-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">Team</div>
                    <div className="text-xs text-gray-500">{project.team.total} members</div>
                  </div>
                </div>
                <div className="flex -space-x-2">
                  {project.team.members.slice(0, 4).map((member) => (
                    <Avatar key={member.id} className="h-6 w-6 border-2 border-white">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                  ))}
                  {project.team.total > 4 && (
                    <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs text-gray-600">
                      +{project.team.total - 4}
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-8 w-8 bg-amber-100 rounded-full flex items-center justify-center">
                    <Flag className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">Risks & Issues</div>
                    <div className="text-xs text-gray-500">{project.risks.total} risks, {project.issues.total} issues</div>
                  </div>
                </div>
                <div className="text-sm font-medium text-amber-600">
                  {project.risks.high} high risks
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">Documents</div>
                    <div className="text-xs text-gray-500">{project.documents.total} files</div>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
          <TabsTrigger value="budget">Budget</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="risks">Risks</TabsTrigger>
          <TabsTrigger value="issues">Issues</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <ProjectOverview projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="tasks">
          <ProjectTasks projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="resources">
          <ProjectResources projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="budget">
          <ProjectBudget projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="documents">
          <ProjectDocuments projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="risks">
          <ProjectRisks projectId={resolvedParams.id} />
        </TabsContent>

        <TabsContent value="issues">
          <ProjectIssues projectId={resolvedParams.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

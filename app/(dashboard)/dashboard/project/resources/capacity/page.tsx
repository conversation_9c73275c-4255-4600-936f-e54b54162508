import { Metadata } from 'next';
import { ResourceCapacityPlannerWrapper } from '@/components/project/resource/ResourceCapacityPlannerWrapper';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ArrowLeft,
  Calendar,
  Download,
  Filter,
  Plus,
  Settings,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const metadata: Metadata = {
  title: 'Resource Capacity Planning | TCM Enterprise Business Suite',
  description: 'Manage resource allocation and capacity planning',
};

export default function ResourceCapacityPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/project/resources">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Resources
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Resource Capacity Planning</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Resource
          </Button>
        </div>
      </div>

      <Tabs defaultValue="capacity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="capacity">Capacity Planning</TabsTrigger>
          <TabsTrigger value="utilization">Utilization Analysis</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
        </TabsList>

        <TabsContent value="capacity">
          <ResourceCapacityPlannerWrapper />
        </TabsContent>

        <TabsContent value="utilization">
          <Card>
            <CardHeader>
              <CardTitle>Resource Utilization Analysis</CardTitle>
              <CardDescription>
                Analyze resource utilization across projects and departments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                The resource utilization analysis will be displayed here, showing how resources are being utilized across different projects and departments.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills">
          <Card>
            <CardHeader>
              <CardTitle>Skills Matrix</CardTitle>
              <CardDescription>
                View and manage resource skills and competencies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                The skills matrix will be displayed here, showing the skills and competencies of each resource and identifying skill gaps.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Resource Allocation Summary</CardTitle>
            <CardDescription>
              Overview of resource allocation across projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Total Resources</p>
                  <p className="text-2xl font-bold">42</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Average Utilization</p>
                  <p className="text-2xl font-bold">78%</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Overallocated Resources</p>
                  <p className="text-2xl font-bold text-red-500">8</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Underallocated Resources</p>
                  <p className="text-2xl font-bold text-blue-500">12</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Resource Allocation Recommendations</CardTitle>
            <CardDescription>
              Suggestions to optimize resource allocation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border rounded-md p-3 bg-amber-50">
                <p className="font-medium">Overallocation Alert</p>
                <p className="text-sm">8 resources are allocated at more than 100% capacity. Consider redistributing workload.</p>
              </div>
              <div className="border rounded-md p-3 bg-blue-50">
                <p className="font-medium">Skill Gap Alert</p>
                <p className="text-sm">Project "Website Redesign" requires UI/UX skills but has insufficient resources with these skills.</p>
              </div>
              <div className="border rounded-md p-3 bg-green-50">
                <p className="font-medium">Optimization Opportunity</p>
                <p className="text-sm">12 resources are under 50% utilization. Consider assigning additional tasks or projects.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

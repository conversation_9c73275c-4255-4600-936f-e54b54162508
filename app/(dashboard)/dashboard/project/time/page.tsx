"use client"

import { TimeTrackingDashboard } from '@/components/project/time/TimeTrackingDashboard';
import { useRouter } from 'next/navigation';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { useEffect, useState } from 'react';

export default function TimeTrackingPage() {
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is authenticated on the client side
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/session');
        if (!response.ok) {
          router.push('/login');
          return;
        }

        const data = await response.json();
        if (!data || !data.user) {
          router.push('/login');
          return;
        }

        setUserId(data.user.id);
      } catch (error) {
        console.error('Error checking authentication:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Time Tracking"
          text="Track and manage time spent on projects and tasks"
        />
        <div className="flex items-center justify-center h-64">
          <p>Loading...</p>
        </div>
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Time Tracking"
        text="Track and manage time spent on projects and tasks"
      />
      {userId && (
        <TimeTrackingDashboard
          userId={userId}
          onEntryClick={(entryId) => {
            console.log(`Time entry clicked: ${entryId}`);
          }}
        />
      )}
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { ProjectTemplateListWrapper } from '@/components/project/template/ProjectTemplateListWrapper';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ArrowLeft,
  FileText,
  Plus,
  Settings
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Project Templates | TCM Enterprise Business Suite',
  description: 'Create and manage project templates',
};

export default function ProjectTemplatesPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/project/projects">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Project Templates</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <ProjectTemplateListWrapper />
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Benefits</CardTitle>
              <CardDescription>
                Why use project templates?
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="h-3 w-3">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Standardize project structure and processes</span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="h-3 w-3">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Save time on project setup and planning</span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="h-3 w-3">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Ensure consistent project delivery</span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="h-3 w-3">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Capture and reuse best practices</span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="h-3 w-3">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Improve project estimation accuracy</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
              <CardDescription>
                How to use project templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="font-medium">1. Browse Templates</h3>
                  <p className="text-sm text-gray-500">
                    Browse through the available templates to find one that matches your project needs.
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium">2. View Template Details</h3>
                  <p className="text-sm text-gray-500">
                    Click on a template to view its details, including tasks, durations, and dependencies.
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium">3. Use Template</h3>
                  <p className="text-sm text-gray-500">
                    Click "Use Template" to create a new project based on the selected template.
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium">4. Customize Project</h3>
                  <p className="text-sm text-gray-500">
                    Customize the project details, tasks, and timelines to fit your specific needs.
                  </p>
                </div>

                <div className="mt-6">
                  <Button className="w-full">
                    <FileText className="mr-2 h-4 w-4" />
                    View Template Documentation
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

import { Metadata } from 'next';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { ImportForm } from '@/components/project/import/ImportForm';
import { TemplateManager } from '@/components/project/import/TemplateManager';

export const metadata: Metadata = {
  title: 'Import Project Data | TCM Enterprise Business Suite',
  description: 'Import project data from templates',
};

export default function ImportPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Import Project Data</h2>
      </div>

      <div className="max-w-4xl">
        <p className="text-gray-500 mb-6">
          Import project data from CSV templates. Download a template, fill it with your data, and upload it to create or update project information.
        </p>
      </div>

      <Tabs defaultValue="import" className="space-y-4">
        <TabsList>
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="templates">Manage Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <ImportForm />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <TemplateManager />
        </TabsContent>
      </Tabs>
    </div>
  );
}

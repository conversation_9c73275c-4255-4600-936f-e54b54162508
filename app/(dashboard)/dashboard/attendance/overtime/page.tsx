import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AttendanceOvertimePage } from '@/components/attendance/attendance-overtime-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Overtime Management | TCM Enterprise Business Suite',
  description: 'Manage employee overtime records and requests',
};

export default async function OvertimeManagementPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.DEPARTMENT_HEAD
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Overtime Management"
        text="Track and manage employee overtime records and requests"
      />
      <div className="flex-1 space-y-6">
        <AttendanceOvertimePage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

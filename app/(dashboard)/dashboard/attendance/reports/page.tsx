import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AttendanceReportsPage as AttendanceReportsComponent } from '@/components/attendance/attendance-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Attendance Reports | TCM Enterprise Business Suite',
  description: 'Generate and view attendance reports and analytics',
};

export default async function AttendanceReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.DEPARTMENT_HEAD
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Attendance Reports"
        text="Generate and view comprehensive attendance reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <AttendanceReportsComponent userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

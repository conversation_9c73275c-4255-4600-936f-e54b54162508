import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AttendanceShiftsPage } from '@/components/attendance/attendance-shifts-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Shift Management | TCM Enterprise Business Suite',
  description: 'Manage employee shifts and schedules',
};

export default async function ShiftsManagementPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Shift Management"
        text="Create and manage employee shifts and schedules"
      />
      <div className="flex-1 space-y-6">
        <AttendanceShiftsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

// app/(dashboard)/dashboard/attendance/policies/page.tsx
import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AttendancePoliciesPage as AttendancePoliciesComponent } from '@/components/attendance/attendance-policies-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Attendance Policies | TCM Enterprise Business Suite',
  description: 'Configure and manage attendance policies and rules',
};

export default async function AttendancePoliciesPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Attendance Policies"
        text="Configure and manage attendance policies and rules"
      />
      <div className="flex-1 space-y-6">
        <AttendancePoliciesComponent userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

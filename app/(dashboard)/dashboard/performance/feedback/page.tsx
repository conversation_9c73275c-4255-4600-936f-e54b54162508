import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PerformanceFeedbackPage } from '@/components/performance/feedback/performance-feedback-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Feedback | TCM Enterprise Business Suite',
  description: 'Give and receive performance feedback',
};

export default async function PerformanceFeedbackPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Performance Feedback"
        text="Give and receive performance feedback"
      />
      <div className="flex-1 space-y-6">
        <PerformanceFeedbackPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

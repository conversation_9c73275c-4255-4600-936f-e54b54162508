import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { PerformancePage } from "@/components/performance/performance-page";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Management Dashboard | TCM Enterprise Business Suite',
  description: 'Track and manage employee performance and reviews',
};

export default async function PerformanceDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return <PerformancePage userId={session.user.id} />;
}

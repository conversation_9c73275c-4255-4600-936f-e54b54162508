import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PerformanceReportsPage } from '@/components/performance/reports/performance-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Reports | TCM Enterprise Business Suite',
  description: 'Generate and view performance reports and analytics',
};

export default async function PerformanceReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access reports
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Performance Reports"
        text="Generate and view performance reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <PerformanceReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PerformanceGoalsPage } from '@/components/performance/goals/performance-goals-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Goals | TCM Enterprise Business Suite',
  description: 'Set and track employee performance goals and objectives',
};

export default async function PerformanceGoalsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Performance Goals"
        text="Set and track employee performance goals and objectives"
      />
      <div className="flex-1 space-y-6">
        <PerformanceGoalsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

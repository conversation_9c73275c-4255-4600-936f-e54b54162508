import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PerformanceReviewsPage } from '@/components/performance/reviews/performance-reviews-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Reviews | TCM Enterprise Business Suite',
  description: 'Manage employee performance reviews and evaluations',
};

export default async function PerformanceReviewsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Performance Reviews"
        text="Manage employee performance reviews and evaluations"
      />
      <div className="flex-1 space-y-6">
        <PerformanceReviewsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

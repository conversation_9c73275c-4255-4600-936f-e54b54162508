// app/(dashboard)/dashboard/auditors/audit-trail/page.tsx
"use client"

import { useEffect, useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  FileText,
  Search,
  Filter,
  Download,
  Eye,
  Calendar,
  User,
  Shield,
  Clock,
  Trash2,
  Edit,
  Plus,
  AlertTriangle,
  CheckCircle,
  Globe,
  RefreshCw
} from "lucide-react"
import { AuditDateFormatter } from "@/lib/utils/date-formatter"
import { useToast } from "@/hooks/use-toast"

interface AuditEntry {
  id: string;
  timestamp: string;
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  userName: string;
  userRole: string;
  userEmail: string;
  ipAddress: string;
  userAgent: string;
  details: any;
  severity: string;
  module: string;
  sessionId: string;
  isError: boolean;
  errorMessage?: string;
  complianceFlags: string[];
}

interface AuditStats {
  totalEntries: number;
  highSeverityCount: number;
  last24HoursCount: number;
  errorCount: number;
  complianceScore: number;
}

interface FilterOptions {
  actions: Array<{ value: string; label: string }>;
  entityTypes: Array<{ value: string; label: string }>;
  modules: Array<{ value: string; label: string }>;
  users: Array<{ value: string; label: string }>;
}

export default function AuditTrailPage() {
  const { toast } = useToast()

  // State management
  const [auditEntries, setAuditEntries] = useState<AuditEntry[]>([])
  const [auditStats, setAuditStats] = useState<AuditStats | null>(null)
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filter state
  const [searchInput, setSearchInput] = useState('')
  const [actionFilter, setActionFilter] = useState('all')
  const [entityTypeFilter, setEntityTypeFilter] = useState('all')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [moduleFilter, setModuleFilter] = useState('all')
  const [userFilter, setUserFilter] = useState('all')
  const [daysFilter, setDaysFilter] = useState('30')

  // Pagination state
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  // Fetch audit trail data
  useEffect(() => {
    fetchAuditTrail()
  }, [page, searchInput, actionFilter, entityTypeFilter, severityFilter, moduleFilter, userFilter, daysFilter])

  const fetchAuditTrail = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(searchInput && { search: searchInput }),
        ...(actionFilter !== 'all' && { action: actionFilter }),
        ...(entityTypeFilter !== 'all' && { entityType: entityTypeFilter }),
        ...(severityFilter !== 'all' && { severity: severityFilter }),
        ...(moduleFilter !== 'all' && { module: moduleFilter }),
        ...(userFilter !== 'all' && { userId: userFilter }),
        ...(daysFilter !== 'all' && { days: daysFilter })
      })

      const response = await fetch(`/api/audit/trail?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch audit trail')
      }

      const data = await response.json()
      setAuditEntries(data.auditEntries)
      setAuditStats(data.stats)
      setFilterOptions(data.filterOptions)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      console.error('Error fetching audit trail:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch audit trail')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle refresh
  const handleRefresh = () => {
    setPage(1)
    fetchAuditTrail()
  }

  // Handle export
  const handleExport = async () => {
    try {
      toast({
        title: "Export Started",
        description: "Generating audit trail export...",
      })

      // TODO: Implement export functionality
      toast({
        title: "Export Complete",
        description: "Audit trail has been exported successfully.",
      })
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export audit trail.",
        variant: "destructive",
      })
    }
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case "DELETE":
        return <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400" />
      case "CREATE":
        return <Plus className="h-4 w-4 text-green-600 dark:text-green-400" />
      case "UPDATE":
        return <Edit className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      case "LOGIN":
        return <User className="h-4 w-4 text-purple-600 dark:text-purple-400" />
      default:
        return <FileText className="h-4 w-4 text-gray-600 dark:text-gray-400" />
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "HIGH":
      case "CRITICAL":
        return <Badge className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700">High</Badge>
      case "MEDIUM":
        return <Badge className="bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700">Medium</Badge>
      case "LOW":
        return <Badge className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700">Low</Badge>
      default:
        return <Badge variant="secondary">{severity}</Badge>
    }
  }

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Audit Trail"
          text="Loading audit trail data..."
        >
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              <Download className="h-4 w-4 mr-2" />
              Export Trail
            </Button>
            <Button variant="outline" size="sm" disabled>
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filters
            </Button>
          </div>
        </DashboardHeader>

        <div className="space-y-6">
          {/* Loading skeletons */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-96" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-5">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-64" />
              <Skeleton className="h-4 w-80" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardShell>
    )
  }

  if (error) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Audit Trail"
          text="Error loading audit trail data"
        >
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </DashboardHeader>

        <div className="space-y-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Audit Trail"
        text="Complete audit trail of all system activities for government compliance"
      >
        <div className="flex items-center gap-2">
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Trail
          </Button>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Audit Trail Filters</CardTitle>
            <CardDescription>
              Filter audit entries by action, user, date, or entity type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search audit trail..."
                    className="pl-9"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Action</label>
                <Select value={actionFilter} onValueChange={setActionFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    {filterOptions?.actions.map(action => (
                      <SelectItem key={action.value} value={action.value}>
                        {action.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Entity Type</label>
                <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All entities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Entities</SelectItem>
                    {filterOptions?.entityTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Severity</label>
                <Select value={severityFilter} onValueChange={setSeverityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <Select value={daysFilter} onValueChange={setDaysFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Last 30 days" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Last 24 hours</SelectItem>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="all">All time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Audit Trail Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              Audit Trail Entries ({totalCount.toLocaleString()})
            </CardTitle>
            <CardDescription>
              Complete chronological record of all system activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Entity</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="text-sm">{AuditDateFormatter.report(entry.timestamp)}</p>
                          <p className="text-xs text-muted-foreground">
                            ID: {entry.id}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getActionIcon(entry.action)}
                          <span className="font-medium text-sm">{entry.action}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{entry.entityType}</p>
                          <p className="text-xs text-muted-foreground">
                            ID: {entry.entityId}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{entry.userName}</p>
                          <p className="text-xs text-muted-foreground">{entry.userRole}</p>
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Globe className="h-3 w-3" />
                            {entry.ipAddress}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 max-w-xs">
                          {entry.action === "DELETE" && entry.details.reason && (
                            <p className="text-xs text-gray-600">
                              Reason: {entry.details.reason.substring(0, 50)}...
                            </p>
                          )}
                          {entry.complianceFlags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {entry.complianceFlags.slice(0, 2).map((flag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {flag}
                                </Badge>
                              ))}
                              {entry.complianceFlags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{entry.complianceFlags.length - 2}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getSeverityBadge(entry.severity)}
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Audit Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 dark:bg-blue-950 rounded-full">
                  <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">Total Entries</p>
                  <p className="text-2xl font-bold">{auditStats?.totalEntries.toLocaleString() || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-50 dark:bg-red-950 rounded-full">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">High Severity</p>
                  <p className="text-2xl font-bold">{auditStats?.highSeverityCount || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 dark:bg-green-950 rounded-full">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">Compliant</p>
                  <p className="text-2xl font-bold">{auditStats?.complianceScore || '0'}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 dark:bg-purple-950 rounded-full">
                  <Clock className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">Last 24h</p>
                  <p className="text-2xl font-bold">{auditStats?.last24HoursCount || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {((page - 1) * 20) + 1} to {Math.min(page * 20, totalCount)} of {totalCount} entries
            </p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardShell>
  )
}

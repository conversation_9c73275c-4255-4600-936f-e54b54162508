// app/(dashboard)/dashboard/auditors/compliance-reports/page.tsx
import { Metadata } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  Download, 
  FileText,
  Calendar,
  Shield,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Database,
  PieChart,
  Activity
} from "lucide-react"

export const metadata: Metadata = {
  title: "Compliance Reports - Auditors",
  description: "Generate compliance reports for government auditing",
}

export default function ComplianceReportsPage() {
  // Mock data - replace with real data from API
  const reportTemplates = [
    {
      id: "monthly-deletion",
      title: "Monthly Deletion Report",
      description: "Comprehensive report of all deletions in the selected month",
      category: "Deletion Reports",
      frequency: "Monthly",
      lastGenerated: "2025-01-01",
      format: ["PDF", "Excel"],
      compliance: "Government Standard",
      icon: FileText
    },
    {
      id: "quarterly-audit",
      title: "Quarterly Audit Summary",
      description: "Complete audit trail summary for quarterly government review",
      category: "Audit Reports",
      frequency: "Quarterly",
      lastGenerated: "2024-12-31",
      format: ["PDF", "Excel"],
      compliance: "Government Standard",
      icon: BarChart3
    },
    {
      id: "user-activity",
      title: "User Activity Report",
      description: "Detailed user activity and access patterns",
      category: "Security Reports",
      frequency: "Weekly",
      lastGenerated: "2025-01-08",
      format: ["PDF", "Excel", "CSV"],
      compliance: "Internal Audit",
      icon: Users
    },
    {
      id: "retention-compliance",
      title: "Data Retention Compliance",
      description: "7-year retention policy compliance status",
      category: "Compliance Reports",
      frequency: "Annual",
      lastGenerated: "2024-12-31",
      format: ["PDF"],
      compliance: "Government Standard",
      icon: Database
    },
    {
      id: "high-value-transactions",
      title: "High-Value Transaction Report",
      description: "Report of all high-value financial transactions and deletions",
      category: "Financial Reports",
      frequency: "Monthly",
      lastGenerated: "2025-01-01",
      format: ["PDF", "Excel"],
      compliance: "Financial Audit",
      icon: TrendingUp
    },
    {
      id: "security-incidents",
      title: "Security Incident Report",
      description: "Security events, failed logins, and suspicious activities",
      category: "Security Reports",
      frequency: "Weekly",
      lastGenerated: "2025-01-08",
      format: ["PDF", "Excel"],
      compliance: "Security Audit",
      icon: Shield
    }
  ]

  const complianceMetrics = {
    overallScore: 98.5,
    deletionCompliance: 99.2,
    retentionCompliance: 97.8,
    auditTrailCompleteness: 100,
    securityCompliance: 96.5
  }

  const getComplianceColor = (score: number) => {
    if (score >= 95) return "text-green-600"
    if (score >= 85) return "text-yellow-600"
    return "text-red-600"
  }

  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case "Government Standard":
        return <Badge className="bg-green-100 text-green-700">Government Standard</Badge>
      case "Financial Audit":
        return <Badge className="bg-blue-100 text-blue-700">Financial Audit</Badge>
      case "Security Audit":
        return <Badge className="bg-purple-100 text-purple-700">Security Audit</Badge>
      case "Internal Audit":
        return <Badge className="bg-gray-100 text-gray-700">Internal Audit</Badge>
      default:
        return <Badge variant="secondary">{compliance}</Badge>
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Compliance Reports"
        text="Generate comprehensive compliance reports for government auditing and internal review"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Report
          </Button>
          <Button size="sm">
            <Download className="h-4 w-4 mr-2" />
            Quick Export
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Compliance Overview */}
        <div className="grid gap-4 md:grid-cols-5">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-full">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Overall Score</p>
                  <p className={`text-2xl font-bold ${getComplianceColor(complianceMetrics.overallScore)}`}>
                    {complianceMetrics.overallScore}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-full">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Deletion Compliance</p>
                  <p className={`text-2xl font-bold ${getComplianceColor(complianceMetrics.deletionCompliance)}`}>
                    {complianceMetrics.deletionCompliance}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 rounded-full">
                  <Database className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Retention Compliance</p>
                  <p className={`text-2xl font-bold ${getComplianceColor(complianceMetrics.retentionCompliance)}`}>
                    {complianceMetrics.retentionCompliance}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-50 rounded-full">
                  <Activity className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Audit Trail</p>
                  <p className={`text-2xl font-bold ${getComplianceColor(complianceMetrics.auditTrailCompleteness)}`}>
                    {complianceMetrics.auditTrailCompleteness}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-50 rounded-full">
                  <Shield className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Security Compliance</p>
                  <p className={`text-2xl font-bold ${getComplianceColor(complianceMetrics.securityCompliance)}`}>
                    {complianceMetrics.securityCompliance}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Report Generation */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Report Generation</CardTitle>
            <CardDescription>
              Generate standard compliance reports with predefined parameters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Report Type</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly-deletion">Monthly Deletion Report</SelectItem>
                    <SelectItem value="quarterly-audit">Quarterly Audit Summary</SelectItem>
                    <SelectItem value="user-activity">User Activity Report</SelectItem>
                    <SelectItem value="retention-compliance">Data Retention Compliance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Time Period</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="current-month">Current Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                    <SelectItem value="current-quarter">Current Quarter</SelectItem>
                    <SelectItem value="last-quarter">Last Quarter</SelectItem>
                    <SelectItem value="current-year">Current Year</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Format</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF Report</SelectItem>
                    <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                    <SelectItem value="csv">CSV Data</SelectItem>
                    <SelectItem value="all">All Formats</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Action</label>
                <Button className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Templates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Available Report Templates
            </CardTitle>
            <CardDescription>
              Predefined compliance report templates for different audit requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {reportTemplates.map((template) => (
                <Card key={template.id} className="cursor-pointer transition-colors hover:bg-accent">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-50 rounded-full">
                            <template.icon className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-medium text-sm">{template.title}</h3>
                            <p className="text-xs text-muted-foreground">{template.category}</p>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-xs text-gray-600">{template.description}</p>
                      
                      <div className="space-y-2">
                        {getComplianceBadge(template.compliance)}
                        
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">Frequency: {template.frequency}</span>
                          <span className="text-muted-foreground">Last: {template.lastGenerated}</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {template.format.map((format, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {format}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" className="flex-1">
                          <Download className="h-3 w-3 mr-1" />
                          Generate
                        </Button>
                        <Button variant="outline" size="sm">
                          <Calendar className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Government Compliance Standards
            </CardTitle>
            <CardDescription>
              Teachers Council of Malawi compliance requirements and standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium text-sm mb-3">Reporting Requirements</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Monthly deletion reports to government auditors
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Quarterly comprehensive audit summaries
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Annual data retention compliance reports
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Real-time security incident reporting
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-3">Compliance Standards</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    7-year data retention period
                  </li>
                  <li className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-purple-600" />
                    Complete audit trail maintenance
                  </li>
                  <li className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-orange-600" />
                    Mandatory deletion reason documentation
                  </li>
                  <li className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-red-600" />
                    Role-based access control enforcement
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

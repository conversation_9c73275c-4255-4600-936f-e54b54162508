import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AssessmentReportsPage } from '@/components/assessment/assessment-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Assessment Reports | TCM Enterprise Business Suite',
  description: 'View assessment reports and analytics',
};

export default async function ReportsPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.TRAINING_COORDINATOR
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Assessment Reports"
        text="View assessment reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <AssessmentReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

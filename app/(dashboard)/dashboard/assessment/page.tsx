import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AssessmentDashboard } from '@/components/assessment/assessment-dashboard';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Assessment Dashboard | TCM Enterprise Business Suite',
  description: 'Manage employee assessments and evaluations',
};

export default async function AssessmentDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.RECRUITER,
    UserRole.TRAINING_COORDINATOR
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Assessment Dashboard"
        text="Manage employee assessments and evaluations"
      />
      <div className="flex-1 space-y-6">
        <AssessmentDashboard userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AssessmentListPage } from '@/components/assessment/assessment-list-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Assessment List | TCM Enterprise Business Suite',
  description: 'Manage and view all assessments',
};

export default async function AssessmentsPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.RECRUITER,
    UserRole.TRAINING_COORDINATOR
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Assessments"
        text="Manage and view all assessments"
      />
      <div className="flex-1 space-y-6">
        <AssessmentListPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

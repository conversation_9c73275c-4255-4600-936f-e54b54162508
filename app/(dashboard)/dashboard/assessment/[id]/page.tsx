// app/(dashboard)/dashboard/assessment/[id]/page.tsx
"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  Clock,
  Edit,
  Play,
  BarChart,
  Users,
  AlertCircle
} from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"

// Define assessment type
interface AssessmentOption {
  id: string;
  text: string;
  isCorrect?: boolean;
}

interface AssessmentQuestion {
  id: string;
  text: string;
  type: string;
  score?: number;
  options?: AssessmentOption[];
}

interface Assessment {
  id: string;
  title: string;
  type: string;
  category?: string;
  status: string;
  description?: string;
  instructions?: string;
  questions?: AssessmentQuestion[];
  totalScore?: number;
  timeLimit?: number;
  passingScore?: number;
  createdAt: string;
  createdBy?: {
    id?: string;
    name?: string;
  };
}

export default function AssessmentDetailPage() {
  const params = useParams();
  const id = params?.id as string;
  const router = useRouter()
  const { toast } = useToast()
  const [assessment, setAssessment] = useState<Assessment | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  // Fetch assessment data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/assessment/${id}`)

        if (!response.ok) {
          throw new Error('Failed to fetch assessment data')
        }

        const data = await response.json()
        setAssessment(data)
      } catch (error) {
        console.error('Error fetching assessment data:', error)
        toast({
          title: "Error",
          description: `Failed to load assessment details. Please try again: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [id, toast])

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="flex items-center gap-1">
          <FileText className="h-3 w-3" />
          <span>Draft</span>
        </Badge>
      case 'active':
        return <Badge className="bg-green-500 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          <span>Active</span>
        </Badge>
      case 'archived':
        return <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Archived</span>
        </Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'skills':
        return 'Skills Assessment'
      case 'personality':
        return 'Personality Assessment'
      case 'knowledge':
        return 'Knowledge Test'
      case 'performance':
        return 'Performance Evaluation'
      default:
        return type?.charAt(0).toUpperCase() + type?.slice(1) || 'Assessment'
    }
  }

  // Loading skeleton
  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Assessment Details"
          text="Loading assessment information..."
        />
        <div className="flex-1 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 w-10 rounded-full" />
              <Skeleton className="h-8 w-[200px]" />
            </div>
            <Skeleton className="h-10 w-[100px]" />
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Skeleton className="h-[200px] rounded-lg" />
            <Skeleton className="h-[200px] rounded-lg" />
          </div>

          <Skeleton className="h-[400px] rounded-lg" />
        </div>
      </DashboardShell>
    )
  }

  // If assessment not found
  if (!assessment) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Assessment Not Found"
          text="The requested assessment could not be found"
        />
        <div className="flex-1 space-y-4">
          <div className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Assessment Not Found</h2>
            <p className="text-muted-foreground mb-6">The assessment you're looking for doesn't exist or has been removed.</p>
            <Button onClick={() => router.push('/dashboard/assessment')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Assessments
            </Button>
          </div>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading={assessment.title}
        text={getTypeLabel(assessment.type)}
        children={
          <div className="flex items-center gap-2">
            {assessment.status === 'active' && (
              <Button>
                <Play className="mr-2 h-4 w-4" />
                Take Assessment
              </Button>
            )}
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </div>
        }
      />
      <div className="flex-1 space-y-6">
        {/* Status Badge */}
        <div className="flex items-center gap-2">
          {getStatusBadge(assessment.status)}
        </div>

        {/* Assessment Info & Stats */}
        <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Assessment Information</CardTitle>
            <CardDescription>Details about this assessment</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
                  <p>{getTypeLabel(assessment.type)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Category</h3>
                  <p>{assessment.category}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Questions</h3>
                  <p>{assessment.questions?.length || 0}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Total Score</h3>
                  <p>{assessment.totalScore || 0} points</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Time Limit</h3>
                  <p>{assessment.timeLimit ? `${assessment.timeLimit} minutes` : 'No limit'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Passing Score</h3>
                  <p>{assessment.passingScore ? `${assessment.passingScore} points` : 'Not set'}</p>
                </div>
              </div>

              {assessment.description && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                  <p className="text-sm">{assessment.description}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Assessment Statistics</CardTitle>
            <CardDescription>Performance and participation data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center h-[200px] text-center">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Data Available</h3>
              <p className="text-sm text-muted-foreground max-w-md mb-4">
                Statistics will be available once participants complete this assessment.
              </p>
              <Button variant="outline" asChild>
                <Link href={`/assessment/${id}/results`}>
                  <BarChart className="mr-2 h-4 w-4" />
                  View Results
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="questions">Questions</TabsTrigger>
            <TabsTrigger value="submissions">Submissions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 pt-4">
            <Card>
            <CardHeader>
              <CardTitle>Assessment Overview</CardTitle>
              <CardDescription>Summary of the assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {assessment.instructions && (
                  <div>
                    <h3 className="text-sm font-medium mb-2">Instructions</h3>
                    <p className="text-sm text-muted-foreground whitespace-pre-line">
                      {assessment.instructions}
                    </p>
                  </div>
                )}

                <Separator />

                <div>
                  <h3 className="text-sm font-medium mb-2">Created By</h3>
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                      {assessment.createdBy?.name?.charAt(0) || 'U'}
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {assessment.createdBy?.name || 'Unknown User'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Created on {new Date(assessment.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </TabsContent>

          <TabsContent value="questions" className="pt-4">
            <Card>
            <CardHeader>
              <CardTitle>Assessment Questions</CardTitle>
              <CardDescription>Questions included in this assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {assessment.questions?.map((question: any, index: number) => (
                  <div key={question.id} className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-sm">
                        {index + 1}
                      </div>
                      <div className="space-y-1 flex-1">
                        <p className="font-medium">{question.text}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {question.type === 'single_choice' ? 'Single Choice' :
                             question.type === 'multiple_choice' ? 'Multiple Choice' :
                             question.type === 'text' ? 'Text Answer' :
                             question.type === 'rating' ? 'Rating Scale' :
                             question.type === 'boolean' ? 'Yes/No' :
                             question.type}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {question.score || 1} point{(question.score || 1) !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                    </div>

                    {['single_choice', 'multiple_choice'].includes(question.type) && question.options && (
                      <div className="ml-8 space-y-1">
                        {question.options.map((option: any) => (
                          <div key={option.id} className="flex items-center gap-2">
                            <div className={`h-4 w-4 rounded-full ${option.isCorrect ? 'bg-green-500' : 'bg-muted'}`} />
                            <p className="text-sm">{option.text}</p>
                          </div>
                        ))}
                      </div>
                    )}

                    <Separator className="mt-4" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          </TabsContent>

          <TabsContent value="submissions" className="pt-4">
            <Card>
            <CardHeader>
              <CardTitle>Assessment Submissions</CardTitle>
              <CardDescription>Participants who have taken this assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Submissions Yet</h3>
                <p className="text-sm text-muted-foreground max-w-md">
                  There are no submissions for this assessment yet.
                </p>
              </div>
            </CardContent>
          </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  )
}

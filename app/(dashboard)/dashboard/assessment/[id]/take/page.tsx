"use client"

import { AssessmentTaker } from "@/components/assessment/assessment-taker"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { useParams } from "next/navigation"

export default function TakeAssessmentPage() {
  const params = useParams();
  const id = params?.id as string;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Take Assessment"
        text="Complete the assessment questions below"
      />
      <div className="flex-1 space-y-4">
        <AssessmentTaker assessmentId={id} />
      </div>
    </DashboardShell>
  )
}

// app/(dashboard)/dashboard/assessment/submission/[id]/results/page.tsx
"use client"

import { AssessmentResults } from "@/components/assessment/assessment-results"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default function AssessmentResultsPage() {
  const params = useParams()
  const [id, setId] = useState<string | null>(null)

  useEffect(() => {
    // In Next.js 15, params is a Promise that needs to be resolved
    if (params) {
      const submissionId = params.id as string
      setId(submissionId)
    }
  }, [params])

  // Show loading state while waiting for the ID
  if (!id) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Assessment Results"
          text="Loading assessment results..."
        />
        <div className="flex-1 space-y-4">
          <Skeleton className="h-[600px] w-full" />
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Assessment Results"
        text="View detailed assessment submission results"
      />
      <div className="flex-1 space-y-4">
        <AssessmentResults submissionId={id} />
      </div>
    </DashboardShell>
  )
}

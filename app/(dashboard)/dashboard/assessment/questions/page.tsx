import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AssessmentQuestionsPage } from '@/components/assessment/assessment-questions-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Question Bank | TCM Enterprise Business Suite',
  description: 'Manage assessment questions',
};

export default async function QuestionsPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.TRAINING_COORDINATOR
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Question Bank"
        text="Manage assessment questions"
      />
      <div className="flex-1 space-y-6">
        <AssessmentQuestionsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

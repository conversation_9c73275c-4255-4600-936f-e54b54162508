import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AssessmentSubmissionsPage } from '@/components/assessment/assessment-submissions-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Assessment Submissions | TCM Enterprise Business Suite',
  description: 'View and manage assessment submissions',
};

export default async function SubmissionsPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.RECRUITER,
    UserRole.TRAINING_COORDINATOR
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Assessment Submissions"
        text="View and manage assessment submissions"
      />
      <div className="flex-1 space-y-6">
        <AssessmentSubmissionsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

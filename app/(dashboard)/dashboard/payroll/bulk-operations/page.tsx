"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/frontend/hooks/useAuth'
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  FileText,
  Play,
  RefreshCw,
  Users,
  Zap
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

// TypeScript interfaces
interface PayrollRun {
  _id: string
  name: string
  description?: string
  payPeriod: {
    month: number
    year: number
    startDate: string
    endDate: string
  }
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string
  departments?: Array<{ _id: string; name: string }>
  createdBy: { firstName: string; lastName: string; email: string }
  createdAt: string
  approvedAt?: string
  approvedBy?: { firstName: string; lastName: string; email: string }
}

interface BulkOperationResult {
  success: boolean
  message: string
  data: {
    totalRuns: number
    processedRuns?: number
    approvedRuns?: number
    paidRuns?: number
    failedRuns: number
    skippedRuns: number
    totalAmount?: number
    batchId?: string
    results: Array<{
      payrollRunId: string
      payrollRunName: string
      status: string
      message?: string
      error?: string
      amount?: number
    }>
  }
}

export default function BulkOperationsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const [activeTab, setActiveTab] = useState('process')

  // State for different operation types
  const [draftRuns, setDraftRuns] = useState<PayrollRun[]>([])
  const [completedRuns, setCompletedRuns] = useState<PayrollRun[]>([])
  const [approvedRuns, setApprovedRuns] = useState<PayrollRun[]>([])

  // Selection state
  const [selectedDraftRuns, setSelectedDraftRuns] = useState<string[]>([])
  const [selectedCompletedRuns, setSelectedCompletedRuns] = useState<string[]>([])
  const [selectedApprovedRuns, setSelectedApprovedRuns] = useState<string[]>([])

  // Loading states
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)

  // Load data on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadEligibleRuns()
    }
  }, [isAuthenticated])

  const loadEligibleRuns = async () => {
    setLoading(true)
    try {
      // Load draft runs for processing
      const draftResponse = await fetch('/api/payroll/runs?status=draft&limit=50')
      if (draftResponse.ok) {
        const draftData = await draftResponse.json()
        setDraftRuns(draftData.data?.payrollRuns || draftData.data?.docs || [])
      }

      // Load completed runs for approval
      const completedResponse = await fetch('/api/payroll/runs/bulk-approve')
      if (completedResponse.ok) {
        const completedData = await completedResponse.json()
        setCompletedRuns(completedData.data?.eligibleRuns || [])
      }

      // Load approved runs for payment
      const approvedResponse = await fetch('/api/payroll/runs/bulk-pay')
      if (approvedResponse.ok) {
        const approvedData = await approvedResponse.json()
        setApprovedRuns(approvedData.data?.eligibleRuns || [])
      }
    } catch (error) {
      console.error('Failed to load eligible runs:', error)
      toast.error('Failed to load payroll runs')
    } finally {
      setLoading(false)
    }
  }

  // Handle bulk processing
  const handleBulkProcess = async () => {
    if (selectedDraftRuns.length === 0) {
      toast.error('Please select at least one payroll run to process')
      return
    }

    setProcessing(true)
    try {
      const response = await fetch('/api/payroll/runs/bulk-process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          payrollRunIds: selectedDraftRuns,
          useBatch: true,
          batchSize: 50,
          notes: `Bulk processed on ${new Date().toLocaleString()}`
        })
      })

      const result: BulkOperationResult = await response.json()

      if (result.success) {
        toast.success(result.message)
        setSelectedDraftRuns([])
        loadEligibleRuns()
      } else {
        toast.error('Failed to process payroll runs')
      }
    } catch (error) {
      console.error('Bulk processing error:', error)
      toast.error('Failed to process payroll runs')
    } finally {
      setProcessing(false)
    }
  }

  // Handle bulk approval
  const handleBulkApprove = async () => {
    if (selectedCompletedRuns.length === 0) {
      toast.error('Please select at least one payroll run to approve')
      return
    }

    setProcessing(true)
    try {
      const response = await fetch('/api/payroll/runs/bulk-approve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          payrollRunIds: selectedCompletedRuns,
          notes: `Bulk approved on ${new Date().toLocaleString()}`
        })
      })

      const result: BulkOperationResult = await response.json()

      if (result.success) {
        toast.success(result.message)
        setSelectedCompletedRuns([])
        loadEligibleRuns()
      } else {
        toast.error('Failed to approve payroll runs')
      }
    } catch (error) {
      console.error('Bulk approval error:', error)
      toast.error('Failed to approve payroll runs')
    } finally {
      setProcessing(false)
    }
  }

  // Handle bulk payment
  const handleBulkPay = async () => {
    if (selectedApprovedRuns.length === 0) {
      toast.error('Please select at least one payroll run to mark as paid')
      return
    }

    setProcessing(true)
    try {
      const response = await fetch('/api/payroll/runs/bulk-pay', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          payrollRunIds: selectedApprovedRuns,
          paymentMethod: 'Bank Transfer',
          paymentReference: `BULK-${Date.now()}`,
          notes: `Bulk payment processed on ${new Date().toLocaleString()}`
        })
      })

      const result: BulkOperationResult = await response.json()

      if (result.success) {
        toast.success(result.message)
        setSelectedApprovedRuns([])
        loadEligibleRuns()
      } else {
        toast.error('Failed to process payments')
      }
    } catch (error) {
      console.error('Bulk payment error:', error)
      toast.error('Failed to process payments')
    } finally {
      setProcessing(false)
    }
  }

  // Get status badge
  const getStatusBadge = (status: PayrollRun['status']) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'processing':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Approved</Badge>
      case 'paid':
        return <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">Paid</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'MWK') => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  // Format month name
  const getMonthName = (month: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]
    return months[month - 1] || 'Unknown'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Bulk Payroll Operations"
        text="Process, approve, and pay multiple payroll runs efficiently"
      >
        <div className="flex items-center space-x-2">
          <Button onClick={loadEligibleRuns} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </DashboardHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft Runs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{draftRuns.length}</div>
            <p className="text-xs text-muted-foreground">Ready for processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Runs</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedRuns.length}</div>
            <p className="text-xs text-muted-foreground">Ready for approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Runs</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedRuns.length}</div>
            <p className="text-xs text-muted-foreground">Ready for payment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(approvedRuns.reduce((sum, run) => sum + run.totalNetSalary, 0))}
            </div>
            <p className="text-xs text-muted-foreground">Awaiting payment</p>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Operations Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Bulk Operations
          </CardTitle>
          <CardDescription>
            Perform bulk operations on multiple payroll runs simultaneously
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="process">Process Runs</TabsTrigger>
              <TabsTrigger value="approve">Approve Runs</TabsTrigger>
              <TabsTrigger value="pay">Pay Runs</TabsTrigger>
            </TabsList>

            <TabsContent value="process" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Process Draft Runs</h3>
                  <p className="text-sm text-muted-foreground">
                    Select draft payroll runs to process in bulk
                  </p>
                </div>
                <Button
                  onClick={handleBulkProcess}
                  disabled={selectedDraftRuns.length === 0 || processing}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Process Selected ({selectedDraftRuns.length})
                </Button>
              </div>

              {draftRuns.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No draft payroll runs available for processing
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedDraftRuns.length === draftRuns.length}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedDraftRuns(draftRuns.map(run => run._id))
                            } else {
                              setSelectedDraftRuns([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Run Name</TableHead>
                      <TableHead>Pay Period</TableHead>
                      <TableHead>Employees</TableHead>
                      <TableHead>Net Salary</TableHead>
                      <TableHead>Created</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {draftRuns.map((run) => (
                      <TableRow key={run._id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedDraftRuns.includes(run._id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedDraftRuns([...selectedDraftRuns, run._id])
                              } else {
                                setSelectedDraftRuns(selectedDraftRuns.filter(id => id !== run._id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{run.name}</div>
                            {getStatusBadge(run.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getMonthName(run.payPeriod.month)} {run.payPeriod.year}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{run.totalEmployees}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(run.totalNetSalary, run.currency)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {format(new Date(run.createdAt), 'MMM dd, yyyy')}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              by {run.createdBy.firstName} {run.createdBy.lastName}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </TabsContent>

            <TabsContent value="approve" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Approve Completed Runs</h3>
                  <p className="text-sm text-muted-foreground">
                    Select completed payroll runs to approve in bulk
                  </p>
                </div>
                <Button
                  onClick={handleBulkApprove}
                  disabled={selectedCompletedRuns.length === 0 || processing}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Selected ({selectedCompletedRuns.length})
                </Button>
              </div>

              {completedRuns.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No completed payroll runs available for approval
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedCompletedRuns.length === completedRuns.length}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedCompletedRuns(completedRuns.map(run => run._id))
                            } else {
                              setSelectedCompletedRuns([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Run Name</TableHead>
                      <TableHead>Pay Period</TableHead>
                      <TableHead>Employees</TableHead>
                      <TableHead>Net Salary</TableHead>
                      <TableHead>Completed</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {completedRuns.map((run) => (
                      <TableRow key={run._id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedCompletedRuns.includes(run._id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCompletedRuns([...selectedCompletedRuns, run._id])
                              } else {
                                setSelectedCompletedRuns(selectedCompletedRuns.filter(id => id !== run._id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{run.name}</div>
                            {getStatusBadge(run.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getMonthName(run.payPeriod.month)} {run.payPeriod.year}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{run.processedEmployees}/{run.totalEmployees}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(run.totalNetSalary, run.currency)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {format(new Date(run.createdAt), 'MMM dd, yyyy')}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              by {run.createdBy.firstName} {run.createdBy.lastName}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </TabsContent>

            <TabsContent value="pay" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Pay Approved Runs</h3>
                  <p className="text-sm text-muted-foreground">
                    Select approved payroll runs to mark as paid in bulk
                  </p>
                </div>
                <Button
                  onClick={handleBulkPay}
                  disabled={selectedApprovedRuns.length === 0 || processing}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <DollarSign className="h-4 w-4 mr-2" />
                  Pay Selected ({selectedApprovedRuns.length})
                </Button>
              </div>

              {approvedRuns.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No approved payroll runs available for payment
                </div>
              ) : (
                <>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                      <div>
                        <h4 className="font-medium text-yellow-800">Payment Confirmation</h4>
                        <p className="text-sm text-yellow-700">
                          Total amount to be paid: {formatCurrency(
                            selectedApprovedRuns.reduce((sum, id) => {
                              const run = approvedRuns.find(r => r._id === id)
                              return sum + (run?.totalNetSalary || 0)
                            }, 0)
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedApprovedRuns.length === approvedRuns.length}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedApprovedRuns(approvedRuns.map(run => run._id))
                              } else {
                                setSelectedApprovedRuns([])
                              }
                            }}
                          />
                        </TableHead>
                        <TableHead>Run Name</TableHead>
                        <TableHead>Pay Period</TableHead>
                        <TableHead>Employees</TableHead>
                        <TableHead>Net Salary</TableHead>
                        <TableHead>Approved</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {approvedRuns.map((run) => (
                        <TableRow key={run._id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedApprovedRuns.includes(run._id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedApprovedRuns([...selectedApprovedRuns, run._id])
                                } else {
                                  setSelectedApprovedRuns(selectedApprovedRuns.filter(id => id !== run._id))
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{run.name}</div>
                              {getStatusBadge(run.status)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getMonthName(run.payPeriod.month)} {run.payPeriod.year}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>{run.processedEmployees}/{run.totalEmployees}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrency(run.totalNetSalary, run.currency)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {run.approvedAt ? format(new Date(run.approvedAt), 'MMM dd, yyyy') : 'N/A'}
                              </div>
                              {run.approvedBy && (
                                <div className="text-sm text-muted-foreground">
                                  by {run.approvedBy.firstName} {run.approvedBy.lastName}
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/frontend/hooks/useAuth'
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  FileText,
  RefreshCw,
  TrendingUp,
  Upload,
  Zap
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

// TypeScript interfaces
interface SalaryRevision {
  _id: string
  employeeId: {
    _id: string
    firstName: string
    lastName: string
    email: string
    department?: { name: string }
  }
  revisionType: string
  previousBasicSalary: number
  newBasicSalary: number
  percentageChange: number
  amountChange: number
  effectiveDate: string
  reason: string
  notes?: string
  approvalStatus: 'pending' | 'approved' | 'rejected'
  approvedBy?: { firstName: string; lastName: string }
  approvedAt?: string
  createdAt: string
  createdBy: { firstName: string; lastName: string }
}

interface Department {
  _id: string
  name: string
}

interface BulkUpdateForm {
  updateType: 'percentage' | 'fixed_amount' | 'department_wide' | 'role_based'
  departments: string[]
  roles: string[]
  percentageIncrease?: number
  fixedAmount?: number
  effectiveDate: string
  reason: string
  notes?: string
  revisionType: 'increment' | 'promotion' | 'adjustment' | 'demotion' | 'annual_review' | 'other'
  autoApprove: boolean
  salaryRangeMin?: number
  salaryRangeMax?: number
}

export default function SalaryRevisionsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()

  // State for revisions
  const [revisions, setRevisions] = useState<SalaryRevision[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)
  
  // State for bulk operations
  const [showBulkDialog, setShowBulkDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [bulkForm, setBulkForm] = useState<BulkUpdateForm>({
    updateType: 'percentage',
    departments: [],
    roles: [],
    effectiveDate: '',
    reason: '',
    revisionType: 'increment',
    autoApprove: false
  })

  // File upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)

  // Load data on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadRevisions()
      loadDepartments()
    }
  }, [isAuthenticated])

  const loadRevisions = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/payroll/salary-revisions?limit=50&sort=-createdAt')
      if (response.ok) {
        const data = await response.json()
        setRevisions(data.data || [])
      }
    } catch (error) {
      console.error('Failed to load salary revisions:', error)
      toast.error('Failed to load salary revisions')
    } finally {
      setLoading(false)
    }
  }

  const loadDepartments = async () => {
    try {
      const response = await fetch('/api/departments')
      if (response.ok) {
        const data = await response.json()
        setDepartments(data.data || [])
      }
    } catch (error) {
      console.error('Failed to load departments:', error)
    }
  }

  // Handle bulk update
  const handleBulkUpdate = async () => {
    if (!bulkForm.effectiveDate || !bulkForm.reason) {
      toast.error('Please fill in all required fields')
      return
    }

    if (bulkForm.updateType === 'percentage' && !bulkForm.percentageIncrease) {
      toast.error('Please enter percentage increase')
      return
    }

    if (bulkForm.updateType === 'fixed_amount' && !bulkForm.fixedAmount) {
      toast.error('Please enter fixed amount')
      return
    }

    setProcessing(true)
    try {
      const requestBody = {
        updateType: bulkForm.updateType,
        filters: {
          departments: bulkForm.departments.length > 0 ? bulkForm.departments : undefined,
          roles: bulkForm.roles.length > 0 ? bulkForm.roles : undefined,
          salaryRange: bulkForm.salaryRangeMin && bulkForm.salaryRangeMax ? {
            min: bulkForm.salaryRangeMin,
            max: bulkForm.salaryRangeMax
          } : undefined
        },
        changes: {
          percentageIncrease: bulkForm.percentageIncrease,
          fixedAmount: bulkForm.fixedAmount
        },
        effectiveDate: bulkForm.effectiveDate,
        reason: bulkForm.reason,
        notes: bulkForm.notes,
        revisionType: bulkForm.revisionType,
        autoApprove: bulkForm.autoApprove
      }

      const response = await fetch('/api/payroll/salary-revisions/bulk-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message)
        setShowBulkDialog(false)
        // Reset form
        setBulkForm({
          updateType: 'percentage',
          departments: [],
          roles: [],
          effectiveDate: '',
          reason: '',
          revisionType: 'increment',
          autoApprove: false
        })
        // Reload revisions to show the new records
        await loadRevisions()
      } else {
        toast.error(result.error || 'Failed to process bulk update')
      }
    } catch (error) {
      console.error('Bulk update error:', error)
      toast.error('Failed to process bulk update')
    } finally {
      setProcessing(false)
    }
  }

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file')
      return
    }

    setProcessing(true)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 500)

      const response = await fetch('/api/payroll/salary-revisions/bulk-import', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      const result = await response.json()

      if (result.success) {
        toast.success(result.message)
        setShowImportDialog(false)
        setSelectedFile(null)
        setUploadProgress(0)
        // Reload revisions to show the imported records
        await loadRevisions()
      } else {
        toast.error(result.error || 'Failed to import salary revisions')
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Failed to import salary revisions')
    } finally {
      setProcessing(false)
    }
  }

  // Get status badge
  const getStatusBadge = (status: SalaryRevision['approvalStatus']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'MWK') => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Salary Revisions"
        text="Manage salary revisions and bulk updates"
      >
        <div className="flex items-center space-x-2">
          <Button onClick={loadRevisions} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Bulk Import Salary Revisions</DialogTitle>
                <DialogDescription>
                  Upload an Excel file with salary revision data
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file">Excel File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                  />
                </div>
                {uploadProgress > 0 && (
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleFileUpload} disabled={!selectedFile || processing}>
                  {processing ? 'Importing...' : 'Import'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
            <DialogTrigger asChild>
              <Button>
                <Zap className="h-4 w-4 mr-2" />
                Bulk Update
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Bulk Salary Update</DialogTitle>
                <DialogDescription>
                  Apply salary changes to multiple employees
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="updateType">Update Type</Label>
                    <Select 
                      value={bulkForm.updateType} 
                      onValueChange={(value: any) => setBulkForm({...bulkForm, updateType: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage Increase</SelectItem>
                        <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                        <SelectItem value="department_wide">Department Wide</SelectItem>
                        <SelectItem value="role_based">Role Based</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="revisionType">Revision Type</Label>
                    <Select 
                      value={bulkForm.revisionType} 
                      onValueChange={(value: any) => setBulkForm({...bulkForm, revisionType: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="increment">Increment</SelectItem>
                        <SelectItem value="promotion">Promotion</SelectItem>
                        <SelectItem value="adjustment">Adjustment</SelectItem>
                        <SelectItem value="annual_review">Annual Review</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {(bulkForm.updateType === 'percentage' || bulkForm.updateType === 'department_wide' || bulkForm.updateType === 'role_based') && (
                  <div>
                    <Label htmlFor="percentageIncrease">Percentage Increase (%)</Label>
                    <Input
                      id="percentageIncrease"
                      type="number"
                      step="0.1"
                      value={bulkForm.percentageIncrease || ''}
                      onChange={(e) => setBulkForm({...bulkForm, percentageIncrease: parseFloat(e.target.value)})}
                    />
                  </div>
                )}

                {(bulkForm.updateType === 'fixed_amount' || bulkForm.updateType === 'department_wide' || bulkForm.updateType === 'role_based') && (
                  <div>
                    <Label htmlFor="fixedAmount">Fixed Amount (MWK)</Label>
                    <Input
                      id="fixedAmount"
                      type="number"
                      value={bulkForm.fixedAmount || ''}
                      onChange={(e) => setBulkForm({...bulkForm, fixedAmount: parseFloat(e.target.value)})}
                    />
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="effectiveDate">Effective Date</Label>
                    <Input
                      id="effectiveDate"
                      type="date"
                      value={bulkForm.effectiveDate}
                      onChange={(e) => setBulkForm({...bulkForm, effectiveDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2 pt-6">
                    <Checkbox
                      id="autoApprove"
                      checked={bulkForm.autoApprove}
                      onCheckedChange={(checked) => setBulkForm({...bulkForm, autoApprove: checked as boolean})}
                    />
                    <Label htmlFor="autoApprove">Auto Approve</Label>
                  </div>
                </div>

                <div>
                  <Label htmlFor="reason">Reason</Label>
                  <Input
                    id="reason"
                    value={bulkForm.reason}
                    onChange={(e) => setBulkForm({...bulkForm, reason: e.target.value})}
                    placeholder="Enter reason for salary revision"
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={bulkForm.notes || ''}
                    onChange={(e) => setBulkForm({...bulkForm, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleBulkUpdate} disabled={processing}>
                  {processing ? 'Processing...' : 'Apply Changes'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </DashboardHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revisions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{revisions.length}</div>
            <p className="text-xs text-muted-foreground">All salary revisions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {revisions.filter(r => r.approvalStatus === 'pending').length}
            </div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {revisions.filter(r => r.approvalStatus === 'approved').length}
            </div>
            <p className="text-xs text-muted-foreground">Approved revisions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Increase</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {revisions.length > 0 
                ? `${(revisions.reduce((sum, r) => sum + r.percentageChange, 0) / revisions.length).toFixed(1)}%`
                : '0%'
              }
            </div>
            <p className="text-xs text-muted-foreground">Average percentage change</p>
          </CardContent>
        </Card>
      </div>

      {/* Revisions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Salary Revisions</CardTitle>
          <CardDescription>
            Recent salary revision requests and their approval status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {revisions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No salary revisions found
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Previous Salary</TableHead>
                  <TableHead>New Salary</TableHead>
                  <TableHead>Change</TableHead>
                  <TableHead>Effective Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {revisions.map((revision) => (
                  <TableRow key={revision._id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {revision.employeeId.firstName} {revision.employeeId.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {revision.employeeId.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {revision.employeeId.department?.name || 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {revision.revisionType.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(revision.previousBasicSalary)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(revision.newBasicSalary)}
                    </TableCell>
                    <TableCell>
                      <div className={`flex items-center ${revision.amountChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        <TrendingUp className="h-4 w-4 mr-1" />
                        {revision.percentageChange >= 0 ? '+' : ''}{revision.percentageChange.toFixed(1)}%
                        <span className="ml-1 text-xs">
                          ({revision.amountChange >= 0 ? '+' : ''}{formatCurrency(revision.amountChange)})
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {format(new Date(revision.effectiveDate), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(revision.approvalStatus)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {format(new Date(revision.createdAt), 'MMM dd, yyyy')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          by {revision.createdBy.firstName} {revision.createdBy.lastName}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

"use client"

import { useState } from "react"
import { FileText } from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { GenerateReportForm } from "@/components/payroll/reports/generate-report-form"
import { ReportsTable } from "@/components/payroll/reports/reports-table"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function PayrollReportsPage() {
  const [activeTab, setActiveTab] = useState("reports")
  const [showGenerateDialog, setShowGenerateDialog] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Handle report generation success
  const handleReportGenerated = () => {
    setShowGenerateDialog(false)
    setActiveTab("reports")
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Reports"
        text="Generate and manage payroll reports"
      >
        <Button onClick={() => setShowGenerateDialog(true)}>
          <FileText className="mr-2 h-4 w-4" />
          Generate Report
        </Button>
      </DashboardHeader>

      <Tabs
        defaultValue="reports"
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="generate">Generate Report</TabsTrigger>
        </TabsList>
        <TabsContent value="reports" className="space-y-4">
          <ReportsTable
            onGenerateReport={() => setActiveTab("generate")}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
        <TabsContent value="generate" className="space-y-4">
          <GenerateReportForm onSuccess={handleReportGenerated} />
        </TabsContent>
      </Tabs>

      {/* Generate Report Dialog */}
      <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Generate Payroll Report</DialogTitle>
            <DialogDescription>
              Create a new payroll report with custom parameters
            </DialogDescription>
          </DialogHeader>
          <GenerateReportForm onSuccess={handleReportGenerated} />
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}

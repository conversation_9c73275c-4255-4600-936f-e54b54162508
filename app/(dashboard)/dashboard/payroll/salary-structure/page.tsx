// app/(dashboard)/dashboard/payroll/salary-structure/page.tsx
"use client"

import { SalaryStructureManager } from "@/components/payroll/salary-structure/salary-structure-manager"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"

export default function SalaryStructurePage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Salary Structures" 
        text="Define and manage salary structures with components and allowances"
      />
      <div className="space-y-6">
        <SalaryStructureManager />
      </div>
    </DashboardShell>
  )
}

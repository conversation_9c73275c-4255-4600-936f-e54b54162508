// app/(dashboard)/dashboard/payroll/salary-bands/page.tsx
"use client"

import { SalaryBandManager } from "@/components/payroll/salary-band/salary-band-manager"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"

export default function SalaryBandsPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Salary Bands" 
        text="Manage TCM salary bands with compensation structures and progression rules"
      />
      <div className="space-y-6">
        <SalaryBandManager />
      </div>
    </DashboardShell>
  )
}

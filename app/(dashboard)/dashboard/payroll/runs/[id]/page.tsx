"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/frontend/hooks/useAuth'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { PayrollRunStatusActions } from "@/components/payroll/payroll-run/payroll-run-status-actions"
import { PayrollWorkflowGuide } from "@/components/payroll/payroll-run/payroll-workflow-guide"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Calendar,
  Download,
  DollarSign,
  FileText,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"
import { useErrorHand<PERSON> } from '@/hooks/use-error-handler'
import { ErrorOverlay } from '@/components/errors/error-overlay'

// TypeScript interfaces
interface PayrollRunUser {
  _id: string
  firstName: string
  lastName: string
  email: string
  name?: string
}

interface PayrollRunDepartment {
  _id: string
  name: string
}

interface PayrollRunPayPeriod {
  month: number
  year: number
  startDate: string
  endDate: string
}

interface PayrollRunDetails {
  _id: string
  name: string
  description?: string
  payPeriod: PayrollRunPayPeriod
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string
  notes?: string
  departments?: PayrollRunDepartment[]
  createdBy: PayrollRunUser
  updatedBy?: PayrollRunUser
  approvedBy?: PayrollRunUser
  approvedAt?: string
  processedAt?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
}

interface PayrollRunResponse {
  success: boolean
  data: PayrollRunDetails
  error?: string
}

export default function PayrollRunDetailsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const params = useParams()
  const router = useRouter()
  const [payrollRun, setPayrollRun] = useState<PayrollRunDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const { error, isErrorOpen, handleApiError, hideError, isActionLoading, loadingAction, setActionLoading } = useErrorHandler()

  const payrollRunId = params?.id as string

  // Fetch payroll run details
  const fetchPayrollRunDetails = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}`)

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const data: PayrollRunResponse = await response.json()

      if (data.success) {
        setPayrollRun(data.data)
      } else {
        console.error('Failed to fetch payroll run details:', data.error)
      }
    } catch (error) {
      console.error('Error fetching payroll run details:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    if (isAuthenticated && payrollRunId) {
      fetchPayrollRunDetails()
    }
  }, [isAuthenticated, payrollRunId])

  // Get status badge color and text
  const getStatusBadge = (status: PayrollRunDetails['status']) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'processing':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Approved</Badge>
      case 'paid':
        return <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">Paid</Badge>
      case 'cancelled':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'MWK') => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  // Format month name
  const getMonthName = (month: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]
    return months[month - 1] || 'Unknown'
  }

  // Get user display name
  const getUserDisplayName = (user: PayrollRunUser) => {
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    return user.email
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading payroll run details...</p>
        </div>
      </div>
    )
  }

  if (!payrollRun) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Payroll Run Details"
          text="Payroll run information"
        >
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Payroll Run Not Found</h3>
            <p className="text-muted-foreground">
              The requested payroll run could not be found or you don't have permission to view it.
            </p>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  return (
    <>
      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          isActionLoading={isActionLoading}
          loadingAction={loadingAction}
          onAction={async (action) => {
            setActionLoading(action)
            try {
              if (action === 'retry') {
                await fetchPayrollRunDetails()
              }
            } finally {
              setActionLoading(undefined)
            }
          }}
        />
      )}

      <DashboardShell>
      <DashboardHeader
        heading={payrollRun.name}
        text={`${getMonthName(payrollRun.payPeriod.month)} ${payrollRun.payPeriod.year} Payroll Run`}
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          {getStatusBadge(payrollRun.status)}
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </DashboardHeader>

      {/* Workflow Guide */}
      <PayrollWorkflowGuide
        currentStatus={payrollRun.status}
        payrollRunId={payrollRun._id}
        totalEmployees={payrollRun.totalEmployees}
        processedEmployees={payrollRun.processedEmployees}
      />

      {/* Status and Actions */}
      <PayrollRunStatusActions
        payrollRun={payrollRun}
        onStatusChange={fetchPayrollRunDetails}
      />

      {/* Financial Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gross Salary</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payrollRun.totalGrossSalary, payrollRun.currency)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total gross salary
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Deductions</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payrollRun.totalDeductions, payrollRun.currency)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total deductions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payrollRun.totalTax, payrollRun.currency)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total tax
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Salary</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payrollRun.totalNetSalary, payrollRun.currency)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total net salary
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pay Period & Basic Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Pay Period & Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Pay Period</label>
              <div className="mt-1">
                <div className="font-medium">
                  {getMonthName(payrollRun.payPeriod.month)} {payrollRun.payPeriod.year}
                </div>
                <div className="text-sm text-muted-foreground">
                  {format(new Date(payrollRun.payPeriod.startDate), 'MMM dd, yyyy')} - {format(new Date(payrollRun.payPeriod.endDate), 'MMM dd, yyyy')}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <label className="text-sm font-medium text-muted-foreground">Description</label>
              <div className="mt-1">
                {payrollRun.description || 'No description provided'}
              </div>
            </div>

            {payrollRun.departments && payrollRun.departments.length > 0 && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Departments</label>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {payrollRun.departments.map((dept) => (
                      <Badge key={dept._id} variant="outline">
                        {dept.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}

            {payrollRun.notes && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Notes</label>
                  <div className="mt-1">
                    {payrollRun.notes}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Audit Trail */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Audit Trail
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <div className="mt-1">
                <div className="font-medium">
                  {format(new Date(payrollRun.createdAt), 'MMM dd, yyyy HH:mm')}
                </div>
                <div className="text-sm text-muted-foreground">
                  by {getUserDisplayName(payrollRun.createdBy)}
                </div>
              </div>
            </div>

            {payrollRun.processedAt && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Processed</label>
                  <div className="mt-1">
                    <div className="font-medium">
                      {format(new Date(payrollRun.processedAt), 'MMM dd, yyyy HH:mm')}
                    </div>
                    {payrollRun.updatedBy && (
                      <div className="text-sm text-muted-foreground">
                        by {getUserDisplayName(payrollRun.updatedBy)}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {payrollRun.approvedAt && payrollRun.approvedBy && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Approved</label>
                  <div className="mt-1">
                    <div className="font-medium">
                      {format(new Date(payrollRun.approvedAt), 'MMM dd, yyyy HH:mm')}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      by {getUserDisplayName(payrollRun.approvedBy)}
                    </div>
                  </div>
                </div>
              </>
            )}

            {payrollRun.paidAt && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Paid</label>
                  <div className="mt-1">
                    <div className="font-medium">
                      {format(new Date(payrollRun.paidAt), 'MMM dd, yyyy HH:mm')}
                    </div>
                  </div>
                </div>
              </>
            )}

            <Separator />

            <div>
              <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
              <div className="mt-1">
                <div className="font-medium">
                  {format(new Date(payrollRun.updatedAt), 'MMM dd, yyyy HH:mm')}
                </div>
                {payrollRun.updatedBy && (
                  <div className="text-sm text-muted-foreground">
                    by {getUserDisplayName(payrollRun.updatedBy)}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
    </>
  )
}

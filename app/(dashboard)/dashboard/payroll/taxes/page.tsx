// app/(dashboard)/dashboard/payroll/taxes/page.tsx
"use client"

import { TaxBracketManager } from "@/components/payroll/tax-bracket/tax-bracket-manager"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"

export default function TaxManagementPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Tax Management" 
        text="Manage tax brackets and PAYE calculations for payroll processing"
      />
      <div className="space-y-6">
        <TaxBracketManager />
      </div>
    </DashboardShell>
  )
}

"use client"

import { AllowanceManager } from "@/components/payroll/allowance/allowance-manager"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export default function AllowancesPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Allowance Management" 
        text="Manage allowances that can be added to employee salaries"
      />
      
      <AllowanceManager />
    </DashboardShell>
  )
}

"use client"

import { DeductionManager } from "@/components/payroll/deduction/deduction-manager"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export default function DeductionsPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Deduction Management" 
        text="Manage deductions that can be applied to employee salaries"
      />
      
      <DeductionManager />
    </DashboardShell>
  )
}

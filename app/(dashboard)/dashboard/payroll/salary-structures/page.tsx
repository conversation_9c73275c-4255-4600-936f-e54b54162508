"use client"

import { SalaryS<PERSON>ureManager } from "@/components/payroll/salary-structure/salary-structure-manager"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export default function SalaryStructuresPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Salary Structures" 
        text="Manage salary structures, components, and calculation rules"
      />
      
      <SalaryStructureManager />
    </DashboardShell>
  )
}

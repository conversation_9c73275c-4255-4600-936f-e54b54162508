// app/(dashboard)/dashboard/payroll/payslips/[id]/page.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { PayslipViewer } from "@/components/payroll/payslip/payslip-viewer"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Button } from "@/components/ui/button"
import { ArrowLeft, FileText, AlertTriangle } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { EmptyState } from "@/components/empty-state"

// Payslip interface - aligned with PayslipViewer component
interface Payslip {
  _id?: string
  id?: string
  employeeId?: string
  payPeriod: {
    month: number
    year: number
  }
  employeeDetails: {
    name: string
    employeeNumber: string
    department?: string
    position?: string
    joinDate?: string | Date
    taxId?: string
    bankAccount?: string
  }
  paymentDetails: {
    grossSalary: number
    totalDeductions: number
    totalTax: number
    netSalary: number
    currency: string
    paymentMethod?: string
    paymentDate?: string | Date
    paymentReference?: string
  }
  earningsBreakdown?: Array<{
    name: string
    amount: number
  }>
  deductionsBreakdown?: Array<{
    name: string
    amount: number
  }>
}

// Mock payslip data for testing
const mockPayslip: Payslip = {
  id: "payslip-001",
  employeeId: "emp-001",
  payPeriod: {
    month: 12,
    year: 2024
  },
  employeeDetails: {
    name: "John Doe",
    employeeNumber: "EMP001",
    department: "Engineering",
    position: "Software Developer",
    joinDate: "2023-01-15",
    taxId: "TAX123456",
    bankAccount: "**********"
  },
  paymentDetails: {
    grossSalary: 150000,
    totalDeductions: 25000,
    totalTax: 15000,
    netSalary: 110000,
    currency: "MWK",
    paymentMethod: "Bank Transfer",
    paymentDate: "2024-12-31",
    paymentReference: "PAY-2024-12-001"
  },
  earningsBreakdown: [
    { name: "Basic Salary", amount: 120000 },
    { name: "Housing Allowance", amount: 20000 },
    { name: "Transport Allowance", amount: 10000 }
  ],
  deductionsBreakdown: [
    { name: "PAYE Tax", amount: 15000 },
    { name: "Pension", amount: 7500 },
    { name: "Medical Insurance", amount: 2500 }
  ]
}

export default function PayslipPage() {
  const params = useParams();
  const id = params?.id as string;
  const router = useRouter()
  const [payslip, setPayslip] = useState<Payslip | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch payslip data
  useEffect(() => {
    const fetchPayslip = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // In a real implementation, this would be an API call
        // For now, simulate an API call with a timeout
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Return mock data for testing - in real app, this would be API response
        setPayslip(mockPayslip)
      } catch (err) {
        console.error("Error fetching payslip:", err)
        setError("Failed to load payslip. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchPayslip()
  }, [id])

  // Handle back
  const handleBack = () => {
    router.push("/payroll")
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payslip Viewer"
        text="View and manage employee payslips"
      >
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Payroll
        </Button>
      </DashboardHeader>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-48" />
          <Skeleton className="h-[600px] w-full" />
        </div>
      ) : error ? (
        <EmptyState
          title="Error loading payslip"
          description={error}
          icon={<AlertTriangle className="h-10 w-10 text-destructive" />}
          action={
            <Button onClick={() => window.location.reload()}>Retry</Button>
          }
        />
      ) : payslip ? (
        <PayslipViewer payslip={payslip} />
      ) : (
        <EmptyState
          title="Payslip not found"
          description="The requested payslip could not be found."
          icon={<FileText className="h-10 w-10 text-muted-foreground" />}
          action={
            <Button onClick={handleBack}>Go Back</Button>
          }
        />
      )}
    </DashboardShell>
  )
}

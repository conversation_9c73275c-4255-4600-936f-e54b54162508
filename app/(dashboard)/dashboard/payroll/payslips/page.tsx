"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/frontend/hooks/useAuth"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/hooks/use-error-handler'
import { ErrorOverlay } from '@/components/errors/error-overlay'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import { BulkPayslipActions } from "@/components/payroll/payslip/bulk-payslip-actions"
import { Checkbox } from "@/components/ui/checkbox"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import {
  Calendar,
  Download,
  Eye,
  FileText,
  Loader2,
  Mail,
  MoreHorizontal,
  Plus,
  Search,
  Trash2,
  Users,
} from "lucide-react"
import { format } from "date-fns"

// Types
interface PayrollRun {
  _id: string
  name: string
  payPeriod: {
    month: number
    year: number
    startDate: string
    endDate: string
  }
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string
  createdAt: string
  updatedAt: string
}

interface Employee {
  _id: string
  firstName: string
  lastName: string
  email: string
  employeeNumber: string
  department: {
    _id: string
    name: string
  }
  position: string
  status: string
  hasPayrollRecord: boolean
  payrollStatus: 'not_processed' | 'draft' | 'approved' | 'paid' | 'cancelled'
  hasPayslip: boolean
  payslipId?: string
  payslipStatus?: 'generated' | 'sent' | 'viewed' | 'downloaded'
  grossSalary?: number
  netSalary?: number
}

interface PayslipPageState {
  payrollRuns: PayrollRun[]
  selectedPayrollRun: string
  employees: Employee[]
  searchTerm: string
  isLoadingRuns: boolean
  isLoadingEmployees: boolean
  selectedPayslips: string[]
  bulkDeleteDialogOpen: boolean
  isDeleting: boolean
}

export default function PayslipsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { error, isErrorOpen, handleApiError, hideError, isActionLoading, loadingAction, setActionLoading } = useErrorHandler()

  const [state, setState] = useState<PayslipPageState>({
    payrollRuns: [],
    selectedPayrollRun: '',
    employees: [],
    searchTerm: '',
    isLoadingRuns: false,
    isLoadingEmployees: false,
    selectedPayslips: [],
    bulkDeleteDialogOpen: false,
    isDeleting: false,
  })

  // Get selected payroll run object
  const selectedRun = state.payrollRuns.find(run => run._id === state.selectedPayrollRun)

  // Filter employees based on search term
  const filteredEmployees = state.employees.filter(employee =>
    `${employee.firstName} ${employee.lastName}`.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
    employee.employeeNumber.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
    employee.department.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
    employee.position.toLowerCase().includes(state.searchTerm.toLowerCase())
  )

  // Fetch payroll runs
  const fetchPayrollRuns = async () => {
    setState(prev => ({ ...prev, isLoadingRuns: true }))
    try {
      const response = await fetch('/api/payroll/runs?limit=50&sortBy=createdAt&sortOrder=desc')

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const data = await response.json()
      if (data.success) {
        setState(prev => ({
          ...prev,
          payrollRuns: data.data.payrollRuns,
          isLoadingRuns: false
        }))
      }
    } catch (error) {
      console.error('Error fetching payroll runs:', error)
      setState(prev => ({ ...prev, isLoadingRuns: false }))
    }
  }

  // Fetch employees for selected payroll run
  const fetchEmployees = async (payrollRunId: string) => {
    setState(prev => ({ ...prev, isLoadingEmployees: true }))
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunId}/employees`)

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const data = await response.json()
      if (data.success) {
        setState(prev => ({
          ...prev,
          employees: data.data.employees || [],
          isLoadingEmployees: false
        }))
      }
    } catch (error) {
      console.error('Error fetching employees:', error)
      setState(prev => ({ ...prev, isLoadingEmployees: false }))
    }
  }

  // Handle payroll run selection
  const handlePayrollRunChange = (payrollRunId: string) => {
    setState(prev => ({
      ...prev,
      selectedPayrollRun: payrollRunId,
      employees: [],
      searchTerm: '',
      selectedPayslips: [], // Clear selected payslips when changing runs
    }))

    if (payrollRunId) {
      fetchEmployees(payrollRunId)
    }
  }

  // Handle bulk delete confirmation
  const handleBulkDelete = async () => {
    if (state.selectedPayslips.length === 0) return

    try {
      setState(prev => ({ ...prev, isDeleting: true }))

      // Call the bulk delete API
      const response = await fetch('/api/payroll/payslips/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: state.selectedPayslips
        }),
      })

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const result = await response.json()

      // Close the dialog and clear selections
      setState(prev => ({
        ...prev,
        bulkDeleteDialogOpen: false,
        selectedPayslips: [],
        isDeleting: false
      }))

      // Show success message
      toast({
        title: "Payslips Deleted",
        description: `Successfully deleted ${result.deletedCount} payslip(s)`,
      })

      // Refresh the employees list to update payslip status
      if (state.selectedPayrollRun) {
        fetchEmployees(state.selectedPayrollRun)
      }
    } catch (error) {
      console.error('Error in bulk delete:', error)
      setState(prev => ({ ...prev, isDeleting: false }))
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete payslips",
        variant: "destructive",
      })
    }
  }

  // Handle select/deselect payslip
  const handleSelectPayslip = (payslipId: string, selected: boolean) => {
    setState(prev => ({
      ...prev,
      selectedPayslips: selected
        ? [...prev.selectedPayslips, payslipId]
        : prev.selectedPayslips.filter(id => id !== payslipId)
    }))
  }

  // Handle select all payslips
  const handleSelectAll = (selected: boolean) => {
    const payslipsWithPayslips = filteredEmployees
      .filter(emp => emp.hasPayslip && emp.payslipId)
      .map(emp => emp.payslipId!)

    setState(prev => ({
      ...prev,
      selectedPayslips: selected ? payslipsWithPayslips : []
    }))
  }

  // Handle individual payslip download
  const handleDownloadPayslip = async (payslipId: string, employeeName: string) => {
    try {
      const response = await fetch(`/api/payroll/payslips/${payslipId}/download`)

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      // Create download link
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `payslip-${employeeName.replace(/\s+/g, '-')}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Payslip Downloaded",
        description: `Payslip for ${employeeName} has been downloaded.`,
      })
    } catch (error) {
      console.error('Error downloading payslip:', error)
    }
  }

  // Handle individual payslip generation
  const handleGeneratePayslip = async (employeeId: string, employeeName: string) => {
    if (!selectedRun) return

    try {
      const response = await fetch(`/api/payroll/runs/${selectedRun._id}/payslips`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeIds: [employeeId]
        }),
      })

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const data = await response.json()
      if (data.success) {
        toast({
          title: "Payslip Generated",
          description: `Payslip for ${employeeName} has been generated successfully.`,
        })

        // Refresh employees to update payslip status
        fetchEmployees(selectedRun._id)
      }
    } catch (error) {
      console.error('Error generating payslip:', error)
    }
  }

  // Load payroll runs on component mount
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchPayrollRuns()
    }
  }, [isAuthenticated, user])

  // Show loading state
  if (isLoading) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardShell>
    )
  }

  // Show authentication required
  if (!isAuthenticated || !user) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <h3 className="text-lg font-medium">Authentication Required</h3>
            <p className="text-muted-foreground">Please log in to access payslips.</p>
          </div>
        </div>
      </DashboardShell>
    )
  }

  return (
    <>
      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          isActionLoading={isActionLoading}
          loadingAction={loadingAction}
          onAction={async (action) => {
            setActionLoading(action)
            try {
              if (action === 'retry') {
                if (state.selectedPayrollRun) {
                  await fetchEmployees(state.selectedPayrollRun)
                } else {
                  await fetchPayrollRuns()
                }
              }
            } finally {
              setActionLoading(undefined)
            }
          }}
        />
      )}

      <DashboardShell>
        <DashboardHeader
          heading="Payslips Management"
          text="Generate, view, and manage employee payslips for payroll runs"
        />

        <div className="space-y-6">
          {/* Payroll Run Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Select Payroll Run
              </CardTitle>
              <CardDescription>
                Choose a payroll run to view and manage its payslips
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Select
                    value={state.selectedPayrollRun}
                    onValueChange={handlePayrollRunChange}
                    disabled={state.isLoadingRuns}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a payroll run" />
                    </SelectTrigger>
                    <SelectContent>
                      {state.payrollRuns.map((run) => (
                        <SelectItem key={run._id} value={run._id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{run.name}</span>
                            <Badge variant={
                              run.status === 'approved' ? 'default' :
                              run.status === 'completed' ? 'secondary' :
                              run.status === 'paid' ? 'default' :
                              'outline'
                            }>
                              {run.status}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {state.isLoadingRuns && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Loading runs...
                  </div>
                )}
              </div>

              {selectedRun && (
                <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Period:</span>
                      <div className="text-muted-foreground">
                        {format(new Date(selectedRun.payPeriod.year, selectedRun.payPeriod.month - 1), 'MMMM yyyy')}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">Employees:</span>
                      <div className="text-muted-foreground">
                        {selectedRun.totalEmployees}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">Status:</span>
                      <div>
                        <Badge variant={
                          selectedRun.status === 'approved' ? 'default' :
                          selectedRun.status === 'completed' ? 'secondary' :
                          selectedRun.status === 'paid' ? 'default' :
                          'outline'
                        }>
                          {selectedRun.status}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">Total Net:</span>
                      <div className="text-muted-foreground">
                        {selectedRun.currency} {selectedRun.totalNetSalary.toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation & Quick Actions */}
          {selectedRun && (
            <Card>
              <CardHeader>
                <CardTitle>Payslip Actions</CardTitle>
                <CardDescription>
                  Manage payslips for the selected payroll run
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <Link href="/dashboard/payroll/payslips/bulk-delete">
                    <Button variant="outline">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Advanced Bulk Delete
                    </Button>
                  </Link>

                  {state.selectedPayslips.length > 0 && (
                    <Button
                      variant="destructive"
                      onClick={() => setState(prev => ({ ...prev, bulkDeleteDialogOpen: true }))}
                      disabled={state.isDeleting}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Selected ({state.selectedPayslips.length})
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bulk Actions */}
          {selectedRun && (
            <BulkPayslipActions
              payrollRun={{
                id: selectedRun._id,
                name: selectedRun.name,
                status: selectedRun.status,
                payPeriod: {
                  month: selectedRun.payPeriod.month,
                  year: selectedRun.payPeriod.year
                }
              }}
            />
          )}

          {/* Employees Table */}
          {state.selectedPayrollRun && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Employees & Payslips
                </CardTitle>
                <CardDescription>
                  Manage individual employee payslips for the selected payroll run
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search employees..."
                      value={state.searchTerm}
                      onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                      className="pl-10"
                    />
                  </div>

                  {state.isLoadingEmployees && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading employees...
                    </div>
                  )}
                </div>

                {/* Employees Table */}
                {state.isLoadingEmployees ? (
                  <div className="flex items-center justify-center h-32">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredEmployees.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium">No Employees Found</h3>
                    <p className="text-muted-foreground">
                      {state.searchTerm
                        ? "No employees match your search criteria."
                        : "No employees found for this payroll run."
                      }
                    </p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-12">
                            <Checkbox
                              checked={
                                state.selectedPayslips.length > 0 &&
                                state.selectedPayslips.length === filteredEmployees.filter(emp => emp.hasPayslip).length
                              }
                              onCheckedChange={handleSelectAll}
                              aria-label="Select all payslips"
                            />
                          </TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Position</TableHead>
                          <TableHead>Payroll Status</TableHead>
                          <TableHead>Payslip Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredEmployees.map((employee) => (
                          <TableRow key={employee._id}>
                            <TableCell>
                              {employee.hasPayslip && employee.payslipId ? (
                                <Checkbox
                                  checked={state.selectedPayslips.includes(employee.payslipId)}
                                  onCheckedChange={(checked) =>
                                    handleSelectPayslip(employee.payslipId!, checked as boolean)
                                  }
                                  aria-label={`Select payslip for ${employee.firstName} ${employee.lastName}`}
                                />
                              ) : (
                                <div className="w-4 h-4" /> // Placeholder for alignment
                              )}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {employee.firstName} {employee.lastName}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {employee.employeeNumber}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{employee.department.name}</TableCell>
                            <TableCell>{employee.position}</TableCell>
                            <TableCell>
                              {employee.hasPayrollRecord ? (
                                <Badge variant={
                                  employee.payrollStatus === 'approved' ? 'default' :
                                  employee.payrollStatus === 'paid' ? 'default' :
                                  employee.payrollStatus === 'draft' ? 'secondary' :
                                  'outline'
                                }>
                                  {employee.payrollStatus === 'not_processed' ? 'Not Processed' :
                                   employee.payrollStatus.charAt(0).toUpperCase() + employee.payrollStatus.slice(1)}
                                </Badge>
                              ) : (
                                <Badge variant="outline">Not Processed</Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              {employee.hasPayslip ? (
                                <Badge variant="default">
                                  {employee.payslipStatus || 'Generated'}
                                </Badge>
                              ) : (
                                <Badge variant="outline">Not Generated</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>

                                  {/* Payslip Actions */}
                                  {employee.hasPayslip ? (
                                    <>
                                      <DropdownMenuItem
                                        onClick={() => handleDownloadPayslip(
                                          employee.payslipId!,
                                          `${employee.firstName} ${employee.lastName}`
                                        )}
                                      >
                                        <Download className="mr-2 h-4 w-4" />
                                        Download Payslip
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Eye className="mr-2 h-4 w-4" />
                                        View Payslip
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Mail className="mr-2 h-4 w-4" />
                                        Email Payslip
                                      </DropdownMenuItem>
                                    </>
                                  ) : employee.hasPayrollRecord ? (
                                    <DropdownMenuItem
                                      onClick={() => handleGeneratePayslip(
                                        employee._id,
                                        `${employee.firstName} ${employee.lastName}`
                                      )}
                                    >
                                      <Plus className="mr-2 h-4 w-4" />
                                      Generate Payslip
                                    </DropdownMenuItem>
                                  ) : (
                                    <DropdownMenuItem disabled>
                                      <Plus className="mr-2 h-4 w-4" />
                                      Process Payroll First
                                    </DropdownMenuItem>
                                  )}

                                  <DropdownMenuSeparator />

                                  {/* Employee Actions */}
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Employee Details
                                  </DropdownMenuItem>

                                  {/* Payroll Actions */}
                                  {!employee.hasPayrollRecord && (
                                    <DropdownMenuItem>
                                      <FileText className="mr-2 h-4 w-4" />
                                      Process Payroll
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Bulk Delete Confirmation Dialog */}
        <AlertDialog open={state.bulkDeleteDialogOpen} onOpenChange={(open) =>
          setState(prev => ({ ...prev, bulkDeleteDialogOpen: open }))
        }>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Bulk Delete</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete {state.selectedPayslips.length} selected payslip(s)?
                This action cannot be undone and will also update the corresponding payroll records.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={state.isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleBulkDelete}
                disabled={state.isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {state.isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DashboardShell>
    </>
  )
}

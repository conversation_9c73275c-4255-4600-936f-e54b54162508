import { Metadata } from 'next';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PayslipBulkDeleteForm } from '@/components/payroll/payslips/payslip-bulk-delete-form';
import { PayslipNav } from '@/components/payroll/payslips/payslip-nav';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Bulk Delete Payslips',
  description: 'Delete multiple payslip records at once',
};

export default function BulkDeletePayslipsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Bulk Delete Payslips"
        text="Delete multiple payslip records at once"
      />

      <div className="grid gap-8 md:grid-cols-[200px_1fr]">
        <aside className="hidden w-[200px] flex-col md:flex">
          <PayslipNav />
        </aside>

        <div className="flex flex-col space-y-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              This action permanently deletes payslip records and updates corresponding payroll records.
              This cannot be undone. Make sure you have a backup or export of any data you want to keep.
            </AlertDescription>
          </Alert>

          <Card>
            <CardHeader>
              <CardTitle>Bulk Delete Options</CardTitle>
              <CardDescription>
                Delete payslips by entering their IDs or using filter criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PayslipBulkDeleteForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  );
}

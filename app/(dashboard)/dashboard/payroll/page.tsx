import { Metada<PERSON> } from "next"
import Link from "next/link"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { IncompletePayrollRuns } from "@/components/payroll/payroll-run/incomplete-payroll-runs"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Calendar,
  CreditCard,
  DollarSign,
  FileText,
  PlusCircle,
  Users,
} from "lucide-react"

export const metadata: Metadata = {
  title: "Payroll Dashboard",
  description: "Manage payroll processing, salary structures, and employee payments",
}

export default function PayrollDashboardPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Dashboard"
        text="Manage payroll processing, salary structures, and employee payments"
      />

      {/* Incomplete Payroll Runs Section */}
      <div className="mb-6">
        <IncompletePayrollRuns />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Payroll Runs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payroll Runs</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Payroll Processing</div>
            <p className="text-xs text-muted-foreground">
              Create and manage payroll runs for your organization
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/previous-runs" className="w-full">
              <Button className="w-full">
                <Calendar className="mr-2 h-4 w-4" />
                Manage Payroll Runs
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Payslips */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payslips</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Employee Payslips</div>
            <p className="text-xs text-muted-foreground">
              Generate, view, and distribute employee payslips
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/payslips" className="w-full">
              <Button className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Manage Payslips
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Salary Structures */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Salary Structures</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Salary Management</div>
            <p className="text-xs text-muted-foreground">
              Define and manage salary structures for different positions
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/salary-structures" className="w-full">
              <Button className="w-full">
                <DollarSign className="mr-2 h-4 w-4" />
                Manage Salary Structures
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Employee Salaries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Employee Salaries</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Employee Compensation</div>
            <p className="text-xs text-muted-foreground">
              Manage individual employee salary details and history
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/employee-salaries" className="w-full">
              <Button className="w-full">
                <Users className="mr-2 h-4 w-4" />
                Manage Employee Salaries
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Allowances & Deductions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Allowances & Deductions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Compensation Components</div>
            <p className="text-xs text-muted-foreground">
              Manage allowances, deductions, and other salary components
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/compensation" className="w-full">
              <Button className="w-full">
                <CreditCard className="mr-2 h-4 w-4" />
                Manage Components
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Create Payroll Run */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
            <PlusCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Create Payroll Run</div>
            <p className="text-xs text-muted-foreground">
              Start a new payroll run for the current period
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/payroll/run" className="w-full">
              <Button className="w-full">
                <PlusCircle className="mr-2 h-4 w-4" />
                New Payroll Run
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </DashboardShell>
  )
}

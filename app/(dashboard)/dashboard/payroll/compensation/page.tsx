"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/frontend/hooks/useAuth'
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  FileText,
  Plus,
  RefreshCw,
  TrendingUp,
  Upload,
  Users,
  Gift,
  AlertTriangle,
  Info,
  Search,
  UserCheck
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

// TypeScript interfaces
interface CompensationRecord {
  _id: string
  employeeId: {
    _id: string
    firstName: string
    lastName: string
    email: string
    department?: { name: string }
  }
  compensationType: string
  amount: number
  effectiveDate: string
  description: string
  notes?: string
  currency: string
  status: 'pending' | 'approved' | 'paid' | 'cancelled'
  isRecurring: boolean
  frequency?: string
  endDate?: string
  taxable: boolean
  pensionable: boolean
  createdAt: string
  createdBy: { firstName: string; lastName: string }
}

interface Employee {
  _id: string
  employeeId: string
  firstName: string
  lastName: string
  email: string
  position: string
  departmentId?: {
    _id: string
    name: string
  }
  employmentStatus: string
}

interface Allowance {
  _id: string
  name: string
  code: string
  description?: string
  isActive: boolean
  isTaxable: boolean
  isPensionable: boolean
}

interface UploadResult {
  totalRows: number
  successCount: number
  errorCount: number
  skippedCount: number
  errors: Array<{
    row: number
    error: string
    employeeInfo?: string
  }>
  skipped: Array<{
    row: number
    reason: string
    employeeInfo?: string
  }>
  results: Array<{
    row: number
    employeeId: string
    employeeName: string
    compensationType: string
    amount: number
    status: string
    message?: string
    recordId?: string
  }>
}

export default function CompensationPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const [activeTab, setActiveTab] = useState('records')

  // State for compensation records
  const [records, setRecords] = useState<CompensationRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)

  // State for bulk operations
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)

  // State for employee selection modal
  const [showEmployeeSelectionModal, setShowEmployeeSelectionModal] = useState(false)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([])
  const [selectedEmployees, setSelectedEmployees] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [loadingEmployees, setLoadingEmployees] = useState(false)
  const [allowances, setAllowances] = useState<Allowance[]>([])
  const [downloadingTemplate, setDownloadingTemplate] = useState(false)

  // Load data on component mount
  useEffect(() => {
    if (isAuthenticated && user) {
      loadRecords()
    }
  }, [isAuthenticated, user])

  const loadRecords = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/payroll/compensation?limit=50&sort=-createdAt')
      if (response.ok) {
        const data = await response.json()
        setRecords(data.data || [])
      } else {
        console.error('API Error:', response.status, response.statusText)
        toast.error('Failed to load compensation records')
      }
    } catch (error) {
      console.error('Failed to load compensation records:', error)
      toast.error('Failed to load compensation records')
    } finally {
      setLoading(false)
    }
  }

  // Load employees for template generation
  const loadEmployees = async () => {
    setLoadingEmployees(true)
    try {
      const response = await fetch('/api/hr/employees?status=active&limit=1000')
      if (response.ok) {
        const data = await response.json()
        const employeeList = data.docs || data.data || []
        setEmployees(employeeList)
        setFilteredEmployees(employeeList)
      } else {
        toast.error('Failed to load employees')
      }
    } catch (error) {
      console.error('Failed to load employees:', error)
      toast.error('Failed to load employees')
    } finally {
      setLoadingEmployees(false)
    }
  }

  // Load allowances for compensation types
  const loadAllowances = async () => {
    try {
      const response = await fetch('/api/payroll/allowances?limit=1000&isActive=true')
      if (response.ok) {
        const data = await response.json()
        const allowanceList = data.data?.docs || data.docs || []
        setAllowances(allowanceList)
      }
    } catch (error) {
      console.error('Failed to load allowances:', error)
    }
  }

  // Filter employees based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredEmployees(employees)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = employees.filter(emp =>
        emp.firstName.toLowerCase().includes(query) ||
        emp.lastName.toLowerCase().includes(query) ||
        emp.email.toLowerCase().includes(query) ||
        emp.employeeId.toLowerCase().includes(query) ||
        (emp.departmentId?.name || '').toLowerCase().includes(query)
      )
      setFilteredEmployees(filtered)
    }
  }, [searchQuery, employees])

  // Handle opening employee selection modal
  const handleOpenEmployeeSelection = async () => {
    setShowEmployeeSelectionModal(true)
    if (employees.length === 0) {
      await loadEmployees()
    }
    if (allowances.length === 0) {
      await loadAllowances()
    }
  }

  // Handle employee selection
  const handleEmployeeSelect = (employeeId: string, checked: boolean) => {
    const newSelected = new Set(selectedEmployees)
    if (checked) {
      newSelected.add(employeeId)
    } else {
      newSelected.delete(employeeId)
    }
    setSelectedEmployees(newSelected)
  }

  // Handle select all employees
  const handleSelectAllEmployees = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(filteredEmployees.map(emp => emp._id))
      setSelectedEmployees(allIds)
    } else {
      setSelectedEmployees(new Set())
    }
  }

  // Handle template download with selected employees
  const handleDownloadTemplate = async () => {
    if (selectedEmployees.size === 0) {
      toast.error('Please select at least one employee')
      return
    }

    setDownloadingTemplate(true)
    try {
      const employeeIds = Array.from(selectedEmployees)
      const response = await fetch('/api/payroll/compensation/template', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ employeeIds })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `compensation_template_${selectedEmployees.size}_employees.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success(`Template downloaded with ${selectedEmployees.size} employees`)
        setShowEmployeeSelectionModal(false)
        setSelectedEmployees(new Set())
        setSearchQuery('')
      } else {
        toast.error('Failed to download template')
      }
    } catch (error) {
      console.error('Template download error:', error)
      toast.error('Failed to download template')
    } finally {
      setDownloadingTemplate(false)
    }
  }

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file')
      return
    }

    setProcessing(true)
    setUploadProgress(0)
    setUploadResult(null)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 500)

      const response = await fetch('/api/payroll/compensation/bulk-import', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      const result = await response.json()

      if (result.success) {
        setUploadResult(result.data)
        toast.success(result.message)
        loadRecords() // Refresh the records
      } else {
        toast.error('Failed to import compensation data')
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Failed to import compensation data')
    } finally {
      setProcessing(false)
    }
  }

  // Get status badge
  const getStatusBadge = (status: CompensationRecord['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>
      case 'paid':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Paid</Badge>
      case 'cancelled':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Get compensation type badge
  const getCompensationTypeBadge = (type: string) => {
    const typeConfig = {
      performance_bonus: { label: 'Performance Bonus', color: 'bg-green-100 text-green-800' },
      holiday_bonus: { label: 'Holiday Bonus', color: 'bg-purple-100 text-purple-800' },
      overtime: { label: 'Overtime', color: 'bg-blue-100 text-blue-800' },
      special_allowance: { label: 'Special Allowance', color: 'bg-orange-100 text-orange-800' },
      one_time_deduction: { label: 'One-time Deduction', color: 'bg-red-100 text-red-800' },
      retroactive_adjustment: { label: 'Retroactive Adjustment', color: 'bg-indigo-100 text-indigo-800' }
    }

    const config = typeConfig[type as keyof typeof typeConfig] || { label: type, color: 'bg-gray-100 text-gray-800' }
    return <Badge variant="secondary" className={config.color}>{config.label}</Badge>
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'MWK') => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  if (isLoading || !isAuthenticated || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading compensation records...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Compensation Management"
        text="Manage bonuses, overtime, and other compensation adjustments"
      >
        <div className="flex items-center space-x-2">
          <Button onClick={loadRecords} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button onClick={handleOpenEmployeeSelection} variant="outline">
            <UserCheck className="h-4 w-4 mr-2" />
            Generate Template
          </Button>

          <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
              <DialogHeader>
                <DialogTitle>Bulk Import Compensation Data</DialogTitle>
                <DialogDescription>
                  Upload an Excel file with compensation data (bonuses, overtime, allowances, deductions)
                </DialogDescription>
              </DialogHeader>

              <div className="flex-1 overflow-y-auto space-y-4">

                <div>
                  <Label htmlFor="file">Excel File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      setSelectedFile(e.target.files?.[0] || null)
                      setUploadResult(null)
                      setUploadProgress(0)
                    }}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Upload an Excel file with compensation data. Download the template first for the correct format.
                  </p>
                </div>

                {uploadProgress > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Upload Progress</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {uploadResult && (
                  <Alert variant={uploadResult.errorCount > 0 ? 'destructive' : uploadResult.skippedCount > 0 ? 'default' : 'default'}>
                    <CheckCircle className="h-4 w-4" />
                    <AlertTitle>Upload Complete</AlertTitle>
                    <AlertDescription>
                      <p>Total rows: {uploadResult.totalRows}</p>
                      <p>Successfully imported: {uploadResult.successCount}</p>
                      <p>Skipped: {uploadResult.skippedCount}</p>
                      <p>Errors: {uploadResult.errorCount}</p>
                      
                      {uploadResult.skippedCount > 0 && (
                        <div className="mt-2">
                          <p className="font-semibold">Skipped items:</p>
                          <ul className="text-xs list-disc pl-5">
                            {uploadResult.skipped.slice(0, 5).map((skipped, index: number) => (
                              <li key={index}>
                                Row {skipped.row}: {skipped.reason}
                              </li>
                            ))}
                            {uploadResult.skipped.length > 5 && (
                              <li>...and {uploadResult.skipped.length - 5} more skipped items</li>
                            )}
                          </ul>
                        </div>
                      )}
                      
                      {uploadResult.errorCount > 0 && (
                        <div className="mt-2">
                          <p className="font-semibold">Error details:</p>
                          <ul className="text-xs list-disc pl-5">
                            {uploadResult.errors.slice(0, 5).map((error, index: number) => (
                              <li key={index}>
                                Row {error.row}: {error.error}
                              </li>
                            ))}
                            {uploadResult.errors.length > 5 && (
                              <li>...and {uploadResult.errors.length - 5} more errors</li>
                            )}
                          </ul>
                        </div>
                      )}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setShowImportDialog(false)
                  setSelectedFile(null)
                  setUploadResult(null)
                  setUploadProgress(0)
                }}>
                  Close
                </Button>
                {!uploadResult && (
                  <Button onClick={handleFileUpload} disabled={!selectedFile || processing}>
                    {processing ? 'Importing...' : 'Import'}
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Employee Selection Modal */}
          <Dialog open={showEmployeeSelectionModal} onOpenChange={setShowEmployeeSelectionModal}>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <UserCheck className="h-5 w-5 mr-2" />
                  Select Employees for Compensation Template
                </DialogTitle>
                <DialogDescription>
                  Choose employees to include in the compensation template. Selected employees will be pre-filled with their information.
                </DialogDescription>
              </DialogHeader>

              <div className="flex-1 flex flex-col space-y-4 overflow-hidden">
                {/* Search Bar */}
                <div className="flex items-center space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by name, email, employee ID, or department..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {selectedEmployees.size} of {filteredEmployees.length} selected
                  </div>
                </div>

                {/* Employee Table */}
                <div className="flex-1 border rounded-md overflow-hidden">
                  {loadingEmployees ? (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p>Loading employees...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="overflow-auto max-h-96">
                      <Table>
                        <TableHeader className="sticky top-0 bg-background">
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={filteredEmployees.length > 0 && selectedEmployees.size === filteredEmployees.length}
                                onCheckedChange={handleSelectAllEmployees}
                                indeterminate={selectedEmployees.size > 0 && selectedEmployees.size < filteredEmployees.length}
                              />
                            </TableHead>
                            <TableHead>Employee ID</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Position</TableHead>
                            <TableHead>Department</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredEmployees.map((employee) => (
                            <TableRow key={employee._id}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedEmployees.has(employee._id)}
                                  onCheckedChange={(checked) => handleEmployeeSelect(employee._id, checked as boolean)}
                                />
                              </TableCell>
                              <TableCell className="font-medium">{employee.employeeId}</TableCell>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{employee.firstName} {employee.lastName}</div>
                                </div>
                              </TableCell>
                              <TableCell>{employee.email}</TableCell>
                              <TableCell>{employee.position}</TableCell>
                              <TableCell>{employee.departmentId?.name || 'N/A'}</TableCell>
                              <TableCell>
                                <Badge variant={employee.employmentStatus === 'active' ? 'default' : 'secondary'}>
                                  {employee.employmentStatus}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>

                      {filteredEmployees.length === 0 && !loadingEmployees && (
                        <div className="text-center py-8 text-muted-foreground">
                          {searchQuery ? 'No employees found matching your search' : 'No employees available'}
                        </div>
                      )}
                    </div>
                  )}
                </div>


              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEmployeeSelectionModal(false)
                    setSelectedEmployees(new Set())
                    setSearchQuery('')
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleDownloadTemplate}
                  disabled={selectedEmployees.size === 0 || downloadingTemplate}
                >
                  {downloadingTemplate ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Download Template ({selectedEmployees.size} employees)
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </DashboardHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{records.length}</div>
            <p className="text-xs text-muted-foreground">All compensation records</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {records.filter(r => r.status === 'pending').length}
            </div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(records.reduce((sum, r) => sum + r.amount, 0))}
            </div>
            <p className="text-xs text-muted-foreground">All compensation</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {records.filter(r => {
                const recordDate = new Date(r.effectiveDate)
                const now = new Date()
                return recordDate.getMonth() === now.getMonth() && recordDate.getFullYear() === now.getFullYear()
              }).length}
            </div>
            <p className="text-xs text-muted-foreground">Records this month</p>
          </CardContent>
        </Card>
      </div>

      {/* Records Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Gift className="h-5 w-5 mr-2" />
            Compensation Records
          </CardTitle>
          <CardDescription>
            Recent compensation adjustments, bonuses, and overtime payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading compensation records...</p>
            </div>
          ) : records.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Gift className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No compensation records found</p>
              <p className="text-sm">Upload compensation data or create records to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Effective Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Recurring</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.map((record) => (
                  <TableRow key={record._id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {record.employeeId.firstName} {record.employeeId.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {record.employeeId.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {record.employeeId.department?.name || 'N/A'}
                    </TableCell>
                    <TableCell>
                      {getCompensationTypeBadge(record.compensationType)}
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${record.compensationType === 'one_time_deduction' ? 'text-red-600' : 'text-green-600'}`}>
                        {record.compensationType === 'one_time_deduction' ? '-' : '+'}
                        {formatCurrency(record.amount, record.currency)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {format(new Date(record.effectiveDate), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(record.status)}
                    </TableCell>
                    <TableCell>
                      {record.isRecurring ? (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {record.frequency}
                        </Badge>
                      ) : (
                        <Badge variant="outline">One-time</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {format(new Date(record.createdAt), 'MMM dd, yyyy')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          by {record.createdBy.firstName} {record.createdBy.lastName}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

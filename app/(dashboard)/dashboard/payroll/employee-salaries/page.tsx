// app/(dashboard)/dashboard/payroll/employee-salaries/page.tsx
"use client"

import { EmployeeSalaryManager } from "@/components/payroll/employee-salary/employee-salary-manager"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"

export default function EmployeeSalariesPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Employee Salaries" 
        text="Manage individual employee salary configurations, allowances, and deductions"
      />
      <div className="space-y-6">
        <EmployeeSalaryManager />
      </div>
    </DashboardShell>
  )
}

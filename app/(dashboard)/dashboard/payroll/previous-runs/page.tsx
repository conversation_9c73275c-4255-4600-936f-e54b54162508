"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/frontend/hooks/useAuth'
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Eye,
  Filter,
  RefreshCw,
  Search,
  Users,
  DollarSign,
  FileText,
  MoreHorizontal,
  Edit,
  Trash2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import Link from "next/link"

// TypeScript interfaces based on the PayrollRun model
interface PayrollRunUser {
  _id: string
  firstName: string
  lastName: string
  email: string
}

interface PayrollRunDepartment {
  _id: string
  name: string
}

interface PayrollRunPayPeriod {
  month: number
  year: number
  startDate: string
  endDate: string
}

interface PayrollRun {
  _id: string
  name: string
  description?: string
  payPeriod: PayrollRunPayPeriod
  status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  totalEmployees: number
  processedEmployees: number
  totalGrossSalary: number
  totalDeductions: number
  totalTax: number
  totalNetSalary: number
  currency: string
  notes?: string
  departments?: PayrollRunDepartment[]
  createdBy: PayrollRunUser
  updatedBy?: PayrollRunUser
  approvedBy?: PayrollRunUser
  approvedAt?: string
  processedAt?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
}

interface PayrollRunsResponse {
  success: boolean
  data: {
    payrollRuns: PayrollRun[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
  error?: string
}

interface PayrollRunFilters {
  status: string
  year: string
  month: string
  search: string
  page: number
  limit: number
}

export default function PreviousPayrollRunsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const [payrollRuns, setPayrollRuns] = useState<PayrollRun[]>([])
  const [loading, setLoading] = useState(false)
  const [totalCount, setTotalCount] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  const [filters, setFilters] = useState<PayrollRunFilters>({
    status: 'all',
    year: 'all',
    month: 'all',
    search: '',
    page: 1,
    limit: 20,
  })

  // Delete state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [payrollRunToDelete, setPayrollRunToDelete] = useState<PayrollRun | null>(null)
  const [deleting, setDeleting] = useState(false)

  // Fetch payroll runs
  const fetchPayrollRuns = async () => {
    setLoading(true)
    try {
      const queryParams = new URLSearchParams()

      // Map frontend filters to API parameters
      if (filters.status && filters.status !== 'all') {
        queryParams.append('status', filters.status)
      }
      if (filters.year && filters.year !== 'all' && filters.month && filters.month !== 'all') {
        queryParams.append('period', `${filters.year}-${filters.month}`)
      }
      if (filters.search) {
        // The API doesn't support search directly, we'll handle this client-side for now
        // or we can extend the API to support search
      }
      queryParams.append('page', filters.page.toString())
      queryParams.append('limit', filters.limit.toString())
      queryParams.append('sortBy', 'createdAt')
      queryParams.append('sortOrder', 'desc')

      const response = await fetch(`/api/payroll/runs?${queryParams}`)
      const data: PayrollRunsResponse = await response.json()

      if (data.success) {
        // Handle both possible response structures
        if (data.data.payrollRuns) {
          setPayrollRuns(data.data.payrollRuns)
          setTotalCount(data.data.pagination.total)
          setTotalPages(data.data.pagination.totalPages)
          setCurrentPage(data.data.pagination.page)
        } else {
          // Fallback for direct docs response
          setPayrollRuns((data.data as any).docs || [])
          setTotalCount((data.data as any).totalDocs || 0)
          setTotalPages((data.data as any).totalPages || 0)
          setCurrentPage((data.data as any).page || 1)
        }
      } else {
        console.error('Failed to fetch payroll runs:', data.error)
      }
    } catch (error) {
      console.error('Error fetching payroll runs:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load data on component mount and filter changes
  useEffect(() => {
    if (isAuthenticated) {
      fetchPayrollRuns()
    }
  }, [filters, isAuthenticated])

  // Handle filter changes
  const handleFilterChange = (key: keyof PayrollRunFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : (typeof value === 'number' ? value : parseInt(value.toString()) || 1), // Reset to page 1 when other filters change
    }))
  }

  // Handle search
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, page: 1 }))
  }

  // Clear filters
  const clearFilters = () => {
    setFilters({
      status: 'all',
      year: 'all',
      month: 'all',
      search: '',
      page: 1,
      limit: 20,
    })
  }

  // Get status badge color and text
  const getStatusBadge = (status: PayrollRun['status']) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'processing':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Approved</Badge>
      case 'paid':
        return <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">Paid</Badge>
      case 'cancelled':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'MWK') => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  // Format month name
  const getMonthName = (month: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]
    return months[month - 1] || 'Unknown'
  }

  // Check if payroll run can be deleted - now allows all statuses with appropriate warnings
  const canDeletePayrollRun = (status: PayrollRun['status']) => {
    // Allow deletion of all statuses but with different warning levels
    return true
  }

  // Get delete warning level and message based on status
  const getDeleteWarning = (status: PayrollRun['status']) => {
    switch (status) {
      case 'draft':
        return {
          level: 'low',
          title: 'Delete Draft Payroll Run',
          message: 'This is a draft payroll run that has not been processed.',
          consequences: [
            'The draft payroll run will be permanently deleted',
            'No financial records have been created yet',
            'This action is relatively safe to perform'
          ]
        }
      case 'cancelled':
        return {
          level: 'low',
          title: 'Delete Cancelled Payroll Run',
          message: 'This payroll run was cancelled and can be safely deleted.',
          consequences: [
            'The cancelled payroll run will be permanently deleted',
            'Any associated records will be removed',
            'This will clean up cancelled data'
          ]
        }
      case 'completed':
        return {
          level: 'medium',
          title: 'Delete Completed Payroll Run',
          message: 'This payroll run has been completed but not yet approved.',
          consequences: [
            'All payroll calculations will be lost',
            'Generated payslips will be deleted',
            'Employees may need to be re-processed'
          ]
        }
      case 'processing':
        return {
          level: 'high',
          title: 'Delete Processing Payroll Run',
          message: 'WARNING: This payroll run is currently being processed!',
          consequences: [
            'May cause data corruption if processing is interrupted',
            'Partial calculations will be lost',
            'System may become unstable',
            'RECOMMENDED: Cancel the run first, then delete'
          ]
        }
      case 'approved':
        return {
          level: 'critical',
          title: 'Delete Approved Payroll Run',
          message: 'CRITICAL: This payroll run has been approved for payment!',
          consequences: [
            'Approved financial records will be deleted',
            'May violate audit trail requirements',
            'Could affect accounting reconciliation',
            'Employee payment records will be lost',
            'RECOMMENDED: Consult with Finance Director before proceeding'
          ]
        }
      case 'paid':
        return {
          level: 'critical',
          title: 'Delete Paid Payroll Run',
          message: 'CRITICAL: This payroll run has been paid to employees!',
          consequences: [
            'Payment records will be permanently deleted',
            'Bank transfer records may become orphaned',
            'Tax reporting may be affected',
            'Employee payment history will be lost',
            'May violate financial regulations',
            'STRONGLY RECOMMENDED: Do not delete paid runs'
          ]
        }
      default:
        return {
          level: 'medium',
          title: 'Delete Payroll Run',
          message: 'This payroll run will be permanently deleted.',
          consequences: [
            'All associated data will be removed',
            'This action cannot be undone'
          ]
        }
    }
  }

  // Handle delete payroll run
  const handleDeletePayrollRun = (payrollRun: PayrollRun) => {
    setPayrollRunToDelete(payrollRun)
    setDeleteDialogOpen(true)
  }

  // Confirm delete payroll run
  const confirmDeletePayrollRun = async () => {
    if (!payrollRunToDelete) return

    setDeleting(true)
    try {
      const response = await fetch(`/api/payroll/runs/${payrollRunToDelete._id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: `Payroll run "${payrollRunToDelete.name}" and its associated records have been deleted successfully.`,
        })

        // Refresh the payroll runs list
        await fetchPayrollRuns()
      } else {
        toast({
          title: "Error",
          description: data.error || 'Failed to delete payroll run',
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error deleting payroll run:', error)
      toast({
        title: "Error",
        description: 'An unexpected error occurred while deleting the payroll run',
        variant: "destructive",
      })
    } finally {
      setDeleting(false)
      setDeleteDialogOpen(false)
      setPayrollRunToDelete(null)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Previous Payroll Runs"
        text="View and manage historical payroll processing records"
      >
        <div className="flex items-center space-x-2">
          <Button onClick={fetchPayrollRuns} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button asChild>
            <Link href="/dashboard/payroll/run">
              <Calendar className="h-4 w-4 mr-2" />
              New Payroll Run
            </Link>
          </Button>
        </div>
      </DashboardHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Runs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
            <p className="text-xs text-muted-foreground">
              All payroll runs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Runs</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {payrollRuns.filter(run => ['completed', 'approved', 'paid'].includes(run.status)).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {payrollRuns.reduce((sum, run) => sum + run.totalEmployees, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all runs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payrollRuns.reduce((sum, run) => sum + run.totalNetSalary, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Net salary total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <Select value={filters.year} onValueChange={(value) => handleFilterChange('year', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All years" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All years</SelectItem>
                  <SelectItem value="2024">2024</SelectItem>
                  <SelectItem value="2023">2023</SelectItem>
                  <SelectItem value="2022">2022</SelectItem>
                  <SelectItem value="2021">2021</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="month">Month</Label>
              <Select value={filters.month} onValueChange={(value) => handleFilterChange('month', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All months" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All months</SelectItem>
                  <SelectItem value="1">January</SelectItem>
                  <SelectItem value="2">February</SelectItem>
                  <SelectItem value="3">March</SelectItem>
                  <SelectItem value="4">April</SelectItem>
                  <SelectItem value="5">May</SelectItem>
                  <SelectItem value="6">June</SelectItem>
                  <SelectItem value="7">July</SelectItem>
                  <SelectItem value="8">August</SelectItem>
                  <SelectItem value="9">September</SelectItem>
                  <SelectItem value="10">October</SelectItem>
                  <SelectItem value="11">November</SelectItem>
                  <SelectItem value="12">December</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search runs..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4 mt-4">
            <Button onClick={handleSearch}>Search</Button>
            <Button variant="outline" onClick={clearFilters}>Clear Filters</Button>
          </div>
        </CardContent>
      </Card>

      {/* Payroll Runs Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Payroll Runs</CardTitle>
              <CardDescription>
                Showing {payrollRuns.length} of {totalCount} payroll runs
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              Loading payroll runs...
            </div>
          ) : payrollRuns.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No payroll runs found for the selected criteria
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Run Name</TableHead>
                    <TableHead>Pay Period</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Employees</TableHead>
                    <TableHead>Gross Salary</TableHead>
                    <TableHead>Net Salary</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payrollRuns.map((run) => (
                    <TableRow key={run._id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{run.name}</div>
                          {run.description && (
                            <div className="text-sm text-muted-foreground">
                              {run.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {getMonthName(run.payPeriod.month)} {run.payPeriod.year}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(run.payPeriod.startDate), 'MMM dd')} - {format(new Date(run.payPeriod.endDate), 'MMM dd, yyyy')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(run.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>{run.processedEmployees}/{run.totalEmployees}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(run.totalGrossSalary, run.currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(run.totalNetSalary, run.currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {format(new Date(run.createdAt), 'MMM dd, yyyy')}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            by {run.createdBy.firstName} {run.createdBy.lastName}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/payroll/runs/${run._id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/payroll/payslips?runId=${run._id}`}>
                                <FileText className="mr-2 h-4 w-4" />
                                View Payslips
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download Report
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />

                            {/* Edit option for draft runs */}
                            {run.status === 'draft' && (
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Run
                              </DropdownMenuItem>
                            )}

                            {/* Delete option for all runs with status-based styling */}
                            <DropdownMenuItem
                              className={
                                ['approved', 'paid'].includes(run.status)
                                  ? "text-red-700 font-medium"
                                  : ['processing'].includes(run.status)
                                  ? "text-orange-600"
                                  : "text-red-600"
                              }
                              onClick={() => handleDeletePayrollRun(run)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Run
                              {['approved', 'paid'].includes(run.status) && (
                                <span className="ml-2 text-xs">(Critical)</span>
                              )}
                              {run.status === 'processing' && (
                                <span className="ml-2 text-xs">(Warning)</span>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFilterChange('page', Math.max(1, currentPage - 1))}
                      disabled={currentPage <= 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFilterChange('page', Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle className={
              payrollRunToDelete && ['approved', 'paid'].includes(payrollRunToDelete.status)
                ? "text-red-700"
                : payrollRunToDelete && payrollRunToDelete.status === 'processing'
                ? "text-orange-600"
                : "text-red-600"
            }>
              {payrollRunToDelete ? getDeleteWarning(payrollRunToDelete.status).title : 'Delete Payroll Run'}
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-4">
              <div>
                Are you sure you want to delete the payroll run <strong>"{payrollRunToDelete?.name}"</strong>?
              </div>

              {payrollRunToDelete && (
                <div className={
                  ['approved', 'paid'].includes(payrollRunToDelete.status)
                    ? "bg-red-50 border border-red-200 rounded-lg p-3"
                    : payrollRunToDelete.status === 'processing'
                    ? "bg-orange-50 border border-orange-200 rounded-lg p-3"
                    : "bg-yellow-50 border border-yellow-200 rounded-lg p-3"
                }>
                  <div className={
                    ['approved', 'paid'].includes(payrollRunToDelete.status)
                      ? "text-red-800 font-medium"
                      : payrollRunToDelete.status === 'processing'
                      ? "text-orange-800 font-medium"
                      : "text-yellow-800 font-medium"
                  }>
                    {getDeleteWarning(payrollRunToDelete.status).message}
                  </div>
                </div>
              )}

              <div>
                <strong>This action will permanently delete:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>The payroll run record</li>
                  <li>All associated payroll records ({payrollRunToDelete?.processedEmployees || 0} employees)</li>
                  <li>All generated payslips</li>
                  <li>Any related journal entries (if applicable)</li>
                </ul>
              </div>

              {payrollRunToDelete && (
                <div>
                  <strong>Potential consequences:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    {getDeleteWarning(payrollRunToDelete.status).consequences.map((consequence, index) => (
                      <li key={index} className={
                        consequence.includes('RECOMMENDED') || consequence.includes('CRITICAL') || consequence.includes('WARNING')
                          ? "font-medium text-red-700"
                          : ""
                      }>
                        {consequence}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="text-red-600 font-medium">
                This action cannot be undone.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeletePayrollRun}
              disabled={deleting}
              className={
                payrollRunToDelete && ['approved', 'paid'].includes(payrollRunToDelete.status)
                  ? "bg-red-700 hover:bg-red-800"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {deleting ? 'Deleting...' :
                payrollRunToDelete && ['approved', 'paid'].includes(payrollRunToDelete.status)
                  ? 'Force Delete (Critical)'
                  : 'Delete Payroll Run'
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardShell>
  )
}

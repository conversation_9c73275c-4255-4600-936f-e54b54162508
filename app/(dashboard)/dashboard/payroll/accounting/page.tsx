"use client"

import { useState } from "react"
import { FileText } from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PayrollAccountingPanel } from "@/components/payroll/accounting/payroll-accounting-panel"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function PayrollAccountingPage() {
  const [activeTab, setActiveTab] = useState("integration")
  const [showJournalEntryDialog, setShowJournalEntryDialog] = useState(false)
  const [selectedJournalEntryId, setSelectedJournalEntryId] = useState<string | null>(null)

  // Handle view journal entry
  const handleViewJournalEntry = (journalEntryId: string) => {
    setSelectedJournalEntryId(journalEntryId)
    setShowJournalEntryDialog(true)
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll & Accounting Integration"
        text="Integrate payroll with accounting system"
      />

      <Tabs
        defaultValue="integration"
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="integration">Accounting Integration</TabsTrigger>
          <TabsTrigger value="history">Transaction History</TabsTrigger>
        </TabsList>
        <TabsContent value="integration" className="space-y-4">
          <PayrollAccountingPanel onViewJournalEntry={handleViewJournalEntry} />
        </TabsContent>
        <TabsContent value="history" className="space-y-4">
          <div className="rounded-md border p-8 text-center">
            <h3 className="text-lg font-medium mb-2">Transaction History</h3>
            <p className="text-muted-foreground mb-4">
              View the history of payroll accounting transactions
            </p>
            <p className="text-sm text-muted-foreground">
              This feature will be available in a future update
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Journal Entry Dialog */}
      <Dialog open={showJournalEntryDialog} onOpenChange={setShowJournalEntryDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Journal Entry Details</DialogTitle>
            <DialogDescription>
              View the details of the journal entry
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="rounded-md border p-4">
              <h3 className="text-sm font-medium mb-2">Journal Entry ID</h3>
              <p className="text-sm font-mono">{selectedJournalEntryId}</p>
            </div>
            <p className="text-sm text-muted-foreground">
              To view the complete journal entry details, please navigate to the Accounting module.
            </p>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  // Navigate to journal entry in accounting module
                  window.open(`/dashboard/accounting/journal-entries/${selectedJournalEntryId}`, "_blank")
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                Open in Accounting Module
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}

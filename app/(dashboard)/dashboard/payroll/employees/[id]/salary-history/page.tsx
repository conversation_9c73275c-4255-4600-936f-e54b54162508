// app/(dashboard)/dashboard/payroll/employees/[id]/salary-history/page.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter, usePara<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye
} from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { mockEmployees } from "@/data/mock-employees"
import { Employee } from "@/types/employee"

// Define the mock employee type to match the structure in mock-employees.ts
interface MockEmployee {
  id: string;
  name: string;
  position: string;
  department: string;
  email: string;
  phone: string;
  address: string;
  avatar: string;
  status: string;
  joinDate: string;
  skills: string[];
  education: Array<{
    degree: string;
    institution: string;
    year: string;
  }>;
  projects: Array<{
    name: string;
    role: string;
    hours: number;
  }>;
}

// Extended employee type for UI display
interface EmployeeWithUIProps extends Employee {
  avatar?: string;
  department?: string;
}
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export default function EmployeeSalaryHistoryPage() {
  const params = useParams();
  const id = params?.id as string;
  const router = useRouter()
  const [employee, setEmployee] = useState<EmployeeWithUIProps | null>(null)
  const [loading, setLoading] = useState(true)

  // Fetch employee data
  useEffect(() => {
    // In a real app, this would be an API call
    const foundMockEmployee = mockEmployees.find(emp => (emp as any).id === id) as unknown as MockEmployee

    if (foundMockEmployee) {
      // Adapt mock data to match Employee type
      const adaptedEmployee: Employee = {
        _id: foundMockEmployee.id,
        employeeId: foundMockEmployee.id,
        firstName: foundMockEmployee.name.split(' ')[0],
        lastName: foundMockEmployee.name.split(' ')[1] || '',
        email: foundMockEmployee.email,
        phone: foundMockEmployee.phone,
        address: foundMockEmployee.address,
        position: foundMockEmployee.position,
        departmentId: foundMockEmployee.department,
        photo: foundMockEmployee.avatar,
        salary: 900000, // Mock salary amount
        employmentStatus: 'active' as const,
      }

      // Add non-standard properties for UI display
      const employeeWithUIProps: EmployeeWithUIProps = {
        ...adaptedEmployee,
        avatar: foundMockEmployee.avatar,
        department: foundMockEmployee.department,
      }

      setEmployee(employeeWithUIProps)
    } else {
      setEmployee(null)
    }

    setLoading(false)
  }, [id])

  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="Employee Salary History" text="Loading...">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </DashboardHeader>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="rounded-full bg-muted h-12 w-12" />
            <div className="space-y-2">
              <div className="h-4 w-[200px] bg-muted rounded" />
              <div className="h-4 w-[150px] bg-muted rounded" />
            </div>
          </div>
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-12 w-full bg-muted rounded" />
          ))}
        </div>
      </DashboardShell>
    )
  }

  if (!employee) {
    return (
      <DashboardShell>
        <DashboardHeader heading="Employee Salary History" text="Employee not found">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="pt-6">
            <p>The requested employee could not be found.</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => router.push('/dashboard/payroll/employees')}>
              View All Employees
            </Button>
          </CardFooter>
        </Card>
      </DashboardShell>
    )
  }

  // Mock salary history data
  const salaryHistory = [
    {
      id: '1',
      date: '2023-01-01',
      type: 'adjustment',
      previousAmount: 850000,
      newAmount: 900000,
      percentageChange: 5.88,
      reason: 'Annual salary review',
      approvedBy: 'Jane Smith'
    },
    {
      id: '2',
      date: '2022-07-15',
      type: 'promotion',
      previousAmount: 750000,
      newAmount: 850000,
      percentageChange: 13.33,
      reason: 'Promotion to Senior Developer',
      approvedBy: 'John Doe'
    },
    {
      id: '3',
      date: '2022-01-01',
      type: 'adjustment',
      previousAmount: 720000,
      newAmount: 750000,
      percentageChange: 4.17,
      reason: 'Annual salary review',
      approvedBy: 'Jane Smith'
    },
    {
      id: '4',
      date: '2021-04-01',
      type: 'initial',
      previousAmount: 0,
      newAmount: 720000,
      percentageChange: 0,
      reason: 'Initial salary on hiring',
      approvedBy: 'John Doe'
    }
  ]

  return (
    <DashboardShell>
      <DashboardHeader heading="Employee Salary History" text={`Salary history for ${employee.firstName} ${employee.lastName}`}>
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </DashboardHeader>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={employee.avatar} alt={`${employee.firstName} ${employee.lastName}`} />
              <AvatarFallback>{employee.firstName[0]}{employee.lastName[0]}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle>{employee.firstName} {employee.lastName}</CardTitle>
              <CardDescription>{employee.position}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="flex flex-col space-y-1">
              <span className="text-sm text-muted-foreground">Current Salary</span>
              <span className="text-lg font-semibold">
                <CurrencyDisplay amount={employee.salary as number} baseCurrency="MWK" />
              </span>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-sm text-muted-foreground">Department</span>
              <span className="text-lg">{employee.department}</span>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-sm text-muted-foreground">Employee ID</span>
              <span className="text-lg">{employee.employeeId}</span>
            </div>
          </div>

          <h3 className="text-lg font-semibold mb-4">Salary Change History</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Previous</TableHead>
                <TableHead>New</TableHead>
                <TableHead>Change</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Approved By</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {salaryHistory.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {new Date(item.date).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      item.type === 'promotion' ? 'default' :
                      item.type === 'adjustment' ? 'outline' :
                      'secondary'
                    }>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {item.type === 'initial' ? (
                      <span className="text-muted-foreground">-</span>
                    ) : (
                      <CurrencyDisplay amount={item.previousAmount} baseCurrency="MWK" />
                    )}
                  </TableCell>
                  <TableCell>
                    <CurrencyDisplay amount={item.newAmount} baseCurrency="MWK" />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {item.type === 'initial' ? (
                        <Minus className="h-4 w-4 text-muted-foreground" />
                      ) : item.percentageChange > 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                      {item.type === 'initial' ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        <span className={item.percentageChange > 0 ? 'text-green-500' : 'text-red-500'}>
                          {item.percentageChange > 0 ? '+' : ''}{item.percentageChange}%
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{item.reason}</TableCell>
                  <TableCell>{item.approvedBy}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/dashboard/payroll/employees')}>
            Back to Employees
          </Button>
          <Button onClick={() => router.push(`/dashboard/payroll/employees/${id}/edit`)}>
            Edit Salary
          </Button>
        </CardFooter>
      </Card>
    </DashboardShell>
  )
}

"use client"

import { EmployeeSalaryManager } from "@/components/payroll/employee-salary/employee-salary-manager"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export default function EmployeeSalariesPage() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Employee Salaries" 
        text="Manage employee salary information, allowances, and deductions"
      />
      
      <EmployeeSalaryManager />
    </DashboardShell>
  )
}

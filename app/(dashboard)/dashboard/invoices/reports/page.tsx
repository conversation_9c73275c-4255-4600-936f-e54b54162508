import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { InvoiceReportsPage } from '@/components/invoices/reports/invoice-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Invoice Reports | TCM Enterprise Business Suite',
  description: 'Generate and view invoice reports and analytics',
};

export default async function InvoiceReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
    UserRole.ACCOUNTANT
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Invoice Reports"
        text="Generate and view invoice reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <InvoiceReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { InvoiceListPage } from '@/components/invoices/invoice-list-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'All Invoices | TCM Enterprise Business Suite',
  description: 'View and manage all customer invoices',
};

export default async function AllInvoicesPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
    UserRole.ACCOUNTANT
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="All Invoices"
        text="View and manage all customer invoices"
      />
      <div className="flex-1 space-y-6">
        <InvoiceListPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { InvoicesPage } from "@/components/invoices/invoices-page";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Invoice Dashboard | TCM Enterprise Business Suite',
  description: 'Manage and track all your customer invoices',
};

export default async function InvoiceDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
    UserRole.ACCOUNTANT
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return <InvoicesPage />;
}

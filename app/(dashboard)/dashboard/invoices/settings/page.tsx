import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { InvoiceSettingsPage } from '@/components/invoices/settings/invoice-settings-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Invoice Settings | TCM Enterprise Business Suite',
  description: 'Configure invoice settings and preferences',
};

export default async function InvoiceSettingsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access settings
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Invoice Settings"
        text="Configure invoice settings and preferences"
      />
      <div className="flex-1 space-y-6">
        <InvoiceSettingsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

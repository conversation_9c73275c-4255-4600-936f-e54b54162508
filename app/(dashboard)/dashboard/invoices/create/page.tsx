import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { CreateInvoicePage } from '@/components/invoices/create-invoice-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Create Invoice | TCM Enterprise Business Suite',
  description: 'Create a new invoice for a customer',
};

export default async function CreateInvoicePageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
    UserRole.ACCOUNTANT
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Create Invoice"
        text="Create a new invoice for a customer"
      />
      <div className="flex-1 space-y-6">
        <CreateInvoicePage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

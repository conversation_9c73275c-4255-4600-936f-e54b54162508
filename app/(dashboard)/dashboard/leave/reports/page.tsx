import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveReportsPage } from '@/components/leave-management/reports/leave-reports-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Reports | TCM Enterprise Business Suite',
  description: 'Generate and view leave reports and analytics',
};

export default async function LeaveReportsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Reports"
        text="Generate and view leave reports and analytics"
      />
      <div className="flex-1 space-y-6">
        <LeaveReportsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

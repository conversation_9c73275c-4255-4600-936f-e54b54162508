import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveBalancesPage } from '@/components/leave-management/balances/leave-balances-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Balances | TCM Enterprise Business Suite',
  description: 'View and manage employee leave balances',
};

export default async function LeaveBalancesPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Balances"
        text="View and manage employee leave balances"
      />
      <div className="flex-1 space-y-6">
        <LeaveBalancesPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

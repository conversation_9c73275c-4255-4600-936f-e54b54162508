import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveSettingsPage } from '@/components/leave-management/settings/leave-settings-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Settings | TCM Enterprise Business Suite',
  description: 'Configure leave management settings and policies',
};

export default async function LeaveSettingsPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access settings
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Settings"
        text="Configure leave management settings and policies"
      />
      <div className="flex-1 space-y-6">
        <LeaveSettingsPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

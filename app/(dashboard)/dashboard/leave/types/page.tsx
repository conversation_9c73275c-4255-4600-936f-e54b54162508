import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveTypesPage } from '@/components/leave-management/types/leave-types-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Types | TCM Enterprise Business Suite',
  description: 'Manage leave types and policies',
};

export default async function LeaveTypesPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can manage leave types
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Types"
        text="Manage leave types and policies"
      />
      <div className="flex-1 space-y-6">
        <LeaveTypesPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}

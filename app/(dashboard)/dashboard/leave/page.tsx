import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { LeaveManagementPage } from "@/components/leave-management/leave-management-page";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Management Dashboard | TCM Enterprise Business Suite',
  description: 'Manage employee leave requests and balances',
};

export default async function LeaveManagementDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return <LeaveManagementPage />;
}

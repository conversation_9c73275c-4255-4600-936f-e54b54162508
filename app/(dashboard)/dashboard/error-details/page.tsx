// app/(dashboard)/dashboard/error-details/page.tsx
"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertTriangle,
  XCircle,
  AlertCircle,
  Info,
  ArrowLeft,
  Copy,
  CheckCircle,
  RefreshCw,
  Bug,
  ExternalLink,
  Clock,
  User,
  Globe,
  Database
} from "lucide-react"

interface StructuredError {
  id: string
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  code: string
  message: string
  userMessage: string
  details?: string
  suggestions?: string[]
  actions?: any[]
  timestamp: string
  context?: Record<string, any>
}

export default function ErrorDetailsPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [error, setError] = useState<StructuredError | null>(null)
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const errorParam = searchParams.get('error')
    if (errorParam) {
      try {
        const decodedError = JSON.parse(decodeURIComponent(errorParam))
        setError(decodedError)
      } catch (err) {
        console.error('Failed to parse error data:', err)
      }
    }
    setLoading(false)
  }, [searchParams])

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <XCircle className="h-6 w-6 text-red-500" />
      case 'HIGH':
        return <AlertTriangle className="h-6 w-6 text-orange-500" />
      case 'MEDIUM':
        return <AlertCircle className="h-6 w-6 text-yellow-500" />
      case 'LOW':
        return <Info className="h-6 w-6 text-blue-500" />
      default:
        return <AlertCircle className="h-6 w-6 text-gray-500" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'LOW':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const copyErrorDetails = async () => {
    if (!error) return
    
    const errorDetails = {
      id: error.id,
      code: error.code,
      message: error.message,
      timestamp: error.timestamp,
      type: error.type,
      severity: error.severity,
      context: error.context
    }
    
    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy error details:', err)
    }
  }

  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Error Details"
          text="Loading error information..."
        />
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </DashboardShell>
    )
  }

  if (!error) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Error Details"
          text="Error information not found"
        />
        <Card>
          <CardContent className="pt-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No error information was provided. Please navigate here from an error dialog.
              </AlertDescription>
            </Alert>
            <div className="mt-4">
              <Button onClick={() => router.back()} variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Error Details"
        text="Comprehensive error information and troubleshooting guide"
      >
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Go Back
        </Button>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Error Summary Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              {getSeverityIcon(error.severity)}
              <div className="flex-1">
                <CardTitle className="text-xl">System Error Encountered</CardTitle>
                <CardDescription className="mt-1">
                  Error ID: <span className="font-mono">{error.id}</span>
                </CardDescription>
              </div>
              <Badge className={getSeverityColor(error.severity)}>
                {error.severity} SEVERITY
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-base">
                {error.userMessage}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="solutions">Solutions</TabsTrigger>
            <TabsTrigger value="technical">Technical</TabsTrigger>
            <TabsTrigger value="context">Context</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Error Overview</CardTitle>
                <CardDescription>
                  Basic information about this error
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Error Code</span>
                    <div className="font-mono bg-muted px-3 py-2 rounded mt-1">
                      {error.code}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Error Type</span>
                    <div className="font-mono bg-muted px-3 py-2 rounded mt-1">
                      {error.type}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Timestamp</span>
                    <div className="bg-muted px-3 py-2 rounded mt-1 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {new Date(error.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Severity Level</span>
                    <div className="mt-1">
                      <Badge className={getSeverityColor(error.severity)}>
                        {error.severity}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="solutions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Suggested Solutions</CardTitle>
                <CardDescription>
                  Recommended actions to resolve this error
                </CardDescription>
              </CardHeader>
              <CardContent>
                {error.suggestions && error.suggestions.length > 0 ? (
                  <div className="space-y-3">
                    {error.suggestions.map((suggestion, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">
                          {index + 1}
                        </div>
                        <p className="text-sm text-blue-900">{suggestion}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No specific solutions are available for this error. Please contact support for assistance.
                    </AlertDescription>
                  </Alert>
                )}

                {error.actions && error.actions.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-medium text-sm mb-3">Available Actions</h4>
                    <div className="flex flex-wrap gap-2">
                      {error.actions.map((action, index) => (
                        <Button
                          key={index}
                          variant={action.variant === 'primary' ? 'default' : 
                                  action.variant === 'destructive' ? 'destructive' : 'outline'}
                          size="sm"
                        >
                          {action.action === 'retry' && <RefreshCw className="mr-2 h-4 w-4" />}
                          {action.action === 'debug' && <Bug className="mr-2 h-4 w-4" />}
                          {action.type === 'link' && <ExternalLink className="mr-2 h-4 w-4" />}
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="technical" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Technical Information</CardTitle>
                <CardDescription>
                  Detailed technical information for developers and support
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <span className="font-medium text-sm text-muted-foreground">Technical Message</span>
                  <ScrollArea className="h-24 w-full border rounded mt-1">
                    <div className="font-mono text-sm p-3 break-all">
                      {error.message}
                    </div>
                  </ScrollArea>
                </div>

                {error.details && (
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Additional Details</span>
                    <ScrollArea className="h-32 w-full border rounded mt-1">
                      <div className="font-mono text-sm p-3 break-all">
                        {error.details}
                      </div>
                    </ScrollArea>
                  </div>
                )}

                <Separator />

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyErrorDetails}
                  >
                    {copied ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy Error Details
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="context" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Error Context</CardTitle>
                <CardDescription>
                  Environmental and contextual information when the error occurred
                </CardDescription>
              </CardHeader>
              <CardContent>
                {error.context ? (
                  <div className="space-y-4">
                    {Object.entries(error.context).map(([key, value]) => (
                      <div key={key}>
                        <span className="font-medium text-sm text-muted-foreground capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                        <div className="font-mono text-sm bg-muted px-3 py-2 rounded mt-1 break-all">
                          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No contextual information is available for this error.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  )
}

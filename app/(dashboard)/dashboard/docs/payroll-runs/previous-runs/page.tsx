"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { Dash<PERSON>Header } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import {
  History,
  Search,
  Filter,
  Download,
  Eye,
  Calendar,
  Users,
  DollarSign,
  FileText,
  MoreHorizontal,
  RefreshCw,
  ArrowUpDown,
  CheckCircle,
  Clock,
  AlertTriangle,
  BookOpen,
  Play
} from "lucide-react"
import Link from "next/link"

export default function PreviousPayrollRunsDocumentationPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Previous Payroll Runs Documentation"
        text="Complete guide to managing and reviewing historical payroll runs"
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/docs/payroll-runs">
              <BookOpen className="h-4 w-4 mr-2" />
              Payroll Runs Guide
            </Link>
          </Button>
          <Button asChild>
            <Link href="/dashboard/payroll/previous-runs">
              <History className="h-4 w-4 mr-2" />
              View Previous Runs
            </Link>
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-8">
        {/* Overview Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <History className="h-5 w-5 mr-2" />
              What are Previous Payroll Runs?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              The <strong>Previous Payroll Runs</strong> section provides a comprehensive view of all historical
              payroll processing activities within your organization. This powerful management interface allows
              you to review, analyze, and manage completed payroll runs with advanced filtering and search capabilities.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-2">Key Purposes:</h4>
              <ul className="space-y-1 text-blue-800">
                <li>• <strong>Historical Review:</strong> Access complete payroll processing history</li>
                <li>• <strong>Audit Trail:</strong> Track all payroll activities and changes</li>
                <li>• <strong>Financial Analysis:</strong> Review salary trends and departmental costs</li>
                <li>• <strong>Compliance:</strong> Maintain records for regulatory requirements</li>
                <li>• <strong>Reporting:</strong> Generate historical payroll reports and exports</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Interface Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Interface Overview
            </CardTitle>
            <CardDescription>
              Understanding the Previous Payroll Runs dashboard and its components
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Summary Statistics */}
            <div className="space-y-4">
              <h4 className="font-semibold">Summary Statistics Cards</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="border rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Total Runs</p>
                      <p className="text-2xl font-bold text-blue-600">24</p>
                    </div>
                    <Calendar className="h-8 w-8 text-blue-500" />
                  </div>
                  <p className="text-xs text-blue-700 mt-1">All payroll runs processed</p>
                </div>

                <div className="border rounded-lg p-4 bg-green-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Total Employees</p>
                      <p className="text-2xl font-bold text-green-600">156</p>
                    </div>
                    <Users className="h-8 w-8 text-green-500" />
                  </div>
                  <p className="text-xs text-green-700 mt-1">Employees processed across all runs</p>
                </div>

                <div className="border rounded-lg p-4 bg-purple-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">Total Paid</p>
                      <p className="text-2xl font-bold text-purple-600">MWK 45.2M</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-purple-500" />
                  </div>
                  <p className="text-xs text-purple-700 mt-1">Total amount disbursed</p>
                </div>

                <div className="border rounded-lg p-4 bg-orange-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-900">This Year</p>
                      <p className="text-2xl font-bold text-orange-600">12</p>
                    </div>
                    <FileText className="h-8 w-8 text-orange-500" />
                  </div>
                  <p className="text-xs text-orange-700 mt-1">Payroll runs this year</p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-muted-foreground">
                  <strong>Note:</strong> Summary statistics are automatically calculated based on your filtered results.
                  Use the filters below to focus on specific time periods or departments.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filtering and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtering and Search Capabilities
            </CardTitle>
            <CardDescription>
              Advanced filtering options to find specific payroll runs quickly
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Filter Options */}
            <div className="space-y-4">
              <h4 className="font-semibold">Available Filters</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 mr-2">Status Filter</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Filter payroll runs by their current status
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">All statuses</Badge>
                      <Badge variant="outline" className="text-xs">Draft</Badge>
                      <Badge variant="outline" className="text-xs">Processing</Badge>
                      <Badge variant="outline" className="text-xs">Completed</Badge>
                      <Badge variant="outline" className="text-xs">Approved</Badge>
                      <Badge variant="outline" className="text-xs">Paid</Badge>
                      <Badge variant="outline" className="text-xs">Cancelled</Badge>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-800 mr-2">Time Period</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Filter by specific year and month combinations
                    </p>
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div>• <strong>Year Filter:</strong> Select specific year (2023, 2024, etc.)</div>
                      <div>• <strong>Month Filter:</strong> Select specific month (January - December)</div>
                      <div>• <strong>Combined:</strong> Use both for precise period filtering</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-purple-100 text-purple-800 mr-2">Search</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Search across payroll run names and descriptions
                    </p>
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div>• <strong>Run Names:</strong> Search by payroll run titles</div>
                      <div>• <strong>Descriptions:</strong> Find runs by description content</div>
                      <div>• <strong>Real-time:</strong> Results update as you type</div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800 mr-2">Quick Actions</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Convenient actions for filter management
                    </p>
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div>• <strong>Clear Filters:</strong> Reset all filters to default</div>
                      <div>• <strong>Refresh:</strong> Reload data with current filters</div>
                      <div>• <strong>Export:</strong> Download filtered results</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Search Tips */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-900 mb-2">Search Tips & Best Practices</h4>
              <ul className="space-y-1 text-yellow-800 text-sm">
                <li>• Use specific keywords from payroll run names for faster results</li>
                <li>• Combine filters for more precise searches (e.g., "Completed" status + "2024" year)</li>
                <li>• Clear filters periodically to see the complete dataset</li>
                <li>• Use the refresh button after making changes to employee data</li>
                <li>• Export filtered results for external analysis and reporting</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Table Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Payroll Runs Table Features
            </CardTitle>
            <CardDescription>
              Understanding the comprehensive payroll runs data table
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Column Descriptions */}
            <div className="space-y-4">
              <h4 className="font-semibold">Table Columns Explained</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Run Name & Period</h5>
                    <p className="text-xs text-muted-foreground">
                      Displays the payroll run name and the pay period (month/year) it covers
                    </p>
                  </div>

                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Status</h5>
                    <p className="text-xs text-muted-foreground">
                      Current status with color-coded badges for easy identification
                    </p>
                  </div>

                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Employee Count</h5>
                    <p className="text-xs text-muted-foreground">
                      Shows processed/total employees (e.g., "45/50" means 45 processed out of 50 total)
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Financial Summary</h5>
                    <p className="text-xs text-muted-foreground">
                      Gross salary, deductions, taxes, and net pay totals in Malawi Kwacha
                    </p>
                  </div>

                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Dates</h5>
                    <p className="text-xs text-muted-foreground">
                      Creation date, processing date, and approval date timestamps
                    </p>
                  </div>

                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-sm">Actions</h5>
                    <p className="text-xs text-muted-foreground">
                      Available actions based on payroll run status and user permissions
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Action Menu */}
            <div className="space-y-4">
              <h4 className="font-semibold">Available Actions</h4>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  <span className="font-medium text-sm">Action Menu Options</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Eye className="h-3 w-3 mr-2 text-blue-500" />
                      <span className="font-medium">View Details</span>
                    </div>
                    <p className="text-xs text-muted-foreground pl-5">
                      Open detailed view with complete payroll run information
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Download className="h-3 w-3 mr-2 text-green-500" />
                      <span className="font-medium">Export Data</span>
                    </div>
                    <p className="text-xs text-muted-foreground pl-5">
                      Download payroll run data in various formats (PDF, Excel, CSV)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <FileText className="h-3 w-3 mr-2 text-purple-500" />
                      <span className="font-medium">View Payslips</span>
                    </div>
                    <p className="text-xs text-muted-foreground pl-5">
                      Access all payslips generated for this payroll run
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Step-by-Step User Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Step-by-Step User Guide
            </CardTitle>
            <CardDescription>
              How to effectively use the Previous Payroll Runs interface
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Navigation Steps */}
            <div className="space-y-4">
              <h4 className="font-semibold">Getting Started</h4>

              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h5 className="font-medium">Step 1: Access Previous Payroll Runs</h5>
                  <p className="text-sm text-muted-foreground mt-1">
                    Navigate to <strong>Payroll → Previous Runs</strong> from the main dashboard sidebar.
                    The page will load with all historical payroll runs displayed.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h5 className="font-medium">Step 2: Review Summary Statistics</h5>
                  <p className="text-sm text-muted-foreground mt-1">
                    Check the summary cards at the top to get an overview of total runs, employees processed,
                    amounts paid, and current year statistics.
                  </p>
                </div>

                <div className="border-l-4 border-purple-500 pl-4">
                  <h5 className="font-medium">Step 3: Apply Filters (Optional)</h5>
                  <p className="text-sm text-muted-foreground mt-1">
                    Use the filter controls to narrow down results by status, year, month, or search terms.
                    Filters can be combined for more precise results.
                  </p>
                </div>

                <div className="border-l-4 border-orange-500 pl-4">
                  <h5 className="font-medium">Step 4: Review Payroll Runs</h5>
                  <p className="text-sm text-muted-foreground mt-1">
                    Browse through the table to review payroll run details. Use sorting options to organize
                    data by date, amount, or status as needed.
                  </p>
                </div>

                <div className="border-l-4 border-red-500 pl-4">
                  <h5 className="font-medium">Step 5: Take Actions</h5>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click the action menu (⋯) for any payroll run to view details, export data,
                    or access related payslips and reports.
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Common Tasks */}
            <div className="space-y-4">
              <h4 className="font-semibold">Common Tasks & Workflows</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2">Monthly Payroll Review</h5>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Filter by current month and year</li>
                    <li>2. Check all runs have "Paid" status</li>
                    <li>3. Review total amounts for accuracy</li>
                    <li>4. Export monthly summary report</li>
                  </ol>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2">Audit Trail Investigation</h5>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Search for specific payroll run name</li>
                    <li>2. Click "View Details" to see full information</li>
                    <li>3. Review processing dates and approvals</li>
                    <li>4. Check employee-specific payslips if needed</li>
                  </ol>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2">Year-End Reporting</h5>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Filter by specific year</li>
                    <li>2. Ensure all runs show "Paid" status</li>
                    <li>3. Export complete year data</li>
                    <li>4. Generate summary reports for accounting</li>
                  </ol>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2">Department Cost Analysis</h5>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Use search to find department-specific runs</li>
                    <li>2. Compare costs across different periods</li>
                    <li>3. Export data for detailed analysis</li>
                    <li>4. Review trends and budget compliance</li>
                  </ol>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Troubleshooting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Troubleshooting & FAQ
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-semibold">Common Issues & Solutions</h4>

              <div className="space-y-3">
                <div className="border rounded-lg p-4 border-yellow-200 bg-yellow-50">
                  <h5 className="font-medium text-yellow-900">Q: Why don't I see any payroll runs?</h5>
                  <p className="text-sm text-yellow-800 mt-1">
                    <strong>A:</strong> Check your filters - you may have restrictive filters applied.
                    Click "Clear Filters" to reset and view all runs. Also verify you have proper permissions
                    to view payroll data.
                  </p>
                </div>

                <div className="border rounded-lg p-4 border-blue-200 bg-blue-50">
                  <h5 className="font-medium text-blue-900">Q: How do I find a specific payroll run?</h5>
                  <p className="text-sm text-blue-800 mt-1">
                    <strong>A:</strong> Use the search box to enter the payroll run name or description.
                    You can also combine filters (year + month + status) to narrow down results quickly.
                  </p>
                </div>

                <div className="border rounded-lg p-4 border-green-200 bg-green-50">
                  <h5 className="font-medium text-green-900">Q: Can I modify a completed payroll run?</h5>
                  <p className="text-sm text-green-800 mt-1">
                    <strong>A:</strong> No, completed and paid payroll runs cannot be modified for audit integrity.
                    If corrections are needed, create a new adjustment payroll run or contact your system administrator.
                  </p>
                </div>

                <div className="border rounded-lg p-4 border-purple-200 bg-purple-50">
                  <h5 className="font-medium text-purple-900">Q: How do I export payroll data?</h5>
                  <p className="text-sm text-purple-800 mt-1">
                    <strong>A:</strong> Click the action menu (⋯) next to any payroll run and select "Export Data".
                    Choose your preferred format (PDF, Excel, CSV) and the download will begin automatically.
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold mb-2">Need Additional Help?</h4>
              <p className="text-sm text-muted-foreground mb-3">
                If you encounter issues not covered in this guide, here are additional resources:
              </p>
              <div className="space-y-2 text-sm">
                <div>• Contact your system administrator for permission-related issues</div>
                <div>• Check the Technical Documentation for API and system details</div>
                <div>• Review the main Payroll Runs guide for process information</div>
                <div>• Submit a support ticket for technical assistance</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle>Related Documentation</CardTitle>
            <CardDescription>
              Explore related topics and detailed guides
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs">
                  <div className="text-left">
                    <div className="font-medium">Payroll Runs Overview</div>
                    <div className="text-sm text-muted-foreground">Complete payroll run system guide</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs/technical">
                  <div className="text-left">
                    <div className="font-medium">Technical Documentation</div>
                    <div className="text-sm text-muted-foreground">API and system architecture</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payslips">
                  <div className="text-left">
                    <div className="font-medium">Payslip Management</div>
                    <div className="text-sm text-muted-foreground">Employee payslip system</div>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

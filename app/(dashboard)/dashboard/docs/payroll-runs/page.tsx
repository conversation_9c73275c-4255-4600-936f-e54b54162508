"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import {
  Calendar,
  Users,
  Calculator,
  FileText,
  CheckCircle,
  Clock,
  DollarSign,
  AlertTriangle,
  ArrowRight,
  Play,
  Check,
  CreditCard,
  X,
  BookOpen,
  Settings
} from "lucide-react"
import Link from "next/link"

export default function PayrollRunsDocumentationPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Runs Documentation"
        text="Complete guide to understanding and managing payroll runs in the TCM Enterprise Suite"
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/docs">
              <BookOpen className="h-4 w-4 mr-2" />
              All Documentation
            </Link>
          </Button>
          <Button asChild>
            <Link href="/dashboard/payroll/run">
              <Play className="h-4 w-4 mr-2" />
              Create Payroll Run
            </Link>
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-8">
        {/* Overview Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              What is a Payroll Run?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              A <strong>Payroll Run</strong> is a systematic process of calculating and processing employee salaries
              for a specific pay period. It encompasses the entire workflow from initial setup to final payment,
              ensuring accurate compensation for all employees within the organization.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-2">Key Components of a Payroll Run:</h4>
              <ul className="space-y-1 text-blue-800">
                <li>• <strong>Pay Period:</strong> Specific month and year with defined start and end dates</li>
                <li>• <strong>Employee Selection:</strong> Employees included in the payroll processing</li>
                <li>• <strong>Salary Calculations:</strong> Basic salary, allowances, deductions, and taxes</li>
                <li>• <strong>Approval Workflow:</strong> Review and authorization process</li>
                <li>• <strong>Payment Processing:</strong> Final disbursement to employees</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Payroll Run States */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Payroll Run States & Workflow
            </CardTitle>
            <CardDescription>
              Understanding the complete lifecycle of a payroll run from creation to completion
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* State Flow Diagram */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="font-semibold mb-4">Payroll Run State Flow</h4>
                <div className="flex items-center justify-between flex-wrap gap-4">
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                      <FileText className="h-6 w-6 text-gray-600" />
                    </div>
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                      <Calculator className="h-6 w-6 text-blue-600" />
                    </div>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                      <Check className="h-6 w-6 text-purple-600" />
                    </div>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800">Approved</Badge>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-2">
                      <CreditCard className="h-6 w-6 text-emerald-600" />
                    </div>
                    <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">Paid</Badge>
                  </div>
                </div>
              </div>

              {/* Detailed State Descriptions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-gray-100 text-gray-800 mr-2">Draft</Badge>
                      <span className="text-sm text-muted-foreground">Initial State</span>
                    </div>
                    <p className="text-sm">
                      Payroll run is created but not yet processed. Can be edited, modified, or deleted.
                      Employee selection and pay period can be adjusted.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">Edit, Delete, Process</div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 mr-2">Processing</Badge>
                      <span className="text-sm text-muted-foreground">Calculation Phase</span>
                    </div>
                    <p className="text-sm">
                      System is calculating salaries, taxes, and deductions for all employees.
                      Payroll run is read-only during this phase.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">View Progress, Cancel</div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-800 mr-2">Completed</Badge>
                      <span className="text-sm text-muted-foreground">Review Phase</span>
                    </div>
                    <p className="text-sm">
                      Calculations are complete and ready for review. All salary components have been
                      calculated and payslips are generated.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">Review, Approve, Reject</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-purple-100 text-purple-800 mr-2">Approved</Badge>
                      <span className="text-sm text-muted-foreground">Authorization Phase</span>
                    </div>
                    <p className="text-sm">
                      Payroll run has been reviewed and approved by authorized personnel.
                      Ready for payment processing and bank transfers.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">Process Payment, Generate Reports</div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-emerald-100 text-emerald-800 mr-2">Paid</Badge>
                      <span className="text-sm text-muted-foreground">Final State</span>
                    </div>
                    <p className="text-sm">
                      Payments have been processed and employees have been paid.
                      Payroll run is complete and archived for record-keeping.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">View Reports, Export Data</div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 border-red-200 bg-red-50">
                    <div className="flex items-center mb-2">
                      <Badge variant="secondary" className="bg-red-100 text-red-800 mr-2">Cancelled</Badge>
                      <span className="text-sm text-muted-foreground">Terminated State</span>
                    </div>
                    <p className="text-sm">
                      Payroll run has been cancelled and will not proceed. Can occur from any state
                      except "Paid". Requires proper authorization.
                    </p>
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">Available Actions:</span>
                      <div className="text-xs text-muted-foreground">View History, Archive</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* How Payroll Runs Work */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              How Payroll Runs Work
            </CardTitle>
            <CardDescription>
              Step-by-step process of creating and managing payroll runs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 5-Step Process */}
            <div className="space-y-4">
              <h4 className="font-semibold">5-Step Payroll Run Process</h4>

              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                    <Calendar className="h-6 w-6 text-blue-600" />
                  </div>
                  <h5 className="font-medium text-sm">1. Setup</h5>
                  <p className="text-xs text-muted-foreground">Configure pay period and departments</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <h5 className="font-medium text-sm">2. Employees</h5>
                  <p className="text-xs text-muted-foreground">Select and verify employee list</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-2">
                    <Calculator className="h-6 w-6 text-purple-600" />
                  </div>
                  <h5 className="font-medium text-sm">3. Calculation</h5>
                  <p className="text-xs text-muted-foreground">Process salary calculations</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mx-auto mb-2">
                    <FileText className="h-6 w-6 text-orange-600" />
                  </div>
                  <h5 className="font-medium text-sm">4. Review</h5>
                  <p className="text-xs text-muted-foreground">Verify and approve calculations</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mx-auto mb-2">
                    <CheckCircle className="h-6 w-6 text-emerald-600" />
                  </div>
                  <h5 className="font-medium text-sm">5. Complete</h5>
                  <p className="text-xs text-muted-foreground">Finalize and process payments</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Detailed Process Steps */}
            <div className="space-y-4">
              <h4 className="font-semibold">Detailed Process Breakdown</h4>

              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h5 className="font-medium">Step 1: Setup & Configuration</h5>
                  <ul className="text-sm text-muted-foreground space-y-1 mt-1">
                    <li>• Define pay period (month/year with specific date range)</li>
                    <li>• Select departments to include (optional - all departments if none selected)</li>
                    <li>• Provide payroll run name and description</li>
                    <li>• Validate employee count and eligibility</li>
                  </ul>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h5 className="font-medium">Step 2: Employee Selection</h5>
                  <ul className="text-sm text-muted-foreground space-y-1 mt-1">
                    <li>• System displays eligible employees based on department filter</li>
                    <li>• Shows employee details: name, number, department, current salary</li>
                    <li>• Allows individual employee inclusion/exclusion</li>
                    <li>• Validates that employees have active salary structures</li>
                  </ul>
                </div>

                <div className="border-l-4 border-purple-500 pl-4">
                  <h5 className="font-medium">Step 3: Salary Calculation</h5>
                  <ul className="text-sm text-muted-foreground space-y-1 mt-1">
                    <li>• Calculates basic salary + allowances = gross salary</li>
                    <li>• Applies PAYE tax calculation based on taxable components</li>
                    <li>• Processes statutory and voluntary deductions</li>
                    <li>• Computes final net salary (gross - deductions - tax)</li>
                  </ul>
                </div>

                <div className="border-l-4 border-orange-500 pl-4">
                  <h5 className="font-medium">Step 4: Review & Approval</h5>
                  <ul className="text-sm text-muted-foreground space-y-1 mt-1">
                    <li>• Displays comprehensive salary breakdown for all employees</li>
                    <li>• Shows total gross salary, deductions, taxes, and net pay</li>
                    <li>• Allows final adjustments before approval</li>
                    <li>• Generates payslips for all employees</li>
                  </ul>
                </div>

                <div className="border-l-4 border-emerald-500 pl-4">
                  <h5 className="font-medium">Step 5: Completion & Payment</h5>
                  <ul className="text-sm text-muted-foreground space-y-1 mt-1">
                    <li>• Finalizes payroll run and locks calculations</li>
                    <li>• Generates bank transfer files for payment processing</li>
                    <li>• Creates accounting journal entries</li>
                    <li>• Distributes payslips to employees via email</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Processing Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Enhanced Processing Features
            </CardTitle>
            <CardDescription>
              Advanced payroll processing capabilities with real-time progress tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Enhanced Processing Overview */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Enhanced Processing Mode
                </h4>
                <p className="text-blue-800 mb-4">
                  Our advanced payroll processing system provides real-time progress tracking,
                  visual feedback, and enhanced user experience during payroll calculations.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white rounded-lg p-4 border border-blue-100">
                    <h5 className="font-medium text-blue-900 mb-2">Real-time Progress</h5>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Live progress bar with percentage completion</li>
                      <li>• Current employee being processed highlighted</li>
                      <li>• Completed employees moved to top with checkmarks</li>
                      <li>• Processing time estimates and averages</li>
                    </ul>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-blue-100">
                    <h5 className="font-medium text-blue-900 mb-2">Visual Feedback</h5>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Animated spinners for active processing</li>
                      <li>• Color-coded status indicators</li>
                      <li>• Smart employee list organization</li>
                      <li>• Detailed error reporting and recovery</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Processing States */}
              <div className="space-y-4">
                <h4 className="font-semibold">Employee Processing States</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="border rounded-lg p-4 text-center">
                    <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2">
                      <Clock className="h-6 w-6 text-gray-600" />
                    </div>
                    <Badge variant="outline" className="mb-2">⏳ Pending</Badge>
                    <p className="text-xs text-muted-foreground">Waiting to be processed</p>
                  </div>
                  <div className="border rounded-lg p-4 text-center border-blue-200 bg-blue-50">
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                      <Calculator className="h-6 w-6 text-blue-600 animate-pulse" />
                    </div>
                    <Badge variant="secondary" className="mb-2 bg-blue-100 text-blue-800">⚡ Processing</Badge>
                    <p className="text-xs text-muted-foreground">Currently calculating salary</p>
                  </div>
                  <div className="border rounded-lg p-4 text-center border-green-200 bg-green-50">
                    <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <Badge variant="default" className="mb-2 bg-green-100 text-green-800">✓ Completed</Badge>
                    <p className="text-xs text-muted-foreground">Successfully processed</p>
                  </div>
                  <div className="border rounded-lg p-4 text-center border-red-200 bg-red-50">
                    <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                      <AlertTriangle className="h-6 w-6 text-red-600" />
                    </div>
                    <Badge variant="destructive" className="mb-2">✗ Error</Badge>
                    <p className="text-xs text-muted-foreground">Processing failed</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Key Features & Capabilities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold">Calculation Features</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Automatic PAYE tax calculation based on Malawi tax brackets</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Flexible allowance and deduction management</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Department-based salary processing</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Real-time calculation validation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Enhanced processing with progress tracking</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">Management Features</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Multi-level approval workflow</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Comprehensive audit trail</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Automated payslip generation with PDF fallback</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Bank transfer file generation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Resumable payroll processing</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">Integration Features</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Accounting system integration</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Automated journal entry creation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Bank account management</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Payment processing workflow</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Error handling and recovery</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle>Related Documentation</CardTitle>
            <CardDescription>
              Explore related topics and detailed guides
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs/previous-runs">
                  <div className="text-left">
                    <div className="font-medium">Previous Payroll Runs</div>
                    <div className="text-sm text-muted-foreground">Managing historical payroll data</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs/technical">
                  <div className="text-left">
                    <div className="font-medium">Technical Documentation</div>
                    <div className="text-sm text-muted-foreground">API and system architecture</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payslips">
                  <div className="text-left">
                    <div className="font-medium">Payslip Management</div>
                    <div className="text-sm text-muted-foreground">Employee payslip system</div>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { Dash<PERSON>Header } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import {
  Code,
  Database,
  Server,
  Layers,
  GitBranch,
  Settings,
  Shield,
  Zap,
  BookOpen,
  Terminal,
  FileCode,
  Network,
  Lock,
  Activity
} from "lucide-react"
import Link from "next/link"

export default function PayrollRunsTechnicalDocumentationPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Payroll Runs Technical Documentation"
        text="Comprehensive technical guide for developers and system administrators"
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/docs/payroll-runs">
              <BookOpen className="h-4 w-4 mr-2" />
              User Guide
            </Link>
          </Button>
          <Button asChild>
            <Link href="/dashboard/docs/developer-docs">
              <Code className="h-4 w-4 mr-2" />
              Developer Docs
            </Link>
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-8">
        {/* System Architecture */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="h-5 w-5 mr-2" />
              System Architecture Overview
            </CardTitle>
            <CardDescription>
              High-level architecture of the payroll run system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Architecture Diagram */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold mb-4">Payroll Run System Components</h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h5 className="font-medium text-blue-600">Frontend Layer</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <FileCode className="h-3 w-3 mr-2" />
                      <span>Next.js 15 App Router</span>
                    </div>
                    <div className="flex items-center">
                      <FileCode className="h-3 w-3 mr-2" />
                      <span>React 19 Components</span>
                    </div>
                    <div className="flex items-center">
                      <FileCode className="h-3 w-3 mr-2" />
                      <span>TypeScript Interfaces</span>
                    </div>
                    <div className="flex items-center">
                      <FileCode className="h-3 w-3 mr-2" />
                      <span>Tailwind CSS Styling</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h5 className="font-medium text-green-600">API Layer</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Server className="h-3 w-3 mr-2" />
                      <span>Next.js API Routes</span>
                    </div>
                    <div className="flex items-center">
                      <Server className="h-3 w-3 mr-2" />
                      <span>RESTful Endpoints</span>
                    </div>
                    <div className="flex items-center">
                      <Server className="h-3 w-3 mr-2" />
                      <span>Middleware Authentication</span>
                    </div>
                    <div className="flex items-center">
                      <Server className="h-3 w-3 mr-2" />
                      <span>Error Handling</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h5 className="font-medium text-purple-600">Data Layer</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Database className="h-3 w-3 mr-2" />
                      <span>MongoDB Database</span>
                    </div>
                    <div className="flex items-center">
                      <Database className="h-3 w-3 mr-2" />
                      <span>Mongoose ODM</span>
                    </div>
                    <div className="flex items-center">
                      <Database className="h-3 w-3 mr-2" />
                      <span>Schema Validation</span>
                    </div>
                    <div className="flex items-center">
                      <Database className="h-3 w-3 mr-2" />
                      <span>Indexed Queries</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Data Flow */}
            <div className="space-y-4">
              <h4 className="font-semibold">Data Flow Architecture</h4>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 font-medium">1</span>
                    </div>
                    <span className="font-medium">User Interface</span>
                  </div>
                  <div className="text-blue-600">→</div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 font-medium">2</span>
                    </div>
                    <span className="font-medium">API Routes</span>
                  </div>
                  <div className="text-blue-600">→</div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 font-medium">3</span>
                    </div>
                    <span className="font-medium">Services</span>
                  </div>
                  <div className="text-blue-600">→</div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 font-medium">4</span>
                    </div>
                    <span className="font-medium">Database</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Database Schema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Database Schema & Models
            </CardTitle>
            <CardDescription>
              Detailed database structure and relationships
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Core Models */}
            <div className="space-y-4">
              <h4 className="font-semibold">Core Data Models</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-600">PayrollRun Model</h5>
                  <div className="bg-gray-50 rounded p-3 text-xs font-mono">
                    <div className="space-y-1">
                      <div><span className="text-blue-600">name:</span> string</div>
                      <div><span className="text-blue-600">description:</span> string?</div>
                      <div><span className="text-blue-600">payPeriod:</span> {`{`}</div>
                      <div className="ml-4"><span className="text-blue-600">month:</span> number</div>
                      <div className="ml-4"><span className="text-blue-600">year:</span> number</div>
                      <div className="ml-4"><span className="text-blue-600">startDate:</span> Date</div>
                      <div className="ml-4"><span className="text-blue-600">endDate:</span> Date</div>
                      <div>{`}`}</div>
                      <div><span className="text-blue-600">status:</span> enum</div>
                      <div><span className="text-blue-600">totalEmployees:</span> number</div>
                      <div><span className="text-blue-600">processedEmployees:</span> number</div>
                      <div><span className="text-blue-600">totalGrossSalary:</span> number</div>
                      <div><span className="text-blue-600">totalDeductions:</span> number</div>
                      <div><span className="text-blue-600">totalTax:</span> number</div>
                      <div><span className="text-blue-600">totalNetSalary:</span> number</div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">PayrollRecord Model</h5>
                  <div className="bg-gray-50 rounded p-3 text-xs font-mono">
                    <div className="space-y-1">
                      <div><span className="text-green-600">employeeId:</span> ObjectId</div>
                      <div><span className="text-green-600">payrollRunId:</span> ObjectId</div>
                      <div><span className="text-green-600">payPeriod:</span> PayPeriod</div>
                      <div><span className="text-green-600">components:</span> Array</div>
                      <div><span className="text-green-600">grossSalary:</span> number</div>
                      <div><span className="text-green-600">totalDeductions:</span> number</div>
                      <div><span className="text-green-600">totalTax:</span> number</div>
                      <div><span className="text-green-600">netSalary:</span> number</div>
                      <div><span className="text-green-600">currency:</span> string</div>
                      <div><span className="text-green-600">status:</span> enum</div>
                      <div><span className="text-green-600">paymentDate:</span> Date?</div>
                      <div><span className="text-green-600">paymentReference:</span> string?</div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-purple-600">PaySlip Model</h5>
                  <div className="bg-gray-50 rounded p-3 text-xs font-mono">
                    <div className="space-y-1">
                      <div><span className="text-purple-600">employeeId:</span> ObjectId</div>
                      <div><span className="text-purple-600">payrollRecordId:</span> ObjectId</div>
                      <div><span className="text-purple-600">payrollRunId:</span> ObjectId</div>
                      <div><span className="text-purple-600">employeeDetails:</span> {`{`}</div>
                      <div className="ml-4"><span className="text-purple-600">name:</span> string</div>
                      <div className="ml-4"><span className="text-purple-600">employeeNumber:</span> string</div>
                      <div className="ml-4"><span className="text-purple-600">department:</span> string</div>
                      <div>{`}`}</div>
                      <div><span className="text-purple-600">paymentDetails:</span> PaymentInfo</div>
                      <div><span className="text-purple-600">earningsBreakdown:</span> Array</div>
                      <div><span className="text-purple-600">deductionsBreakdown:</span> Array</div>
                      <div><span className="text-purple-600">taxBreakdown:</span> Array</div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-orange-600">Status Enums</h5>
                  <div className="bg-gray-50 rounded p-3 text-xs font-mono">
                    <div className="space-y-1">
                      <div className="font-medium">PayrollRun Status:</div>
                      <div>• draft</div>
                      <div>• processing</div>
                      <div>• completed</div>
                      <div>• approved</div>
                      <div>• paid</div>
                      <div>• cancelled</div>
                      <div className="mt-2 font-medium">PayrollRecord Status:</div>
                      <div>• draft</div>
                      <div>• calculated</div>
                      <div>• approved</div>
                      <div>• paid</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Database Indexes */}
            <div className="space-y-4">
              <h4 className="font-semibold">Database Indexes & Performance</h4>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h5 className="font-medium text-yellow-900 mb-2">Optimized Indexes</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium mb-1">PayrollRun Collection:</div>
                    <ul className="space-y-1 text-yellow-800">
                      <li>• payPeriod.year + payPeriod.month (compound)</li>
                      <li>• status (single field)</li>
                      <li>• departments (array)</li>
                      <li>• createdAt (single field)</li>
                    </ul>
                  </div>
                  <div>
                    <div className="font-medium mb-1">PayrollRecord Collection:</div>
                    <ul className="space-y-1 text-yellow-800">
                      <li>• employeeId + payPeriod.year + payPeriod.month</li>
                      <li>• payrollRunId (single field)</li>
                      <li>• status (single field)</li>
                      <li>• payPeriod.year + payPeriod.month</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Network className="h-5 w-5 mr-2" />
              API Endpoints Reference
            </CardTitle>
            <CardDescription>
              Complete API documentation for payroll run operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Endpoint Categories */}
            <div className="space-y-4">
              <h4 className="font-semibold">Payroll Runs API</h4>

              <div className="space-y-3">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Badge variant="secondary" className="bg-green-100 text-green-800 mr-2">GET</Badge>
                      <code className="text-sm">/api/payroll/runs</code>
                    </div>
                    <span className="text-xs text-muted-foreground">List payroll runs</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Retrieve paginated list of payroll runs with filtering options
                  </p>
                  <div className="text-xs">
                    <strong>Query Parameters:</strong> page, limit, status, period, sortBy, sortOrder
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 mr-2">POST</Badge>
                      <code className="text-sm">/api/payroll/runs</code>
                    </div>
                    <span className="text-xs text-muted-foreground">Create payroll run</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Create a new payroll run with specified parameters
                  </p>
                  <div className="text-xs">
                    <strong>Body:</strong> name, description, payPeriod, departments, currency
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Badge variant="secondary" className="bg-green-100 text-green-800 mr-2">GET</Badge>
                      <code className="text-sm">/api/payroll/runs/[id]</code>
                    </div>
                    <span className="text-xs text-muted-foreground">Get payroll run details</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Retrieve detailed information for a specific payroll run
                  </p>
                  <div className="text-xs">
                    <strong>Response:</strong> Complete payroll run object with populated references
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800 mr-2">POST</Badge>
                      <code className="text-sm">/api/payroll/runs/[id]/process</code>
                    </div>
                    <span className="text-xs text-muted-foreground">Process payroll run</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Trigger salary calculations for all employees in the payroll run
                  </p>
                  <div className="text-xs">
                    <strong>Body:</strong> userId, notes (optional)
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Service Layer */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Service Layer Architecture
            </CardTitle>
            <CardDescription>
              Business logic and service implementations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Core Services */}
            <div className="space-y-4">
              <h4 className="font-semibold">Core Services</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-600">PayrollService</h5>
                  <div className="text-sm space-y-1">
                    <div><strong>Location:</strong> <code>lib/services/payroll/payroll-service.ts</code></div>
                    <div><strong>Purpose:</strong> Core payroll run management</div>
                  </div>
                  <div className="mt-2 text-xs">
                    <strong>Key Methods:</strong>
                    <ul className="mt-1 space-y-1">
                      <li>• createPayrollRun()</li>
                      <li>• processPayrollRun()</li>
                      <li>• approvePayrollRun()</li>
                      <li>• markPayrollRunAsPaid()</li>
                      <li>• getPayrollRuns()</li>
                    </ul>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">SalaryCalculationService</h5>
                  <div className="text-sm space-y-1">
                    <div><strong>Location:</strong> <code>lib/services/payroll/salary-calculation-service.ts</code></div>
                    <div><strong>Purpose:</strong> Salary and tax calculations</div>
                  </div>
                  <div className="mt-2 text-xs">
                    <strong>Key Methods:</strong>
                    <ul className="mt-1 space-y-1">
                      <li>• calculateSalary()</li>
                      <li>• getEmployeeSalary()</li>
                      <li>• calculateYearToDateTotals()</li>
                    </ul>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-purple-600">PayslipGenerationService</h5>
                  <div className="text-sm space-y-1">
                    <div><strong>Location:</strong> <code>lib/services/payroll/payslip-generation-service.ts</code></div>
                    <div><strong>Purpose:</strong> Payslip creation and management</div>
                  </div>
                  <div className="mt-2 text-xs">
                    <strong>Key Methods:</strong>
                    <ul className="mt-1 space-y-1">
                      <li>• generatePayslipsForPayrollRun()</li>
                      <li>• generatePayslipPdf()</li>
                      <li>• markPayslipAsSent()</li>
                    </ul>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-orange-600">TaxService</h5>
                  <div className="text-sm space-y-1">
                    <div><strong>Location:</strong> <code>lib/services/payroll/tax-service.ts</code></div>
                    <div><strong>Purpose:</strong> Tax calculations and compliance</div>
                  </div>
                  <div className="mt-2 text-xs">
                    <strong>Key Methods:</strong>
                    <ul className="mt-1 space-y-1">
                      <li>• calculatePAYE()</li>
                      <li>• getTaxBrackets()</li>
                      <li>• calculateTaxExemptions()</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Service Integration */}
            <div className="space-y-4">
              <h4 className="font-semibold">Service Integration Flow</h4>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 text-xs font-medium">1</span>
                    </div>
                    <span className="text-sm"><strong>PayrollService.processPayrollRun()</strong> initiates processing</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <span className="text-green-600 text-xs font-medium">2</span>
                    </div>
                    <span className="text-sm"><strong>SalaryCalculationService.calculateSalary()</strong> for each employee</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <span className="text-purple-600 text-xs font-medium">3</span>
                    </div>
                    <span className="text-sm"><strong>TaxService.calculatePAYE()</strong> computes taxes</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center mr-3">
                      <span className="text-orange-600 text-xs font-medium">4</span>
                    </div>
                    <span className="text-sm"><strong>PayslipGenerationService.generatePayslipsForPayrollRun()</strong> creates payslips</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security & Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Security & Access Control
            </CardTitle>
            <CardDescription>
              Authentication, authorization, and security measures
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Role-Based Access */}
            <div className="space-y-4">
              <h4 className="font-semibold">Role-Based Access Control</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-red-600">Administrative Roles</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">SUPER_ADMIN</Badge>
                      <span>Full system access</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">SYSTEM_ADMIN</Badge>
                      <span>System configuration</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-600">Finance Roles</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">FINANCE_DIRECTOR</Badge>
                      <span>Approve payroll runs</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">FINANCE_MANAGER</Badge>
                      <span>Process payroll runs</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">ACCOUNTANT</Badge>
                      <span>View payroll data</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">HR Roles</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">HR_DIRECTOR</Badge>
                      <span>Manage payroll setup</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">HR_MANAGER</Badge>
                      <span>Employee management</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-purple-600">Employee Roles</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2 text-xs">EMPLOYEE</Badge>
                      <span>View own payslips</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Security Measures */}
            <div className="space-y-4">
              <h4 className="font-semibold">Security Implementation</h4>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h5 className="font-medium text-red-900 mb-2">Security Features</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium mb-1">Authentication:</div>
                    <ul className="space-y-1 text-red-800">
                      <li>• Session-based authentication</li>
                      <li>• JWT token validation</li>
                      <li>• Password hashing (bcrypt)</li>
                      <li>• Account lockout protection</li>
                    </ul>
                  </div>
                  <div>
                    <div className="font-medium mb-1">Data Protection:</div>
                    <ul className="space-y-1 text-red-800">
                      <li>• Encrypted salary information</li>
                      <li>• Audit logging for all changes</li>
                      <li>• Input validation and sanitization</li>
                      <li>• SQL injection prevention</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance & Monitoring */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Performance & Monitoring
            </CardTitle>
            <CardDescription>
              System optimization and monitoring strategies
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Performance Optimizations */}
            <div className="space-y-4">
              <h4 className="font-semibold">Performance Optimizations</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-600">Database Optimizations</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Compound indexes for complex queries</li>
                    <li>• Efficient population of related documents</li>
                    <li>• Pagination for large datasets</li>
                    <li>• Aggregation pipelines for statistics</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-green-600">Caching Strategy</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Employee salary structure caching</li>
                    <li>• Tax bracket caching</li>
                    <li>• Department information caching</li>
                    <li>• Payslip PDF caching</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            {/* Monitoring */}
            <div className="space-y-4">
              <h4 className="font-semibold">Monitoring & Logging</h4>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-medium text-green-900 mb-2">Logging Categories</h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="font-medium mb-1">PAYROLL:</div>
                    <ul className="space-y-1 text-green-800">
                      <li>• Payroll run operations</li>
                      <li>• Salary calculations</li>
                      <li>• Processing errors</li>
                    </ul>
                  </div>
                  <div>
                    <div className="font-medium mb-1">API:</div>
                    <ul className="space-y-1 text-green-800">
                      <li>• Request/response logging</li>
                      <li>• Authentication events</li>
                      <li>• Error tracking</li>
                    </ul>
                  </div>
                  <div>
                    <div className="font-medium mb-1">DATABASE:</div>
                    <ul className="space-y-1 text-green-800">
                      <li>• Query performance</li>
                      <li>• Connection issues</li>
                      <li>• Data integrity checks</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle>Related Documentation</CardTitle>
            <CardDescription>
              Explore related technical resources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs">
                  <div className="text-left">
                    <div className="font-medium">Payroll Runs Overview</div>
                    <div className="text-sm text-muted-foreground">User guide and concepts</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/payroll-runs/previous-runs">
                  <div className="text-left">
                    <div className="font-medium">Previous Runs Guide</div>
                    <div className="text-sm text-muted-foreground">Historical data management</div>
                  </div>
                </Link>
              </Button>

              <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                <Link href="/dashboard/docs/developer-docs">
                  <div className="text-left">
                    <div className="font-medium">Developer Documentation</div>
                    <div className="text-sm text-muted-foreground">Complete system architecture</div>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

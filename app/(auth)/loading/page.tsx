"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '../../../lib/frontend/hooks/useAuth';
import { AuthLoadingBanner } from '../../../components/auth/auth-loading-banner';

export default function LoadingPage() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const [checkComplete, setCheckComplete] = useState(false);

  useEffect(() => {
    // If auth is still loading, wait for it
    if (isLoading) return;

    // Set a flag in sessionStorage to prevent redirect loops
    const redirected = sessionStorage.getItem('auth_redirected');
    if (redirected) {
      // If we've already redirected, clear the flag and do nothing
      sessionStorage.removeItem('auth_redirected');
      return;
    }

    // Set a small delay to ensure the loading screen is shown
    const timer = setTimeout(() => {
      // Set the flag to prevent redirect loops
      sessionStorage.setItem('auth_redirected', 'true');

      // Redirect based on authentication status
      if (isAuthenticated) {
        window.location.href = '/dashboard';
      } else {
        window.location.href = '/login';
      }

      setCheckComplete(true);
    }, 1500); // Show loading for at least 1.5 seconds

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading]);

  // Always show the loading banner
  return (
    <AuthLoadingBanner
      redirectTo=""
      message="Welcome to TCM Enterprise Business Suite"
      subMessage={
        checkComplete
          ? "Redirecting you now..."
          : isAuthenticated
            ? `Preparing your dashboard${user ? `, ${user.firstName}` : ''}...`
            : "Checking your login status..."
      }
      delayMs={0} // Don't auto-redirect
      backgroundImage="/images/office-workspace.jpg" // Use a professional office image
    />
  );
}

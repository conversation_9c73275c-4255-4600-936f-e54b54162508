# 🔍 DEEP SCAN: Employee Salaries Page
## `http://localhost:3000/dashboard/payroll/employee-salaries`

---

## 📁 **PAGE STRUCTURE**

### **Main Page Component**
- **File**: `app/(dashboard)/dashboard/payroll/employee-salaries/page.tsx`
- **Main Component**: `EmployeeSalaryManager`
- **Layout Components**: `DashboardShell`, `DashboardHeader`

---

## 🗄️ **MODELS USED**

### **1. Employee Model**
- **File**: `models/Employee.ts`
- **Interface**: `IEmployee`
- **Key Fields**:
  - `employeeId`, `firstName`, `lastName`, `email`
  - `departmentId` (ref: Department)
  - `position`, `employmentStatus`, `employmentType`
  - `managerId`, `salary`, `avatar`

### **2. EmployeeSalary Model**
- **File**: `models/payroll/EmployeeSalary.ts`
- **Interface**: `IEmployeeSalary`
- **Key Fields**:
  - `employeeId` (ref: Employee)
  - `salaryStructureId` (ref: SalaryStructure)
  - `basicSalary`, `currency`, `isActive`
  - `effectiveDate`, `endDate`
  - `allowances[]`, `deductions[]`
  - `paymentMethod`, `bankName`, `bankAccountNumber`

### **3. SalaryStructure Model**
- **File**: `models/payroll/SalaryStructure.ts`
- **Interface**: `ISalaryStructure`
- **Key Fields**:
  - `name`, `description`, `isActive`
  - `components[]` (ISalaryComponent)
  - `applicableRoles[]`, `applicableDepartments[]`
  - `effectiveDate`, `expiryDate`

### **4. Department Model**
- **File**: `models/Department.ts`
- **Interface**: `IDepartment`
- **Key Fields**:
  - `name`, `description`, `departmentCode`
  - `head` (ref: User), `employees[]`
  - `status`, `location`, `budget`

---

## 🏪 **FRONTEND STORES (ZUSTAND)**

### **1. Employee Salary Store**
- **File**: `lib/frontend/employeeSalaryStore.ts`
- **Store**: `useEmployeeSalaryStore`
- **State**:
  - `employeeSalaries[]`, `selectedSalary`, `isLoading`, `error`
  - `salariesCache{}`, `salaryCache{}`, `cacheDuration`
- **Actions**:
  - `fetchEmployeeSalaries()`, `fetchEmployeeSalaryById()`
  - `createEmployeeSalary()`, `updateEmployeeSalary()`, `deleteEmployeeSalary()`
  - `bulkDeleteEmployeeSalaries()`, `setSelectedSalary()`

### **2. Salary Structure Store**
- **File**: `lib/frontend/salaryStructureStore.ts`
- **Store**: `useSalaryStructureStore`
- **State**:
  - `salaryStructures[]`, `selectedStructure`, `isLoading`, `error`
  - `structuresCache{}`, `structureCache{}`, `cacheDuration`
- **Actions**:
  - `fetchSalaryStructures()`, `fetchSalaryStructureById()`
  - `createSalaryStructure()`, `updateSalaryStructure()`, `deleteSalaryStructure()`

---

## 🌐 **API ROUTES**

### **1. Employee Salaries API**
- **Base Route**: `/api/payroll/employee-salaries`

#### **Main Routes**:
- **GET** `/api/payroll/employee-salaries` - List all employee salaries
  - **File**: `app/api/payroll/employee-salaries/route.ts`
  - **Query Params**: `page`, `limit`, `search`, `employeeId`
  - **Returns**: Paginated employee salaries with populated employee data

- **POST** `/api/payroll/employee-salaries` - Create new employee salary
  - **File**: `app/api/payroll/employee-salaries/route.ts`
  - **Body**: Employee salary data
  - **Returns**: Created employee salary

#### **Individual Salary Routes**:
- **GET** `/api/payroll/employee-salaries/[id]` - Get specific employee salary
  - **File**: `app/api/payroll/employee-salaries/[id]/route.ts`
  - **Returns**: Employee salary with populated data

- **PATCH** `/api/payroll/employee-salaries/[id]` - Update employee salary
  - **File**: `app/api/payroll/employee-salaries/[id]/route.ts`
  - **Body**: Partial employee salary data
  - **Returns**: Updated employee salary

- **DELETE** `/api/payroll/employee-salaries/[id]` - Delete employee salary
  - **File**: `app/api/payroll/employee-salaries/[id]/route.ts`
  - **Returns**: Success confirmation

#### **Bulk Operations**:
- **POST** `/api/payroll/employee-salaries/bulk-delete` - Bulk delete salaries
  - **File**: `app/api/payroll/employee-salaries/bulk-delete/route.ts`
  - **Body**: `{ ids: string[] }`
  - **Returns**: Deletion count and results

- **POST** `/api/payroll/employee-salaries/bulk-import` - Bulk import salaries
  - **File**: `app/api/payroll/employee-salaries/bulk-import/route.ts`
  - **Body**: CSV/Excel data
  - **Returns**: Import results

#### **Template & Export**:
- **GET** `/api/payroll/employee-salaries/template` - Download import template
  - **File**: `app/api/payroll/employee-salaries/template/route.ts`
  - **Returns**: Excel template file

### **2. Salary Structures API**
- **Base Route**: `/api/payroll/salary-structures`

#### **Main Routes**:
- **GET** `/api/payroll/salary-structures` - List salary structures
  - **File**: `app/api/payroll/salary-structures/route.ts`
  - **Query Params**: `page`, `limit`, `name`, `isActive`
  - **Returns**: Paginated salary structures

- **GET** `/api/payroll/salary-structures/[id]` - Get specific structure
  - **File**: `app/api/payroll/salary-structures/[id]/route.ts`
  - **Returns**: Salary structure details

---

## 🧩 **CHILD COMPONENTS**

### **1. Employee Salary Form**
- **File**: `components/payroll/employee-salary/employee-salary-form.tsx`
- **Purpose**: Create/edit employee salary records
- **Dependencies**: Employee, SalaryStructure, Allowance, Deduction hooks

### **2. Employee Salary Details**
- **File**: `components/payroll/employee-salary/employee-salary-details.tsx`
- **Purpose**: Display detailed salary information
- **Features**: Read-only view with formatted data

### **3. Bulk Employee Salary Upload**
- **File**: `components/payroll/employee-salary/bulk-employee-salary-upload.tsx`
- **Purpose**: Bulk import employee salaries via CSV/Excel
- **Features**: File upload, validation, progress tracking

---

## 🔧 **SERVICES & UTILITIES**

### **1. Frontend Hooks**
- `usePayrollEmployees()` - Fetch employees for payroll
- `useSalaryStructures()` - Fetch salary structures
- `useAllowances()` - Fetch allowances
- `useDeductions()` - Fetch deductions

### **2. UI Components**
- `SimpleCurrencyDisplay` - Format currency values
- `EnhancedDatePicker` - Date selection
- `Avatar`, `Badge`, `Card`, `Table` - UI elements
- `Dialog`, `AlertDialog` - Modal components

---

## 📊 **DATA FLOW**

### **1. Page Load**
```
Page Load → EmployeeSalaryManager → useEmployeeSalaryStore
    ↓
fetchEmployeeSalaries() → GET /api/payroll/employee-salaries
    ↓
Store Updates → Component Re-renders → Table Display
```

### **2. Salary Reactivation**
```
Click Red X → confirmReactivate() → handleReactivateSalary()
    ↓
updateEmployeeSalary() → PATCH /api/payroll/employee-salaries/[id]
    ↓
Store Updates → fetchEmployeeSalaries() → UI Refresh
```

### **3. CRUD Operations**
```
Create: Form Submit → createEmployeeSalary() → POST /api/payroll/employee-salaries
Update: Form Submit → updateEmployeeSalary() → PATCH /api/payroll/employee-salaries/[id]
Delete: Confirm → deleteEmployeeSalary() → DELETE /api/payroll/employee-salaries/[id]
Bulk Delete: Select → bulkDeleteEmployeeSalaries() → POST /api/payroll/employee-salaries/bulk-delete
```

---

## 🔍 **KEY FEATURES**

### **1. Visual Status Management**
- ✅ Green checkmark for active salaries
- ❌ Red X (clickable) for inactive salaries
- 🔄 One-click reactivation with confirmation modal

### **2. Advanced Filtering & Search**
- Search by employee name, department, position, employee number
- Filter by department and salary structure
- Sort by multiple columns (employee, department, salary, date, status)

### **3. Bulk Operations**
- Bulk delete selected salaries
- Bulk import via CSV/Excel upload
- Template download for imports

### **4. Caching & Performance**
- 5-minute cache duration for API responses
- Intelligent cache invalidation on mutations
- Optimistic UI updates

---

## 🎯 **INTEGRATION POINTS**

### **1. Employee Module**
- Fetches employee data for salary assignment
- Displays employee information (name, department, position)
- Links to employee profile pages

### **2. Payroll Module**
- Integrates with payroll calculation services
- Uses unified payroll service for salary calculations
- Connects to tax calculation services

### **3. Department Module**
- Filters employees by department
- Displays department information
- Links to department management

### **4. Authentication**
- Uses custom authentication system
- Session-based API calls
- Role-based access control

---

## 📈 **PERFORMANCE OPTIMIZATIONS**

### **1. Caching Strategy**
- Frontend store caching (5 minutes)
- API response caching
- Intelligent cache invalidation

### **2. Pagination**
- Server-side pagination (default: 10 items)
- Lazy loading of additional pages
- Efficient database queries

### **3. Optimistic Updates**
- Immediate UI feedback
- Background API calls
- Error handling with rollback

---

## 🚨 **ERROR HANDLING**

### **1. API Errors**
- Graceful 404 handling (empty state)
- Network error recovery
- User-friendly error messages

### **2. Validation**
- Form validation before submission
- Server-side validation
- Real-time feedback

### **3. Loading States**
- Skeleton loaders during fetch
- Button loading states during actions
- Progress indicators for bulk operations

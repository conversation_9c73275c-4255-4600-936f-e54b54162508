const fs = require('fs');
const path = require('path');

// Employee data (same as Excel version)
const employeeData = [
  {
    firstName: 'Kiss',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+265 999 123 456',
    dateOfBirth: '1985-05-15',
    gender: 'Male',
    maritalStatus: 'married',
    numberOfChildren: 2,
    department: 'Human Resources',
    position: 'Software Developer',
    employmentType: 'full-time',
    employmentStatus: 'active',
    hireDate: '2023-01-15',
    salary: 75000,
    address: '123 Main Street',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Chinsapo',
    traditionalAuthority: 'Kalonga',
    district: 'Lilongwe',
    nationalId: 'MWI123456789',
    nextOfKinName: 'Mary Do<PERSON>',
    nextOfKinRelationship: 'Spouse',
    nextOfKinPhone: '+265 888 123 456',
    nextOfKinAddress: '123 Main Street, Lilongwe',
    emergencyContactName: '<PERSON>',
    emergencyContactPhone: '+265 999 987 654',
    emergencyContactRelationship: 'Brother',
    notes: 'Sample employee record'
  },
  {
    firstName: '<PERSON>',
    lastName: 'Chakwera',
    email: '<EMAIL>',
    phone: '',
    dateOfBirth: '',
    gender: 'Female',
    maritalStatus: 'married',
    numberOfChildren: 4,
    department: 'Administration',
    position: 'Registrar',
    employmentType: 'part-time',
    employmentStatus: 'active',
    hireDate: '2023-09-01',
    salary: 3256128.73,
    address: 'P/Bag, 35, Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 25',
    traditionalAuthority: 'Chitukula',
    district: 'Lilongwe',
    nationalId: '',
    nextOfKinName: 'Mr. Mphandamkoko',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Donnie',
    lastName: 'Mwenechanya',
    email: '<EMAIL>',
    phone: '************',
    dateOfBirth: '1999-04-16',
    gender: 'Male',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'Finance Department',
    position: 'Accounts Assistant',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: 'P.O Box 305 Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 49',
    traditionalAuthority: '',
    district: '',
    nationalId: '00CD6Z66',
    nextOfKinName: 'Frank Mwenechanya',
    nextOfKinRelationship: 'Brother',
    nextOfKinPhone: '************',
    nextOfKinAddress: '',
    emergencyContactName: 'Samuel Kumwembe',
    emergencyContactPhone: '************',
    emergencyContactRelationship: 'Brother',
    notes: ''
  }
];

// Convert to CSV
function convertToCSV(data) {
  if (data.length === 0) return '';
  
  // Get headers from first object
  const headers = Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    headers.join(','), // Header row
    ...data.map(row => 
      headers.map(header => {
        const value = row[header] || '';
        // Escape commas and quotes in values
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');
  
  return csvContent;
}

// Generate CSV
const csvContent = convertToCSV(employeeData);
const outputPath = path.join(__dirname, 'TCM_Employee_Import_Template.csv');

fs.writeFileSync(outputPath, csvContent, 'utf8');

console.log(`CSV file created successfully: ${outputPath}`);
console.log(`Total employees: ${employeeData.length}`);

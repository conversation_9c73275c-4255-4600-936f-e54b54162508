const XLSX = require('xlsx');
const path = require('path');

// Employee data based on your provided records
const employeeData = [
  {
    firstName: 'Kiss',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+265 999 123 456',
    dateOfBirth: '1985-05-15',
    gender: 'Male',
    maritalStatus: 'married',
    numberOfChildren: 2,
    department: 'Human Resources',
    position: 'Software Developer',
    employmentType: 'full-time',
    employmentStatus: 'active',
    hireDate: '2023-01-15',
    salary: 75000,
    address: '123 Main Street',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Chinsapo',
    traditionalAuthority: 'Kalonga',
    district: 'Lilongwe',
    nationalId: 'MWI123456789',
    nextOfKinName: 'Mary Do<PERSON>',
    nextOfKinRelationship: 'Spouse',
    nextOfKinPhone: '+265 888 123 456',
    nextOfKinAddress: '123 Main Street, Lilongwe',
    emergencyContactName: '<PERSON>',
    emergencyContactPhone: '+265 999 987 654',
    emergencyContactRelationship: 'Brother',
    notes: 'Sample employee record'
  },
  {
    firstName: '<PERSON>',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+265 888 987 654',
    dateOfBirth: '1990-08-22',
    gender: 'Female',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'Finance Department',
    position: 'HR Specialist',
    employmentType: 'full-time',
    employmentStatus: 'active',
    hireDate: '2023-02-01',
    salary: 65000,
    address: '456 Oak Avenue',
    city: 'Blantyre',
    state: 'Southern Region',
    country: 'Malawi',
    village: 'Ndirande',
    traditionalAuthority: 'Machinjiri',
    district: 'Blantyre',
    nationalId: 'MWI987654321',
    nextOfKinName: 'Robert Smith',
    nextOfKinRelationship: 'Father',
    nextOfKinPhone: '+265 888 456 789',
    nextOfKinAddress: '789 Cedar Lane, Blantyre',
    emergencyContactName: 'Sarah Smith',
    emergencyContactPhone: '+265 999 456 123',
    emergencyContactRelationship: 'Sister',
    notes: 'Sample employee record'
  },
  {
    firstName: 'Michael',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+265 991 456 789',
    dateOfBirth: '1988-11-30',
    gender: 'Male',
    maritalStatus: 'divorced',
    numberOfChildren: 1,
    department: 'Compliance',
    position: 'Finance Analyst',
    employmentType: 'part-time',
    employmentStatus: 'active',
    hireDate: '2023-03-15',
    salary: 45000,
    address: '789 Pine Road',
    city: 'Mzuzu',
    state: 'Northern Region',
    country: 'Malawi',
    village: 'Chibavi',
    traditionalAuthority: 'Mtwalo',
    district: 'Mzuzu',
    nationalId: 'MWI456789123',
    nextOfKinName: 'Elizabeth Johnson',
    nextOfKinRelationship: 'Mother',
    nextOfKinPhone: '+265 888 789 123',
    nextOfKinAddress: '321 Elm Street, Mzuzu',
    emergencyContactName: 'David Johnson',
    emergencyContactPhone: '+265 999 789 456',
    emergencyContactRelationship: 'Father',
    notes: 'Sample employee record'
  },
  {
    firstName: 'Grace',
    lastName: 'Chakwera',
    email: '<EMAIL>',
    phone: '',
    dateOfBirth: '',
    gender: 'Female',
    maritalStatus: 'married',
    numberOfChildren: 4,
    department: 'Administration',
    position: 'Registrar',
    employmentType: 'part-time',
    employmentStatus: 'active',
    hireDate: '2023-09-01',
    salary: 3256128.73,
    address: 'P/Bag, 35, Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 25',
    traditionalAuthority: 'Chitukula',
    district: 'Lilongwe',
    nationalId: '',
    nextOfKinName: 'Mr. Mphandamkoko',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Lindiwe',
    lastName: 'Chide',
    email: '<EMAIL>',
    phone: '',
    dateOfBirth: '',
    gender: 'Female',
    maritalStatus: 'married',
    numberOfChildren: 2,
    department: 'Administration',
    position: 'Director of Registration',
    employmentType: 'part-time',
    employmentStatus: 'active',
    hireDate: '2024-03-01',
    salary: 1477725.28,
    address: 'P/Bag, 35, Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 49',
    traditionalAuthority: 'Chitukula',
    district: 'Lilongwe',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Zainabu',
    lastName: 'Wanyawa',
    email: '<EMAIL>',
    phone: '************',
    dateOfBirth: '',
    gender: 'Female',
    maritalStatus: 'married',
    numberOfChildren: 0,
    department: 'Finance Department',
    position: 'Accounts Assistant',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: 'PO. Box 149, Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 21',
    traditionalAuthority: 'Tsabango',
    district: 'Lilongwe',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Mellisa',
    lastName: 'Tewesa',
    email: '<EMAIL>',
    phone: '************',
    dateOfBirth: '2000-05-20',
    gender: 'Female',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'Store',
    position: 'Stores Assistant',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: '',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: '',
    traditionalAuthority: '',
    district: '',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Takondwa',
    lastName: 'Mapando',
    email: '<EMAIL>',
    phone: '265993835657',
    dateOfBirth: '',
    gender: 'Female',
    maritalStatus: 'married',
    numberOfChildren: 1,
    department: 'Procurement',
    position: 'Procurement Officer',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: '',
    city: '',
    state: '',
    country: 'Malawi',
    village: '',
    traditionalAuthority: '',
    district: '',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Ronald',
    lastName: 'Sikwese',
    email: '<EMAIL>',
    phone: '265880723002',
    dateOfBirth: '',
    gender: 'Male',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'ICT',
    position: 'ICT Officer',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: 'P.O. Box 946, Blantyre',
    city: 'Blantyre',
    state: 'Southern Region',
    country: 'Malawi',
    village: '',
    traditionalAuthority: '',
    district: '',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Clement',
    lastName: 'Phiri',
    email: '<EMAIL>',
    phone: '',
    dateOfBirth: '',
    gender: 'Male',
    maritalStatus: '',
    numberOfChildren: 0,
    department: 'ICT',
    position: 'ICT Officer',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: '',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: '',
    traditionalAuthority: '',
    district: '',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Jones',
    lastName: 'Nchungula',
    email: '<EMAIL>',
    phone: '265888996100',
    dateOfBirth: '1998-07-13',
    gender: 'Male',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'ICT',
    position: 'ICT Officer',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: 'P.O. Box 31789, Blantyre',
    city: 'Blantyre',
    state: 'Southern Region',
    country: 'Malawi',
    village: '',
    traditionalAuthority: '',
    district: '',
    nationalId: '',
    nextOfKinName: '',
    nextOfKinRelationship: '',
    nextOfKinPhone: '',
    nextOfKinAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  },
  {
    firstName: 'Donnie',
    lastName: 'Mwenechanya',
    email: '<EMAIL>',
    phone: '************',
    dateOfBirth: '1999-04-16',
    gender: 'Male',
    maritalStatus: 'single',
    numberOfChildren: 0,
    department: 'Finance Department',
    position: 'Accounts Assistant',
    employmentType: 'intern',
    employmentStatus: 'active',
    hireDate: '2025-01-01',
    salary: 250000,
    address: 'P.O Box 305 Lilongwe',
    city: 'Lilongwe',
    state: 'Central Region',
    country: 'Malawi',
    village: 'Area 49',
    traditionalAuthority: '',
    district: '',
    nationalId: '00CD6Z66',
    nextOfKinName: 'Frank Mwenechanya',
    nextOfKinRelationship: 'Brother',
    nextOfKinPhone: '************',
    nextOfKinAddress: '',
    emergencyContactName: 'Samuel Kumwembe',
    emergencyContactPhone: '************',
    emergencyContactRelationship: 'Brother',
    notes: ''
  }
];

// Create workbook and worksheet
const wb = XLSX.utils.book_new();
const ws = XLSX.utils.json_to_sheet(employeeData);

// Set column widths for better readability
const colWidths = [
  { wch: 15 }, // firstName
  { wch: 15 }, // lastName
  { wch: 25 }, // email
  { wch: 18 }, // phone
  { wch: 12 }, // dateOfBirth
  { wch: 10 }, // gender
  { wch: 15 }, // maritalStatus
  { wch: 12 }, // numberOfChildren
  { wch: 20 }, // department
  { wch: 25 }, // position
  { wch: 15 }, // employmentType
  { wch: 15 }, // employmentStatus
  { wch: 12 }, // hireDate
  { wch: 12 }, // salary
  { wch: 30 }, // address
  { wch: 15 }, // city
  { wch: 18 }, // state
  { wch: 12 }, // country
  { wch: 15 }, // village
  { wch: 20 }, // traditionalAuthority
  { wch: 15 }, // district
  { wch: 15 }, // nationalId
  { wch: 20 }, // nextOfKinName
  { wch: 15 }, // nextOfKinRelationship
  { wch: 18 }, // nextOfKinPhone
  { wch: 30 }, // nextOfKinAddress
  { wch: 20 }, // emergencyContactName
  { wch: 18 }, // emergencyContactPhone
  { wch: 15 }, // emergencyContactRelationship
  { wch: 30 }  // notes
];

ws['!cols'] = colWidths;

// Add the worksheet to workbook
XLSX.utils.book_append_sheet(wb, ws, 'Employee Data');

// Write the file
const outputPath = path.join(__dirname, 'TCM_Employee_Import_Template.xlsx');
XLSX.writeFile(wb, outputPath);

console.log(`Excel file created successfully: ${outputPath}`);
console.log(`Total employees: ${employeeData.length}`);

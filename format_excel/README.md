# TCM Employee Excel Template Generator

This directory contains scripts to generate properly formatted Excel files for the TCM Enterprise Business Suite employee bulk import feature.

## Files

- `generate-employee-excel.js` - Main script to generate the Excel file
- `package.json` - Node.js dependencies
- `TCM_Employee_Import_Template.xlsx` - Generated Excel file (created after running the script)

## Usage

### 1. Install Dependencies

```bash
cd format_excel
npm install
```

### 2. Generate Excel File

```bash
npm run generate
```

This will create `TCM_Employee_Import_Template.xlsx` in the current directory.

## Excel File Structure

The generated Excel file contains the following columns:

### Required Fields
- `firstName` - Em<PERSON>loyee's first name
- `lastName` - Em<PERSON>loyee's last name  
- `email` - Em<PERSON>loyee's email address
- `position` - Job position/title
- `employmentType` - full-time, part-time, contract, intern, temporary, volunteer
- `employmentStatus` - active, inactive, on-leave, terminated
- `hireDate` - Date of hire (YYYY-MM-DD format)

### Optional Fields
- `phone` - Phone number
- `dateOfBirth` - Date of birth (YYYY-MM-DD format)
- `gender` - Male, Female, Other
- `maritalStatus` - single, married, divorced, widowed
- `numberOfChildren` - Number of children
- `department` - Department name
- `salary` - Annual salary amount
- `address` - Physical address
- `city` - City
- `state` - State/Region
- `country` - Country
- `village` - Village name
- `traditionalAuthority` - Traditional Authority
- `district` - District
- `nationalId` - National ID number
- `nextOfKinName` - Next of kin name
- `nextOfKinRelationship` - Relationship to next of kin
- `nextOfKinPhone` - Next of kin phone number
- `nextOfKinAddress` - Next of kin address
- `emergencyContactName` - Emergency contact name
- `emergencyContactPhone` - Emergency contact phone
- `emergencyContactRelationship` - Emergency contact relationship
- `notes` - Additional notes

## Sample Data

The generated file includes 13 sample employee records based on actual TCM data:

1. Kiss Doe - Software Developer (HR)
2. Jane Smith - HR Specialist (Finance)
3. Michael Johnson - Finance Analyst (Compliance)
4. Grace Chakwera - Registrar (Administration)
5. Lindiwe Chide - Director of Registration (Administration)
6. Zainabu Wanyawa - Accounts Assistant (Finance)
7. Mellisa Tewesa - Stores Assistant (Store)
8. Takondwa Mapando - Procurement Officer (Procurement)
9. Ronald Sikwese - ICT Officer (ICT)
10. Clement Phiri - ICT Officer (ICT)
11. Jones Nchungula - ICT Officer (ICT)
12. Donnie Mwenechanya - Accounts Assistant (Finance)

## Import Instructions

1. Download the generated Excel file
2. Modify the data as needed for your organization
3. Save the file
4. In the TCM Enterprise Business Suite:
   - Navigate to Employees → Bulk Import
   - Upload the Excel file
   - Review the import results
   - Verify the imported data

## Data Validation

The system will validate:
- Required fields are present
- Email format is valid
- Employment type values are valid
- Employment status values are valid
- Date formats are correct (YYYY-MM-DD)
- No duplicate email addresses

## Notes

- All dates should be in YYYY-MM-DD format
- Employment types must be: full-time, part-time, contract, intern, temporary, volunteer
- Employment status must be: active, inactive, on-leave, terminated
- Marital status must be: single, married, divorced, widowed
- Phone numbers should include country code (e.g., +265 for Malawi)
- Salary amounts should be numeric values without currency symbols

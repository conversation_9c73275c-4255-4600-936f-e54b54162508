{"name": "tcm-employee-excel-generator", "version": "1.0.0", "description": "Generate formatted Excel files for TCM Enterprise Business Suite employee import", "main": "generate-employee-excel.js", "scripts": {"generate": "node generate-employee-excel.js", "generate-csv": "node generate-csv.js", "generate-all": "npm run generate && npm run generate-csv", "install-deps": "npm install xlsx"}, "dependencies": {"xlsx": "^0.18.5"}, "keywords": ["excel", "employee", "import", "tcm", "malawi"], "author": "TCM Enterprise Business Suite", "license": "MIT"}
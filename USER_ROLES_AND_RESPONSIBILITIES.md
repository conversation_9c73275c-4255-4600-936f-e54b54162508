# 👥 **USER ROLES AND RES<PERSON><PERSON><PERSON>ILITIES**
## Teachers Council of Malawi - Comprehensive Role Definition

---

## 🔐 **ADMINISTRATIVE ROLES**

### **SUPER_ADMIN** (`super_admin`)
**Highest level system administrator with unrestricted access**
- **Responsibilities:**
  - Complete system administration and configuration
  - User management across all modules
  - System security and backup management
  - Global settings and policy configuration
  - Emergency system recovery and maintenance
- **Access Level:** Full access to all modules and functions
- **Workflow Authority:** Can override any approval workflow

### **SYSTEM_ADMIN** (`system_admin`)
**Technical system administrator with broad access**
- **Responsibilities:**
  - System configuration and maintenance
  - User account management
  - Technical support and troubleshooting
  - System monitoring and performance optimization
  - Data backup and recovery operations
- **Access Level:** Full technical access, limited policy changes
- **Workflow Authority:** Can escalate and resolve workflow issues

### **ADMIN** (`admin`)
**General administrator with operational oversight**
- **Responsibilities:**
  - Day-to-day administrative operations
  - User support and training
  - Basic system configuration
  - Report generation and distribution
  - Compliance monitoring
- **Access Level:** Administrative access to most modules
- **Workflow Authority:** Can approve high-value transactions and resolve escalations

---

## 👔 **MANAGEMENT HIERARCHY ROLES**

### **DIRECTOR** (`director`)
**Senior executive with strategic oversight**
- **Responsibilities:**
  - Strategic planning and decision making
  - High-level budget approval and oversight
  - Policy development and implementation
  - Organizational governance
  - Stakeholder management
- **Access Level:** Executive-level access to all financial and strategic data
- **Workflow Authority:** Final approval for transactions > MWK 200,000
- **Budget Authority:** Unlimited budget approval rights

### **MANAGER** (`manager`)
**Middle management with departmental oversight**
- **Responsibilities:**
  - Departmental budget management
  - Team supervision and performance management
  - Operational planning and execution
  - Resource allocation and optimization
  - Staff development and training
- **Access Level:** Full access to departmental data and moderate cross-departmental access
- **Workflow Authority:** Approval for transactions MWK 50,001 - MWK 200,000
- **Budget Authority:** Departmental budget management and approval

### **SUPERVISOR** (`supervisor`)
**First-line management with team oversight**
- **Responsibilities:**
  - Direct team supervision
  - Daily operational oversight
  - Initial transaction review and approval
  - Staff scheduling and task assignment
  - Performance monitoring and feedback
- **Access Level:** Team-level data access with limited cross-team visibility
- **Workflow Authority:** Approval for transactions up to MWK 50,000
- **Budget Authority:** Small budget allocations and expense approvals

---

## 💰 **FINANCIAL ROLES**

### **FINANCE_DIRECTOR** (`finance_director`)
**Senior financial executive**
- **Responsibilities:**
  - Financial strategy and planning
  - Investment and funding decisions
  - Financial risk management
  - Regulatory compliance oversight
  - Financial reporting to board/stakeholders
- **Access Level:** Complete financial system access
- **Budget Authority:** Strategic financial decisions and major budget approvals

### **FINANCE_MANAGER** (`finance_manager`)
**Financial operations manager**
- **Responsibilities:**
  - Financial operations management
  - Budget preparation and monitoring
  - Financial analysis and reporting
  - Cash flow management
  - Vendor and contract management
- **Access Level:** Full financial module access
- **Budget Authority:** Operational budget management and approval

### **FINANCE_OFFICER** (`finance_officer`)
**Financial operations specialist**
- **Responsibilities:**
  - Transaction processing and verification
  - Financial record maintenance
  - Compliance monitoring
  - Financial data analysis
  - Audit support and documentation
- **Access Level:** Financial transaction and reporting access
- **Budget Authority:** Transaction verification and processing

### **ACCOUNTANT** (`accountant`)
**Accounting professional**
- **Responsibilities:**
  - Bookkeeping and account reconciliation
  - Financial statement preparation
  - Tax preparation and compliance
  - Expense tracking and categorization
  - Financial audit support
- **Access Level:** Accounting module access with transaction processing rights
- **Budget Authority:** Transaction recording and basic approvals

### **BUDGET_ANALYST** (`budget_analyst`)
**Budget planning and analysis specialist**
- **Responsibilities:**
  - Budget preparation and analysis
  - Variance analysis and reporting
  - Financial forecasting and modeling
  - Cost analysis and optimization
  - Budget performance monitoring
- **Access Level:** Budget module access with analytical tools
- **Budget Authority:** Budget analysis and recommendation authority

---

## 💼 **PAYROLL ROLES**

### **PAYROLL_MANAGER** (`payroll_manager`)
**Payroll operations manager**
- **Responsibilities:**
  - Payroll system management
  - Payroll policy development
  - Compliance with labor laws
  - Payroll audit and verification
  - Staff payroll training and support
- **Access Level:** Full payroll system access
- **Workflow Authority:** Payroll approval and processing authority

### **PAYROLL_OFFICER** (`payroll_officer`)
**Payroll processing specialist**
- **Responsibilities:**
  - Payroll calculation and processing
  - Employee data maintenance
  - Deduction and benefit administration
  - Payroll reporting and documentation
  - Employee payroll inquiries
- **Access Level:** Payroll processing and employee data access

### **PAYROLL_SPECIALIST** (`payroll_specialist`)
**Specialized payroll functions**
- **Responsibilities:**
  - Complex payroll calculations
  - Tax and statutory deduction management
  - Payroll system configuration
  - Special payroll projects
  - Advanced payroll reporting
- **Access Level:** Advanced payroll features and configuration

---

## 🏢 **HUMAN RESOURCES ROLES**

### **HR_DIRECTOR** (`hr_director`)
**Senior HR executive**
- **Responsibilities:**
  - HR strategy and policy development
  - Organizational development
  - Executive recruitment and retention
  - Employee relations and compliance
  - HR budget and resource management
- **Access Level:** Complete HR system access
- **Workflow Authority:** HR policy and major personnel decisions

### **HR_MANAGER** (`hr_manager`)
**HR operations manager**
- **Responsibilities:**
  - HR operations management
  - Employee lifecycle management
  - Performance management systems
  - Training and development programs
  - HR compliance and reporting
- **Access Level:** Full HR module access

### **HR_OFFICER** (`hr_officer`)
**HR operations specialist**
- **Responsibilities:**
  - Employee record management
  - Recruitment and onboarding
  - Benefits administration
  - Employee relations support
  - HR documentation and filing
- **Access Level:** HR operational data access

---

## 🛒 **PROCUREMENT ROLES**

### **PROCUREMENT_MANAGER** (`procurement_manager`)
**Procurement operations manager**
- **Responsibilities:**
  - Procurement strategy and policy
  - Vendor management and relations
  - Contract negotiation and management
  - Procurement compliance and audit
  - Cost optimization and savings
- **Access Level:** Full procurement system access
- **Workflow Authority:** Procurement approval authority

### **PROCUREMENT_OFFICER** (`procurement_officer`)
**Procurement specialist**
- **Responsibilities:**
  - Purchase order processing
  - Vendor evaluation and selection
  - Procurement documentation
  - Inventory coordination
  - Procurement compliance monitoring
- **Access Level:** Procurement processing and vendor management

---

## 📊 **OPERATIONAL ROLES**

### **DEPARTMENT_HEAD** (`department_head`)
**Departmental leadership**
- **Responsibilities:**
  - Departmental strategic planning
  - Resource allocation and management
  - Performance oversight
  - Cross-departmental coordination
  - Stakeholder communication
- **Access Level:** Departmental data with cross-functional visibility

### **TEAM_LEADER** (`team_leader`)
**Team coordination and leadership**
- **Responsibilities:**
  - Team coordination and guidance
  - Project oversight and delivery
  - Resource planning and allocation
  - Performance monitoring
  - Team development and training
- **Access Level:** Team-level data access

### **EMPLOYEE** (`employee`)
**Standard organizational member**
- **Responsibilities:**
  - Task execution and delivery
  - Data entry and maintenance
  - Report generation and submission
  - Compliance with policies
  - Professional development
- **Access Level:** Role-specific data access with limited administrative rights

---

## 🔄 **WORKFLOW APPROVAL HIERARCHY**

### **Approval Thresholds:**
- **Up to MWK 50,000:** SUPERVISOR approval required
- **MWK 50,001 - MWK 200,000:** SUPERVISOR → MANAGER approval chain
- **Above MWK 200,000:** SUPERVISOR → MANAGER → DIRECTOR approval chain
- **Emergency/Escalated:** ADMIN can override any level

### **Auto-Approval Criteria:**
- **Amount:** ≤ MWK 10,000
- **Risk Score:** < 30
- **Transaction Type:** Not budget adjustment
- **Compliance:** No special requirements

---

## 📋 **ROLE ASSIGNMENT GUIDELINES**

### **For Teachers Council of Malawi:**
- **Council Members:** DIRECTOR or MANAGER roles
- **Administrative Staff:** ADMIN, MANAGER, or SUPERVISOR roles
- **Finance Team:** FINANCE_MANAGER, ACCOUNTANT, BUDGET_ANALYST
- **HR Team:** HR_MANAGER, HR_OFFICER, PAYROLL_OFFICER
- **General Staff:** EMPLOYEE with specific module access

### **Security Considerations:**
- **Principle of Least Privilege:** Users receive minimum access required for their role
- **Segregation of Duties:** Financial approvals require multiple role involvement
- **Regular Review:** Role assignments reviewed quarterly
- **Audit Trail:** All role-based actions logged for compliance

---

*This role structure ensures proper governance, security, and operational efficiency for the Teachers Council of Malawi's integrated management system.*

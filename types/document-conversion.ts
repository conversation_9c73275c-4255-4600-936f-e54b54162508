// types/document-conversion.ts

export interface DocumentType {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'hr' | 'finance' | 'accounting' | 'payroll' | 'procurement' | 'inventory';
  supportedFormats: FileFormat[];
  requiredFields: string[];
  optionalFields: string[];
  sampleData?: Record<string, unknown>[];
}

export interface FileFormat {
  extension: string;
  mimeType: string;
  description: string;
}

export interface ConversionRequest {
  documentType: string;
  file: File;
  options?: ConversionOptions;
}

export interface ConversionOptions {
  skipValidation?: boolean;
  includeHeaders?: boolean;
  dateFormat?: 'auto' | 'yyyy-mm-dd' | 'dd/mm/yyyy' | 'mm/dd/yyyy';
  encoding?: 'utf-8' | 'latin1' | 'ascii';
  delimiter?: ',' | ';' | '\t';
  sheetName?: string;
  startRow?: number;
  endRow?: number;
}

export interface ConversionResult {
  success: boolean;
  originalFileName: string;
  convertedFileName: string;
  downloadUrl: string;
  totalRows: number;
  convertedRows: number;
  errors: ConversionError[];
  warnings: ConversionWarning[];
  previewData: Record<string, unknown>[];
  metadata: ConversionMetadata;
}

export interface ConversionError {
  row: number;
  column: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  suggestedFix?: string;
}

export interface ConversionWarning {
  row: number;
  column: string;
  message: string;
  originalValue: string;
  convertedValue: string;
}

export interface ConversionMetadata {
  processedAt: string;
  processingTime: number;
  fileSize: number;
  originalFormat: string;
  targetFormat: string;
  conversionRules: ConversionRule[];
}

export interface ConversionRule {
  sourceField: string;
  targetField: string;
  transformation: 'direct' | 'format' | 'lookup' | 'calculate' | 'split' | 'merge';
  parameters?: Record<string, unknown>;
}

export interface FieldMapping {
  sourceColumn: string;
  targetField: string;
  required: boolean;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'email' | 'phone';
  validation?: ValidationRule[];
  transformation?: FieldTransformation;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'phone' | 'date' | 'number' | 'enum' | 'regex' | 'length';
  parameters?: Record<string, unknown>;
  message: string;
}

export interface FieldTransformation {
  type: 'uppercase' | 'lowercase' | 'trim' | 'format_date' | 'format_phone' | 'lookup' | 'calculate';
  parameters?: Record<string, unknown>;
}

// Employee-specific types
export interface EmployeeConversionData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed';
  numberOfChildren?: number;
  department: string;
  position: string;
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern' | 'temporary' | 'volunteer';
  employmentStatus: 'active' | 'inactive' | 'on-leave' | 'terminated';
  hireDate: string;
  salary?: number;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  village?: string;
  traditionalAuthority?: string;
  district?: string;
  nationalId?: string;
  nextOfKinName?: string;
  nextOfKinRelationship?: string;
  nextOfKinPhone?: string;
  nextOfKinAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  notes?: string;
}

// Department-specific types
export interface DepartmentConversionData {
  name: string;
  description?: string;
  head?: string;
  budget?: number;
  location?: string;
  isActive?: boolean;
}

// Budget-specific types
export interface BudgetConversionData {
  name: string;
  description?: string;
  category: string;
  amount: number;
  startDate: string;
  endDate: string;
  department?: string;
  status: 'draft' | 'approved' | 'active' | 'completed' | 'cancelled';
  fiscalYear: string;
}

// Salary-specific types
export interface SalaryConversionData {
  employeeId: string;
  basicSalary: number;
  currency: string;
  effectiveDate: string;
  endDate?: string;
  allowances?: SalaryAllowance[];
  deductions?: SalaryDeduction[];
  bankName?: string;
  bankAccountNumber?: string;
  paymentMethod: 'bank_transfer' | 'cash' | 'check' | 'mobile_money';
}

export interface SalaryAllowance {
  name: string;
  amount?: number;
  percentage?: number;
  isTaxable: boolean;
  isPensionable: boolean;
}

export interface SalaryDeduction {
  name: string;
  amount?: number;
  percentage?: number;
  isStatutory: boolean;
}

// API Response types
export interface ConversionApiResponse {
  success: boolean;
  data?: ConversionResult;
  error?: string;
  message?: string;
}

export interface DocumentTypeApiResponse {
  success: boolean;
  data?: DocumentType[];
  error?: string;
}

// Component Props types
export interface ConvertDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (result: ConversionResult) => void;
}

export interface DocumentTypeSelectProps {
  documentTypes: DocumentType[];
  selectedType: string | null;
  onSelect: (typeId: string) => void;
  isLoading?: boolean;
}

export interface FileUploadProps {
  documentType: DocumentType;
  onFileSelect: (file: File) => void;
  selectedFile: File | null;
  isUploading?: boolean;
}

export interface ConversionOptionsProps {
  documentType: DocumentType;
  options: ConversionOptions;
  onOptionsChange: (options: ConversionOptions) => void;
}

export interface ConversionResultsProps {
  result: ConversionResult;
  onDownload: () => void;
  onReset: () => void;
}

// Utility types
export type ConversionStatus = 'idle' | 'uploading' | 'processing' | 'completed' | 'error';

export interface ConversionState {
  status: ConversionStatus;
  selectedDocumentType: string | null;
  selectedFile: File | null;
  options: ConversionOptions;
  result: ConversionResult | null;
  error: string | null;
}

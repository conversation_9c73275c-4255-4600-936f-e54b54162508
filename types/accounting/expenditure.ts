// types/accounting/expenditure.ts
// Shared types and enums for expenditure/expense management

export enum ExpenditureCategory {
  OPERATIONAL = 'operational',
  CAPITAL = 'capital',
  ADMINISTRATIVE = 'administrative',
  PERSONNEL = 'personnel',
  TRAVEL = 'travel',
  UTILITIES = 'utilities',
  MAINTENANCE = 'maintenance',
  PROFESSIONAL_SERVICES = 'professional_services',
  SUPPLIES = 'supplies',
  EQUIPMENT = 'equipment',
  TRAINING = 'training',
  INSURANCE = 'insurance',
  COMMUNICATIONS = 'communications',
  OTHER = 'other'
}

export enum ExpenditureSubcategory {
  // Operational
  OFFICE_SUPPLIES = 'office_supplies',
  STATIONERY = 'stationery',
  PRINTING = 'printing',
  POSTAGE = 'postage',
  
  // Capital
  FURNITURE = 'furniture',
  COMPUTER_EQUIPMENT = 'computer_equipment',
  VEHICLES = 'vehicles',
  MACHINERY = 'machinery',
  
  // Administrative
  LEGAL_FEES = 'legal_fees',
  AUDIT_FEES = 'audit_fees',
  CONSULTING = 'consulting',
  BANK_CHARGES = 'bank_charges',
  
  // Personnel
  SALARIES = 'salaries',
  BENEFITS = 'benefits',
  TRAINING_COSTS = 'training_costs',
  RECRUITMENT = 'recruitment',
  
  // Travel
  DOMESTIC_TRAVEL = 'domestic_travel',
  INTERNATIONAL_TRAVEL = 'international_travel',
  ACCOMMODATION = 'accommodation',
  MEALS = 'meals',
  
  // Utilities
  ELECTRICITY = 'electricity',
  WATER = 'water',
  INTERNET = 'internet',
  TELEPHONE = 'telephone',
  
  // Maintenance
  BUILDING_MAINTENANCE = 'building_maintenance',
  EQUIPMENT_MAINTENANCE = 'equipment_maintenance',
  VEHICLE_MAINTENANCE = 'vehicle_maintenance',
  
  // Other
  MISCELLANEOUS = 'miscellaneous'
}

export enum ExpenditureStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold'
}

export enum ExpenditurePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

export enum PaymentMethod {
  CASH = 'cash',
  BANK_TRANSFER = 'bank_transfer',
  CHECK = 'check',
  CREDIT_CARD = 'credit_card',
  MOBILE_MONEY = 'mobile_money',
  WIRE_TRANSFER = 'wire_transfer',
  OTHER = 'other'
}

// Vendor information interface
export interface VendorInfo {
  vendorId?: string;
  vendorName: string;
  vendorEmail?: string;
  vendorPhone?: string;
  vendorAddress?: string;
  isPreferred?: boolean;
}

// Tax information interface
export interface TaxInfo {
  taxType: 'VAT' | 'withholding' | 'excise' | 'none';
  taxRate: number;
  taxAmount?: number;
  isExempt: boolean;
  exemptionReason?: string;
}

// Budget allocation interface
export interface BudgetAllocation {
  budgetId: string;
  budgetName?: string;
  allocatedAmount: number;
  percentage: number;
  isOverBudget?: boolean;
  remainingBudget?: number;
}

// Approval step interface
export interface ApprovalStep {
  stepNumber: number;
  approverUserId?: string;
  approverRole: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected' | 'skipped';
  comments?: string;
  approvedAt?: Date;
  rejectedAt?: Date;
  isRequired: boolean;
  amountThreshold?: number;
  canDelegate: boolean;
  delegatedTo?: string;
  escalationDate?: Date;
}

// Status history interface
export interface StatusHistory {
  status: ExpenditureStatus;
  changedBy: string;
  changedAt: Date;
  notes?: string;
  reason?: string;
}

// Approval workflow interface
export interface ApprovalWorkflow {
  currentApprover?: string;
  currentLevel: number;
  status: 'pending' | 'approved' | 'rejected';
  approvalHistory: Array<{
    approver: string;
    status: 'approved' | 'rejected';
    date: Date;
    comments?: string;
    level: number;
  }>;
  requiredApprovers: Array<{
    level: number;
    approver: string;
    role: string;
    amountThreshold?: number;
  }>;
  autoApprovalRules?: {
    maxAmount?: number;
    categories?: string[];
    skipApproval?: boolean;
  };
}

// Receipt/attachment interface
export interface Receipt {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
  isProcessed?: boolean;
  extractedData?: {
    amount?: number;
    date?: Date;
    vendor?: string;
    description?: string;
    confidence?: number;
  };
}

// Budget impact tracking interface
export interface BudgetImpact {
  budgetId: string;
  categoryId: string;
  subcategoryId?: string;
  impactAmount: number; // Negative for expenses
  utilizationPercentage: number;
  varianceCreated: number;
}

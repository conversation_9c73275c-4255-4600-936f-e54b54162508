# Budget Status Income Workflow Fix

## Issue Description

The Budget Status stats component on the income overview page (`/dashboard/accounting/income/overview`) was incorrectly treating both "approved" and "received" income as "actual" budget items, reversing the intended workflow:

- **Intended Workflow**: 
  - `approved` income = "Budgeted" items (committed but not yet physically received)
  - `received` income = "Actual" items (physically received income)

- **Previous Incorrect Behavior**: 
  - Both `approved` and `received` income were counted as "actual" income

## Files Modified

### 1. `app/api/accounting/income/summary/route.ts`

**Changes Made**:
- Updated aggregation logic to separate approved vs received income
- Added separate calculations for `actualIncome` (received only) and `budgetedIncome` (approved only)
- Enhanced income by source aggregation to include both actual and budgeted values
- Updated monthly data to include separate actual and budgeted income tracking

**Key Changes**:
```typescript
// Separate queries for different income statuses
const actualIncomeResult = await Income.aggregate([
  { $match: { ...baseFilter, status: 'received' } },
  { $group: { _id: null, total: { $sum: '$amount' } } }
]);

const budgetedIncomeResult = await Income.aggregate([
  { $match: { ...baseFilter, status: 'approved' } },
  { $group: { _id: null, total: { $sum: '$amount' } } }
]);
```

### 2. `lib/hooks/accounting/use-income-stats.ts`

**Changes Made**:
- Updated `IncomeStats` interface to include new fields:
  - `actualIncome`: Income that has been physically received
  - `budgetedIncome`: Income that has been approved but not yet received
  - `plannedBudgetIncome`: Income from budget planning
  - `actualBudgetVariance`: Variance between actual and planned budget
- Enhanced calculation logic to properly handle the distinction
- Updated income by source data structure to include actual and budgeted values

**Key Interface Updates**:
```typescript
export interface IncomeStats {
  totalIncome: number;
  actualIncome: number;           // NEW: Received income only
  budgetedIncome: number;         // NEW: Approved income only  
  plannedBudgetIncome: number;    // NEW: From budget planning
  actualBudgetVariance: number;   // NEW: Actual vs planned variance
  actualBudgetVariancePercentage: number; // NEW
  // ... other fields
}
```

### 3. `components/accounting/income/income-overview.tsx`

**Changes Made**:
- Updated KPI calculations to use actual income for budget performance
- Modified budget utilization to compare actual income vs planned budget
- Enhanced Budget Status section to show three categories:
  - **Actual**: Physically received income (`received` status)
  - **Approved**: Committed but not yet received income (`approved` status)  
  - **Planned**: Income from budget planning
- Updated progress bars and variance calculations

**Key UI Changes**:
```typescript
// Budget utilization based on actual income vs planned budget
const budgetUtilization = plannedBudgetTotal > 0 ? (actualTotal / plannedBudgetTotal) * 100 : 0;

// Three-column quick stats
<div className="grid grid-cols-3 gap-4 pt-4 border-t">
  <div className="text-center">
    <div className="text-lg font-semibold">{formatCompactCurrency(kpis?.actualIncome || 0)}</div>
    <div className="text-xs text-muted-foreground">Actual</div>
  </div>
  <div className="text-center">
    <div className="text-lg font-semibold">{formatCompactCurrency(kpis?.budgetedIncome || 0)}</div>
    <div className="text-xs text-muted-foreground">Approved</div>
  </div>
  <div className="text-center">
    <div className="text-lg font-semibold">{formatCompactCurrency(kpis?.plannedBudgetIncome || 0)}</div>
    <div className="text-xs text-muted-foreground">Planned</div>
  </div>
</div>
```

## Income Status Workflow (Corrected)

### Status Definitions

1. **Draft**: Income entry created but not submitted
2. **Pending Approval**: Submitted for approval
3. **Approved**: ✅ **BUDGETED ITEMS** - Financially approved but not yet physically received
4. **Received**: ✅ **ACTUAL ITEMS** - Physically received and triggers budget updates
5. **Rejected**: Rejected and excluded from calculations
6. **Cancelled**: Cancelled and excluded from calculations

### Budget Impact Flow

```
Draft → Pending → Approved (BUDGETED) → Received (ACTUAL)
                     ↓                      ↓
              Added to "Approved"    Added to "Actual"
              income calculations    income calculations
              (Committed funds)      (Physical receipt)
```

### Budget Status Component Behavior

- **Budget Utilization**: Calculated as `(Actual Income / Planned Budget) * 100`
- **Budget Variance**: `Actual Income - Planned Budget`
- **Progress Indicators**:
  - 🟢 **On Track** (80-100%): `budgetUtilization >= 80 && budgetUtilization <= 100`
  - 🟡 **Needs Attention** (<50%): `budgetUtilization < 50`
  - 🔴 **Over Budget** (>100%): `budgetUtilization > 100`

## Testing

1. Navigate to `/dashboard/accounting/income/overview`
2. Check the Budget Status section shows three distinct values:
   - **Actual**: Only income with `received` status
   - **Approved**: Only income with `approved` status
   - **Planned**: Income from budget planning
3. Verify budget utilization is calculated using actual income vs planned budget
4. Test status changes from `approved` to `received` and confirm UI updates correctly

## Benefits

1. **Accurate Budget Tracking**: Clear distinction between committed and received funds
2. **Proper Workflow**: Aligns with government accounting standards
3. **Better Decision Making**: Managers can see both committed and actual income
4. **Compliance**: Meets auditing requirements for income status tracking
5. **Real-time Updates**: Budget status updates immediately when income status changes

## Notes

- The changes maintain backward compatibility with existing data
- All existing income records will continue to work with the new logic
- The budget management page and components remain unaffected as they were already working correctly
- Only the Budget Status stats component on the income overview page was corrected

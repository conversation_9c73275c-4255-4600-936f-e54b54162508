{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "node scripts/fix-api-route-params-v2.js && node scripts/fix-edge-runtime-issues.js && node scripts/fix-route-handler-params.js && next build", "start": "node server.js", "start:standalone": "node .next/standalone/server.js", "lint": "next lint", "vercel-build": "node scripts/fix-api-route-params-v2.js && node scripts/fix-edge-runtime-issues.js && node scripts/fix-route-handler-params.js && SKIP_TYPE_CHECK=true next build", "build:production": "SKIP_TYPE_CHECK=true next build", "analyze": "ANALYZE=true next build", "clean": "node -e \"const fs=require('fs');const path=require('path');const dirs=['.next','node_modules/.cache'];dirs.forEach(d=>{if(fs.existsSync(d))fs.rmSync(d,{recursive:true,force:true})});\"", "test": "jest", "fix-api-routes": "node scripts/fix-api-route-params-v2.js", "fix-edge-runtime": "node scripts/fix-edge-runtime-issues.js", "fix-route-params": "node scripts/fix-route-handler-params.js", "scan-components": "node scripts/run-scanner.js", "fix-account-form": "node scripts/fix-account-form.js", "fix-components": "node scripts/fix-component-issues.js", "fix-object-types": "node scripts/fix-object-types.js", "auto-fix-types": "node scripts/auto-detect-fix-types.js", "fix-remaining": "node scripts/fix-remaining-issues.js", "fix-form-types": "node scripts/fix-form-type-issues.js", "ts-check": "node scripts/run-typescript-checker.js", "ts-check-fix": "node scripts/run-typescript-checker.js --apply-fixes", "ts-scan": "npx tsc --esModuleInterop scripts/typescript-error-scanner.ts && node scripts/typescript-error-scanner.js", "ts-report": "node scripts/generate-typescript-error-report.js", "ts-fix": "node scripts/fix-typescript-errors.js", "fix-unknown-types": "node scripts/fix-unknown-type-errors.js", "fix-db-tools": "node scripts/fix-database-tools-error.js", "fix-catch-blocks": "node scripts/fix-catch-blocks.js", "fix-badge-variants": "node scripts/fix-badge-variants.js", "fix-all": "node scripts/fix-all-typescript-issues.js", "scan-ts-errors": "npx ts-node scripts/ts-error-scanner.ts", "fix-ts-errors": "npx ts-node scripts/ts-error-fixer.ts", "scan-fix-ts": "powershell -File scripts/Fix-TsErrors.ps1", "fix-accounting": "node scripts/fix-accounting-module.js", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:full": "npm run lint && npm run type-check && npm run test && npm run build", "test:a11y": "jest --testPathPattern=accessibility", "format": "prettier --write .", "format:check": "prettier --check .", "setup-hooks": "husky install"}, "dependencies": {"@hookform/resolvers": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@types/archiver": "^6.0.3", "@types/json2csv": "^5.0.7", "@types/ua-parser-js": "^0.7.39", "archiver": "^7.0.1", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto-js": "^4.2.0", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.10.5", "html2canvas": "^1.4.1", "immer": "^10.1.1", "input-otp": "1.4.1", "jsbarcode": "^3.11.6", "json2csv": "6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jstat": "^1.9.6", "lucide-react": "^0.454.0", "mongodb": "^6.16.0", "mongoose": "^8.14.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "pdfkit": "^0.17.0", "pdfmake": "^0.2.10", "qrcode": "^1.5.4", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-dropzone": "^14.2.3", "react-hook-form": "latest", "react-json-tree": "^0.20.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.5.0", "react-to-print": "^3.1.0", "recharts": "latest", "remark-gfm": "^4.0.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "vaul": "^0.9.9", "xlsx": "^0.18.5", "xmldom": "^0.6.0", "zod": "latest", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/jspdf": "^2.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.3", "typescript": "^5"}}
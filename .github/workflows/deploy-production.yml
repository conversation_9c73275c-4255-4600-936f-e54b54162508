name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  pre-deployment-checks:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run full test suite
      run: npm run test:full
      
    - name: Security audit
      run: npm audit --audit-level=moderate
      
    - name: Build verification
      run: npm run build
      env:
        NEXT_PUBLIC_ENV: production
        NEXT_PUBLIC_API_URL: ${{ secrets.PROD_API_URL }}

  deploy-vercel-prod:
    name: Deploy to Vercel Production
    runs-on: ubuntu-latest
    needs: pre-deployment-checks
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_ENV: production
        NEXT_PUBLIC_API_URL: ${{ secrets.PROD_API_URL }}
        
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./
        scope: ${{ secrets.VERCEL_ORG_ID }}
        alias-domains: tcm-enterprise.vercel.app

  deploy-railway-prod:
    name: Deploy to Railway Production
    runs-on: ubuntu-latest
    needs: pre-deployment-checks
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_ENV: production
        NEXT_PUBLIC_API_URL: ${{ secrets.PROD_API_URL }}
        
    - name: Deploy to Railway
      uses: bervProject/railway-deploy@v1.2.0
      with:
        railway_token: ${{ secrets.RAILWAY_TOKEN }}
        service: ${{ secrets.RAILWAY_SERVICE_ID_PROD }}

  post-deployment-tests:
    name: Post-deployment Tests
    runs-on: ubuntu-latest
    needs: [deploy-vercel-prod, deploy-railway-prod]
    
    steps:
    - name: Wait for deployment stabilization
      run: sleep 120
      
    - name: Health check - Vercel
      run: |
        response=$(curl -s -o /dev/null -w "%{http_code}" https://tcm-enterprise.vercel.app/api/health)
        if [ $response -eq 200 ]; then
          echo "✅ Vercel production health check passed"
        else
          echo "❌ Vercel production health check failed with status $response"
          exit 1
        fi
        
    - name: Health check - Railway
      run: |
        response=$(curl -s -o /dev/null -w "%{http_code}" ${{ secrets.RAILWAY_PROD_URL }}/api/health)
        if [ $response -eq 200 ]; then
          echo "✅ Railway production health check passed"
        else
          echo "❌ Railway production health check failed with status $response"
          exit 1
        fi
        
    - name: Performance check
      run: |
        # Basic performance check using curl timing
        time_total=$(curl -w "%{time_total}" -s -o /dev/null https://tcm-enterprise.vercel.app)
        if (( $(echo "$time_total < 3.0" | bc -l) )); then
          echo "✅ Performance check passed (${time_total}s)"
        else
          echo "⚠️ Performance check warning: slow response (${time_total}s)"
        fi

  rollback-on-failure:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [deploy-vercel-prod, deploy-railway-prod, post-deployment-tests]
    if: failure()
    
    steps:
    - name: Rollback Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        vercel-args: '--rollback'
        
    - name: Notify rollback
      run: |
        echo "🔄 Production deployment failed - automatic rollback initiated"
        echo "Please investigate the issue before attempting another deployment"

  notify-production-deployment:
    name: Notify Production Deployment
    runs-on: ubuntu-latest
    needs: [deploy-vercel-prod, deploy-railway-prod, post-deployment-tests]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.deploy-vercel-prod.result == 'success' && needs.deploy-railway-prod.result == 'success' && needs.post-deployment-tests.result == 'success' }}
      run: |
        echo "🎉 Production deployment successful!"
        echo "Vercel: https://tcm-enterprise.vercel.app"
        echo "Railway: ${{ secrets.RAILWAY_PROD_URL }}"
        echo "All health checks passed ✅"
        
    - name: Notify failure
      if: ${{ needs.deploy-vercel-prod.result == 'failure' || needs.deploy-railway-prod.result == 'failure' || needs.post-deployment-tests.result == 'failure' }}
      run: |
        echo "❌ Production deployment failed!"
        echo "Rollback procedures have been initiated"
        echo "Please review logs and fix issues before retry"

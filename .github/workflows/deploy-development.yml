name: Deploy to Development

on:
  push:
    branches: [ development ]
  workflow_dispatch:

jobs:
  deploy-vercel-dev:
    name: Deploy to Vercel Development
    runs-on: ubuntu-latest
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_ENV: development
        NEXT_PUBLIC_API_URL: ${{ secrets.DEV_API_URL }}
        
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./
        scope: ${{ secrets.VERCEL_ORG_ID }}
        alias-domains: tcm-enterprise-dev.vercel.app

  deploy-railway-dev:
    name: Deploy to Railway Development
    runs-on: ubuntu-latest
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_ENV: development
        NEXT_PUBLIC_API_URL: ${{ secrets.DEV_API_URL }}
        
    - name: Deploy to Railway
      uses: bervProject/railway-deploy@v1.2.0
      with:
        railway_token: ${{ secrets.RAILWAY_TOKEN }}
        service: ${{ secrets.RAILWAY_SERVICE_ID_DEV }}

  smoke-test:
    name: Smoke Test Development Deployment
    runs-on: ubuntu-latest
    needs: [deploy-vercel-dev, deploy-railway-dev]
    
    steps:
    - name: Wait for deployment
      run: sleep 60
      
    - name: Test Vercel deployment
      run: |
        response=$(curl -s -o /dev/null -w "%{http_code}" https://tcm-enterprise-dev.vercel.app)
        if [ $response -eq 200 ]; then
          echo "✅ Vercel development deployment successful"
        else
          echo "❌ Vercel development deployment failed with status $response"
          exit 1
        fi
        
    - name: Test Railway deployment
      run: |
        response=$(curl -s -o /dev/null -w "%{http_code}" ${{ secrets.RAILWAY_DEV_URL }})
        if [ $response -eq 200 ]; then
          echo "✅ Railway development deployment successful"
        else
          echo "❌ Railway development deployment failed with status $response"
          exit 1
        fi

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-vercel-dev, deploy-railway-dev, smoke-test]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.deploy-vercel-dev.result == 'success' && needs.deploy-railway-dev.result == 'success' && needs.smoke-test.result == 'success' }}
      run: |
        echo "🚀 Development deployment successful!"
        echo "Vercel: https://tcm-enterprise-dev.vercel.app"
        echo "Railway: ${{ secrets.RAILWAY_DEV_URL }}"
        
    - name: Notify failure
      if: ${{ needs.deploy-vercel-dev.result == 'failure' || needs.deploy-railway-dev.result == 'failure' || needs.smoke-test.result == 'failure' }}
      run: echo "❌ Development deployment failed! Please check logs."

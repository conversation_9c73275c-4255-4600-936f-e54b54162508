name: Continuous Integration

on:
  push:
    branches: [ development, main ]
  pull_request:
    branches: [ development, main ]

jobs:
  lint-and-test:
    name: <PERSON><PERSON>, Type Check & Test
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run TypeScript type check
      run: npm run type-check
      
    - name: Run tests
      run: npm run test
      
    - name: Build application
      run: npm run build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files-${{ matrix.node-version }}
        path: .next/
        retention-days: 1

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  accessibility-test:
    name: Accessibility Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Start application
      run: npm start &
      
    - name: Wait for application
      run: npx wait-on http://localhost:3000
      
    - name: Run accessibility tests
      run: npm run test:a11y

  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  notify-status:
    name: Notify Status
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan, accessibility-test, performance-test]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.lint-and-test.result == 'success' && needs.security-scan.result == 'success' && needs.accessibility-test.result == 'success' && needs.performance-test.result == 'success' }}
      run: echo "✅ All CI checks passed! Ready for review."
      
    - name: Notify failure
      if: ${{ needs.lint-and-test.result == 'failure' || needs.security-scan.result == 'failure' || needs.accessibility-test.result == 'failure' || needs.performance-test.result == 'failure' }}
      run: echo "❌ CI checks failed! Please review and fix issues."

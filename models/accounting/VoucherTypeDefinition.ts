import mongoose, { Document, Schema } from 'mongoose';

/**
 * Interface for voucher type field definition
 */
export interface IVoucherFieldDefinition {
  fieldName: string;
  fieldType: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object' | 'reference';
  label: string;
  description?: string;
  required: boolean;
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: string[];
    customValidator?: string;
  };
  displayOptions?: {
    order: number;
    group?: string;
    hidden?: boolean;
    readonly?: boolean;
    placeholder?: string;
    helpText?: string;
  };
  referenceModel?: string; // For reference fields
  dependsOn?: string[]; // Fields this field depends on
}

/**
 * Interface for voucher type definition
 */
export interface IVoucherTypeDefinition extends Document {
  typeId: string; // e.g., 'payroll_salary', 'payment_general'
  name: string;
  description: string;
  category: 'payment' | 'receipt' | 'journal' | 'adjustment';
  subCategory: string; // e.g., 'payroll', 'general', 'expense', 'utility'
  
  // Core behavior configuration
  configuration: {
    requiresApproval: boolean;
    approvalLevels: number;
    autoNumbering: boolean;
    numberPrefix: string;
    allowManualEntry: boolean;
    requiresAttachments: boolean;
    defaultFiscalYear: boolean;
  };
  
  // Dynamic field definitions
  customFields: IVoucherFieldDefinition[];
  
  // Integration settings
  integrationSettings: {
    sourceModule?: 'payroll' | 'procurement' | 'expense' | 'manual';
    apiEndpoint?: string; // For fetching related data
    dataTransformer?: string; // Function name for data transformation
    validationRules?: string[]; // Custom validation rules
  };
  
  // Display and UI configuration
  displayConfiguration: {
    formLayout: 'single-column' | 'two-column' | 'tabbed';
    fieldGroups: {
      groupName: string;
      fields: string[];
      collapsible: boolean;
      defaultExpanded: boolean;
    }[];
    listViewFields: string[]; // Fields to show in list view
    detailViewFields: string[]; // Fields to show in detail view
    exportFields: string[]; // Fields to include in exports
  };
  
  // Workflow configuration
  workflowConfiguration: {
    approvalWorkflow?: {
      levels: {
        level: number;
        roles: string[];
        amountThreshold?: number;
        required: boolean;
      }[];
    };
    notifications?: {
      onCreate: boolean;
      onApproval: boolean;
      onRejection: boolean;
      onPosting: boolean;
      recipients: string[]; // Roles or specific users
    };
  };
  
  // Status and metadata
  isActive: boolean;
  version: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for voucher field definition
 */
const VoucherFieldDefinitionSchema = new Schema({
  fieldName: {
    type: String,
    required: true,
    trim: true
  },
  fieldType: {
    type: String,
    required: true,
    enum: ['string', 'number', 'date', 'boolean', 'array', 'object', 'reference']
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  required: {
    type: Boolean,
    default: false
  },
  defaultValue: Schema.Types.Mixed,
  validation: {
    min: Number,
    max: Number,
    pattern: String,
    enum: [String],
    customValidator: String
  },
  displayOptions: {
    order: {
      type: Number,
      default: 0
    },
    group: String,
    hidden: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    placeholder: String,
    helpText: String
  },
  referenceModel: String,
  dependsOn: [String]
}, { _id: false });

/**
 * Schema for voucher type definition
 */
const VoucherTypeDefinitionSchema = new Schema({
  typeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['payment', 'receipt', 'journal', 'adjustment']
  },
  subCategory: {
    type: String,
    required: true,
    trim: true
  },
  
  configuration: {
    requiresApproval: {
      type: Boolean,
      default: true
    },
    approvalLevels: {
      type: Number,
      default: 1,
      min: 0,
      max: 5
    },
    autoNumbering: {
      type: Boolean,
      default: true
    },
    numberPrefix: {
      type: String,
      default: 'V'
    },
    allowManualEntry: {
      type: Boolean,
      default: true
    },
    requiresAttachments: {
      type: Boolean,
      default: false
    },
    defaultFiscalYear: {
      type: Boolean,
      default: true
    }
  },
  
  customFields: [VoucherFieldDefinitionSchema],
  
  integrationSettings: {
    sourceModule: {
      type: String,
      enum: ['payroll', 'procurement', 'expense', 'manual']
    },
    apiEndpoint: String,
    dataTransformer: String,
    validationRules: [String]
  },
  
  displayConfiguration: {
    formLayout: {
      type: String,
      enum: ['single-column', 'two-column', 'tabbed'],
      default: 'two-column'
    },
    fieldGroups: [{
      groupName: {
        type: String,
        required: true
      },
      fields: [String],
      collapsible: {
        type: Boolean,
        default: false
      },
      defaultExpanded: {
        type: Boolean,
        default: true
      }
    }],
    listViewFields: [String],
    detailViewFields: [String],
    exportFields: [String]
  },
  
  workflowConfiguration: {
    approvalWorkflow: {
      levels: [{
        level: {
          type: Number,
          required: true
        },
        roles: [String],
        amountThreshold: Number,
        required: {
          type: Boolean,
          default: true
        }
      }]
    },
    notifications: {
      onCreate: {
        type: Boolean,
        default: false
      },
      onApproval: {
        type: Boolean,
        default: false
      },
      onRejection: {
        type: Boolean,
        default: false
      },
      onPosting: {
        type: Boolean,
        default: false
      },
      recipients: [String]
    }
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  version: {
    type: Number,
    default: 1
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Create indexes
VoucherTypeDefinitionSchema.index({ typeId: 1 });
VoucherTypeDefinitionSchema.index({ category: 1, subCategory: 1 });
VoucherTypeDefinitionSchema.index({ isActive: 1 });

export const VoucherTypeDefinition = mongoose.models.VoucherTypeDefinition || 
  mongoose.model<IVoucherTypeDefinition>('VoucherTypeDefinition', VoucherTypeDefinitionSchema);

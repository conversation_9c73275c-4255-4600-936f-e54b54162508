import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for budget fund source tracking
 */
export interface IBudgetFundSource extends Document {
  sourceType: 'income' | 'expense';
  sourceId: mongoose.Types.ObjectId; // Reference to Income or Expense record
  sourceReference: string; // Income/Expense reference number
  sourceDescription: string;
  amount: number;
  status: 'draft' | 'approved' | 'received' | 'paid'; // Mirrors source status
  contributionType: 'projected' | 'expected' | 'actual';
  dateAdded: Date;
  dateUpdated: Date;
}

/**
 * Interface for budget fund category
 */
export interface IBudgetFundCategory extends Document {
  name: string;
  description?: string;
  type: 'income' | 'expense';
  source: 'government_subvention' | 'registration_fees' | 'licensing_fees' | 'donations' | 'personnel' | 'operations' | 'other';
  
  // Projected amounts (from draft income/expenses)
  projectedAmount: number;
  projectedSources: IBudgetFundSource[];
  
  // Expected amounts (from approved income/expenses)
  expectedAmount: number;
  expectedSources: IBudgetFundSource[];
  
  // Actual amounts (from received income/paid expenses)
  actualAmount: number;
  actualSources: IBudgetFundSource[];
  
  // Calculated fields
  totalProjected: number;
  totalExpected: number;
  totalActual: number;
  variance: number;
  utilizationPercentage: number;
  
  budgetFund: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Main BudgetFund interface - represents the overall budget derived from income/expense flows
 */
export interface IBudgetFund extends Document {
  name: string;
  description?: string;
  fiscalYear: string;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'closed' | 'archived';
  
  // Auto-generated from income/expense records
  autoGenerated: boolean;
  lastAutoUpdate: Date;
  
  // Summary totals
  totalProjectedIncome: number;    // Sum of all draft income
  totalExpectedIncome: number;     // Sum of all approved income
  totalActualIncome: number;       // Sum of all received income
  
  totalProjectedExpense: number;   // Sum of all draft expenses
  totalExpectedExpense: number;    // Sum of all approved expenses
  totalActualExpense: number;      // Sum of all paid expenses
  
  // Net calculations
  netProjected: number;            // Projected income - projected expenses
  netExpected: number;             // Expected income - expected expenses
  netActual: number;               // Actual income - actual expenses
  
  // Categories
  categories: mongoose.Types.ObjectId[];
  
  // Links to traditional budget (if exists)
  linkedBudget?: mongoose.Types.ObjectId;
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Budget Fund Source Schema
const BudgetFundSourceSchema: Schema = new Schema({
  sourceType: {
    type: String,
    required: true,
    enum: ['income', 'expense']
  },
  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'sourceType'
  },
  sourceReference: {
    type: String,
    required: true,
    trim: true
  },
  sourceDescription: {
    type: String,
    required: true,
    trim: true
  },
  amount: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'approved', 'received', 'paid']
  },
  contributionType: {
    type: String,
    required: true,
    enum: ['projected', 'expected', 'actual']
  },
  dateAdded: {
    type: Date,
    required: true,
    default: Date.now
  },
  dateUpdated: {
    type: Date,
    required: true,
    default: Date.now
  }
});

// Budget Fund Category Schema
const BudgetFundCategorySchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    type: {
      type: String,
      required: true,
      enum: ['income', 'expense']
    },
    source: {
      type: String,
      required: true,
      enum: ['government_subvention', 'registration_fees', 'licensing_fees', 'donations', 'personnel', 'operations', 'other']
    },
    
    // Projected amounts (from draft)
    projectedAmount: {
      type: Number,
      required: true,
      default: 0
    },
    projectedSources: [BudgetFundSourceSchema],
    
    // Expected amounts (from approved)
    expectedAmount: {
      type: Number,
      required: true,
      default: 0
    },
    expectedSources: [BudgetFundSourceSchema],
    
    // Actual amounts (from received/paid)
    actualAmount: {
      type: Number,
      required: true,
      default: 0
    },
    actualSources: [BudgetFundSourceSchema],
    
    // Calculated fields
    totalProjected: {
      type: Number,
      required: true,
      default: 0
    },
    totalExpected: {
      type: Number,
      required: true,
      default: 0
    },
    totalActual: {
      type: Number,
      required: true,
      default: 0
    },
    variance: {
      type: Number,
      required: true,
      default: 0
    },
    utilizationPercentage: {
      type: Number,
      required: true,
      default: 0
    },
    
    budgetFund: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetFund',
      required: true
    }
  },
  {
    timestamps: true
  }
);

// Main Budget Fund Schema
const BudgetFundSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    fiscalYear: {
      type: String,
      required: true,
      trim: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'closed', 'archived'],
      default: 'active'
    },
    
    autoGenerated: {
      type: Boolean,
      required: true,
      default: true
    },
    lastAutoUpdate: {
      type: Date,
      required: true,
      default: Date.now
    },
    
    // Summary totals
    totalProjectedIncome: {
      type: Number,
      required: true,
      default: 0
    },
    totalExpectedIncome: {
      type: Number,
      required: true,
      default: 0
    },
    totalActualIncome: {
      type: Number,
      required: true,
      default: 0
    },
    
    totalProjectedExpense: {
      type: Number,
      required: true,
      default: 0
    },
    totalExpectedExpense: {
      type: Number,
      required: true,
      default: 0
    },
    totalActualExpense: {
      type: Number,
      required: true,
      default: 0
    },
    
    // Net calculations
    netProjected: {
      type: Number,
      required: true,
      default: 0
    },
    netExpected: {
      type: Number,
      required: true,
      default: 0
    },
    netActual: {
      type: Number,
      required: true,
      default: 0
    },
    
    categories: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetFundCategory'
    }],
    
    linkedBudget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget'
    },
    
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true
  }
);

// Indexes for performance
BudgetFundSchema.index({ fiscalYear: 1 });
BudgetFundSchema.index({ status: 1 });
BudgetFundSchema.index({ startDate: 1, endDate: 1 });
BudgetFundCategorySchema.index({ budgetFund: 1, type: 1 });
BudgetFundCategorySchema.index({ source: 1 });

// Method to recalculate totals
BudgetFundSchema.methods.recalculateTotals = async function() {
  const BudgetFundCategory = mongoose.models.BudgetFundCategory;
  
  const categories = await BudgetFundCategory.find({ budgetFund: this._id });
  
  // Reset totals
  this.totalProjectedIncome = 0;
  this.totalExpectedIncome = 0;
  this.totalActualIncome = 0;
  this.totalProjectedExpense = 0;
  this.totalExpectedExpense = 0;
  this.totalActualExpense = 0;
  
  // Calculate totals from categories
  categories.forEach(category => {
    if (category.type === 'income') {
      this.totalProjectedIncome += category.projectedAmount;
      this.totalExpectedIncome += category.expectedAmount;
      this.totalActualIncome += category.actualAmount;
    } else {
      this.totalProjectedExpense += category.projectedAmount;
      this.totalExpectedExpense += category.expectedAmount;
      this.totalActualExpense += category.actualAmount;
    }
  });
  
  // Calculate net amounts
  this.netProjected = this.totalProjectedIncome - this.totalProjectedExpense;
  this.netExpected = this.totalExpectedIncome - this.totalExpectedExpense;
  this.netActual = this.totalActualIncome - this.totalActualExpense;
  
  this.lastAutoUpdate = new Date();
  
  return this.save();
};

// Method to recalculate category totals
BudgetFundCategorySchema.methods.recalculateTotals = function() {
  this.totalProjected = this.projectedAmount;
  this.totalExpected = this.expectedAmount;
  this.totalActual = this.actualAmount;
  
  // Calculate variance (actual vs expected)
  this.variance = this.actualAmount - this.expectedAmount;
  
  // Calculate utilization percentage
  this.utilizationPercentage = this.expectedAmount > 0 
    ? (this.actualAmount / this.expectedAmount) * 100 
    : 0;
    
  return this;
};

// Pre-save middleware to recalculate totals
BudgetFundCategorySchema.pre('save', function() {
  this.recalculateTotals();
});

export const BudgetFund = mongoose.models.BudgetFund || mongoose.model<IBudgetFund>('BudgetFund', BudgetFundSchema);
export const BudgetFundCategory = mongoose.models.BudgetFundCategory || mongoose.model<IBudgetFundCategory>('BudgetFundCategory', BudgetFundCategorySchema);

export default BudgetFund;

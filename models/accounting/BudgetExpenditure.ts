// models/accounting/BudgetExpenditure.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface IBudgetExpenditureStatusHistory {
  status: 'draft' | 'approved' | 'paid' | 'cancelled';
  changedBy: mongoose.Types.ObjectId;
  changedAt: Date;
  notes?: string;
  reason?: string;
}

export interface IBudgetExpenditure extends Document {
  // Reference to source expenditure
  sourceExpenditure: mongoose.Types.ObjectId;
  
  // Budget linking
  budget: mongoose.Types.ObjectId;
  budgetCategory: mongoose.Types.ObjectId;
  budgetSubcategory?: mongoose.Types.ObjectId;
  
  // Financial details
  amount: number;
  currency: string;
  exchangeRate: number;
  amountInBaseCurrency: number;
  
  // Expenditure details
  date: Date;
  reference: string;
  description?: string;
  category: string;
  subcategory?: string;
  
  // Status and workflow
  status: 'draft' | 'approved' | 'paid' | 'cancelled';
  contributionType: 'projected' | 'expected' | 'actual';
  
  // Budget impact (NEGATIVE for expenditures)
  budgetImpact: {
    impactAmount: number; // Negative value
    utilizationPercentage: number;
    varianceCreated: number;
    budgetedAmount: number;
    actualAmount: number;
  };
  
  // Status tracking
  statusHistory?: IBudgetExpenditureStatusHistory[];
  submittedAt?: Date;
  approvedAt?: Date;
  paidAt?: Date;
  cancelledAt?: Date;
  approvedBy?: mongoose.Types.ObjectId;
  paidBy?: mongoose.Types.ObjectId;
  cancelledBy?: mongoose.Types.ObjectId;
  
  // Metadata
  fiscalYear: string;
  appliedToBudget: boolean;
  paymentMethod?: string;
  vendor?: string;
  department?: string;
  notes?: string;
  
  // Audit fields
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const BudgetExpenditureSchema: Schema = new Schema(
  {
    sourceExpenditure: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Expense',
      required: true,
      index: true
    },
    budget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
      required: true,
      index: true
    },
    budgetCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetCategory',
      required: true,
      index: true
    },
    budgetSubcategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetSubcategory',
      index: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      required: true,
      default: 'MWK',
      uppercase: true
    },
    exchangeRate: {
      type: Number,
      required: true,
      default: 1,
      min: 0
    },
    amountInBaseCurrency: {
      type: Number,
      required: true,
      min: 0
    },
    date: {
      type: Date,
      required: true,
      index: true
    },
    reference: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    description: {
      type: String,
      trim: true
    },
    category: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    subcategory: {
      type: String,
      trim: true
    },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'approved', 'paid', 'cancelled'],
      default: 'draft',
      index: true
    },
    contributionType: {
      type: String,
      required: true,
      enum: ['projected', 'expected', 'actual'],
      default: 'projected',
      index: true
    },
    budgetImpact: {
      impactAmount: {
        type: Number,
        required: true
      },
      utilizationPercentage: {
        type: Number,
        default: 0
      },
      varianceCreated: {
        type: Number,
        default: 0
      },
      budgetedAmount: {
        type: Number,
        default: 0
      },
      actualAmount: {
        type: Number,
        default: 0
      }
    },
    statusHistory: [{
      status: {
        type: String,
        required: true,
        enum: ['draft', 'approved', 'paid', 'cancelled']
      },
      changedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      changedAt: {
        type: Date,
        required: true,
        default: Date.now
      },
      notes: {
        type: String,
        trim: true
      },
      reason: {
        type: String,
        trim: true
      }
    }],
    submittedAt: {
      type: Date,
      index: true
    },
    approvedAt: {
      type: Date,
      index: true
    },
    paidAt: {
      type: Date,
      index: true
    },
    cancelledAt: {
      type: Date,
      index: true
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    paidBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    fiscalYear: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    appliedToBudget: {
      type: Boolean,
      required: true,
      default: true,
      index: true
    },
    paymentMethod: {
      type: String,
      trim: true
    },
    vendor: {
      type: String,
      trim: true
    },
    department: {
      type: String,
      trim: true,
      index: true
    },
    notes: {
      type: String,
      trim: true
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes for better query performance
BudgetExpenditureSchema.index({ date: -1, status: 1 });
BudgetExpenditureSchema.index({ budget: 1, budgetCategory: 1 });
BudgetExpenditureSchema.index({ sourceExpenditure: 1 });
BudgetExpenditureSchema.index({ fiscalYear: 1, status: 1 });
BudgetExpenditureSchema.index({ department: 1, category: 1 });
BudgetExpenditureSchema.index({ vendor: 1 });
BudgetExpenditureSchema.index({ amount: -1 });
BudgetExpenditureSchema.index({ createdAt: -1 });
BudgetExpenditureSchema.index({ contributionType: 1, status: 1 });

// Virtual for total budget impact (always negative for expenditures)
BudgetExpenditureSchema.virtual('totalBudgetImpact').get(function() {
  return -Math.abs(this.budgetImpact.impactAmount);
});

// Virtual for budget utilization percentage
BudgetExpenditureSchema.virtual('budgetUtilizationPercentage').get(function() {
  if (this.budgetImpact.budgetedAmount <= 0) return 0;
  return (Math.abs(this.budgetImpact.actualAmount) / this.budgetImpact.budgetedAmount) * 100;
});

// Virtual for variance percentage
BudgetExpenditureSchema.virtual('variancePercentage').get(function() {
  if (this.budgetImpact.budgetedAmount <= 0) return 0;
  return (this.budgetImpact.varianceCreated / this.budgetImpact.budgetedAmount) * 100;
});

// Instance methods
BudgetExpenditureSchema.methods.updateStatus = function(
  newStatus: 'draft' | 'approved' | 'paid' | 'cancelled',
  changedBy: mongoose.Types.ObjectId,
  notes?: string,
  reason?: string
) {
  // Add to status history
  if (!this.statusHistory) {
    this.statusHistory = [];
  }

  this.statusHistory.push({
    status: newStatus,
    changedBy,
    changedAt: new Date(),
    notes,
    reason
  });

  // Update status and timestamp fields
  this.status = newStatus;

  switch (newStatus) {
    case 'approved':
      this.approvedAt = new Date();
      this.approvedBy = changedBy;
      this.contributionType = 'expected';
      break;
    case 'paid':
      this.paidAt = new Date();
      this.paidBy = changedBy;
      this.contributionType = 'actual';
      break;
    case 'cancelled':
      this.cancelledAt = new Date();
      this.cancelledBy = changedBy;
      break;
  }

  return this;
};

BudgetExpenditureSchema.methods.calculateBudgetImpact = function(budgetCategory: any) {
  const impactAmount = -Math.abs(this.amountInBaseCurrency); // Always negative
  const budgetedAmount = budgetCategory?.budgetedAmount || 0;
  const actualAmount = budgetCategory?.actualAmount || 0;

  this.budgetImpact = {
    impactAmount,
    utilizationPercentage: budgetedAmount > 0 ? (Math.abs(actualAmount) / budgetedAmount) * 100 : 0,
    varianceCreated: actualAmount - budgetedAmount,
    budgetedAmount,
    actualAmount
  };

  return this;
};

// Static methods
BudgetExpenditureSchema.statics.findByBudget = function(budgetId: string) {
  return this.find({ budget: budgetId }).populate('budgetCategory budgetSubcategory sourceExpenditure');
};

BudgetExpenditureSchema.statics.findByCategory = function(categoryId: string) {
  return this.find({ budgetCategory: categoryId }).populate('budget sourceExpenditure');
};

BudgetExpenditureSchema.statics.findByStatus = function(status: string) {
  return this.find({ status }).populate('budget budgetCategory sourceExpenditure');
};

BudgetExpenditureSchema.statics.findByFiscalYear = function(fiscalYear: string) {
  return this.find({ fiscalYear }).populate('budget budgetCategory sourceExpenditure');
};

BudgetExpenditureSchema.statics.getTotalByCategory = function(categoryId: string, status?: string) {
  const match: any = { budgetCategory: categoryId };
  if (status) {
    match.status = status;
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$budgetCategory',
        totalAmount: { $sum: '$amountInBaseCurrency' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amountInBaseCurrency' }
      }
    }
  ]);
};

BudgetExpenditureSchema.statics.getTotalByBudget = function(budgetId: string, status?: string) {
  const match: any = { budget: budgetId };
  if (status) {
    match.status = status;
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$budget',
        totalAmount: { $sum: '$amountInBaseCurrency' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amountInBaseCurrency' }
      }
    }
  ]);
};

export default mongoose.models.BudgetExpenditure || mongoose.model<IBudgetExpenditure>('BudgetExpenditure', BudgetExpenditureSchema);

import mongoose, { Schema, Document, Model } from 'mongoose';

// Purchase order status
export enum PurchaseOrderStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SENT_TO_VENDOR = 'sent_to_vendor',
  ACKNOWLEDGED = 'acknowledged',
  IN_PROGRESS = 'in_progress',
  PARTIALLY_RECEIVED = 'partially_received',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  CLOSED = 'closed'
}

// Purchase order type
export enum PurchaseOrderType {
  GOODS = 'goods',
  SERVICES = 'services',
  MAINTENANCE = 'maintenance',
  CONSULTING = 'consulting',
  UTILITIES = 'utilities',
  TRAVEL = 'travel',
  OTHER = 'other'
}

// Priority levels
export enum PurchaseOrderPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

// Delivery terms
export enum DeliveryTerms {
  EXW = 'exw', // Ex Works
  FCA = 'fca', // Free Carrier
  CPT = 'cpt', // Carriage Paid To
  CIP = 'cip', // Carriage and Insurance Paid To
  DAP = 'dap', // Delivered at Place
  DPU = 'dpu', // Delivered at Place Unloaded
  DDP = 'ddp', // Delivered Duty Paid
  PICKUP = 'pickup',
  DELIVERY = 'delivery'
}

// Line item interface
export interface PurchaseOrderLineItem {
  id: string;
  itemNumber?: string;
  description: string;
  specification?: string;
  category: string;
  subcategory?: string;
  quantity: number;
  unitOfMeasure: string;
  unitPrice: number;
  totalPrice: number;
  taxRate: number;
  taxAmount: number;
  discountRate: number;
  discountAmount: number;
  netAmount: number;
  budgetCode?: string;
  accountCode?: string;
  deliveryDate?: Date;
  receivedQuantity: number;
  remainingQuantity: number;
  isCompleted: boolean;
  notes?: string;
}

// Approval step interface
export interface ApprovalStep {
  stepNumber: number;
  approverRole: string;
  approverUserId?: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected' | 'skipped';
  approvedAt?: Date;
  rejectedAt?: Date;
  comments?: string;
  amountLimit?: number;
  isRequired: boolean;
  canDelegate: boolean;
  delegatedTo?: string;
}

// Delivery information
export interface DeliveryInfo {
  deliveryAddress: {
    street: string;
    city: string;
    state?: string;
    postalCode?: string;
    country: string;
    contactPerson?: string;
    contactPhone?: string;
  };
  deliveryTerms: DeliveryTerms;
  requestedDeliveryDate: Date;
  promisedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  deliveryInstructions?: string;
  shippingMethod?: string;
  trackingNumber?: string;
}

// Terms and conditions
export interface TermsAndConditions {
  paymentTerms: string;
  deliveryTerms: string;
  warrantyTerms?: string;
  returnPolicy?: string;
  penaltyClause?: string;
  customTerms: string[];
}

// Purchase order interface
export interface IPurchaseOrder extends Document {
  // Basic Information
  purchaseOrderNumber: string;
  title: string;
  description: string;
  type: PurchaseOrderType;
  priority: PurchaseOrderPriority;
  
  // Dates
  orderDate: Date;
  requiredDate: Date;
  promisedDate?: Date;
  completedDate?: Date;
  
  // Status and Workflow
  status: PurchaseOrderStatus;
  approvalWorkflow: ApprovalStep[];
  currentApprovalStep: number;
  
  // Requestor Information
  requestedBy: string;
  requestedByName: string;
  requestedByEmail: string;
  department: string;
  costCenter?: string;
  
  // Vendor Information
  vendorId: string;
  vendorName: string;
  vendorContact: {
    name: string;
    email: string;
    phone: string;
  };
  
  // Financial Information
  currency: string;
  exchangeRate: number;
  subtotal: number;
  totalTax: number;
  totalDiscount: number;
  shippingCost: number;
  otherCharges: number;
  totalAmount: number;
  amountInBaseCurrency: number;
  
  // Line Items
  lineItems: PurchaseOrderLineItem[];
  
  // Delivery Information
  deliveryInfo: DeliveryInfo;
  
  // Terms and Conditions
  termsAndConditions: TermsAndConditions;
  
  // Budget and Allocation
  budgetAllocations: Array<{
    budgetId: string;
    budgetName: string;
    categoryId: string;
    categoryName: string;
    allocatedAmount: number;
    percentage: number;
  }>;
  
  // Documents and Attachments
  attachments: Array<{
    id: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    uploadedAt: Date;
    uploadedBy: string;
    category: string;
  }>;
  
  // Tracking and Progress
  progressPercentage: number;
  receivedItems: number;
  totalItems: number;
  
  // Audit Trail
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
  
  // Additional Metadata
  tags: string[];
  notes: string[];
  isUrgent: boolean;
  requiresInspection: boolean;
  projectId?: string;
  contractId?: string;
  
  // Methods
  calculateTotals(): void;
  addLineItem(item: Omit<PurchaseOrderLineItem, 'id'>): void;
  removeLineItem(itemId: string): void;
  updateLineItem(itemId: string, updates: Partial<PurchaseOrderLineItem>): void;
  canBeApprovedBy(userId: string, userRole: string): boolean;
  getNextApprover(): ApprovalStep | null;
  updateApprovalStatus(stepNumber: number, status: 'approved' | 'rejected', comments?: string): void;
  markAsReceived(itemId: string, receivedQuantity: number): void;
  calculateProgress(): number;
  generatePurchaseOrderNumber(): string;
}

// Purchase order schema
const PurchaseOrderSchema = new Schema<IPurchaseOrder>({
  purchaseOrderNumber: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: Object.values(PurchaseOrderType),
    required: true,
    index: true
  },
  priority: {
    type: String,
    enum: Object.values(PurchaseOrderPriority),
    default: PurchaseOrderPriority.MEDIUM,
    index: true
  },
  orderDate: {
    type: Date,
    required: true,
    index: true
  },
  requiredDate: {
    type: Date,
    required: true
  },
  promisedDate: Date,
  completedDate: Date,
  status: {
    type: String,
    enum: Object.values(PurchaseOrderStatus),
    default: PurchaseOrderStatus.DRAFT,
    index: true
  },
  approvalWorkflow: [{
    stepNumber: {
      type: Number,
      required: true
    },
    approverRole: {
      type: String,
      required: true
    },
    approverUserId: String,
    approverName: String,
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'skipped'],
      default: 'pending'
    },
    approvedAt: Date,
    rejectedAt: Date,
    comments: String,
    amountLimit: Number,
    isRequired: {
      type: Boolean,
      default: true
    },
    canDelegate: {
      type: Boolean,
      default: false
    },
    delegatedTo: String
  }],
  currentApprovalStep: {
    type: Number,
    default: 0
  },
  requestedBy: {
    type: String,
    required: true,
    index: true
  },
  requestedByName: {
    type: String,
    required: true
  },
  requestedByEmail: {
    type: String,
    required: true
  },
  department: {
    type: String,
    required: true,
    index: true
  },
  costCenter: String,
  vendorId: {
    type: String,
    required: true,
    index: true
  },
  vendorName: {
    type: String,
    required: true
  },
  vendorContact: {
    name: String,
    email: String,
    phone: String
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK'
  },
  exchangeRate: {
    type: Number,
    default: 1,
    min: 0
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  totalTax: {
    type: Number,
    default: 0,
    min: 0
  },
  totalDiscount: {
    type: Number,
    default: 0,
    min: 0
  },
  shippingCost: {
    type: Number,
    default: 0,
    min: 0
  },
  otherCharges: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0,
    index: true
  },
  amountInBaseCurrency: {
    type: Number,
    required: true,
    min: 0
  },
  lineItems: [{
    id: {
      type: String,
      required: true
    },
    itemNumber: String,
    description: {
      type: String,
      required: true
    },
    specification: String,
    category: {
      type: String,
      required: true
    },
    subcategory: String,
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unitOfMeasure: {
      type: String,
      required: true
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0
    },
    taxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    taxAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    discountRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    discountAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    netAmount: {
      type: Number,
      required: true,
      min: 0
    },
    budgetCode: String,
    accountCode: String,
    deliveryDate: Date,
    receivedQuantity: {
      type: Number,
      default: 0,
      min: 0
    },
    remainingQuantity: {
      type: Number,
      required: true,
      min: 0
    },
    isCompleted: {
      type: Boolean,
      default: false
    },
    notes: String
  }],
  deliveryInfo: {
    deliveryAddress: {
      street: String,
      city: String,
      state: String,
      postalCode: String,
      country: {
        type: String,
        default: 'Malawi'
      },
      contactPerson: String,
      contactPhone: String
    },
    deliveryTerms: {
      type: String,
      enum: Object.values(DeliveryTerms),
      default: DeliveryTerms.DELIVERY
    },
    requestedDeliveryDate: Date,
    promisedDeliveryDate: Date,
    actualDeliveryDate: Date,
    deliveryInstructions: String,
    shippingMethod: String,
    trackingNumber: String
  },
  termsAndConditions: {
    paymentTerms: String,
    deliveryTerms: String,
    warrantyTerms: String,
    returnPolicy: String,
    penaltyClause: String,
    customTerms: [String]
  },
  budgetAllocations: [{
    budgetId: {
      type: String,
      required: true
    },
    budgetName: {
      type: String,
      required: true
    },
    categoryId: {
      type: String,
      required: true
    },
    categoryName: {
      type: String,
      required: true
    },
    allocatedAmount: {
      type: Number,
      required: true,
      min: 0
    },
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    }
  }],
  attachments: [{
    id: String,
    filename: String,
    originalName: String,
    mimeType: String,
    size: Number,
    url: String,
    uploadedAt: Date,
    uploadedBy: String,
    category: String
  }],
  progressPercentage: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  receivedItems: {
    type: Number,
    default: 0,
    min: 0
  },
  totalItems: {
    type: Number,
    default: 0,
    min: 0
  },
  createdBy: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedBy: {
    type: String,
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  tags: [String],
  notes: [String],
  isUrgent: {
    type: Boolean,
    default: false,
    index: true
  },
  requiresInspection: {
    type: Boolean,
    default: false
  },
  projectId: String,
  contractId: String
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
PurchaseOrderSchema.index({ orderDate: -1, status: 1 });
PurchaseOrderSchema.index({ requestedBy: 1, status: 1 });
PurchaseOrderSchema.index({ department: 1, type: 1 });
PurchaseOrderSchema.index({ vendorId: 1 });
PurchaseOrderSchema.index({ totalAmount: -1 });
PurchaseOrderSchema.index({ requiredDate: 1, status: 1 });

// Methods
PurchaseOrderSchema.methods.calculateTotals = function(): void {
  this.subtotal = this.lineItems.reduce((sum, item) => sum + item.totalPrice, 0);
  this.totalTax = this.lineItems.reduce((sum, item) => sum + item.taxAmount, 0);
  this.totalDiscount = this.lineItems.reduce((sum, item) => sum + item.discountAmount, 0);
  this.totalAmount = this.subtotal + this.totalTax - this.totalDiscount + this.shippingCost + this.otherCharges;
  this.amountInBaseCurrency = this.totalAmount * this.exchangeRate;
  this.totalItems = this.lineItems.length;
};

PurchaseOrderSchema.methods.addLineItem = function(item: Omit<PurchaseOrderLineItem, 'id'>): void {
  const newItem = {
    id: new mongoose.Types.ObjectId().toString(),
    ...item,
    receivedQuantity: 0,
    remainingQuantity: item.quantity,
    isCompleted: false
  };
  this.lineItems.push(newItem);
  this.calculateTotals();
};

PurchaseOrderSchema.methods.removeLineItem = function(itemId: string): void {
  this.lineItems = this.lineItems.filter(item => item.id !== itemId);
  this.calculateTotals();
};

PurchaseOrderSchema.methods.updateLineItem = function(itemId: string, updates: Partial<PurchaseOrderLineItem>): void {
  const item = this.lineItems.find(item => item.id === itemId);
  if (item) {
    Object.assign(item, updates);
    if (updates.quantity !== undefined || updates.receivedQuantity !== undefined) {
      item.remainingQuantity = item.quantity - item.receivedQuantity;
      item.isCompleted = item.remainingQuantity <= 0;
    }
    this.calculateTotals();
  }
};

PurchaseOrderSchema.methods.canBeApprovedBy = function(userId: string, userRole: string): boolean {
  const currentStep = this.approvalWorkflow[this.currentApprovalStep];
  if (!currentStep) return false;
  
  return (currentStep.approverUserId === userId || currentStep.approverRole === userRole) &&
         currentStep.status === 'pending';
};

PurchaseOrderSchema.methods.getNextApprover = function(): ApprovalStep | null {
  return this.approvalWorkflow[this.currentApprovalStep] || null;
};

PurchaseOrderSchema.methods.updateApprovalStatus = function(
  stepNumber: number, 
  status: 'approved' | 'rejected', 
  comments?: string
): void {
  const step = this.approvalWorkflow.find(s => s.stepNumber === stepNumber);
  if (step) {
    step.status = status;
    step.comments = comments;
    if (status === 'approved') {
      step.approvedAt = new Date();
      this.currentApprovalStep++;
      if (this.currentApprovalStep >= this.approvalWorkflow.length) {
        this.status = PurchaseOrderStatus.APPROVED;
      }
    } else {
      step.rejectedAt = new Date();
      this.status = PurchaseOrderStatus.REJECTED;
    }
  }
};

PurchaseOrderSchema.methods.markAsReceived = function(itemId: string, receivedQuantity: number): void {
  const item = this.lineItems.find(item => item.id === itemId);
  if (item) {
    item.receivedQuantity += receivedQuantity;
    item.remainingQuantity = Math.max(0, item.quantity - item.receivedQuantity);
    item.isCompleted = item.remainingQuantity <= 0;
    
    this.receivedItems = this.lineItems.filter(item => item.isCompleted).length;
    this.progressPercentage = this.calculateProgress();
    
    if (this.receivedItems === this.totalItems) {
      this.status = PurchaseOrderStatus.COMPLETED;
      this.completedDate = new Date();
    } else if (this.receivedItems > 0) {
      this.status = PurchaseOrderStatus.PARTIALLY_RECEIVED;
    }
  }
};

PurchaseOrderSchema.methods.calculateProgress = function(): number {
  if (this.totalItems === 0) return 0;
  const totalReceived = this.lineItems.reduce((sum, item) => sum + item.receivedQuantity, 0);
  const totalOrdered = this.lineItems.reduce((sum, item) => sum + item.quantity, 0);
  return totalOrdered > 0 ? (totalReceived / totalOrdered) * 100 : 0;
};

PurchaseOrderSchema.methods.generatePurchaseOrderNumber = function(): string {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `PO-${year}${month}-${random}`;
};

// Pre-save middleware
PurchaseOrderSchema.pre('save', function(next) {
  if (this.isNew && !this.purchaseOrderNumber) {
    this.purchaseOrderNumber = this.generatePurchaseOrderNumber();
  }
  
  // Calculate totals
  this.calculateTotals();
  
  // Update progress
  this.progressPercentage = this.calculateProgress();
  
  // Update timestamp
  this.updatedAt = new Date();
  
  next();
});

// Create and export the model
const PurchaseOrder: Model<IPurchaseOrder> = mongoose.models.PurchaseOrder || 
  mongoose.model<IPurchaseOrder>('PurchaseOrder', PurchaseOrderSchema);

export default PurchaseOrder;
export { PurchaseOrderSchema };

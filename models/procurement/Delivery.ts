import mongoose, { Document, Schema } from 'mongoose';

// Delivery item interface for tracking individual items in a delivery
export interface IDeliveryItem {
  purchaseOrderItemId: mongoose.Types.ObjectId;
  itemName: string;
  itemCode?: string;
  itemDescription?: string;
  quantityOrdered: number;
  quantityDelivered: number;
  quantityAccepted: number;
  quantityRejected: number;
  quantityPending: number;
  unitPrice: number;
  totalValue: number;
  rejectionReason?: string;
  condition: 'good' | 'damaged' | 'defective' | 'incomplete' | 'expired';
  expiryDate?: Date;
  batchNumber?: string;
  serialNumbers?: string[];
  notes?: string;
}

// Quality inspection interface
export interface IQualityInspection {
  inspectedBy: mongoose.Types.ObjectId;
  inspectionDate: Date;
  inspectionType: 'visual' | 'functional' | 'compliance' | 'full';
  overallRating: 'excellent' | 'good' | 'fair' | 'poor' | 'failed';
  criteria: Array<{
    criterion: string;
    rating: 'pass' | 'fail' | 'conditional';
    notes?: string;
  }>;
  defectsFound: Array<{
    type: 'cosmetic' | 'functional' | 'safety' | 'compliance';
    severity: 'minor' | 'major' | 'critical';
    description: string;
    quantity: number;
  }>;
  recommendations: string[];
  certificatesRequired: boolean;
  certificatesReceived: boolean;
  inspectionNotes: string;
}

// Main Delivery interface
export interface IDelivery extends Document {
  deliveryNumber: string;
  purchaseOrderId: mongoose.Types.ObjectId;
  supplierId: mongoose.Types.ObjectId;
  contractId?: mongoose.Types.ObjectId;
  
  // Delivery scheduling
  expectedDate: Date;
  promisedDate?: Date;
  actualDate?: Date;
  
  // Delivery status and tracking
  status: 'scheduled' | 'in_transit' | 'delivered' | 'partially_delivered' | 'delayed' | 'cancelled' | 'returned';
  deliveryType: 'full' | 'partial' | 'split' | 'emergency';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  
  // Shipping and logistics
  trackingNumber?: string;
  carrier?: string;
  shippingMethod: 'standard' | 'express' | 'overnight' | 'pickup' | 'direct';
  shippingCost?: number;
  
  // Delivery location and contact
  deliveryAddress: {
    street: string;
    city: string;
    state?: string;
    postalCode?: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contactPerson: string;
  contactPhone: string;
  contactEmail?: string;
  
  // Items and quantities
  items: IDeliveryItem[];
  totalItems: number;
  totalValue: number;
  currency: string;
  
  // Goods receipt and inspection
  receivedBy?: mongoose.Types.ObjectId;
  receivedDate?: Date;
  goodsReceiptNumber?: string;
  qualityInspection?: IQualityInspection;
  
  // Documentation and compliance
  packingList: boolean;
  invoice: boolean;
  deliveryNote: boolean;
  qualityCertificates: boolean;
  customsDocuments: boolean;
  
  // Attachments and documents
  attachments: Array<{
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    uploadedBy: mongoose.Types.ObjectId;
    uploadedAt: Date;
    description?: string;
    category: 'packing_list' | 'invoice' | 'delivery_note' | 'certificate' | 'photo' | 'other';
  }>;
  
  // Performance tracking
  deliveryPerformance: {
    onTimeDelivery: boolean;
    daysEarly?: number;
    daysLate?: number;
    accuracyRate: number; // Percentage of items delivered correctly
    qualityScore?: number; // Based on inspection results
  };
  
  // Issues and exceptions
  issues: Array<{
    type: 'delay' | 'damage' | 'shortage' | 'quality' | 'documentation' | 'other';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    reportedBy: mongoose.Types.ObjectId;
    reportedDate: Date;
    resolution?: string;
    resolvedBy?: mongoose.Types.ObjectId;
    resolvedDate?: Date;
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
  }>;
  
  // Financial information
  invoiceAmount?: number;
  paidAmount?: number;
  paymentStatus: 'pending' | 'partial' | 'paid' | 'disputed';
  paymentDueDate?: Date;
  
  // Audit trail
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  
  // Additional metadata
  tags?: string[];
  notes?: string;
  internalNotes?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

// Delivery item subdocument schema
const DeliveryItemSchema = new Schema({
  purchaseOrderItemId: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true
  },
  itemName: {
    type: String,
    required: true,
    trim: true
  },
  itemCode: {
    type: String,
    trim: true,
    uppercase: true
  },
  itemDescription: {
    type: String,
    trim: true
  },
  quantityOrdered: {
    type: Number,
    required: true,
    min: 0
  },
  quantityDelivered: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  quantityAccepted: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  quantityRejected: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  quantityPending: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0
  },
  totalValue: {
    type: Number,
    required: true,
    min: 0
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  condition: {
    type: String,
    enum: ['good', 'damaged', 'defective', 'incomplete', 'expired'],
    default: 'good'
  },
  expiryDate: Date,
  batchNumber: {
    type: String,
    trim: true,
    uppercase: true
  },
  serialNumbers: [{
    type: String,
    trim: true,
    uppercase: true
  }],
  notes: {
    type: String,
    trim: true
  }
}, { _id: false });

// Quality inspection subdocument schema
const QualityInspectionSchema = new Schema({
  inspectedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  inspectionDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  inspectionType: {
    type: String,
    enum: ['visual', 'functional', 'compliance', 'full'],
    required: true
  },
  overallRating: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'failed'],
    required: true
  },
  criteria: [{
    criterion: {
      type: String,
      required: true,
      trim: true
    },
    rating: {
      type: String,
      enum: ['pass', 'fail', 'conditional'],
      required: true
    },
    notes: {
      type: String,
      trim: true
    }
  }],
  defectsFound: [{
    type: {
      type: String,
      enum: ['cosmetic', 'functional', 'safety', 'compliance'],
      required: true
    },
    severity: {
      type: String,
      enum: ['minor', 'major', 'critical'],
      required: true
    },
    description: {
      type: String,
      required: true,
      trim: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    }
  }],
  recommendations: [{
    type: String,
    trim: true
  }],
  certificatesRequired: {
    type: Boolean,
    default: false
  },
  certificatesReceived: {
    type: Boolean,
    default: false
  },
  inspectionNotes: {
    type: String,
    required: true,
    trim: true
  }
}, { _id: false });

// Delivery address subdocument schema
const DeliveryAddressSchema = new Schema({
  street: {
    type: String,
    required: true,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    trim: true
  },
  postalCode: {
    type: String,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true,
    default: 'Malawi'
  },
  coordinates: {
    latitude: {
      type: Number,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      min: -180,
      max: 180
    }
  }
}, { _id: false });

// Attachment subdocument schema
const AttachmentSchema = new Schema({
  fileName: {
    type: String,
    required: true,
    trim: true
  },
  filePath: {
    type: String,
    required: true,
    trim: true
  },
  fileType: {
    type: String,
    required: true,
    trim: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    enum: ['packing_list', 'invoice', 'delivery_note', 'certificate', 'photo', 'other'],
    default: 'other'
  }
}, { _id: false });

// Performance tracking subdocument schema
const DeliveryPerformanceSchema = new Schema({
  onTimeDelivery: {
    type: Boolean,
    required: true,
    default: false
  },
  daysEarly: {
    type: Number,
    min: 0
  },
  daysLate: {
    type: Number,
    min: 0
  },
  accuracyRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 0
  },
  qualityScore: {
    type: Number,
    min: 0,
    max: 100
  }
}, { _id: false });

// Issues subdocument schema
const IssueSchema = new Schema({
  type: {
    type: String,
    enum: ['delay', 'damage', 'shortage', 'quality', 'documentation', 'other'],
    required: true
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  reportedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reportedDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  resolution: {
    type: String,
    trim: true
  },
  resolvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolvedDate: Date,
  status: {
    type: String,
    enum: ['open', 'in_progress', 'resolved', 'closed'],
    default: 'open'
  }
}, { _id: false });

// Main Delivery schema
const DeliverySchema = new Schema<IDelivery>({
  deliveryNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  purchaseOrderId: {
    type: Schema.Types.ObjectId,
    ref: 'PurchaseOrder',
    required: true,
    index: true
  },
  supplierId: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true,
    index: true
  },
  contractId: {
    type: Schema.Types.ObjectId,
    ref: 'Contract',
    index: true
  },
  expectedDate: {
    type: Date,
    required: true,
    index: true
  },
  promisedDate: Date,
  actualDate: {
    type: Date,
    index: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in_transit', 'delivered', 'partially_delivered', 'delayed', 'cancelled', 'returned'],
    default: 'scheduled',
    index: true
  },
  deliveryType: {
    type: String,
    enum: ['full', 'partial', 'split', 'emergency'],
    default: 'full'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  },
  trackingNumber: {
    type: String,
    trim: true,
    uppercase: true,
    index: true
  },
  carrier: {
    type: String,
    trim: true
  },
  shippingMethod: {
    type: String,
    enum: ['standard', 'express', 'overnight', 'pickup', 'direct'],
    default: 'standard'
  },
  shippingCost: {
    type: Number,
    min: 0
  },
  deliveryAddress: {
    type: DeliveryAddressSchema,
    required: true
  },
  contactPerson: {
    type: String,
    required: true,
    trim: true
  },
  contactPhone: {
    type: String,
    required: true,
    trim: true
  },
  contactEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  items: {
    type: [DeliveryItemSchema],
    required: true,
    validate: {
      validator: function(items: IDeliveryItem[]) {
        return items.length > 0;
      },
      message: 'At least one delivery item is required'
    }
  },
  totalItems: {
    type: Number,
    required: true,
    min: 0
  },
  totalValue: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    uppercase: true,
    maxlength: 3
  },
  receivedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  receivedDate: Date,
  goodsReceiptNumber: {
    type: String,
    trim: true,
    uppercase: true
  },
  qualityInspection: QualityInspectionSchema,
  packingList: {
    type: Boolean,
    default: false
  },
  invoice: {
    type: Boolean,
    default: false
  },
  deliveryNote: {
    type: Boolean,
    default: false
  },
  qualityCertificates: {
    type: Boolean,
    default: false
  },
  customsDocuments: {
    type: Boolean,
    default: false
  },
  attachments: [AttachmentSchema],
  deliveryPerformance: {
    type: DeliveryPerformanceSchema,
    default: () => ({})
  },
  issues: [IssueSchema],
  invoiceAmount: {
    type: Number,
    min: 0
  },
  paidAmount: {
    type: Number,
    min: 0,
    default: 0
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'partial', 'paid', 'disputed'],
    default: 'pending'
  },
  paymentDueDate: Date,
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  notes: {
    type: String,
    trim: true
  },
  internalNotes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
DeliverySchema.index({ deliveryNumber: 1 });
DeliverySchema.index({ purchaseOrderId: 1 });
DeliverySchema.index({ supplierId: 1 });
DeliverySchema.index({ status: 1 });
DeliverySchema.index({ expectedDate: 1 });
DeliverySchema.index({ actualDate: 1 });
DeliverySchema.index({ trackingNumber: 1 });
DeliverySchema.index({ priority: 1 });
DeliverySchema.index({ createdBy: 1 });

// Compound indexes
DeliverySchema.index({ supplierId: 1, status: 1 });
DeliverySchema.index({ purchaseOrderId: 1, status: 1 });
DeliverySchema.index({ expectedDate: 1, status: 1 });
DeliverySchema.index({ status: 1, priority: 1 });

// Virtual for delivery status display
DeliverySchema.virtual('statusDisplay').get(function() {
  const today = new Date();

  if (this.status === 'scheduled' && this.expectedDate < today) {
    return 'overdue';
  }

  if (this.status === 'in_transit' && this.expectedDate < today) {
    return 'delayed_in_transit';
  }

  return this.status;
});

// Virtual for days until/since expected delivery
DeliverySchema.virtual('daysFromExpected').get(function() {
  if (this.expectedDate) {
    const today = new Date();
    const diffTime = this.expectedDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for completion percentage
DeliverySchema.virtual('completionPercentage').get(function() {
  if (this.items && this.items.length > 0) {
    const totalOrdered = this.items.reduce((sum, item) => sum + item.quantityOrdered, 0);
    const totalDelivered = this.items.reduce((sum, item) => sum + item.quantityDelivered, 0);
    return totalOrdered > 0 ? Math.round((totalDelivered / totalOrdered) * 100) : 0;
  }
  return 0;
});

// Virtual for acceptance rate
DeliverySchema.virtual('acceptanceRate').get(function() {
  if (this.items && this.items.length > 0) {
    const totalDelivered = this.items.reduce((sum, item) => sum + item.quantityDelivered, 0);
    const totalAccepted = this.items.reduce((sum, item) => sum + item.quantityAccepted, 0);
    return totalDelivered > 0 ? Math.round((totalAccepted / totalDelivered) * 100) : 0;
  }
  return 0;
});

// Pre-save middleware to generate delivery number
DeliverySchema.pre('save', async function(next) {
  if (this.isNew && !this.deliveryNumber) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const count = await mongoose.model('Delivery').countDocuments({
      deliveryNumber: new RegExp(`^DEL-${year}${month}-`)
    });
    this.deliveryNumber = `DEL-${year}${month}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Pre-save middleware to calculate totals
DeliverySchema.pre('save', function(next) {
  if (this.items && this.items.length > 0) {
    this.totalItems = this.items.reduce((sum, item) => sum + item.quantityDelivered, 0);
    this.totalValue = this.items.reduce((sum, item) => sum + item.totalValue, 0);

    // Calculate delivery performance
    if (this.actualDate && this.expectedDate) {
      const diffTime = this.actualDate.getTime() - this.expectedDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      this.deliveryPerformance.onTimeDelivery = diffDays <= 0;
      if (diffDays < 0) {
        this.deliveryPerformance.daysEarly = Math.abs(diffDays);
        this.deliveryPerformance.daysLate = undefined;
      } else if (diffDays > 0) {
        this.deliveryPerformance.daysLate = diffDays;
        this.deliveryPerformance.daysEarly = undefined;
      }

      // Calculate accuracy rate
      const totalOrdered = this.items.reduce((sum, item) => sum + item.quantityOrdered, 0);
      const totalAccepted = this.items.reduce((sum, item) => sum + item.quantityAccepted, 0);
      this.deliveryPerformance.accuracyRate = totalOrdered > 0 ? Math.round((totalAccepted / totalOrdered) * 100) : 0;
    }
  }
  next();
});

// Static methods
DeliverySchema.statics.findOverdue = function(days: number = 0) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  return this.find({
    status: { $in: ['scheduled', 'in_transit'] },
    expectedDate: { $lt: cutoffDate }
  }).populate('supplierId', 'name contactEmail')
    .populate('purchaseOrderId', 'orderNumber');
};

DeliverySchema.statics.findBySupplier = function(supplierId: string) {
  return this.find({ supplierId })
    .populate('supplierId', 'name contactEmail')
    .populate('purchaseOrderId', 'orderNumber');
};

DeliverySchema.statics.findByPurchaseOrder = function(purchaseOrderId: string) {
  return this.find({ purchaseOrderId })
    .populate('supplierId', 'name contactEmail');
};

DeliverySchema.statics.findPendingReceipt = function() {
  return this.find({
    status: 'delivered',
    receivedBy: { $exists: false }
  }).populate('supplierId', 'name contactEmail')
    .populate('purchaseOrderId', 'orderNumber');
};

// Instance methods
DeliverySchema.methods.isOverdue = function() {
  return this.expectedDate < new Date() && !['delivered', 'cancelled', 'returned'].includes(this.status);
};

DeliverySchema.methods.canReceive = function() {
  return ['delivered', 'partially_delivered'].includes(this.status) && !this.receivedBy;
};

DeliverySchema.methods.canInspect = function() {
  return this.receivedBy && !this.qualityInspection;
};

DeliverySchema.methods.hasIssues = function() {
  return this.issues && this.issues.some(issue => ['open', 'in_progress'].includes(issue.status));
};

DeliverySchema.methods.getOpenIssues = function() {
  return this.issues ? this.issues.filter(issue => ['open', 'in_progress'].includes(issue.status)) : [];
};

// Export the model
const Delivery = mongoose.models.Delivery || mongoose.model<IDelivery>('Delivery', DeliverySchema);
export default Delivery;

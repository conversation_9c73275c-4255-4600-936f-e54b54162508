import mongoose, { Document, Schema } from 'mongoose';

// Contract interface
export interface IContract extends Document {
  contractNumber: string;
  supplierId: mongoose.Types.ObjectId;
  title: string;
  description: string;
  contractType: 'service' | 'supply' | 'maintenance' | 'lease' | 'consulting' | 'construction';
  value: number;
  currency: string;
  startDate: Date;
  endDate: Date;
  renewalDate?: Date;
  autoRenewal: boolean;
  renewalTerms?: string;
  status: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed' | 'suspended' | 'pending_approval';
  
  // Contract terms and conditions
  terms: string[];
  paymentTerms: string;
  deliveryTerms?: string;
  penaltyClause?: string;
  warrantyTerms?: string;
  
  // Performance metrics
  performanceMetrics?: Array<{
    metric: string;
    target: string;
    measurement: string;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  }>;
  
  // Financial details
  budgetCategory?: mongoose.Types.ObjectId;
  costCenter?: mongoose.Types.ObjectId;
  taxRate?: number;
  discountRate?: number;
  
  // Approval workflow
  approvalWorkflow: Array<{
    level: number;
    approverRole: string;
    approverId?: mongoose.Types.ObjectId;
    approvalDate?: Date;
    status: 'pending' | 'approved' | 'rejected';
    comments?: string;
  }>;
  
  // Documents and attachments
  attachments: Array<{
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    uploadedBy: mongoose.Types.ObjectId;
    uploadedAt: Date;
    description?: string;
  }>;
  
  // Compliance and legal
  complianceRequirements?: string[];
  legalReview: {
    required: boolean;
    reviewedBy?: mongoose.Types.ObjectId;
    reviewDate?: Date;
    status?: 'pending' | 'approved' | 'rejected';
    comments?: string;
  };
  
  // Notifications and reminders
  notifications: {
    renewalReminder: boolean;
    expiryReminder: boolean;
    performanceReview: boolean;
    reminderDays: number[];
  };
  
  // Audit trail
  createdBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  reviewDate?: Date;
  lastModifiedBy?: mongoose.Types.ObjectId;
  
  // Additional metadata
  tags?: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  riskLevel: 'low' | 'medium' | 'high';
  notes?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

// Performance metric subdocument schema
const PerformanceMetricSchema = new Schema({
  metric: {
    type: String,
    required: true,
    trim: true
  },
  target: {
    type: String,
    required: true,
    trim: true
  },
  measurement: {
    type: String,
    required: true,
    trim: true
  },
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annually'],
    default: 'monthly'
  }
}, { _id: false });

// Approval workflow subdocument schema
const ApprovalWorkflowSchema = new Schema({
  level: {
    type: Number,
    required: true,
    min: 1
  },
  approverRole: {
    type: String,
    required: true,
    trim: true
  },
  approverId: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: Date,
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  comments: {
    type: String,
    trim: true
  }
}, { _id: false });

// Attachment subdocument schema
const AttachmentSchema = new Schema({
  fileName: {
    type: String,
    required: true,
    trim: true
  },
  filePath: {
    type: String,
    required: true,
    trim: true
  },
  fileType: {
    type: String,
    required: true,
    trim: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  description: {
    type: String,
    trim: true
  }
}, { _id: false });

// Legal review subdocument schema
const LegalReviewSchema = new Schema({
  required: {
    type: Boolean,
    default: false
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: Date,
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected']
  },
  comments: {
    type: String,
    trim: true
  }
}, { _id: false });

// Notifications subdocument schema
const NotificationsSchema = new Schema({
  renewalReminder: {
    type: Boolean,
    default: true
  },
  expiryReminder: {
    type: Boolean,
    default: true
  },
  performanceReview: {
    type: Boolean,
    default: false
  },
  reminderDays: {
    type: [Number],
    default: [30, 60, 90] // Days before expiry/renewal
  }
}, { _id: false });

// Main Contract schema
const ContractSchema = new Schema<IContract>({
  contractNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  supplierId: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  contractType: {
    type: String,
    enum: ['service', 'supply', 'maintenance', 'lease', 'consulting', 'construction'],
    required: true,
    index: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    uppercase: true,
    maxlength: 3
  },
  startDate: {
    type: Date,
    required: true,
    index: true
  },
  endDate: {
    type: Date,
    required: true,
    index: true
  },
  renewalDate: Date,
  autoRenewal: {
    type: Boolean,
    default: false
  },
  renewalTerms: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'expired', 'terminated', 'renewed', 'suspended', 'pending_approval'],
    default: 'draft',
    index: true
  },
  terms: [{
    type: String,
    trim: true
  }],
  paymentTerms: {
    type: String,
    required: true,
    trim: true
  },
  deliveryTerms: {
    type: String,
    trim: true
  },
  penaltyClause: {
    type: String,
    trim: true
  },
  warrantyTerms: {
    type: String,
    trim: true
  },
  performanceMetrics: [PerformanceMetricSchema],
  budgetCategory: {
    type: Schema.Types.ObjectId,
    ref: 'BudgetCategory'
  },
  costCenter: {
    type: Schema.Types.ObjectId,
    ref: 'CostCenter'
  },
  taxRate: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  discountRate: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  approvalWorkflow: [ApprovalWorkflowSchema],
  attachments: [AttachmentSchema],
  complianceRequirements: [{
    type: String,
    trim: true
  }],
  legalReview: {
    type: LegalReviewSchema,
    default: () => ({ required: false })
  },
  notifications: {
    type: NotificationsSchema,
    default: () => ({})
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: Date,
  reviewDate: Date,
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
ContractSchema.index({ contractNumber: 1 });
ContractSchema.index({ supplierId: 1 });
ContractSchema.index({ status: 1 });
ContractSchema.index({ contractType: 1 });
ContractSchema.index({ startDate: 1, endDate: 1 });
ContractSchema.index({ createdBy: 1 });
ContractSchema.index({ value: 1 });
ContractSchema.index({ 'approvalWorkflow.status': 1 });

// Compound indexes
ContractSchema.index({ supplierId: 1, status: 1 });
ContractSchema.index({ contractType: 1, status: 1 });
ContractSchema.index({ endDate: 1, status: 1 }); // For expiry tracking

// Virtual for contract duration in days
ContractSchema.virtual('durationDays').get(function() {
  if (this.startDate && this.endDate) {
    const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for days until expiry
ContractSchema.virtual('daysUntilExpiry').get(function() {
  if (this.endDate) {
    const today = new Date();
    const diffTime = this.endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for contract status display
ContractSchema.virtual('statusDisplay').get(function() {
  const today = new Date();
  
  if (this.status === 'active' && this.endDate < today) {
    return 'expired';
  }
  
  if (this.status === 'active' && this.daysUntilExpiry <= 30) {
    return 'expiring_soon';
  }
  
  return this.status;
});

// Pre-save middleware to generate contract number
ContractSchema.pre('save', async function(next) {
  if (this.isNew && !this.contractNumber) {
    const year = new Date().getFullYear();
    const count = await mongoose.model('Contract').countDocuments({
      contractNumber: new RegExp(`^CT-${year}-`)
    });
    this.contractNumber = `CT-${year}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Pre-save middleware to update lastModifiedBy
ContractSchema.pre('save', function(next) {
  if (!this.isNew && this.isModified()) {
    this.lastModifiedBy = this.createdBy; // This should be set by the service layer
  }
  next();
});

// Static methods
ContractSchema.statics.findExpiring = function(days: number = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    status: 'active',
    endDate: { $lte: futureDate, $gte: new Date() }
  }).populate('supplierId', 'name contactEmail');
};

ContractSchema.statics.findBySupplier = function(supplierId: string) {
  return this.find({ supplierId }).populate('supplierId', 'name contactEmail');
};

ContractSchema.statics.findByType = function(contractType: string) {
  return this.find({ contractType });
};

// Instance methods
ContractSchema.methods.isExpired = function() {
  return this.endDate < new Date();
};

ContractSchema.methods.isExpiringSoon = function(days: number = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  return this.endDate <= futureDate && this.endDate >= new Date();
};

ContractSchema.methods.canRenew = function() {
  return this.status === 'active' && (this.isExpired() || this.isExpiringSoon(90));
};

// Export the model
const Contract = mongoose.models.Contract || mongoose.model<IContract>('Contract', ContractSchema);
export default Contract;

import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * Interface for Procurement Category document
 */
export interface IProcurementCategory extends Document {
  // Basic Information
  name: string;
  code: string;
  description?: string;
  
  // Hierarchy
  parentCategory?: mongoose.Types.ObjectId;
  level: number; // 0 = root, 1 = first level, etc.
  path: string; // Full path like "IT/Hardware/Computers"
  
  // Budget Integration
  budgetCategory?: mongoose.Types.ObjectId; // Link to accounting budget category
  costCenter?: mongoose.Types.ObjectId; // Link to cost center
  
  // Approval Configuration
  approvalLimit?: number; // Maximum amount that can be approved at this category level
  requiredApprovers: mongoose.Types.ObjectId[]; // Users who can approve for this category
  autoApprovalThreshold?: number; // Amount below which auto-approval is allowed
  
  // Supplier Management
  defaultSuppliers?: mongoose.Types.ObjectId[]; // Preferred suppliers for this category
  restrictedSuppliers?: mongoose.Types.ObjectId[]; // Suppliers not allowed for this category
  requiresQuotation: boolean; // Whether quotations are mandatory
  minimumQuotations?: number; // Minimum number of quotations required
  
  // Operational Settings
  leadTime?: number; // Expected lead time in days
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  isRestricted: boolean; // Requires special permissions to use
  
  // Compliance and Requirements
  complianceRequirements?: string[]; // List of compliance requirements
  requiredDocuments?: string[]; // Documents required for procurement in this category
  qualityStandards?: string[]; // Quality standards that must be met
  
  // Workflow Configuration
  requiresInspection: boolean; // Whether goods receipt requires inspection
  inspectionCriteria?: string[]; // Criteria for inspection
  requiresApprovalWorkflow: boolean; // Whether approval workflow is mandatory
  
  // Financial Controls
  budgetCheckRequired: boolean; // Whether budget availability check is required
  allowOverBudget: boolean; // Whether over-budget spending is allowed
  overBudgetApprovalRequired: boolean; // Whether over-budget requires special approval
  
  // Metadata
  tags?: string[]; // Tags for categorization and search
  notes?: string; // Additional notes
  
  // Audit Trail
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  
  // Instance Methods
  getFullPath(): string;
  getChildren(): Promise<IProcurementCategory[]>;
  getAncestors(): Promise<IProcurementCategory[]>;
  canApprove(amount: number, userId: string): boolean;
  isDescendantOf(categoryId: string): Promise<boolean>;
  validateBudgetAvailability(amount: number): Promise<boolean>;
}

/**
 * Procurement Category Schema
 */
const procurementCategorySchema = new Schema<IProcurementCategory>(
  {
    // Basic Information
    name: {
      type: String,
      required: [true, 'Category name is required'],
      trim: true,
      maxlength: [100, 'Category name cannot exceed 100 characters'],
      index: true
    },
    code: {
      type: String,
      required: [true, 'Category code is required'],
      unique: true,
      trim: true,
      uppercase: true,
      maxlength: [20, 'Category code cannot exceed 20 characters'],
      match: [/^[A-Z0-9_-]+$/, 'Category code can only contain uppercase letters, numbers, underscores, and hyphens'],
      index: true
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters']
    },
    
    // Hierarchy
    parentCategory: {
      type: Schema.Types.ObjectId,
      ref: 'ProcurementCategory',
      index: true
    },
    level: {
      type: Number,
      required: true,
      default: 0, // Default value, will be updated by middleware
      min: [0, 'Level cannot be negative'],
      max: [10, 'Maximum category depth is 10 levels'],
      index: true
    },
    path: {
      type: String,
      required: true,
      default: '', // Default value, will be updated by middleware
      trim: true,
      index: true
    },
    
    // Budget Integration
    budgetCategory: {
      type: Schema.Types.ObjectId,
      ref: 'BudgetCategory',
      index: true
    },
    costCenter: {
      type: Schema.Types.ObjectId,
      ref: 'CostCenter',
      index: true
    },
    
    // Approval Configuration
    approvalLimit: {
      type: Number,
      min: [0, 'Approval limit cannot be negative'],
      index: true
    },
    requiredApprovers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    autoApprovalThreshold: {
      type: Number,
      min: [0, 'Auto-approval threshold cannot be negative']
    },
    
    // Supplier Management
    defaultSuppliers: [{
      type: Schema.Types.ObjectId,
      ref: 'Supplier'
    }],
    restrictedSuppliers: [{
      type: Schema.Types.ObjectId,
      ref: 'Supplier'
    }],
    requiresQuotation: {
      type: Boolean,
      default: false,
      index: true
    },
    minimumQuotations: {
      type: Number,
      min: [1, 'Minimum quotations must be at least 1'],
      max: [10, 'Maximum quotations cannot exceed 10']
    },
    
    // Operational Settings
    leadTime: {
      type: Number,
      min: [0, 'Lead time cannot be negative'],
      max: [365, 'Lead time cannot exceed 365 days']
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
      index: true
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    isRestricted: {
      type: Boolean,
      default: false,
      index: true
    },
    
    // Compliance and Requirements
    complianceRequirements: [{
      type: String,
      trim: true
    }],
    requiredDocuments: [{
      type: String,
      trim: true
    }],
    qualityStandards: [{
      type: String,
      trim: true
    }],
    
    // Workflow Configuration
    requiresInspection: {
      type: Boolean,
      default: false,
      index: true
    },
    inspectionCriteria: [{
      type: String,
      trim: true
    }],
    requiresApprovalWorkflow: {
      type: Boolean,
      default: true,
      index: true
    },
    
    // Financial Controls
    budgetCheckRequired: {
      type: Boolean,
      default: true,
      index: true
    },
    allowOverBudget: {
      type: Boolean,
      default: false,
      index: true
    },
    overBudgetApprovalRequired: {
      type: Boolean,
      default: true,
      index: true
    },
    
    // Metadata
    tags: [{
      type: String,
      trim: true,
      lowercase: true
    }],
    notes: {
      type: String,
      trim: true,
      maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    
    // Audit Trail
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Created by is required'],
      index: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
procurementCategorySchema.index({ name: 1, isActive: 1 });
procurementCategorySchema.index({ parentCategory: 1, isActive: 1 });
procurementCategorySchema.index({ budgetCategory: 1, isActive: 1 });
procurementCategorySchema.index({ riskLevel: 1, isActive: 1 });
procurementCategorySchema.index({ level: 1, path: 1 });
procurementCategorySchema.index({ tags: 1 });

// Compound index for hierarchy queries
procurementCategorySchema.index({ 
  parentCategory: 1, 
  level: 1, 
  isActive: 1 
});

// Text index for search
procurementCategorySchema.index({
  name: 'text',
  description: 'text',
  tags: 'text',
  notes: 'text'
});

// Virtual for subcategories
procurementCategorySchema.virtual('subcategories', {
  ref: 'ProcurementCategory',
  localField: '_id',
  foreignField: 'parentCategory'
});

// Virtual for parent category details
procurementCategorySchema.virtual('parent', {
  ref: 'ProcurementCategory',
  localField: 'parentCategory',
  foreignField: '_id',
  justOne: true
});

// Pre-validate middleware to update path and level
procurementCategorySchema.pre('validate', async function(next) {
  try {
    console.log('Pre-validate middleware called for category:', this.name);
    if (this.isModified('parentCategory') || this.isNew) {
      await this.updatePathAndLevel();
    }

    // Ensure path and level are always set
    if (!this.path) {
      throw new Error('Path must be set by middleware');
    }
    if (this.level === undefined || this.level === null) {
      throw new Error('Level must be set by middleware');
    }

    console.log('Pre-validate middleware completed - path:', this.path, 'level:', this.level);
    next();
  } catch (error) {
    console.error('Error in pre-validate middleware:', error);
    next(error);
  }
});

// Instance Methods
procurementCategorySchema.methods.getFullPath = function(): string {
  return this.path;
};

procurementCategorySchema.methods.getChildren = async function(): Promise<IProcurementCategory[]> {
  return await this.constructor.find({
    parentCategory: this._id,
    isActive: true
  }).sort({ name: 1 });
};

procurementCategorySchema.methods.getAncestors = async function(): Promise<IProcurementCategory[]> {
  const ancestors: IProcurementCategory[] = [];
  let current = this;

  while (current.parentCategory) {
    const parent = await this.constructor.findById(current.parentCategory);
    if (!parent) break;
    ancestors.unshift(parent);
    current = parent;
  }

  return ancestors;
};

procurementCategorySchema.methods.canApprove = function(amount: number, userId: string): boolean {
  // Check if user is in required approvers
  const isApprover = this.requiredApprovers.some(
    (approverId: mongoose.Types.ObjectId) => approverId.toString() === userId
  );
  
  if (!isApprover) return false;
  
  // Check approval limit
  if (this.approvalLimit && amount > this.approvalLimit) return false;
  
  return true;
};

procurementCategorySchema.methods.isDescendantOf = async function(categoryId: string): Promise<boolean> {
  let current = this;

  while (current.parentCategory) {
    if (current.parentCategory.toString() === categoryId) return true;
    current = await this.constructor.findById(current.parentCategory);
    if (!current) break;
  }

  return false;
};

procurementCategorySchema.methods.validateBudgetAvailability = async function(amount: number): Promise<boolean> {
  if (!this.budgetCheckRequired) return true;
  
  // This would integrate with the budget service
  // For now, return true - implement budget checking logic
  return true;
};

procurementCategorySchema.methods.updatePathAndLevel = async function(): Promise<void> {
  console.log('updatePathAndLevel called for category:', this.name, 'parentCategory:', this.parentCategory);

  if (!this.parentCategory) {
    this.level = 0;
    this.path = this.name;
    console.log('Root category - level:', this.level, 'path:', this.path);
    return;
  }

  const parent = await this.constructor.findById(this.parentCategory);
  if (!parent) {
    throw new Error('Parent category not found');
  }

  this.level = parent.level + 1;
  this.path = `${parent.path}/${this.name}`;
  console.log('Child category - level:', this.level, 'path:', this.path);
};

// Static Methods
procurementCategorySchema.statics.findByPath = function(path: string) {
  return this.findOne({ path, isActive: true });
};

procurementCategorySchema.statics.getRootCategories = function() {
  return this.find({ 
    parentCategory: { $exists: false }, 
    isActive: true 
  }).sort({ name: 1 });
};

procurementCategorySchema.statics.getCategoryHierarchy = async function() {
  const categories = await this.find({ isActive: true }).sort({ path: 1 });
  return buildHierarchy(categories);
};

// Helper function to build hierarchy
function buildHierarchy(categories: IProcurementCategory[]) {
  const categoryMap = new Map();
  const rootCategories: any[] = [];
  
  // Create map of categories
  categories.forEach(category => {
    categoryMap.set(category._id.toString(), {
      ...category.toObject(),
      children: []
    });
  });
  
  // Build hierarchy
  categories.forEach(category => {
    const categoryObj = categoryMap.get(category._id.toString());
    
    if (category.parentCategory) {
      const parent = categoryMap.get(category.parentCategory.toString());
      if (parent) {
        parent.children.push(categoryObj);
      }
    } else {
      rootCategories.push(categoryObj);
    }
  });
  
  return rootCategories;
}

// Create and export the model
const ProcurementCategory: Model<IProcurementCategory> = 
  mongoose.models.ProcurementCategory || 
  mongoose.model<IProcurementCategory>('ProcurementCategory', procurementCategorySchema);

export default ProcurementCategory;

import mongoose, { Document, Schema, Model } from 'mongoose';

export interface IProcurementInventory extends Document {
  // Basic Information
  inventoryId: string;
  name: string;
  description?: string;
  category: mongoose.Types.ObjectId;
  sku?: string;
  barcode?: string;
  
  // Stock Information
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  unit: string;
  
  // Financial Information
  unitPrice: number;
  totalValue: number;
  currency: string;
  lastPurchasePrice?: number;
  averageCost?: number;
  
  // Location and Storage
  location: string;
  warehouse?: string;
  shelf?: string;
  zone?: string;
  
  // Procurement Context
  preferredSuppliers: mongoose.Types.ObjectId[];
  lastPurchaseOrder?: mongoose.Types.ObjectId;
  lastPurchaseDate?: Date;
  nextReorderDate?: Date;
  
  // Status and Tracking
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'on_order' | 'discontinued' | 'obsolete';
  reorderPoint: number;
  reorderQuantity: number;
  leadTime?: number;
  
  // Quality and Compliance
  qualityGrade?: 'A' | 'B' | 'C';
  expiryDate?: Date;
  batchNumber?: string;
  serialNumbers?: string[];
  
  // Integration
  generalInventoryId?: mongoose.Types.ObjectId;
  assetId?: mongoose.Types.ObjectId;
  
  // Metadata
  tags: string[];
  notes?: string;
  attachments: string[];
  
  // Audit
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  
  // Instance Methods
  calculateReorderPoint(): number;
  isLowStock(): boolean;
  needsReorder(): boolean;
  updateTotalValue(): void;
  getStockStatus(): string;
}

const procurementInventorySchema = new Schema<IProcurementInventory>(
  {
    // Basic Information
    inventoryId: {
      type: String,
      required: [true, 'Inventory ID is required'],
      unique: true,
      trim: true,
      uppercase: true,
      index: true
    },
    name: {
      type: String,
      required: [true, 'Item name is required'],
      trim: true,
      maxlength: [200, 'Name cannot exceed 200 characters'],
      index: true
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: 'ProcurementCategory',
      required: [true, 'Category is required'],
      index: true
    },
    sku: {
      type: String,
      trim: true,
      uppercase: true,
      index: true
    },
    barcode: {
      type: String,
      trim: true,
      index: true
    },
    
    // Stock Information
    currentStock: {
      type: Number,
      required: [true, 'Current stock is required'],
      min: [0, 'Stock cannot be negative'],
      default: 0
    },
    minimumStock: {
      type: Number,
      required: [true, 'Minimum stock is required'],
      min: [0, 'Minimum stock cannot be negative'],
      default: 0
    },
    maximumStock: {
      type: Number,
      min: [0, 'Maximum stock cannot be negative']
    },
    unit: {
      type: String,
      required: [true, 'Unit is required'],
      trim: true,
      lowercase: true
    },
    
    // Financial Information
    unitPrice: {
      type: Number,
      required: [true, 'Unit price is required'],
      min: [0, 'Unit price cannot be negative']
    },
    totalValue: {
      type: Number,
      min: [0, 'Total value cannot be negative'],
      default: 0
    },
    currency: {
      type: String,
      required: true,
      default: 'MWK',
      uppercase: true,
      maxlength: 3
    },
    lastPurchasePrice: {
      type: Number,
      min: [0, 'Last purchase price cannot be negative']
    },
    averageCost: {
      type: Number,
      min: [0, 'Average cost cannot be negative']
    },
    
    // Location and Storage
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
      index: true
    },
    warehouse: {
      type: String,
      trim: true,
      index: true
    },
    shelf: {
      type: String,
      trim: true
    },
    zone: {
      type: String,
      trim: true
    },
    
    // Procurement Context
    preferredSuppliers: [{
      type: Schema.Types.ObjectId,
      ref: 'Supplier'
    }],
    lastPurchaseOrder: {
      type: Schema.Types.ObjectId,
      ref: 'PurchaseOrder'
    },
    lastPurchaseDate: {
      type: Date,
      index: true
    },
    nextReorderDate: {
      type: Date,
      index: true
    },
    
    // Status and Tracking
    status: {
      type: String,
      enum: ['in_stock', 'low_stock', 'out_of_stock', 'on_order', 'discontinued', 'obsolete'],
      default: 'in_stock',
      required: true,
      index: true
    },
    reorderPoint: {
      type: Number,
      min: [0, 'Reorder point cannot be negative'],
      default: 0
    },
    reorderQuantity: {
      type: Number,
      min: [0, 'Reorder quantity cannot be negative'],
      default: 0
    },
    leadTime: {
      type: Number,
      min: [0, 'Lead time cannot be negative']
    },
    
    // Quality and Compliance
    qualityGrade: {
      type: String,
      enum: ['A', 'B', 'C']
    },
    expiryDate: {
      type: Date,
      index: true
    },
    batchNumber: {
      type: String,
      trim: true
    },
    serialNumbers: [{
      type: String,
      trim: true
    }],
    
    // Integration
    generalInventoryId: {
      type: Schema.Types.ObjectId,
      ref: 'InventoryItem'
    },
    assetId: {
      type: Schema.Types.ObjectId,
      ref: 'Asset'
    },
    
    // Metadata
    tags: [{
      type: String,
      trim: true,
      lowercase: true
    }],
    notes: {
      type: String,
      trim: true
    },
    attachments: [{
      type: String,
      trim: true
    }],
    
    // Audit
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
procurementInventorySchema.index({ name: 'text', description: 'text' });
procurementInventorySchema.index({ category: 1, status: 1 });
procurementInventorySchema.index({ location: 1, warehouse: 1 });
procurementInventorySchema.index({ currentStock: 1, minimumStock: 1 });
procurementInventorySchema.index({ createdAt: -1 });

// Pre-save middleware
procurementInventorySchema.pre('save', function(next) {
  const item = this as IProcurementInventory;

  // Update total value
  item.updateTotalValue();

  // Update status based on stock levels
  if (item.currentStock <= 0) {
    item.status = 'out_of_stock';
  } else if (item.isLowStock()) {
    item.status = 'low_stock';
  } else if (item.status === 'low_stock' || item.status === 'out_of_stock') {
    item.status = 'in_stock';
  }

  next();
});

// Instance Methods
procurementInventorySchema.methods.calculateReorderPoint = function(): number {
  const item = this as IProcurementInventory;
  const leadTimeDays = item.leadTime || 7;
  const safetyStock = Math.ceil(item.minimumStock * 0.2);
  return Math.max(item.minimumStock, leadTimeDays * (item.reorderQuantity / 30) + safetyStock);
};

procurementInventorySchema.methods.isLowStock = function(): boolean {
  const item = this as IProcurementInventory;
  return item.currentStock <= item.minimumStock;
};

procurementInventorySchema.methods.needsReorder = function(): boolean {
  const item = this as IProcurementInventory;
  return item.currentStock <= item.reorderPoint;
};

procurementInventorySchema.methods.updateTotalValue = function(): void {
  const item = this as IProcurementInventory;
  item.totalValue = item.currentStock * item.unitPrice;
};

procurementInventorySchema.methods.getStockStatus = function(): string {
  const item = this as IProcurementInventory;
  if (item.currentStock <= 0) return 'out_of_stock';
  if (item.isLowStock()) return 'low_stock';
  if (item.needsReorder()) return 'needs_reorder';
  return 'in_stock';
};

// Static Methods
procurementInventorySchema.statics.findLowStock = function() {
  return this.find({
    $expr: { $lte: ['$currentStock', '$minimumStock'] },
    status: { $ne: 'discontinued' }
  });
};

procurementInventorySchema.statics.findByCategory = function(categoryId: string) {
  return this.find({ category: categoryId, status: { $ne: 'discontinued' } });
};

procurementInventorySchema.statics.findByLocation = function(location: string) {
  return this.find({ location, status: { $ne: 'discontinued' } });
};

// Create and export the model
const ProcurementInventory = mongoose.models.ProcurementInventory ||
  mongoose.model<IProcurementInventory>('ProcurementInventory', procurementInventorySchema);

export default ProcurementInventory;

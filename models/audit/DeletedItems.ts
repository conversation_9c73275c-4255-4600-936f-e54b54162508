// models/audit/DeletedItems.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface IDeletedItem extends Document {
  // Original item data
  originalId: string;
  originalModel: 'Income' | 'Expenditure' | 'Budget' | 'Employee' | 'Payroll' | 'ProcurementCategory' | 'ProcurementInventory' | 'Other';
  originalData: any; // Complete original document
  
  // Deletion metadata
  deletedAt: Date;
  deletedBy: mongoose.Types.ObjectId;
  deletionReason: string; // Required field
  deletionType: 'single' | 'bulk';
  
  // Audit trail
  deletedByUser: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  
  // Context information
  fiscalYear?: string;
  department?: string;
  budgetId?: string;
  relatedTransactions?: string[]; // IDs of related items that were also deleted
  
  // System metadata
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  
  // Recovery information
  canBeRecovered: boolean;
  recoveryDeadline?: Date; // After this date, recovery may not be possible
  
  // Additional audit fields
  auditNotes?: string;
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
  reviewStatus?: 'pending' | 'approved' | 'flagged' | 'investigated';
  
  // Compliance fields
  complianceFlags?: string[];
  retentionPeriod?: number; // Days to retain this record
  
  createdAt: Date;
  updatedAt: Date;
}

const DeletedItemSchema = new Schema<IDeletedItem>({
  // Original item data
  originalId: {
    type: String,
    required: true,
    index: true
  },
  originalModel: {
    type: String,
    required: true,
    enum: ['Income', 'Expenditure', 'Budget', 'Employee', 'Payroll', 'ProcurementCategory', 'ProcurementInventory', 'Other'],
    index: true
  },
  originalData: {
    type: Schema.Types.Mixed,
    required: true
  },
  
  // Deletion metadata
  deletedAt: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  deletedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  deletionReason: {
    type: String,
    required: true,
    minlength: 10,
    maxlength: 1000,
    trim: true
  },
  deletionType: {
    type: String,
    required: true,
    enum: ['single', 'bulk'],
    default: 'single'
  },
  
  // Audit trail
  deletedByUser: {
    id: { type: String, required: true },
    name: { type: String, required: true },
    email: { type: String, required: true },
    role: { type: String, required: true }
  },
  
  // Context information
  fiscalYear: {
    type: String,
    index: true
  },
  department: {
    type: String,
    index: true
  },
  budgetId: {
    type: String,
    index: true
  },
  relatedTransactions: [{
    type: String
  }],
  
  // System metadata
  ipAddress: String,
  userAgent: String,
  sessionId: String,
  
  // Recovery information
  canBeRecovered: {
    type: Boolean,
    default: true
  },
  recoveryDeadline: {
    type: Date,
    default: function() {
      // Default to 90 days from deletion
      return new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
    }
  },
  
  // Additional audit fields
  auditNotes: String,
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  reviewStatus: {
    type: String,
    enum: ['pending', 'approved', 'flagged', 'investigated'],
    default: 'pending'
  },
  
  // Compliance fields
  complianceFlags: [String],
  retentionPeriod: {
    type: Number,
    default: 2555, // 7 years in days (government standard)
    min: 365,
    max: 3650
  }
}, {
  timestamps: true,
  collection: 'deleted_items'
});

// Indexes for efficient querying
DeletedItemSchema.index({ deletedAt: -1 });
DeletedItemSchema.index({ originalModel: 1, deletedAt: -1 });
DeletedItemSchema.index({ deletedBy: 1, deletedAt: -1 });
DeletedItemSchema.index({ fiscalYear: 1, originalModel: 1 });
DeletedItemSchema.index({ reviewStatus: 1, deletedAt: -1 });
DeletedItemSchema.index({ 'deletedByUser.role': 1, deletedAt: -1 });

// Compound indexes for common queries
DeletedItemSchema.index({ 
  originalModel: 1, 
  fiscalYear: 1, 
  deletedAt: -1 
});

DeletedItemSchema.index({ 
  deletedBy: 1, 
  originalModel: 1, 
  deletedAt: -1 
});

// Virtual for formatted deletion date
DeletedItemSchema.virtual('formattedDeletionDate').get(function() {
  return this.deletedAt.toLocaleDateString('en-MW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Virtual for days until recovery deadline
DeletedItemSchema.virtual('daysUntilRecoveryDeadline').get(function() {
  if (!this.recoveryDeadline) return null;
  const now = new Date();
  const deadline = new Date(this.recoveryDeadline);
  const diffTime = deadline.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
});

// Virtual for recovery status
DeletedItemSchema.virtual('recoveryStatus').get(function() {
  if (!this.canBeRecovered) return 'not_recoverable';
  if (!this.recoveryDeadline) return 'recoverable';
  
  const daysLeft = this.daysUntilRecoveryDeadline;
  if (daysLeft === null || daysLeft <= 0) return 'recovery_expired';
  if (daysLeft <= 7) return 'recovery_expiring_soon';
  return 'recoverable';
});

// Static method to create deleted item record
DeletedItemSchema.statics.createDeletedRecord = async function(
  originalDoc: any,
  deletionInfo: {
    deletedBy: string;
    deletionReason: string;
    deletionType: 'single' | 'bulk';
    userInfo: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
    context?: {
      fiscalYear?: string;
      department?: string;
      budgetId?: string;
      relatedTransactions?: string[];
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    };
  }
) {
  const deletedItem = new this({
    originalId: originalDoc._id.toString(),
    originalModel: originalDoc.constructor.modelName,
    originalData: originalDoc.toObject(),
    deletedBy: deletionInfo.deletedBy,
    deletionReason: deletionInfo.deletionReason,
    deletionType: deletionInfo.deletionType,
    deletedByUser: deletionInfo.userInfo,
    ...deletionInfo.context
  });
  
  return await deletedItem.save();
};

// Static method for audit queries
DeletedItemSchema.statics.getAuditReport = async function(filters: {
  startDate?: Date;
  endDate?: Date;
  model?: string;
  deletedBy?: string;
  fiscalYear?: string;
  reviewStatus?: string;
}) {
  const query: any = {};
  
  if (filters.startDate || filters.endDate) {
    query.deletedAt = {};
    if (filters.startDate) query.deletedAt.$gte = filters.startDate;
    if (filters.endDate) query.deletedAt.$lte = filters.endDate;
  }
  
  if (filters.model) query.originalModel = filters.model;
  if (filters.deletedBy) query.deletedBy = filters.deletedBy;
  if (filters.fiscalYear) query.fiscalYear = filters.fiscalYear;
  if (filters.reviewStatus) query.reviewStatus = filters.reviewStatus;
  
  return await this.find(query)
    .populate('deletedBy', 'firstName lastName email role')
    .populate('reviewedBy', 'firstName lastName email role')
    .sort({ deletedAt: -1 });
};

// Pre-save middleware
DeletedItemSchema.pre('save', function(next) {
  // Ensure deletion reason is provided and meaningful
  if (!this.deletionReason || this.deletionReason.trim().length < 10) {
    return next(new Error('Deletion reason must be at least 10 characters long'));
  }
  
  // Set compliance flags based on original model
  if (!this.complianceFlags) {
    this.complianceFlags = [];
    
    if (this.originalModel === 'Income' || this.originalModel === 'Expenditure') {
      this.complianceFlags.push('FINANCIAL_AUDIT_REQUIRED');
    }
    
    if (this.originalData?.amount && this.originalData.amount > 1000000) {
      this.complianceFlags.push('HIGH_VALUE_TRANSACTION');
    }
  }
  
  next();
});

const DeletedItem = mongoose.models.DeletedItem || mongoose.model<IDeletedItem>('DeletedItem', DeletedItemSchema);

export default DeletedItem;

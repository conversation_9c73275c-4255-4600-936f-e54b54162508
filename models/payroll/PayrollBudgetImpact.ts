import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for payroll budget impact document
 */
export interface IPayrollBudgetImpact extends Document {
  payrollRunId: mongoose.Types.ObjectId;
  budgetId: mongoose.Types.ObjectId;
  departmentId?: mongoose.Types.ObjectId;
  
  // Budget Impact Details
  budgetedAmount: number;
  actualAmount: number;
  variance: number;
  variancePercentage: number;
  
  // Period Information
  fiscalYear: string;
  period: string; // e.g., "2024-01", "Q1-2024"
  payPeriod: {
    month: number;
    year: number;
    startDate: Date;
    endDate: Date;
  };
  
  // Budget Category Breakdown
  categoryImpacts: {
    categoryId: mongoose.Types.ObjectId;
    categoryName: string;
    categoryType: 'income' | 'expense';
    budgetedAmount: number;
    actualAmount: number;
    variance: number;
    variancePercentage: number;
  }[];
  
  // Variance Analysis
  varianceStatus: 'within_budget' | 'over_budget' | 'under_budget';
  varianceThreshold: number; // Percentage threshold for alerts
  requiresApproval: boolean;
  
  // Alert and Notification Status
  alertsSent: boolean;
  alertLevel: 'info' | 'warning' | 'critical';
  notificationsSent: string[]; // Array of notification IDs
  
  // Approval Workflow
  approvalStatus: 'not_required' | 'pending' | 'approved' | 'rejected';
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  approvalNotes?: string;
  
  // Metadata
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for payroll budget impact
 */
const PayrollBudgetImpactSchema: Schema = new Schema(
  {
    payrollRunId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PayrollRun',
      required: true,
    },
    budgetId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
      required: true,
    },
    departmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Department',
    },
    
    // Budget Impact Details
    budgetedAmount: {
      type: Number,
      required: true,
      default: 0,
    },
    actualAmount: {
      type: Number,
      required: true,
      default: 0,
    },
    variance: {
      type: Number,
      required: true,
      default: 0,
    },
    variancePercentage: {
      type: Number,
      required: true,
      default: 0,
    },
    
    // Period Information
    fiscalYear: {
      type: String,
      required: true,
      trim: true,
    },
    period: {
      type: String,
      required: true,
      trim: true,
    },
    payPeriod: {
      month: {
        type: Number,
        required: true,
        min: 1,
        max: 12,
      },
      year: {
        type: Number,
        required: true,
      },
      startDate: {
        type: Date,
        required: true,
      },
      endDate: {
        type: Date,
        required: true,
      },
    },
    
    // Budget Category Breakdown
    categoryImpacts: [{
      categoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'BudgetCategory',
        required: true,
      },
      categoryName: {
        type: String,
        required: true,
        trim: true,
      },
      categoryType: {
        type: String,
        required: true,
        enum: ['income', 'expense'],
      },
      budgetedAmount: {
        type: Number,
        required: true,
        default: 0,
      },
      actualAmount: {
        type: Number,
        required: true,
        default: 0,
      },
      variance: {
        type: Number,
        required: true,
        default: 0,
      },
      variancePercentage: {
        type: Number,
        required: true,
        default: 0,
      },
    }],
    
    // Variance Analysis
    varianceStatus: {
      type: String,
      required: true,
      enum: ['within_budget', 'over_budget', 'under_budget'],
      default: 'within_budget',
    },
    varianceThreshold: {
      type: Number,
      required: true,
      default: 10, // 10% threshold by default
    },
    requiresApproval: {
      type: Boolean,
      required: true,
      default: false,
    },
    
    // Alert and Notification Status
    alertsSent: {
      type: Boolean,
      required: true,
      default: false,
    },
    alertLevel: {
      type: String,
      required: true,
      enum: ['info', 'warning', 'critical'],
      default: 'info',
    },
    notificationsSent: [{
      type: String,
      trim: true,
    }],
    
    // Approval Workflow
    approvalStatus: {
      type: String,
      required: true,
      enum: ['not_required', 'pending', 'approved', 'rejected'],
      default: 'not_required',
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    approvedAt: {
      type: Date,
    },
    approvalNotes: {
      type: String,
      trim: true,
    },
    
    // Metadata
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
PayrollBudgetImpactSchema.index({ payrollRunId: 1 });
PayrollBudgetImpactSchema.index({ budgetId: 1 });
PayrollBudgetImpactSchema.index({ departmentId: 1 });
PayrollBudgetImpactSchema.index({ fiscalYear: 1, period: 1 });
PayrollBudgetImpactSchema.index({ varianceStatus: 1 });
PayrollBudgetImpactSchema.index({ approvalStatus: 1 });
PayrollBudgetImpactSchema.index({ 'payPeriod.year': 1, 'payPeriod.month': 1 });
PayrollBudgetImpactSchema.index({ createdAt: -1 });

// Add compound indexes for common queries
PayrollBudgetImpactSchema.index({ budgetId: 1, fiscalYear: 1 });
PayrollBudgetImpactSchema.index({ departmentId: 1, fiscalYear: 1 });
PayrollBudgetImpactSchema.index({ varianceStatus: 1, alertsSent: 1 });

// Virtual for absolute variance percentage
PayrollBudgetImpactSchema.virtual('absoluteVariancePercentage').get(function() {
  return Math.abs(this.variancePercentage);
});

// Method to determine if variance is significant
PayrollBudgetImpactSchema.methods.isSignificantVariance = function() {
  return Math.abs(this.variancePercentage) > this.varianceThreshold;
};

// Method to determine alert level based on variance
PayrollBudgetImpactSchema.methods.calculateAlertLevel = function() {
  const absVariance = Math.abs(this.variancePercentage);
  
  if (absVariance > 25) {
    return 'critical';
  } else if (absVariance > 10) {
    return 'warning';
  } else {
    return 'info';
  }
};

// Static method to get budget impacts by fiscal year
PayrollBudgetImpactSchema.statics.getByFiscalYear = function(fiscalYear: string) {
  return this.find({ fiscalYear }).sort({ createdAt: -1 });
};

// Static method to get budget impacts requiring approval
PayrollBudgetImpactSchema.statics.getRequiringApproval = function() {
  return this.find({ 
    requiresApproval: true, 
    approvalStatus: 'pending' 
  }).sort({ createdAt: -1 });
};

// Create and export the model
const PayrollBudgetImpact = mongoose.models.PayrollBudgetImpact || 
  mongoose.model<IPayrollBudgetImpact>('PayrollBudgetImpact', PayrollBudgetImpactSchema);

export { PayrollBudgetImpact };
export default PayrollBudgetImpact;

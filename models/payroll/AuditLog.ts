import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for audit log document
 */
export interface IAuditLog extends Document {
  // Basic audit information
  timestamp: Date;
  userId: mongoose.Types.ObjectId;
  action: string;
  module: string;
  entityType: string;
  entityId: string;
  
  // Change tracking
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  changes?: Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }>;
  
  // Request context
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  
  // Additional metadata
  description?: string;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  // Bulk operation tracking
  batchId?: string;
  batchSize?: number;
  batchIndex?: number;
  
  // Error tracking
  isError: boolean;
  errorMessage?: string;
  errorStack?: string;
  
  // Compliance and retention
  retentionDate?: Date;
  isArchived: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for audit log
 */
const AuditLogSchema: Schema = new Schema(
  {
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
      index: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    action: {
      type: String,
      required: true,
      trim: true,
      index: true,
      enum: [
        // CRUD operations
        'create', 'read', 'update', 'delete',
        // Bulk operations
        'bulk_import', 'bulk_update', 'bulk_delete', 'bulk_export',
        // Payroll operations
        'payroll_run_create', 'payroll_run_process', 'payroll_run_approve', 
        'payroll_run_pay', 'payroll_run_cancel',
        'salary_create', 'salary_update', 'salary_delete', 'salary_revision',
        'payslip_generate', 'payslip_email', 'payslip_download',
        // Authentication
        'login', 'logout', 'login_failed', 'password_change',
        // System operations
        'system_backup', 'system_restore', 'data_migration',
        // Custom operations
        'custom'
      ],
    },
    module: {
      type: String,
      required: true,
      trim: true,
      index: true,
      enum: [
        'payroll', 'employees', 'accounting', 'hr', 'auth', 'system', 'reports', 'exports',
        'income', 'expenditure', 'budget', 'vouchers', 'audit', 'recovery'
      ],
    },
    entityType: {
      type: String,
      required: true,
      trim: true,
      index: true,
      enum: [
        'Employee', 'EmployeeSalary', 'PayrollRun', 'PayrollRecord', 'Payslip',
        'SalaryStructure', 'Allowance', 'Deduction', 'TaxBracket', 'SalaryBand',
        'User', 'Department', 'Account', 'Transaction', 'JournalEntry',
        'Income', 'Expenditure', 'Budget', 'BudgetCategory', 'BudgetItem',
        'Voucher', 'VoucherType', 'Session', 'Login', 'Logout',
        'BulkOperation', 'Report', 'Export', 'DeletedItem', 'Recovery'
      ],
    },
    entityId: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    oldValues: {
      type: Schema.Types.Mixed,
    },
    newValues: {
      type: Schema.Types.Mixed,
    },
    changes: [{
      field: {
        type: String,
        required: true,
        trim: true,
      },
      oldValue: {
        type: Schema.Types.Mixed,
      },
      newValue: {
        type: Schema.Types.Mixed,
      },
    }],
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    sessionId: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    severity: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
      index: true,
    },
    batchId: {
      type: String,
      trim: true,
      index: true,
    },
    batchSize: {
      type: Number,
    },
    batchIndex: {
      type: Number,
    },
    isError: {
      type: Boolean,
      required: true,
      default: false,
      index: true,
    },
    errorMessage: {
      type: String,
      trim: true,
    },
    errorStack: {
      type: String,
      trim: true,
    },
    retentionDate: {
      type: Date,
      index: true,
    },
    isArchived: {
      type: Boolean,
      required: true,
      default: false,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient querying
AuditLogSchema.index({ module: 1, entityType: 1, timestamp: -1 });
AuditLogSchema.index({ userId: 1, timestamp: -1 });
AuditLogSchema.index({ action: 1, timestamp: -1 });
AuditLogSchema.index({ batchId: 1, batchIndex: 1 });
AuditLogSchema.index({ timestamp: -1, isArchived: 1 });

// TTL index for automatic cleanup (optional - can be configured)
AuditLogSchema.index({ retentionDate: 1 }, { expireAfterSeconds: 0 });

// Pre-save middleware to set retention date
AuditLogSchema.pre('save', function(next) {
  if (this.isNew && !this.retentionDate) {
    // Set retention date to 7 years from now (compliance requirement)
    const retentionDate = new Date();
    retentionDate.setFullYear(retentionDate.getFullYear() + 7);
    this.retentionDate = retentionDate;
  }
  next();
});

// Create and export the model
const AuditLog = mongoose.models.AuditLog || mongoose.model<IAuditLog>('AuditLog', AuditLogSchema);

export default AuditLog;

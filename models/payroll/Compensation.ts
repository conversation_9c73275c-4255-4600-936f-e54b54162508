import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for compensation document
 */
export interface ICompensation extends Document {
  employeeId: mongoose.Types.ObjectId;
  compensationType: 'performance_bonus' | 'holiday_bonus' | 'overtime' | 'special_allowance' | 'one_time_deduction' | 'retroactive_adjustment';
  amount: number;
  effectiveDate: Date;
  description: string;
  notes?: string;
  currency: string;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  isRecurring: boolean;
  frequency?: 'one_time' | 'monthly' | 'quarterly' | 'annually';
  endDate?: Date;
  taxable: boolean;
  pensionable: boolean;
  payrollRunId?: mongoose.Types.ObjectId; // Optional link to payroll run when processed
  processedAt?: Date; // When this compensation was processed in payroll
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for compensation
 */
const CompensationSchema: Schema = new Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    compensationType: {
      type: String,
      required: true,
      enum: ['performance_bonus', 'holiday_bonus', 'overtime', 'special_allowance', 'one_time_deduction', 'retroactive_adjustment'],
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    effectiveDate: {
      type: Date,
      required: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    currency: {
      type: String,
      required: true,
      default: 'MWK',
      trim: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'approved', 'paid', 'cancelled'],
      default: 'pending',
    },
    isRecurring: {
      type: Boolean,
      required: true,
      default: false,
    },
    frequency: {
      type: String,
      enum: ['one_time', 'monthly', 'quarterly', 'annually'],
      default: 'one_time',
    },
    endDate: {
      type: Date,
    },
    taxable: {
      type: Boolean,
      required: true,
      default: true,
    },
    pensionable: {
      type: Boolean,
      required: true,
      default: false,
    },
    payrollRunId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PayrollRun',
    },
    processedAt: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
CompensationSchema.index({ employeeId: 1 });
CompensationSchema.index({ compensationType: 1 });
CompensationSchema.index({ status: 1 });
CompensationSchema.index({ effectiveDate: 1 });
CompensationSchema.index({ isRecurring: 1 });
CompensationSchema.index({ payrollRunId: 1 });
CompensationSchema.index({ createdAt: -1 });

// Compound indexes
CompensationSchema.index({ employeeId: 1, status: 1 });
CompensationSchema.index({ employeeId: 1, effectiveDate: 1 });
CompensationSchema.index({ status: 1, effectiveDate: 1 });

// Create and export the model
const Compensation = mongoose.models.Compensation || mongoose.model<ICompensation>('Compensation', CompensationSchema);

export default Compensation;

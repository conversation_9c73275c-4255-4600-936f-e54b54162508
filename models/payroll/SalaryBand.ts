import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for salary band document
 * Defines salary ranges and benefits for TCM role codes
 */
export interface ISalaryBand extends Document {
  tcmCode: string;              // TCM 1, TCM 2, etc.
  name: string;                 // Executive, Director, Manager, etc.
  description?: string;         // Description of the band
  minSalary: number;           // Minimum salary for this band
  maxSalary: number;           // Maximum salary for this band
  currency: string;            // Currency (default: MWK)
  
  // Standard components for this band
  standardAllowances: {
    name: string;
    amount?: number;
    percentage?: number;
    isTaxable: boolean;
  }[];
  
  standardDeductions: {
    name: string;
    amount?: number;
    percentage?: number;
  }[];
  
  // Progression settings
  stepIncrement?: number;       // Amount for step progression
  maxSteps?: number;           // Maximum steps in this band
  annualIncrementPercentage?: number; // Annual increment percentage
  
  // Effective dates
  effectiveDate: Date;
  expiryDate?: Date;
  isActive: boolean;
  
  // Approval and audit
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  
  // Metadata
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for standard allowance/deduction in salary band
 */
const StandardComponentSchema: Schema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  amount: {
    type: Number,
  },
  percentage: {
    type: Number,
  },
  isTaxable: {
    type: Boolean,
    default: true,
  },
});

/**
 * Schema for salary band
 */
const SalaryBandSchema: Schema = new Schema(
  {
    tcmCode: {
      type: String,
      required: true,
      trim: true,
      uppercase: true,
      unique: true,
      validate: {
        validator: function(v: string) {
          return /^TCM\s+\d+$/.test(v);
        },
        message: 'TCM Code must be in format "TCM X" where X is a number'
      }
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    minSalary: {
      type: Number,
      required: true,
      min: 0,
    },
    maxSalary: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function(this: ISalaryBand, v: number) {
          return v >= this.minSalary;
        },
        message: 'Maximum salary must be greater than or equal to minimum salary'
      }
    },
    currency: {
      type: String,
      required: true,
      default: 'MWK',
      trim: true,
    },
    standardAllowances: [StandardComponentSchema],
    standardDeductions: [{
      name: {
        type: String,
        required: true,
        trim: true,
      },
      amount: {
        type: Number,
      },
      percentage: {
        type: Number,
      },
    }],
    stepIncrement: {
      type: Number,
      min: 0,
    },
    maxSteps: {
      type: Number,
      min: 1,
      default: 10,
    },
    annualIncrementPercentage: {
      type: Number,
      min: 0,
      max: 100,
    },
    effectiveDate: {
      type: Date,
      required: true,
    },
    expiryDate: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    approvedAt: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
SalaryBandSchema.index({ tcmCode: 1 });
SalaryBandSchema.index({ isActive: 1 });
SalaryBandSchema.index({ effectiveDate: -1 });

// Add pre-save validation
SalaryBandSchema.pre('save', function(next) {
  const salaryBand = this as ISalaryBand;
  
  // Ensure expiry date is after effective date
  if (salaryBand.expiryDate && salaryBand.expiryDate <= salaryBand.effectiveDate) {
    return next(new Error('Expiry date must be after effective date'));
  }
  
  next();
});

// Create and export the model
const SalaryBand = mongoose.models.SalaryBand || mongoose.model<ISalaryBand>('SalaryBand', SalaryBandSchema);

export default SalaryBand;

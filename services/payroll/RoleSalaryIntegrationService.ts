import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Role, { IRole } from '@/models/Role';
import SalaryBand, { ISalaryBand } from '@/models/payroll/SalaryBand';
import SalaryStructure, { ISalaryStructure } from '@/models/payroll/SalaryStructure';
import EmployeeSalary, { IEmployeeSalary } from '@/models/payroll/EmployeeSalary';
import { Employee } from '@/models/Employee';
import mongoose from 'mongoose';

/**
 * Service for integrating roles with salary management
 */
export class RoleSalaryIntegrationService {

  /**
   * Get salary band for a specific role
   * @param roleId - Role ID
   * @returns Salary band or null if not found
   */
  async getSalaryBandForRole(roleId: string): Promise<ISalaryBand | null> {
    try {
      await connectToDatabase();
      
      // Get role details
      const role = await Role.findById(roleId);
      if (!role) {
        throw new Error(`Role with ID ${roleId} not found`);
      }

      // Find salary band by TCM code
      const salaryBand = await SalaryBand.findOne({
        tcmCode: role.code.toUpperCase(),
        isActive: true
      });

      return salaryBand;
    } catch (error) {
      logger.error('Error getting salary band for role', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get suggested salary structures for a role
   * @param roleId - Role ID
   * @returns Array of applicable salary structures
   */
  async getSuggestedSalaryStructures(roleId: string): Promise<ISalaryStructure[]> {
    try {
      await connectToDatabase();
      
      // Get role details
      const role = await Role.findById(roleId);
      if (!role) {
        throw new Error(`Role with ID ${roleId} not found`);
      }

      // Find salary structures that apply to this role
      const salaryStructures = await SalaryStructure.find({
        $or: [
          { applicableRoles: { $in: [role.code] } },
          { applicableRoles: { $in: [roleId] } },
          { applicableRoles: { $size: 0 } } // Universal structures
        ],
        isActive: true,
        effectiveDate: { $lte: new Date() },
        $or: [
          { expiryDate: { $exists: false } },
          { expiryDate: { $gte: new Date() } }
        ]
      }).sort({ name: 1 });

      return salaryStructures;
    } catch (error) {
      logger.error('Error getting suggested salary structures', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Validate if a salary is within the acceptable range for a role
   * @param roleId - Role ID
   * @param basicSalary - Basic salary amount
   * @returns Validation result with details
   */
  async validateSalaryForRole(roleId: string, basicSalary: number): Promise<{
    isValid: boolean;
    message: string;
    salaryBand?: ISalaryBand;
    suggestions?: {
      minSalary: number;
      maxSalary: number;
      recommendedSalary: number;
    };
  }> {
    try {
      await connectToDatabase();
      
      const salaryBand = await this.getSalaryBandForRole(roleId);
      
      if (!salaryBand) {
        return {
          isValid: true, // Allow if no band defined
          message: 'No salary band defined for this role. Manual validation required.'
        };
      }

      const isWithinRange = basicSalary >= salaryBand.minSalary && basicSalary <= salaryBand.maxSalary;
      
      if (isWithinRange) {
        return {
          isValid: true,
          message: 'Salary is within the acceptable range for this role.',
          salaryBand
        };
      } else {
        const recommendedSalary = Math.round((salaryBand.minSalary + salaryBand.maxSalary) / 2);
        
        return {
          isValid: false,
          message: `Salary ${basicSalary.toLocaleString()} is outside the range for ${salaryBand.name} (${salaryBand.tcmCode})`,
          salaryBand,
          suggestions: {
            minSalary: salaryBand.minSalary,
            maxSalary: salaryBand.maxSalary,
            recommendedSalary
          }
        };
      }
    } catch (error) {
      logger.error('Error validating salary for role', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Create salary assignment for employee based on their role
   * @param employeeId - Employee ID
   * @param userId - User ID creating the assignment
   * @param overrides - Optional overrides for salary calculation
   * @returns Created employee salary
   */
  async createRoleBasedSalaryAssignment(
    employeeId: string,
    userId: string,
    overrides?: {
      basicSalary?: number;
      salaryStructureId?: string;
      effectiveDate?: Date;
    }
  ): Promise<IEmployeeSalary> {
    try {
      await connectToDatabase();
      
      // Get employee with role information
      const employee = await Employee.findById(employeeId).populate('roleId');
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      // For now, use position field since roleId might not be implemented yet
      // This will be updated when Employee model is enhanced
      const roleCode = employee.position; // Temporary until roleId is implemented
      
      if (!roleCode) {
        throw new Error('Employee does not have a role assigned');
      }

      // Find role by position/code
      const role = await Role.findOne({ 
        $or: [
          { code: roleCode },
          { name: roleCode }
        ]
      });

      if (!role) {
        throw new Error(`Role not found for position: ${roleCode}`);
      }

      // Get salary band for role
      const salaryBand = await this.getSalaryBandForRole(role._id.toString());
      
      // Get suggested salary structures
      const suggestedStructures = await this.getSuggestedSalaryStructures(role._id.toString());
      
      if (suggestedStructures.length === 0) {
        throw new Error(`No salary structures found for role: ${role.name}`);
      }

      // Use the first suggested structure or override
      const salaryStructure = overrides?.salaryStructureId 
        ? await SalaryStructure.findById(overrides.salaryStructureId)
        : suggestedStructures[0];

      if (!salaryStructure) {
        throw new Error('Salary structure not found');
      }

      // Calculate basic salary
      let basicSalary = overrides?.basicSalary;
      
      if (!basicSalary && salaryBand) {
        // Use recommended salary (midpoint of band)
        basicSalary = Math.round((salaryBand.minSalary + salaryBand.maxSalary) / 2);
      }

      if (!basicSalary) {
        throw new Error('Unable to determine basic salary for role');
      }

      // Validate salary
      const validation = await this.validateSalaryForRole(role._id.toString(), basicSalary);
      if (!validation.isValid) {
        logger.warn('Salary validation warning', LogCategory.PAYROLL, {
          employeeId,
          roleId: role._id,
          basicSalary,
          message: validation.message
        });
      }

      // Create employee salary
      const employeeSalaryData = {
        employeeId: new mongoose.Types.ObjectId(employeeId),
        salaryStructureId: salaryStructure._id,
        basicSalary,
        currency: salaryBand?.currency || 'MWK',
        effectiveDate: overrides?.effectiveDate || new Date(),
        isActive: true,
        allowances: salaryBand?.standardAllowances || [],
        deductions: salaryBand?.standardDeductions || [],
        createdBy: new mongoose.Types.ObjectId(userId)
      };

      const employeeSalary = new EmployeeSalary(employeeSalaryData);
      await employeeSalary.save();

      logger.info('Role-based salary assignment created', LogCategory.PAYROLL, {
        employeeId,
        roleId: role._id,
        salaryBandId: salaryBand?._id,
        basicSalary,
        userId
      });

      return employeeSalary;
    } catch (error) {
      logger.error('Error creating role-based salary assignment', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Get salary statistics by role
   * @param roleId - Role ID (optional, if not provided returns all roles)
   * @returns Salary statistics
   */
  async getSalaryStatisticsByRole(roleId?: string): Promise<any[]> {
    try {
      await connectToDatabase();
      
      const matchStage: any = { isActive: true };
      if (roleId) {
        // This will need to be updated when Employee.roleId is implemented
        matchStage.roleId = new mongoose.Types.ObjectId(roleId);
      }

      const pipeline = [
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employee'
          }
        },
        { $unwind: '$employee' },
        {
          $lookup: {
            from: 'roles',
            localField: 'employee.position', // Temporary until roleId is implemented
            foreignField: 'name',
            as: 'role'
          }
        },
        { $unwind: { path: '$role', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$role._id',
            roleName: { $first: '$role.name' },
            roleCode: { $first: '$role.code' },
            count: { $sum: 1 },
            avgSalary: { $avg: '$basicSalary' },
            minSalary: { $min: '$basicSalary' },
            maxSalary: { $max: '$basicSalary' },
            totalSalary: { $sum: '$basicSalary' }
          }
        },
        { $sort: { roleCode: 1 } }
      ];

      const statistics = await EmployeeSalary.aggregate(pipeline);
      return statistics;
    } catch (error) {
      logger.error('Error getting salary statistics by role', LogCategory.PAYROLL, error);
      throw error;
    }
  }
}

// Export singleton instance
export const roleSalaryIntegrationService = new RoleSalaryIntegrationService();

// Define the PayrollRecord interface
export interface PayrollRecord {
  id: string;
  employee: {
    id: string;
    name: string;
    department: string;
    position: string;
    avatar?: string;
  };
  baseSalary: number;
  bonus: number;
  deductions: number;
  netPay: number;
  status: string;
  paymentDate: string;
}

// Empty array for payroll data - we'll fetch real data from the API
export const mockPayrollData: PayrollRecord[] = []

// Define the PayrollHistory interface
export interface PayrollHistory {
  id: string;
  period: string;
  employees: number;
  totalAmount: number;
  processDate: string;
  status: string;
}

// Empty array for payroll history - we'll fetch real data from the API
export const mockPayrollHistory: PayrollHistory[] = []

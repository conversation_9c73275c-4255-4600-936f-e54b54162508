import type { Task } from "@/types/task"

export const mockTasks: Task[] = [
  {
    id: "task-1",
    title: "Review job applications",
    description: "Review and shortlist candidates for the Senior Developer position",
    status: "todo",
    priority: "high",
    createdAt: "2023-05-01T10:00:00Z",
    dueDate: "2023-05-10T17:00:00Z",
    assignedBy: {
      id: "user-1",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
    category: "Recruitment",
    assignedTo: [
      {
        id: "user-2",
        name: "<PERSON>",
        avatar: "/placeholder-user.jpg",
        position: "HR Specialist",
      },
      {
        id: "user-3",
        name: "<PERSON>",
        avatar: "/placeholder-user.jpg",
        position: "Hiring Manager",
      },
    ],
    comments: [
      {
        user: {
          id: "user-1",
          name: "<PERSON>",
          avatar: "/placeholder-user.jpg",
        },
        text: "We need to finalize this by Friday.",
        timestamp: "2023-05-02T14:30:00Z",
      },
    ],
  },
  {
    id: "task-2",
    title: "Prepare onboarding materials",
    description: "Create onboarding documents and schedule for new hires starting next month",
    status: "in-progress",
    priority: "medium",
    createdAt: "2023-05-02T09:00:00Z",
    dueDate: "2023-05-15T17:00:00Z",
    assignedBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    category: "Onboarding",
    assignedTo: [
      {
        id: "user-4",
        name: "Emily Rodriguez",
        avatar: "/placeholder-user.jpg",
        position: "HR Coordinator",
      },
    ],
    attachments: [
      {
        name: "Onboarding_Template.docx",
        size: "245 KB",
      },
    ],
    comments: [
      {
        user: {
          id: "user-4",
          name: "Emily Rodriguez",
          avatar: "/placeholder-user.jpg",
        },
        text: "I've started working on the welcome packet.",
        timestamp: "2023-05-03T11:20:00Z",
      },
    ],
  },
  {
    id: "task-3",
    title: "Update employee handbook",
    description: "Update the employee handbook with new policies and procedures",
    status: "completed",
    priority: "medium",
    createdAt: "2023-04-20T14:00:00Z",
    dueDate: "2023-05-05T17:00:00Z",
    assignedBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    category: "Documentation",
    assignedTo: [
      {
        id: "user-1",
        name: "John Doe",
        avatar: "/placeholder-user.jpg",
        position: "HR Director",
      },
    ],
    attachments: [
      {
        name: "Employee_Handbook_v2.pdf",
        size: "1.2 MB",
      },
    ],
    comments: [
      {
        user: {
          id: "user-1",
          name: "John Doe",
          avatar: "/placeholder-user.jpg",
        },
        text: "Completed and sent for legal review.",
        timestamp: "2023-05-04T16:45:00Z",
      },
    ],
  },
  {
    id: "task-4",
    title: "Conduct performance reviews",
    description: "Schedule and conduct quarterly performance reviews for the engineering team",
    status: "todo",
    priority: "high",
    createdAt: "2023-05-03T11:00:00Z",
    dueDate: "2023-05-20T17:00:00Z",
    assignedBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    category: "Performance",
    assignedTo: [
      {
        id: "user-1",
        name: "John Doe",
        avatar: "/placeholder-user.jpg",
        position: "HR Director",
      },
      {
        id: "user-5",
        name: "David Kim",
        avatar: "/placeholder-user.jpg",
        position: "Team Lead",
      },
    ],
  },
  {
    id: "task-5",
    title: "Plan team building event",
    description: "Organize a team building event for the end of the quarter",
    status: "in-progress",
    priority: "low",
    createdAt: "2023-05-04T13:00:00Z",
    dueDate: "2023-06-15T17:00:00Z",
    assignedBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    category: "Team Building",
    assignedTo: [
      {
        id: "user-4",
        name: "Emily Rodriguez",
        avatar: "/placeholder-user.jpg",
        position: "HR Coordinator",
      },
    ],
    comments: [
      {
        user: {
          id: "user-4",
          name: "Emily Rodriguez",
          avatar: "/placeholder-user.jpg",
        },
        text: "I've contacted several venues for quotes.",
        timestamp: "2023-05-05T10:15:00Z",
      },
    ],
  },
]

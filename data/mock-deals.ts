import type { Deal } from "@/types/deal"

export const mockDeals: Deal[] = [
  {
    id: "deal-001",
    name: "Enterprise Software Implementation",
    customerId: "cust-001",
    customerName: "Acme Corporation",
    stage: "proposal",
    value: 250000,
    currency: "USD",
    probability: 70,
    expectedCloseDate: "2023-06-30T00:00:00Z",
    products: [
      {
        id: "prod-001",
        name: "Enterprise License",
        quantity: 1,
        price: 150000,
      },
      {
        id: "prod-002",
        name: "Implementation Services",
        quantity: 100,
        price: 1000,
      },
    ],
    assignedTo: {
      id: "emp-002",
      name: "<PERSON>",
    },
    activities: [
      {
        id: "act-001",
        type: "meeting",
        date: "2023-04-15T14:30:00Z",
        description: "Initial requirements gathering",
        employeeId: "emp-002",
        employeeName: "<PERSON>",
      },
      {
        id: "act-002",
        type: "email",
        date: "2023-04-18T09:15:00Z",
        description: "Sent proposal draft for internal review",
        employeeId: "emp-002",
        employeeName: "<PERSON>",
      },
    ],
    createdAt: "2023-04-10T08:00:00Z",
    updatedAt: "2023-04-18T09:15:00Z",
    tags: ["enterprise", "software", "high-value"],
    notes: "Client needs implementation completed by Q3",
  },
  {
    id: "deal-002",
    name: "Cloud Migration Project",
    customerId: "cust-002",
    customerName: "GlobalTech Solutions",
    stage: "qualified",
    value: 120000,
    currency: "USD",
    probability: 50,
    expectedCloseDate: "2023-07-15T00:00:00Z",
    products: [
      {
        id: "prod-003",
        name: "Cloud Migration Assessment",
        quantity: 1,
        price: 20000,
      },
      {
        id: "prod-004",
        name: "Migration Services",
        quantity: 100,
        price: 1000,
      },
    ],
    assignedTo: {
      id: "emp-003",
      name: "Michael Chen",
    },
    activities: [
      {
        id: "act-003",
        type: "call",
        date: "2023-04-10T11:00:00Z",
        description: "Discussed cloud migration needs and timeline",
        employeeId: "emp-003",
        employeeName: "Michael Chen",
      },
    ],
    createdAt: "2023-04-05T10:30:00Z",
    updatedAt: "2023-04-10T11:30:00Z",
    tags: ["cloud", "migration", "mid-size"],
    notes: "Need to schedule technical assessment",
  },
  {
    id: "deal-003",
    name: "Multi-location Inventory System",
    customerId: "cust-003",
    customerName: "Innovative Retail Group",
    stage: "lead",
    value: 350000,
    currency: "USD",
    probability: 30,
    expectedCloseDate: "2023-08-30T00:00:00Z",
    assignedTo: {
      id: "emp-001",
      name: "John Doe",
    },
    activities: [
      {
        id: "act-004",
        type: "meeting",
        date: "2023-04-05T15:00:00Z",
        description: "Initial discovery meeting",
        employeeId: "emp-001",
        employeeName: "John Doe",
      },
    ],
    createdAt: "2023-04-01T14:00:00Z",
    updatedAt: "2023-04-05T16:30:00Z",
    tags: ["retail", "inventory", "multi-location"],
    notes: "Potential large deal, need to schedule demo",
  },
  {
    id: "deal-004",
    name: "Healthcare Compliance Solution",
    customerId: "cust-004",
    customerName: "HealthFirst Medical Group",
    stage: "negotiation",
    value: 180000,
    currency: "USD",
    probability: 85,
    expectedCloseDate: "2023-05-15T00:00:00Z",
    products: [
      {
        id: "prod-005",
        name: "Compliance Module",
        quantity: 1,
        price: 100000,
      },
      {
        id: "prod-006",
        name: "Staff Training",
        quantity: 80,
        price: 1000,
      },
    ],
    assignedTo: {
      id: "emp-005",
      name: "David Kim",
    },
    activities: [
      {
        id: "act-005",
        type: "email",
        date: "2023-04-12T08:45:00Z",
        description: "Sent updated proposal with compliance details",
        employeeId: "emp-005",
        employeeName: "David Kim",
      },
      {
        id: "act-006",
        type: "call",
        date: "2023-04-14T13:30:00Z",
        description: "Negotiation call about implementation timeline",
        employeeId: "emp-005",
        employeeName: "David Kim",
      },
    ],
    createdAt: "2023-03-20T09:15:00Z",
    updatedAt: "2023-04-14T14:00:00Z",
    tags: ["healthcare", "compliance", "high-probability"],
    notes: "Final negotiation stage, expect to close by mid-May",
  },
  {
    id: "deal-005",
    name: "Sustainable Project Management Tool",
    customerId: "cust-005",
    customerName: "EcoBuilders Construction",
    stage: "lead",
    value: 75000,
    currency: "USD",
    probability: 20,
    expectedCloseDate: "2023-09-30T00:00:00Z",
    assignedTo: {
      id: "emp-004",
      name: "Emily Rodriguez",
    },
    activities: [
      {
        id: "act-007",
        type: "note",
        date: "2023-04-08T16:00:00Z",
        description: "Initial contact at sustainability conference",
        employeeId: "emp-004",
        employeeName: "Emily Rodriguez",
      },
    ],
    createdAt: "2023-04-08T16:00:00Z",
    updatedAt: "2023-04-08T16:00:00Z",
    tags: ["construction", "sustainability", "new-lead"],
    notes: "Need to schedule initial demo",
  },
]

import type { Employee } from "@/types/employee"

export const mockEmployees: Employee[] = [
  {
    _id: "emp-001",
    employeeId: "EMP001",
    firstName: "<PERSON>",
    lastName: "<PERSON>e",
    email: "<EMAIL>",
    phone: "(*************",
    address: "123 Main St, Anytown, CA 94568",
    position: "HR Director",
    departmentId: "dept-hr",
    employmentType: "full-time",
    employmentStatus: "active",
    hireDate: "2018-03-15T00:00:00Z",
    salary: 900000,
    photo: "/placeholder-user.jpg",
  },
  {
    _id: "emp-002",
    employeeId: "EMP002",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    address: "456 Oak Ave, Techville, CA 94569",
    position: "Senior Developer",
    departmentId: "dept-eng",
    employmentType: "full-time",
    employmentStatus: "active",
    hireDate: "2019-06-10T00:00:00Z",
    salary: 850000,
    photo: "/placeholder-user.jpg",
  },
  {
    _id: "emp-003",
    employeeId: "EMP003",
    firstName: "<PERSON>",
    lastName: "Chen",
    email: "<EMAIL>",
    phone: "(*************",
    address: "789 Pine St, Marketville, CA 94570",
    position: "Marketing Specialist",
    departmentId: "dept-marketing",
    employmentType: "full-time",
    employmentStatus: "active",
    hireDate: "2020-02-20T00:00:00Z",
    salary: 750000,
    photo: "/placeholder-user.jpg",
  },
  {
    _id: "emp-004",
    employeeId: "EMP004",
    firstName: "Emily",
    lastName: "Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    address: "101 Elm St, Staffville, CA 94571",
    position: "HR Specialist",
    departmentId: "dept-hr",
    employmentType: "full-time",
    employmentStatus: "active",
    hireDate: "2021-01-15T00:00:00Z",
    salary: 700000,
    photo: "/placeholder-user.jpg",
  },
  {
    _id: "emp-005",
    employeeId: "EMP005",
    firstName: "David",
    lastName: "Kim",
    email: "<EMAIL>",
    phone: "(*************",
    address: "202 Cedar Ave, Codeville, CA 94572",
    position: "Engineering Manager",
    departmentId: "dept-eng",
    employmentType: "full-time",
    employmentStatus: "active",
    hireDate: "2017-08-05T00:00:00Z",
    salary: 950000,
    photo: "/placeholder-user.jpg",
  },
]

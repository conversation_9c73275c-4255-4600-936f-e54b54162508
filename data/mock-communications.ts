export type Communication = {
  id: string
  type: "email" | "message" | "chat" | "call"
  customerName: string
  customerAvatar?: string
  customerId: string
  subject: string
  content: string
  timestamp: string
  status: "unread" | "read" | "replied" | "pending" | "closed"
  assignedTo?: string
  tags?: string[]
}

export const mockCommunications: Communication[] = [
  {
    id: "comm-001",
    type: "email",
    customerName: "Acme Corporation",
    customerId: "cust-001",
    subject: "Product Inquiry",
    content: "We're interested in your enterprise solution. Could you provide more information about pricing and features?",
    timestamp: "2023-06-25T14:30:00Z",
    status: "unread",
    assignedTo: "<PERSON>",
    tags: ["inquiry", "enterprise", "pricing"]
  },
  {
    id: "comm-002",
    type: "message",
    customerName: "TechNova Solutions",
    customerId: "cust-002",
    subject: "Support Request",
    content: "We're experiencing issues with the latest update. The dashboard is not loading correctly for some users.",
    timestamp: "2023-06-25T13:15:00Z",
    status: "replied",
    assignedTo: "<PERSON>",
    tags: ["support", "bug", "dashboard"]
  },
  {
    id: "comm-003",
    type: "chat",
    customerName: "Global Logistics Inc.",
    customerId: "cust-003",
    subject: "Live Chat Session",
    content: "Customer inquired about reactivating their subscription and requested information about current promotions.",
    timestamp: "2023-06-25T11:45:00Z",
    status: "closed",
    assignedTo: "Emily Rodriguez",
    tags: ["subscription", "reactivation", "promotions"]
  },
  {
    id: "comm-004",
    type: "call",
    customerName: "Bright Future Education",
    customerId: "cust-004",
    subject: "Phone Consultation",
    content: "Discussed implementation timeline and training requirements for their team of 15 administrators.",
    timestamp: "2023-06-24T16:20:00Z",
    status: "pending",
    assignedTo: "David Kim",
    tags: ["implementation", "training", "timeline"]
  },
  {
    id: "comm-005",
    type: "email",
    customerName: "HealthPlus Medical",
    customerId: "cust-005",
    subject: "Contract Renewal",
    content: "We'd like to discuss renewing our contract which expires next month. We're interested in exploring the premium tier.",
    timestamp: "2023-06-24T10:05:00Z",
    status: "read",
    assignedTo: "Sarah Johnson",
    tags: ["renewal", "contract", "upgrade"]
  },
  {
    id: "comm-006",
    type: "message",
    customerName: "GreenEarth Renewables",
    customerId: "cust-006",
    subject: "Feature Request",
    content: "We'd like to suggest adding a sustainability metrics dashboard to track environmental impact data.",
    timestamp: "2023-06-23T15:40:00Z",
    status: "replied",
    assignedTo: "Michael Chen",
    tags: ["feature request", "sustainability", "dashboard"]
  },
  {
    id: "comm-007",
    type: "chat",
    customerName: "Luxury Retreats Hotels",
    customerId: "cust-007",
    subject: "Live Chat Session",
    content: "Customer had questions about integrating their existing booking system with our platform.",
    timestamp: "2023-06-23T13:25:00Z",
    status: "closed",
    assignedTo: "Emily Rodriguez",
    tags: ["integration", "booking system"]
  },
  {
    id: "comm-008",
    type: "call",
    customerName: "Financial Fortress",
    customerId: "cust-008",
    subject: "Phone Consultation",
    content: "Discussed security features and compliance requirements for financial data handling.",
    timestamp: "2023-06-22T11:10:00Z",
    status: "pending",
    assignedTo: "David Kim",
    tags: ["security", "compliance", "financial"]
  },
  {
    id: "comm-009",
    type: "email",
    customerName: "Creative Minds Agency",
    customerId: "cust-009",
    subject: "Billing Question",
    content: "We noticed an unexpected charge on our last invoice. Could you please review and clarify?",
    timestamp: "2023-06-22T09:50:00Z",
    status: "unread",
    assignedTo: "Sarah Johnson",
    tags: ["billing", "invoice", "support"]
  },
  {
    id: "comm-010",
    type: "message",
    customerName: "FreshFoods Grocery",
    customerId: "cust-010",
    subject: "Demo Request",
    content: "We're interested in scheduling a demo of your inventory management system for our procurement team.",
    timestamp: "2023-06-21T16:35:00Z",
    status: "replied",
    assignedTo: "Michael Chen",
    tags: ["demo", "inventory", "procurement"]
  }
]

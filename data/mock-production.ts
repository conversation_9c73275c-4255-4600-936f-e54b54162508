import type { ProductionBatch, MaterialRequisition, ProductionStats } from "@/types/production"

export const productionStats: ProductionStats = {
  activeBatches: 8,
  activeBatchesChange: 2.5,
  pendingQC: 3,
  pendingQCItems: 120,
  completedBatches: 24,
  efficiency: 92,
  efficiencyChange: 3.5,
}

export const productionBatches: ProductionBatch[] = [
  {
    id: "batch-001",
    batchNumber: "PRD-2023-001",
    productName: "Custom Desk Organizer",
    quantity: 50,
    startDate: "2023-04-10",
    endDate: "2023-04-20",
    progressPercentage: 100,
    status: "completed",
    assignedTo: "Production Team A",
    rawMaterials: [
      {
        id: "mat-001",
        name: "Bamboo Wood",
        quantity: 100,
        unit: "sq ft",
        consumed: 95,
        wastage: 5,
      },
      {
        id: "mat-002",
        name: "Wood Finish",
        quantity: 5,
        unit: "gallons",
        consumed: 4.5,
        wastage: 0.5,
      },
    ],
    qualityChecks: [
      {
        id: "qc-001",
        checkDate: "2023-04-18",
        inspector: "Quality Team",
        result: "pass",
        notes: "All items meet quality standards",
      },
    ],
  },
  {
    id: "batch-002",
    batchNumber: "PRD-2023-002",
    productName: "Adjustable Monitor Stand",
    quantity: 30,
    startDate: "2023-04-15",
    progressPercentage: 75,
    status: "in-progress",
    assignedTo: "Production Team B",
    rawMaterials: [
      {
        id: "mat-003",
        name: "Aluminum Tubing",
        quantity: 60,
        unit: "meters",
        consumed: 45,
        wastage: 2,
      },
      {
        id: "mat-004",
        name: "Rubber Padding",
        quantity: 10,
        unit: "sheets",
        consumed: 7,
        wastage: 0.5,
      },
    ],
  },
  {
    id: "batch-003",
    batchNumber: "PRD-2023-003",
    productName: "Ergonomic Keyboard Rest",
    quantity: 40,
    startDate: "2023-04-12",
    endDate: "2023-04-18",
    progressPercentage: 100,
    status: "completed",
    assignedTo: "Production Team A",
    rawMaterials: [
      {
        id: "mat-005",
        name: "Memory Foam",
        quantity: 20,
        unit: "sheets",
        consumed: 18,
        wastage: 2,
      },
      {
        id: "mat-006",
        name: "Fabric Cover",
        quantity: 15,
        unit: "yards",
        consumed: 14,
        wastage: 1,
      },
    ],
    qualityChecks: [
      {
        id: "qc-002",
        checkDate: "2023-04-17",
        inspector: "Quality Team",
        result: "pass",
        notes: "All items meet quality standards",
      },
    ],
  },
  {
    id: "batch-004",
    batchNumber: "PRD-2023-004",
    productName: "Cable Management System",
    quantity: 60,
    startDate: "2023-04-18",
    progressPercentage: 40,
    status: "in-progress",
    assignedTo: "Production Team C",
    rawMaterials: [
      {
        id: "mat-007",
        name: "Plastic Tubing",
        quantity: 120,
        unit: "meters",
        consumed: 48,
        wastage: 2,
      },
      {
        id: "mat-008",
        name: "Adhesive Backing",
        quantity: 30,
        unit: "rolls",
        consumed: 12,
        wastage: 0,
      },
    ],
  },
  {
    id: "batch-005",
    batchNumber: "PRD-2023-005",
    productName: "Custom Laptop Stand",
    quantity: 25,
    startDate: "2023-04-05",
    endDate: "2023-04-15",
    progressPercentage: 100,
    status: "completed",
    assignedTo: "Production Team B",
    rawMaterials: [
      {
        id: "mat-009",
        name: "Aluminum Sheet",
        quantity: 50,
        unit: "sq ft",
        consumed: 45,
        wastage: 5,
      },
      {
        id: "mat-010",
        name: "Silicone Padding",
        quantity: 10,
        unit: "sheets",
        consumed: 9,
        wastage: 1,
      },
    ],
    qualityChecks: [
      {
        id: "qc-003",
        checkDate: "2023-04-14",
        inspector: "Quality Team",
        result: "pass",
        notes: "All items meet quality standards",
      },
    ],
  },
  {
    id: "batch-006",
    batchNumber: "PRD-2023-006",
    productName: "Desk Cable Grommets",
    quantity: 100,
    startDate: "2023-04-20",
    progressPercentage: 20,
    status: "in-progress",
    assignedTo: "Production Team A",
    rawMaterials: [
      {
        id: "mat-011",
        name: "ABS Plastic",
        quantity: 50,
        unit: "kg",
        consumed: 10,
        wastage: 1,
      },
    ],
  },
  {
    id: "batch-007",
    batchNumber: "PRD-2023-007",
    productName: "Whiteboard Markers",
    quantity: 500,
    startDate: "2023-04-17",
    progressPercentage: 90,
    status: "pending-qc",
    assignedTo: "Production Team D",
    rawMaterials: [
      {
        id: "mat-012",
        name: "Plastic Barrels",
        quantity: 500,
        unit: "units",
        consumed: 490,
        wastage: 10,
      },
      {
        id: "mat-013",
        name: "Ink Solution",
        quantity: 25,
        unit: "liters",
        consumed: 24,
        wastage: 1,
      },
    ],
  },
  {
    id: "batch-008",
    batchNumber: "PRD-2023-008",
    productName: "Desk Drawer Organizers",
    quantity: 75,
    startDate: "2023-04-19",
    progressPercentage: 60,
    status: "in-progress",
    assignedTo: "Production Team C",
    rawMaterials: [
      {
        id: "mat-014",
        name: "Recycled Plastic",
        quantity: 150,
        unit: "kg",
        consumed: 90,
        wastage: 5,
      },
    ],
  },
  {
    id: "batch-009",
    batchNumber: "PRD-2023-009",
    productName: "Mouse Pad with Wrist Support",
    quantity: 80,
    startDate: "2023-04-16",
    progressPercentage: 85,
    status: "pending-qc",
    assignedTo: "Production Team B",
    rawMaterials: [
      {
        id: "mat-015",
        name: "Neoprene",
        quantity: 40,
        unit: "sheets",
        consumed: 36,
        wastage: 4,
      },
      {
        id: "mat-016",
        name: "Gel Filling",
        quantity: 20,
        unit: "kg",
        consumed: 17,
        wastage: 1,
      },
    ],
  },
  {
    id: "batch-010",
    batchNumber: "PRD-2023-010",
    productName: "Desk Name Plates",
    quantity: 50,
    startDate: "2023-04-14",
    progressPercentage: 30,
    status: "on-hold",
    assignedTo: "Production Team A",
    rawMaterials: [
      {
        id: "mat-017",
        name: "Acrylic Sheet",
        quantity: 25,
        unit: "sheets",
        consumed: 8,
        wastage: 1,
      },
    ],
    notes: "On hold due to material quality issues",
  },
  {
    id: "batch-011",
    batchNumber: "PRD-2023-011",
    productName: "Wireless Charging Pads",
    quantity: 40,
    startDate: "2023-04-25",
    progressPercentage: 0,
    status: "planned",
    assignedTo: "Production Team B",
    rawMaterials: [
      {
        id: "mat-018",
        name: "Charging Coils",
        quantity: 40,
        unit: "units",
        consumed: 0,
        wastage: 0,
      },
      {
        id: "mat-019",
        name: "Plastic Housing",
        quantity: 40,
        unit: "units",
        consumed: 0,
        wastage: 0,
      },
    ],
    notes: "Scheduled for next week",
  },
  {
    id: "batch-012",
    batchNumber: "PRD-2023-012",
    productName: "Ergonomic Footrests",
    quantity: 30,
    startDate: "2023-04-28",
    progressPercentage: 0,
    status: "planned",
    assignedTo: "Production Team C",
    rawMaterials: [
      {
        id: "mat-020",
        name: "Memory Foam",
        quantity: 15,
        unit: "sheets",
        consumed: 0,
        wastage: 0,
      },
      {
        id: "mat-021",
        name: "Steel Frame",
        quantity: 30,
        unit: "units",
        consumed: 0,
        wastage: 0,
      },
    ],
    notes: "Waiting for material delivery",
  },
]

export const materialRequisitions: MaterialRequisition[] = [
  {
    id: "req-001",
    requisitionNumber: "REQ-2023-001",
    requestedBy: "John Smith",
    department: "Production",
    date: "2023-04-05",
    purpose: "For Batch PRD-2023-001",
    status: "fulfilled",
    items: [
      {
        id: "item-001",
        name: "Bamboo Wood",
        quantity: 100,
        unit: "sq ft",
        urgency: "medium",
      },
      {
        id: "item-002",
        name: "Wood Finish",
        quantity: 5,
        unit: "gallons",
        urgency: "medium",
      },
    ],
    approvedBy: "Michael Johnson",
    approvalDate: "2023-04-06",
  },
  {
    id: "req-002",
    requisitionNumber: "REQ-2023-002",
    requestedBy: "Sarah Williams",
    department: "Production",
    date: "2023-04-12",
    purpose: "For Batch PRD-2023-002",
    status: "in-progress",
    items: [
      {
        id: "item-003",
        name: "Aluminum Tubing",
        quantity: 60,
        unit: "meters",
        urgency: "high",
      },
      {
        id: "item-004",
        name: "Rubber Padding",
        quantity: 10,
        unit: "sheets",
        urgency: "medium",
      },
    ],
    approvedBy: "Michael Johnson",
    approvalDate: "2023-04-13",
  },
  {
    id: "req-003",
    requisitionNumber: "REQ-2023-003",
    requestedBy: "David Wilson",
    department: "Production",
    date: "2023-04-18",
    purpose: "For Batch PRD-2023-006",
    status: "approved",
    items: [
      {
        id: "item-005",
        name: "ABS Plastic",
        quantity: 50,
        unit: "kg",
        urgency: "medium",
      },
    ],
    approvedBy: "Michael Johnson",
    approvalDate: "2023-04-19",
  },
  {
    id: "req-004",
    requisitionNumber: "REQ-2023-004",
    requestedBy: "Emily Davis",
    department: "Production",
    date: "2023-04-20",
    purpose: "For Batch PRD-2023-008",
    status: "pending",
    items: [
      {
        id: "item-006",
        name: "Recycled Plastic",
        quantity: 150,
        unit: "kg",
        urgency: "high",
      },
    ],
  },
  {
    id: "req-005",
    requisitionNumber: "REQ-2023-005",
    requestedBy: "Robert Brown",
    department: "Maintenance",
    date: "2023-04-15",
    purpose: "Equipment Repair",
    status: "rejected",
    items: [
      {
        id: "item-007",
        name: "Replacement Parts",
        quantity: 5,
        unit: "sets",
        urgency: "high",
        notes: "For CNC machine repair",
      },
    ],
    approvedBy: "Michael Johnson",
    approvalDate: "2023-04-16",
    notes: "Rejected due to budget constraints. Please resubmit with detailed cost analysis.",
  },
  {
    id: "req-006",
    requisitionNumber: "REQ-2023-006",
    requestedBy: "Jennifer Adams",
    department: "Quality Control",
    date: "2023-04-21",
    purpose: "Testing Equipment",
    status: "pending",
    items: [
      {
        id: "item-008",
        name: "Calibration Tools",
        quantity: 2,
        unit: "sets",
        urgency: "medium",
      },
      {
        id: "item-009",
        name: "Testing Materials",
        quantity: 10,
        unit: "kits",
        urgency: "low",
      },
    ],
  },
]

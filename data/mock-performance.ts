export const mockPerformanceReviews = [
  {
    id: "review-001",
    employee: {
      id: "emp-001",
      name: "<PERSON>",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Review",
    date: "2023-05-15",
    reviewer: {
      id: "emp-005",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
    score: 4.5,
    status: "completed",
  },
  {
    id: "review-002",
    employee: {
      id: "emp-002",
      name: "<PERSON>",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    type: "Quarterly Review",
    date: "2023-05-18",
    reviewer: {
      id: "emp-005",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
    score: 4.8,
    status: "scheduled",
  },
  {
    id: "review-003",
    employee: {
      id: "emp-003",
      name: "<PERSON>",
      department: "Marketing",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Review",
    date: "2023-05-20",
    reviewer: {
      id: "emp-001",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
    score: 4.2,
    status: "scheduled",
  },
  {
    id: "review-004",
    employee: {
      id: "emp-004",
      name: "Emily Rodriguez",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    type: "Probation Review",
    date: "2023-04-15",
    reviewer: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    score: 4.0,
    status: "completed",
  },
  {
    id: "review-005",
    employee: {
      id: "emp-005",
      name: "David Kim",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Review",
    date: "2023-06-10",
    reviewer: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
    score: 4.7,
    status: "scheduled",
  },
]

export const mockPerformanceGoals = [
  {
    id: "goal-001",
    title: "Improve Employee Onboarding Process",
    description: "Streamline the onboarding process to reduce time-to-productivity for new hires",
    type: "individual",
    status: "in-progress",
    progress: 65,
    dueDate: "2023-06-30",
    timeRemaining: "1 month left",
    assignedTo: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "goal-002",
    title: "Implement New Performance Review System",
    description: "Roll out the new performance review system across all departments",
    type: "team",
    status: "in-progress",
    progress: 40,
    dueDate: "2023-07-15",
    timeRemaining: "2 months left",
  },
  {
    id: "goal-003",
    title: "Reduce Employee Turnover Rate",
    description: "Implement strategies to reduce turnover rate by 15%",
    type: "company",
    status: "in-progress",
    progress: 30,
    dueDate: "2023-12-31",
    timeRemaining: "7 months left",
  },
  {
    id: "goal-004",
    title: "Complete Leadership Training",
    description: "Complete the advanced leadership training program",
    type: "individual",
    status: "completed",
    progress: 100,
    dueDate: "2023-04-30",
    timeRemaining: "Completed",
    assignedTo: {
      id: "emp-005",
      name: "David Kim",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "goal-005",
    title: "Improve Team Communication",
    description: "Implement new communication tools and protocols for the engineering team",
    type: "team",
    status: "in-progress",
    progress: 75,
    dueDate: "2023-05-31",
    timeRemaining: "2 weeks left",
  },
  {
    id: "goal-006",
    title: "Enhance Recruitment Process",
    description: "Optimize the recruitment process to reduce time-to-hire by 20%",
    type: "individual",
    status: "in-progress",
    progress: 50,
    dueDate: "2023-08-15",
    timeRemaining: "3 months left",
    assignedTo: {
      id: "emp-004",
      name: "Emily Rodriguez",
      avatar: "/placeholder-user.jpg",
    },
  },
]

export const mockFeedback = [
  {
    id: "feedback-001",
    from: {
      id: "emp-005",
      name: "David Kim",
      position: "Engineering Manager",
      avatar: "/placeholder-user.jpg",
    },
    to: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    content:
      "John has been instrumental in improving our hiring process. His guidance has helped us find top talent quickly.",
    type: "positive",
    date: "2023-05-10",
  },
  {
    id: "feedback-002",
    from: {
      id: "emp-002",
      name: "Sarah Johnson",
      position: "Senior Developer",
      avatar: "/placeholder-user.jpg",
    },
    to: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    content:
      "The new onboarding process John implemented has made it much easier for new team members to get up to speed.",
    type: "positive",
    date: "2023-05-05",
  },
  {
    id: "feedback-003",
    from: {
      id: "emp-003",
      name: "Michael Chen",
      position: "Marketing Specialist",
      avatar: "/placeholder-user.jpg",
    },
    to: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    content:
      "I think the new benefits package could be better communicated. Many team members are confused about the changes.",
    type: "constructive",
    date: "2023-05-12",
  },
  {
    id: "feedback-004",
    from: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    to: {
      id: "emp-002",
      name: "Sarah Johnson",
      position: "Senior Developer",
      avatar: "/placeholder-user.jpg",
    },
    content:
      "Sarah has been a great mentor to junior developers. Her technical leadership has significantly improved team productivity.",
    type: "positive",
    date: "2023-05-08",
  },
  {
    id: "feedback-005",
    from: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    to: {
      id: "emp-005",
      name: "David Kim",
      position: "Engineering Manager",
      avatar: "/placeholder-user.jpg",
    },
    content:
      "David's team has been consistently meeting deadlines, but there have been some quality issues that need attention.",
    type: "constructive",
    date: "2023-05-11",
  },
]

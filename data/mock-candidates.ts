import type { Candidate } from "@/types/candidate"

export const mockCandidates: Candidate[] = [
  {
    id: "cand-001",
    name: "<PERSON>",
    position: "Senior Software Engineer",
    email: "<EMAIL>",
    phone: "(*************",
    location: "San Francisco, CA",
    avatar: "/placeholder-user.jpg",
    status: "interview",
    appliedDate: "2023-04-20T00:00:00Z",
    source: "LinkedIn",
    skills: ["JavaScript", "React", "Node.js", "TypeScript", "AWS", "Docker"],
    education: [
      {
        degree: "MS, Computer Science",
        institution: "Stanford University",
        year: "2018",
      },
      {
        degree: "BS, Computer Engineering",
        institution: "UC Berkeley",
        year: "2016",
      },
    ],
    experience: [
      {
        position: "Software Engineer",
        company: "Tech Solutions Inc.",
        period: "2018 - Present",
        description:
          "Developed and maintained web applications using React and Node.js. Implemented CI/CD pipelines and containerized applications with Docker.",
      },
      {
        position: "Junior Developer",
        company: "StartUp Co.",
        period: "2016 - 2018",
        description:
          "Worked on front-end development using JavaScript and React. Collaborated with design team to implement UI components.",
      },
    ],
  },
  {
    id: "cand-002",
    name: "<PERSON> <PERSON>",
    position: "Marketing Manager",
    email: "<EMAIL>",
    phone: "(*************",
    location: "New York, NY",
    avatar: "/placeholder-user.jpg",
    status: "screening",
    appliedDate: "2023-04-25T00:00:00Z",
    source: "Company Website",
    skills: ["Digital Marketing", "Content Strategy", "SEO", "Social Media", "Analytics", "Campaign Management"],
    education: [
      {
        degree: "MBA, Marketing",
        institution: "NYU Stern",
        year: "2019",
      },
      {
        degree: "BA, Communications",
        institution: "Columbia University",
        year: "2017",
      },
    ],
    experience: [
      {
        position: "Digital Marketing Specialist",
        company: "Marketing Pros",
        period: "2019 - Present",
        description:
          "Managed digital marketing campaigns across multiple channels. Increased website traffic by 45% through SEO optimization and content strategy.",
      },
      {
        position: "Marketing Coordinator",
        company: "Brand Solutions",
        period: "2017 - 2019",
        description:
          "Assisted in the development and execution of marketing campaigns. Managed social media accounts and created content for various platforms.",
      },
    ],
  },
  {
    id: "cand-003",
    name: "Robert Chen",
    position: "HR Specialist",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Chicago, IL",
    avatar: "/placeholder-user.jpg",
    status: "hired",
    appliedDate: "2023-04-15T00:00:00Z",
    source: "Indeed",
    skills: ["Recruitment", "Onboarding", "Employee Relations", "HRIS", "Benefits Administration"],
    education: [
      {
        degree: "BS, Human Resources Management",
        institution: "University of Illinois",
        year: "2020",
      },
    ],
    experience: [
      {
        position: "HR Assistant",
        company: "Corporate Services LLC",
        period: "2020 - Present",
        description:
          "Assisted with recruitment, onboarding, and employee relations. Maintained employee records and HR documentation.",
      },
      {
        position: "HR Intern",
        company: "Global Enterprises",
        period: "2019 - 2020",
        description:
          "Supported HR team with administrative tasks. Assisted with recruitment events and candidate screening.",
      },
    ],
  },
  {
    id: "cand-004",
    name: "Sophia Martinez",
    position: "Financial Analyst",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Boston, MA",
    avatar: "/placeholder-user.jpg",
    status: "screening",
    appliedDate: "2023-05-01T00:00:00Z",
    source: "Referral",
    skills: ["Financial Modeling", "Excel", "Data Analysis", "Forecasting", "Budgeting"],
    education: [
      {
        degree: "MBA, Finance",
        institution: "Boston University",
        year: "2021",
      },
      {
        degree: "BS, Finance",
        institution: "Northeastern University",
        year: "2019",
      },
    ],
    experience: [
      {
        position: "Junior Financial Analyst",
        company: "Finance Corp",
        period: "2021 - Present",
        description:
          "Prepared financial forecasts and budgets. Analyzed financial data and created reports for management.",
      },
      {
        position: "Finance Intern",
        company: "Investment Group",
        period: "2020 - 2021",
        description:
          "Assisted with financial analysis and reporting. Supported budget planning and expense management.",
      },
    ],
  },
  {
    id: "cand-005",
    name: "William Taylor",
    position: "Product Designer",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Seattle, WA",
    avatar: "/placeholder-user.jpg",
    status: "rejected",
    appliedDate: "2023-04-18T00:00:00Z",
    source: "Dribbble",
    skills: ["UI/UX Design", "Figma", "Sketch", "User Research", "Prototyping", "Visual Design"],
    education: [
      {
        degree: "BFA, Graphic Design",
        institution: "Rhode Island School of Design",
        year: "2018",
      },
    ],
    experience: [
      {
        position: "UI/UX Designer",
        company: "Design Studio",
        period: "2018 - Present",
        description:
          "Designed user interfaces for web and mobile applications. Created wireframes, prototypes, and high-fidelity mockups.",
      },
      {
        position: "Design Intern",
        company: "Creative Agency",
        period: "2017 - 2018",
        description:
          "Assisted senior designers with various design projects. Created visual assets for digital and print media.",
      },
    ],
  },
]

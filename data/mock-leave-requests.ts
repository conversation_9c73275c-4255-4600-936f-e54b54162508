import type { LeaveRequest } from "@/types/leave-request"

export const mockLeaveRequests: LeaveRequest[] = [
  {
    id: "leave-001",
    employee: {
      id: "emp-001",
      name: "<PERSON>",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Leave",
    startDate: "2023-05-15",
    endDate: "2023-05-19",
    days: 5,
    reason: "Family vacation",
    status: "approved",
    requestDate: "2023-05-01",
    reviewDate: "2023-05-03",
    reviewedBy: {
      id: "emp-005",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "leave-002",
    employee: {
      id: "emp-002",
      name: "<PERSON>",
      position: "Senior Developer",
      avatar: "/placeholder-user.jpg",
    },
    type: "Sick Leave",
    startDate: "2023-05-08",
    endDate: "2023-05-09",
    days: 2,
    reason: "Flu",
    status: "approved",
    requestDate: "2023-05-07",
    reviewDate: "2023-05-07",
    reviewedBy: {
      id: "emp-005",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "leave-003",
    employee: {
      id: "emp-003",
      name: "<PERSON> <PERSON>",
      position: "Marketing Specialist",
      avatar: "/placeholder-user.jpg",
    },
    type: "Personal Leave",
    startDate: "2023-05-22",
    endDate: "2023-05-24",
    days: 3,
    reason: "Family event",
    status: "pending",
    requestDate: "2023-05-10",
  },
  {
    id: "leave-004",
    employee: {
      id: "emp-004",
      name: "Emily Rodriguez",
      position: "HR Specialist",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Leave",
    startDate: "2023-06-05",
    endDate: "2023-06-09",
    days: 5,
    reason: "Summer vacation",
    status: "pending",
    requestDate: "2023-05-12",
  },
  {
    id: "leave-005",
    employee: {
      id: "emp-005",
      name: "David Kim",
      position: "Engineering Manager",
      avatar: "/placeholder-user.jpg",
    },
    type: "Sick Leave",
    startDate: "2023-05-02",
    endDate: "2023-05-03",
    days: 2,
    reason: "Migraine",
    status: "approved",
    requestDate: "2023-05-02",
    reviewDate: "2023-05-02",
    reviewedBy: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "leave-006",
    employee: {
      id: "emp-002",
      name: "Sarah Johnson",
      position: "Senior Developer",
      avatar: "/placeholder-user.jpg",
    },
    type: "Annual Leave",
    startDate: "2023-07-10",
    endDate: "2023-07-21",
    days: 10,
    reason: "Summer vacation",
    status: "pending",
    requestDate: "2023-05-15",
  },
  {
    id: "leave-007",
    employee: {
      id: "emp-003",
      name: "Michael Chen",
      position: "Marketing Specialist",
      avatar: "/placeholder-user.jpg",
    },
    type: "Personal Leave",
    startDate: "2023-05-05",
    endDate: "2023-05-05",
    days: 1,
    reason: "Doctor's appointment",
    status: "approved",
    requestDate: "2023-05-03",
    reviewDate: "2023-05-03",
    reviewedBy: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "leave-008",
    employee: {
      id: "emp-004",
      name: "Emily Rodriguez",
      position: "HR Specialist",
      avatar: "/placeholder-user.jpg",
    },
    type: "Sick Leave",
    startDate: "2023-04-27",
    endDate: "2023-04-28",
    days: 2,
    reason: "Cold",
    status: "approved",
    requestDate: "2023-04-27",
    reviewDate: "2023-04-27",
    reviewedBy: {
      id: "emp-001",
      name: "John Doe",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "leave-009",
    employee: {
      id: "emp-001",
      name: "John Doe",
      position: "HR Director",
      avatar: "/placeholder-user.jpg",
    },
    type: "Personal Leave",
    startDate: "2023-05-26",
    endDate: "2023-05-26",
    days: 1,
    reason: "Personal matter",
    status: "rejected",
    requestDate: "2023-05-20",
    reviewDate: "2023-05-21",
    reviewedBy: {
      id: "emp-005",
      name: "David Kim",
      avatar: "/placeholder-user.jpg",
    },
    comments: "Critical meeting scheduled on this day. Please reschedule your leave.",
  },
]

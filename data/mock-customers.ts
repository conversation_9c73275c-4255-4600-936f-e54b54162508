import type { Customer } from "@/types/customer"

export const mockCustomers: Customer[] = [
  {
    id: "cust-001",
    name: "Acme Corporation",
    industry: "Technology",
    website: "https://acme.example.com",
    logo: "/placeholder.svg?height=40&width=40",
    status: "active",
    address: "123 Business Ave",
    city: "San Francisco",
    state: "CA",
    zipCode: "94105",
    country: "USA",
    annualRevenue: 5000000,
    employeeCount: 120,
    accountManager: {
      id: "emp-002",
      name: "<PERSON>",
    },
    tags: ["enterprise", "tech", "high-value"],
    contacts: [
      {
        id: "contact-001",
        name: "<PERSON>",
        position: "CTO",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: true,
      },
      {
        id: "contact-002",
        name: "<PERSON>",
        position: "Procurement Manager",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: false,
      },
    ],
    interactions: [
      {
        id: "int-001",
        type: "meeting",
        date: "2023-04-15T14:30:00Z",
        summary: "Discussed upcoming project requirements and timeline",
        employeeId: "emp-002",
        employeeName: "<PERSON> <PERSON>",
        followUpDate: "2023-04-22T10:00:00Z",
        followUpTask: "Send project proposal",
      },
      {
        id: "int-002",
        type: "email",
        date: "2023-04-18T09:15:00Z",
        summary: "Sent initial proposal draft for review",
        employeeId: "emp-002",
        employeeName: "Sarah Johnson",
      },
    ],
    createdAt: "2022-01-15T08:00:00Z",
    updatedAt: "2023-04-18T09:15:00Z",
    notes: "Key enterprise client with multiple ongoing projects",
  },
  {
    id: "cust-002",
    name: "GlobalTech Solutions",
    industry: "IT Services",
    website: "https://globaltech.example.com",
    logo: "/placeholder.svg?height=40&width=40",
    status: "active",
    address: "456 Tech Parkway",
    city: "Boston",
    state: "MA",
    zipCode: "02110",
    country: "USA",
    annualRevenue: 3200000,
    employeeCount: 85,
    accountManager: {
      id: "emp-003",
      name: "Michael Chen",
    },
    tags: ["mid-market", "it-services", "growing"],
    contacts: [
      {
        id: "contact-003",
        name: "Robert Johnson",
        position: "CEO",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: true,
      },
    ],
    interactions: [
      {
        id: "int-003",
        type: "call",
        date: "2023-04-10T11:00:00Z",
        summary: "Quarterly check-in call, discussed expanding services",
        employeeId: "emp-003",
        employeeName: "Michael Chen",
        followUpDate: "2023-05-01T13:00:00Z",
        followUpTask: "Prepare service expansion proposal",
      },
    ],
    createdAt: "2022-03-10T10:30:00Z",
    updatedAt: "2023-04-10T11:30:00Z",
    notes: "Interested in expanding to cloud services",
  },
  {
    id: "cust-003",
    name: "Innovative Retail Group",
    industry: "Retail",
    website: "https://irg.example.com",
    logo: "/placeholder.svg?height=40&width=40",
    status: "prospect",
    address: "789 Shopping Lane",
    city: "Chicago",
    state: "IL",
    zipCode: "60611",
    country: "USA",
    employeeCount: 500,
    tags: ["retail", "prospect", "multi-location"],
    contacts: [
      {
        id: "contact-004",
        name: "Maria Garcia",
        position: "Head of Operations",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: true,
      },
      {
        id: "contact-005",
        name: "David Wilson",
        position: "IT Director",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: false,
      },
    ],
    interactions: [
      {
        id: "int-004",
        type: "meeting",
        date: "2023-04-05T15:00:00Z",
        summary: "Initial meeting to discuss potential solutions for their multi-location inventory challenges",
        employeeId: "emp-001",
        employeeName: "John Doe",
      },
    ],
    createdAt: "2023-03-25T14:00:00Z",
    updatedAt: "2023-04-05T16:30:00Z",
    notes: "Looking for inventory management solutions across 50+ locations",
  },
  {
    id: "cust-004",
    name: "HealthFirst Medical Group",
    industry: "Healthcare",
    website: "https://healthfirst.example.com",
    logo: "/placeholder.svg?height=40&width=40",
    status: "active",
    address: "101 Wellness Blvd",
    city: "Seattle",
    state: "WA",
    zipCode: "98101",
    country: "USA",
    annualRevenue: ********,
    employeeCount: 300,
    accountManager: {
      id: "emp-005",
      name: "David Kim",
    },
    tags: ["healthcare", "enterprise", "compliance"],
    contacts: [
      {
        id: "contact-006",
        name: "Dr. Sarah Miller",
        position: "Chief Medical Officer",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: true,
      },
    ],
    interactions: [
      {
        id: "int-005",
        type: "email",
        date: "2023-04-12T08:45:00Z",
        summary: "Sent updated compliance documentation for their review",
        employeeId: "emp-005",
        employeeName: "David Kim",
      },
      {
        id: "int-006",
        type: "call",
        date: "2023-04-14T13:30:00Z",
        summary: "Discussed implementation timeline for new system",
        employeeId: "emp-005",
        employeeName: "David Kim",
        followUpDate: "2023-04-21T10:00:00Z",
        followUpTask: "Send implementation schedule",
      },
    ],
    createdAt: "2022-05-20T09:15:00Z",
    updatedAt: "2023-04-14T14:00:00Z",
    notes: "Requires HIPAA compliance for all solutions",
  },
  {
    id: "cust-005",
    name: "EcoBuilders Construction",
    industry: "Construction",
    website: "https://ecobuilders.example.com",
    logo: "/placeholder.svg?height=40&width=40",
    status: "lead",
    address: "202 Green Building Way",
    city: "Portland",
    state: "OR",
    zipCode: "97201",
    country: "USA",
    employeeCount: 75,
    tags: ["construction", "lead", "sustainability"],
    contacts: [
      {
        id: "contact-007",
        name: "Thomas Green",
        position: "Founder & CEO",
        email: "<EMAIL>",
        phone: "(*************",
        isPrimary: true,
      },
    ],
    interactions: [
      {
        id: "int-007",
        type: "note",
        date: "2023-04-08T16:00:00Z",
        summary: "Lead generated from sustainability conference",
        employeeId: "emp-004",
        employeeName: "Emily Rodriguez",
      },
    ],
    createdAt: "2023-04-08T16:00:00Z",
    updatedAt: "2023-04-08T16:00:00Z",
    notes: "Interested in sustainable project management solutions",
  },
]

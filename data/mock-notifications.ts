export const mockNotifications = [
  {
    id: "notif-001",
    title: "Leave Request Approved",
    message: "Your leave request for May 15-19 has been approved.",
    type: "leave",
    time: "10 minutes ago",
    read: false,
    important: true,
    user: {
      id: "emp-005",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "notif-002",
    title: "New Employee Joined",
    message: "<PERSON> has joined the Marketing department.",
    type: "user",
    time: "1 hour ago",
    read: false,
    important: false,
    user: {
      id: "emp-006",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "notif-003",
    title: "Performance Review Scheduled",
    message: "Your quarterly performance review is scheduled for May 18.",
    type: "calendar",
    time: "3 hours ago",
    read: true,
    important: true,
    user: {
      id: "emp-001",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "notif-004",
    title: "Document Updated",
    message: "The Employee Handbook has been updated with new policies.",
    type: "document",
    time: "Yesterday",
    read: true,
    important: false,
    user: {
      id: "emp-001",
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "notif-005",
    title: "Training Course Available",
    message: "New training course 'Leadership Fundamentals' is now available.",
    type: "calendar",
    time: "Yesterday",
    read: false,
    important: false,
    user: null,
  },
  {
    id: "notif-006",
    title: "Payroll Processed",
    message: "April 2023 payroll has been processed successfully.",
    type: "document",
    time: "2 days ago",
    read: true,
    important: false,
    user: null,
  },
  {
    id: "notif-007",
    title: "Leave Request Submitted",
    message: "Michael Chen has submitted a leave request for your approval.",
    type: "leave",
    time: "2 days ago",
    read: true,
    important: false,
    user: {
      id: "emp-003",
      name: "Michael Chen",
      avatar: "/placeholder-user.jpg",
    },
  },
  {
    id: "notif-008",
    title: "System Maintenance",
    message: "The HR system will be undergoing maintenance on Saturday, May 13.",
    type: "system",
    time: "3 days ago",
    read: true,
    important: true,
    user: null,
  },
  {
    id: "notif-009",
    title: "New Job Opening",
    message: "A new job opening for 'Senior Developer' has been posted.",
    type: "document",
    time: "4 days ago",
    read: true,
    important: false,
    user: null,
  },
  {
    id: "notif-010",
    title: "Benefits Enrollment",
    message: "Annual benefits enrollment period starts next week.",
    type: "calendar",
    time: "5 days ago",
    read: true,
    important: true,
    user: null,
  },
]

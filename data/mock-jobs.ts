import type { Job } from "@/types/job"

export const mockJobs: Job[] = [
  {
    id: "job-001",
    title: "Senior Software Engineer",
    department: "Engineering",
    location: "San Francisco, CA (Hybrid)",
    type: "Full-time",
    salary: "$120,000 - $150,000",
    description:
      "We are seeking an experienced Senior Software Engineer to join our engineering team. The ideal candidate will have strong experience in full-stack development and will be responsible for designing, developing, and maintaining our core products.",
    requirements: [
      "5+ years of experience in software development",
      "Strong proficiency in JavaScript/TypeScript, React, and Node.js",
      "Experience with cloud services (AWS, Azure, or GCP)",
      "Knowledge of database design and optimization",
      "Excellent problem-solving and communication skills",
    ],
    responsibilities: [
      "Design and implement new features for our web applications",
      "Collaborate with product managers and designers to define requirements",
      "Write clean, maintainable, and efficient code",
      "Mentor junior developers and conduct code reviews",
      "Participate in agile development processes",
    ],
    status: "active",
    postedDate: "2023-04-15T00:00:00Z",
    closingDate: "2023-05-30T00:00:00Z",
    applicants: 45,
  },
  {
    id: "job-002",
    title: "Marketing Manager",
    department: "Marketing",
    location: "New York, NY (Remote)",
    type: "Full-time",
    salary: "$90,000 - $110,000",
    description:
      "We are looking for a Marketing Manager to lead our marketing efforts and drive brand awareness and customer acquisition. The successful candidate will develop and execute marketing strategies across various channels.",
    requirements: [
      "3+ years of experience in digital marketing",
      "Strong understanding of marketing channels and metrics",
      "Experience with marketing automation tools",
      "Excellent analytical and communication skills",
      "Bachelor's degree in Marketing or related field",
    ],
    responsibilities: [
      "Develop and implement marketing strategies",
      "Manage digital marketing campaigns across multiple channels",
      "Analyze campaign performance and optimize for results",
      "Collaborate with content and design teams",
      "Manage marketing budget and report on ROI",
    ],
    status: "active",
    postedDate: "2023-04-20T00:00:00Z",
    closingDate: "2023-05-25T00:00:00Z",
    applicants: 32,
  },
  {
    id: "job-003",
    title: "HR Specialist",
    department: "Human Resources",
    location: "Chicago, IL (On-site)",
    type: "Full-time",
    salary: "$65,000 - $80,000",
    description:
      "We are seeking an HR Specialist to join our Human Resources team. The ideal candidate will support various HR functions including recruitment, onboarding, and employee relations.",
    requirements: [
      "2+ years of experience in HR",
      "Knowledge of HR policies and procedures",
      "Experience with HRIS systems",
      "Strong interpersonal and communication skills",
      "Bachelor's degree in Human Resources or related field",
    ],
    responsibilities: [
      "Assist with recruitment and onboarding processes",
      "Maintain employee records and HR documentation",
      "Support benefits administration and enrollment",
      "Address employee inquiries and concerns",
      "Assist with HR projects and initiatives",
    ],
    status: "active",
    postedDate: "2023-04-25T00:00:00Z",
    closingDate: "2023-05-20T00:00:00Z",
    applicants: 18,
  },
  {
    id: "job-004",
    title: "Financial Analyst",
    department: "Finance",
    location: "Boston, MA (Hybrid)",
    type: "Full-time",
    salary: "$75,000 - $95,000",
    description:
      "We are looking for a Financial Analyst to join our Finance team. The successful candidate will be responsible for financial planning, analysis, and reporting to support business decisions.",
    requirements: [
      "3+ years of experience in financial analysis",
      "Strong proficiency in Excel and financial modeling",
      "Knowledge of accounting principles and financial reporting",
      "Excellent analytical and problem-solving skills",
      "Bachelor's degree in Finance, Accounting, or related field",
    ],
    responsibilities: [
      "Prepare financial forecasts and budgets",
      "Analyze financial data and identify trends",
      "Create financial reports and presentations",
      "Support budget planning and expense management",
      "Collaborate with department heads on financial matters",
    ],
    status: "draft",
    postedDate: "2023-05-01T00:00:00Z",
    closingDate: "2023-06-15T00:00:00Z",
    applicants: 0,
  },
  {
    id: "job-005",
    title: "Product Designer",
    department: "Design",
    location: "Seattle, WA (Remote)",
    type: "Full-time",
    salary: "$100,000 - $130,000",
    description:
      "We are seeking a talented Product Designer to create intuitive and engaging user experiences for our products. The ideal candidate will have a strong portfolio demonstrating UX/UI design skills.",
    requirements: [
      "4+ years of experience in product design",
      "Proficiency in design tools (Figma, Sketch, Adobe XD)",
      "Experience with user research and usability testing",
      "Strong visual design and interaction design skills",
      "Portfolio showcasing UX/UI design work",
    ],
    responsibilities: [
      "Design user interfaces for web and mobile applications",
      "Create wireframes, prototypes, and high-fidelity mockups",
      "Conduct user research and usability testing",
      "Collaborate with product managers and engineers",
      "Maintain and evolve design systems",
    ],
    status: "active",
    postedDate: "2023-04-10T00:00:00Z",
    closingDate: "2023-05-15T00:00:00Z",
    applicants: 38,
  },
]

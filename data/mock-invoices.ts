import type { Invoice, InvoiceStats } from "@/types/invoice"

export const mockInvoices: Invoice[] = [
  {
    id: "inv-001",
    invoiceNumber: "INV-2023-001",
    customerId: "cust-001",
    customerName: "Acme Corporation",
    status: "paid",
    issueDate: "2023-03-15",
    dueDate: "2023-04-14",
    items: [
      {
        id: "item-001",
        description: "Website Development",
        quantity: 1,
        unitPrice: 5000,
        taxRate: 10,
        total: 5500,
      },
      {
        id: "item-002",
        description: "Hosting (Annual)",
        quantity: 1,
        unitPrice: 600,
        taxRate: 10,
        total: 660,
      },
    ],
    subtotal: 5600,
    taxTotal: 560,
    total: 6160,
    notes: "Thank you for your business!",
    terms: "Payment due within 30 days",
    createdBy: "<PERSON>",
    createdAt: "2023-03-15T10:30:00Z",
    updatedAt: "2023-03-15T10:30:00Z",
    paidDate: "2023-04-10",
    paidAmount: 6160,
  },
  {
    id: "inv-002",
    invoiceNumber: "INV-2023-002",
    customerId: "cust-002",
    customerName: "TechStart Inc.",
    status: "overdue",
    issueDate: "2023-03-20",
    dueDate: "2023-04-19",
    items: [
      {
        id: "item-003",
        description: "UI/UX Design",
        quantity: 40,
        unitPrice: 85,
        taxRate: 10,
        total: 3740,
      },
    ],
    subtotal: 3400,
    taxTotal: 340,
    total: 3740,
    notes: "Please pay promptly",
    terms: "Payment due within 30 days",
    createdBy: "John Doe",
    createdAt: "2023-03-20T14:15:00Z",
    updatedAt: "2023-03-20T14:15:00Z",
  },
  {
    id: "inv-003",
    invoiceNumber: "INV-2023-003",
    customerId: "cust-003",
    customerName: "Global Enterprises",
    status: "sent",
    issueDate: "2023-04-05",
    dueDate: "2023-05-05",
    items: [
      {
        id: "item-004",
        description: "Consulting Services",
        quantity: 20,
        unitPrice: 150,
        taxRate: 10,
        total: 3300,
      },
      {
        id: "item-005",
        description: "Market Research Report",
        quantity: 1,
        unitPrice: 2500,
        taxRate: 10,
        total: 2750,
      },
    ],
    subtotal: 5500,
    taxTotal: 550,
    total: 6050,
    notes: "Thank you for your business!",
    terms: "Payment due within 30 days",
    createdBy: "Jane Smith",
    createdAt: "2023-04-05T09:45:00Z",
    updatedAt: "2023-04-05T09:45:00Z",
  },
  {
    id: "inv-004",
    invoiceNumber: "INV-2023-004",
    customerId: "cust-004",
    customerName: "Retail Solutions Ltd.",
    status: "draft",
    issueDate: "2023-04-10",
    dueDate: "2023-05-10",
    items: [
      {
        id: "item-006",
        description: "POS System Implementation",
        quantity: 1,
        unitPrice: 7500,
        taxRate: 10,
        total: 8250,
      },
    ],
    subtotal: 7500,
    taxTotal: 750,
    total: 8250,
    terms: "Payment due within 30 days",
    createdBy: "John Doe",
    createdAt: "2023-04-10T16:20:00Z",
    updatedAt: "2023-04-10T16:20:00Z",
  },
  {
    id: "inv-005",
    invoiceNumber: "INV-2023-005",
    customerId: "cust-001",
    customerName: "Acme Corporation",
    status: "sent",
    issueDate: "2023-04-15",
    dueDate: "2023-05-15",
    items: [
      {
        id: "item-007",
        description: "Maintenance Services",
        quantity: 10,
        unitPrice: 120,
        taxRate: 10,
        total: 1320,
      },
    ],
    subtotal: 1200,
    taxTotal: 120,
    total: 1320,
    notes: "Monthly maintenance as per contract",
    terms: "Payment due within 30 days",
    createdBy: "Jane Smith",
    createdAt: "2023-04-15T11:10:00Z",
    updatedAt: "2023-04-15T11:10:00Z",
  },
  {
    id: "inv-006",
    invoiceNumber: "INV-2023-006",
    customerId: "cust-005",
    customerName: "Innovative Solutions",
    status: "paid",
    issueDate: "2023-03-01",
    dueDate: "2023-03-31",
    items: [
      {
        id: "item-008",
        description: "Software License (Annual)",
        quantity: 5,
        unitPrice: 1200,
        taxRate: 10,
        total: 6600,
      },
    ],
    subtotal: 6000,
    taxTotal: 600,
    total: 6600,
    notes: "Thank you for your business!",
    terms: "Payment due within 30 days",
    createdBy: "John Doe",
    createdAt: "2023-03-01T13:25:00Z",
    updatedAt: "2023-03-01T13:25:00Z",
    paidDate: "2023-03-25",
    paidAmount: 6600,
  },
]

export const invoiceStats: InvoiceStats = {
  totalOutstanding: 19110,
  overdue: 3740,
  paid: 12760,
  draft: 8250,
  averageDaysToPayment: 22,
}

import type { AttendanceRecord } from "@/types/attendance"

export const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: "att-001",
    employee: {
      id: "emp-001",
      name: "<PERSON>",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-01T00:00:00Z",
    checkIn: {
      time: "09:00 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:30 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8.5,
    notes: "",
  },
  {
    id: "att-002",
    employee: {
      id: "emp-002",
      name: "<PERSON>",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-01T00:00:00Z",
    checkIn: {
      time: "09:15 AM",
      status: "late",
    },
    checkOut: {
      time: "06:00 PM",
      status: "on-time",
    },
    status: "late",
    hoursWorked: 8.75,
    notes: "Traffic delay",
  },
  {
    id: "att-003",
    employee: {
      id: "emp-003",
      name: "<PERSON>",
      department: "Marketing",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-01T00:00:00Z",
    checkIn: {
      time: "08:45 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:15 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8.5,
    notes: "",
  },
  {
    id: "att-004",
    employee: {
      id: "emp-004",
      name: "Emily Rodriguez",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-01T00:00:00Z",
    checkIn: {
      time: "09:00 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:00 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8,
    notes: "",
  },
  {
    id: "att-005",
    employee: {
      id: "emp-005",
      name: "David Kim",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-01T00:00:00Z",
    status: "absent",
    notes: "Sick leave",
  },
  {
    id: "att-006",
    employee: {
      id: "emp-001",
      name: "John Doe",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-02T00:00:00Z",
    checkIn: {
      time: "08:55 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:15 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8.33,
    notes: "",
  },
  {
    id: "att-007",
    employee: {
      id: "emp-002",
      name: "Sarah Johnson",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-02T00:00:00Z",
    checkIn: {
      time: "09:00 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:30 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8.5,
    notes: "",
  },
  {
    id: "att-008",
    employee: {
      id: "emp-003",
      name: "Michael Chen",
      department: "Marketing",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-02T00:00:00Z",
    checkIn: {
      time: "09:30 AM",
      status: "late",
    },
    checkOut: {
      time: "06:00 PM",
      status: "on-time",
    },
    status: "late",
    hoursWorked: 8.5,
    notes: "Doctor's appointment",
  },
  {
    id: "att-009",
    employee: {
      id: "emp-004",
      name: "Emily Rodriguez",
      department: "Human Resources",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-02T00:00:00Z",
    checkIn: {
      time: "08:45 AM",
      status: "on-time",
    },
    checkOut: {
      time: "05:00 PM",
      status: "on-time",
    },
    status: "present",
    hoursWorked: 8.25,
    notes: "",
  },
  {
    id: "att-010",
    employee: {
      id: "emp-005",
      name: "David Kim",
      department: "Engineering",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-05-02T00:00:00Z",
    status: "absent",
    notes: "Sick leave",
  },
]

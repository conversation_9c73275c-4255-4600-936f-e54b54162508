export type Contact = {
  id: string
  name: string
  company: string
  title: string
  email: string
  phone: string
  customerId: string
}

export const mockContacts: Contact[] = [
  {
    id: "contact-001",
    name: "<PERSON>",
    company: "Acme Corporation",
    title: "CEO",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-001"
  },
  {
    id: "contact-002",
    name: "<PERSON>",
    company: "Acme Corporation",
    title: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-001"
  },
  {
    id: "contact-003",
    name: "<PERSON>",
    company: "TechNova Solutions",
    title: "CTO",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-002"
  },
  {
    id: "contact-004",
    name: "<PERSON>",
    company: "TechNova Solutions",
    title: "COO",
    email: "emily.<PERSON><PERSON><PERSON><PERSON>@technova.com",
    phone: "+****************",
    customerId: "cust-002"
  },
  {
    id: "contact-005",
    name: "<PERSON>",
    company: "Global Logistics Inc.",
    title: "Director of Operations",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-003"
  },
  {
    id: "contact-006",
    name: "Lisa Wong",
    company: "Bright Future Education",
    title: "Principal",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-004"
  },
  {
    id: "contact-007",
    name: "Robert Taylor",
    company: "HealthPlus Medical",
    title: "Medical Director",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-005"
  },
  {
    id: "contact-008",
    name: "Jennifer Garcia",
    company: "GreenEarth Renewables",
    title: "Sustainability Manager",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-006"
  },
  {
    id: "contact-009",
    name: "Thomas Brown",
    company: "Luxury Retreats Hotels",
    title: "General Manager",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-007"
  },
  {
    id: "contact-010",
    name: "Amanda Wilson",
    company: "Financial Fortress",
    title: "Investment Advisor",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-008"
  },
  {
    id: "contact-011",
    name: "Daniel Martinez",
    company: "Creative Minds Agency",
    title: "Creative Director",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-009"
  },
  {
    id: "contact-012",
    name: "Sophia Lee",
    company: "FreshFoods Grocery",
    title: "Purchasing Manager",
    email: "<EMAIL>",
    phone: "+****************",
    customerId: "cust-010"
  }
]

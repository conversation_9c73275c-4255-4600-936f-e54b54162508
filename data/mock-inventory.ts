import type { InventoryItem, InventoryCategory, EquipmentAssignment, InventoryStats } from "@/types/inventory"

export const inventoryStats: InventoryStats = {
  totalItems: 1248,
  totalItemsChange: 5.2,
  lowStock: 42,
  lowStockItems: 8,
  availableItems: 1156,
  availablePercentage: 92,
  pendingOrders: 12,
  pendingOrdersValue: "$24,500",
}

export const inventoryItems: InventoryItem[] = [
  {
    id: "inv-001",
    name: "Dell XPS 15 Laptop",
    sku: "COMP-LAP-001",
    category: "Electronics",
    quantity: 24,
    status: "in-stock",
    value: 1499.99,
    location: "Main Office - Storage Room A",
    lastUpdated: "2023-04-15",
    supplier: "Dell Inc.",
    minimumStock: 5,
    source: "external",
  },
  {
    id: "inv-002",
    name: "<PERSON> Aeron Chair",
    sku: "FURN-CHR-002",
    category: "Furniture",
    quantity: 32,
    status: "in-stock",
    value: 1095.0,
    location: "Warehouse B - Section 3",
    lastUpdated: "2023-03-22",
    supplier: "Office Supplies Co.",
    minimumStock: 10,
    source: "external",
  },
  {
    id: "inv-003",
    name: "iPhone 14 Pro",
    sku: "COMP-PHN-003",
    category: "Electronics",
    quantity: 3,
    status: "low-stock",
    value: 999.0,
    location: "IT Department - Secure Cabinet",
    lastUpdated: "2023-04-18",
    supplier: "Apple Inc.",
    minimumStock: 5,
    source: "external",
  },
  {
    id: "inv-004",
    name: "Logitech MX Master Mouse",
    sku: "COMP-ACC-004",
    category: "Accessories",
    quantity: 45,
    status: "in-stock",
    value: 99.99,
    location: "IT Department - Storage Room",
    lastUpdated: "2023-04-10",
    supplier: "Logitech",
    minimumStock: 15,
    source: "external",
  },
  {
    id: "inv-005",
    name: "HP LaserJet Pro Printer",
    sku: "COMP-PRN-005",
    category: "Electronics",
    quantity: 0,
    status: "out-of-stock",
    value: 349.99,
    location: "Main Office - Print Room",
    lastUpdated: "2023-04-05",
    supplier: "HP Inc.",
    minimumStock: 2,
    source: "external",
  },
  {
    id: "inv-006",
    name: "Standing Desk Converter",
    sku: "FURN-DSK-006",
    category: "Furniture",
    quantity: 8,
    status: "on-order",
    value: 299.99,
    location: "Warehouse A - Section 2",
    lastUpdated: "2023-04-12",
    supplier: "Ergonomic Solutions Inc.",
    minimumStock: 5,
    source: "external",
  },
  {
    id: "inv-007",
    name: "Conference Room Table",
    sku: "FURN-TBL-007",
    category: "Furniture",
    quantity: 4,
    status: "in-stock",
    value: 1200.0,
    location: "Warehouse B - Section 1",
    lastUpdated: "2023-03-15",
    supplier: "Office Furniture Co.",
    minimumStock: 2,
    source: "external",
  },
  {
    id: "inv-008",
    name: "Whiteboard - Large",
    sku: "OFF-WB-008",
    category: "Office Supplies",
    quantity: 12,
    status: "in-stock",
    value: 149.99,
    location: "Warehouse A - Section 4",
    lastUpdated: "2023-04-02",
    supplier: "Office Depot",
    minimumStock: 5,
    source: "external",
  },
  {
    id: "inv-009",
    name: "Custom Desk Organizer",
    sku: "PROD-ORG-001",
    category: "Office Supplies",
    quantity: 35,
    status: "in-stock",
    value: 45.99,
    location: "Production Facility - Finished Goods",
    lastUpdated: "2023-04-20",
    minimumStock: 10,
    source: "internal",
    productionBatchId: "batch-001",
    costPerUnit: 22.5,
  },
  {
    id: "inv-010",
    name: "Ergonomic Keyboard Rest",
    sku: "PROD-ERG-002",
    category: "Accessories",
    quantity: 28,
    status: "in-stock",
    value: 29.99,
    location: "Production Facility - Finished Goods",
    lastUpdated: "2023-04-18",
    minimumStock: 15,
    source: "internal",
    productionBatchId: "batch-003",
    costPerUnit: 12.75,
  },
  {
    id: "inv-011",
    name: "Custom Laptop Stand",
    sku: "PROD-STD-003",
    category: "Accessories",
    quantity: 5,
    status: "low-stock",
    value: 79.99,
    location: "Production Facility - Finished Goods",
    lastUpdated: "2023-04-15",
    minimumStock: 10,
    source: "internal",
    productionBatchId: "batch-005",
    costPerUnit: 35.25,
  },
]

export const inventoryCategories: InventoryCategory[] = [
  {
    id: "cat-001",
    name: "Electronics",
    itemCount: 452,
    totalValue: 524500.0,
    stockPercentage: 88,
    description: "Computers, phones, printers, and other electronic devices",
  },
  {
    id: "cat-002",
    name: "Furniture",
    itemCount: 325,
    totalValue: 378250.0,
    stockPercentage: 95,
    description: "Office furniture including desks, chairs, and storage",
  },
  {
    id: "cat-003",
    name: "Office Supplies",
    itemCount: 278,
    totalValue: 42350.0,
    stockPercentage: 76,
    description: "General office supplies and stationery",
  },
  {
    id: "cat-004",
    name: "Accessories",
    itemCount: 193,
    totalValue: 28750.0,
    stockPercentage: 92,
    description: "Computer and phone accessories",
  },
]

export const equipmentAssignments: EquipmentAssignment[] = [
  {
    id: "assign-001",
    itemId: "inv-001",
    itemName: "Dell XPS 15 Laptop",
    employeeId: "emp-001",
    employeeName: "John Smith",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "Engineering",
    assignedDate: "2023-02-15",
    status: "active",
    notes: "Primary work laptop",
  },
  {
    id: "assign-002",
    itemId: "inv-003",
    itemName: "iPhone 14 Pro",
    employeeId: "emp-002",
    employeeName: "Sarah Johnson",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "Sales",
    assignedDate: "2023-03-10",
    status: "active",
    notes: "Company phone for client communications",
  },
  {
    id: "assign-003",
    itemId: "inv-002",
    itemName: "Herman Miller Aeron Chair",
    employeeId: "emp-003",
    employeeName: "Michael Brown",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "Design",
    assignedDate: "2023-01-20",
    status: "active",
  },
  {
    id: "assign-004",
    itemId: "inv-004",
    itemName: "Logitech MX Master Mouse",
    employeeId: "emp-004",
    employeeName: "Emily Davis",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "Marketing",
    assignedDate: "2023-03-05",
    returnDate: "2023-04-10",
    status: "returned",
    notes: "Returned due to employee preference for trackpad",
  },
  {
    id: "assign-005",
    itemId: "inv-006",
    itemName: "Standing Desk Converter",
    employeeId: "emp-005",
    employeeName: "David Wilson",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "HR",
    assignedDate: "2023-02-28",
    status: "active",
  },
  {
    id: "assign-006",
    itemId: "inv-001",
    itemName: "Dell XPS 15 Laptop",
    employeeId: "emp-006",
    employeeName: "Jessica Martinez",
    employeeAvatar: "/placeholder.svg?height=32&width=32",
    department: "Finance",
    assignedDate: "2023-01-15",
    returnDate: "2023-04-15",
    status: "overdue",
    notes: "Employee on extended leave, equipment not returned",
  },
]

// components/procurement/requisition-detail.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Download,
  Trash2,
  AlertTriangle,
  User,
  Building,
  Calendar,
  DollarSign,
  Package,
  MessageSquare
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { toast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"

// Requisition interfaces
interface RequisitionItem {
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  estimatedUnitPrice: number;
  estimatedTotal: number;
  category?: string;
  specifications?: string;
}

interface Requisition {
  _id: string;
  requisitionNumber: string;
  title: string;
  description: string;
  justification?: string;
  departmentId: {
    _id: string;
    name: string;
  } | null;
  requestedBy: {
    _id: string;
    name: string;
    email: string;
  } | null;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'ordered' | 'completed' | 'cancelled';
  priority: 'high' | 'medium' | 'low' | 'urgent';
  items: RequisitionItem[];
  totalAmount: number;
  urgentDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  approvedBy?: {
    _id: string;
    name: string;
  } | null;
  approvalDate?: string;
  rejectionReason?: string;
}

interface RequisitionDetailProps {
  requisitionId: string;
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    draft: { variant: "outline" as const, label: "Draft", icon: FileText, color: "text-gray-600" },
    submitted: { variant: "secondary" as const, label: "Submitted", icon: Clock, color: "text-blue-600" },
    approved: { variant: "default" as const, label: "Approved", icon: CheckCircle, color: "text-green-600" },
    rejected: { variant: "destructive" as const, label: "Rejected", icon: XCircle, color: "text-red-600" },
    ordered: { variant: "default" as const, label: "Ordered", icon: Package, color: "text-purple-600" },
    completed: { variant: "default" as const, label: "Completed", icon: CheckCircle, color: "text-green-700" },
    cancelled: { variant: "destructive" as const, label: "Cancelled", icon: XCircle, color: "text-red-700" }
  }

  const config = statusConfig[status as keyof typeof statusConfig] || {
    variant: "outline" as const,
    label: status,
    icon: FileText,
    color: "text-gray-600"
  }
  
  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const getPriorityBadge = (priority: string) => {
  const priorityConfig = {
    urgent: { variant: "destructive" as const, label: "Urgent Priority", color: "text-red-600" },
    high: { variant: "destructive" as const, label: "High Priority", color: "text-orange-600" },
    medium: { variant: "secondary" as const, label: "Medium Priority", color: "text-yellow-600" },
    low: { variant: "outline" as const, label: "Low Priority", color: "text-green-600" }
  }
  
  const config = priorityConfig[priority as keyof typeof priorityConfig] || { 
    variant: "outline" as const, 
    label: priority,
    color: "text-gray-600"
  }
  return <Badge variant={config.variant}>{config.label}</Badge>
}

export function RequisitionDetail({ requisitionId }: RequisitionDetailProps) {
  const router = useRouter()
  const [requisition, setRequisition] = useState<Requisition | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch requisition details
  const fetchRequisition = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/procurement/requisition/${requisitionId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch requisition');
      }
      const result = await response.json();
      setRequisition(result.data);
    } catch (error) {
      console.error('Error fetching requisition:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch requisition details',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRequisition();
  }, [requisitionId]);

  const handleBack = () => {
    router.push('/dashboard/procurement/requisitions');
  };

  const handleEdit = () => {
    router.push(`/dashboard/procurement/requisitions/${requisitionId}/edit`);
  };

  const handleApprove = async () => {
    try {
      const response = await fetch(`/api/procurement/requisition/${requisitionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvalDate: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition approved successfully',
      });

      fetchRequisition();
    } catch (error) {
      console.error('Error approving requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to approve requisition',
        variant: 'destructive',
      });
    }
  };

  const handleReject = async () => {
    const reason = prompt('Please provide a reason for rejecting this requisition:');
    if (!reason || reason.trim().length < 10) {
      toast({
        title: 'Rejection Cancelled',
        description: 'A valid reason is required to reject a requisition',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/procurement/requisition/${requisitionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          rejectionReason: reason.trim()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition rejected successfully',
      });

      fetchRequisition();
    } catch (error) {
      console.error('Error rejecting requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reject requisition',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!requisition) return;

    // Check if requisition can be deleted
    if (['approved', 'completed'].includes(requisition.status)) {
      toast({
        title: 'Cannot Delete',
        description: `Cannot delete requisition with status: ${requisition.status}`,
        variant: 'destructive',
      });
      return;
    }

    const reason = prompt(`Please provide a reason for deleting requisition "${requisition.requisitionNumber}":`);
    if (!reason || reason.trim().length < 10) {
      toast({
        title: 'Deletion Cancelled',
        description: 'A valid reason (minimum 10 characters) is required to delete a requisition',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/procurement/requisition/${requisitionId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deletionReason: reason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition deleted successfully with audit trail',
      });

      router.push('/dashboard/procurement/requisitions');
    } catch (error) {
      console.error('Error deleting requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete requisition',
        variant: 'destructive',
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Requisition Details"
          text="Loading requisition information..."
        >
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Requisitions
          </Button>
        </DashboardHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
        </div>
      </DashboardShell>
    );
  }

  if (!requisition) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Requisition Not Found"
          text="The requested requisition could not be found."
        >
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Requisitions
          </Button>
        </DashboardHeader>
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading={`Requisition ${requisition.requisitionNumber}`}
        text={requisition.title}
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          
          {requisition.status === 'draft' && (
            <Button variant="outline" onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
          
          {requisition.status === 'submitted' && (
            <>
              <Button variant="outline" onClick={handleReject} className="text-red-600 hover:text-red-700">
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700">
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
            </>
          )}
          
          {['draft', 'rejected'].includes(requisition.status) && (
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}
          
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Status and Priority */}
        <div className="flex items-center gap-4">
          {getStatusBadge(requisition.status)}
          {getPriorityBadge(requisition.priority)}
          {requisition.urgentDate && (
            <Badge variant="outline" className="text-orange-600">
              <AlertTriangle className="mr-1 h-3 w-3" />
              Urgent by {formatDate(requisition.urgentDate)}
            </Badge>
          )}
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <User className="h-4 w-4" />
                  Requested By
                </div>
                <div>
                  <div className="font-medium">{requisition.requestedBy?.name || 'N/A'}</div>
                  <div className="text-sm text-muted-foreground">{requisition.requestedBy?.email || 'N/A'}</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Building className="h-4 w-4" />
                  Department
                </div>
                <div className="font-medium">{requisition.departmentId?.name || 'N/A'}</div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  Created Date
                </div>
                <div className="font-medium">{formatDate(requisition.createdAt)}</div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <DollarSign className="h-4 w-4" />
                  Total Amount
                </div>
                <div className="font-medium text-lg">{formatCurrency(requisition.totalAmount)}</div>
              </div>
              
              {requisition.approvedBy && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <CheckCircle className="h-4 w-4" />
                    Approved By
                  </div>
                  <div>
                    <div className="font-medium">{requisition.approvedBy.name}</div>
                    {requisition.approvalDate && (
                      <div className="text-sm text-muted-foreground">{formatDate(requisition.approvalDate)}</div>
                    )}
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  Last Updated
                </div>
                <div className="font-medium">{formatDate(requisition.updatedAt)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Description and Justification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Description & Justification
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-muted-foreground">{requisition.description || 'No description provided'}</p>
            </div>
            
            {requisition.justification && (
              <div>
                <h4 className="font-medium mb-2">Justification</h4>
                <p className="text-muted-foreground">{requisition.justification}</p>
              </div>
            )}
            
            {requisition.notes && (
              <div>
                <h4 className="font-medium mb-2">Additional Notes</h4>
                <p className="text-muted-foreground">{requisition.notes}</p>
              </div>
            )}
            
            {requisition.rejectionReason && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium mb-2 text-red-800">Rejection Reason</h4>
                <p className="text-red-700">{requisition.rejectionReason}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Requisition Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Requisition Items ({requisition.items?.length || 0})
            </CardTitle>
            <CardDescription>
              Detailed breakdown of requested items
            </CardDescription>
          </CardHeader>
          <CardContent>
            {requisition.items && requisition.items.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Category</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {requisition.items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell className="max-w-[200px]">
                          <div className="truncate" title={item.description}>
                            {item.description || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.unit}</TableCell>
                        <TableCell>{formatCurrency(item.estimatedUnitPrice || 0)}</TableCell>
                        <TableCell className="font-medium">{formatCurrency(item.estimatedTotal || 0)}</TableCell>
                        <TableCell>
                          {item.category && (
                            <Badge variant="outline">{item.category}</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                <Separator />

                <div className="p-4 bg-muted/50">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Total Amount:</span>
                    <span className="text-2xl font-bold text-primary">
                      {formatCurrency(requisition.totalAmount)}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>No items found in this requisition</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Item Specifications */}
        {requisition.items && requisition.items.some(item => item.specifications) && (
          <Card>
            <CardHeader>
              <CardTitle>Item Specifications</CardTitle>
              <CardDescription>
                Detailed specifications for requested items
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {requisition.items
                  .filter(item => item.specifications)
                  .map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">{item.name}</h4>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                        {item.specifications}
                      </p>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Timeline</CardTitle>
            <CardDescription>
              Track the progress and changes of this requisition
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Requisition Created</span>
                    <Badge variant="outline">Draft</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Created by {requisition.requestedBy?.name || 'Unknown'} on {formatDate(requisition.createdAt)}
                  </p>
                </div>
              </div>

              {requisition.status !== 'draft' && (
                <div className="flex items-start gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
                    <Clock className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Requisition Submitted</span>
                      <Badge variant="secondary">Submitted</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Submitted for approval
                    </p>
                  </div>
                </div>
              )}

              {requisition.status === 'approved' && requisition.approvedBy && (
                <div className="flex items-start gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Requisition Approved</span>
                      <Badge variant="default">Approved</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Approved by {requisition.approvedBy.name} on {requisition.approvalDate ? formatDate(requisition.approvalDate) : 'Unknown date'}
                    </p>
                  </div>
                </div>
              )}

              {requisition.status === 'rejected' && (
                <div className="flex items-start gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                    <XCircle className="h-4 w-4 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Requisition Rejected</span>
                      <Badge variant="destructive">Rejected</Badge>
                    </div>
                    {requisition.rejectionReason && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Reason: {requisition.rejectionReason}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {['ordered', 'completed'].includes(requisition.status) && (
                <div className="flex items-start gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100">
                    <Package className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {requisition.status === 'ordered' ? 'Purchase Order Created' : 'Requisition Completed'}
                      </span>
                      <Badge variant="default">
                        {requisition.status === 'ordered' ? 'Ordered' : 'Completed'}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {requisition.status === 'ordered'
                        ? 'Purchase order has been created and sent to suppliers'
                        : 'All items have been received and requisition is complete'
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

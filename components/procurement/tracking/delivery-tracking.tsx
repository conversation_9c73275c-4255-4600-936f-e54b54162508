"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import {
  Search,
  Truck,
  Package,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Eye,
  Calendar,
  Building,
  Phone
} from "lucide-react"

// Tracking event interface
interface TrackingEvent {
  _id: string
  timestamp: Date
  location: string
  status: string
  description: string
  carrier?: string
}

// Delivery tracking interface
interface DeliveryTracking {
  _id: string
  deliveryNumber: string
  trackingNumber: string
  carrierName: string
  status: 'scheduled' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception' | 'cancelled'
  estimatedDelivery: Date
  actualDelivery?: Date
  origin: {
    address: string
    city: string
    country: string
  }
  destination: {
    address: string
    city: string
    country: string
    contactPerson?: string
    contactPhone?: string
  }
  events: TrackingEvent[]
  lastUpdated: Date
}

interface DeliveryTrackingProps {
  onTrackingSearch?: (trackingNumber: string) => Promise<DeliveryTracking | null>
  initialTrackingNumber?: string
}

// Status configuration
const statusConfig = {
  scheduled: { 
    label: 'Scheduled', 
    color: 'bg-blue-100 text-blue-800',
    icon: Calendar,
    description: 'Delivery has been scheduled'
  },
  picked_up: { 
    label: 'Picked Up', 
    color: 'bg-purple-100 text-purple-800',
    icon: Package,
    description: 'Package has been picked up by carrier'
  },
  in_transit: { 
    label: 'In Transit', 
    color: 'bg-yellow-100 text-yellow-800',
    icon: Truck,
    description: 'Package is on the way'
  },
  out_for_delivery: { 
    label: 'Out for Delivery', 
    color: 'bg-orange-100 text-orange-800',
    icon: Truck,
    description: 'Package is out for delivery'
  },
  delivered: { 
    label: 'Delivered', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
    description: 'Package has been delivered'
  },
  exception: { 
    label: 'Exception', 
    color: 'bg-red-100 text-red-800',
    icon: AlertTriangle,
    description: 'Delivery exception occurred'
  },
  cancelled: { 
    label: 'Cancelled', 
    color: 'bg-gray-100 text-gray-800',
    icon: XCircle,
    description: 'Delivery has been cancelled'
  },
}

export function DeliveryTracking({ 
  onTrackingSearch,
  initialTrackingNumber = ""
}: DeliveryTrackingProps) {
  const [trackingNumber, setTrackingNumber] = useState(initialTrackingNumber)
  const [searchInput, setSearchInput] = useState(initialTrackingNumber)
  const [trackingData, setTrackingData] = useState<DeliveryTracking | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Auto-search if initial tracking number is provided
  useEffect(() => {
    if (initialTrackingNumber && onTrackingSearch) {
      handleSearch(initialTrackingNumber)
    }
  }, [initialTrackingNumber])

  // Handle tracking search
  const handleSearch = async (searchTerm?: string) => {
    const numberToSearch = searchTerm || searchInput
    if (!numberToSearch.trim()) {
      toast({
        title: "Error",
        description: "Please enter a tracking number",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      if (onTrackingSearch) {
        const result = await onTrackingSearch(numberToSearch)
        if (result) {
          setTrackingData(result)
          setTrackingNumber(numberToSearch)
        } else {
          setError("Tracking information not found")
          setTrackingData(null)
        }
      } else {
        // Mock data for demonstration
        const mockData: DeliveryTracking = {
          _id: "mock-id",
          deliveryNumber: "DEL-2024-001",
          trackingNumber: numberToSearch,
          carrierName: "Express Logistics",
          status: 'in_transit',
          estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          origin: {
            address: "123 Supplier Street",
            city: "Lilongwe",
            country: "Malawi"
          },
          destination: {
            address: "456 Company Avenue",
            city: "Blantyre",
            country: "Malawi",
            contactPerson: "John Doe",
            contactPhone: "+*********** 456"
          },
          events: [
            {
              _id: "1",
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
              location: "Lilongwe, Malawi",
              status: "picked_up",
              description: "Package picked up from sender",
              carrier: "Express Logistics"
            },
            {
              _id: "2",
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              location: "Lilongwe Hub, Malawi",
              status: "in_transit",
              description: "Package processed at sorting facility",
              carrier: "Express Logistics"
            },
            {
              _id: "3",
              timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
              location: "Blantyre Hub, Malawi",
              status: "in_transit",
              description: "Package arrived at destination facility",
              carrier: "Express Logistics"
            }
          ],
          lastUpdated: new Date()
        }
        setTrackingData(mockData)
        setTrackingNumber(numberToSearch)
      }
    } catch (err) {
      setError("Failed to fetch tracking information")
      setTrackingData(null)
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh tracking data
  const handleRefresh = () => {
    if (trackingNumber) {
      handleSearch(trackingNumber)
    }
  }

  // Calculate progress percentage
  const getProgressPercentage = (status: string) => {
    const statusOrder = ['scheduled', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered']
    const currentIndex = statusOrder.indexOf(status)
    return currentIndex >= 0 ? ((currentIndex + 1) / statusOrder.length) * 100 : 0
  }

  const statusInfo = trackingData ? statusConfig[trackingData.status] : null
  const StatusIcon = statusInfo?.icon

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Delivery Tracking</h2>
          <p className="text-muted-foreground">
            Track your packages in real-time
          </p>
        </div>
      </div>

      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle>Track Your Package</CardTitle>
          <CardDescription>
            Enter your tracking number to get real-time delivery updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="Enter tracking number (e.g., TRK123456789)"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={() => handleSearch()} disabled={isLoading}>
              {isLoading ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Search className="mr-2 h-4 w-4" />
              )}
              {isLoading ? 'Searching...' : 'Track'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Tracking Not Found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {error}
            </p>
            <Button variant="outline" onClick={() => setError(null)}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Tracking Results */}
      {trackingData && !error && (
        <div className="space-y-6">
          {/* Status Overview */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  {StatusIcon && <StatusIcon className="h-5 w-5" />}
                  Tracking #{trackingData.trackingNumber}
                </CardTitle>
                <CardDescription>
                  Delivery #{trackingData.deliveryNumber} • {trackingData.carrierName}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={statusInfo?.color}>
                  {statusInfo?.label}
                </Badge>
                <Button variant="ghost" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Delivery Progress</p>
                  <Progress value={getProgressPercentage(trackingData.status)} className="h-3" />
                  <p className="text-sm text-muted-foreground mt-1">
                    {statusInfo?.description}
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Estimated Delivery</label>
                    <p className="text-sm mt-1">
                      {format(new Date(trackingData.estimatedDelivery), "PPP 'at' p")}
                    </p>
                  </div>
                  {trackingData.actualDelivery && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Actual Delivery</label>
                      <p className="text-sm mt-1">
                        {format(new Date(trackingData.actualDelivery), "PPP 'at' p")}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Route */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Origin */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Origin
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm">{trackingData.origin.address}</p>
                    <p className="text-sm text-muted-foreground">
                      {trackingData.origin.city}, {trackingData.origin.country}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Destination */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Destination
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                    <div>
                      <p className="text-sm">{trackingData.destination.address}</p>
                      <p className="text-sm text-muted-foreground">
                        {trackingData.destination.city}, {trackingData.destination.country}
                      </p>
                    </div>
                  </div>
                  
                  {trackingData.destination.contactPerson && (
                    <>
                      <Separator />
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{trackingData.destination.contactPerson}</span>
                        </div>
                        {trackingData.destination.contactPhone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{trackingData.destination.contactPhone}</span>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tracking Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Tracking Timeline</CardTitle>
              <CardDescription>
                Detailed tracking history for your package
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trackingData.events
                  .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                  .map((event, index) => {
                    const isLatest = index === 0
                    return (
                      <div key={event._id} className="flex items-start gap-3">
                        <div className={`w-3 h-3 rounded-full mt-1 ${
                          isLatest ? 'bg-primary' : 'bg-muted-foreground'
                        }`}></div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className={`font-medium ${isLatest ? 'text-primary' : ''}`}>
                                {event.description}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {event.location}
                                {event.carrier && ` • ${event.carrier}`}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">
                                {format(new Date(event.timestamp), "MMM dd")}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {format(new Date(event.timestamp), "HH:mm")}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
              </div>
              
              <div className="mt-6 pt-4 border-t">
                <p className="text-xs text-muted-foreground">
                  Last updated: {format(new Date(trackingData.lastUpdated), "PPP 'at' p")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Empty State */}
      {!trackingData && !error && !isLoading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Track Your Delivery</h3>
            <p className="text-muted-foreground text-center">
              Enter a tracking number above to get real-time updates on your package delivery.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

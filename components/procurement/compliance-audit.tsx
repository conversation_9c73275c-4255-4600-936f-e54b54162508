// components/procurement/compliance-audit.tsx
"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  FileText,
  Scale,
  Eye,
  Download
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

// Mock compliance data
const complianceMetrics = {
  overallScore: 94,
  totalChecks: 25,
  passedChecks: 23,
  failedChecks: 2,
  lastAuditDate: "2024-01-10"
}

const complianceChecks = [
  {
    id: "COMP-001",
    category: "Supplier Verification",
    description: "All suppliers have valid business licenses",
    status: "passed",
    lastCheck: "2024-01-15",
    score: 100
  },
  {
    id: "COMP-002",
    category: "Purchase Authorization",
    description: "All purchases above threshold properly authorized",
    status: "passed",
    lastCheck: "2024-01-14",
    score: 98
  },
  {
    id: "COMP-003",
    category: "Documentation",
    description: "Complete documentation for all transactions",
    status: "failed",
    lastCheck: "2024-01-13",
    score: 85
  },
  {
    id: "COMP-004",
    category: "Competitive Bidding",
    description: "Proper competitive bidding process followed",
    status: "passed",
    lastCheck: "2024-01-12",
    score: 95
  },
  {
    id: "COMP-005",
    category: "Contract Management",
    description: "All contracts properly executed and stored",
    status: "failed",
    lastCheck: "2024-01-11",
    score: 78
  }
]

const auditTrail = [
  {
    id: "AUD-001",
    action: "Purchase Order Created",
    user: "John Banda",
    timestamp: "2024-01-15 10:30:00",
    details: "PO-2024-001 created for Office Supplies Ltd",
    riskLevel: "low"
  },
  {
    id: "AUD-002",
    action: "Supplier Added",
    user: "Mary Phiri",
    timestamp: "2024-01-14 14:20:00",
    details: "New supplier Tech Solutions Inc added to system",
    riskLevel: "medium"
  },
  {
    id: "AUD-003",
    action: "Contract Approved",
    user: "Peter Mwale",
    timestamp: "2024-01-13 09:15:00",
    details: "Annual contract CON-2024-001 approved",
    riskLevel: "high"
  }
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    passed: { variant: "default" as const, label: "Passed", icon: CheckCircle },
    failed: { variant: "destructive" as const, label: "Failed", icon: AlertTriangle },
    pending: { variant: "secondary" as const, label: "Pending", icon: FileText }
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || { 
    variant: "outline" as const, 
    label: status,
    icon: FileText
  }
  
  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const getRiskBadge = (riskLevel: string) => {
  const riskConfig = {
    low: { variant: "default" as const, label: "Low Risk" },
    medium: { variant: "secondary" as const, label: "Medium Risk" },
    high: { variant: "destructive" as const, label: "High Risk" }
  }
  
  const config = riskConfig[riskLevel as keyof typeof riskConfig] || { 
    variant: "outline" as const, 
    label: riskLevel 
  }
  return <Badge variant={config.variant}>{config.label}</Badge>
}

export function ComplianceAudit() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Compliance & Audit"
        text="Monitor procurement compliance and audit trails"
      >
        <div className="flex gap-2">
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Audit Log
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Compliance Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{complianceMetrics.overallScore}%</div>
              <Progress value={complianceMetrics.overallScore} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Passed Checks</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{complianceMetrics.passedChecks}</div>
              <p className="text-xs text-muted-foreground">
                of {complianceMetrics.totalChecks} total checks
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Checks</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{complianceMetrics.failedChecks}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Last Audit</CardTitle>
              <Scale className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{complianceMetrics.lastAuditDate}</div>
              <p className="text-xs text-muted-foreground">
                5 days ago
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Compliance Checks */}
        <Card>
          <CardHeader>
            <CardTitle>Compliance Checks</CardTitle>
            <CardDescription>
              Current status of compliance requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Check ID</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Last Check</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {complianceChecks.map((check) => (
                  <TableRow key={check.id}>
                    <TableCell className="font-medium">{check.id}</TableCell>
                    <TableCell>{check.category}</TableCell>
                    <TableCell>{check.description}</TableCell>
                    <TableCell>{getStatusBadge(check.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={check.score >= 90 ? "text-green-600" : check.score >= 80 ? "text-orange-600" : "text-red-600"}>
                          {check.score}%
                        </span>
                        <Progress value={check.score} className="w-16" />
                      </div>
                    </TableCell>
                    <TableCell>{check.lastCheck}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Audit Trail */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Audit Trail</CardTitle>
            <CardDescription>
              Recent procurement activities and changes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Audit ID</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditTrail.map((audit) => (
                  <TableRow key={audit.id}>
                    <TableCell className="font-medium">{audit.id}</TableCell>
                    <TableCell>{audit.action}</TableCell>
                    <TableCell>{audit.user}</TableCell>
                    <TableCell className="font-mono text-sm">{audit.timestamp}</TableCell>
                    <TableCell>{audit.details}</TableCell>
                    <TableCell>{getRiskBadge(audit.riskLevel)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

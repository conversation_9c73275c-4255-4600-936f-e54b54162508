'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { FileSpreadsheet, Download, Upload, CheckCircle2, AlertCircle, X, ShoppingCart } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface UploadResult {
  totalRows: number;
  successfulOrders: Array<{
    row: number;
    orderNumber: string;
    supplierName: string;
    total: number;
    status: string;
  }>;
  failedOrders: Array<{
    row: number;
    supplierName: string;
    error: string;
    data: any;
  }>;
  duplicateOrders: Array<{
    row: number;
    supplierName: string;
    existingOrderNumber: string;
    action: string;
  }>;
}

interface BulkPurchaseOrderUploadProps {
  onSuccess?: () => void;
}

export function BulkPurchaseOrderUpload({ onSuccess }: BulkPurchaseOrderUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [activeTab, setActiveTab] = useState("upload");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase();
      if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
        setUploadError('Please upload a CSV or Excel file');
        setFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      // Check file size (max 10MB for purchase orders)
      if (selectedFile.size > 10 * 1024 * 1024) {
        setUploadError('File size exceeds 10MB limit');
        setFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      setFile(selectedFile);
      setUploadError(null);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadError('Please select a file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 500);

      // Send the file to the server
      const response = await fetch('/api/procurement/purchase-orders/bulk-import', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload file');
      }

      const result = await response.json();
      setUploadProgress(100);
      setUploadResult(result.data);

      // Show success toast
      toast({
        title: "Upload Completed",
        description: `Processed ${result.data.totalRows} rows. ${result.data.successfulOrders.length} purchase orders imported successfully.`,
        variant: result.data.failedOrders.length > 0 ? "destructive" : "default",
      });

      // Call onSuccess callback if provided
      if (onSuccess && result.data.successfulOrders.length > 0) {
        onSuccess();
      }

      // Switch to results tab
      setActiveTab("results");
    } catch (error: unknown) {
      console.error('Error uploading file:', error);
      setUploadError(error instanceof Error ? error.message : 'An error occurred during upload');
      setUploadProgress(0);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownloadTemplate = () => {
    console.log('Downloading template...');

    // Download the template file from our API with cache-busting parameter
    const timestamp = new Date().getTime();
    window.location.href = `/api/procurement/purchase-orders/template?t=${timestamp}`;

    // Show success toast
    toast({
      title: "Template Downloaded",
      description: "The purchase order import template has been downloaded with sample data and instructions.",
      variant: "default"
    });
  };

  const resetUpload = () => {
    setFile(null);
    setUploadProgress(0);
    setUploadError(null);
    setUploadResult(null);
    setActiveTab("upload");
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upload">Upload File</TabsTrigger>
          <TabsTrigger value="template">Download Template</TabsTrigger>
          <TabsTrigger value="results" disabled={!uploadResult}>
            Results
          </TabsTrigger>
        </TabsList>

        <TabsContent value="template" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Download Template
              </CardTitle>
              <CardDescription>
                Download the purchase order import template with sample data and required format
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground space-y-2">
                <p>The template includes the following columns:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li><strong>supplierName</strong> - Supplier name (required, must match existing supplier)</li>
                  <li><strong>orderDate</strong> - Order date (required, format: YYYY-MM-DD)</li>
                  <li><strong>expectedDeliveryDate</strong> - Expected delivery date (optional)</li>
                  <li><strong>itemName</strong> - Item name (required)</li>
                  <li><strong>itemDescription</strong> - Item description (optional)</li>
                  <li><strong>quantity</strong> - Quantity (required, positive number)</li>
                  <li><strong>unit</strong> - Unit of measurement (default: pcs)</li>
                  <li><strong>unitPrice</strong> - Unit price in MWK (required)</li>
                  <li><strong>tax</strong> - Tax amount in MWK (optional)</li>
                  <li><strong>discount</strong> - Discount percentage (optional)</li>
                  <li><strong>paymentTerms</strong> - Payment terms (optional)</li>
                  <li><strong>shippingTerms</strong> - Shipping terms (optional)</li>
                  <li><strong>notes</strong> - Additional notes (optional)</li>
                  <li><strong>status</strong> - Order status (default: draft)</li>
                </ul>
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="font-medium text-blue-800">Import Logic:</p>
                  <p className="text-blue-700 text-sm">Items with the same supplier name and order date will be grouped into a single purchase order. Each row represents one line item.</p>
                </div>
              </div>

              <Button onClick={handleDownloadTemplate} className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download Purchase Order Template
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Purchase Orders
              </CardTitle>
              <CardDescription>
                Upload a CSV or Excel file containing purchase order data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-center p-4 border-2 border-dashed rounded-md">
                <div className="space-y-2 text-center">
                  <FileSpreadsheet className="mx-auto h-12 w-12 text-muted-foreground" />
                  <div className="text-sm">
                    <Label htmlFor="file-upload" className="relative cursor-pointer rounded-md font-medium text-primary">
                      <span>Upload a file</span>
                      <Input
                        id="file-upload"
                        ref={fileInputRef}
                        type="file"
                        className="sr-only"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileChange}
                        disabled={isUploading}
                      />
                    </Label>
                    <p className="text-xs text-muted-foreground">CSV or Excel files up to 10MB</p>
                  </div>
                </div>
              </div>

              {file && (
                <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    <span className="text-sm font-medium">{file.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setFile(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {uploadProgress > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Upload Progress</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              {uploadError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Upload Failed</AlertTitle>
                  <AlertDescription>{uploadError}</AlertDescription>
                </Alert>
              )}

              <div className="flex gap-2">
                <Button
                  onClick={handleUpload}
                  disabled={!file || isUploading}
                  className="flex-1"
                >
                  {isUploading ? 'Uploading...' : 'Upload Purchase Orders'}
                </Button>
                <Button
                  variant="outline"
                  onClick={resetUpload}
                  disabled={isUploading}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {uploadResult && (
            <>
              {/* Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    Import Results
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{uploadResult.totalRows}</div>
                      <div className="text-sm text-muted-foreground">Total Rows</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{uploadResult.successfulOrders.length}</div>
                      <div className="text-sm text-muted-foreground">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{uploadResult.failedOrders.length}</div>
                      <div className="text-sm text-muted-foreground">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{uploadResult.duplicateOrders.length}</div>
                      <div className="text-sm text-muted-foreground">Duplicates</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Successful Orders Section */}
              {uploadResult.successfulOrders && uploadResult.successfulOrders.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-green-600 flex items-center gap-2">
                      <ShoppingCart className="h-5 w-5" />
                      Successfully Imported Purchase Orders
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border max-h-64 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Row</TableHead>
                            <TableHead>Order Number</TableHead>
                            <TableHead>Supplier</TableHead>
                            <TableHead>Total</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {uploadResult.successfulOrders.map((order, index) => (
                            <TableRow key={index}>
                              <TableCell>{order.row}</TableCell>
                              <TableCell className="font-medium">{order.orderNumber}</TableCell>
                              <TableCell>{order.supplierName}</TableCell>
                              <TableCell>{formatCurrency(order.total)}</TableCell>
                              <TableCell>
                                <Badge variant="default">{order.status}</Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Failed Orders Section */}
              {uploadResult.failedOrders && uploadResult.failedOrders.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-red-600">Failed Imports</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border max-h-64 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Row</TableHead>
                            <TableHead>Supplier</TableHead>
                            <TableHead>Error</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {uploadResult.failedOrders.map((order, index) => (
                            <TableRow key={index}>
                              <TableCell>{order.row}</TableCell>
                              <TableCell>{order.supplierName || 'N/A'}</TableCell>
                              <TableCell className="text-red-600 text-sm">{order.error}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="flex justify-end">
                <Button onClick={resetUpload}>
                  Upload Another File
                </Button>
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

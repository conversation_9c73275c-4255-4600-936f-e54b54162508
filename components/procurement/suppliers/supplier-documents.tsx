"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  Trash2, 
  Upload,
  FileText,
  File,
  Image,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  User
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface Document {
  _id: string;
  title: string;
  description?: string;
  documentType: 'contract' | 'certificate' | 'license' | 'insurance' | 'tax_document' | 'bank_statement' | 'compliance_document' | 'quality_certificate' | 'other';
  fileName: string;
  fileType: string;
  fileSize: number;
  filePath: string;
  issuedDate?: string;
  expiryDate?: string;
  isExpired: boolean;
  isVerified: boolean;
  verifiedBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  verifiedAt?: string;
  verificationNotes?: string;
  tags: string[];
  isActive: boolean;
  uploadedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface SupplierDocumentsProps {
  supplierId: string;
}

export function SupplierDocuments({ supplierId }: SupplierDocumentsProps) {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  // Fetch documents
  const fetchDocuments = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(typeFilter !== 'all' && { documentType: typeFilter }),
        ...(statusFilter === 'expired' && { isExpired: 'true' }),
        ...(statusFilter === 'active' && { isExpired: 'false' })
      });

      const response = await fetch(`/api/procurement/suppliers/${supplierId}/documents?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }
      
      const data = await response.json();
      setDocuments(data.success ? data.data : []);
      setTotalPages(data.pagination?.pages || 1);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch supplier documents',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [supplierId, page, typeFilter, statusFilter]);

  // Filter documents based on search term
  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getDocumentTypeIcon = (type: string) => {
    const iconMap = {
      contract: FileText,
      certificate: Shield,
      license: Shield,
      insurance: Shield,
      tax_document: FileText,
      bank_statement: FileText,
      compliance_document: Shield,
      quality_certificate: Shield,
      other: File
    };
    return iconMap[type as keyof typeof iconMap] || File;
  };

  const getDocumentTypeBadge = (type: string) => {
    const typeColors = {
      contract: 'bg-blue-100 text-blue-800',
      certificate: 'bg-green-100 text-green-800',
      license: 'bg-purple-100 text-purple-800',
      insurance: 'bg-orange-100 text-orange-800',
      tax_document: 'bg-red-100 text-red-800',
      bank_statement: 'bg-indigo-100 text-indigo-800',
      compliance_document: 'bg-yellow-100 text-yellow-800',
      quality_certificate: 'bg-emerald-100 text-emerald-800',
      other: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge variant="outline" className={typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'}>
        {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  const getStatusBadge = (document: Document) => {
    if (document.isExpired) {
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Expired
        </Badge>
      );
    }
    
    if (document.isVerified) {
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Verified
        </Badge>
      );
    }
    
    return (
      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
        <Clock className="w-3 h-3 mr-1" />
        Pending
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays > 0;
  };

  const handleViewDocument = (document: Document) => {
    // TODO: Implement document viewer
    toast({
      title: 'Document Viewer',
      description: `Viewing document: ${document.title}`,
    });
  };

  const handleDownloadDocument = (document: Document) => {
    // TODO: Implement document download
    toast({
      title: 'Download Document',
      description: `Downloading: ${document.fileName}`,
    });
  };

  const handleDeleteDocument = (document: Document) => {
    // TODO: Implement document deletion
    toast({
      title: 'Delete Document',
      description: `Deleting document: ${document.title}`,
    });
  };

  const handleUploadDocument = () => {
    setIsUploadDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documents.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Verified</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {documents.filter(d => d.isVerified).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {documents.filter(d => d.isExpired).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {documents.filter(d => d.expiryDate && isExpiringSoon(d.expiryDate)).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="type">Document Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="contract">Contract</SelectItem>
                  <SelectItem value="certificate">Certificate</SelectItem>
                  <SelectItem value="license">License</SelectItem>
                  <SelectItem value="insurance">Insurance</SelectItem>
                  <SelectItem value="tax_document">Tax Document</SelectItem>
                  <SelectItem value="bank_statement">Bank Statement</SelectItem>
                  <SelectItem value="compliance_document">Compliance Document</SelectItem>
                  <SelectItem value="quality_certificate">Quality Certificate</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setTypeFilter('all');
                setStatusFilter('all');
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Documents ({filteredDocuments.length})</CardTitle>
              <CardDescription>
                Documents and files related to this supplier
              </CardDescription>
            </div>
            <Button onClick={handleUploadDocument}>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>File Info</TableHead>
                  <TableHead>Dates</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Uploaded By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      Loading documents...
                    </TableCell>
                  </TableRow>
                ) : filteredDocuments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No documents found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredDocuments.map((document) => {
                    const Icon = getDocumentTypeIcon(document.documentType);
                    return (
                      <TableRow key={document._id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{document.title}</div>
                              {document.description && (
                                <div className="text-sm text-muted-foreground">
                                  {document.description.length > 50 
                                    ? `${document.description.substring(0, 50)}...` 
                                    : document.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getDocumentTypeBadge(document.documentType)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="text-sm font-medium">{document.fileName}</div>
                            <div className="text-sm text-muted-foreground">
                              {formatFileSize(document.fileSize)} • {document.fileType.toUpperCase()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {document.issuedDate && (
                              <div className="text-sm">
                                <span className="text-muted-foreground">Issued:</span> {formatDate(document.issuedDate)}
                              </div>
                            )}
                            {document.expiryDate && (
                              <div className={`text-sm ${document.isExpired ? 'text-red-600' : isExpiringSoon(document.expiryDate) ? 'text-orange-600' : ''}`}>
                                <span className="text-muted-foreground">Expires:</span> {formatDate(document.expiryDate)}
                                {isExpiringSoon(document.expiryDate) && !document.isExpired && (
                                  <AlertTriangle className="inline h-3 w-3 ml-1" />
                                )}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(document)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <User className="h-3 w-3 text-muted-foreground" />
                            <div className="text-sm">
                              {document.uploadedBy.firstName} {document.uploadedBy.lastName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewDocument(document)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownloadDocument(document)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteDocument(document)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Page {page} of {totalPages}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Upload Document</DialogTitle>
            <DialogDescription>
              Upload a new document for this supplier.
            </DialogDescription>
          </DialogHeader>
          <div className="p-4">
            <p className="text-muted-foreground">Document upload form coming soon...</p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Trash2, X, Users, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface Supplier {
  _id: string;
  supplierId: string;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  website?: string;
  category: string[];
  taxId?: string;
  paymentTerms?: string;
  bankName?: string;
  accountNumber?: string;
  status: 'active' | 'inactive' | 'blacklisted';
  rating?: number;
  notes?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors?: Array<{ message: string }>;
}

interface BulkSupplierDeleteProps {
  selectedSuppliers: Supplier[];
  onSuccess?: () => void;
  onCancel?: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BulkSupplierDelete({ 
  selectedSuppliers, 
  onSuccess, 
  onCancel, 
  isOpen, 
  onOpenChange 
}: BulkSupplierDeleteProps) {
  const [deletionReason, setDeletionReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);

  // Calculate statistics
  const activeSuppliers = selectedSuppliers.filter(s => s.status === 'active').length;
  const preferredSuppliers = selectedSuppliers.filter(s => s.rating && s.rating >= 4).length;
  const categoriesAffected = [...new Set(selectedSuppliers.flatMap(s => s.category))].length;

  const handleDelete = async () => {
    if (!deletionReason.trim() || deletionReason.trim().length < 25) {
      setDeleteError('Deletion reason must be at least 25 characters long');
      return;
    }

    if (selectedSuppliers.length === 0) {
      setDeleteError('No suppliers selected for deletion');
      return;
    }

    // Additional validation for active suppliers
    if (activeSuppliers > 0 && deletionReason.trim().length < 50) {
      setDeleteError(`Active suppliers require detailed deletion reason (minimum 50 characters). Found ${activeSuppliers} active suppliers.`);
      return;
    }

    setIsDeleting(true);
    setDeleteError(null);
    setDeleteResult(null);

    try {
      const response = await fetch('/api/procurement/suppliers/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supplierIds: selectedSuppliers.map(supplier => supplier._id),
          deletionReason: deletionReason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete suppliers');
      }

      const result = await response.json();
      setDeleteResult(result.data);

      // Show success toast
      toast({
        title: 'Bulk Delete Completed',
        description: `Successfully deleted ${result.data.deletedCount} out of ${result.data.requestedCount} suppliers with audit trail.`,
        variant: result.data.errors && result.data.errors.length > 0 ? 'destructive' : 'default',
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close dialog after successful deletion
      setTimeout(() => {
        onOpenChange(false);
        resetForm();
      }, 2000);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : 'An unexpected error occurred');
      
      toast({
        title: 'Bulk Delete Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const resetForm = () => {
    setDeletionReason('');
    setDeleteError(null);
    setDeleteResult(null);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
    resetForm();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'blacklisted':
        return <Badge variant="destructive">Blacklisted</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRatingStars = (rating?: number) => {
    if (!rating) return null;
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
          />
        ))}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Bulk Delete Suppliers
          </DialogTitle>
          <DialogDescription>
            You are about to permanently delete {selectedSuppliers.length} suppliers. 
            This action cannot be undone and will create an audit trail.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">{selectedSuppliers.length}</div>
              <div className="text-xs text-muted-foreground">Suppliers</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold text-orange-600">{activeSuppliers}</div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold text-yellow-600">{preferredSuppliers}</div>
              <div className="text-xs text-muted-foreground">High-Rated</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">{categoriesAffected}</div>
              <div className="text-xs text-muted-foreground">Categories</div>
            </div>
          </div>

          {/* Selected Suppliers */}
          <div>
            <Label className="text-sm font-medium">Selected Suppliers ({selectedSuppliers.length})</Label>
            <div className="mt-2 max-h-40 overflow-y-auto border rounded-md p-3 bg-muted/50">
              <div className="space-y-2">
                {selectedSuppliers.map((supplier) => (
                  <div key={supplier._id} className="flex items-center justify-between text-sm border-b pb-2 last:border-b-0">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{supplier.name}</div>
                        <div className="text-xs text-muted-foreground">{supplier.supplierId}</div>
                      </div>
                      {supplier.rating && supplier.rating >= 4 && (
                        <Badge variant="outline" className="text-xs">High-Rated</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {getRatingStars(supplier.rating)}
                      {getStatusBadge(supplier.status)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Active Suppliers Warning */}
          {activeSuppliers > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Active Suppliers Detected</AlertTitle>
              <AlertDescription>
                {activeSuppliers} active suppliers will be deleted. 
                This may affect ongoing procurement processes and require detailed justification (minimum 50 characters).
              </AlertDescription>
            </Alert>
          )}

          {/* High-Rated Suppliers Warning */}
          {preferredSuppliers > 0 && (
            <Alert>
              <Star className="h-4 w-4" />
              <AlertTitle>High-Rated Suppliers</AlertTitle>
              <AlertDescription>
                {preferredSuppliers} suppliers have ratings of 4+ stars. 
                Consider the impact on procurement quality and supplier relationships.
              </AlertDescription>
            </Alert>
          )}

          {/* Deletion Reason */}
          <div>
            <Label htmlFor="deletion-reason" className="text-sm font-medium">
              Deletion Reason <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="deletion-reason"
              placeholder={`Provide a detailed reason for deleting these suppliers (minimum ${activeSuppliers > 0 ? '50' : '25'} characters). Include business justification and impact assessment...`}
              value={deletionReason}
              onChange={(e) => setDeletionReason(e.target.value)}
              disabled={isDeleting}
              className="mt-1"
              rows={4}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {deletionReason.length}/{activeSuppliers > 0 ? '50' : '25'} characters minimum
              {activeSuppliers > 0 && ' (Active suppliers require detailed justification)'}
            </p>
          </div>

          {/* Warning */}
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-4 space-y-1 text-sm">
                <li>This action will permanently delete all selected suppliers</li>
                <li>Suppliers with active purchase orders cannot be deleted</li>
                <li>Suppliers with pending contracts cannot be deleted</li>
                <li>All supplier relationships and history will be archived</li>
                <li>Audit records will be created for compliance</li>
                <li>This action cannot be undone</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Error Display */}
          {deleteError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Delete Failed</AlertTitle>
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {deleteResult && (
            <Alert variant={deleteResult.errors && deleteResult.errors.length > 0 ? "destructive" : "default"}>
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Delete Results</AlertTitle>
              <AlertDescription>
                <div className="space-y-1">
                  <p>Requested deletions: {deleteResult.requestedCount}</p>
                  <p>Successfully deleted: {deleteResult.deletedCount}</p>
                  <p>Audit records created: {deleteResult.auditRecordsCreated}</p>
                </div>
                
                {deleteResult.errors && deleteResult.errors.length > 0 && (
                  <div className="mt-3">
                    <p className="font-medium">Errors:</p>
                    <ul className="text-xs space-y-1">
                      {deleteResult.errors.map((error, index) => (
                        <li key={index}>{error.message}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={
              isDeleting || 
              deletionReason.trim().length < (activeSuppliers > 0 ? 50 : 25) || 
              selectedSuppliers.length === 0
            }
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? 'Deleting...' : `Delete ${selectedSuppliers.length} Suppliers`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

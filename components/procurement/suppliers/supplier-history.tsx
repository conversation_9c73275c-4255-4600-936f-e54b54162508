"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Filter, 
  Calendar, 
  User,
  Activity,
  FileText,
  Edit,
  Plus,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Clock,
  DollarSign,
  TrendingUp,
  Shield,
  Settings
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface HistoryEntry {
  _id: string;
  action: 'created' | 'updated' | 'status_changed' | 'rating_updated' | 'contract_added' | 'contract_renewed' | 'contract_terminated' | 'document_uploaded' | 'document_verified' | 'performance_review' | 'payment_made' | 'order_placed' | 'delivery_received' | 'issue_reported' | 'issue_resolved' | 'note_added';
  description: string;
  details: Record<string, any>;
  previousValues: Record<string, any>;
  newValues: Record<string, any>;
  performedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
  };
  performedAt: string;
  category: 'administrative' | 'financial' | 'operational' | 'compliance' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  relatedEntityType?: string;
  relatedEntityId?: string;
  ipAddress?: string;
  userAgent?: string;
}

interface HistorySummary {
  totalActions: number;
  recentActivity: number;
  actionBreakdown: Array<{
    _id: string;
    count: number;
    lastOccurrence: string;
  }>;
}

interface SupplierHistoryProps {
  supplierId: string;
}

export function SupplierHistory({ supplierId }: SupplierHistoryProps) {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [summary, setSummary] = useState<HistorySummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch history
  const fetchHistory = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(actionFilter !== 'all' && { action: actionFilter }),
        ...(categoryFilter !== 'all' && { category: categoryFilter }),
        ...(severityFilter !== 'all' && { severity: severityFilter })
      });

      const response = await fetch(`/api/procurement/suppliers/${supplierId}/history?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch history');
      }
      
      const data = await response.json();
      setHistory(data.success ? data.data : []);
      setSummary(data.summary);
      setTotalPages(data.pagination?.pages || 1);
    } catch (error) {
      console.error('Error fetching history:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch supplier history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, [supplierId, page, actionFilter, categoryFilter, severityFilter]);

  // Filter history based on search term
  const filteredHistory = history.filter(entry =>
    entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entry.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
    `${entry.performedBy.firstName} ${entry.performedBy.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getActionIcon = (action: string) => {
    const iconMap = {
      created: Plus,
      updated: Edit,
      status_changed: Settings,
      rating_updated: TrendingUp,
      contract_added: FileText,
      contract_renewed: CheckCircle,
      contract_terminated: AlertTriangle,
      document_uploaded: FileText,
      document_verified: Shield,
      performance_review: TrendingUp,
      payment_made: DollarSign,
      order_placed: Plus,
      delivery_received: CheckCircle,
      issue_reported: AlertTriangle,
      issue_resolved: CheckCircle,
      note_added: FileText
    };
    return iconMap[action as keyof typeof iconMap] || Activity;
  };

  const getActionBadge = (action: string) => {
    const actionColors = {
      created: 'bg-green-100 text-green-800',
      updated: 'bg-blue-100 text-blue-800',
      status_changed: 'bg-orange-100 text-orange-800',
      rating_updated: 'bg-purple-100 text-purple-800',
      contract_added: 'bg-indigo-100 text-indigo-800',
      contract_renewed: 'bg-green-100 text-green-800',
      contract_terminated: 'bg-red-100 text-red-800',
      document_uploaded: 'bg-blue-100 text-blue-800',
      document_verified: 'bg-green-100 text-green-800',
      performance_review: 'bg-purple-100 text-purple-800',
      payment_made: 'bg-emerald-100 text-emerald-800',
      order_placed: 'bg-blue-100 text-blue-800',
      delivery_received: 'bg-green-100 text-green-800',
      issue_reported: 'bg-red-100 text-red-800',
      issue_resolved: 'bg-green-100 text-green-800',
      note_added: 'bg-gray-100 text-gray-800'
    };

    const Icon = getActionIcon(action);

    return (
      <Badge variant="outline" className={actionColors[action as keyof typeof actionColors] || 'bg-gray-100 text-gray-800'}>
        <Icon className="w-3 h-3 mr-1" />
        {action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  const getCategoryBadge = (category: string) => {
    const categoryColors = {
      administrative: 'bg-blue-100 text-blue-800',
      financial: 'bg-green-100 text-green-800',
      operational: 'bg-orange-100 text-orange-800',
      compliance: 'bg-purple-100 text-purple-800',
      performance: 'bg-indigo-100 text-indigo-800'
    };

    return (
      <Badge variant="outline" className={categoryColors[category as keyof typeof categoryColors] || 'bg-gray-100 text-gray-800'}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    );
  };

  const getSeverityBadge = (severity: string) => {
    const severityColors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };

    return (
      <Badge variant="outline" className={severityColors[severity as keyof typeof severityColors] || 'bg-gray-100 text-gray-800'}>
        {severity.charAt(0).toUpperCase() + severity.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalActions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Recent Activity (30 days)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.recentActivity}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Most Common Action</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium">
                {summary.actionBreakdown[0]?._id.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'N/A'}
              </div>
              <div className="text-xs text-muted-foreground">
                {summary.actionBreakdown[0]?.count || 0} times
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Last Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium">
                {summary.actionBreakdown[0]?.lastOccurrence 
                  ? formatRelativeTime(summary.actionBreakdown[0].lastOccurrence)
                  : 'N/A'}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search history..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="action">Action</Label>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                  <SelectItem value="updated">Updated</SelectItem>
                  <SelectItem value="status_changed">Status Changed</SelectItem>
                  <SelectItem value="contract_added">Contract Added</SelectItem>
                  <SelectItem value="document_uploaded">Document Uploaded</SelectItem>
                  <SelectItem value="payment_made">Payment Made</SelectItem>
                  <SelectItem value="order_placed">Order Placed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="administrative">Administrative</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="operational">Operational</SelectItem>
                  <SelectItem value="compliance">Compliance</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="severity">Severity</Label>
              <Select value={severityFilter} onValueChange={setSeverityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All severities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setActionFilter('all');
                setCategoryFilter('all');
                setSeverityFilter('all');
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* History Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Activity History ({filteredHistory.length})</CardTitle>
          <CardDescription>
            Complete audit trail and activity history for this supplier
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">Loading history...</p>
              </div>
            </div>
          ) : filteredHistory.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold">No History Found</h3>
              <p className="text-muted-foreground">No activity history matches your current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHistory.map((entry, index) => {
                const Icon = getActionIcon(entry.action);
                return (
                  <div key={entry._id} className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                        <Icon className="h-4 w-4" />
                      </div>
                      {index < filteredHistory.length - 1 && (
                        <div className="w-px h-16 bg-border mt-2" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0 pb-8">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            {getActionBadge(entry.action)}
                            {getCategoryBadge(entry.category)}
                            {getSeverityBadge(entry.severity)}
                          </div>
                          <p className="text-sm font-medium">{entry.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {entry.performedBy.firstName} {entry.performedBy.lastName}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(entry.performedAt)}
                            </div>
                          </div>
                          
                          {/* Show details if available */}
                          {Object.keys(entry.details).length > 0 && (
                            <div className="mt-2 p-2 bg-muted rounded text-xs">
                              <strong>Details:</strong>
                              <pre className="mt-1 whitespace-pre-wrap">
                                {JSON.stringify(entry.details, null, 2)}
                              </pre>
                            </div>
                          )}
                          
                          {/* Show changes if available */}
                          {Object.keys(entry.previousValues).length > 0 && Object.keys(entry.newValues).length > 0 && (
                            <div className="mt-2 p-2 bg-muted rounded text-xs">
                              <strong>Changes:</strong>
                              <div className="mt-1 space-y-1">
                                {Object.keys(entry.newValues).map(key => (
                                  <div key={key}>
                                    <span className="font-medium">{key}:</span>
                                    <span className="text-red-600 line-through ml-1">
                                      {String(entry.previousValues[key])}
                                    </span>
                                    <span className="text-green-600 ml-1">
                                      {String(entry.newValues[key])}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatRelativeTime(entry.performedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                Page {page} of {totalPages}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

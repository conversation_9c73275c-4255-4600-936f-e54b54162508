// components/procurement/purchase-orders.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  PlusCircle,
  Search,
  Eye,
  Edit,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  FileText,
  Package,
  Upload,
  Trash2,
  Trash,
  <PERSON><PERSON><PERSON>,
  Filter
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { toast } from "@/components/ui/use-toast"
import { PurchaseOrderForm } from "@/components/procurement/forms/purchase-order-form"
import { BulkPurchaseOrderUpload } from "@/components/procurement/purchase-orders/bulk-purchase-order-upload"
import { BulkPurchaseOrderDelete } from "@/components/procurement/purchase-orders/bulk-purchase-order-delete"

// Purchase Order interfaces
interface PurchaseOrderItem {
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  tax?: number;
  discount?: number;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  requisitionId?: string;
  supplierId: {
    _id: string;
    name: string;
    supplierId: string;
  };
  orderDate: string;
  expectedDeliveryDate?: string;
  actualDeliveryDate?: string;
  status: 'draft' | 'sent' | 'confirmed' | 'partially_received' | 'received' | 'cancelled' | 'closed';
  items: PurchaseOrderItem[];
  subtotal: number;
  tax: number;
  discount?: number;
  total: number;
  paymentTerms?: string;
  shippingTerms?: string;
  notes?: string;
  attachments?: string[];
  createdBy: string;
  approvedBy?: string;
  approvalDate?: string;
  createdAt: string;
  updatedAt: string;
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    draft: { variant: "outline" as const, label: "Draft", icon: FileText },
    sent: { variant: "secondary" as const, label: "Sent", icon: Clock },
    confirmed: { variant: "default" as const, label: "Confirmed", icon: CheckCircle },
    partially_received: { variant: "secondary" as const, label: "Partially Received", icon: Package },
    received: { variant: "default" as const, label: "Received", icon: CheckCircle },
    cancelled: { variant: "destructive" as const, label: "Cancelled", icon: AlertTriangle },
    closed: { variant: "outline" as const, label: "Closed", icon: FileText }
  }

  const config = statusConfig[status as keyof typeof statusConfig] || {
    variant: "outline" as const,
    label: status.replace('_', ' ').toUpperCase(),
    icon: FileText
  }

  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
  }).format(amount);
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-MW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function PurchaseOrders() {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [supplierFilter, setSupplierFilter] = useState("all");

  // Modal states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingOrder, setEditingOrder] = useState<PurchaseOrder | null>(null);

  // Bulk operations state
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isBulkUploadDialogOpen, setIsBulkUploadDialogOpen] = useState(false);

  // Fetch purchase orders
  const fetchPurchaseOrders = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/procurement/purchase-orders');
      if (!response.ok) {
        throw new Error('Failed to fetch purchase orders');
      }
      const data = await response.json();
      setPurchaseOrders(data.docs || []);
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch purchase orders',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/procurement/suppliers');
      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  useEffect(() => {
    fetchPurchaseOrders();
    fetchSuppliers();
  }, []);

  // Filter purchase orders
  const filteredOrders = purchaseOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.supplierId.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.items.some(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    const matchesSupplier = supplierFilter === "all" || order.supplierId._id === supplierFilter;

    return matchesSearch && matchesStatus && matchesSupplier;
  });

  // Handler functions
  const handleCreateOrder = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditOrder = (order: PurchaseOrder) => {
    setEditingOrder(order);
    setIsEditDialogOpen(true);
  };

  const handleViewOrder = (order: PurchaseOrder) => {
    // TODO: Implement view order details
    console.log('View order:', order);
  };

  const handleDeleteOrder = async (order: PurchaseOrder) => {
    // Check if order can be deleted
    if (['confirmed', 'partially_received', 'received'].includes(order.status)) {
      toast({
        title: 'Cannot Delete',
        description: `Cannot delete purchase order with status: ${order.status}`,
        variant: 'destructive',
      });
      return;
    }

    const reason = prompt(`Please provide a reason for deleting purchase order "${order.orderNumber}":`);
    if (!reason || reason.trim().length < 10) {
      toast({
        title: 'Deletion Cancelled',
        description: 'A valid reason (minimum 10 characters) is required to delete a purchase order',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/procurement/purchase-orders/${order._id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deletionReason: reason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete purchase order');
      }

      toast({
        title: 'Success',
        description: 'Purchase order deleted successfully with audit trail',
      });

      fetchPurchaseOrders();
    } catch (error) {
      console.error('Error deleting purchase order:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete purchase order',
        variant: 'destructive',
      });
    }
  };

  // Bulk operations handlers
  const handleSelectOrder = (orderId: string, selected: boolean) => {
    setSelectedOrders(prev =>
      selected
        ? [...prev, orderId]
        : prev.filter(id => id !== orderId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? purchaseOrders.map(order => order._id) : []);
  };

  const handleBulkDelete = () => {
    if (selectedOrders.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select purchase orders to delete',
        variant: 'destructive',
      });
      return;
    }
    setIsBulkDeleteDialogOpen(true);
  };

  const handleBulkUpload = () => {
    setIsBulkUploadDialogOpen(true);
  };

  const handleBulkOperationSuccess = () => {
    setSelectedOrders([]);
    fetchPurchaseOrders();
  };

  const handleRefresh = () => {
    fetchPurchaseOrders();
  };

  // Calculate statistics
  const totalOrders = purchaseOrders.length;
  const activeOrders = purchaseOrders.filter(o => ['sent', 'confirmed', 'partially_received'].includes(o.status)).length;
  const totalValue = purchaseOrders.reduce((sum, order) => sum + order.total, 0);
  const pendingOrders = purchaseOrders.filter(o => o.status === 'draft').length;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Purchase Orders"
        text="Manage purchase orders and track delivery status"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <Settings className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleBulkUpload}>
            <Upload className="mr-2 h-4 w-4" />
            Bulk Import
          </Button>
          <Button onClick={handleCreateOrder}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Order
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalOrders}</div>
              <p className="text-xs text-muted-foreground">
                All purchase orders
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Orders</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeOrders}</div>
              <p className="text-xs text-muted-foreground">
                In progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingOrders}</div>
              <p className="text-xs text-muted-foreground">
                Draft orders
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Combined order value
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search orders..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="sent">Sent</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="partially_received">Partially Received</SelectItem>
                    <SelectItem value="received">Received</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="supplier">Supplier</Label>
                <Select value={supplierFilter} onValueChange={setSupplierFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All suppliers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Suppliers</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier._id} value={supplier._id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setSupplierFilter('all');
                }}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>Purchase Orders ({filteredOrders.length})</CardTitle>
            <CardDescription>
              Track and manage all purchase orders
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Bulk Actions Bar */}
            {selectedOrders.length > 0 && (
              <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-md mb-4">
                <div className="text-sm">
                  {selectedOrders.length} {selectedOrders.length === 1 ? 'order' : 'orders'} selected
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="h-8 gap-1"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Selected
                </Button>
              </div>
            )}

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={purchaseOrders.length > 0 && selectedOrders.length === purchaseOrders.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </TableHead>
                    <TableHead>Order Number</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Order Date</TableHead>
                    <TableHead>Expected Delivery</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        Loading purchase orders...
                      </TableCell>
                    </TableRow>
                  ) : filteredOrders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        No purchase orders found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredOrders.map((order) => (
                      <TableRow key={order._id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedOrders.includes(order._id)}
                            onCheckedChange={(checked) => handleSelectOrder(order._id, !!checked)}
                            aria-label={`Select ${order.orderNumber}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{order.orderNumber}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{order.supplierId.name}</div>
                            <div className="text-sm text-muted-foreground">{order.supplierId.supplierId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                            <div className="text-xs text-muted-foreground">
                              {order.items.slice(0, 2).map(item => item.name).join(', ')}
                              {order.items.length > 2 && ` +${order.items.length - 2} more`}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(order.total)}</TableCell>
                        <TableCell>{getStatusBadge(order.status)}</TableCell>
                        <TableCell>{formatDate(order.orderDate)}</TableCell>
                        <TableCell>
                          {order.expectedDeliveryDate ? formatDate(order.expectedDeliveryDate) : 'Not set'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="ghost" size="sm" onClick={() => handleViewOrder(order)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleEditOrder(order)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleDeleteOrder(order)}>
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Create Purchase Order Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Purchase Order</DialogTitle>
            <DialogDescription>
              Create a new purchase order for procurement
            </DialogDescription>
          </DialogHeader>
          <PurchaseOrderForm
            suppliers={suppliers}
            onSubmit={async (data) => {
              try {
                const response = await fetch('/api/procurement/purchase-orders', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(data),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to create purchase order');
                }

                toast({
                  title: 'Success',
                  description: 'Purchase order created successfully',
                });

                setIsCreateDialogOpen(false);
                fetchPurchaseOrders();
              } catch (error) {
                console.error('Error creating purchase order:', error);
                throw error;
              }
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Purchase Order Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Purchase Order</DialogTitle>
            <DialogDescription>
              Update purchase order information
            </DialogDescription>
          </DialogHeader>
          <PurchaseOrderForm
            initialData={editingOrder ? {
              orderNumber: editingOrder.orderNumber,
              title: `Purchase Order ${editingOrder.orderNumber}`,
              supplierId: editingOrder.supplierId._id,
              orderDate: new Date(editingOrder.orderDate),
              expectedDeliveryDate: editingOrder.expectedDeliveryDate ? new Date(editingOrder.expectedDeliveryDate) : undefined,
              items: editingOrder.items.map(item => ({
                name: item.name,
                description: item.description,
                quantity: item.quantity,
                unit: item.unit,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                tax: item.tax || 0,
                discount: item.discount || 0,
              })),
              paymentTerms: editingOrder.paymentTerms,
              shippingTerms: editingOrder.shippingTerms,
              notes: editingOrder.notes,
              status: editingOrder.status,
            } : undefined}
            suppliers={suppliers}
            onSubmit={async (data) => {
              if (!editingOrder) return;

              try {
                const response = await fetch(`/api/procurement/purchase-orders/${editingOrder._id}`, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(data),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to update purchase order');
                }

                toast({
                  title: 'Success',
                  description: 'Purchase order updated successfully',
                });

                setIsEditDialogOpen(false);
                setEditingOrder(null);
                fetchPurchaseOrders();
              } catch (error) {
                console.error('Error updating purchase order:', error);
                throw error;
              }
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Upload Dialog */}
      <Dialog open={isBulkUploadDialogOpen} onOpenChange={setIsBulkUploadDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Import Purchase Orders</DialogTitle>
            <DialogDescription>
              Import multiple purchase orders from a CSV or Excel file
            </DialogDescription>
          </DialogHeader>
          <BulkPurchaseOrderUpload
            onSuccess={() => {
              setIsBulkUploadDialogOpen(false);
              fetchPurchaseOrders();
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Delete Purchase Orders</DialogTitle>
            <DialogDescription>
              Delete multiple purchase orders with audit trail
            </DialogDescription>
          </DialogHeader>
          <BulkPurchaseOrderDelete
            selectedOrderIds={selectedOrders}
            selectedOrders={purchaseOrders.filter(order => selectedOrders.includes(order._id))}
            onSuccess={() => {
              setIsBulkDeleteDialogOpen(false);
              setSelectedOrders([]);
              fetchPurchaseOrders();
            }}
            onCancel={() => {
              setIsBulkDeleteDialogOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}

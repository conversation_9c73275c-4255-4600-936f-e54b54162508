"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"
import { format, differenceInDays } from "date-fns"
import {
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  FileText,
  User,
  Building,
  DollarSign,
  AlertTriangle,
  Download,
  RefreshCw,
  XCircle,
  Eye,
  Plus,
  Paperclip
} from "lucide-react"

// Contract interface (based on the existing model)
interface ContractDocument {
  _id: string
  name: string
  type: string
  url: string
  uploadedAt: Date
  uploadedBy: string
}

interface ContractPerformance {
  deliveryOnTime: number
  qualityScore: number
  complianceScore: number
  overallRating: number
}

interface Contract {
  _id: string
  contractNumber: string
  title: string
  description?: string
  supplierId: string
  supplierName?: string
  supplierContact?: {
    name: string
    email: string
    phone: string
  }
  type: 'goods' | 'services' | 'maintenance' | 'consulting'
  status: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed'
  startDate: Date
  endDate: Date
  value: number
  currency: string
  department: string
  terms: {
    paymentTerms: string
    deliveryTerms: string
    penaltyClause?: string
    terminationClause?: string
  }
  documents: ContractDocument[]
  performance?: ContractPerformance
  renewalHistory?: Array<{
    renewedDate: Date
    previousEndDate: Date
    newEndDate: Date
    renewedBy: string
  }>
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

interface ContractDetailsProps {
  contract: Contract
  onEdit?: () => void
  onRenew?: () => void
  onTerminate?: () => void
  onBack?: () => void
  isLoading?: boolean
}

// Status configuration
const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'bg-gray-100 text-gray-800',
    icon: FileText
  },
  active: { 
    label: 'Active', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  expired: { 
    label: 'Expired', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
  terminated: { 
    label: 'Terminated', 
    color: 'bg-orange-100 text-orange-800',
    icon: AlertTriangle
  },
  renewed: { 
    label: 'Renewed', 
    color: 'bg-blue-100 text-blue-800',
    icon: RefreshCw
  },
}

// Type configuration
const typeConfig = {
  goods: { label: 'Goods', color: 'bg-blue-50 text-blue-700' },
  services: { label: 'Services', color: 'bg-purple-50 text-purple-700' },
  maintenance: { label: 'Maintenance', color: 'bg-orange-50 text-orange-700' },
  consulting: { label: 'Consulting', color: 'bg-green-50 text-green-700' },
}

export function ContractDetails({ 
  contract, 
  onEdit, 
  onRenew, 
  onTerminate, 
  onBack,
  isLoading = false 
}: ContractDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()

  // Calculate contract duration and remaining time
  const totalDuration = differenceInDays(new Date(contract.endDate), new Date(contract.startDate))
  const elapsed = differenceInDays(new Date(), new Date(contract.startDate))
  const remaining = differenceInDays(new Date(contract.endDate), new Date())
  const progress = Math.max(0, Math.min(100, (elapsed / totalDuration) * 100))

  // Check if contract is expiring soon (within 30 days)
  const isExpiringSoon = remaining <= 30 && remaining > 0
  const isExpired = remaining < 0

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount)
  }

  // Handle document download
  const handleDocumentDownload = (document: ContractDocument) => {
    // Implementation for document download
    toast({
      title: "Download Started",
      description: `Downloading ${document.name}`,
    })
  }

  const statusInfo = statusConfig[contract.status]
  const typeInfo = typeConfig[contract.type]
  const StatusIcon = statusInfo.icon

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{contract.title}</h1>
            <p className="text-muted-foreground">Contract #{contract.contractNumber}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
          {contract.status === 'active' && onRenew && (
            <Button variant="outline" size="sm" onClick={onRenew}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Renew
            </Button>
          )}
          {contract.status === 'active' && onTerminate && (
            <Button variant="outline" size="sm" onClick={onTerminate}>
              <XCircle className="mr-2 h-4 w-4" />
              Terminate
            </Button>
          )}
          {onEdit && (
            <Button size="sm" onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <StatusIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={statusInfo.color}>
              <StatusIcon className="h-3 w-3 mr-1" />
              {statusInfo.label}
            </Badge>
            {isExpiringSoon && (
              <p className="text-xs text-orange-600 mt-1">
                Expires in {remaining} days
              </p>
            )}
            {isExpired && (
              <p className="text-xs text-red-600 mt-1">
                Expired {Math.abs(remaining)} days ago
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contract Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(contract.value, contract.currency)}
            </div>
            <Badge variant="outline" className={typeInfo.color}>
              {typeInfo.label}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Duration</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDuration}</div>
            <p className="text-xs text-muted-foreground">days total</p>
            <div className="mt-2">
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round(progress)}% elapsed
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {contract.performance ? (
              <>
                <div className="text-2xl font-bold">
                  {contract.performance.overallRating.toFixed(1)}
                </div>
                <p className="text-xs text-muted-foreground">out of 5.0</p>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold text-muted-foreground">-</div>
                <p className="text-xs text-muted-foreground">No data</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="terms">Terms</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 pt-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Contract Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contract Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="text-sm mt-1">{contract.description || 'No description provided'}</p>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Start Date</label>
                    <p className="text-sm mt-1">{format(new Date(contract.startDate), "PPP")}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">End Date</label>
                    <p className="text-sm mt-1">{format(new Date(contract.endDate), "PPP")}</p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Department</label>
                  <p className="text-sm mt-1">{contract.department}</p>
                </div>
              </CardContent>
            </Card>

            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle>Supplier Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src="" />
                    <AvatarFallback>
                      <Building className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{contract.supplierName || 'Unknown Supplier'}</p>
                    <p className="text-sm text-muted-foreground">Supplier ID: {contract.supplierId}</p>
                  </div>
                </div>
                
                {contract.supplierContact && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{contract.supplierContact.name}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>{contract.supplierContact.email}</p>
                        <p>{contract.supplierContact.phone}</p>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="terms" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Terms & Conditions</CardTitle>
              <CardDescription>
                Detailed terms and conditions of the contract
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Payment Terms</label>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">
                  {contract.terms.paymentTerms || 'No payment terms specified'}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Delivery Terms</label>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">
                  {contract.terms.deliveryTerms || 'No delivery terms specified'}
                </p>
              </div>

              {contract.terms.penaltyClause && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Penalty Clause</label>
                  <p className="text-sm mt-1 p-3 bg-red-50 border border-red-200 rounded-md">
                    {contract.terms.penaltyClause}
                  </p>
                </div>
              )}

              {contract.terms.terminationClause && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Termination Clause</label>
                  <p className="text-sm mt-1 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    {contract.terms.terminationClause}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4 pt-4">
          {contract.performance ? (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>
                    Supplier performance evaluation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Delivery On Time</span>
                        <span>{contract.performance.deliveryOnTime}%</span>
                      </div>
                      <Progress value={contract.performance.deliveryOnTime} className="h-2 mt-1" />
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Quality Score</span>
                        <span>{contract.performance.qualityScore}%</span>
                      </div>
                      <Progress value={contract.performance.qualityScore} className="h-2 mt-1" />
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Compliance Score</span>
                        <span>{contract.performance.complianceScore}%</span>
                      </div>
                      <Progress value={contract.performance.complianceScore} className="h-2 mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Overall Rating</CardTitle>
                  <CardDescription>
                    Comprehensive performance rating
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary">
                      {contract.performance.overallRating.toFixed(1)}
                    </div>
                    <div className="text-sm text-muted-foreground">out of 5.0</div>
                    <div className="mt-4">
                      <Progress value={(contract.performance.overallRating / 5) * 100} className="h-3" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Performance Data</h3>
                <p className="text-muted-foreground text-center">
                  Performance metrics will be available once the contract is active and deliveries begin.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="documents" className="space-y-4 pt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Contract Documents</CardTitle>
                <CardDescription>
                  All documents related to this contract
                </CardDescription>
              </div>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Upload Document
              </Button>
            </CardHeader>
            <CardContent>
              {contract.documents.length > 0 ? (
                <div className="space-y-3">
                  {contract.documents.map((document) => (
                    <div key={document._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Paperclip className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{document.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {document.type} • Uploaded {format(new Date(document.uploadedAt), "MMM dd, yyyy")}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDocumentDownload(document)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Documents</h3>
                  <p className="text-muted-foreground text-center">
                    Upload contract documents to keep all related files organized.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract History</CardTitle>
              <CardDescription>
                Timeline of contract events and renewals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Contract Creation */}
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="font-medium">Contract Created</p>
                    <p className="text-sm text-muted-foreground">
                      Created by {contract.createdBy} on {format(new Date(contract.createdAt), "PPP")}
                    </p>
                  </div>
                </div>

                {/* Renewal History */}
                {contract.renewalHistory?.map((renewal, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="font-medium">Contract Renewed</p>
                      <p className="text-sm text-muted-foreground">
                        Renewed by {renewal.renewedBy} on {format(new Date(renewal.renewedDate), "PPP")}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Extended from {format(new Date(renewal.previousEndDate), "MMM dd, yyyy")} to {format(new Date(renewal.newEndDate), "MMM dd, yyyy")}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Current Status */}
                <div className="flex items-start gap-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    contract.status === 'active' ? 'bg-green-500' :
                    contract.status === 'expired' ? 'bg-red-500' :
                    contract.status === 'terminated' ? 'bg-orange-500' :
                    'bg-gray-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="font-medium">Current Status: {statusInfo.label}</p>
                    <p className="text-sm text-muted-foreground">
                      Last updated on {format(new Date(contract.updatedAt), "PPP")}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

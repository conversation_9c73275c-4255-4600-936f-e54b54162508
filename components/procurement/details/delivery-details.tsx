"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import {
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Package,
  Truck,
  MapPin,
  Building,
  AlertTriangle,
  XCircle,
  Eye,
  Plus,
  User,
  Phone,
  Mail
} from "lucide-react"

// Delivery interfaces (based on the existing model)
interface DeliveryItem {
  _id: string
  itemName: string
  description?: string
  quantity: number
  receivedQuantity: number
  unit: string
  condition: 'good' | 'damaged' | 'missing'
  notes?: string
}

interface DeliveryAddress {
  street: string
  city: string
  state?: string
  postalCode?: string
  country: string
  contactPerson?: string
  contactPhone?: string
}

interface Delivery {
  _id: string
  deliveryNumber: string
  purchaseOrderId: string
  purchaseOrderNumber?: string
  supplierId: string
  supplierName?: string
  status: 'scheduled' | 'in_transit' | 'delivered' | 'partially_received' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate: Date
  deliveredDate?: Date
  expectedDate: Date
  items: DeliveryItem[]
  deliveryAddress: DeliveryAddress
  trackingNumber?: string
  carrierName?: string
  notes?: string
  receivedBy?: string
  qualityCheckNotes?: string
  createdAt: Date
  updatedAt: Date
}

interface DeliveryDetailsProps {
  delivery: Delivery
  onEdit?: () => void
  onReceive?: () => void
  onComplete?: () => void
  onCancel?: () => void
  onBack?: () => void
  isLoading?: boolean
}

// Status configuration
const statusConfig = {
  scheduled: { 
    label: 'Scheduled', 
    color: 'bg-blue-100 text-blue-800',
    icon: Calendar
  },
  in_transit: { 
    label: 'In Transit', 
    color: 'bg-yellow-100 text-yellow-800',
    icon: Truck
  },
  delivered: { 
    label: 'Delivered', 
    color: 'bg-green-100 text-green-800',
    icon: Package
  },
  partially_received: { 
    label: 'Partially Received', 
    color: 'bg-orange-100 text-orange-800',
    icon: AlertTriangle
  },
  completed: { 
    label: 'Completed', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  cancelled: { 
    label: 'Cancelled', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
}

// Priority configuration
const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
}

// Condition configuration
const conditionConfig = {
  good: { label: 'Good', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  damaged: { label: 'Damaged', color: 'bg-red-100 text-red-800', icon: AlertTriangle },
  missing: { label: 'Missing', color: 'bg-gray-100 text-gray-800', icon: XCircle },
}

export function DeliveryDetails({ 
  delivery, 
  onEdit, 
  onReceive, 
  onComplete,
  onCancel,
  onBack,
  isLoading = false 
}: DeliveryDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [receivingMode, setReceivingMode] = useState(false)
  const { toast } = useToast()

  // Calculate delivery progress
  const totalQuantity = delivery.items.reduce((sum, item) => sum + item.quantity, 0)
  const receivedQuantity = delivery.items.reduce((sum, item) => sum + item.receivedQuantity, 0)
  const progress = totalQuantity > 0 ? Math.round((receivedQuantity / totalQuantity) * 100) : 0

  // Check if delivery is overdue
  const isOverdue = new Date(delivery.expectedDate) < new Date() && 
                   !['delivered', 'completed', 'cancelled'].includes(delivery.status)

  const statusInfo = statusConfig[delivery.status]
  const priorityInfo = priorityConfig[delivery.priority]
  const StatusIcon = statusInfo.icon

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Delivery #{delivery.deliveryNumber}</h1>
            <p className="text-muted-foreground">
              {delivery.purchaseOrderNumber && `PO: ${delivery.purchaseOrderNumber} • `}
              {delivery.supplierName || 'Unknown Supplier'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {delivery.trackingNumber && (
            <Button variant="outline" size="sm">
              <Truck className="mr-2 h-4 w-4" />
              Track Package
            </Button>
          )}
          {['delivered', 'partially_received'].includes(delivery.status) && onReceive && (
            <Button variant="outline" size="sm" onClick={onReceive}>
              <Package className="mr-2 h-4 w-4" />
              Receive Items
            </Button>
          )}
          {delivery.status === 'partially_received' && onComplete && (
            <Button variant="outline" size="sm" onClick={onComplete}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Complete
            </Button>
          )}
          {!['completed', 'cancelled'].includes(delivery.status) && onEdit && (
            <Button size="sm" onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <StatusIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={statusInfo.color}>
              <StatusIcon className="h-3 w-3 mr-1" />
              {statusInfo.label}
            </Badge>
            {isOverdue && (
              <p className="text-xs text-red-600 mt-1">
                Overdue
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Priority</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant="outline" className={priorityInfo.color}>
              {priorityInfo.label}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Progress</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{progress}%</div>
            <Progress value={progress} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {receivedQuantity} of {totalQuantity} items
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expected Date</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-sm font-medium ${isOverdue ? 'text-red-600' : ''}`}>
              {format(new Date(delivery.expectedDate), "MMM dd, yyyy")}
            </div>
            {delivery.deliveredDate && (
              <p className="text-xs text-muted-foreground mt-1">
                Delivered: {format(new Date(delivery.deliveredDate), "MMM dd, yyyy")}
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="tracking">Tracking</TabsTrigger>
          <TabsTrigger value="receipt">Receipt</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 pt-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle>Delivery Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Scheduled Date</label>
                    <p className="text-sm mt-1">{format(new Date(delivery.scheduledDate), "PPP")}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Expected Date</label>
                    <p className="text-sm mt-1">{format(new Date(delivery.expectedDate), "PPP")}</p>
                  </div>
                </div>
                
                {delivery.trackingNumber && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Tracking Number</label>
                      <p className="text-sm mt-1 font-mono">{delivery.trackingNumber}</p>
                    </div>
                  </>
                )}
                
                {delivery.carrierName && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Carrier</label>
                    <p className="text-sm mt-1">{delivery.carrierName}</p>
                  </div>
                )}
                
                {delivery.notes && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Notes</label>
                      <p className="text-sm mt-1">{delivery.notes}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Delivery Address */}
            <Card>
              <CardHeader>
                <CardTitle>Delivery Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div className="flex-1">
                    <p className="text-sm">{delivery.deliveryAddress.street}</p>
                    <p className="text-sm">
                      {delivery.deliveryAddress.city}
                      {delivery.deliveryAddress.state && `, ${delivery.deliveryAddress.state}`}
                      {delivery.deliveryAddress.postalCode && ` ${delivery.deliveryAddress.postalCode}`}
                    </p>
                    <p className="text-sm">{delivery.deliveryAddress.country}</p>
                  </div>
                </div>
                
                {delivery.deliveryAddress.contactPerson && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{delivery.deliveryAddress.contactPerson}</span>
                      </div>
                      {delivery.deliveryAddress.contactPhone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{delivery.deliveryAddress.contactPhone}</span>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="items" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Items</CardTitle>
              <CardDescription>
                Items included in this delivery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {delivery.items.map((item) => {
                  const conditionInfo = conditionConfig[item.condition]
                  const ConditionIcon = conditionInfo.icon
                  const itemProgress = item.quantity > 0 ? (item.receivedQuantity / item.quantity) * 100 : 0

                  return (
                    <div key={item._id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.itemName}</h4>
                          {item.description && (
                            <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                          )}
                        </div>
                        <Badge className={conditionInfo.color}>
                          <ConditionIcon className="h-3 w-3 mr-1" />
                          {conditionInfo.label}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-3">
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">Ordered</label>
                          <p className="text-sm">{item.quantity} {item.unit}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">Received</label>
                          <p className="text-sm">{item.receivedQuantity} {item.unit}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">Progress</label>
                          <div className="flex items-center gap-2">
                            <Progress value={itemProgress} className="flex-1 h-2" />
                            <span className="text-xs">{Math.round(itemProgress)}%</span>
                          </div>
                        </div>
                      </div>

                      {item.notes && (
                        <div className="mt-3 p-2 bg-muted rounded text-sm">
                          <strong>Notes:</strong> {item.notes}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Timeline</CardTitle>
              <CardDescription>
                Track the progress of your delivery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Scheduled */}
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="font-medium">Delivery Scheduled</p>
                    <p className="text-sm text-muted-foreground">
                      Scheduled for {format(new Date(delivery.scheduledDate), "PPP")}
                    </p>
                  </div>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>

                {/* In Transit */}
                <div className="flex items-start gap-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    ['in_transit', 'delivered', 'partially_received', 'completed'].includes(delivery.status)
                      ? 'bg-yellow-500' : 'bg-gray-300'
                  }`}></div>
                  <div className="flex-1">
                    <p className="font-medium">In Transit</p>
                    <p className="text-sm text-muted-foreground">
                      {delivery.trackingNumber ? `Tracking: ${delivery.trackingNumber}` : 'Package is on the way'}
                    </p>
                    {delivery.carrierName && (
                      <p className="text-sm text-muted-foreground">Carrier: {delivery.carrierName}</p>
                    )}
                  </div>
                  {['in_transit', 'delivered', 'partially_received', 'completed'].includes(delivery.status) && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>

                {/* Delivered */}
                <div className="flex items-start gap-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    ['delivered', 'partially_received', 'completed'].includes(delivery.status)
                      ? 'bg-green-500' : 'bg-gray-300'
                  }`}></div>
                  <div className="flex-1">
                    <p className="font-medium">Delivered</p>
                    <p className="text-sm text-muted-foreground">
                      {delivery.deliveredDate
                        ? `Delivered on ${format(new Date(delivery.deliveredDate), "PPP")}`
                        : `Expected ${format(new Date(delivery.expectedDate), "PPP")}`
                      }
                    </p>
                  </div>
                  {['delivered', 'partially_received', 'completed'].includes(delivery.status) && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>

                {/* Received */}
                <div className="flex items-start gap-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    ['partially_received', 'completed'].includes(delivery.status)
                      ? 'bg-green-500' : 'bg-gray-300'
                  }`}></div>
                  <div className="flex-1">
                    <p className="font-medium">Items Received</p>
                    <p className="text-sm text-muted-foreground">
                      {delivery.status === 'completed'
                        ? 'All items received and verified'
                        : delivery.status === 'partially_received'
                        ? `${receivedQuantity} of ${totalQuantity} items received`
                        : 'Pending receipt verification'
                      }
                    </p>
                    {delivery.receivedBy && (
                      <p className="text-sm text-muted-foreground">Received by: {delivery.receivedBy}</p>
                    )}
                  </div>
                  {['partially_received', 'completed'].includes(delivery.status) && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>

                {/* Completed */}
                <div className="flex items-start gap-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    delivery.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                  }`}></div>
                  <div className="flex-1">
                    <p className="font-medium">Delivery Completed</p>
                    <p className="text-sm text-muted-foreground">
                      {delivery.status === 'completed'
                        ? 'Delivery process completed successfully'
                        : 'Pending completion'
                      }
                    </p>
                  </div>
                  {delivery.status === 'completed' && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="receipt" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Goods Receipt</CardTitle>
              <CardDescription>
                Record receipt and quality check information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {delivery.receivedBy && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Received By</label>
                  <p className="text-sm mt-1">{delivery.receivedBy}</p>
                </div>
              )}

              {delivery.qualityCheckNotes && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Quality Check Notes</label>
                  <div className="text-sm mt-1 p-3 bg-muted rounded-md">
                    {delivery.qualityCheckNotes}
                  </div>
                </div>
              )}

              {!delivery.receivedBy && ['delivered', 'partially_received'].includes(delivery.status) && (
                <div className="border-2 border-dashed border-muted rounded-lg p-6 text-center">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Ready for Receipt</h3>
                  <p className="text-muted-foreground mb-4">
                    This delivery is ready to be received. Click the button below to start the receipt process.
                  </p>
                  {onReceive && (
                    <Button onClick={onReceive}>
                      <Package className="mr-2 h-4 w-4" />
                      Start Receipt Process
                    </Button>
                  )}
                </div>
              )}

              {delivery.status === 'completed' && (
                <div className="border border-green-200 bg-green-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="font-medium text-green-800">Receipt Completed</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    All items have been received and the delivery process is complete.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// components/procurement/contract-management.tsx
"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  PlusCircle, 
  Search, 
  Eye,
  Edit,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Calendar
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

// Mock data for contracts
const mockContracts = [
  {
    id: "CON-2024-001",
    supplier: "Office Supplies Ltd",
    title: "Office Supplies Annual Contract",
    value: 2500000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "active",
    renewalDate: "2024-11-01",
    type: "annual",
    category: "Office Supplies"
  },
  {
    id: "CON-2024-002", 
    supplier: "Tech Solutions Inc",
    title: "IT Equipment & Support",
    value: 5000000,
    startDate: "2024-02-01",
    endDate: "2025-01-31",
    status: "active",
    renewalDate: "2024-12-01",
    type: "annual",
    category: "IT Equipment"
  },
  {
    id: "CON-2023-015",
    supplier: "Cleaning Services Co",
    title: "Facility Cleaning Services",
    value: 800000,
    startDate: "2023-06-01",
    endDate: "2024-05-31",
    status: "expiring_soon",
    renewalDate: "2024-04-01",
    type: "annual",
    category: "Services"
  },
  {
    id: "CON-2024-003",
    supplier: "Security Systems Ltd",
    title: "Security Equipment Maintenance",
    value: 1200000,
    startDate: "2024-03-01",
    endDate: "2027-02-28",
    status: "active",
    renewalDate: "2026-12-01",
    type: "multi_year",
    category: "Security"
  }
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    active: { variant: "default" as const, label: "Active", icon: CheckCircle },
    expiring_soon: { variant: "destructive" as const, label: "Expiring Soon", icon: AlertTriangle },
    expired: { variant: "destructive" as const, label: "Expired", icon: AlertTriangle },
    draft: { variant: "outline" as const, label: "Draft", icon: FileText },
    pending: { variant: "secondary" as const, label: "Pending", icon: Clock },
    terminated: { variant: "destructive" as const, label: "Terminated", icon: AlertTriangle }
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || { 
    variant: "outline" as const, 
    label: status,
    icon: FileText
  }
  
  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const getTypeLabel = (type: string) => {
  const typeLabels = {
    annual: "Annual",
    multi_year: "Multi-Year",
    monthly: "Monthly",
    project: "Project-Based"
  }
  return typeLabels[type as keyof typeof typeLabels] || type
}

const getDaysUntilExpiry = (endDate: string) => {
  const today = new Date()
  const expiry = new Date(endDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export function ContractManagement() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredContracts = mockContracts.filter(contract => {
    return contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
           contract.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
           contract.category.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const expiringContracts = mockContracts.filter(contract => {
    const daysUntilExpiry = getDaysUntilExpiry(contract.endDate)
    return daysUntilExpiry <= 90 && daysUntilExpiry > 0
  })

  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Contract Management"
        text="Manage supplier contracts, renewals, and compliance"
      >
        <div className="flex gap-2">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Contract
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockContracts.length}</div>
              <p className="text-xs text-muted-foreground">
                Active and pending contracts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Contracts</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockContracts.filter(c => c.status === "active").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {expiringContracts.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Within 90 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                MWK {mockContracts.reduce((sum, c) => sum + c.value, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Combined contract value
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Expiring Contracts Alert */}
        {expiringContracts.length > 0 && (
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800">
                <AlertTriangle className="h-5 w-5" />
                Contracts Expiring Soon
              </CardTitle>
              <CardDescription className="text-orange-700">
                {expiringContracts.length} contract(s) require attention for renewal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {expiringContracts.map((contract) => (
                  <div key={contract.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <div>
                      <p className="font-medium">{contract.title}</p>
                      <p className="text-sm text-muted-foreground">{contract.supplier}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        Expires: {contract.endDate}
                      </p>
                      <p className="text-xs text-orange-600">
                        {getDaysUntilExpiry(contract.endDate)} days remaining
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>Search Contracts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search contracts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contracts Table */}
        <Card>
          <CardHeader>
            <CardTitle>Contracts ({filteredContracts.length})</CardTitle>
            <CardDescription>
              Manage all supplier contracts and agreements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contract ID</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContracts.map((contract) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">{contract.id}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{contract.title}</p>
                        <p className="text-xs text-muted-foreground">{contract.category}</p>
                      </div>
                    </TableCell>
                    <TableCell>{contract.supplier}</TableCell>
                    <TableCell>MWK {contract.value.toLocaleString()}</TableCell>
                    <TableCell>{getTypeLabel(contract.type)}</TableCell>
                    <TableCell>{contract.startDate}</TableCell>
                    <TableCell>
                      <div>
                        <p>{contract.endDate}</p>
                        {getDaysUntilExpiry(contract.endDate) <= 90 && getDaysUntilExpiry(contract.endDate) > 0 && (
                          <p className="text-xs text-orange-600">
                            {getDaysUntilExpiry(contract.endDate)} days left
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(contract.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { FolderTree, DollarSign, Shield, Plus, X } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Category form schema
const categoryFormSchema = z.object({
  // Basic Information
  name: z.string().min(1, "Category name is required").max(100, "Name too long"),
  code: z.string().min(1, "Category code is required").max(20, "Code too long"),
  description: z.string().optional(),
  
  // Hierarchy
  parentCategoryId: z.string().optional().transform(val => val === 'none' ? undefined : val),
  level: z.number().min(0).max(5).default(0),

  // Budget Integration
  budgetCategoryId: z.string().optional().transform(val => val === 'none' ? undefined : val),
  defaultCurrency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  
  // Approval Limits
  approvalLimits: z.array(z.object({
    level: z.number().min(1, "Level must be positive"),
    role: z.string().min(1, "Role is required"),
    minAmount: z.number().min(0, "Minimum amount must be non-negative"),
    maxAmount: z.number().min(0, "Maximum amount must be non-negative"),
    currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  })).default([]),
  
  // Procurement Rules
  requiresQuotes: z.boolean().default(false),
  minimumQuotes: z.number().min(1).max(10).default(3),
  requiresTender: z.boolean().default(false),
  tenderThreshold: z.number().min(0).default(0),
  
  // Compliance
  complianceRequirements: z.array(z.string()).default([]),
  mandatoryDocuments: z.array(z.string()).default([]),
  
  // Supplier Management
  preferredSuppliers: z.array(z.string()).default([]),
  restrictedSuppliers: z.array(z.string()).default([]),
  requiresSupplierApproval: z.boolean().default(false),
  
  // Additional Settings
  isActive: z.boolean().default(true),
  allowSubcategories: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
}).refine((data) => {
  // Validate approval limits
  return data.approvalLimits.every(limit => limit.maxAmount >= limit.minAmount)
}, {
  message: "Maximum amount must be greater than or equal to minimum amount",
  path: ["approvalLimits"],
})

export type CategoryFormData = z.infer<typeof categoryFormSchema>

interface CategoryFormProps {
  initialData?: Partial<CategoryFormData>
  onSubmit: (data: CategoryFormData) => Promise<void>
  isLoading?: boolean
  parentCategories?: Array<{ _id: string; name: string; level: number }>
  budgetCategories?: Array<{ _id: string; name: string }>
  suppliers?: Array<{ _id: string; name: string }>
}

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

const approvalRoles = [
  { value: 'department_head', label: 'Department Head' },
  { value: 'procurement_manager', label: 'Procurement Manager' },
  { value: 'finance_manager', label: 'Finance Manager' },
  { value: 'director', label: 'Director' },
  { value: 'ceo', label: 'CEO' },
]

const mandatoryDocumentOptions = [
  'Purchase Requisition',
  'Quotations',
  'Supplier Evaluation',
  'Budget Approval',
  'Technical Specifications',
  'Quality Certificates',
  'Insurance Documents',
  'Tax Clearance',
  'Business License',
  'Environmental Compliance',
]

export function CategoryForm({ 
  initialData, 
  onSubmit, 
  isLoading = false, 
  parentCategories = [],
  budgetCategories = [],
  suppliers = []
}: CategoryFormProps) {
  const [newTag, setNewTag] = useState("")
  const [newCompliance, setNewCompliance] = useState("")

  const form = useForm({
    resolver: zodResolver(categoryFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      name: '',
      code: '',
      description: '',
      parentCategoryId: 'none',
      level: 0,
      budgetCategoryId: 'none',
      defaultCurrency: 'MWK',
      approvalLimits: [],
      requiresQuotes: false,
      minimumQuotes: 3,
      requiresTender: false,
      tenderThreshold: 0,
      complianceRequirements: [],
      mandatoryDocuments: [],
      preferredSuppliers: [],
      restrictedSuppliers: [],
      requiresSupplierApproval: false,
      isActive: true,
      allowSubcategories: true,
      tags: [],
      notes: '',
      ...initialData
    }
  }) as ReturnType<typeof useForm<CategoryFormData>>

  const handleSubmit = async (data: CategoryFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: "Category has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save category. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Add new approval limit
  const addApprovalLimit = () => {
    const currentLimits = form.getValues("approvalLimits") || []
    form.setValue("approvalLimits", [
      ...currentLimits,
      {
        level: currentLimits.length + 1,
        role: '',
        minAmount: 0,
        maxAmount: 0,
        currency: 'MWK'
      }
    ])
  }

  // Remove approval limit
  const removeApprovalLimit = (index: number) => {
    const currentLimits = form.getValues("approvalLimits") || []
    form.setValue("approvalLimits", currentLimits.filter((_, i) => i !== index))
  }

  // Add new tag
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues("tags") || []
      if (!currentTags.includes(newTag.trim())) {
        form.setValue("tags", [...currentTags, newTag.trim()])
      }
      setNewTag("")
    }
  }

  // Remove tag
  const removeTag = (index: number) => {
    const currentTags = form.getValues("tags") || []
    form.setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  // Add compliance requirement
  const addCompliance = () => {
    if (newCompliance.trim()) {
      const currentCompliance = form.getValues("complianceRequirements") || []
      if (!currentCompliance.includes(newCompliance.trim())) {
        form.setValue("complianceRequirements", [...currentCompliance, newCompliance.trim()])
      }
      setNewCompliance("")
    }
  }

  // Remove compliance requirement
  const removeCompliance = (index: number) => {
    const currentCompliance = form.getValues("complianceRequirements") || []
    form.setValue("complianceRequirements", currentCompliance.filter((_, i) => i !== index))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderTree className="h-5 w-5" />
          {initialData ? 'Edit Category' : 'Create New Category'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update category details and settings' : 'Create a new procurement category with approval workflows'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <FolderTree className="h-5 w-5" />
                Basic Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter category name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category Code *</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., IT-EQUIP" 
                          {...field}
                          onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        />
                      </FormControl>
                      <FormDescription>
                        Unique identifier for this category
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the category purpose and scope"
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="parentCategoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Parent Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select parent category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Parent (Root Category)</SelectItem>
                          {parentCategories.map((category) => (
                            <SelectItem key={category._id} value={category._id}>
                              {'  '.repeat(category.level)}{category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="budgetCategoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Link to budget category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Budget Link</SelectItem>
                          {budgetCategories.map((category) => (
                            <SelectItem key={category._id} value={category._id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="defaultCurrency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {currencies.map((currency) => (
                            <SelectItem key={currency.value} value={currency.value}>
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Approval Limits */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Approval Limits
                </h3>
                <Button type="button" onClick={addApprovalLimit} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Limit
                </Button>
              </div>

              {(form.watch("approvalLimits") || []).map((_, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium">Approval Level {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeApprovalLimit(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <FormField
                      control={form.control}
                      name={`approvalLimits.${index}.role`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Role</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {approvalRoles.map((role) => (
                                <SelectItem key={role.value} value={role.value}>
                                  {role.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`approvalLimits.${index}.minAmount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Min Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`approvalLimits.${index}.maxAmount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Max Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`approvalLimits.${index}.currency`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Currency</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {currencies.map((currency) => (
                                <SelectItem key={currency.value} value={currency.value}>
                                  {currency.value}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`approvalLimits.${index}.level`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Level</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </Card>
              ))}

              {(form.watch("approvalLimits") || []).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Shield className="h-8 w-8 mx-auto mb-2" />
                  <p>No approval limits defined yet.</p>
                  <p className="text-sm">Click "Add Limit" to define approval workflows.</p>
                </div>
              )}
            </div>

            {/* Procurement Rules */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Procurement Rules
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="requiresQuotes"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Requires Quotes</FormLabel>
                          <FormDescription>
                            Procurement in this category requires multiple quotes
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {form.watch("requiresQuotes") && (
                    <FormField
                      control={form.control}
                      name="minimumQuotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Number of Quotes</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              max="10"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 3)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="requiresTender"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Requires Tender Process</FormLabel>
                          <FormDescription>
                            High-value procurement requires formal tender
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {form.watch("requiresTender") && (
                    <FormField
                      control={form.control}
                      name="tenderThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tender Threshold Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>
                            Amount above which tender process is required
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Compliance Requirements */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Compliance & Documentation</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <FormLabel>Compliance Requirements</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add compliance requirement"
                      value={newCompliance}
                      onChange={(e) => setNewCompliance(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCompliance())}
                    />
                    <Button type="button" onClick={addCompliance} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {(form.watch("complianceRequirements") || []).map((requirement, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                        <span className="flex-1 text-sm">{requirement}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCompliance(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <FormLabel>Mandatory Documents</FormLabel>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {mandatoryDocumentOptions.map((doc) => (
                      <FormField
                        key={doc}
                        control={form.control}
                        name="mandatoryDocuments"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(doc)}
                                onCheckedChange={(checked) => {
                                  const current = field.value || []
                                  if (checked) {
                                    field.onChange([...current, doc])
                                  } else {
                                    field.onChange(current.filter((item) => item !== doc))
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {doc}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Category Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Category Settings</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active Category</FormLabel>
                        <FormDescription>
                          Category is available for use
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowSubcategories"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Allow Subcategories</FormLabel>
                        <FormDescription>
                          Can have child categories
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requiresSupplierApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Supplier Approval Required</FormLabel>
                        <FormDescription>
                          Suppliers must be pre-approved
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-3">
              <FormLabel>Tags</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tags for categorization"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {(form.watch("tags") || []).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 hover:bg-transparent"
                      onClick={() => removeTag(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes or comments about this category"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end pt-6">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : initialData ? "Update Category" : "Create Category"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

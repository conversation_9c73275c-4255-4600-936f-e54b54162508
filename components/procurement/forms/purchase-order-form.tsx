"use client"

import { useState, useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CalendarIcon, Plus, X, FileText, ShoppingCart, Truck, CreditCard, Import, CheckCircle, AlertCircle, Circle } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"

// Purchase order item schema
const purchaseOrderItemSchema = z.object({
  requisitionItemId: z.string().optional(),
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unit: z.string().min(1, "Unit is required"),
  unitPrice: z.number().min(0, "Unit price must be non-negative"),
  totalPrice: z.number().min(0, "Total price must be non-negative"),
  tax: z.number().min(0, "Tax must be non-negative").default(0),
  discount: z.number().min(0, "Discount must be non-negative").default(0),
  budgetCode: z.string().optional(),
  accountCode: z.string().optional(),
  notes: z.string().optional(),
})

// Main purchase order form schema
const purchaseOrderFormSchema = z.object({
  // Basic Information
  orderNumber: z.string().min(1, "Order number is required"),
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().optional(),
  type: z.enum(['goods', 'services', 'maintenance', 'emergency', 'blanket']).default('goods'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  
  // References
  requisitionId: z.string().optional(),
  contractId: z.string().optional(),
  supplierId: z.string().min(1, "Supplier is required"),
  
  // Dates
  orderDate: z.date({ required_error: "Order date is required" }),
  requiredDate: z.date({ required_error: "Required date is required" }),
  expectedDeliveryDate: z.date().optional(),
  
  // Items
  items: z.array(purchaseOrderItemSchema).min(1, "At least one item is required"),
  
  // Financial
  subtotal: z.number().min(0, "Subtotal must be non-negative").default(0),
  taxAmount: z.number().min(0, "Tax amount must be non-negative").default(0),
  discountAmount: z.number().min(0, "Discount amount must be non-negative").default(0),
  totalAmount: z.number().min(0, "Total amount must be non-negative").default(0),
  currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  
  // Terms and Conditions
  paymentTerms: z.string().optional(),
  shippingTerms: z.string().optional(),
  deliveryTerms: z.enum(['pickup', 'delivery', 'fob_origin', 'fob_destination']).default('delivery'),
  
  // Delivery Information
  deliveryAddress: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().min(1, "Country is required"),
    contactPerson: z.string().optional(),
    contactPhone: z.string().optional(),
  }),
  
  // Additional Information
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  specialInstructions: z.string().optional(),
  
  // Status
  status: z.enum(['draft', 'pending_approval', 'approved', 'sent', 'confirmed', 'partially_received', 'received', 'cancelled']).default('draft'),
}).refine((data) => data.requiredDate >= data.orderDate, {
  message: "Required date must be on or after order date",
  path: ["requiredDate"],
})

type PurchaseOrderFormData = z.infer<typeof purchaseOrderFormSchema>

interface PurchaseOrderFormProps {
  initialData?: Partial<PurchaseOrderFormData>
  onSubmit: (data: PurchaseOrderFormData) => Promise<void>
  isLoading?: boolean
  suppliers?: Array<{ _id: string; name: string; paymentTerms?: string }>
  requisitions?: Array<{ _id: string; title: string; items: any[] }>
  contracts?: Array<{ _id: string; title: string; supplierId: string }>
  currentUser?: { _id: string; name: string; department?: string }
}

const orderTypes = [
  { value: 'goods', label: 'Goods Purchase' },
  { value: 'services', label: 'Services' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'emergency', label: 'Emergency Purchase' },
  { value: 'blanket', label: 'Blanket Order' },
]

const priorities = [
  { value: 'low', label: 'Low Priority', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Medium Priority', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'High Priority', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' },
]

const deliveryTermsOptions = [
  { value: 'pickup', label: 'Customer Pickup' },
  { value: 'delivery', label: 'Supplier Delivery' },
  { value: 'fob_origin', label: 'FOB Origin' },
  { value: 'fob_destination', label: 'FOB Destination' },
]

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

const units = [
  'pieces', 'units', 'boxes', 'packages', 'sets', 'pairs',
  'meters', 'liters', 'kilograms', 'hours', 'days', 'months'
]

// Step validation schemas
const basicInfoSchema = z.object({
  title: z.string().min(1, "Title is required"),
  orderNumber: z.string().min(1, "Order number is required"),
  supplierId: z.string().min(1, "Supplier is required"),
  orderDate: z.date({ required_error: "Order date is required" }),
  requiredDate: z.date({ required_error: "Required date is required" }),
}).refine((data) => data.requiredDate >= data.orderDate, {
  message: "Required date must be on or after order date",
  path: ["requiredDate"],
})

const itemsSchema = z.object({
  items: z.array(purchaseOrderItemSchema).min(1, "At least one item is required"),
})

const deliverySchema = z.object({
  deliveryAddress: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    country: z.string().min(1, "Country is required"),
  }),
})

// Step completion status type
type StepStatus = 'incomplete' | 'valid' | 'invalid'

interface StepValidation {
  basic: StepStatus
  items: StepStatus
  delivery: StepStatus
  terms: StepStatus
}

export function PurchaseOrderForm({
  initialData,
  onSubmit,
  isLoading = false,
  suppliers = [],
  requisitions = [],
  contracts = [],
  currentUser
}: PurchaseOrderFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [selectedRequisition, setSelectedRequisition] = useState<string>("")
  const [stepValidation, setStepValidation] = useState<StepValidation>({
    basic: 'incomplete',
    items: 'incomplete',
    delivery: 'incomplete',
    terms: 'incomplete'
  })
  const [attemptedSteps, setAttemptedSteps] = useState<Set<string>>(new Set(['basic']))

  const form = useForm({
    resolver: zodResolver(purchaseOrderFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      orderNumber: '',
      title: '',
      description: '',
      type: 'goods',
      priority: 'medium',
      requisitionId: '',
      contractId: '',
      supplierId: '',
      orderDate: new Date(),
      requiredDate: new Date(new Date().setDate(new Date().getDate() + 14)),
      expectedDeliveryDate: new Date(new Date().setDate(new Date().getDate() + 21)),
      items: [{
        requisitionItemId: '',
        name: '',
        description: '',
        quantity: 1,
        unit: 'pieces',
        unitPrice: 0,
        totalPrice: 0,
        tax: 0,
        discount: 0,
        budgetCode: '',
        accountCode: '',
        notes: '',
      }],
      subtotal: 0,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 0,
      currency: 'MWK',
      paymentTerms: '',
      shippingTerms: '',
      deliveryTerms: 'delivery',
      deliveryAddress: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'Malawi',
        contactPerson: '',
        contactPhone: '',
      },
      notes: '',
      internalNotes: '',
      specialInstructions: '',
      status: 'draft',
      ...initialData
    }
  }) as ReturnType<typeof useForm<PurchaseOrderFormData>>

  const { fields, append, remove, replace } = useFieldArray({
    control: form.control,
    name: "items"
  })

  // Step validation functions
  const validateStep = async (step: string): Promise<boolean> => {
    const formData = form.getValues()

    try {
      switch (step) {
        case 'basic':
          await basicInfoSchema.parseAsync({
            title: formData.title,
            orderNumber: formData.orderNumber,
            supplierId: formData.supplierId,
            orderDate: formData.orderDate,
            requiredDate: formData.requiredDate,
          })
          return true

        case 'items':
          await itemsSchema.parseAsync({
            items: formData.items,
          })
          // Additional validation for items
          const hasValidItems = formData.items.every(item =>
            item.name && item.name.trim() !== '' &&
            item.quantity > 0 &&
            item.unitPrice >= 0 &&
            item.unit && item.unit.trim() !== ''
          )
          return hasValidItems

        case 'delivery':
          await deliverySchema.parseAsync({
            deliveryAddress: formData.deliveryAddress,
          })
          return true

        case 'terms':
          // Terms step is optional, always valid
          return true

        default:
          return false
      }
    } catch (error) {
      return false
    }
  }

  // Update step validation status
  const updateStepValidation = async () => {
    const newValidation: StepValidation = {
      basic: await validateStep('basic') ? 'valid' : 'invalid',
      items: await validateStep('items') ? 'valid' : 'invalid',
      delivery: await validateStep('delivery') ? 'valid' : 'invalid',
      terms: await validateStep('terms') ? 'valid' : 'invalid',
    }

    setStepValidation(newValidation)
  }

  // Handle step navigation with validation
  const navigateToStep = async (targetStep: string) => {
    const steps = ['basic', 'items', 'delivery', 'terms']
    const currentIndex = steps.indexOf(activeTab)
    const targetIndex = steps.indexOf(targetStep)

    // If moving forward, validate current step
    if (targetIndex > currentIndex) {
      const isCurrentStepValid = await validateStep(activeTab)

      if (!isCurrentStepValid) {
        // Trigger form validation to show errors
        await form.trigger()

        // Mark this step as attempted
        setAttemptedSteps(prev => new Set([...prev, activeTab]))

        toast({
          title: "Validation Error",
          description: `Please complete all required fields in the ${getStepLabel(activeTab)} step before proceeding.`,
          variant: "destructive",
        })
        return
      }
    }

    // Mark target step as attempted
    setAttemptedSteps(prev => new Set([...prev, targetStep]))
    setActiveTab(targetStep)
  }

  // Get step label for display
  const getStepLabel = (step: string): string => {
    const labels = {
      basic: 'Basic Information',
      items: 'Items',
      delivery: 'Delivery',
      terms: 'Terms & Conditions'
    }
    return labels[step as keyof typeof labels] || step
  }

  // Get step icon based on validation status
  const getStepIcon = (step: string) => {
    const isAttempted = attemptedSteps.has(step)
    const status = stepValidation[step as keyof StepValidation]

    if (!isAttempted) {
      return <Circle className="h-4 w-4 text-muted-foreground" />
    }

    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'invalid':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Circle className="h-4 w-4 text-muted-foreground" />
    }
  }

  // Update validation status when form values change
  useEffect(() => {
    const subscription = form.watch(() => {
      updateStepValidation()
    })
    return () => subscription.unsubscribe()
  }, [form])

  // Initial validation update
  useEffect(() => {
    updateStepValidation()
  }, [])

  const handleSubmit = async (data: PurchaseOrderFormData) => {
    try {
      // Validate all steps before submission
      const allStepsValid = await Promise.all([
        validateStep('basic'),
        validateStep('items'),
        validateStep('delivery'),
        validateStep('terms')
      ])

      if (!allStepsValid.every(Boolean)) {
        toast({
          title: "Validation Error",
          description: "Please complete all required fields in all steps before submitting.",
          variant: "destructive",
        })
        return
      }

      await onSubmit(data)
      toast({
        title: "Success",
        description: "Purchase order has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save purchase order. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Generate order number
  const generateOrderNumber = () => {
    const supplier = suppliers.find(s => s._id === form.getValues("supplierId"))?.name || "SUP"
    const id = `PO-${supplier.substring(0, 3).toUpperCase()}-${Date.now().toString().slice(-6)}`
    form.setValue("orderNumber", id)
  }

  // Import items from requisition
  const importFromRequisition = () => {
    const requisition = requisitions.find(r => r._id === selectedRequisition)
    if (requisition && requisition.items) {
      const importedItems = requisition.items.map(item => ({
        requisitionItemId: item._id || '',
        name: item.name || '',
        description: item.description || '',
        quantity: item.quantity || 1,
        unit: item.unit || 'pieces',
        unitPrice: item.estimatedUnitPrice || 0,
        totalPrice: (item.quantity || 1) * (item.estimatedUnitPrice || 0),
        tax: 0,
        discount: 0,
        budgetCode: '',
        accountCode: '',
        notes: item.notes || '',
      }))
      replace(importedItems)
      form.setValue("requisitionId", selectedRequisition)
      form.setValue("title", `PO for ${requisition.title}`)
      calculateTotals()
      toast({
        title: "Success",
        description: `Imported ${importedItems.length} items from requisition.`,
      })
    }
  }

  // Calculate item total
  const calculateItemTotal = (index: number) => {
    const quantity = form.watch(`items.${index}.quantity`)
    const unitPrice = form.watch(`items.${index}.unitPrice`)
    const discount = form.watch(`items.${index}.discount`) || 0
    const tax = form.watch(`items.${index}.tax`) || 0
    
    const subtotal = quantity * unitPrice
    const discountAmount = subtotal * (discount / 100)
    const taxableAmount = subtotal - discountAmount
    const taxAmount = taxableAmount * (tax / 100)
    const total = taxableAmount + taxAmount
    
    form.setValue(`items.${index}.totalPrice`, total)
    calculateTotals()
  }

  // Calculate grand totals
  const calculateTotals = () => {
    const items = form.getValues("items")
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    const totalDiscount = items.reduce((sum, item) => {
      const itemSubtotal = item.quantity * item.unitPrice
      return sum + (itemSubtotal * ((item.discount || 0) / 100))
    }, 0)
    const taxableAmount = subtotal - totalDiscount
    const totalTax = items.reduce((sum, item) => {
      const itemSubtotal = item.quantity * item.unitPrice
      const itemDiscount = itemSubtotal * ((item.discount || 0) / 100)
      const itemTaxable = itemSubtotal - itemDiscount
      return sum + (itemTaxable * ((item.tax || 0) / 100))
    }, 0)
    const total = taxableAmount + totalTax
    
    form.setValue("subtotal", subtotal)
    form.setValue("discountAmount", totalDiscount)
    form.setValue("taxAmount", totalTax)
    form.setValue("totalAmount", total)
  }

  // Add new item
  const addItem = () => {
    append({
      requisitionItemId: '',
      name: '',
      description: '',
      quantity: 1,
      unit: 'pieces',
      unitPrice: 0,
      totalPrice: 0,
      tax: 0,
      discount: 0,
      budgetCode: '',
      accountCode: '',
      notes: '',
    })
  }

  // Update supplier-related fields when supplier changes
  const handleSupplierChange = (supplierId: string) => {
    const supplier = suppliers.find(s => s._id === supplierId)
    if (supplier) {
      form.setValue("supplierId", supplierId)
      if (supplier.paymentTerms) {
        form.setValue("paymentTerms", supplier.paymentTerms)
      }
      // Filter contracts for this supplier
      const supplierContracts = contracts.filter(c => c.supplierId === supplierId)
      if (supplierContracts.length === 1) {
        form.setValue("contractId", supplierContracts[0]._id)
      }
    }
  }

  return (
    <Card className="w-full max-w-5xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          {initialData ? 'Edit Purchase Order' : 'Create New Purchase Order'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update purchase order details and items' : 'Create a new purchase order from requisitions or manually'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={navigateToStep} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger
                  value="basic"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.basic === 'valid' && "text-green-600",
                    stepValidation.basic === 'invalid' && attemptedSteps.has('basic') && "text-red-600"
                  )}
                >
                  {getStepIcon('basic')}
                  Basic Info
                </TabsTrigger>
                <TabsTrigger
                  value="items"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.items === 'valid' && "text-green-600",
                    stepValidation.items === 'invalid' && attemptedSteps.has('items') && "text-red-600"
                  )}
                >
                  {getStepIcon('items')}
                  Items
                </TabsTrigger>
                <TabsTrigger
                  value="delivery"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.delivery === 'valid' && "text-green-600",
                    stepValidation.delivery === 'invalid' && attemptedSteps.has('delivery') && "text-red-600"
                  )}
                >
                  {getStepIcon('delivery')}
                  Delivery
                </TabsTrigger>
                <TabsTrigger
                  value="terms"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.terms === 'valid' && "text-green-600",
                    stepValidation.terms === 'invalid' && attemptedSteps.has('terms') && "text-red-600"
                  )}
                >
                  {getStepIcon('terms')}
                  Terms
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.basic === 'invalid' && attemptedSteps.has('basic') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete all required fields in this step before proceeding.
                    </AlertDescription>
                  </Alert>
                )}

                {/* Requisition Import Section */}
                {requisitions.length > 0 && (
                  <Card className="p-4 bg-blue-50 border-blue-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium flex items-center gap-2">
                        <Import className="h-4 w-4" />
                        Import from Requisition
                      </h4>
                      <Button
                        type="button"
                        size="sm"
                        onClick={importFromRequisition}
                        disabled={!selectedRequisition}
                      >
                        Import Items
                      </Button>
                    </div>
                    <Select value={selectedRequisition} onValueChange={setSelectedRequisition}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a requisition to import items" />
                      </SelectTrigger>
                      <SelectContent>
                        {requisitions.map((req) => (
                          <SelectItem key={req._id} value={req._id}>
                            {req.title} ({req.items?.length || 0} items)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </Card>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Purchase Order Title *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter PO title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="orderNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order Number *</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input placeholder="PO-XXX-XXXXXX" {...field} />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={generateOrderNumber}
                          >
                            Generate
                          </Button>
                        </div>
                        <FormDescription>
                          Unique identifier for this purchase order
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Brief description of the purchase order"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {orderTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {priorities.map((priority) => (
                              <SelectItem key={priority.value} value={priority.value}>
                                <div className="flex items-center gap-2">
                                  <Badge className={priority.color}>
                                    {priority.label}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.value} value={currency.value}>
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier *</FormLabel>
                        <Select onValueChange={handleSupplierChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {suppliers.map((supplier) => (
                              <SelectItem key={supplier._id} value={supplier._id}>
                                {supplier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contractId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contract Reference</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select contract (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="no-contract">No Contract</SelectItem>
                            {contracts
                              .filter(c => c.supplierId === form.watch("supplierId"))
                              .map((contract) => (
                                <SelectItem key={contract._id} value={contract._id}>
                                  {contract.title}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="orderDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Order Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requiredDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Required Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          When do you need these items?
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expectedDeliveryDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Expected Delivery</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          Expected delivery date
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Items Tab */}
              <TabsContent value="items" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.items === 'invalid' && attemptedSteps.has('items') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please add at least one item with valid details (name, quantity, unit price, and unit).
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Purchase Order Items
                  </h3>
                  <Button type="button" onClick={addItem} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Item Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter item name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.unit`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unit *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {units.map((unit) => (
                                  <SelectItem key={unit} value={unit}>
                                    {unit}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`items.${index}.description`}
                      render={({ field }) => (
                        <FormItem className="mb-4">
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Detailed description of the item"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Quantity *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseInt(e.target.value) || 1)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.unitPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unit Price *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseFloat(e.target.value) || 0)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.discount`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Discount %</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                max="100"
                                placeholder="0"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseFloat(e.target.value) || 0)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.tax`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tax %</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                max="100"
                                placeholder="0"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseFloat(e.target.value) || 0)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.totalPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total Price</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                readOnly
                                className="bg-muted"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.budgetCode`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Budget Code</FormLabel>
                            <FormControl>
                              <Input placeholder="Budget code" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`items.${index}.notes`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Item Notes</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any specific notes for this item"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </Card>
                ))}

                {/* Financial Summary */}
                <Card className="p-4 bg-muted/50">
                  <h4 className="font-medium mb-3">Financial Summary</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Subtotal:</span>
                      <div className="font-medium">
                        {form.watch("currency")} {form.watch("subtotal")?.toLocaleString() || '0.00'}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Discount:</span>
                      <div className="font-medium text-green-600">
                        -{form.watch("currency")} {form.watch("discountAmount")?.toLocaleString() || '0.00'}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tax:</span>
                      <div className="font-medium">
                        {form.watch("currency")} {form.watch("taxAmount")?.toLocaleString() || '0.00'}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total:</span>
                      <div className="text-lg font-bold">
                        {form.watch("currency")} {form.watch("totalAmount")?.toLocaleString() || '0.00'}
                      </div>
                    </div>
                  </div>
                </Card>
              </TabsContent>

              {/* Delivery Tab */}
              <TabsContent value="delivery" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.delivery === 'invalid' && attemptedSteps.has('delivery') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete all required delivery address fields (street, city, country).
                    </AlertDescription>
                  </Alert>
                )}

                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Delivery Information
                </h3>

                <FormField
                  control={form.control}
                  name="deliveryTerms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Delivery Terms</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {deliveryTermsOptions.map((term) => (
                            <SelectItem key={term.value} value={term.value}>
                              {term.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <h4 className="font-medium">Delivery Address</h4>

                  <FormField
                    control={form.control}
                    name="deliveryAddress.street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter street address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="deliveryAddress.city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter city" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deliveryAddress.state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Province</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter state or province" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="deliveryAddress.postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter postal code" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deliveryAddress.country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter country" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="deliveryAddress.contactPerson"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Person</FormLabel>
                          <FormControl>
                            <Input placeholder="Contact person name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deliveryAddress.contactPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="Contact phone number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="specialInstructions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Special Delivery Instructions</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any special instructions for delivery"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Terms Tab */}
              <TabsContent value="terms" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Terms & Conditions
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="paymentTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="e.g., Net 30 days, 2/10 net 30"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Payment terms and conditions
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shippingTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shipping Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="e.g., FOB Origin, CIF, DDP"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Shipping and delivery terms
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>General Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes or terms"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="internalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Internal Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes (not visible to supplier)"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        These notes are for internal use only and will not be shared with the supplier
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status and Approval */}
                <Card className="p-4 bg-blue-50 border-blue-200">
                  <h4 className="font-medium mb-3">Status & Approval</h4>
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="pending_approval">Pending Approval</SelectItem>
                            <SelectItem value="approved">Approved</SelectItem>
                            <SelectItem value="sent">Sent to Supplier</SelectItem>
                            <SelectItem value="confirmed">Confirmed by Supplier</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Current status of the purchase order
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Card>
              </TabsContent>
            </Tabs>

            {/* Step validation summary */}
            {Object.values(stepValidation).some(status => status === 'invalid') && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please complete all required fields in the highlighted steps before submitting.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const tabs = ["basic", "items", "delivery", "terms"]
                  const currentIndex = tabs.indexOf(activeTab)
                  if (currentIndex > 0) {
                    setActiveTab(tabs[currentIndex - 1])
                  }
                }}
                disabled={activeTab === "basic"}
              >
                Previous
              </Button>

              {activeTab === "terms" ? (
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => form.setValue("status", "draft")}
                  >
                    Save as Draft
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : form.watch("status") === "draft" ? "Submit for Approval" : "Update Purchase Order"}
                  </Button>
                </div>
              ) : (
                <Button
                  type="button"
                  onClick={async () => {
                    const tabs = ["basic", "items", "delivery", "terms"]
                    const currentIndex = tabs.indexOf(activeTab)
                    if (currentIndex < tabs.length - 1) {
                      await navigateToStep(tabs[currentIndex + 1])
                    }
                  }}
                >
                  Next
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

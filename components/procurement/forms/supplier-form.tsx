"use client"

import { useState } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Building2, Contact, CreditCard, FileText, Plus, X, Star, Globe } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Supplier form schema
const supplierFormSchema = z.object({
  // Basic Information
  name: z.string().min(1, "Supplier name is required").max(100, "Name too long"),
  supplierId: z.string().min(1, "Supplier ID is required").max(20, "ID too long"),
  category: z.array(z.string()).min(1, "At least one category is required"),
  status: z.enum(['active', 'inactive', 'pending', 'preferred', 'blacklisted']).default('pending'),
  
  // Contact Information
  contactPerson: z.string().min(1, "Contact person is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().min(1, "Phone number is required"),
  website: z.string().url("Valid website URL").optional().or(z.literal("")),
  
  // Address Information
  address: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().min(1, "Country is required"),
  }),
  
  // Business Information
  taxId: z.string().optional(),
  businessLicense: z.string().optional(),
  registrationNumber: z.string().optional(),
  
  // Financial Information
  paymentTerms: z.string().optional(),
  currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  creditLimit: z.number().min(0, "Credit limit must be non-negative").optional(),
  
  // Banking Details
  bankDetails: z.object({
    bankName: z.string().optional(),
    accountNumber: z.string().optional(),
    branchCode: z.string().optional(),
    swiftCode: z.string().optional(),
  }).optional(),
  
  // Performance & Rating
  rating: z.number().min(0).max(5).default(0),
  leadTime: z.string().optional(),
  minimumOrderValue: z.number().min(0, "Minimum order value must be non-negative").optional(),
  
  // Certifications & Compliance
  certifications: z.array(z.object({
    name: z.string(),
    issuedBy: z.string(),
    validUntil: z.date().optional(),
    documentUrl: z.string().optional(),
  })).default([]),
  
  // Insurance Information
  insurance: z.object({
    provider: z.string().optional(),
    policyNumber: z.string().optional(),
    coverage: z.number().optional(),
    validUntil: z.date().optional(),
  }).optional(),
  
  // Additional Information
  specializations: z.array(z.string()).default([]),
  servicesOffered: z.array(z.string()).default([]),
  qualityStandards: z.array(z.string()).default([]),
  notes: z.string().optional(),
  
  // Preferences
  preferredCommunication: z.enum(['email', 'phone', 'both']).default('email'),
  emergencyContact: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().optional(),
  }).optional(),
})

type SupplierFormData = z.infer<typeof supplierFormSchema>

interface SupplierFormProps {
  initialData?: Partial<SupplierFormData>
  onSubmit: (data: SupplierFormData) => Promise<void>
  isLoading?: boolean
  categories?: Array<{ value: string; label: string }>
}

const supplierStatuses = [
  { value: 'pending', label: 'Pending Approval' },
  { value: 'active', label: 'Active' },
  { value: 'preferred', label: 'Preferred Supplier' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'blacklisted', label: 'Blacklisted' },
]

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

const communicationPreferences = [
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'both', label: 'Both Email & Phone' },
]

const defaultCategories = [
  { value: 'electronics', label: 'Electronics' },
  { value: 'office_supplies', label: 'Office Supplies' },
  { value: 'construction', label: 'Construction Materials' },
  { value: 'services', label: 'Professional Services' },
  { value: 'maintenance', label: 'Maintenance & Repair' },
  { value: 'catering', label: 'Catering & Food Services' },
  { value: 'transportation', label: 'Transportation' },
  { value: 'consulting', label: 'Consulting Services' },
]

export function SupplierForm({ 
  initialData, 
  onSubmit, 
  isLoading = false, 
  categories = defaultCategories 
}: SupplierFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [newSpecialization, setNewSpecialization] = useState("")
  const [newService, setNewService] = useState("")
  const [newQualityStandard, setNewQualityStandard] = useState("")

  const form = useForm({
    resolver: zodResolver(supplierFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      name: '',
      supplierId: '',
      category: [],
      status: 'pending',
      contactPerson: '',
      email: '',
      phone: '',
      website: '',
      address: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'Malawi',
      },
      taxId: '',
      businessLicense: '',
      registrationNumber: '',
      paymentTerms: '',
      currency: 'MWK',
      creditLimit: 0,
      bankDetails: {
        bankName: '',
        accountNumber: '',
        branchCode: '',
        swiftCode: '',
      },
      rating: 0,
      leadTime: '',
      minimumOrderValue: 0,
      certifications: [],
      insurance: {
        provider: '',
        policyNumber: '',
        coverage: 0,
        validUntil: undefined,
      },
      specializations: [],
      servicesOffered: [],
      qualityStandards: [],
      notes: '',
      preferredCommunication: 'email',
      emergencyContact: {
        name: '',
        phone: '',
        email: '',
      },
      ...initialData
    }
  }) as ReturnType<typeof useForm<SupplierFormData>>

  const { fields: certificationFields, append: appendCertification, remove: removeCertification } = useFieldArray({
    control: form.control,
    name: "certifications"
  })

  const handleSubmit = async (data: SupplierFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: "Supplier has been saved successfully.",
      })
    } catch (error) {
      console.error('Supplier form submission error:', error)

      // Extract error message from different error types
      let errorMessage = "Failed to save supplier. Please try again."

      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = (error as any).message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  // Generate supplier ID
  const generateSupplierId = () => {
    const name = form.getValues("name")
    if (name) {
      const id = `SUP-${name.substring(0, 3).toUpperCase()}-${Date.now().toString().slice(-4)}`
      form.setValue("supplierId", id)
    }
  }

  // Add specialization
  const addSpecialization = () => {
    if (newSpecialization.trim()) {
      const current = form.getValues("specializations") || []
      if (!current.includes(newSpecialization.trim())) {
        form.setValue("specializations", [...current, newSpecialization.trim()])
      }
      setNewSpecialization("")
    }
  }

  // Remove specialization
  const removeSpecialization = (index: number) => {
    const current = form.getValues("specializations") || []
    form.setValue("specializations", current.filter((_, i) => i !== index))
  }

  // Add service
  const addService = () => {
    if (newService.trim()) {
      const current = form.getValues("servicesOffered") || []
      if (!current.includes(newService.trim())) {
        form.setValue("servicesOffered", [...current, newService.trim()])
      }
      setNewService("")
    }
  }

  // Remove service
  const removeService = (index: number) => {
    const current = form.getValues("servicesOffered") || []
    form.setValue("servicesOffered", current.filter((_, i) => i !== index))
  }

  // Add quality standard
  const addQualityStandard = () => {
    if (newQualityStandard.trim()) {
      const current = form.getValues("qualityStandards") || []
      if (!current.includes(newQualityStandard.trim())) {
        form.setValue("qualityStandards", [...current, newQualityStandard.trim()])
      }
      setNewQualityStandard("")
    }
  }

  // Remove quality standard
  const removeQualityStandard = (index: number) => {
    const current = form.getValues("qualityStandards") || []
    form.setValue("qualityStandards", current.filter((_, i) => i !== index))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          {initialData ? 'Edit Supplier' : 'Register New Supplier'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update supplier information and settings' : 'Register a new supplier with complete business details'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="contact" className="flex items-center gap-2">
                  <Contact className="h-4 w-4" />
                  Contact & Address
                </TabsTrigger>
                <TabsTrigger value="financial" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Financial
                </TabsTrigger>
                <TabsTrigger value="additional" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Additional
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter supplier name"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e)
                              if (!form.getValues("supplierId")) {
                                generateSupplierId()
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier ID *</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input placeholder="SUP-XXX-XXXX" {...field} />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={generateSupplierId}
                          >
                            Generate
                          </Button>
                        </div>
                        <FormDescription>
                          Unique identifier for this supplier
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="category"
                  render={() => (
                    <FormItem>
                      <FormLabel>Categories *</FormLabel>
                      <FormDescription>
                        Select all categories that apply to this supplier
                      </FormDescription>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {categories.map((category) => (
                          <FormField
                            key={category.value}
                            control={form.control}
                            name="category"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(category.value)}
                                    onCheckedChange={(checked) => {
                                      const current = field.value || []
                                      if (checked) {
                                        field.onChange([...current, category.value])
                                      } else {
                                        field.onChange(current.filter((item) => item !== category.value))
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {category.label}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {supplierStatuses.map((status) => (
                              <SelectItem key={status.value} value={status.value}>
                                {status.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="rating"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rating</FormLabel>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="5"
                              step="0.1"
                              placeholder="0.0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-4 w-4 ${
                                  star <= Math.floor(field.value || 0)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <FormDescription>
                          Supplier performance rating (0-5 stars)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="taxId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax ID</FormLabel>
                        <FormControl>
                          <Input placeholder="Tax identification number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="businessLicense"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business License</FormLabel>
                        <FormControl>
                          <Input placeholder="Business license number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="registrationNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Registration Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Company registration number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Contact & Address Tab */}
              <TabsContent value="contact" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Contact className="h-5 w-5" />
                  Contact Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="contactPerson"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Person *</FormLabel>
                        <FormControl>
                          <Input placeholder="Primary contact name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address *</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="+265 123 456 789" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <div className="flex">
                          <Globe className="h-4 w-4 mt-3 mr-2 text-muted-foreground" />
                          <FormControl>
                            <Input placeholder="https://www.supplier.com" {...field} />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Business Address</h4>

                  <FormField
                    control={form.control}
                    name="address.street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address *</FormLabel>
                        <FormControl>
                          <Input placeholder="Street address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <FormField
                      control={form.control}
                      name="address.city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City *</FormLabel>
                          <FormControl>
                            <Input placeholder="City" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Region</FormLabel>
                          <FormControl>
                            <Input placeholder="State or region" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code</FormLabel>
                          <FormControl>
                            <Input placeholder="Postal code" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country *</FormLabel>
                          <FormControl>
                            <Input placeholder="Country" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Emergency Contact</h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="emergencyContact.name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Emergency Contact Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Emergency contact" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContact.phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Emergency Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="Emergency phone" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContact.email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Emergency Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="Emergency email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="preferredCommunication"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Communication Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {communicationPreferences.map((pref) => (
                            <SelectItem key={pref.value} value={pref.value}>
                              {pref.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Financial Tab */}
              <TabsContent value="financial" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Financial Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.value} value={currency.value}>
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="creditLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Credit Limit</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="minimumOrderValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Order Value</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="paymentTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="e.g., Net 30 days, 2/10 net 30"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="leadTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lead Time</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="e.g., 5-7 business days, 2-3 weeks"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Banking Details</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="bankDetails.bankName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bank Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Bank name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bankDetails.accountNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Account Number</FormLabel>
                          <FormControl>
                            <Input placeholder="Account number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="bankDetails.branchCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Branch Code</FormLabel>
                          <FormControl>
                            <Input placeholder="Branch code" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bankDetails.swiftCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SWIFT Code</FormLabel>
                          <FormControl>
                            <Input placeholder="SWIFT/BIC code" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Additional Information Tab */}
              <TabsContent value="additional" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Additional Information
                </h3>

                {/* Specializations */}
                <div className="space-y-3">
                  <FormLabel>Specializations</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add specialization"
                      value={newSpecialization}
                      onChange={(e) => setNewSpecialization(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialization())}
                    />
                    <Button type="button" onClick={addSpecialization} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {(form.watch("specializations") || []).map((spec, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {spec}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => removeSpecialization(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Services Offered */}
                <div className="space-y-3">
                  <FormLabel>Services Offered</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add service"
                      value={newService}
                      onChange={(e) => setNewService(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addService())}
                    />
                    <Button type="button" onClick={addService} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {form.watch("servicesOffered").map((service, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {service}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => removeService(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Quality Standards */}
                <div className="space-y-3">
                  <FormLabel>Quality Standards & Certifications</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add quality standard"
                      value={newQualityStandard}
                      onChange={(e) => setNewQualityStandard(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addQualityStandard())}
                    />
                    <Button type="button" onClick={addQualityStandard} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {form.watch("qualityStandards").map((standard, index) => (
                      <Badge key={index} variant="default" className="flex items-center gap-1">
                        {standard}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => removeQualityStandard(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes about this supplier"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const tabs = ["basic", "contact", "financial", "additional"]
                  const currentIndex = tabs.indexOf(activeTab)
                  if (currentIndex > 0) {
                    setActiveTab(tabs[currentIndex - 1])
                  }
                }}
                disabled={activeTab === "basic"}
              >
                Previous
              </Button>
              
              {activeTab === "additional" ? (
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : initialData ? "Update Supplier" : "Register Supplier"}
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={() => {
                    const tabs = ["basic", "contact", "financial", "additional"]
                    const currentIndex = tabs.indexOf(activeTab)
                    if (currentIndex < tabs.length - 1) {
                      setActiveTab(tabs[currentIndex + 1])
                    }
                  }}
                >
                  Next
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

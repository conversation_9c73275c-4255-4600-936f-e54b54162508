"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Package, DollarSign, MapPin, Plus, X, AlertTriangle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Inventory form schema
const inventoryFormSchema = z.object({
  // Basic Information
  name: z.string().min(1, "Item name is required").max(200, "Name too long"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  
  // Stock Information
  currentStock: z.number().min(0, "Stock cannot be negative"),
  minimumStock: z.number().min(0, "Minimum stock cannot be negative"),
  maximumStock: z.number().min(0, "Maximum stock cannot be negative").optional(),
  unit: z.string().min(1, "Unit is required"),
  
  // Financial Information
  unitPrice: z.number().min(0, "Unit price cannot be negative"),
  currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  lastPurchasePrice: z.number().min(0).optional(),
  
  // Location and Storage
  location: z.string().min(1, "Location is required"),
  warehouse: z.string().optional(),
  shelf: z.string().optional(),
  zone: z.string().optional(),
  
  // Status and Tracking
  status: z.enum(['in_stock', 'low_stock', 'out_of_stock', 'on_order', 'discontinued', 'obsolete']).default('in_stock'),
  reorderPoint: z.number().min(0, "Reorder point cannot be negative").default(0),
  reorderQuantity: z.number().min(0, "Reorder quantity cannot be negative").default(0),
  leadTime: z.number().min(0, "Lead time cannot be negative").optional(),
  
  // Quality and Compliance
  qualityGrade: z.enum(['A', 'B', 'C']).optional(),
  expiryDate: z.string().optional(),
  batchNumber: z.string().optional(),
  serialNumbers: z.array(z.string()).default([]),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  
  // Supplier Information
  preferredSuppliers: z.array(z.string()).default([]),
}).refine((data) => {
  // Validate maximum stock is greater than minimum stock
  if (data.maximumStock && data.maximumStock < data.minimumStock) {
    return false;
  }
  return true;
}, {
  message: "Maximum stock must be greater than minimum stock",
  path: ["maximumStock"],
})

export type InventoryFormData = z.infer<typeof inventoryFormSchema>

interface InventoryFormProps {
  initialData?: Partial<InventoryFormData>
  onSubmit: (data: InventoryFormData) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  categories?: Array<{ _id: string; name: string }>
  suppliers?: Array<{ _id: string; name: string }>
}

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

const statusOptions = [
  { value: 'in_stock', label: 'In Stock', color: 'default' },
  { value: 'low_stock', label: 'Low Stock', color: 'secondary' },
  { value: 'out_of_stock', label: 'Out of Stock', color: 'destructive' },
  { value: 'on_order', label: 'On Order', color: 'secondary' },
  { value: 'discontinued', label: 'Discontinued', color: 'secondary' },
  { value: 'obsolete', label: 'Obsolete', color: 'destructive' },
]

const qualityGrades = [
  { value: 'A', label: 'Grade A - Excellent' },
  { value: 'B', label: 'Grade B - Good' },
  { value: 'C', label: 'Grade C - Fair' },
]

const units = [
  'pieces', 'units', 'boxes', 'cartons', 'pallets',
  'kg', 'grams', 'tonnes', 'liters', 'ml',
  'meters', 'cm', 'inches', 'feet',
  'reams', 'packs', 'sets', 'pairs'
]

export function InventoryForm({ 
  initialData, 
  onSubmit, 
  onCancel,
  isLoading = false, 
  categories = [],
  suppliers = []
}: InventoryFormProps) {
  const [newTag, setNewTag] = useState("")
  const [newSerialNumber, setNewSerialNumber] = useState("")

  const form = useForm({
    resolver: zodResolver(inventoryFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      name: '',
      description: '',
      category: '',
      sku: '',
      barcode: '',
      currentStock: 0,
      minimumStock: 0,
      maximumStock: 0,
      unit: 'pieces',
      unitPrice: 0,
      currency: 'MWK',
      lastPurchasePrice: 0,
      location: '',
      warehouse: '',
      shelf: '',
      zone: '',
      status: 'in_stock' as const,
      reorderPoint: 0,
      reorderQuantity: 0,
      leadTime: 0,
      qualityGrade: undefined,
      expiryDate: '',
      batchNumber: '',
      serialNumbers: [],
      tags: [],
      notes: '',
      preferredSuppliers: [],
      ...initialData
    }
  }) as ReturnType<typeof useForm<InventoryFormData>>

  const handleSubmit = async (data: InventoryFormData) => {
    try {
      // Calculate total value
      const processedData = {
        ...data,
        totalValue: data.currentStock * data.unitPrice,
        expiryDate: data.expiryDate ? new Date(data.expiryDate) : undefined,
      }
      
      await onSubmit(processedData)
      toast({
        title: "Success",
        description: "Inventory item has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save inventory item. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Add new tag
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues("tags") || []
      if (!currentTags.includes(newTag.trim())) {
        form.setValue("tags", [...currentTags, newTag.trim()])
      }
      setNewTag("")
    }
  }

  // Remove tag
  const removeTag = (index: number) => {
    const currentTags = form.getValues("tags") || []
    form.setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  // Add serial number
  const addSerialNumber = () => {
    if (newSerialNumber.trim()) {
      const currentSerials = form.getValues("serialNumbers") || []
      if (!currentSerials.includes(newSerialNumber.trim())) {
        form.setValue("serialNumbers", [...currentSerials, newSerialNumber.trim()])
      }
      setNewSerialNumber("")
    }
  }

  // Remove serial number
  const removeSerialNumber = (index: number) => {
    const currentSerials = form.getValues("serialNumbers") || []
    form.setValue("serialNumbers", currentSerials.filter((_, i) => i !== index))
  }

  // Calculate reorder point suggestion
  const calculateReorderPoint = () => {
    const minStock = form.getValues("minimumStock")
    const leadTime = form.getValues("leadTime") || 7
    const suggestedReorderPoint = Math.ceil(minStock * 1.2 + (minStock * leadTime / 30))
    form.setValue("reorderPoint", suggestedReorderPoint)
    toast({
      title: "Reorder Point Calculated",
      description: `Suggested reorder point: ${suggestedReorderPoint} units`,
    })
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {initialData ? 'Edit Inventory Item' : 'Create New Inventory Item'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update inventory item details and stock information' : 'Add a new item to the procurement inventory'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Package className="h-5 w-5" />
                Basic Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Item Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter item name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category._id} value={category._id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the inventory item"
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU</FormLabel>
                      <FormControl>
                        <Input placeholder="Stock Keeping Unit" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="barcode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Barcode</FormLabel>
                      <FormControl>
                        <Input placeholder="Barcode number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Stock Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Package className="h-5 w-5" />
                Stock Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <FormField
                  control={form.control}
                  name="currentStock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Stock *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumStock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Stock *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maximumStock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Stock</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select unit" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {units.map((unit) => (
                            <SelectItem key={unit} value={unit}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="reorderPoint"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reorder Point</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={calculateReorderPoint}
                        >
                          Calculate
                        </Button>
                      </div>
                      <FormDescription>
                        Stock level that triggers reordering
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reorderQuantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reorder Quantity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Quantity to order when restocking
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="leadTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lead Time (days)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Days from order to delivery
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Financial Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Financial Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="unitPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit Price *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {currencies.map((currency) => (
                            <SelectItem key={currency.value} value={currency.value}>
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastPurchasePrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Purchase Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Display calculated total value */}
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Total Value:</span>
                  <span className="text-lg font-bold">
                    {form.watch("currency")} {(form.watch("currentStock") * form.watch("unitPrice")).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Location and Storage */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location and Storage
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location *</FormLabel>
                      <FormControl>
                        <Input placeholder="Storage location" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="warehouse"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Warehouse</FormLabel>
                      <FormControl>
                        <Input placeholder="Warehouse name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shelf"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shelf</FormLabel>
                      <FormControl>
                        <Input placeholder="Shelf number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="zone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Zone</FormLabel>
                      <FormControl>
                        <Input placeholder="Storage zone" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Status and Quality */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Status and Quality
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusOptions.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="qualityGrade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quality Grade</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select grade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {qualityGrades.map((grade) => (
                            <SelectItem key={grade.value} value={grade.value}>
                              {grade.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="expiryDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiry Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="batchNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Batch Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Batch/Lot number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Serial Numbers */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Serial Numbers</h3>

              <div className="flex gap-2">
                <Input
                  placeholder="Add serial number"
                  value={newSerialNumber}
                  onChange={(e) => setNewSerialNumber(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSerialNumber())}
                />
                <Button type="button" onClick={addSerialNumber} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {form.watch("serialNumbers").length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {form.watch("serialNumbers").map((serial, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {serial}
                      <button
                        type="button"
                        onClick={() => removeSerialNumber(index)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tags</h3>

              <div className="flex gap-2">
                <Input
                  placeholder="Add tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {form.watch("tags").length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {form.watch("tags").map((tag, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(index)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this inventory item"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex justify-end gap-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : initialData ? "Update Item" : "Create Item"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

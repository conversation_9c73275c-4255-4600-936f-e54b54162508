// components/procurement/forms/requisition-edit-form.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  Loader2
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { toast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"

// Interfaces
interface RequisitionItem {
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  estimatedUnitPrice: number;
  estimatedTotal: number;
  category?: string;
  specifications?: string;
}

interface Department {
  _id: string;
  name: string;
}

interface Requisition {
  _id: string;
  requisitionNumber: string;
  title: string;
  description: string;
  justification?: string;
  departmentId: {
    _id: string;
    name: string;
  } | null;
  priority: 'high' | 'medium' | 'low' | 'urgent';
  items: RequisitionItem[];
  totalAmount: number;
  urgentDate?: string;
  notes?: string;
  status: string;
}

interface RequisitionEditFormProps {
  requisitionId: string;
}

export function RequisitionEditForm({ requisitionId }: RequisitionEditFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [requisition, setRequisition] = useState<Requisition | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    justification: '',
    departmentId: '',
    priority: 'medium' as 'high' | 'medium' | 'low' | 'urgent',
    urgentDate: '',
    notes: '',
    items: [] as RequisitionItem[]
  });

  // Fetch requisition and departments
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch requisition details
        const reqResponse = await fetch(`/api/procurement/requisition/${requisitionId}`);
        if (!reqResponse.ok) {
          throw new Error('Failed to fetch requisition');
        }
        const reqResult = await reqResponse.json();
        const reqData = reqResult.data;
        
        // Check if requisition can be edited
        if (reqData.status !== 'draft') {
          toast({
            title: 'Cannot Edit',
            description: `Cannot edit requisition with status: ${reqData.status}`,
            variant: 'destructive',
          });
          router.push(`/dashboard/procurement/requisitions/${requisitionId}`);
          return;
        }
        
        setRequisition(reqData);
        
        // Set form data
        setFormData({
          title: reqData.title || '',
          description: reqData.description || '',
          justification: reqData.justification || '',
          departmentId: reqData.departmentId?._id || '',
          priority: reqData.priority || 'medium',
          urgentDate: reqData.urgentDate ? reqData.urgentDate.split('T')[0] : '',
          notes: reqData.notes || '',
          items: reqData.items || []
        });

        // Fetch departments
        const deptResponse = await fetch('/api/departments');
        if (deptResponse.ok) {
          const deptResult = await deptResponse.json();
          setDepartments(deptResult.docs || deptResult || []);
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load requisition data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [requisitionId, router]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleItemChange = (index: number, field: string, value: string | number) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };

    // Recalculate total for the item
    if (field === 'quantity' || field === 'estimatedUnitPrice') {
      const quantity = field === 'quantity' ? Number(value) : updatedItems[index].quantity;
      const unitPrice = field === 'estimatedUnitPrice' ? Number(value) : updatedItems[index].estimatedUnitPrice;
      updatedItems[index].estimatedTotal = quantity * unitPrice;
    }

    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          name: '',
          description: '',
          quantity: 1,
          unit: '',
          estimatedUnitPrice: 0,
          estimatedTotal: 0,
          category: '',
          specifications: ''
        }
      ]
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const calculateTotalAmount = () => {
    return formData.items.reduce((sum, item) => sum + (item.estimatedTotal || 0), 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Title is required',
        variant: 'destructive',
      });
      return;
    }

    if (formData.items.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'At least one item is required',
        variant: 'destructive',
      });
      return;
    }

    // Validate items
    for (let i = 0; i < formData.items.length; i++) {
      const item = formData.items[i];
      if (!item.name.trim() || !item.unit.trim() || item.quantity <= 0) {
        toast({
          title: 'Validation Error',
          description: `Item ${i + 1}: Name, unit, and quantity are required`,
          variant: 'destructive',
        });
        return;
      }
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        ...formData,
        totalAmount: calculateTotalAmount(),
        urgentDate: formData.urgentDate || undefined
      };

      const response = await fetch(`/api/procurement/requisition/${requisitionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition updated successfully',
      });

      router.push(`/dashboard/procurement/requisitions/${requisitionId}`);
    } catch (error) {
      console.error('Error updating requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update requisition',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/dashboard/procurement/requisitions/${requisitionId}`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Edit Requisition"
          text="Loading requisition data..."
        >
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </DashboardHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-10 w-1/2" />
            </CardContent>
          </Card>
        </div>
      </DashboardShell>
    );
  }

  if (!requisition) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Requisition Not Found"
          text="The requested requisition could not be found."
        >
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </DashboardHeader>
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading={`Edit Requisition ${requisition.requisitionNumber}`}
        text="Update requisition details and items"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button onClick={handleSubmit} disabled={isSaving}>
            {isSaving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </DashboardHeader>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update the basic details of the requisition
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter requisition title"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={formData.departmentId}
                  onValueChange={(value) => handleInputChange('departmentId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept._id} value={dept._id}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what you need and why"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="justification">Justification</Label>
              <Textarea
                id="justification"
                value={formData.justification}
                onChange={(e) => handleInputChange('justification', e.target.value)}
                placeholder="Provide business justification for this requisition"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange('priority', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="urgent">Urgent Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.priority === 'urgent' && (
                <div className="space-y-2">
                  <Label htmlFor="urgentDate">Required By Date</Label>
                  <Input
                    id="urgentDate"
                    type="date"
                    value={formData.urgentDate}
                    onChange={(e) => handleInputChange('urgentDate', e.target.value)}
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Any additional information or special requirements"
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Requisition Items */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Requisition Items</CardTitle>
                <CardDescription>
                  Add and manage items for this requisition
                </CardDescription>
              </div>
              <Button type="button" onClick={addItem} variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {formData.items.length > 0 ? (
              <div className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item Name *</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Qty *</TableHead>
                        <TableHead>Unit *</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead className="w-[50px]">Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formData.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Input
                              value={item.name}
                              onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                              placeholder="Item name"
                              className="min-w-[150px]"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              value={item.description || ''}
                              onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                              placeholder="Description"
                              className="min-w-[150px]"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => handleItemChange(index, 'quantity', Number(e.target.value))}
                              min="1"
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              value={item.unit}
                              onChange={(e) => handleItemChange(index, 'unit', e.target.value)}
                              placeholder="Unit"
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={item.estimatedUnitPrice}
                              onChange={(e) => handleItemChange(index, 'estimatedUnitPrice', Number(e.target.value))}
                              min="0"
                              step="0.01"
                              className="w-24"
                            />
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">
                              {formatCurrency(item.estimatedTotal || 0)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Input
                              value={item.category || ''}
                              onChange={(e) => handleItemChange(index, 'category', e.target.value)}
                              placeholder="Category"
                              className="min-w-[100px]"
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeItem(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Total Amount */}
                <div className="flex justify-end">
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center gap-4">
                      <span className="text-lg font-semibold">Total Amount:</span>
                      <span className="text-2xl font-bold text-primary">
                        {formatCurrency(calculateTotalAmount())}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Item Specifications */}
                <div className="space-y-4">
                  <h4 className="font-medium">Item Specifications (Optional)</h4>
                  {formData.items.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <Label htmlFor={`specs-${index}`} className="text-sm font-medium">
                        Specifications for: {item.name || `Item ${index + 1}`}
                      </Label>
                      <Textarea
                        id={`specs-${index}`}
                        value={item.specifications || ''}
                        onChange={(e) => handleItemChange(index, 'specifications', e.target.value)}
                        placeholder="Enter detailed specifications, requirements, or technical details for this item"
                        rows={3}
                        className="mt-2"
                      />
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p className="mb-4">No items added yet</p>
                <Button type="button" onClick={addItem}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Item
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-4">
          <Button type="button" variant="outline" onClick={handleBack}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </DashboardShell>
  )
}

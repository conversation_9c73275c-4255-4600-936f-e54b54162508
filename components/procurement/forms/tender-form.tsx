// components/procurement/forms/tender-form.tsx
"use client"

import { useState } from "react"
import { useForm, useFieldArray, Control } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { CalendarIcon, Plus, X, FileText, G<PERSON><PERSON>, Users, Award } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

// Tender requirement schema
const tenderRequirementSchema = z.object({
  description: z.string().min(1, "Requirement description is required"),
  isRequired: z.boolean().default(true),
  weight: z.number().min(0, "Weight must be non-negative").max(100, "Weight cannot exceed 100").optional(),
})

// Evaluation criteria schema
const evaluationCriteriaSchema = z.object({
  name: z.string().min(1, "Criteria name is required"),
  weight: z.number().min(0, "Weight must be non-negative").max(100, "Weight cannot exceed 100"),
  description: z.string().optional(),
})

// Main tender form schema
const tenderFormSchema = z.object({
  // Basic Information
  tenderNumber: z.string().min(1, "Tender number is required"),
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  
  // Department and Dates
  department: z.string().min(1, "Department is required"),
  publishDate: z.date({ required_error: "Publish date is required" }),
  closingDate: z.date({ required_error: "Closing date is required" }),
  
  // Financial
  estimatedValue: z.number().min(0, "Estimated value must be non-negative").optional(),
  currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  
  // Requirements and Criteria
  requirements: z.array(tenderRequirementSchema).min(1, "At least one requirement is required"),
  evaluationCriteria: z.array(evaluationCriteriaSchema).min(1, "At least one evaluation criteria is required"),
  
  // Status and Workflow
  status: z.enum(['draft', 'published', 'closed', 'awarded', 'cancelled']).default('draft'),
  
  // Additional Information
  notes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  
  // Supplier Invitation
  inviteAllSuppliers: z.boolean().default(false),
  invitedSuppliers: z.array(z.string()).default([]),
  
  // Bid Submission Settings
  allowLateSubmissions: z.boolean().default(false),
  requireTechnicalProposal: z.boolean().default(true),
  requireFinancialProposal: z.boolean().default(true),
  minimumBidAmount: z.number().min(0, "Minimum bid amount must be non-negative").optional(),
  maximumBidAmount: z.number().min(0, "Maximum bid amount must be non-negative").optional(),
}).refine((data) => data.closingDate > data.publishDate, {
  message: "Closing date must be after publish date",
  path: ["closingDate"],
}).refine((data) => !data.maximumBidAmount || !data.minimumBidAmount || data.maximumBidAmount >= data.minimumBidAmount, {
  message: "Maximum bid amount must be greater than or equal to minimum bid amount",
  path: ["maximumBidAmount"],
})

type TenderFormData = z.infer<typeof tenderFormSchema>

interface TenderFormProps {
  initialData?: Partial<TenderFormData>
  onSubmit: (data: TenderFormData) => Promise<void>
  isLoading?: boolean
  departments?: Array<{ _id: string; name: string }>
  suppliers?: Array<{ _id: string; name: string; category: string }>
  categories?: Array<{ value: string; label: string }>
  currentUser?: { _id: string; name: string; department?: string }
}

const tenderStatuses = [
  { value: 'draft', label: 'Draft', color: 'bg-gray-100 text-gray-800' },
  { value: 'published', label: 'Published', color: 'bg-blue-100 text-blue-800' },
  { value: 'closed', label: 'Closed', color: 'bg-orange-100 text-orange-800' },
  { value: 'awarded', label: 'Awarded', color: 'bg-green-100 text-green-800' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
]

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

export function TenderForm({
  initialData,
  onSubmit,
  isLoading = false,
  departments = [],
  suppliers = [],
  categories = [],
  currentUser
}: TenderFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const { toast } = useToast()

  const form = useForm({
    resolver: zodResolver(tenderFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      tenderNumber: initialData?.tenderNumber || '',
      title: initialData?.title || '',
      description: initialData?.description || '',
      category: initialData?.category || '',
      department: initialData?.department || currentUser?.department || '',
      publishDate: initialData?.publishDate || new Date(),
      closingDate: initialData?.closingDate || new Date(new Date().setDate(new Date().getDate() + 30)),
      estimatedValue: initialData?.estimatedValue || undefined,
      currency: initialData?.currency || 'MWK',
      requirements: initialData?.requirements || [{
        description: '',
        isRequired: true,
        weight: 0,
      }],
      evaluationCriteria: initialData?.evaluationCriteria || [{
        name: '',
        weight: 100,
        description: '',
      }],
      status: initialData?.status || 'draft',
      notes: initialData?.notes || '',
      attachments: initialData?.attachments || [],
      inviteAllSuppliers: initialData?.inviteAllSuppliers || false,
      invitedSuppliers: initialData?.invitedSuppliers || [],
      allowLateSubmissions: initialData?.allowLateSubmissions || false,
      requireTechnicalProposal: initialData?.requireTechnicalProposal ?? true,
      requireFinancialProposal: initialData?.requireFinancialProposal ?? true,
      minimumBidAmount: initialData?.minimumBidAmount || undefined,
      maximumBidAmount: initialData?.maximumBidAmount || undefined,
    }
  }) as ReturnType<typeof useForm<TenderFormData>>

  const { fields: requirementFields, append: appendRequirement, remove: removeRequirement } = useFieldArray({
    control: form.control,
    name: "requirements"
  })

  const { fields: criteriaFields, append: appendCriteria, remove: removeCriteria } = useFieldArray({
    control: form.control,
    name: "evaluationCriteria"
  })

  const handleSubmit = async (data: TenderFormData) => {
    try {
      // Validate total weight of evaluation criteria
      const totalWeight = data.evaluationCriteria.reduce((sum, criteria) => sum + criteria.weight, 0)
      if (Math.abs(totalWeight - 100) > 0.01) { // Allow for small floating point differences
        toast({
          title: "Validation Error",
          description: `Total weight of evaluation criteria must equal 100%. Current total: ${totalWeight}%`,
          variant: "destructive",
        })
        return
      }

      // Validate date logic
      if (data.closingDate <= data.publishDate) {
        toast({
          title: "Validation Error",
          description: "Closing date must be after publish date.",
          variant: "destructive",
        })
        return
      }

      // Validate bid amounts
      if (data.minimumBidAmount && data.maximumBidAmount && data.minimumBidAmount > data.maximumBidAmount) {
        toast({
          title: "Validation Error",
          description: "Maximum bid amount must be greater than minimum bid amount.",
          variant: "destructive",
        })
        return
      }

      await onSubmit(data)
      toast({
        title: "Success",
        description: "Tender has been saved successfully.",
      })
    } catch (error) {
      console.error('Tender submission error:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save tender. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Generate tender number
  const generateTenderNumber = () => {
    const dept = departments.find(d => d._id === form.getValues("department"))?.name || "DEPT"
    const year = new Date().getFullYear()
    const id = `TND-${dept.substring(0, 3).toUpperCase()}-${year}-${Date.now().toString().slice(-4)}`
    form.setValue("tenderNumber", id)
  }

  // Add new requirement
  const addRequirement = () => {
    appendRequirement({
      description: '',
      isRequired: true,
      weight: 0,
    })
  }

  // Add new evaluation criteria
  const addCriteria = () => {
    const currentCriteria = form.getValues("evaluationCriteria")
    const remainingWeight = 100 - currentCriteria.reduce((sum, c) => sum + (c.weight || 0), 0)

    appendCriteria({
      name: '',
      weight: Math.max(0, remainingWeight),
      description: '',
    })
  }

  // Calculate total weight
  const calculateTotalWeight = () => {
    const criteria = form.getValues("evaluationCriteria")
    return criteria.reduce((sum, criteria) => sum + (criteria.weight || 0), 0)
  }

  // Handle supplier invitation toggle
  const handleInviteAllToggle = (checked: boolean) => {
    form.setValue("inviteAllSuppliers", checked)
    if (checked) {
      form.setValue("invitedSuppliers", [])
    }
  }

  // Handle individual supplier invitation
  const handleSupplierInvite = (supplierId: string, checked: boolean) => {
    const currentInvited = form.getValues("invitedSuppliers")
    if (checked) {
      form.setValue("invitedSuppliers", [...currentInvited, supplierId])
    } else {
      form.setValue("invitedSuppliers", currentInvited.filter(id => id !== supplierId))
    }
  }

  return (
    <Card className="w-full max-w-5xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gavel className="h-5 w-5" />
          {initialData ? 'Edit Tender' : 'Create New Tender'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update tender details and requirements' : 'Create a new tender for supplier bidding'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="requirements" className="flex items-center gap-2">
                  <Gavel className="h-4 w-4" />
                  Requirements
                </TabsTrigger>
                <TabsTrigger value="evaluation" className="flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  Evaluation
                </TabsTrigger>
                <TabsTrigger value="suppliers" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Suppliers
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tender Title *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter tender title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tenderNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tender Number *</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input placeholder="TND-XXX-YYYY-XXXX" {...field} />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={generateTenderNumber}
                          >
                            Generate
                          </Button>
                        </div>
                        <FormDescription>
                          Unique identifier for this tender
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Detailed description of the tender"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.value} value={category.value}>
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {departments.map((dept) => (
                              <SelectItem key={dept._id} value={dept._id}>
                                {dept.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="publishDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Publish Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              autoFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          When the tender will be published
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="closingDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Closing Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date <= form.watch("publishDate")
                              }
                              autoFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          Deadline for bid submissions
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="estimatedValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Value</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Estimated total value of the tender
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.value} value={currency.value}>
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tenderStatuses.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              <div className="flex items-center gap-2">
                                <Badge className={status.color}>
                                  {status.label}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes or instructions"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Requirements Tab */}
              <TabsContent value="requirements" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Gavel className="h-5 w-5" />
                    Tender Requirements
                  </h3>
                  <Button type="button" onClick={addRequirement} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Requirement
                  </Button>
                </div>

                {requirementFields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Requirement {index + 1}</h4>
                      {requirementFields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeRequirement(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <FormField
                      control={form.control}
                      name={`requirements.${index}.description`}
                      render={({ field }) => (
                        <FormItem className="mb-4">
                          <FormLabel>Requirement Description *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe the requirement in detail"
                              className="min-h-[80px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`requirements.${index}.isRequired`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Mandatory Requirement
                              </FormLabel>
                              <FormDescription>
                                Check if this requirement is mandatory for bid qualification
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`requirements.${index}.weight`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormDescription>
                              Weight for scoring (0-100%)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </Card>
                ))}

                {/* Bid Submission Settings */}
                <Card className="p-4 bg-blue-50 border-blue-200">
                  <h4 className="font-medium mb-3">Bid Submission Settings</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <FormField
                      control={form.control}
                      name="requireTechnicalProposal"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Require Technical Proposal
                            </FormLabel>
                            <FormDescription>
                              Bidders must submit technical proposals
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="requireFinancialProposal"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Require Financial Proposal
                            </FormLabel>
                            <FormDescription>
                              Bidders must submit financial proposals
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="minimumBidAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Bid Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>
                            Minimum acceptable bid amount
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maximumBidAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Bid Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum acceptable bid amount
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="allowLateSubmissions"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Allow Late Submissions
                          </FormLabel>
                          <FormDescription>
                            Accept bids submitted after the closing date
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </Card>
              </TabsContent>

              {/* Evaluation Tab */}
              <TabsContent value="evaluation" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    Evaluation Criteria
                  </h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      Total Weight: {calculateTotalWeight()}%
                    </Badge>
                    <Button type="button" onClick={addCriteria} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Criteria
                    </Button>
                  </div>
                </div>

                {calculateTotalWeight() !== 100 && criteriaFields.length > 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-800">
                      ⚠️ Total weight must equal 100%. Current total: {calculateTotalWeight()}%
                    </p>
                  </div>
                )}

                {criteriaFields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Criteria {index + 1}</h4>
                      {criteriaFields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCriteria(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`evaluationCriteria.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Criteria Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Technical Capability" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`evaluationCriteria.${index}.weight`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (%) *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`evaluationCriteria.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe how this criteria will be evaluated"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </Card>
                ))}
              </TabsContent>

              {/* Suppliers Tab */}
              <TabsContent value="suppliers" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Supplier Invitations
                </h3>

                <FormField
                  control={form.control}
                  name="inviteAllSuppliers"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={handleInviteAllToggle}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Invite All Suppliers
                        </FormLabel>
                        <FormDescription>
                          Make this tender open to all registered suppliers
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {!form.watch("inviteAllSuppliers") && (
                  <div className="space-y-4">
                    <h4 className="font-medium">Select Suppliers to Invite</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                      {suppliers.map((supplier) => (
                        <Card key={supplier._id} className="p-3">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              checked={form.watch("invitedSuppliers").includes(supplier._id)}
                              onCheckedChange={(checked) => handleSupplierInvite(supplier._id, checked as boolean)}
                            />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{supplier.name}</p>
                              <p className="text-xs text-muted-foreground">{supplier.category}</p>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>

                    {form.watch("invitedSuppliers").length > 0 && (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-800">
                          {form.watch("invitedSuppliers").length} supplier(s) selected for invitation
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const tabs = ["basic", "requirements", "evaluation", "suppliers"]
                  const currentIndex = tabs.indexOf(activeTab)
                  if (currentIndex > 0) {
                    setActiveTab(tabs[currentIndex - 1])
                  }
                }}
                disabled={activeTab === "basic"}
              >
                Previous
              </Button>
              
              {activeTab === "suppliers" ? (
                <div className="flex gap-2">
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => form.setValue("status", "draft")}
                  >
                    Save as Draft
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : form.watch("status") === "draft" ? "Publish Tender" : "Update Tender"}
                  </Button>
                </div>
              ) : (
                <Button
                  type="button"
                  onClick={() => {
                    const tabs = ["basic", "requirements", "evaluation", "suppliers"]
                    const currentIndex = tabs.indexOf(activeTab)
                    if (currentIndex < tabs.length - 1) {
                      setActiveTab(tabs[currentIndex + 1])
                    }
                  }}
                >
                  Next
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

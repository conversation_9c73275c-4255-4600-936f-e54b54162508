'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Trash2, AlertTriangle, CheckCircle2, X } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors: Array<{ requisitionId: string; message: string }>;
}

interface BulkRequisitionDeleteProps {
  selectedRequisitionIds: string[];
  selectedRequisitions: Array<{
    _id: string;
    requisitionNumber: string;
    title: string;
    status: string;
    totalAmount: number;
    departmentId?: { name: string };
  }>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function BulkRequisitionDelete({ 
  selectedRequisitionIds, 
  selectedRequisitions, 
  onSuccess, 
  onCancel 
}: BulkRequisitionDeleteProps) {
  const [deletionReason, setDeletionReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteProgress, setDeleteProgress] = useState(0);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);

  const handleDelete = async () => {
    if (!deletionReason.trim()) {
      setDeleteError('Deletion reason is required');
      return;
    }

    if (deletionReason.trim().length < 25) {
      setDeleteError('Deletion reason must be at least 25 characters');
      return;
    }

    setIsDeleting(true);
    setDeleteProgress(0);
    setDeleteError(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setDeleteProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);

      // Send delete request
      const response = await fetch('/api/procurement/requisition/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requisitionIds: selectedRequisitionIds,
          deletionReason: deletionReason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete requisitions');
      }

      const result = await response.json();
      setDeleteProgress(100);
      setDeleteResult(result.data);

      // Show success toast
      toast({
        title: "Deletion Completed",
        description: `${result.data.deletedCount} of ${result.data.requestedCount} requisitions deleted successfully.`,
        variant: result.data.errors.length > 0 ? "destructive" : "default",
      });

      // Call onSuccess callback if provided
      if (onSuccess && result.data.deletedCount > 0) {
        onSuccess();
      }

    } catch (error: unknown) {
      console.error('Error deleting requisitions:', error);
      setDeleteError(error instanceof Error ? error.message : 'An error occurred during deletion');
      setDeleteProgress(0);
    } finally {
      setIsDeleting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: "outline" as const, color: "text-gray-600" },
      pending_approval: { variant: "secondary" as const, color: "text-yellow-600" },
      approved: { variant: "default" as const, color: "text-green-600" },
      rejected: { variant: "destructive" as const, color: "text-red-600" },
      completed: { variant: "default" as const, color: "text-green-800" }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  // Check if any requisitions cannot be deleted
  const undeletableRequisitions = selectedRequisitions.filter(req => 
    ['approved', 'completed'].includes(req.status)
  );

  if (deleteResult) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
            Deletion Results
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{deleteResult.requestedCount}</div>
              <div className="text-sm text-muted-foreground">Requested</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{deleteResult.deletedCount}</div>
              <div className="text-sm text-muted-foreground">Deleted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{deleteResult.errors.length}</div>
              <div className="text-sm text-muted-foreground">Errors</div>
            </div>
          </div>

          {/* Errors */}
          {deleteResult.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Some requisitions could not be deleted</AlertTitle>
              <AlertDescription>
                <div className="mt-2 space-y-1">
                  {deleteResult.errors.map((error, index) => (
                    <div key={index} className="text-sm">
                      <strong>Requisition ID {error.requisitionId}:</strong> {error.message}
                    </div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end">
            <Button onClick={onCancel}>
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <Trash2 className="h-5 w-5" />
          Delete Requisitions
        </CardTitle>
        <CardDescription>
          You are about to delete {selectedRequisitionIds.length} requisition{selectedRequisitionIds.length !== 1 ? 's' : ''}. This action cannot be undone.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Warning for undeletable requisitions */}
        {undeletableRequisitions.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Warning: Some requisitions cannot be deleted</AlertTitle>
            <AlertDescription>
              {undeletableRequisitions.length} requisition{undeletableRequisitions.length !== 1 ? 's' : ''} cannot be deleted because they have been approved or completed. Only draft or rejected requisitions can be deleted.
            </AlertDescription>
          </Alert>
        )}

        {/* Requisitions to be deleted */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Requisitions to Delete:</Label>
          <div className="max-h-48 overflow-y-auto border rounded-md p-3 space-y-2">
            {selectedRequisitions.map((requisition) => (
              <div key={requisition._id} className="flex items-center justify-between p-2 bg-muted rounded-sm">
                <div className="flex-1">
                  <div className="font-medium text-sm">{requisition.requisitionNumber}</div>
                  <div className="text-xs text-muted-foreground">{requisition.title}</div>
                  {requisition.departmentId && (
                    <div className="text-xs text-muted-foreground">{requisition.departmentId.name}</div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">{formatCurrency(requisition.totalAmount)}</span>
                  {getStatusBadge(requisition.status)}
                  {undeletableRequisitions.some(u => u._id === requisition._id) && (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Deletion reason */}
        <div className="space-y-2">
          <Label htmlFor="deletion-reason">
            Deletion Reason <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="deletion-reason"
            placeholder="Please provide a detailed reason for deleting these requisitions (minimum 25 characters)..."
            value={deletionReason}
            onChange={(e) => setDeletionReason(e.target.value)}
            disabled={isDeleting}
            rows={4}
            className="resize-none"
          />
          <div className="text-xs text-muted-foreground">
            {deletionReason.length}/25 characters minimum
          </div>
        </div>

        {/* Progress */}
        {deleteProgress > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Deletion Progress</span>
              <span>{deleteProgress}%</span>
            </div>
            <Progress value={deleteProgress} className="w-full" />
          </div>
        )}

        {/* Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Deletion Failed</AlertTitle>
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || !deletionReason.trim() || deletionReason.trim().length < 25}
          >
            {isDeleting ? 'Deleting...' : `Delete ${selectedRequisitionIds.length} Requisition${selectedRequisitionIds.length !== 1 ? 's' : ''}`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

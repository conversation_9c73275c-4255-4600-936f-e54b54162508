// components/procurement/delivery-tracking.tsx
"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Truck, 
  Package, 
  CheckCircle, 
  Clock,
  MapPin,
  Eye
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

// Mock delivery data
const mockDeliveries = [
  {
    id: "DEL-2024-001",
    orderId: "PO-2024-001",
    supplier: "Office Supplies Ltd",
    items: "Office Stationery & Equipment",
    status: "delivered",
    trackingNumber: "TRK123456789",
    estimatedDelivery: "2024-01-20",
    actualDelivery: "2024-01-19",
    location: "Main Office Reception"
  },
  {
    id: "DEL-2024-002",
    orderId: "PO-2024-002",
    supplier: "Tech Solutions Inc",
    items: "Computer Hardware",
    status: "in_transit",
    trackingNumber: "TRK987654321",
    estimatedDelivery: "2024-01-25",
    actualDelivery: null,
    location: "Distribution Center - Lilongwe"
  },
  {
    id: "DEL-2024-003",
    orderId: "PO-2024-004",
    supplier: "Cleaning Services Co",
    items: "Cleaning Supplies",
    status: "pending",
    trackingNumber: "TRK456789123",
    estimatedDelivery: "2024-01-22",
    actualDelivery: null,
    location: "Supplier Warehouse"
  }
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    pending: { variant: "outline" as const, label: "Pending", icon: Clock },
    in_transit: { variant: "secondary" as const, label: "In Transit", icon: Truck },
    delivered: { variant: "default" as const, label: "Delivered", icon: CheckCircle },
    delayed: { variant: "destructive" as const, label: "Delayed", icon: Clock }
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || { 
    variant: "outline" as const, 
    label: status,
    icon: Package
  }
  
  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

export function DeliveryTracking() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Delivery Tracking"
        text="Track deliveries, shipments, and logistics"
      />

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Deliveries</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockDeliveries.length}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Transit</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockDeliveries.filter(d => d.status === "in_transit").length}
              </div>
              <p className="text-xs text-muted-foreground">Currently shipping</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delivered</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockDeliveries.filter(d => d.status === "delivered").length}
              </div>
              <p className="text-xs text-muted-foreground">Successfully delivered</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Rate</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">95%</div>
              <p className="text-xs text-muted-foreground">Delivery performance</p>
            </CardContent>
          </Card>
        </div>

        {/* Deliveries Table */}
        <Card>
          <CardHeader>
            <CardTitle>Active Deliveries</CardTitle>
            <CardDescription>
              Track current and recent deliveries
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Delivery ID</TableHead>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Tracking Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Expected Delivery</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockDeliveries.map((delivery) => (
                  <TableRow key={delivery.id}>
                    <TableCell className="font-medium">{delivery.id}</TableCell>
                    <TableCell>{delivery.orderId}</TableCell>
                    <TableCell>{delivery.supplier}</TableCell>
                    <TableCell>{delivery.items}</TableCell>
                    <TableCell className="font-mono text-sm">{delivery.trackingNumber}</TableCell>
                    <TableCell>{getStatusBadge(delivery.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{delivery.location}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p>{delivery.estimatedDelivery}</p>
                        {delivery.actualDelivery && (
                          <p className="text-xs text-green-600">
                            Delivered: {delivery.actualDelivery}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}

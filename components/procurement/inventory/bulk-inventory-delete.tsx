'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Trash2, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface InventoryItem {
  _id: string;
  name: string;
  sku?: string;
  inventoryId?: string;
  currentStock: number;
  totalValue?: number;
  status: string;
}

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors?: Array<{ message: string }>;
}

interface BulkInventoryDeleteProps {
  selectedItems: InventoryItem[];
  onSuccess?: () => void;
  onCancel?: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BulkInventoryDelete({ 
  selectedItems, 
  onSuccess, 
  onCancel, 
  isOpen, 
  onOpenChange 
}: BulkInventoryDeleteProps) {
  const [deletionReason, setDeletionReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);

  // Calculate totals
  const totalStock = selectedItems.reduce((sum, item) => sum + item.currentStock, 0);
  const totalValue = selectedItems.reduce((sum, item) => sum + (item.totalValue || 0), 0);
  const highValueItems = selectedItems.filter(item => (item.totalValue || 0) > 50000);

  const handleDelete = async () => {
    if (!deletionReason.trim() || deletionReason.trim().length < 25) {
      setDeleteError('Deletion reason must be at least 25 characters long');
      return;
    }

    if (selectedItems.length === 0) {
      setDeleteError('No inventory items selected for deletion');
      return;
    }

    // Additional validation for high-value items
    if (highValueItems.length > 0 && deletionReason.trim().length < 50) {
      setDeleteError(`High-value items require detailed deletion reason (minimum 50 characters). Found ${highValueItems.length} high-value items.`);
      return;
    }

    setIsDeleting(true);
    setDeleteError(null);
    setDeleteResult(null);

    try {
      const response = await fetch('/api/procurement/inventory/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemIds: selectedItems.map(item => item._id),
          deletionReason: deletionReason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete inventory items');
      }

      const result = await response.json();
      setDeleteResult(result.data);

      // Show success toast
      toast({
        title: 'Bulk Delete Completed',
        description: `Successfully deleted ${result.data.deletedCount} out of ${result.data.requestedCount} inventory items with audit trail.`,
        variant: result.data.errors && result.data.errors.length > 0 ? 'destructive' : 'default',
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close dialog after successful deletion
      setTimeout(() => {
        onOpenChange(false);
        resetForm();
      }, 2000);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : 'An unexpected error occurred');
      
      toast({
        title: 'Bulk Delete Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const resetForm = () => {
    setDeletionReason('');
    setDeleteError(null);
    setDeleteResult(null);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
    resetForm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Bulk Delete Inventory Items
          </DialogTitle>
          <DialogDescription>
            You are about to permanently delete {selectedItems.length} inventory items with a total stock of {totalStock} units 
            and total value of MWK {totalValue.toLocaleString()}. This action cannot be undone and will create an audit trail.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">{selectedItems.length}</div>
              <div className="text-xs text-muted-foreground">Items</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">{totalStock}</div>
              <div className="text-xs text-muted-foreground">Total Stock</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">MWK {(totalValue / 1000).toFixed(0)}K</div>
              <div className="text-xs text-muted-foreground">Total Value</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold text-orange-600">{highValueItems.length}</div>
              <div className="text-xs text-muted-foreground">High-Value Items</div>
            </div>
          </div>

          {/* Selected Items */}
          <div>
            <Label className="text-sm font-medium">Selected Items ({selectedItems.length})</Label>
            <div className="mt-2 max-h-40 overflow-y-auto border rounded-md p-3 bg-muted/50">
              <div className="space-y-2">
                {selectedItems.map((item) => (
                  <div key={item._id} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {item.name}
                      </Badge>
                      {item.sku && (
                        <span className="text-xs text-muted-foreground">SKU: {item.sku}</span>
                      )}
                      {(item.totalValue || 0) > 50000 && (
                        <Badge variant="destructive" className="text-xs">High Value</Badge>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Stock: {item.currentStock} | Value: MWK {(item.totalValue || 0).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* High-Value Items Warning */}
          {highValueItems.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>High-Value Items Detected</AlertTitle>
              <AlertDescription>
                {highValueItems.length} items have values exceeding MWK 50,000. 
                These require detailed deletion reasons (minimum 50 characters) including asset disposition procedures.
              </AlertDescription>
            </Alert>
          )}

          {/* Deletion Reason */}
          <div>
            <Label htmlFor="deletion-reason" className="text-sm font-medium">
              Deletion Reason <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="deletion-reason"
              placeholder={`Provide a detailed reason for deleting these inventory items (minimum ${highValueItems.length > 0 ? '50' : '25'} characters). Include stock disposition procedures for items with inventory...`}
              value={deletionReason}
              onChange={(e) => setDeletionReason(e.target.value)}
              disabled={isDeleting}
              className="mt-1"
              rows={4}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {deletionReason.length}/{highValueItems.length > 0 ? '50' : '25'} characters minimum
              {totalStock > 0 && ' (Include stock disposition for items with inventory)'}
            </p>
          </div>

          {/* Warning */}
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-4 space-y-1 text-sm">
                <li>This action will permanently delete all selected inventory items</li>
                <li>Items with pending purchase orders cannot be deleted</li>
                <li>Items with active requisitions cannot be deleted</li>
                <li>Stock movements will be logged for audit compliance</li>
                <li>High-value items require detailed justification</li>
                <li>This action cannot be undone</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Error Display */}
          {deleteError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Delete Failed</AlertTitle>
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {deleteResult && (
            <Alert variant={deleteResult.errors && deleteResult.errors.length > 0 ? "destructive" : "default"}>
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Delete Results</AlertTitle>
              <AlertDescription>
                <div className="space-y-1">
                  <p>Requested deletions: {deleteResult.requestedCount}</p>
                  <p>Successfully deleted: {deleteResult.deletedCount}</p>
                  <p>Audit records created: {deleteResult.auditRecordsCreated}</p>
                </div>
                
                {deleteResult.errors && deleteResult.errors.length > 0 && (
                  <div className="mt-3">
                    <p className="font-medium">Errors:</p>
                    <ul className="text-xs space-y-1">
                      {deleteResult.errors.map((error, index) => (
                        <li key={index}>{error.message}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={
              isDeleting || 
              deletionReason.trim().length < (highValueItems.length > 0 ? 50 : 25) || 
              selectedItems.length === 0
            }
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? 'Deleting...' : `Delete ${selectedItems.length} Items`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Trash2, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface Category {
  _id: string;
  name: string;
  code?: string;
  description?: string;
  status: string;
}

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors?: Array<{ message: string }>;
}

interface BulkCategoryDeleteProps {
  selectedCategories: Category[];
  onSuccess?: () => void;
  onCancel?: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BulkCategoryDelete({ 
  selectedCategories, 
  onSuccess, 
  onCancel, 
  isOpen, 
  onOpenChange 
}: BulkCategoryDeleteProps) {
  const [deletionReason, setDeletionReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);

  const handleDelete = async () => {
    if (!deletionReason.trim() || deletionReason.trim().length < 20) {
      setDeleteError('Deletion reason must be at least 20 characters long');
      return;
    }

    if (selectedCategories.length === 0) {
      setDeleteError('No categories selected for deletion');
      return;
    }

    setIsDeleting(true);
    setDeleteError(null);
    setDeleteResult(null);

    try {
      const response = await fetch('/api/procurement/categories/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categoryIds: selectedCategories.map(cat => cat._id),
          deletionReason: deletionReason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete categories');
      }

      const result = await response.json();
      setDeleteResult(result.data);

      // Show success toast
      toast({
        title: 'Bulk Delete Completed',
        description: `Successfully deleted ${result.data.deletedCount} out of ${result.data.requestedCount} categories with audit trail.`,
        variant: result.data.errors && result.data.errors.length > 0 ? 'destructive' : 'default',
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close dialog after successful deletion
      setTimeout(() => {
        onOpenChange(false);
        resetForm();
      }, 2000);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : 'An unexpected error occurred');
      
      toast({
        title: 'Bulk Delete Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const resetForm = () => {
    setDeletionReason('');
    setDeleteError(null);
    setDeleteResult(null);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
    resetForm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Bulk Delete Categories
          </DialogTitle>
          <DialogDescription>
            You are about to permanently delete {selectedCategories.length} categories. 
            This action cannot be undone and will create an audit trail.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Selected Categories */}
          <div>
            <Label className="text-sm font-medium">Selected Categories ({selectedCategories.length})</Label>
            <div className="mt-2 max-h-32 overflow-y-auto border rounded-md p-3 bg-muted/50">
              <div className="flex flex-wrap gap-2">
                {selectedCategories.map((category) => (
                  <Badge key={category._id} variant="secondary" className="text-xs">
                    {category.name}
                    {category.code && ` (${category.code})`}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Deletion Reason */}
          <div>
            <Label htmlFor="deletion-reason" className="text-sm font-medium">
              Deletion Reason <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="deletion-reason"
              placeholder="Provide a detailed reason for deleting these categories (minimum 20 characters)..."
              value={deletionReason}
              onChange={(e) => setDeletionReason(e.target.value)}
              disabled={isDeleting}
              className="mt-1"
              rows={4}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {deletionReason.length}/20 characters minimum
            </p>
          </div>

          {/* Warning */}
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-4 space-y-1 text-sm">
                <li>This action will permanently delete all selected categories</li>
                <li>Categories with active inventory items cannot be deleted</li>
                <li>Categories with child categories cannot be deleted</li>
                <li>All deletion activities will be logged for audit compliance</li>
                <li>This action cannot be undone</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Error Display */}
          {deleteError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Delete Failed</AlertTitle>
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {deleteResult && (
            <Alert variant={deleteResult.errors && deleteResult.errors.length > 0 ? "destructive" : "default"}>
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Delete Results</AlertTitle>
              <AlertDescription>
                <div className="space-y-1">
                  <p>Requested deletions: {deleteResult.requestedCount}</p>
                  <p>Successfully deleted: {deleteResult.deletedCount}</p>
                  <p>Audit records created: {deleteResult.auditRecordsCreated}</p>
                </div>
                
                {deleteResult.errors && deleteResult.errors.length > 0 && (
                  <div className="mt-3">
                    <p className="font-medium">Errors:</p>
                    <ul className="text-xs space-y-1">
                      {deleteResult.errors.map((error, index) => (
                        <li key={index}>{error.message}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || deletionReason.trim().length < 20 || selectedCategories.length === 0}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? 'Deleting...' : `Delete ${selectedCategories.length} Categories`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { FileSpreadsheet, Download, AlertCircle, CheckCircle2, Upload, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import * as XLSX from 'xlsx';
import { toast } from '@/components/ui/use-toast';

interface UploadResult {
  totalRows: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: Array<{ row: number; error: string }>;
  imported: Array<{ row: number; name: string; code?: string }>;
  skipped: Array<{ row: number; name: string; reason: string }>;
}

interface BulkCategoryUploadProps {
  onSuccess?: () => void;
}

export function BulkCategoryUpload({ onSuccess }: BulkCategoryUploadProps = {}) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setUploadError(null);
      setUploadResult(null);
    }
  };

  const clearFile = () => {
    setFile(null);
    setUploadError(null);
    setUploadResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);
    setUploadResult(null);

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 500);

      // Send the file to the server
      const response = await fetch('/api/procurement/categories/bulk-import', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload file');
      }

      const result = await response.json();
      setUploadProgress(100);
      setUploadResult(result.data);

      // Show success toast
      toast({
        title: 'Import Completed',
        description: `Successfully imported ${result.data.successCount} categories. ${result.data.errorCount} errors, ${result.data.skippedCount} skipped.`,
        variant: result.data.errorCount > 0 ? 'destructive' : 'default',
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'An unexpected error occurred');
      setUploadProgress(0);
      
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const downloadTemplate = () => {
    // Create template data
    const templateData = [
      {
        'Name': 'Office Supplies',
        'Code': 'OFF_SUP',
        'Description': 'General office supplies and stationery',
        'Category Type': 'general',
        'Approval Limit': '5000',
        'Risk Level': 'low',
        'Lead Time': '7',
        'Quality Standards': 'ISO 9001',
        'Environmental Impact': 'low',
        'Tags': 'office, supplies, stationery',
        'Notes': 'Standard office supplies category',
        'Status': 'active'
      },
      {
        'Name': 'IT Equipment',
        'Code': 'IT_EQUIP',
        'Description': 'Information technology equipment and accessories',
        'Category Type': 'equipment',
        'Approval Limit': '50000',
        'Risk Level': 'medium',
        'Lead Time': '14',
        'Quality Standards': 'ISO 27001, CE',
        'Environmental Impact': 'medium',
        'Tags': 'IT, technology, equipment',
        'Notes': 'All IT related equipment and accessories',
        'Status': 'active'
      }
    ];

    // Create workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(templateData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Categories');

    // Save file
    XLSX.writeFile(wb, 'procurement_categories_template.xlsx');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Bulk Category Upload</CardTitle>
        <CardDescription>
          Import multiple procurement categories at once using a CSV or Excel file.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="template">Template</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4 py-4">
            <div className="flex items-center justify-center p-4 border-2 border-dashed rounded-md">
              <div className="space-y-2 text-center">
                <FileSpreadsheet className="mx-auto h-12 w-12 text-muted-foreground" />
                <div className="text-sm">
                  <Label htmlFor="file-upload" className="relative cursor-pointer rounded-md font-medium text-primary">
                    <span>Upload a file</span>
                    <Input
                      id="file-upload"
                      ref={fileInputRef}
                      type="file"
                      className="sr-only"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileChange}
                      disabled={isUploading}
                    />
                  </Label>
                  <p className="text-xs text-muted-foreground">CSV or Excel files up to 5MB</p>
                </div>
              </div>
            </div>

            {file && (
              <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                <div className="flex items-center space-x-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span className="text-sm font-medium">{file.name}</span>
                  <span className="text-xs text-muted-foreground">
                    ({(file.size / 1024).toFixed(1)} KB)
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFile}
                  disabled={isUploading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}

            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}

            {uploadError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Upload Failed</AlertTitle>
                <AlertDescription>{uploadError}</AlertDescription>
              </Alert>
            )}

            {uploadResult && (
              <Alert variant={uploadResult.errorCount > 0 ? "destructive" : "default"}>
                <CheckCircle2 className="h-4 w-4" />
                <AlertTitle>Import Results</AlertTitle>
                <AlertDescription>
                  <div className="space-y-1">
                    <p>Total rows processed: {uploadResult.totalRows}</p>
                    <p>Successfully imported: {uploadResult.successCount}</p>
                    <p>Errors: {uploadResult.errorCount}</p>
                    <p>Skipped (duplicates): {uploadResult.skippedCount}</p>
                  </div>
                  
                  {uploadResult.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="font-medium">Errors:</p>
                      <ul className="text-xs space-y-1 max-h-32 overflow-y-auto">
                        {uploadResult.errors.slice(0, 10).map((error, index) => (
                          <li key={index}>Row {error.row}: {error.error}</li>
                        ))}
                        {uploadResult.errors.length > 10 && (
                          <li>... and {uploadResult.errors.length - 10} more errors</li>
                        )}
                      </ul>
                    </div>
                  )}

                  {uploadResult.skipped.length > 0 && (
                    <div className="mt-3">
                      <p className="font-medium">Skipped:</p>
                      <ul className="text-xs space-y-1 max-h-32 overflow-y-auto">
                        {uploadResult.skipped.slice(0, 5).map((skipped, index) => (
                          <li key={index}>Row {skipped.row}: {skipped.name} - {skipped.reason}</li>
                        ))}
                        {uploadResult.skipped.length > 5 && (
                          <li>... and {uploadResult.skipped.length - 5} more skipped</li>
                        )}
                      </ul>
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="template" className="space-y-4 py-4">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Download our template file to ensure your data is formatted correctly for import.
              </p>
              <Button onClick={downloadTemplate} className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Required Fields:</h4>
                <ul className="text-xs list-disc pl-5 text-muted-foreground space-y-1">
                  <li><strong>Name</strong> - The category name (required)</li>
                  <li><strong>Code</strong> - Unique category code (optional, auto-generated if not provided)</li>
                  <li><strong>Description</strong> - Category description (optional)</li>
                  <li><strong>Category Type</strong> - Type of category (general, equipment, services, etc.)</li>
                  <li><strong>Approval Limit</strong> - Maximum approval amount (optional, number)</li>
                  <li><strong>Risk Level</strong> - Risk assessment (low, medium, high)</li>
                  <li><strong>Lead Time</strong> - Expected lead time in days (optional, number)</li>
                  <li><strong>Quality Standards</strong> - Applicable quality standards (optional)</li>
                  <li><strong>Environmental Impact</strong> - Environmental impact level (low, medium, high)</li>
                  <li><strong>Tags</strong> - Comma-separated tags (optional)</li>
                  <li><strong>Notes</strong> - Additional notes (optional)</li>
                  <li><strong>Status</strong> - Category status (active, inactive)</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleUpload} 
          disabled={!file || isUploading}
          className="w-full"
        >
          <Upload className="mr-2 h-4 w-4" />
          {isUploading ? 'Uploading...' : 'Upload Categories'}
        </Button>
      </CardFooter>
    </Card>
  );
}

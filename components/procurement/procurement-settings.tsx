// components/procurement/procurement-settings.tsx
"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Settings, 
  Save, 
  Bell,
  Shield,
  DollarSign,
  Users,
  FileText
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"

export function ProcurementSettings() {
  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Procurement Settings"
        text="Configure procurement system settings and preferences"
      >
        <Button>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </DashboardHeader>

      <div className="space-y-6">
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="approval">Approval Workflows</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="integration">Integration</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  General Settings
                </CardTitle>
                <CardDescription>
                  Configure basic procurement system settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="org-name">Organization Name</Label>
                    <Input id="org-name" defaultValue="Teachers Council of Malawi" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Default Currency</Label>
                    <Select defaultValue="mwk">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mwk">Malawian Kwacha (MWK)</SelectItem>
                        <SelectItem value="usd">US Dollar (USD)</SelectItem>
                        <SelectItem value="eur">Euro (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="fiscal-year">Fiscal Year Start</Label>
                    <Select defaultValue="april">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="january">January</SelectItem>
                        <SelectItem value="april">April</SelectItem>
                        <SelectItem value="july">July</SelectItem>
                        <SelectItem value="october">October</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="po-prefix">Purchase Order Prefix</Label>
                    <Input id="po-prefix" defaultValue="PO-" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="terms">Default Terms & Conditions</Label>
                  <Textarea 
                    id="terms" 
                    placeholder="Enter default terms and conditions for purchase orders..."
                    className="min-h-[100px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="approval" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Approval Workflows
                </CardTitle>
                <CardDescription>
                  Configure approval thresholds and workflows
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Require approval for all purchases</Label>
                      <p className="text-sm text-muted-foreground">All purchase orders require approval regardless of amount</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="threshold-1">Level 1 Approval Threshold</Label>
                      <Input id="threshold-1" type="number" defaultValue="50000" />
                      <p className="text-xs text-muted-foreground">Department head approval required</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="threshold-2">Level 2 Approval Threshold</Label>
                      <Input id="threshold-2" type="number" defaultValue="200000" />
                      <p className="text-xs text-muted-foreground">Finance manager approval required</p>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="threshold-3">Level 3 Approval Threshold</Label>
                      <Input id="threshold-3" type="number" defaultValue="500000" />
                      <p className="text-xs text-muted-foreground">Director approval required</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="auto-approve">Auto-approve below</Label>
                      <Input id="auto-approve" type="number" defaultValue="10000" />
                      <p className="text-xs text-muted-foreground">Automatic approval for small amounts</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Settings
                </CardTitle>
                <CardDescription>
                  Configure email and system notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>New purchase requisition notifications</Label>
                      <p className="text-sm text-muted-foreground">Notify approvers when new requisitions are submitted</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Purchase order status updates</Label>
                      <p className="text-sm text-muted-foreground">Notify requestors of purchase order status changes</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Delivery notifications</Label>
                      <p className="text-sm text-muted-foreground">Notify when deliveries are received</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Contract expiry alerts</Label>
                      <p className="text-sm text-muted-foreground">Alert when contracts are nearing expiry</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Low stock alerts</Label>
                      <p className="text-sm text-muted-foreground">Notify when inventory items are running low</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notification-emails">Additional notification emails</Label>
                  <Textarea 
                    id="notification-emails" 
                    placeholder="Enter additional email addresses (one per line)..."
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Compliance Settings
                </CardTitle>
                <CardDescription>
                  Configure compliance requirements and audit settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Require competitive bidding</Label>
                      <p className="text-sm text-muted-foreground">Mandate competitive bidding for purchases above threshold</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bidding-threshold">Competitive bidding threshold</Label>
                    <Input id="bidding-threshold" type="number" defaultValue="100000" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="min-suppliers">Minimum number of suppliers for bidding</Label>
                    <Select defaultValue="3">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2">2 suppliers</SelectItem>
                        <SelectItem value="3">3 suppliers</SelectItem>
                        <SelectItem value="4">4 suppliers</SelectItem>
                        <SelectItem value="5">5 suppliers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Audit trail logging</Label>
                      <p className="text-sm text-muted-foreground">Log all procurement activities for audit purposes</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="retention-period">Audit log retention period (months)</Label>
                    <Input id="retention-period" type="number" defaultValue="60" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integration" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  System Integration
                </CardTitle>
                <CardDescription>
                  Configure integration with other systems
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Accounting system integration</Label>
                      <p className="text-sm text-muted-foreground">Sync procurement data with accounting system</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Budget system integration</Label>
                      <p className="text-sm text-muted-foreground">Check budget availability before approving purchases</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Inventory system sync</Label>
                      <p className="text-sm text-muted-foreground">Update inventory levels when items are received</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="api-key">API Key for external integrations</Label>
                    <Input id="api-key" type="password" placeholder="Enter API key..." />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhook-url">Webhook URL for notifications</Label>
                    <Input id="webhook-url" placeholder="https://..." />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  )
}

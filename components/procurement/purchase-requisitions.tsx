// components/procurement/purchase-requisitions.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  PlusCircle,
  Search,
  Filter,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Download,
  Upload,
  Trash2,
  Trash,
  Settings
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { toast } from "@/components/ui/use-toast"
import { BulkRequisitionUpload } from "@/components/procurement/requisitions/bulk-requisition-upload"
import { BulkRequisitionDelete } from "@/components/procurement/requisitions/bulk-requisition-delete"

// Requisition interfaces
interface RequisitionItem {
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  estimatedUnitPrice: number;
  estimatedTotal: number;
  category?: string;
  specifications?: string;
}

interface Requisition {
  _id: string;
  requisitionNumber: string;
  title: string;
  description: string;
  justification?: string;
  departmentId: {
    _id: string;
    name: string;
  };
  requestedBy: {
    _id: string;
    name: string;
    email: string;
  };
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'ordered' | 'completed' | 'cancelled';
  priority: 'high' | 'medium' | 'low' | 'urgent';
  items: RequisitionItem[];
  totalAmount: number;
  urgentDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  approvedBy?: {
    _id: string;
    name: string;
  };
  approvalDate?: string;
  rejectionReason?: string;
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    draft: { variant: "outline" as const, label: "Draft", icon: FileText },
    submitted: { variant: "secondary" as const, label: "Submitted", icon: Clock },
    approved: { variant: "default" as const, label: "Approved", icon: CheckCircle },
    rejected: { variant: "destructive" as const, label: "Rejected", icon: XCircle },
    ordered: { variant: "default" as const, label: "Ordered", icon: CheckCircle },
    completed: { variant: "default" as const, label: "Completed", icon: CheckCircle },
    cancelled: { variant: "destructive" as const, label: "Cancelled", icon: XCircle }
  }

  const config = statusConfig[status as keyof typeof statusConfig] || {
    variant: "outline" as const,
    label: status,
    icon: FileText
  }
  
  const Icon = config.icon
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const getPriorityBadge = (priority: string) => {
  const priorityConfig = {
    high: { variant: "destructive" as const, label: "High Priority" },
    medium: { variant: "secondary" as const, label: "Medium Priority" },
    low: { variant: "outline" as const, label: "Low Priority" }
  }
  
  const config = priorityConfig[priority as keyof typeof priorityConfig] || { 
    variant: "outline" as const, 
    label: priority 
  }
  return <Badge variant={config.variant}>{config.label}</Badge>
}

export function PurchaseRequisitions() {
  const router = useRouter()
  const [requisitions, setRequisitions] = useState<Requisition[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRequisition, setSelectedRequisition] = useState<Requisition | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  // Bulk operations state
  const [selectedRequisitions, setSelectedRequisitions] = useState<string[]>([]);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isBulkUploadDialogOpen, setIsBulkUploadDialogOpen] = useState(false);

  // Fetch requisitions
  const fetchRequisitions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/procurement/requisition');
      if (!response.ok) {
        throw new Error('Failed to fetch requisitions');
      }
      const data = await response.json();
      console.log('API Response:', data); // Debug log
      // The API returns paginated data with docs property
      setRequisitions(data.docs || []);
    } catch (error) {
      console.error('Error fetching requisitions:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch requisitions',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRequisitions();
  }, []);

  // Filter requisitions with error handling
  const filteredRequisitions = requisitions.filter(req => {
    try {
      const matchesSearch = req.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           req.departmentId?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           req.requestedBy?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           req.requisitionNumber?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === "all" || req.status === statusFilter;
      const matchesPriority = priorityFilter === "all" || req.priority === priorityFilter;

      return matchesSearch && matchesStatus && matchesPriority;
    } catch (error) {
      console.error('Error filtering requisition:', req, error);
      return false;
    }
  });

  // Handler functions
  const handleCreateRequisition = () => {
    // Navigate to the create requisition page instead of showing dialog
    router.push('/dashboard/procurement/requisitions/create');
  };

  const handleEditRequisition = (requisition: Requisition) => {
    // Navigate to the edit page
    router.push(`/dashboard/procurement/requisitions/${requisition._id}/edit`);
  };

  const handleViewRequisition = (requisition: Requisition) => {
    // Navigate to the detail page instead of showing dialog
    router.push(`/dashboard/procurement/requisitions/${requisition._id}`);
  };

  const handleDeleteRequisition = async (requisition: Requisition) => {
    // Check if requisition can be deleted
    if (['approved', 'completed'].includes(requisition.status)) {
      toast({
        title: 'Cannot Delete',
        description: `Cannot delete requisition with status: ${requisition.status}`,
        variant: 'destructive',
      });
      return;
    }

    const reason = prompt(`Please provide a reason for deleting requisition "${requisition.requisitionNumber}":`);
    if (!reason || reason.trim().length < 10) {
      toast({
        title: 'Deletion Cancelled',
        description: 'A valid reason (minimum 10 characters) is required to delete a requisition',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/procurement/requisition/${requisition._id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deletionReason: reason.trim(),
          context: {
            department: 'Procurement',
            fiscalYear: new Date().getFullYear().toString()
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition deleted successfully with audit trail',
      });

      fetchRequisitions();
    } catch (error) {
      console.error('Error deleting requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete requisition',
        variant: 'destructive',
      });
    }
  };

  const handleApproveRequisition = async (requisition: Requisition) => {
    try {
      const response = await fetch(`/api/procurement/requisition/${requisition._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvalDate: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition approved successfully',
      });

      fetchRequisitions();
    } catch (error) {
      console.error('Error approving requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to approve requisition',
        variant: 'destructive',
      });
    }
  };

  const handleRejectRequisition = async (requisition: Requisition) => {
    const reason = prompt('Please provide a reason for rejecting this requisition:');
    if (!reason || reason.trim().length < 10) {
      toast({
        title: 'Rejection Cancelled',
        description: 'A valid reason is required to reject a requisition',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/procurement/requisition/${requisition._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          rejectionReason: reason.trim()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject requisition');
      }

      toast({
        title: 'Success',
        description: 'Requisition rejected successfully',
      });

      fetchRequisitions();
    } catch (error) {
      console.error('Error rejecting requisition:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reject requisition',
        variant: 'destructive',
      });
    }
  };

  // Bulk operations handlers
  const handleSelectRequisition = (requisitionId: string, selected: boolean) => {
    setSelectedRequisitions(prev =>
      selected
        ? [...prev, requisitionId]
        : prev.filter(id => id !== requisitionId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedRequisitions(selected ? (requisitions?.map(req => req?._id).filter(Boolean) || []) : []);
  };

  const handleBulkDelete = () => {
    if (selectedRequisitions.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select requisitions to delete',
        variant: 'destructive',
      });
      return;
    }
    setIsBulkDeleteDialogOpen(true);
  };

  const handleBulkUpload = () => {
    setIsBulkUploadDialogOpen(true);
  };

  const handleBulkOperationSuccess = () => {
    setSelectedRequisitions([]);
    fetchRequisitions();
  };

  const handleRefresh = () => {
    fetchRequisitions();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate statistics with null safety
  const totalRequisitions = requisitions?.length || 0;
  const pendingApproval = requisitions?.filter(r => r?.status === 'submitted')?.length || 0;
  const approved = requisitions?.filter(r => r?.status === 'approved')?.length || 0;
  const totalValue = requisitions?.reduce((sum, req) => sum + (req?.totalAmount || 0), 0) || 0;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Purchase Requisitions"
        text="Create and manage purchase requisitions for approval"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <Settings className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleBulkUpload}>
            <Upload className="mr-2 h-4 w-4" />
            Bulk Import
          </Button>
          <Button onClick={handleCreateRequisition}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Requisition
          </Button>
          
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requisitions</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalRequisitions}</div>
              <p className="text-xs text-muted-foreground">
                All requisitions
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingApproval}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting approval
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{approved}</div>
              <p className="text-xs text-muted-foreground">
                Ready for procurement
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Combined value
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search requisitions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="submitted">Submitted</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="ordered">Ordered</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="urgent">Urgent Priority</SelectItem>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="low">Low Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setPriorityFilter('all');
                }}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Requisitions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Requisitions ({filteredRequisitions.length})</CardTitle>
            <CardDescription>
              Manage and track purchase requisitions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Bulk Actions Bar */}
            {selectedRequisitions.length > 0 && (
              <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-md mb-4">
                <div className="text-sm">
                  {selectedRequisitions.length} {selectedRequisitions.length === 1 ? 'requisition' : 'requisitions'} selected
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="h-8 gap-1"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Selected
                </Button>
              </div>
            )}

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={(requisitions?.length || 0) > 0 && selectedRequisitions.length === (requisitions?.length || 0)}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </TableHead>
                    <TableHead>Requisition #</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Requestor</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="h-24 text-center">
                        Loading requisitions...
                      </TableCell>
                    </TableRow>
                  ) : filteredRequisitions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="h-24 text-center">
                        No requisitions found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRequisitions.map((requisition) => (
                      <TableRow key={requisition._id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedRequisitions.includes(requisition._id)}
                            onCheckedChange={(checked) => handleSelectRequisition(requisition._id, !!checked)}
                            aria-label={`Select ${requisition.requisitionNumber}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{requisition.requisitionNumber || 'N/A'}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{requisition.title || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                              {requisition.description || 'No description'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{requisition.departmentId?.name || 'N/A'}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{requisition.requestedBy?.name || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">{requisition.requestedBy?.email || 'N/A'}</div>
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(requisition.totalAmount || 0)}</TableCell>
                        <TableCell>
                          <Badge variant={
                            requisition.status === "approved" ? "default" :
                            requisition.status === "submitted" ? "secondary" :
                            requisition.status === "rejected" ? "destructive" :
                            requisition.status === "completed" ? "default" :
                            requisition.status === "ordered" ? "default" :
                            requisition.status === "cancelled" ? "destructive" : "outline"
                          }>
                            {requisition.status.replace("_", " ").toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            requisition.priority === "urgent" ? "destructive" :
                            requisition.priority === "high" ? "destructive" :
                            requisition.priority === "medium" ? "secondary" : "outline"
                          }>
                            {requisition.priority.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(requisition.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewRequisition(requisition)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditRequisition(requisition)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {requisition.status === 'submitted' && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleApproveRequisition(requisition)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRejectRequisition(requisition)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteRequisition(requisition)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Requisition Details Dialog */}
        <Dialog open={!!selectedRequisition} onOpenChange={() => setSelectedRequisition(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Requisition Details - {selectedRequisition?.requisitionNumber}</DialogTitle>
              <DialogDescription>
                View detailed information about this purchase requisition
              </DialogDescription>
            </DialogHeader>
            {selectedRequisition && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Title</Label>
                    <p className="text-sm text-muted-foreground">{selectedRequisition.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Department</Label>
                    <p className="text-sm text-muted-foreground">{selectedRequisition.departmentId?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Requestor</Label>
                    <p className="text-sm text-muted-foreground">{selectedRequisition.requestedBy?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Date Created</Label>
                    <p className="text-sm text-muted-foreground">{formatDate(selectedRequisition.createdAt)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Status</Label>
                    <div className="mt-1">{getStatusBadge(selectedRequisition.status)}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Priority</Label>
                    <div className="mt-1">{getPriorityBadge(selectedRequisition.priority)}</div>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Description</Label>
                  <p className="text-sm text-muted-foreground mt-1">{selectedRequisition.description}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium">Items</Label>
                  <Table className="mt-2">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedRequisition.items?.map((item: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>{item?.name || 'N/A'}</TableCell>
                          <TableCell>{item?.quantity || 0}</TableCell>
                          <TableCell>MWK {(item?.estimatedUnitPrice || 0).toLocaleString()}</TableCell>
                          <TableCell>MWK {(item?.estimatedTotal || 0).toLocaleString()}</TableCell>
                        </TableRow>
                      )) || []}
                    </TableBody>
                  </Table>
                  <div className="mt-4 text-right">
                    <p className="text-lg font-semibold">
                      Total: {formatCurrency(selectedRequisition.totalAmount)}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedRequisition(null)}>
                Close
              </Button>
              {selectedRequisition?.status === "submitted" && (
                <>
                  <Button variant="destructive">
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Bulk Upload Dialog */}
        <Dialog open={isBulkUploadDialogOpen} onOpenChange={setIsBulkUploadDialogOpen}>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Bulk Import Requisitions</DialogTitle>
              <DialogDescription>
                Import multiple requisitions from a CSV or Excel file
              </DialogDescription>
            </DialogHeader>
            <BulkRequisitionUpload
              onSuccess={() => {
                setIsBulkUploadDialogOpen(false);
                fetchRequisitions();
              }}
            />
          </DialogContent>
        </Dialog>

        {/* Bulk Delete Dialog */}
        <Dialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Bulk Delete Requisitions</DialogTitle>
              <DialogDescription>
                Delete multiple requisitions with audit trail
              </DialogDescription>
            </DialogHeader>
            <BulkRequisitionDelete
              selectedRequisitionIds={selectedRequisitions}
              selectedRequisitions={requisitions?.filter(req => req?._id && selectedRequisitions.includes(req._id)) || []}
              onSuccess={() => {
                setIsBulkDeleteDialogOpen(false);
                setSelectedRequisitions([]);
                fetchRequisitions();
              }}
              onCancel={() => {
                setIsBulkDeleteDialogOpen(false);
              }}
            />
          </DialogContent>
        </Dialog>
      </div>
    </DashboardShell>
  )
}

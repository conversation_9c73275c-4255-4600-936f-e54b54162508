"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { InventoryModal, type InventoryItem } from "@/components/procurement/modals/inventory-modal"
import { BulkInventoryUpload } from "@/components/procurement/inventory/bulk-inventory-upload"
import { BulkInventoryDelete } from "@/components/procurement/inventory/bulk-inventory-delete"
import {
  Package,
  Search,
  Filter,
  Plus,
  Edit,
  Eye,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Download,
  Upload
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface InventoryListProps {
  items: InventoryItem[]
  isLoading?: boolean
  onRefresh?: () => void
  onCreateItem?: (data: any) => Promise<void>
  onUpdateItem?: (id: string, data: any) => Promise<void>
  onDeleteItem?: (id: string) => Promise<void>
  onStockUpdate?: (id: string, quantity: number, operation: 'add' | 'remove') => Promise<void>
  categories?: Array<{ _id: string; name: string }>
  suppliers?: Array<{ _id: string; name: string }>
}

export function InventoryList({
  items,
  isLoading = false,
  onRefresh,
  onCreateItem,
  onUpdateItem,
  onDeleteItem,
  onStockUpdate,
  categories = [],
  suppliers = []
}: InventoryListProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [locationFilter, setLocationFilter] = useState<string>("")
  const [modalOpen, setModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [selectedItem, setSelectedItem] = useState<InventoryItem | undefined>()

  // Bulk operations state
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false)
  const [isBulkUploadDialogOpen, setIsBulkUploadDialogOpen] = useState(false)

  // Filter items based on search and filters
  const filteredItems = items.filter(item => {
    const matchesSearch = !searchTerm || 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.inventoryId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || item.status === statusFilter
    const matchesCategory = categoryFilter === "all" || item.category._id === categoryFilter
    const matchesLocation = !locationFilter || 
      item.location.toLowerCase().includes(locationFilter.toLowerCase())

    return matchesSearch && matchesStatus && matchesCategory && matchesLocation
  })

  // Calculate summary statistics
  const totalItems = items.length
  const lowStockItems = items.filter(item => item.currentStock <= item.minimumStock && item.currentStock > 0).length
  const outOfStockItems = items.filter(item => item.currentStock === 0).length
  const totalValue = items.reduce((sum, item) => sum + item.totalValue, 0)

  const getStatusBadge = (_status: string, currentStock: number, minimumStock: number) => {
    if (currentStock === 0) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Out of Stock
      </Badge>
    } else if (currentStock <= minimumStock) {
      return <Badge variant="secondary" className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Low Stock
      </Badge>
    } else {
      return <Badge variant="default" className="flex items-center gap-1">
        <Package className="h-3 w-3" />
        In Stock
      </Badge>
    }
  }

  const handleCreateItem = () => {
    setSelectedItem(undefined)
    setModalMode('create')
    setModalOpen(true)
  }

  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item)
    setModalMode('edit')
    setModalOpen(true)
  }

  const handleViewItem = (item: InventoryItem) => {
    setSelectedItem(item)
    setModalMode('view')
    setModalOpen(true)
  }

  // Bulk operations handlers
  const handleSelectItem = (itemId: string, selected: boolean) => {
    setSelectedItems(prev =>
      selected
        ? [...prev, itemId]
        : prev.filter(id => id !== itemId)
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedItems(selected ? items.map(item => item._id) : [])
  }

  const handleBulkDelete = () => {
    if (selectedItems.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select items to delete',
        variant: 'destructive',
      })
      return
    }
    setIsBulkDeleteDialogOpen(true)
  }

  const handleBulkUpload = () => {
    setIsBulkUploadDialogOpen(true)
  }

  const handleBulkOperationSuccess = () => {
    setSelectedItems([])
    onRefresh?.()
  }

  const handleDeleteItem = async (item: InventoryItem) => {
    if (onDeleteItem && window.confirm(`Are you sure you want to delete "${item.name}"?`)) {
      try {
        await onDeleteItem(item._id)
        toast({
          title: "Success",
          description: "Inventory item deleted successfully",
        })
        onRefresh?.()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete inventory item",
          variant: "destructive",
        })
      }
    }
  }

  const handleModalSubmit = async (data: any) => {
    try {
      if (modalMode === 'create' && onCreateItem) {
        await onCreateItem(data)
        toast({
          title: "Success",
          description: "Inventory item created successfully",
        })
      } else if (modalMode === 'edit' && selectedItem && onUpdateItem) {
        await onUpdateItem(selectedItem._id, data)
        toast({
          title: "Success",
          description: "Inventory item updated successfully",
        })
      }
      setModalOpen(false)
      onRefresh?.()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save inventory item",
        variant: "destructive",
      })
    }
  }



  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
            <p className="text-xs text-muted-foreground">
              Tracked inventory items
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{lowStockItems}</div>
            <p className="text-xs text-muted-foreground">
              Need restocking
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{outOfStockItems}</div>
            <p className="text-xs text-muted-foreground">
              Urgent restocking needed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">MWK {totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Current inventory value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
              <CardDescription>
                Filter and search inventory items
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onRefresh}>
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" onClick={handleBulkUpload}>
                <Upload className="mr-2 h-4 w-4" />
                Bulk Import
              </Button>
              <Button onClick={handleCreateItem}>
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="in_stock">In Stock</SelectItem>
                <SelectItem value="low_stock">Low Stock</SelectItem>
                <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                <SelectItem value="on_order">On Order</SelectItem>
                <SelectItem value="discontinued">Discontinued</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category._id} value={category._id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              placeholder="Filter by location"
              value={locationFilter}
              onChange={(e) => setLocationFilter(e.target.value)}
              className="w-full sm:w-[180px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Items ({filteredItems.length})</CardTitle>
          <CardDescription>
            Manage your procurement inventory items
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Bulk Actions Bar */}
          {selectedItems.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-md mb-4">
              <div className="text-sm">
                {selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'} selected
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
                className="h-8 gap-1"
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected
              </Button>
            </div>
          )}

          {/* Inventory Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40px]">
                    <Checkbox
                      checked={items.length > 0 && selectedItems.length === items.length}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </TableHead>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      Loading inventory items...
                    </TableCell>
                  </TableRow>
                ) : filteredItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      No inventory items found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredItems.map((item) => (
                    <TableRow key={item._id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(item._id)}
                          onCheckedChange={(checked) => handleSelectItem(item._id, !!checked)}
                          aria-label={`Select ${item.name}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{item.inventoryId}</div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-muted-foreground">{item.category.name}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.currentStock} {item.unit}</div>
                          <div className="text-sm text-muted-foreground">
                            Min: {item.minimumStock}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {item.currency} {item.unitPrice.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {item.currency} {item.totalValue.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.location}</div>
                          {item.warehouse && (
                            <div className="text-sm text-muted-foreground">{item.warehouse}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(item.status, item.currentStock, item.minimumStock)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewItem(item)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteItem(item)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Modal */}
      <InventoryModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        mode={modalMode}
        item={selectedItem}
        onSubmit={handleModalSubmit}
        onDelete={onDeleteItem}
        onStockUpdate={onStockUpdate}
        categories={categories}
        suppliers={suppliers}
        isLoading={isLoading}
      />

      {/* Bulk Upload Dialog */}
      <Dialog open={isBulkUploadDialogOpen} onOpenChange={setIsBulkUploadDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Import Inventory</DialogTitle>
            <DialogDescription>
              Import multiple inventory items from a CSV or Excel file
            </DialogDescription>
          </DialogHeader>
          <BulkInventoryUpload
            onSuccess={() => {
              handleBulkOperationSuccess()
              setIsBulkUploadDialogOpen(false)
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <BulkInventoryDelete
        selectedItems={items.filter(item => selectedItems.includes(item._id))}
        isOpen={isBulkDeleteDialogOpen}
        onOpenChange={setIsBulkDeleteDialogOpen}
        onSuccess={handleBulkOperationSuccess}
        onCancel={() => setSelectedItems([])}
      />
    </div>
  )
}

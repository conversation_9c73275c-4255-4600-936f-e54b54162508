// components\procurement\lists\contract-list.tsx
"use client"

import { useState, useMemo } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { DataTable } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  FileText, 
  Calendar, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Filter,
  Download,
  Plus
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Contract interface (based on the existing model)
interface Contract {
  _id: string
  contractNumber: string
  title: string
  supplierId: string
  supplierName?: string
  type: 'goods' | 'services' | 'maintenance' | 'consulting'
  status: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed'
  startDate: Date
  endDate: Date
  value: number
  currency: string
  department: string
  description?: string
  createdAt: Date
  updatedAt: Date
}

interface ContractListProps {
  contracts?: Contract[]
  isLoading?: boolean
  onEdit?: (contract: Contract) => void
  onView?: (contract: Contract) => void
  onDelete?: (contractId: string) => void
  onRenew?: (contract: Contract) => void
  onTerminate?: (contract: Contract) => void
}

// Status configuration
const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'bg-gray-100 text-gray-800',
    icon: FileText
  },
  active: { 
    label: 'Active', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  expired: { 
    label: 'Expired', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
  terminated: { 
    label: 'Terminated', 
    color: 'bg-orange-100 text-orange-800',
    icon: AlertTriangle
  },
  renewed: { 
    label: 'Renewed', 
    color: 'bg-blue-100 text-blue-800',
    icon: Clock
  },
}

// Type configuration
const typeConfig = {
  goods: { label: 'Goods', color: 'bg-blue-50 text-blue-700' },
  services: { label: 'Services', color: 'bg-purple-50 text-purple-700' },
  maintenance: { label: 'Maintenance', color: 'bg-orange-50 text-orange-700' },
  consulting: { label: 'Consulting', color: 'bg-green-50 text-green-700' },
}

export function ContractList({ 
  contracts = [], 
  isLoading = false,
  onEdit,
  onView,
  onDelete,
  onRenew,
  onTerminate
}: ContractListProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [departmentFilter, setDepartmentFilter] = useState<string>("all")

  // Get unique departments for filter
  const departments = useMemo(() => {
    const depts = [...new Set(contracts.map(c => c.department).filter(Boolean))]
    return depts.sort()
  }, [contracts])

  // Filter contracts based on selected filters
  const filteredContracts = useMemo(() => {
    return contracts.filter(contract => {
      if (statusFilter !== "all" && contract.status !== statusFilter) return false
      if (typeFilter !== "all" && contract.type !== typeFilter) return false
      if (departmentFilter !== "all" && contract.department !== departmentFilter) return false
      return true
    })
  }, [contracts, statusFilter, typeFilter, departmentFilter])

  // Check if contract is expiring soon (within 30 days)
  const isExpiringSoon = (endDate: Date) => {
    const today = new Date()
    const thirtyDaysFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000))
    return new Date(endDate) <= thirtyDaysFromNow && new Date(endDate) > today
  }

  // Check if contract is expired
  const isExpired = (endDate: Date) => {
    return new Date(endDate) < new Date()
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount)
  }

  // Define columns
  const columns: ColumnDef<Contract>[] = [
    {
      accessorKey: "contractNumber",
      header: "Contract #",
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue("contractNumber")}
        </div>
      ),
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => (
        <div className="max-w-[200px]">
          <div className="font-medium truncate">{row.getValue("title")}</div>
          <div className="text-sm text-muted-foreground truncate">
            {row.original.supplierName || 'Unknown Supplier'}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.getValue("type") as keyof typeof typeConfig
        const config = typeConfig[type]
        return (
          <Badge variant="outline" className={config.color}>
            {config.label}
          </Badge>
        )
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as keyof typeof statusConfig
        const config = statusConfig[status]
        const Icon = config.icon
        
        return (
          <div className="flex items-center gap-2">
            <Badge className={config.color}>
              <Icon className="h-3 w-3 mr-1" />
              {config.label}
            </Badge>
            {isExpiringSoon(row.original.endDate) && status === 'active' && (
              <span title="Expiring soon">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              </span>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "value",
      header: "Value",
      cell: ({ row }) => (
        <div className="font-medium">
          {formatCurrency(row.getValue("value"), row.original.currency)}
        </div>
      ),
    },
    {
      accessorKey: "startDate",
      header: "Start Date",
      cell: ({ row }) => (
        <div className="text-sm">
          {format(new Date(row.getValue("startDate")), "MMM dd, yyyy")}
        </div>
      ),
    },
    {
      accessorKey: "endDate",
      header: "End Date",
      cell: ({ row }) => {
        const endDate = new Date(row.getValue("endDate"))
        const expired = isExpired(endDate)
        const expiringSoon = isExpiringSoon(endDate)
        
        return (
          <div className={cn(
            "text-sm",
            expired && "text-red-600 font-medium",
            expiringSoon && !expired && "text-orange-600 font-medium"
          )}>
            {format(endDate, "MMM dd, yyyy")}
          </div>
        )
      },
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("department")}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const contract = row.original
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onView?.(contract)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(contract)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Contract
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {contract.status === 'active' && (
                <>
                  <DropdownMenuItem onClick={() => onRenew?.(contract)}>
                    <Calendar className="mr-2 h-4 w-4" />
                    Renew Contract
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onTerminate?.(contract)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Terminate
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-red-600"
                onClick={() => onDelete?.(contract._id)}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  // Get contract counts by status
  const statusCounts = useMemo(() => {
    const counts = contracts.reduce((acc, contract) => {
      acc[contract.status] = (acc[contract.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    counts.all = contracts.length
    counts.expiring = contracts.filter(c => 
      c.status === 'active' && isExpiringSoon(c.endDate)
    ).length
    
    return counts
  }, [contracts])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Contracts</h2>
          <p className="text-muted-foreground">
            Manage and track all procurement contracts
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Contract
        </Button>
      </div>

      {/* Status Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.all || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statusCounts.active || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{statusCounts.expiring || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{statusCounts.expired || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {Object.entries(statusConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.entries(typeConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Contract List</CardTitle>
          <CardDescription>
            {filteredContracts.length} of {contracts.length} contracts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={filteredContracts}
            isLoading={isLoading}
            searchKey="title"
            searchPlaceholder="Search contracts..."
          />
        </CardContent>
      </Card>
    </div>
  )
}

// components\procurement\lists\delivery-list.tsx
"use client"

import { useState, useMemo } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { DataTable } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Truck, 
  Package, 
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  MapPin,
  Calendar,
  Plus,
  Filter
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Delivery interface (based on the existing model)
interface DeliveryItem {
  itemName: string
  quantity: number
  receivedQuantity: number
  unit: string
  condition: 'good' | 'damaged' | 'missing'
}

interface Delivery {
  _id: string
  deliveryNumber: string
  purchaseOrderId: string
  purchaseOrderNumber?: string
  supplierId: string
  supplierName?: string
  status: 'scheduled' | 'in_transit' | 'delivered' | 'partially_received' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate: Date
  deliveredDate?: Date
  expectedDate: Date
  items: DeliveryItem[]
  deliveryAddress: {
    street: string
    city: string
    state?: string
    country: string
  }
  trackingNumber?: string
  notes?: string
  receivedBy?: string
  createdAt: Date
  updatedAt: Date
}

interface DeliveryListProps {
  deliveries?: Delivery[]
  isLoading?: boolean
  onEdit?: (delivery: Delivery) => void
  onView?: (delivery: Delivery) => void
  onReceive?: (delivery: Delivery) => void
  onTrack?: (delivery: Delivery) => void
  onCancel?: (deliveryId: string) => void
}

// Status configuration
const statusConfig = {
  scheduled: { 
    label: 'Scheduled', 
    color: 'bg-blue-100 text-blue-800',
    icon: Calendar
  },
  in_transit: { 
    label: 'In Transit', 
    color: 'bg-yellow-100 text-yellow-800',
    icon: Truck
  },
  delivered: { 
    label: 'Delivered', 
    color: 'bg-green-100 text-green-800',
    icon: Package
  },
  partially_received: { 
    label: 'Partially Received', 
    color: 'bg-orange-100 text-orange-800',
    icon: AlertTriangle
  },
  completed: { 
    label: 'Completed', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  cancelled: { 
    label: 'Cancelled', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
}

// Priority configuration
const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
}

export function DeliveryList({ 
  deliveries = [], 
  isLoading = false,
  onEdit,
  onView,
  onReceive,
  onTrack,
  onCancel
}: DeliveryListProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")

  // Filter deliveries based on selected filters
  const filteredDeliveries = useMemo(() => {
    return deliveries.filter(delivery => {
      if (statusFilter !== "all" && delivery.status !== statusFilter) return false
      if (priorityFilter !== "all" && delivery.priority !== priorityFilter) return false
      return true
    })
  }, [deliveries, statusFilter, priorityFilter])

  // Check if delivery is overdue
  const isOverdue = (expectedDate: Date, status: string) => {
    return new Date(expectedDate) < new Date() && 
           !['delivered', 'completed', 'cancelled'].includes(status)
  }

  // Calculate delivery progress
  const calculateProgress = (items: DeliveryItem[]) => {
    if (!items.length) return 0
    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0)
    const receivedQuantity = items.reduce((sum, item) => sum + item.receivedQuantity, 0)
    return Math.round((receivedQuantity / totalQuantity) * 100)
  }

  // Define columns
  const columns: ColumnDef<Delivery>[] = [
    {
      accessorKey: "deliveryNumber",
      header: "Delivery #",
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue("deliveryNumber")}
        </div>
      ),
    },
    {
      accessorKey: "purchaseOrderNumber",
      header: "PO #",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.purchaseOrderNumber || 'N/A'}
        </div>
      ),
    },
    {
      accessorKey: "supplierName",
      header: "Supplier",
      cell: ({ row }) => (
        <div className="max-w-[150px]">
          <div className="font-medium truncate">
            {row.original.supplierName || 'Unknown Supplier'}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as keyof typeof statusConfig
        const config = statusConfig[status]
        const Icon = config.icon
        const delivery = row.original
        
        return (
          <div className="flex items-center gap-2">
            <Badge className={config.color}>
              <Icon className="h-3 w-3 mr-1" />
              {config.label}
            </Badge>
            {isOverdue(delivery.expectedDate, delivery.status) && (
              <span title="Overdue">
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </span>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.getValue("priority") as keyof typeof priorityConfig
        const config = priorityConfig[priority]
        return (
          <Badge variant="outline" className={config.color}>
            {config.label}
          </Badge>
        )
      },
    },
    {
      accessorKey: "progress",
      header: "Progress",
      cell: ({ row }) => {
        const progress = calculateProgress(row.original.items)
        return (
          <div className="w-[100px]">
            <div className="flex items-center gap-2">
              <Progress value={progress} className="flex-1" />
              <span className="text-xs text-muted-foreground">{progress}%</span>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "expectedDate",
      header: "Expected Date",
      cell: ({ row }) => {
        const expectedDate = new Date(row.getValue("expectedDate"))
        const delivery = row.original
        const overdue = isOverdue(expectedDate, delivery.status)
        
        return (
          <div className={cn(
            "text-sm",
            overdue && "text-red-600 font-medium"
          )}>
            {format(expectedDate, "MMM dd, yyyy")}
          </div>
        )
      },
    },
    {
      accessorKey: "deliveredDate",
      header: "Delivered Date",
      cell: ({ row }) => {
        const deliveredDate = row.original.deliveredDate
        return (
          <div className="text-sm">
            {deliveredDate ? format(new Date(deliveredDate), "MMM dd, yyyy") : '-'}
          </div>
        )
      },
    },
    {
      accessorKey: "trackingNumber",
      header: "Tracking",
      cell: ({ row }) => {
        const trackingNumber = row.original.trackingNumber
        return (
          <div className="text-sm font-mono">
            {trackingNumber || '-'}
          </div>
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const delivery = row.original
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onView?.(delivery)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              {delivery.trackingNumber && (
                <DropdownMenuItem onClick={() => onTrack?.(delivery)}>
                  <MapPin className="mr-2 h-4 w-4" />
                  Track Delivery
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {['delivered', 'partially_received'].includes(delivery.status) && (
                <DropdownMenuItem onClick={() => onReceive?.(delivery)}>
                  <Package className="mr-2 h-4 w-4" />
                  Receive Items
                </DropdownMenuItem>
              )}
              {!['completed', 'cancelled'].includes(delivery.status) && (
                <DropdownMenuItem onClick={() => onEdit?.(delivery)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Delivery
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {!['completed', 'cancelled'].includes(delivery.status) && (
                <DropdownMenuItem 
                  className="text-red-600"
                  onClick={() => onCancel?.(delivery._id)}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Cancel Delivery
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  // Get delivery counts by status
  const statusCounts = useMemo(() => {
    const counts = deliveries.reduce((acc, delivery) => {
      acc[delivery.status] = (acc[delivery.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    counts.all = deliveries.length
    counts.overdue = deliveries.filter(d => 
      isOverdue(d.expectedDate, d.status)
    ).length
    
    return counts
  }, [deliveries])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Deliveries</h2>
          <p className="text-muted-foreground">
            Track and manage all delivery schedules and receipts
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Schedule Delivery
        </Button>
      </div>

      {/* Status Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Deliveries</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.all || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Truck className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{statusCounts.in_transit || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <Package className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statusCounts.delivered || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{statusCounts.overdue || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {Object.entries(statusConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Priority</label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  {Object.entries(priorityConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery List</CardTitle>
          <CardDescription>
            {filteredDeliveries.length} of {deliveries.length} deliveries
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={filteredDeliveries}
            isLoading={isLoading}
            searchKey="deliveryNumber"
            searchPlaceholder="Search deliveries..."
          />
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, useMemo } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { DataTable } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Folder, 
  FolderOpen,
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  Users,
  Plus,
  ChevronRight,
  ChevronDown
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { cn } from "@/lib/utils"

// Category interface (based on the existing model)
interface Category {
  _id: string
  name: string
  code: string
  description?: string
  parentId?: string
  level: number
  isActive: boolean
  approvalLimits: {
    level1: number
    level2: number
    level3: number
  }
  requiredApprovers: number
  allowedRoles: string[]
  subcategories?: Category[]
  itemCount?: number
  totalValue?: number
  createdAt: Date
  updatedAt: Date
}

interface CategoryListProps {
  categories?: Category[]
  isLoading?: boolean
  onEdit?: (category: Category) => void
  onView?: (category: Category) => void
  onDelete?: (categoryId: string) => void
  onToggleStatus?: (categoryId: string, isActive: boolean) => void
  onAddSubcategory?: (parentCategory: Category) => void
}

// Status configuration
const statusConfig = {
  active: { 
    label: 'Active', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  inactive: { 
    label: 'Inactive', 
    color: 'bg-gray-100 text-gray-800',
    icon: XCircle
  },
}

export function CategoryList({ 
  categories = [], 
  isLoading = false,
  onEdit,
  onView,
  onDelete,
  onToggleStatus,
  onAddSubcategory
}: CategoryListProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [levelFilter, setLevelFilter] = useState<string>("all")
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  // Build hierarchical structure
  const hierarchicalCategories = useMemo(() => {
    const categoryMap = new Map<string, Category>()
    const rootCategories: Category[] = []

    // First pass: create map and identify root categories
    categories.forEach(category => {
      categoryMap.set(category._id, { ...category, subcategories: [] })
      if (!category.parentId) {
        rootCategories.push(categoryMap.get(category._id)!)
      }
    })

    // Second pass: build hierarchy
    categories.forEach(category => {
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId)
        const child = categoryMap.get(category._id)
        if (parent && child) {
          parent.subcategories!.push(child)
        }
      }
    })

    return rootCategories
  }, [categories])

  // Flatten categories for table view
  const flattenedCategories = useMemo(() => {
    const flatten = (cats: Category[], level = 0): Category[] => {
      const result: Category[] = []
      cats.forEach(cat => {
        result.push({ ...cat, level })
        if (cat.subcategories && expandedCategories.has(cat._id)) {
          result.push(...flatten(cat.subcategories, level + 1))
        }
      })
      return result
    }
    return flatten(hierarchicalCategories)
  }, [hierarchicalCategories, expandedCategories])

  // Filter categories
  const filteredCategories = useMemo(() => {
    return flattenedCategories.filter(category => {
      if (statusFilter !== "all") {
        const isActive = statusFilter === "active"
        if (category.isActive !== isActive) return false
      }
      if (levelFilter !== "all") {
        const level = parseInt(levelFilter)
        if (category.level !== level) return false
      }
      return true
    })
  }, [flattenedCategories, statusFilter, levelFilter])

  // Toggle category expansion
  const toggleExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  // Define columns
  const columns: ColumnDef<Category>[] = [
    {
      accessorKey: "name",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original
        const hasSubcategories = category.subcategories && category.subcategories.length > 0
        const isExpanded = expandedCategories.has(category._id)
        
        return (
          <div className="flex items-center gap-2" style={{ paddingLeft: `${category.level * 20}px` }}>
            {hasSubcategories ? (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => toggleExpansion(category._id)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            ) : (
              <div className="w-6" />
            )}
            
            {hasSubcategories ? (
              isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-600" />
              ) : (
                <Folder className="h-4 w-4 text-blue-600" />
              )
            ) : (
              <div className="h-4 w-4 rounded bg-gray-200" />
            )}
            
            <div>
              <div className="font-medium">{category.name}</div>
              <div className="text-sm text-muted-foreground">{category.code}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => (
        <div className="max-w-[200px] text-sm text-muted-foreground truncate">
          {row.getValue("description") || '-'}
        </div>
      ),
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("isActive") as boolean
        const config = statusConfig[isActive ? 'active' : 'inactive']
        const Icon = config.icon
        
        return (
          <Badge className={config.color}>
            <Icon className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        )
      },
    },
    {
      accessorKey: "requiredApprovers",
      header: "Approvers",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.getValue("requiredApprovers")}</span>
        </div>
      ),
    },
    {
      accessorKey: "approvalLimits",
      header: "Approval Limits",
      cell: ({ row }) => {
        const limits = row.getValue("approvalLimits") as Category['approvalLimits']
        return (
          <div className="text-sm space-y-1">
            <div>L1: {formatCurrency(limits.level1)}</div>
            <div>L2: {formatCurrency(limits.level2)}</div>
            <div>L3: {formatCurrency(limits.level3)}</div>
          </div>
        )
      },
    },
    {
      accessorKey: "itemCount",
      header: "Items",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.itemCount || 0}
        </div>
      ),
    },
    {
      accessorKey: "totalValue",
      header: "Total Value",
      cell: ({ row }) => (
        <div className="text-sm font-medium">
          {row.original.totalValue ? formatCurrency(row.original.totalValue) : '-'}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const category = row.original
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onView?.(category)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(category)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Category
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onAddSubcategory?.(category)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Subcategory
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onToggleStatus?.(category._id, !category.isActive)}
              >
                {category.isActive ? (
                  <>
                    <XCircle className="mr-2 h-4 w-4" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-red-600"
                onClick={() => onDelete?.(category._id)}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  // Get category statistics
  const categoryStats = useMemo(() => {
    const stats = {
      total: categories.length,
      active: categories.filter(c => c.isActive).length,
      inactive: categories.filter(c => !c.isActive).length,
      rootCategories: categories.filter(c => !c.parentId).length,
      totalItems: categories.reduce((sum, c) => sum + (c.itemCount || 0), 0),
      totalValue: categories.reduce((sum, c) => sum + (c.totalValue || 0), 0),
    }
    return stats
  }, [categories])

  // Get unique levels for filter
  const availableLevels = useMemo(() => {
    const levels = [...new Set(categories.map(c => c.level))].sort()
    return levels
  }, [categories])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Categories</h2>
          <p className="text-muted-foreground">
            Manage procurement categories and approval hierarchies
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Category
        </Button>
      </div>

      {/* Statistics Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
            <Folder className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categoryStats.total}</div>
            <p className="text-xs text-muted-foreground">
              {categoryStats.rootCategories} root categories
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{categoryStats.active}</div>
            <p className="text-xs text-muted-foreground">
              {categoryStats.inactive} inactive
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{categoryStats.totalItems}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(categoryStats.totalValue)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Level</label>
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {availableLevels.map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      Level {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Actions</label>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setExpandedCategories(new Set(categories.map(c => c._id)))}
                >
                  Expand All
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setExpandedCategories(new Set())}
                >
                  Collapse All
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Category Hierarchy</CardTitle>
          <CardDescription>
            {filteredCategories.length} of {categories.length} categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={filteredCategories}
            isLoading={isLoading}
            searchKey="name"
            searchPlaceholder="Search categories..."
          />
        </CardContent>
      </Card>
    </div>
  )
}

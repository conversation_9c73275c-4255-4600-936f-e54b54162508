"use client";

import { useDatabaseRemoverStore, CategorizedModels, DatabaseModel } from '@/lib/stores/database-remover-store';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Database,
  Users,
  DollarSign,
  FileText,
  Shield,
  AlertTriangle,
  Activity,
  ChevronRight
} from 'lucide-react';

interface DatabaseModelsAccordionProps {
  models: CategorizedModels;
}

export function DatabaseModelsAccordion({ models }: DatabaseModelsAccordionProps) {
  const {
    expandedCategories,
    currentModel,
    toggleCategoryExpansion,
    fetchModelData
  } = useDatabaseRemoverStore();

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system': return <Shield className="h-4 w-4" />;
      case 'hr': return <Users className="h-4 w-4" />;
      case 'payroll': return <DollarSign className="h-4 w-4" />;
      case 'accounting': return <FileText className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'system': return 'System & Security';
      case 'hr': return 'Human Resources';
      case 'payroll': return 'Payroll & Compensation';
      case 'accounting': return 'Accounting & Finance';
      case 'other': return 'Other Modules';
      default: return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  const getCategoryDescription = (category: string) => {
    switch (category) {
      case 'system': return 'Core system data, users, and security logs';
      case 'hr': return 'Employee records and organizational structure';
      case 'payroll': return 'Salary structures and payroll processing data';
      case 'accounting': return 'Financial transactions and budget records';
      case 'other': return 'Additional system modules and data';
      default: return '';
    }
  };

  const getDangerLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getDangerLevelIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertTriangle className="h-3 w-3" />;
      case 'high': return <Shield className="h-3 w-3" />;
      default: return <Activity className="h-3 w-3" />;
    }
  };

  const handleModelSelect = async (model: DatabaseModel) => {
    await fetchModelData(model.name);
  };

  const renderModelItem = (model: DatabaseModel) => (
    <div
      key={model.name}
      className={`p-3 rounded-lg border transition-all hover:bg-accent/50 ${
        currentModel?.name === model.name 
          ? 'bg-accent border-primary' 
          : 'border-border'
      }`}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <h4 className="font-medium text-sm">{model.displayName}</h4>
          <Badge 
            variant={getDangerLevelColor(model.dangerLevel)}
            className="text-xs"
          >
            {getDangerLevelIcon(model.dangerLevel)}
            {model.dangerLevel}
          </Badge>
        </div>
        <Badge variant="outline" className="text-xs">
          {model.count.toLocaleString()}
        </Badge>
      </div>
      
      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
        {model.description}
      </p>
      
      {model.dependencies && model.dependencies.length > 0 && (
        <div className="mb-3">
          <p className="text-xs text-muted-foreground mb-1">Dependencies:</p>
          <div className="flex flex-wrap gap-1">
            {model.dependencies.map((dep) => (
              <Badge key={dep} variant="outline" className="text-xs">
                {dep}
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      <Button
        variant={currentModel?.name === model.name ? "default" : "outline"}
        size="sm"
        className="w-full"
        onClick={() => handleModelSelect(model)}
      >
        {currentModel?.name === model.name ? (
          <>
            <Database className="h-3 w-3 mr-2" />
            Selected
          </>
        ) : (
          <>
            <ChevronRight className="h-3 w-3 mr-2" />
            View Data
          </>
        )}
      </Button>
    </div>
  );

  const categories = Object.entries(models).filter(([_, categoryModels]) => 
    categoryModels && categoryModels.length > 0
  );

  return (
    <Accordion 
      type="multiple" 
      value={expandedCategories}
      onValueChange={(value) => {
        // Handle the value change manually since we're using a custom store
        expandedCategories.forEach(category => {
          if (!value.includes(category)) {
            toggleCategoryExpansion(category);
          }
        });
        value.forEach(category => {
          if (!expandedCategories.includes(category)) {
            toggleCategoryExpansion(category);
          }
        });
      }}
      className="w-full"
    >
      {categories.map(([category, categoryModels]) => {
        const totalRecords = categoryModels.reduce((sum, model) => sum + model.count, 0);
        const criticalModels = categoryModels.filter(model => 
          model.dangerLevel === 'critical' || model.dangerLevel === 'high'
        ).length;

        return (
          <AccordionItem key={category} value={category}>
            <AccordionTrigger className="hover:no-underline">
              <div className="flex items-center justify-between w-full mr-4">
                <div className="flex items-center gap-3">
                  {getCategoryIcon(category)}
                  <div className="text-left">
                    <div className="font-medium">{getCategoryTitle(category)}</div>
                    <div className="text-xs text-muted-foreground">
                      {getCategoryDescription(category)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {criticalModels > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {criticalModels}
                    </Badge>
                  )}
                  <Badge variant="outline" className="text-xs">
                    {categoryModels.length} models
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {totalRecords.toLocaleString()} records
                  </Badge>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-3 pt-2">
                {categoryModels.map(renderModelItem)}
              </div>
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
}

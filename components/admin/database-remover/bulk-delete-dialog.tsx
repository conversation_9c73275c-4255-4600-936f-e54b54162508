"use client";

import { useState } from 'react';
import { useDatabaseRemoverStore } from '@/lib/stores/database-remover-store';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
// import { Progress } from '@/components/ui/progress'; // Not needed for this component
import { 
  AlertTriangle, 
  Trash2, 
  Shield, 
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface BulkDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  modelName: string;
  selectedCount: number;
}

export function BulkDeleteDialog({ 
  open, 
  onOpenChange, 
  modelName, 
  selectedCount 
}: BulkDeleteDialogProps) {
  const {
    currentModel,
    deletionReason,
    confirmDeletion,
    deleteRelatedData,
    isDeleting,
    setDeletionReason,
    setConfirmDeletion,
    setDeleteRelatedData,
    bulkDeleteItems,
    clearSelection
  } = useDatabaseRemoverStore();

  const [result, setResult] = useState<any>(null);
  const [showResult, setShowResult] = useState(false);

  const handleDelete = async () => {
    if (!deletionReason.trim() || deletionReason.length < 10) {
      toast.error('Deletion reason must be at least 10 characters');
      return;
    }

    if (!confirmDeletion) {
      toast.error('Please confirm the deletion operation');
      return;
    }

    try {
      const deleteResult = await bulkDeleteItems(modelName);
      setResult(deleteResult);
      setShowResult(true);
      
      if (deleteResult.success) {
        toast.success(`Successfully deleted ${deleteResult.deletedCount} items`);
      } else {
        toast.error('Deletion operation failed');
      }
    } catch (error) {
      toast.error('Failed to delete items');
      console.error('Bulk delete error:', error);
    }
  };

  const handleClose = () => {
    setResult(null);
    setShowResult(false);
    setDeletionReason('');
    setConfirmDeletion(false);
    setDeleteRelatedData(false);
    clearSelection();
    onOpenChange(false);
  };

  const getDangerLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  if (showResult && result) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Deletion {result.success ? 'Completed' : 'Failed'}
            </DialogTitle>
            <DialogDescription>
              Results of the bulk deletion operation
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {result.deletedCount}
                </div>
                <div className="text-sm text-muted-foreground">Items Deleted</div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {result.auditRecords.length}
                </div>
                <div className="text-sm text-muted-foreground">Audit Records Created</div>
              </div>
            </div>

            {/* Errors */}
            {result.errors && result.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">Errors occurred:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {result.errors.map((error: string, index: number) => (
                      <li key={index} className="text-sm">{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Warnings */}
            {result.warnings && result.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">Warnings:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {result.warnings.map((warning: string, index: number) => (
                      <li key={index} className="text-sm">{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Audit Records */}
            {result.auditRecords && result.auditRecords.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Audit Trail Created</h4>
                <div className="text-sm text-muted-foreground">
                  {result.auditRecords.length} audit record(s) have been created for compliance tracking.
                  These records can be viewed in the Auditors module.
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button onClick={handleClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Bulk Delete Confirmation
          </DialogTitle>
          <DialogDescription>
            You are about to permanently delete {selectedCount} items from {currentModel?.displayName}.
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Model Info */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">{currentModel?.displayName}</h4>
              <Badge variant={getDangerLevelColor(currentModel?.dangerLevel || 'low')}>
                {currentModel?.dangerLevel} risk
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              {currentModel?.description}
            </p>
            <div className="text-sm">
              <span className="font-medium">Selected items:</span> {selectedCount}
            </div>
          </div>

          {/* Dependencies Warning */}
          {currentModel?.dependencies && currentModel.dependencies.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Warning: This model has dependencies</div>
                <div className="text-sm">
                  Deleting items from this model may affect related data in: {' '}
                  {currentModel.dependencies.join(', ')}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Deletion Reason */}
          <div className="space-y-2">
            <Label htmlFor="deletion-reason">
              Deletion Reason <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="deletion-reason"
              placeholder="Provide a detailed reason for this deletion (minimum 10 characters)..."
              value={deletionReason}
              onChange={(e) => setDeletionReason(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="text-xs text-muted-foreground">
              {deletionReason.length}/10 characters minimum
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="delete-related"
                checked={deleteRelatedData}
                onCheckedChange={(checked) => setDeleteRelatedData(checked as boolean)}
              />
              <Label htmlFor="delete-related" className="text-sm">
                Also delete related data (if applicable)
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="confirm-deletion"
                checked={confirmDeletion}
                onCheckedChange={(checked) => setConfirmDeletion(checked as boolean)}
              />
              <Label htmlFor="confirm-deletion" className="text-sm font-medium">
                I understand this action is permanent and cannot be undone
              </Label>
            </div>
          </div>

          {/* Final Warning */}
          <Alert variant="destructive">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <div className="font-medium mb-1">Final Warning</div>
              <div className="text-sm">
                This will permanently delete {selectedCount} records from the database. 
                Audit trails will be created, but the original data will be lost forever.
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={
              isDeleting || 
              !deletionReason.trim() || 
              deletionReason.length < 10 || 
              !confirmDeletion
            }
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete {selectedCount} Items
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

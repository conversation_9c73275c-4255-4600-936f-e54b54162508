"use client";

import { useState } from 'react';
import { useDatabaseRemoverStore, DatabaseModel } from '@/lib/stores/database-remover-store';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  Trash2, 
  Shield, 
  CheckCircle,
  XCircle,
  Loader2,
  Skull
} from 'lucide-react';
import { toast } from 'sonner';

interface DeleteAllDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  model: DatabaseModel;
}

export function DeleteAllDialog({ 
  open, 
  onOpenChange, 
  model 
}: DeleteAllDialogProps) {
  const {
    deletionReason,
    confirmDeletion,
    isDeleting,
    setDeletionReason,
    setConfirmDeletion,
    deleteAllModelData
  } = useDatabaseRemoverStore();

  const [confirmModelName, setConfirmModelName] = useState('');
  const [result, setResult] = useState<any>(null);
  const [showResult, setShowResult] = useState(false);

  const handleDeleteAll = async () => {
    if (!deletionReason.trim() || deletionReason.length < 10) {
      toast.error('Deletion reason must be at least 10 characters');
      return;
    }

    if (!confirmDeletion) {
      toast.error('Please confirm the deletion operation');
      return;
    }

    if (confirmModelName !== model.name) {
      toast.error('Model name confirmation does not match');
      return;
    }

    try {
      const deleteResult = await deleteAllModelData(model.name, confirmModelName);
      setResult(deleteResult);
      setShowResult(true);
      
      if (deleteResult.success) {
        toast.success(`Successfully deleted all data from ${model.displayName}`);
      } else {
        toast.error('Delete-all operation failed');
      }
    } catch (error) {
      toast.error('Failed to delete all model data');
      console.error('Delete-all error:', error);
    }
  };

  const handleClose = () => {
    setResult(null);
    setShowResult(false);
    setDeletionReason('');
    setConfirmDeletion(false);
    setConfirmModelName('');
    onOpenChange(false);
  };

  const getDangerLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const isFormValid = () => {
    return (
      deletionReason.trim().length >= 10 &&
      confirmDeletion &&
      confirmModelName === model.name
    );
  };

  if (showResult && result) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Delete-All Operation {result.success ? 'Completed' : 'Failed'}
            </DialogTitle>
            <DialogDescription>
              Results of the delete-all operation for {model.displayName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {result.deletedCount}
                </div>
                <div className="text-sm text-muted-foreground">Total Items Deleted</div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {result.auditRecords?.length || 0}
                </div>
                <div className="text-sm text-muted-foreground">Audit Records Created</div>
              </div>
            </div>

            {/* Success Message */}
            {result.success && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  All data has been successfully removed from {model.displayName}. 
                  The collection is now empty and ready for fresh data.
                </AlertDescription>
              </Alert>
            )}

            {/* Errors */}
            {result.errors && result.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">Errors occurred:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {result.errors.map((error: string, index: number) => (
                      <li key={index} className="text-sm">{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Warnings */}
            {result.warnings && result.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">Warnings:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {result.warnings.map((warning: string, index: number) => (
                      <li key={index} className="text-sm">{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Audit Information */}
            {result.success && (
              <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <h4 className="font-medium mb-2">Compliance & Audit</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>• Complete audit trail has been created for this operation</p>
                  <p>• All deletion records are stored for compliance purposes</p>
                  <p>• Operation details are logged with user information and timestamps</p>
                  <p>• Records can be reviewed in the Auditors module</p>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button onClick={handleClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Skull className="h-5 w-5 text-destructive" />
            Delete ALL Data - EXTREME CAUTION
          </DialogTitle>
          <DialogDescription>
            You are about to permanently delete ALL {model.count.toLocaleString()} records 
            from {model.displayName}. This will completely empty the collection.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Critical Warning */}
          <Alert variant="destructive">
            <Skull className="h-4 w-4" />
            <AlertDescription>
              <div className="font-bold mb-2">⚠️ EXTREME DANGER ⚠️</div>
              <div className="text-sm space-y-1">
                <p>• This will delete ALL {model.count.toLocaleString()} records in {model.displayName}</p>
                <p>• The entire collection will be emptied</p>
                <p>• This action is IRREVERSIBLE</p>
                <p>• Only proceed if you are absolutely certain</p>
              </div>
            </AlertDescription>
          </Alert>

          {/* Model Info */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">{model.displayName}</h4>
              <Badge variant={getDangerLevelColor(model.dangerLevel)}>
                {model.dangerLevel} risk
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              {model.description}
            </p>
            <div className="text-sm space-y-1">
              <div><span className="font-medium">Collection:</span> {model.collection}</div>
              <div><span className="font-medium">Total Records:</span> {model.count.toLocaleString()}</div>
            </div>
          </div>

          {/* Dependencies Warning */}
          {model.dependencies && model.dependencies.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Critical Dependencies Warning</div>
                <div className="text-sm">
                  This model has dependencies that may be affected: {' '}
                  <span className="font-medium">{model.dependencies.join(', ')}</span>
                  <br />
                  Deleting all data may cause system instability or data corruption.
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Deletion Reason */}
          <div className="space-y-2">
            <Label htmlFor="deletion-reason">
              Deletion Reason <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="deletion-reason"
              placeholder="Provide a detailed justification for deleting ALL data from this model (minimum 10 characters)..."
              value={deletionReason}
              onChange={(e) => setDeletionReason(e.target.value)}
              className="min-h-[120px]"
            />
            <div className="text-xs text-muted-foreground">
              {deletionReason.length}/10 characters minimum
            </div>
          </div>

          {/* Model Name Confirmation */}
          <div className="space-y-2">
            <Label htmlFor="confirm-model-name">
              Type the model name to confirm: <span className="font-mono font-bold">{model.name}</span>
            </Label>
            <Input
              id="confirm-model-name"
              placeholder={`Type "${model.name}" to confirm`}
              value={confirmModelName}
              onChange={(e) => setConfirmModelName(e.target.value)}
              className={confirmModelName === model.name ? 'border-green-500' : ''}
            />
          </div>

          {/* Final Confirmation */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="confirm-deletion"
              checked={confirmDeletion}
              onCheckedChange={(checked) => setConfirmDeletion(checked as boolean)}
            />
            <Label htmlFor="confirm-deletion" className="text-sm font-medium">
              I understand this will permanently delete ALL data and cannot be undone
            </Label>
          </div>

          {/* Ultimate Warning */}
          <Alert variant="destructive">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <div className="font-bold mb-1">FINAL WARNING</div>
              <div className="text-sm">
                You are about to delete {model.count.toLocaleString()} records permanently. 
                This action will empty the entire {model.displayName} collection. 
                Ensure you have proper backups before proceeding.
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteAll}
            disabled={isDeleting || !isFormValid()}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting All...
              </>
            ) : (
              <>
                <Skull className="h-4 w-4 mr-2" />
                DELETE ALL {model.count.toLocaleString()} RECORDS
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

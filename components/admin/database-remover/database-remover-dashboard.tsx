"use client";

import { useEffect } from 'react';
import { useDatabaseRemoverStore } from '@/lib/stores/database-remover-store';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { DatabaseModelsAccordion } from './database-models-accordion';
import { ModelDataViewer } from './model-data-viewer';
import { 
  Database, 
  RefreshCw, 
  AlertTriangle, 
  Shield, 
  Activity,
  HardDrive,
  Users,
  FileText
} from 'lucide-react';

export function DatabaseRemoverDashboard() {
  const {
    models,
    modelsSummary,
    currentModel,
    isLoadingModels,
    isRefreshing,
    error,
    fetchModels,
    refreshModels,
    resetState
  } = useDatabaseRemoverStore();

  useEffect(() => {
    fetchModels();
    return () => resetState();
  }, [fetchModels, resetState]);

  const handleRefresh = async () => {
    await refreshModels();
  };

  const getDangerLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getDangerLevelIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <Shield className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Database Data Remover"
        text="Super Admin tool for managing and removing database records across all system modules"
      >
        <div className="flex items-center gap-2">
          <Badge variant="destructive" className="text-white">
            <Shield className="h-3 w-3 mr-1" />
            Super Admin Only
          </Badge>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </DashboardHeader>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Models</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoadingModels ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{modelsSummary?.totalModels || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Database collections
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoadingModels ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="text-2xl font-bold">
                {modelsSummary?.totalRecords?.toLocaleString() || 0}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              Across all collections
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Models</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            {isLoadingModels ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold text-destructive">
                {modelsSummary?.criticalModels || 0}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              High-risk collections
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoadingModels ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-sm font-medium">
                {modelsSummary?.lastUpdated 
                  ? new Date(modelsSummary.lastUpdated).toLocaleTimeString()
                  : 'Never'
                }
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              Data refresh time
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-12">
        {/* Models List */}
        <div className="lg:col-span-5">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Models
              </CardTitle>
              <CardDescription>
                Select a model to view and manage its data. Models are grouped by category.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingModels ? (
                <div className="space-y-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : models ? (
                <DatabaseModelsAccordion models={models} />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No models available
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Model Data Viewer */}
        <div className="lg:col-span-7">
          {currentModel ? (
            <ModelDataViewer />
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Database className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a Model</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  Choose a database model from the list to view its data and perform management operations.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Warning Notice */}
      <Alert variant="destructive" className="mt-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Warning:</strong> This tool provides direct access to database records. 
          All deletion operations are logged and create audit trails, but deleted data may not be recoverable. 
          Use with extreme caution and ensure you have proper backups.
        </AlertDescription>
      </Alert>
    </DashboardShell>
  );
}

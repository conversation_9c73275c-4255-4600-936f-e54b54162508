"use client";

import { useState } from 'react';
import { useDatabaseRemoverStore } from '@/lib/stores/database-remover-store';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { BulkDeleteDialog } from './bulk-delete-dialog';
import { DeleteAllDialog } from './delete-all-dialog';
import { 
  Database, 
  Trash2, 
  AlertTriangle, 
  CheckSquare, 
  Square,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Eye
} from 'lucide-react';

export function ModelDataViewer() {
  const {
    currentModel,
    currentModelData,
    selectedItems,
    isLoadingModelData,
    currentPage,
    itemsPerPage,
    error,
    toggleItemSelection,
    selectAllItems,
    clearSelection,
    setCurrentPage,
    fetchModelData
  } = useDatabaseRemoverStore();

  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [showDeleteAllDialog, setShowDeleteAllDialog] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  if (!currentModel) return null;

  const handlePageChange = async (newPage: number) => {
    setCurrentPage(newPage);
    await fetchModelData(currentModel.name, newPage);
  };

  const handleSelectAll = () => {
    if (selectedItems.length === currentModelData?.items.length) {
      clearSelection();
    } else {
      selectAllItems();
    }
  };

  const toggleRowExpansion = (itemId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedRows(newExpanded);
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'object') {
      if (value instanceof Date) return new Date(value).toLocaleDateString();
      if (Array.isArray(value)) return `Array (${value.length} items)`;
      return 'Object';
    }
    if (typeof value === 'string' && value.length > 50) {
      return value.substring(0, 50) + '...';
    }
    return String(value);
  };

  const getDisplayFields = (items: any[]) => {
    if (!items || items.length === 0) return [];
    
    const firstItem = items[0];
    const commonFields = ['_id', 'name', 'title', 'email', 'createdAt', 'updatedAt'];
    const availableFields = Object.keys(firstItem);
    
    // Prioritize common fields, then add others
    const displayFields = commonFields.filter(field => availableFields.includes(field));
    const otherFields = availableFields
      .filter(field => !commonFields.includes(field))
      .slice(0, 5); // Limit to 5 additional fields
    
    return [...displayFields, ...otherFields].slice(0, 8); // Max 8 columns
  };

  const getDangerLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  if (isLoadingModelData) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const displayFields = getDisplayFields(currentModelData?.items || []);
  const pagination = currentModelData?.pagination;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              {currentModel.displayName}
            </CardTitle>
            <CardDescription className="flex items-center gap-2 mt-1">
              {currentModel.description}
              <Badge variant={getDangerLevelColor(currentModel.dangerLevel)}>
                {currentModel.dangerLevel}
              </Badge>
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {selectedItems.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowBulkDeleteDialog(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected ({selectedItems.length})
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDeleteAllDialog(true)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {currentModelData?.items && currentModelData.items.length > 0 ? (
          <>
            {/* Data Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedItems.length === currentModelData.items.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-12"></TableHead>
                    {displayFields.map((field) => (
                      <TableHead key={field} className="font-medium">
                        {field.charAt(0).toUpperCase() + field.slice(1)}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentModelData.items.map((item) => (
                    <>
                      <TableRow key={item._id} className="hover:bg-muted/50">
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.includes(item._id)}
                            onCheckedChange={() => toggleItemSelection(item._id)}
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleRowExpansion(item._id)}
                          >
                            {expandedRows.has(item._id) ? (
                              <ChevronLeft className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </TableCell>
                        {displayFields.map((field) => (
                          <TableCell key={field} className="max-w-xs">
                            <div className="truncate">
                              {formatValue(item[field])}
                            </div>
                          </TableCell>
                        ))}
                      </TableRow>
                      {expandedRows.has(item._id) && (
                        <TableRow>
                          <TableCell colSpan={displayFields.length + 2}>
                            <div className="p-4 bg-muted/30 rounded-md">
                              <h4 className="font-medium mb-2">Full Record Details</h4>
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                {Object.entries(item).map(([key, value]) => (
                                  <div key={key}>
                                    <span className="font-medium text-muted-foreground">
                                      {key}:
                                    </span>
                                    <div className="mt-1 break-all">
                                      {typeof value === 'object' 
                                        ? JSON.stringify(value, null, 2)
                                        : String(value || '-')
                                      }
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}
                  {pagination.totalCount} records
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No Data Found</h3>
            <p>This model contains no records.</p>
          </div>
        )}
      </CardContent>

      {/* Dialogs */}
      <BulkDeleteDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
        modelName={currentModel.name}
        selectedCount={selectedItems.length}
      />
      
      <DeleteAllDialog
        open={showDeleteAllDialog}
        onOpenChange={setShowDeleteAllDialog}
        model={currentModel}
      />
    </Card>
  );
}

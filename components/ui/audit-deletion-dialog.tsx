'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertTriangle, Shield, Clock, FileText } from "lucide-react"
import { AuditDeletionUI, AuditDeletionUIConfig } from "@/lib/utils/audit-deletion-ui"
import { DeletionReasonRequiredError } from "@/components/ui/deletion-reason-required-error"

interface AuditDeletionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (deletionReason: string) => Promise<void>;
  selectedCount: number;
  itemType: string;
  isProcessing?: boolean;
  config?: Partial<AuditDeletionUIConfig>;
  showComplianceInfo?: boolean;
}

export function AuditDeletionDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedCount,
  itemType,
  isProcessing = false,
  config,
  showComplianceInfo = true
}: AuditDeletionDialogProps) {
  const [deletionReason, setDeletionReason] = useState('')
  const [showReasonError, setShowReasonError] = useState(false)
  const [attemptedReason, setAttemptedReason] = useState('')

  // Get configuration for this item type
  const itemTypeConfig = AuditDeletionUI.getConfigForItemType(itemType as any)
  const finalConfig = { ...itemTypeConfig, ...config }

  // Get validation state
  const validationResult = AuditDeletionUI.handleDeletionReasonValidation(deletionReason, finalConfig)
  const inputProps = AuditDeletionUI.getDeletionReasonInputProps(
    deletionReason,
    setDeletionReason,
    finalConfig
  )

  const handleConfirm = async () => {
    if (!validationResult.validation.isValid) {
      setAttemptedReason(deletionReason)

      if (validationResult.shouldShowErrorComponent) {
        // Show the dedicated error component instead of toast
        setShowReasonError(true)
        onOpenChange(false) // Close the main dialog
        return
      } else if (validationResult.shouldShowToast) {
        // Show toast for other validation errors
        AuditDeletionUI.showValidationError(validationResult.validation)
        return
      }
    }

    try {
      await onConfirm(deletionReason.trim())
      setDeletionReason('') // Reset on success
      setAttemptedReason('')
    } catch (error) {
      // Error handling is done by the parent component
    }
  }

  const handleCancel = () => {
    setDeletionReason('')
    setAttemptedReason('')
    onOpenChange(false)
  }

  const handleTryAgain = () => {
    setShowReasonError(false)
    onOpenChange(true) // Reopen the main dialog
  }

  return (
    <>
      {/* Deletion Reason Required Error Component */}
      <DeletionReasonRequiredError
        open={showReasonError}
        onOpenChange={setShowReasonError}
        onTryAgain={handleTryAgain}
        selectedCount={selectedCount}
        itemType={itemType}
        attemptedReason={attemptedReason}
        showComplianceInfo={showComplianceInfo}
      />

      {/* Main Deletion Dialog */}
      <AlertDialog open={open} onOpenChange={onOpenChange}>
        <AlertDialogContent className="max-w-2xl">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            {AuditDeletionUI.getContextualTitle(itemType, selectedCount)}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base">
            {AuditDeletionUI.getContextualDescription(itemType, selectedCount)}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Selection Summary */}
          <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-red-600" />
              <span className="font-medium text-red-800">
                {selectedCount} {itemType}{selectedCount > 1 ? 's' : ''} selected for deletion
              </span>
            </div>
            <Badge variant="destructive">Permanent Action</Badge>
          </div>

          {/* Compliance Information */}
          {showComplianceInfo && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>Government Audit Compliance:</strong> All deletions are recorded in the audit trail 
                with your user information, timestamp, and reason. This ensures full transparency and 
                compliance with government auditing standards.
              </AlertDescription>
            </Alert>
          )}

          {/* Warning Message */}
          {finalConfig.warningMessage && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> {finalConfig.warningMessage}
              </AlertDescription>
            </Alert>
          )}

          <Separator />

          {/* Deletion Reason Input */}
          <div className="space-y-3">
            <Label htmlFor="deletionReason" className="text-base font-medium text-red-700">
              Deletion Reason (Required for Audit Compliance) *
            </Label>
            
            <Textarea
              id="deletionReason"
              placeholder={finalConfig.placeholder}
              value={deletionReason}
              onChange={inputProps.onChange}
              className={inputProps.className}
              rows={4}
              maxLength={finalConfig.maxLength}
              required
            />

            {/* Character Count and Validation */}
            <div className="flex justify-between items-start text-xs">
              <div className="space-y-1">
                <span className={inputProps.characterCountClass}>
                  {validationResult.validation.characterCount}/{finalConfig.minLength} minimum characters
                </span>
                {!validationResult.validation.isValid && validationResult.validation.error && (
                  <p className="text-red-500 font-medium">
                    {validationResult.validation.error}
                  </p>
                )}
              </div>
              <span className="text-muted-foreground">
                {validationResult.validation.remainingCharacters} characters remaining
              </span>
            </div>

            {/* Validation Progress */}
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div
                className={`h-1 rounded-full transition-all duration-300 ${
                  validationResult.validation.isValid ? 'bg-green-500' :
                  validationResult.validation.characterCount > 0 ? 'bg-yellow-500' : 'bg-gray-300'
                }`}
                style={{
                  width: `${Math.min((validationResult.validation.characterCount / finalConfig.minLength) * 100, 100)}%`
                }}
              />
            </div>
          </div>

          {/* Audit Trail Information */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start gap-2">
              <Clock className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Audit Trail Details:</p>
                <ul className="mt-1 space-y-1 text-xs">
                  <li>• Items will be moved to secure audit storage</li>
                  <li>• Retention period: 7 years (government standard)</li>
                  <li>• Accessible only to AUDITOR and SUPER_ADMIN roles</li>
                  <li>• Your deletion reason will be permanently recorded</li>
                  <li>• Action timestamp and user details will be logged</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isProcessing}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!validationResult.validation.isValid || isProcessing}
            className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processing...
              </>
            ) : (
              `Delete ${selectedCount} ${itemType}${selectedCount > 1 ? 's' : ''}`
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default AuditDeletionDialog

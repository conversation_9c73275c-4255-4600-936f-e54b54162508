'use client';

import React from 'react';
import { CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProcessingOverlayProps {
  isVisible: boolean;
  isProcessing: boolean;
  isSuccess: boolean;
  processingText?: string;
  successText?: string;
  onComplete?: () => void;
  autoHideDelay?: number; // milliseconds
}

export function ProcessingOverlay({
  isVisible,
  isProcessing,
  isSuccess,
  processingText = 'Processing...',
  successText = 'Success!',
  onComplete,
  autoHideDelay = 2000
}: ProcessingOverlayProps) {
  // Auto-hide after success
  React.useEffect(() => {
    if (isSuccess && autoHideDelay > 0) {
      const timer = setTimeout(() => {
        onComplete?.();
      }, autoHideDelay);
      return () => clearTimeout(timer);
    }
  }, [isSuccess, autoHideDelay, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className={cn(
          "absolute inset-0 transition-all duration-300",
          isProcessing && "bg-black/20 backdrop-blur-sm",
          isSuccess && "bg-green-500/10 backdrop-blur-sm"
        )}
      />
      
      {/* Content */}
      <div 
        className={cn(
          "relative z-10 flex flex-col items-center justify-center p-8 rounded-2xl shadow-2xl transition-all duration-500 transform",
          isProcessing && "bg-white/95 border border-gray-200/50 scale-100",
          isSuccess && "bg-green-50/95 border border-green-200/50 scale-110"
        )}
      >
        {/* Processing State */}
        {isProcessing && (
          <>
            <div className="relative mb-4">
              <Loader2 className="h-12 w-12 text-blue-600 animate-spin" />
              {/* Pulsing ring */}
              <div className="absolute inset-0 h-12 w-12 border-2 border-blue-200 rounded-full animate-pulse" />
            </div>
            <p className="text-lg font-medium text-gray-900 mb-2">
              {processingText}
            </p>
            <p className="text-sm text-gray-600 text-center max-w-xs">
              Please wait while we update the status...
            </p>
          </>
        )}

        {/* Success State */}
        {isSuccess && (
          <>
            <div className="relative mb-4">
              <CheckCircle className="h-12 w-12 text-green-600 animate-pulse" />
              {/* Success ring animation */}
              <div className="absolute inset-0 h-12 w-12 border-2 border-green-300 rounded-full animate-ping opacity-75" />
            </div>
            <p className="text-lg font-medium text-green-900 mb-2">
              {successText}
            </p>
            <p className="text-sm text-green-700 text-center max-w-xs">
              Status updated successfully!
            </p>
          </>
        )}
      </div>
    </div>
  );
}

// Hook for managing processing overlay state
export function useProcessingOverlay() {
  const [isVisible, setIsVisible] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);

  const showProcessing = React.useCallback((text?: string) => {
    setIsVisible(true);
    setIsProcessing(true);
    setIsSuccess(false);
  }, []);

  const showSuccess = React.useCallback((text?: string) => {
    setIsProcessing(false);
    setIsSuccess(true);
  }, []);

  const hide = React.useCallback(() => {
    setIsVisible(false);
    setIsProcessing(false);
    setIsSuccess(false);
  }, []);

  const reset = React.useCallback(() => {
    setIsVisible(false);
    setIsProcessing(false);
    setIsSuccess(false);
  }, []);

  return {
    isVisible,
    isProcessing,
    isSuccess,
    showProcessing,
    showSuccess,
    hide,
    reset
  };
}

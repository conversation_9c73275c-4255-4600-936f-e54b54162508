// components/errors/error-overlay.tsx
"use client"

import { useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  AlertTriangle,
  XCircle,
  AlertCircle,
  Info,
  ExternalLink,
  RefreshCw,
  Bug,
  ChevronDown,
  ChevronUp,
  Copy,
  CheckCircle,
  Loader2
} from "lucide-react"

export interface ErrorAction {
  label: string
  action: string
  type: 'button' | 'link' | 'retry'
  variant?: 'primary' | 'secondary' | 'destructive'
  url?: string
  data?: Record<string, any>
}

export interface StructuredError {
  id: string
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  code: string
  message: string
  userMessage: string
  details?: string
  suggestions?: string[]
  actions?: ErrorAction[]
  timestamp: string
  context?: Record<string, any>
}

interface ErrorOverlayProps {
  error: StructuredError | null
  isOpen: boolean
  onClose: () => void
  onAction?: (action: string, data?: Record<string, any>) => void
  isActionLoading?: boolean
  loadingAction?: string
}

export function ErrorOverlay({
  error,
  isOpen,
  onClose,
  onAction,
  isActionLoading = false,
  loadingAction
}: ErrorOverlayProps) {
  const router = useRouter()
  const [showDetails, setShowDetails] = useState(false)
  const [copied, setCopied] = useState(false)

  // Early return if no error or dialog is not open
  if (!error || !isOpen) {
    return null
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'HIGH':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'MEDIUM':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'LOW':
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'LOW':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const handleAction = async (action: ErrorAction) => {
    if (action.type === 'link' && action.url) {
      if (action.url.startsWith('http')) {
        window.open(action.url, '_blank')
      } else {
        router.push(action.url)
      }
    } else if (action.action === 'view-details') {
      // Navigate to error details page with error data
      const errorData = encodeURIComponent(JSON.stringify(error))
      router.push(`/dashboard/error-details?error=${errorData}`)
    } else if (onAction) {
      onAction(action.action, action.data)
    }
  }

  const copyErrorDetails = async () => {
    const errorDetails = {
      id: error.id,
      code: error.code,
      message: error.message,
      timestamp: error.timestamp,
      type: error.type,
      severity: error.severity
    }

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy error details:', err)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center gap-3">
            {getSeverityIcon(error.severity)}
            <div className="flex-1">
              <DialogTitle className="text-lg font-semibold">
                System Error Encountered
              </DialogTitle>
              <DialogDescription className="mt-1">
                Error ID: {error.id}
              </DialogDescription>
            </div>
            <Badge className={getSeverityColor(error.severity)}>
              {error.severity}
            </Badge>
          </div>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-4">
            {/* User Message */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {error.userMessage}
              </AlertDescription>
            </Alert>

            {/* Error Code and Type */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-muted-foreground">Error Code:</span>
                <div className="font-mono bg-muted px-2 py-1 rounded mt-1">
                  {error.code}
                </div>
              </div>
              <div>
                <span className="font-medium text-muted-foreground">Error Type:</span>
                <div className="font-mono bg-muted px-2 py-1 rounded mt-1">
                  {error.type}
                </div>
              </div>
            </div>

            {/* Suggestions */}
            {error.suggestions && error.suggestions.length > 0 && (
              <div>
                <h4 className="font-medium text-sm mb-2">Suggested Solutions:</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {error.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            {error.actions && error.actions.length > 0 && (
              <div>
                <h4 className="font-medium text-sm mb-3">Available Actions:</h4>
                <div className="flex flex-wrap gap-2">
                  {error.actions.map((action, index) => {
                    const isCurrentActionLoading = isActionLoading && loadingAction === action.action
                    return (
                      <Button
                        key={index}
                        variant={action.variant === 'primary' ? 'default' :
                                action.variant === 'destructive' ? 'destructive' : 'outline'}
                        size="sm"
                        onClick={() => handleAction(action)}
                        disabled={isActionLoading}
                        className="text-xs"
                      >
                        {isCurrentActionLoading ? (
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        ) : (
                          <>
                            {action.type === 'link' && <ExternalLink className="mr-1 h-3 w-3" />}
                            {action.action === 'retry' && <RefreshCw className="mr-1 h-3 w-3" />}
                            {action.action === 'debug' && <Bug className="mr-1 h-3 w-3" />}
                          </>
                        )}
                        {isCurrentActionLoading ? 'Processing...' : action.label}
                      </Button>
                    )
                  })}
                </div>
              </div>
            )}

            <Separator />

            {/* Technical Details Toggle */}
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
                className="text-xs text-muted-foreground hover:text-foreground"
              >
                {showDetails ? (
                  <>
                    <ChevronUp className="mr-1 h-3 w-3" />
                    Hide Technical Details
                  </>
                ) : (
                  <>
                    <ChevronDown className="mr-1 h-3 w-3" />
                    Show Technical Details
                  </>
                )}
              </Button>

              {showDetails && (
                <div className="mt-3 space-y-3">
                  {/* Technical Message */}
                  <div>
                    <span className="font-medium text-xs text-muted-foreground">Technical Message:</span>
                    <div className="font-mono text-xs bg-muted p-2 rounded mt-1 break-all">
                      {error.message}
                    </div>
                  </div>

                  {/* Additional Details */}
                  {error.details && (
                    <div>
                      <span className="font-medium text-xs text-muted-foreground">Details:</span>
                      <div className="font-mono text-xs bg-muted p-2 rounded mt-1 break-all">
                        {error.details}
                      </div>
                    </div>
                  )}

                  {/* Timestamp */}
                  <div>
                    <span className="font-medium text-xs text-muted-foreground">Timestamp:</span>
                    <div className="font-mono text-xs bg-muted p-2 rounded mt-1">
                      {new Date(error.timestamp).toLocaleString()}
                    </div>
                  </div>

                  {/* Copy Error Details */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyErrorDetails}
                    className="text-xs"
                  >
                    {copied ? (
                      <>
                        <CheckCircle className="mr-1 h-3 w-3 text-green-500" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="mr-1 h-3 w-3" />
                        Copy Error Details
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => {
              const errorData = encodeURIComponent(JSON.stringify(error))
              router.push(`/dashboard/error-details?error=${errorData}`)
            }}
            className="text-xs"
          >
            <ExternalLink className="mr-1 h-3 w-3" />
            View Full Details
          </Button>
          <Button onClick={onClose} className="text-xs">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

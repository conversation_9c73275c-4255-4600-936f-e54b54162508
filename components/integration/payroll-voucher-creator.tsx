"use client"

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Users,
  DollarSign,
  Loader2,
  ArrowRight,
  Eye
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";

interface PayrollRun {
  id: string;
  name: string;
  description: string;
  payPeriod: {
    month: number;
    year: number;
  };
  status: string;
  totalEmployees: number;
  totalGrossSalary: number;
  totalDeductions: number;
  totalTax: number;
  totalNetSalary: number;
  currency: string;
  createdBy: {
    name: string;
    email: string;
  };
  approvedBy: {
    name: string;
    email: string;
  };
  approvedAt: string;
  voucherStatus: string;
}

interface PayrollRunSummary {
  totalPayrollRuns: number;
  totalAmount: number;
  totalEmployees: number;
  oldestRun: PayrollRun | null;
}

export function PayrollVoucherCreator() {
  const [payrollRuns, setPayrollRuns] = useState<PayrollRun[]>([]);
  const [summary, setSummary] = useState<PayrollRunSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedPayrollRun, setSelectedPayrollRun] = useState<PayrollRun | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [userPermissions, setUserPermissions] = useState<any>(null);

  // Fetch payroll runs ready for voucher creation
  const fetchPayrollRuns = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/integration/payroll-voucher/create');
      
      if (!response.ok) {
        throw new Error('Failed to fetch payroll runs');
      }

      const data = await response.json();
      
      if (data.success) {
        setPayrollRuns(data.data.payrollRuns);
        setSummary(data.data.summary);
        setUserPermissions(data.data.userPermissions);
      } else {
        throw new Error(data.error || 'Failed to fetch payroll runs');
      }
    } catch (error: any) {
      console.error('Error fetching payroll runs:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch payroll runs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Create voucher from payroll run
  const createVoucher = async (payrollRunId: string, autoApprove: boolean = false) => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/integration/payroll-voucher/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payrollRunId,
          autoApprove
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create voucher');
      }

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        });
        
        // Refresh the list
        await fetchPayrollRuns();
        setShowCreateDialog(false);
        setSelectedPayrollRun(null);
      } else {
        throw new Error(data.error || 'Failed to create voucher');
      }
    } catch (error: any) {
      console.error('Error creating voucher:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create voucher",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'default';
      case 'not_created':
        return 'secondary';
      case 'created':
        return 'outline';
      default:
        return 'outline';
    }
  };

  useEffect(() => {
    fetchPayrollRuns();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading payroll runs...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ready for Vouchers</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalPayrollRuns}</div>
              <p className="text-xs text-muted-foreground">
                Approved payroll runs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalAmount)}</div>
              <p className="text-xs text-muted-foreground">
                Net salary payments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalEmployees}</div>
              <p className="text-xs text-muted-foreground">
                Across all payroll runs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Oldest Run</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {summary.oldestRun ? 
                  format(new Date(summary.oldestRun.approvedAt), 'dd/MM') : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                Needs attention
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Alert for urgent actions */}
      {summary && summary.oldestRun && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Payroll run "{summary.oldestRun.name}" has been approved since{' '}
            {format(new Date(summary.oldestRun.approvedAt), 'dd/MM/yyyy')} and needs a voucher created.
          </AlertDescription>
        </Alert>
      )}

      {/* Payroll Runs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payroll Runs Ready for Vouchers</CardTitle>
          <CardDescription>
            Approved payroll runs that need payment vouchers created
          </CardDescription>
        </CardHeader>
        <CardContent>
          {payrollRuns.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <p className="text-muted-foreground">No payroll runs ready for voucher creation</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Payroll Run</TableHead>
                  <TableHead>Pay Period</TableHead>
                  <TableHead>Employees</TableHead>
                  <TableHead>Net Amount</TableHead>
                  <TableHead>Approved</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payrollRuns.map((run) => (
                  <TableRow key={run.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{run.name}</div>
                        <div className="text-sm text-muted-foreground truncate max-w-xs">
                          {run.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {format(new Date(run.payPeriod.year, run.payPeriod.month - 1), 'MMMM yyyy')}
                    </TableCell>
                    <TableCell>{run.totalEmployees}</TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(run.totalNetSalary)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {format(new Date(run.approvedAt), 'dd/MM/yyyy')}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        by {run.approvedBy.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(run.voucherStatus)}>
                        {run.voucherStatus.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedPayrollRun(run);
                            setShowCreateDialog(true);
                          }}
                          disabled={actionLoading}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Create Voucher
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // Navigate to payroll run details
                            window.open(`/dashboard/payroll/run/${run.id}`, '_blank');
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Voucher Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Payment Voucher</DialogTitle>
            <DialogDescription>
              Create a payment voucher for the selected payroll run
            </DialogDescription>
          </DialogHeader>

          {selectedPayrollRun && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Payroll Run</label>
                  <p className="text-sm text-muted-foreground">{selectedPayrollRun.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Pay Period</label>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(selectedPayrollRun.payPeriod.year, selectedPayrollRun.payPeriod.month - 1), 'MMMM yyyy')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Employees</label>
                  <p className="text-sm text-muted-foreground">{selectedPayrollRun.totalEmployees}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Net Amount</label>
                  <p className="text-sm font-medium">{formatCurrency(selectedPayrollRun.totalNetSalary)}</p>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  This will create a payment voucher that will require approval before payment processing.
                  The voucher will go through the standard approval workflow (HR → Finance → Admin for large amounts).
                </AlertDescription>
              </Alert>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            {userPermissions?.canAutoApprove && (
              <Button
                variant="secondary"
                onClick={() => selectedPayrollRun && createVoucher(selectedPayrollRun.id, true)}
                disabled={actionLoading}
              >
                {actionLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Create & Auto-Approve
              </Button>
            )}
            <Button
              onClick={() => selectedPayrollRun && createVoucher(selectedPayrollRun.id, false)}
              disabled={actionLoading}
            >
              {actionLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Create Voucher
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

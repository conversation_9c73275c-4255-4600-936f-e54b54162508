// components/payroll/employee-salary/bulk-employee-salary-upload.tsx
"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Upload,
  Download,
  FileSpreadsheet,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Refresh<PERSON>w
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface BulkUploadResult {
  totalRows: number
  successCount: number
  errorCount: number
  skippedCount: number
  errors: Array<{ row: number; error: string }>
  warnings: Array<{ row: number; warning: string }>
  skipped: Array<{
    row: number;
    reason: string;
    employeeName?: string;
    employeeEmail?: string;
    employeeId?: string;
    existingSalary?: number
  }>
  imported: Array<{
    row: number;
    employeeName: string;
    employeeEmail: string;
    basicSalary: number;
  }>
}

interface BulkEmployeeSalaryUploadProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function BulkEmployeeSalaryUpload({ isOpen, onClose, onSuccess }: BulkEmployeeSalaryUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [result, setResult] = useState<BulkUploadResult | null>(null)
  const [showResults, setShowResults] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      // Validate file type
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase()
      if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
        toast({
          title: "Invalid file type",
          description: "Please select a CSV or Excel file (.csv, .xlsx, .xls)",
          variant: "destructive",
        })
        return
      }

      // Validate file size (max 10MB)
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select a file smaller than 10MB",
          variant: "destructive",
        })
        return
      }

      setFile(selectedFile)
      setResult(null)
      setShowResults(false)
    }
  }

  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a file to upload",
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Create a FormData object
      const formData = new FormData()
      formData.append('file', file)

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 500)

      // Send the file to the server
      const response = await fetch('/api/payroll/employee-salaries/bulk-import', {
        method: 'POST',
        body: formData,
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upload file')
      }

      const data = await response.json()
      setResult(data.data)
      setShowResults(true)

      // Show enhanced success toast
      toast({
        title: "Upload completed",
        description: `Processed ${data.data.totalRows} rows. ${data.data.successCount} imported, ${data.data.skippedCount} skipped, ${data.data.errorCount} errors.`,
      })

      // Call onSuccess callback if provided
      if (onSuccess && data.data.successCount > 0) {
        onSuccess()
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/payroll/employee-salaries/template')

      if (!response.ok) {
        throw new Error('Failed to download template')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = 'employee-salary-import-template.xlsx'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Template downloaded",
        description: "The employee salary import template has been downloaded successfully.",
      })
    } catch (error) {
      console.error('Template download error:', error)
      toast({
        title: "Download failed",
        description: "Failed to download the template file",
        variant: "destructive",
      })
    }
  }

  const resetUpload = () => {
    setFile(null)
    setResult(null)
    setShowResults(false)
    setUploadProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleClose = () => {
    resetUpload()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Bulk Employee Salary Import
          </DialogTitle>
          <DialogDescription>
            Upload an Excel or CSV file to import multiple employee salary records at once.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Step 1: Download Enhanced Template</CardTitle>
              <CardDescription>
                Download the template containing ALL active employees from your system with pre-filled data for validation and convenience.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                onClick={downloadTemplate}
                className="w-full sm:w-auto"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </CardContent>
          </Card>

          {/* File Upload Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Step 2: Upload File</CardTitle>
              <CardDescription>
                Select your completed Excel or CSV file to upload.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Select File
                </Button>
                {file && (
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-muted-foreground">{file.name}</span>
                    <Badge variant="secondary">{(file.size / 1024).toFixed(1)} KB</Badge>
                  </div>
                )}
              </div>

              {file && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleUpload}
                    disabled={isUploading}
                    className="flex-1 sm:flex-none"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload & Process
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={resetUpload}
                    disabled={isUploading}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </div>
              )}

              {isUploading && (
                <div className="space-y-2">
                  <Progress value={uploadProgress} className="w-full" />
                  <p className="text-sm text-muted-foreground text-center">
                    Processing file... {uploadProgress}%
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Results Section */}
          {showResults && result && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  Import Results
                  {result.errorCount === 0 ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Summary */}
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{result.totalRows}</div>
                    <div className="text-sm text-muted-foreground">Total Rows</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{result.successCount}</div>
                    <div className="text-sm text-muted-foreground">Imported</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{result.skippedCount}</div>
                    <div className="text-sm text-muted-foreground">Skipped</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{result.errorCount}</div>
                    <div className="text-sm text-muted-foreground">Errors</div>
                  </div>
                </div>

                {/* Warnings */}
                {result.warnings && result.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">Warnings ({result.warnings.length})</div>
                      <ScrollArea className="h-32">
                        <div className="space-y-1">
                          {result.warnings.map((warning, index) => (
                            <div key={index} className="text-sm">
                              <span className="font-medium">Row {warning.row}:</span> {warning.warning}
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Errors */}
                {result.errors && result.errors.length > 0 && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">Errors ({result.errors.length})</div>
                      <ScrollArea className="h-32">
                        <div className="space-y-1">
                          {result.errors.map((error, index) => (
                            <div key={index} className="text-sm">
                              <span className="font-medium">Row {error.row}:</span> {error.error}
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Successfully Imported Employees */}
                {result.imported && result.imported.length > 0 && (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription>
                      <div className="font-medium mb-2 text-green-800">Successfully Imported ({result.imported.length})</div>
                      <ScrollArea className="h-32">
                        <div className="space-y-1">
                          {result.imported.map((imported, index) => (
                            <div key={index} className="text-sm text-green-700">
                              <span className="font-medium">Row {imported.row}:</span> {imported.employeeName} ({imported.employeeEmail}) - MWK {imported.basicSalary.toLocaleString()}
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Skipped Employees */}
                {result.skipped && result.skipped.length > 0 && (
                  <Alert className="border-orange-200 bg-orange-50">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <AlertDescription>
                      <div className="font-medium mb-2 text-orange-800">Skipped Employees ({result.skipped.length})</div>
                      <ScrollArea className="h-32">
                        <div className="space-y-1">
                          {result.skipped.map((skipped, index) => (
                            <div key={index} className="text-sm text-orange-700">
                              <span className="font-medium">Row {skipped.row}:</span> {skipped.employeeName || skipped.employeeEmail || 'Unknown'} - {skipped.reason}
                              {skipped.existingSalary && (
                                <span className="text-xs text-orange-600 ml-2">(Current salary: MWK {skipped.existingSalary.toLocaleString()})</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

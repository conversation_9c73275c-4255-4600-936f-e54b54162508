'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Zap, Users, DollarSign, Calendar, CheckCircle, XCircle, AlertTriangle, Loader2 } from 'lucide-react'

interface QuickSetupResult {
  totalEmployees: number;
  employeesWithExistingSalaries: number;
  employeesProcessed: number;
  employeesCreated: number;
  employeesSkipped: number;
  errors: Array<{ employeeName: string; error: string }>;
  created: Array<{
    employeeName: string;
    email: string;
    basicSalary: number;
    department: string;
  }>;
}

export function QuickSalarySetup() {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<QuickSetupResult | null>(null)
  const { toast } = useToast()

  // Configuration state
  const [config, setConfig] = useState({
    effectiveDate: new Date().toISOString().split('T')[0],
    currency: 'MWK',
    paymentMethod: 'bank_transfer',
    useSalaryFromEmployeeRecord: true,
    defaultBasicSalary: 250000,
    onlyEmployeesWithoutSalaries: true
  })

  const handleQuickSetup = async () => {
    try {
      setIsLoading(true)
      setResult(null)

      const response = await fetch('/api/payroll/employee-salaries/quick-setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to perform quick setup')
      }

      setResult(data.data)
      toast({
        title: "Quick Setup Completed!",
        description: data.message,
      })

    } catch (error) {
      console.error('Quick setup failed:', error)
      toast({
        title: "Setup Failed",
        description: error instanceof Error ? error.message : 'An error occurred during setup',
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Zap className="h-4 w-4" />
          Quick Setup
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            Quick Employee Salary Setup
          </DialogTitle>
          <DialogDescription>
            Automatically create salary records for all employees using their existing salary data or default values.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Setup Configuration</CardTitle>
              <CardDescription>
                Configure how salary records should be created for your employees.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="effectiveDate">Effective Date</Label>
                  <Input
                    id="effectiveDate"
                    type="date"
                    value={config.effectiveDate}
                    onChange={(e) => setConfig(prev => ({ ...prev, effectiveDate: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={config.currency} onValueChange={(value) => setConfig(prev => ({ ...prev, currency: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MWK">MWK (Malawian Kwacha)</SelectItem>
                      <SelectItem value="USD">USD (US Dollar)</SelectItem>
                      <SelectItem value="EUR">EUR (Euro)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select value={config.paymentMethod} onValueChange={(value) => setConfig(prev => ({ ...prev, paymentMethod: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="check">Check</SelectItem>
                      <SelectItem value="mobile_money">Mobile Money</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultSalary">Default Basic Salary</Label>
                  <Input
                    id="defaultSalary"
                    type="number"
                    value={config.defaultBasicSalary}
                    onChange={(e) => setConfig(prev => ({ ...prev, defaultBasicSalary: Number(e.target.value) }))}
                    disabled={config.useSalaryFromEmployeeRecord}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Use Employee's Current Salary</Label>
                    <p className="text-sm text-muted-foreground">
                      Use the salary amount from employee records instead of default value
                    </p>
                  </div>
                  <Switch
                    checked={config.useSalaryFromEmployeeRecord}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, useSalaryFromEmployeeRecord: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Only Employees Without Salaries</Label>
                    <p className="text-sm text-muted-foreground">
                      Skip employees who already have active salary records
                    </p>
                  </div>
                  <Switch
                    checked={config.onlyEmployeesWithoutSalaries}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, onlyEmployeesWithoutSalaries: checked }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleQuickSetup}
              disabled={isLoading}
              size="lg"
              className="gap-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Setting up salaries...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  Start Quick Setup
                </>
              )}
            </Button>
          </div>

          {/* Results */}
          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Setup Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Summary Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold text-blue-600">
                      <Users className="h-5 w-5" />
                      {result.totalEmployees}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Employees</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold text-green-600">
                      <CheckCircle className="h-5 w-5" />
                      {result.employeesCreated}
                    </div>
                    <p className="text-sm text-muted-foreground">Created</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold text-yellow-600">
                      <AlertTriangle className="h-5 w-5" />
                      {result.employeesWithExistingSalaries}
                    </div>
                    <p className="text-sm text-muted-foreground">Had Salaries</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold text-red-600">
                      <XCircle className="h-5 w-5" />
                      {result.employeesSkipped}
                    </div>
                    <p className="text-sm text-muted-foreground">Errors</p>
                  </div>
                </div>

                {/* Created Employees */}
                {result.created.length > 0 && (
                  <div>
                    <h4 className="font-medium text-green-600 mb-2">✅ Successfully Created ({result.created.length})</h4>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {result.created.map((emp, index) => (
                        <div key={index} className="flex items-center justify-between text-sm bg-green-50 p-2 rounded">
                          <span>{emp.employeeName}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">{emp.department}</Badge>
                            <span className="font-medium">MWK {emp.basicSalary.toLocaleString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Errors */}
                {result.errors.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-600 mb-2">❌ Errors ({result.errors.length})</h4>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {result.errors.map((error, index) => (
                        <div key={index} className="text-sm bg-red-50 p-2 rounded">
                          <span className="font-medium">{error.employeeName}:</span> {error.error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  CheckCircle,
  Loader2,
  AlertCircle,
  Clock,
  Users,
  TrendingUp,
  X,
  User,
  DollarSign
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface EmployeeProgress {
  employeeId: string
  employeeName: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
  processingTime?: number
  grossSalary?: number
  netSalary?: number
}

interface ProcessingProgress {
  operationId: string
  payrollRunId: string
  status: 'starting' | 'processing' | 'completed' | 'error'
  totalEmployees: number
  processedEmployees: number
  failedEmployees: number
  percentage: number
  currentEmployee?: string
  employees: EmployeeProgress[]
  estimatedTimeRemaining?: number
  averageProcessingTime?: number
  error?: string
  startTime: number
  elapsedTime: number
}

interface EnhancedProcessingModalProps {
  isOpen: boolean
  onClose: () => void
  payrollRunId: string
  operationId: string | null
  onComplete?: (success: boolean) => void
}

export function EnhancedProcessingModal({
  isOpen,
  onClose,
  payrollRunId,
  operationId,
  onComplete
}: EnhancedProcessingModalProps) {
  const [progress, setProgress] = useState<ProcessingProgress | null>(null)
  const [isPolling, setIsPolling] = useState(false)

  // Format time in a readable format
  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Poll for progress updates
  const pollProgress = async () => {
    if (!operationId || !isPolling) {
      console.log('Polling skipped:', { operationId, isPolling })
      return
    }

    try {
      console.log('Polling progress for operation:', operationId)
      const response = await fetch(
        `/api/payroll/runs/${payrollRunId}/process-enhanced?operationId=${operationId}`
      )

      if (!response.ok) {
        console.error('Progress API response not ok:', response.status, response.statusText)
        throw new Error(`Failed to get progress: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('Progress result:', result)

      if (result.success) {
        setProgress(result.data)

        // Check if completed or error
        if (result.data.status === 'completed') {
          setIsPolling(false)
          toast({
            title: "Payroll Processing Complete",
            description: `Successfully processed ${result.data.processedEmployees} employees.`,
          })
          onComplete?.(true)
        } else if (result.data.status === 'error') {
          setIsPolling(false)
          toast({
            title: "Processing Error",
            description: result.data.error || "An error occurred during processing.",
            variant: "destructive"
          })
          onComplete?.(false)
        }
      } else {
        console.error('Progress API returned success: false', result)
        throw new Error(result.error || 'Failed to get progress')
      }
    } catch (error) {
      console.error('Error polling progress:', {
        error,
        operationId,
        payrollRunId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorType: typeof error
      })
      // Continue polling on error, but show warning after multiple failures
    }
  }

  // Start polling when operation starts
  useEffect(() => {
    if (operationId && isOpen) {
      setIsPolling(true)
      pollProgress()
    }
  }, [operationId, isOpen])

  // Poll every 1 second while processing or starting
  useEffect(() => {
    if (isPolling && (progress?.status === 'processing' || progress?.status === 'starting' || !progress)) {
      const interval = setInterval(pollProgress, 1000)
      return () => clearInterval(interval)
    }
  }, [isPolling, progress?.status])

  // Get status icon
  const getStatusIcon = () => {
    if (!progress) return <Loader2 className="h-5 w-5 animate-spin" />

    switch (progress.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  // Get status message
  const getStatusMessage = () => {
    if (!progress) return "Initializing..."

    const currentEmployee = getCurrentProcessingEmployee()

    switch (progress.status) {
      case 'starting':
        return "Starting payroll processing..."
      case 'processing':
        return currentEmployee
          ? `Currently Processing: ${currentEmployee.employeeName}`
          : "Processing employees..."
      case 'completed':
        return "Payroll processing completed successfully!"
      case 'error':
        return "An error occurred during processing"
      default:
        return "Processing payroll..."
    }
  }

  // Get employee status icon
  const getEmployeeStatusIcon = (status: EmployeeProgress['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  // Sort employees for better UX: completed first, then processing, then pending, then errors
  const getSortedEmployees = () => {
    if (!progress?.employees) return []

    const employees = [...progress.employees]

    return employees.sort((a, b) => {
      // Priority order: completed -> processing -> pending -> error
      const statusPriority = {
        'completed': 1,
        'processing': 2,
        'pending': 3,
        'error': 4
      }

      const aPriority = statusPriority[a.status] || 5
      const bPriority = statusPriority[b.status] || 5

      if (aPriority !== bPriority) {
        return aPriority - bPriority
      }

      // Within same status, sort by name
      return a.employeeName.localeCompare(b.employeeName)
    })
  }

  // Get current processing employee
  const getCurrentProcessingEmployee = () => {
    return progress?.employees.find(emp => emp.status === 'processing')
  }

  // Handle close
  const handleClose = () => {
    if (progress?.status === 'processing') {
      // Don't allow closing while processing
      return
    }
    setIsPolling(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getStatusIcon()}
            Enhanced Payroll Processing
          </DialogTitle>
          <DialogDescription>
            Real-time progress tracking for payroll processing
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Message */}
          <div className="text-center">
            <p className="text-lg font-medium">{getStatusMessage()}</p>
            {progress?.error && (
              <p className="text-sm text-red-600 mt-1">{progress.error}</p>
            )}
          </div>

          {/* Current Processing Employee Highlight */}
          {progress?.status === 'processing' && getCurrentProcessingEmployee() && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-4">
                <div className="flex items-center justify-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  <div className="text-center">
                    <p className="font-semibold text-blue-900">Currently Processing</p>
                    <p className="text-lg font-bold text-blue-700">
                      {getCurrentProcessingEmployee()?.employeeName}
                    </p>
                  </div>
                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Progress Bar */}
          {progress && (
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm font-bold text-blue-600">{progress.percentage.toFixed(1)}%</span>
                </div>
                <Progress
                  value={progress.percentage}
                  className="h-4"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    <span className="font-medium text-green-600">{progress.processedEmployees}</span> completed
                    {progress.failedEmployees > 0 && (
                      <span className="ml-2">
                        <span className="font-medium text-red-600">{progress.failedEmployees}</span> failed
                      </span>
                    )}
                  </span>
                  <span>
                    <span className="font-medium">{progress.totalEmployees - progress.processedEmployees - progress.failedEmployees}</span> remaining
                  </span>
                </div>
              </div>

              {/* Time Information */}
              {progress.elapsedTime > 0 && (
                <div className="flex justify-between text-xs text-muted-foreground bg-gray-50 p-2 rounded">
                  <span>Elapsed: {formatTime(progress.elapsedTime)}</span>
                  {progress.estimatedTimeRemaining && (
                    <span>ETA: {formatTime(progress.estimatedTimeRemaining)}</span>
                  )}
                  {progress.averageProcessingTime && (
                    <span>Avg: {formatTime(progress.averageProcessingTime)}/employee</span>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Statistics Cards */}
          {progress && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    Total
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{progress.totalEmployees}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Completed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{progress.processedEmployees}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    Failed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{progress.failedEmployees}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    ETA
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium">
                    {progress.estimatedTimeRemaining
                      ? formatTime(progress.estimatedTimeRemaining)
                      : "Calculating..."
                    }
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Employee List */}
          {progress && progress.employees.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Employee Progress
                  <Badge variant="outline" className="ml-auto">
                    {progress.processedEmployees} / {progress.totalEmployees}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-80 w-full">
                  <div className="space-y-2">
                    {getSortedEmployees().map((employee, index) => (
                      <div
                        key={employee.employeeId}
                        className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                          employee.status === 'completed'
                            ? 'bg-green-50 border-green-200 shadow-sm'
                            : employee.status === 'processing'
                              ? 'bg-blue-50 border-blue-200 shadow-md ring-2 ring-blue-100'
                              : employee.status === 'error'
                                ? 'bg-red-50 border-red-200'
                                : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          {getEmployeeStatusIcon(employee.status)}
                          <div>
                            <div className={`font-medium text-sm ${
                              employee.status === 'processing' ? 'text-blue-900' :
                              employee.status === 'completed' ? 'text-green-900' :
                              employee.status === 'error' ? 'text-red-900' : 'text-gray-900'
                            }`}>
                              {employee.employeeName}
                              {employee.status === 'completed' && (
                                <span className="ml-2 text-xs text-green-600 font-normal">✓ Complete</span>
                              )}
                              {employee.status === 'processing' && (
                                <span className="ml-2 text-xs text-blue-600 font-normal">⚡ Processing...</span>
                              )}
                            </div>
                            {employee.status === 'error' && employee.error && (
                              <div className="text-xs text-red-600 mt-1">{employee.error}</div>
                            )}
                            {employee.status === 'completed' && employee.processingTime && (
                              <div className="text-xs text-green-600 mt-1">
                                Completed in {formatTime(employee.processingTime)}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {employee.netSalary && (
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              <span className="font-medium">{formatCurrency(employee.netSalary)}</span>
                            </div>
                          )}
                          <Badge variant={
                            employee.status === 'completed' ? 'default' :
                            employee.status === 'processing' ? 'secondary' :
                            employee.status === 'error' ? 'destructive' :
                            'outline'
                          } className={
                            employee.status === 'processing' ? 'animate-pulse' : ''
                          }>
                            {employee.status === 'completed' ? '✓ Done' :
                             employee.status === 'processing' ? '⚡ Processing' :
                             employee.status === 'error' ? '✗ Error' :
                             '⏳ Pending'}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            {progress?.status === 'completed' || progress?.status === 'error' ? (
              <Button onClick={handleClose}>
                Close
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={progress?.status === 'processing'}
              >
                {progress?.status === 'processing' ? 'Processing...' : 'Cancel'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

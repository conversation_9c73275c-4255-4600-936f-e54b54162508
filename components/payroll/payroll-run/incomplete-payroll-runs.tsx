// components/payroll/payroll-run/incomplete-payroll-runs.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  Play,
  Eye,
  RefreshCw,
  ArrowRight,
  Calendar,
  Users,
  FileText,
  CreditCard,
  XCircle
} from "lucide-react"

interface IncompletePayrollRun {
  _id: string
  name: string
  status: 'draft' | 'processing' | 'completed' | 'approved'
  payPeriod: {
    month: number
    year: number
  }
  totalEmployees: number
  processedEmployees: number
  createdAt: string
  updatedAt: string
}

export function IncompletePayrollRuns() {
  const router = useRouter()
  const [incompleteRuns, setIncompleteRuns] = useState<IncompletePayrollRun[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState<string | null>(null)

  useEffect(() => {
    fetchIncompleteRuns()
  }, [])

  const fetchIncompleteRuns = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/payroll/runs?status=incomplete&limit=5')

      if (!response.ok) {
        throw new Error('Failed to fetch incomplete payroll runs')
      }

      const result = await response.json()
      setIncompleteRuns(result.data?.payrollRuns || [])

    } catch (error) {
      console.error('Error fetching incomplete payroll runs:', error)
      toast({
        title: "Error",
        description: "Failed to fetch incomplete payroll runs",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'draft':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className="h-3 w-3" />,
          title: 'Draft',
          description: 'Ready to process',
          action: 'Process',
          actionIcon: <Play className="h-3 w-3" />,
          priority: 'medium'
        }
      case 'processing':
        return {
          color: 'bg-blue-100 text-blue-800 animate-pulse',
          icon: <Clock className="h-3 w-3" />,
          title: 'Processing',
          description: 'In progress',
          action: 'Check Progress',
          actionIcon: <Eye className="h-3 w-3" />,
          priority: 'high'
        }
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className="h-3 w-3" />,
          title: 'Completed',
          description: 'Ready for approval',
          action: 'Approve',
          actionIcon: <CheckCircle className="h-3 w-3" />,
          priority: 'high'
        }
      case 'approved':
        return {
          color: 'bg-purple-100 text-purple-800',
          icon: <CheckCircle className="h-3 w-3" />,
          title: 'Approved',
          description: 'Ready for payment',
          action: 'Mark as Paid',
          actionIcon: <CreditCard className="h-3 w-3" />,
          priority: 'high'
        }
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <AlertTriangle className="h-3 w-3" />,
          title: 'Unknown',
          description: 'Unknown status',
          action: 'View',
          actionIcon: <Eye className="h-3 w-3" />,
          priority: 'low'
        }
    }
  }

  const handleQuickAction = async (run: IncompletePayrollRun) => {
    const statusInfo = getStatusInfo(run.status)

    if (run.status === 'draft' || run.status === 'processing') {
      // For draft and processing, redirect to the payroll run details page
      router.push(`/dashboard/payroll/runs/${run._id}`)
    } else if (run.status === 'completed') {
      // For completed, try to approve
      try {
        setIsProcessing(run._id)
        const response = await fetch(`/api/payroll/runs/${run._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'approve',
            notes: `Approved from dashboard on ${new Date().toLocaleString()}`
          })
        })

        if (!response.ok) {
          throw new Error('Failed to approve payroll run')
        }

        toast({
          title: "Success",
          description: "Payroll run approved successfully",
        })

        // Refresh the list
        fetchIncompleteRuns()

      } catch (error) {
        console.error('Error approving payroll run:', error)
        toast({
          title: "Error",
          description: "Failed to approve payroll run",
          variant: "destructive"
        })
      } finally {
        setIsProcessing(null)
      }
    } else {
      // For other statuses, just navigate to details
      router.push(`/dashboard/payroll/runs/${run._id}`)
    }
  }

  const getPriorityOrder = (status: string) => {
    const statusInfo = getStatusInfo(status)
    switch (statusInfo.priority) {
      case 'high': return 1
      case 'medium': return 2
      case 'low': return 3
      default: return 4
    }
  }

  // Sort runs by priority and then by updated date
  const sortedRuns = [...incompleteRuns].sort((a, b) => {
    const priorityDiff = getPriorityOrder(a.status) - getPriorityOrder(b.status)
    if (priorityDiff !== 0) return priorityDiff
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  })

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Incomplete Payroll Runs
          </CardTitle>
          <CardDescription>
            Payroll runs that need your attention
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </CardContent>
      </Card>
    )
  }

  if (sortedRuns.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            All Payroll Runs Complete
          </CardTitle>
          <CardDescription>
            No payroll runs need your attention right now
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Great! All your payroll runs are either completed and paid, or cancelled.
              You can create a new payroll run when needed.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          Incomplete Payroll Runs ({sortedRuns.length})
        </CardTitle>
        <CardDescription>
          Payroll runs that need your attention - sorted by priority
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {sortedRuns.map((run) => {
          const statusInfo = getStatusInfo(run.status)
          const completionPercentage = run.totalEmployees > 0
            ? (run.processedEmployees / run.totalEmployees) * 100
            : 0

          return (
            <div
              key={run._id}
              className={`border rounded-lg p-4 transition-all hover:shadow-md ${
                statusInfo.priority === 'high'
                  ? 'border-orange-200 bg-orange-50'
                  : statusInfo.priority === 'medium'
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-sm">{run.name}</h4>
                    <Badge className={`${statusInfo.color} text-xs`}>
                      <div className="flex items-center gap-1">
                        {statusInfo.icon}
                        {statusInfo.title}
                      </div>
                    </Badge>
                    {statusInfo.priority === 'high' && (
                      <Badge variant="destructive" className="text-xs">
                        Urgent
                      </Badge>
                    )}
                  </div>

                  <div className="text-xs text-muted-foreground mb-2">
                    {new Date(run.payPeriod.year, run.payPeriod.month - 1).toLocaleString('default', { month: 'long' })} {run.payPeriod.year}
                    {run.status === 'processing' && (
                      <span className="ml-2">
                        • {run.processedEmployees}/{run.totalEmployees} employees ({completionPercentage.toFixed(0)}%)
                      </span>
                    )}
                  </div>

                  <div className="text-xs text-muted-foreground">
                    {statusInfo.description} • Updated {new Date(run.updatedAt).toLocaleDateString()}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant={statusInfo.priority === 'high' ? 'default' : 'outline'}
                    onClick={() => handleQuickAction(run)}
                    disabled={isProcessing === run._id}
                    className="text-xs"
                  >
                    {isProcessing === run._id ? (
                      <>
                        <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        {statusInfo.actionIcon}
                        <span className="ml-1">{statusInfo.action}</span>
                      </>
                    )}
                  </Button>

                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => router.push(`/dashboard/payroll/runs/${run._id}`)}
                    className="text-xs"
                  >
                    <ArrowRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {run.status === 'processing' && run.totalEmployees > 0 && (
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-blue-500 h-1.5 rounded-full transition-all"
                      style={{ width: `${completionPercentage}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          )
        })}

        <div className="pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/payroll/runs')}
            className="w-full text-xs"
          >
            View All Payroll Runs
            <ArrowRight className="ml-2 h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

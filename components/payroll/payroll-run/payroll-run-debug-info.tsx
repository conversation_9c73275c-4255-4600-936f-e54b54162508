// components/payroll/payroll-run/payroll-run-debug-info.tsx
"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import {
  Bug,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  FileText,
  Loader2,
  RefreshCw
} from "lucide-react"

interface PayrollRunDebugInfoProps {
  payrollRunId: string
}

interface DebugInfo {
  payrollRun: {
    id: string
    name: string
    status: string
    totalEmployees: number
    processedEmployees: number
    createdAt: string
    updatedAt: string
    processedAt?: string
    notes?: string
  }
  statistics: {
    totalActiveEmployees: number
    existingRecords: number
    unprocessedEmployees: number
    processingBatches: number
    completionPercentage: number
  }
  existingRecords: Array<{
    employeeId: string
    employeeName: string
    status: string
    grossSalary: number
    netSalary: number
    createdAt: string
  }>
  unprocessedEmployees: Array<{
    employeeId: string
    employeeName: string
    department: string
  }>
  processingBatches: Array<{
    id: string
    status: string
    totalEmployees: number
    processedEmployees: number
    currentEmployee?: string
    startedAt: string
    completedAt?: string
    error?: string
  }>
  recommendations: Array<{
    type: 'info' | 'warning' | 'error' | 'success'
    message: string
    action: string
  }>
}

export function PayrollRunDebugInfo({ payrollRunId }: PayrollRunDebugInfoProps) {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const fetchDebugInfo = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/payroll/runs/${payrollRunId}/debug`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch debug information')
      }

      const result = await response.json()
      setDebugInfo(result.data)

    } catch (error) {
      console.error('Error fetching debug info:', error)
      toast({
        title: "Error",
        description: "Failed to fetch debug information",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'approved':
        return 'bg-purple-100 text-purple-800'
      case 'paid':
        return 'bg-emerald-100 text-emerald-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleOpenDialog = () => {
    setIsOpen(true)
    if (!debugInfo) {
      fetchDebugInfo()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" onClick={handleOpenDialog}>
          <Bug className="mr-2 h-4 w-4" />
          Debug Info
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Payroll Run Debug Information
          </DialogTitle>
          <DialogDescription>
            Detailed information about the current payroll run state and processing status
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Last updated: {debugInfo ? new Date().toLocaleString() : 'Not loaded'}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchDebugInfo}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>

        {debugInfo && (
          <ScrollArea className="h-[60vh] w-full">
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="mt-1">
                        <Badge className={getStatusBadgeColor(debugInfo.payrollRun.status)}>
                          {debugInfo.payrollRun.status.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Completion</label>
                      <div className="mt-1 text-sm">
                        {debugInfo.statistics.completionPercentage.toFixed(1)}% 
                        ({debugInfo.statistics.existingRecords}/{debugInfo.statistics.totalActiveEmployees})
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                    <div className="mt-1 text-sm">
                      {new Date(debugInfo.payrollRun.createdAt).toLocaleString()}
                    </div>
                  </div>

                  {debugInfo.payrollRun.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Notes</label>
                      <div className="mt-1 text-sm bg-gray-50 p-2 rounded">
                        {debugInfo.payrollRun.notes}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recommendations */}
              {debugInfo.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {debugInfo.recommendations.map((rec, index) => (
                      <Alert key={index} variant={rec.type === 'error' ? 'destructive' : 'default'}>
                        {getRecommendationIcon(rec.type)}
                        <AlertDescription>
                          <div className="space-y-1">
                            <div>{rec.message}</div>
                            <div className="text-xs font-medium">Suggested Action: {rec.action}</div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Processing Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Processing Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {debugInfo.statistics.totalActiveEmployees}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Employees</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {debugInfo.statistics.existingRecords}
                      </div>
                      <div className="text-sm text-muted-foreground">Processed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {debugInfo.statistics.unprocessedEmployees}
                      </div>
                      <div className="text-sm text-muted-foreground">Remaining</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {debugInfo.statistics.processingBatches}
                      </div>
                      <div className="text-sm text-muted-foreground">Batches</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Processing Batches */}
              {debugInfo.processingBatches.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Processing Batches</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {debugInfo.processingBatches.map((batch, index) => (
                        <div key={batch.id} className="border rounded p-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium">Batch {index + 1}</div>
                              <div className="text-sm text-muted-foreground">
                                {batch.processedEmployees}/{batch.totalEmployees} employees
                              </div>
                              {batch.currentEmployee && (
                                <div className="text-sm">Current: {batch.currentEmployee}</div>
                              )}
                            </div>
                            <Badge className={getStatusBadgeColor(batch.status)}>
                              {batch.status}
                            </Badge>
                          </div>
                          {batch.error && (
                            <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                              Error: {batch.error}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Unprocessed Employees */}
              {debugInfo.unprocessedEmployees.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Unprocessed Employees ({debugInfo.unprocessedEmployees.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-32">
                      <div className="space-y-1">
                        {debugInfo.unprocessedEmployees.map((emp) => (
                          <div key={emp.employeeId} className="text-sm flex justify-between">
                            <span>{emp.employeeName}</span>
                            <span className="text-muted-foreground">{emp.department}</span>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}
            </div>
          </ScrollArea>
        )}

        {!debugInfo && !isLoading && (
          <div className="text-center py-8 text-muted-foreground">
            Click "Refresh" to load debug information
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

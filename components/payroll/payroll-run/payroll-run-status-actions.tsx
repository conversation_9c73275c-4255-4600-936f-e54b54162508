// components/payroll/payroll-run/payroll-run-status-actions.tsx
"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { toast } from "@/components/ui/use-toast"
import { ProcessingProgress } from "./processing-progress"
import { PayrollRunDebugInfo } from "./payroll-run-debug-info"
import { useErrorHandler } from "@/hooks/use-error-handler"
import { ErrorOverlay } from "@/components/errors/error-overlay"
import {
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  CreditCard,
  AlertTriangle,
  Clock,
  FileText,
  Eye,
  Refresh<PERSON><PERSON>,
  XCircle,
  Loader2,
  Cal<PERSON>tor,
  Bug
} from "lucide-react"

interface PayrollRunStatusActionsProps {
  payrollRun: {
    _id: string
    name: string
    status: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
    totalEmployees: number
    processedEmployees: number
    totalGrossSalary?: number
    totalDeductions?: number
    totalTax?: number
    totalNetSalary?: number
    processedAt?: string
    approvedAt?: string
    paidAt?: string
  }
  onStatusChange?: () => void
}

export function PayrollRunStatusActions({ payrollRun, onStatusChange }: PayrollRunStatusActionsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showProcessingDialog, setShowProcessingDialog] = useState(false)
  const [showReprocessButton, setShowReprocessButton] = useState(false)
  const { error, isErrorOpen, handleApiError, hideError, isActionLoading, loadingAction, setActionLoading } = useErrorHandler()
  const [processingProgress, setProcessingProgress] = useState({
    total: 0,
    processed: 0,
    percentage: 0,
    currentEmployee: '',
    status: 'idle' as 'idle' | 'validating' | 'processing' | 'completed' | 'error',
    employees: [] as Array<{
      id: string;
      name: string;
      status: 'pending' | 'processing' | 'completed' | 'error';
      error?: string;
      processingTime?: number;
    }>,
    estimatedTimeRemaining: 0,
    averageProcessingTime: 0
  })

  // Get status information
  const getStatusInfo = () => {
    switch (payrollRun.status) {
      case 'draft':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className="h-4 w-4" />,
          title: 'Draft',
          description: 'Payroll run is ready to be processed',
          nextAction: 'Process Payroll'
        }
      case 'processing':
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <Clock className="h-4 w-4 animate-pulse" />,
          title: 'Processing',
          description: 'Payroll is currently being processed',
          nextAction: 'Check Progress'
        }
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className="h-4 w-4" />,
          title: 'Completed',
          description: 'Processing completed, ready for approval',
          nextAction: 'Approve Payroll'
        }
      case 'approved':
        return {
          color: 'bg-emerald-100 text-emerald-800',
          icon: <CheckCircle className="h-4 w-4" />,
          title: 'Approved',
          description: 'Payroll approved, ready for payment',
          nextAction: 'Mark as Paid'
        }
      case 'paid':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CreditCard className="h-4 w-4" />,
          title: 'Paid',
          description: 'Payroll has been paid to employees',
          nextAction: null
        }
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className="h-4 w-4" />,
          title: 'Cancelled',
          description: 'Payroll run has been cancelled',
          nextAction: null
        }
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <AlertTriangle className="h-4 w-4" />,
          title: 'Unknown',
          description: 'Unknown status',
          nextAction: null
        }
    }
  }

  // Check processing status
  const checkProcessingStatus = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/process/status`)

      if (!response.ok) {
        throw new Error('Failed to get processing status')
      }

      const result = await response.json()

      // Update progress state
      setProcessingProgress({
        total: result.data.totalEmployees || 0,
        processed: result.data.processedEmployees || 0,
        percentage: result.data.totalEmployees > 0
          ? (result.data.processedEmployees / result.data.totalEmployees) * 100
          : 0,
        currentEmployee: result.data.currentEmployee || '',
        status: result.data.status || 'idle',
        employees: result.data.employees || [],
        estimatedTimeRemaining: result.data.estimatedTimeRemaining || 0,
        averageProcessingTime: result.data.averageProcessingTime || 0
      })

      setShowProcessingDialog(true)

      // If processing is completed but status is still processing, offer to update
      if (result.data.status === 'completed' && payrollRun.status === 'processing') {
        toast({
          title: "Processing Completed",
          description: "Payroll processing has completed. The status will be updated automatically.",
        })

        // Refresh the parent component
        if (onStatusChange) {
          setTimeout(() => onStatusChange(), 2000)
        }
      }

    } catch (error) {
      console.error('Error checking processing status:', error)
      toast({
        title: "Error",
        description: "Failed to check processing status",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Resume/Continue processing
  const resumeProcessing = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          notes: `Resumed processing on ${new Date().toLocaleString()}`,
          batchSize: Math.min(Math.max(payrollRun.totalEmployees || 10, 10), 25),
          useBatch: true,
          resume: true // Flag to indicate this is a resume operation
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to resume processing')
      }

      const result = await response.json()

      toast({
        title: "Processing Resumed",
        description: "Payroll processing has been resumed successfully.",
      })

      // Start checking status
      setShowProcessingDialog(true)
      setTimeout(() => checkProcessingStatus(), 1000)

    } catch (error) {
      console.error('Error resuming processing:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to resume processing",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Process payroll (for draft status)
  const processPayroll = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          notes: `Processed on ${new Date().toLocaleString()}`,
          batchSize: Math.min(Math.max(payrollRun.totalEmployees || 10, 10), 25),
          useBatch: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to process payroll')
      }

      toast({
        title: "Processing Started",
        description: "Payroll processing has been started successfully.",
      })

      // Start checking status
      setShowProcessingDialog(true)
      setTimeout(() => checkProcessingStatus(), 1000)

    } catch (error) {
      console.error('Error processing payroll:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process payroll",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Approve payroll
  const approvePayroll = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'approve',
          notes: `Approved on ${new Date().toLocaleString()}`
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to approve payroll')
      }

      toast({
        title: "Payroll Approved",
        description: "Payroll has been approved successfully.",
      })

      if (onStatusChange) {
        onStatusChange()
      }

    } catch (error) {
      console.error('Error approving payroll:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to approve payroll",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reprocess payroll (for payroll runs with no records)
  const reprocessPayroll = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/reprocess`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reprocess payroll')
      }

      const result = await response.json()

      toast({
        title: "Payroll Reprocessed",
        description: `Successfully reprocessed ${result.data.processedEmployees} employees. Totals have been updated.`,
      })

      if (onStatusChange) {
        onStatusChange()
      }

    } catch (error) {
      console.error('Error reprocessing payroll:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reprocess payroll",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Debug payroll records
  const debugPayrollRecords = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/debug-records`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to debug payroll records')
      }

      const result = await response.json()

      // Show debug information in console and toast
      console.log('Payroll Records Debug Info:', result.data)

      const debugInfo = result.data
      const analysis = debugInfo.analysis

      toast({
        title: "Debug Information",
        description: `Found ${debugInfo.payrollRecords.totalRecords} total records (${debugInfo.payrollRecords.activeRecords} active). Check console for details.`,
      })

      // Also show recommendations
      if (analysis.recommendations.length > 0) {
        setTimeout(() => {
          toast({
            title: "Recommendations",
            description: analysis.recommendations[0],
            variant: analysis.recommendations[0].includes('zero') ? 'destructive' : 'default'
          })
        }, 2000)
      }

      // If no records found, suggest reprocessing and show reprocess button
      if (debugInfo.payrollRecords.totalRecords === 0) {
        setShowReprocessButton(true)
        setTimeout(() => {
          toast({
            title: "No Records Found",
            description: "No payroll records were created during processing. Click 'Reprocess Payroll' to fix this.",
            variant: "destructive"
          })
        }, 4000)
      } else {
        setShowReprocessButton(false)
      }

    } catch (error) {
      console.error('Error debugging payroll records:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to debug payroll records",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Recalculate totals
  const recalculateTotals = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}/recalculate-totals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        await handleApiError(response)
        return
      }

      const result = await response.json()

      toast({
        title: "Totals Recalculated",
        description: result.data.hasChanges
          ? `Totals updated successfully. ${result.data.recordsProcessed} records processed.`
          : `Totals verified. No changes needed. ${result.data.recordsProcessed} records processed.`,
      })

      if (onStatusChange) {
        onStatusChange()
      }

    } catch (error) {
      console.error('Error recalculating totals:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to recalculate totals",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Mark as paid
  const markAsPaid = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/payroll/runs/${payrollRun._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'pay',
          notes: `Marked as paid on ${new Date().toLocaleString()}`
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to mark as paid')
      }

      toast({
        title: "Payroll Paid",
        description: "Payroll has been marked as paid successfully.",
      })

      if (onStatusChange) {
        onStatusChange()
      }

    } catch (error) {
      console.error('Error marking as paid:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to mark as paid",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <>
      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          isActionLoading={isActionLoading}
          loadingAction={loadingAction}
          onAction={async (action, data) => {
            setActionLoading(action)
            try {
              if (action === 'debug') {
                await debugPayrollRecords()
              } else if (action === 'reprocess') {
                await reprocessPayroll()
              } else if (action === 'retry') {
                await recalculateTotals()
              }
            } finally {
              setActionLoading(undefined)
            }
          }}
        />
      )}

      <Card>
        <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {statusInfo.icon}
          Current Status: {statusInfo.title}
        </CardTitle>
        <CardDescription>{statusInfo.description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Badge */}
        <div className="flex items-center gap-2">
          <Badge className={statusInfo.color}>
            {payrollRun.status.toUpperCase()}
          </Badge>
          {payrollRun.status === 'processing' && (
            <span className="text-sm text-muted-foreground">
              {payrollRun.processedEmployees || 0} / {payrollRun.totalEmployees || 0} employees processed
            </span>
          )}
        </div>

        {/* Progress Information for Processing Status */}
        {payrollRun.status === 'processing' && (
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription>
              This payroll run is currently being processed. You can check the progress or resume processing if it was interrupted.
            </AlertDescription>
          </Alert>
        )}

        {/* Zero Totals Warning */}
        {['completed', 'approved', 'paid'].includes(payrollRun.status) &&
         (payrollRun.totalGrossSalary === 0 && payrollRun.totalDeductions === 0 &&
          payrollRun.totalTax === 0 && payrollRun.totalNetSalary === 0) && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">Zero Totals Detected</div>
                <div>
                  This payroll run shows zero totals, which may indicate a calculation error.
                  Click "Recalculate Totals" to fix this issue.
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          {payrollRun.status === 'draft' && (
            <Button onClick={processPayroll} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Process Payroll
                </>
              )}
            </Button>
          )}

          {payrollRun.status === 'processing' && (
            <>
              <Dialog open={showProcessingDialog} onOpenChange={setShowProcessingDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" onClick={checkProcessingStatus} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Checking...
                      </>
                    ) : (
                      <>
                        <Eye className="mr-2 h-4 w-4" />
                        Check Progress
                      </>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Payroll Processing Progress</DialogTitle>
                    <DialogDescription>
                      Current status of payroll processing for {payrollRun.name}
                    </DialogDescription>
                  </DialogHeader>
                  <ProcessingProgress progress={processingProgress} />
                </DialogContent>
              </Dialog>

              <Button onClick={resumeProcessing} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resuming...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Resume Processing
                  </>
                )}
              </Button>
            </>
          )}

          {payrollRun.status === 'completed' && (
            <Button onClick={approvePayroll} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve Payroll
                </>
              )}
            </Button>
          )}

          {payrollRun.status === 'approved' && (
            <Button onClick={markAsPaid} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Marking as Paid...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Mark as Paid
                </>
              )}
            </Button>
          )}
          {/* Debug Records Button - for completed, approved, or paid status with zero totals */}
          {['completed', 'approved', 'paid'].includes(payrollRun.status) &&
           (payrollRun.totalGrossSalary === 0 && payrollRun.totalDeductions === 0 &&
            payrollRun.totalTax === 0 && payrollRun.totalNetSalary === 0) && (
            <Button variant="outline" onClick={debugPayrollRecords} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Debugging...
                </>
              ) : (
                <>
                  <Bug className="mr-2 h-4 w-4" />
                  Debug Records
                </>
              )}
            </Button>
          )}

          {/* Reprocess Payroll Button - shown after debug reveals no records */}
          {showReprocessButton && ['completed', 'approved', 'paid'].includes(payrollRun.status) && (
            <Button variant="default" onClick={reprocessPayroll} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Reprocessing...
                </>
              ) : (
                <>
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reprocess Payroll
                </>
              )}
            </Button>
          )}

          {/* Recalculate Totals Button - for completed, approved, or paid status */}
          {['completed', 'approved', 'paid'].includes(payrollRun.status) && (
            <Button variant="outline" onClick={recalculateTotals} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Calculating...
                </>
              ) : (
                <>
                  <Calculator className="mr-2 h-4 w-4" />
                  Recalculate Totals
                </>
              )}
            </Button>
          )}

          {/* Debug Information Button */}
          <PayrollRunDebugInfo payrollRunId={payrollRun._id} />
        </div>

        {/* Next Steps Information */}
        {statusInfo.nextAction && (
          <div className="text-sm text-muted-foreground">
            <strong>Next Step:</strong> {statusInfo.nextAction}
          </div>
        )}
      </CardContent>
    </Card>
    </>
  )
}

// components/payroll/payroll-run/payroll-workflow-guide.tsx
"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowRight,
  FileText,
  Play,
  Eye,
  CreditCard,
  RefreshCw,
  Info
} from "lucide-react"

interface PayrollWorkflowGuideProps {
  currentStatus: 'draft' | 'processing' | 'completed' | 'approved' | 'paid' | 'cancelled'
  payrollRunId: string
  totalEmployees: number
  processedEmployees: number
}

export function PayrollWorkflowGuide({ 
  currentStatus, 
  payrollRunId, 
  totalEmployees, 
  processedEmployees 
}: PayrollWorkflowGuideProps) {
  
  const workflowSteps = [
    {
      id: 'draft',
      title: 'Draft',
      description: 'Payroll run created and ready for processing',
      icon: <FileText className="h-4 w-4" />,
      status: 'completed' as const
    },
    {
      id: 'processing',
      title: 'Processing',
      description: 'Employee salaries are being calculated',
      icon: <Clock className="h-4 w-4" />,
      status: currentStatus === 'processing' ? 'current' : 
              ['completed', 'approved', 'paid'].includes(currentStatus) ? 'completed' : 'pending' as const
    },
    {
      id: 'completed',
      title: 'Completed',
      description: 'Processing finished, ready for approval',
      icon: <CheckCircle className="h-4 w-4" />,
      status: currentStatus === 'completed' ? 'current' : 
              ['approved', 'paid'].includes(currentStatus) ? 'completed' : 'pending' as const
    },
    {
      id: 'approved',
      title: 'Approved',
      description: 'Payroll approved by authorized personnel',
      icon: <CheckCircle className="h-4 w-4" />,
      status: currentStatus === 'approved' ? 'current' : 
              currentStatus === 'paid' ? 'completed' : 'pending' as const
    },
    {
      id: 'paid',
      title: 'Paid',
      description: 'Payments made to employees',
      icon: <CreditCard className="h-4 w-4" />,
      status: currentStatus === 'paid' ? 'completed' : 'pending' as const
    }
  ]

  const getCurrentStepInfo = () => {
    switch (currentStatus) {
      case 'draft':
        return {
          title: 'Ready to Process',
          description: 'Your payroll run is ready to be processed. Click "Process Payroll" to begin calculating employee salaries.',
          action: 'Process Payroll',
          actionIcon: <Play className="h-4 w-4" />,
          alertType: 'info' as const,
          nextSteps: [
            'Review employee salary structures',
            'Ensure all departments are selected',
            'Click "Process Payroll" to start'
          ]
        }
      case 'processing':
        return {
          title: 'Processing in Progress',
          description: `Processing ${processedEmployees} of ${totalEmployees} employees. You can check progress or resume if interrupted.`,
          action: 'Check Progress',
          actionIcon: <Eye className="h-4 w-4" />,
          alertType: 'warning' as const,
          nextSteps: [
            'Monitor processing progress',
            'Resume processing if interrupted',
            'Wait for completion before proceeding'
          ]
        }
      case 'completed':
        return {
          title: 'Ready for Approval',
          description: 'Processing completed successfully. Review the results and approve the payroll run.',
          action: 'Approve Payroll',
          actionIcon: <CheckCircle className="h-4 w-4" />,
          alertType: 'success' as const,
          nextSteps: [
            'Review calculated salaries',
            'Check for any discrepancies',
            'Approve payroll for payment'
          ]
        }
      case 'approved':
        return {
          title: 'Ready for Payment',
          description: 'Payroll has been approved and is ready for payment processing.',
          action: 'Mark as Paid',
          actionIcon: <CreditCard className="h-4 w-4" />,
          alertType: 'success' as const,
          nextSteps: [
            'Process bank transfers',
            'Generate payment confirmations',
            'Mark payroll as paid'
          ]
        }
      case 'paid':
        return {
          title: 'Payroll Complete',
          description: 'Payroll has been successfully processed and paid to employees.',
          action: null,
          actionIcon: null,
          alertType: 'success' as const,
          nextSteps: [
            'Generate payroll reports',
            'Archive payroll records',
            'Prepare for next pay period'
          ]
        }
      case 'cancelled':
        return {
          title: 'Payroll Cancelled',
          description: 'This payroll run has been cancelled and will not be processed.',
          action: null,
          actionIcon: null,
          alertType: 'destructive' as const,
          nextSteps: [
            'Review cancellation reason',
            'Create new payroll run if needed',
            'Update employee records if necessary'
          ]
        }
      default:
        return {
          title: 'Unknown Status',
          description: 'The payroll run status is not recognized.',
          action: null,
          actionIcon: null,
          alertType: 'destructive' as const,
          nextSteps: ['Contact system administrator']
        }
    }
  }

  const stepInfo = getCurrentStepInfo()

  const getStepBadgeVariant = (status: 'completed' | 'current' | 'pending') => {
    switch (status) {
      case 'completed':
        return 'default'
      case 'current':
        return 'secondary'
      case 'pending':
        return 'outline'
    }
  }

  const getStepBadgeColor = (status: 'completed' | 'current' | 'pending') => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 hover:bg-green-100'
      case 'current':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100'
      case 'pending':
        return 'bg-gray-100 text-gray-600 hover:bg-gray-100'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Payroll Workflow Guide
        </CardTitle>
        <CardDescription>
          Track your payroll run progress and see what to do next
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status Alert */}
        <Alert variant={stepInfo.alertType}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">{stepInfo.title}</div>
              <div>{stepInfo.description}</div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Workflow Steps */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm text-muted-foreground">WORKFLOW PROGRESS</h4>
          <div className="flex flex-wrap gap-2">
            {workflowSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <Badge 
                  variant={getStepBadgeVariant(step.status)}
                  className={`${getStepBadgeColor(step.status)} flex items-center gap-1`}
                >
                  {step.icon}
                  {step.title}
                  {step.status === 'completed' && <CheckCircle className="h-3 w-3" />}
                  {step.status === 'current' && <Clock className="h-3 w-3 animate-pulse" />}
                </Badge>
                {index < workflowSteps.length - 1 && (
                  <ArrowRight className="h-4 w-4 mx-2 text-muted-foreground" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Next Steps */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-muted-foreground">NEXT STEPS</h4>
          <ul className="space-y-2">
            {stepInfo.nextSteps.map((step, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                {step}
              </li>
            ))}
          </ul>
        </div>

        {/* Special Instructions for Processing Status */}
        {currentStatus === 'processing' && (
          <Alert>
            <RefreshCw className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">Processing Interrupted?</div>
                <div>
                  If the processing seems stuck or was interrupted, you can safely resume it. 
                  The system will skip already processed employees and continue from where it left off.
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Troubleshooting Tips */}
        {currentStatus === 'processing' && processedEmployees === 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">Processing Not Started?</div>
                <div>
                  If processing hasn't started after a few minutes, try resuming the process. 
                  Check that employees have active salary structures assigned.
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

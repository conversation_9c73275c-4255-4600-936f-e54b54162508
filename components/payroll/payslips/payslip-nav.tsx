'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Trash2, 
  Download,
  Upload,
  Settings
} from 'lucide-react';

const navItems = [
  {
    title: 'View Payslips',
    href: '/dashboard/payroll/payslips',
    icon: FileText,
    description: 'View and manage payslips'
  },
  {
    title: 'Bulk Delete',
    href: '/dashboard/payroll/payslips/bulk-delete',
    icon: Trash2,
    description: 'Delete multiple payslips'
  },
];

export function PayslipNav() {
  const pathname = usePathname();

  return (
    <nav className="flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1">
      {navItems.map((item) => (
        <Link key={item.href} href={item.href}>
          <Button
            variant={pathname === item.href ? 'default' : 'ghost'}
            className={cn(
              'w-full justify-start',
              pathname === item.href && 'bg-muted font-medium'
            )}
          >
            <item.icon className="mr-2 h-4 w-4" />
            {item.title}
          </Button>
        </Link>
      ))}
    </nav>
  );
}

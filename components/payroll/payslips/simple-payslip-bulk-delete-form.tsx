'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Trash2, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

// Define the form schema
const formSchema = z.object({
  payslipIds: z.string().min(1, {
    message: 'Please enter at least one payslip ID',
  }),
  confirmDelete: z.boolean().refine(val => val === true, {
    message: 'You must confirm the deletion',
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface DeleteRequestBody {
  ids: string[];
}

export function SimplePayslipBulkDeleteForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [deleteResult, setDeleteResult] = useState<{
    deletedCount: number;
    errors: Array<{ id: string; error: string }>;
  } | null>(null);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      payslipIds: '',
      confirmDelete: false,
    },
  });

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);

      // Split the comma-separated IDs
      const ids = values.payslipIds.split(',').map(id => id.trim()).filter(Boolean);

      if (ids.length === 0) {
        toast.error('Please enter at least one payslip ID');
        setIsLoading(false);
        return;
      }

      // Send delete request
      const requestBody: DeleteRequestBody = { ids };
      const response = await fetch('/api/payroll/payslips/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete payslips');
      }

      const result = await response.json();
      setDeleteResult(result);

      // Show success message
      toast.success(`Successfully deleted ${result.deletedCount} payslip(s)`);

      // Reset form
      form.reset({
        payslipIds: '',
        confirmDelete: false,
      });
    } catch (error) {
      console.error('Error deleting payslips:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete payslips');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="payslipIds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payslip IDs</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter payslip IDs separated by commas"
                    {...field}
                    disabled={isLoading}
                    className="min-h-[100px]"
                  />
                </FormControl>
                <FormDescription>
                  Enter the IDs of payslips to delete, separated by commas.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmDelete"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-destructive/10">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Confirm Deletion</FormLabel>
                  <FormDescription>
                    I understand that this action will permanently delete payslip records and cannot be undone.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            variant="destructive"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Payslips
              </>
            )}
          </Button>
        </form>
      </Form>

      {/* Delete Results */}
      {deleteResult && (
        <div className="rounded-md border p-4">
          <h3 className="text-lg font-medium mb-2">Delete Results</h3>
          <div className="space-y-2">
            <p className="text-sm text-green-600">
              Successfully deleted: {deleteResult.deletedCount} payslip(s)
            </p>
            {deleteResult.errors.length > 0 && (
              <div>
                <p className="text-sm text-red-600 font-medium">
                  Errors ({deleteResult.errors.length}):
                </p>
                <ul className="text-sm text-red-600 list-disc list-inside">
                  {deleteResult.errors.map((error, index) => (
                    <li key={index}>
                      ID {error.id}: {error.error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

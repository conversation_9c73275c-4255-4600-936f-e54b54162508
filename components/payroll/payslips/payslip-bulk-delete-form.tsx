'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Trash2, Loader2, Calendar, Filter } from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Define the form schema
const formSchema = z.object({
  deleteMethod: z.enum(['filter', 'ids'], {
    required_error: 'Please select a delete method.',
  }),
  payrollRunId: z.string().optional(),
  department: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
  dateRange: z.object({
    from: z.date(),
    to: z.date().optional(),
  }).optional(),
  payslipIds: z.string().optional(),
  confirmDelete: z.boolean().refine(val => val === true, {
    message: 'You must confirm the deletion',
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface PayrollRun {
  _id: string;
  name: string;
  payPeriod: {
    month: number;
    year: number;
  };
  status: string;
}

interface DeleteFilter {
  payrollRunId?: string;
  department?: string;
  status?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
  searchTerm?: string;
}

interface DeleteRequestBody {
  ids?: string[];
  filter?: DeleteFilter;
}

export function PayslipBulkDeleteForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [payrollRuns, setPayrollRuns] = useState<PayrollRun[]>([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deleteCount, setDeleteCount] = useState(0);
  const [deleteResult, setDeleteResult] = useState<{
    deletedCount: number;
    errors: Array<{ id: string; error: string }>;
  } | null>(null);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      deleteMethod: 'filter',
      confirmDelete: false,
    },
  });

  // Watch for changes to the delete method
  const deleteMethod = form.watch('deleteMethod');

  // Fetch payroll runs on component mount
  useEffect(() => {
    fetchPayrollRuns();
  }, []);

  const fetchPayrollRuns = async () => {
    try {
      const response = await fetch('/api/payroll/runs');
      if (!response.ok) {
        throw new Error('Failed to fetch payroll runs');
      }
      const data = await response.json();
      if (data.success) {
        setPayrollRuns(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching payroll runs:', error);
      toast.error('Failed to fetch payroll runs');
    }
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);

      // Build request body
      const requestBody: DeleteRequestBody = {};

      if (values.deleteMethod === 'ids') {
        // Split the comma-separated IDs
        const ids = values.payslipIds?.split(',').map(id => id.trim()).filter(Boolean);

        if (!ids || ids.length === 0) {
          toast.error('Please enter at least one payslip ID');
          setIsLoading(false);
          return;
        }

        requestBody.ids = ids;
        setDeleteCount(ids.length);
      } else {
        // Build filter
        const filter: DeleteFilter = {};

        if (values.payrollRunId && values.payrollRunId !== 'all') {
          filter.payrollRunId = values.payrollRunId;
        }

        if (values.department && values.department !== 'all') {
          filter.department = values.department;
        }

        if (values.status && values.status !== 'all') {
          filter.status = values.status;
        }

        if (values.search) {
          filter.searchTerm = values.search;
        }

        if (values.dateRange?.from) {
          filter.dateRange = {
            from: values.dateRange.from,
            to: values.dateRange.to || new Date()
          };
        }

        // Check if at least one filter is provided
        if (Object.keys(filter).length === 0) {
          toast.error('Please provide at least one filter criteria');
          setIsLoading(false);
          return;
        }

        requestBody.filter = filter;
        setDeleteCount(0); // We don't know the count yet for filter-based deletion
      }

      // Show confirmation dialog
      setConfirmDialogOpen(true);
      setIsLoading(false);
    } catch (error) {
      console.error('Error preparing delete request:', error);
      toast.error('Error preparing delete request');
      setIsLoading(false);
    }
  };

  // Handle confirmed deletion
  const handleConfirmedDelete = async () => {
    try {
      setIsLoading(true);
      setConfirmDialogOpen(false);

      const values = form.getValues();
      const requestBody: DeleteRequestBody = {};

      if (values.deleteMethod === 'ids') {
        const ids = values.payslipIds?.split(',').map(id => id.trim()).filter(Boolean);
        requestBody.ids = ids;
      } else {
        // Rebuild filter from form values
        const filter: DeleteFilter = {};

        if (values.payrollRunId && values.payrollRunId !== 'all') {
          filter.payrollRunId = values.payrollRunId;
        }

        if (values.department && values.department !== 'all') {
          filter.department = values.department;
        }

        if (values.status && values.status !== 'all') {
          filter.status = values.status;
        }

        if (values.search) {
          filter.searchTerm = values.search;
        }

        if (values.dateRange?.from) {
          filter.dateRange = {
            from: values.dateRange.from,
            to: values.dateRange.to || new Date()
          };
        }

        requestBody.filter = filter;
      }

      // Send delete request
      const response = await fetch('/api/payroll/payslips/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete payslips');
      }

      const result = await response.json();
      setDeleteResult(result);

      // Show success message
      toast.success(`Successfully deleted ${result.deletedCount} payslip(s)`);

      // Reset form
      form.reset({
        deleteMethod: 'filter',
        confirmDelete: false,
      });
    } catch (error) {
      console.error('Error deleting payslips:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete payslips');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="deleteMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Delete Method</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isLoading}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="filter">Delete by Filter</SelectItem>
                    <SelectItem value="ids">Delete by IDs</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose how to select payslips for deletion.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {deleteMethod === 'filter' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter Criteria
                </CardTitle>
                <CardDescription>
                  Specify criteria to select payslips for deletion
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="payrollRunId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payroll Run</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payroll run" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="all">All Payroll Runs</SelectItem>
                          {payrollRuns.map((run) => (
                            <SelectItem key={run._id} value={run._id}>
                              {run.name} ({run.payPeriod.month}/{run.payPeriod.year})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Filter by specific payroll run (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter department name"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Filter by department (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="generated">Generated</SelectItem>
                          <SelectItem value="sent">Sent</SelectItem>
                          <SelectItem value="viewed">Viewed</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Filter by payslip status (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="search"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Search Term</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Search by employee name or number"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Search in employee names and numbers (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateRange"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pay Period Range</FormLabel>
                      <FormControl>
                        <DatePickerWithRange
                          date={field.value}
                          onDateChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Filter by pay period date range (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {deleteMethod === 'ids' && (
            <FormField
              control={form.control}
              name="payslipIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payslip IDs</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter payslip IDs separated by commas"
                      {...field}
                      disabled={isLoading}
                      className="min-h-[100px]"
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the IDs of payslips to delete, separated by commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="confirmDelete"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-destructive/10">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Confirm Deletion</FormLabel>
                  <FormDescription>
                    I understand that this action will permanently delete payslip records and cannot be undone.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            variant="destructive"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Payslips
              </>
            )}
          </Button>
        </form>
      </Form>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Bulk Delete</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteCount > 0 
                ? `Are you sure you want to delete ${deleteCount} payslip(s)? This action cannot be undone.`
                : 'Are you sure you want to delete payslips matching the specified criteria? This action cannot be undone.'
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmedDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Results */}
      {deleteResult && (
        <Card>
          <CardHeader>
            <CardTitle>Delete Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Success: {deleteResult.deletedCount}
                </Badge>
                {deleteResult.errors.length > 0 && (
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    Errors: {deleteResult.errors.length}
                  </Badge>
                )}
              </div>

              {deleteResult.errors.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-2">Errors:</h4>
                  <div className="space-y-1">
                    {deleteResult.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-600">
                        ID {error.id}: {error.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

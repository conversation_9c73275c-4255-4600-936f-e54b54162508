"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Loader2, FileText, DollarSign, Building, Target } from "lucide-react"

interface PayrollRun {
  _id: string
  runNumber: string
  payPeriod: {
    month: number
    year: number
  }
  status: string
  totalEmployees: number
  totalAmount: number
}

interface BulkIntegrationPanelProps {
  payrollRuns: PayrollRun[]
  onRefresh: () => void
}

export function BulkIntegrationPanel({ payrollRuns, onRefresh }: BulkIntegrationPanelProps) {
  const [selectedRuns, setSelectedRuns] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentAction, setCurrentAction] = useState<string>("")
  const [batchSize, setBatchSize] = useState(10)
  const [autoPost, setAutoPost] = useState(false)
  const [allocationType, setAllocationType] = useState<"department" | "cost_center" | "both">("both")
  const { toast } = useToast()

  // Filter payroll runs that can be processed (completed status)
  const eligibleRuns = payrollRuns.filter(run => run.status === 'completed')

  const handleRunSelection = (runId: string, checked: boolean) => {
    if (checked) {
      setSelectedRuns(prev => [...prev, runId])
    } else {
      setSelectedRuns(prev => prev.filter(id => id !== runId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRuns(eligibleRuns.map(run => run._id))
    } else {
      setSelectedRuns([])
    }
  }

  const createBulkJournalEntries = async () => {
    if (selectedRuns.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select at least one payroll run",
        variant: "destructive"
      })
      return
    }

    try {
      setCurrentAction("bulk_create_journal_entries")
      setIsProcessing(true)

      const response = await fetch("/api/payroll/accounting/bulk-journal-entries", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "bulk_create_journal_entries",
          payrollRunIds: selectedRuns,
          options: {
            autoPost,
            batchSize,
            includeDepartmentAllocation: true,
            includeCostCenterMapping: true,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create bulk journal entries")
      }

      const result = await response.json()

      toast({
        title: "Success",
        description: `Bulk journal entries created successfully. ${result.data.successful} successful, ${result.data.failed} failed.`,
      })

      // Clear selection and refresh
      setSelectedRuns([])
      onRefresh()

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
      setCurrentAction("")
    }
  }

  const createBulkAllocations = async () => {
    if (selectedRuns.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select at least one payroll run",
        variant: "destructive"
      })
      return
    }

    try {
      setCurrentAction("bulk_allocations")
      setIsProcessing(true)

      const response = await fetch("/api/payroll/accounting/bulk-allocations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "bulk_department_allocation",
          payrollRunIds: selectedRuns,
          options: {
            autoPost,
            batchSize,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create bulk allocations")
      }

      const result = await response.json()

      toast({
        title: "Success",
        description: `Bulk allocations created successfully. ${result.data.successful} successful, ${result.data.failed} failed.`,
      })

      // Clear selection and refresh
      setSelectedRuns([])
      onRefresh()

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
      setCurrentAction("")
    }
  }

  const runAutomatedWorkflow = async () => {
    if (selectedRuns.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select at least one payroll run",
        variant: "destructive"
      })
      return
    }

    try {
      setCurrentAction("automated_posting_workflow")
      setIsProcessing(true)

      const response = await fetch("/api/payroll/accounting/bulk-journal-entries", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "automated_posting_workflow",
          payrollRunIds: selectedRuns,
          workflow: {
            createJournalEntries: true,
            createAllocations: true,
            autoPost,
            postingDelay: 0,
            notifyOnCompletion: true,
            rollbackOnError: false,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to run automated workflow")
      }

      const result = await response.json()

      toast({
        title: "Success",
        description: `Automated workflow completed. ${result.data.journalEntriesCreated} journal entries, ${result.data.allocationsCreated} allocations, ${result.data.entriesPosted} posted.`,
      })

      // Clear selection and refresh
      setSelectedRuns([])
      onRefresh()

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
      setCurrentAction("")
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Payroll-Accounting Integration Bulk Operations
          </CardTitle>
          <CardDescription>
            Create journal entries and allocations for multiple payroll runs in bulk
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="journal-entries" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="journal-entries">Journal Entries</TabsTrigger>
              <TabsTrigger value="allocations">Allocations</TabsTrigger>
              <TabsTrigger value="workflow">Automated Workflow</TabsTrigger>
            </TabsList>

            {/* Configuration Options */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="space-y-2">
                <Label htmlFor="batchSize">Batch Size</Label>
                <Input
                  id="batchSize"
                  type="number"
                  min="1"
                  max="100"
                  value={batchSize}
                  onChange={(e) => setBatchSize(parseInt(e.target.value) || 10)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allocationType">Allocation Type</Label>
                <Select value={allocationType} onValueChange={(value: any) => setAllocationType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="department">Department Only</SelectItem>
                    <SelectItem value="cost_center">Cost Center Only</SelectItem>
                    <SelectItem value="both">Both</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id="autoPost"
                  checked={autoPost}
                  onCheckedChange={(checked) => setAutoPost(checked as boolean)}
                />
                <Label htmlFor="autoPost">Auto-post entries</Label>
              </div>
            </div>

            {/* Payroll Run Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Select Payroll Runs</h3>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="selectAll"
                    checked={selectedRuns.length === eligibleRuns.length && eligibleRuns.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="selectAll">Select All ({eligibleRuns.length})</Label>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {eligibleRuns.map((run) => (
                  <Card key={run._id} className="relative">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`run-${run._id}`}
                              checked={selectedRuns.includes(run._id)}
                              onCheckedChange={(checked) => handleRunSelection(run._id, checked as boolean)}
                            />
                            <Label htmlFor={`run-${run._id}`} className="font-medium">
                              Run #{run.runNumber}
                            </Label>
                          </div>
                          <p className="text-sm text-gray-600">
                            {run.payPeriod.month}/{run.payPeriod.year}
                          </p>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{run.status}</Badge>
                            <span className="text-sm text-gray-500">
                              {run.totalEmployees} employees
                            </span>
                          </div>
                          <p className="text-sm font-medium">
                            MWK {run.totalAmount?.toLocaleString() || '0'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {eligibleRuns.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No completed payroll runs available for processing
                </div>
              )}
            </div>

            <TabsContent value="journal-entries" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Bulk Journal Entries</h3>
                  <p className="text-sm text-gray-600">
                    Create journal entries for selected payroll runs with department allocations
                  </p>
                </div>
                <Button
                  onClick={createBulkJournalEntries}
                  disabled={isProcessing || selectedRuns.length === 0}
                  className="flex items-center gap-2"
                >
                  {isProcessing && currentAction === "bulk_create_journal_entries" && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  <FileText className="h-4 w-4" />
                  Create Journal Entries ({selectedRuns.length})
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="allocations" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Bulk Allocations</h3>
                  <p className="text-sm text-gray-600">
                    Create department and cost center allocations for selected payroll runs
                  </p>
                </div>
                <Button
                  onClick={createBulkAllocations}
                  disabled={isProcessing || selectedRuns.length === 0}
                  className="flex items-center gap-2"
                >
                  {isProcessing && currentAction === "bulk_allocations" && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  <Target className="h-4 w-4" />
                  Create Allocations ({selectedRuns.length})
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="workflow" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Automated Workflow</h3>
                  <p className="text-sm text-gray-600">
                    Run complete automated workflow: journal entries + allocations + posting
                  </p>
                </div>
                <Button
                  onClick={runAutomatedWorkflow}
                  disabled={isProcessing || selectedRuns.length === 0}
                  className="flex items-center gap-2"
                  variant="default"
                >
                  {isProcessing && currentAction === "automated_posting_workflow" && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  <DollarSign className="h-4 w-4" />
                  Run Workflow ({selectedRuns.length})
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

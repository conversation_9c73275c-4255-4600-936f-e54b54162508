"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Percent, Download, Loader2 } from "lucide-react"

interface SalaryBandsExportProps {
  onExportStart: (jobId: string, type: string) => void
  onExportComplete: (jobId: string, fileName: string, downloadUrl: string) => void
  onExportError: (jobId: string, error: string) => void
}

export function SalaryBandsExport({ 
  onExportStart, 
  onExportComplete, 
  onExportError 
}: SalaryBandsExportProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleExport = async (format: string) => {
    setIsLoading(true)
    const jobId = `salary_bands_${Date.now()}`
    
    try {
      onExportStart(jobId, 'Salary Bands')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const filename = `salary_bands_export_${new Date().toISOString().split('T')[0]}.${format}`
      const downloadUrl = '#' // Placeholder
      
      onExportComplete(jobId, filename, downloadUrl)
      
    } catch (error) {
      onExportError(jobId, 'Export failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Percent className="h-5 w-5 mr-2" />
            Salary Bands Export
          </CardTitle>
          <CardDescription>
            Export TCM salary bands and compensation structures
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Percent className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Salary Bands Export</h3>
            <p className="text-muted-foreground mb-4">
              Export all TCM salary bands with compensation structures, allowances, and deductions.
            </p>
            <div className="flex flex-wrap gap-2 justify-center mb-6">
              <Badge variant="outline">TCM 1-12</Badge>
              <Badge variant="outline">Salary Ranges</Badge>
              <Badge variant="outline">Allowances</Badge>
              <Badge variant="outline">Deductions</Badge>
            </div>
            <Button 
              onClick={() => handleExport('excel')} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Coming Soon
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Download, 
  FileText, 
  BarChart3,
  Loader2,
  Calendar,
  Building
} from "lucide-react"
import { toast } from "sonner"

const reportExportSchema = z.object({
  reportType: z.enum(['payroll_summary', 'tax_summary', 'department_summary', 'employee_summary']),
  format: z.enum(['excel', 'pdf', 'csv']),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  department: z.string().optional(),
  payrollRunId: z.string().optional()
})

type ReportExportFormData = z.infer<typeof reportExportSchema>

interface BulkReportsExportProps {
  onExportStart: (jobId: string, type: string) => void
  onExportComplete: (jobId: string, fileName: string, downloadUrl: string) => void
  onExportError: (jobId: string, error: string) => void
}

export function BulkReportsExport({
  onExportStart,
  onExportComplete,
  onExportError
}: BulkReportsExportProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [departments, setDepartments] = useState<string[]>([])
  const [payrollRuns, setPayrollRuns] = useState<any[]>([])

  // Set default dates (current month)
  const now = new Date()
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

  const form = useForm<ReportExportFormData>({
    resolver: zodResolver(reportExportSchema),
    defaultValues: {
      reportType: 'payroll_summary',
      format: 'excel',
      startDate: firstDayOfMonth.toISOString().split('T')[0],
      endDate: lastDayOfMonth.toISOString().split('T')[0]
    }
  })

  const watchedReportType = form.watch('reportType')
  const watchedFormat = form.watch('format')

  // Load initial data
  useEffect(() => {
    loadDepartments()
    loadPayrollRuns()
  }, [])

  const loadDepartments = async () => {
    try {
      const response = await fetch('/api/departments')
      if (response.ok) {
        const data = await response.json()
        const deptNames = data.departments?.map((dept: any) => dept.name) || []
        setDepartments(deptNames)
      }
    } catch (error) {
      console.error('Failed to load departments:', error)
    }
  }

  const loadPayrollRuns = async () => {
    try {
      const response = await fetch('/api/payroll/runs?status=completed&limit=20')
      if (response.ok) {
        const data = await response.json()
        setPayrollRuns(data.runs || [])
      }
    } catch (error) {
      console.error('Failed to load payroll runs:', error)
    }
  }

  const onSubmit = async (data: ReportExportFormData) => {
    setIsLoading(true)
    const jobId = `reports_${Date.now()}`
    
    try {
      onExportStart(jobId, 'Payroll Reports')

      // Build query parameters
      const params = new URLSearchParams()
      Object.entries(data).forEach(([key, value]) => {
        if (value && value !== 'all') params.append(key, value)
      })

      const response = await fetch(`/api/payroll/export/bulk-reports?${params.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Export failed')
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || 'payroll_report.xlsx'

      // Create blob and download URL
      const blob = await response.blob()
      const downloadUrl = URL.createObjectURL(blob)

      onExportComplete(jobId, filename, downloadUrl)
      
      // Trigger download
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast.success(`Report exported successfully as ${filename}`)
      
    } catch (error) {
      console.error('Export error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Export failed'
      onExportError(jobId, errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const reportTypes = [
    { value: 'payroll_summary', label: 'Payroll Summary', description: 'Overall payroll statistics and totals' },
    { value: 'tax_summary', label: 'Tax Summary', description: 'Tax calculations and deductions by department' },
    { value: 'department_summary', label: 'Department Summary', description: 'Payroll breakdown by department' },
    { value: 'employee_summary', label: 'Employee Summary', description: 'Individual employee payroll summary' }
  ]

  const selectedReportType = reportTypes.find(type => type.value === watchedReportType)

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Report Type */}
          <FormField
            control={form.control}
            name="reportType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Report Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{type.label}</span>
                          <span className="text-xs text-muted-foreground">{type.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Export Format */}
          <FormField
            control={form.control}
            name="format"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Export Format</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="excel">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span>Excel Workbook (.xlsx)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="pdf">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-red-600" />
                        <span>PDF Document (.pdf)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="csv">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span>CSV File (.csv)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Separator />

          {/* Date Range */}
          <div className="space-y-4">
            <h4 className="font-medium">Report Period</h4>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Optional Filters */}
          <div className="space-y-4">
            <h4 className="font-medium">Optional Filters</h4>
            
            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="All departments" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      {departments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4" />
                            <span>{dept}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="payrollRunId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Specific Payroll Run (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="All payroll runs (use date range)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Payroll Runs</SelectItem>
                      {payrollRuns.map((run) => (
                        <SelectItem key={run._id} value={run._id}>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4" />
                            <span>{run.name} - {new Date(run.payPeriod.startDate).toLocaleDateString()}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Separator />

          {/* Report Preview */}
          {selectedReportType && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  {selectedReportType.label}
                </CardTitle>
                <CardDescription>{selectedReportType.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  This report will include:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    {watchedReportType === 'payroll_summary' && (
                      <>
                        <li>Total employees and payslips processed</li>
                        <li>Gross and net salary totals</li>
                        <li>Total allowances and deductions</li>
                        <li>Tax calculations summary</li>
                      </>
                    )}
                    {watchedReportType === 'tax_summary' && (
                      <>
                        <li>Tax calculations by department</li>
                        <li>PAYE breakdown and totals</li>
                        <li>Tax bracket analysis</li>
                        <li>Average tax rates</li>
                      </>
                    )}
                    {watchedReportType === 'department_summary' && (
                      <>
                        <li>Payroll costs by department</li>
                        <li>Employee count per department</li>
                        <li>Average salaries by department</li>
                        <li>Department budget analysis</li>
                      </>
                    )}
                    {watchedReportType === 'employee_summary' && (
                      <>
                        <li>Individual employee payroll totals</li>
                        <li>Salary progression tracking</li>
                        <li>Allowances and deductions breakdown</li>
                        <li>Year-to-date calculations</li>
                      </>
                    )}
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Export Button */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {watchedFormat === 'excel' && 'Export as Excel workbook with charts and summaries'}
              {watchedFormat === 'pdf' && 'Export as formatted PDF report'}
              {watchedFormat === 'csv' && 'Export as CSV file for data analysis'}
            </div>
            
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

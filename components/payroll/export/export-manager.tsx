"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Download, 
  FileText, 
  Users, 
  CreditCard, 
  Building, 
  Percent,
  Calendar,
  Filter,
  Settings,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react"
import { BulkPayslipsExport } from "./bulk-payslips-export"
import { BulkReportsExport } from "./bulk-reports-export"
import { BankTransferExport } from "./bank-transfer-export"
import { EmployeeDataExport } from "./employee-data-export"
import { SalaryBandsExport } from "./salary-bands-export"

interface ExportJob {
  id: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  fileName?: string
  downloadUrl?: string
  createdAt: Date
  completedAt?: Date
  error?: string
}

export function ExportManager() {
  const [activeTab, setActiveTab] = useState<string>('payslips')
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([])

  const exportTypes = [
    {
      id: 'payslips',
      title: 'Bulk Payslips',
      description: 'Export multiple payslips in various formats',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      component: BulkPayslipsExport
    },
    {
      id: 'reports',
      title: 'Payroll Reports',
      description: 'Generate comprehensive payroll reports',
      icon: CreditCard,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      component: BulkReportsExport
    },
    {
      id: 'bank-transfers',
      title: 'Bank Transfer Files',
      description: 'Generate bank transfer files for payments',
      icon: Building,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      component: BankTransferExport
    },
    {
      id: 'employee-data',
      title: 'Employee Data',
      description: 'Export employee information and salary data',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      component: EmployeeDataExport
    },
    {
      id: 'salary-bands',
      title: 'Salary Bands',
      description: 'Export TCM salary bands and structures',
      icon: Percent,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      component: SalaryBandsExport
    }
  ]

  const activeExportType = exportTypes.find(type => type.id === activeTab)
  const ActiveComponent = activeExportType?.component

  const handleExportStart = (jobId: string, type: string) => {
    const newJob: ExportJob = {
      id: jobId,
      type,
      status: 'processing',
      createdAt: new Date()
    }
    setExportJobs(prev => [newJob, ...prev])
  }

  const handleExportComplete = (jobId: string, fileName: string, downloadUrl: string) => {
    setExportJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { ...job, status: 'completed', fileName, downloadUrl, completedAt: new Date() }
        : job
    ))
  }

  const handleExportError = (jobId: string, error: string) => {
    setExportJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { ...job, status: 'failed', error, completedAt: new Date() }
        : job
    ))
  }

  const getStatusIcon = (status: ExportJob['status']) => {
    switch (status) {
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: ExportJob['status']) => {
    switch (status) {
      case 'processing':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Processing</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>
      default:
        return <Badge variant="outline">Pending</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Export Manager</h2>
          <p className="text-muted-foreground">
            Export payroll data in various formats for reporting and integration
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter Jobs
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Export Type Selection */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Export Types</CardTitle>
              <CardDescription>Choose what to export</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {exportTypes.map((type) => {
                const Icon = type.icon
                return (
                  <button
                    key={type.id}
                    onClick={() => setActiveTab(type.id)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      activeTab === type.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-accent'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-md ${type.bgColor}`}>
                        <Icon className={`h-4 w-4 ${type.color}`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{type.title}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {type.description}
                        </p>
                      </div>
                    </div>
                  </button>
                )
              })}
            </CardContent>
          </Card>
        </div>

        {/* Export Configuration */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-3">
                {activeExportType && (
                  <div className={`p-2 rounded-md ${activeExportType.bgColor}`}>
                    <activeExportType.icon className={`h-5 w-5 ${activeExportType.color}`} />
                  </div>
                )}
                <div>
                  <CardTitle>{activeExportType?.title}</CardTitle>
                  <CardDescription>{activeExportType?.description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {ActiveComponent && (
                <ActiveComponent
                  onExportStart={handleExportStart}
                  onExportComplete={handleExportComplete}
                  onExportError={handleExportError}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Export Jobs History */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Exports</CardTitle>
              <CardDescription>Export job history</CardDescription>
            </CardHeader>
            <CardContent>
              {exportJobs.length === 0 ? (
                <div className="text-center py-6">
                  <Download className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">No exports yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {exportJobs.slice(0, 5).map((job) => (
                    <div key={job.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(job.status)}
                          <span className="text-sm font-medium">{job.type}</span>
                        </div>
                        {getStatusBadge(job.status)}
                      </div>
                      
                      <div className="text-xs text-muted-foreground mb-2">
                        {job.createdAt.toLocaleString()}
                      </div>

                      {job.status === 'completed' && job.downloadUrl && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full"
                          onClick={() => window.open(job.downloadUrl, '_blank')}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                      )}

                      {job.status === 'failed' && job.error && (
                        <div className="text-xs text-red-600 mt-1">
                          {job.error}
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {exportJobs.length > 5 && (
                    <Button variant="ghost" size="sm" className="w-full">
                      View All ({exportJobs.length})
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Exports</p>
                <p className="text-2xl font-bold">{exportJobs.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Completed</p>
                <p className="text-2xl font-bold">
                  {exportJobs.filter(job => job.status === 'completed').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm font-medium">Processing</p>
                <p className="text-2xl font-bold">
                  {exportJobs.filter(job => job.status === 'processing').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Failed</p>
                <p className="text-2xl font-bold">
                  {exportJobs.filter(job => job.status === 'failed').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Download, 
  FileText, 
  Calendar,
  Users,
  Building,
  Loader2,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"

const exportSchema = z.object({
  format: z.enum(['excel', 'pdf', 'zip']),
  payrollRunId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  department: z.string().optional(),
  employeeIds: z.array(z.string()).optional()
})

type ExportFormData = z.infer<typeof exportSchema>

interface BulkPayslipsExportProps {
  onExportStart: (jobId: string, type: string) => void
  onExportComplete: (jobId: string, fileName: string, downloadUrl: string) => void
  onExportError: (jobId: string, error: string) => void
}

export function BulkPayslipsExport({ 
  onExportStart, 
  onExportComplete, 
  onExportError 
}: BulkPayslipsExportProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [payrollRuns, setPayrollRuns] = useState<any[]>([])
  const [departments, setDepartments] = useState<string[]>([])
  const [employees, setEmployees] = useState<any[]>([])
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])

  const form = useForm<ExportFormData>({
    resolver: zodResolver(exportSchema),
    defaultValues: {
      format: 'excel',
      employeeIds: []
    }
  })

  const watchedFormat = form.watch('format')
  const watchedPayrollRun = form.watch('payrollRunId')
  const watchedDepartment = form.watch('department')

  // Load initial data
  useEffect(() => {
    loadPayrollRuns()
    loadDepartments()
    loadEmployees()
  }, [])

  const loadPayrollRuns = async () => {
    try {
      const response = await fetch('/api/payroll/runs?status=completed&limit=20')
      if (response.ok) {
        const data = await response.json()
        setPayrollRuns(data.runs || [])
      }
    } catch (error) {
      console.error('Failed to load payroll runs:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      const response = await fetch('/api/departments')
      if (response.ok) {
        const data = await response.json()
        const deptNames = data.departments?.map((dept: any) => dept.name) || []
        setDepartments(deptNames)
      }
    } catch (error) {
      console.error('Failed to load departments:', error)
    }
  }

  const loadEmployees = async () => {
    try {
      const response = await fetch('/api/employees?status=active&limit=100')
      if (response.ok) {
        const data = await response.json()
        setEmployees(data.employees || [])
      }
    } catch (error) {
      console.error('Failed to load employees:', error)
    }
  }

  const handleEmployeeSelection = (employeeId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmployees(prev => [...prev, employeeId])
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== employeeId))
    }
  }

  const handleSelectAllEmployees = (checked: boolean) => {
    if (checked) {
      const filteredEmployees = (watchedDepartment && watchedDepartment !== 'all')
        ? employees.filter(emp => emp.department === watchedDepartment)
        : employees
      setSelectedEmployees(filteredEmployees.map(emp => emp._id))
    } else {
      setSelectedEmployees([])
    }
  }

  const onSubmit = async (data: ExportFormData) => {
    setIsLoading(true)
    const jobId = `payslips_${Date.now()}`
    
    try {
      onExportStart(jobId, 'Bulk Payslips')

      // Build query parameters
      const params = new URLSearchParams()
      params.append('format', data.format)

      if (data.payrollRunId && data.payrollRunId !== 'all') {
        params.append('payrollRunId', data.payrollRunId)
      }

      if (data.startDate) {
        params.append('startDate', data.startDate)
      }

      if (data.endDate) {
        params.append('endDate', data.endDate)
      }

      if (data.department && data.department !== 'all') {
        params.append('department', data.department)
      }
      
      if (selectedEmployees.length > 0) {
        params.append('employeeIds', selectedEmployees.join(','))
      }

      const response = await fetch(`/api/payroll/export/bulk-payslips?${params.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Export failed')
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || 'payslips_export.xlsx'

      // Create blob and download URL
      const blob = await response.blob()
      const downloadUrl = URL.createObjectURL(blob)

      onExportComplete(jobId, filename, downloadUrl)
      
      // Trigger download
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast.success(`Payslips exported successfully as ${filename}`)
      
    } catch (error) {
      console.error('Export error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Export failed'
      onExportError(jobId, errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredEmployees = (watchedDepartment && watchedDepartment !== 'all')
    ? employees.filter(emp => emp.department === watchedDepartment)
    : employees

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Export Format */}
          <FormField
            control={form.control}
            name="format"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Export Format</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="excel">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span>Excel Workbook (.xlsx)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="pdf">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-red-600" />
                        <span>PDF Document (.pdf)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="zip">
                      <div className="flex items-center space-x-2">
                        <Download className="h-4 w-4 text-blue-600" />
                        <span>ZIP Archive (Individual PDFs)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Separator />

          {/* Filter Options */}
          <div className="space-y-4">
            <h4 className="font-medium">Filter Options</h4>
            
            {/* Payroll Run Selection */}
            <FormField
              control={form.control}
              name="payrollRunId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payroll Run (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payroll run or leave empty for date range" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Payroll Runs</SelectItem>
                      {payrollRuns.map((run) => (
                        <SelectItem key={run._id} value={run._id}>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4" />
                            <span>{run.name} - {new Date(run.payPeriodStart).toLocaleDateString()}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Range (if no payroll run selected) */}
            {(!watchedPayrollRun || watchedPayrollRun === 'all') && (
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Department Filter */}
            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="All departments" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      {departments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4" />
                            <span>{dept}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Separator />

          {/* Employee Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Employee Selection</h4>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}
                  onCheckedChange={handleSelectAllEmployees}
                />
                <label htmlFor="select-all" className="text-sm">
                  Select All ({filteredEmployees.length})
                </label>
              </div>
            </div>

            {filteredEmployees.length > 0 ? (
              <div className="max-h-48 overflow-y-auto border rounded-md p-3 space-y-2">
                {filteredEmployees.map((employee) => (
                  <div key={employee._id} className="flex items-center space-x-2">
                    <Checkbox
                      id={employee._id}
                      checked={selectedEmployees.includes(employee._id)}
                      onCheckedChange={(checked) => handleEmployeeSelection(employee._id, checked as boolean)}
                    />
                    <label htmlFor={employee._id} className="text-sm flex-1 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <span>{employee.firstName} {employee.lastName}</span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {employee.employeeId}
                          </Badge>
                          {employee.department && (
                            <Badge variant="secondary" className="text-xs">
                              {employee.department}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                <Users className="h-8 w-8 mx-auto mb-2" />
                <p>No employees found</p>
              </div>
            )}

            {selectedEmployees.length > 0 && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>{selectedEmployees.length} employee(s) selected</span>
              </div>
            )}
          </div>

          <Separator />

          {/* Export Button */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {watchedFormat === 'excel' && 'Export as Excel workbook with summary and detailed sheets'}
              {watchedFormat === 'pdf' && 'Export as single PDF document with all payslips'}
              {watchedFormat === 'zip' && 'Export as ZIP file containing individual PDF payslips'}
            </div>
            
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Payslips
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

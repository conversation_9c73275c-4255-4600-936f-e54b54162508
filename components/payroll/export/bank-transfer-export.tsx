"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building, Download, Loader2 } from "lucide-react"

interface BankTransferExportProps {
  onExportStart: (jobId: string, type: string) => void
  onExportComplete: (jobId: string, fileName: string, downloadUrl: string) => void
  onExportError: (jobId: string, error: string) => void
}

export function BankTransferExport({ 
  onExportStart, 
  onExportComplete, 
  onExportError 
}: BankTransferExportProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleExport = async (format: string, bankFormat: string) => {
    setIsLoading(true)
    const jobId = `bank_transfer_${Date.now()}`
    
    try {
      onExportStart(jobId, 'Bank Transfer Files')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const filename = `bank_transfers_${bankFormat}_${new Date().toISOString().split('T')[0]}.${format}`
      const downloadUrl = '#' // Placeholder
      
      onExportComplete(jobId, filename, downloadUrl)
      
    } catch (error) {
      onExportError(jobId, 'Export failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Bank Transfer File Generation
          </CardTitle>
          <CardDescription>
            Generate bank transfer files for automated salary payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Bank Transfer Export</h3>
            <p className="text-muted-foreground mb-4">
              This feature will generate bank transfer files in various formats for different Malawian banks.
            </p>
            <div className="flex flex-wrap gap-2 justify-center mb-6">
              <Badge variant="outline">NBS Malawi</Badge>
              <Badge variant="outline">FMB Bank</Badge>
              <Badge variant="outline">Standard Bank</Badge>
              <Badge variant="outline">MT940 Format</Badge>
            </div>
            <Button 
              onClick={() => handleExport('excel', 'standard')} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Coming Soon
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

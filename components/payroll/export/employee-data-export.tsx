"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Download, Loader2 } from "lucide-react"

interface EmployeeDataExportProps {
  onExportStart: (jobId: string, type: string) => void
  onExportComplete: (jobId: string, fileName: string, downloadUrl: string) => void
  onExportError: (jobId: string, error: string) => void
}

export function EmployeeDataExport({ 
  onExportStart, 
  onExportComplete, 
  onExportError 
}: EmployeeDataExportProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleExport = async (exportType: string) => {
    setIsLoading(true)
    const jobId = `employee_data_${Date.now()}`
    
    try {
      onExportStart(jobId, 'Employee Data')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const filename = `employee_data_${exportType}_${new Date().toISOString().split('T')[0]}.xlsx`
      const downloadUrl = '#' // Placeholder
      
      onExportComplete(jobId, filename, downloadUrl)
      
    } catch (error) {
      onExportError(jobId, 'Export failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Employee Data Export
          </CardTitle>
          <CardDescription>
            Export employee information and salary data for external systems
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Employee Data Export</h3>
            <p className="text-muted-foreground mb-4">
              Export comprehensive employee data including personal information, salary details, and payroll history.
            </p>
            <div className="flex flex-wrap gap-2 justify-center mb-6">
              <Badge variant="outline">Basic Info</Badge>
              <Badge variant="outline">Salary Data</Badge>
              <Badge variant="outline">Payroll History</Badge>
              <Badge variant="outline">Bank Details</Badge>
            </div>
            <Button 
              onClick={() => handleExport('complete')} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Coming Soon
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

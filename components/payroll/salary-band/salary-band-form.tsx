"use client"

import { useState } from "react"
import { use<PERSON><PERSON>, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker"
import { Plus, Trash, DollarSign, Percent, Calendar, Building } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Validation schema
const salaryBandSchema = z.object({
  tcmCode: z.string().min(1, "TCM Code is required").regex(/^TCM\s+\d+$/, "TCM Code must be in format 'TCM X'"),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  minSalary: z.number().min(0, "Minimum salary must be positive"),
  maxSalary: z.number().min(0, "Maximum salary must be positive"),
  currency: z.string().min(1, "Currency is required"),
  standardAllowances: z.array(z.object({
    name: z.string().min(1, "Allowance name is required"),
    amount: z.number().optional(),
    percentage: z.number().optional(),
    isTaxable: z.boolean()
  })),
  standardDeductions: z.array(z.object({
    name: z.string().min(1, "Deduction name is required"),
    amount: z.number().optional(),
    percentage: z.number().optional()
  })),
  stepIncrement: z.number().optional(),
  maxSteps: z.number().optional(),
  annualIncrementPercentage: z.number().optional(),
  effectiveDate: z.date(),
  expiryDate: z.date().optional(),
  isActive: z.boolean()
}).refine((data) => data.maxSalary >= data.minSalary, {
  message: "Maximum salary must be greater than or equal to minimum salary",
  path: ["maxSalary"]
})

type SalaryBandFormData = z.infer<typeof salaryBandSchema>

interface SalaryBand {
  _id?: string
  id?: string
  tcmCode: string
  name: string
  description?: string
  minSalary: number
  maxSalary: number
  currency: string
  standardAllowances: {
    name: string
    amount?: number
    percentage?: number
    isTaxable: boolean
  }[]
  standardDeductions: {
    name: string
    amount?: number
    percentage?: number
  }[]
  stepIncrement?: number
  maxSteps?: number
  annualIncrementPercentage?: number
  effectiveDate: Date | string
  expiryDate?: Date | string | null
  isActive: boolean
}

interface SalaryBandFormProps {
  band?: SalaryBand | null
  onSubmit: (data: SalaryBandFormData) => void
  onCancel: () => void
}

// TCM Code options
const tcmCodes = Array.from({ length: 12 }, (_, i) => ({
  value: `TCM ${i + 1}`,
  label: `TCM ${i + 1}`,
  description: getTCMDescription(i + 1)
}))

function getTCMDescription(level: number): string {
  const descriptions: Record<number, string> = {
    1: "Registrar/Chief Executive",
    2: "Director",
    3: "Deputy Director",
    4: "Assistant Director",
    5: "Principal Officer",
    6: "Senior Officer",
    7: "Officer",
    8: "Assistant Officer",
    9: "Senior Clerk",
    10: "Clerk",
    11: "Assistant Clerk",
    12: "Office Assistant"
  }
  return descriptions[level] || `Level ${level}`
}

// Currency options
const currencies = [
  { value: "MWK", label: "MWK - Malawi Kwacha" },
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "GBP", label: "GBP - British Pound" }
]

export function SalaryBandForm({ band, onSubmit, onCancel }: SalaryBandFormProps) {
  const [activeTab, setActiveTab] = useState("basic")

  const form = useForm<SalaryBandFormData>({
    resolver: zodResolver(salaryBandSchema),
    defaultValues: {
      tcmCode: band?.tcmCode || "",
      name: band?.name || "",
      description: band?.description || "",
      minSalary: band?.minSalary || 0,
      maxSalary: band?.maxSalary || 0,
      currency: band?.currency || "MWK",
      standardAllowances: band?.standardAllowances || [],
      standardDeductions: band?.standardDeductions || [],
      stepIncrement: band?.stepIncrement || undefined,
      maxSteps: band?.maxSteps || 10,
      annualIncrementPercentage: band?.annualIncrementPercentage || undefined,
      effectiveDate: band?.effectiveDate ? new Date(band.effectiveDate) : new Date(),
      expiryDate: band?.expiryDate ? new Date(band.expiryDate) : undefined,
      isActive: band?.isActive ?? true
    }
  })

  const {
    fields: allowanceFields,
    append: appendAllowance,
    remove: removeAllowance
  } = useFieldArray({
    control: form.control,
    name: "standardAllowances"
  })

  const {
    fields: deductionFields,
    append: appendDeduction,
    remove: removeDeduction
  } = useFieldArray({
    control: form.control,
    name: "standardDeductions"
  })

  const handleSubmit = (data: SalaryBandFormData) => {
    onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="salary">Salary Range</TabsTrigger>
            <TabsTrigger value="allowances">Allowances</TabsTrigger>
            <TabsTrigger value="deductions">Deductions</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="tcmCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>TCM Code</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select TCM code" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tcmCodes.map((tcm) => (
                          <SelectItem key={tcm.value} value={tcm.value}>
                            <div className="flex flex-col">
                              <span className="font-medium">{tcm.label}</span>
                              <span className="text-sm text-muted-foreground">{tcm.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the TCM level for this salary band
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Band Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Director, Manager" {...field} />
                    </FormControl>
                    <FormDescription>
                      Descriptive name for this salary band
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe the roles and responsibilities for this band..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional description of the salary band
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="effectiveDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Effective Date</FormLabel>
                    <FormControl>
                      <EnhancedDatePicker
                        date={field.value}
                        onDateChange={field.onChange}
                        placeholder="Select effective date"
                      />
                    </FormControl>
                    <FormDescription>
                      When this band becomes active
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiryDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expiry Date (Optional)</FormLabel>
                    <FormControl>
                      <EnhancedDatePicker
                        date={field.value}
                        onDateChange={field.onChange}
                        placeholder="Select expiry date"
                      />
                    </FormControl>
                    <FormDescription>
                      When this band expires (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Active Status
                      </FormLabel>
                      <FormDescription>
                        Whether this salary band is currently active
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          <TabsContent value="salary" className="space-y-4 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="minSalary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Salary</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Minimum salary for this band
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxSalary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Salary</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum salary for this band
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Currency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {currencies.map((currency) => (
                          <SelectItem key={currency.value} value={currency.value}>
                            {currency.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Currency for salary amounts
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="stepIncrement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Step Increment (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Amount for each step progression
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxSteps"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Steps</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="10"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 10)}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum number of steps in this band
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="annualIncrementPercentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Annual Increment % (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        step="0.1"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Annual increment percentage
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          <TabsContent value="allowances" className="space-y-4 mt-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">Standard Allowances</h3>
                <p className="text-sm text-muted-foreground">
                  Define standard allowances for this salary band
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendAllowance({ name: "", amount: undefined, percentage: undefined, isTaxable: true })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Allowance
              </Button>
            </div>

            <div className="space-y-4">
              {allowanceFields.map((field, index) => (
                <Card key={field.id}>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                      <FormField
                        control={form.control}
                        name={`standardAllowances.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Allowance Name</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Housing Allowance" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`standardAllowances.${index}.amount`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fixed Amount</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`standardAllowances.${index}.percentage`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Percentage</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                step="0.1"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex items-center gap-2">
                        <FormField
                          control={form.control}
                          name={`standardAllowances.${index}.isTaxable`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel className="text-sm">Taxable</FormLabel>
                            </FormItem>
                          )}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeAllowance(index)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {allowanceFields.length === 0 && (
              <div className="text-center py-8 border-2 border-dashed border-muted rounded-lg">
                <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">No allowances defined</h3>
                <p className="text-muted-foreground mb-4">
                  Add standard allowances for this salary band
                </p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => appendAllowance({ name: "", amount: undefined, percentage: undefined, isTaxable: true })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Allowance
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="deductions" className="space-y-4 mt-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">Standard Deductions</h3>
                <p className="text-sm text-muted-foreground">
                  Define standard deductions for this salary band
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendDeduction({ name: "", amount: undefined, percentage: undefined })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Deduction
              </Button>
            </div>

            <div className="space-y-4">
              {deductionFields.map((field, index) => (
                <Card key={field.id}>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                      <FormField
                        control={form.control}
                        name={`standardDeductions.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Deduction Name</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., PAYE, Pension" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`standardDeductions.${index}.amount`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fixed Amount</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`standardDeductions.${index}.percentage`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Percentage</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                step="0.1"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeDeduction(index)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {deductionFields.length === 0 && (
              <div className="text-center py-8 border-2 border-dashed border-muted rounded-lg">
                <Percent className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">No deductions defined</h3>
                <p className="text-muted-foreground mb-4">
                  Add standard deductions for this salary band
                </p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => appendDeduction({ name: "", amount: undefined, percentage: undefined })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Deduction
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            {band ? "Update Salary Band" : "Create Salary Band"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

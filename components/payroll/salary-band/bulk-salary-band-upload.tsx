"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  ArrowRight,
  Building
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface BulkSalaryBandUploadProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface UploadResult {
  success: boolean
  message: string
  data?: {
    processed: number
    successful: number
    failed: number
    results: {
      row: number
      tcmCode: string
      name: string
      status: 'success' | 'error' | 'warning'
      message: string
    }[]
  }
}

export function BulkSalaryBandUpload({ isOpen, onClose, onSuccess }: BulkSalaryBandUploadProps) {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [currentStep, setCurrentStep] = useState<'upload' | 'processing' | 'results'>('upload')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast({
          title: "Invalid File Type",
          description: "Please select an Excel file (.xlsx or .xls)",
          variant: "destructive",
        })
        return
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select a file smaller than 10MB",
          variant: "destructive",
        })
        return
      }

      setSelectedFile(file)
    }
  }

  // Handle template download
  const handleDownloadTemplate = async () => {
    try {
      // In a real implementation, this would call the API
      console.log("Downloading salary band template...")
      
      // Mock download
      const link = document.createElement('a')
      link.href = '/api/payroll/salary-bands/template'
      link.download = 'salary-bands-template.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast({
        title: "Template Downloaded",
        description: "The salary band template has been downloaded successfully.",
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download template. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setCurrentStep('processing')
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // In a real implementation, this would call the API
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock successful result
      const mockResult: UploadResult = {
        success: true,
        message: "Salary bands imported successfully",
        data: {
          processed: 12,
          successful: 10,
          failed: 2,
          results: [
            {
              row: 1,
              tcmCode: "TCM 1",
              name: "Registrar",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 2,
              tcmCode: "TCM 2",
              name: "Director",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 3,
              tcmCode: "TCM 3",
              name: "Deputy Director",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 4,
              tcmCode: "TCM 4",
              name: "Assistant Director",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 5,
              tcmCode: "TCM 5",
              name: "Principal Officer",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 6,
              tcmCode: "TCM 6",
              name: "Senior Officer",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 7,
              tcmCode: "TCM 7",
              name: "Officer",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 8,
              tcmCode: "TCM 8",
              name: "Assistant Officer",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 9,
              tcmCode: "TCM 9",
              name: "Senior Clerk",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 10,
              tcmCode: "TCM 10",
              name: "Clerk",
              status: 'success',
              message: "Salary band created successfully"
            },
            {
              row: 11,
              tcmCode: "TCM 1",
              name: "Duplicate Registrar",
              status: 'error',
              message: "TCM code already exists"
            },
            {
              row: 12,
              tcmCode: "TCM INVALID",
              name: "Invalid Code",
              status: 'error',
              message: "Invalid TCM code format"
            }
          ]
        }
      }

      clearInterval(progressInterval)
      setUploadProgress(100)
      setUploadResult(mockResult)
      setCurrentStep('results')

      if (mockResult.success) {
        toast({
          title: "Upload Successful",
          description: `${mockResult.data?.successful} salary bands imported successfully.`,
        })
      }

    } catch (error) {
      console.error('Upload error:', error)
      setUploadResult({
        success: false,
        message: "Failed to upload salary bands. Please try again."
      })
      setCurrentStep('results')
      
      toast({
        title: "Upload Failed",
        description: "Failed to upload salary bands. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // Reset form
  const handleReset = () => {
    setCurrentStep('upload')
    setSelectedFile(null)
    setUploadProgress(0)
    setUploadResult(null)
    setIsUploading(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Handle close
  const handleClose = () => {
    if (uploadResult?.success) {
      onSuccess()
    }
    handleReset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Import Salary Bands</DialogTitle>
          <DialogDescription>
            Import multiple salary bands from an Excel file with TCM codes and compensation structures.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-center space-x-4">
            <div className={`flex items-center space-x-2 ${currentStep === 'upload' ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 'upload' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                1
              </div>
              <span className="text-sm font-medium">Upload File</span>
            </div>
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
            <div className={`flex items-center space-x-2 ${currentStep === 'processing' ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 'processing' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                2
              </div>
              <span className="text-sm font-medium">Processing</span>
            </div>
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
            <div className={`flex items-center space-x-2 ${currentStep === 'results' ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 'results' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                3
              </div>
              <span className="text-sm font-medium">Results</span>
            </div>
          </div>

          {/* Step 1: Upload File */}
          {currentStep === 'upload' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Step 1: Download Template</CardTitle>
                  <CardDescription>
                    Download the salary band template to ensure your data is in the correct format.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button onClick={handleDownloadTemplate} variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Download Salary Band Template
                  </Button>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <p><strong>Template includes:</strong></p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>TCM codes (TCM 1 through TCM 12)</li>
                      <li>Salary ranges and progression settings</li>
                      <li>Standard allowances and deductions</li>
                      <li>Effective dates and status information</li>
                      <li>Detailed instructions and examples</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Step 2: Upload Your File</CardTitle>
                  <CardDescription>
                    Select the completed Excel file with your salary band data.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                      <div className="text-center">
                        <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <div className="space-y-2">
                          <p className="text-sm font-medium">
                            {selectedFile ? selectedFile.name : "Choose a file or drag and drop"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Excel files only (.xlsx, .xls) • Max 10MB
                          </p>
                        </div>
                        <Input
                          ref={fileInputRef}
                          type="file"
                          accept=".xlsx,.xls"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mt-4"
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          Select File
                        </Button>
                      </div>
                    </div>

                    {selectedFile && (
                      <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-green-600" />
                          <div>
                            <p className="text-sm font-medium">{selectedFile.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={() => {
                            setSelectedFile(null)
                            if (fileInputRef.current) {
                              fileInputRef.current.value = ''
                            }
                          }}
                          variant="ghost"
                          size="sm"
                        >
                          Remove
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Step 2: Processing */}
          {currentStep === 'processing' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Processing Salary Bands</CardTitle>
                  <CardDescription>
                    Please wait while we process your salary band data...
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span className="text-sm">
                        {uploadProgress < 30 ? "Validating file format..." :
                         uploadProgress < 60 ? "Processing salary band data..." :
                         uploadProgress < 90 ? "Creating salary bands..." :
                         "Finalizing import..."}
                      </span>
                    </div>
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-xs text-muted-foreground">
                      {uploadProgress}% complete
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Step 3: Results */}
          {currentStep === 'results' && uploadResult && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    {uploadResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600 mr-2" />
                    )}
                    Import {uploadResult.success ? 'Completed' : 'Failed'}
                  </CardTitle>
                  <CardDescription>
                    {uploadResult.message}
                  </CardDescription>
                </CardHeader>
                {uploadResult.data && (
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{uploadResult.data.processed}</div>
                        <div className="text-sm text-muted-foreground">Total Processed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{uploadResult.data.successful}</div>
                        <div className="text-sm text-muted-foreground">Successful</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{uploadResult.data.failed}</div>
                        <div className="text-sm text-muted-foreground">Failed</div>
                      </div>
                    </div>

                    {uploadResult.data.results.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3">Detailed Results</h4>
                        <div className="border rounded-lg">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Row</TableHead>
                                <TableHead>TCM Code</TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Message</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {uploadResult.data.results.map((result, index) => (
                                <TableRow key={index}>
                                  <TableCell>{result.row}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline" className="font-mono">
                                      {result.tcmCode}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>{result.name}</TableCell>
                                  <TableCell>
                                    <Badge variant={
                                      result.status === 'success' ? 'default' :
                                      result.status === 'warning' ? 'secondary' : 'destructive'
                                    }>
                                      {result.status === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
                                      {result.status === 'warning' && <AlertTriangle className="h-3 w-3 mr-1" />}
                                      {result.status === 'error' && <XCircle className="h-3 w-3 mr-1" />}
                                      {result.status}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="text-sm">{result.message}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}
                  </CardContent>
                )}
              </Card>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            {currentStep === 'upload' && (
              <>
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpload} 
                  disabled={!selectedFile || isUploading}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload & Process
                </Button>
              </>
            )}
            
            {currentStep === 'processing' && (
              <Button variant="outline" disabled>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </Button>
            )}
            
            {currentStep === 'results' && (
              <>
                <Button variant="outline" onClick={handleReset}>
                  Upload Another File
                </Button>
                <Button onClick={handleClose}>
                  {uploadResult?.success ? 'Complete' : 'Close'}
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

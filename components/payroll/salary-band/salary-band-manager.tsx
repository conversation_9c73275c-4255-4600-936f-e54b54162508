"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash,
  Eye,
  Calendar,
  DollarSign,
  Building,
  User,
  Filter,
  ArrowUpDown,
  Loader2,
  AlertTriangle,
  RefreshCw,
  Upload,
  Check,
  X
} from "lucide-react"
import { SalaryBandForm } from "./salary-band-form"
import { SalaryBandDetails } from "./salary-band-details"
import { BulkSalaryBandUpload } from "./bulk-salary-band-upload"
import { useToast } from "@/components/ui/use-toast"
import { SimpleCurrencyDisplay } from "@/components/ui/simple-currency-display"

// Salary band interfaces
interface SalaryBand {
  _id?: string
  id?: string
  tcmCode: string
  name: string
  description?: string
  minSalary: number
  maxSalary: number
  currency: string
  standardAllowances: {
    name: string
    amount?: number
    percentage?: number
    isTaxable: boolean
  }[]
  standardDeductions: {
    name: string
    amount?: number
    percentage?: number
  }[]
  stepIncrement?: number
  maxSteps?: number
  annualIncrementPercentage?: number
  effectiveDate: Date | string
  expiryDate?: Date | string | null
  isActive: boolean
  approvedBy?: string
  approvedAt?: Date | string
  createdBy?: string
  updatedBy?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

// Mock data for development
const mockSalaryBands: SalaryBand[] = [
  {
    _id: "1",
    tcmCode: "TCM 1",
    name: "Registrar",
    description: "Chief Executive Officer level",
    minSalary: 2500000,
    maxSalary: 3500000,
    currency: "MWK",
    standardAllowances: [
      { name: "Housing Allowance", percentage: 30, isTaxable: true },
      { name: "Transport Allowance", amount: 200000, isTaxable: true }
    ],
    standardDeductions: [
      { name: "PAYE", percentage: 30 },
      { name: "Pension", percentage: 5 }
    ],
    stepIncrement: 100000,
    maxSteps: 10,
    annualIncrementPercentage: 5,
    effectiveDate: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01"
  },
  {
    _id: "2",
    tcmCode: "TCM 2",
    name: "Director",
    description: "Director level positions",
    minSalary: 1800000,
    maxSalary: 2400000,
    currency: "MWK",
    standardAllowances: [
      { name: "Housing Allowance", percentage: 25, isTaxable: true },
      { name: "Transport Allowance", amount: 150000, isTaxable: true }
    ],
    standardDeductions: [
      { name: "PAYE", percentage: 25 },
      { name: "Pension", percentage: 5 }
    ],
    stepIncrement: 80000,
    maxSteps: 8,
    annualIncrementPercentage: 4,
    effectiveDate: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01"
  },
  {
    _id: "3",
    tcmCode: "TCM 3",
    name: "Deputy Director",
    description: "Deputy Director level positions",
    minSalary: 1400000,
    maxSalary: 1800000,
    currency: "MWK",
    standardAllowances: [
      { name: "Housing Allowance", percentage: 20, isTaxable: true },
      { name: "Transport Allowance", amount: 120000, isTaxable: true }
    ],
    standardDeductions: [
      { name: "PAYE", percentage: 25 },
      { name: "Pension", percentage: 5 }
    ],
    stepIncrement: 60000,
    maxSteps: 8,
    annualIncrementPercentage: 4,
    effectiveDate: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01"
  }
]

export function SalaryBandManager() {
  const { toast } = useToast()
  
  // State management
  const [salaryBands, setSalaryBands] = useState<SalaryBand[]>(mockSalaryBands)
  const [isLoading, setIsLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showInactive, setShowInactive] = useState(false)
  const [selectedBands, setSelectedBands] = useState<string[]>([])
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedBand, setSelectedBand] = useState<SalaryBand | null>(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [bandToDelete, setBandToDelete] = useState<string | null>(null)
  const [isBulkUploadOpen, setIsBulkUploadOpen] = useState(false)
  const [isBulkDeleteLoading, setIsBulkDeleteLoading] = useState(false)

  // Filter salary bands based on search query and active status
  const filteredBands = mockSalaryBands.filter(
    (band) => {
      const matchesSearch = band.tcmCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           band.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (band.description && band.description.toLowerCase().includes(searchQuery.toLowerCase()))
      const matchesStatus = showInactive || band.isActive
      return matchesSearch && matchesStatus
    }
  )

  // Handle bulk upload success
  const handleBulkUploadSuccess = () => {
    // In a real implementation, this would refresh the salary bands list
    console.log("Bulk upload successful, refreshing salary bands...")
    toast({
      title: "Bulk Import Successful",
      description: "Salary bands have been imported successfully.",
    })
    setIsBulkUploadOpen(false)
  }

  // Handle create default TCM bands
  const handleCreateDefaultBands = () => {
    toast({
      title: "Default Bands Created",
      description: "Default TCM salary bands (TCM 1-12) have been created successfully.",
    })
  }

  // Confirm delete
  const confirmDelete = (id: string) => {
    setBandToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  // Handle delete
  const handleDelete = async () => {
    if (!bandToDelete) return
    
    try {
      // In a real implementation, this would call the API
      console.log("Deleting salary band:", bandToDelete)
      
      toast({
        title: "Salary Band Deleted",
        description: "The salary band has been deleted successfully.",
      })
      
      setIsDeleteDialogOpen(false)
      setBandToDelete(null)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete salary band. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedBands.length === 0) return
    
    setIsBulkDeleteLoading(true)
    try {
      // In a real implementation, this would call the API
      console.log("Bulk deleting salary bands:", selectedBands)
      
      toast({
        title: "Bulk Delete Successful",
        description: `${selectedBands.length} salary bands have been deleted successfully.`,
      })
      
      setSelectedBands([])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete salary bands. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsBulkDeleteLoading(false)
    }
  }

  // Handle checkbox selection
  const handleSelectBand = (bandId: string, checked: boolean) => {
    if (checked) {
      setSelectedBands([...selectedBands, bandId])
    } else {
      setSelectedBands(selectedBands.filter(id => id !== bandId))
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedBands(filteredBands.map(band => band._id || band.id || ''))
    } else {
      setSelectedBands([])
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative w-full sm:w-96">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search salary bands..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={isLoading || isRefreshing}
          />
        </div>
        <div className="flex items-center gap-2 flex-wrap">
          {selectedBands.length > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              disabled={isBulkDeleteLoading}
              className="h-8"
            >
              {isBulkDeleteLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash className="h-4 w-4 mr-2" />
              )}
              Delete ({selectedBands.length})
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInactive(!showInactive)}
            className="h-8"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showInactive ? "Hide Inactive" : "Show Inactive"}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" className="h-8 gap-1">
                <Plus className="h-4 w-4" />
                <span>New Band</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Custom Band
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCreateDefaultBands}>
                <Check className="h-4 w-4 mr-2" />
                Create Default TCM Bands
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsBulkUploadOpen(true)}>
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Bands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredBands.length}</div>
            <p className="text-xs text-muted-foreground">
              {filteredBands.filter(b => b.isActive).length} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Salary Range</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              <SimpleCurrencyDisplay 
                amount={Math.min(...filteredBands.map(b => b.minSalary))} 
                currency="MWK" 
              />
              {" - "}
              <SimpleCurrencyDisplay 
                amount={Math.max(...filteredBands.map(b => b.maxSalary))} 
                currency="MWK" 
              />
            </div>
            <p className="text-xs text-muted-foreground">Min - Max range</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">TCM Levels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredBands.map(b => b.tcmCode)).size}
            </div>
            <p className="text-xs text-muted-foreground">Unique TCM codes</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Selected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{selectedBands.length}</div>
            <p className="text-xs text-muted-foreground">
              {selectedBands.length > 0 ? "bands selected" : "No selection"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Salary Bands Table */}
      <Card>
        <CardHeader>
          <CardTitle>Salary Bands</CardTitle>
          <CardDescription>
            Manage TCM salary bands and their compensation structures
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedBands.length === filteredBands.length && filteredBands.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>TCM Code</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Salary Range</TableHead>
                <TableHead>Allowances</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Effective Date</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBands.map((band) => (
                <TableRow key={band._id || band.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedBands.includes(band._id || band.id || '')}
                      onCheckedChange={(checked) => 
                        handleSelectBand(band._id || band.id || '', checked as boolean)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-mono">
                      {band.tcmCode}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{band.name}</div>
                      {band.description && (
                        <div className="text-sm text-muted-foreground">
                          {band.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <SimpleCurrencyDisplay amount={band.minSalary} currency={band.currency} />
                      {" - "}
                      <SimpleCurrencyDisplay amount={band.maxSalary} currency={band.currency} />
                    </div>
                    {band.stepIncrement && (
                      <div className="text-xs text-muted-foreground">
                        Step: <SimpleCurrencyDisplay amount={band.stepIncrement} currency={band.currency} />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {band.standardAllowances.length} allowances
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {band.standardDeductions.length} deductions
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={band.isActive ? "default" : "secondary"}>
                      {band.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(band.effectiveDate).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedBand(band)
                            setIsEditMode(false)
                            setIsViewDialogOpen(true)
                          }}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedBand(band)
                            setIsEditMode(true)
                            setIsCreateDialogOpen(true)
                          }}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => confirmDelete(band._id || band.id || '')}
                          className="text-destructive"
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredBands.length === 0 && (
            <div className="text-center py-8">
              <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No salary bands found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? "No bands match your search criteria." : "Get started by creating your first salary band."}
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Salary Band
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit Salary Band" : "Create New Salary Band"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode 
                ? "Update the salary band information and compensation structure."
                : "Create a new salary band with compensation structure and TCM code mapping."
              }
            </DialogDescription>
          </DialogHeader>
          <SalaryBandForm
            band={isEditMode ? selectedBand : undefined}
            onSubmit={(data) => {
              console.log("Salary band data:", data)
              toast({
                title: isEditMode ? "Salary Band Updated" : "Salary Band Created",
                description: isEditMode 
                  ? "The salary band has been updated successfully."
                  : "The salary band has been created successfully.",
              })
              setIsCreateDialogOpen(false)
              setSelectedBand(null)
              setIsEditMode(false)
            }}
            onCancel={() => {
              setIsCreateDialogOpen(false)
              setSelectedBand(null)
              setIsEditMode(false)
            }}
          />
        </DialogContent>
      </Dialog>

      {/* View Details Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Salary Band Details</DialogTitle>
            <DialogDescription>
              View detailed information about this salary band and its compensation structure.
            </DialogDescription>
          </DialogHeader>
          {selectedBand && (
            <SalaryBandDetails
              band={selectedBand}
              onEdit={() => {
                setIsViewDialogOpen(false)
                setIsEditMode(true)
                setIsCreateDialogOpen(true)
              }}
              onClose={() => {
                setIsViewDialogOpen(false)
                setSelectedBand(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the salary band
              and may affect employees currently assigned to this band.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Upload Dialog */}
      <BulkSalaryBandUpload
        isOpen={isBulkUploadOpen}
        onClose={() => setIsBulkUploadOpen(false)}
        onSuccess={handleBulkUploadSuccess}
      />
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  Calendar,
  DollarSign,
  Building,
  User,
  Edit,
  TrendingUp,
  Percent,
  CheckCircle,
  XCircle,
  Clock,
  Users
} from "lucide-react"
import { SimpleCurrencyDisplay } from "@/components/ui/simple-currency-display"

interface SalaryBand {
  _id?: string
  id?: string
  tcmCode: string
  name: string
  description?: string
  minSalary: number
  maxSalary: number
  currency: string
  standardAllowances: {
    name: string
    amount?: number
    percentage?: number
    isTaxable: boolean
  }[]
  standardDeductions: {
    name: string
    amount?: number
    percentage?: number
  }[]
  stepIncrement?: number
  maxSteps?: number
  annualIncrementPercentage?: number
  effectiveDate: Date | string
  expiryDate?: Date | string | null
  isActive: boolean
  approvedBy?: string
  approvedAt?: Date | string
  createdBy?: string
  updatedBy?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

interface SalaryBandDetailsProps {
  band: SalaryBand
  onEdit: () => void
  onClose: () => void
}

export function SalaryBandDetails({ band, onEdit, onClose }: SalaryBandDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")

  // Format dates
  const effectiveDateFormatted = new Date(band.effectiveDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const expiryDateFormatted = band.expiryDate 
    ? new Date(band.expiryDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null

  const createdDateFormatted = band.createdAt 
    ? new Date(band.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null

  // Calculate salary range
  const salaryRange = band.maxSalary - band.minSalary
  const midSalary = band.minSalary + (salaryRange / 2)

  // Calculate total allowances and deductions
  const totalFixedAllowances = band.standardAllowances
    .filter(a => a.amount)
    .reduce((sum, a) => sum + (a.amount || 0), 0)

  const totalPercentageAllowances = band.standardAllowances
    .filter(a => a.percentage)
    .reduce((sum, a) => sum + (a.percentage || 0), 0)

  const totalFixedDeductions = band.standardDeductions
    .filter(d => d.amount)
    .reduce((sum, d) => sum + (d.amount || 0), 0)

  const totalPercentageDeductions = band.standardDeductions
    .filter(d => d.percentage)
    .reduce((sum, d) => sum + (d.percentage || 0), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="font-mono text-lg px-3 py-1">
              {band.tcmCode}
            </Badge>
            <h2 className="text-2xl font-bold">{band.name}</h2>
          </div>
          {band.description && (
            <p className="text-muted-foreground max-w-2xl">
              {band.description}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={band.isActive ? "default" : "secondary"}>
            {band.isActive ? "Active" : "Inactive"}
          </Badge>
          <Button onClick={onEdit} size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Salary Range
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-lg font-bold">
                <SimpleCurrencyDisplay amount={band.minSalary} currency={band.currency} />
                {" - "}
                <SimpleCurrencyDisplay amount={band.maxSalary} currency={band.currency} />
              </div>
              <div className="text-sm text-muted-foreground">
                Mid-point: <SimpleCurrencyDisplay amount={midSalary} currency={band.currency} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Step Progression
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-lg font-bold">
                {band.stepIncrement ? (
                  <SimpleCurrencyDisplay amount={band.stepIncrement} currency={band.currency} />
                ) : (
                  "Not defined"
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                Max steps: {band.maxSteps || "Not defined"}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Percent className="h-4 w-4 mr-2" />
              Annual Increment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-lg font-bold">
                {band.annualIncrementPercentage ? `${band.annualIncrementPercentage}%` : "Not defined"}
              </div>
              <div className="text-sm text-muted-foreground">
                Yearly increase
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Effective Period
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-sm font-medium">
                From: {effectiveDateFormatted}
              </div>
              <div className="text-sm text-muted-foreground">
                {expiryDateFormatted ? `Until: ${expiryDateFormatted}` : "No expiry date"}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="allowances">Allowances</TabsTrigger>
          <TabsTrigger value="deductions">Deductions</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Salary Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Salary Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Minimum Salary:</span>
                  <span className="font-bold">
                    <SimpleCurrencyDisplay amount={band.minSalary} currency={band.currency} />
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Maximum Salary:</span>
                  <span className="font-bold">
                    <SimpleCurrencyDisplay amount={band.maxSalary} currency={band.currency} />
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Salary Range:</span>
                  <span className="font-bold">
                    <SimpleCurrencyDisplay amount={salaryRange} currency={band.currency} />
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Currency:</span>
                  <Badge variant="outline">{band.currency}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Progression Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progression Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Step Increment:</span>
                  <span className="font-bold">
                    {band.stepIncrement ? (
                      <SimpleCurrencyDisplay amount={band.stepIncrement} currency={band.currency} />
                    ) : (
                      <span className="text-muted-foreground">Not defined</span>
                    )}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Maximum Steps:</span>
                  <span className="font-bold">
                    {band.maxSteps || <span className="text-muted-foreground">Not defined</span>}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Annual Increment:</span>
                  <span className="font-bold">
                    {band.annualIncrementPercentage ? (
                      `${band.annualIncrementPercentage}%`
                    ) : (
                      <span className="text-muted-foreground">Not defined</span>
                    )}
                  </span>
                </div>
                <Separator />
                {band.stepIncrement && band.maxSteps && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Progression:</span>
                    <span className="font-bold">
                      <SimpleCurrencyDisplay 
                        amount={band.stepIncrement * band.maxSteps} 
                        currency={band.currency} 
                      />
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Compensation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Standard Allowances</h4>
                  <div className="space-y-1 text-sm">
                    <div>Total Fixed: <SimpleCurrencyDisplay amount={totalFixedAllowances} currency={band.currency} /></div>
                    <div>Total Percentage: {totalPercentageAllowances}%</div>
                    <div>Count: {band.standardAllowances.length} allowances</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Standard Deductions</h4>
                  <div className="space-y-1 text-sm">
                    <div>Total Fixed: <SimpleCurrencyDisplay amount={totalFixedDeductions} currency={band.currency} /></div>
                    <div>Total Percentage: {totalPercentageDeductions}%</div>
                    <div>Count: {band.standardDeductions.length} deductions</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="allowances" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Standard Allowances</CardTitle>
              <CardDescription>
                Allowances automatically applied to employees in this salary band
              </CardDescription>
            </CardHeader>
            <CardContent>
              {band.standardAllowances.length > 0 ? (
                <div className="space-y-3">
                  {band.standardAllowances.map((allowance, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <div>
                          <div className="font-medium">{allowance.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {allowance.amount && (
                              <span>Fixed: <SimpleCurrencyDisplay amount={allowance.amount} currency={band.currency} /></span>
                            )}
                            {allowance.percentage && (
                              <span>{allowance.amount ? " + " : ""}Percentage: {allowance.percentage}%</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={allowance.isTaxable ? "default" : "secondary"}>
                          {allowance.isTaxable ? "Taxable" : "Non-taxable"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No allowances defined</h3>
                  <p className="text-muted-foreground">
                    No standard allowances have been configured for this salary band.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="deductions" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Standard Deductions</CardTitle>
              <CardDescription>
                Deductions automatically applied to employees in this salary band
              </CardDescription>
            </CardHeader>
            <CardContent>
              {band.standardDeductions.length > 0 ? (
                <div className="space-y-3">
                  {band.standardDeductions.map((deduction, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Percent className="h-4 w-4 text-red-600" />
                        <div>
                          <div className="font-medium">{deduction.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {deduction.amount && (
                              <span>Fixed: <SimpleCurrencyDisplay amount={deduction.amount} currency={band.currency} /></span>
                            )}
                            {deduction.percentage && (
                              <span>{deduction.amount ? " + " : ""}Percentage: {deduction.percentage}%</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Percent className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No deductions defined</h3>
                  <p className="text-muted-foreground">
                    No standard deductions have been configured for this salary band.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metadata" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Status Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={band.isActive ? "default" : "secondary"}>
                    {band.isActive ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Active
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3 mr-1" />
                        Inactive
                      </>
                    )}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Effective Date:</span>
                  <span className="text-sm">{effectiveDateFormatted}</span>
                </div>
                {expiryDateFormatted && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Expiry Date:</span>
                    <span className="text-sm">{expiryDateFormatted}</span>
                  </div>
                )}
                {band.approvedAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Approved:</span>
                    <span className="text-sm">
                      {new Date(band.approvedAt).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Audit Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {createdDateFormatted && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Created:</span>
                    <span className="text-sm">{createdDateFormatted}</span>
                  </div>
                )}
                {band.updatedAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Last Updated:</span>
                    <span className="text-sm">
                      {new Date(band.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">TCM Code:</span>
                  <Badge variant="outline" className="font-mono">
                    {band.tcmCode}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
        <Button onClick={onEdit}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Salary Band
        </Button>
      </div>
    </div>
  )
}

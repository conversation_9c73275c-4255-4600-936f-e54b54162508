"use client";

import React, { useRef, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  FileSpreadsheet,
  File,
  X,
  AlertCircle,
  CheckCircle2,
  Info
} from 'lucide-react';

import type { FileUploadProps } from '@/types/document-conversion';

export function FileUploadSection({
  documentType,
  onFileSelect,
  selectedFile,
  isUploading
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const handleFileSelect = (file: File) => {
    setUploadError(null);

    // Validate file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const supportedExtensions = documentType.supportedFormats.map(f => f.extension.toLowerCase());

    if (!fileExtension || !supportedExtensions.includes(fileExtension)) {
      setUploadError(
        `Unsupported file type. Please upload one of: ${supportedExtensions.map(ext => ext.toUpperCase()).join(', ')}`
      );
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setUploadError('File size exceeds 10MB limit. Please choose a smaller file.');
      return;
    }

    onFileSelect(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setUploadError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onFileSelect(null as any); // Reset file selection
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Upload Your File</h3>
        <p className="text-muted-foreground">
          Upload your {documentType.name.toLowerCase()} file for conversion
        </p>
      </div>

      {/* Document Type Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            {documentType.name}
          </CardTitle>
          <CardDescription>{documentType.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <span className="text-sm font-medium">Supported formats:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {documentType.supportedFormats.map((format) => (
                <Badge key={format.extension} variant="secondary" className="text-xs">
                  {format.extension.toUpperCase()} - {format.description}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <span className="text-sm font-medium">Required fields ({documentType.requiredFields.length}):</span>
            <div className="text-sm text-muted-foreground mt-1">
              {documentType.requiredFields.join(', ')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Upload Area */}
      {!selectedFile ? (
        <Card
          className={`border-2 border-dashed transition-colors cursor-pointer ${
            dragActive
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-primary/50'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Upload className={`h-12 w-12 mb-4 ${dragActive ? 'text-primary' : 'text-muted-foreground'}`} />
            <div className="text-center">
              <p className="text-lg font-medium mb-2">
                {dragActive ? 'Drop your file here' : 'Upload your file'}
              </p>
              <p className="text-sm text-muted-foreground mb-4">
                Drag and drop your file here, or click to browse
              </p>
              <Button variant="outline" disabled={isUploading}>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Choose File
              </Button>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept={documentType.supportedFormats.map(f => `.${f.extension}`).join(',')}
              onChange={handleInputChange}
              disabled={isUploading}
            />
          </CardContent>
        </Card>
      ) : (
        /* Selected File Display */
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <File className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(selectedFile.size)} • {selectedFile.type || 'Unknown type'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-600" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                  disabled={isUploading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Error */}
      {uploadError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{uploadError}</AlertDescription>
        </Alert>
      )}

      {/* Upload Guidelines */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Upload Guidelines:</strong>
          <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
            <li>Maximum file size: 10MB</li>
            <li>Ensure your file contains the required fields listed above</li>
            <li>The first row should contain column headers</li>
            <li>Date fields should be in a recognizable format (will be auto-detected)</li>
            <li>Remove any merged cells or complex formatting before upload</li>
          </ul>
        </AlertDescription>
      </Alert>

      {selectedFile && (
        <div className="mt-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-2 text-primary">
            <CheckCircle2 className="h-4 w-4" />
            <span className="font-medium">File ready for conversion</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            You can now configure conversion options or proceed directly to convert.
          </p>
        </div>
      )}
    </div>
  );
}

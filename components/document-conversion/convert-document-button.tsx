"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { ConvertDocumentModal } from './convert-document-modal';
import { toast } from '@/components/ui/use-toast';
import type { ConversionResult } from '@/types/document-conversion';

export function ConvertDocumentButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSuccess = (result: ConversionResult) => {
    toast({
      title: "Document Converted Successfully",
      description: `${result.convertedRows} rows processed. File ready for download.`,
      variant: "default"
    });
  };

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-9 w-9"
        onClick={() => setIsModalOpen(true)}
        title="Convert Documents"
      >
        <FileText className="h-4 w-4" />
        <span className="sr-only">Convert Documents</span>
      </Button>

      <ConvertDocumentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleSuccess}
      />
    </>
  );
}

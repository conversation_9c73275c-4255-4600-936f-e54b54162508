"use client";

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Download, RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

import { DocumentTypeSelect } from './document-type-select'; 
import { FileUploadSection } from './file-upload-section'; 
import { ConversionOptionsSection } from './conversion-options-section'; 
import { ConversionResultsSection } from './conversion-results-section';

import type {
  ConvertDocumentModalProps,
  DocumentType,
  ConversionState,
  ConversionOptions,
  ConversionApiResponse
} from '@/types/document-conversion';

const initialOptions: ConversionOptions = {
  skipValidation: false,
  includeHeaders: true,
  dateFormat: 'auto',
  encoding: 'utf-8',
  delimiter: ',',
  startRow: 1
};

export function ConvertDocumentModal({ isOpen, onClose, onSuccess }: ConvertDocumentModalProps) {
  const [state, setState] = useState<ConversionState>({
    status: 'idle',
    selectedDocumentType: null,
    selectedFile: null,
    options: initialOptions,
    result: null,
    error: null
  });

  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [isLoadingTypes, setIsLoadingTypes] = useState(false);
  const [activeTab, setActiveTab] = useState('select');

  // Load document types on mount
  useEffect(() => {
    if (isOpen) {
      loadDocumentTypes();
    }
  }, [isOpen]);

  const loadDocumentTypes = async () => {
    setIsLoadingTypes(true);
    try {
      const response = await fetch('/api/document-conversion/types');
      const data = await response.json();

      if (data.success) {
        setDocumentTypes(data.data);
      } else {
        throw new Error(data.error || 'Failed to load document types');
      }
    } catch (error) {
      console.error('Error loading document types:', error);
      toast({
        title: "Error",
        description: "Failed to load document types. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingTypes(false);
    }
  };

  const handleDocumentTypeSelect = (typeId: string) => {
    setState(prev => ({
      ...prev,
      selectedDocumentType: typeId,
      selectedFile: null,
      result: null,
      error: null
    }));
    setActiveTab('upload');
  };

  const handleFileSelect = (file: File) => {
    setState(prev => ({
      ...prev,
      selectedFile: file,
      result: null,
      error: null
    }));
    setActiveTab('options');
  };

  const handleOptionsChange = (options: ConversionOptions) => {
    setState(prev => ({
      ...prev,
      options
    }));
  };

  const handleConvert = async () => {
    if (!state.selectedDocumentType || !state.selectedFile) {
      toast({
        title: "Error",
        description: "Please select a document type and file.",
        variant: "destructive"
      });
      return;
    }

    setState(prev => ({ ...prev, status: 'processing', error: null }));

    try {
      const formData = new FormData();
      formData.append('file', state.selectedFile);
      formData.append('documentType', state.selectedDocumentType);
      formData.append('options', JSON.stringify(state.options));

      const response = await fetch('/api/document-conversion/convert', {
        method: 'POST',
        body: formData
      });

      const data: ConversionApiResponse = await response.json();

      if (data.success && data.data) {
        setState(prev => ({
          ...prev,
          status: 'completed',
          result: data.data!
        }));
        setActiveTab('results');

        toast({
          title: "Conversion Successful",
          description: `Document converted successfully. ${data.data.convertedRows} rows processed.`,
          variant: "default"
        });

        if (onSuccess) {
          onSuccess(data.data);
        }
      } else {
        throw new Error(data.error || 'Conversion failed');
      }
    } catch (error) {
      console.error('Conversion error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

      setState(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage
      }));

      toast({
        title: "Conversion Failed",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  const handleDownload = () => {
    if (state.result?.downloadUrl) {
      window.open(state.result.downloadUrl, '_blank');
    }
  };

  const handleReset = () => {
    setState({
      status: 'idle',
      selectedDocumentType: null,
      selectedFile: null,
      options: initialOptions,
      result: null,
      error: null
    });
    setActiveTab('select');
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const selectedDocumentType = documentTypes.find(type => type.id === state.selectedDocumentType);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Convert Documents
          </DialogTitle>
          <DialogDescription>
            Convert your existing Excel/CSV files to system-compatible formats for bulk import.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Indicator */}
          {state.status === 'processing' && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Converting document...</span>
                <span>Processing</span>
              </div>
              <Progress value={undefined} className="w-full" />
            </div>
          )}

          {/* Error Alert */}
          {state.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="select" disabled={state.status === 'processing'}>
                1. Select Type
              </TabsTrigger>
              <TabsTrigger
                value="upload"
                disabled={!state.selectedDocumentType || state.status === 'processing'}
              >
                2. Upload File
              </TabsTrigger>
              <TabsTrigger
                value="options"
                disabled={!state.selectedFile || state.status === 'processing'}
              >
                3. Options
              </TabsTrigger>
              <TabsTrigger
                value="results"
                disabled={!state.result}
              >
                4. Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="select" className="space-y-4">
              <DocumentTypeSelect
                documentTypes={documentTypes}
                selectedType={state.selectedDocumentType}
                onSelect={handleDocumentTypeSelect}
                isLoading={isLoadingTypes}
              />
            </TabsContent>

            <TabsContent value="upload" className="space-y-4">
              {selectedDocumentType && (
                <FileUploadSection
                  documentType={selectedDocumentType}
                  onFileSelect={handleFileSelect}
                  selectedFile={state.selectedFile}
                  isUploading={state.status === 'processing'}
                />
              )}
            </TabsContent>

            <TabsContent value="options" className="space-y-4">
              {selectedDocumentType && (
                <ConversionOptionsSection
                  documentType={selectedDocumentType}
                  options={state.options}
                  onOptionsChange={handleOptionsChange}
                />
              )}
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              {state.result && (
                <ConversionResultsSection
                  result={state.result}
                  onDownload={handleDownload}
                  onReset={handleReset}
                />
              )}
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>

            <div className="flex gap-2">
              {activeTab === 'options' && state.selectedFile && (
                <Button
                  onClick={handleConvert}
                  disabled={state.status === 'processing'}
                  className="min-w-[120px]"
                >
                  {state.status === 'processing' ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Converting...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Convert
                    </>
                  )}
                </Button>
              )}

              {state.result && (
                <Button onClick={handleDownload}>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

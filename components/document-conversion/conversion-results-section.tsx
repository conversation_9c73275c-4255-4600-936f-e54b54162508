"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Download,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  RefreshCw,
  FileText,
  Clock,
  Database,
  TrendingUp
} from 'lucide-react';

import type { ConversionResultsProps, ConversionError } from '@/types/document-conversion';

export function ConversionResultsSection({
  result,
  onDownload,
  onReset
}: ConversionResultsProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatProcessingTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const errorsByType = result.errors.reduce((acc, error) => {
    if (!acc[error.severity]) acc[error.severity] = [];
    acc[error.severity].push(error);
    return acc;
  }, {} as Record<string, ConversionError[]>);

  const successRate = result.totalRows > 0
    ? Math.round((result.convertedRows / result.totalRows) * 100)
    : 0;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Conversion Results</h3>
        <p className="text-muted-foreground">
          Your document has been processed and converted
        </p>
      </div>

      {/* Success Summary */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="text-center">
            <h4 className="text-lg font-semibold text-green-800 mb-2">
              Conversion Completed Successfully
            </h4>
            <p className="text-green-700">
              {result.convertedRows} of {result.totalRows} rows converted ({successRate}% success rate)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Conversion Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Rows</p>
                <p className="text-2xl font-bold">{result.totalRows}</p>
              </div>
              <Database className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Converted</p>
                <p className="text-2xl font-bold text-green-600">{result.convertedRows}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Errors</p>
                <p className="text-2xl font-bold text-red-600">{result.errors.length}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Processing Time</p>
                <p className="text-2xl font-bold">{formatProcessingTime(result.metadata.processingTime)}</p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* File Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <FileText className="h-4 w-4" />
            File Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium">Original File:</span>
              <p className="text-sm text-muted-foreground">{result.originalFileName}</p>
            </div>
            <div>
              <span className="text-sm font-medium">Converted File:</span>
              <p className="text-sm text-muted-foreground">{result.convertedFileName}</p>
            </div>
            <div>
              <span className="text-sm font-medium">File Size:</span>
              <p className="text-sm text-muted-foreground">{formatFileSize(result.metadata.fileSize)}</p>
            </div>
            <div>
              <span className="text-sm font-medium">Format:</span>
              <p className="text-sm text-muted-foreground">
                {result.metadata.originalFormat} → {result.metadata.targetFormat}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Errors and Warnings */}
      {(result.errors.length > 0 || result.warnings.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Issues Found
            </CardTitle>
            <CardDescription>
              Review the following issues that were encountered during conversion
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Errors */}
            {errorsByType.error && errorsByType.error.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <span className="font-medium text-red-600">
                    Errors ({errorsByType.error.length})
                  </span>
                </div>
                <ScrollArea className="h-32 border rounded-md p-3">
                  <div className="space-y-2">
                    {errorsByType.error.map((error, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">Row {error.row}:</span>
                        <span className="text-muted-foreground ml-1">{error.message}</span>
                        {error.suggestedFix && (
                          <div className="text-xs text-blue-600 ml-4">
                            Suggestion: {error.suggestedFix}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Warnings */}
            {errorsByType.warning && errorsByType.warning.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-600">
                    Warnings ({errorsByType.warning.length})
                  </span>
                </div>
                <ScrollArea className="h-32 border rounded-md p-3">
                  <div className="space-y-2">
                    {errorsByType.warning.map((warning, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">Row {warning.row}:</span>
                        <span className="text-muted-foreground ml-1">{warning.message}</span>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Data Transformations */}
            {result.warnings.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <RefreshCw className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-600">
                    Data Transformations ({result.warnings.length})
                  </span>
                </div>
                <ScrollArea className="h-32 border rounded-md p-3">
                  <div className="space-y-2">
                    {result.warnings.map((warning, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">Row {warning.row}, {warning.column}:</span>
                        <div className="text-muted-foreground ml-4">
                          <span className="line-through">{warning.originalValue}</span>
                          <span className="mx-2">→</span>
                          <span className="text-green-600">{warning.convertedValue}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Preview Data */}
      {result.previewData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Data Preview</CardTitle>
            <CardDescription>
              First few rows of the converted data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64 w-full">
              <div className="min-w-full">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      {Object.keys(result.previewData[0] || {}).map((key) => (
                        <th key={key} className="text-left p-2 font-medium">
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {result.previewData.slice(0, 5).map((row, index) => (
                      <tr key={index} className="border-b">
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="p-2 text-muted-foreground">
                            {String(value || '')}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-4 border-t">
        <Button variant="outline" onClick={onReset}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Convert Another File
        </Button>

        <Button onClick={onDownload} className="min-w-[140px]">
          <Download className="mr-2 h-4 w-4" />
          Download Converted File
        </Button>
      </div>

      {/* Success Message */}
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle2 className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <strong>Ready for Import:</strong> Your converted file is now ready to be uploaded to the system for bulk import.
          The file has been formatted according to system requirements and validated for compatibility.
        </AlertDescription>
      </Alert>
    </div>
  );
}

"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Users,
  Building2,
  Calculator,
  Wallet,
  ShoppingCart,
  Package,
  FileSpreadsheet,
  CheckCircle2
} from 'lucide-react';

import type { DocumentTypeSelectProps, DocumentType } from '@/types/document-conversion';

const categoryIcons = {
  hr: Users,
  finance: Calculator,
  accounting: Wallet,
  payroll: Wallet,
  procurement: ShoppingCart,
  inventory: Package
};

const categoryColors = {
  hr: 'bg-blue-100 text-blue-800 border-blue-200',
  finance: 'bg-green-100 text-green-800 border-green-200',
  accounting: 'bg-purple-100 text-purple-800 border-purple-200',
  payroll: 'bg-orange-100 text-orange-800 border-orange-200',
  procurement: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  inventory: 'bg-yellow-100 text-yellow-800 border-yellow-200'
};

export function DocumentTypeSelect({
  documentTypes,
  selectedType,
  onSelect,
  isLoading
}: DocumentTypeSelectProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Select Document Type</h3>
          <p className="text-muted-foreground">Choose the type of document you want to convert</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-6 w-6 rounded" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
                <Skeleton className="h-5 w-3/4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (documentTypes.length === 0) {
    return (
      <div className="text-center py-8">
        <FileSpreadsheet className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Document Types Available</h3>
        <p className="text-muted-foreground">
          No document conversion types are currently configured. Please contact your administrator.
        </p>
      </div>
    );
  }

  const groupedTypes = documentTypes.reduce((acc, type) => {
    if (!acc[type.category]) {
      acc[type.category] = [];
    }
    acc[type.category].push(type);
    return acc;
  }, {} as Record<string, DocumentType[]>);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Select Document Type</h3>
        <p className="text-muted-foreground">
          Choose the type of document you want to convert to system-compatible format
        </p>
      </div>

      {Object.entries(groupedTypes).map(([category, types]) => {
        const CategoryIcon = categoryIcons[category as keyof typeof categoryIcons] || FileSpreadsheet;

        return (
          <div key={category} className="space-y-3">
            <div className="flex items-center gap-2">
              <CategoryIcon className="h-5 w-5 text-muted-foreground" />
              <h4 className="font-medium capitalize">{category} Documents</h4>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {types.map((type) => {
                const isSelected = selectedType === type.id;
                const categoryColor = categoryColors[type.category] || 'bg-gray-100 text-gray-800 border-gray-200';

                return (
                  <Card
                    key={type.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      isSelected
                        ? 'ring-2 ring-primary border-primary shadow-md'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => onSelect(type.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {isSelected && (
                            <CheckCircle2 className="h-5 w-5 text-primary" />
                          )}
                          <CategoryIcon className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <Badge
                          variant="outline"
                          className={categoryColor}
                        >
                          {type.category.toUpperCase()}
                        </Badge>
                      </div>
                      <CardTitle className="text-base">{type.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm mb-3">
                        {type.description}
                      </CardDescription>

                      <div className="space-y-2">
                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">Supported formats:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {type.supportedFormats.map((format) => (
                              <Badge
                                key={format.extension}
                                variant="secondary"
                                className="text-xs px-1.5 py-0.5"
                              >
                                {format.extension.toUpperCase()}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">Required fields:</span>
                          <span className="ml-1">{type.requiredFields.length}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        );
      })}

      {selectedType && (
        <div className="mt-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-2 text-primary">
            <CheckCircle2 className="h-4 w-4" />
            <span className="font-medium">Document type selected</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            You can now proceed to upload your file for conversion.
          </p>
        </div>
      )}
    </div>
  );
}

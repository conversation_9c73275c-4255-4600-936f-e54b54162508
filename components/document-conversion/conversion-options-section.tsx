"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Settings, Calendar, FileText, Database } from 'lucide-react';

import type { ConversionOptionsProps, ConversionOptions } from '@/types/document-conversion';

export function ConversionOptionsSection({
  documentType,
  options,
  onOptionsChange
}: ConversionOptionsProps) {
  const updateOption = <K extends keyof ConversionOptions>(
    key: K,
    value: ConversionOptions[K]
  ) => {
    onOptionsChange({
      ...options,
      [key]: value
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Conversion Options</h3>
        <p className="text-muted-foreground">
          Configure how your {documentType.name.toLowerCase()} will be processed
        </p>
      </div>

      {/* General Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="h-4 w-4" />
            General Options
          </CardTitle>
          <CardDescription>
            Basic conversion settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="include-headers">Include Headers</Label>
              <p className="text-sm text-muted-foreground">
                First row contains column headers
              </p>
            </div>
            <Switch
              id="include-headers"
              checked={options.includeHeaders}
              onCheckedChange={(checked) => updateOption('includeHeaders', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="skip-validation">Skip Validation</Label>
              <p className="text-sm text-muted-foreground">
                Skip data validation during conversion
              </p>
            </div>
            <Switch
              id="skip-validation"
              checked={options.skipValidation}
              onCheckedChange={(checked) => updateOption('skipValidation', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Date & Format Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date & Format Options
          </CardTitle>
          <CardDescription>
            Configure date formats and encoding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date-format">Date Format</Label>
              <Select
                value={options.dateFormat}
                onValueChange={(value) =>
                  updateOption('dateFormat', value as ConversionOptions['dateFormat'])
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select date format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">
                    Auto-detect
                    <Badge variant="secondary" className="ml-2 text-xs">Recommended</Badge>
                  </SelectItem>
                  <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                  <SelectItem value="dd/mm/yyyy">DD/MM/YYYY</SelectItem>
                  <SelectItem value="mm/dd/yyyy">MM/DD/YYYY</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="encoding">File Encoding</Label>
              <Select
                value={options.encoding}
                onValueChange={(value) =>
                  updateOption('encoding', value as ConversionOptions['encoding'])
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select encoding" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="utf-8">
                    UTF-8
                    <Badge variant="secondary" className="ml-2 text-xs">Recommended</Badge>
                  </SelectItem>
                  <SelectItem value="latin1">Latin1</SelectItem>
                  <SelectItem value="ascii">ASCII</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CSV-specific Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <FileText className="h-4 w-4" />
            CSV Options
          </CardTitle>
          <CardDescription>
            Options for CSV file processing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="delimiter">Field Delimiter</Label>
            <Select
              value={options.delimiter}
              onValueChange={(value) =>
                updateOption('delimiter', value as ConversionOptions['delimiter'])
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select delimiter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=",">
                  Comma (,)
                  <Badge variant="secondary" className="ml-2 text-xs">Most common</Badge>
                </SelectItem>
                <SelectItem value=";">Semicolon (;)</SelectItem>
                <SelectItem value="\t">Tab</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Excel-specific Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Database className="h-4 w-4" />
            Excel Options
          </CardTitle>
          <CardDescription>
            Options for Excel file processing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sheet-name">Sheet Name</Label>
              <Input
                id="sheet-name"
                placeholder="Leave empty for first sheet"
                value={options.sheetName || ''}
                onChange={(e) => updateOption('sheetName', e.target.value || undefined)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="start-row">Start Row</Label>
              <Input
                id="start-row"
                type="number"
                min="1"
                placeholder="1"
                value={options.startRow || 1}
                onChange={(e) => updateOption('startRow', parseInt(e.target.value) || 1)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end-row">End Row</Label>
              <Input
                id="end-row"
                type="number"
                min="1"
                placeholder="Leave empty for all rows"
                value={options.endRow || ''}
                onChange={(e) => updateOption('endRow', parseInt(e.target.value) || undefined)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document-specific Information */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-base">Document Requirements</CardTitle>
          <CardDescription>
            Specific requirements for {documentType.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <span className="text-sm font-medium">Required Fields:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {documentType.requiredFields.map((field) => (
                <Badge key={field} variant="destructive" className="text-xs">
                  {field}
                </Badge>
              ))}
            </div>
          </div>

          {documentType.optionalFields.length > 0 && (
            <div>
              <span className="text-sm font-medium">Optional Fields:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {documentType.optionalFields.slice(0, 10).map((field) => (
                  <Badge key={field} variant="secondary" className="text-xs">
                    {field}
                  </Badge>
                ))}
                {documentType.optionalFields.length > 10 && (
                  <Badge variant="outline" className="text-xs">
                    +{documentType.optionalFields.length - 10} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          <div className="text-xs text-muted-foreground pt-2 border-t">
            <p>
              <strong>Note:</strong> The conversion process will automatically map your columns to the required format.
              Missing required fields will be flagged as errors.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

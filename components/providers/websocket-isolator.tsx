'use client';

import React, { useState, useEffect, useRef } from 'react';
import { WebSocketProvider } from './websocket-provider';
import { ErrorBoundary } from '@/components/error-boundary';

interface WebSocketIsolatorProps {
  children: React.ReactNode;
  enabled?: boolean;
}

/**
 * WebSocket Isolator Component
 * 
 * This component isolates WebSocket functionality to prevent it from causing
 * infinite loops or crashes in the main application. It includes:
 * - Error boundary protection
 * - Connection state management
 * - Automatic retry with exponential backoff
 * - Circuit breaker pattern
 */
export function WebSocketIsolator({ 
  children, 
  enabled = true 
}: WebSocketIsolatorProps) {
  const [wsEnabled, setWsEnabled] = useState(enabled);
  const [errorCount, setErrorCount] = useState(0);
  const [lastError, setLastError] = useState<Error | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const MAX_ERRORS = 3;
  const RETRY_DELAY = 5000; // 5 seconds
  
  // Handle WebSocket errors
  const handleWebSocketError = (error: Error) => {
    console.warn('WebSocket Isolator: Error detected:', error);
    setLastError(error);
    setErrorCount(prev => prev + 1);
    
    // Disable WebSocket if too many errors
    if (errorCount >= MAX_ERRORS) {
      console.warn('WebSocket Isolator: Too many errors, disabling WebSocket');
      setWsEnabled(false);
      
      // Schedule retry after delay
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      retryTimeoutRef.current = setTimeout(() => {
        console.log('WebSocket Isolator: Retrying WebSocket connection');
        setErrorCount(0);
        setLastError(null);
        setWsEnabled(true);
      }, RETRY_DELAY);
    }
  };
  
  // Reset error count when WebSocket is manually enabled
  useEffect(() => {
    if (enabled && !wsEnabled) {
      setErrorCount(0);
      setLastError(null);
      setWsEnabled(true);
    }
  }, [enabled, wsEnabled]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);
  
  // If WebSocket is disabled, render children without WebSocket provider
  if (!wsEnabled || !enabled) {
    return (
      <div>
        {children}
        {lastError && (
          <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded max-w-sm">
            <div className="flex">
              <div className="py-1">
                <svg className="fill-current h-6 w-6 text-yellow-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                </svg>
              </div>
              <div>
                <p className="font-bold">Real-time updates disabled</p>
                <p className="text-sm">Connection issues detected. Retrying automatically...</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
  
  return (
    <ErrorBoundary
      onError={(error) => {
        handleWebSocketError(error);
      }}
      fallback={
        <div>
          {children}
          <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-sm">
            <div className="flex">
              <div className="py-1">
                <svg className="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                </svg>
              </div>
              <div>
                <p className="font-bold">Real-time updates unavailable</p>
                <p className="text-sm">WebSocket connection failed. Page will work without live updates.</p>
                <button 
                  onClick={() => {
                    setErrorCount(0);
                    setLastError(null);
                    setWsEnabled(true);
                  }}
                  className="mt-2 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
                >
                  Retry Connection
                </button>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <WebSocketProvider autoConnect={true}>
        {children}
      </WebSocketProvider>
    </ErrorBoundary>
  );
}

/**
 * Hook to check if WebSocket is available
 */
export function useWebSocketAvailable() {
  const [available, setAvailable] = useState(true);
  
  useEffect(() => {
    // Check if WebSocket is supported
    if (typeof window !== 'undefined' && !window.WebSocket) {
      setAvailable(false);
    }
  }, []);
  
  return available;
}

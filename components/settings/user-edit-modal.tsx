"use client"

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { UserRole, UserStatus } from "@/types/user-roles";
import { useErrorHandler } from "@/lib/frontend/hooks/useErrorHandler";
import { ErrorOverlay } from "@/components/errors/error-overlay";

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  department?: string;
  position?: string;
  phoneNumber?: string;
  address?: string;
  avatar?: string;
  dateOfBirth?: string;
  dateOfJoining?: string;
  emergencyContact?: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };
  statusReason?: string;
  allowMultipleDevices: boolean;
  trustedDevicesOnly: boolean;
  singleDeviceLogin: boolean;
}

interface UserEditModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedUser: User) => void;
}

export function UserEditModal({ user, isOpen, onClose, onSuccess }: UserEditModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user.firstName || '',
    lastName: user.lastName || '',
    email: user.email || '',
    role: user.role || UserRole.EMPLOYEE,
    status: user.status || UserStatus.ACTIVE,
    department: user.department || '',
    position: user.position || '',
    phoneNumber: user.phoneNumber || '',
    address: user.address || '',
    dateOfBirth: user.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
    dateOfJoining: user.dateOfJoining ? user.dateOfJoining.split('T')[0] : '',
    emergencyContactName: user.emergencyContact?.name || '',
    emergencyContactRelationship: user.emergencyContact?.relationship || '',
    emergencyContactPhone: user.emergencyContact?.phoneNumber || '',
    statusReason: user.statusReason || '',
    allowMultipleDevices: user.allowMultipleDevices || false,
    trustedDevicesOnly: user.trustedDevicesOnly || false,
    singleDeviceLogin: user.singleDeviceLogin || true
  });

  const { error, isErrorOpen, handleApiError, handleGenericError, handleErrorAction, hideError } = useErrorHandler();

  // Handle form field changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare update data
      const updateData: any = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        status: formData.status,
        department: formData.department || undefined,
        position: formData.position || undefined,
        phoneNumber: formData.phoneNumber || undefined,
        address: formData.address || undefined,
        dateOfBirth: formData.dateOfBirth || undefined,
        dateOfJoining: formData.dateOfJoining || undefined,
        statusReason: formData.statusReason || undefined,
        allowMultipleDevices: formData.allowMultipleDevices,
        trustedDevicesOnly: formData.trustedDevicesOnly,
        singleDeviceLogin: formData.singleDeviceLogin,
        currentStatus: user.status // For audit trail
      };

      // Add emergency contact if provided
      if (formData.emergencyContactName || formData.emergencyContactRelationship || formData.emergencyContactPhone) {
        updateData.emergencyContact = {
          name: formData.emergencyContactName,
          relationship: formData.emergencyContactRelationship,
          phoneNumber: formData.emergencyContactPhone
        };
      }

      const response = await fetch(`/api/users/${user._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const data = await response.json();

      if (data.status === 'success') {
        setIsSuccess(true);
        setTimeout(() => {
          onSuccess(data.data.user);
          handleClose();
        }, 1500);
      } else {
        handleGenericError(data.error || 'Failed to update user');
      }
    } catch (error: unknown) {
      console.error('Error updating user:', error);
      handleGenericError(error instanceof Error ? error : 'An error occurred while updating user');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isLoading) {
      setIsSuccess(false);
      hideError();
      onClose();
    }
  };

  // Custom error action handler
  const handleCustomErrorAction = (action: string, data?: Record<string, any>) => {
    if (action === 'retry') {
      hideError();
      handleSubmit(new Event('submit') as any);
    } else {
      handleErrorAction(action, data);
    }
  };

  // Format role for display
  const formatRole = (role: UserRole): string => {
    return role.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and settings. Changes will be saved immediately.
            </DialogDescription>
          </DialogHeader>

          {isSuccess ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="mb-4 rounded-full bg-green-100 p-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium">User Updated Successfully</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                The user information has been updated successfully.
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="work">Work Details</TabsTrigger>
                  <TabsTrigger value="contact">Contact</TabsTrigger>
                  <TabsTrigger value="security">Security</TabsTrigger>
                </TabsList>

                {/* Basic Information Tab */}
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dateOfBirth">Date of Birth</Label>
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={formData.dateOfBirth}
                        onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="dateOfJoining">Date of Joining</Label>
                      <Input
                        id="dateOfJoining"
                        type="date"
                        value={formData.dateOfJoining}
                        onChange={(e) => handleInputChange('dateOfJoining', e.target.value)}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Work Details Tab */}
                <TabsContent value="work" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="role">Role *</Label>
                      <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(UserRole).map((role) => (
                            <SelectItem key={role} value={role}>
                              {formatRole(role)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="status">Status *</Label>
                      <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(UserStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="department">Department</Label>
                      <Input
                        id="department"
                        value={formData.department}
                        onChange={(e) => handleInputChange('department', e.target.value)}
                        placeholder="e.g., Human Resources"
                      />
                    </div>
                    <div>
                      <Label htmlFor="position">Position</Label>
                      <Input
                        id="position"
                        value={formData.position}
                        onChange={(e) => handleInputChange('position', e.target.value)}
                        placeholder="e.g., Senior Manager"
                      />
                    </div>
                  </div>

                  {formData.status !== UserStatus.ACTIVE && (
                    <div>
                      <Label htmlFor="statusReason">Status Reason</Label>
                      <Textarea
                        id="statusReason"
                        value={formData.statusReason}
                        onChange={(e) => handleInputChange('statusReason', e.target.value)}
                        placeholder="Reason for status change..."
                        rows={3}
                      />
                    </div>
                  )}
                </TabsContent>

                {/* Contact Information Tab */}
                <TabsContent value="contact" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phoneNumber">Phone Number</Label>
                      <Input
                        id="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Street address, city, state, zip code"
                      rows={3}
                    />
                  </div>

                  <Separator />

                  <div>
                    <h4 className="text-sm font-medium mb-3">Emergency Contact</h4>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="emergencyContactName">Name</Label>
                          <Input
                            id="emergencyContactName"
                            value={formData.emergencyContactName}
                            onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                            placeholder="Emergency contact name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="emergencyContactRelationship">Relationship</Label>
                          <Input
                            id="emergencyContactRelationship"
                            value={formData.emergencyContactRelationship}
                            onChange={(e) => handleInputChange('emergencyContactRelationship', e.target.value)}
                            placeholder="e.g., Spouse, Parent"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="emergencyContactPhone">Phone Number</Label>
                        <Input
                          id="emergencyContactPhone"
                          value={formData.emergencyContactPhone}
                          onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Security Settings Tab */}
                <TabsContent value="security" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Allow Multiple Devices</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow user to be logged in on multiple devices simultaneously
                        </p>
                      </div>
                      <Switch
                        checked={formData.allowMultipleDevices}
                        onCheckedChange={(checked) => handleInputChange('allowMultipleDevices', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Single Device Login</Label>
                        <p className="text-sm text-muted-foreground">
                          Force logout from other devices when logging in
                        </p>
                      </div>
                      <Switch
                        checked={formData.singleDeviceLogin}
                        onCheckedChange={(checked) => handleInputChange('singleDeviceLogin', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Trusted Devices Only</Label>
                        <p className="text-sm text-muted-foreground">
                          Only allow login from previously trusted devices
                        </p>
                      </div>
                      <Switch
                        checked={formData.trustedDevicesOnly}
                        onCheckedChange={(checked) => handleInputChange('trustedDevicesOnly', checked)}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update User
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          onAction={handleCustomErrorAction}
        />
      )}
    </>
  );
}

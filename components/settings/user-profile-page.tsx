"use client"

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  ArrowLeft, 
  Edit, 
  Key, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Building, 
  User, 
  Shield,
  Clock,
  AlertCircle
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import { UserRole, UserStatus } from "@/types/user-roles";
import { useErrorHandler } from "@/lib/frontend/hooks/useErrorHandler";
import { ErrorOverlay } from "@/components/errors/error-overlay";
import { UserEditModal } from "@/components/settings/user-edit-modal";
import { PasswordResetModal } from "@/components/settings/password-reset-modal";
import { useToast } from "@/components/ui/use-toast";

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  department?: string;
  position?: string;
  phoneNumber?: string;
  address?: string;
  avatar?: string;
  dateOfBirth?: string;
  dateOfJoining?: string;
  emergencyContact?: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  statusReason?: string;
  statusChangedAt?: string;
  statusChangedBy?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  allowMultipleDevices: boolean;
  trustedDevicesOnly: boolean;
  singleDeviceLogin: boolean;
}

interface UserProfilePageProps {
  userId: string;
}

export function UserProfilePage({ userId }: UserProfilePageProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPasswordResetModalOpen, setIsPasswordResetModalOpen] = useState(false);
  
  const router = useRouter();
  const { toast } = useToast();
  const { error, isErrorOpen, handleApiError, handleGenericError, handleErrorAction, hideError } = useErrorHandler();

  // Custom error action handler
  const handleCustomErrorAction = (action: string, data?: Record<string, any>) => {
    if (action === 'retry') {
      hideError();
      fetchUser();
    } else if (action === 'back') {
      hideError();
      router.push('/dashboard/settings/users');
    } else {
      handleErrorAction(action, data);
    }
  };

  // Fetch user details
  const fetchUser = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/users/${userId}`);
      
      if (!response.ok) {
        await handleApiError(response);
        return;
      }
      
      const data = await response.json();
      
      if (data.status === 'success') {
        setUser(data.data.user);
      } else {
        handleGenericError(data.error || 'Failed to fetch user details');
      }
    } catch (error: unknown) {
      console.error('Error fetching user:', error);
      handleGenericError(error instanceof Error ? error : 'An error occurred while fetching user details');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle user update success
  const handleUserUpdated = (updatedUser: User) => {
    setUser(updatedUser);
    setIsEditModalOpen(false);
    toast({
      title: "Success",
      description: "User information updated successfully",
    });
  };

  // Handle password reset success
  const handlePasswordReset = () => {
    setIsPasswordResetModalOpen(false);
    toast({
      title: "Success",
      description: "Password reset successfully",
    });
  };

  // Initial fetch
  useEffect(() => {
    fetchUser();
  }, [userId]);

  // Format role for display
  const formatRole = (role: UserRole): string => {
    return role.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  // Get status badge
  const getStatusBadge = (status: UserStatus) => {
    const statusConfig = {
      [UserStatus.ACTIVE]: { variant: "default" as const, className: "bg-green-500", label: "Active" },
      [UserStatus.INACTIVE]: { variant: "secondary" as const, className: "", label: "Inactive" },
      [UserStatus.SUSPENDED]: { variant: "destructive" as const, className: "", label: "Suspended" },
      [UserStatus.PENDING]: { variant: "outline" as const, className: "", label: "Pending" }
    };

    const config = statusConfig[status] || statusConfig[UserStatus.INACTIVE];
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        
        {/* Profile card skeleton */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Skeleton className="h-20 w-20 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-48" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">User not found</h3>
          <p className="text-muted-foreground mb-4">The requested user could not be found.</p>
          <Button onClick={() => router.push('/dashboard/settings/users')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => router.push('/dashboard/settings/users')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{user.firstName} {user.lastName}</h1>
            <p className="text-muted-foreground">{formatRole(user.role)}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsPasswordResetModalOpen(true)}
          >
            <Key className="mr-2 h-4 w-4" />
            Reset Password
          </Button>
          <Button 
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit User
          </Button>
        </div>
      </div>

      {/* Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={user.avatar || "/placeholder-user.jpg"} alt={`${user.firstName} ${user.lastName}`} />
              <AvatarFallback className="text-lg">
                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <div>
                <h2 className="text-xl font-semibold">{user.firstName} {user.lastName}</h2>
                <p className="text-muted-foreground">{user.email}</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(user.status)}
                <Badge variant="outline">{formatRole(user.role)}</Badge>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{user.email}</span>
              </div>
              {user.phoneNumber && (
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{user.phoneNumber}</span>
                </div>
              )}
              {user.address && (
                <div className="flex items-center gap-3 md:col-span-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{user.address}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Work Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Work Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>{formatRole(user.role)}</span>
              </div>
              {user.department && (
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span>{user.department}</span>
                </div>
              )}
              {user.position && (
                <div className="flex items-center gap-3 md:col-span-2">
                  <span className="text-sm font-medium">Position:</span>
                  <span>{user.position}</span>
                </div>
              )}
              {user.dateOfJoining && (
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Joined {format(new Date(user.dateOfJoining), 'MMM dd, yyyy')}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Account Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Account Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span>Status: {getStatusBadge(user.status)}</span>
              </div>
              {user.lastLogin && (
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Last login {formatDistanceToNow(new Date(user.lastLogin), { addSuffix: true })}</span>
                </div>
              )}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Created:</span>
                <span>{format(new Date(user.createdAt), 'MMM dd, yyyy')}</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Updated:</span>
                <span>{format(new Date(user.updatedAt), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </div>

          {/* Emergency Contact */}
          {user.emergencyContact && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3">Emergency Contact</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium">Name:</span>
                    <span>{user.emergencyContact.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium">Relationship:</span>
                    <span>{user.emergencyContact.relationship}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{user.emergencyContact.phoneNumber}</span>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Security Settings */}
          <Separator />
          <div>
            <h3 className="text-lg font-medium mb-3">Security Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Multiple Devices:</span>
                <Badge variant={user.allowMultipleDevices ? "default" : "secondary"}>
                  {user.allowMultipleDevices ? "Allowed" : "Not Allowed"}
                </Badge>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Single Device Login:</span>
                <Badge variant={user.singleDeviceLogin ? "default" : "secondary"}>
                  {user.singleDeviceLogin ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Trusted Devices Only:</span>
                <Badge variant={user.trustedDevicesOnly ? "default" : "secondary"}>
                  {user.trustedDevicesOnly ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </div>
          </div>

          {/* Status Information */}
          {user.statusReason && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3">Status Information</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium">Reason:</span>
                    <span>{user.statusReason}</span>
                  </div>
                  {user.statusChangedAt && (
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium">Changed:</span>
                      <span>{format(new Date(user.statusChangedAt), 'MMM dd, yyyy HH:mm')}</span>
                    </div>
                  )}
                  {user.statusChangedBy && (
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium">Changed by:</span>
                      <span>{user.statusChangedBy.firstName} {user.statusChangedBy.lastName}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      {user && (
        <>
          <UserEditModal
            user={user}
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSuccess={handleUserUpdated}
          />
          
          <PasswordResetModal
            user={user}
            isOpen={isPasswordResetModalOpen}
            onClose={() => setIsPasswordResetModalOpen(false)}
            onSuccess={handlePasswordReset}
          />
        </>
      )}

      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          onAction={handleCustomErrorAction}
        />
      )}
    </div>
  );
}

"use client"

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Loader2, Eye, EyeOff, Key, RefreshCw, Copy } from "lucide-react";
import { useErrorHandler } from "@/lib/frontend/hooks/useErrorHandler";
import { ErrorOverlay } from "@/components/errors/error-overlay";

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
}

interface PasswordResetModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface ResetResult {
  isTemporary: boolean;
  resetType: string;
  temporaryPassword?: string;
  resetBy: {
    id: string;
    name: string;
    email: string;
  };
  resetAt: string;
}

export function PasswordResetModal({ user, isOpen, onClose, onSuccess }: PasswordResetModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [resetResult, setResetResult] = useState<ResetResult | null>(null);
  const [resetType, setResetType] = useState<'temporary' | 'custom'>('temporary');
  const [customPassword, setCustomPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [copied, setCopied] = useState(false);

  const { error, isErrorOpen, handleApiError, handleGenericError, handleErrorAction, hideError } = useErrorHandler();

  // Validate custom password
  const validatePassword = (): boolean => {
    if (resetType === 'temporary') return true;

    if (!customPassword) {
      setPasswordError('Password is required');
      return false;
    }

    if (customPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }

    if (customPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return false;
    }

    setPasswordError('');
    return true;
  };

  // Handle password reset
  const handlePasswordReset = async () => {
    if (!validatePassword()) return;

    setIsLoading(true);

    try {
      const requestData = {
        resetType,
        ...(resetType === 'custom' && { newPassword: customPassword }),
        ...(resetType === 'temporary' && { generateTemporary: true })
      };

      const response = await fetch(`/api/users/${user._id}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        await handleApiError(response);
        return;
      }

      const data = await response.json();

      if (data.status === 'success') {
        setResetResult(data.data);
        setIsSuccess(true);
      } else {
        handleGenericError(data.error || 'Failed to reset password');
      }
    } catch (error: unknown) {
      console.error('Error resetting password:', error);
      handleGenericError(error instanceof Error ? error : 'An error occurred while resetting password');
    } finally {
      setIsLoading(false);
    }
  };

  // Copy temporary password to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isLoading) {
      setIsSuccess(false);
      setResetResult(null);
      setCustomPassword('');
      setConfirmPassword('');
      setPasswordError('');
      setCopied(false);
      hideError();
      onClose();
    }
  };

  // Handle success completion
  const handleSuccessComplete = () => {
    onSuccess();
    handleClose();
  };

  // Custom error action handler
  const handleCustomErrorAction = (action: string, data?: Record<string, any>) => {
    if (action === 'retry') {
      hideError();
      handlePasswordReset();
    } else {
      handleErrorAction(action, data);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Reset Password
            </DialogTitle>
            <DialogDescription>
              Reset password for {user.firstName} {user.lastName} ({user.email})
            </DialogDescription>
          </DialogHeader>

          {isSuccess && resetResult ? (
            <div className="space-y-4">
              <div className="flex flex-col items-center justify-center py-4 text-center">
                <div className="mb-4 rounded-full bg-green-100 p-3">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-medium">Password Reset Successfully</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  The user's password has been reset successfully.
                </p>
              </div>

              {resetResult.isTemporary && resetResult.temporaryPassword && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Temporary Password</CardTitle>
                    <CardDescription>
                      Share this password securely with the user. They should change it on first login.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Input
                        value={resetResult.temporaryPassword}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(resetResult.temporaryPassword!)}
                      >
                        {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    {copied && (
                      <p className="text-xs text-green-600 mt-1">Copied to clipboard!</p>
                    )}
                  </CardContent>
                </Card>
              )}

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {resetResult.isTemporary 
                    ? "The user will be required to change this password on their next login."
                    : "The user can now log in with their new password."
                  }
                </AlertDescription>
              </Alert>

              <div className="flex justify-end">
                <Button onClick={handleSuccessComplete}>
                  Done
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Tabs value={resetType} onValueChange={(value) => setResetType(value as 'temporary' | 'custom')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="temporary">Generate Temporary</TabsTrigger>
                  <TabsTrigger value="custom">Set Custom</TabsTrigger>
                </TabsList>

                <TabsContent value="temporary" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center gap-2">
                        <RefreshCw className="h-4 w-4" />
                        Generate Temporary Password
                      </CardTitle>
                      <CardDescription>
                        A secure temporary password will be generated automatically. The user will be required to change it on their next login.
                      </CardDescription>
                    </CardHeader>
                  </Card>
                </TabsContent>

                <TabsContent value="custom" className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="customPassword">New Password</Label>
                      <div className="relative">
                        <Input
                          id="customPassword"
                          type={showPassword ? "text" : "password"}
                          value={customPassword}
                          onChange={(e) => {
                            setCustomPassword(e.target.value);
                            setPasswordError('');
                          }}
                          placeholder="Enter new password"
                          className="pr-10"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            setPasswordError('');
                          }}
                          placeholder="Confirm new password"
                          className="pr-10"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>

                    {passwordError && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{passwordError}</AlertDescription>
                      </Alert>
                    )}

                    <div className="text-xs text-muted-foreground">
                      Password must be at least 8 characters long.
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This action will immediately reset the user's password. They will need to use the new password to log in.
                </AlertDescription>
              </Alert>

              <div className="flex justify-end gap-3">
                <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                  Cancel
                </Button>
                <Button onClick={handlePasswordReset} disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Reset Password
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Error Overlay */}
      {error && (
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          onAction={handleCustomErrorAction}
        />
      )}
    </>
  );
}

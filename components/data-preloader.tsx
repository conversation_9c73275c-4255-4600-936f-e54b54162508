// components/data-preloader.tsx
"use client"

import { useEffect } from 'react'
import { localStorageService } from '@/lib/services/local-storage-service'

interface DataPreloaderProps {
  children: React.ReactNode
}

export function DataPreloader({ children }: DataPreloaderProps) {
  useEffect(() => {
    // Preload form data in background when app starts
    const preloadData = async () => {
      try {
        console.log('🚀 Starting background data preload...')
        await localStorageService.preloadFormData()
        console.log('✅ Background data preload completed')
      } catch (error) {
        console.warn('⚠️ Background data preload failed:', error)
      }
    }

    // Start preloading after a short delay to not block initial render
    const timer = setTimeout(preloadData, 1000)

    return () => clearTimeout(timer)
  }, [])

  return <>{children}</>
}

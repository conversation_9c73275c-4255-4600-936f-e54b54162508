"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Define the form schema
const formSchema = z.object({
  departmentIds: z.string().min(1, {
    message: 'Please enter at least one department ID',
  }),
  confirmDelete: z.boolean().refine(val => val === true, {
    message: 'You must confirm the deletion',
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface DeleteRequestBody {
  ids: string[];
}

export function SimpleBulkDeleteForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [deleteResult, setDeleteResult] = useState<{
    deletedCount: number;
    errors: Array<{ id: string; error: string }>;
  } | null>(null);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      departmentIds: '',
      confirmDelete: false,
    },
  });

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);

      // Split the comma-separated IDs
      const ids = values.departmentIds.split(',').map(id => id.trim()).filter(Boolean);

      if (ids.length === 0) {
        toast({
          title: "Error",
          description: "Please enter at least one department ID",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Send delete request
      const requestBody: DeleteRequestBody = { ids };
      const response = await fetch('/api/hr/departments/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete departments');
      }

      const result = await response.json();
      setDeleteResult(result);

      // Show success message
      toast({
        title: "Success",
        description: `Successfully deleted ${result.deletedCount} department(s)`,
        variant: "default",
      });

      // Reset form
      form.reset({
        departmentIds: '',
        confirmDelete: false,
      });
    } catch (error: unknown) {
      console.error('Error deleting departments:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete departments';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="departmentIds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Department IDs</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter department IDs separated by commas"
                    {...field}
                    disabled={isLoading}
                    className="min-h-[100px]"
                  />
                </FormControl>
                <FormDescription>
                  Enter the IDs of departments to delete, separated by commas.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmDelete"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-destructive/10">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Confirm Deletion</FormLabel>
                  <FormDescription>
                    I understand that this action will permanently delete department records and cannot be undone.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? 'Deleting...' : 'Delete Departments'}
          </Button>
        </form>
      </Form>

      {/* Results Display */}
      {deleteResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Deletion Results
            </CardTitle>
            <CardDescription>
              Summary of the bulk delete operation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Badge variant="default" className="text-sm">
                {deleteResult.deletedCount} Deleted
              </Badge>
              {deleteResult.errors.length > 0 && (
                <Badge variant="destructive" className="text-sm">
                  {deleteResult.errors.length} Errors
                </Badge>
              )}
            </div>

            {deleteResult.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-destructive">Errors:</h4>
                {deleteResult.errors.map((error, index) => (
                  <Alert key={index} variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>ID {error.id}:</strong> {error.error}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

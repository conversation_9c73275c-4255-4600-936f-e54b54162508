'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  User,
  FileText,
  Send,
  ArrowRight,
  Zap,
  Settings,
  History,
  Bell
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  approverRole: string;
  approverName?: string;
  approverId?: string;
  status: 'pending' | 'approved' | 'rejected' | 'skipped' | 'auto_approved';
  approvedAt?: Date;
  rejectedAt?: Date;
  comments?: string;
  autoApprovalRule?: string;
  timeLimit?: number; // hours
  escalationRule?: string;
}

interface WorkflowItem {
  id: string;
  type: 'income' | 'expense' | 'budget_adjustment' | 'transfer';
  title: string;
  description: string;
  amount: number;
  submittedBy: string;
  submittedAt: Date;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'in_progress' | 'approved' | 'rejected' | 'escalated';
  currentStep: number;
  totalSteps: number;
  steps: WorkflowStep[];
  budgetImpact?: {
    budgetId: string;
    budgetName: string;
    categoryId: string;
    categoryName: string;
    utilizationBefore: number;
    utilizationAfter: number;
    exceedsLimit: boolean;
  };
  attachments?: string[];
  urgencyScore: number;
  riskScore: number;
  autoApprovalEligible: boolean;
  estimatedCompletionTime: Date;
}

interface AutomatedApprovalWorkflowProps {
  userId: string;
  userRole: string;
  showOnlyMyItems?: boolean;
  refreshInterval?: number;
  className?: string;
}

export function AutomatedApprovalWorkflow({
  userId,
  userRole,
  showOnlyMyItems = false,
  refreshInterval = 30000,
  className = ''
}: AutomatedApprovalWorkflowProps) {
  const [workflowItems, setWorkflowItems] = useState<WorkflowItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedItem, setSelectedItem] = useState<WorkflowItem | null>(null);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'escalate'>('approve');
  const [actionComments, setActionComments] = useState('');
  const [isSubmittingAction, setIsSubmittingAction] = useState(false);
  
  const isMobile = useIsMobile();

  // Fetch workflow items
  const fetchWorkflowItems = async () => {
    try {
      setError(null);
      const params = new URLSearchParams({
        userId,
        userRole,
        showOnlyMyItems: showOnlyMyItems.toString()
      });

      const response = await fetch(`/api/accounting/workflows/approval-items?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch workflow items');
      }
      
      const data = await response.json();
      setWorkflowItems(data);
    } catch (error) {
      console.error('Error fetching workflow items:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch workflow items');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle workflow action
  const handleWorkflowAction = async () => {
    if (!selectedItem) return;

    setIsSubmittingAction(true);
    try {
      const response = await fetch('/api/accounting/workflows/approval-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemId: selectedItem.id,
          action: actionType,
          comments: actionComments,
          userId,
          userRole
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process workflow action');
      }

      // Refresh workflow items
      await fetchWorkflowItems();
      
      // Close dialog and reset state
      setIsActionDialogOpen(false);
      setSelectedItem(null);
      setActionComments('');
    } catch (error) {
      console.error('Error processing workflow action:', error);
      setError(error instanceof Error ? error.message : 'Failed to process action');
    } finally {
      setIsSubmittingAction(false);
    }
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchWorkflowItems();
    
    const interval = setInterval(fetchWorkflowItems, refreshInterval);
    return () => clearInterval(interval);
  }, [userId, userRole, showOnlyMyItems, refreshInterval]);

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'auto_approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'escalated':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'default';
    }
  };

  // Get urgency color
  const getUrgencyColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  // Filter items by tab
  const getFilteredItems = (tab: string) => {
    switch (tab) {
      case 'pending':
        return workflowItems.filter(item => item.status === 'pending' || item.status === 'in_progress');
      case 'approved':
        return workflowItems.filter(item => item.status === 'approved');
      case 'rejected':
        return workflowItems.filter(item => item.status === 'rejected');
      case 'escalated':
        return workflowItems.filter(item => item.status === 'escalated');
      default:
        return workflowItems;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-blue-500" />
              <span>Automated Approval Workflow</span>
            </CardTitle>
            <CardDescription>
              AI-powered approval system with automated routing and escalation
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Bell className="h-3 w-3 mr-1" />
              {workflowItems.filter(item => item.status === 'pending').length} Pending
            </Badge>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending">
              Pending ({getFilteredItems('pending').length})
            </TabsTrigger>
            <TabsTrigger value="approved">
              Approved ({getFilteredItems('approved').length})
            </TabsTrigger>
            <TabsTrigger value="rejected">
              Rejected ({getFilteredItems('rejected').length})
            </TabsTrigger>
            <TabsTrigger value="escalated">
              Escalated ({getFilteredItems('escalated').length})
            </TabsTrigger>
          </TabsList>

          {['pending', 'approved', 'rejected', 'escalated'].map((tab) => (
            <TabsContent key={tab} value={tab} className="space-y-4">
              {getFilteredItems(tab).length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No {tab} items found
                </div>
              ) : (
                getFilteredItems(tab).map((item) => (
                  <Card key={item.id} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(item.status)}
                            <CardTitle className="text-base">{item.title}</CardTitle>
                            <Badge variant={getPriorityVariant(item.priority)}>
                              {item.priority}
                            </Badge>
                            {item.autoApprovalEligible && (
                              <Badge variant="outline" className="text-xs">
                                <Zap className="h-3 w-3 mr-1" />
                                Auto-eligible
                              </Badge>
                            )}
                          </div>
                          <CardDescription>{item.description}</CardDescription>
                        </div>
                        <div className="text-right space-y-1">
                          <div className="font-semibold">{formatCurrency(item.amount)}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.submittedAt.toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{item.currentStep} of {item.totalSteps} steps</span>
                        </div>
                        <Progress value={(item.currentStep / item.totalSteps) * 100} />
                      </div>

                      {/* Current Step */}
                      {item.currentStep <= item.totalSteps && (
                        <div className="bg-blue-50 p-3 rounded-md">
                          <div className="flex items-center space-x-2 mb-2">
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="font-medium text-blue-800">
                              Current Step: {item.steps[item.currentStep - 1]?.name}
                            </span>
                          </div>
                          <p className="text-sm text-blue-700">
                            {item.steps[item.currentStep - 1]?.description}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-blue-600">
                              Approver: {item.steps[item.currentStep - 1]?.approverRole}
                            </span>
                            <span className="text-xs text-blue-600">
                              Est. completion: {item.estimatedCompletionTime.toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Budget Impact */}
                      {item.budgetImpact && (
                        <div className={`p-3 rounded-md ${item.budgetImpact.exceedsLimit ? 'bg-red-50' : 'bg-green-50'}`}>
                          <div className="flex items-center space-x-2 mb-2">
                            <FileText className="h-4 w-4" />
                            <span className="font-medium">Budget Impact</span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">Budget:</span>
                              <span className="ml-1">{item.budgetImpact.budgetName}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Category:</span>
                              <span className="ml-1">{item.budgetImpact.categoryName}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Before:</span>
                              <span className="ml-1">{item.budgetImpact.utilizationBefore.toFixed(1)}%</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">After:</span>
                              <span className={`ml-1 ${item.budgetImpact.exceedsLimit ? 'text-red-600 font-semibold' : ''}`}>
                                {item.budgetImpact.utilizationAfter.toFixed(1)}%
                              </span>
                            </div>
                          </div>
                          {item.budgetImpact.exceedsLimit && (
                            <div className="mt-2 text-xs text-red-700">
                              ⚠️ This transaction will exceed the budget limit
                            </div>
                          )}
                        </div>
                      )}

                      {/* Risk and Urgency Scores */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <span className="text-sm font-medium">Urgency Score</span>
                          <div className="flex items-center space-x-2">
                            <Progress value={item.urgencyScore} className="flex-1" />
                            <span className={`text-sm font-semibold ${getUrgencyColor(item.urgencyScore)}`}>
                              {item.urgencyScore}
                            </span>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <span className="text-sm font-medium">Risk Score</span>
                          <div className="flex items-center space-x-2">
                            <Progress value={item.riskScore} className="flex-1" />
                            <span className={`text-sm font-semibold ${getUrgencyColor(item.riskScore)}`}>
                              {item.riskScore}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      {item.status === 'pending' && item.steps[item.currentStep - 1]?.approverRole === userRole && (
                        <div className="flex items-center space-x-2 pt-2">
                          <Button
                            size="sm"
                            onClick={() => {
                              setSelectedItem(item);
                              setActionType('approve');
                              setIsActionDialogOpen(true);
                            }}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedItem(item);
                              setActionType('reject');
                              setIsActionDialogOpen(true);
                            }}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Reject
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedItem(item);
                              setActionType('escalate');
                              setIsActionDialogOpen(true);
                            }}
                          >
                            <Send className="h-4 w-4 mr-2" />
                            Escalate
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Action Dialog */}
        <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {actionType === 'approve' ? 'Approve' : actionType === 'reject' ? 'Reject' : 'Escalate'} Item
              </DialogTitle>
              <DialogDescription>
                {selectedItem?.title} - {formatCurrency(selectedItem?.amount || 0)}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Comments</label>
                <Textarea
                  placeholder={`Add comments for ${actionType}...`}
                  value={actionComments}
                  onChange={(e) => setActionComments(e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div className="flex items-center justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsActionDialogOpen(false)}
                  disabled={isSubmittingAction}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleWorkflowAction}
                  disabled={isSubmittingAction}
                  variant={actionType === 'reject' ? 'destructive' : 'default'}
                >
                  {isSubmittingAction ? 'Processing...' : `${actionType === 'approve' ? 'Approve' : actionType === 'reject' ? 'Reject' : 'Escalate'}`}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}

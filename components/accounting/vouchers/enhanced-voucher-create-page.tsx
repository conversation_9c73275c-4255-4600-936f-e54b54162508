"use client"

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  FileText,
  Users,
  Building,
  Receipt,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { EnhancedVoucherForm } from './enhanced-voucher-form';
import { toast } from "@/components/ui/use-toast";
import { useErrorHandler } from "@/lib/frontend/hooks/useErrorHandler";
import { ErrorOverlay } from "@/components/errors/error-overlay";

export function EnhancedVoucherCreatePage() {
  const [recentVouchers, setRecentVouchers] = useState<any[]>([]);
  const { error, isErrorOpen, handleApiError, handleGenericError, clearError } = useErrorHandler();

  // Handle voucher creation success
  const handleVoucherCreated = async (data: any) => {
    try {
      let response;
      let result;

      // Check if it's a payroll voucher with payroll run data
      if (data.voucherType === 'payroll_salary' && data.payrollRunData) {
        // Use the payroll voucher integration API
        response = await fetch('/api/integration/payroll-voucher/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            payrollRunId: data.payrollRunData.id,
            autoApprove: false
          })
        });
      } else {
        // For non-payroll vouchers, use the simpler voucher API that doesn't require accounts
        const voucherData = {
          voucherType: mapVoucherType(data.voucherType),
          date: data.date.toISOString(),
          description: data.description,
          totalAmount: data.totalAmount,
          fiscalYear: data.fiscalYear || '2024-2025',
          payee: data.payee,
          paymentMethod: data.paymentMethod || 'bank_transfer',
          notes: data.notes,
          status: 'draft'
        };

        // Use the simpler voucher API that doesn't require complex accounting entries
        response = await fetch('/api/accounting/voucher', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(voucherData)
        });
      }

      if (!response.ok) {
        await handleApiError(response);
        return; // Exit early, error handler will show the error
      }

      result = await response.json();

      const newVoucher = {
        id: result.data._id || result.data.id || `V-${Date.now()}`,
        type: data.voucherType,
        amount: data.totalAmount,
        description: data.description,
        payee: data.payee,
        createdAt: new Date(),
        status: 'draft',
        voucherNumber: result.data.voucherNumber
      };

      // Add to recent vouchers
      setRecentVouchers(prev => [newVoucher, ...prev.slice(0, 4)]);

      // Show success message with enhanced details
      const voucherNumber = result.data?.voucher?.voucherNumber || result.data?.voucherNumber || 'N/A';
      toast({
        title: "Voucher Created Successfully",
        description: `${getVoucherTypeLabel(data.voucherType)} (${voucherNumber}) for ${formatCurrency(data.totalAmount)} has been created and is pending approval.`,
      });

    } catch (error) {
      console.error('Error creating voucher:', error);
      // Use generic error handler for non-API errors
      handleGenericError(error instanceof Error ? error : new Error('Failed to create voucher'));
      throw error; // Re-throw to prevent form reset on error
    }
  };

  // Map voucher types to API expected values
  const mapVoucherType = (formType: string) => {
    const typeMap: Record<string, string> = {
      'payroll_salary': 'payment',
      'payment_general': 'payment',
      'payment_expense': 'payment',
      'payment_utility': 'payment',
      'receipt': 'receipt',
      'journal': 'journal'
    };
    return typeMap[formType] || 'payment';
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Get voucher type label
  const getVoucherTypeLabel = (value: string) => {
    const types: Record<string, string> = {
      'payroll_salary': 'Payroll Voucher (Salary)',
      'payment_general': 'General Payment Voucher',
      'payment_expense': 'Expense Voucher',
      'payment_utility': 'Utility Payment Voucher',
      'receipt': 'Receipt Voucher'
    };
    return types[value] || value;
  };

  // Get voucher type icon
  const getVoucherTypeIcon = (value: string) => {
    switch (value) {
      case 'payroll_salary':
        return Users;
      case 'payment_general':
        return FileText;
      case 'payment_expense':
        return Receipt;
      case 'payment_utility':
        return Building;
      case 'receipt':
        return FileText;
      default:
        return FileText;
    }
  };

  return (
    <DashboardShell>
      <ErrorOverlay
        error={error}
        isOpen={isErrorOpen}
        onClose={clearError}
      />
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Voucher</h1>
            <p className="text-muted-foreground">
              Create different types of vouchers including payroll, general payments, and expenses.
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/vouchers/management">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Voucher Management</span>
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <EnhancedVoucherForm onSubmit={handleVoucherCreated} />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Voucher Types Guide */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Voucher Types Guide</CardTitle>
                <CardDescription>
                  Choose the appropriate voucher type for your transaction
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 border rounded-lg">
                    <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-sm">Payroll Voucher</p>
                      <p className="text-xs text-muted-foreground">
                        Monthly salary payments to employees. Automatically pulls data from approved payroll runs.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 border rounded-lg">
                    <FileText className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-sm">General Payment</p>
                      <p className="text-xs text-muted-foreground">
                        Payments to suppliers, vendors, and service providers.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 border rounded-lg">
                    <Receipt className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-sm">Expense Voucher</p>
                      <p className="text-xs text-muted-foreground">
                        Employee expense reimbursements for travel, training, etc.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 border rounded-lg">
                    <Building className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-sm">Utility Payment</p>
                      <p className="text-xs text-muted-foreground">
                        Utility bills, rent, and facility-related payments.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Vouchers */}
            {recentVouchers.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Recently Created
                  </CardTitle>
                  <CardDescription>
                    Vouchers created in this session
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentVouchers.map((voucher) => {
                      const Icon = getVoucherTypeIcon(voucher.type);
                      return (
                        <div key={voucher.id} className="flex items-center gap-3 p-3 border rounded-lg">
                          <Icon className="h-4 w-4 text-muted-foreground" />
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">
                              {voucher.description}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatCurrency(voucher.amount)} • {voucher.payee}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {voucher.status}
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-blue-600" />
                  Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2"></div>
                    <p className="text-muted-foreground">
                      <strong>Payroll vouchers</strong> automatically populate amount and description when you select a pay period.
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2"></div>
                    <p className="text-muted-foreground">
                      All vouchers require approval before payment processing.
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2"></div>
                    <p className="text-muted-foreground">
                      Use detailed descriptions to help with approval and audit processes.
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2"></div>
                    <p className="text-muted-foreground">
                      You can reset the form at any time to start over.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/dashboard/accounting/vouchers/management">
                      <FileText className="h-4 w-4 mr-2" />
                      View All Vouchers
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/dashboard/accounting/vouchers/management?tab=approvals">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Pending Approvals
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/dashboard/payroll/run">
                      <Users className="h-4 w-4 mr-2" />
                      Payroll Runs
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardShell>
  );
}

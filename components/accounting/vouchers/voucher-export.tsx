"use client"

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Download,
  FileText,
  FileSpreadsheet,
  Calendar,
  Filter,
  Loader2,
  BarChart3
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";

interface ExportOptions {
  formats: string[];
  voucherTypes: string[];
  statuses: string[];
  categories: string[];
  includeItemsOption: boolean;
  maxExportLimit: number;
}

interface ExportStatistics {
  totalVouchers: number;
  byType: Record<string, number>;
  byStatus: Record<string, number>;
  byCategory: Record<string, number>;
  totalAmount: number;
  dateRange: {
    earliest: string | null;
    latest: string | null;
  };
}

interface ExportFilters {
  format: 'pdf' | 'excel';
  voucherType?: string;
  status?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  includeItems: boolean;
}

export function VoucherExport() {
  const [exportOptions, setExportOptions] = useState<ExportOptions | null>(null);
  const [statistics, setStatistics] = useState<ExportStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [filters, setFilters] = useState<ExportFilters>({
    format: 'pdf',
    includeItems: false
  });

  // Fetch export options and statistics
  const fetchExportData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/accounting/vouchers/export');
      
      if (!response.ok) {
        throw new Error('Failed to fetch export options');
      }

      const data = await response.json();
      
      if (data.success) {
        setExportOptions(data.data.exportOptions);
        setStatistics(data.data.statistics);
      } else {
        throw new Error(data.error || 'Failed to fetch export options');
      }
    } catch (error: any) {
      console.error('Error fetching export data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch export options",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Export vouchers
  const exportVouchers = async () => {
    try {
      setExporting(true);
      
      const exportPayload = {
        format: filters.format,
        voucherType: filters.voucherType,
        status: filters.status,
        dateRange: filters.dateRange,
        includeItems: filters.includeItems
      };

      const response = await fetch('/api/accounting/vouchers/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportPayload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export vouchers');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      
      const filename = `vouchers_export_${format(new Date(), 'yyyy-MM-dd')}.${filters.format === 'pdf' ? 'pdf' : 'xlsx'}`;
      a.download = filename;
      
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success",
        description: `Vouchers exported successfully as ${filters.format.toUpperCase()}`,
      });
      
      setShowExportDialog(false);
    } catch (error: any) {
      console.error('Error exporting vouchers:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to export vouchers",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      format: 'pdf',
      includeItems: false
    });
  };

  useEffect(() => {
    fetchExportData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading export options...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Vouchers</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalVouchers}</div>
              <p className="text-xs text-muted-foreground">
                Available for export
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(statistics.totalAmount)}</div>
              <p className="text-xs text-muted-foreground">
                All vouchers combined
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Date Range</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-bold">
                {statistics.dateRange.earliest && statistics.dateRange.latest ? (
                  <>
                    {format(new Date(statistics.dateRange.earliest), 'dd/MM/yy')} - {' '}
                    {format(new Date(statistics.dateRange.latest), 'dd/MM/yy')}
                  </>
                ) : 'No data'}
              </div>
              <p className="text-xs text-muted-foreground">
                Available period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payroll Vouchers</CardTitle>
              <FileSpreadsheet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.byCategory.payroll || 0}</div>
              <p className="text-xs text-muted-foreground">
                Employee payments
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Vouchers</CardTitle>
          <CardDescription>
            Export voucher data in PDF or Excel format with customizable filters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Quick Export Buttons */}
            <div className="space-y-2">
              <Label>Quick Export</Label>
              <div className="space-y-2">
                <Button
                  className="w-full justify-start"
                  variant="outline"
                  onClick={() => {
                    setFilters({ format: 'pdf', includeItems: false });
                    setShowExportDialog(true);
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  All Vouchers (PDF)
                </Button>
                <Button
                  className="w-full justify-start"
                  variant="outline"
                  onClick={() => {
                    setFilters({ format: 'excel', includeItems: true });
                    setShowExportDialog(true);
                  }}
                >
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  All Vouchers (Excel)
                </Button>
              </div>
            </div>

            {/* By Type */}
            <div className="space-y-2">
              <Label>By Type</Label>
              <div className="space-y-2">
                {exportOptions?.voucherTypes.map((type) => (
                  <Button
                    key={type}
                    className="w-full justify-start"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setFilters({ format: 'pdf', voucherType: type, includeItems: false });
                      setShowExportDialog(true);
                    }}
                  >
                    <Badge variant="outline" className="mr-2">
                      {statistics?.byType[type] || 0}
                    </Badge>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            {/* By Status */}
            <div className="space-y-2">
              <Label>By Status</Label>
              <div className="space-y-2">
                {exportOptions?.statuses.slice(0, 4).map((status) => (
                  <Button
                    key={status}
                    className="w-full justify-start"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setFilters({ format: 'pdf', status, includeItems: false });
                      setShowExportDialog(true);
                    }}
                  >
                    <Badge variant="outline" className="mr-2">
                      {statistics?.byStatus[status] || 0}
                    </Badge>
                    {status.replace('_', ' ').toUpperCase()}
                  </Button>
                ))}
              </div>
            </div>

            {/* By Category */}
            <div className="space-y-2">
              <Label>By Category</Label>
              <div className="space-y-2">
                {exportOptions?.categories.map((category) => (
                  <Button
                    key={category}
                    className="w-full justify-start"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setFilters({ format: 'pdf', voucherType: undefined, status: undefined, includeItems: false });
                      // Note: We would need to add category filter to the API
                      setShowExportDialog(true);
                    }}
                  >
                    <Badge variant="outline" className="mr-2">
                      {statistics?.byCategory[category] || 0}
                    </Badge>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t">
            <Button
              onClick={() => {
                resetFilters();
                setShowExportDialog(true);
              }}
              className="w-full md:w-auto"
            >
              <Filter className="h-4 w-4 mr-2" />
              Custom Export with Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Export Vouchers</DialogTitle>
            <DialogDescription>
              Configure your export settings
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Format Selection */}
            <div className="space-y-2">
              <Label>Export Format</Label>
              <Select
                value={filters.format}
                onValueChange={(value: 'pdf' | 'excel') => 
                  setFilters(prev => ({ ...prev, format: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      PDF Report
                    </div>
                  </SelectItem>
                  <SelectItem value="excel">
                    <div className="flex items-center">
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Excel Spreadsheet
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Voucher Type Filter */}
            <div className="space-y-2">
              <Label>Voucher Type (Optional)</Label>
              <Select
                value={filters.voucherType || 'all'}
                onValueChange={(value) => 
                  setFilters(prev => ({ 
                    ...prev, 
                    voucherType: value === 'all' ? undefined : value 
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {exportOptions?.voucherTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)} ({statistics?.byType[type] || 0})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label>Status (Optional)</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => 
                  setFilters(prev => ({ 
                    ...prev, 
                    status: value === 'all' ? undefined : value 
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {exportOptions?.statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace('_', ' ').toUpperCase()} ({statistics?.byStatus[status] || 0})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <Label>Date Range (Optional)</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Start Date</Label>
                  <Input
                    type="date"
                    value={filters.dateRange?.startDate || ''}
                    onChange={(e) => 
                      setFilters(prev => ({
                        ...prev,
                        dateRange: {
                          startDate: e.target.value,
                          endDate: prev.dateRange?.endDate || ''
                        }
                      }))
                    }
                  />
                </div>
                <div>
                  <Label className="text-xs">End Date</Label>
                  <Input
                    type="date"
                    value={filters.dateRange?.endDate || ''}
                    onChange={(e) => 
                      setFilters(prev => ({
                        ...prev,
                        dateRange: {
                          startDate: prev.dateRange?.startDate || '',
                          endDate: e.target.value
                        }
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Include Items Option */}
            {filters.format === 'excel' && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeItems"
                  checked={filters.includeItems}
                  onCheckedChange={(checked) => 
                    setFilters(prev => ({ ...prev, includeItems: checked as boolean }))
                  }
                />
                <Label htmlFor="includeItems" className="text-sm">
                  Include voucher items details
                </Label>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={exporting}
            >
              Cancel
            </Button>
            <Button
              onClick={exportVouchers}
              disabled={exporting}
            >
              {exporting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              <Download className="h-4 w-4 mr-2" />
              Export {filters.format.toUpperCase()}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

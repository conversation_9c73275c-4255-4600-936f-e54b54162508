"use client";

import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Download,
  CheckCircle,
  XCircle,
  Trash2,
  FileText,
  Send,
  Archive,
  RotateCcw,
  AlertTriangle,
  Loader2,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { ErrorOverlay } from '@/components/errors/error-overlay';

export interface VoucherData {
  id: string;
  voucherNumber: string;
  voucherType: 'payment' | 'receipt' | 'journal' | 'adjustment';
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'posted' | 'voided' | 'cancelled';
  totalAmount: number;
  date: string;
  description: string;
  payee?: string;
  createdBy?: string;
  canEdit?: boolean;
  canDelete?: boolean;
  canApprove?: boolean;
  canVoid?: boolean;
}

interface VoucherActionsProps {
  voucher: VoucherData;
  onView?: (voucher: VoucherData) => void;
  onEdit?: (voucher: VoucherData) => void;
  onDuplicate?: (voucher: VoucherData) => void;
  onApprove?: (voucher: VoucherData) => void;
  onReject?: (voucher: VoucherData) => void;
  onDelete?: (voucher: VoucherData) => void;
  onVoid?: (voucher: VoucherData) => void;
  onExport?: (voucher: VoucherData, format: 'pdf' | 'excel') => void;
  onSendForApproval?: (voucher: VoucherData) => void;
  onRefresh?: () => void;
  disabled?: boolean;
}

export function VoucherActions({
  voucher,
  onView,
  onEdit,
  onDuplicate,
  onApprove,
  onReject,
  onDelete,
  onVoid,
  onExport,
  onSendForApproval,
  onRefresh,
  disabled = false,
}: VoucherActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState<string | null>(null);
  const { error, isErrorOpen, handleError, hideError } = useErrorHandler();

  // Helper function to handle async actions
  const handleAsyncAction = async (
    action: string,
    asyncFunction: () => Promise<void> | void,
    successMessage?: string
  ) => {
    try {
      setIsLoading(true);
      setLoadingAction(action);
      
      await asyncFunction();
      
      if (successMessage) {
        toast({
          title: 'Success',
          description: successMessage,
        });
      }
      
      // Refresh data if callback provided
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      handleError(error, {
        action,
        voucherId: voucher.id,
        voucherNumber: voucher.voucherNumber,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
      setLoadingAction(null);
    }
  };

  // Delete voucher with confirmation
  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete voucher ${voucher.voucherNumber}? This action cannot be undone.`)) {
      return;
    }

    await handleAsyncAction(
      'delete',
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete voucher');
        }

        if (onDelete) {
          onDelete(voucher);
        }
      },
      `Voucher ${voucher.voucherNumber} has been deleted successfully.`
    );
  };

  // Void voucher with confirmation
  const handleVoid = async () => {
    if (!confirm(`Are you sure you want to void voucher ${voucher.voucherNumber}? This will mark it as voided.`)) {
      return;
    }

    await handleAsyncAction(
      'void',
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}/void`, {
          method: 'POST',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to void voucher');
        }

        if (onVoid) {
          onVoid(voucher);
        }
      },
      `Voucher ${voucher.voucherNumber} has been voided successfully.`
    );
  };

  // Approve voucher
  const handleApprove = async () => {
    await handleAsyncAction(
      'approve',
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}/approve`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'approve',
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to approve voucher');
        }

        if (onApprove) {
          onApprove(voucher);
        }
      },
      `Voucher ${voucher.voucherNumber} has been approved successfully.`
    );
  };

  // Reject voucher
  const handleReject = async () => {
    const reason = prompt('Please provide a reason for rejection:');
    if (!reason) return;

    await handleAsyncAction(
      'reject',
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}/approve`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'reject',
            comments: reason,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to reject voucher');
        }

        if (onReject) {
          onReject(voucher);
        }
      },
      `Voucher ${voucher.voucherNumber} has been rejected.`
    );
  };

  // Send for approval
  const handleSendForApproval = async () => {
    await handleAsyncAction(
      'send_for_approval',
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}/submit`, {
          method: 'POST',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to send voucher for approval');
        }

        if (onSendForApproval) {
          onSendForApproval(voucher);
        }
      },
      `Voucher ${voucher.voucherNumber} has been sent for approval.`
    );
  };

  // Export voucher
  const handleExport = async (format: 'pdf' | 'excel') => {
    await handleAsyncAction(
      `export_${format}`,
      async () => {
        const response = await fetch(`/api/accounting/vouchers/${voucher.id}/export?format=${format}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to export voucher as ${format.toUpperCase()}`);
        }

        // Handle file download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `voucher-${voucher.voucherNumber}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        if (onExport) {
          onExport(voucher, format);
        }
      },
      `Voucher ${voucher.voucherNumber} exported as ${format.toUpperCase()} successfully.`
    );
  };

  // Determine available actions based on voucher status and permissions
  const canEdit = voucher.canEdit !== false && ['draft', 'rejected'].includes(voucher.status);
  const canDelete = voucher.canDelete !== false && ['draft'].includes(voucher.status);
  const canApprove = voucher.canApprove !== false && ['pending_approval'].includes(voucher.status);
  const canVoid = voucher.canVoid !== false && ['approved', 'posted'].includes(voucher.status);
  const canSendForApproval = ['draft', 'rejected'].includes(voucher.status);
  const canDuplicate = true; // Always allow duplication
  const canExport = !['voided', 'cancelled'].includes(voucher.status);

  return (
    <>
      <div className="flex items-center gap-2">
        {/* Primary View Button - Always visible for accessibility */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onView?.(voucher)}
          disabled={disabled}
          className="h-8"
        >
          <Eye className="mr-1 h-3 w-3" />
          View
        </Button>

        {/* Actions Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              disabled={disabled || isLoading}
            >
              <span className="sr-only">Open actions menu</span>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MoreHorizontal className="h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel className="flex items-center justify-between">
              Actions
              <Badge variant="outline" className="text-xs">
                {voucher.status}
              </Badge>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

          {/* Edit Action */}
          {canEdit && (
            <DropdownMenuItem onClick={() => onEdit?.(voucher)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Voucher
            </DropdownMenuItem>
          )}

          {/* Duplicate Action */}
          {canDuplicate && (
            <DropdownMenuItem onClick={() => onDuplicate?.(voucher)}>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {/* Workflow Actions */}
          {canSendForApproval && (
            <DropdownMenuItem 
              onClick={handleSendForApproval}
              disabled={loadingAction === 'send_for_approval'}
            >
              <Send className="mr-2 h-4 w-4" />
              Send for Approval
            </DropdownMenuItem>
          )}

          {canApprove && (
            <DropdownMenuItem 
              onClick={handleApprove}
              disabled={loadingAction === 'approve'}
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve
            </DropdownMenuItem>
          )}

          {canApprove && (
            <DropdownMenuItem 
              onClick={handleReject}
              disabled={loadingAction === 'reject'}
            >
              <XCircle className="mr-2 h-4 w-4" />
              Reject
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {/* Export Actions */}
          {canExport && (
            <>
              <DropdownMenuItem 
                onClick={() => handleExport('pdf')}
                disabled={loadingAction === 'export_pdf'}
              >
                <Download className="mr-2 h-4 w-4" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleExport('excel')}
                disabled={loadingAction === 'export_excel'}
              >
                <FileText className="mr-2 h-4 w-4" />
                Export as Excel
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuSeparator />

          {/* Destructive Actions */}
          {canVoid && (
            <DropdownMenuItem 
              onClick={handleVoid}
              disabled={loadingAction === 'void'}
              className="text-orange-600"
            >
              <Archive className="mr-2 h-4 w-4" />
              Void Voucher
            </DropdownMenuItem>
          )}

          {canDelete && (
            <DropdownMenuItem 
              onClick={handleDelete}
              disabled={loadingAction === 'delete'}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Voucher
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      </div>

      <ErrorOverlay
        error={error}
        isOpen={isErrorOpen}
        onClose={hideError}
        onAction={(action) => {
          if (action === 'retry' && onRefresh) {
            onRefresh();
          }
        }}
      />
    </>
  );
}

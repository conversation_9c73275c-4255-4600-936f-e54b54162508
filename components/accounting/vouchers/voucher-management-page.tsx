"use client"

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  Clock,
  Download,
  Users,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { VoucherApprovalDashboard } from './voucher-approval-dashboard';
import { VoucherExport } from './voucher-export';
import { PayrollVoucherCreator } from '@/components/integration/payroll-voucher-creator';

export function VoucherManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');

  // Sample statistics for the overview
  const stats = {
    totalVouchers: 156,
    pendingApproval: 12,
    approvedToday: 8,
    totalAmount: 45750000,
    payrollVouchers: 23,
    generalVouchers: 133
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Voucher Management</h1>
            <p className="text-muted-foreground">
              Comprehensive voucher management with approval workflows, payroll integration, and export capabilities.
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
            <Button size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/vouchers/create">
                <FileText className="h-4 w-4" />
                <span>Create Voucher</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
            <TabsList className="grid w-full grid-cols-5 lg:w-auto">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="approvals">
                Approvals
                {stats.pendingApproval > 0 && (
                  <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                    {stats.pendingApproval}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="payroll">Payroll Integration</TabsTrigger>
              <TabsTrigger value="export">Export</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
          </div>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-6 space-y-6">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Vouchers</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalVouchers}</div>
                  <p className="text-xs text-muted-foreground">All time</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">{stats.pendingApproval}</div>
                  <p className="text-xs text-muted-foreground">Requires action</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{stats.approvedToday}</div>
                  <p className="text-xs text-muted-foreground">Today's activity</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
                  <p className="text-xs text-muted-foreground">All vouchers</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Payroll Vouchers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.payrollVouchers}</div>
                  <p className="text-xs text-muted-foreground">Employee payments</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">General Vouchers</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.generalVouchers}</div>
                  <p className="text-xs text-muted-foreground">Other transactions</p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Pending Approvals
                  </CardTitle>
                  <CardDescription>
                    Review and approve pending vouchers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-3xl font-bold text-orange-600">{stats.pendingApproval}</div>
                    <p className="text-sm text-muted-foreground">vouchers awaiting approval</p>
                    <Button 
                      className="w-full" 
                      onClick={() => setActiveTab('approvals')}
                      disabled={stats.pendingApproval === 0}
                    >
                      Review Approvals
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Payroll Integration
                  </CardTitle>
                  <CardDescription>
                    Create vouchers from approved payroll runs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-3xl font-bold text-blue-600">3</div>
                    <p className="text-sm text-muted-foreground">payroll runs ready for vouchers</p>
                    <Button 
                      className="w-full" 
                      variant="outline"
                      onClick={() => setActiveTab('payroll')}
                    >
                      Manage Payroll Vouchers
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Export & Reports
                  </CardTitle>
                  <CardDescription>
                    Export vouchers to PDF or Excel
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-3xl font-bold text-green-600">{stats.totalVouchers}</div>
                    <p className="text-sm text-muted-foreground">vouchers available for export</p>
                    <Button 
                      className="w-full" 
                      variant="outline"
                      onClick={() => setActiveTab('export')}
                    >
                      Export Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest voucher activities and approvals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4 p-3 border rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <p className="font-medium">Payment Voucher PV-2025-0156 approved</p>
                      <p className="text-sm text-muted-foreground">Office supplies payment - {formatCurrency(125000)}</p>
                    </div>
                    <Badge variant="default">Approved</Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 p-3 border rounded-lg">
                    <Clock className="h-5 w-5 text-orange-600" />
                    <div className="flex-1">
                      <p className="font-medium">Payroll Voucher PV-2025-0155 pending approval</p>
                      <p className="text-sm text-muted-foreground">January 2025 salary payments - {formatCurrency(2500000)}</p>
                    </div>
                    <Badge variant="secondary">Pending</Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 p-3 border rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <p className="font-medium">Receipt Voucher RV-2025-0034 created</p>
                      <p className="text-sm text-muted-foreground">Registration fee collection - {formatCurrency(75000)}</p>
                    </div>
                    <Badge variant="outline">Draft</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Approvals Tab */}
          <TabsContent value="approvals" className="mt-6">
            <VoucherApprovalDashboard />
          </TabsContent>

          {/* Payroll Integration Tab */}
          <TabsContent value="payroll" className="mt-6">
            <PayrollVoucherCreator />
          </TabsContent>

          {/* Export Tab */}
          <TabsContent value="export" className="mt-6">
            <VoucherExport />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Voucher Analytics</CardTitle>
                <CardDescription>
                  Detailed analytics and insights will be available in a future update
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4" />
                    <p>Analytics dashboard coming soon</p>
                    <p className="text-sm">Track voucher trends, approval times, and financial insights</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

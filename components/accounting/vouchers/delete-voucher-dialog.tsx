"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertTriangle,
  Trash2,
  Loader2,
  FileText,
  DollarSign,
  Calendar,
  User,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { ErrorOverlay } from '@/components/errors/error-overlay';
import { VoucherData } from './voucher-actions';

interface DeleteVoucherDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  voucher: VoucherData | null;
  onDelete?: (voucher: VoucherData) => void;
  onRefresh?: () => void;
}

export function DeleteVoucherDialog({
  open,
  onOpenChange,
  voucher,
  onDelete,
  onRefresh,
}: DeleteVoucherDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmationChecked, setConfirmationChecked] = useState(false);
  const [deleteReason, setDeleteReason] = useState('');
  const [deleteType, setDeleteType] = useState<'soft' | 'hard'>('soft');
  
  const { error, isErrorOpen, handleError, hideError } = useErrorHandler();

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setConfirmationChecked(false);
      setDeleteReason('');
      setDeleteType('soft');
    }
  }, [open]);

  const handleDelete = async () => {
    if (!voucher || !confirmationChecked) return;

    try {
      setIsDeleting(true);

      const response = await fetch(`/api/accounting/vouchers/${voucher.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deleteType,
          reason: deleteReason,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete voucher');
      }

      const result = await response.json();

      toast({
        title: 'Voucher Deleted',
        description: result.message || `Voucher ${voucher.voucherNumber} has been deleted successfully.`,
      });

      // Call callbacks
      if (onDelete) {
        onDelete(voucher);
      }
      if (onRefresh) {
        onRefresh();
      }

      // Close dialog
      onOpenChange(false);
    } catch (error) {
      handleError(error, {
        action: 'delete_voucher',
        voucherId: voucher?.id,
        voucherNumber: voucher?.voucherNumber,
        deleteType,
        reason: deleteReason,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!voucher) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const canHardDelete = voucher.status === 'draft' && !voucher.createdBy;
  const isHighValue = voucher.totalAmount > 1000000; // 1M MWK
  const requiresReason = isHighValue || voucher.status !== 'draft';

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              Delete Voucher
            </DialogTitle>
            <DialogDescription>
              You are about to delete the following voucher. This action may not be reversible.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Voucher Details */}
            <div className="border rounded-lg p-4 bg-muted/50">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">{voucher.voucherNumber}</h4>
                <Badge variant={
                  voucher.status === 'draft' ? 'outline' :
                  voucher.status === 'pending' ? 'secondary' :
                  voucher.status === 'approved' ? 'default' :
                  voucher.status === 'rejected' ? 'destructive' :
                  'outline'
                }>
                  {voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>{formatCurrency(voucher.totalAmount)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(voucher.date)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{voucher.voucherType}</span>
                </div>
                {voucher.payee && (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{voucher.payee}</span>
                  </div>
                )}
              </div>
              
              <div className="mt-3 pt-3 border-t">
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {voucher.description}
                </p>
              </div>
            </div>

            {/* Warnings */}
            {isHighValue && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This is a high-value voucher ({formatCurrency(voucher.totalAmount)}). 
                  Deletion requires additional confirmation and reason.
                </AlertDescription>
              </Alert>
            )}

            {voucher.status !== 'draft' && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This voucher has been processed beyond draft status. 
                  Deleting it may affect financial records and audit trails.
                </AlertDescription>
              </Alert>
            )}

            {/* Delete Type Selection */}
            {canHardDelete && (
              <div className="space-y-3">
                <Label className="text-sm font-medium">Delete Type</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="soft-delete"
                      checked={deleteType === 'soft'}
                      onCheckedChange={() => setDeleteType('soft')}
                    />
                    <Label htmlFor="soft-delete" className="text-sm">
                      Soft Delete (Mark as deleted, keep for audit)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hard-delete"
                      checked={deleteType === 'hard'}
                      onCheckedChange={() => setDeleteType('hard')}
                    />
                    <Label htmlFor="hard-delete" className="text-sm">
                      Hard Delete (Permanently remove from database)
                    </Label>
                  </div>
                </div>
              </div>
            )}

            {/* Reason for Deletion */}
            {requiresReason && (
              <div className="space-y-2">
                <Label htmlFor="delete-reason" className="text-sm font-medium">
                  Reason for Deletion {requiresReason && <span className="text-red-500">*</span>}
                </Label>
                <Textarea
                  id="delete-reason"
                  placeholder="Please provide a reason for deleting this voucher..."
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                  rows={3}
                />
              </div>
            )}

            {/* Confirmation Checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="confirm-delete"
                checked={confirmationChecked}
                onCheckedChange={setConfirmationChecked}
              />
              <Label htmlFor="confirm-delete" className="text-sm">
                I understand that this action will delete the voucher and may affect financial records.
              </Label>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={
                isDeleting || 
                !confirmationChecked || 
                (requiresReason && !deleteReason.trim())
              }
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Voucher
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ErrorOverlay
        error={error}
        isOpen={isErrorOpen}
        onClose={hideError}
        onAction={(action) => {
          if (action === 'retry') {
            handleDelete();
          }
        }}
      />
    </>
  );
}

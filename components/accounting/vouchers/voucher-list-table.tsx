"use client";

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw,
  Loader2,
  FileText,
  CheckCircle,
  XCircle,
  Trash2,
} from 'lucide-react';
import { VoucherActions, VoucherData } from './voucher-actions';
import { DeleteVoucherDialog } from './delete-voucher-dialog';
import { toast } from '@/components/ui/use-toast';

interface VoucherListTableProps {
  vouchers: VoucherData[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onCreateNew?: () => void;
  onBulkExport?: (voucherIds: string[]) => void;
  onBulkApprove?: (voucherIds: string[]) => void;
  onBulkReject?: (voucherIds: string[]) => void;
  onBulkDelete?: (voucherIds: string[]) => void;
  showBulkActions?: boolean;
  title?: string;
  description?: string;
}

export function VoucherListTable({
  vouchers,
  isLoading = false,
  onRefresh,
  onCreateNew,
  onBulkExport,
  onBulkApprove,
  onBulkReject,
  onBulkDelete,
  showBulkActions = true,
  title = "Vouchers",
  description = "Manage and track all vouchers",
}: VoucherListTableProps) {
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [voucherToDelete, setVoucherToDelete] = useState<VoucherData | null>(null);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  // Filter vouchers based on search and filters
  const filteredVouchers = vouchers.filter(voucher => {
    const matchesSearch = 
      voucher.voucherNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      voucher.payee?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || voucher.status === statusFilter;
    const matchesType = typeFilter === 'all' || voucher.voucherType === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // Handle voucher selection
  const handleVoucherSelection = (voucherId: string, checked: boolean) => {
    if (checked) {
      setSelectedVouchers(prev => [...prev, voucherId]);
    } else {
      setSelectedVouchers(prev => prev.filter(id => id !== voucherId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVouchers(filteredVouchers.map(v => v.id));
    } else {
      setSelectedVouchers([]);
    }
  };

  // Handle individual voucher actions
  const handleView = (voucher: VoucherData) => {
    // Navigate to voucher detail page
    window.open(`/dashboard/accounting/vouchers/${voucher.id}`, '_blank');
  };

  const handleEdit = (voucher: VoucherData) => {
    // Navigate to voucher edit page
    window.open(`/dashboard/accounting/vouchers/${voucher.id}/edit`, '_blank');
  };

  const handleDuplicate = (voucher: VoucherData) => {
    // Navigate to create page with voucher data
    window.open(`/dashboard/accounting/vouchers/create?duplicate=${voucher.id}`, '_blank');
  };

  const handleDelete = (voucher: VoucherData) => {
    setVoucherToDelete(voucher);
    setDeleteDialogOpen(true);
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string, actionFunction?: (ids: string[]) => void) => {
    if (selectedVouchers.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select vouchers to perform bulk actions.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setBulkActionLoading(true);
      
      if (actionFunction) {
        await actionFunction(selectedVouchers);
      }
      
      // Clear selection after successful action
      setSelectedVouchers([]);
      
      toast({
        title: 'Success',
        description: `Bulk ${action} completed successfully.`,
      });
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to perform bulk ${action}.`,
        variant: 'destructive',
      });
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'draft': return 'outline';
      case 'pending_approval': return 'secondary';
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'posted': return 'default';
      case 'voided': return 'secondary';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const allSelected = filteredVouchers.length > 0 && selectedVouchers.length === filteredVouchers.length;
  const someSelected = selectedVouchers.length > 0 && selectedVouchers.length < filteredVouchers.length;

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>{description}</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Refresh
              </Button>
              {onCreateNew && (
                <Button size="sm" onClick={onCreateNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Voucher
                </Button>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search vouchers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending_approval">Pending Approval</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="posted">Posted</SelectItem>
                <SelectItem value="voided">Voided</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="payment">Payment</SelectItem>
                <SelectItem value="receipt">Receipt</SelectItem>
                <SelectItem value="journal">Journal</SelectItem>
                <SelectItem value="adjustment">Adjustment</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {showBulkActions && selectedVouchers.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedVouchers.length} voucher(s) selected
              </span>
              <div className="flex items-center gap-2 ml-auto">
                {onBulkApprove && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('approval', onBulkApprove)}
                    disabled={bulkActionLoading}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Approve
                  </Button>
                )}
                {onBulkReject && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('rejection', onBulkReject)}
                    disabled={bulkActionLoading}
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Reject
                  </Button>
                )}
                {onBulkExport && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('export', onBulkExport)}
                    disabled={bulkActionLoading}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                )}
                {onBulkDelete && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('deletion', onBulkDelete)}
                    disabled={bulkActionLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setSelectedVouchers([])}
                >
                  Clear
                </Button>
              </div>
            </div>
          )}
        </CardHeader>

        <CardContent>
          {filteredVouchers.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No vouchers found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'No vouchers match your current filters.'
                  : 'Get started by creating your first voucher.'}
              </p>
              {onCreateNew && (
                <Button onClick={onCreateNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Voucher
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {showBulkActions && (
                      <TableHead className="w-12">
                        <Checkbox
                          checked={allSelected}
                          onCheckedChange={handleSelectAll}
                          ref={(el) => {
                            if (el) el.indeterminate = someSelected;
                          }}
                        />
                      </TableHead>
                    )}
                    <TableHead>Voucher #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Payee</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-32">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVouchers.map((voucher) => (
                    <TableRow key={voucher.id}>
                      {showBulkActions && (
                        <TableCell>
                          <Checkbox
                            checked={selectedVouchers.includes(voucher.id)}
                            onCheckedChange={(checked) =>
                              handleVoucherSelection(voucher.id, checked as boolean)
                            }
                          />
                        </TableCell>
                      )}
                      <TableCell className="font-medium">
                        {voucher.voucherNumber}
                      </TableCell>
                      <TableCell>{formatDate(voucher.date)}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {voucher.voucherType}
                        </Badge>
                      </TableCell>
                      <TableCell>{voucher.payee || '—'}</TableCell>
                      <TableCell className="max-w-[200px] truncate" title={voucher.description}>
                        {voucher.description}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(voucher.totalAmount)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(voucher.status)}>
                          {voucher.status === 'pending_approval'
                            ? 'Pending Approval'
                            : voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <VoucherActions
                          voucher={voucher}
                          onView={handleView}
                          onEdit={handleEdit}
                          onDuplicate={handleDuplicate}
                          onDelete={handleDelete}
                          onRefresh={onRefresh}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <DeleteVoucherDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        voucher={voucherToDelete}
        onDelete={() => {
          if (onRefresh) onRefresh();
        }}
        onRefresh={onRefresh}
      />
    </>
  );
}

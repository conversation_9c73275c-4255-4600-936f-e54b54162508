"use client"

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from '@/hooks/use-toast';
import { Loader2, Calendar, DollarSign, FileText, User, Settings, Database, ChevronDown, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ErrorOverlay } from '@/components/errors/error-overlay';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

/**
 * Interface for voucher type definition
 */
interface VoucherTypeDefinition {
  typeId: string;
  name: string;
  description: string;
  category: string;
  subCategory: string;
  configuration: {
    requiresApproval: boolean;
    approvalLevels: number;
    autoNumbering: boolean;
    numberPrefix: string;
    allowManualEntry: boolean;
    requiresAttachments: boolean;
    defaultFiscalYear: boolean;
  };
  customFields: Array<{
    fieldName: string;
    fieldType: string;
    label: string;
    description?: string;
    required: boolean;
    defaultValue?: any;
    validation?: any;
    displayOptions?: {
      order: number;
      group?: string;
      hidden?: boolean;
      readonly?: boolean;
      placeholder?: string;
      helpText?: string;
    };
    referenceModel?: string;
    dependsOn?: string[];
  }>;
  displayConfiguration: {
    formLayout: string;
    fieldGroups: Array<{
      groupName: string;
      fields: string[];
      collapsible: boolean;
      defaultExpanded: boolean;
    }>;
  };
}

/**
 * Interface for form props
 */
interface DynamicVoucherFormProps {
  onSubmit: (data: any) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  initialData?: any;
}

/**
 * Dynamic schema based on voucher type
 */
const createDynamicSchema = (voucherType: VoucherTypeDefinition | null) => {
  const baseSchema = {
    voucherTypeId: z.string().min(1, 'Voucher type is required'),
    date: z.date({
      required_error: 'Date is required',
    }),
    description: z.string().min(3, 'Description must be at least 3 characters'),
    totalAmount: z.number().min(0.01, 'Amount must be greater than 0'),
    fiscalYear: z.string().optional(),
    payee: z.string().optional(),
    paymentMethod: z.string().optional(),
    notes: z.string().optional(),
  };

  // Add dynamic fields based on voucher type
  const dynamicFields: Record<string, any> = {};
  
  if (voucherType?.customFields) {
    voucherType.customFields.forEach(field => {
      let fieldSchema: any;
      
      switch (field.fieldType) {
        case 'string':
          fieldSchema = z.string();
          if (field.validation?.pattern) {
            fieldSchema = fieldSchema.regex(new RegExp(field.validation.pattern));
          }
          break;
        case 'number':
          fieldSchema = z.number();
          if (field.validation?.min !== undefined) {
            fieldSchema = fieldSchema.min(field.validation.min);
          }
          if (field.validation?.max !== undefined) {
            fieldSchema = fieldSchema.max(field.validation.max);
          }
          break;
        case 'date':
          fieldSchema = z.date();
          break;
        case 'boolean':
          fieldSchema = z.boolean();
          break;
        case 'array':
          fieldSchema = z.array(z.any());
          break;
        default:
          fieldSchema = z.any();
      }
      
      if (!field.required) {
        fieldSchema = fieldSchema.optional();
      }
      
      dynamicFields[field.fieldName] = fieldSchema;
    });
  }

  return z.object({
    ...baseSchema,
    dynamicFields: z.object(dynamicFields).optional()
  });
};

/**
 * Dynamic Voucher Form Component
 */
export function DynamicVoucherForm({ onSubmit, onCancel, isLoading = false, initialData }: DynamicVoucherFormProps) {
  const [voucherTypes, setVoucherTypes] = useState<VoucherTypeDefinition[]>([]);
  const [selectedVoucherType, setSelectedVoucherType] = useState<VoucherTypeDefinition | null>(null);
  const [isLoadingTypes, setIsLoadingTypes] = useState(true);
  const [error, setError] = useState<any>(null);
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Create dynamic form schema
  const formSchema = createDynamicSchema(selectedVoucherType);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      voucherTypeId: initialData?.voucherTypeId || '',
      date: initialData?.date || new Date(),
      description: initialData?.description || '',
      totalAmount: initialData?.totalAmount || 0,
      fiscalYear: initialData?.fiscalYear || new Date().getFullYear().toString(),
      payee: initialData?.payee || '',
      paymentMethod: initialData?.paymentMethod || '',
      notes: initialData?.notes || '',
      dynamicFields: initialData?.dynamicFields || {}
    },
  });

  // Fetch voucher types on component mount
  useEffect(() => {
    fetchVoucherTypes();
  }, []);

  // Update selected voucher type when form value changes
  useEffect(() => {
    const voucherTypeId = form.watch('voucherTypeId');
    if (voucherTypeId) {
      const voucherType = voucherTypes.find(type => type.typeId === voucherTypeId);
      setSelectedVoucherType(voucherType || null);
      
      // Initialize expanded groups
      if (voucherType?.displayConfiguration.fieldGroups) {
        const initialExpanded: Record<string, boolean> = {};
        voucherType.displayConfiguration.fieldGroups.forEach(group => {
          initialExpanded[group.groupName] = group.defaultExpanded;
        });
        setExpandedGroups(initialExpanded);
      }
    }
  }, [form.watch('voucherTypeId'), voucherTypes]);

  /**
   * Fetch available voucher types
   */
  const fetchVoucherTypes = async () => {
    try {
      setIsLoadingTypes(true);
      const response = await fetch('/api/accounting/voucher-types');
      
      if (!response.ok) {
        throw new Error('Failed to fetch voucher types');
      }
      
      const result = await response.json();
      if (result.success) {
        setVoucherTypes(result.data.voucherTypes);
      } else {
        throw new Error(result.error || 'Failed to fetch voucher types');
      }
    } catch (error: any) {
      console.error('Error fetching voucher types:', error);
      setError({
        type: 'FETCH_ERROR',
        title: 'Failed to load voucher types',
        message: error.message,
        actions: [
          {
            label: 'Retry',
            action: 'retry',
            type: 'button',
            variant: 'primary'
          }
        ]
      });
    } finally {
      setIsLoadingTypes(false);
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setError(null);
      
      // Prepare data for API
      const voucherData = {
        voucherTypeId: values.voucherTypeId,
        coreFields: {
          date: values.date,
          description: values.description,
          totalAmount: values.totalAmount,
          fiscalYear: values.fiscalYear,
          payee: values.payee,
          paymentMethod: values.paymentMethod,
          notes: values.notes
        },
        dynamicFields: values.dynamicFields || {}
      };
      
      await onSubmit(voucherData);
    } catch (error: any) {
      console.error('Error submitting voucher:', error);
      setError({
        type: 'SUBMIT_ERROR',
        title: 'Failed to create voucher',
        message: error.message,
        actions: [
          {
            label: 'Retry',
            action: 'retry',
            type: 'button',
            variant: 'primary'
          }
        ]
      });
    }
  };

  /**
   * Handle error actions
   */
  const handleErrorAction = (action: string) => {
    switch (action) {
      case 'retry':
        if (error?.type === 'FETCH_ERROR') {
          fetchVoucherTypes();
        } else {
          form.handleSubmit(handleSubmit)();
        }
        break;
      default:
        setError(null);
    }
  };

  /**
   * Toggle field group expansion
   */
  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  /**
   * Render dynamic field based on type
   */
  const renderDynamicField = (field: any) => {
    const fieldName = `dynamicFields.${field.fieldName}`;
    
    switch (field.fieldType) {
      case 'string':
        return (
          <FormField
            key={field.fieldName}
            control={form.control}
            name={fieldName as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={field.displayOptions?.placeholder}
                    disabled={field.displayOptions?.readonly || isLoading}
                    {...formField}
                  />
                </FormControl>
                {field.description && (
                  <FormDescription>{field.description}</FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        );
        
      case 'number':
        return (
          <FormField
            key={field.fieldName}
            control={form.control}
            name={fieldName as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder={field.displayOptions?.placeholder}
                    disabled={field.displayOptions?.readonly || isLoading}
                    {...formField}
                    onChange={(e) => formField.onChange(Number(e.target.value))}
                  />
                </FormControl>
                {field.description && (
                  <FormDescription>{field.description}</FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        );
        
      case 'date':
        return (
          <FormField
            key={field.fieldName}
            control={form.control}
            name={fieldName as any}
            render={({ field: formField }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !formField.value && "text-muted-foreground"
                        )}
                        disabled={field.displayOptions?.readonly || isLoading}
                      >
                        {formField.value ? (
                          format(formField.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <Calendar className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={formField.value}
                      onSelect={formField.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {field.description && (
                  <FormDescription>{field.description}</FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        );
        
      default:
        return (
          <FormField
            key={field.fieldName}
            control={form.control}
            name={fieldName as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={field.displayOptions?.placeholder}
                    disabled={field.displayOptions?.readonly || isLoading}
                    {...formField}
                  />
                </FormControl>
                {field.description && (
                  <FormDescription>{field.description}</FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        );
    }
  };

  if (error) {
    return (
      <ErrorOverlay
        error={error}
        isOpen={true}
        onAction={handleErrorAction}
        onClose={() => setError(null)}
      />
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Dynamic Voucher Creation
        </CardTitle>
        <CardDescription>
          Create vouchers with type-specific fields and intelligent data handling
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Voucher Type Selection */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <h3 className="text-lg font-medium">Voucher Type</h3>
              </div>
              
              <FormField
                control={form.control}
                name="voucherTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Voucher Type *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoadingTypes || isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={
                            isLoadingTypes ? "Loading voucher types..." : "Select a voucher type"
                          } />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {voucherTypes.map((type) => (
                          <SelectItem key={type.typeId} value={type.typeId}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {type.category}
                              </Badge>
                              <span>{type.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of voucher to create. This determines the available fields and workflow.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {selectedVoucherType && (
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">{selectedVoucherType.name}</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    {selectedVoucherType.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">
                      {selectedVoucherType.category}
                    </Badge>
                    <Badge variant="outline">
                      {selectedVoucherType.subCategory}
                    </Badge>
                    {selectedVoucherType.configuration.requiresApproval && (
                      <Badge variant="default">
                        Requires Approval
                      </Badge>
                    )}
                    {selectedVoucherType.configuration.autoNumbering && (
                      <Badge variant="default">
                        Auto Numbering
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Core Fields */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <h3 className="text-lg font-medium">Core Information</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                              disabled={isLoading}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <Calendar className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="totalAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total Amount *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            className="pl-10"
                            disabled={isLoading}
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter voucher description..."
                        className="min-h-[80px]"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="payee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payee</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Enter payee name"
                            className="pl-10"
                            disabled={isLoading}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="paymentMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="check">Check</SelectItem>
                          <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                          <SelectItem value="credit_card">Credit Card</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="fiscalYear"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fiscal Year</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="YYYY"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional notes or comments..."
                        className="min-h-[60px]"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Dynamic Fields */}
            {selectedVoucherType && selectedVoucherType.customFields.length > 0 && (
              <>
                <Separator />
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    <h3 className="text-lg font-medium">Type-Specific Fields</h3>
                  </div>

                  {selectedVoucherType.displayConfiguration.fieldGroups.length > 0 ? (
                    // Render grouped fields
                    selectedVoucherType.displayConfiguration.fieldGroups.map((group) => (
                      <Collapsible
                        key={group.groupName}
                        open={expandedGroups[group.groupName]}
                        onOpenChange={() => toggleGroup(group.groupName)}
                      >
                        <CollapsibleTrigger asChild>
                          <Button
                            variant="ghost"
                            className="flex w-full justify-between p-4 hover:bg-muted"
                          >
                            <span className="font-medium">{group.groupName}</span>
                            {expandedGroups[group.groupName] ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="space-y-4 p-4 border rounded-lg">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {group.fields.map((fieldName) => {
                              const field = selectedVoucherType.customFields.find(
                                f => f.fieldName === fieldName
                              );
                              return field ? renderDynamicField(field) : null;
                            })}
                          </div>
                        </CollapsibleContent>
                      </Collapsible>
                    ))
                  ) : (
                    // Render ungrouped fields
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedVoucherType.customFields
                        .sort((a, b) => (a.displayOptions?.order || 0) - (b.displayOptions?.order || 0))
                        .map(renderDynamicField)}
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Form Actions */}
            <div className="flex justify-end gap-4 pt-6">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={isLoading || !selectedVoucherType}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Voucher
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

"use client"

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Users,
  DollarSign,
  Loader2,
  Download
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";

interface PendingVoucher {
  id: string;
  voucherNumber: string;
  voucherType: string;
  date: string;
  description: string;
  totalAmount: number;
  status: string;
  voucherCategory: string;
  payee?: string;
  paymentMethod?: string;
  fiscalYear: string;
  createdBy: {
    name: string;
    email: string;
  };
  createdAt: string;
  payrollRun?: {
    id: string;
    name: string;
    payPeriod: {
      month: number;
      year: number;
    };
  };
  approvalWorkflow: {
    currentLevel: number;
    workflowType: string;
    approvalHistory: any[];
  };
}

interface ApprovalSummary {
  totalPending: number;
  urgentCount: number;
  normalCount: number;
  totalAmount: number;
  byCategory: Record<string, number>;
  byType: Record<string, number>;
}

export function VoucherApprovalDashboard() {
  const [pendingVouchers, setPendingVouchers] = useState<PendingVoucher[]>([]);
  const [groupedVouchers, setGroupedVouchers] = useState<{
    urgent: PendingVoucher[];
    normal: PendingVoucher[];
  }>({ urgent: [], normal: [] });
  const [summary, setSummary] = useState<ApprovalSummary | null>(null);
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve');
  const [approvalComments, setApprovalComments] = useState('');
  const [userPermissions, setUserPermissions] = useState<any>(null);

  // Fetch pending approvals
  const fetchPendingApprovals = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/accounting/vouchers/pending-approval');
      
      if (!response.ok) {
        throw new Error('Failed to fetch pending approvals');
      }

      const data = await response.json();
      
      if (data.success) {
        setPendingVouchers(data.data.vouchers);
        setGroupedVouchers(data.data.groupedVouchers);
        setSummary(data.data.summary);
        setUserPermissions(data.data.permissions);
      } else {
        throw new Error(data.error || 'Failed to fetch pending approvals');
      }
    } catch (error: any) {
      console.error('Error fetching pending approvals:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch pending approvals",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Process single voucher approval
  const processApproval = async (voucherId: string, action: 'approve' | 'reject', comments?: string) => {
    try {
      setActionLoading(true);
      
      const response = await fetch(`/api/accounting/vouchers/${voucherId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          comments
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} voucher`);
      }

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        });
        
        // Refresh the list
        await fetchPendingApprovals();
      } else {
        throw new Error(data.error || `Failed to ${action} voucher`);
      }
    } catch (error: any) {
      console.error(`Error ${action}ing voucher:`, error);
      toast({
        title: "Error",
        description: error.message || `Failed to ${action} voucher`,
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Process bulk approval
  const processBulkApproval = async () => {
    if (selectedVouchers.length === 0) {
      toast({
        title: "Warning",
        description: "Please select vouchers to approve",
        variant: "destructive",
      });
      return;
    }

    try {
      setActionLoading(true);
      
      const response = await fetch('/api/accounting/vouchers/pending-approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          voucherIds: selectedVouchers,
          comments: approvalComments
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process bulk approval');
      }

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        });
        
        // Clear selections and refresh
        setSelectedVouchers([]);
        setApprovalComments('');
        setShowApprovalDialog(false);
        await fetchPendingApprovals();
      } else {
        throw new Error(data.error || 'Failed to process bulk approval');
      }
    } catch (error: any) {
      console.error('Error processing bulk approval:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to process bulk approval",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending_approval':
        return 'secondary';
      case 'approved':
        return 'default';
      case 'rejected':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Get category badge variant
  const getCategoryBadgeVariant = (category: string) => {
    switch (category) {
      case 'payroll':
        return 'default';
      case 'procurement':
        return 'secondary';
      case 'expense':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Handle voucher selection
  const handleVoucherSelection = (voucherId: string, checked: boolean) => {
    if (checked) {
      setSelectedVouchers(prev => [...prev, voucherId]);
    } else {
      setSelectedVouchers(prev => prev.filter(id => id !== voucherId));
    }
  };

  // Handle select all
  const handleSelectAll = (vouchers: PendingVoucher[], checked: boolean) => {
    const voucherIds = vouchers.map(v => v.id);
    if (checked) {
      setSelectedVouchers(prev => [...new Set([...prev, ...voucherIds])]);
    } else {
      setSelectedVouchers(prev => prev.filter(id => !voucherIds.includes(id)));
    }
  };

  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading pending approvals...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalPending}</div>
              <p className="text-xs text-muted-foreground">
                {summary.urgentCount} urgent, {summary.normalCount} normal
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalAmount)}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting approval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payroll Vouchers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.byCategory.payroll || 0}</div>
              <p className="text-xs text-muted-foreground">
                Employee payments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Vouchers</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.byType.payment || 0}</div>
              <p className="text-xs text-muted-foreground">
                Outgoing payments
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Action Buttons */}
      {selectedVouchers.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {selectedVouchers.length} voucher(s) selected.
            <div className="flex gap-2 mt-2">
              <Button
                size="sm"
                onClick={() => {
                  setApprovalAction('approve');
                  setShowApprovalDialog(true);
                }}
                disabled={actionLoading}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Bulk Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setApprovalAction('reject');
                  setShowApprovalDialog(true);
                }}
                disabled={actionLoading}
              >
                <XCircle className="h-4 w-4 mr-1" />
                Bulk Reject
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedVouchers([])}
              >
                Clear Selection
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Vouchers Tabs */}
      <Tabs defaultValue="urgent" className="w-full">
        <TabsList>
          <TabsTrigger value="urgent">
            Urgent ({groupedVouchers.urgent.length})
          </TabsTrigger>
          <TabsTrigger value="normal">
            Normal ({groupedVouchers.normal.length})
          </TabsTrigger>
          <TabsTrigger value="all">
            All ({pendingVouchers.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="urgent">
          <VoucherTable
            vouchers={groupedVouchers.urgent}
            selectedVouchers={selectedVouchers}
            onVoucherSelection={handleVoucherSelection}
            onSelectAll={handleSelectAll}
            onApprove={(id) => processApproval(id, 'approve')}
            onReject={(id) => processApproval(id, 'reject')}
            actionLoading={actionLoading}
            formatCurrency={formatCurrency}
            getStatusBadgeVariant={getStatusBadgeVariant}
            getCategoryBadgeVariant={getCategoryBadgeVariant}
          />
        </TabsContent>

        <TabsContent value="normal">
          <VoucherTable
            vouchers={groupedVouchers.normal}
            selectedVouchers={selectedVouchers}
            onVoucherSelection={handleVoucherSelection}
            onSelectAll={handleSelectAll}
            onApprove={(id) => processApproval(id, 'approve')}
            onReject={(id) => processApproval(id, 'reject')}
            actionLoading={actionLoading}
            formatCurrency={formatCurrency}
            getStatusBadgeVariant={getStatusBadgeVariant}
            getCategoryBadgeVariant={getCategoryBadgeVariant}
          />
        </TabsContent>

        <TabsContent value="all">
          <VoucherTable
            vouchers={pendingVouchers}
            selectedVouchers={selectedVouchers}
            onVoucherSelection={handleVoucherSelection}
            onSelectAll={handleSelectAll}
            onApprove={(id) => processApproval(id, 'approve')}
            onReject={(id) => processApproval(id, 'reject')}
            actionLoading={actionLoading}
            formatCurrency={formatCurrency}
            getStatusBadgeVariant={getStatusBadgeVariant}
            getCategoryBadgeVariant={getCategoryBadgeVariant}
          />
        </TabsContent>
      </Tabs>

      {/* Bulk Approval Dialog */}
      <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approve' ? 'Bulk Approve' : 'Bulk Reject'} Vouchers
            </DialogTitle>
            <DialogDescription>
              You are about to {approvalAction} {selectedVouchers.length} voucher(s).
              {approvalAction === 'reject' && ' Please provide a reason for rejection.'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Textarea
              placeholder={
                approvalAction === 'approve'
                  ? 'Optional approval comments...'
                  : 'Reason for rejection (required)...'
              }
              value={approvalComments}
              onChange={(e) => setApprovalComments(e.target.value)}
              required={approvalAction === 'reject'}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowApprovalDialog(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={processBulkApproval}
              disabled={actionLoading || (approvalAction === 'reject' && !approvalComments.trim())}
              variant={approvalAction === 'approve' ? 'default' : 'destructive'}
            >
              {actionLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {approvalAction === 'approve' ? 'Approve' : 'Reject'} Selected
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// VoucherTable Component
interface VoucherTableProps {
  vouchers: PendingVoucher[];
  selectedVouchers: string[];
  onVoucherSelection: (voucherId: string, checked: boolean) => void;
  onSelectAll: (vouchers: PendingVoucher[], checked: boolean) => void;
  onApprove: (voucherId: string) => void;
  onReject: (voucherId: string) => void;
  actionLoading: boolean;
  formatCurrency: (amount: number) => string;
  getStatusBadgeVariant: (status: string) => any;
  getCategoryBadgeVariant: (category: string) => any;
}

function VoucherTable({
  vouchers,
  selectedVouchers,
  onVoucherSelection,
  onSelectAll,
  onApprove,
  onReject,
  actionLoading,
  formatCurrency,
  getStatusBadgeVariant,
  getCategoryBadgeVariant,
}: VoucherTableProps) {
  const allSelected = vouchers.length > 0 && vouchers.every(v => selectedVouchers.includes(v.id));
  const someSelected = vouchers.some(v => selectedVouchers.includes(v.id));

  if (vouchers.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">No vouchers pending approval</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Pending Vouchers ({vouchers.length})</CardTitle>
            <CardDescription>
              Vouchers requiring your approval
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={allSelected}
              onCheckedChange={(checked) => onSelectAll(vouchers, checked as boolean)}
              aria-label="Select all vouchers"
            />
            <span className="text-sm text-muted-foreground">Select All</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Select</TableHead>
              <TableHead>Voucher #</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Payee</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {vouchers.map((voucher) => (
              <TableRow key={voucher.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedVouchers.includes(voucher.id)}
                    onCheckedChange={(checked) => onVoucherSelection(voucher.id, checked as boolean)}
                    aria-label={`Select voucher ${voucher.voucherNumber}`}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  {voucher.voucherNumber}
                  {voucher.payrollRun && (
                    <div className="text-xs text-muted-foreground">
                      Payroll: {format(new Date(voucher.payrollRun.payPeriod.year, voucher.payrollRun.payPeriod.month - 1), 'MMM yyyy')}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(voucher.voucherType)}>
                    {voucher.voucherType.toUpperCase()}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={getCategoryBadgeVariant(voucher.voucherCategory)}>
                    {voucher.voucherCategory.toUpperCase()}
                  </Badge>
                </TableCell>
                <TableCell className="max-w-xs truncate">
                  {voucher.description}
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(voucher.totalAmount)}
                </TableCell>
                <TableCell>{voucher.payee || 'N/A'}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    {format(new Date(voucher.createdAt), 'dd/MM/yyyy')}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    by {voucher.createdBy.name}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => onApprove(voucher.id)}
                      disabled={actionLoading}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approve
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onReject(voucher.id)}
                      disabled={actionLoading}
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Reject
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

"use client"

import React, { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, Check, ChevronsUpDown, FileText, Users, Building, Receipt } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useErrorHandler } from "@/hooks/use-error-handler";
import { ErrorOverlay } from "@/components/errors/error-overlay";

// Enhanced voucher types with specific categories
const voucherTypes = [
  { 
    label: "Payroll Voucher (Salary)", 
    value: "payroll_salary",
    icon: Users,
    description: "Create voucher for monthly salary payments"
  },
  { 
    label: "General Payment Voucher", 
    value: "payment_general",
    icon: FileText,
    description: "General payments to suppliers and vendors"
  },
  { 
    label: "Expense Voucher", 
    value: "payment_expense",
    icon: Receipt,
    description: "Employee expense reimbursements"
  },
  { 
    label: "Utility Payment Voucher", 
    value: "payment_utility",
    icon: Building,
    description: "Utility bills and facility payments"
  },
  { 
    label: "Receipt Voucher", 
    value: "receipt",
    icon: FileText,
    description: "Record payments received"
  },
];



// Generate month options for the last 12 months
const generateMonthOptions = () => {
  const months = [];
  const currentDate = new Date();
  
  for (let i = 0; i < 12; i++) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    months.push({
      value: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`,
      label: format(date, 'MMMM yyyy'),
      month: date.getMonth() + 1,
      year: date.getFullYear()
    });
  }
  
  return months;
};

// Define the form schema
const enhancedVoucherFormSchema = z.object({
  voucherType: z.string().min(1, "Please select a voucher type"),
  date: z.date({
    required_error: "A date is required",
  }),
  description: z.string().min(2, "Description must be at least 2 characters"),
  totalAmount: z.coerce.number().positive("Total amount must be positive"),
  fiscalYear: z.string().min(1, "Fiscal year is required"),
  payee: z.string().optional(),
  paymentMethod: z.string().optional(),
  notes: z.string().optional(),
  // Payroll-specific fields
  selectedMonth: z.string().optional(),
  payrollRunId: z.string().optional(),
  // General payment fields
  supplierName: z.string().optional(),
  invoiceNumber: z.string().optional(),
  // Expense fields
  employeeName: z.string().optional(),
  expenseCategory: z.string().optional(),
});

type EnhancedVoucherFormValues = z.infer<typeof enhancedVoucherFormSchema>;

// Default values
const defaultValues: Partial<EnhancedVoucherFormValues> = {
  date: new Date(),
  fiscalYear: "2024-2025",
  totalAmount: 0,
  description: "",
  notes: "",
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

interface EnhancedVoucherFormProps {
  onSubmit?: (data: EnhancedVoucherFormValues) => void;
  initialData?: Partial<EnhancedVoucherFormValues>;
}

export function EnhancedVoucherForm({ onSubmit, initialData }: EnhancedVoucherFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPayrollRun, setSelectedPayrollRun] = useState<any>(null);
  const [employeeRecords, setEmployeeRecords] = useState<any[]>([]);
  const [isLoadingPayrollData, setIsLoadingPayrollData] = useState(false);
  const [monthOptions] = useState(generateMonthOptions());

  // Error handling
  const { error, isErrorOpen, handleApiError, hideError, handleError } = useErrorHandler();

  // Initialize the form
  const form = useForm<EnhancedVoucherFormValues>({
    resolver: zodResolver(enhancedVoucherFormSchema),
    defaultValues: initialData || defaultValues,
  });

  const watchedVoucherType = form.watch("voucherType");
  const watchedSelectedMonth = form.watch("selectedMonth");

  // Effect to handle payroll run selection based on month
  useEffect(() => {
    const fetchPayrollData = async () => {
      if (watchedVoucherType === "payroll_salary" && watchedSelectedMonth) {
        const [year, month] = watchedSelectedMonth.split('-').map(Number);

        setIsLoadingPayrollData(true);
        setSelectedPayrollRun(null);
        setEmployeeRecords([]);

        try {
          const response = await fetch('/api/payroll/runs/approved', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              month,
              year,
              includeEmployees: true
            })
          });

          if (!response.ok) {
            await handleApiError(response);
            return;
          }

          const result = await response.json();

          if (result.success && result.data.payrollRun) {
            const { payrollRun, employeeRecords: employees } = result.data;

            // Check if payroll run is approved
            if (payrollRun.status !== 'approved') {
              // Create custom error for unapproved payroll run
              const unapprovedError = {
                id: `payroll-unapproved-${Date.now()}`,
                type: 'BUSINESS_LOGIC',
                severity: 'MEDIUM' as const,
                code: 'PAYROLL_NOT_APPROVED',
                message: `Payroll run for ${format(new Date(year, month - 1), 'MMMM yyyy')} is not approved`,
                userMessage: `The payroll run for ${format(new Date(year, month - 1), 'MMMM yyyy')} has not been approved yet and cannot be used for voucher creation.`,
                timestamp: new Date().toISOString(),
                details: `Payroll run status: ${payrollRun.status}. Only approved payroll runs can be used for voucher creation.`,
                suggestions: [
                  'Wait for the payroll run to be approved by HR',
                  'Contact HR to approve the payroll run',
                  'Select a different month with an approved payroll run'
                ],
                actions: [
                  {
                    label: 'Select Different Month',
                    action: 'select-different-month',
                    type: 'button' as const,
                    variant: 'primary' as const
                  },
                  {
                    label: 'View Payroll Runs',
                    action: 'view-payroll-runs',
                    type: 'link' as const,
                    variant: 'secondary' as const,
                    url: '/dashboard/payroll/run'
                  }
                ]
              };

              hideError(); // Clear any existing errors
              setTimeout(() => {
                handleError(unapprovedError);
              }, 100);
              return;
            }

            // Check if voucher already exists
            if (payrollRun.voucherStatus === 'created' || payrollRun.voucherId) {
              const voucherExistsError = {
                id: `voucher-exists-${Date.now()}`,
                type: 'BUSINESS_LOGIC',
                severity: 'MEDIUM' as const,
                code: 'VOUCHER_ALREADY_EXISTS',
                message: `Voucher already exists for ${format(new Date(year, month - 1), 'MMMM yyyy')} payroll`,
                userMessage: `A voucher has already been created for the ${format(new Date(year, month - 1), 'MMMM yyyy')} payroll run.`,
                timestamp: new Date().toISOString(),
                details: `Voucher ID: ${payrollRun.voucherId || 'Unknown'}. Each payroll run can only have one voucher.`,
                suggestions: [
                  'Select a different month without an existing voucher',
                  'View the existing voucher details',
                  'Contact finance team if you need to modify the existing voucher'
                ],
                actions: [
                  {
                    label: 'Select Different Month',
                    action: 'select-different-month',
                    type: 'button' as const,
                    variant: 'primary' as const
                  },
                  {
                    label: 'View Vouchers',
                    action: 'view-vouchers',
                    type: 'link' as const,
                    variant: 'secondary' as const,
                    url: '/dashboard/accounting/vouchers/management'
                  }
                ]
              };

              hideError();
              setTimeout(() => {
                handleError(voucherExistsError);
              }, 100);
              return;
            }

            setSelectedPayrollRun(payrollRun);
            setEmployeeRecords(employees || []);

            // Auto-populate form fields
            form.setValue("payrollRunId", payrollRun.id);
            form.setValue("totalAmount", payrollRun.totalNetSalary);
            form.setValue("description", `Salary payment for ${format(new Date(year, month - 1), 'MMMM yyyy')} - ${payrollRun.totalEmployees} employees`);
            form.setValue("payee", "Employees - Salary Payment");
            form.setValue("paymentMethod", "bank_transfer");

            toast({
              title: "Payroll Data Loaded",
              description: `Found approved payroll run with ${payrollRun.totalEmployees} employees for ${format(new Date(year, month - 1), 'MMMM yyyy')}`,
            });
          } else {
            // No payroll run found for this period
            const noPayrollError = {
              id: `no-payroll-${Date.now()}`,
              type: 'NOT_FOUND',
              severity: 'MEDIUM' as const,
              code: 'PAYROLL_RUN_NOT_FOUND',
              message: `No payroll run found for ${format(new Date(year, month - 1), 'MMMM yyyy')}`,
              userMessage: `No payroll run was found for ${format(new Date(year, month - 1), 'MMMM yyyy')}. A payroll run must be created and approved before creating a voucher.`,
              timestamp: new Date().toISOString(),
              details: 'This usually means no payroll was processed for this period, or the payroll run was deleted.',
              suggestions: [
                'Create a payroll run for this period first',
                'Select a different month with an existing payroll run',
                'Contact HR to verify payroll processing for this period'
              ],
              actions: [
                {
                  label: 'Create Payroll Run',
                  action: 'create-payroll-run',
                  type: 'link' as const,
                  variant: 'primary' as const,
                  url: '/dashboard/payroll/run/create'
                },
                {
                  label: 'Select Different Month',
                  action: 'select-different-month',
                  type: 'button' as const,
                  variant: 'secondary' as const
                }
              ]
            };

            hideError();
            setTimeout(() => {
              handleError(noPayrollError);
            }, 100);
          }
        } catch (error) {
          console.error('Error fetching payroll data:', error);
          handleError(error, {
            context: 'payroll-voucher-creation',
            month,
            year,
            operation: 'fetch-payroll-data'
          });
        } finally {
          setIsLoadingPayrollData(false);
        }
      }
    };

    fetchPayrollData();
  }, [watchedVoucherType, watchedSelectedMonth, form, handleApiError, handleError, hideError]);

  // Handle form submission
  const handleSubmit = async (data: EnhancedVoucherFormValues) => {
    setIsSubmitting(true);
    try {
      // Enhance data with payroll information if it's a payroll voucher
      const enhancedData = {
        ...data,
        ...(data.voucherType === "payroll_salary" && selectedPayrollRun && {
          payrollRunData: selectedPayrollRun,
          employeeRecords: employeeRecords,
          payrollSummary: {
            totalEmployees: selectedPayrollRun.totalEmployees,
            totalGrossSalary: selectedPayrollRun.totalGrossSalary,
            totalDeductions: selectedPayrollRun.totalDeductions,
            totalTax: selectedPayrollRun.totalTax,
            totalNetSalary: selectedPayrollRun.totalNetSalary
          }
        })
      };

      if (onSubmit) {
        // Call the parent's onSubmit function and wait for it to complete
        await onSubmit(enhancedData);

        // Only reset form after successful submission
        form.reset(defaultValues);
        setSelectedPayrollRun(null);
        setEmployeeRecords([]);
        setIsLoadingPayrollData(false);
      } else {
        // If no onSubmit prop, simulate voucher creation
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

        toast({
          title: "Voucher Created",
          description: `${getVoucherTypeLabel(data.voucherType)} for ${formatCurrency(data.totalAmount)} has been created.`,
        });

        // Reset form after successful creation
        form.reset(defaultValues);
        setSelectedPayrollRun(null);
        setEmployeeRecords([]);
        setIsLoadingPayrollData(false);
      }
    } catch (error) {
      console.error("Error submitting voucher:", error);
      toast({
        title: "Error",
        description: "There was an error creating the voucher. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get voucher type label
  const getVoucherTypeLabel = (value: string) => {
    return voucherTypes.find(type => type.value === value)?.label || value;
  };

  // Handle error actions
  const handleErrorAction = (action: string, data?: Record<string, any>) => {
    switch (action) {
      case 'select-different-month':
        // Clear the current month selection
        form.setValue("selectedMonth", "");
        setSelectedPayrollRun(null);
        setEmployeeRecords([]);
        hideError();
        break;
      case 'retry':
        // Retry the current operation
        if (watchedSelectedMonth) {
          // Re-trigger the payroll data fetch
          const [year, month] = watchedSelectedMonth.split('-').map(Number);
          // The useEffect will handle the retry automatically
        }
        hideError();
        break;
      default:
        // For link actions, the ErrorOverlay handles navigation
        hideError();
        break;
    }
  };

  // Render voucher type specific fields
  const renderTypeSpecificFields = () => {
    switch (watchedVoucherType) {
      case "payroll_salary":
        return (
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="selectedMonth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Pay Period</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoadingPayrollData}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pay period" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {monthOptions.map((month) => (
                        <SelectItem key={month.value} value={month.value}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isLoadingPayrollData && (
              <Card className="border-blue-200">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm text-muted-foreground">Loading payroll data...</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {selectedPayrollRun && !isLoadingPayrollData && (
              <div className="space-y-4">
                <Card className="border-blue-200">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Payroll Run Summary
                    </CardTitle>
                    <CardDescription>
                      {selectedPayrollRun.name} - {selectedPayrollRun.totalEmployees} employees
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Gross Salary</p>
                        <p className="text-lg font-bold">{formatCurrency(selectedPayrollRun.totalGrossSalary)}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Total Deductions</p>
                        <p className="text-lg font-bold text-red-600">-{formatCurrency(selectedPayrollRun.totalDeductions)}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Tax Deductions</p>
                        <p className="text-lg font-bold text-red-600">-{formatCurrency(selectedPayrollRun.totalTax)}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Net Salary</p>
                        <p className="text-xl font-bold text-green-600">{formatCurrency(selectedPayrollRun.totalNetSalary)}</p>
                      </div>
                    </div>

                    <Separator className="my-4" />

                    <div className="flex items-center justify-between text-sm">
                      <div>
                        <Badge variant="default" className="mr-2">Approved</Badge>
                        <span className="text-muted-foreground">
                          by {selectedPayrollRun.approvedBy?.name || 'System'} on {format(new Date(selectedPayrollRun.approvedAt), 'PPP')}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Employee Records */}
                {employeeRecords.length > 0 && (
                  <Card className="border-green-200">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Employee Payees ({employeeRecords.length})
                      </CardTitle>
                      <CardDescription>
                        Employees included in this payroll voucher
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-64 overflow-y-auto">
                        <div className="grid grid-cols-1 gap-3">
                          {employeeRecords.map((record, index) => {
                            // Construct full name from firstName and lastName
                            const fullName = record.employee.fullName ||
                              `${record.employee.firstName || ''} ${record.employee.lastName || ''}`.trim() ||
                              'Unknown Employee';

                            return (
                              <div key={record._id} className="flex items-center justify-between p-3 border rounded-lg">
                                <div className="flex-1">
                                  <p className="font-medium text-sm">{fullName}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {record.employee.employeeNumber} • {record.employee.department}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {record.employee.position} • {record.payrollData.paymentMethod?.replace('_', ' ').toUpperCase()}
                                  </p>
                                </div>
                                <div className="text-right">
                                  <p className="font-bold text-sm text-green-600">
                                    {formatCurrency(record.payrollData.netSalary)}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    Net Pay
                                  </p>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {employeeRecords.length > 6 && (
                        <div className="mt-3 pt-3 border-t">
                          <p className="text-sm text-muted-foreground text-center">
                            Showing {Math.min(employeeRecords.length, 6)} of {employeeRecords.length} employees
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        );

      case "payment_general":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="supplierName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Supplier/Vendor Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter supplier name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="invoiceNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invoice Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter invoice number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        );

      case "payment_expense":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="employeeName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employee Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter employee name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="expenseCategory"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expense Category</FormLabel>
                  <FormControl>
                    <Input placeholder="Travel, Training, etc." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Create Voucher</CardTitle>
          <CardDescription>
            Select voucher type and fill in the required information
          </CardDescription>
        </CardHeader>
        <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Voucher Type Selection */}
            <FormField
              control={form.control}
              name="voucherType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Voucher Type</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      // Reset type-specific fields when changing type
                      form.setValue("selectedMonth", "");
                      form.setValue("payrollRunId", "");
                      form.setValue("supplierName", "");
                      form.setValue("invoiceNumber", "");
                      form.setValue("employeeName", "");
                      form.setValue("expenseCategory", "");
                      setSelectedPayrollRun(null);
                      setEmployeeRecords([]);
                      hideError(); // Clear any existing errors
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="h-auto p-4">
                        <SelectValue placeholder="Select voucher type">
                          {field.value && (
                            <div className="flex items-center gap-3">
                              {(() => {
                                const selectedType = voucherTypes.find(type => type.value === field.value);
                                const Icon = selectedType?.icon || FileText;
                                return (
                                  <>
                                    <Icon className="h-5 w-5" />
                                    <div className="text-left">
                                      <div className="font-medium">{selectedType?.label}</div>
                                      <div className="text-sm text-muted-foreground">{selectedType?.description}</div>
                                    </div>
                                  </>
                                );
                              })()}
                            </div>
                          )}
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {voucherTypes.map((type) => {
                        const Icon = type.icon;
                        return (
                          <SelectItem key={type.value} value={type.value} className="p-3">
                            <div className="flex items-center gap-3">
                              <Icon className="h-5 w-5" />
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-sm text-muted-foreground">{type.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Type-specific fields */}
            {watchedVoucherType && (
              <div className="space-y-4">
                <Separator />
                <h3 className="text-lg font-medium">
                  {getVoucherTypeLabel(watchedVoucherType)} Details
                </h3>
                {renderTypeSpecificFields()}
              </div>
            )}

            {/* Common fields */}
            {watchedVoucherType && (
              <>
                <Separator />
                <h3 className="text-lg font-medium">General Information</h3>
                
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {/* Date Field */}
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Total Amount Field */}
                  <FormField
                    control={form.control}
                    name="totalAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total Amount (MWK)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            disabled={watchedVoucherType === "payroll_salary" && selectedPayrollRun}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Description Field */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter a detailed description of the voucher"
                          className="resize-none"
                          {...field}
                          disabled={watchedVoucherType === "payroll_salary" && selectedPayrollRun}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {/* Payee Field */}
                  <FormField
                    control={form.control}
                    name="payee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payee</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Who will be paid"
                            {...field}
                            disabled={watchedVoucherType === "payroll_salary" && selectedPayrollRun}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Payment Method Field */}
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Bank transfer, cash, etc."
                            {...field}
                            disabled={watchedVoucherType === "payroll_salary" && selectedPayrollRun}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Fiscal Year Field */}
                  <FormField
                    control={form.control}
                    name="fiscalYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fiscal Year</FormLabel>
                        <FormControl>
                          <Input placeholder="2024-2025" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Notes Field */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <div className="flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      form.reset(defaultValues);
                      setSelectedPayrollRun(null);
                      setEmployeeRecords([]);
                      setIsLoadingPayrollData(false);
                    }}
                  >
                    Reset Form
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || (watchedVoucherType === "payroll_salary" && !selectedPayrollRun)}
                    onClick={form.handleSubmit(handleSubmit)}
                  >
                    {isSubmitting ? "Creating..." : "Create Voucher"}
                  </Button>
                </div>
              </>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>

    {/* Error Overlay */}
    <ErrorOverlay
      error={error}
      isOpen={isErrorOpen}
      onClose={hideError}
      onAction={handleErrorAction}
    />
    </>
  );
}

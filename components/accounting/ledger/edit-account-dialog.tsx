"use client";

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';
import { Account, useChartOfAccounts, UpdateAccountData } from '@/hooks/useChartOfAccounts';

// Form schema
const editAccountSchema = z.object({
  accountNumber: z.string().min(1, 'Account number is required'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense'], {
    required_error: 'Please select an account type',
  }),
  subtype: z.string().optional(),
  description: z.string().optional(),
  parentAccount: z.string().optional(),
  isActive: z.boolean().default(true),
  isLocked: z.boolean().default(false),
  lockReason: z.string().optional(),
});

type EditAccountFormValues = z.infer<typeof editAccountSchema>;

interface EditAccountDialogProps {
  account: Account | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditAccountDialog({ account, open, onOpenChange }: EditAccountDialogProps) {
  const { accounts, updateAccountAsync, isUpdating } = useChartOfAccounts();

  const form = useForm<EditAccountFormValues>({
    resolver: zodResolver(editAccountSchema),
    defaultValues: {
      accountNumber: '',
      name: '',
      type: 'asset',
      subtype: '',
      description: '',
      parentAccount: '',
      isActive: true,
      isLocked: false,
      lockReason: '',
    },
  });

  // Reset form when account changes
  useEffect(() => {
    if (account) {
      form.reset({
        accountNumber: account.accountNumber || '',
        name: account.name || '',
        type: account.type,
        subtype: account.subtype || '',
        description: account.description || '',
        parentAccount: account.parentAccount?._id || 'none',
        isActive: account.isActive,
        isLocked: account.isLocked || false,
        lockReason: account.lockReason || '',
      });
    }
  }, [account, form]);

  // Get parent account options (exclude current account and its children)
  const getParentAccountOptions = () => {
    if (!account) return [];
    
    return accounts
      .filter(acc => {
        // Exclude self
        if (acc.id === account.id) return false;
        
        // Exclude children (accounts that have this account as parent)
        if (acc.parentAccount?._id === account.id) return false;
        
        // Only allow nesting up to 3 levels
        const level = acc.level || 0;
        return level < 3;
      })
      .map(acc => ({
        value: acc.id,
        label: `${acc.accountNumber} - ${acc.name}`,
        level: acc.level || 0,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  };

  const onSubmit = async (values: EditAccountFormValues) => {
    if (!account) return;

    try {
      const updateData: UpdateAccountData = {
        accountNumber: values.accountNumber,
        name: values.name,
        type: values.type,
        subtype: values.subtype || undefined,
        description: values.description || undefined,
        parentAccount: values.parentAccount && values.parentAccount !== "none" ? values.parentAccount : undefined,
        isActive: values.isActive,
        isLocked: values.isLocked,
        lockReason: values.lockReason || undefined,
      };

      await updateAccountAsync({ id: account.id, data: updateData });
      onOpenChange(false);
    } catch (error) {
      // Error is handled by the hook
      console.error('Failed to update account:', error);
    }
  };

  const parentAccountOptions = getParentAccountOptions();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Account</DialogTitle>
          <DialogDescription>
            Update the account details. Changes will be reflected across the system.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Number</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 1100" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Cash at Bank" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Type</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="asset">Asset</SelectItem>
                        <SelectItem value="liability">Liability</SelectItem>
                        <SelectItem value="equity">Equity</SelectItem>
                        <SelectItem value="revenue">Revenue</SelectItem>
                        <SelectItem value="expense">Expense</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="subtype"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtype (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Current Asset" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="parentAccount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Account (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select parent account" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No Parent Account</SelectItem>
                      {parentAccountOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <span style={{ paddingLeft: `${option.level * 12}px` }}>
                            {option.label}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select a parent account to create a hierarchical structure.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter a description for this account"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Inactive accounts won't appear in transactions
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isLocked"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Locked</FormLabel>
                      <FormDescription>
                        Locked accounts cannot be modified
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {form.watch('isLocked') && (
              <FormField
                control={form.control}
                name="lockReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lock Reason</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Reason for locking this account"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdating}>
                {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Account
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

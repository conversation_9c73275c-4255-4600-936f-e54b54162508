"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Filter, X, RotateCcw } from 'lucide-react';

// Form schema
const advancedFilterSchema = z.object({
  accountTypes: z.array(z.string()).optional(),
  status: z.enum(['all', 'active', 'inactive']).default('all'),
  balanceRange: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  parentAccount: z.string().optional(),
  costCenter: z.string().optional(),
  fiscalYear: z.string().optional(),
  dateRange: z.object({
    startDate: z.date().optional(),
    endDate: z.date().optional(),
  }).optional(),
  searchTerm: z.string().optional(),
  tags: z.array(z.string()).optional(),
  hasTransactions: z.enum(['all', 'with', 'without']).default('all'),
  isLocked: z.enum(['all', 'locked', 'unlocked']).default('all'),
});

type AdvancedFilterFormValues = z.infer<typeof advancedFilterSchema>;

export interface FilterCriteria extends AdvancedFilterFormValues {
  // Additional computed properties
  hasActiveFilters: boolean;
}

interface AdvancedFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyFilters: (filters: FilterCriteria) => void;
  currentFilters?: Partial<FilterCriteria>;
  accounts?: Array<{ id: string; accountNumber: string; name: string; type: string }>;
  costCenters?: Array<{ id: string; code: string; name: string }>;
}

export function AdvancedFilterDialog({
  open,
  onOpenChange,
  onApplyFilters,
  currentFilters = {},
  accounts = [],
  costCenters = [],
}: AdvancedFilterDialogProps) {
  const [selectedTags, setSelectedTags] = useState<string[]>(currentFilters.tags || []);
  const [tagInput, setTagInput] = useState('');

  const form = useForm<AdvancedFilterFormValues>({
    resolver: zodResolver(advancedFilterSchema),
    defaultValues: {
      accountTypes: currentFilters.accountTypes || [],
      status: currentFilters.status || 'all',
      balanceRange: currentFilters.balanceRange || {},
      parentAccount: currentFilters.parentAccount || '',
      costCenter: currentFilters.costCenter || '',
      fiscalYear: currentFilters.fiscalYear || '',
      dateRange: currentFilters.dateRange || {},
      searchTerm: currentFilters.searchTerm || '',
      tags: currentFilters.tags || [],
      hasTransactions: currentFilters.hasTransactions || 'all',
      isLocked: currentFilters.isLocked || 'all',
    },
  });

  const accountTypeOptions = [
    { value: 'asset', label: 'Assets' },
    { value: 'liability', label: 'Liabilities' },
    { value: 'equity', label: 'Equity' },
    { value: 'revenue', label: 'Revenue' },
    { value: 'expense', label: 'Expenses' },
  ];

  const fiscalYearOptions = [
    { value: '2024', label: '2024' },
    { value: '2023', label: '2023' },
    { value: '2022', label: '2022' },
    { value: '2021', label: '2021' },
  ];

  const parentAccountOptions = accounts
    .filter(account => !account.type || ['asset', 'liability', 'equity', 'revenue', 'expense'].includes(account.type))
    .map(account => ({
      value: account.id,
      label: `${account.accountNumber} - ${account.name}`,
    }));

  const costCenterOptions = costCenters.map(center => ({
    value: center.id,
    label: `${center.code} - ${center.name}`,
  }));

  const addTag = () => {
    if (tagInput.trim() && !selectedTags.includes(tagInput.trim())) {
      const newTags = [...selectedTags, tagInput.trim()];
      setSelectedTags(newTags);
      form.setValue('tags', newTags);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = selectedTags.filter(tag => tag !== tagToRemove);
    setSelectedTags(newTags);
    form.setValue('tags', newTags);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const onSubmit = (values: AdvancedFilterFormValues) => {
    // Check if any filters are active
    const hasActiveFilters = Boolean(
      (values.accountTypes && values.accountTypes.length > 0) ||
      values.status !== 'all' ||
      (values.balanceRange && (values.balanceRange.min !== undefined || values.balanceRange.max !== undefined)) ||
      values.parentAccount ||
      values.costCenter ||
      values.fiscalYear ||
      (values.dateRange && (values.dateRange.startDate || values.dateRange.endDate)) ||
      values.searchTerm ||
      (values.tags && values.tags.length > 0) ||
      values.hasTransactions !== 'all' ||
      values.isLocked !== 'all'
    );

    const filterCriteria: FilterCriteria = {
      ...values,
      hasActiveFilters,
    };

    onApplyFilters(filterCriteria);
    onOpenChange(false);
  };

  const resetFilters = () => {
    form.reset({
      accountTypes: [],
      status: 'all',
      balanceRange: {},
      parentAccount: '',
      costCenter: '',
      fiscalYear: '',
      dateRange: {},
      searchTerm: '',
      tags: [],
      hasTransactions: 'all',
      isLocked: 'all',
    });
    setSelectedTags([]);
    setTagInput('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
          </DialogTitle>
          <DialogDescription>
            Apply advanced filters to narrow down your chart of accounts view.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Account Types */}
            <FormField
              control={form.control}
              name="accountTypes"
              render={() => (
                <FormItem>
                  <FormLabel>Account Types</FormLabel>
                  <div className="grid grid-cols-3 gap-2">
                    {accountTypeOptions.map(option => (
                      <FormField
                        key={option.value}
                        control={form.control}
                        name="accountTypes"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(option.value)}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, option.value]);
                                  } else {
                                    field.onChange(
                                      currentValue.filter(value => value !== option.value)
                                    );
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {option.label}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Status and Basic Filters */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">All Accounts</SelectItem>
                        <SelectItem value="active">Active Only</SelectItem>
                        <SelectItem value="inactive">Inactive Only</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hasTransactions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transaction History</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">All Accounts</SelectItem>
                        <SelectItem value="with">With Transactions</SelectItem>
                        <SelectItem value="without">Without Transactions</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Balance Range */}
            <FormItem>
              <FormLabel>Balance Range</FormLabel>
              <div className="grid grid-cols-2 gap-2">
                <FormField
                  control={form.control}
                  name="balanceRange.min"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Minimum balance"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="balanceRange.max"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Maximum balance"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <FormDescription>
                Filter accounts by their current balance range
              </FormDescription>
            </FormItem>

            <Separator />

            {/* Hierarchy and Organization */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="parentAccount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parent Account</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No Filter</SelectItem>
                        {parentAccountOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="costCenter"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Center</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select cost center" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No Filter</SelectItem>
                        {costCenterOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Date and Fiscal Year */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fiscalYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Year</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select fiscal year" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">All Years</SelectItem>
                        {fiscalYearOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isLocked"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lock Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select lock status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">All Accounts</SelectItem>
                        <SelectItem value="locked">Locked Only</SelectItem>
                        <SelectItem value="unlocked">Unlocked Only</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Search Term */}
            <FormField
              control={form.control}
              name="searchTerm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Search Term</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Search in account number, name, or description"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Search across account numbers, names, and descriptions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <FormItem>
              <FormLabel>Tags</FormLabel>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add tag and press Enter"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    Add
                  </Button>
                </div>
                {selectedTags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {selectedTags.map(tag => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              <FormDescription>
                Filter accounts by tags
              </FormDescription>
            </FormItem>

            <DialogFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={resetFilters}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset Filters
              </Button>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  <Filter className="mr-2 h-4 w-4" />
                  Apply Filters
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

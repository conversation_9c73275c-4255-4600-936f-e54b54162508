"use client";

import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertTriangle, Info } from 'lucide-react';
import { Account, useChartOfAccounts } from '@/hooks/useChartOfAccounts';

interface DeleteAccountDialogProps {
  account: Account | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteAccountDialog({ account, open, onOpenChange }: DeleteAccountDialogProps) {
  const { accounts, deleteAccountAsync, isDeleting } = useChartOfAccounts();
  const [deleteType, setDeleteType] = useState<'soft' | 'hard'>('soft');

  if (!account) return null;

  // Check for child accounts
  const childAccounts = accounts.filter(acc => acc.parentAccount?._id === account.id);
  const hasChildren = childAccounts.length > 0;

  // Check if account has transactions (mock check - in real implementation, this would be an API call)
  const hasTransactions = account.balance !== 0; // Simple check based on balance

  const handleDelete = async () => {
    try {
      await deleteAccountAsync(account.id);
      onOpenChange(false);
    } catch (error) {
      // Error is handled by the hook
      console.error('Failed to delete account:', error);
    }
  };

  const canHardDelete = !hasChildren && !hasTransactions;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-[500px]">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Delete Account
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p>
                You are about to delete the following account:
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{account.name}</span>
                    <Badge variant={account.type === 'asset' ? 'default' : 
                      account.type === 'liability' ? 'secondary' :
                      account.type === 'equity' ? 'outline' :
                      account.type === 'revenue' ? 'default' : 'destructive'}>
                      {account.type.charAt(0).toUpperCase() + account.type.slice(1)}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <div>Account Number: {account.accountNumber}</div>
                    {account.description && (
                      <div>Description: {account.description}</div>
                    )}
                    {account.balance !== 0 && (
                      <div>Current Balance: {account.balance.toLocaleString()}</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Warnings and Information */}
              <div className="space-y-3">
                {hasChildren && (
                  <div className="flex gap-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium text-yellow-800">Child Accounts Found</div>
                      <div className="text-yellow-700">
                        This account has {childAccounts.length} child account(s). 
                        Deleting this account will also affect:
                      </div>
                      <ul className="mt-2 space-y-1">
                        {childAccounts.slice(0, 3).map(child => (
                          <li key={child.id} className="text-yellow-700">
                            • {child.accountNumber} - {child.name}
                          </li>
                        ))}
                        {childAccounts.length > 3 && (
                          <li className="text-yellow-700">
                            • ... and {childAccounts.length - 3} more
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                )}

                {hasTransactions && (
                  <div className="flex gap-3 p-3 rounded-lg bg-red-50 border border-red-200">
                    <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium text-red-800">Account Has Transactions</div>
                      <div className="text-red-700">
                        This account has a non-zero balance ({account.balance.toLocaleString()}), 
                        indicating it has transaction history. Deleting it may affect financial reports.
                      </div>
                    </div>
                  </div>
                )}

                {!hasChildren && !hasTransactions && (
                  <div className="flex gap-3 p-3 rounded-lg bg-green-50 border border-green-200">
                    <Info className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium text-green-800">Safe to Delete</div>
                      <div className="text-green-700">
                        This account has no child accounts or transaction history. 
                        It can be safely deleted without affecting other data.
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Delete Options */}
              <div className="space-y-3">
                <div className="text-sm font-medium">Deletion Options:</div>
                
                <div className="space-y-2">
                  <label className="flex items-start gap-3 p-3 rounded-lg border cursor-pointer hover:bg-muted/50">
                    <input
                      type="radio"
                      name="deleteType"
                      value="soft"
                      checked={deleteType === 'soft'}
                      onChange={(e) => setDeleteType(e.target.value as 'soft')}
                      className="mt-1"
                    />
                    <div className="text-sm">
                      <div className="font-medium">Deactivate Account (Recommended)</div>
                      <div className="text-muted-foreground">
                        Mark the account as inactive. It will be hidden from new transactions 
                        but preserved for historical reporting.
                      </div>
                    </div>
                  </label>

                  <label className={`flex items-start gap-3 p-3 rounded-lg border cursor-pointer hover:bg-muted/50 ${
                    !canHardDelete ? 'opacity-50 cursor-not-allowed' : ''
                  }`}>
                    <input
                      type="radio"
                      name="deleteType"
                      value="hard"
                      checked={deleteType === 'hard'}
                      onChange={(e) => setDeleteType(e.target.value as 'hard')}
                      disabled={!canHardDelete}
                      className="mt-1"
                    />
                    <div className="text-sm">
                      <div className="font-medium">Permanently Delete</div>
                      <div className="text-muted-foreground">
                        Completely remove the account from the system. 
                        {!canHardDelete && ' (Not available - account has dependencies)'}
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <div className="text-sm text-muted-foreground">
                <strong>Warning:</strong> This action cannot be undone. 
                {deleteType === 'hard' 
                  ? ' The account will be permanently removed from the system.'
                  : ' The account will be marked as inactive and hidden from active use.'
                }
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {deleteType === 'soft' ? 'Deactivate Account' : 'Delete Permanently'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

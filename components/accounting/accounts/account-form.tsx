// components/accounting/accounts/account-form.tsx
"use client"

import { useState, useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { useAccountingStore } from '@/lib/frontend/accountingStore'
import { Loader2 } from 'lucide-react'

// Define the form schema
const accountFormSchema = z.object({
  accountNumber: z.string().min(1, 'Account number is required'),
  name: z.string().min(3, 'Name must be at least 3 characters'),
  type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense'], {
    required_error: 'Please select an account type',
  }),
  subtype: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  parentAccount: z.string().optional(),
  costCenter: z.string().optional(),
  tags: z.string().optional(),
  fiscalYear: z.string().optional(),
  reportingGroup: z.string().optional(),
})

type AccountFormValues = z.infer<typeof accountFormSchema>

interface AccountFormProps {
  account?: {
    _id: string;
    accountNumber: string;
    name: string;
    type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
    subtype?: string;
    description?: string;
    isActive: boolean;
    parentAccount?: { _id: string };
    costCenter?: { _id: string };
    tags?: string[];
    fiscalYear?: string;
    reportingGroup?: string;
  }
  onSuccess?: () => void
  onCancel?: () => void
}

export function AccountForm({ account, onSuccess, onCancel }: AccountFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [parentAccounts, setParentAccounts] = useState<Array<{
    _id: string;
    accountNumber: string;
    name: string;
  }>>([])
  const [costCenters, setCostCenters] = useState<Array<{
    _id: string;
    code: string;
    name: string;
  }>>([])

  const { fetchAccounts, createAccount, updateAccount, isLoadingAccounts } = useAccountingStore()

  // Initialize form with default values or existing account data
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema) as any, // Type assertion to fix resolver type issue
    defaultValues: account
      ? {
          accountNumber: account.accountNumber,
          name: account.name,
          type: account.type,
          subtype: account.subtype || '',
          description: account.description || '',
          isActive: account.isActive,
          parentAccount: account.parentAccount?._id || '',
          costCenter: account.costCenter?._id || '',
          tags: account.tags?.join(', ') || '',
          fiscalYear: account.fiscalYear || '',
          reportingGroup: account.reportingGroup || '',
        }
      : {
          accountNumber: '',
          name: '',
          type: 'asset' as const,
          subtype: '',
          description: '',
          isActive: true,
          parentAccount: '',
          costCenter: '',
          tags: '',
          fiscalYear: '',
          reportingGroup: '',
        },
  })

  // Fetch parent accounts and cost centers on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch accounts for parent account dropdown
        const response = await fetch('/api/accounting/accounts')
        const data = await response.json()
        setParentAccounts(data.accounts || [])

        // Fetch cost centers for cost center dropdown
        // This would be replaced with actual cost center API endpoint
        const costCenterResponse = await fetch('/api/accounting/cost-centers')
        const costCenterData = await costCenterResponse.json()
        setCostCenters(costCenterData.costCenters || [])
      } catch (error: unknown) {
        console.error('Error fetching data:', error)
        const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'Unknown error';
        toast({
          title: 'Error',
          description: `Failed to load form data: ${errorMessage}`,
          variant: 'destructive',
        })
      }
    }

    fetchData()
  }, [])

  // Handle form submission
  async function onSubmit(values: AccountFormValues) {
    setIsSubmitting(true)

    try {
      // Process tags from comma-separated string to array
      const processedValues = {
        ...values,
        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()) : undefined,
      }

      if (account) {
        // Update existing account
        await updateAccount(account._id, processedValues)
        toast({
          title: 'Account Updated',
          description: 'The account has been updated successfully',
        })
      } else {
        // Create new account
        await createAccount(processedValues)
        toast({
          title: 'Account Created',
          description: 'The account has been created successfully',
        })
      }

      // Refresh accounts list
      fetchAccounts()

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error: unknown) {
      console.error('Error saving account:', error)
      const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to save account: ${errorMessage}`,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{account ? 'Edit Account' : 'Create New Account'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control as any}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter account number" {...field} />
                    </FormControl>
                    <FormDescription>
                      Unique identifier for this account
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter account name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Type *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="asset">Asset</SelectItem>
                        <SelectItem value="liability">Liability</SelectItem>
                        <SelectItem value="equity">Equity</SelectItem>
                        <SelectItem value="revenue">Revenue</SelectItem>
                        <SelectItem value="expense">Expense</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="subtype"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtype</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter account subtype" {...field} />
                    </FormControl>
                    <FormDescription>
                      Optional subcategory for this account
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="parentAccount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parent Account</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {parentAccounts.map((parentAccount) => (
                          <SelectItem
                            key={parentAccount._id}
                            value={parentAccount._id}
                            disabled={account && account._id === parentAccount._id}
                          >
                            {parentAccount.accountNumber} - {parentAccount.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optional parent account for hierarchical structure
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="costCenter"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Center</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select cost center" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {costCenters.map((costCenter) => (
                          <SelectItem
                            key={costCenter._id}
                            value={costCenter._id}
                          >
                            {costCenter.code} - {costCenter.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optional cost center for this account
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="fiscalYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Year</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 2023-2024" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control as any}
                name="reportingGroup"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reporting Group</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter reporting group" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control as any} // Type assertion to fix control type issue
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter account description"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control as any}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter tags separated by commas" {...field} />
                  </FormControl>
                  <FormDescription>
                    Optional tags for filtering and organization
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control as any}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Active</FormLabel>
                    <FormDescription>
                      Inactive accounts are hidden from most views
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <CardFooter className="flex justify-between px-0">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {account ? 'Update Account' : 'Create Account'}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

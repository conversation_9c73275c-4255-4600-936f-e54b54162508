// components/accounting/accounts/account-details.tsx
"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  Edit,
  Trash2,
  AlertTriangle,
  ChevronRight,
  FileText,
  Clock,
  Tag,
  Building,
  Banknote
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { toast } from '@/components/ui/use-toast'
import { useAccountingStore } from '@/lib/frontend/accountingStore'
import { formatCurrency } from '@/lib/utils/format'
import { formatDate } from '@/lib/utils'
import { EmptyState } from '@/components/empty-state'

interface AccountDetailsProps {
  accountId: string
}

// Extended Account type to include additional properties
interface ExtendedAccount {
  _id: string;
  accountNumber: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  subtype?: string;
  description?: string;
  balance: number;
  isActive: boolean;
  parentAccount?: {
    _id: string;
    accountNumber: string;
    name: string;
  };
  costCenter?: {
    _id: string;
    code: string;
    name: string;
  };
  tags?: string[];
  fiscalYear?: string;
  reportingGroup?: string;
  isLocked?: boolean;
  lockReason?: string;
  lastReconciliationDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export function AccountDetails({ accountId }: AccountDetailsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [transactions, setTransactions] = useState<any[]>([])
  const [journalEntries, setJournalEntries] = useState<any[]>([])

  const {
    selectedAccount: storeAccount,
    fetchAccount,
    deleteAccount,
    isLoadingAccounts
  } = useAccountingStore()

  // Cast selectedAccount to ExtendedAccount to handle additional properties
  const selectedAccount = storeAccount as unknown as ExtendedAccount

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        // Fetch account details
        await fetchAccount(accountId)

        // Fetch related transactions
        const transactionsResponse = await fetch(`/api/accounting/transactions?account=${accountId}&limit=5`)
        const transactionsData = await transactionsResponse.json()
        setTransactions(transactionsData.transactions || [])

        // Fetch related journal entries
        const journalResponse = await fetch(`/api/accounting/journal?account=${accountId}&limit=5`)
        const journalData = await journalResponse.json()
        setJournalEntries(journalData.journalEntries || [])
      } catch (error) {
        console.error('Error loading account details:', error)
        toast({
          title: 'Error',
          description: 'Failed to load account details: ${errorMessage}',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [accountId, fetchAccount])

  const handleDelete = async () => {
    try {
      await deleteAccount(accountId)
      toast({
        title: 'Account Deleted',
        description: 'The account has been deleted successfully',
      })
      router.push('/accounting/accounts')
    } catch (error) {
      console.error('Error deleting account:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete account: ${errorMessage}',
        variant: 'destructive',
      })
    }
  }

  if (isLoading || isLoadingAccounts) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Account Details...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-muted rounded w-48"></div>
              <div className="h-4 bg-muted rounded w-64"></div>
              <div className="h-4 bg-muted rounded w-52"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!selectedAccount) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Account Not Found</CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<AlertTriangle className="h-8 w-8 text-muted-foreground" />}
            title="Account Not Found"
            description="The account you're looking for doesn't exist or you don't have permission to view it."
            action={
              <Button variant="outline" asChild>
                <Link href="/accounting/accounts">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Accounts
                </Link>
              </Button>
            }
          />
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/accounting/accounts">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              {selectedAccount.accountNumber} - {selectedAccount.name}
            </h2>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Badge variant="outline" className="capitalize">
                {selectedAccount.type}
              </Badge>
              {selectedAccount.subtype && (
                <>
                  <ChevronRight className="h-4 w-4" />
                  <span>{selectedAccount.subtype}</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/accounting/accounts/${accountId}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the account
                  and all associated data.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete}>
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Current Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(selectedAccount.balance, 'MWK')}
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated: {formatDate(selectedAccount.updatedAt)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant={selectedAccount.isActive ? "default" : "secondary"}>
                {selectedAccount.isActive ? "Active" : "Inactive"}
              </Badge>
              {selectedAccount.isLocked && (
                <Badge variant="destructive">Locked</Badge>
              )}
            </div>
            {selectedAccount.isLocked && selectedAccount.lockReason && (
              <p className="text-xs text-muted-foreground mt-2">
                Lock reason: {selectedAccount.lockReason}
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Last Reconciliation
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedAccount.lastReconciliationDate ? (
              <div className="text-sm">
                {formatDate(selectedAccount.lastReconciliationDate)}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                Never reconciled
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="transactions">Recent Transactions</TabsTrigger>
          <TabsTrigger value="journal">Journal Entries</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Account Number</h3>
                  <p>{selectedAccount.accountNumber}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Name</h3>
                  <p>{selectedAccount.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
                  <p className="capitalize">{selectedAccount.type}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Subtype</h3>
                  <p>{selectedAccount.subtype || "—"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Parent Account</h3>
                  <p>
                    {selectedAccount.parentAccount ? (
                      <Link
                        href={`/accounting/accounts/${selectedAccount.parentAccount._id}`}
                        className="text-primary hover:underline"
                      >
                        {selectedAccount.parentAccount.accountNumber} - {selectedAccount.parentAccount.name}
                      </Link>
                    ) : (
                      "—"
                    )}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Cost Center</h3>
                  <p>
                    {selectedAccount.costCenter ? (
                      <Link
                        href={`/accounting/cost-centers/${selectedAccount.costCenter._id}`}
                        className="text-primary hover:underline"
                      >
                        {selectedAccount.costCenter.code} - {selectedAccount.costCenter.name}
                      </Link>
                    ) : (
                      "—"
                    )}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Fiscal Year</h3>
                  <p>{selectedAccount.fiscalYear || "—"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Reporting Group</h3>
                  <p>{selectedAccount.reportingGroup || "—"}</p>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                <p className="mt-1">{selectedAccount.description || "No description provided."}</p>
              </div>

              {selectedAccount.tags && selectedAccount.tags.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Tags</h3>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedAccount.tags.map((tag: string) => (
                        <Badge key={tag} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </>
              )}

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Created By</h3>
                  <p>{selectedAccount.createdBy}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(selectedAccount.createdAt)}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Last Updated By</h3>
                  <p>{selectedAccount.updatedBy}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(selectedAccount.updatedAt)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                The most recent transactions for this account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-md">
                      <div className="flex items-start space-x-4">
                        <div className={`p-2 rounded-full ${
                          transaction.type === 'debit' ? 'bg-red-100' : 'bg-green-100'
                        }`}>
                          {transaction.type === 'debit' ? (
                            <ArrowLeft className="h-4 w-4 text-red-600" />
                          ) : (
                            <ArrowLeft className="h-4 w-4 text-green-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium">{transaction.description}</h4>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>{formatDate(transaction.date)}</span>
                            <span>•</span>
                            <span>{transaction.transactionNumber}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${
                          transaction.type === 'debit' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transaction.type === 'debit' ? '-' : '+'}{formatCurrency(transaction.amount, 'MWK')}
                        </div>
                        <Badge variant="outline">{transaction.status}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={<Banknote className="h-8 w-8 text-muted-foreground" />}
                  title="No Transactions"
                  description="This account doesn't have any transactions yet."
                  action={
                    <Button asChild>
                      <Link href={`/accounting/transactions/new?account=${accountId}`}>
                        Create Transaction
                      </Link>
                    </Button>
                  }
                />
              )}
            </CardContent>
            {transactions.length > 0 && (
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/accounting/transactions?account=${accountId}`}>
                    View All Transactions
                  </Link>
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="journal">
          <Card>
            <CardHeader>
              <CardTitle>Journal Entries</CardTitle>
              <CardDescription>
                Journal entries that include this account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {journalEntries.length > 0 ? (
                <div className="space-y-4">
                  {journalEntries.map((entry) => (
                    <div key={entry._id} className="flex items-center justify-between p-4 border rounded-md">
                      <div>
                        <h4 className="font-medium">{entry.description}</h4>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <FileText className="h-3 w-3" />
                          <span>{entry.reference}</span>
                          <span>•</span>
                          <span>{formatDate(entry.date)}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline" className="capitalize">
                          {entry.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={<FileText className="h-8 w-8 text-muted-foreground" />}
                  title="No Journal Entries"
                  description="This account doesn't have any journal entries yet."
                  action={
                    <Button asChild>
                      <Link href={`/accounting/journal/new?account=${accountId}`}>
                        Create Journal Entry
                      </Link>
                    </Button>
                  }
                />
              )}
            </CardContent>
            {journalEntries.length > 0 && (
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/accounting/journal?account=${accountId}`}>
                    View All Journal Entries
                  </Link>
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

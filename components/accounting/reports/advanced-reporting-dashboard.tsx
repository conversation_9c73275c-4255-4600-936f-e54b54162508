"use client"

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import {
  FileText,
  BarChart4,
  TrendingUp,
  Calendar,
  Filter,
  Download,
  Share,
  Settings,
  Play,
  Clock,
  AlertTriangle,
  CheckCircle,
  Zap,
  Target,
  Activity,
  Eye,
  Edit,
  Copy
} from 'lucide-react';
import { useAdvancedReporting } from '@/lib/hooks/accounting/use-advanced-reporting';
import { ReportData, ReportConfiguration } from '@/lib/services/accounting/advanced-reporting-service';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Chart colors
const CHART_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

interface AdvancedReportingDashboardProps {
  onReportGenerated?: (reportData: ReportData) => void;
}

export function AdvancedReportingDashboard({ onReportGenerated }: AdvancedReportingDashboardProps) {
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [reportType, setReportType] = useState<string>('income_summary');
  const [reportCategory, setReportCategory] = useState<string>('financial');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 3)),
    endDate: new Date()
  });
  const [selectedGroupBy, setSelectedGroupBy] = useState<string[]>(['date', 'source']);
  const [selectedAggregations, setSelectedAggregations] = useState<string[]>(['sum', 'count']);
  const [chartType, setChartType] = useState<string>('bar');
  const [schedulingEnabled, setSchedulingEnabled] = useState(false);
  const [schedulingFrequency, setSchedulingFrequency] = useState<string>('monthly');
  const [currentReport, setCurrentReport] = useState<ReportData | null>(null);

  const {
    configuration,
    isLoadingConfiguration,
    templates,
    isLoadingTemplates,
    generateReport,
    isGeneratingReport,
    generateReportError,
    createConfiguration,
    isCreatingConfiguration,
    exportReport,
    validateConfiguration,
    getDefaultConfiguration,
    formatReportData
  } = useAdvancedReporting();

  // Handle report generation
  const handleGenerateReport = async () => {
    // First create a configuration
    const configRequest = {
      name: reportName || 'Untitled Report',
      description: reportDescription,
      type: reportType as any,
      category: reportCategory as any,
      scope: {
        dateRange: {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          period: 'custom' as const
        },
        filters: {},
        groupBy: selectedGroupBy as any,
        aggregations: selectedAggregations as any
      },
      visualization: {
        chartType: chartType as any,
        layout: 'single' as const,
        styling: {
          colors: CHART_COLORS,
          theme: 'light' as const,
          responsive: true
        }
      },
      scheduling: {
        enabled: schedulingEnabled,
        frequency: schedulingFrequency as any,
        recipients: [],
        format: 'pdf' as const
      },
      permissions: {
        viewRoles: [],
        editRoles: [],
        shareRoles: []
      }
    };

    const validationErrors = validateConfiguration(configRequest);
    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors);
      return;
    }

    const config = await createConfiguration(configRequest);
    if (!config) return;

    // Generate report using the configuration
    const reportData = await generateReport({
      configurationId: config.id,
      overrides: {
        dateRange: {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate
        }
      }
    });

    if (reportData) {
      setCurrentReport(reportData);
      onReportGenerated?.(reportData);
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    const template = templates?.find(t => t.id === templateId);
    if (!template) return;

    setReportName(template.name);
    setReportDescription(template.description);
    setReportType(template.configuration?.type || 'income_summary');
    setReportCategory(template.category);
    setChartType(template.configuration?.visualization?.chartType || 'bar');
  };

  // Format report data for charts
  const formattedReportData = useMemo(() => {
    if (!currentReport) return null;
    return formatReportData(currentReport);
  }, [currentReport, formatReportData]);

  // Render chart based on type
  const renderChart = () => {
    if (!formattedReportData) return null;

    const data = formattedReportData.chartData;

    switch (chartType) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <Legend />
              <Line type="monotone" dataKey="totalAmount" stroke="#0088FE" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="source" />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <Legend />
              <Bar dataKey="totalAmount" fill="#0088FE" />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <Legend />
              <Area type="monotone" dataKey="totalAmount" stroke="#0088FE" fill="#0088FE" fillOpacity={0.6} />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data}
                dataKey="totalAmount"
                nameKey="source"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={(entry) => `${entry.source}: ${formatCurrency(entry.totalAmount)}`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <div className="text-center py-8">
            <BarChart4 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Chart visualization will appear here</p>
          </div>
        );
    }
  };

  if (isLoadingConfiguration || isLoadingTemplates) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Advanced Reporting</CardTitle>
          <CardDescription>Loading reporting configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="builder" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="builder">Report Builder</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
        </TabsList>

        {/* Report Builder Tab */}
        <TabsContent value="builder" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Report Configuration
              </CardTitle>
              <CardDescription>
                Configure your custom report with advanced options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="reportName">Report Name</Label>
                    <Input
                      id="reportName"
                      value={reportName}
                      onChange={(e) => setReportName(e.target.value)}
                      placeholder="Enter report name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reportDescription">Description</Label>
                    <Textarea
                      id="reportDescription"
                      value={reportDescription}
                      onChange={(e) => setReportDescription(e.target.value)}
                      placeholder="Enter report description"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Report Type</Label>
                    <Select value={reportType} onValueChange={setReportType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {configuration?.reportTypes.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Select value={reportCategory} onValueChange={setReportCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="financial">Financial</SelectItem>
                        <SelectItem value="operational">Operational</SelectItem>
                        <SelectItem value="compliance">Compliance</SelectItem>
                        <SelectItem value="strategic">Strategic</SelectItem>
                        <SelectItem value="executive">Executive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Data Configuration */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Date Range</Label>
                    <div className="grid gap-2 grid-cols-2">
                      <Input
                        type="date"
                        value={dateRange.startDate.toISOString().split('T')[0]}
                        onChange={(e) => setDateRange(prev => ({
                          ...prev,
                          startDate: new Date(e.target.value)
                        }))}
                      />
                      <Input
                        type="date"
                        value={dateRange.endDate.toISOString().split('T')[0]}
                        onChange={(e) => setDateRange(prev => ({
                          ...prev,
                          endDate: new Date(e.target.value)
                        }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Group By</Label>
                    <div className="grid gap-2 grid-cols-2">
                      {['date', 'source', 'category', 'budget'].map(option => (
                        <div key={option} className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedGroupBy.includes(option)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedGroupBy(prev => [...prev, option]);
                              } else {
                                setSelectedGroupBy(prev => prev.filter(item => item !== option));
                              }
                            }}
                          />
                          <Label className="text-sm capitalize">{option}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Aggregations</Label>
                    <div className="grid gap-2 grid-cols-2">
                      {['sum', 'average', 'count', 'min', 'max'].map(option => (
                        <div key={option} className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedAggregations.includes(option)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedAggregations(prev => [...prev, option]);
                              } else {
                                setSelectedAggregations(prev => prev.filter(item => item !== option));
                              }
                            }}
                          />
                          <Label className="text-sm capitalize">{option}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Chart Type</Label>
                    <Select value={chartType} onValueChange={setChartType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {configuration?.visualizations.map(viz => (
                          <SelectItem key={viz.type} value={viz.type}>
                            {viz.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Scheduling Options */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={schedulingEnabled}
                    onCheckedChange={setSchedulingEnabled}
                  />
                  <Label>Enable Scheduling</Label>
                </div>

                {schedulingEnabled && (
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Frequency</Label>
                      <Select value={schedulingFrequency} onValueChange={setSchedulingFrequency}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  onClick={handleGenerateReport}
                  disabled={isGeneratingReport || isCreatingConfiguration}
                  className="gap-2"
                >
                  {isGeneratingReport || isCreatingConfiguration ? (
                    <Activity className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  {isGeneratingReport || isCreatingConfiguration ? 'Generating...' : 'Generate Report'}
                </Button>
              </div>

              {generateReportError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Generation Error</AlertTitle>
                  <AlertDescription>{generateReportError.message}</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Report Templates
              </CardTitle>
              <CardDescription>
                Choose from pre-built report templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {templates?.map(template => (
                  <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">{template.category}</Badge>
                          {template.isOfficial && (
                            <Badge variant="default">Official</Badge>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {template.tags.map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTemplateSelect(template.id)}
                            className="flex-1"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Use Template
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Eye className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-4">
          {currentReport ? (
            <>
              {/* Summary Statistics */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Records</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formattedReportData?.summary.totalRecords || 0}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Execution Time</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(formattedReportData?.performance.executionTime || 0) / 1000}s
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Data Size</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {((formattedReportData?.performance.dataSize || 0) / 1024).toFixed(1)}KB
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Insights</CardTitle>
                    <Zap className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formattedReportData?.summary.insights.length || 0}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Chart Visualization */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <BarChart4 className="h-5 w-5" />
                      Report Visualization
                    </span>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" onClick={() => exportReport(currentReport.id, 'pdf')}>
                        <Download className="h-3 w-3 mr-1" />
                        PDF
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => exportReport(currentReport.id, 'excel')}>
                        <Download className="h-3 w-3 mr-1" />
                        Excel
                      </Button>
                      <Button size="sm" variant="outline">
                        <Share className="h-3 w-3 mr-1" />
                        Share
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {renderChart()}
                </CardContent>
              </Card>

              {/* Insights */}
              {formattedReportData?.summary.insights && formattedReportData.summary.insights.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      AI-Generated Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {formattedReportData.summary.insights.map((insight, index) => (
                        <Alert key={index} className={
                          insight.impact === 'critical' ? 'border-red-200 bg-red-50' :
                          insight.impact === 'high' ? 'border-orange-200 bg-orange-50' :
                          insight.impact === 'medium' ? 'border-yellow-200 bg-yellow-50' :
                          'border-blue-200 bg-blue-50'
                        }>
                          <div className="flex items-start space-x-2">
                            {insight.type === 'trend' && <TrendingUp className="h-4 w-4 mt-0.5" />}
                            {insight.type === 'anomaly' && <AlertTriangle className="h-4 w-4 mt-0.5" />}
                            {insight.type === 'milestone' && <Target className="h-4 w-4 mt-0.5" />}
                            {insight.type === 'alert' && <AlertTriangle className="h-4 w-4 mt-0.5" />}
                            <div>
                              <AlertTitle>{insight.title}</AlertTitle>
                              <AlertDescription>{insight.description}</AlertDescription>
                              <div className="mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {(insight.confidence * 100).toFixed(0)}% confidence
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <BarChart4 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Generate a report to see results here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Scheduled Reports Tab */}
        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Scheduled Reports
              </CardTitle>
              <CardDescription>
                Manage your scheduled report deliveries
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  No scheduled reports yet
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Enable scheduling when creating a report to see scheduled deliveries here
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

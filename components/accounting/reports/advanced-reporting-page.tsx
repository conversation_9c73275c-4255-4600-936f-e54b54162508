"use client"

import React, { useState } from 'react';
import { DashboardShell } from "@/components/dashboard-shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  BarChart4, 
  TrendingUp,
  FileText,
  Settings,
  Zap,
  Target,
  Activity,
  Calendar,
  Download,
  Share,
  Eye,
  Plus,
  Filter
} from "lucide-react";
import Link from "next/link";
import { AdvancedReportingDashboard } from '@/components/accounting/reports/advanced-reporting-dashboard';
import { useAdvancedReporting } from '@/lib/hooks/accounting/use-advanced-reporting';
import { ReportData } from '@/lib/services/accounting/advanced-reporting-service';

export function AdvancedReportingPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');
  const [generatedReports, setGeneratedReports] = useState<ReportData[]>([]);

  const {
    configuration,
    isLoadingConfiguration,
    templates,
    isLoadingTemplates
  } = useAdvancedReporting();

  const handleReportGenerated = (reportData: ReportData) => {
    setGeneratedReports(prev => [reportData, ...prev]);
  };

  // Mock statistics for demonstration
  const statistics = {
    totalReports: generatedReports.length + 12,
    templatesUsed: 8,
    scheduledReports: 3,
    avgExecutionTime: 2.4,
    dataProcessed: 156.7,
    insightsGenerated: 24
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Advanced Reporting</h1>
            <p className="text-muted-foreground">
              Create custom reports with advanced analytics, AI insights, and automated scheduling
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/reports">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Link>
            </Button>
            <Button size="sm" className="gap-1">
              <Plus className="h-4 w-4" />
              <span>New Report</span>
            </Button>
          </div>
        </div>

        {/* Statistics Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalReports}</div>
              <p className="text-xs text-muted-foreground">
                +{generatedReports.length} this session
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Templates Used</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.templatesUsed}</div>
              <p className="text-xs text-muted-foreground">
                {templates?.length || 0} available
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.scheduledReports}</div>
              <p className="text-xs text-muted-foreground">
                Active schedules
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Time</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.avgExecutionTime}s</div>
              <p className="text-xs text-muted-foreground">
                Execution time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Data Processed</CardTitle>
              <BarChart4 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.dataProcessed}MB</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI Insights</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.insightsGenerated}</div>
              <p className="text-xs text-muted-foreground">
                Generated insights
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="builder" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="builder">Report Builder</TabsTrigger>
            <TabsTrigger value="gallery">Report Gallery</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Report Builder Tab */}
          <TabsContent value="builder" className="space-y-4">
            <AdvancedReportingDashboard onReportGenerated={handleReportGenerated} />
          </TabsContent>

          {/* Report Gallery Tab */}
          <TabsContent value="gallery" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Report Gallery
                </CardTitle>
                <CardDescription>
                  Browse and manage your generated reports
                </CardDescription>
              </CardHeader>
              <CardContent>
                {generatedReports.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {generatedReports.map((report, index) => (
                      <Card key={report.id} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardHeader>
                          <CardTitle className="text-lg">Report #{index + 1}</CardTitle>
                          <CardDescription>
                            Generated {report.generatedAt.toLocaleDateString()}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <Badge variant="outline">
                                {report.dataPoints.length} records
                              </Badge>
                              <Badge variant={report.status === 'completed' ? 'default' : 'secondary'}>
                                {report.status}
                              </Badge>
                            </div>
                            
                            <div className="text-sm text-muted-foreground">
                              <div>Execution: {(report.performance.executionTime / 1000).toFixed(2)}s</div>
                              <div>Insights: {report.summary.insights.length}</div>
                            </div>

                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline" className="flex-1">
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>
                              <Button size="sm" variant="outline">
                                <Download className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Share className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      No reports generated yet
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Use the Report Builder to create your first advanced report
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Usage Analytics
                  </CardTitle>
                  <CardDescription>
                    Reporting system usage and performance metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Report Generation Rate</span>
                      <span className="text-sm">
                        {((generatedReports.length / Math.max(statistics.totalReports, 1)) * 100).toFixed(1)}%
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Template Usage</span>
                      <span className="text-sm">
                        {statistics.templatesUsed} / {templates?.length || 0}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Avg Insights per Report</span>
                      <span className="text-sm">
                        {generatedReports.length > 0 
                          ? (generatedReports.reduce((sum, r) => sum + r.summary.insights.length, 0) / generatedReports.length).toFixed(1)
                          : '0'
                        }
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Performance Metrics
                  </CardTitle>
                  <CardDescription>
                    System performance and optimization insights
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Average Execution Time</span>
                      <Badge variant="default">
                        {statistics.avgExecutionTime}s
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Data Processing Efficiency</span>
                      <Badge variant="default">
                        95.2%
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Cache Hit Rate</span>
                      <Badge variant="default">
                        78.5%
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Reporting Settings
                </CardTitle>
                <CardDescription>
                  Configure advanced reporting preferences and defaults
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">Default Configuration</h4>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Default Chart Type</label>
                        <p className="text-sm text-muted-foreground">Bar Chart</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Default Period</label>
                        <p className="text-sm text-muted-foreground">Monthly</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Auto-Export Format</label>
                        <p className="text-sm text-muted-foreground">PDF</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Cache Duration</label>
                        <p className="text-sm text-muted-foreground">15 minutes</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Performance Settings</h4>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Max Data Points</label>
                        <p className="text-sm text-muted-foreground">10,000 per report</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Query Timeout</label>
                        <p className="text-sm text-muted-foreground">30 seconds</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Concurrent Reports</label>
                        <p className="text-sm text-muted-foreground">5 maximum</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Auto-Cleanup</label>
                        <p className="text-sm text-muted-foreground">30 days</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button>
                      <Settings className="h-4 w-4 mr-2" />
                      Update Settings
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Getting Started Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Advanced Reporting Features
            </CardTitle>
            <CardDescription>
              Explore the powerful capabilities of the advanced reporting system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BarChart4 className="h-5 w-5 text-blue-500" />
                  <h4 className="font-medium">Custom Report Builder</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Build custom reports with drag-and-drop interface, advanced filters, and multiple visualization options
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  <h4 className="font-medium">AI-Powered Insights</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Automatic anomaly detection, trend analysis, and intelligent recommendations powered by machine learning
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-500" />
                  <h4 className="font-medium">Automated Scheduling</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Schedule reports for automatic generation and delivery via email in multiple formats
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-purple-500" />
                  <h4 className="font-medium">Performance Analytics</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Real-time performance monitoring with execution time tracking and optimization suggestions
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Share className="h-5 w-5 text-orange-500" />
                  <h4 className="font-medium">Collaboration Tools</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share reports with team members, create public dashboards, and manage access permissions
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Download className="h-5 w-5 text-red-500" />
                  <h4 className="font-medium">Multi-Format Export</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Export reports in PDF, Excel, CSV, and JSON formats with customizable styling and branding
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Database, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface DebugData {
  incomeCount: number;
  expenditureCount: number;
  budgetCount: number;
  recentIncome: any[];
  recentExpenditures: any[];
  apiStatus: {
    incomeApi: boolean;
    expenditureApi: boolean;
    budgetApi: boolean;
  };
}

export function DataDebugPanel() {
  const [debugData, setDebugData] = useState<DebugData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchDebugData = async () => {
    setIsLoading(true);
    try {
      // Test income API
      const incomeResponse = await fetch('/api/accounting/income?limit=5');
      const incomeData = incomeResponse.ok ? await incomeResponse.json() : null;

      // Test expenditure API
      const expenditureResponse = await fetch('/api/accounting/expenditure/simple?limit=5');
      const expenditureData = expenditureResponse.ok ? await expenditureResponse.json() : null;

      // Test budget API
      const budgetResponse = await fetch('/api/accounting/budget');
      const budgetData = budgetResponse.ok ? await budgetResponse.json() : null;

      // Test income summary API
      const summaryResponse = await fetch('/api/accounting/income/summary?fiscalYear=2024-2025');
      const summaryData = summaryResponse.ok ? await summaryResponse.json() : null;

      setDebugData({
        incomeCount: incomeData?.pagination?.totalCount || 0,
        expenditureCount: expenditureData?.totalCount || 0,
        budgetCount: budgetData?.budgets?.length || 0,
        recentIncome: incomeData?.incomeTransactions || [],
        recentExpenditures: expenditureData?.expenditures || [],
        apiStatus: {
          incomeApi: incomeResponse.ok,
          expenditureApi: expenditureResponse.ok,
          budgetApi: budgetResponse.ok,
        }
      });

      console.log('Debug Data:', {
        incomeData,
        expenditureData,
        budgetData,
        summaryData
      });

      toast({
        title: "Debug Data Loaded",
        description: "Check the console for detailed API responses",
      });

    } catch (error) {
      console.error('Error fetching debug data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch debug data. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Debug Panel
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button 
            onClick={fetchDebugData} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Loading Debug Data...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Fetch Debug Data
              </>
            )}
          </Button>

          {debugData && (
            <div className="space-y-4">
              {/* API Status */}
              <div>
                <h3 className="font-medium mb-2">API Status</h3>
                <div className="grid grid-cols-3 gap-2">
                  <div className="flex items-center gap-2">
                    {debugData.apiStatus.incomeApi ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm">Income API</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {debugData.apiStatus.expenditureApi ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm">Expenditure API</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {debugData.apiStatus.budgetApi ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm">Budget API</span>
                  </div>
                </div>
              </div>

              {/* Data Counts */}
              <div>
                <h3 className="font-medium mb-2">Data Counts</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{debugData.incomeCount}</div>
                    <div className="text-sm text-muted-foreground">Income Records</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{debugData.expenditureCount}</div>
                    <div className="text-sm text-muted-foreground">Expenditure Records</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{debugData.budgetCount}</div>
                    <div className="text-sm text-muted-foreground">Budget Records</div>
                  </div>
                </div>
              </div>

              {/* Recent Income */}
              {debugData.recentIncome.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Recent Income</h3>
                  <div className="space-y-2">
                    {debugData.recentIncome.slice(0, 3).map((income, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded">
                        <div>
                          <div className="font-medium">{income.reference}</div>
                          <div className="text-sm text-muted-foreground">
                            {income.source} • {income.fiscalYear}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">MWK {income.amount?.toLocaleString()}</div>
                          <Badge variant={income.status === 'received' ? 'default' : 'secondary'}>
                            {income.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Expenditures */}
              {debugData.recentExpenditures.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Recent Expenditures</h3>
                  <div className="space-y-2">
                    {debugData.recentExpenditures.slice(0, 3).map((expenditure, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                        <div>
                          <div className="font-medium">{expenditure.expenditureNumber}</div>
                          <div className="text-sm text-muted-foreground">
                            {expenditure.category} • {expenditure.department}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">MWK {expenditure.amount?.toLocaleString()}</div>
                          <Badge variant={expenditure.status === 'paid' ? 'default' : 'secondary'}>
                            {expenditure.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* No Data Messages */}
              {debugData.incomeCount === 0 && debugData.expenditureCount === 0 && (
                <div className="text-center py-4">
                  <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                  <p className="text-muted-foreground">No income or expenditure data found</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Try creating some test data using the income and expenditure forms
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

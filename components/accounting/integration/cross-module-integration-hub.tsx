'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Network, 
  Zap, 
  Activity, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  RefreshCw,
  Settings,
  BarChart3,
  PieChart,
  Users,
  Building,
  DollarSign,
  Calendar,
  FileText,
  Target
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface ModuleStatus {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'maintenance';
  lastSync: Date;
  syncStatus: 'synced' | 'syncing' | 'failed' | 'pending';
  dataPoints: number;
  errorCount: number;
  performance: {
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
  dependencies: string[];
  version: string;
}

interface IntegrationMetrics {
  totalTransactions: number;
  syncedTransactions: number;
  failedTransactions: number;
  averageResponseTime: number;
  dataConsistencyScore: number;
  systemHealth: number;
  lastFullSync: Date;
  nextScheduledSync: Date;
}

interface DataFlow {
  id: string;
  source: string;
  target: string;
  dataType: 'income' | 'expense' | 'budget' | 'payroll' | 'voucher';
  volume: number;
  status: 'active' | 'paused' | 'error';
  lastTransfer: Date;
  transferRate: number;
  errorRate: number;
}

interface CrossModuleIntegrationHubProps {
  refreshInterval?: number;
  showAdvancedMetrics?: boolean;
  className?: string;
}

export function CrossModuleIntegrationHub({
  refreshInterval = 60000, // 1 minute
  showAdvancedMetrics = true,
  className = ''
}: CrossModuleIntegrationHubProps) {
  const [modules, setModules] = useState<ModuleStatus[]>([]);
  const [metrics, setMetrics] = useState<IntegrationMetrics | null>(null);
  const [dataFlows, setDataFlows] = useState<DataFlow[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const isMobile = useIsMobile();

  // Fetch integration data
  const fetchIntegrationData = async () => {
    try {
      setError(null);
      const response = await fetch('/api/accounting/integration/status');
      
      if (!response.ok) {
        throw new Error('Failed to fetch integration data');
      }
      
      const data = await response.json();
      setModules(data.modules);
      setMetrics(data.metrics);
      setDataFlows(data.dataFlows);
    } catch (error) {
      console.error('Error fetching integration data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch integration data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchIntegrationData();
  };

  // Trigger sync for specific module
  const triggerModuleSync = async (moduleId: string) => {
    try {
      const response = await fetch('/api/accounting/integration/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ moduleId }),
      });

      if (!response.ok) {
        throw new Error('Failed to trigger sync');
      }

      // Refresh data after sync
      await fetchIntegrationData();
    } catch (error) {
      console.error('Error triggering sync:', error);
      setError(error instanceof Error ? error.message : 'Failed to trigger sync');
    }
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchIntegrationData();
    
    const interval = setInterval(fetchIntegrationData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  // Get status icon and color
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'maintenance':
        return <Settings className="h-4 w-4 text-blue-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get sync status badge variant
  const getSyncStatusVariant = (status: string) => {
    switch (status) {
      case 'synced': return 'default';
      case 'syncing': return 'secondary';
      case 'failed': return 'destructive';
      case 'pending': return 'outline';
      default: return 'outline';
    }
  };

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Network className="h-5 w-5 text-blue-500" />
              <span>Cross-Module Integration Hub</span>
            </CardTitle>
            <CardDescription>
              Real-time monitoring and management of module integrations
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              Live Monitoring
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="modules">Modules</TabsTrigger>
            <TabsTrigger value="dataflows">Data Flows</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {metrics && (
              <>
                {/* System Health Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center space-x-2">
                        <Target className="h-4 w-4 text-green-500" />
                        <span>System Health</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className={`text-2xl font-bold ${getHealthScoreColor(metrics.systemHealth)}`}>
                        {metrics.systemHealth}%
                      </div>
                      <Progress value={metrics.systemHealth} className="mt-2" />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center space-x-2">
                        <BarChart3 className="h-4 w-4 text-blue-500" />
                        <span>Data Consistency</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className={`text-2xl font-bold ${getHealthScoreColor(metrics.dataConsistencyScore)}`}>
                        {metrics.dataConsistencyScore}%
                      </div>
                      <Progress value={metrics.dataConsistencyScore} className="mt-2" />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-yellow-500" />
                        <span>Response Time</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {metrics.averageResponseTime}ms
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Average across all modules
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center space-x-2">
                        <Activity className="h-4 w-4 text-purple-500" />
                        <span>Sync Status</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {metrics.syncedTransactions}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        of {metrics.totalTransactions} synced
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Sync Timeline */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Sync Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">Last Full Sync</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {metrics.lastFullSync.toLocaleString()}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">Next Scheduled Sync</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {metrics.nextScheduledSync.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Failed Transactions Alert */}
                {metrics.failedTransactions > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {metrics.failedTransactions} transactions failed to sync. 
                      <Button variant="link" className="p-0 ml-2 h-auto">
                        View Details
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </>
            )}
          </TabsContent>

          {/* Modules Tab */}
          <TabsContent value="modules" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {modules.map((module) => (
                <Card key={module.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(module.status)}
                        <CardTitle className="text-base">{module.name}</CardTitle>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getSyncStatusVariant(module.syncStatus)}>
                          {module.syncStatus}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          v{module.version}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Module Metrics */}
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <span className="text-sm text-muted-foreground">Data Points</span>
                        <div className="font-semibold">{module.dataPoints.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Response Time</span>
                        <div className="font-semibold">{module.performance.responseTime}ms</div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Error Rate</span>
                        <div className={`font-semibold ${module.performance.errorRate > 5 ? 'text-red-600' : 'text-green-600'}`}>
                          {module.performance.errorRate.toFixed(1)}%
                        </div>
                      </div>
                    </div>

                    {/* Performance Indicators */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Throughput</span>
                        <span>{module.performance.throughput}/min</span>
                      </div>
                      <Progress value={Math.min(100, (module.performance.throughput / 1000) * 100)} />
                    </div>

                    {/* Dependencies */}
                    {module.dependencies.length > 0 && (
                      <div className="space-y-2">
                        <span className="text-sm font-medium">Dependencies</span>
                        <div className="flex flex-wrap gap-1">
                          {module.dependencies.map((dep, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {dep}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => triggerModuleSync(module.id)}
                        disabled={module.syncStatus === 'syncing'}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${module.syncStatus === 'syncing' ? 'animate-spin' : ''}`} />
                        Sync Now
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4 mr-2" />
                        Configure
                      </Button>
                    </div>

                    {/* Last Sync */}
                    <div className="text-xs text-muted-foreground">
                      Last sync: {module.lastSync.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Data Flows Tab */}
          <TabsContent value="dataflows" className="space-y-4">
            <div className="space-y-4">
              {dataFlows.map((flow) => (
                <Card key={flow.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Network className="h-4 w-4 text-blue-500" />
                        <CardTitle className="text-base">
                          {flow.source} → {flow.target}
                        </CardTitle>
                        <Badge variant="outline" className="capitalize">
                          {flow.dataType}
                        </Badge>
                      </div>
                      <Badge variant={flow.status === 'active' ? 'default' : flow.status === 'error' ? 'destructive' : 'secondary'}>
                        {flow.status}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Flow Metrics */}
                    <div className="grid grid-cols-4 gap-4">
                      <div>
                        <span className="text-sm text-muted-foreground">Volume</span>
                        <div className="font-semibold">{flow.volume.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Transfer Rate</span>
                        <div className="font-semibold">{flow.transferRate}/min</div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Error Rate</span>
                        <div className={`font-semibold ${flow.errorRate > 5 ? 'text-red-600' : 'text-green-600'}`}>
                          {flow.errorRate.toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Last Transfer</span>
                        <div className="font-semibold text-xs">
                          {flow.lastTransfer.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>

                    {/* Transfer Rate Progress */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Transfer Performance</span>
                        <span>{flow.transferRate}/min</span>
                      </div>
                      <Progress value={Math.min(100, (flow.transferRate / 100) * 100)} />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            {showAdvancedMetrics && (
              <>
                {/* Integration Performance Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle>Integration Performance Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={[
                        { time: '00:00', responseTime: 120, throughput: 850, errors: 2 },
                        { time: '04:00', responseTime: 110, throughput: 920, errors: 1 },
                        { time: '08:00', responseTime: 140, throughput: 1200, errors: 3 },
                        { time: '12:00', responseTime: 160, throughput: 1400, errors: 5 },
                        { time: '16:00', responseTime: 130, throughput: 1100, errors: 2 },
                        { time: '20:00', responseTime: 115, throughput: 950, errors: 1 }
                      ]}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="time" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="responseTime" stroke="#8884d8" name="Response Time (ms)" />
                        <Line type="monotone" dataKey="throughput" stroke="#82ca9d" name="Throughput" />
                        <Line type="monotone" dataKey="errors" stroke="#ff7300" name="Errors" />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Module Health Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Module Health Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {modules.filter(m => m.status === 'healthy').length}
                        </div>
                        <div className="text-sm text-muted-foreground">Healthy</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                          {modules.filter(m => m.status === 'warning').length}
                        </div>
                        <div className="text-sm text-muted-foreground">Warning</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {modules.filter(m => m.status === 'error').length}
                        </div>
                        <div className="text-sm text-muted-foreground">Error</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {modules.filter(m => m.status === 'maintenance').length}
                        </div>
                        <div className="text-sm text-muted-foreground">Maintenance</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

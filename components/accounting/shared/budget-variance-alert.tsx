'use client';

import { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  AlertTriangle, 
  AlertCircle, 
  CheckCircle, 
  Info, 
  X,
  TrendingUp,
  TrendingDown,
  Clock
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

export interface BudgetVariance {
  id: string;
  budgetId: string;
  budgetName: string;
  categoryId: string;
  categoryName: string;
  subcategoryId?: string;
  subcategoryName?: string;
  varianceType: 'over-budget' | 'under-budget' | 'warning' | 'critical';
  varianceAmount: number;
  variancePercentage: number;
  budgetedAmount: number;
  actualAmount: number;
  threshold: number;
  alertLevel: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  recommendations?: string[];
  createdAt: Date;
  isRead: boolean;
  requiresAction: boolean;
}

interface BudgetVarianceAlertProps {
  variance: BudgetVariance;
  onDismiss?: (varianceId: string) => void;
  onMarkAsRead?: (varianceId: string) => void;
  onTakeAction?: (varianceId: string, action: string) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

export function BudgetVarianceAlert({
  variance,
  onDismiss,
  onMarkAsRead,
  onTakeAction,
  showActions = true,
  compact = false,
  className = ''
}: BudgetVarianceAlertProps) {
  const [isDismissed, setIsDismissed] = useState(false);
  const isMobile = useIsMobile();

  // Get alert icon based on alert level
  const getAlertIcon = () => {
    switch (variance.alertLevel) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'info':
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // Get alert variant based on alert level
  const getAlertVariant = () => {
    switch (variance.alertLevel) {
      case 'critical':
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
      default:
        return 'default';
    }
  };

  // Get variance trend icon
  const getVarianceTrendIcon = () => {
    if (variance.varianceType === 'over-budget') {
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    } else if (variance.varianceType === 'under-budget') {
      return <TrendingDown className="h-4 w-4 text-green-500" />;
    }
    return <TrendingUp className="h-4 w-4 text-yellow-500" />;
  };

  // Get variance badge variant
  const getVarianceBadgeVariant = () => {
    switch (variance.varianceType) {
      case 'over-budget':
      case 'critical':
        return 'destructive';
      case 'warning':
        return 'secondary';
      case 'under-budget':
        return 'outline';
      default:
        return 'default';
    }
  };

  // Handle dismiss
  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.(variance.id);
  };

  // Handle mark as read
  const handleMarkAsRead = () => {
    onMarkAsRead?.(variance.id);
  };

  // Handle take action
  const handleTakeAction = (action: string) => {
    onTakeAction?.(variance.id, action);
  };

  if (isDismissed) {
    return null;
  }

  if (compact) {
    return (
      <Alert variant={getAlertVariant()} className={`${className} ${!variance.isRead ? 'border-l-4 border-l-blue-500' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-2">
            {getAlertIcon()}
            <div className="flex-1 min-w-0">
              <AlertTitle className="text-sm">
                {variance.categoryName} - {variance.varianceType.replace('-', ' ')}
              </AlertTitle>
              <AlertDescription className="text-xs">
                {variance.message}
              </AlertDescription>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Badge variant={getVarianceBadgeVariant()} className="text-xs">
              {variance.variancePercentage > 0 ? '+' : ''}{variance.variancePercentage.toFixed(1)}%
            </Badge>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </Alert>
    );
  }

  return (
    <Card className={`${className} ${!variance.isRead ? 'border-l-4 border-l-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            {getAlertIcon()}
            <div>
              <CardTitle className="text-lg">
                Budget Variance Alert
              </CardTitle>
              <CardDescription className="flex items-center space-x-2">
                <span>{variance.budgetName}</span>
                <span>•</span>
                <span>{variance.categoryName}</span>
                {variance.subcategoryName && (
                  <>
                    <span>•</span>
                    <span>{variance.subcategoryName}</span>
                  </>
                )}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getVarianceBadgeVariant()}>
              {variance.varianceType.replace('-', ' ')}
            </Badge>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Variance Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Budgeted Amount</p>
            <p className="text-lg font-semibold">
              {formatCurrency(variance.budgetedAmount)}
            </p>
          </div>
          
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Actual Amount</p>
            <p className="text-lg font-semibold">
              {formatCurrency(variance.actualAmount)}
            </p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              {getVarianceTrendIcon()}
              <p className="text-sm text-muted-foreground">Variance</p>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-lg font-semibold">
                {formatCurrency(Math.abs(variance.varianceAmount))}
              </p>
              <Badge variant={getVarianceBadgeVariant()} className="text-xs">
                {variance.variancePercentage > 0 ? '+' : ''}{variance.variancePercentage.toFixed(1)}%
              </Badge>
            </div>
          </div>
        </div>

        {/* Alert Message */}
        <Alert variant={getAlertVariant()}>
          {getAlertIcon()}
          <AlertTitle>Alert Details</AlertTitle>
          <AlertDescription>
            {variance.message}
          </AlertDescription>
        </Alert>

        {/* Recommendations */}
        {variance.recommendations && variance.recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recommendations</h4>
            <ul className="space-y-1">
              {variance.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-muted-foreground">{recommendation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Timestamp */}
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>
            Alert created: {variance.createdAt.toLocaleString()}
          </span>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex flex-wrap gap-2 pt-2">
            {!variance.isRead && onMarkAsRead && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAsRead}
              >
                Mark as Read
              </Button>
            )}
            
            {variance.requiresAction && onTakeAction && (
              <>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleTakeAction('review-budget')}
                >
                  Review Budget
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTakeAction('adjust-allocation')}
                >
                  Adjust Allocation
                </Button>
                
                {variance.varianceType === 'over-budget' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTakeAction('request-approval')}
                  >
                    Request Approval
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Component for displaying multiple variance alerts
interface BudgetVarianceAlertsProps {
  variances: BudgetVariance[];
  onDismiss?: (varianceId: string) => void;
  onMarkAsRead?: (varianceId: string) => void;
  onTakeAction?: (varianceId: string, action: string) => void;
  showActions?: boolean;
  compact?: boolean;
  maxAlerts?: number;
  className?: string;
}

export function BudgetVarianceAlerts({
  variances,
  onDismiss,
  onMarkAsRead,
  onTakeAction,
  showActions = true,
  compact = false,
  maxAlerts = 5,
  className = ''
}: BudgetVarianceAlertsProps) {
  const displayedVariances = variances.slice(0, maxAlerts);
  const hasMoreAlerts = variances.length > maxAlerts;

  if (variances.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {displayedVariances.map((variance) => (
        <BudgetVarianceAlert
          key={variance.id}
          variance={variance}
          onDismiss={onDismiss}
          onMarkAsRead={onMarkAsRead}
          onTakeAction={onTakeAction}
          showActions={showActions}
          compact={compact}
        />
      ))}
      
      {hasMoreAlerts && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {variances.length - maxAlerts} more budget variance alerts available.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Link2, 
  Unlink, 
  Target, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface BudgetLinkData {
  transactionId: string;
  transactionType: 'income' | 'expense';
  transactionAmount: number;
  transactionDate: Date;
  budgetId: string;
  budgetName: string;
  categoryId: string;
  categoryName: string;
  subcategoryId?: string;
  subcategoryName?: string;
  isLinked: boolean;
  linkStatus: 'linked' | 'pending' | 'failed' | 'unlinked';
  budgetImpact: {
    beforeAmount: number;
    afterAmount: number;
    utilizationBefore: number;
    utilizationAfter: number;
    remainingBudget: number;
    isOverBudget: boolean;
  };
  lastUpdated: Date;
}

interface TransactionBudgetLinkProps {
  transactionId: string;
  transactionType: 'income' | 'expense';
  budgetId?: string;
  categoryId?: string;
  subcategoryId?: string;
  amount: number;
  onLinkChange?: (isLinked: boolean, budgetData?: any) => void;
  showImpactPreview?: boolean;
  allowUnlink?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  className?: string;
}

export function TransactionBudgetLink({
  transactionId,
  transactionType,
  budgetId,
  categoryId,
  subcategoryId,
  amount,
  onLinkChange,
  showImpactPreview = true,
  allowUnlink = true,
  autoRefresh = false,
  refreshInterval = 30000,
  className = ''
}: TransactionBudgetLinkProps) {
  const [linkData, setLinkData] = useState<BudgetLinkData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  
  const isMobile = useIsMobile();

  // Fetch budget link data
  const fetchLinkData = async () => {
    if (!budgetId || !categoryId) {
      setLinkData(null);
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      const params = new URLSearchParams({
        transactionId,
        transactionType,
        budgetId,
        categoryId,
        amount: amount.toString()
      });

      if (subcategoryId) {
        params.append('subcategoryId', subcategoryId);
      }

      const response = await fetch(`/api/accounting/budget/transaction-link?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch budget link data');
      }
      
      const data = await response.json();
      setLinkData(data);
      
      // Notify parent component of link status
      onLinkChange?.(data.isLinked, data);
    } catch (error) {
      console.error('Error fetching budget link data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch budget link data');
    } finally {
      setIsLoading(false);
    }
  };

  // Link transaction to budget
  const linkToBudget = async () => {
    if (!budgetId || !categoryId) return;

    setIsUpdating(true);
    try {
      const response = await fetch('/api/accounting/budget/transaction-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId,
          transactionType,
          budgetId,
          categoryId,
          subcategoryId,
          amount
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to link transaction to budget');
      }

      await fetchLinkData();
    } catch (error) {
      console.error('Error linking transaction to budget:', error);
      setError(error instanceof Error ? error.message : 'Failed to link transaction');
    } finally {
      setIsUpdating(false);
    }
  };

  // Unlink transaction from budget
  const unlinkFromBudget = async () => {
    if (!linkData) return;

    setIsUpdating(true);
    try {
      const response = await fetch('/api/accounting/budget/transaction-link', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId,
          budgetId: linkData.budgetId,
          categoryId: linkData.categoryId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to unlink transaction from budget');
      }

      await fetchLinkData();
    } catch (error) {
      console.error('Error unlinking transaction from budget:', error);
      setError(error instanceof Error ? error.message : 'Failed to unlink transaction');
    } finally {
      setIsUpdating(false);
    }
  };

  // Initial load and auto-refresh
  useEffect(() => {
    fetchLinkData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchLinkData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [transactionId, budgetId, categoryId, subcategoryId, amount, autoRefresh, refreshInterval]);

  // Get status icon
  const getStatusIcon = () => {
    if (!linkData) return <Unlink className="h-4 w-4 text-gray-400" />;
    
    switch (linkData.linkStatus) {
      case 'linked':
        return <Link2 className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'unlinked':
      default:
        return <Unlink className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = () => {
    if (!linkData) return 'outline';
    
    switch (linkData.linkStatus) {
      case 'linked':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'unlinked':
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!budgetId || !categoryId) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-2 text-muted-foreground">
            <Unlink className="h-4 w-4" />
            <span className="text-sm">No budget selected</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">Budget Link</CardTitle>
          </div>
          <Badge variant={getStatusBadgeVariant()}>
            {linkData?.linkStatus || 'unlinked'}
          </Badge>
        </div>
        {linkData && (
          <CardDescription>
            {linkData.budgetName} • {linkData.categoryName}
            {linkData.subcategoryName && ` • ${linkData.subcategoryName}`}
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Link Status and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">
              {linkData?.isLinked ? 'Linked to Budget' : 'Not Linked'}
            </span>
            {linkData?.isLinked && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {!linkData?.isLinked ? (
              <Button
                size="sm"
                onClick={linkToBudget}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Link2 className="h-4 w-4 mr-2" />
                )}
                Link to Budget
              </Button>
            ) : (
              allowUnlink && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={unlinkFromBudget}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Unlink className="h-4 w-4 mr-2" />
                  )}
                  Unlink
                </Button>
              )
            )}
          </div>
        </div>

        {/* Budget Impact Preview */}
        {showImpactPreview && linkData?.budgetImpact && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Budget Impact</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Before Transaction</p>
                <p className="text-sm font-medium">
                  {formatCurrency(linkData.budgetImpact.beforeAmount)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {linkData.budgetImpact.utilizationBefore.toFixed(1)}% utilized
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">After Transaction</p>
                <p className="text-sm font-medium">
                  {formatCurrency(linkData.budgetImpact.afterAmount)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {linkData.budgetImpact.utilizationAfter.toFixed(1)}% utilized
                </p>
              </div>
            </div>

            {/* Utilization Progress */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Budget Utilization</span>
                <span className="text-xs font-medium">
                  {linkData.budgetImpact.utilizationAfter.toFixed(1)}%
                </span>
              </div>
              <Progress 
                value={Math.min(100, linkData.budgetImpact.utilizationAfter)} 
                className="h-2"
              />
            </div>

            {/* Remaining Budget */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Remaining Budget</span>
              <span className="text-sm font-medium">
                {formatCurrency(linkData.budgetImpact.remainingBudget)}
              </span>
            </div>

            {/* Over Budget Warning */}
            {linkData.budgetImpact.isOverBudget && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This transaction will exceed the budget allocation for this category.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Last Updated */}
        {linkData && (
          <div className="text-xs text-muted-foreground">
            Last updated: {linkData.lastUpdated.toLocaleString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

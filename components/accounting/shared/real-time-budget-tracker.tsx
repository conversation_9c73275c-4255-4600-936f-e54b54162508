'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  DollarSign,
  Target,
  Activity
} from 'lucide-react';
import { useIsMobile } from '@/lib/hooks/use-responsive';
import { formatCurrency } from '@/lib/utils/currency';

interface BudgetTrackerData {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  totalBudgeted: number;
  totalActual: number;
  totalRemaining: number;
  utilizationPercentage: number;
  categories: {
    id: string;
    name: string;
    type: 'income' | 'expense';
    budgeted: number;
    actual: number;
    remaining: number;
    utilizationPercentage: number;
    status: 'on-track' | 'warning' | 'exceeded' | 'under-utilized';
  }[];
  lastUpdated: Date;
  trends: {
    monthlyChange: number;
    weeklyChange: number;
    projectedEndOfYear: number;
  };
}

interface RealTimeBudgetTrackerProps {
  budgetId: string;
  refreshInterval?: number; // in milliseconds, default 30 seconds
  showCategories?: boolean;
  showTrends?: boolean;
  compact?: boolean;
  className?: string;
}

export function RealTimeBudgetTracker({
  budgetId,
  refreshInterval = 30000,
  showCategories = true,
  showTrends = true,
  compact = false,
  className = ''
}: RealTimeBudgetTrackerProps) {
  const [data, setData] = useState<BudgetTrackerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  const isMobile = useIsMobile();

  // Fetch budget data
  const fetchBudgetData = async () => {
    try {
      setError(null);
      const response = await fetch(`/api/accounting/budget/${budgetId}/real-time-tracker`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch budget data');
      }
      
      const budgetData = await response.json();
      setData(budgetData);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching budget data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch budget data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchBudgetData();
    
    const interval = setInterval(fetchBudgetData, refreshInterval);
    return () => clearInterval(interval);
  }, [budgetId, refreshInterval]);

  // Get status color based on utilization
  const getStatusColor = (utilizationPercentage: number, type: 'income' | 'expense') => {
    if (type === 'income') {
      if (utilizationPercentage >= 90) return 'text-green-600';
      if (utilizationPercentage >= 70) return 'text-blue-600';
      if (utilizationPercentage >= 50) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (utilizationPercentage > 100) return 'text-red-600';
      if (utilizationPercentage > 90) return 'text-yellow-600';
      if (utilizationPercentage > 75) return 'text-blue-600';
      return 'text-green-600';
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'on-track': return 'default';
      case 'warning': return 'secondary';
      case 'exceeded': return 'destructive';
      case 'under-utilized': return 'outline';
      default: return 'default';
    }
  };

  // Get progress bar color
  const getProgressColor = (utilizationPercentage: number, type: 'income' | 'expense') => {
    if (type === 'expense') {
      if (utilizationPercentage > 100) return 'bg-red-500';
      if (utilizationPercentage > 90) return 'bg-yellow-500';
      if (utilizationPercentage > 75) return 'bg-blue-500';
      return 'bg-green-500';
    } else {
      if (utilizationPercentage >= 90) return 'bg-green-500';
      if (utilizationPercentage >= 70) return 'bg-blue-500';
      if (utilizationPercentage >= 50) return 'bg-yellow-500';
      return 'bg-red-500';
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className={compact ? 'pb-2' : ''}>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className={compact ? 'pb-2' : ''}>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className={compact ? 'text-lg' : 'text-xl'}>
              {data.budgetName}
            </CardTitle>
            <CardDescription>
              {data.fiscalYear} • Last updated: {lastRefresh.toLocaleTimeString()}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-green-500 animate-pulse" />
            <Badge variant="outline" className="text-xs">
              Live
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Budget Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Budgeted</span>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(data.totalBudgeted)}
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Actual</span>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(data.totalActual)}
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Remaining</span>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(data.totalRemaining)}
            </p>
          </div>
        </div>

        {/* Overall Utilization */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Utilization</span>
            <span className={`text-sm font-bold ${getStatusColor(data.utilizationPercentage, 'expense')}`}>
              {data.utilizationPercentage.toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={Math.min(100, data.utilizationPercentage)} 
            className="h-2"
          />
        </div>

        {/* Trends Section */}
        {showTrends && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Trends</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="flex items-center space-x-2">
                {data.trends.monthlyChange >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <div>
                  <p className="text-xs text-muted-foreground">Monthly Change</p>
                  <p className="text-sm font-medium">
                    {data.trends.monthlyChange >= 0 ? '+' : ''}
                    {data.trends.monthlyChange.toFixed(1)}%
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {data.trends.weeklyChange >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <div>
                  <p className="text-xs text-muted-foreground">Weekly Change</p>
                  <p className="text-sm font-medium">
                    {data.trends.weeklyChange >= 0 ? '+' : ''}
                    {data.trends.weeklyChange.toFixed(1)}%
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-xs text-muted-foreground">Projected EOY</p>
                  <p className="text-sm font-medium">
                    {data.trends.projectedEndOfYear.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Categories Breakdown */}
        {showCategories && data.categories.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Category Breakdown</h4>
            <div className="space-y-3">
              {data.categories.map((category) => (
                <div key={category.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{category.name}</span>
                      <Badge 
                        variant={getStatusBadgeVariant(category.status)}
                        className="text-xs"
                      >
                        {category.status.replace('-', ' ')}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {formatCurrency(category.actual)} / {formatCurrency(category.budgeted)}
                      </p>
                      <p className={`text-xs ${getStatusColor(category.utilizationPercentage, category.type)}`}>
                        {category.utilizationPercentage.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                  <Progress 
                    value={Math.min(100, category.utilizationPercentage)} 
                    className="h-1.5"
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

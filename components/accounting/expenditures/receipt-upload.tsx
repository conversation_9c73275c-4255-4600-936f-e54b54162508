"use client"

import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Upload,
  FileImage,
  X,
  CheckCircle,
  AlertTriangle,
  Eye,
  RotateCcw,
  Download,
  Camera,
  Scan,
  FileText,
  Loader2
} from 'lucide-react';
import { 
  ReceiptData, 
  ReceiptProcessingStatus, 
  receiptProcessingService 
} from '@/lib/services/accounting/receipt-processing-service';

interface ReceiptUploadProps {
  expenditureId?: string;
  onReceiptProcessed?: (receipt: ReceiptData) => void;
  onExtractedDataUpdate?: (data: any) => void;
  maxFiles?: number;
  disabled?: boolean;
}

interface UploadedFile {
  file: File;
  id: string;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  progress: number;
  receiptData?: ReceiptData;
  error?: string;
  preview?: string;
}

export function ReceiptUpload({
  expenditureId,
  onReceiptProcessed,
  onExtractedDataUpdate,
  maxFiles = 5,
  disabled = false
}: ReceiptUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (disabled) return;

    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      file,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'uploading',
      progress: 0,
      preview: URL.createObjectURL(file)
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    
    // Process each file
    for (const uploadedFile of newFiles) {
      await processFile(uploadedFile);
    }
  }, [disabled, expenditureId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
      'application/pdf': ['.pdf']
    },
    maxFiles: maxFiles - uploadedFiles.length,
    disabled: disabled || uploadedFiles.length >= maxFiles
  });

  // Process individual file
  const processFile = async (uploadedFile: UploadedFile) => {
    try {
      setIsProcessing(true);
      
      // Update status to processing
      updateFileStatus(uploadedFile.id, 'processing', 10);
      
      // Simulate upload progress
      for (let progress = 20; progress <= 50; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        updateFileStatus(uploadedFile.id, 'processing', progress);
      }
      
      // Process receipt
      const receiptData = await receiptProcessingService.processReceipt(
        uploadedFile.file,
        expenditureId || 'temp',
        'current-user'
      );
      
      // Update progress to completion
      for (let progress = 60; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        updateFileStatus(uploadedFile.id, 'processing', progress);
      }
      
      // Mark as completed
      updateFileStatus(uploadedFile.id, 'completed', 100, receiptData);
      
      // Notify parent components
      onReceiptProcessed?.(receiptData);
      if (receiptData.extractedData) {
        onExtractedDataUpdate?.(receiptData.extractedData);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Processing failed';
      updateFileStatus(uploadedFile.id, 'failed', 0, undefined, errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Update file status
  const updateFileStatus = (
    fileId: string, 
    status: UploadedFile['status'], 
    progress: number,
    receiptData?: ReceiptData,
    error?: string
  ) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, status, progress, receiptData, error }
        : file
    ));
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  };

  // Reprocess file
  const reprocessFile = async (fileId: string) => {
    const file = uploadedFiles.find(f => f.id === fileId);
    if (file) {
      await processFile(file);
    }
  };

  // Get status color
  const getStatusColor = (status: UploadedFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return 'blue';
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Get status icon
  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <FileImage className="h-4 w-4" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Scan className="h-5 w-5" />
          Receipt Upload & Processing
        </CardTitle>
        <CardDescription>
          Upload receipt images or PDFs for automatic data extraction
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Area */}
        {uploadedFiles.length < maxFiles && (
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input {...getInputProps()} ref={fileInputRef} />
            <div className="space-y-2">
              <Upload className="h-8 w-8 mx-auto text-gray-400" />
              <div>
                <p className="text-sm font-medium">
                  {isDragActive ? 'Drop files here' : 'Drag & drop receipts here'}
                </p>
                <p className="text-xs text-muted-foreground">
                  or click to browse (JPEG, PNG, WebP, PDF up to 10MB)
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Camera Capture Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || uploadedFiles.length >= maxFiles}
            className="gap-2"
          >
            <Camera className="h-4 w-4" />
            Take Photo
          </Button>
        </div>

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-3">
            <Separator />
            <h4 className="font-medium">Uploaded Receipts ({uploadedFiles.length}/{maxFiles})</h4>
            
            {uploadedFiles.map((file) => (
              <div key={file.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      {file.preview && (
                        <img
                          src={file.preview}
                          alt="Receipt preview"
                          className="w-12 h-12 object-cover rounded border"
                        />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{file.file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(file.file.size)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={`border-${getStatusColor(file.status)}-500`}>
                      {getStatusIcon(file.status)}
                      <span className="ml-1 capitalize">{file.status}</span>
                    </Badge>
                    
                    {file.status === 'failed' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => reprocessFile(file.id)}
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Progress Bar */}
                {(file.status === 'uploading' || file.status === 'processing') && (
                  <div className="space-y-1">
                    <Progress value={file.progress} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      {file.status === 'uploading' ? 'Uploading...' : 'Processing with OCR...'}
                    </p>
                  </div>
                )}

                {/* Error Message */}
                {file.status === 'failed' && file.error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{file.error}</AlertDescription>
                  </Alert>
                )}

                {/* Extracted Data Preview */}
                {file.status === 'completed' && file.receiptData?.extractedData && (
                  <div className="bg-green-50 border border-green-200 rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-sm text-green-800">Extracted Data</h5>
                      <Badge variant="outline" className="border-green-500 text-green-700">
                        {file.receiptData.ocrConfidence?.toFixed(1)}% confidence
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      {file.receiptData.extractedData.merchantName && (
                        <div>
                          <span className="font-medium">Merchant:</span>
                          <p>{file.receiptData.extractedData.merchantName}</p>
                        </div>
                      )}
                      
                      {file.receiptData.extractedData.totalAmount && (
                        <div>
                          <span className="font-medium">Amount:</span>
                          <p>
                            {file.receiptData.extractedData.currency} {file.receiptData.extractedData.totalAmount.toLocaleString()}
                          </p>
                        </div>
                      )}
                      
                      {file.receiptData.extractedData.transactionDate && (
                        <div>
                          <span className="font-medium">Date:</span>
                          <p>{file.receiptData.extractedData.transactionDate.toLocaleDateString()}</p>
                        </div>
                      )}
                      
                      {file.receiptData.extractedData.paymentMethod && (
                        <div>
                          <span className="font-medium">Payment:</span>
                          <p>{file.receiptData.extractedData.paymentMethod}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Processing receipts with OCR technology...
            </AlertDescription>
          </Alert>
        )}

        {/* Help Text */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Ensure receipts are clear and well-lit for best OCR results</p>
          <p>• Supported formats: JPEG, PNG, WebP, PDF (max 10MB each)</p>
          <p>• Extracted data will be automatically filled in the form</p>
        </div>
      </CardContent>
    </Card>
  );
}

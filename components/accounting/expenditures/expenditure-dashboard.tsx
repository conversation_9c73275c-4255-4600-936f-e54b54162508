"use client"

import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Plus,
  AlertTriangle,
  TrendingUp,
  BarChart4,
  FileText,
  Settings
} from 'lucide-react';
import {
  useExpenditures,
  useExpenditureStatistics,
  useExpenditureManagement
} from '@/lib/hooks/accounting/use-expenditure-management';
import {
  ExpenditureCategory,
  ExpenditureStatus,
  ExpenditurePriority
} from '@/types/accounting/expenditure';

// Import our component chunks
import { ExpenditureMetrics, ExpenditureDetailedMetrics, ExpenditureQuickActions } from './expenditure-metrics';
import {
  ExpenditureOverviewCharts,
  CategoryBreakdown<PERSON>hart,
  MonthlyTrendChart
} from './expenditure-charts';
import { ExpenditureTable } from './expenditure-table';

interface ExpenditureDashboardProps {
  onCreateExpenditure?: () => void;
  onViewExpenditure?: (expenditureId: string) => void;
  onEditExpenditure?: (expenditureId: string) => void;
}

export function ExpenditureDashboard({
  onCreateExpenditure,
  onViewExpenditure,
  onEditExpenditure
}: ExpenditureDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const { deleteExpenditure } = useExpenditureManagement();

  // Calculate date range based on selected period
  const dateRange = useMemo(() => {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }
    
    return { startDate, endDate };
  }, [selectedPeriod]);

  // Get expenditure statistics
  const {
    data: statisticsData,
    isLoading: isLoadingStatistics,
    error: statisticsError
  } = useExpenditureStatistics(
    dateRange.startDate,
    dateRange.endDate,
    {
      department: selectedDepartment !== 'all' ? [selectedDepartment] : undefined,
      category: selectedCategory !== 'all' ? [selectedCategory as ExpenditureCategory] : undefined
    }
  );

  // Get recent expenditures
  const {
    data: expendituresData,
    isLoading: isLoadingExpenditures,
    error: expendituresError
  } = useExpenditures(
    {
      department: selectedDepartment !== 'all' ? [selectedDepartment] : undefined,
      category: selectedCategory !== 'all' ? [selectedCategory as ExpenditureCategory] : undefined,
      dateRange
    },
    1,
    20,
    'createdAt',
    'desc'
  );

  const statistics = statisticsData?.statistics;
  const expenditures = expendituresData?.expenditures || [];

  // Handle bulk actions
  const handleBulkAction = async (action: string, expenditureIds: string[]) => {
    switch (action) {
      case 'delete':
        for (const id of expenditureIds) {
          await deleteExpenditure(id);
        }
        break;
      case 'approve':
        // TODO: Implement bulk approval
        console.log('Bulk approve:', expenditureIds);
        break;
      case 'reject':
        // TODO: Implement bulk rejection
        console.log('Bulk reject:', expenditureIds);
        break;
      case 'export':
        // TODO: Implement bulk export
        console.log('Bulk export:', expenditureIds);
        break;
    }
  };

  // Handle delete expenditure
  const handleDeleteExpenditure = async (expenditureId: string) => {
    await deleteExpenditure(expenditureId);
  };

  if (statisticsError || expendituresError) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error Loading Dashboard</AlertTitle>
        <AlertDescription>
          {statisticsError?.message || expendituresError?.message || 'Failed to load expenditure data'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Expenditure Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor spending, track budgets, and manage vendor payments
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="Administration">Administration</SelectItem>
              <SelectItem value="Finance Department">Finance Department</SelectItem>
              <SelectItem value="ICT">ICT</SelectItem>
              <SelectItem value="Compliance">Compliance</SelectItem>
              <SelectItem value="Store">Store</SelectItem>
              <SelectItem value="Procurement">Procurement</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="operational">Operational</SelectItem>
              <SelectItem value="capital">Capital</SelectItem>
              <SelectItem value="personnel">Personnel</SelectItem>
              <SelectItem value="administrative">Administrative</SelectItem>
              <SelectItem value="travel">Travel</SelectItem>
              <SelectItem value="utilities">Utilities</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="supplies">Supplies</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={onCreateExpenditure} className="gap-1">
            <Plus className="h-4 w-4" />
            <span>New Expenditure</span>
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <ExpenditureMetrics
        statistics={statistics}
        isLoading={isLoadingStatistics}
        period={selectedPeriod}
      />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="gap-1">
            <BarChart4 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="analytics" className="gap-1">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="recent" className="gap-1">
            <FileText className="h-4 w-4" />
            Recent
          </TabsTrigger>
          <TabsTrigger value="detailed" className="gap-1">
            <Settings className="h-4 w-4" />
            Detailed
          </TabsTrigger>
          <TabsTrigger value="actions" className="gap-1">
            <AlertTriangle className="h-4 w-4" />
            Actions
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <ExpenditureOverviewCharts
            statistics={statistics}
            isLoading={isLoadingStatistics}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4">
            <MonthlyTrendChart
              statistics={statistics}
              isLoading={isLoadingStatistics}
            />
            <CategoryBreakdownChart
              statistics={statistics}
              isLoading={isLoadingStatistics}
            />
          </div>
        </TabsContent>

        {/* Recent Tab */}
        <TabsContent value="recent" className="space-y-4">
          <ExpenditureTable
            expenditures={expenditures}
            isLoading={isLoadingExpenditures}
            onViewExpenditure={onViewExpenditure}
            onEditExpenditure={onEditExpenditure}
            onDeleteExpenditure={handleDeleteExpenditure}
            onBulkAction={handleBulkAction}
          />
        </TabsContent>

        {/* Detailed Tab */}
        <TabsContent value="detailed" className="space-y-4">
          <ExpenditureDetailedMetrics
            statistics={statistics}
            isLoading={isLoadingStatistics}
          />
          <div className="grid gap-4 md:grid-cols-2">
            <CategoryBreakdownChart
              statistics={statistics}
              isLoading={isLoadingStatistics}
            />
            <MonthlyTrendChart
              statistics={statistics}
              isLoading={isLoadingStatistics}
            />
          </div>
        </TabsContent>

        {/* Actions Tab */}
        <TabsContent value="actions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <ExpenditureQuickActions statistics={statistics} />
            <div className="space-y-4">
              {/* Additional action cards can go here */}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ScatterChart,
  Scatter
} from 'recharts';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Lightbulb,
  Zap,
  BarChart3,
  PieChart,
  Activity,
  RefreshCw,
  Download,
  Settings,
  Eye,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  aiAnalyticsService,
  SpendingPattern,
  SpendingForecast,
  SpendingAnomaly,
  BudgetOptimization,
  VendorInsight,
  TrendDirection,
  AnomalySeverity,
  RiskLevel
} from '@/lib/services/accounting/ai-analytics-service';
import { ExpenditureCategory } from '@/types/accounting/expenditure';

interface AIAnalyticsDashboardProps {
  expenditureData?: any[];
  onInsightAction?: (action: string, data: any) => void;
}

export function AIAnalyticsDashboard({ expenditureData = [], onInsightAction }: AIAnalyticsDashboardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [spendingPatterns, setSpendingPatterns] = useState<SpendingPattern[]>([]);
  const [forecasts, setForecast] = useState<SpendingForecast[]>([]);
  const [anomalies, setAnomalies] = useState<SpendingAnomaly[]>([]);
  const [budgetOptimizations, setBudgetOptimizations] = useState<BudgetOptimization[]>([]);
  const [vendorInsights, setVendorInsights] = useState<VendorInsight[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load AI analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(endDate.getMonth() - 12); // Last 12 months

      // Load spending patterns
      const patterns = await aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate);
      setSpendingPatterns(patterns);

      // Generate forecasts for key categories
      const forecastPromises = Object.values(ExpenditureCategory).slice(0, 3).map(category =>
        aiAnalyticsService.generateSpendingForecast(category, 'Administration', 6)
      );
      const forecastResults = await Promise.all(forecastPromises);
      setForecast(forecastResults.flat());

      // Detect anomalies
      if (expenditureData.length > 0) {
        const detectedAnomalies = await aiAnalyticsService.detectAnomalies(expenditureData);
        setAnomalies(detectedAnomalies);
      }

      // Generate budget optimizations
      const mockBudgets = [
        { department: 'Administration', category: ExpenditureCategory.OPERATIONAL, allocated: 100000, spent: 65000 },
        { department: 'ICT', category: ExpenditureCategory.TECHNOLOGY, allocated: 200000, spent: 195000 },
        { department: 'Finance', category: ExpenditureCategory.PROFESSIONAL_SERVICES, allocated: 80000, spent: 45000 }
      ];
      const optimizations = await aiAnalyticsService.optimizeBudgetAllocation(mockBudgets);
      setBudgetOptimizations(optimizations);

      // Analyze vendor performance
      const vendorAnalyses = await Promise.all([
        aiAnalyticsService.analyzeVendorPerformance('vendor-1'),
        aiAnalyticsService.analyzeVendorPerformance('vendor-2'),
        aiAnalyticsService.analyzeVendorPerformance('vendor-3')
      ]);
      setVendorInsights(vendorAnalyses);

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load AI analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get trend icon
  const getTrendIcon = (trend: TrendDirection) => {
    switch (trend) {
      case TrendDirection.INCREASING:
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case TrendDirection.DECREASING:
        return <TrendingDown className="h-4 w-4 text-green-500" />;
      case TrendDirection.STABLE:
        return <Activity className="h-4 w-4 text-blue-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get risk color
  const getRiskColor = (risk: RiskLevel) => {
    switch (risk) {
      case RiskLevel.LOW:
        return 'green';
      case RiskLevel.MEDIUM:
        return 'yellow';
      case RiskLevel.HIGH:
        return 'orange';
      case RiskLevel.CRITICAL:
        return 'red';
      default:
        return 'gray';
    }
  };

  // Get anomaly severity color
  const getAnomalySeverityColor = (severity: AnomalySeverity) => {
    switch (severity) {
      case AnomalySeverity.LOW:
        return 'blue';
      case AnomalySeverity.MEDIUM:
        return 'yellow';
      case AnomalySeverity.HIGH:
        return 'orange';
      case AnomalySeverity.CRITICAL:
        return 'red';
      default:
        return 'gray';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            AI-Powered Analytics
          </h2>
          <p className="text-muted-foreground">
            Intelligent insights and predictions for expenditure management
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Badge>
          <Button variant="outline" size="sm" onClick={loadAnalyticsData}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Key AI Insights */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Patterns Detected</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{spendingPatterns.length}</div>
            <p className="text-xs text-muted-foreground">
              Spending patterns identified
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Anomalies Found</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{anomalies.length}</div>
            <p className="text-xs text-muted-foreground">
              Require investigation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Optimization Opportunities</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{budgetOptimizations.length}</div>
            <p className="text-xs text-muted-foreground">
              Budget optimizations available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Forecast Accuracy</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87.3%</div>
            <p className="text-xs text-muted-foreground">
              Average prediction accuracy
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="patterns" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
        </TabsList>

        {/* Spending Patterns Tab */}
        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Spending Patterns Analysis</CardTitle>
              <CardDescription>
                AI-identified patterns in organizational spending behavior
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {spendingPatterns.slice(0, 6).map((pattern, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getTrendIcon(pattern.trend)}
                      <div>
                        <p className="font-medium">
                          {pattern.category.replace(/_/g, ' ')} - {pattern.department}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Avg: {formatCurrency(pattern.averageAmount)} | Frequency: {pattern.frequency}/month
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className={`border-${pattern.confidence > 0.8 ? 'green' : 'yellow'}-500`}>
                        {(pattern.confidence * 100).toFixed(0)}% confidence
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Forecasts Tab */}
        <TabsContent value="forecasts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Spending Forecasts</CardTitle>
              <CardDescription>
                AI-generated predictions for future expenditures
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={forecasts.slice(0, 12)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="predictedAmount" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    name="Predicted Amount"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Anomalies Tab */}
        <TabsContent value="anomalies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Spending Anomalies</CardTitle>
              <CardDescription>
                AI-detected unusual spending patterns requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {anomalies.length > 0 ? anomalies.map((anomaly) => (
                  <Alert key={anomaly.id} variant={anomaly.severity === AnomalySeverity.HIGH ? 'destructive' : 'default'}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle className="flex items-center justify-between">
                      <span>{anomaly.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                      <Badge variant="outline" className={`border-${getAnomalySeverityColor(anomaly.severity)}-500`}>
                        {anomaly.severity}
                      </Badge>
                    </AlertTitle>
                    <AlertDescription>
                      <p>{anomaly.description}</p>
                      <p className="text-sm mt-2">
                        <strong>Suggested Action:</strong> {anomaly.suggestedAction}
                      </p>
                      <div className="flex items-center justify-between mt-3">
                        <span className="text-xs">
                          Confidence: {(anomaly.confidence * 100).toFixed(0)}%
                        </span>
                        <div className="space-x-2">
                          <Button size="sm" variant="outline" onClick={() => onInsightAction?.('view', anomaly)}>
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => onInsightAction?.('resolve', anomaly)}>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Resolve
                          </Button>
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>
                )) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <p>No anomalies detected. Your spending patterns look normal!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Budget Optimization Tab */}
        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Budget Optimization Recommendations</CardTitle>
              <CardDescription>
                AI-suggested budget adjustments for improved efficiency
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {budgetOptimizations.map((optimization, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">
                          {optimization.department} - {optimization.category.replace(/_/g, ' ')}
                        </h4>
                        <p className="text-sm text-muted-foreground">{optimization.reasoning}</p>
                      </div>
                      <Badge variant="outline" className="border-green-500 text-green-700">
                        {(optimization.confidence * 100).toFixed(0)}% confidence
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Current:</span>
                        <p>{formatCurrency(optimization.currentAllocation)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Suggested:</span>
                        <p>{formatCurrency(optimization.suggestedAllocation)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Savings:</span>
                        <p className={optimization.potentialSavings > 0 ? 'text-green-600' : 'text-red-600'}>
                          {formatCurrency(Math.abs(optimization.potentialSavings))}
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button size="sm" variant="outline" onClick={() => onInsightAction?.('view', optimization)}>
                        <Eye className="h-3 w-3 mr-1" />
                        Details
                      </Button>
                      <Button size="sm" onClick={() => onInsightAction?.('implement', optimization)}>
                        <Target className="h-3 w-3 mr-1" />
                        Implement
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Vendor Analysis Tab */}
        <TabsContent value="vendors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vendor Performance Insights</CardTitle>
              <CardDescription>
                AI analysis of vendor performance and recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vendorInsights.map((vendor, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{vendor.vendorName}</h4>
                      <Badge variant="outline" className={`border-${getRiskColor(vendor.riskAssessment)}-500`}>
                        {vendor.riskAssessment} risk
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <span className="text-sm font-medium">Performance</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={vendor.performanceScore} className="flex-1" />
                          <span className="text-sm">{vendor.performanceScore.toFixed(0)}%</span>
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium">Cost Efficiency</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={vendor.costEfficiency} className="flex-1" />
                          <span className="text-sm">{vendor.costEfficiency.toFixed(0)}%</span>
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium">Reliability</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={vendor.reliabilityScore} className="flex-1" />
                          <span className="text-sm">{vendor.reliabilityScore.toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>

                    {vendor.recommendations.length > 0 && (
                      <div className="space-y-2">
                        <span className="text-sm font-medium">Recommendations:</span>
                        {vendor.recommendations.map((rec, recIndex) => (
                          <div key={recIndex} className="text-sm bg-blue-50 p-2 rounded">
                            <Lightbulb className="h-3 w-3 inline mr-1" />
                            {rec.description} (Impact: {rec.potentialImpact}%)
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client"

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CreditCard,
  TrendingUp,
  Clock,
  AlertTriangle,
  DollarSign,
  Users,
  CheckCircle,
  XCircle
} from 'lucide-react';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

interface ExpenditureStatistics {
  totalExpenditures: number;
  totalAmount: number;
  averageAmount: number;
  pendingApprovals: number;
  pendingPayments: number;
  overBudgetCount: number;
  urgentCount: number;
}

interface ExpenditureMetricsProps {
  statistics?: ExpenditureStatistics;
  isLoading?: boolean;
  period?: string;
}

export function ExpenditureMetrics({ statistics, isLoading, period = 'month' }: ExpenditureMetricsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-3 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const metrics = [
    {
      title: 'Total Expenditures',
      value: formatCurrency(statistics?.totalAmount || 0),
      subtitle: `${statistics?.totalExpenditures || 0} transactions`,
      icon: CreditCard,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Average Amount',
      value: formatCurrency(statistics?.averageAmount || 0),
      subtitle: 'Per transaction',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Pending Approvals',
      value: statistics?.pendingApprovals || 0,
      subtitle: 'Awaiting approval',
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    },
    {
      title: 'Urgent Items',
      value: statistics?.urgentCount || 0,
      subtitle: 'Require attention',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => {
        const Icon = metric.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${metric.bgColor}`}>
                <Icon className={`h-4 w-4 ${metric.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {typeof metric.value === 'number' ? metric.value.toLocaleString() : metric.value}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {metric.subtitle}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

// Additional metrics for detailed view
export function ExpenditureDetailedMetrics({ statistics, isLoading }: ExpenditureMetricsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-6 w-24 mb-2" />
              <Skeleton className="h-3 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const detailedMetrics = [
    {
      title: 'Pending Payments',
      value: statistics?.pendingPayments || 0,
      subtitle: 'Approved, awaiting payment',
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Over Budget',
      value: statistics?.overBudgetCount || 0,
      subtitle: 'Exceeding budget limits',
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: 'Completed',
      value: (statistics?.totalExpenditures || 0) - (statistics?.pendingApprovals || 0) - (statistics?.pendingPayments || 0),
      subtitle: 'Fully processed',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-3">
      {detailedMetrics.map((metric, index) => {
        const Icon = metric.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${metric.bgColor}`}>
                <Icon className={`h-4 w-4 ${metric.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metric.value.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {metric.subtitle}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

// Quick action metrics
export function ExpenditureQuickActions({ statistics }: { statistics?: ExpenditureStatistics }) {
  const actions = [
    {
      title: 'Review Approvals',
      count: statistics?.pendingApprovals || 0,
      action: 'review-approvals',
      urgent: (statistics?.pendingApprovals || 0) > 5,
      icon: Clock
    },
    {
      title: 'Process Payments',
      count: statistics?.pendingPayments || 0,
      action: 'process-payments',
      urgent: (statistics?.pendingPayments || 0) > 10,
      icon: DollarSign
    },
    {
      title: 'Handle Urgent',
      count: statistics?.urgentCount || 0,
      action: 'handle-urgent',
      urgent: (statistics?.urgentCount || 0) > 0,
      icon: AlertTriangle
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <div
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors ${
                  action.urgent ? 'border-red-200 bg-red-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${action.urgent ? 'bg-red-100' : 'bg-gray-100'}`}>
                    <Icon className={`h-4 w-4 ${action.urgent ? 'text-red-600' : 'text-gray-600'}`} />
                  </div>
                  <div>
                    <p className="font-medium">{action.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {action.count} items
                    </p>
                  </div>
                </div>
                {action.urgent && (
                  <div className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                    Urgent
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

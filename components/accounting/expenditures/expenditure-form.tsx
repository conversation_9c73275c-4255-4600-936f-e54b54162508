"use client"

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Plus,
  Minus,
  Upload,
  FileText,
  Calculator,
  AlertTriangle,
  CheckCircle,
  Building,
  User,
  Calendar,
  DollarSign,
  Tag,
  Save,
  X
} from 'lucide-react';
import {
  ExpenditureCategory,
  ExpenditurePriority,
  PaymentMethod
} from '@/types/accounting/expenditure';
import { useExpenditureManagement } from '@/lib/hooks/accounting/use-expenditure-management';
import { ReceiptUpload } from './receipt-upload';
import { useBudget } from '@/lib/hooks/accounting/use-budget';

// Form validation schema
const expenditureFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description too long'),
  category: z.nativeEnum(ExpenditureCategory),
  subcategory: z.string().min(1, 'Subcategory is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().min(0).default(1),
  expenditureDate: z.date(),
  dueDate: z.date().optional(),
  priority: z.nativeEnum(ExpenditurePriority).default(ExpenditurePriority.MEDIUM),
  department: z.string().min(1, 'Department is required'),
  costCenter: z.string().optional(),
  vendor: z.object({
    vendorId: z.string().optional(),
    vendorName: z.string().min(1, 'Vendor name is required'),
    vendorEmail: z.string().email().optional().or(z.literal('')),
    vendorPhone: z.string().optional(),
    vendorAddress: z.string().optional()
  }),
  budgetAllocations: z.array(z.object({
    budgetId: z.string().min(1, 'Budget is required'),
    allocatedAmount: z.number().min(0, 'Amount must be positive'),
    percentage: z.number().min(0).max(100, 'Percentage must be between 0-100')
  })).min(1, 'At least one budget allocation is required'),
  taxInfo: z.object({
    taxType: z.enum(['VAT', 'withholding', 'excise', 'none']).default('none'),
    taxRate: z.number().min(0).max(100).default(0),
    isExempt: z.boolean().default(false),
    exemptionReason: z.string().optional()
  }).optional(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  tags: z.array(z.string()).default([]),
  notes: z.array(z.string()).default([]),
  isUrgent: z.boolean().default(false),
  requiresReceipt: z.boolean().default(true),
  isCapitalExpenditure: z.boolean().default(false),
  projectId: z.string().optional()
});

type ExpenditureFormData = z.infer<typeof expenditureFormSchema>;

interface ExpenditureFormProps {
  onSubmit?: (data: ExpenditureFormData) => void;
  onCancel?: () => void;
  initialData?: Partial<ExpenditureFormData> & { id?: string };
  isEditing?: boolean;
}

// Data will be fetched from APIs

export function ExpenditureForm({ onSubmit, onCancel, initialData, isEditing = false }: ExpenditureFormProps) {
  const [currentTag, setCurrentTag] = useState('');
  const [currentNote, setCurrentNote] = useState('');
  const [totalAmount, setTotalAmount] = useState(0);

  const {
    createExpenditure,
    updateExpenditure,
    isCreating,
    isUpdating,
    validateExpenditure,
    calculateTotalAmount,
    formatCurrency
  } = useExpenditureManagement();

  // Get budget data
  const { activeBudgets } = useBudget();

  // Dynamic data
  const departments = [
    'Administration',
    'Finance Department',
    'ICT',
    'Compliance',
    'Store',
    'Procurement',
    'Human Resources',
    'Operations'
  ];

  const subcategories = {
    [ExpenditureCategory.OPERATIONAL]: ['office_supplies', 'utilities', 'communications', 'transport'],
    [ExpenditureCategory.CAPITAL]: ['equipment', 'infrastructure', 'technology', 'vehicles'],
    [ExpenditureCategory.PERSONNEL]: ['salaries', 'benefits', 'training', 'recruitment'],
    [ExpenditureCategory.ADMINISTRATIVE]: ['legal', 'audit', 'consulting', 'insurance'],
    [ExpenditureCategory.TRAVEL]: ['domestic', 'international', 'accommodation', 'meals'],
    [ExpenditureCategory.UTILITIES]: ['electricity', 'water', 'internet', 'phone'],
    [ExpenditureCategory.MAINTENANCE]: ['building', 'equipment', 'vehicles', 'grounds'],
    [ExpenditureCategory.SUPPLIES]: ['stationery', 'cleaning', 'medical', 'safety']
  };

  const form = useForm<ExpenditureFormData>({
    resolver: zodResolver(expenditureFormSchema),
    defaultValues: {
      title: '',
      description: '',
      category: ExpenditureCategory.OPERATIONAL,
      subcategory: 'office_supplies',
      amount: 0,
      currency: 'MWK',
      exchangeRate: 1,
      expenditureDate: new Date(),
      priority: ExpenditurePriority.MEDIUM,
      department: '',
      vendor: {
        vendorName: '',
        vendorEmail: '',
        vendorPhone: '',
        vendorAddress: ''
      },
      budgetAllocations: [{ budgetId: '', allocatedAmount: 0, percentage: 100 }],
      taxInfo: {
        taxType: 'none',
        taxRate: 0,
        isExempt: false
      },
      tags: [],
      notes: [],
      isUrgent: false,
      requiresReceipt: true,
      isCapitalExpenditure: false,
      ...initialData
    }
  });

  const { fields: budgetFields, append: appendBudget, remove: removeBudget } = useFieldArray({
    control: form.control,
    name: 'budgetAllocations'
  });

  const watchedValues = form.watch();
  const selectedCategory = form.watch('category');
  const amount = form.watch('amount');
  const taxInfo = form.watch('taxInfo');

  // Calculate total amount including tax
  useEffect(() => {
    const total = calculateTotalAmount(amount, taxInfo);
    setTotalAmount(total);
  }, [amount, taxInfo, calculateTotalAmount]);

  // Update subcategory options when category changes
  useEffect(() => {
    const categorySubcategories = subcategories[selectedCategory] || [];
    if (categorySubcategories.length > 0) {
      form.setValue('subcategory', categorySubcategories[0]);
    }
  }, [selectedCategory, form, subcategories]);

  // Handle form submission
  const handleSubmit = async (data: ExpenditureFormData) => {
    try {
      // Validate the data
      const validationErrors = validateExpenditure(data);
      if (validationErrors.length > 0) {
        validationErrors.forEach(error => {
          form.setError('root', { message: error });
        });
        return;
      }

      if (isEditing && initialData?.id) {
        await updateExpenditure({ id: initialData.id, ...data });
      } else {
        await createExpenditure(data);
      }

      onSubmit?.(data);
    } catch (error) {
      console.error('Error submitting expenditure:', error);
    }
  };

  // Add tag
  const addTag = () => {
    if (currentTag.trim()) {
      const currentTags = form.getValues('tags');
      if (!currentTags.includes(currentTag.trim())) {
        form.setValue('tags', [...currentTags, currentTag.trim()]);
      }
      setCurrentTag('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags');
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
  };

  // Add note
  const addNote = () => {
    if (currentNote.trim()) {
      const currentNotes = form.getValues('notes');
      form.setValue('notes', [...currentNotes, currentNote.trim()]);
      setCurrentNote('');
    }
  };

  // Remove note
  const removeNote = (index: number) => {
    const currentNotes = form.getValues('notes');
    form.setValue('notes', currentNotes.filter((_, i) => i !== index));
  };

  // Auto-distribute budget allocations
  const autoDistributeBudget = () => {
    const allocations = form.getValues('budgetAllocations');
    const amount = form.getValues('amount');
    const equalPercentage = 100 / allocations.length;
    const equalAmount = amount / allocations.length;

    allocations.forEach((_, index) => {
      form.setValue(`budgetAllocations.${index}.percentage`, equalPercentage);
      form.setValue(`budgetAllocations.${index}.allocatedAmount`, equalAmount);
    });
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {isEditing ? 'Edit Expenditure' : 'Create New Expenditure'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update expenditure details' : 'Fill in the details to create a new expenditure request'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter expenditure title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {departments.map(dept => (
                          <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the expenditure purpose and details"
                      className="min-h-20"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category and Classification */}
            <div className="grid gap-4 md:grid-cols-3">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(ExpenditureCategory).map(category => (
                          <SelectItem key={category} value={category}>
                            {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subcategory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subcategory *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {(subcategories[selectedCategory] || []).map(sub => (
                          <SelectItem key={sub} value={sub}>
                            {sub.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(ExpenditurePriority).map(priority => (
                          <SelectItem key={priority} value={priority}>
                            {priority.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Amount and Financial Details */}
            <div className="grid gap-4 md:grid-cols-3">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Currency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="MWK">MWK - Malawian Kwacha</SelectItem>
                        <SelectItem value="USD">USD - US Dollar</SelectItem>
                        <SelectItem value="EUR">EUR - Euro</SelectItem>
                        <SelectItem value="GBP">GBP - British Pound</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-end">
                <div className="text-sm">
                  <Label>Total Amount</Label>
                  <div className="text-lg font-bold text-green-600">
                    {formatCurrency(totalAmount)}
                  </div>
                  <p className="text-xs text-muted-foreground">Including tax</p>
                </div>
              </div>
            </div>

            {/* Receipt Upload Section */}
            <Separator />
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Receipt & Documentation</h3>
              <ReceiptUpload
                expenditureId={initialData?.id}
                onReceiptProcessed={(receipt) => {
                  console.log('Receipt processed:', receipt);
                }}
                onExtractedDataUpdate={(data) => {
                  // Auto-fill form with extracted data
                  if (data.merchantName && !form.getValues('vendor.vendorName')) {
                    form.setValue('vendor.vendorName', data.merchantName);
                  }
                  if (data.totalAmount && !form.getValues('amount')) {
                    form.setValue('amount', data.totalAmount);
                  }
                  if (data.transactionDate && !form.getValues('expenditureDate')) {
                    form.setValue('expenditureDate', data.transactionDate);
                  }
                }}
                maxFiles={3}
                disabled={isCreating || isUpdating}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating || isUpdating}
                className="min-w-32"
              >
                {isCreating || isUpdating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" />
                    {isEditing ? 'Update' : 'Create'} Expenditure
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

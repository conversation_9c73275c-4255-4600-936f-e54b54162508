"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  FileText,
  Calendar,
  Building,
  User,
  DollarSign,
  Clock,
  AlertTriangle
} from 'lucide-react';
import {
  ExpenditureStatus,
  ExpenditurePriority,
  ExpenditureCategory
} from '@/types/accounting/expenditure';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

interface Expenditure {
  id: string;
  expenditureNumber: string;
  title: string;
  description: string;
  category: ExpenditureCategory;
  subcategory: string;
  amount: number;
  currency: string;
  status: ExpenditureStatus;
  priority: ExpenditurePriority;
  expenditureDate: Date;
  dueDate?: Date;
  requestedBy: string;
  requestedByName: string;
  department: string;
  vendor: {
    vendorName: string;
    isPreferred: boolean;
  };
  totalAmount: number;
  isUrgent: boolean;
  isCapitalExpenditure: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ExpenditureTableProps {
  expenditures: Expenditure[];
  isLoading?: boolean;
  onViewExpenditure?: (expenditureId: string) => void;
  onEditExpenditure?: (expenditureId: string) => void;
  onDeleteExpenditure?: (expenditureId: string) => void;
  onBulkAction?: (action: string, expenditureIds: string[]) => void;
}

// Status color mapping
const getStatusColor = (status: ExpenditureStatus): string => {
  switch (status) {
    case ExpenditureStatus.DRAFT:
      return 'gray';
    case ExpenditureStatus.SUBMITTED:
      return 'blue';
    case ExpenditureStatus.PENDING_APPROVAL:
      return 'yellow';
    case ExpenditureStatus.APPROVED:
      return 'green';
    case ExpenditureStatus.REJECTED:
      return 'red';
    case ExpenditureStatus.PAID:
      return 'purple';
    case ExpenditureStatus.CANCELLED:
      return 'gray';
    case ExpenditureStatus.ON_HOLD:
      return 'orange';
    default:
      return 'gray';
  }
};

// Priority color mapping
const getPriorityColor = (priority: ExpenditurePriority): string => {
  switch (priority) {
    case ExpenditurePriority.LOW:
      return 'green';
    case ExpenditurePriority.MEDIUM:
      return 'yellow';
    case ExpenditurePriority.HIGH:
      return 'orange';
    case ExpenditurePriority.URGENT:
      return 'red';
    case ExpenditurePriority.CRITICAL:
      return 'purple';
    default:
      return 'gray';
  }
};

export function ExpenditureTable({
  expenditures,
  isLoading,
  onViewExpenditure,
  onEditExpenditure,
  onDeleteExpenditure,
  onBulkAction
}: ExpenditureTableProps) {
  const [selectedExpenditures, setSelectedExpenditures] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedExpenditures(expenditures.map(exp => exp.id));
    } else {
      setSelectedExpenditures([]);
    }
  };

  // Handle individual selection
  const handleSelectExpenditure = (expenditureId: string, checked: boolean) => {
    if (checked) {
      setSelectedExpenditures(prev => [...prev, expenditureId]);
    } else {
      setSelectedExpenditures(prev => prev.filter(id => id !== expenditureId));
    }
  };

  // Filter expenditures based on search
  const filteredExpenditures = expenditures.filter(expenditure =>
    expenditure.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expenditure.expenditureNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expenditure.vendor.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expenditure.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Expenditures</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Recent Expenditures</span>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search expenditures..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-64"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-1" />
              Filter
            </Button>
            {selectedExpenditures.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Actions ({selectedExpenditures.length})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onBulkAction?.('approve', selectedExpenditures)}>
                    Approve Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onBulkAction?.('reject', selectedExpenditures)}>
                    Reject Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onBulkAction?.('export', selectedExpenditures)}>
                    Export Selected
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onBulkAction?.('delete', selectedExpenditures)}
                    className="text-red-600"
                  >
                    Delete Selected
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {filteredExpenditures.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedExpenditures.length === expenditures.length}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Expenditure</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredExpenditures.map((expenditure) => (
                  <TableRow key={expenditure.id} className="hover:bg-muted/50">
                    <TableCell>
                      <Checkbox
                        checked={selectedExpenditures.includes(expenditure.id)}
                        onCheckedChange={(checked) => 
                          handleSelectExpenditure(expenditure.id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-sm">{expenditure.title}</p>
                          {expenditure.isUrgent && (
                            <AlertTriangle className="h-3 w-3 text-red-500" />
                          )}
                          {expenditure.isCapitalExpenditure && (
                            <Building className="h-3 w-3 text-blue-500" />
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {expenditure.expenditureNumber}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">{expenditure.vendor.vendorName}</span>
                        {expenditure.vendor.isPreferred && (
                          <Badge variant="secondary" className="text-xs">
                            Preferred
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {expenditure.category.replace(/_/g, ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(expenditure.totalAmount)}</p>
                        <p className="text-xs text-muted-foreground">{expenditure.currency}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`border-${getStatusColor(expenditure.status)}-500 text-${getStatusColor(expenditure.status)}-700`}
                      >
                        {expenditure.status.replace(/_/g, ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`border-${getPriorityColor(expenditure.priority)}-500 text-${getPriorityColor(expenditure.priority)}-700`}
                      >
                        {expenditure.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <p>{new Date(expenditure.expenditureDate).toLocaleDateString()}</p>
                        {expenditure.dueDate && (
                          <p className="text-xs text-muted-foreground">
                            Due: {new Date(expenditure.dueDate).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <p>{expenditure.department}</p>
                        <p className="text-xs text-muted-foreground">
                          {expenditure.requestedByName}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onViewExpenditure?.(expenditure.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => onEditExpenditure?.(expenditure.id)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onDeleteExpenditure?.(expenditure.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {searchTerm ? 'No expenditures found matching your search' : 'No expenditures found'}
            </p>
            {searchTerm && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSearchTerm('')}
                className="mt-2"
              >
                Clear Search
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

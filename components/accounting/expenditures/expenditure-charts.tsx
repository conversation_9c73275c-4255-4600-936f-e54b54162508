"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { ExpenditureCategory } from '@/types/accounting/expenditure';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Chart colors
const CHART_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FF6B6B', '#4ECDC4'];

interface ExpenditureStatistics {
  byCategory: Record<ExpenditureCategory, { count: number; amount: number }>;
  byDepartment: Record<string, { count: number; amount: number }>;
  monthlyTrend: Array<{ month: string; count: number; amount: number }>;
  totalAmount: number;
}

interface ExpenditureChartsProps {
  statistics?: ExpenditureStatistics;
  isLoading?: boolean;
}

// Category spending pie chart
export function CategorySpendingChart({ statistics, isLoading }: ExpenditureChartsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>Distribution of expenditures by category</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-80 w-full" />
        </CardContent>
      </Card>
    );
  }

  const categoryData = React.useMemo(() => {
    if (!statistics) return [];
    
    return Object.entries(statistics.byCategory)
      .filter(([_, data]) => data.count > 0)
      .map(([category, data]) => ({
        name: category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        value: data.amount,
        count: data.count,
        percentage: ((data.amount / (statistics.totalAmount || 1)) * 100).toFixed(1)
      }))
      .sort((a, b) => b.value - a.value);
  }, [statistics]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-blue-600">Amount: {formatCurrency(data.value)}</p>
          <p className="text-gray-600">Transactions: {data.count}</p>
          <p className="text-gray-600">Percentage: {data.percentage}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Spending by Category</CardTitle>
        <CardDescription>Distribution of expenditures by category</CardDescription>
      </CardHeader>
      <CardContent>
        {categoryData.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={categoryData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={({ name, percentage }) => `${name}: ${percentage}%`}
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-80 flex items-center justify-center text-muted-foreground">
            No category data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Department spending bar chart
export function DepartmentSpendingChart({ statistics, isLoading }: ExpenditureChartsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Top Spending Departments</CardTitle>
          <CardDescription>Departments with highest expenditures</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-80 w-full" />
        </CardContent>
      </Card>
    );
  }

  const departmentData = React.useMemo(() => {
    if (!statistics) return [];
    
    return Object.entries(statistics.byDepartment)
      .filter(([_, data]) => data.count > 0)
      .map(([department, data]) => ({
        name: department.length > 15 ? department.substring(0, 15) + '...' : department,
        fullName: department,
        amount: data.amount,
        count: data.count
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 8); // Top 8 departments
  }, [statistics]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{data.fullName}</p>
          <p className="text-blue-600">Amount: {formatCurrency(data.amount)}</p>
          <p className="text-gray-600">Transactions: {data.count}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Spending Departments</CardTitle>
        <CardDescription>Departments with highest expenditures</CardDescription>
      </CardHeader>
      <CardContent>
        {departmentData.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={departmentData} margin={{ bottom: 60 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45} 
                textAnchor="end" 
                height={80}
                fontSize={12}
              />
              <YAxis tickFormatter={(value) => formatCurrency(value)} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="amount" fill="#0088FE" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-80 flex items-center justify-center text-muted-foreground">
            No department data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Monthly trend chart
export function MonthlyTrendChart({ statistics, isLoading }: ExpenditureChartsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Spending Trend</CardTitle>
          <CardDescription>Expenditure patterns over time</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-96 w-full" />
        </CardContent>
      </Card>
    );
  }

  const trendData = React.useMemo(() => {
    if (!statistics) return [];
    
    return statistics.monthlyTrend.map(item => ({
      month: new Date(item.month + '-01').toLocaleDateString('en-US', { 
        month: 'short', 
        year: '2-digit' 
      }),
      amount: item.amount,
      count: item.count
    }));
  }, [statistics]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Spending Trend</CardTitle>
        <CardDescription>Expenditure patterns over time</CardDescription>
      </CardHeader>
      <CardContent>
        {trendData.length > 0 ? (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => formatCurrency(value)} />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'amount' ? formatCurrency(Number(value)) : value,
                  name === 'amount' ? 'Amount' : 'Transactions'
                ]}
              />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="amount" 
                stroke="#0088FE" 
                fill="#0088FE" 
                fillOpacity={0.6}
                name="Amount"
              />
            </AreaChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-96 flex items-center justify-center text-muted-foreground">
            No trend data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Combined overview chart
export function ExpenditureOverviewCharts({ statistics, isLoading }: ExpenditureChartsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <CategorySpendingChart statistics={statistics} isLoading={isLoading} />
      <DepartmentSpendingChart statistics={statistics} isLoading={isLoading} />
    </div>
  );
}

// Category breakdown with details
export function CategoryBreakdownChart({ statistics, isLoading }: ExpenditureChartsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Category Breakdown</CardTitle>
          <CardDescription>Detailed analysis by expenditure category</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const categoryBreakdown = React.useMemo(() => {
    if (!statistics) return [];
    
    return Object.entries(statistics.byCategory)
      .filter(([_, data]) => data.count > 0)
      .map(([category, data]) => ({
        category,
        name: category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        amount: data.amount,
        count: data.count,
        percentage: ((data.amount / (statistics.totalAmount || 1)) * 100)
      }))
      .sort((a, b) => b.amount - a.amount);
  }, [statistics]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Category Breakdown</CardTitle>
        <CardDescription>Detailed analysis by expenditure category</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {categoryBreakdown.map((item, index) => (
            <div key={item.category} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-4 h-4 rounded-full" 
                  style={{ backgroundColor: CHART_COLORS[index % CHART_COLORS.length] }}
                ></div>
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-sm text-muted-foreground">{item.count} transactions</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold">{formatCurrency(item.amount)}</p>
                <p className="text-sm text-muted-foreground">
                  {item.percentage.toFixed(1)}%
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

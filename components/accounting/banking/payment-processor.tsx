"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format } from 'date-fns';
import {
  Download,
  FileText,
  RefreshCw,
  PlusCircle,
  FileDown,
  Filter,
  ArrowUpDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  CheckCircle,
  XCircle,
  Clock,
  Search,
  Printer,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PaymentForm } from './payment-form';
import { PaymentDetails } from './payment-details';
import { PaymentList } from './payment-list';
import { PaymentStatus, PaymentMethod } from '@/models/accounting/Payment';
import { paymentService, Payment as PaymentType, PaymentFilterOptions } from '@/lib/services/payment-service';

// Sample data for demonstration (fallback if API fails)
const samplePayments: PaymentType[] = [
  {
    id: "PAY-2025-0001",
    reference: "INV-2025-001",
    date: new Date("2025-01-15"),
    amount: 125000,
    currency: "MWK",
    payee: "Office Supplies Ltd",
    description: "Payment for office supplies",
    status: "completed",
    method: "bank_transfer",
    bankAccount: "1",
    bankAccountName: "TCM Operations Account",
    createdBy: "John Banda",
    createdAt: new Date("2025-01-15T10:30:00Z"),
    updatedAt: new Date("2025-01-15T14:45:00Z")
  },
  {
    id: "PAY-2025-0002",
    reference: "INV-2025-002",
    date: new Date("2025-01-20"),
    amount: 350000,
    currency: "MWK",
    payee: "IT Solutions Inc",
    description: "Payment for IT services",
    status: "pending",
    method: "check",
    bankAccount: "1",
    bankAccountName: "TCM Operations Account",
    createdBy: "Mary Phiri",
    createdAt: new Date("2025-01-20T09:15:00Z")
  },
  {
    id: "PAY-2025-0003",
    reference: "INV-2025-003",
    date: new Date("2025-01-25"),
    amount: 75000,
    currency: "MWK",
    payee: "Cleaning Services Co",
    description: "Payment for cleaning services",
    status: "processing",
    method: "bank_transfer",
    bankAccount: "1",
    bankAccountName: "TCM Operations Account",
    createdBy: "James Mwanza",
    createdAt: new Date("2025-01-25T11:45:00Z")
  }
];

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Get status badge variant
const getStatusBadge = (status: PaymentStatus) => {
  switch (status) {
    case 'pending':
      return { variant: "default" as const, icon: <Clock className="mr-1 h-3 w-3" /> };
    case 'processing':
      return { variant: "default" as const, icon: <RefreshCw className="mr-1 h-3 w-3" /> };
    case 'completed':
      return { variant: "default" as const, icon: <CheckCircle className="mr-1 h-3 w-3" /> };
    case 'failed':
      return { variant: 'destructive' as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    case 'cancelled':
      return { variant: "default" as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    default:
      return { variant: "default" as const, icon: null };
  }
};

// Get method display name
const getMethodDisplayName = (method: PaymentMethod) => {
  switch (method) {
    case 'bank_transfer':
      return 'Bank Transfer';
    case 'check':
      return 'Check';
    case 'cash':
      return 'Cash';
    case 'mobile_money':
      return 'Mobile Money';
    case 'other':
      return 'Other';
    default:
      return method;
  }
};

interface PaymentProcessorProps {}

export function PaymentProcessor({}: PaymentProcessorProps) {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [methodFilter, setMethodFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<PaymentType | null>(null);
  const [payments, setPayments] = useState<PaymentType[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch payments on component mount
  useEffect(() => {
    fetchPayments();
  }, []);

  // Fetch payments from API
  const fetchPayments = async (filters?: PaymentFilterOptions) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await paymentService.getPayments(filters);
      setPayments(data);
    } catch (err) {
      console.error('Error fetching payments:', err);
      setError('Failed to load payments. Using sample data instead.');
      setPayments(samplePayments);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter payments based on search query and filters
  const filteredPayments = payments.filter(payment => {
    // Search query filter
    const matchesSearch =
      payment.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.payee.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Status filter
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;

    // Method filter
    const matchesMethod = methodFilter === 'all' || payment.method === methodFilter;

    // Tab filter
    const matchesTab = activeTab === 'all' || payment.status === activeTab;

    return matchesSearch && matchesStatus && matchesMethod && matchesTab;
  });

  // Handle creating a new payment
  const handleCreate = () => {
    setSelectedPayment(null);
    setShowCreateDialog(true);
  };

  // Handle editing a payment
  const handleEdit = (payment: PaymentType) => {
    setSelectedPayment(payment);
    setShowEditDialog(true);
  };

  // Handle viewing payment details
  const handleViewDetails = (payment: PaymentType) => {
    setSelectedPayment(payment);
    setShowDetailsDialog(true);
  };

  // Handle duplicating a payment
  const handleDuplicate = (payment: PaymentType) => {
    setSelectedPayment({
      ...payment,
      id: `PAY-${Date.now().toString().slice(-8)}`,
      reference: `${payment.reference}-COPY`,
      date: new Date(),
      status: 'pending',
      createdAt: new Date(),
      createdBy: "Current User", // In a real app, this would be the current user
    });
    setShowCreateDialog(true);
  };

  // Handle deleting a payment
  const handleDelete = (payment: PaymentType) => {
    setSelectedPayment(payment);
    setShowDeleteDialog(true);
  };

  // Handle confirming delete
  const confirmDelete = async () => {
    if (selectedPayment) {
      setIsSubmitting(true);

      try {
        await paymentService.deletePayment(selectedPayment.id);
        setPayments(payments.filter(p => p.id !== selectedPayment.id));
      } catch (err) {
        console.error('Error deleting payment:', err);
        setError('Failed to delete payment. Please try again.');
        // In a real app, we would show a toast notification here
      } finally {
        setIsSubmitting(false);
        setShowDeleteDialog(false);
        setSelectedPayment(null);
      }
    }
  };

  // Handle processing payments
  const handleProcess = async () => {
    setIsProcessing(true);

    try {
      // Get all pending payment IDs
      const pendingPaymentIds = payments
        .filter(payment => payment.status === 'pending')
        .map(payment => payment.id);

      if (pendingPaymentIds.length === 0) {
        setError('No pending payments to process.');
        return;
      }

      await paymentService.processPayments(pendingPaymentIds);

      // Update pending payments to processing
      const updatedPayments = payments.map(payment => {
        if (payment.status === 'pending') {
          return { ...payment, status: 'processing' };
        }
        return payment;
      });

      setPayments(updatedPayments);
    } catch (err) {
      console.error('Error processing payments:', err);
      setError('Failed to process payments. Please try again.');
      // In a real app, we would show a toast notification here
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle form submission for new payment
  const handleCreateSubmit = async (values: unknown) => {
    setIsSubmitting(true);

    try {
      const paymentData = {
        reference: values.reference,
        date: values.date,
        amount: values.amount,
        currency: values.currency,
        payee: values.payee,
        description: values.description,
        method: values.method as PaymentMethod,
        bankAccount: values.bankAccount,
        checkNumber: values.checkNumber,
        transactionId: values.transactionId,
        notes: values.notes,
      };

      let newPayment: PaymentType;

      if (selectedPayment) {
        // If duplicating, create a new payment based on the selected one
        newPayment = await paymentService.createPayment(paymentData);
      } else {
        // If creating new, create a new payment
        newPayment = await paymentService.createPayment(paymentData);
      }

      setPayments([...payments, newPayment]);
      setShowCreateDialog(false);
      setSelectedPayment(null);
    } catch (err) {
      console.error('Error creating payment:', err);
      setError('Failed to create payment. Please try again.');
      // In a real app, we would show a toast notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission for editing payment
  const handleEditSubmit = async (values: unknown) => {
    setIsSubmitting(true);

    try {
      if (selectedPayment) {
        const paymentData = {
          id: selectedPayment.id,
          reference: values.reference,
          date: values.date,
          amount: values.amount,
          currency: values.currency,
          payee: values.payee,
          description: values.description,
          method: values.method as PaymentMethod,
          bankAccount: values.bankAccount,
          checkNumber: values.checkNumber,
          transactionId: values.transactionId,
          notes: values.notes,
        };

        const updatedPayment = await paymentService.updatePayment(paymentData);

        setPayments(payments.map(payment =>
          payment.id === selectedPayment.id ? updatedPayment : payment
        ));
      }

      setShowEditDialog(false);
      setSelectedPayment(null);
    } catch (err) {
      console.error('Error updating payment:', err);
      setError('Failed to update payment. Please try again.');
      // In a real app, we would show a toast notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle printing a payment
  const handlePrint = () => {
    // In a real implementation, this would print the payment
    console.log('Printing payment:', selectedPayment);
  };

  // Handle exporting a payment
  const handleExport = () => {
    // In a real implementation, this would export the payment
    console.log('Exporting payment:', selectedPayment);
  };

  return (
    <div className="space-y-6">
      {/* Header with filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Payment Processing</h2>
          <p className="text-muted-foreground">
            Process payments, manage payment methods, and track payment status.
          </p>
          {error && (
            <div className="mt-2 flex items-center text-sm text-destructive">
              <AlertCircle className="mr-1 h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" className="gap-1">
            <FileDown className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button
            size="sm"
            onClick={handleProcess}
            disabled={isProcessing || isLoading || payments.filter(p => p.status === 'pending').length === 0}
          >
            {isProcessing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Process Payments
              </>
            )}
          </Button>
          <Button size="sm" onClick={handleCreate} disabled={isLoading}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Payment
          </Button>
        </div>
      </div>

      {/* Tabs and Filters */}
      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <TabsList className="grid w-full max-w-md grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="failed">Failed</TabsTrigger>
          </TabsList>

          <div className="flex flex-wrap items-center gap-2">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search payments..."
                className="w-full pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={methodFilter} onValueChange={setMethodFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Methods</SelectItem>
                <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                <SelectItem value="check">Check</SelectItem>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="mobile_money">Mobile Money</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Payment List */}
        <TabsContent value="all" className="mt-6">
          <Card>
            <CardHeader className="px-6 py-4">
              <CardTitle>All Payments</CardTitle>
              <CardDescription>
                Showing {filteredPayments.length} payments
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="flex h-40 items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <PaymentList
                  payments={filteredPayments}
                  onEdit={handleEdit}
                  onViewDetails={handleViewDetails}
                  onDuplicate={handleDuplicate}
                  onDelete={handleDelete}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other tabs would filter by status */}
        <TabsContent value="pending" className="mt-6">
          <Card>
            <CardHeader className="px-6 py-4">
              <CardTitle>Pending Payments</CardTitle>
              <CardDescription>
                Showing {filteredPayments.filter(p => p.status === 'pending').length} pending payments
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Similar table as above but filtered for pending */}
              {/* This would be a component in a real implementation */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          <Card>
            <CardHeader className="px-6 py-4">
              <CardTitle>Completed Payments</CardTitle>
              <CardDescription>
                Showing {filteredPayments.filter(p => p.status === 'completed').length} completed payments
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Similar table as above but filtered for completed */}
              {/* This would be a component in a real implementation */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="failed" className="mt-6">
          <Card>
            <CardHeader className="px-6 py-4">
              <CardTitle>Failed Payments</CardTitle>
              <CardDescription>
                Showing {filteredPayments.filter(p => p.status === 'failed').length} failed payments
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Similar table as above but filtered for failed */}
              {/* This would be a component in a real implementation */}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Payment Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedPayment ? 'Duplicate Payment' : 'Create New Payment'}</DialogTitle>
            <DialogDescription>
              {selectedPayment
                ? 'Create a new payment based on an existing one.'
                : 'Fill in the details to create a new payment.'}
            </DialogDescription>
          </DialogHeader>
          <PaymentForm
            defaultValues={selectedPayment ? {
              reference: selectedPayment.reference,
              date: selectedPayment.date,
              amount: selectedPayment.amount,
              currency: selectedPayment.currency,
              payee: selectedPayment.payee,
              description: selectedPayment.description,
              method: selectedPayment.method,
              bankAccount: selectedPayment.bankAccount,
              checkNumber: selectedPayment.checkNumber,
              transactionId: selectedPayment.transactionId,
              notes: selectedPayment.notes,
            } : undefined}
            onSubmit={handleCreateSubmit}
            isSubmitting={isSubmitting}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Payment Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Payment</DialogTitle>
            <DialogDescription>
              Update the payment details.
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <PaymentForm
              defaultValues={{
                reference: selectedPayment.reference,
                date: selectedPayment.date,
                amount: selectedPayment.amount,
                currency: selectedPayment.currency,
                payee: selectedPayment.payee,
                description: selectedPayment.description,
                method: selectedPayment.method,
                bankAccount: selectedPayment.bankAccount,
                checkNumber: selectedPayment.checkNumber,
                transactionId: selectedPayment.transactionId,
                notes: selectedPayment.notes,
              }}
              onSubmit={handleEditSubmit}
              isSubmitting={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Payment Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-3xl">
          {selectedPayment && (
            <PaymentDetails
              payment={selectedPayment}
              onEdit={() => {
                setShowDetailsDialog(false);
                setTimeout(() => handleEdit(selectedPayment), 100);
              }}
              onDuplicate={() => {
                setShowDetailsDialog(false);
                setTimeout(() => handleDuplicate(selectedPayment), 100);
              }}
              onDelete={() => {
                setShowDetailsDialog(false);
                setTimeout(() => handleDelete(selectedPayment), 100);
              }}
              onPrint={handlePrint}
              onExport={handleExport}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the payment
              {selectedPayment && ` "${selectedPayment.reference}"`} and remove it from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

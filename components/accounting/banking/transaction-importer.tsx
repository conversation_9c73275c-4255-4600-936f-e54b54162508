"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/components/ui/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Upload, FileText, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { format } from 'date-fns';

// Define the form schema
const importFormSchema = z.object({
  accountId: z.string({
    required_error: 'Please select a bank account',
  }),
  format: z.enum(['csv', 'excel', 'json'], {
    required_error: 'Please select an import format',
  }),
  dateFormat: z.string().optional(),
  skipFirstRow: z.boolean().default(true),
  validateOnly: z.boolean().default(false),
});

// Define the form values type
type ImportFormValues = z.infer<typeof importFormSchema>;

// Define the component props
interface TransactionImporterProps {
  bankAccounts: Array<{
    id: string;
    name: string;
    accountNumber: string;
  }>;
  onImportComplete?: (result: unknown) => void;
}

export function TransactionImporter({
  bankAccounts,
  onImportComplete,
}: TransactionImporterProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("basic");
  const [columnMapping, setColumnMapping] = useState({
    date: "Date",
    description: "Description",
    amount: "Amount",
    reference: "Reference",
    category: "Category",
    payee: "Payee",
    notes: "Notes",
    checkNumber: "CheckNumber",
  });
  const [importHistory, setImportHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [importResult, setImportResult] = useState<unknown>(null);

  const { toast } = useToast();

  // Initialize the form
  const form = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema) as any,
    defaultValues: {
      format: 'csv',
      dateFormat: 'MM/DD/YYYY',
      skipFirstRow: true,
      validateOnly: false,
    },
  });

  // Load import history
  const loadImportHistory = async () => {
    try {
      setLoadingHistory(true);
      const response = await fetch('/api/accounting/banking/transactions/imports');
      
      if (!response.ok) {
        throw new Error('Failed to load import history');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setImportHistory(data.data.docs);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to load import history',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error loading import history:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoadingHistory(false);
    }
  };

  // Load import history on mount
  useEffect(() => {
    loadImportHistory();
  }, []);

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle column mapping change
  const handleColumnMappingChange = (field: string, value: string) => {
    setColumnMapping((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle form submission
  const onSubmit = async (data: ImportFormValues) => {
    if (!file) {
      toast({
        title: 'Error',
        description: 'Please select a file to import',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsImporting(true);
      setImportResult(null);
      
      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('accountId', data.accountId);
      formData.append('format', data.format);
      formData.append('dateFormat', data.dateFormat || '');
      formData.append('columnMapping', JSON.stringify(columnMapping));
      formData.append('skipFirstRow', data.skipFirstRow.toString());
      formData.append('validateOnly', data.validateOnly.toString());
      
      // Send import request
      const response = await fetch('/api/accounting/banking/transactions/import', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to import transactions');
      }
      
      const result = await response.json();
      
      if (result.success) {
        toast({
          title: data.validateOnly ? 'Validation Successful' : 'Import Successful',
          description: result.message,
        });
        
        // Set import result
        setImportResult(result);
        
        // Reload import history
        loadImportHistory();
        
        if (onImportComplete) {
          onImportComplete(result);
        }
      } else {
        toast({
          title: 'Import Failed',
          description: result.error || 'An unknown error occurred',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error importing transactions:', error);
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Import Transactions</CardTitle>
          <CardDescription>
            Import bank transactions from various file formats
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic" onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control as any}
                      name="accountId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bank Account</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                            disabled={isImporting}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select bank account" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {bankAccounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.name} ({account.accountNumber})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Select the bank account to import transactions for
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>File Format</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isImporting}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="csv">CSV</SelectItem>
                              <SelectItem value="excel">Excel</SelectItem>
                              <SelectItem value="json">JSON</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose the format of the file to import
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="dateFormat"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date Format</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="MM/DD/YYYY"
                              disabled={isImporting}
                            />
                          </FormControl>
                          <FormDescription>
                            Format of dates in the import file
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div>
                      <FormLabel>Import File</FormLabel>
                      <div className="mt-2">
                        <Input
                          type="file"
                          onChange={handleFileChange}
                          accept=".csv,.xlsx,.xls,.json"
                          disabled={isImporting}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Select the file to import
                      </p>
                    </div>

                    <FormField
                      control={form.control as any}
                      name="skipFirstRow"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={isImporting}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Skip First Row</FormLabel>
                            <FormDescription>
                              Skip the first row (header) in the import file
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="validateOnly"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={isImporting}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Validate Only</FormLabel>
                            <FormDescription>
                              Validate the file without importing transactions
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" disabled={isImporting || !file}>
                    {isImporting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {form.getValues().validateOnly ? 'Validating...' : 'Importing...'}
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        {form.getValues().validateOnly ? 'Validate File' : 'Import Transactions'}
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </TabsContent>
            
            <TabsContent value="advanced">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Column Mapping</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Map columns in your import file to transaction fields
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(columnMapping).map(([field, value]) => (
                      <div key={field} className="space-y-2">
                        <label className="text-sm font-medium">
                          {field.charAt(0).toUpperCase() + field.slice(1)}
                        </label>
                        <Input
                          value={value}
                          onChange={(e) => handleColumnMappingChange(field, e.target.value)}
                          placeholder={`Column name for ${field}`}
                          disabled={isImporting}
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Column Mapping</AlertTitle>
                  <AlertDescription>
                    Enter the exact column names from your import file. The system will use these mappings to match columns to transaction fields.
                  </AlertDescription>
                </Alert>
              </div>
            </TabsContent>
          </Tabs>
          
          {importResult && (
            <div className="mt-6">
              <Alert className={importResult.success ? "bg-green-50" : "bg-red-50"}>
                {importResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertTitle>
                  {importResult.success ? "Import Successful" : "Import Failed"}
                </AlertTitle>
                <AlertDescription>
                  {importResult.message}
                </AlertDescription>
              </Alert>
              
              {importResult.success && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-muted p-4 rounded-md">
                    <div className="text-sm text-muted-foreground">Total</div>
                    <div className="text-2xl font-bold">{importResult.totalTransactions}</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-md">
                    <div className="text-sm text-green-600">Imported</div>
                    <div className="text-2xl font-bold text-green-700">{importResult.importedTransactions}</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-md">
                    <div className="text-sm text-yellow-600">Duplicates</div>
                    <div className="text-2xl font-bold text-yellow-700">{importResult.duplicateTransactions}</div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-md">
                    <div className="text-sm text-red-600">Failed</div>
                    <div className="text-2xl font-bold text-red-700">{importResult.failedTransactions}</div>
                  </div>
                </div>
              )}
              
              {importResult.errors && importResult.errors.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Errors</h4>
                  <div className="max-h-40 overflow-y-auto border rounded-md">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Row</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {importResult.errors.map((error: unknown, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{error.row}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-500">{error.message}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Import History</CardTitle>
          <CardDescription>
            Recent transaction imports
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingHistory ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading import history...</span>
            </div>
          ) : importHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No import history found
            </div>
          ) : (
            <div className="space-y-4">
              {importHistory.map((import_) => (
                <div
                  key={import_._id}
                  className="flex items-center justify-between border-b pb-4"
                >
                  <div>
                    <h4 className="font-medium">{import_.name}</h4>
                    <div className="text-sm text-muted-foreground">
                      {import_.format.toUpperCase()} • {import_.importedTransactions} of {import_.totalTransactions} imported
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(import_.createdAt), 'PPP p')}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`px-2 py-1 rounded-full text-xs ${
                      import_.status === 'completed' ? 'bg-green-100 text-green-800' :
                      import_.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {import_.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

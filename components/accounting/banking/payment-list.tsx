"use client";

import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Edit,
  FileText,
  Copy,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
} from 'lucide-react';
import { PaymentStatus, PaymentMethod } from '@/models/accounting/Payment';

interface Payment {
  id: string;
  reference: string;
  date: Date;
  amount: number;
  currency: string;
  payee: string;
  description: string;
  status: PaymentStatus;
  method: PaymentMethod;
  bankAccount: string;
  bankAccountName?: string;
  checkNumber?: string;
  transactionId?: string;
  receiptNumber?: string;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedBy?: string;
  updatedAt?: Date;
}

interface PaymentListProps {
  payments: Payment[];
  onEdit: (payment: Payment) => void;
  onViewDetails: (payment: Payment) => void;
  onDuplicate: (payment: Payment) => void;
  onDelete: (payment: Payment) => void;
}

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Get status badge variant
const getStatusBadge = (status: PaymentStatus) => {
  switch (status) {
    case 'pending':
      return { variant: "default" as const, icon: <Clock className="mr-1 h-3 w-3" /> };
    case 'processing':
      return { variant: "default" as const, icon: <RefreshCw className="mr-1 h-3 w-3" /> };
    case 'completed':
      return { variant: "default" as const, icon: <CheckCircle className="mr-1 h-3 w-3" /> };
    case 'failed':
      return { variant: 'destructive' as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    case 'cancelled':
      return { variant: "default" as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    default:
      return { variant: "default" as const, icon: null };
  }
};

// Get method display name
const getMethodDisplayName = (method: PaymentMethod) => {
  switch (method) {
    case 'bank_transfer':
      return 'Bank Transfer';
    case 'check':
      return 'Check';
    case 'cash':
      return 'Cash';
    case 'mobile_money':
      return 'Mobile Money';
    case 'other':
      return 'Other';
    default:
      return method;
  }
};

export function PaymentList({ 
  payments, 
  onEdit, 
  onViewDetails, 
  onDuplicate, 
  onDelete 
}: PaymentListProps) {
  return (
    <div className="relative w-full overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Reference</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Payee</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {payments.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No payments found.
              </TableCell>
            </TableRow>
          ) : (
            payments.map((payment) => {
              const statusBadge = getStatusBadge(payment.status);
              return (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">{payment.reference}</TableCell>
                  <TableCell>{format(payment.date, "MMM d, yyyy")}</TableCell>
                  <TableCell>{payment.payee}</TableCell>
                  <TableCell>{getMethodDisplayName(payment.method)}</TableCell>
                  <TableCell>
                    <Badge variant={statusBadge.variant} className="flex w-fit items-center">
                      {statusBadge.icon}
                      <span className="capitalize">{payment.status}</span>
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(payment.amount, payment.currency)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onSelect={() => onEdit(payment)}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={() => onViewDetails(payment)}>
                          <FileText className="mr-2 h-4 w-4" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={() => onDuplicate(payment)}>
                          <Copy className="mr-2 h-4 w-4" />
                          <span>Duplicate</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-destructive"
                          onSelect={() => onDelete(payment)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
}

"use client";

import { useState } from "react";
import { format } from "date-fns";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Building,
  CreditCard,
  Calendar,
  RefreshCw,
  FileDown,
  Printer,
  Edit,
  Trash2,
  Clock,
  ArrowUpDown,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { BankAccount } from "@/lib/services/bank-account-service";

interface BankAccountDetailsProps {
  account: BankAccount;
  onEdit: () => void;
  onDelete: () => void;
  onPrint: () => void;
  onExport: () => void;
  onRefreshBalance: () => void;
  onViewTransactions: () => void;
  onViewStatement: () => void;
}

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Get status badge variant
const getStatusBadge = (status: 'active' | 'inactive' | 'closed') => {
  switch (status) {
    case 'active':
      return { variant: "default" as const, icon: <CheckCircle className="mr-1 h-3 w-3" /> };
    case 'inactive':
      return { variant: "default" as const, icon: <Clock className="mr-1 h-3 w-3" /> };
    case 'closed':
      return { variant: 'destructive' as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    default:
      return { variant: "default" as const, icon: null };
  }
};

// Get account type display name
const getAccountTypeDisplayName = (type: string) => {
  return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
};

export function BankAccountDetails({ 
  account, 
  onEdit, 
  onDelete, 
  onPrint, 
  onExport,
  onRefreshBalance,
  onViewTransactions,
  onViewStatement
}: BankAccountDetailsProps) {
  const statusBadge = getStatusBadge(account.status);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle className="text-xl">{account.accountName}</CardTitle>
            <CardDescription>
              Account Number: {account.accountNumber}
            </CardDescription>
          </div>
          <Badge variant={statusBadge.variant} className="w-fit">
            {statusBadge.icon}
            <span className="capitalize">{account.status}</span>
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.currentBalance, account.currency)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.availableBalance, account.currency)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Opening Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.openingBalance, account.currency)}</div>
            </CardContent>
          </Card>
        </div>

        <Separator />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Bank Name</h3>
            <p className="mt-1 text-base">{account.bankName}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Branch Name</h3>
            <p className="mt-1 text-base">{account.branchName}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Account Type</h3>
            <p className="mt-1 text-base">{getAccountTypeDisplayName(account.accountType)}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Currency</h3>
            <p className="mt-1 text-base">{account.currency}</p>
          </div>
        </div>

        {account.branchCode && (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Branch Code</h3>
              <p className="mt-1 text-base">{account.branchCode}</p>
            </div>
            {account.swiftCode && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">SWIFT Code</h3>
                <p className="mt-1 text-base">{account.swiftCode}</p>
              </div>
            )}
          </div>
        )}

        {account.minimumBalance !== undefined && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Minimum Balance</h3>
            <p className="mt-1 text-base">{formatCurrency(account.minimumBalance, account.currency)}</p>
          </div>
        )}

        <Separator />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Opened Date</h3>
            <p className="mt-1 text-base">{format(account.openedDate, "MMMM d, yyyy")}</p>
          </div>
          {account.lastReconciliationDate && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Last Reconciliation</h3>
              <p className="mt-1 text-base">{format(account.lastReconciliationDate, "MMMM d, yyyy")}</p>
            </div>
          )}
        </div>

        {account.closedDate && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Closed Date</h3>
            <p className="mt-1 text-base">{format(account.closedDate, "MMMM d, yyyy")}</p>
          </div>
        )}

        {account.description && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
            <p className="mt-1 text-base">{account.description}</p>
          </div>
        )}

        {(account.contactPerson || account.contactEmail || account.contactPhone) && (
          <>
            <Separator />
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {account.contactPerson && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Contact Person</h3>
                  <p className="mt-1 text-base">{account.contactPerson}</p>
                </div>
              )}
              {account.contactEmail && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Contact Email</h3>
                  <p className="mt-1 text-base">{account.contactEmail}</p>
                </div>
              )}
              {account.contactPhone && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Contact Phone</h3>
                  <p className="mt-1 text-base">{account.contactPhone}</p>
                </div>
              )}
            </div>
          </>
        )}

        {account.notes && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="mt-1 text-base">{account.notes}</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-wrap justify-between gap-2">
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="gap-1" onClick={onViewTransactions}>
            <ArrowUpDown className="h-4 w-4" />
            <span>Transactions</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onViewStatement}>
            <CreditCard className="h-4 w-4" />
            <span>Statement</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onRefreshBalance}>
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="gap-1" onClick={onExport}>
            <FileDown className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onPrint}>
            <Printer className="h-4 w-4" />
            <span>Print</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onEdit}>
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <Button variant="destructive" size="sm" className="gap-1" onClick={onDelete}>
            <Trash2 className="h-4 w-4" />
            <span>Delete</span>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

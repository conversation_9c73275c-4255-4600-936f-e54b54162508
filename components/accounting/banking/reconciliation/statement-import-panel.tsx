"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Upload, AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

import { BankAccount } from "@/types/banking";
import { BankStatementItem } from "@/models/accounting/BankStatement";

// Define the form schema
const importFormSchema = z.object({
  bankAccount: z.string({
    required_error: "Please select a bank account",
  }),
  autoDetectFormat: z.boolean(),
  dateFormat: z.string().optional(),
});

type ImportFormValues = z.infer<typeof importFormSchema>;

interface StatementImportPanelProps {
  bankAccounts: BankAccount[];
  selectedAccount: string;
  onAccountChange: (accountId: string) => void;
  onImportComplete: (items: BankStatementItem[]) => void;
}

export function StatementImportPanel({
  bankAccounts,
  selectedAccount,
  onAccountChange,
  onImportComplete,
}: StatementImportPanelProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("basic");
  const [columnMapping, setColumnMapping] = useState({
    date: "Date",
    description: "Description",
    reference: "Reference",
    amount: "Amount",
    balance: "Balance",
  });

  const { toast } = useToast();

  // Initialize the form
  const form = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema),
    defaultValues: {
      bankAccount: selectedAccount,
      autoDetectFormat: true,
      dateFormat: "MM/DD/YYYY",
    },
  });

  // Update form when selectedAccount changes
  if (form.getValues("bankAccount") !== selectedAccount) {
    form.setValue("bankAccount", selectedAccount);
  }

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ImportFormValues) => {
    if (!file) {
      toast({
        title: "No File Selected",
        description: "Please select a file to import.",
        variant: "destructive",
      });
      return;
    }

    setIsImporting(true);

    try {
      // In a real implementation, this would call the API to import the statement
      // For now, simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update the selected account
      if (data.bankAccount !== selectedAccount) {
        onAccountChange(data.bankAccount);
      }

      // Generate mock statement items
      const mockItems: BankStatementItem[] = Array.from({ length: 10 }, (_, i) => ({
        id: `MOCK-${Date.now()}-${i}`,
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date in the last 30 days
        description: `Transaction ${i + 1} from imported statement`,
        reference: `REF-${Math.floor(Math.random() * 10000)}`,
        amount: Math.round((Math.random() * 2000 - 1000) * 100) / 100, // Random amount between -1000 and 1000
        balance: Math.round(Math.random() * 10000 * 100) / 100, // Random balance between 0 and 10000
        imported: true,
        matched: false,
      }));

      toast({
        title: "Import Successful",
        description: `Successfully imported ${mockItems.length} transactions.`,
      });

      onImportComplete(mockItems);
    } catch (error: unknown) {
      // Use a more descriptive error message
      const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : "An unknown error occurred during import";
      console.error("Error importing statement:", errorMessage);
      toast({
        title: "Import Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Handle column mapping change
  const handleColumnMappingChange = (field: keyof typeof columnMapping, value: string) => {
    setColumnMapping(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Import Bank Statement</CardTitle>
        <CardDescription>
          Upload a bank statement file to import transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="bankAccount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Account</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      onAccountChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a bank account" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {bankAccounts.map(account => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the bank account for this statement
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="statement-file">Bank Statement File</Label>
              <Input
                id="statement-file"
                type="file"
                onChange={handleFileChange}
                accept=".csv,.ofx,.qif,.pdf,.xlsx,.xls"
              />
              {file && (
                <p className="text-sm text-muted-foreground">
                  Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                </p>
              )}
            </div>

            <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4">
                <FormField
                  control={form.control as any}
                  name="autoDetectFormat"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Auto-detect file format</FormLabel>
                        <FormDescription>
                          Automatically detect the format of your bank statement file
                        </FormDescription>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="dateFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Format</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select date format" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                          <SelectItem value="YYYY/MM/DD">YYYY/MM/DD</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the date format used in your statement
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Column Mapping</h3>
                  <p className="text-sm text-muted-foreground">
                    Specify which columns in your file correspond to which fields
                  </p>

                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date-column">Date Column</Label>
                        <Input
                          id="date-column"
                          value={columnMapping.date}
                          onChange={(e) => handleColumnMappingChange('date', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description-column">Description Column</Label>
                        <Input
                          id="description-column"
                          value={columnMapping.description}
                          onChange={(e) => handleColumnMappingChange('description', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="reference-column">Reference Column</Label>
                        <Input
                          id="reference-column"
                          value={columnMapping.reference}
                          onChange={(e) => handleColumnMappingChange('reference', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="amount-column">Amount Column</Label>
                        <Input
                          id="amount-column"
                          value={columnMapping.amount}
                          onChange={(e) => handleColumnMappingChange('amount', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="balance-column">Balance Column (Optional)</Label>
                      <Input
                        id="balance-column"
                        value={columnMapping.balance}
                        onChange={(e) => handleColumnMappingChange('balance', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <Button type="submit" disabled={isImporting || !file}>
              {isImporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Import Statement
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-start">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Supported File Formats</AlertTitle>
          <AlertDescription>
            We support CSV, OFX, QIF, PDF, and Excel (XLSX) formats. For best results, use the export format provided by your bank.
          </AlertDescription>
        </Alert>
      </CardFooter>
    </Card>
  );
}

"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Download, FileText } from "lucide-react";
import { format } from "date-fns";

import { Reconciliation } from "@/types/reconciliation";

interface ReconciliationHistoryPanelProps {
  bankAccountId: string;
  currentReconciliation: Reconciliation | null;
}

export function ReconciliationHistoryPanel({
  bankAccountId,
  currentReconciliation,
}: ReconciliationHistoryPanelProps) {
  const [reconciliations, setReconciliations] = useState<Reconciliation[]>([]);

  // Format date
  const formatDate = (date: Date) => {
    return format(new Date(date), "MMM d, yyyy");
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'completed':
        return <Badge className="bg-blue-500">Completed</Badge>;
      case 'approved':
        return <Badge className="bg-green-500">Approved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Load reconciliation history
  useEffect(() => {
    // In a real implementation, this would fetch reconciliation history from the API
    // For now, generate mock data
    const mockReconciliations: Reconciliation[] = [
      {
        id: "REC-2025-01",
        bankAccountId,
        bankAccountName: "TCM Operations Account",
        statementDate: new Date(2025, 0, 31), // Jan 31, 2025
        startDate: new Date(2025, 0, 1), // Jan 1, 2025
        endDate: new Date(2025, 0, 31), // Jan 31, 2025
        startingBalance: 5000000,
        endingBalance: 5500000,
        bankStatementBalance: 5500000,
        adjustedBankBalance: 5500000,
        bookBalance: 5500000,
        adjustedBookBalance: 5500000,
        difference: 0,
        status: 'approved',
        transactions: [],
        unreconciled: [],
        adjustments: [],
        notes: "January 2025 reconciliation",
        completedBy: "John Doe",
        completedAt: new Date(2025, 1, 5), // Feb 5, 2025
        approvedBy: "Jane Smith",
        approvedAt: new Date(2025, 1, 7), // Feb 7, 2025
        createdBy: "John Doe",
        createdAt: new Date(2025, 1, 5), // Feb 5, 2025
      },
      {
        id: "REC-2024-12",
        bankAccountId,
        bankAccountName: "TCM Operations Account",
        statementDate: new Date(2024, 11, 31), // Dec 31, 2024
        startDate: new Date(2024, 11, 1), // Dec 1, 2024
        endDate: new Date(2024, 11, 31), // Dec 31, 2024
        startingBalance: 4500000,
        endingBalance: 5000000,
        bankStatementBalance: 5000000,
        adjustedBankBalance: 5000000,
        bookBalance: 5000000,
        adjustedBookBalance: 5000000,
        difference: 0,
        status: 'approved',
        transactions: [],
        unreconciled: [],
        adjustments: [],
        notes: "December 2024 reconciliation",
        completedBy: "John Doe",
        completedAt: new Date(2025, 0, 8), // Jan 8, 2025
        approvedBy: "Jane Smith",
        approvedAt: new Date(2025, 0, 10), // Jan 10, 2025
        createdBy: "John Doe",
        createdAt: new Date(2025, 0, 8), // Jan 8, 2025
      },
      {
        id: "REC-2024-11",
        bankAccountId,
        bankAccountName: "TCM Operations Account",
        statementDate: new Date(2024, 10, 30), // Nov 30, 2024
        startDate: new Date(2024, 10, 1), // Nov 1, 2024
        endDate: new Date(2024, 10, 30), // Nov 30, 2024
        startingBalance: 4000000,
        endingBalance: 4500000,
        bankStatementBalance: 4500000,
        adjustedBankBalance: 4500000,
        bookBalance: 4500000,
        adjustedBookBalance: 4500000,
        difference: 0,
        status: 'approved',
        transactions: [],
        unreconciled: [],
        adjustments: [],
        notes: "November 2024 reconciliation",
        completedBy: "John Doe",
        completedAt: new Date(2024, 11, 7), // Dec 7, 2024
        approvedBy: "Jane Smith",
        approvedAt: new Date(2024, 11, 9), // Dec 9, 2024
        createdBy: "John Doe",
        createdAt: new Date(2024, 11, 7), // Dec 7, 2024
      },
    ];

    // Add current reconciliation if it exists
    if (currentReconciliation) {
      setReconciliations([currentReconciliation, ...mockReconciliations]);
    } else {
      setReconciliations(mockReconciliations);
    }
  }, [bankAccountId, currentReconciliation]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Reconciliation History</CardTitle>
        <CardDescription>
          Previous reconciliations for this bank account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Period</TableHead>
                <TableHead>Completed Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Difference</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reconciliations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    No reconciliation history found
                  </TableCell>
                </TableRow>
              ) : (
                reconciliations.map((reconciliation) => (
                  <TableRow key={reconciliation.id}>
                    <TableCell>
                      {formatDate(reconciliation.startDate)} - {formatDate(reconciliation.endDate)}
                    </TableCell>
                    <TableCell>
                      {reconciliation.completedAt ? formatDate(reconciliation.completedAt) : "Not completed"}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(reconciliation.status)}
                    </TableCell>
                    <TableCell>
                      <span className={reconciliation.difference === 0 ? "text-green-500" : "text-red-500"}>
                        {new Intl.NumberFormat("en-MW", {
                          style: "currency",
                          currency: "MWK",
                        }).format(reconciliation.difference)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="sm">
                          <FileText className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Export
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter>
        <div className="text-sm text-muted-foreground">
          Showing {reconciliations.length} reconciliations
        </div>
      </CardFooter>
    </Card>
  );
}

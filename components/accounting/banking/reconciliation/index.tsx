// components/accounting/banking/reconciliation/index.tsx
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Check, RefreshCw } from "lucide-react";

// Import sub-components
import { ReconciliationSummary } from "./reconciliation-summary";
import { StatementImportPanel } from "./statement-import-panel";
import { TransactionMatchingPanel } from "./transaction-matching-panel";
import { ReconciliationApprovalPanel } from "./reconciliation-approval-panel";
import { ReconciliationHistoryPanel } from "./reconciliation-history-panel";

// Import types
import { BankAccount } from "@/types/banking";
import { BankTransaction, BankStatementItem, Reconciliation } from "@/types/reconciliation";

interface BankReconciliationModuleProps {
  bankAccounts: BankAccount[];
  initialTransactions?: BankTransaction[];
  initialStatementItems?: BankStatementItem[];
  onComplete?: (reconciliation: Reconciliation) => void;
}

export function BankReconciliationModule({
  bankAccounts,
  initialTransactions = [],
  initialStatementItems = [],
  onComplete,
}: BankReconciliationModuleProps) {
  // State
  const [activeTab, setActiveTab] = useState("summary");
  const [selectedAccount, setSelectedAccount] = useState<string>(
    bankAccounts.length > 0 ? bankAccounts[0].id : ""
  );
  const [transactions, setTransactions] = useState<BankTransaction[]>(initialTransactions);
  const [statementItems, setStatementItems] = useState<BankStatementItem[]>(initialStatementItems);
  const [isReconciling, setIsReconciling] = useState(false);
  const [reconciliation, setReconciliation] = useState<Reconciliation | null>(null);
  
  const { toast } = useToast();

  // Calculate balances and differences
  const bookBalance = transactions.reduce((sum, tx) => sum + tx.amount, 0);
  const bankBalance = statementItems.reduce((sum, item) => sum + item.amount, 0);
  const difference = bookBalance - bankBalance;

  // Calculate reconciliation statistics
  const totalTransactions = transactions.length;
  const reconciledCount = transactions.filter(tx => tx.reconciled).length;
  const reconciledPercentage = totalTransactions > 0 
    ? Math.round((reconciledCount / totalTransactions) * 100) 
    : 0;

  const totalStatementItems = statementItems.length;
  const matchedCount = statementItems.filter(item => item.matched).length;
  const matchedPercentage = totalStatementItems > 0 
    ? Math.round((matchedCount / totalStatementItems) * 100) 
    : 0;

  // Handle bank account change
  const handleAccountChange = (accountId: string) => {
    setSelectedAccount(accountId);
    // In a real implementation, this would fetch transactions and statement items for the selected account
  };

  // Handle statement import completion
  const handleImportComplete = (newItems: BankStatementItem[]) => {
    setStatementItems(prev => [...prev, ...newItems]);
    toast({
      title: "Import Successful",
      description: `Successfully imported ${newItems.length} statement items.`,
    });
    setActiveTab("match");
  };

  // Handle transaction matching completion
  const handleMatchComplete = (matches: { 
    statementItem: BankStatementItem; 
    transaction: BankTransaction  | undefined;
    confidence: number;
  }[]) => {
    // Update transactions
    const updatedTransactions = [...transactions];
    const updatedStatementItems = [...statementItems];
    
    matches.forEach(match => {
      if (match.transaction) {
        // Update transaction
        const txIndex = updatedTransactions.findIndex(tx => tx.id === match.transaction?.id);
        if (txIndex !== -1) {
          updatedTransactions[txIndex] = {
            ...updatedTransactions[txIndex],
            reconciled: true,
          };
        }
        
        // Update statement item
        const itemIndex = updatedStatementItems.findIndex(item => item.id === match.statementItem.id);
        if (itemIndex !== -1) {
          updatedStatementItems[itemIndex] = {
            ...updatedStatementItems[itemIndex],
            matched: true,
            matchedTransactionId: match.transaction.id,
          };
        }
      }
    });
    
    setTransactions(updatedTransactions);
    setStatementItems(updatedStatementItems);
    
    toast({
      title: "Matching Complete",
      description: `Successfully matched ${matches.filter(m => m.transaction).length} transactions.`,
    });
    
    setActiveTab("summary");
  };

  // Handle reconciliation completion
  const handleReconcile = async () => {
    setIsReconciling(true);
    
    try {
      // Create reconciliation object
      const newReconciliation: Reconciliation = {
        id: `REC-${Date.now()}`,
        bankAccountId: selectedAccount,
        bankAccountName: bankAccounts.find(a => a.id === selectedAccount)?.name || "",
        statementDate: new Date(),
        startDate: new Date(Math.min(...statementItems.map(item => item.date.getTime()))),
        endDate: new Date(Math.max(...statementItems.map(item => item.date.getTime()))),
        startingBalance: 0, // Would be calculated in a real implementation
        endingBalance: bookBalance,
        bankStatementBalance: bankBalance,
        adjustedBankBalance: bankBalance,
        bookBalance: bookBalance,
        adjustedBookBalance: bookBalance,
        difference: difference,
        status: 'completed',
        transactions: transactions.filter(tx => tx.reconciled),
        unreconciled: transactions.filter(tx => !tx.reconciled),
        adjustments: [],
        notes: `Reconciliation for ${bankAccounts.find(a => a.id === selectedAccount)?.name}`,
        completedBy: "Current User", // Would come from auth in a real implementation
        completedAt: new Date(),
        createdBy: "Current User",
        createdAt: new Date(),
      };
      
      setReconciliation(newReconciliation);
      
      // Call onComplete callback
      if (onComplete) {
        onComplete(newReconciliation);
      }
      
      toast({
        title: "Reconciliation Complete",
        description: "The bank reconciliation has been completed successfully.",
      });
      
      setActiveTab("approve");
    } catch (error) {
      console.error("Error completing reconciliation:", error);
      toast({
        title: "Reconciliation Failed",
        description: "There was an error completing the reconciliation.",
        variant: "destructive",
      });
    } finally {
      setIsReconciling(false);
    }
  };

  // Handle reconciliation approval
  const handleApprove = async (id: string, notes: string) => {
    if (!reconciliation) return;
    
    try {
      // Update reconciliation
      const updatedReconciliation: Reconciliation = {
        ...reconciliation,
        status: 'approved',
        approvedBy: "Approver User", // Would come from auth in a real implementation
        approvedAt: new Date(),
        notes: notes ? `${reconciliation.notes}\n\nApproval Notes: ${notes}` : reconciliation.notes,
      };
      
      setReconciliation(updatedReconciliation);
      
      toast({
        title: "Reconciliation Approved",
        description: "The reconciliation has been approved successfully.",
      });
      
      setActiveTab("history");
    } catch (error) {
      console.error("Error approving reconciliation:", error);
      toast({
        title: "Approval Failed",
        description: "There was an error approving the reconciliation.",
        variant: "destructive",
      });
    }
  };

  // Handle reconciliation rejection
  const handleReject = async (id: string, notes: string) => {
    if (!reconciliation) return;
    
    try {
      // Update reconciliation
      const updatedReconciliation: Reconciliation = {
        ...reconciliation,
        status: 'in_progress',
        notes: `${reconciliation.notes}\n\nRejection Notes: ${notes}`,
      };
      
      setReconciliation(updatedReconciliation);
      
      toast({
        title: "Reconciliation Rejected",
        description: "The reconciliation has been rejected and returned for revision.",
      });
      
      setActiveTab("summary");
    } catch (error) {
      console.error("Error rejecting reconciliation:", error);
      toast({
        title: "Rejection Failed",
        description: "There was an error rejecting the reconciliation.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Bank Reconciliation</CardTitle>
            <CardDescription>
              Reconcile bank statements with accounting records
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {reconciliation?.status !== 'approved' && (
              <Button 
                onClick={handleReconcile} 
                disabled={isReconciling || reconciledCount === 0 || matchedCount === 0}
              >
                {isReconciling ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Reconciling...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Complete Reconciliation
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
            <TabsTrigger value="match">Match</TabsTrigger>
            <TabsTrigger value="approve">Approve</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summary">
            <ReconciliationSummary 
              bankAccounts={bankAccounts}
              selectedAccount={selectedAccount}
              onAccountChange={handleAccountChange}
              transactions={transactions}
              statementItems={statementItems}
              bookBalance={bookBalance}
              bankBalance={bankBalance}
              difference={difference}
              reconciledCount={reconciledCount}
              totalTransactions={totalTransactions}
              reconciledPercentage={reconciledPercentage}
              matchedCount={matchedCount}
              totalStatementItems={totalStatementItems}
              matchedPercentage={matchedPercentage}
            />
          </TabsContent>
          
          <TabsContent value="import">
            <StatementImportPanel 
              bankAccounts={bankAccounts}
              selectedAccount={selectedAccount}
              onAccountChange={handleAccountChange}
              onImportComplete={handleImportComplete}
            />
          </TabsContent>
          
          <TabsContent value="match">
            <TransactionMatchingPanel 
              transactions={transactions}
              statementItems={statementItems}
              onMatchComplete={handleMatchComplete}
            />
          </TabsContent>
          
          <TabsContent value="approve">
            {reconciliation ? (
              <ReconciliationApprovalPanel 
                reconciliation={reconciliation}
                onApprove={handleApprove}
                onReject={handleReject}
              />
            ) : (
              <div className="flex flex-col items-center justify-center p-8">
                <p className="text-muted-foreground">
                  No reconciliation to approve. Complete a reconciliation first.
                </p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setActiveTab("summary")}
                >
                  Go to Summary
                </Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="history">
            <ReconciliationHistoryPanel 
              bankAccountId={selectedAccount}
              currentReconciliation={reconciliation}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

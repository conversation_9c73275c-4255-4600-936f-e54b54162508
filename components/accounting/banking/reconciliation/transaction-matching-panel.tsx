// components\accounting\banking\reconciliation\transaction-matching-panel.tsx
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, RefreshCw, Check, Search, Filter } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/components/ui/use-toast";

import { BankTransaction, BankStatementItem } from "@/types/reconciliation";

interface TransactionMatchingPanelProps {
  transactions: BankTransaction[];
  statementItems: BankStatementItem[];
  onMatchComplete: (matches: Array<{
    statementItem: BankStatementItem;
    transaction: BankTransaction | undefined;
    confidence: number;
  }>) => void;
}

export function TransactionMatchingPanel({
  transactions,
  statementItems,
  onMatchComplete,
}: TransactionMatchingPanelProps) {
  const [isMatching, setIsMatching] = useState(false);
  const [matchResults, setMatchResults] = useState<Array<{
    statementItem: BankStatementItem;
    transaction: BankTransaction | undefined;
    confidence: number;
    matchType: string;
  }>>([]);
  const [selectedMatches, setSelectedMatches] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<"all" | "matched" | "unmatched">("all");
  const [filterConfidence, setFilterConfidence] = useState<"all" | "high" | "medium" | "low">("all");

  const { toast } = useToast();

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Perform initial matching when component mounts or transactions/statementItems change
  useEffect(() => {
    if (statementItems.length > 0 && transactions.length > 0) {
      handleAutoMatch();
    }
    // We only want to run this once on mount, not on every change to handleAutoMatch
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [statementItems.length, transactions.length]);

  // Handle auto-matching
  const handleAutoMatch = async () => {
    setIsMatching(true);

    try {
      // In a real implementation, this would call the matching service
      // For now, simulate a delay and generate mock matches
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate mock match results
      const results = statementItems.map(item => {
        // Find potential matches based on amount
        const potentialMatches = transactions.filter(tx =>
          !tx.reconciled && Math.abs(tx.amount - item.amount) < 0.01
        );

        if (potentialMatches.length > 0) {
          // Sort by date proximity
          potentialMatches.sort((a, b) =>
            Math.abs(a.date.getTime() - item.date.getTime()) -
            Math.abs(b.date.getTime() - item.date.getTime())
          );

          // Calculate confidence based on date proximity
          const daysDiff = Math.abs(potentialMatches[0].date.getTime() - item.date.getTime()) / (24 * 60 * 60 * 1000);
          const confidence = daysDiff < 1 ? 1.0 : daysDiff < 3 ? 0.8 : daysDiff < 7 ? 0.5 : 0.3;

          return {
            statementItem: item,
            transaction: potentialMatches[0],
            confidence,
            matchType: confidence > 0.8 ? 'exact' : confidence > 0.5 ? 'amount_date' : 'fuzzy',
          };
        } else {
          return {
            statementItem: item,
            transaction: undefined, // Changed from null to undefined to match the state type
            confidence: 0,
            matchType: 'none',
          };
        }
      });

      // Type assertion to ensure compatibility with state type
      setMatchResults(results as Array<{
        statementItem: BankStatementItem;
        transaction: BankTransaction | undefined;
        confidence: number;
        matchType: string;
      }>);

      // Select high-confidence matches by default
      const highConfidenceMatches = results
        .filter(result => result.confidence >= 0.8 && result.transaction)
        .map(result => result.statementItem.id);

      setSelectedMatches(highConfidenceMatches);

      toast({
        title: "Auto-Matching Complete",
        description: `Found ${results.filter(r => r.transaction).length} potential matches out of ${results.length} statement items.`,
      });
    } catch (error: unknown) {
      // Use a more descriptive error message
      const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : "An unknown error occurred during transaction matching";
      console.error("Error matching transactions:", errorMessage);
      toast({
        title: "Matching Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsMatching(false);
    }
  };

  // Handle selection change
  const handleSelectionChange = (statementItemId: string, checked: boolean) => {
    if (checked) {
      setSelectedMatches(prev => [...prev, statementItemId]);
    } else {
      setSelectedMatches(prev => prev.filter(id => id !== statementItemId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredResults = getFilteredResults();
      setSelectedMatches(filteredResults.filter(result => result.transaction).map(result => result.statementItem.id));
    } else {
      setSelectedMatches([]);
    }
  };

  // Handle confirm matches
  const handleConfirmMatches = () => {
    // Filter results to only include selected matches
    const confirmedMatches = matchResults
      .filter(result => selectedMatches.includes(result.statementItem.id))
      .map(result => ({
        statementItem: result.statementItem,
        transaction: result.transaction,
        confidence: result.confidence,
      }));

    // Call onMatchComplete callback
    onMatchComplete(confirmedMatches);

    toast({
      title: "Matches Confirmed",
      description: `Successfully confirmed ${confirmedMatches.length} matches.`,
    });
  };

  // Get confidence badge
  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge variant="default" className="bg-green-500">High</Badge>;
    } else if (confidence >= 0.5) {
      return <Badge variant="secondary" className="bg-yellow-500">Medium</Badge>;
    } else if (confidence > 0) {
      return <Badge variant="destructive">Low</Badge>;
    } else {
      return <Badge variant="outline">None</Badge>;
    }
  };

  // Filter results based on search and filters
  const getFilteredResults = () => {
    return matchResults.filter(result => {
      // Filter by search term
      const searchMatch =
        searchTerm === "" ||
        result.statementItem.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.statementItem.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (result.transaction?.description.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
        (result.transaction?.reference.toLowerCase().includes(searchTerm.toLowerCase()) || false);

      // Filter by match status
      const statusMatch =
        filterStatus === "all" ||
        (filterStatus === "matched" && result.transaction) ||
        (filterStatus === "unmatched" && !result.transaction);

      // Filter by confidence
      let confidenceMatch = true;
      if (filterConfidence === "high") {
        confidenceMatch = result.confidence >= 0.8;
      } else if (filterConfidence === "medium") {
        confidenceMatch = result.confidence >= 0.5 && result.confidence < 0.8;
      } else if (filterConfidence === "low") {
        confidenceMatch = result.confidence > 0 && result.confidence < 0.5;
      }

      return searchMatch && statusMatch && confidenceMatch;
    });
  };

  const filteredResults = getFilteredResults();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction Matcher</CardTitle>
        <CardDescription>
          Match bank statement items with transactions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAutoMatch}
              disabled={isMatching}
            >
              {isMatching ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Matching...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Auto-Match
                </>
              )}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleConfirmMatches}
              disabled={isMatching || selectedMatches.length === 0}
            >
              <Check className="mr-2 h-4 w-4" />
              Confirm Matches
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={(value: "all" | "matched" | "unmatched") => setFilterStatus(value)}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Items</SelectItem>
                  <SelectItem value="matched">Matched</SelectItem>
                  <SelectItem value="unmatched">Unmatched</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterConfidence} onValueChange={(value: "all" | "high" | "medium" | "low") => setFilterConfidence(value)}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Confidence" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Confidence</SelectItem>
                  <SelectItem value="high">High Confidence</SelectItem>
                  <SelectItem value="medium">Medium Confidence</SelectItem>
                  <SelectItem value="low">Low Confidence</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={filteredResults.length > 0 &&
                      filteredResults.filter(r => r.transaction).length > 0 &&
                      selectedMatches.length === filteredResults.filter(r => r.transaction).length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Statement Item</TableHead>
                <TableHead>Transaction</TableHead>
                <TableHead className="w-[100px]">Confidence</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredResults.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4">
                    No matching results found
                  </TableCell>
                </TableRow>
              ) : (
                filteredResults.map((result) => (
                  <TableRow key={result.statementItem.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedMatches.includes(result.statementItem.id)}
                        onCheckedChange={(checked) =>
                          handleSelectionChange(result.statementItem.id, checked === true)
                        }
                        disabled={!result.transaction}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{result.statementItem.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(result.statementItem.date, "MMM d, yyyy")} | {formatCurrency(result.statementItem.amount)}
                      </div>
                      {result.statementItem.reference && (
                        <div className="text-xs text-muted-foreground">
                          Ref: {result.statementItem.reference}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {result.transaction ? (
                        <>
                          <div className="font-medium">{result.transaction.description}</div>
                          <div className="text-sm text-muted-foreground">
                            {format(result.transaction.date, "MMM d, yyyy")} | {formatCurrency(result.transaction.amount)}
                          </div>
                          {result.transaction.reference && (
                            <div className="text-xs text-muted-foreground">
                              Ref: {result.transaction.reference}
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-muted-foreground italic">No match found</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {getConfidenceBadge(result.confidence)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredResults.length} of {matchResults.length} items
        </div>
        <div className="text-sm">
          {selectedMatches.length} items selected
        </div>
      </CardFooter>
    </Card>
  );
}

"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { format } from "date-fns";

import { BankAccount } from "@/types/banking";
import { BankTransaction, BankStatementItem } from "@/types/reconciliation";

interface ReconciliationSummaryProps {
  bankAccounts: BankAccount[];
  selectedAccount: string;
  onAccountChange: (accountId: string) => void;
  transactions: BankTransaction[];
  statementItems: BankStatementItem[];
  bookBalance: number;
  bankBalance: number;
  difference: number;
  reconciledCount: number;
  totalTransactions: number;
  reconciledPercentage: number;
  matchedCount: number;
  totalStatementItems: number;
  matchedPercentage: number;
}

export function ReconciliationSummary({
  bankAccounts,
  selectedAccount,
  onAccountChange,
  transactions,
  statementItems,
  bookBalance,
  bankBalance,
  difference,
  reconciledCount,
  totalTransactions,
  reconciledPercentage,
  matchedCount,
  totalStatementItems,
  matchedPercentage,
}: ReconciliationSummaryProps) {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <Select value={selectedAccount} onValueChange={onAccountChange}>
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Select bank account" />
          </SelectTrigger>
          <SelectContent>
            {bankAccounts.map((account) => (
              <SelectItem key={account.id} value={account.id}>
                {account.name} - {account.bank}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Book Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(bookBalance)}</div>
            <p className="text-xs text-muted-foreground">
              As of {format(new Date(), "MMMM d, yyyy")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Bank Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(bankBalance)}</div>
            <p className="text-xs text-muted-foreground">
              As of {format(new Date(), "MMMM d, yyyy")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Difference</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${Math.abs(difference) > 0.01 ? "text-red-500" : "text-green-500"}`}>
              {formatCurrency(difference)}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.abs(difference) > 0.01 ? "Needs reconciliation" : "Balanced"}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Reconciliation Progress</CardTitle>
            <CardDescription>
              Current status of the reconciliation process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Book Transactions</span>
                <span className="text-sm text-muted-foreground">
                  {reconciledCount} of {totalTransactions} reconciled
                </span>
              </div>
              <Progress value={reconciledPercentage} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Bank Statement Items</span>
                <span className="text-sm text-muted-foreground">
                  {matchedCount} of {totalStatementItems} matched
                </span>
              </div>
              <Progress value={matchedPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <h3 className="text-lg font-medium mb-2">Book Transactions</h3>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Match</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  transactions.slice(0, 5).map((transaction) => (
                    <TableRow key={transaction.id} className={transaction.reconciled ? "bg-green-50" : ""}>
                      <TableCell>
                        <Checkbox checked={transaction.reconciled} disabled />
                      </TableCell>
                      <TableCell>{format(transaction.date, "MMM d, yyyy")}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>{transaction.reference}</TableCell>
                      <TableCell className={`text-right ${transaction.amount < 0 ? "text-red-500" : "text-green-500"}`}>
                        {formatCurrency(transaction.amount)}
                      </TableCell>
                    </TableRow>
                  ))
                )}
                {transactions.length > 5 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-2 text-sm text-muted-foreground">
                      Showing 5 of {transactions.length} transactions
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium mb-2">Bank Statement</h3>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Match</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {statementItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No statement items found
                    </TableCell>
                  </TableRow>
                ) : (
                  statementItems.slice(0, 5).map((item) => (
                    <TableRow key={item.id} className={item.matched ? "bg-green-50" : ""}>
                      <TableCell>
                        <Checkbox checked={item.matched} disabled />
                      </TableCell>
                      <TableCell>{format(item.date, "MMM d, yyyy")}</TableCell>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.reference}</TableCell>
                      <TableCell className={`text-right ${item.amount < 0 ? "text-red-500" : "text-green-500"}`}>
                        {formatCurrency(item.amount)}
                      </TableCell>
                    </TableRow>
                  ))
                )}
                {statementItems.length > 5 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-2 text-sm text-muted-foreground">
                      Showing 5 of {statementItems.length} statement items
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}

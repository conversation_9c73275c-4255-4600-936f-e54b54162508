"use client";

import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, PlusCircle, Link2, CreditCard } from 'lucide-react';
import Link from 'next/link';
import { BankAccountManager } from '@/components/accounting/banking/bank-account-manager';
import { BankReconciliation } from '@/components/accounting/banking/bank-reconciliation';
import { BankReconciliationModule } from '@/components/accounting/banking/reconciliation';
import { CashFlowManager } from '@/components/accounting/banking/cash-flow-manager';
import { PaymentProcessor } from '@/components/accounting/banking/payment-processor';
import { BankAccount } from '@/types/banking';
import { BankTransaction, Reconciliation } from '@/types/reconciliation';
import { useToast } from '@/components/ui/use-toast';

interface BankingPageClientProps {
  bankAccounts: BankAccount[];
  sampleTransactions: BankTransaction[];
}

export function BankingPageClient({ bankAccounts, sampleTransactions }: BankingPageClientProps) {
  const { toast } = useToast();

  const handleReconciliationComplete = (reconciliation: Reconciliation) => {
    console.log("Reconciliation completed:", reconciliation);
    toast({
      title: "Reconciliation Saved",
      description: "The reconciliation has been saved successfully.",
    });
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Banking & Treasury</h1>
            <p className="text-muted-foreground">Manage bank accounts, reconciliations, and cash flow for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting/banking/integrations">
                <Link2 className="h-4 w-4" />
                <span>API Integrations</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting/payments">
                <CreditCard className="h-4 w-4" />
                <span>Payment Gateways</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button size="sm" className="gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>New Account</span>
            </Button>
          </div>
        </div>

        {/* Banking & Treasury Content */}
        <Tabs defaultValue="accounts" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="accounts">Bank Accounts</TabsTrigger>
            <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
            <TabsTrigger value="cash-flow">Cash Flow</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
          </TabsList>

          <TabsContent value="accounts" className="mt-6">
            <BankAccountManager />
          </TabsContent>

          <TabsContent value="reconciliation" className="mt-6">
            <BankReconciliationModule
              bankAccounts={bankAccounts}
              initialTransactions={sampleTransactions}
              onComplete={handleReconciliationComplete}
            />
          </TabsContent>

          <TabsContent value="cash-flow" className="mt-6">
            <CashFlowManager />
          </TabsContent>

          <TabsContent value="payments" className="mt-6">
            <PaymentProcessor />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

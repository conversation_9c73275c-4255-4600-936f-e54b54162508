import { BankStatementItem } from '@/models/accounting/BankStatement';

/**
 * Supported statement formats
 */
export type StatementFormat = 'csv' | 'xlsx' | 'ofx' | 'qif' | 'pdf';

export interface ParserOptions {
  dateFormat?: string;
  columnMapping?: {
    date: string;
    description: string;
    reference: string;
    amount: string;
    balance?: string;
  };
  bankAccountId: string;
}

/**
 * Interface for bank statement format parsers
 */
export interface IStatementFormatParser {
  /**
   * Parse a bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  parse(file: Buffer, options: ParserOptions): Promise<BankStatementItem[]>;

  /**
   * Check if this parser can handle the given file
   * @param file File buffer
   * @returns True if the parser can handle the file, false otherwise
   */
  canParse(file: Buffer): boolean;
}

/**
 * Abstract class for bank statement format parsers
 */
export abstract class StatementFormatParser implements IStatementFormatParser {
  /**
   * Parse a bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  abstract parse(file: Buffer, options: ParserOptions): Promise<BankStatementItem[]>;

  /**
   * Check if this parser can handle the given file
   * @param file File buffer
   * @returns True if the parser can handle the file, false otherwise
   */
  abstract canParse(file: Buffer): boolean;
}

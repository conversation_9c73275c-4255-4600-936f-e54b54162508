import { BankStatementItem } from '@/models/accounting/BankStatement';
import { ParserOptions, IStatementFormatParser } from './statement-format-parser';
import { v4 as uuidv4 } from 'uuid';

/**
 * Parser for XLSX (Excel) bank statements
 */
export class XLSXStatementParser implements IStatementFormatParser {
  readonly format = 'xlsx';

  /**
   * Check if this parser can parse the given file
   * @param file File buffer
   * @returns True if this parser can parse the file
   */
  canParse(file: Buffer): boolean {
    // Check for Excel file signature
    // XLSX files start with PK (zip file signature)
    return file[0] === 0x50 && file[1] === 0x4B;
  }

  /**
   * Parse an XLSX bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  async parse(file: Buffer, options: ParserOptions): Promise<BankStatementItem[]> {
    try {
      // In a real implementation, we would use a library like xlsx or exceljs
      // For this example, we'll return mock data

      console.log('Parsing XLSX file (mock implementation)');

      // Generate mock data
      const items: BankStatementItem[] = [];

      for (let i = 0; i < 10; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);

        items.push({
          id: uuidv4(),
          date,
          description: `Transaction ${i + 1}`,
          reference: `REF${i + 1000}`,
          amount: Math.round((Math.random() * 1000 - 500) * 100) / 100, // Random amount between -500 and 500
          imported: true,
          matched: false,
          processed: false,
        });
      }

      return items;
    } catch (error: unknown) {
      console.error('Error parsing XLSX file:', error);
      throw new Error(`Failed to parse XLSX file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

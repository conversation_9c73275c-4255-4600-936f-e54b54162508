// components/accounting/banking/statement-parser/csv-statement-parser.ts
import { BankStatementItem } from '@/models/accounting/BankStatement';
import { ParserOptions, IStatementFormatParser } from './statement-format-parser';
import { v4 as uuidv4 } from 'uuid';

/**
 * Parser for CSV bank statements
 */
export class CSVStatementParser implements IStatementFormatParser {

  /**
   * Check if this parser can parse the given file
   * @param file File buffer
   * @returns True if this parser can parse the file
   */
  canParse(file: Buffer): boolean {
    // Check if the file starts with a CSV header
    const header = file.toString('utf-8', 0, 100).toLowerCase();
    return header.includes('date') &&
      (header.includes('description') || header.includes('details') || header.includes('transaction')) &&
      (header.includes('amount') || header.includes('debit') || header.includes('credit'));
  }

  /**
   * Parse a CSV bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  async parse(file: <PERSON><PERSON><PERSON>, options: ParserOptions): Promise<BankStatementItem[]> {
    try {
      // Convert buffer to string
      const content = file.toString('utf-8');

      // Split into lines
      const lines = content.split(/\\r?\\n/).filter(line => line.trim().length > 0);

      // Extract header
      const header = lines[0].split(',').map(col => col.trim().toLowerCase());

      // Map columns based on options or auto-detect
      const columnMap = this.getColumnMap(header, options.columnMapping);

      // Parse rows
      const items: BankStatementItem[] = [];

      for (let i = 1; i < lines.length; i++) {
        const row = this.parseCSVRow(lines[i]);

        if (row.length < header.length) {
          continue; // Skip invalid rows
        }

        try {
          const item = this.mapRowToItem(row, columnMap, options.dateFormat);
          items.push(item);
        } catch (error) {
          console.warn(`Error parsing row ${i}:`, error);
          // Continue with next row
        }
      }

      return items;
    } catch (error: unknown) {
      console.error('Error parsing CSV file:', error);
      throw new Error(`Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse a CSV row, handling quoted values
   * @param line CSV line
   * @returns Array of values
   */
  private parseCSVRow(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  }

  /**
   * Get column mapping based on header and options
   * @param header CSV header
   * @param mapping Optional column mapping
   * @returns Column mapping
   */
  private getColumnMap(header: string[], mapping?: ParserOptions['columnMapping']) {
    if (mapping) {
      return {
        date: header.indexOf(mapping.date.toLowerCase()),
        description: header.indexOf(mapping.description.toLowerCase()),
        reference: header.indexOf(mapping.reference.toLowerCase()),
        amount: header.indexOf(mapping.amount.toLowerCase()),
        balance: mapping.balance ? header.indexOf(mapping.balance.toLowerCase()) : -1,
      };
    }

    // Auto-detect columns
    return {
      date: this.findColumnIndex(header, ['date', 'transaction date', 'trans date']),
      description: this.findColumnIndex(header, ['description', 'details', 'transaction', 'particulars', 'narrative']),
      reference: this.findColumnIndex(header, ['reference', 'ref', 'check', 'cheque', 'transaction id']),
      amount: this.findColumnIndex(header, ['amount', 'value', 'transaction amount']),
      balance: this.findColumnIndex(header, ['balance', 'running balance', 'closing balance']),
    };
  }

  /**
   * Find column index based on possible names
   * @param header CSV header
   * @param possibleNames Possible column names
   * @returns Column index or -1 if not found
   */
  private findColumnIndex(header: string[], possibleNames: string[]): number {
    for (const name of possibleNames) {
      const index = header.indexOf(name);
      if (index !== -1) {
        return index;
      }
    }
    return -1;
  }

  /**
   * Map a CSV row to a bank statement item
   * @param row CSV row
   * @param columnMap Column mapping
   * @param dateFormat Optional date format
   * @returns Bank statement item
   */
  private mapRowToItem(
    row: string[],
    columnMap: { date: number; description: number; reference: number; amount: number; balance: number },
    dateFormat?: string
  ): BankStatementItem {
    // Validate required columns
    if (columnMap.date === -1 || columnMap.description === -1 || columnMap.amount === -1) {
      throw new Error('Missing required columns in CSV file');
    }

    // Parse date
    const dateStr = row[columnMap.date];
    const date = this.parseDate(dateStr, dateFormat);

    // Parse description
    const description = row[columnMap.description];

    // Parse reference
    const reference = columnMap.reference !== -1 ? row[columnMap.reference] : '';

    // Parse amount
    const amountStr = row[columnMap.amount];
    const amount = this.parseAmount(amountStr);

    // Parse balance
    let balance: number | undefined;
    if (columnMap.balance !== -1) {
      const balanceStr = row[columnMap.balance];
      balance = this.parseAmount(balanceStr);
    }

    return {
      id: uuidv4(),
      date,
      description,
      reference,
      amount,
      balance,
      imported: true,
      matched: false,
      processed: false,
    };
  }

  /**
   * Parse a date string
   * @param dateStr Date string
   * @param format Optional date format
   * @returns Date object
   */
  private parseDate(dateStr: string, format?: string): Date {
    // Try to parse with the specified format
    if (format) {
      // Implement format-specific parsing here
      // For simplicity, we'll just use Date.parse for now
    }

    // Try common formats
    const date = new Date(dateStr);

    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date format: ${dateStr}`);
    }

    return date;
  }

  /**
   * Parse an amount string
   * @param amountStr Amount string
   * @returns Numeric amount
   */
  private parseAmount(amountStr: string): number {
    // Remove currency symbols and commas
    const cleaned = amountStr.replace(/[^0-9.-]/g, '');

    // Parse as float
    const amount = parseFloat(cleaned);

    if (isNaN(amount)) {
      throw new Error(`Invalid amount format: ${amountStr}`);
    }

    return amount;
  }
}

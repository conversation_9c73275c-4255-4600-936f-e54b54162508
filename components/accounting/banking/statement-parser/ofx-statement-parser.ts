import { BankStatementItem } from '@/models/accounting/BankStatement';
import { IStatementFormatParser, ParserOptions } from './statement-format-parser';

/**
 * OFX Statement Parser
 *
 * This class parses OFX (Open Financial Exchange) format bank statements.
 */
export class OFXStatementParser implements IStatementFormatParser {
  /**
   * Check if this parser can handle the given file
   * @param file File buffer
   * @returns True if the parser can handle the file, false otherwise
   */
  canParse(file: Buffer): boolean {
    const content = file.toString('utf-8');
    return content.includes('<OFX>') || content.includes('<ofx>');
  }

  /**
   * Parse a bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  async parse(file: Buffer, _options: ParserOptions): Promise<BankStatementItem[]> {
    const content = file.toString('utf-8');
    const transactions = this.parseOFXContent(content);

    return transactions.map((transaction, index) => ({
      id: `OFX-${index}`,
      date: new Date(transaction.date),
      description: transaction.description,
      reference: transaction.reference,
      amount: transaction.amount,
      imported: true,
      matched: false,
      processed: false,
    }));
  }

  /**
   * Parse an OFX file content into transactions
   * @param content The OFX file content as a string
   * @returns Array of parsed transactions
   */
  parseOFXContent(content: string): Array<{
    date: string;
    description: string;
    reference: string;
    amount: number;
  }> {
    // Check if the content is valid OFX
    if (!content.includes('<OFX>') && !content.includes('<ofx>')) {
      throw new Error('Invalid OFX file format');
    }

    // Extract transactions
    const transactions = this.extractTransactions(content);

    return transactions;
  }

  /**
   * Extract transactions from OFX content
   * @param content The OFX file content
   * @returns Array of transactions
   */
  private extractTransactions(content: string): Array<{
    date: string;
    description: string;
    reference: string;
    amount: number;
  }> {
    const transactions: Array<{
      date: string;
      description: string;
      reference: string;
      amount: number;
    }> = [];

    // Extract transaction sections
    const transactionRegex = /<STMTTRN>([\\s\\S]*?)<\/STMTTRN>/gi;
    let match;

    while ((match = transactionRegex.exec(content)) !== null) {
      const transactionContent = match[1];

      // Extract transaction details
      const dateMatch = /<DTPOSTED>(.*?)<\/DTPOSTED>/i.exec(transactionContent);
      const amountMatch = /<TRNAMT>(.*?)<\/TRNAMT>/i.exec(transactionContent);
      const nameMatch = /<NAME>(.*?)<\/NAME>/i.exec(transactionContent);
      const memoMatch = /<MEMO>(.*?)<\/MEMO>/i.exec(transactionContent);
      const fitidMatch = /<FITID>(.*?)<\/FITID>/i.exec(transactionContent);

      if (dateMatch && amountMatch) {
        // Parse date (format: YYYYMMDD)
        const dateStr = dateMatch[1];
        const year = parseInt(dateStr.substring(0, 4));
        const month = parseInt(dateStr.substring(4, 6)) - 1; // 0-based month
        const day = parseInt(dateStr.substring(6, 8));
        const date = new Date(year, month, day).toISOString();

        // Parse amount
        const amount = parseFloat(amountMatch[1]);

        // Get description and reference
        const description = nameMatch ? nameMatch[1] : (memoMatch ? memoMatch[1] : 'Unknown');
        const reference = fitidMatch ? fitidMatch[1] : '';

        transactions.push({
          date,
          description,
          reference,
          amount,
        });
      }
    }

    return transactions;
  }
}

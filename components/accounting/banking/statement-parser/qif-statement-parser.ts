import { BankStatementItem } from '@/models/accounting/BankStatement';
import { ParserOptions, IStatementFormatParser } from './statement-format-parser';
import { v4 as uuidv4 } from 'uuid';

/**
 * Parser for QIF (Quicken Interchange Format) bank statements
 */
export class QIFStatementParser implements IStatementFormatParser {
  readonly format = 'qif';

  /**
   * Check if this parser can parse the given file
   * @param file File buffer
   * @returns True if this parser can parse the file
   */
  canParse(file: Buffer): boolean {
    // Check if the file starts with QIF header
    const content = file.toString('utf-8', 0, 100);
    return content.includes('!Type:Bank') ||
           content.includes('!Type:CCard') ||
           content.includes('!Type:Cash');
  }

  /**
   * Parse a QIF bank statement file
   * @param file File buffer
   * @param options Parser options
   * @returns Parsed statement items
   */
  async parse(file: Buffer, options: ParserOptions): Promise<BankStatementItem[]> {
    try {
      // Convert buffer to string
      const content = file.toString('utf-8');

      // Split into lines
      const lines = content.split(/\\r?\\n/);

      // Parse transactions
      const items: BankStatementItem[] = [];
      let currentItem: Partial<BankStatementItem> = {};

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (line === '^') {
          // End of transaction
          if (currentItem.date && currentItem.amount !== undefined) {
            items.push({
              id: uuidv4(),
              date: currentItem.date,
              description: currentItem.description || 'Unknown',
              reference: currentItem.reference || '',
              amount: currentItem.amount,
              imported: true,
              matched: false,
              processed: false,
            });
          }

          // Reset for next transaction
          currentItem = {};
        } else if (line.startsWith('D')) {
          // Date
          const dateStr = line.substring(1);
          currentItem.date = this.parseDate(dateStr);
        } else if (line.startsWith('T')) {
          // Amount
          const amountStr = line.substring(1);
          currentItem.amount = parseFloat(amountStr);
        } else if (line.startsWith('P') || line.startsWith('M')) {
          // Payee or Memo (description)
          currentItem.description = line.substring(1);
        } else if (line.startsWith('N')) {
          // Check number or reference
          currentItem.reference = line.substring(1);
        }
      }

      return items;
    } catch (error: unknown) {
      console.error('Error parsing QIF file:', error);
      throw new Error(`Failed to parse QIF file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse a date string in QIF format
   * @param dateStr Date string (MM/DD/YYYY or MM/DD/YY)
   * @returns Date object
   */
  private parseDate(dateStr: string): Date {
    // QIF dates are typically in MM/DD/YYYY or MM/DD/YY format
    const parts = dateStr.split('/');

    if (parts.length !== 3) {
      throw new Error(`Invalid QIF date format: ${dateStr}`);
    }

    const month = parseInt(parts[0]) - 1; // 0-based month
    const day = parseInt(parts[1]);
    let year = parseInt(parts[2]);

    // Handle 2-digit years
    if (year < 100) {
      year += year < 50 ? 2000 : 1900;
    }

    const date = new Date(year, month, day);

    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date: ${dateStr}`);
    }

    return date;
  }
}

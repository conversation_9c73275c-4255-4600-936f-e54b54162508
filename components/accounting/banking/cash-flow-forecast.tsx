"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format, addMonths, startOfMonth, endOfMonth } from 'date-fns';
import { Download, FileText, RefreshCw, TrendingUp, TrendingDown, DollarSign, Calendar } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Generate forecast months
const generateForecastMonths = (startDate: Date, months: number) => {
  const result = [];
  for (let i = 0; i < months; i++) {
    const date = addMonths(startDate, i);
    result.push({
      month: format(date, 'MMM yyyy'),
      startDate: startOfMonth(date),
      endDate: endOfMonth(date),
    });
  }
  return result;
};

// Sample cash flow forecast data
const generateSampleForecastData = (months: number) => {
  const startDate = new Date();
  const forecastMonths = generateForecastMonths(startDate, months);
  const result = [];

  for (let index = 0; index < forecastMonths.length; index++) {
    const month = forecastMonths[index];

    // Base values with some randomness
    const certificationFees = 3500000 + Math.floor(Math.random() * 500000);
    const membershipFees = 4500000 + Math.floor(Math.random() * 700000);
    const governmentSubventions = index % 3 === 0 ? 2000000 : 0; // Quarterly
    const otherIncome = 300000 + Math.floor(Math.random() * 200000);

    const salaries = 2500000 + Math.floor(Math.random() * 200000);
    const officeExpenses = 350000 + Math.floor(Math.random() * 50000);
    const utilities = 450000 + Math.floor(Math.random() * 75000);
    const rent = 1200000; // Fixed
    const otherExpenses = 750000 + Math.floor(Math.random() * 150000);

    const totalIncome = certificationFees + membershipFees + governmentSubventions + otherIncome;
    const totalExpenses = salaries + officeExpenses + utilities + rent + otherExpenses;
    const netCashFlow = totalIncome - totalExpenses;

    // Calculate running balance
    const previousBalance = index > 0 ? result[index - 1].endingBalance : 5000000;
    const endingBalance = previousBalance + netCashFlow;

    result.push({
      month: month.month,
      startDate: month.startDate,
      endDate: month.endDate,
      certificationFees,
      membershipFees,
      governmentSubventions,
      otherIncome,
      totalIncome,
      salaries,
      officeExpenses,
      utilities,
      rent,
      otherExpenses,
      totalExpenses,
      netCashFlow,
      beginningBalance: previousBalance,
      endingBalance,
    });
  }

  return result;
};

// Generate sample data
const forecastData = generateSampleForecastData(12);

// Custom tooltip for charts
const CustomTooltip = ({ active, payload, label }: unknown) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background p-3 border rounded-md shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: unknown, index: number) => (
          <p key={`item-${index}`} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {formatCurrency(entry.value)}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

interface CashFlowForecastProps {
  data?: typeof forecastData;
}

export function CashFlowForecast({ data = forecastData }: CashFlowForecastProps) {
  const [forecastPeriod, setForecastPeriod] = useState("12-months");
  const [isGenerating, setIsGenerating] = useState(false);
  const [viewType, setViewType] = useState("chart");

  // Calculate summary statistics
  const totalIncome = data.reduce((sum, month) => sum + month.totalIncome, 0);
  const totalExpenses = data.reduce((sum, month) => sum + month.totalExpenses, 0);
  const netCashFlow = totalIncome - totalExpenses;
  const averageMonthlyIncome = totalIncome / data.length;
  const averageMonthlyExpenses = totalExpenses / data.length;
  const lowestCashBalance = Math.min(...data.map(month => month.endingBalance));
  const highestCashBalance = Math.max(...data.map(month => month.endingBalance));

  // Handle generate button click
  const handleGenerate = () => {
    setIsGenerating(true);
    setTimeout(() => {
      setIsGenerating(false);
    }, 1500);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Cash Flow Forecast</CardTitle>
            <CardDescription>
              Forecast cash flow based on historical data and future projections
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={forecastPeriod} onValueChange={setForecastPeriod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3-months">3 Months</SelectItem>
                <SelectItem value="6-months">6 Months</SelectItem>
                <SelectItem value="12-months">12 Months</SelectItem>
                <SelectItem value="24-months">24 Months</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleGenerate} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Generate
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="income">Income</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="details">Detailed Forecast</TabsTrigger>
          </TabsList>

          <div className="flex justify-end gap-2 mb-4">
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={viewType === "chart" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewType("chart")}
                className="rounded-none"
              >
                Chart
              </Button>
              <Button
                variant={viewType === "table" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewType("table")}
                className="rounded-none"
              >
                Table
              </Button>
            </div>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="mr-2 h-4 w-4" />
              Report
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Forecast Income</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(totalIncome)}</div>
                <p className="text-xs text-muted-foreground">
                  Avg. {formatCurrency(averageMonthlyIncome)}/month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Forecast Expenses</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
                <p className="text-xs text-muted-foreground">
                  Avg. {formatCurrency(averageMonthlyExpenses)}/month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Cash Flow</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${netCashFlow >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {formatCurrency(netCashFlow)}
                </div>
                <p className="text-xs text-muted-foreground">
                  For the next {data.length} months
                </p>
              </CardContent>
            </Card>
          </div>

          <TabsContent value="overview">
            {viewType === "chart" ? (
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={data}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) =>
                        new Intl.NumberFormat('en-MW', {
                          notation: 'compact',
                          compactDisplay: 'short',
                          currency: 'MWK',
                        }).format(value)
                      }
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="totalIncome"
                      name="Income"
                      stackId="1"
                      stroke="#10b981"
                      fill="#10b981"
                      fillOpacity={0.3}
                    />
                    <Area
                      type="monotone"
                      dataKey="totalExpenses"
                      name="Expenses"
                      stackId="2"
                      stroke="#ef4444"
                      fill="#ef4444"
                      fillOpacity={0.3}
                    />
                    <Line
                      type="monotone"
                      dataKey="endingBalance"
                      name="Cash Balance"
                      stroke="#6366f1"
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Month</TableHead>
                      <TableHead className="text-right">Income</TableHead>
                      <TableHead className="text-right">Expenses</TableHead>
                      <TableHead className="text-right">Net Cash Flow</TableHead>
                      <TableHead className="text-right">Ending Balance</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((month, index) => (
                      <TableRow key={index}>
                        <TableCell>{month.month}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.totalIncome)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.totalExpenses)}</TableCell>
                        <TableCell className={`text-right ${month.netCashFlow >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {formatCurrency(month.netCashFlow)}
                        </TableCell>
                        <TableCell className="text-right font-medium">{formatCurrency(month.endingBalance)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
            <div className="mt-4 text-sm text-muted-foreground">
              <p>
                Cash flow forecast for the next {data.length} months. The lowest projected cash balance is {formatCurrency(lowestCashBalance)} and the highest is {formatCurrency(highestCashBalance)}.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="income">
            {viewType === "chart" ? (
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={data}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) =>
                        new Intl.NumberFormat('en-MW', {
                          notation: 'compact',
                          compactDisplay: 'short',
                          currency: 'MWK',
                        }).format(value)
                      }
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="certificationFees" name="Certification Fees" stackId="a" fill="#10b981" />
                    <Bar dataKey="membershipFees" name="Membership Fees" stackId="a" fill="#6366f1" />
                    <Bar dataKey="governmentSubventions" name="Government Subventions" stackId="a" fill="#f59e0b" />
                    <Bar dataKey="otherIncome" name="Other Income" stackId="a" fill="#8b5cf6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Month</TableHead>
                      <TableHead className="text-right">Certification Fees</TableHead>
                      <TableHead className="text-right">Membership Fees</TableHead>
                      <TableHead className="text-right">Government Subventions</TableHead>
                      <TableHead className="text-right">Other Income</TableHead>
                      <TableHead className="text-right">Total Income</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((month, index) => (
                      <TableRow key={index}>
                        <TableCell>{month.month}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.certificationFees)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.membershipFees)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.governmentSubventions)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.otherIncome)}</TableCell>
                        <TableCell className="text-right font-medium">{formatCurrency(month.totalIncome)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="expenses">
            {viewType === "chart" ? (
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={data}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) =>
                        new Intl.NumberFormat('en-MW', {
                          notation: 'compact',
                          compactDisplay: 'short',
                          currency: 'MWK',
                        }).format(value)
                      }
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="salaries" name="Salaries" stackId="a" fill="#ef4444" />
                    <Bar dataKey="officeExpenses" name="Office Expenses" stackId="a" fill="#f59e0b" />
                    <Bar dataKey="utilities" name="Utilities" stackId="a" fill="#6366f1" />
                    <Bar dataKey="rent" name="Rent" stackId="a" fill="#8b5cf6" />
                    <Bar dataKey="otherExpenses" name="Other Expenses" stackId="a" fill="#ec4899" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Month</TableHead>
                      <TableHead className="text-right">Salaries</TableHead>
                      <TableHead className="text-right">Office Expenses</TableHead>
                      <TableHead className="text-right">Utilities</TableHead>
                      <TableHead className="text-right">Rent</TableHead>
                      <TableHead className="text-right">Other Expenses</TableHead>
                      <TableHead className="text-right">Total Expenses</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((month, index) => (
                      <TableRow key={index}>
                        <TableCell>{month.month}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.salaries)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.officeExpenses)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.utilities)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.rent)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(month.otherExpenses)}</TableCell>
                        <TableCell className="text-right font-medium">{formatCurrency(month.totalExpenses)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="details">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead className="text-right">Beginning Balance</TableHead>
                    <TableHead className="text-right">Total Income</TableHead>
                    <TableHead className="text-right">Total Expenses</TableHead>
                    <TableHead className="text-right">Net Cash Flow</TableHead>
                    <TableHead className="text-right">Ending Balance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((month, index) => (
                    <TableRow key={index}>
                      <TableCell>{month.month}</TableCell>
                      <TableCell className="text-right">{formatCurrency(month.beginningBalance)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(month.totalIncome)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(month.totalExpenses)}</TableCell>
                      <TableCell className={`text-right ${month.netCashFlow >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {formatCurrency(month.netCashFlow)}
                      </TableCell>
                      <TableCell className="text-right font-medium">{formatCurrency(month.endingBalance)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Cash Flow Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Starting Cash Balance:</span>
                      <span className="text-sm font-medium">{formatCurrency(data[0].beginningBalance)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Forecast Income:</span>
                      <span className="text-sm font-medium">{formatCurrency(totalIncome)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Forecast Expenses:</span>
                      <span className="text-sm font-medium">{formatCurrency(totalExpenses)}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-medium">Net Cash Flow:</span>
                      <span className={`text-sm font-medium ${netCashFlow >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {formatCurrency(netCashFlow)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Ending Cash Balance:</span>
                      <span className="text-sm font-medium">{formatCurrency(data[data.length - 1].endingBalance)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Forecast Assumptions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>This forecast is based on the following assumptions:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Historical income and expense patterns from the past 12 months</li>
                      <li>Government subventions received quarterly</li>
                      <li>Fixed costs such as rent remain constant</li>
                      <li>Seasonal variations in certification and membership fees</li>
                      <li>No major unexpected expenses or income</li>
                    </ul>
                    <p className="text-xs text-muted-foreground mt-2">
                      Last updated: {format(new Date(), "MMMM d, yyyy")}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

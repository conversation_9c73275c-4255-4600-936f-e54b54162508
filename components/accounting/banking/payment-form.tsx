"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, Check, ChevronsUpDown } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

// Define the form schema with Zod
const paymentFormSchema = z.object({
  reference: z.string().min(1, {
    message: "Reference is required",
  }),
  date: z.date({
    required_error: "Payment date is required",
  }),
  amount: z.coerce.number().positive({
    message: "Amount must be a positive number",
  }),
  currency: z.string().default("MWK"),
  payee: z.string().min(1, {
    message: "Payee is required",
  }),
  description: z.string().min(1, {
    message: "Description is required",
  }),
  method: z.enum(["bank_transfer", "check", "cash", "mobile_money", "other"], {
    required_error: "Payment method is required",
  }),
  bankAccount: z.string().min(1, {
    message: "Bank account is required",
  }),
  checkNumber: z.string().optional(),
  transactionId: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentFormValues = z.infer<typeof paymentFormSchema>;

// Sample bank accounts for the dropdown
const bankAccounts = [
  { id: "1", name: "TCM Operations Account" },
  { id: "2", name: "TCM Payroll Account" },
  { id: "3", name: "TCM Reserve Account" },
  { id: "4", name: "TCM Development Fund" },
];

// Sample payees for the combobox
const payees = [
  { id: "1", name: "Office Supplies Ltd" },
  { id: "2", name: "IT Solutions Inc" },
  { id: "3", name: "Cleaning Services Co" },
  { id: "4", name: "Training Solutions Ltd" },
  { id: "5", name: "Consulting Group" },
  { id: "6", name: "Maintenance Services" },
  { id: "7", name: "Utility Company" },
  { id: "8", name: "Internet Provider" },
  { id: "9", name: "Security Services" },
  { id: "10", name: "Catering Services" },
];

interface PaymentFormProps {
  defaultValues?: Partial<PaymentFormValues>;
  onSubmit: (values: PaymentFormValues) => void;
  isSubmitting?: boolean;
}

export function PaymentForm({
  defaultValues,
  onSubmit,
  isSubmitting = false,
}: PaymentFormProps) {
  const [open, setOpen] = useState(false);

  // Initialize the form
  const form = useForm<PaymentFormValues>({
    resolver: zodResolver(paymentFormSchema) as any,
    defaultValues: {
      reference: defaultValues?.reference || "",
      date: defaultValues?.date || new Date(),
      amount: defaultValues?.amount || undefined,
      currency: defaultValues?.currency || "MWK",
      payee: defaultValues?.payee || "",
      description: defaultValues?.description || "",
      method: defaultValues?.method || undefined,
      bankAccount: defaultValues?.bankAccount || "",
      checkNumber: defaultValues?.checkNumber || "",
      transactionId: defaultValues?.transactionId || "",
      notes: defaultValues?.notes || "",
    },
  });

  // Watch the payment method to conditionally show fields
  const watchMethod = form.watch("method");

  // Handle form submission
  const handleSubmit = (values: PaymentFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {/* Reference */}
          <FormField
            control={form.control as any}
            name="reference"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reference</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., INV-2025-001" {...field} />
                </FormControl>
                <FormDescription>
                  Enter an invoice or reference number
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Date */}
          <FormField
            control={form.control as any}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Payment Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {/* Amount */}
          <FormField
            control={form.control as any}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="0.00"
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? undefined : Number(value));
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Currency */}
          <FormField
            control={form.control as any}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="MWK">Malawian Kwacha (MWK)</SelectItem>
                    <SelectItem value="USD">US Dollar (USD)</SelectItem>
                    <SelectItem value="EUR">Euro (EUR)</SelectItem>
                    <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                    <SelectItem value="ZAR">South African Rand (ZAR)</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Payee */}
        <FormField
          control={form.control as any}
          name="payee"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Payee</FormLabel>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "w-full justify-between",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? payees.find((payee) => payee.name === field.value)
                            ?.name
                        : "Select payee"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[400px] p-0">
                  <Command>
                    <CommandInput placeholder="Search payee..." />
                    <CommandEmpty>No payee found.</CommandEmpty>
                    <CommandGroup>
                      {payees.map((payee) => (
                        <CommandItem
                          key={payee.id}
                          value={payee.name}
                          onSelect={() => {
                            form.setValue("payee", payee.name);
                            setOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              payee.name === field.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {payee.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormDescription>
                Select a payee or enter a new one
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control as any}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter payment description"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {/* Payment Method */}
          <FormField
            control={form.control as any}
            name="method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Method</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="check">Check</SelectItem>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="mobile_money">Mobile Money</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Bank Account */}
          <FormField
            control={form.control as any}
            name="bankAccount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bank Account</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select bank account" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {bankAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Conditional fields based on payment method */}
        {watchMethod === "check" && (
          <FormField
            control={form.control as any}
            name="checkNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Check Number</FormLabel>
                <FormControl>
                  <Input placeholder="Enter check number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {(watchMethod === "bank_transfer" || watchMethod === "mobile_money") && (
          <FormField
            control={form.control as any}
            name="transactionId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Transaction ID</FormLabel>
                <FormControl>
                  <Input placeholder="Enter transaction ID" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Notes */}
        <FormField
          control={form.control as any}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Additional notes (optional)"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Payment"}
          </Button>
        </div>
      </form>
    </Form>
  );
}

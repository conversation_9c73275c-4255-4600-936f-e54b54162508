"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, RefreshCw, Check, X, AlertCircle, Search, Filter } from "lucide-react";
import { BankStatementItem } from "@/models/accounting/BankStatement";
import { BankTransaction, MatchResult, reconciliationMatchingService } from "@/lib/services/reconciliation-matching-service";
import { useToast } from "@/components/ui/use-toast";
import { formatCurrency } from '@/lib/utils/format';

interface TransactionMatcherProps {
  bankAccountId: string;
  statementItems: BankStatementItem[];
  transactions: BankTransaction[];
  onMatchComplete?: (matches: MatchResult[]) => void;
}

export function TransactionMatcher({
  bankAccountId,
  statementItems,
  transactions,
  onMatchComplete,
}: TransactionMatcherProps) {
  const [isMatching, setIsMatching] = useState(false);
  const [matchResults, setMatchResults] = useState<MatchResult[]>([]);
  const [selectedMatches, setSelectedMatches] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<"all" | "matched" | "unmatched">("all");
  const [filterConfidence, setFilterConfidence] = useState<"all" | "high" | "medium" | "low">("all");

  const { toast } = useToast();

  // Perform initial matching
  useEffect(() => {
    if (statementItems.length > 0 && transactions.length > 0) {
      handleAutoMatch();
    }
  }, [statementItems, transactions]);

  // Handle auto-matching
  const handleAutoMatch = async () => {
    setIsMatching(true);

    try {
      // Perform matching
      const results = reconciliationMatchingService.matchTransactions(statementItems, transactions);

      // Set results
      setMatchResults(results);

      // Select high-confidence matches by default
      const highConfidenceMatches = results
        .filter(result => result.confidence >= 0.8 && result.transaction)
        .map(result => result.statementItem.id);

      setSelectedMatches(highConfidenceMatches);

      toast({
        title: "Auto-Matching Complete",
        description: `Found ${results.filter(r => r.transaction).length} potential matches out of ${results.length} statement items.`,
      });
    } catch (error: unknown) {
      console.error("Error matching transactions:", error);
      toast({
        title: "Matching Failed",
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsMatching(false);
    }
  };

  // Handle manual matching
  const handleManualMatch = (statementItemId: string, transactionId: string  | undefined) => {
    setMatchResults(prev =>
      prev.map(result => {
        if (result.statementItem.id === statementItemId) {
          // Find the transaction
          const transaction = transactions.find(tx => tx.id === transactionId) || null;

          return {
            ...result,
            transaction,
            confidence: transaction ? 1.0 : 0,
            matchType: transaction ? 'exact' : 'none',
          };
        }
        return result;
      })
    );
  };

  // Handle selection change
  const handleSelectionChange = (statementItemId: string, checked: boolean) => {
    if (checked) {
      setSelectedMatches(prev => [...prev, statementItemId]);
    } else {
      setSelectedMatches(prev => prev.filter(id => id !== statementItemId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredResults = getFilteredResults();
      setSelectedMatches(filteredResults.map(result => result.statementItem.id));
    } else {
      setSelectedMatches([]);
    }
  };

  // Handle confirm matches
  const handleConfirmMatches = () => {
    // Filter results to only include selected matches
    const confirmedMatches = matchResults.map(result => {
      if (selectedMatches.includes(result.statementItem.id)) {
        return result;
      } else {
        return {
          ...result,
          transaction: null,
          confidence: 0,
          matchType: 'none' as const,
        };
      }
    });

    // Call onMatchComplete callback
    if (onMatchComplete) {
      onMatchComplete(confirmedMatches);
    }

    toast({
      title: "Matches Confirmed",
      description: `Successfully confirmed ${selectedMatches.length} matches.`,
    });
  };

  // Get confidence badge
  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge className="bg-green-500">High</Badge>;
    } else if (confidence >= 0.5) {
      return <Badge className="bg-yellow-500">Medium</Badge>;
    } else if (confidence > 0) {
      return <Badge className="bg-red-500">Low</Badge>;
    } else {
      return <Badge variant="outline">None</Badge>;
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Format amount
  const formatAmount = (amount: number) => {
    return formatCurrency(amount, 'MWK');
  };

  // Filter results based on search and filters
  const getFilteredResults = () => {
    return matchResults.filter(result => {
      // Filter by search term
      const searchMatch =
        searchTerm === "" ||
        result.statementItem.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.statementItem.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (result.transaction?.description.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
        (result.transaction?.reference.toLowerCase().includes(searchTerm.toLowerCase()) || false);

      // Filter by match status
      const statusMatch =
        filterStatus === "all" ||
        (filterStatus === "matched" && result.transaction) ||
        (filterStatus === "unmatched" && !result.transaction);

      // Filter by confidence
      let confidenceMatch = true;
      if (filterConfidence === "high") {
        confidenceMatch = result.confidence >= 0.8;
      } else if (filterConfidence === "medium") {
        confidenceMatch = result.confidence >= 0.5 && result.confidence < 0.8;
      } else if (filterConfidence === "low") {
        confidenceMatch = result.confidence > 0 && result.confidence < 0.5;
      }

      return searchMatch && statusMatch && confidenceMatch;
    });
  };

  const filteredResults = getFilteredResults();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction Matcher</CardTitle>
        <CardDescription>
          Match bank statement items with transactions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAutoMatch}
              disabled={isMatching}
            >
              {isMatching ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Matching...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Auto-Match
                </>
              )}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleConfirmMatches}
              disabled={isMatching || selectedMatches.length === 0}
            >
              <Check className="mr-2 h-4 w-4" />
              Confirm Matches
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={(value: "all" | "matched" | "unmatched") => setFilterStatus(value)}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Items</SelectItem>
                  <SelectItem value="matched">Matched</SelectItem>
                  <SelectItem value="unmatched">Unmatched</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterConfidence} onValueChange={(value: "all" | "high" | "medium" | "low") => setFilterConfidence(value)}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Confidence" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Confidence</SelectItem>
                  <SelectItem value="high">High Confidence</SelectItem>
                  <SelectItem value="medium">Medium Confidence</SelectItem>
                  <SelectItem value="low">Low Confidence</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={filteredResults.length > 0 && selectedMatches.length === filteredResults.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Statement Item</TableHead>
                <TableHead>Transaction</TableHead>
                <TableHead className="w-[100px]">Confidence</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredResults.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4">
                    No matching results found
                  </TableCell>
                </TableRow>
              ) : (
                filteredResults.map((result) => (
                  <TableRow key={result.statementItem.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedMatches.includes(result.statementItem.id)}
                        onCheckedChange={(checked) =>
                          handleSelectionChange(result.statementItem.id, checked === true)
                        }
                        disabled={!result.transaction}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{result.statementItem.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(result.statementItem.date)} | {formatAmount(result.statementItem.amount)}
                      </div>
                      {result.statementItem.reference && (
                        <div className="text-xs text-muted-foreground">
                          Ref: {result.statementItem.reference}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {result.transaction ? (
                        <>
                          <div className="font-medium">{result.transaction.description}</div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(result.transaction.date)} | {formatAmount(result.transaction.amount)}
                          </div>
                          {result.transaction.reference && (
                            <div className="text-xs text-muted-foreground">
                              Ref: {result.transaction.reference}
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-muted-foreground italic">No match found</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {getConfidenceBadge(result.confidence)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredResults.length} of {matchResults.length} items
        </div>
        <div className="text-sm">
          {selectedMatches.length} items selected
        </div>
      </CardFooter>
    </Card>
  );
}

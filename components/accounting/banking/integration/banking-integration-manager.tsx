// components/accounting/banking/integration/banking-integration-manager.tsx
"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Edit, Trash, RefreshCw, Download, Calendar } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/components/ui/use-toast";

import { BankingIntegration, BankingCredentials, BankingProvider, AuthMethod, ConnectionStatus } from "@/types/banking-integration";
import { BankAccount } from "@/types/banking";

// Define the form schema
const integrationFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  provider: z.enum([
    'standard_bank',
    'national_bank',
    'fdh_bank',
    'first_capital_bank',
    'nedbank',
    'ecobank',
    'open_banking',
    'custom'
  ], {
    required_error: "Provider is required",
  }),
  description: z.string().optional(),
  bankAccountId: z.string({
    required_error: "Bank account is required",
  }),
  credentialsId: z.string({
    required_error: "Credentials are required",
  }),
  settings: z.object({
    baseUrl: z.string().min(1, "Base URL is required"),
    accountEndpoint: z.string().optional(),
    transactionEndpoint: z.string().optional(),
    balanceEndpoint: z.string().optional(),
    statementEndpoint: z.string().optional(),
    paymentEndpoint: z.string().optional(),
    webhookEndpoint: z.string().optional(),
    pollingInterval: z.number().optional(),
    startDate: z.date().optional(),
  }),
});

type IntegrationFormValues = z.infer<typeof integrationFormSchema>;

interface BankingIntegrationManagerProps {
  bankAccounts: BankAccount[];
  initialIntegrations?: BankingIntegration[];
  initialCredentials?: BankingCredentials[];
}

export function BankingIntegrationManager({
  bankAccounts,
  initialIntegrations = [],
  initialCredentials = [],
}: BankingIntegrationManagerProps) {
  const [integrations, setIntegrations] = useState<BankingIntegration[]>(initialIntegrations);
  const [credentials, setCredentials] = useState<BankingCredentials[]>(initialCredentials);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState<Record<string, boolean>>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingIntegration, setEditingIntegration] = useState<BankingIntegration | null>(null);
  const [activeTab, setActiveTab] = useState("integrations");

  const { toast } = useToast();

  // Initialize the form
  const form = useForm<IntegrationFormValues>({
    resolver: zodResolver(integrationFormSchema),
    defaultValues: {
      name: "",
      provider: "standard_bank",
      description: "",
      bankAccountId: "",
      credentialsId: "",
      settings: {
        baseUrl: "",
        pollingInterval: 60, // Default to 60 minutes
      },
    },
  });

  // Load integrations and credentials
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would fetch from the API
        // For now, use the initial data
        setIsLoading(false);
      } catch (error) {
        console.error("Error loading data:", error);
        toast({
          title: "Error",
          description: `Failed to load integrations and credentials: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    loadData();
  }, [toast]);

  // Handle edit integration
  const handleEditIntegration = (integration: BankingIntegration) => {
    setEditingIntegration(integration);
    form.reset({
      name: integration.name,
      provider: integration.provider,
      description: integration.description,
      bankAccountId: integration.bankAccountId,
      credentialsId: integration.credentialsId,
      settings: {
        baseUrl: integration.settings.baseUrl,
        accountEndpoint: integration.settings.accountEndpoint,
        transactionEndpoint: integration.settings.transactionEndpoint,
        balanceEndpoint: integration.settings.balanceEndpoint,
        statementEndpoint: integration.settings.statementEndpoint,
        paymentEndpoint: integration.settings.paymentEndpoint,
        webhookEndpoint: integration.settings.webhookEndpoint,
        pollingInterval: integration.settings.pollingInterval,
        startDate: integration.settings.startDate,
      },
    });
    setIsDialogOpen(true);
  };

  // Handle new integration
  const handleNewIntegration = () => {
    setEditingIntegration(null);
    form.reset({
      name: "",
      provider: "standard_bank",
      description: "",
      bankAccountId: bankAccounts.length > 0 ? bankAccounts[0].id : "",
      credentialsId: credentials.length > 0 ? credentials[0].id : "",
      settings: {
        baseUrl: "",
        pollingInterval: 60, // Default to 60 minutes
      },
    });
    setIsDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: IntegrationFormValues) => {
    setIsLoading(true);

    try {
      if (editingIntegration) {
        // Update integration
        // In a real implementation, this would call the API
        const updatedIntegration = {
          ...editingIntegration,
          ...data,
          updatedAt: new Date(),
        };

        // Update the integrations list
        setIntegrations(prev =>
          prev.map(integration =>
            integration.id === editingIntegration.id ? updatedIntegration : integration
          )
        );

        toast({
          title: "Integration Updated",
          description: "The banking integration has been updated successfully.",
        });
      } else {
        // Create integration
        // In a real implementation, this would call the API
        const newIntegration: BankingIntegration = {
          ...data,
          id: `BI-${Date.now()}`,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Add to the integrations list
        setIntegrations(prev => [newIntegration, ...prev]);

        toast({
          title: "Integration Created",
          description: "The banking integration has been created successfully.",
        });
      }

      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving integration:", error);
      toast({
        title: "Error",
        description: `Failed to save the banking integration: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete integration
  const handleDeleteIntegration = async (id: string) => {
    if (!confirm("Are you sure you want to delete this integration?")) {
      return;
    }

    setIsLoading(true);

    try {
      // In a real implementation, this would call the API

      // Remove from the integrations list
      setIntegrations(prev => prev.filter(integration => integration.id !== id));

      toast({
        title: "Integration Deleted",
        description: "The banking integration has been deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting integration:", error);
      toast({
        title: "Error",
        description: `Failed to delete the banking integration: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle test connection
  const handleTestConnection = async (id: string) => {
    setIsLoading(true);

    try {
      // In a real implementation, this would call the API

      // Update the integration status
      setIntegrations(prev =>
        prev.map(integration =>
          integration.id === id ? { ...integration, status: 'connected' } : integration
        )
      );

      toast({
        title: "Connection Successful",
        description: "The banking integration connection was successful.",
      });
    } catch (error) {
      console.error("Error testing connection:", error);
      toast({
        title: "Connection Failed",
        description: `Failed to connect to the banking API: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sync transactions
  const handleSyncTransactions = async (id: string) => {
    setIsSyncing({ ...isSyncing, [id]: true });

    try {
      // In a real implementation, this would call the API

      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update the integration lastSyncedAt
      setIntegrations(prev =>
        prev.map(integration =>
          integration.id === id ? { ...integration, lastSyncedAt: new Date() } : integration
        )
      );

      toast({
        title: "Sync Successful",
        description: "Bank transactions have been synced successfully.",
      });
    } catch (error) {
      console.error("Error syncing transactions:", error);
      toast({
        title: "Sync Failed",
        description: `Failed to sync bank transactions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsSyncing({ ...isSyncing, [id]: false });
    }
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return "Never";
    return format(new Date(date), "MMM d, yyyy h:mm a");
  };

  // Get status badge
  const getStatusBadge = (status: ConnectionStatus) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-500">Connected</Badge>;
      case 'disconnected':
        return <Badge variant="outline">Disconnected</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'expired':
        return <Badge className="bg-yellow-500">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get provider name
  const getProviderName = (provider: BankingProvider) => {
    switch (provider) {
      case 'standard_bank':
        return "Standard Bank";
      case 'national_bank':
        return "National Bank of Malawi";
      case 'fdh_bank':
        return "FDH Bank";
      case 'first_capital_bank':
        return "First Capital Bank";
      case 'nedbank':
        return "Nedbank";
      case 'ecobank':
        return "Ecobank";
      case 'open_banking':
        return "Open Banking";
      case 'custom':
        return "Custom";
      default:
        return provider;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div>
            <CardTitle>Banking Integrations</CardTitle>
            <CardDescription>
              Connect to banking APIs for automated data feeds
            </CardDescription>
          </div>
          <Button onClick={handleNewIntegration} className="mt-2 sm:mt-0">
            <Plus className="mr-2 h-4 w-4" />
            New Integration
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="integrations" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
            <TabsTrigger value="credentials">Credentials</TabsTrigger>
          </TabsList>

          <TabsContent value="integrations" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : integrations.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <p className="text-muted-foreground mb-4">
                  No banking integrations found. Create your first integration to connect to your bank.
                </p>
                <Button onClick={handleNewIntegration}>
                  <Plus className="mr-2 h-4 w-4" />
                  New Integration
                </Button>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Integration</TableHead>
                      <TableHead>Bank Account</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Synced</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {integrations.map((integration) => (
                      <TableRow key={integration.id}>
                        <TableCell>
                          <div className="font-medium">{integration.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {getProviderName(integration.provider)}
                          </div>
                          {integration.description && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {integration.description}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {bankAccounts.find(account => account.id === integration.bankAccountId)?.name || integration.bankAccountId}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(integration.status)}
                        </TableCell>
                        <TableCell>
                          {formatDate(integration.lastSyncedAt)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTestConnection(integration.id)}
                              disabled={isLoading}
                            >
                              <RefreshCw className="h-4 w-4" />
                              <span className="sr-only">Test</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSyncTransactions(integration.id)}
                              disabled={isSyncing[integration.id]}
                            >
                              {isSyncing[integration.id] ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Download className="h-4 w-4" />
                              )}
                              <span className="sr-only">Sync</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditIntegration(integration)}
                              disabled={isLoading}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteIntegration(integration.id)}
                              disabled={isLoading}
                            >
                              <Trash className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="credentials" className="space-y-4">
            {/* Credentials management UI would go here */}
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <p className="text-muted-foreground mb-4">
                Banking credentials management UI would be implemented here.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{editingIntegration ? "Edit Integration" : "New Integration"}</DialogTitle>
            <DialogDescription>
              {editingIntegration
                ? "Edit the banking integration details."
                : "Create a new banking integration to connect to your bank."}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Integration Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter integration name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="provider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Provider</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select bank provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="standard_bank">Standard Bank</SelectItem>
                          <SelectItem value="national_bank">National Bank of Malawi</SelectItem>
                          <SelectItem value="fdh_bank">FDH Bank</SelectItem>
                          <SelectItem value="first_capital_bank">First Capital Bank</SelectItem>
                          <SelectItem value="nedbank">Nedbank</SelectItem>
                          <SelectItem value="ecobank">Ecobank</SelectItem>
                          <SelectItem value="open_banking">Open Banking</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter integration description"
                        className="resize-none"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="bankAccountId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Account</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select bank account" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {bankAccounts.map(account => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="credentialsId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Credentials</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select credentials" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {credentials.map(cred => (
                            <SelectItem key={cred.id} value={cred.id}>
                              {getProviderName(cred.provider)} - {cred.authMethod}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">API Settings</h3>

                <FormField
                  control={form.control}
                  name="settings.baseUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Base URL</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter API base URL" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="settings.accountEndpoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Endpoint (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter account endpoint" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.transactionEndpoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Transaction Endpoint (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter transaction endpoint" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="settings.balanceEndpoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Balance Endpoint (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter balance endpoint" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.statementEndpoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Statement Endpoint (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter statement endpoint" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="settings.pollingInterval"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Polling Interval (minutes)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter polling interval"
                            {...field}
                            value={field.value?.toString() || "60"}
                            onChange={e => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          How often to sync transactions automatically
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.startDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date (Optional)</FormLabel>
                        <FormControl>
                          <div className="flex">
                            <Input
                              type="date"
                              {...field}
                              value={field.value ? format(field.value, "yyyy-MM-dd") : ""}
                              onChange={e => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={() => field.onChange(new Date())}
                              className="ml-2"
                            >
                              <Calendar className="h-4 w-4" />
                            </Button>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Date from which to start syncing transactions
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)} type="button">
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Integration"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Upload, FileText, AlertCircle as AlertCircleIcon } from "lucide-react";
import { statementImportService, StatementImportResult } from "@/lib/services/statement-import-service";
import { useToast } from "@/components/ui/use-toast";

// Define the form schema
const importFormSchema = z.object({
  bankAccount: z.string({
    required_error: "Please select a bank account",
  }),
  autoDetectFormat: z.boolean().default(true),
  dateFormat: z.string().optional(),
});

type ImportFormValues = z.infer<typeof importFormSchema>;

// Sample bank accounts data
const bankAccounts = [
  { id: "1", name: "TCM Operations Account" },
  { id: "2", name: "TCM Payroll Account" },
  { id: "3", name: "TCM Reserve Account" },
];

interface StatementImporterProps {
  onImportComplete?: (result: StatementImportResult) => void;
}

export function StatementImporter({ onImportComplete }: StatementImporterProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("basic");
  const [columnMapping, setColumnMapping] = useState({
    date: "Date",
    description: "Description",
    reference: "Reference",
    amount: "Amount",
    balance: "Balance",
  });

  const { toast } = useToast();

  // Initialize the form
  const form = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema) as any,
    defaultValues: {
      bankAccount: "",
      autoDetectFormat: true,
      dateFormat: "MM/DD/YYYY",
    },
  });

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ImportFormValues) => {
    if (!file) {
      toast({
        title: "No File Selected",
        description: "Please select a file to import.",
        variant: "destructive",
      });
      return;
    }

    setIsImporting(true);

    try {
      // Convert file to buffer
      const buffer = await file.arrayBuffer();

      // Import statement
      const result = await statementImportService.importStatement(
        Buffer.from(buffer),
        {
          bankAccountId: data.bankAccount,
          dateFormat: data.dateFormat,
          columnMapping: activeTab === "advanced" ? columnMapping : undefined,
          autoDetectFormat: data.autoDetectFormat,
        }
      );

      if (result.success) {
        toast({
          title: "Import Successful",
          description: `Successfully imported ${result.totalItems} transactions.${
            result.duplicates ? ` ${result.duplicates} duplicates were found.` : ""
          }`,
        });

        if (onImportComplete) {
          onImportComplete(result);
        }
      } else {
        toast({
          title: "Import Failed",
          description: result.errors?.[0] || "An unknown error occurred.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      console.error("Error importing statement:", error);
      toast({
        title: "Import Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Handle column mapping change
  const handleColumnMappingChange = (field: keyof typeof columnMapping, value: string) => {
    setColumnMapping(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Import Bank Statement</CardTitle>
        <CardDescription>
          Upload a bank statement file to import transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control as any}
              name="bankAccount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Account</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a bank account" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {bankAccounts.map(account => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the bank account for this statement
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="statement-file">Bank Statement File</Label>
              <Input
                id="statement-file"
                type="file"
                onChange={handleFileChange}
                accept=".csv,.ofx,.qif,.pdf,.xlsx,.xls"
              />
              {file && (
                <p className="text-sm text-muted-foreground">
                  Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                </p>
              )}
            </div>

            <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4">
                <FormField
                  control={form.control as any}
                  name="autoDetectFormat"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Auto-detect file format</FormLabel>
                        <FormDescription>
                          Automatically detect the format of your bank statement file
                        </FormDescription>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="dateFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Format</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select date format" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                          <SelectItem value="YYYY/MM/DD">YYYY/MM/DD</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the date format used in your statement
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Column Mapping</h3>
                  <p className="text-sm text-muted-foreground">
                    Specify which columns in your file correspond to which fields
                  </p>

                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date-column">Date Column</Label>
                        <Input
                          id="date-column"
                          value={columnMapping.date}
                          onChange={(e) => handleColumnMappingChange('date', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description-column">Description Column</Label>
                        <Input
                          id="description-column"
                          value={columnMapping.description}
                          onChange={(e) => handleColumnMappingChange('description', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="reference-column">Reference Column</Label>
                        <Input
                          id="reference-column"
                          value={columnMapping.reference}
                          onChange={(e) => handleColumnMappingChange('reference', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="amount-column">Amount Column</Label>
                        <Input
                          id="amount-column"
                          value={columnMapping.amount}
                          onChange={(e) => handleColumnMappingChange('amount', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="balance-column">Balance Column (Optional)</Label>
                      <Input
                        id="balance-column"
                        value={columnMapping.balance}
                        onChange={(e) => handleColumnMappingChange('balance', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <Button type="submit" disabled={isImporting || !file}>
              {isImporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Import Statement
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-start">
        <Alert>
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Supported File Formats</AlertTitle>
          <AlertDescription>
            We support CSV, OFX, QIF, PDF, and Excel (XLSX) formats. For best results, use the export format provided by your bank.
          </AlertDescription>
        </Alert>
      </CardFooter>
    </Card>
  );
}

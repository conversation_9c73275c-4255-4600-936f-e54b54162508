"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building, CreditCard, ArrowUpDown, RefreshCw, AlertCircle } from "lucide-react";
import { BankAccountManager } from "./bank-account-manager";
import { PaymentProcessor } from "./payment-processor";
import { BankReconciliation } from "./bank-reconciliation";

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

interface BankingDashboardProps {}

export function BankingDashboard({}: BankingDashboardProps) {
  const [activeTab, setActiveTab] = useState("accounts");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Sample data for the dashboard summary
  const dashboardData = {
    totalBalance: ********,
    totalAccounts: 4,
    activeAccounts: 3,
    pendingPayments: 5,
    completedPayments: 12,
    unreconciledItems: 8
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);
    
    // In a real implementation, this would fetch updated data from the API
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Banking & Treasury</h2>
          <p className="text-muted-foreground">
            Manage bank accounts, payments, and reconciliations
          </p>
        </div>
        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {error && (
        <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive flex items-center">
          <AlertCircle className="mr-2 h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(dashboardData.totalBalance)}</div>
            <p className="text-xs text-muted-foreground">
              Across {dashboardData.activeAccounts} active accounts
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.pendingPayments}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardData.completedPayments} payments completed this month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unreconciled Items</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.unreconciledItems}</div>
            <p className="text-xs text-muted-foreground">
              Items requiring reconciliation
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="accounts" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="accounts">Bank Accounts</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
        </TabsList>
        <TabsContent value="accounts" className="space-y-4">
          <BankAccountManager />
        </TabsContent>
        <TabsContent value="payments" className="space-y-4">
          <PaymentProcessor />
        </TabsContent>
        <TabsContent value="reconciliation" className="space-y-4">
          <BankReconciliation />
        </TabsContent>
      </Tabs>
    </div>
  );
}

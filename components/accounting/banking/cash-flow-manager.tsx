"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format, addMonths, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import { 
  Download, 
  FileText, 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar,
  PlusCircle,
  FileDown,
  Filter,
  ArrowUpDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { CashFlowForecast } from './cash-flow-forecast';

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Sample cash flow statements
const sampleCashFlowStatements = [
  {
    id: "CF-2025-Q1",
    name: "Q1 2025 Cash Flow Statement",
    type: "actual",
    period: "quarterly",
    startDate: "2025-01-01",
    endDate: "2025-03-31",
    fiscalYear: "2024-2025",
    status: "published",
    operatingActivities: {
      inflows: 12500000,
      outflows: 8750000,
      netCashFromOperating: 3750000
    },
    investingActivities: {
      inflows: 0,
      outflows: 1200000,
      netCashFromInvesting: -1200000
    },
    financingActivities: {
      inflows: 0,
      outflows: 0,
      netCashFromFinancing: 0
    },
    beginningCashBalance: 5000000,
    netCashFlow: 2550000,
    endingCashBalance: 7550000,
    createdAt: "2025-04-05",
    publishedAt: "2025-04-10"
  },
  {
    id: "CF-2024-Q4",
    name: "Q4 2024 Cash Flow Statement",
    type: "actual",
    period: "quarterly",
    startDate: "2024-10-01",
    endDate: "2024-12-31",
    fiscalYear: "2024-2025",
    status: "published",
    operatingActivities: {
      inflows: 11800000,
      outflows: 9200000,
      netCashFromOperating: 2600000
    },
    investingActivities: {
      inflows: 500000,
      outflows: 2000000,
      netCashFromInvesting: -1500000
    },
    financingActivities: {
      inflows: 0,
      outflows: 0,
      netCashFromFinancing: 0
    },
    beginningCashBalance: 3900000,
    netCashFlow: 1100000,
    endingCashBalance: 5000000,
    createdAt: "2025-01-08",
    publishedAt: "2025-01-15"
  },
  {
    id: "CF-2024-Q3",
    name: "Q3 2024 Cash Flow Statement",
    type: "actual",
    period: "quarterly",
    startDate: "2024-07-01",
    endDate: "2024-09-30",
    fiscalYear: "2024-2025",
    status: "published",
    operatingActivities: {
      inflows: 10500000,
      outflows: 8900000,
      netCashFromOperating: 1600000
    },
    investingActivities: {
      inflows: 0,
      outflows: 800000,
      netCashFromInvesting: -800000
    },
    financingActivities: {
      inflows: 0,
      outflows: 0,
      netCashFromFinancing: 0
    },
    beginningCashBalance: 3100000,
    netCashFlow: 800000,
    endingCashBalance: 3900000,
    createdAt: "2024-10-07",
    publishedAt: "2024-10-12"
  },
  {
    id: "CF-2025-Q2-FORECAST",
    name: "Q2 2025 Cash Flow Forecast",
    type: "forecast",
    period: "quarterly",
    startDate: "2025-04-01",
    endDate: "2025-06-30",
    fiscalYear: "2024-2025",
    status: "draft",
    operatingActivities: {
      inflows: 13200000,
      outflows: 9100000,
      netCashFromOperating: 4100000
    },
    investingActivities: {
      inflows: 0,
      outflows: 1500000,
      netCashFromInvesting: -1500000
    },
    financingActivities: {
      inflows: 0,
      outflows: 0,
      netCashFromFinancing: 0
    },
    beginningCashBalance: 7550000,
    netCashFlow: 2600000,
    endingCashBalance: 10150000,
    createdAt: "2025-03-20",
    publishedAt: null
  },
  {
    id: "CF-2025-ANNUAL-FORECAST",
    name: "2025 Annual Cash Flow Forecast",
    type: "forecast",
    period: "annual",
    startDate: "2025-01-01",
    endDate: "2025-12-31",
    fiscalYear: "2024-2025",
    status: "draft",
    operatingActivities: {
      inflows: 52000000,
      outflows: 38000000,
      netCashFromOperating: 14000000
    },
    investingActivities: {
      inflows: 1000000,
      outflows: 6000000,
      netCashFromInvesting: -5000000
    },
    financingActivities: {
      inflows: 0,
      outflows: 0,
      netCashFromFinancing: 0
    },
    beginningCashBalance: 5000000,
    netCashFlow: 9000000,
    endingCashBalance: 14000000,
    createdAt: "2024-12-15",
    publishedAt: null
  }
];

// Sample cash flow analysis data
const sampleCashFlowAnalysis = {
  operatingRatio: 0.78,
  cashFlowMargin: 0.22,
  cashFlowToDebt: 2.5,
  cashFlowCoverage: 3.2,
  freeCashFlow: 2350000,
  cashConversionCycle: 45,
  cashBurnRate: 2900000,
  cashRunway: 8.5,
  cashFlowTrend: [
    { month: "Jan", value: 1200000 },
    { month: "Feb", value: 1350000 },
    { month: "Mar", value: 1100000 },
    { month: "Apr", value: 1450000 },
    { month: "May", value: 1600000 },
    { month: "Jun", value: 1750000 },
  ],
  seasonalityPattern: "Higher cash inflows during Q2 and Q4 due to certification cycles",
  volatilityScore: 0.15,
  recommendations: [
    "Optimize accounts receivable collection to improve cash conversion cycle",
    "Consider staggering major capital expenditures to maintain healthier cash reserves",
    "Implement cash flow forecasting on a monthly basis to improve accuracy",
    "Review pricing strategy for certification services to improve cash flow margin"
  ]
};

interface CashFlowManagerProps {}

export function CashFlowManager({}: CashFlowManagerProps) {
  const [activeTab, setActiveTab] = useState("statements");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [periodFilter, setPeriodFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [isCreating, setIsCreating] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Filter cash flow statements
  const filteredStatements = sampleCashFlowStatements.filter(statement => {
    // Search query filter
    if (searchQuery && !statement.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !statement.id.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Status filter
    if (statusFilter !== "all" && statement.status !== statusFilter) {
      return false;
    }
    
    // Period filter
    if (periodFilter !== "all" && statement.period !== periodFilter) {
      return false;
    }
    
    // Type filter
    if (typeFilter !== "all" && statement.type !== typeFilter) {
      return false;
    }
    
    return true;
  });

  // Handle create button click
  const handleCreate = () => {
    setIsCreating(true);
    setTimeout(() => {
      setIsCreating(false);
    }, 1500);
  };

  // Handle export button click
  const handleExport = () => {
    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
    }, 1500);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-500">{status}</Badge>;
      case "draft":
        return <Badge variant="outline">{status}</Badge>;
      case "archived":
        return <Badge variant="secondary">{status}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Get type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case "actual":
        return <Badge className="bg-blue-500">{type}</Badge>;
      case "forecast":
        return <Badge className="bg-amber-500">{type}</Badge>;
      case "analysis":
        return <Badge className="bg-purple-500">{type}</Badge>;
      default:
        return <Badge>{type}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Cash Flow Management</h2>
          <p className="text-muted-foreground">
            Create, analyze, and manage cash flow statements and forecasts
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <FileDown className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
          <Button size="sm" onClick={handleCreate} disabled={isCreating}>
            {isCreating ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <PlusCircle className="mr-2 h-4 w-4" />
                New Statement
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="statements" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="statements">Cash Flow Statements</TabsTrigger>
          <TabsTrigger value="forecast">Cash Flow Forecast</TabsTrigger>
          <TabsTrigger value="analysis">Cash Flow Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="statements" className="mt-6 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by name or ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
              <Select value={periodFilter} onValueChange={setPeriodFilter}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Periods</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="actual">Actual</SelectItem>
                  <SelectItem value="forecast">Forecast</SelectItem>
                  <SelectItem value="analysis">Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Date Range</TableHead>
                  <TableHead>Net Cash Flow</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStatements.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                      No cash flow statements found matching your filters.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStatements.map((statement) => (
                    <TableRow key={statement.id}>
                      <TableCell className="font-medium">{statement.id}</TableCell>
                      <TableCell>{statement.name}</TableCell>
                      <TableCell className="capitalize">{statement.period}</TableCell>
                      <TableCell>{getTypeBadge(statement.type)}</TableCell>
                      <TableCell>
                        {format(parseISO(statement.startDate), "MMM d, yyyy")} - {format(parseISO(statement.endDate), "MMM d, yyyy")}
                      </TableCell>
                      <TableCell className={statement.netCashFlow >= 0 ? "text-green-600" : "text-red-600"}>
                        {formatCurrency(statement.netCashFlow)}
                      </TableCell>
                      <TableCell>{getStatusBadge(statement.status)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <FileText className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <FileDown className="mr-2 h-4 w-4" />
                              Export
                            </DropdownMenuItem>
                            {statement.status === "draft" && (
                              <DropdownMenuItem>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Publish
                              </DropdownMenuItem>
                            )}
                            {statement.status === "published" && (
                              <DropdownMenuItem>
                                <Clock className="mr-2 h-4 w-4" />
                                Archive
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {filteredStatements.length > 0 && (
            <div className="text-sm text-muted-foreground">
              Showing {filteredStatements.length} of {sampleCashFlowStatements.length} cash flow statements
            </div>
          )}
        </TabsContent>

        <TabsContent value="forecast" className="mt-6">
          <CashFlowForecast />
        </TabsContent>

        <TabsContent value="analysis" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Cash Flow Metrics</CardTitle>
                <CardDescription>
                  Key performance indicators for cash flow management
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Operating Ratio</p>
                    <p className="text-2xl font-bold">{(sampleCashFlowAnalysis.operatingRatio * 100).toFixed(1)}%</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Flow Margin</p>
                    <p className="text-2xl font-bold">{(sampleCashFlowAnalysis.cashFlowMargin * 100).toFixed(1)}%</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Flow to Debt</p>
                    <p className="text-2xl font-bold">{sampleCashFlowAnalysis.cashFlowToDebt.toFixed(1)}x</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Flow Coverage</p>
                    <p className="text-2xl font-bold">{sampleCashFlowAnalysis.cashFlowCoverage.toFixed(1)}x</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Free Cash Flow</p>
                    <p className="text-2xl font-bold">{formatCurrency(sampleCashFlowAnalysis.freeCashFlow)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Conversion Cycle</p>
                    <p className="text-2xl font-bold">{sampleCashFlowAnalysis.cashConversionCycle} days</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Burn Rate</p>
                    <p className="text-2xl font-bold">{formatCurrency(sampleCashFlowAnalysis.cashBurnRate)}/mo</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cash Runway</p>
                    <p className="text-2xl font-bold">{sampleCashFlowAnalysis.cashRunway.toFixed(1)} months</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cash Flow Insights</CardTitle>
                <CardDescription>
                  Analysis and recommendations for improving cash flow
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Seasonality Pattern</p>
                  <p className="text-sm">{sampleCashFlowAnalysis.seasonalityPattern}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Volatility Score</p>
                  <p className="text-sm">
                    {(sampleCashFlowAnalysis.volatilityScore * 100).toFixed(1)}% 
                    <span className="text-muted-foreground ml-2">
                      (Lower is better, &lt;20% is considered stable)
                    </span>
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Recommendations</p>
                  <ul className="text-sm space-y-1 list-disc pl-5">
                    {sampleCashFlowAnalysis.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Cash Flow Trend Analysis</CardTitle>
                <CardDescription>
                  Monthly net cash flow trend for the past 6 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* This would be a chart in a real implementation */}
                  <div className="flex h-full items-end gap-2">
                    {sampleCashFlowAnalysis.cashFlowTrend.map((item, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div 
                          className="bg-primary w-12 rounded-t-md" 
                          style={{ 
                            height: `${(item.value / 2000000) * 100}%`,
                            minHeight: '20px'
                          }}
                        ></div>
                        <div className="mt-2 text-xs">{item.month}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(item.value).replace('MWK', '')}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client";

import { BankReconciliationModule } from "@/components/accounting/banking/reconciliation";
import { BankAccount } from "@/types/banking";
import { BankTransaction, Reconciliation } from "@/types/reconciliation";
import { useToast } from "@/components/ui/use-toast";

interface ReconciliationPageClientProps {
  bankAccounts: BankAccount[];
  initialTransactions: BankTransaction[];
}

export function ReconciliationPageClient({ bankAccounts, initialTransactions }: ReconciliationPageClientProps) {
  const { toast } = useToast();

  const handleReconciliationComplete = (reconciliation: Reconciliation) => {
    console.log("Reconciliation completed:", reconciliation);
    toast({
      title: "Reconciliation Saved",
      description: "The reconciliation has been saved successfully.",
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Bank Reconciliation</h1>
        <p className="text-muted-foreground">
          Reconcile bank statements with accounting records
        </p>
      </div>

      <BankReconciliationModule
        bankAccounts={bankAccounts}
        initialTransactions={initialTransactions}
        onComplete={handleReconciliationComplete}
      />
    </div>
  );
}

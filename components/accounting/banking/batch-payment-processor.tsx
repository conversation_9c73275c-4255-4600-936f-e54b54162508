"use client";

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Loader2, CheckCircle, XCircle, AlertTriangle, Info, Search, Filter, PlayCircle, Send } from 'lucide-react';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';

interface Payment {
  id: string;
  reference: string;
  date: Date;
  amount: number;
  currency: string;
  payee: string;
  description: string;
  status: string;
  method: string;
  bankAccount: string;
  bankAccountName?: string;
  createdBy: string;
  createdAt: Date;
}

export function BatchPaymentProcessor() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [resultDialogOpen, setResultDialogOpen] = useState(false);
  const [processingResults, setProcessingResults] = useState<{
    processed: number;
    failed: number;
    skipped: number;
    results: Array<{
      id: string;
      success: boolean;
      status: string;
      message?: string;
    }>;
  } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('pending');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch pending payments
  const fetchPayments = async () => {
    try {
      setLoading(true);

      // In a real implementation, this would fetch from the API
      // For now, use mock data
      const mockPayments: Payment[] = Array.from({ length: 15 }, (_, i) => ({
        id: `PAY-2025-${1000 + i}`,
        reference: `INV-2025-${1000 + i}`,
        date: new Date(2025, 0, 1 + i),
        amount: 10000 + (i * 1000),
        currency: 'MWK',
        payee: `Vendor ${i + 1}`,
        description: `Payment for services ${i + 1}`,
        status: 'pending',
        method: i % 2 === 0 ? 'bank_transfer' : 'check',
        bankAccount: `ACCT-${i + 1}`,
        bankAccountName: `Bank Account ${i + 1}`,
        createdBy: 'John Doe',
        createdAt: new Date(2025, 0, 1)
      }));

      setPayments(mockPayments);
      setTotalPages(Math.ceil(mockPayments.length / 10));
    } catch (error: unknown) {
      console.error('Error fetching payments:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Process selected payments
  const processPayments = async () => {
    if (selectedPayments.length === 0) {
      toast({
        title: 'Error',
        description: 'No payments selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      setProcessing(true);

      const response = await fetch('/api/accounting/banking/payments/batch-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentIds: selectedPayments }),
      });

      if (!response.ok) {
        throw new Error('Failed to process payments');
      }

      const data = await response.json();

      if (data.success) {
        setProcessingResults(data.data);
        setResultDialogOpen(true);

        // Update payment statuses in the UI
        const updatedPayments = [...payments];
        data.data.results.forEach((result: unknown) => {
          const paymentIndex = updatedPayments.findIndex(p => p.id === result.id);
          if (paymentIndex !== -1) {
            updatedPayments[paymentIndex].status = result.status;
          }
        });

        setPayments(updatedPayments);
        setSelectedPayments([]);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to process payments',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error processing payments:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
      setConfirmDialogOpen(false);
    }
  };

  // Send notifications for processed payments
  const sendNotifications = async () => {
    if (!processingResults) return;

    const successfulPaymentIds = processingResults.results
      .filter(result => result.success)
      .map(result => result.id);

    if (successfulPaymentIds.length === 0) {
      toast({
        title: 'Error',
        description: 'No successful payments to notify',
        variant: 'destructive',
      });
      return;
    }

    try {
      setProcessing(true);

      const response = await fetch('/api/accounting/banking/payments/send-notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentIds: successfulPaymentIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to send notifications');
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: `Notifications sent for ${successfulPaymentIds.length} payments`,
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to send notifications',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error sending notifications:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  // Handle payment selection
  const togglePaymentSelection = (paymentId: string) => {
    setSelectedPayments(prev => {
      if (prev.includes(paymentId)) {
        return prev.filter(id => id !== paymentId);
      } else {
        return [...prev, paymentId];
      }
    });
  };

  // Handle select all
  const toggleSelectAll = () => {
    if (selectedPayments.length === payments.filter(p => p.status === 'pending').length) {
      setSelectedPayments([]);
    } else {
      setSelectedPayments(payments.filter(p => p.status === 'pending').map(p => p.id));
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPayments();
  };

  // Initial fetch
  useEffect(() => {
    fetchPayments();
  }, []);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Processing</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Batch Payment Processor</CardTitle>
        <CardDescription>
          Process multiple payments at once
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <form onSubmit={handleSearch} className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search payments..."
                className="pl-8 w-[250px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit" variant="outline" size="sm">
              Search
            </Button>
          </form>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              className="border rounded px-2 py-1 text-sm"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="pending">Pending</option>
              <option value="all">All</option>
            </select>
          </div>
        </div>

        <div className="mb-4">
          <Button
            onClick={() => setConfirmDialogOpen(true)}
            disabled={selectedPayments.length === 0 || processing}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <PlayCircle className="mr-2 h-4 w-4" />
            Process Selected Payments ({selectedPayments.length})
          </Button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading payments...</span>
          </div>
        ) : payments.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No payments found</h3>
            <p className="text-sm text-muted-foreground mt-1">
              There are no pending payments to process.
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectedPayments.length === payments.filter(p => p.status === 'pending').length && payments.filter(p => p.status === 'pending').length > 0}
                      onCheckedChange={toggleSelectAll}
                      aria-label="Select all"
                      disabled={payments.filter(p => p.status === 'pending').length === 0}
                    />
                  </TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Payee</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Bank Account</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedPayments.includes(payment.id)}
                        onCheckedChange={() => togglePaymentSelection(payment.id)}
                        aria-label={`Select payment ${payment.reference}`}
                        disabled={payment.status !== 'pending'}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{payment.reference}</TableCell>
                    <TableCell>{format(new Date(payment.date), 'dd MMM yyyy')}</TableCell>
                    <TableCell>{payment.payee}</TableCell>
                    <TableCell>{formatCurrency(payment.amount, payment.currency)}</TableCell>
                    <TableCell>{payment.bankAccountName || payment.bankAccount}</TableCell>
                    <TableCell>{getStatusBadge(payment.status)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {!loading && payments.length > 0 && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                {page > 1 && (
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(prev => Math.max(prev - 1, 1));
                      }}
                    />
                  </PaginationItem>
                )}

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                  <PaginationItem key={p}>
                    <PaginationLink
                      href="#"
                      isActive={p === page}
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(p);
                      }}
                    >
                      {p}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                {page < totalPages && (
                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(prev => Math.min(prev + 1, totalPages));
                      }}
                    />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Payments</DialogTitle>
            <DialogDescription>
              Are you sure you want to process {selectedPayments.length} payment{selectedPayments.length !== 1 ? 's' : ''}?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-4">
              This action will:
            </p>
            <ul className="list-disc pl-5 space-y-2 text-sm">
              <li>Update payment status from pending to processing</li>
              <li>Create bank transactions for each payment</li>
              <li>Update bank account balances</li>
              <li>Mark successful payments as completed</li>
            </ul>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={processing}
            >
              Cancel
            </Button>
            <Button
              onClick={processPayments}
              disabled={processing}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <PlayCircle className="mr-2 h-4 w-4" />
                  Process Payments
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Results Dialog */}
      <Dialog open={resultDialogOpen} onOpenChange={setResultDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Payment Processing Results</DialogTitle>
            <DialogDescription>
              Summary of payment processing results
            </DialogDescription>
          </DialogHeader>

          {processingResults && (
            <div className="py-4">
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <p className="text-green-700 text-2xl font-bold">{processingResults.processed}</p>
                  <p className="text-green-600 text-sm">Processed</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg text-center">
                  <p className="text-red-700 text-2xl font-bold">{processingResults.failed}</p>
                  <p className="text-red-600 text-sm">Failed</p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg text-center">
                  <p className="text-gray-700 text-2xl font-bold">{processingResults.skipped}</p>
                  <p className="text-gray-600 text-sm">Skipped</p>
                </div>
              </div>

              <div className="rounded-md border max-h-[300px] overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Payment ID</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Message</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {processingResults.results.map((result) => (
                      <TableRow key={result.id}>
                        <TableCell className="font-medium">{result.id}</TableCell>
                        <TableCell>{getStatusBadge(result.status)}</TableCell>
                        <TableCell>{result.message || (result.success ? 'Successfully processed' : 'Failed to process')}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setResultDialogOpen(false);
                setProcessingResults(null);
              }}
            >
              Close
            </Button>
            <Button
              onClick={sendNotifications}
              disabled={processing || !processingResults || processingResults.processed === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Notifications
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

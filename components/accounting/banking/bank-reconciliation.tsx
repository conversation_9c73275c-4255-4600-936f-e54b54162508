"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from 'date-fns';
import { CalendarIcon, Check, Download, FileText, Upload, RefreshCw, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { StatementImporter } from './statement-importer';
import { TransactionMatcher } from './transaction-matcher';
import { ReconciliationApproval } from './reconciliation-approval';

// Import formatCurrency from utils
import { formatCurrency } from '@/lib/utils/format';

// Sample bank accounts
const bankAccounts = [
  { id: "1", name: "TCM Main Operating Account", bank: "National Bank of Malawi", accountNumber: "**********" },
  { id: "2", name: "TCM Payroll Account", bank: "Standard Bank Malawi", accountNumber: "**********" },
  { id: "3", name: "TCM Reserve Fund", bank: "FDH Bank", accountNumber: "**********" },
  { id: "4", name: "TCM USD Account", bank: "National Bank of Malawi", accountNumber: "**********" },
];

// Sample transaction data
const sampleTransactions = [
  {
    id: "1",
    date: new Date("2025-02-15"),
    description: "Salary Payment - February 2025",
    reference: "PAY-2025-02",
    amount: -2500000,
    category: "Payroll",
    reconciled: true,
    bankStatement: true,
  },
  {
    id: "2",
    date: new Date("2025-02-18"),
    description: "Office Supplies - Stationery",
    reference: "EXP-2025-023",
    amount: -75000,
    category: "Office Expenses",
    reconciled: true,
    bankStatement: true,
  },
  {
    id: "3",
    date: new Date("2025-02-20"),
    description: "Certification Fee Income",
    reference: "INC-2025-045",
    amount: 350000,
    category: "Revenue",
    reconciled: true,
    bankStatement: true,
  },
  {
    id: "4",
    date: new Date("2025-02-22"),
    description: "Utility Bill - Electricity",
    reference: "EXP-2025-024",
    amount: -120000,
    category: "Utilities",
    reconciled: false,
    bankStatement: true,
  },
  {
    id: "5",
    date: new Date("2025-02-25"),
    description: "Membership Fee Income",
    reference: "INC-2025-046",
    amount: 450000,
    category: "Revenue",
    reconciled: false,
    bankStatement: true,
  },
  {
    id: "6",
    date: new Date("2025-02-26"),
    description: "Travel Expenses - Regional Meeting",
    reference: "EXP-2025-025",
    amount: -85000,
    category: "Travel",
    reconciled: false,
    bankStatement: false,
  },
  {
    id: "7",
    date: new Date("2025-02-28"),
    description: "Bank Charges",
    reference: "FEE-2025-002",
    amount: -5000,
    category: "Bank Fees",
    reconciled: false,
    bankStatement: true,
  },
  {
    id: "8",
    date: new Date("2025-03-01"),
    description: "Government Subvention",
    reference: "INC-2025-047",
    amount: 1000000,
    category: "Revenue",
    reconciled: false,
    bankStatement: false,
  },
];

// Sample bank statement data
const sampleBankStatement = [
  {
    id: "BS-1",
    date: new Date("2025-02-15"),
    description: "SALARY PAYMENT FEB 2025",
    reference: "NBM-123456",
    amount: -2500000,
    matched: true,
    matchedId: "1",
  },
  {
    id: "BS-2",
    date: new Date("2025-02-18"),
    description: "PAYMENT TO OFFICE SUPPLIES LTD",
    reference: "NBM-123457",
    amount: -75000,
    matched: true,
    matchedId: "2",
  },
  {
    id: "BS-3",
    date: new Date("2025-02-20"),
    description: "DEPOSIT - CERTIFICATION FEES",
    reference: "NBM-123458",
    amount: 350000,
    matched: true,
    matchedId: "3",
  },
  {
    id: "BS-4",
    date: new Date("2025-02-22"),
    description: "PAYMENT TO ELECTRICITY COMPANY",
    reference: "NBM-123459",
    amount: -120000,
    matched: false,
    matchedId: null,
  },
  {
    id: "BS-5",
    date: new Date("2025-02-25"),
    description: "DEPOSIT - MEMBERSHIP FEES",
    reference: "NBM-123460",
    amount: 450000,
    matched: false,
    matchedId: null,
  },
  {
    id: "BS-6",
    date: new Date("2025-02-28"),
    description: "BANK CHARGES",
    reference: "NBM-123461",
    amount: -5000,
    matched: false,
    matchedId: null,
  },
  {
    id: "BS-7",
    date: new Date("2025-03-02"),
    description: "INTEREST EARNED",
    reference: "NBM-123462",
    amount: 12500,
    matched: false,
    matchedId: null,
  },
];

interface BankReconciliationProps {
  transactions?: typeof sampleTransactions;
  bankStatement?: typeof sampleBankStatement;
}

export function BankReconciliation({
  transactions = sampleTransactions,
  bankStatement = sampleBankStatement,
}: BankReconciliationProps) {
  const [selectedAccount, setSelectedAccount] = useState(bankAccounts[0].id);
  const [selectedPeriod, setSelectedPeriod] = useState("february-2025");
  const [isReconciling, setIsReconciling] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [reconciledTransactions, setReconciledTransactions] = useState(
    transactions.map(t => ({ ...t }))
  );
  const [reconciledBankStatement, setReconciledBankStatement] = useState(
    bankStatement.map(t => ({ ...t }))
  );
  const [activeTab, setActiveTab] = useState("reconciliation");
  const { toast } = useToast();

  // Calculate reconciliation statistics
  const totalTransactions = transactions.length;
  const reconciledCount = transactions.filter(t => t.reconciled).length;
  const reconciledPercentage = Math.round((reconciledCount / totalTransactions) * 100);

  const totalBankStatementItems = bankStatement.length;
  const matchedBankStatementItems = bankStatement.filter(t => t.matched).length;
  const matchedPercentage = Math.round((matchedBankStatementItems / totalBankStatementItems) * 100);

  // Calculate balances
  const bookBalance = transactions.reduce((sum, t) => sum + t.amount, 0);
  const bankBalance = bankStatement.reduce((sum, t) => sum + t.amount, 0);
  const difference = bookBalance - bankBalance;

  // Handle reconcile button click
  const handleReconcile = () => {
    setIsReconciling(true);
    setTimeout(() => {
      setIsReconciling(false);
    }, 1500);
  };

  // Handle upload button click
  const handleUpload = () => {
    setIsUploading(true);
    setTimeout(() => {
      setIsUploading(false);
    }, 1500);
  };

  // Handle checkbox change for transactions
  const handleTransactionCheck = (id: string, checked: boolean) => {
    setReconciledTransactions(prev =>
      prev.map(t => t.id === id ? { ...t, reconciled: checked } : t)
    );
  };

  // Handle checkbox change for bank statement
  const handleBankStatementCheck = (id: string, checked: boolean) => {
    setReconciledBankStatement(prev =>
      prev.map(t => t.id === id ? { ...t, matched: checked } : t)
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Bank Reconciliation</CardTitle>
            <CardDescription>
              Reconcile bank statements with accounting records
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedAccount} onValueChange={setSelectedAccount}>
              <SelectTrigger className="w-[250px]">
                <SelectValue placeholder="Select bank account" />
              </SelectTrigger>
              <SelectContent>
                {bankAccounts.map(account => (
                  <SelectItem key={account.id} value={account.id}>
                    {account.name} - {account.bank}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="january-2025">January 2025</SelectItem>
                <SelectItem value="february-2025">February 2025</SelectItem>
                <SelectItem value="march-2025">March 2025</SelectItem>
                <SelectItem value="custom">Custom Period</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="reconciliation" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
            <TabsTrigger value="import">Import Statement</TabsTrigger>
            <TabsTrigger value="match">Match Transactions</TabsTrigger>
            <TabsTrigger value="approve">Approval</TabsTrigger>
            <TabsTrigger value="summary">Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="reconciliation" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Book Balance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(bookBalance)}</div>
                  <p className="text-xs text-muted-foreground">
                    As of {format(new Date(), "MMMM d, yyyy")}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Bank Balance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(bankBalance)}</div>
                  <p className="text-xs text-muted-foreground">
                    As of {format(new Date(), "MMMM d, yyyy")}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Difference</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${Math.abs(difference) > 0.01 ? 'text-red-500' : 'text-green-500'}`}>
                    {formatCurrency(difference)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {Math.abs(difference) > 0.01 ? 'Needs reconciliation' : 'Balanced'}
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <h3 className="text-lg font-medium mb-2">Book Transactions</h3>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">Match</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {reconciledTransactions.map(transaction => (
                        <TableRow key={transaction.id} className={transaction.reconciled ? 'bg-green-50' : ''}>
                          <TableCell>
                            <Checkbox
                              checked={transaction.reconciled}
                              onCheckedChange={(checked) =>
                                handleTransactionCheck(transaction.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>{format(transaction.date, "MMM d, yyyy")}</TableCell>
                          <TableCell>{transaction.description}</TableCell>
                          <TableCell>{transaction.reference}</TableCell>
                          <TableCell className={`text-right ${transaction.amount < 0 ? 'text-red-500' : 'text-green-500'}`}>
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium mb-2">Bank Statement</h3>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">Match</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {reconciledBankStatement.map(item => (
                        <TableRow key={item.id} className={item.matched ? 'bg-green-50' : ''}>
                          <TableCell>
                            <Checkbox
                              checked={item.matched}
                              onCheckedChange={(checked) =>
                                handleBankStatementCheck(item.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>{format(item.date, "MMM d, yyyy")}</TableCell>
                          <TableCell>{item.description}</TableCell>
                          <TableCell>{item.reference}</TableCell>
                          <TableCell className={`text-right ${item.amount < 0 ? 'text-red-500' : 'text-green-500'}`}>
                            {formatCurrency(item.amount)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Save Draft
              </Button>
              <Button onClick={handleReconcile} disabled={isReconciling}>
                {isReconciling ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Reconciling...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Complete Reconciliation
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <StatementImporter
              onImportComplete={(result) => {
                // Handle import completion
                console.log("Import completed:", result);
                toast({
                  title: "Import Successful",
                  description: `Successfully imported ${result.totalItems} transactions.`,
                });
              }}
            />
          </TabsContent>

          <TabsContent value="match" className="space-y-4">
            <TransactionMatcher
              bankAccountId={selectedAccount}
              statementItems={reconciledBankStatement.map(item => ({
                id: item.id,
                date: item.date,
                description: item.description,
                reference: item.reference,
                amount: item.amount,
                imported: true,
                matched: item.matched,
                matchedTransactionId: item.matchedId,
                processed: false
              }))}
              transactions={reconciledTransactions.map(tx => ({
                id: tx.id,
                date: tx.date,
                description: tx.description,
                reference: tx.reference,
                amount: tx.amount,
                reconciled: tx.reconciled
              }))}
              onMatchComplete={(matches) => {
                // Handle match completion
                console.log("Matching completed:", matches);
                toast({
                  title: "Matching Completed",
                  description: `Successfully matched ${matches.filter(m => m.transaction).length} transactions.`,
                });

                // Update reconciled transactions and bank statement
                const updatedTransactions = [...reconciledTransactions];
                const updatedBankStatement = [...reconciledBankStatement];

                matches.forEach(match => {
                  if (match.transaction) {
                    // Update transaction
                    const txIndex = updatedTransactions.findIndex(tx => tx.id === match.transaction?.id);
                    if (txIndex !== -1) {
                      updatedTransactions[txIndex].reconciled = true;
                    }

                    // Update bank statement item
                    const itemIndex = updatedBankStatement.findIndex(item => item.id === match.statementItem.id);
                    if (itemIndex !== -1) {
                      updatedBankStatement[itemIndex].matched = true;
                      updatedBankStatement[itemIndex].matchedId = match.transaction.id;
                    }
                  }
                });

                setReconciledTransactions(updatedTransactions);
                setReconciledBankStatement(updatedBankStatement);

                // Move to the reconciliation tab
                setActiveTab("reconciliation");
              }}
            />
          </TabsContent>

          <TabsContent value="approve" className="space-y-4">
            <ReconciliationApproval
              reconciliation={{
                id: "REC-2025-02",
                bankAccountId: selectedAccount,
                bankAccountName: bankAccounts.find(a => a.id === selectedAccount)?.name || "",
                statementDate: new Date(),
                startDate: new Date(2025, 1, 1), // Feb 1, 2025
                endDate: new Date(2025, 1, 28), // Feb 28, 2025
                startingBalance: 5000000,
                endingBalance: bookBalance,
                bankStatementBalance: bankBalance,
                adjustedBankBalance: bankBalance,
                bookBalance: bookBalance,
                adjustedBookBalance: bookBalance,
                difference: difference,
                status: 'completed',
                transactions: reconciledTransactions.filter(t => t.reconciled),
                unreconciled: reconciledTransactions.filter(t => !t.reconciled),
                adjustments: [],
                notes: "February 2025 reconciliation for TCM Main Operating Account",
                completedBy: "John Doe",
                completedAt: new Date(),
                createdBy: "John Doe",
                createdAt: new Date(2025, 2, 5), // Mar 5, 2025
              }}
              onApprove={async (id, notes) => {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1500));
                toast({
                  title: "Reconciliation Approved",
                  description: "The reconciliation has been approved successfully.",
                });
              }}
              onReject={async (id, notes) => {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1500));
                toast({
                  title: "Reconciliation Rejected",
                  description: "The reconciliation has been rejected and returned for revision.",
                });
              }}
            />
          </TabsContent>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Reconciliation Progress</CardTitle>
                  <CardDescription>
                    Current status of the reconciliation process
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Book Transactions</span>
                      <span className="text-sm text-muted-foreground">{reconciledCount} of {totalTransactions} reconciled</span>
                    </div>
                    <Progress value={reconciledPercentage} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Bank Statement Items</span>
                      <span className="text-sm text-muted-foreground">{matchedBankStatementItems} of {totalBankStatementItems} matched</span>
                    </div>
                    <Progress value={matchedPercentage} className="h-2" />
                  </div>
                  <div className="pt-4">
                    <h4 className="text-sm font-medium mb-2">Reconciliation Summary</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Book Balance:</span>
                        <span className="text-sm font-medium">{formatCurrency(bookBalance)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Bank Balance:</span>
                        <span className="text-sm font-medium">{formatCurrency(bankBalance)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span className="text-sm font-medium">Difference:</span>
                        <span className={`text-sm font-medium ${Math.abs(difference) > 0.01 ? 'text-red-500' : 'text-green-500'}`}>
                          {formatCurrency(difference)}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Reconciliation History</CardTitle>
                  <CardDescription>
                    Previous reconciliations for this account
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Period</TableHead>
                          <TableHead>Completed Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>January 2025</TableCell>
                          <TableCell>Feb 5, 2025</TableCell>
                          <TableCell>
                            <Badge>Completed</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">View</Button>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>December 2024</TableCell>
                          <TableCell>Jan 8, 2025</TableCell>
                          <TableCell>
                            <Badge className="bg-green-500">Approved</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">View</Button>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>November 2024</TableCell>
                          <TableCell>Dec 7, 2024</TableCell>
                          <TableCell>
                            <Badge className="bg-green-500">Approved</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">View</Button>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export Report
              </Button>
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Print Summary
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

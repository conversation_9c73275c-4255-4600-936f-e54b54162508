"use client";

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Loader2, CheckCircle, XCircle, AlertCircle, Info, Search, Filter, PlayCircle, Send, FileText } from 'lucide-react';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';

interface BankStatement {
  _id: string;
  bankAccountId: {
    _id: string;
    name: string;
    accountNumber: string;
  };
  importDate: string;
  startDate: string;
  endDate: string;
  startingBalance: number;
  endingBalance: number;
  items: Array<{
    _id: string;
    date: string;
    description: string;
    reference: string;
    amount: number;
    balance?: number;
    imported: boolean;
    matched: boolean;
    matchedTransactionId?: string;
    category?: string;
    notes?: string;
    checkNumber?: string;
    payee?: string;
    processed: boolean;
    processingNotes?: string;
  }>;
  status: 'imported' | 'processing' | 'processed' | 'reconciled' | 'approved';
  processingStatus?: 'pending' | 'in_progress' | 'completed' | 'failed';
  processingErrors?: string[];
  originalFilename?: string;
  fileFormat?: string;
  createdBy: {
    _id: string;
    name: string;
  };
  createdAt: string;
}

interface StatementProcessorProps {
  bankAccountId: string;
  onProcessComplete?: (result: unknown) => void;
}

export function StatementProcessor({
  bankAccountId,
  onProcessComplete,
}: StatementProcessorProps) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [statements, setStatements] = useState<BankStatement[]>([]);
  const [selectedStatement, setSelectedStatement] = useState<BankStatement | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [processingOptions, setProcessingOptions] = useState({
    createTransactions: true,
    updateBalances: true,
    categorizeTransactions: true,
    matchExistingTransactions: true,
  });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('imported');

  const { toast } = useToast();

  // Fetch statements
  const fetchStatements = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/accounting/banking/statements?accountId=${bankAccountId}&page=${page}&limit=10&sortField=importDate&sortOrder=desc`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch statements');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setStatements(data.data.docs);
        setTotalPages(data.data.totalPages);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to fetch statements',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error fetching statements:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Process statement
  const processStatement = async () => {
    if (!selectedStatement) return;
    
    try {
      setProcessing(true);
      
      const response = await fetch(`/api/accounting/banking/statements/${selectedStatement._id}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processingOptions),
      });
      
      if (!response.ok) {
        throw new Error('Failed to process statement');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Statement processed successfully',
        });
        
        // Refresh statements
        fetchStatements();
        
        if (onProcessComplete) {
          onProcessComplete(data);
        }
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to process statement',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error processing statement:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
      setConfirmDialogOpen(false);
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchStatements();
  };

  // Initial fetch
  useEffect(() => {
    fetchStatements();
  }, [bankAccountId, page]);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'imported':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Imported</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Processing</Badge>;
      case 'processed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Processed</Badge>;
      case 'reconciled':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Reconciled</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">Approved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Statement Processor</CardTitle>
        <CardDescription>
          Process imported bank statements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <form onSubmit={handleSearch} className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search statements..."
                className="pl-8 w-[250px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit" variant="outline" size="sm">
              Search
            </Button>
          </form>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              className="border rounded px-2 py-1 text-sm"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All</option>
              <option value="imported">Imported</option>
              <option value="processing">Processing</option>
              <option value="processed">Processed</option>
              <option value="reconciled">Reconciled</option>
              <option value="approved">Approved</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading statements...</span>
          </div>
        ) : statements.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No statements found</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Import bank statements to process them.
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Import Date</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Balance</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {statements.map((statement) => (
                  <TableRow key={statement._id}>
                    <TableCell>{format(new Date(statement.importDate), 'dd MMM yyyy')}</TableCell>
                    <TableCell>
                      {format(new Date(statement.startDate), 'dd MMM yyyy')} - {format(new Date(statement.endDate), 'dd MMM yyyy')}
                    </TableCell>
                    <TableCell>{statement.items.length}</TableCell>
                    <TableCell>{formatCurrency(statement.endingBalance, 'MWK')}</TableCell>
                    <TableCell>{getStatusBadge(statement.status)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => {
                            setSelectedStatement(statement);
                            setConfirmDialogOpen(true);
                          }}
                          disabled={statement.status !== 'imported'}
                        >
                          <PlayCircle className="h-4 w-4 mr-1" />
                          Process
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => {
                            // View statement details
                          }}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {!loading && statements.length > 0 && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                {page > 1 && (
                  <PaginationItem>
                    <PaginationPrevious 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page - 1);
                      }} 
                    />
                  </PaginationItem>
                )}
                
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                  <PaginationItem key={p}>
                    <PaginationLink 
                      href="#" 
                      isActive={p === page}
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(p);
                      }}
                    >
                      {p}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                
                {page < totalPages && (
                  <PaginationItem>
                    <PaginationNext 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page + 1);
                      }} 
                    />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>

      {/* Processing Options Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Statement</DialogTitle>
            <DialogDescription>
              Configure processing options for this statement
            </DialogDescription>
          </DialogHeader>
          
          {selectedStatement && (
            <div className="space-y-4 py-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Import Date</Label>
                  <p className="font-medium">{format(new Date(selectedStatement.importDate), 'dd MMM yyyy')}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Period</Label>
                  <p className="font-medium">
                    {format(new Date(selectedStatement.startDate), 'dd MMM yyyy')} - {format(new Date(selectedStatement.endDate), 'dd MMM yyyy')}
                  </p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Items</Label>
                  <p className="font-medium">{selectedStatement.items.length}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Ending Balance</Label>
                  <p className="font-medium">{formatCurrency(selectedStatement.endingBalance, 'MWK')}</p>
                </div>
              </div>
              
              <div className="space-y-4 border-t pt-4">
                <h4 className="font-medium">Processing Options</h4>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="createTransactions"
                    checked={processingOptions.createTransactions}
                    onCheckedChange={(checked) => 
                      setProcessingOptions(prev => ({ ...prev, createTransactions: !!checked }))
                    }
                  />
                  <label
                    htmlFor="createTransactions"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Create Transactions
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="matchExistingTransactions"
                    checked={processingOptions.matchExistingTransactions}
                    onCheckedChange={(checked) => 
                      setProcessingOptions(prev => ({ ...prev, matchExistingTransactions: !!checked }))
                    }
                  />
                  <label
                    htmlFor="matchExistingTransactions"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Match Existing Transactions
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="categorizeTransactions"
                    checked={processingOptions.categorizeTransactions}
                    onCheckedChange={(checked) => 
                      setProcessingOptions(prev => ({ ...prev, categorizeTransactions: !!checked }))
                    }
                  />
                  <label
                    htmlFor="categorizeTransactions"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Categorize Transactions
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="updateBalances"
                    checked={processingOptions.updateBalances}
                    onCheckedChange={(checked) => 
                      setProcessingOptions(prev => ({ ...prev, updateBalances: !!checked }))
                    }
                  />
                  <label
                    htmlFor="updateBalances"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Update Account Balance
                  </label>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={processing}
            >
              Cancel
            </Button>
            <Button
              onClick={processStatement}
              disabled={processing}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <PlayCircle className="mr-2 h-4 w-4" />
                  Process Statement
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

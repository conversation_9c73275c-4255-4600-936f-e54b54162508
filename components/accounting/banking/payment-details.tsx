"use client";

import { useState } from "react";
import { format } from "date-fns";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON>eader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  FileDown, 
  Printer, 
  Edit, 
  Copy, 
  Trash2 
} from "lucide-react";
import { PaymentStatus, PaymentMethod } from "@/models/accounting/Payment";

interface PaymentDetailsProps {
  payment: {
    id: string;
    reference: string;
    date: Date;
    amount: number;
    currency: string;
    payee: string;
    description: string;
    status: PaymentStatus;
    method: PaymentMethod;
    bankAccount: string;
    bankAccountName?: string;
    checkNumber?: string;
    transactionId?: string;
    receiptNumber?: string;
    notes?: string;
    createdBy: string;
    createdAt: Date;
    updatedBy?: string;
    updatedAt?: Date;
  };
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onPrint: () => void;
  onExport: () => void;
}

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Get status badge variant
const getStatusBadge = (status: PaymentStatus) => {
  switch (status) {
    case 'pending':
      return { variant: "default" as const, icon: <Clock className="mr-1 h-3 w-3" /> };
    case 'processing':
      return { variant: "default" as const, icon: <RefreshCw className="mr-1 h-3 w-3" /> };
    case 'completed':
      return { variant: "default" as const, icon: <CheckCircle className="mr-1 h-3 w-3" /> };
    case 'failed':
      return { variant: 'destructive' as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    case 'cancelled':
      return { variant: "default" as const, icon: <XCircle className="mr-1 h-3 w-3" /> };
    default:
      return { variant: "default" as const, icon: null };
  }
};

// Get method display name
const getMethodDisplayName = (method: PaymentMethod) => {
  switch (method) {
    case 'bank_transfer':
      return 'Bank Transfer';
    case 'check':
      return 'Check';
    case 'cash':
      return 'Cash';
    case 'mobile_money':
      return 'Mobile Money';
    case 'other':
      return 'Other';
    default:
      return method;
  }
};

export function PaymentDetails({ 
  payment, 
  onEdit, 
  onDuplicate, 
  onDelete, 
  onPrint, 
  onExport 
}: PaymentDetailsProps) {
  const statusBadge = getStatusBadge(payment.status);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle className="text-xl">Payment Details</CardTitle>
            <CardDescription>
              Payment ID: {payment.id}
            </CardDescription>
          </div>
          <Badge variant={statusBadge.variant} className="w-fit">
            {statusBadge.icon}
            <span className="capitalize">{payment.status}</span>
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Reference</h3>
            <p className="mt-1 text-base">{payment.reference}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="mt-1 text-base">{format(payment.date, "MMMM d, yyyy")}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Payee</h3>
            <p className="mt-1 text-base">{payment.payee}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
            <p className="mt-1 text-base font-medium">{formatCurrency(payment.amount, payment.currency)}</p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
          <p className="mt-1 text-base">{payment.description}</p>
        </div>

        <Separator />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Payment Method</h3>
            <p className="mt-1 text-base">{getMethodDisplayName(payment.method)}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Bank Account</h3>
            <p className="mt-1 text-base">{payment.bankAccountName || payment.bankAccount}</p>
          </div>
        </div>

        {payment.method === 'check' && payment.checkNumber && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Check Number</h3>
            <p className="mt-1 text-base">{payment.checkNumber}</p>
          </div>
        )}

        {(payment.method === 'bank_transfer' || payment.method === 'mobile_money') && payment.transactionId && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Transaction ID</h3>
            <p className="mt-1 text-base">{payment.transactionId}</p>
          </div>
        )}

        {payment.receiptNumber && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Receipt Number</h3>
            <p className="mt-1 text-base">{payment.receiptNumber}</p>
          </div>
        )}

        {payment.notes && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="mt-1 text-base">{payment.notes}</p>
          </div>
        )}

        <Separator />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Created By</h3>
            <p className="mt-1 text-base">{payment.createdBy}</p>
            <p className="text-xs text-muted-foreground">{format(payment.createdAt, "MMM d, yyyy 'at' h:mm a")}</p>
          </div>
          {payment.updatedBy && payment.updatedAt && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Last Updated By</h3>
              <p className="mt-1 text-base">{payment.updatedBy}</p>
              <p className="text-xs text-muted-foreground">{format(payment.updatedAt, "MMM d, yyyy 'at' h:mm a")}</p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex flex-wrap justify-between gap-2">
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="gap-1" onClick={onExport}>
            <FileDown className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onPrint}>
            <Printer className="h-4 w-4" />
            <span>Print</span>
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="gap-1" onClick={onEdit}>
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <Button variant="outline" size="sm" className="gap-1" onClick={onDuplicate}>
            <Copy className="h-4 w-4" />
            <span>Duplicate</span>
          </Button>
          <Button variant="destructive" size="sm" className="gap-1" onClick={onDelete}>
            <Trash2 className="h-4 w-4" />
            <span>Delete</span>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

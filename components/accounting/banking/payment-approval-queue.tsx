"use client";

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Loader2, <PERSON><PERSON>ircle, XCircle, Alert<PERSON>riangle, Info, Search, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';

interface Payment {
  _id: string;
  reference: string;
  date: string;
  amount: number;
  currency: string;
  payee: string;
  description: string;
  status: string;
  method: string;
  bankAccount: {
    _id: string;
    name: string;
    accountNumber: string;
  };
  approvalStatus: string;
  createdBy: {
    _id: string;
    name: string;
  };
  createdAt: string;
}

interface PaginatedResult {
  docs: Payment[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage: number  | undefined;
  nextPage: number  | undefined;
}

export function PaymentApprovalQueue() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [pagination, setPagination] = useState({
    totalDocs: 0,
    limit: 10,
    totalPages: 0,
    page: 1,
    hasPrevPage: false,
    hasNextPage: false,
    prevPage: null,
    nextPage: null
  });
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [processingAction, setProcessingAction] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('pending');

  // Fetch payments requiring approval
  const fetchPayments = async (page = 1) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/accounting/banking/payments/approval-queue?page=${page}&limit=10`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setPayments(data.data.docs);
        setPagination({
          totalDocs: data.data.totalDocs,
          limit: data.data.limit,
          totalPages: data.data.totalPages,
          page: data.data.page,
          hasPrevPage: data.data.hasPrevPage,
          hasNextPage: data.data.hasNextPage,
          prevPage: data.data.prevPage,
          nextPage: data.data.nextPage
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to fetch payments',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error fetching payments:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Approve payment
  const approvePayment = async () => {
    if (!selectedPayment) return;
    
    try {
      setProcessingAction(true);
      const response = await fetch(`/api/accounting/banking/payments/${selectedPayment._id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: approvalNotes }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to approve payment');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Payment approved successfully',
        });
        
        // Refresh payments
        fetchPayments(pagination.page);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to approve payment',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error approving payment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setProcessingAction(false);
      setApprovalDialogOpen(false);
      setApprovalNotes('');
      setSelectedPayment(null);
    }
  };

  // Reject payment
  const rejectPayment = async () => {
    if (!selectedPayment) return;
    
    if (!rejectionReason.trim()) {
      toast({
        title: 'Error',
        description: 'Rejection reason is required',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      setProcessingAction(true);
      const response = await fetch(`/api/accounting/banking/payments/${selectedPayment._id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to reject payment');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Payment rejected successfully',
        });
        
        // Refresh payments
        fetchPayments(pagination.page);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to reject payment',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error rejecting payment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setProcessingAction(false);
      setRejectionDialogOpen(false);
      setRejectionReason('');
      setSelectedPayment(null);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchPayments(page);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPayments(1);
  };

  // Initial fetch
  useEffect(() => {
    fetchPayments();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Payment Approval Queue</CardTitle>
        <CardDescription>
          Review and approve pending payments
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <form onSubmit={handleSearch} className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search payments..."
                className="pl-8 w-[250px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit" variant="outline" size="sm">
              Search
            </Button>
          </form>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              className="border rounded px-2 py-1 text-sm"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="pending">Pending</option>
              <option value="all">All</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading payments...</span>
          </div>
        ) : payments.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No payments requiring approval</h3>
            <p className="text-sm text-muted-foreground mt-1">
              All payments have been processed or there are no pending payments.
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Reference</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Payee</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Bank Account</TableHead>
                  <TableHead>Requested By</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment._id}>
                    <TableCell className="font-medium">{payment.reference}</TableCell>
                    <TableCell>{format(new Date(payment.date), 'dd MMM yyyy')}</TableCell>
                    <TableCell>{payment.payee}</TableCell>
                    <TableCell>{formatCurrency(payment.amount, payment.currency)}</TableCell>
                    <TableCell>{payment.bankAccount?.name || 'N/A'}</TableCell>
                    <TableCell>{payment.createdBy?.name || 'Unknown'}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 bg-green-50 hover:bg-green-100 text-green-600 border-green-200"
                          onClick={() => {
                            setSelectedPayment(payment);
                            setApprovalDialogOpen(true);
                          }}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
                          onClick={() => {
                            setSelectedPayment(payment);
                            setRejectionDialogOpen(true);
                          }}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {!loading && payments.length > 0 && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                {pagination.hasPrevPage && (
                  <PaginationItem>
                    <PaginationPrevious 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.prevPage) handlePageChange(pagination.prevPage);
                      }} 
                    />
                  </PaginationItem>
                )}
                
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink 
                      href="#" 
                      isActive={page === pagination.page}
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page);
                      }}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                
                {pagination.hasNextPage && (
                  <PaginationItem>
                    <PaginationNext 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.nextPage) handlePageChange(pagination.nextPage);
                      }} 
                    />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Payment</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve this payment?
            </DialogDescription>
          </DialogHeader>
          
          {selectedPayment && (
            <div className="space-y-4 py-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Reference</Label>
                  <p className="font-medium">{selectedPayment.reference}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Date</Label>
                  <p className="font-medium">{format(new Date(selectedPayment.date), 'dd MMM yyyy')}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Payee</Label>
                  <p className="font-medium">{selectedPayment.payee}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Amount</Label>
                  <p className="font-medium">{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</p>
                </div>
                <div className="col-span-2">
                  <Label className="text-xs text-muted-foreground">Description</Label>
                  <p className="font-medium">{selectedPayment.description}</p>
                </div>
              </div>
              
              <div>
                <Label htmlFor="approvalNotes">Approval Notes (Optional)</Label>
                <Textarea
                  id="approvalNotes"
                  placeholder="Add any notes about this approval"
                  value={approvalNotes}
                  onChange={(e) => setApprovalNotes(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setApprovalDialogOpen(false);
                setApprovalNotes('');
                setSelectedPayment(null);
              }}
              disabled={processingAction}
            >
              Cancel
            </Button>
            <Button
              onClick={approvePayment}
              disabled={processingAction}
              className="bg-green-600 hover:bg-green-700"
            >
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve Payment
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Payment</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this payment.
            </DialogDescription>
          </DialogHeader>
          
          {selectedPayment && (
            <div className="space-y-4 py-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Reference</Label>
                  <p className="font-medium">{selectedPayment.reference}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Date</Label>
                  <p className="font-medium">{format(new Date(selectedPayment.date), 'dd MMM yyyy')}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Payee</Label>
                  <p className="font-medium">{selectedPayment.payee}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Amount</Label>
                  <p className="font-medium">{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</p>
                </div>
              </div>
              
              <div>
                <Label htmlFor="rejectionReason">Rejection Reason <span className="text-red-500">*</span></Label>
                <Textarea
                  id="rejectionReason"
                  placeholder="Provide a reason for rejecting this payment"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="mt-1"
                  required
                />
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setRejectionDialogOpen(false);
                setRejectionReason('');
                setSelectedPayment(null);
              }}
              disabled={processingAction}
            >
              Cancel
            </Button>
            <Button
              onClick={rejectPayment}
              disabled={processingAction || !rejectionReason.trim()}
              variant="destructive"
            >
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rejecting...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject Payment
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/components/ui/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Download, FileDown, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { EnhancedDatePicker } from '@/components/ui/enhanced-date-picker';

// Define the form schema
const exportFormSchema = z.object({
  format: z.enum(['csv', 'excel', 'json'], {
    required_error: 'Please select an export format',
  }),
  accountId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  dateFormat: z.string().optional(),
  includeAll: z.boolean().default(true),
  includeFields: z.array(z.string()).optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc'),
});

// Define the form values type
type ExportFormValues = z.infer<typeof exportFormSchema>;

// Define the component props
interface TransactionExporterProps {
  bankAccounts: Array<{
    id: string;
    name: string;
    accountNumber: string;
  }>;
  onExportComplete?: (result: unknown) => void;
}

export function TransactionExporter({
  bankAccounts,
  onExportComplete,
}: TransactionExporterProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportHistory, setExportHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  const { toast } = useToast();

  // Initialize the form
  const form = useForm<ExportFormValues>({
    resolver: zodResolver(exportFormSchema) as any,
    defaultValues: {
      format: 'excel',
      dateFormat: 'yyyy-MM-dd',
      includeAll: true,
      sortDirection: 'desc',
    },
  });

  // Load export history
  const loadExportHistory = async () => {
    try {
      setLoadingHistory(true);
      const response = await fetch('/api/accounting/banking/transactions/exports');
      
      if (!response.ok) {
        throw new Error('Failed to load export history');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setExportHistory(data.data.docs);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to load export history',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error loading export history:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoadingHistory(false);
    }
  };

  // Load export history on mount
  useEffect(() => {
    loadExportHistory();
  }, []);

  // Handle form submission
  const onSubmit = async (data: ExportFormValues) => {
    try {
      setIsExporting(true);
      
      // Prepare request body
      const requestBody = {
        format: data.format,
        accountId: data.accountId,
        startDate: data.startDate,
        endDate: data.endDate,
        dateFormat: data.dateFormat,
        includeFields: !data.includeAll ? data.includeFields : undefined,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
      };
      
      // Send export request
      const response = await fetch('/api/accounting/banking/transactions/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      
      if (!response.ok) {
        throw new Error('Failed to export transactions');
      }
      
      const result = await response.json();
      
      if (result.success) {
        toast({
          title: 'Export Successful',
          description: `Successfully exported ${result.totalTransactions} transactions.`,
        });
        
        // Reload export history
        loadExportHistory();
        
        if (onExportComplete) {
          onExportComplete(result);
        }
        
        // Open download link in new tab
        if (result.fileUrl) {
          window.open(result.fileUrl, '_blank');
        }
      } else {
        toast({
          title: 'Export Failed',
          description: result.error || 'An unknown error occurred',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      console.error('Error exporting transactions:', error);
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Export Transactions</CardTitle>
          <CardDescription>
            Export bank transactions to various formats
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control as any}
                  name="format"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Export Format</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isExporting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="csv">CSV</SelectItem>
                          <SelectItem value="excel">Excel</SelectItem>
                          <SelectItem value="json">JSON</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the format for the exported data
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="accountId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Account</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isExporting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="All accounts" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">All accounts</SelectItem>
                          {bankAccounts.map((account) => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name} ({account.accountNumber})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select a specific account or export from all accounts
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <EnhancedDatePicker
                        date={field.value}
                        setDate={field.onChange}
                        disabled={isExporting}
                      />
                      <FormDescription>
                        Export transactions from this date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <EnhancedDatePicker
                        date={field.value}
                        setDate={field.onChange}
                        disabled={isExporting}
                      />
                      <FormDescription>
                        Export transactions until this date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="dateFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Format</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="yyyy-MM-dd"
                          disabled={isExporting}
                        />
                      </FormControl>
                      <FormDescription>
                        Format for dates in the exported file
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="sortDirection"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sort Direction</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isExporting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select sort direction" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="asc">Ascending</SelectItem>
                          <SelectItem value="desc">Descending</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Sort order for the exported data
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control as any}
                  name="includeAll"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isExporting}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Include All Fields</FormLabel>
                        <FormDescription>
                          Export all available transaction fields
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <Button type="submit" disabled={isExporting}>
                {isExporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Export Transactions
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Export History</CardTitle>
          <CardDescription>
            Recent transaction exports
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingHistory ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading export history...</span>
            </div>
          ) : exportHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No export history found
            </div>
          ) : (
            <div className="space-y-4">
              {exportHistory.map((export_) => (
                <div
                  key={export_._id}
                  className="flex items-center justify-between border-b pb-4"
                >
                  <div>
                    <h4 className="font-medium">{export_.name}</h4>
                    <div className="text-sm text-muted-foreground">
                      {export_.format.toUpperCase()} • {export_.totalTransactions} transactions
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(export_.createdAt), 'PPP p')}
                    </div>
                  </div>
                  <div>
                    {export_.status === 'completed' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(export_.fileUrl, '_blank')}
                      >
                        <FileDown className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    ) : (
                      <div className="text-sm text-muted-foreground">
                        {export_.status}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

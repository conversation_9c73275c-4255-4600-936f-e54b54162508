"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, CheckCircle2, XCircle, AlertCircle, FileText, User, Calendar } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { formatCurrency } from '@/lib/utils/format';

interface ReconciliationApprovalProps {
  reconciliation: {
    id: string;
    bankAccountId: string;
    bankAccountName: string;
    statementDate: Date;
    startDate: Date;
    endDate: Date;
    startingBalance: number;
    endingBalance: number;
    bankStatementBalance: number;
    adjustedBankBalance: number;
    bookBalance: number;
    adjustedBookBalance: number;
    difference: number;
    status: 'draft' | 'in_progress' | 'completed' | 'approved';
    transactions: unknown[];
    unreconciled: unknown[];
    adjustments: {
      description: string;
      amount: number;
      type: 'addition' | 'subtraction';
      category: string;
      notes?: string;
    }[];
    notes?: string;
    completedBy?: string;
    completedAt?: Date;
    approvedBy?: string;
    approvedAt?: Date;
    createdBy: string;
    createdAt: Date;
  };
  onApprove?: (id: string, notes: string) => Promise<void>;
  onReject?: (id: string, notes: string) => Promise<void>;
}

export function ReconciliationApproval({
  reconciliation,
  onApprove,
  onReject,
}: ReconciliationApprovalProps) {
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState("");
  const [rejectionNotes, setRejectionNotes] = useState("");
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);

  const { toast } = useToast();

  // Handle approve
  const handleApprove = async () => {
    if (!onApprove) return;

    setIsApproving(true);

    try {
      await onApprove(reconciliation.id, approvalNotes);

      toast({
        title: "Reconciliation Approved",
        description: "The reconciliation has been approved successfully.",
      });

      setApprovalDialogOpen(false);
    } catch (error: unknown) {
      console.error("Error approving reconciliation:", error);
      toast({
        title: "Approval Failed",
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsApproving(false);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!onReject) return;

    setIsRejecting(true);

    try {
      await onReject(reconciliation.id, rejectionNotes);

      toast({
        title: "Reconciliation Rejected",
        description: "The reconciliation has been rejected and returned for revision.",
      });

      setRejectionDialogOpen(false);
    } catch (error: unknown) {
      console.error("Error rejecting reconciliation:", error);
      toast({
        title: "Rejection Failed",
        description: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsRejecting(false);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Format amount
  const formatAmount = (amount: number) => {
    return formatCurrency(amount, 'MWK');
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'completed':
        return <Badge className="bg-blue-500">Completed</Badge>;
      case 'approved':
        return <Badge className="bg-green-500">Approved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div>
            <CardTitle>Reconciliation Approval</CardTitle>
            <CardDescription>
              Review and approve bank reconciliation
            </CardDescription>
          </div>
          <div className="flex items-center mt-2 sm:mt-0">
            {getStatusBadge(reconciliation.status)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Bank Account</div>
            <div>{reconciliation.bankAccountName}</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium">Statement Date</div>
            <div>{formatDate(reconciliation.statementDate)}</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium">Period</div>
            <div>{formatDate(reconciliation.startDate)} to {formatDate(reconciliation.endDate)}</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium">Completed By</div>
            <div>{reconciliation.completedBy || "Not completed"}</div>
          </div>
        </div>

        <div className="border rounded-md p-4 bg-muted/50">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium">Book Balance</div>
              <div className="text-lg">{formatAmount(reconciliation.bookBalance)}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Bank Statement Balance</div>
              <div className="text-lg">{formatAmount(reconciliation.bankStatementBalance)}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Adjusted Book Balance</div>
              <div className="text-lg">{formatAmount(reconciliation.adjustedBookBalance)}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Adjusted Bank Balance</div>
              <div className="text-lg">{formatAmount(reconciliation.adjustedBankBalance)}</div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <div className="text-sm font-medium">Difference</div>
            <div className={`text-lg font-bold ${reconciliation.difference === 0 ? 'text-green-500' : 'text-red-500'}`}>
              {formatAmount(reconciliation.difference)}
            </div>
          </div>
        </div>

        <Tabs defaultValue="adjustments">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="adjustments">Adjustments</TabsTrigger>
            <TabsTrigger value="transactions">Reconciled Items</TabsTrigger>
            <TabsTrigger value="unreconciled">Unreconciled Items</TabsTrigger>
          </TabsList>

          <TabsContent value="adjustments" className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reconciliation.adjustments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4">
                      No adjustments found
                    </TableCell>
                  </TableRow>
                ) : (
                  reconciliation.adjustments.map((adjustment, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="font-medium">{adjustment.description}</div>
                        {adjustment.notes && (
                          <div className="text-sm text-muted-foreground">{adjustment.notes}</div>
                        )}
                      </TableCell>
                      <TableCell>{adjustment.category.replace('_', ' ')}</TableCell>
                      <TableCell className="text-right">
                        <span className={adjustment.type === 'addition' ? 'text-green-500' : 'text-red-500'}>
                          {adjustment.type === 'addition' ? '+' : '-'}{formatAmount(adjustment.amount)}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="transactions" className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reconciliation.transactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      No reconciled transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  reconciliation.transactions.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell>{formatDate(new Date(transaction.date))}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>{transaction.reference}</TableCell>
                      <TableCell className="text-right">
                        <span className={transaction.amount >= 0 ? 'text-green-500' : 'text-red-500'}>
                          {formatAmount(transaction.amount)}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="unreconciled" className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reconciliation.unreconciled.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      No unreconciled transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  reconciliation.unreconciled.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell>{formatDate(new Date(transaction.date))}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>{transaction.reference}</TableCell>
                      <TableCell className="text-right">
                        <span className={transaction.amount >= 0 ? 'text-green-500' : 'text-red-500'}>
                          {formatAmount(transaction.amount)}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>

        {reconciliation.notes && (
          <div className="border rounded-md p-4">
            <div className="text-sm font-medium mb-2">Notes</div>
            <div className="text-sm">{reconciliation.notes}</div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Created by {reconciliation.createdBy} on {formatDate(reconciliation.createdAt)}
        </div>

        {reconciliation.status === 'completed' && (
          <div className="flex gap-2">
            <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Reject Reconciliation</DialogTitle>
                  <DialogDescription>
                    Please provide a reason for rejecting this reconciliation. This will be sent back to the preparer for revision.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="rejection-notes" className="text-sm font-medium">
                      Rejection Notes
                    </label>
                    <Textarea
                      id="rejection-notes"
                      placeholder="Enter reason for rejection..."
                      value={rejectionNotes}
                      onChange={(e) => setRejectionNotes(e.target.value)}
                      rows={4}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setRejectionDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleReject}
                    disabled={isRejecting || !rejectionNotes.trim()}
                  >
                    {isRejecting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Rejecting...
                      </>
                    ) : (
                      <>
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject Reconciliation
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="default" size="sm">
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Approve Reconciliation</DialogTitle>
                  <DialogDescription>
                    Please review the reconciliation details before approving. You can add optional notes.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="approval-notes" className="text-sm font-medium">
                      Approval Notes (Optional)
                    </label>
                    <Textarea
                      id="approval-notes"
                      placeholder="Enter any notes..."
                      value={approvalNotes}
                      onChange={(e) => setApprovalNotes(e.target.value)}
                      rows={4}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="default"
                    onClick={handleApprove}
                    disabled={isApproving}
                  >
                    {isApproving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Approving...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 className="mr-2 h-4 w-4" />
                        Approve Reconciliation
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

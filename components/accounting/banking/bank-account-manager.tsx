// components\accounting\banking\bank-account-manager.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { z } from "zod";
import {
  ColumnDef,
  ColumnFiltersState,
  Row,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ArrowUpDown,
  ChevronDown,
  Download,
  Filter,
  MoreHorizontal,
  Plus,
  Search,
  Trash2,
  Edit,
  Eye,
  RefreshCw,
  CreditCard,
  Building,
  Loader2,
  AlertCircle,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { BankAccountForm } from './bank-account-form';
import { BankAccountDetails } from './bank-account-details';
import { bankAccountService, BankAccount as BankAccountType } from '@/lib/services/bank-account-service';

// Define the bank account data type for the table
export type BankAccount = BankAccountType;

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Define the form schema with Zod
const bankAccountFormSchema = z.object({
  accountNumber: z.string().min(2, {
    message: "Account number must be at least 2 characters",
  }),
  accountName: z.string().min(2, {
    message: "Account name must be at least 2 characters",
  }),
  bankName: z.string().min(2, {
    message: "Bank name must be at least 2 characters",
  }),
  branchName: z.string().min(2, {
    message: "Branch name must be at least 2 characters",
  }),
  accountType: z.enum(['checking', 'savings', 'current', 'fixed_deposit', 'other'], {
    required_error: "Please select an account type",
  }),
  currency: z.string().min(3, {
    message: "Currency code must be at least 3 characters",
  }),
  description: z.string().optional(),
  status: z.enum(['active', 'inactive', 'closed']).default('active'),
  branchCode: z.string().optional(),
  swiftCode: z.string().optional(),
  openingBalance: z.number().optional(),
  minimumBalance: z.number().optional(),
  openedDate: z.union([z.date(), z.string()]).optional(),
  contactPerson: z.string().optional(),
  contactEmail: z.string().optional(),
  contactPhone: z.string().optional(),
  notes: z.string().optional(),
});

// Define the base columns for the bank accounts table (without the actions column)
export const baseColumns: ColumnDef<BankAccount>[] = [
  {
    accessorKey: "accountNumber",
    header: "Account Number",
    cell: ({ row }) => <div className="font-medium">{row.getValue("accountNumber")}</div>,
  },
  {
    accessorKey: "accountName",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Account Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => <div>{row.getValue("accountName")}</div>,
  },
  {
    accessorKey: "bankName",
    header: "Bank",
    cell: ({ row }) => <div>{row.getValue("bankName")}</div>,
  },
  {
    accessorKey: "accountType",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("accountType") as string;
      return <div className="capitalize">{type.replace('_', ' ')}</div>;
    },
  },
  {
    accessorKey: "currentBalance",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="justify-end"
        >
          Balance
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("currentBalance"));
      const currency = row.original.currency;
      return (
        <div className="text-right font-medium">
          {formatCurrency(amount, currency)}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const badgeVariant = status === 'active' ? "default" : // Changed from "default" to "default"
        status === 'inactive' ? "secondary" :
        "destructive";

      return (
        <Badge variant={badgeVariant}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "lastReconciliationDate",
    header: "Last Reconciliation",
    cell: ({ row }) => {
      const date = row.getValue("lastReconciliationDate") as Date  | undefined;
      return <div>{date ? format(date, "PPP") : "—"}</div>;
    },
  },
];

// Define the form values type based on the schema
type BankAccountFormValues = z.infer<typeof bankAccountFormSchema>;

interface BankAccountManagerProps {
  data?: BankAccount[];
}

export function BankAccountManager({ data }: BankAccountManagerProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Dialog states
  const [isAddAccountOpen, setIsAddAccountOpen] = useState(false);
  const [isEditAccountOpen, setIsEditAccountOpen] = useState(false);
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<BankAccount | null>(null);

  // Fetch bank accounts on component mount
  useEffect(() => {
    fetchBankAccounts();
  }, []);

  // Fetch bank accounts from API
  const fetchBankAccounts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const fetchedAccounts = await bankAccountService.getBankAccounts();
      setAccounts(fetchedAccounts);
    } catch (err: unknown) {
      console.error('Error fetching bank accounts:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load bank accounts';
      setError(`${errorMessage}. Using sample data instead.`);
      setAccounts(bankAccountService.getSampleBankAccounts());
    } finally {
      setIsLoading(false);
    }
  };

  // Create the complete columns array with the actions column
  const columns = [
    ...baseColumns,
    {
      id: "actions",
      cell: ({ row }: { row: Row<BankAccount> }) => {
        const account = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(account.id)}
              >
                Copy ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={() => handleViewDetails(account)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleViewTransactions(account)}>
                <Eye className="mr-2 h-4 w-4" />
                View Transactions
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleEdit(account)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Account
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleRefreshBalance(account)}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Balance
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleViewStatement(account)}>
                <CreditCard className="mr-2 h-4 w-4" />
                View Statement
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onSelect={() => handleDelete(account)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Account
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: accounts,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Handle creating a new account
  const handleCreateSubmit = async (values: BankAccountFormValues) => {
    setIsSubmitting(true);

    try {
      // Convert string values to numbers where needed
      const processedValues = {
        ...values,
        openingBalance: values.openingBalance || 0,
        minimumBalance: values.minimumBalance || 0,
        currentBalance: values.openingBalance || 0, // Initialize current balance with opening balance
      };

      const newAccount = await bankAccountService.createBankAccount(processedValues as any);
      setAccounts([...accounts, newAccount]);
      setIsAddAccountOpen(false);
      toast({
        title: "Bank account created",
        description: `Bank account "${values.accountName}" has been created.`,
      });
    } catch (err: unknown) {
      console.error('Error creating bank account:', err);
      const errorMessage = err instanceof Error ? err instanceof Error ? err instanceof Error ? err.message : 'An error occurred' : 'An error occurred' : 'Unknown error';
      toast({
        title: "Error",
        description: `Failed to create bank account: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing an account
  const handleEdit = (account: BankAccount) => {
    setSelectedAccount(account);
    setIsEditAccountOpen(true);
  };

  // Handle viewing account details
  const handleViewDetails = (account: BankAccount) => {
    setSelectedAccount(account);
    setIsViewDetailsOpen(true);
  };

  // Handle viewing transactions
  const handleViewTransactions = (account: BankAccount) => {
    toast({
      title: "Feature coming soon",
      description: "Transaction history will be available in a future update.",
    });
  };

  // Handle refreshing balance
  const handleRefreshBalance = (account: BankAccount) => {
    toast({
      title: "Refreshing balance",
      description: `Refreshing balance for account "${account.accountName}"...`,
    });

    // In a real implementation, this would call an API to refresh the balance
    setTimeout(() => {
      toast({
        title: "Balance refreshed",
        description: `Balance for account "${account.accountName}" has been refreshed.`,
      });
    }, 1500);
  };

  // Handle viewing statement
  const handleViewStatement = (account: BankAccount) => {
    toast({
      title: "Feature coming soon",
      description: "Bank statements will be available in a future update.",
    });
  };

  // Handle deleting an account
  const handleDelete = (account: BankAccount) => {
    setSelectedAccount(account);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming delete
  const confirmDelete = async () => {
    if (selectedAccount) {
      setIsSubmitting(true);

      try {
        await bankAccountService.deleteBankAccount(selectedAccount.id);
        setAccounts(accounts.filter(a => a.id !== selectedAccount.id));
        setIsDeleteDialogOpen(false);
        toast({
          title: "Bank account deleted",
          description: `Bank account "${selectedAccount.accountName}" has been deleted.`,
        });
      } catch (err: unknown) {
        console.error('Error deleting bank account:', err);
        const errorMessage = err instanceof Error ? err instanceof Error ? err instanceof Error ? err.message : 'An error occurred' : 'An error occurred' : 'Unknown error';
        toast({
          title: "Error",
          description: `Failed to delete bank account: ${errorMessage}`,
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
        setSelectedAccount(null);
      }
    }
  };

  // Handle editing account submission
  const handleEditSubmit = async (values: BankAccountFormValues) => {
    if (selectedAccount) {
      setIsSubmitting(true);

      try {
        // Process values to ensure correct types
        const processedValues = {
          id: selectedAccount.id,
          ...values,
          openingBalance: values.openingBalance || 0,
          minimumBalance: values.minimumBalance || 0,
          // Preserve the current balance from the selected account
          currentBalance: selectedAccount.currentBalance,
        };

        const updatedAccount = await bankAccountService.updateBankAccount(processedValues as any);

        setAccounts(accounts.map(account =>
          account.id === selectedAccount.id ? updatedAccount : account
        ));

        setIsEditAccountOpen(false);
        toast({
          title: "Bank account updated",
          description: `Bank account "${values.accountName}" has been updated.`,
        });
      } catch (err: unknown) {
        console.error('Error updating bank account:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        toast({
          title: "Error",
          description: `Failed to update bank account: ${errorMessage}`,
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
        setSelectedAccount(null);
      }
    }
  };

  // Calculate total balance
  const totalBalance = accounts.reduce((sum, account) => {
    if (account.currency === 'MWK' && account.status === 'active') {
      return sum + account.currentBalance;
    }
    return sum;
  }, 0);

  // Count active accounts
  const activeAccounts = accounts.filter(account => account.status === 'active').length;

  return (
    <div className="space-y-6">
      {error && (
        <div className="mb-4 rounded-md bg-destructive/15 p-3 text-sm text-destructive flex items-center">
          <AlertCircle className="mr-2 h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance (MWK)</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalBalance)}</div>
            <p className="text-xs text-muted-foreground">
              Across {activeAccounts} active accounts
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">USD Balance</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                accounts.find(account => account.currency === 'USD' && account.status === 'active')?.currentBalance || 0,
                'USD'
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Foreign currency account
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fixed Deposits</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                accounts.filter(account => account.accountType === 'fixed_deposit' && account.status === 'active')
                  .reduce((sum, account) => sum + account.currentBalance, 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Total fixed deposit value
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle>Bank Accounts</CardTitle>
              <CardDescription>
                Manage bank accounts for the Teachers Council of Malawi
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Dialog open={isAddAccountOpen} onOpenChange={setIsAddAccountOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Account
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[650px]">
                  <DialogHeader>
                    <DialogTitle>Add New Bank Account</DialogTitle>
                    <DialogDescription>
                      Create a new bank account for the Teachers Council of Malawi.
                    </DialogDescription>
                  </DialogHeader>
                  <BankAccountForm
                    onSubmit={handleCreateSubmit}
                    isSubmitting={isSubmitting}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search accounts..."
                    className="pl-8 w-[300px]"
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      table.getColumn("accountName")?.setFilterValue(e.target.value);
                    }}
                  />
                </div>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filter</span>
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9 gap-1">
                      <ChevronDown className="h-4 w-4" />
                      <span>Columns</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <Download className="h-4 w-4" />
                  <span className="hidden sm:inline">Export</span>
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              {isLoading ? (
                <div className="flex h-40 items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => {
                          return (
                            <TableHead key={header.id}>
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && "selected"}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          No bank accounts found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">
                Showing {table.getFilteredRowModel().rows.length} of {accounts.length} accounts
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Account Dialog */}
      <Dialog open={isEditAccountOpen} onOpenChange={setIsEditAccountOpen}>
        <DialogContent className="sm:max-w-[650px]">
          <DialogHeader>
            <DialogTitle>Edit Bank Account</DialogTitle>
            <DialogDescription>
              Update the bank account details.
            </DialogDescription>
          </DialogHeader>
          {selectedAccount && (
            <BankAccountForm
              defaultValues={{
                accountNumber: selectedAccount.accountNumber,
                accountName: selectedAccount.accountName,
                bankName: selectedAccount.bankName,
                branchName: selectedAccount.branchName,
                branchCode: selectedAccount.branchCode,
                swiftCode: selectedAccount.swiftCode,
                accountType: selectedAccount.accountType,
                currency: selectedAccount.currency,
                openingBalance: selectedAccount.openingBalance,
                minimumBalance: selectedAccount.minimumBalance,
                status: selectedAccount.status,
                openedDate: selectedAccount.openedDate,
                description: selectedAccount.description,
                contactPerson: selectedAccount.contactPerson,
                contactEmail: selectedAccount.contactEmail,
                contactPhone: selectedAccount.contactPhone,
                notes: selectedAccount.notes,
              }}
              onSubmit={handleEditSubmit}
              isSubmitting={isSubmitting}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Account Details Dialog */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[650px]">
          {selectedAccount && (
            <BankAccountDetails
              account={selectedAccount}
              onEdit={() => {
                setIsViewDetailsOpen(false);
                setTimeout(() => handleEdit(selectedAccount), 100);
              }}
              onDelete={() => {
                setIsViewDetailsOpen(false);
                setTimeout(() => handleDelete(selectedAccount), 100);
              }}
              onPrint={() => {
                toast({
                  title: "Feature coming soon",
                  description: "Printing will be available in a future update.",
                });
              }}
              onExport={() => {
                toast({
                  title: "Feature coming soon",
                  description: "Exporting will be available in a future update.",
                });
              }}
              onRefreshBalance={() => handleRefreshBalance(selectedAccount)}
              onViewTransactions={() => {
                setIsViewDetailsOpen(false);
                setTimeout(() => handleViewTransactions(selectedAccount), 100);
              }}
              onViewStatement={() => {
                setIsViewDetailsOpen(false);
                setTimeout(() => handleViewStatement(selectedAccount), 100);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the bank account
              {selectedAccount && ` "${selectedAccount.accountName}"`} and remove it from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { format } from "date-fns"
import { CalendarIcon, Check, ChevronDown, Download, FileText, Loader2, Search, X } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { toast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Define the reconciliation form schema with Zod
const reconciliationFormSchema = z.object({
  bankAccount: z.string({
    required_error: "Please select a bank account",
  }),
  statementDate: z.date({
    required_error: "Statement date is required",
  }),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
  startingBalance: z.coerce.number({
    required_error: "Starting balance is required",
  }),
  endingBalance: z.coerce.number({
    required_error: "Ending balance is required",
  }),
  bankStatementBalance: z.coerce.number({
    required_error: "Bank statement balance is required",
  }),
  notes: z.string().optional(),
});

type ReconciliationFormValues = z.infer<typeof reconciliationFormSchema>;

// Format currency
const formatCurrency = (value: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Sample bank accounts data
const bankAccounts = [
  {
    id: "1",
    name: "TCM Main Operating Account",
    accountNumber: "**********",
    bank: "National Bank of Malawi",
    currency: "MWK",
    balance: ********,
  },
  {
    id: "2",
    name: "TCM Payroll Account",
    accountNumber: "**********",
    bank: "Standard Bank Malawi",
    currency: "MWK",
    balance: 5000000,
  },
  {
    id: "3",
    name: "TCM Reserve Fund",
    accountNumber: "**********",
    bank: "FDH Bank",
    currency: "MWK",
    balance: ********,
  },
  {
    id: "4",
    name: "TCM Foreign Currency Account",
    accountNumber: "USD12345678",
    bank: "National Bank of Malawi",
    currency: "USD",
    balance: 50000,
  },
];

// Sample transactions data
const transactions = [
  {
    id: "1",
    date: new Date("2025-07-05"),
    description: "Government Subvention Q1 2025-2026",
    reference: "GS-Q1-2025",
    amount: 5000000,
    type: "deposit",
    status: "cleared",
    isReconciled: false,
  },
  {
    id: "2",
    date: new Date("2025-07-10"),
    description: "Transfer to Payroll Account",
    reference: "TRF-PAY-2025-07",
    amount: -1500000,
    type: "withdrawal",
    status: "cleared",
    isReconciled: false,
  },
  {
    id: "3",
    date: new Date("2025-07-15"),
    description: "Office Supplies Payment",
    reference: "CHK-10001",
    amount: -250000,
    type: "withdrawal",
    status: "cleared",
    isReconciled: false,
  },
  {
    id: "4",
    date: new Date("2025-07-20"),
    description: "Teacher Registration Fees",
    reference: "REG-BATCH-2025-07",
    amount: 2000000,
    type: "deposit",
    status: "cleared",
    isReconciled: false,
  },
  {
    id: "5",
    date: new Date("2025-07-25"),
    description: "Bank Service Charges",
    reference: "BSC-2025-07",
    amount: -5000,
    type: "fee",
    status: "cleared",
    isReconciled: false,
  },
  {
    id: "6",
    date: new Date("2025-08-05"),
    description: "Utility Payments",
    reference: "CHK-10002",
    amount: -350000,
    type: "withdrawal",
    status: "pending",
    isReconciled: false,
  },
  {
    id: "7",
    date: new Date("2025-08-10"),
    description: "Transfer to Payroll Account",
    reference: "TRF-PAY-2025-08",
    amount: -1500000,
    type: "withdrawal",
    status: "pending",
    isReconciled: false,
  },
];

// Sample reconciliation adjustments
const adjustments = [
  {
    id: "1",
    description: "Outstanding Check #10002",
    amount: -350000,
    type: "subtraction",
    category: "outstanding_check",
  },
  {
    id: "2",
    description: "Deposit in Transit (Teacher Registration Fees)",
    amount: 750000,
    type: "addition",
    category: "deposit_in_transit",
  },
  {
    id: "3",
    description: "Bank Service Fee (not recorded)",
    amount: -2500,
    type: "subtraction",
    category: "fee",
  },
];

export function ReconciliationTool() {
  const [isReconciling, setIsReconciling] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTransactions, setFilteredTransactions] = useState(transactions);
  const [adjustmentsList, setAdjustmentsList] = useState(adjustments);
  const [isAddAdjustmentOpen, setIsAddAdjustmentOpen] = useState(false);
  const [newAdjustment, setNewAdjustment] = useState({
    description: '',
    amount: 0,
    type: 'addition',
    category: 'deposit_in_transit',
  });

  // Initialize the form
  const form = useForm<ReconciliationFormValues>({
    resolver: zodResolver(reconciliationFormSchema) as any,
    defaultValues: {
      bankAccount: "1",
      statementDate: new Date(),
      startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // First day of current month
      endDate: new Date(), // Today
      startingBalance: ********,
      endingBalance: ********,
      bankStatementBalance: ********,
      notes: "",
    },
  });

  // Watch form values for calculations
  const watchBankAccount = form.watch("bankAccount");
  const watchEndingBalance = form.watch("endingBalance");
  const watchBankStatementBalance = form.watch("bankStatementBalance");

  // Calculate adjusted balances and difference
  const calculateAdjustedBalances = () => {
    // Sum of selected transactions
    const transactionsTotal = selectedTransactions.reduce((sum, id) => {
      const transaction = transactions.find(t => t.id === id);
      return sum + (transaction ? transaction.amount : 0);
    }, 0);

    // Sum of adjustments
    const adjustmentsTotal = adjustmentsList.reduce((sum, adj) => {
      return sum + (adj.type === 'addition' ? adj.amount : -adj.amount);
    }, 0);

    const adjustedBookBalance = watchEndingBalance + transactionsTotal;
    const adjustedBankBalance = watchBankStatementBalance + adjustmentsTotal;
    const difference = adjustedBookBalance - adjustedBankBalance;

    return {
      adjustedBookBalance,
      adjustedBankBalance,
      difference,
    };
  };

  const { adjustedBookBalance, adjustedBankBalance, difference } = calculateAdjustedBalances();

  // Handle form submission
  async function onSubmit(data: ReconciliationFormValues) {
    setIsReconciling(true);
    
    try {
      // In a real implementation, this would call the API to save the reconciliation
      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Reconciliation Completed",
        description: `Bank account reconciliation for ${bankAccounts.find(a => a.id === data.bankAccount)?.name} has been completed.`,
      });
      
      // Reset the form
      form.reset(data);
      setSelectedTransactions([]);
    } catch (error) {
      console.error("Error completing reconciliation:", error);
      toast({
        title: "Error Completing Reconciliation",
        description: "There was an error completing the reconciliation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsReconciling(false);
    }
  }

  // Handle transaction selection
  const toggleTransaction = (id: string) => {
    setSelectedTransactions(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilteredTransactions(
      transactions.filter(transaction => 
        transaction.description.toLowerCase().includes(query.toLowerCase()) ||
        transaction.reference.toLowerCase().includes(query.toLowerCase())
      )
    );
  };

  // Handle adding a new adjustment
  const handleAddAdjustment = () => {
    setAdjustmentsList(prev => [
      ...prev, 
      { 
        id: `${prev.length + 1}`,
        ...newAdjustment
      }
    ]);
    setNewAdjustment({
      description: '',
      amount: 0,
      type: 'addition',
      category: 'deposit_in_transit',
    });
    setIsAddAdjustmentOpen(false);
  };

  // Handle removing an adjustment
  const handleRemoveAdjustment = (id: string) => {
    setAdjustmentsList(prev => prev.filter(adj => adj.id !== id));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Bank Reconciliation</CardTitle>
          <CardDescription>
            Reconcile bank statements with accounting records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control as any}
                  name="bankAccount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Account</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select bank account" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {bankAccounts.map(account => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name} ({account.accountNumber})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="statementDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Statement Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="startingBalance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Starting Balance</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormDescription>
                        {formatCurrency(field.value)}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="endingBalance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ending Balance (Book)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormDescription>
                        {formatCurrency(field.value)}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control as any}
                  name="bankStatementBalance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Statement Balance</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormDescription>
                        {formatCurrency(field.value)}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Adjusted Book Balance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(adjustedBookBalance)}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Adjusted Bank Balance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(adjustedBankBalance)}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Difference</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`text-2xl font-bold ${difference === 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {formatCurrency(difference)}
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <Tabs defaultValue="transactions" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="transactions">Transactions</TabsTrigger>
                  <TabsTrigger value="adjustments">Adjustments</TabsTrigger>
                </TabsList>
                
                <TabsContent value="transactions" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search transactions..."
                        className="pl-8 w-[300px]"
                        value={searchQuery}
                        onChange={(e) => handleSearch(e.target.value)}
                      />
                    </div>
                    <div>
                      <Button variant="outline" size="sm" className="gap-1">
                        <Download className="h-4 w-4" />
                        <span>Export</span>
                      </Button>
                    </div>
                  </div>
                  
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]"></TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Reference</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead className="text-right">Amount</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTransactions.length > 0 ? (
                          filteredTransactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedTransactions.includes(transaction.id)}
                                  onCheckedChange={() => toggleTransaction(transaction.id)}
                                />
                              </TableCell>
                              <TableCell>{format(transaction.date, "MMM d, yyyy")}</TableCell>
                              <TableCell>{transaction.description}</TableCell>
                              <TableCell>{transaction.reference}</TableCell>
                              <TableCell className="capitalize">{transaction.type}</TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(transaction.amount)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={transaction.status === 'cleared' ? 'outline' : 'secondary'}>
                                  {transaction.status}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="h-24 text-center">
                              No transactions found.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    {selectedTransactions.length} of {filteredTransactions.length} transactions selected
                  </div>
                </TabsContent>
                
                <TabsContent value="adjustments" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Reconciliation Adjustments</h3>
                    <Dialog open={isAddAdjustmentOpen} onOpenChange={setIsAddAdjustmentOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">Add Adjustment</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Reconciliation Adjustment</DialogTitle>
                          <DialogDescription>
                            Add an adjustment to reconcile differences between bank statement and book balance.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="adjustment-type" className="text-right">
                              Type
                            </label>
                            <Select
                              value={newAdjustment.type}
                              onValueChange={(value) => setNewAdjustment({...newAdjustment, type: value})}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="addition">Addition</SelectItem>
                                <SelectItem value="subtraction">Subtraction</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="adjustment-category" className="text-right">
                              Category
                            </label>
                            <Select
                              value={newAdjustment.category}
                              onValueChange={(value) => setNewAdjustment({...newAdjustment, category: value})}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="outstanding_check">Outstanding Check</SelectItem>
                                <SelectItem value="deposit_in_transit">Deposit in Transit</SelectItem>
                                <SelectItem value="bank_error">Bank Error</SelectItem>
                                <SelectItem value="book_error">Book Error</SelectItem>
                                <SelectItem value="fee">Bank Fee</SelectItem>
                                <SelectItem value="interest">Interest</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="adjustment-description" className="text-right">
                              Description
                            </label>
                            <Input
                              id="adjustment-description"
                              value={newAdjustment.description}
                              onChange={(e) => setNewAdjustment({...newAdjustment, description: e.target.value})}
                              className="col-span-3"
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="adjustment-amount" className="text-right">
                              Amount
                            </label>
                            <Input
                              id="adjustment-amount"
                              type="number"
                              value={newAdjustment.amount}
                              onChange={(e) => setNewAdjustment({...newAdjustment, amount: parseFloat(e.target.value)})}
                              className="col-span-3"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button type="button" onClick={handleAddAdjustment}>Add Adjustment</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                  
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Description</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead className="text-right">Amount</TableHead>
                          <TableHead className="w-[70px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {adjustmentsList.length > 0 ? (
                          adjustmentsList.map((adjustment) => (
                            <TableRow key={adjustment.id}>
                              <TableCell>{adjustment.description}</TableCell>
                              <TableCell className="capitalize">{adjustment.category.replace('_', ' ')}</TableCell>
                              <TableCell className="capitalize">{adjustment.type}</TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(adjustment.amount)}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleRemoveAdjustment(adjustment.id)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="h-24 text-center">
                              No adjustments added.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
              
              <FormField
                control={form.control as any}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about this reconciliation"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.reset();
                    setSelectedTransactions([]);
                    setAdjustmentsList(adjustments);
                  }}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  disabled={isReconciling || difference !== 0}
                >
                  {isReconciling ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Reconciling...
                    </>
                  ) : difference === 0 ? (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Complete Reconciliation
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Reconciliation Incomplete
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-between border-t px-6 py-4">
          <p className="text-sm text-muted-foreground">
            Reconciliation must have a zero difference to be completed.
          </p>
          <Button variant="ghost" size="sm" className="gap-1">
            <Download className="h-4 w-4" />
            <span>Download Template</span>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

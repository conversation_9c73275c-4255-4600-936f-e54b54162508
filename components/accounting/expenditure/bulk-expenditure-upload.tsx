'use client';

import React, { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Upload,
  Download,
  FileSpreadsheet,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  FileText,
  BarChart3
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';

interface BulkExpenditureUploadProps {
  onUploadComplete?: () => void;
  onClose?: () => void;
}

interface ImportResult {
  success: boolean;
  message: string;
  successCount: number;
  errorCount: number;
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;
  warnings: Array<{
    row: number;
    warning: string;
    data: any;
  }>;
}

export function BulkExpenditureUpload({ onUploadComplete, onClose }: BulkExpenditureUploadProps) {
  const [activeTab, setActiveTab] = useState('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const { toast } = useToast();

  // File drop zone configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Validate file type
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ];
      
      if (!validTypes.includes(file.type)) {
        setUploadError('Please upload a valid Excel (.xlsx, .xls) or CSV file');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError('File size must be less than 10MB');
        return;
      }

      setSelectedFile(file);
      setUploadError(null);
      toast({
        title: "File Selected",
        description: `Selected ${file.name} for upload`,
      });
    }
  }, [toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFile) {
      setUploadError('Please select a file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', selectedFile);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 500);

      // Upload file to bulk import API
      const response = await fetch('/api/accounting/expense/bulk-import', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Upload failed`);
      }

      const result = await response.json();
      setImportResult(result);

      if (result.success) {
        toast({
          title: "Import Successful",
          description: `Successfully imported ${result.successCount} expenditure transactions`,
          variant: "default"
        });

        if (onUploadComplete) {
          onUploadComplete();
        }
      } else {
        toast({
          title: "Import Completed with Issues",
          description: `${result.successCount} successful, ${result.errorCount} failed`,
          variant: "destructive"
        });
      }

      // Switch to results tab
      setActiveTab("results");
    } catch (error: unknown) {
      console.error('Error uploading file:', error);
      setUploadError(error instanceof Error ? error.message : 'An error occurred during upload');
      setUploadProgress(0);
    } finally {
      setIsUploading(false);
    }
  };

  // Download template
  const handleDownloadTemplate = () => {
    console.log('Downloading expenditure template...');

    // Download the template file from our API with cache-busting parameter
    const timestamp = new Date().getTime();
    window.location.href = `/api/accounting/expense/template?t=${timestamp}`;

    // Show success toast
    toast({
      title: "Template Downloaded",
      description: "The expenditure import template has been downloaded with all required fields.",
      variant: "default"
    });
  };

  // Reset form
  const handleReset = () => {
    setSelectedFile(null);
    setUploadProgress(0);
    setUploadError(null);
    setImportResult(null);
    setActiveTab('upload');
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Bulk Expenditure Upload
        </CardTitle>
        <CardDescription>
          Import multiple expenditure transactions from Excel or CSV files
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="template">Download Template</TabsTrigger>
            <TabsTrigger value="results" disabled={!importResult}>
              Results
            </TabsTrigger>
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-4">
              {/* File Drop Zone */}
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-primary bg-primary/5'
                    : 'border-muted-foreground/25 hover:border-primary/50'
                }`}
              >
                <input {...getInputProps()} />
                <div className="flex flex-col items-center gap-4">
                  <div className="p-4 bg-muted rounded-full">
                    <FileSpreadsheet className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-lg font-medium">
                      {isDragActive
                        ? 'Drop the file here...'
                        : 'Drag & drop your expenditure file here'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      or click to select a file
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="outline">.xlsx</Badge>
                    <Badge variant="outline">.xls</Badge>
                    <Badge variant="outline">.csv</Badge>
                  </div>
                </div>
              </div>

              {/* Selected File */}
              {selectedFile && (
                <Alert>
                  <FileText className="h-4 w-4" />
                  <AlertTitle>File Selected</AlertTitle>
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <span>{selectedFile.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Upload Error */}
              {uploadError && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertTitle>Upload Error</AlertTitle>
                  <AlertDescription>{uploadError}</AlertDescription>
                </Alert>
              )}

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Uploading...</span>
                    <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={handleUpload}
                  disabled={!selectedFile || isUploading}
                  className="flex-1"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Expenditures
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={handleReset} disabled={isUploading}>
                  Reset
                </Button>
                {onClose && (
                  <Button variant="ghost" onClick={onClose} disabled={isUploading}>
                    Cancel
                  </Button>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Template Tab */}
          <TabsContent value="template" className="space-y-4">
            <div className="space-y-4">
              <Alert>
                <Download className="h-4 w-4" />
                <AlertTitle>Download Import Template</AlertTitle>
                <AlertDescription>
                  Download the Excel template with all required fields and formatting for expenditure imports.
                </AlertDescription>
              </Alert>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Required Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1 text-sm">
                      <li>• Date (YYYY-MM-DD format)</li>
                      <li>• Category</li>
                      <li>• Amount (numeric)</li>
                      <li>• Reference</li>
                      <li>• Fiscal Year</li>
                      <li>• Status</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Optional Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1 text-sm">
                      <li>• Description</li>
                      <li>• Vendor</li>
                      <li>• Department</li>
                      <li>• Payment Method</li>
                      <li>• Budget</li>
                      <li>• Budget Category</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>

              <Button onClick={handleDownloadTemplate} className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
            </div>
          </TabsContent>

          {/* Results Tab */}
          <TabsContent value="results" className="space-y-4">
            {importResult && (
              <div className="space-y-4">
                {/* Summary */}
                <div className="grid gap-4 md:grid-cols-3">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-2xl font-bold text-green-600">
                            {importResult.successCount}
                          </p>
                          <p className="text-sm text-muted-foreground">Successful</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-500" />
                        <div>
                          <p className="text-2xl font-bold text-red-600">
                            {importResult.errorCount}
                          </p>
                          <p className="text-sm text-muted-foreground">Failed</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-2xl font-bold text-blue-600">
                            {importResult.successCount + importResult.errorCount}
                          </p>
                          <p className="text-sm text-muted-foreground">Total Processed</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Errors */}
                {importResult.errors && importResult.errors.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-red-600">
                        <XCircle className="h-5 w-5" />
                        Import Errors ({importResult.errors.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {importResult.errors.map((error, index) => (
                          <Alert key={index} variant="destructive">
                            <AlertDescription>
                              <strong>Row {error.row}:</strong> {error.error}
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Warnings */}
                {importResult.warnings && importResult.warnings.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-yellow-600">
                        <AlertTriangle className="h-5 w-5" />
                        Import Warnings ({importResult.warnings.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {importResult.warnings.map((warning, index) => (
                          <Alert key={index}>
                            <AlertDescription>
                              <strong>Row {warning.row}:</strong> {warning.warning}
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex gap-2">
                  <Button onClick={handleReset} className="flex-1">
                    Import More Files
                  </Button>
                  {onClose && (
                    <Button variant="outline" onClick={onClose}>
                      Close
                    </Button>
                  )}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

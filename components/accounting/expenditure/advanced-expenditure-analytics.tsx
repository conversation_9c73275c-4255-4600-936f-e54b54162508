'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Pie<PERSON><PERSON> as PieChartIcon,
  Activity,
  Calendar,
  Download,
  Filter
} from 'lucide-react';
import { useExpenditureStats } from '@/lib/hooks/accounting/use-expenditure-stats';
import { useExpenditureStore } from '@/lib/stores/expenditure-store';
import { formatCurrency } from '@/lib/utils/currency';

interface AdvancedExpenditureAnalyticsProps {
  fiscalYear?: string;
  budgetId?: string;
}

// Color palette for charts
const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8',
  '#82CA9D', '#FFC658', '#FF7C7C', '#8DD1E1', '#D084D0'
];

export function AdvancedExpenditureAnalytics({
  fiscalYear: propFiscalYear,
  budgetId: propBudgetId
}: AdvancedExpenditureAnalyticsProps) {
  const { getCurrentFiscalYear } = useExpenditureStore();
  const [selectedFiscalYear, setSelectedFiscalYear] = useState(propFiscalYear || getCurrentFiscalYear());
  const [selectedBudgetId, setSelectedBudgetId] = useState<string | undefined>(propBudgetId);
  const [selectedPeriod, setSelectedPeriod] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');

  // Get expenditure statistics
  const {
    expenditureStats,
    isLoading,
    refetch
  } = useExpenditureStats(selectedFiscalYear, selectedBudgetId, {
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Process data for charts
  const chartData = useMemo(() => {
    if (!expenditureStats) return null;

    // Category breakdown for pie chart
    const categoryData = expenditureStats.expenditureByCategory.map((category, index) => ({
      name: category.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: category.amount,
      percentage: category.percentage,
      budgeted: category.budgeted,
      variance: category.variance,
      utilization: category.utilizationPercentage,
      color: COLORS[index % COLORS.length]
    }));

    // Monthly trend data
    const monthlyData = expenditureStats.monthlyExpenditure.map(month => ({
      month: month.month,
      actual: month.amount,
      budgeted: month.budgeted,
      variance: month.variance,
      utilization: month.utilizationPercentage
    }));

    // Budget utilization by category
    const utilizationData = expenditureStats.expenditureByCategory.map(category => ({
      category: category.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      utilization: Math.min(category.utilizationPercentage, 150), // Cap at 150% for display
      budgeted: category.budgeted,
      actual: category.amount,
      status: category.utilizationPercentage > 100 ? 'over' : 
              category.utilizationPercentage > 80 ? 'warning' : 'good'
    }));

    return {
      categoryData,
      monthlyData,
      utilizationData
    };
  }, [expenditureStats]);

  // Key metrics
  const keyMetrics = useMemo(() => {
    if (!expenditureStats) return null;

    const totalExpenditure = expenditureStats.totalExpenditure;
    const budgetedExpenditure = expenditureStats.budgetedExpenditure;
    const budgetUtilization = budgetedExpenditure > 0 ? (totalExpenditure / budgetedExpenditure) * 100 : 0;
    const variance = totalExpenditure - budgetedExpenditure;
    const variancePercentage = expenditureStats.budgetVariancePercentage;

    return {
      totalExpenditure,
      budgetedExpenditure,
      budgetUtilization,
      variance,
      variancePercentage,
      yearOverYearChange: expenditureStats.yearOverYearChange,
      trendDirection: expenditureStats.trendDirection
    };
  }, [expenditureStats]);

  const handleExportData = () => {
    // TODO: Implement data export functionality
    console.log('Exporting expenditure analytics data...');
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-20 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="h-96 bg-muted animate-pulse rounded" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Advanced Expenditure Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive expenditure analysis and insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedFiscalYear} onValueChange={setSelectedFiscalYear}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Fiscal Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2025-2026">2025-2026</SelectItem>
              <SelectItem value="2024-2025">2024-2025</SelectItem>
              <SelectItem value="2023-2024">2023-2024</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {keyMetrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Total Expenditure */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenditure</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(keyMetrics.totalExpenditure)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {keyMetrics.yearOverYearChange !== 0 && (
                  <>
                    {keyMetrics.yearOverYearChange > 0 ? (
                      <TrendingUp className="mr-1 h-3 w-3 text-red-500" />
                    ) : (
                      <TrendingDown className="mr-1 h-3 w-3 text-green-500" />
                    )}
                    <span className={keyMetrics.yearOverYearChange > 0 ? 'text-red-600' : 'text-green-600'}>
                      {Math.abs(keyMetrics.yearOverYearChange).toFixed(1)}% from last year
                    </span>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Budget Utilization */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {keyMetrics.budgetUtilization.toFixed(1)}%
              </div>
              <Progress value={Math.min(keyMetrics.budgetUtilization, 100)} className="mt-2" />
              <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                <span>{formatCurrency(keyMetrics.totalExpenditure)}</span>
                <span>{formatCurrency(keyMetrics.budgetedExpenditure)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Budget Variance */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Variance</CardTitle>
              {keyMetrics.variance <= 0 ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${
                keyMetrics.variance <= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {keyMetrics.variance >= 0 ? '+' : ''}
                {formatCurrency(keyMetrics.variance)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span>
                  {keyMetrics.variancePercentage >= 0 ? '+' : ''}
                  {keyMetrics.variancePercentage.toFixed(1)}% vs budget
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Trend Direction */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trend Direction</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Badge variant={
                  keyMetrics.trendDirection === 'up' ? 'destructive' :
                  keyMetrics.trendDirection === 'down' ? 'default' : 'secondary'
                }>
                  {keyMetrics.trendDirection === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
                  {keyMetrics.trendDirection === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
                  {keyMetrics.trendDirection === 'stable' && <Activity className="h-3 w-3 mr-1" />}
                  {keyMetrics.trendDirection.charAt(0).toUpperCase() + keyMetrics.trendDirection.slice(1)}
                </Badge>
              </div>
              <div className="text-xs text-muted-foreground mt-2">
                Based on {selectedPeriod} analysis
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts and Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="utilization">Utilization</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Category Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5" />
                  Expenditure by Category
                </CardTitle>
                <CardDescription>
                  Distribution of expenditures across categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={chartData?.categoryData || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name}: ${percentage.toFixed(1)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData?.categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [formatCurrency(value), 'Amount']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Monthly Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Monthly Expenditure Trend
                </CardTitle>
                <CardDescription>
                  Actual vs budgeted expenditures by month
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={chartData?.monthlyData || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
                    <Tooltip formatter={(value: any) => [formatCurrency(value), '']} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="budgeted"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                      name="Budgeted"
                    />
                    <Area
                      type="monotone"
                      dataKey="actual"
                      stackId="2"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      fillOpacity={0.6}
                      name="Actual"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Category Performance Analysis</CardTitle>
              <CardDescription>
                Detailed breakdown of expenditure performance by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData?.categoryData || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                  />
                  <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
                  <Tooltip formatter={(value: any) => [formatCurrency(value), '']} />
                  <Legend />
                  <Bar dataKey="budgeted" fill="#8884d8" name="Budgeted" />
                  <Bar dataKey="value" fill="#82ca9d" name="Actual" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expenditure Trends</CardTitle>
              <CardDescription>
                Monthly expenditure trends and patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={chartData?.monthlyData || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
                  <Tooltip formatter={(value: any) => [formatCurrency(value), '']} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="actual"
                    stroke="#8884d8"
                    strokeWidth={2}
                    name="Actual Expenditure"
                  />
                  <Line
                    type="monotone"
                    dataKey="budgeted"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Budgeted Expenditure"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Utilization Tab */}
        <TabsContent value="utilization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Budget Utilization by Category</CardTitle>
              <CardDescription>
                Budget utilization percentage across different categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {chartData?.utilizationData.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.category}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {formatCurrency(item.actual)} / {formatCurrency(item.budgeted)}
                        </span>
                        <Badge variant={
                          item.status === 'over' ? 'destructive' :
                          item.status === 'warning' ? 'default' : 'secondary'
                        }>
                          {item.utilization.toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                    <Progress 
                      value={Math.min(item.utilization, 100)} 
                      className={`h-2 ${
                        item.status === 'over' ? 'bg-red-100' :
                        item.status === 'warning' ? 'bg-yellow-100' : 'bg-green-100'
                      }`}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

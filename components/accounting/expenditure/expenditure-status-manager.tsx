'use client';

import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { ProcessingOverlay, useProcessingOverlay } from '@/components/ui/processing-overlay';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  CreditCard, 
  AlertTriangle,
  Loader2,
  FileText,
  User,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import { useExpenditureStore } from '@/lib/stores/expenditure-store';

interface ExpenditureStatusManagerProps {
  expenditure: any;
  onStatusUpdate?: (expenditure: any) => void;
  trigger?: React.ReactNode;
  disabled?: boolean;
}

// Status configuration with icons and colors
const STATUS_CONFIG = {
  draft: {
    label: 'Draft',
    icon: FileText,
    color: 'bg-gray-100 text-gray-800',
    description: 'Expenditure is being prepared'
  },
  pending_approval: {
    label: 'Pending Approval',
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800',
    description: 'Waiting for approval'
  },
  approved: {
    label: 'Approved',
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800',
    description: 'Approved for payment'
  },
  rejected: {
    label: 'Rejected',
    icon: XCircle,
    color: 'bg-red-100 text-red-800',
    description: 'Rejected and requires revision'
  },
  paid: {
    label: 'Paid',
    icon: CreditCard,
    color: 'bg-blue-100 text-blue-800',
    description: 'Payment has been processed'
  },
  cancelled: {
    label: 'Cancelled',
    icon: AlertTriangle,
    color: 'bg-gray-100 text-gray-800',
    description: 'Expenditure has been cancelled'
  }
};

// Valid status transitions
const STATUS_TRANSITIONS: Record<string, string[]> = {
  draft: ['pending_approval', 'cancelled'],
  pending_approval: ['approved', 'rejected', 'cancelled'],
  approved: ['paid', 'cancelled'],
  rejected: ['draft', 'cancelled'],
  paid: ['cancelled'], // Paid expenses can only be cancelled
  cancelled: [] // Cancelled expenses cannot change status
};

export function ExpenditureStatusManager({
  expenditure,
  onStatusUpdate,
  trigger,
  disabled = false
}: ExpenditureStatusManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [notes, setNotes] = useState('');
  const [reason, setReason] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { updateExpenditureStatus } = useExpenditureStore();
  const overlay = useProcessingOverlay();

  const currentStatus = expenditure?.status || 'draft';
  const currentStatusConfig = STATUS_CONFIG[currentStatus as keyof typeof STATUS_CONFIG];
  const availableTransitions = STATUS_TRANSITIONS[currentStatus] || [];

  const handleStatusUpdate = async () => {
    if (!selectedStatus) {
      toast({
        title: 'Error',
        description: 'Please select a status',
        variant: 'destructive',
      });
      return;
    }

    // Validate required fields for certain status changes
    if (selectedStatus === 'rejected' && !reason.trim()) {
      toast({
        title: 'Error',
        description: 'Rejection reason is required',
        variant: 'destructive',
      });
      return;
    }

    setIsUpdating(true);

    // Show processing overlay
    const statusLabel = STATUS_CONFIG[selectedStatus as keyof typeof STATUS_CONFIG].label;
    overlay.showProcessing(`Updating status to ${statusLabel}...`);

    try {
      await updateExpenditureStatus(
        expenditure.id || expenditure._id,
        selectedStatus,
        notes.trim() || undefined,
        reason.trim() || undefined
      );

      // Show success overlay
      overlay.showSuccess(`Status updated to ${statusLabel}!`);

      // Call callback if provided
      if (onStatusUpdate) {
        onStatusUpdate({
          ...expenditure,
          status: selectedStatus,
          [`${selectedStatus}At`]: new Date(),
          [`${selectedStatus}By`]: 'current-user', // This would come from auth context
        });
      }

      // Reset form and close dialog after success animation
      setTimeout(() => {
        setSelectedStatus('');
        setNotes('');
        setReason('');
        setIsOpen(false);
        overlay.hide();
      }, 2500);

    } catch (error) {
      // Hide overlay on error
      overlay.hide();

      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update status',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      // Reset form when closing
      setSelectedStatus('');
      setNotes('');
      setReason('');
    }
    setIsOpen(open);
  };

  const StatusIcon = currentStatusConfig?.icon || FileText;

  return (
    <>
      <ProcessingOverlay
        isVisible={overlay.isVisible}
        isProcessing={overlay.isProcessing}
        isSuccess={overlay.isSuccess}
        onComplete={overlay.hide}
      />

      <Dialog open={isOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            variant="outline"
            size="sm"
            disabled={disabled || availableTransitions.length === 0}
            className="gap-2"
          >
            <StatusIcon className="h-4 w-4" />
            Update Status
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <StatusIcon className="h-5 w-5" />
            Update Expenditure Status
          </DialogTitle>
          <DialogDescription>
            Change the status of expenditure {expenditure?.reference}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <div className="space-y-2">
            <Label>Current Status</Label>
            <div className="flex items-center gap-2">
              <Badge className={currentStatusConfig?.color}>
                <StatusIcon className="h-3 w-3 mr-1" />
                {currentStatusConfig?.label}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {currentStatusConfig?.description}
              </span>
            </div>
          </div>

          <Separator />

          {/* Status Selection */}
          <div className="space-y-2">
            <Label htmlFor="status">New Status *</Label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {availableTransitions.map(status => {
                  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG];
                  const Icon = config.icon;
                  return (
                    <SelectItem key={status} value={status}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span>{config.label}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {availableTransitions.length === 0 && (
              <p className="text-sm text-muted-foreground">
                No status transitions available from {currentStatusConfig?.label}
              </p>
            )}
          </div>

          {/* Rejection Reason (required for rejected status) */}
          {selectedStatus === 'rejected' && (
            <div className="space-y-2">
              <Label htmlFor="reason">Rejection Reason *</Label>
              <Textarea
                id="reason"
                placeholder="Please provide a reason for rejection..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any additional notes..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          {/* Expenditure Details */}
          <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium text-sm">Expenditure Details</h4>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-muted-foreground">Reference:</span>
                <div className="font-medium">{expenditure?.reference}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Amount:</span>
                <div className="font-medium">
                  MWK {expenditure?.amount?.toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Category:</span>
                <div className="font-medium">{expenditure?.category}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Date:</span>
                <div className="font-medium">
                  {expenditure?.date ? format(new Date(expenditure.date), 'PPP') : 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Status History */}
          {expenditure?.statusHistory && expenditure.statusHistory.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Status History</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {expenditure.statusHistory.slice(-3).map((history: any, index: number) => {
                  const config = STATUS_CONFIG[history.status as keyof typeof STATUS_CONFIG];
                  const Icon = config?.icon || FileText;
                  return (
                    <div key={index} className="flex items-center gap-3 text-sm p-2 bg-muted/30 rounded">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{config?.label}</span>
                          <span className="text-muted-foreground">
                            {history.changedAt ? format(new Date(history.changedAt), 'PPp') : ''}
                          </span>
                        </div>
                        {history.notes && (
                          <div className="text-muted-foreground text-xs mt-1">
                            {history.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isUpdating}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleStatusUpdate}
            disabled={isUpdating || !selectedStatus || availableTransitions.length === 0}
          >
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Status'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}

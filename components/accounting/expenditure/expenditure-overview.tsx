'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  RefreshCw,
  Clock,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight,
  BarChart4,
  Activity,
  Zap,
  CreditCard,
  Plus
} from 'lucide-react';
import { useExpenditureStats } from '@/lib/hooks/accounting/use-expenditure-stats';
import { useBudget } from '@/lib/hooks/accounting/use-budget';
import { useIsMobile } from '@/lib/hooks/use-responsive';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/accounting/shared/date-range-picker';
import { ExpenseCategoriesChart } from '@/components/accounting/expenditure/expense-categories-chart';
import { ExpenseTable } from '@/components/accounting/expenditure/expense-table';
import { ExpenseEmptyState } from '@/components/accounting/expenditure/expense-empty-state';
import { formatCurrency } from '@/lib/utils/currency';
import { useExpenditureStore } from '@/lib/stores/expenditure-store';
import Link from 'next/link';

// Utility functions
const formatCompactCurrency = (value: number) => {
  if (value >= **********) {
    return `MWK ${(value / **********).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`;
  }
  return formatCurrency(value);
};

export function ExpenditureOverview() {
  // Use enhanced expenditure store for fiscal year management
  const { getCurrentFiscalYear, getActiveFiscalYears } = useExpenditureStore();
  const [fiscalYear, setFiscalYear] = useState(() => getCurrentFiscalYear());
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [activeBudgetId, setActiveBudgetId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  const isMobile = useIsMobile();

  // Auto-refresh interval (10 minutes = 600,000 ms)
  const REFRESH_INTERVAL = 10 * 60 * 1000;

  // Get budget data
  const { activeBudgets } = useBudget();

  // Get active budget for the fiscal year
  const activeBudget = useMemo(() => {
    if (!activeBudgets?.length) return null;
    return activeBudgets.find((budget: any) =>
      budget.fiscalYear === fiscalYear &&
      (budget.status === 'active' || budget.status === 'approved')
    ) || activeBudgets[0];
  }, [activeBudgets, fiscalYear]);

  // Update active budget ID when budget changes
  React.useEffect(() => {
    if (activeBudget?.id && activeBudget.id !== activeBudgetId) {
      setActiveBudgetId(activeBudget.id);
    }
  }, [activeBudget, activeBudgetId]);

  // Get expenditure statistics with auto-refetch
  const {
    expenditureStats,
    isLoading: isLoadingExpenditureStats,
    refetch: refetchExpenditureStats
  } = useExpenditureStats(fiscalYear, activeBudgetId || undefined, {
    refetchInterval: REFRESH_INTERVAL,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
    staleTime: 5 * 60 * 1000, // Consider data stale after 5 minutes
  });

  // Loading timeout handler
  React.useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isLoadingExpenditureStats) {
      timeout = setTimeout(() => {
        setLoadingTimeout(true);
      }, 15000); // 15 second timeout
    } else {
      setLoadingTimeout(false);
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [isLoadingExpenditureStats]);

  // Manual refresh function
  const handleManualRefresh = useCallback(() => {
    console.log('ExpenditureOverview: Manual refresh triggered');
    if (refetchExpenditureStats) {
      refetchExpenditureStats();
      setLastRefresh(new Date());
    }
  }, [refetchExpenditureStats]);

  // Handle fiscal year change
  const handleFiscalYearChange = useCallback((newFiscalYear: string) => {
    if (newFiscalYear !== fiscalYear) {
      console.log('ExpenditureOverview: Fiscal year changing from', fiscalYear, 'to', newFiscalYear);
      setFiscalYear(newFiscalYear);
    }
  }, [fiscalYear]);

  // Handle date range change
  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    setDateRange(range);
  }, []);

  // Calculate KPIs and metrics
  const kpis = useMemo(() => {
    if (!expenditureStats) return null;

    const currentTotal = expenditureStats.totalExpenditure || 0;
    const budgetedTotal = expenditureStats.budgetedExpenditure || 0;

    const budgetVariance = currentTotal - budgetedTotal;
    const budgetVariancePercentage = budgetedTotal > 0 ? (budgetVariance / budgetedTotal) * 100 : 0;
    const periodChange = expenditureStats.yearOverYearChange || 0;

    const averageMonthlyExpense = expenditureStats.monthlyExpenditure
      ? expenditureStats.monthlyExpenditure.reduce((sum, month) => sum + month.amount, 0) / expenditureStats.monthlyExpenditure.length
      : 0;

    const budgetUtilization = budgetedTotal > 0 ? (currentTotal / budgetedTotal) * 100 : 0;

    return {
      totalExpense: currentTotal,
      budgetedExpense: budgetedTotal,
      budgetVariance,
      budgetVariancePercentage,
      periodChange,
      averageMonthlyExpense,
      budgetUtilization,
      isOverBudget: budgetUtilization > 100,
      isOnTrack: budgetUtilization >= 80 && budgetUtilization <= 100,
      needsAttention: budgetUtilization < 50
    };
  }, [expenditureStats]);

  // Handle loading timeout
  if (loadingTimeout) {
    return (
      <ExpenseEmptyState
        type="loading-timeout"
        title="Expenditure Overview Loading Timeout"
        description="The expenditure overview is taking longer than expected to load."
        onRetry={handleManualRefresh}
      />
    );
  }

  // Handle loading state
  if (isLoadingExpenditureStats && !loadingTimeout) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>

        {/* KPI Cards Skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  // Handle no data state - but be more lenient to show data even if budget linkage is missing
  if (!isLoadingExpenditureStats && (!expenditureStats || expenditureStats.totalExpenditure === 0)) {
    return (
      <div className="space-y-6">
        {/* Header with Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Expenditure Overview</h2>
            <p className="text-muted-foreground">
              Track and analyze expense performance for {fiscalYear}
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button asChild>
              <Link href="/dashboard/accounting/expenditure/create">
                <Plus className="mr-2 h-4 w-4" />
                Create New
              </Link>
            </Button>

            <Select value={fiscalYear} onValueChange={handleFiscalYearChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Select fiscal year" />
              </SelectTrigger>
              <SelectContent>
                {getActiveFiscalYears().length > 0 ? (
                  getActiveFiscalYears().map(fy => (
                    <SelectItem key={fy.value} value={fy.value}>
                      {fy.label} {fy.isCurrent ? '(Current)' : ''}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value={fiscalYear} disabled>
                    {fiscalYear} (Default)
                  </SelectItem>
                )}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={handleManualRefresh}
              disabled={isLoadingExpenditureStats}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingExpenditureStats ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* No Data State */}
        <ExpenseEmptyState
          type="no-data"
          title="No Expense Data Available"
          description={`No expense records found for fiscal year ${fiscalYear}. The expense tracking APIs may not be set up yet, or there might be no data for this period.`}
          onRetry={handleManualRefresh}
          showActions={true}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Expenditure Overview</h2>
          <p className="text-muted-foreground">
            Track and analyze expense performance for {fiscalYear}
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button asChild>
            <Link href="/dashboard/accounting/expenditure/new">
              <Plus className="mr-2 h-4 w-4" />
              Create New
            </Link>
          </Button>

          <Select value={fiscalYear} onValueChange={handleFiscalYearChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {getActiveFiscalYears().length > 0 ? (
                getActiveFiscalYears().map(fy => (
                  <SelectItem key={fy.value} value={fy.value}>
                    {fy.label} {fy.isCurrent ? '(Current)' : ''}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value={fiscalYear} disabled>
                  {fiscalYear} (Default)
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          <DateRangePicker
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            className="hidden md:block"
          />

          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isLoadingExpenditureStats}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingExpenditureStats ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Expense */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isMobile
                ? formatCompactCurrency(kpis?.totalExpense || 0)
                : formatCurrency(kpis?.totalExpense || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              {kpis?.periodChange !== undefined && kpis.periodChange !== 0 ? (
                <>
                  {kpis.periodChange >= 0 ? (
                    <ArrowUpRight className="mr-1 h-3 w-3 text-red-500" />
                  ) : (
                    <ArrowDownRight className="mr-1 h-3 w-3 text-green-500" />
                  )}
                  <span className={kpis.periodChange >= 0 ? 'text-red-600' : 'text-green-600'}>
                    {Math.abs(kpis.periodChange).toFixed(1)}% from last period
                  </span>
                </>
              ) : (
                <span className="text-muted-foreground">
                  No previous period data available
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Budget Performance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {kpis?.budgetedExpense && kpis.budgetedExpense > 0
                ? `${kpis.budgetUtilization.toFixed(1)}%`
                : 'No Budget'
              }
            </div>
            <Progress
              value={Math.min(kpis?.budgetUtilization || 0, 100)}
              className="mt-2"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
              <span>
                {isMobile
                  ? formatCompactCurrency(kpis?.totalExpense || 0)
                  : formatCurrency(kpis?.totalExpense || 0)
                }
              </span>
              <span>
                {kpis?.budgetedExpense && kpis.budgetedExpense > 0
                  ? (isMobile
                      ? formatCompactCurrency(kpis.budgetedExpense)
                      : formatCurrency(kpis.budgetedExpense)
                    )
                  : 'No budget set'
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Budget Variance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Variance</CardTitle>
            {kpis?.budgetVariance && kpis.budgetVariance <= 0 ? (
              <TrendingDown className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingUp className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              kpis?.budgetVariance && kpis.budgetVariance <= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {kpis?.budgetVariance && kpis.budgetVariance >= 0 ? '+' : ''}
              {isMobile
                ? formatCompactCurrency(kpis?.budgetVariance || 0)
                : formatCurrency(kpis?.budgetVariance || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>
                {kpis?.budgetVariancePercentage && kpis.budgetVariancePercentage >= 0 ? '+' : ''}
                {kpis?.budgetVariancePercentage.toFixed(1)}% vs budget
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Average Monthly */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Monthly Expense</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isMobile
                ? formatCompactCurrency(kpis?.averageMonthlyExpense || 0)
                : formatCurrency(kpis?.averageMonthlyExpense || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Calendar className="mr-1 h-3 w-3" />
              <span>Based on {expenditureStats?.monthlyExpenditure?.length || 0} months</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Different Views */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Expense Categories Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Expenses by Category</CardTitle>
                <CardDescription>
                  Distribution of expenses across different categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ExpenseCategoriesChart
                  fiscalYear={fiscalYear}
                  budgetId={activeBudgetId || undefined}
                  showFilters={false}
                  height={300}
                />
              </CardContent>
            </Card>

            {/* Budget Status */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Status</CardTitle>
                <CardDescription>
                  Current budget performance and alerts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Budget Progress */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Budget Utilization</span>
                      <span className="text-sm text-muted-foreground">
                        {kpis?.budgetUtilization.toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={Math.min(kpis?.budgetUtilization || 0, 100)} />
                  </div>

                  {/* Status Alerts */}
                  <div className="space-y-3">
                    {kpis?.isOverBudget && (
                      <div className="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-800">Over Budget</p>
                          <p className="text-sm text-red-600">
                            Expenses have exceeded the allocated budget by{' '}
                            {formatCurrency(Math.abs(kpis.budgetVariance))}
                          </p>
                        </div>
                      </div>
                    )}

                    {kpis?.isOnTrack && (
                      <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-green-800">On Track</p>
                          <p className="text-sm text-green-600">
                            Expense performance is within expected budget range
                          </p>
                        </div>
                      </div>
                    )}

                    {kpis?.needsAttention && (
                      <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-yellow-800">Under Budget</p>
                          <p className="text-sm text-yellow-600">
                            Expenses are significantly below budget expectations
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatCompactCurrency(kpis?.totalExpense || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">Actual</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatCompactCurrency(kpis?.budgetedExpense || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">Budgeted</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Expense Transactions</CardTitle>
              <CardDescription>
                Latest expense entries for {fiscalYear}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ExpenseTable
                fiscalYear={fiscalYear}
                budgetId={activeBudgetId || undefined}
                showFilters={false}
                limit={5}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <ExpenseCategoriesChart
            fiscalYear={fiscalYear}
            budgetId={activeBudgetId || undefined}
            showFilters={true}
            height={400}
          />
        </TabsContent>

        {/* Transactions Tab */}
        <TabsContent value="transactions" className="space-y-4">
          <ExpenseTable
            fiscalYear={fiscalYear}
            budgetId={activeBudgetId || undefined}
            showFilters={true}
            limit={20}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Expense Analytics</CardTitle>
                <CardDescription>
                  Detailed expense performance analysis and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart4 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Advanced Analytics</h3>
                  <p className="text-muted-foreground mb-4">
                    Detailed analytics and forecasting features coming soon.
                  </p>
                  <Badge variant="secondary">
                    <Zap className="mr-1 h-3 w-3" />
                    Auto-refresh: Every 10 minutes
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Footer Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                <span>Auto-refresh: Every 10 minutes</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Activity className="h-3 w-3" />
              <span>Fiscal Year: {fiscalYear}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

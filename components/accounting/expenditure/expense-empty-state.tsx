'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  CreditCard, 
  TrendingDown, 
  FileText, 
  AlertCircle, 
  RefreshCw, 
  Plus,
  BarChart3,
  PieChart,
  Activity,
  Database
} from 'lucide-react';
import Link from 'next/link';

interface ExpenseEmptyStateProps {
  type: 'no-data' | 'error' | 'loading-timeout';
  title?: string;
  description?: string;
  error?: string;
  onRetry?: () => void;
  showActions?: boolean;
}

export function ExpenseEmptyState({ 
  type, 
  title, 
  description, 
  error, 
  onRetry,
  showActions = true 
}: ExpenseEmptyStateProps) {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-data':
        return {
          icon: <Database className="h-12 w-12 text-muted-foreground" />,
          title: title || 'No Expense Data Available',
          description: description || 'No expense records found for the selected period. Start by recording your first expense transaction.',
          variant: 'default' as const,
          actions: showActions ? (
            <div className="flex flex-col sm:flex-row gap-2">
              <Button asChild>
                <Link href="/dashboard/accounting/expenditure/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Record Expense
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/dashboard/accounting/expenditures">
                  <FileText className="mr-2 h-4 w-4" />
                  View All Expenses
                </Link>
              </Button>
            </div>
          ) : null
        };

      case 'error':
        return {
          icon: <AlertCircle className="h-12 w-12 text-destructive" />,
          title: title || 'Failed to Load Expense Data',
          description: description || 'There was an error loading the expense data. Please try again.',
          variant: 'destructive' as const,
          actions: showActions ? (
            <div className="flex flex-col sm:flex-row gap-2">
              {onRetry && (
                <Button onClick={onRetry} variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              )}
              <Button variant="outline" asChild>
                <Link href="/dashboard/accounting">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Back to Accounting
                </Link>
              </Button>
            </div>
          ) : null
        };

      case 'loading-timeout':
        return {
          icon: <RefreshCw className="h-12 w-12 text-amber-500" />,
          title: title || 'Loading Timeout',
          description: description || 'The data is taking longer than expected to load. Please refresh the page or try again.',
          variant: 'default' as const,
          actions: showActions ? (
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={() => window.location.reload()} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Page
              </Button>
              {onRetry && (
                <Button onClick={onRetry}>
                  <Activity className="mr-2 h-4 w-4" />
                  Retry
                </Button>
              )}
            </div>
          ) : null
        };

      default:
        return {
          icon: <CreditCard className="h-12 w-12 text-muted-foreground" />,
          title: 'Expenditure Overview',
          description: 'Expense data will appear here once available.',
          variant: 'default' as const,
          actions: null
        };
    }
  };

  const content = getEmptyStateContent();

  return (
    <Card className="w-full">
      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
        <div className="mb-4">
          {content.icon}
        </div>
        <CardTitle className="mb-2 text-xl">
          {content.title}
        </CardTitle>
        <CardDescription className="mb-6 max-w-md">
          {content.description}
        </CardDescription>
        
        {error && (
          <Alert variant="destructive" className="mb-6 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription className="text-sm">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {content.actions && (
          <div className="flex flex-col sm:flex-row gap-2">
            {content.actions}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Specific empty state components for different expense sections
export function ExpenseStatsEmptyState({ onRetry }: { onRetry?: () => void }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[
        { title: 'Total Expenses', icon: CreditCard },
        { title: 'Budget Utilization', icon: TrendingDown },
        { title: 'Salaries & Wages', icon: FileText },
        { title: 'Office Supplies', icon: PieChart }
      ].map((item, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
            <item.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-4 text-center">
              <Database className="h-8 w-8 text-muted-foreground mb-2" />
              <div className="text-sm text-muted-foreground">No data available</div>
              {onRetry && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={onRetry}
                  className="mt-2"
                >
                  <RefreshCw className="mr-1 h-3 w-3" />
                  Retry
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function ExpenseChartEmptyState({ 
  title = "Chart Data", 
  onRetry 
}: { 
  title?: string; 
  onRetry?: () => void; 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Chart data will appear here once available</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
          <div className="text-sm text-muted-foreground mb-4">
            No chart data available for the selected period
          </div>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

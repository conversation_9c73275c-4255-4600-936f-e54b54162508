'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Asset, useAssetStore } from '@/lib/store/useAssetStore';

// Define the form schema
const disposalFormSchema = z.object({
  disposalDate: z.date({
    required_error: 'Disposal date is required',
  }),
  disposalAmount: z.coerce.number().min(0, 'Amount must be a positive number').optional(),
  disposalReason: z.string().min(2, 'Reason is required'),
  disposalMethod: z.string().min(2, 'Method is required'),
  disposalReceiptNumber: z.string().optional(),
});

// Define the form values type
type DisposalFormValues = z.infer<typeof disposalFormSchema>;

// Define the component props
interface DisposalFormProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null;
}

export function DisposalForm({ isOpen, onClose, asset }: DisposalFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { disposeAsset } = useAssetStore();

  // Return null if asset is null
  if (!asset) return null;

  // Initialize the form
  const form = useForm<DisposalFormValues>({
    resolver: zodResolver(disposalFormSchema) as any,
    defaultValues: {
      disposalDate: new Date(),
      disposalAmount: 0,
      disposalReason: '',
      disposalMethod: '',
      disposalReceiptNumber: '',
    },
  });

  // Handle form submission
  const onSubmit = async (values: DisposalFormValues) => {
    setIsSubmitting(true);

    try {
      // Dispose asset
      await disposeAsset(asset._id, {
        disposalDate: format(values.disposalDate, 'yyyy-MM-dd'),
        disposalAmount: values.disposalAmount,
        disposalReason: values.disposalReason,
        disposalMethod: values.disposalMethod,
        disposalReceiptNumber: values.disposalReceiptNumber,
      });

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error submitting disposal form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Dispose Asset</DialogTitle>
          <DialogDescription>
            Record disposal details for {asset.name} (ID: {asset.assetId})
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Disposal Date */}
            <FormField
              control={form.control as any}
              name="disposalDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Disposal Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Disposal Method */}
            <FormField
              control={form.control as any}
              name="disposalMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Disposal Method</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select disposal method" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sale">Sale</SelectItem>
                      <SelectItem value="donation">Donation</SelectItem>
                      <SelectItem value="scrapping">Scrapping</SelectItem>
                      <SelectItem value="trade-in">Trade-in</SelectItem>
                      <SelectItem value="theft">Theft/Loss</SelectItem>
                      <SelectItem value="destruction">Destruction</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Disposal Amount */}
            <FormField
              control={form.control as any}
              name="disposalAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Disposal Amount ({asset.currency})</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value === undefined ? '' : field.value}
                      onChange={(e) => {
                        const value = e.target.value === '' ? undefined : Number(e.target.value);
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Amount received from disposal (if any)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Receipt Number */}
            <FormField
              control={form.control as any}
              name="disposalReceiptNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Receipt Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Receipt number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Disposal Reason */}
            <FormField
              control={form.control as any}
              name="disposalReason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Disposal Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Reason for disposal"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} variant="destructive">
                {isSubmitting ? 'Processing...' : 'Dispose Asset'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Asset, useAssetStore } from '@/lib/store/useAssetStore';

// Define the form schema
const depreciationFormSchema = z.object({
  date: z.date({
    required_error: 'Depreciation date is required',
  }),
  amount: z.coerce.number().min(0, 'Amount must be a positive number').optional(),
  fiscalYear: z.string().min(4, 'Fiscal year is required'),
  fiscalPeriod: z.string().min(1, 'Fiscal period is required'),
  notes: z.string().optional(),
});

// Define the form values type
type DepreciationFormValues = z.infer<typeof depreciationFormSchema>;

// Define the component props
interface DepreciationFormProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null;
}

export function DepreciationForm({ isOpen, onClose, asset }: DepreciationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const { recordDepreciation } = useAssetStore();

  // Return null if asset is null
  if (!asset) return null;

  // Get current fiscal year and period
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear().toString();
  const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0');

  // Initialize the form
  const form = useForm<DepreciationFormValues>({
    resolver: zodResolver(depreciationFormSchema) as any,
    defaultValues: {
      date: new Date(),
      amount: undefined,
      fiscalYear: currentYear,
      fiscalPeriod: currentMonth,
      notes: '',
    },
  });

  // Handle form submission
  const onSubmit = async (values: DepreciationFormValues) => {
    setIsSubmitting(true);

    try {
      // Record depreciation
      await recordDepreciation(asset._id, {
        date: format(values.date, 'yyyy-MM-dd'),
        amount: values.amount,
        fiscalYear: values.fiscalYear,
        fiscalPeriod: values.fiscalPeriod,
        notes: values.notes,
      });

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error submitting depreciation form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate estimated depreciation amount
  const calculateDepreciation = () => {
    setIsCalculating(true);

    try {
      let estimatedAmount = 0;

      // Simple calculation based on depreciation method
      if (asset.depreciationMethod === 'straight-line' && asset.usefulLifeYears) {
        // Straight-line: (Cost - Salvage) / Useful Life / 12 (monthly)
        const salvageValue = asset.salvageValue || 0;
        estimatedAmount = (asset.acquisitionCost - salvageValue) / asset.usefulLifeYears / 12;
      } else if (asset.depreciationMethod === 'reducing-balance' && asset.depreciationRate) {
        // Reducing balance: Book Value * Rate / 12 (monthly)
        estimatedAmount = asset.currentBookValue * (asset.depreciationRate / 100) / 12;
      }

      // Ensure depreciation doesn't reduce book value below salvage value
      const salvageValue = asset.salvageValue || 0;
      if (asset.currentBookValue - estimatedAmount < salvageValue) {
        estimatedAmount = asset.currentBookValue - salvageValue;
      }

      // Update form
      form.setValue('amount', Math.max(0, Math.round(estimatedAmount)));
    } catch (error) {
      console.error('Error calculating depreciation:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Record Depreciation</DialogTitle>
          <DialogDescription>
            Record depreciation for {asset.name} (ID: {asset.assetId})
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Depreciation Date */}
            <FormField
              control={form.control as any}
              name="date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Depreciation Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Fiscal Year */}
              <FormField
                control={form.control as any}
                name="fiscalYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Year</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Fiscal Period */}
              <FormField
                control={form.control as any}
                name="fiscalPeriod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Period</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Amount */}
            <FormField
              control={form.control as any}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount ({asset.currency})</FormLabel>
                  <div className="flex space-x-2">
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        value={field.value === undefined ? '' : field.value}
                        onChange={(e) => {
                          const value = e.target.value === '' ? undefined : Number(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={calculateDepreciation}
                      disabled={isCalculating}
                    >
                      {isCalculating ? 'Calculating...' : 'Calculate'}
                    </Button>
                  </div>
                  <FormDescription>
                    Leave blank to use the system's calculated amount
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control as any}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Record Depreciation'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

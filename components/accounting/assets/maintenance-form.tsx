// components/accounting/assets/maintenance-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Asset, useAssetStore } from '@/lib/store/useAssetStore';

// Define the form schema
const maintenanceFormSchema = z.object({
  date: z.date({
    required_error: 'Maintenance date is required',
  }),
  description: z.string().min(2, 'Description must be at least 2 characters'),
  cost: z.coerce.number().min(0, 'Cost must be a positive number'),
  provider: z.string().optional(),
  receiptNumber: z.string().optional(),
  notes: z.string().optional(),
});

// Define the form values type
type MaintenanceFormValues = z.infer<typeof maintenanceFormSchema>;

// Define the component props
interface MaintenanceFormProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null;
  isCompleting?: boolean;
}

export function MaintenanceForm({ isOpen, onClose, asset, isCompleting = false }: MaintenanceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { recordMaintenance, completeMaintenance } = useAssetStore();

  // Return null if asset is null
  if (!asset) return null;

  // Initialize the form
  const form = useForm<MaintenanceFormValues>({
    resolver: zodResolver(maintenanceFormSchema) as any,
    defaultValues: {
      date: new Date(),
      description: '',
      cost: 0,
      provider: '',
      receiptNumber: '',
      notes: '',
    },
  });

  // Handle form submission
  const onSubmit = async (values: MaintenanceFormValues) => {
    setIsSubmitting(true);

    try {
      if (isCompleting) {
        // Complete maintenance
        await completeMaintenance(asset._id);
      } else {
        // Record maintenance
        await recordMaintenance(asset._id, {
          date: format(values.date, 'yyyy-MM-dd'),
          description: values.description,
          cost: values.cost,
          provider: values.provider,
          receiptNumber: values.receiptNumber,
          notes: values.notes,
        });
      }

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error submitting maintenance form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isCompleting ? 'Complete Maintenance' : 'Record Maintenance'}
          </DialogTitle>
          <DialogDescription>
            {isCompleting
              ? `Mark maintenance as completed for ${asset.name} (${asset.assetId}).`
              : `Record maintenance details for ${asset.name} (${asset.assetId}).`}
          </DialogDescription>
        </DialogHeader>

        {isCompleting ? (
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-4">
              This will change the asset status from "Under Maintenance" to "Active".
              Are you sure you want to complete the maintenance?
            </p>
            <DialogFooter>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  setIsSubmitting(true);
                  try {
                    await completeMaintenance(asset._id);
                    onClose();
                  } catch (error) {
                    console.error('Error completing maintenance:', error);
                  } finally {
                    setIsSubmitting(false);
                  }
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Completing...' : 'Complete Maintenance'}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Maintenance Date */}
              <FormField
                control={form.control as any}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Maintenance Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control as any}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter maintenance description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Cost */}
              <FormField
                control={form.control as any}
                name="cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost ({asset.currency})</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                {/* Provider */}
                <FormField
                  control={form.control as any}
                  name="provider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Provider</FormLabel>
                      <FormControl>
                        <Input placeholder="Service provider" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Receipt Number */}
                <FormField
                  control={form.control as any}
                  name="receiptNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Receipt Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Receipt number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Notes */}
              <FormField
                control={form.control as any}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional notes"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Saving...' : 'Record Maintenance'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

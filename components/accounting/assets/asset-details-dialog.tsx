'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  FileText,
  Clock,
  DollarSign,
  Calendar,
  Tag,
  MapPin,
  User,
  Building,
  Truck,
  Wrench,
  AlertTriangle,
  Edit,
  Trash
} from 'lucide-react';
import { Asset, useAssetStore } from '@/lib/store/useAssetStore';
import { AssetForm } from './asset-form';

// Status badge colors
const statusColors = {
  active: 'bg-green-100 text-green-800',
  'under-maintenance': 'bg-yellow-100 text-yellow-800',
  disposed: 'bg-red-100 text-red-800',
  lost: 'bg-gray-100 text-gray-800',
  'written-off': 'bg-purple-100 text-purple-800',
};

interface AssetDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null | undefined;
  onEdit: (asset: Asset) => void;
  onDelete: (id: string) => void;
}

export function AssetDetailsDialog({
  isOpen,
  onClose,
  asset,
  onEdit,
  onDelete
}: AssetDetailsDialogProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  if (!asset) return null;

  // Format dates
  const acquisitionDate = new Date(asset.acquisitionDate);
  const warrantyExpiryDate = asset.warrantyExpiryDate ? new Date(asset.warrantyExpiryDate) : null;
  const lastDepreciationDate = asset.lastDepreciationDate ? new Date(asset.lastDepreciationDate) : null;
  const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <span className="mr-2">{asset.name}</span>
              <Badge variant="outline" className={statusColors[asset.status]}>
                {asset.status.charAt(0).toUpperCase() + asset.status.slice(1).replace('-', ' ')}
              </Badge>
            </DialogTitle>
            <DialogDescription>
              Asset ID: {asset.assetId}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="details" className="mt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            </TabsList>

            {/* Details Tab */}
            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Category:</span>
                      <span className="text-sm">{asset.category}</span>
                    </div>
                    {asset.subCategory && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Sub-Category:</span>
                        <span className="text-sm">{asset.subCategory}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Location:</span>
                      <span className="text-sm">{asset.location}</span>
                    </div>
                    {asset.department && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Department:</span>
                        <span className="text-sm">{asset.department.name}</span>
                      </div>
                    )}
                    {asset.custodian && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Custodian:</span>
                        <span className="text-sm">{`${asset.custodian.firstName} ${asset.custodian.lastName}`}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Technical Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {asset.serialNumber && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Serial Number:</span>
                        <span className="text-sm">{asset.serialNumber}</span>
                      </div>
                    )}
                    {asset.model && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Model:</span>
                        <span className="text-sm">{asset.model}</span>
                      </div>
                    )}
                    {asset.manufacturer && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Manufacturer:</span>
                        <span className="text-sm">{asset.manufacturer}</span>
                      </div>
                    )}
                    {warrantyExpiryDate && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Warranty Expiry:</span>
                        <span className="text-sm">{format(warrantyExpiryDate, 'dd MMM yyyy')}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Acquisition Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Acquisition Date:</span>
                        <span className="text-sm">{format(acquisitionDate, 'dd MMM yyyy')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Acquisition Cost:</span>
                        <span className="text-sm">{asset.currency} {asset.acquisitionCost.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      {asset.supplier && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Supplier:</span>
                          <span className="text-sm">{asset.supplier}</span>
                        </div>
                      )}
                      {asset.invoiceNumber && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Invoice Number:</span>
                          <span className="text-sm">{asset.invoiceNumber}</span>
                        </div>
                      )}
                      {asset.purchaseOrderNumber && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">PO Number:</span>
                          <span className="text-sm">{asset.purchaseOrderNumber}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {asset.description && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{asset.description}</p>
                  </CardContent>
                </Card>
              )}

              {asset.notes && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{asset.notes}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Financial Tab */}
            <TabsContent value="financial" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Financial Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Acquisition Cost:</span>
                        <span className="text-sm">{asset.currency} {asset.acquisitionCost.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Current Book Value:</span>
                        <span className="text-sm">{asset.currency} {asset.currentBookValue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Accumulated Depreciation:</span>
                        <span className="text-sm">{asset.currency} {asset.accumulatedDepreciation.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Depreciation Method:</span>
                        <span className="text-sm">
                          {asset.depreciationMethod === 'straight-line' ? 'Straight Line' :
                           asset.depreciationMethod === 'reducing-balance' ? 'Reducing Balance' :
                           asset.depreciationMethod === 'units-of-production' ? 'Units of Production' : 'None'}
                        </span>
                      </div>
                      {asset.depreciationRate && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Depreciation Rate:</span>
                          <span className="text-sm">{asset.depreciationRate}%</span>
                        </div>
                      )}
                      {asset.usefulLifeYears && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Useful Life:</span>
                          <span className="text-sm">{asset.usefulLifeYears} years</span>
                        </div>
                      )}
                      {asset.salvageValue !== undefined && asset.salvageValue !== null && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Salvage Value:</span>
                          <span className="text-sm">{asset.currency} {asset.salvageValue.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {asset.status === 'disposed' && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Disposal Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Disposal Date:</span>
                          <span className="text-sm">{disposalDate ? format(disposalDate, 'dd MMM yyyy') : 'N/A'}</span>
                        </div>
                        {asset.disposalAmount !== undefined && (
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Disposal Amount:</span>
                            <span className="text-sm">{asset.currency} {asset.disposalAmount.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        {asset.disposalMethod && (
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Disposal Method:</span>
                            <span className="text-sm">{asset.disposalMethod}</span>
                          </div>
                        )}
                        {asset.disposalReason && (
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Disposal Reason:</span>
                            <span className="text-sm">{asset.disposalReason}</span>
                          </div>
                        )}
                        {asset.disposalReceiptNumber && (
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Receipt Number:</span>
                            <span className="text-sm">{asset.disposalReceiptNumber}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Depreciation Tab */}
            <TabsContent value="depreciation" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Depreciation History</CardTitle>
                  <CardDescription>
                    Record of all depreciation entries for this asset
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {asset.depreciationHistory && asset.depreciationHistory.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Accumulated</TableHead>
                          <TableHead>Book Value</TableHead>
                          <TableHead>Fiscal Year</TableHead>
                          <TableHead>Period</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {asset.depreciationHistory.map((record, index) => (
                          <TableRow key={index}>
                            <TableCell>{format(new Date(record.date), 'dd MMM yyyy')}</TableCell>
                            <TableCell>{asset.currency} {record.amount.toLocaleString()}</TableCell>
                            <TableCell>{asset.currency} {record.accumulatedDepreciation.toLocaleString()}</TableCell>
                            <TableCell>{asset.currency} {record.bookValue.toLocaleString()}</TableCell>
                            <TableCell>{record.fiscalYear}</TableCell>
                            <TableCell>{record.fiscalPeriod}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Calendar className="h-12 w-12 text-muted-foreground mb-2" />
                      <h3 className="text-lg font-medium">No depreciation records</h3>
                      <p className="text-sm text-muted-foreground">
                        This asset has no depreciation history yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Maintenance Tab */}
            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance History</CardTitle>
                  <CardDescription>
                    Record of all maintenance activities for this asset
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {asset.maintenanceHistory && asset.maintenanceHistory.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Cost</TableHead>
                          <TableHead>Provider</TableHead>
                          <TableHead>Receipt</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {asset.maintenanceHistory.map((record, index) => (
                          <TableRow key={index}>
                            <TableCell>{format(new Date(record.date), 'dd MMM yyyy')}</TableCell>
                            <TableCell>{record.description}</TableCell>
                            <TableCell>{asset.currency} {record.cost.toLocaleString()}</TableCell>
                            <TableCell>{record.provider || 'N/A'}</TableCell>
                            <TableCell>{record.receiptNumber || 'N/A'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Wrench className="h-12 w-12 text-muted-foreground mb-2" />
                      <h3 className="text-lg font-medium">No maintenance records</h3>
                      <p className="text-sm text-muted-foreground">
                        This asset has no maintenance history yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className="flex justify-between">
            <div>
              <Button
                variant="destructive"
                onClick={() => onDelete(asset._id)}
                className="mr-2"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete Asset
              </Button>
            </div>
            <div>
              <Button variant="outline" onClick={onClose} className="mr-2">
                Close
              </Button>
              <Button onClick={() => setIsEditDialogOpen(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Asset
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Asset Dialog */}
      {isEditDialogOpen && (
        <AssetForm
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          asset={asset}
          isEditing={true}
        />
      )}
    </>
  );
}

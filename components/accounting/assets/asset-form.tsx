'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Asset, useAssetStore } from '@/lib/store/useAssetStore';

// Define the form schema
const assetFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  subCategory: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  department: z.string().optional(),
  custodian: z.string().optional(),
  acquisitionDate: z.date({
    required_error: 'Acquisition date is required',
  }),
  acquisitionCost: z.coerce.number().min(0, 'Acquisition cost must be a positive number'),
  currency: z.string().default('MWK'),
  supplier: z.string().optional(),
  invoiceNumber: z.string().optional(),
  serialNumber: z.string().optional(),
  model: z.string().optional(),
  manufacturer: z.string().optional(),
  purchaseOrderNumber: z.string().optional(),
  warrantyExpiryDate: z.date().optional().nullable(),
  depreciationMethod: z.enum(['straight-line', 'reducing-balance', 'units-of-production', 'none']),
  depreciationRate: z.coerce.number().min(0).optional().nullable(),
  usefulLifeYears: z.coerce.number().min(0).optional().nullable(),
  salvageValue: z.coerce.number().min(0).optional().nullable(),
  status: z.enum(['active', 'under-maintenance', 'disposed', 'lost', 'written-off']).default('active'),
  notes: z.string().optional(),
});

// Define the form values type
type AssetFormValues = z.infer<typeof assetFormSchema>;

// Define the component props
interface AssetFormProps {
  isOpen: boolean;
  onClose: () => void;
  asset?: Asset | null | undefined;
  isEditing?: boolean;
}

export function AssetForm({ isOpen, onClose, asset, isEditing = false }: AssetFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createAsset, updateAsset } = useAssetStore();

  // Initialize the form
  const form = useForm<AssetFormValues>({
    resolver: zodResolver(assetFormSchema) as any,
    defaultValues: {
      name: '',
      description: '',
      category: '',
      subCategory: '',
      location: '',
      department: '',
      custodian: '',
      acquisitionDate: new Date(),
      acquisitionCost: 0,
      currency: 'MWK',
      supplier: '',
      invoiceNumber: '',
      serialNumber: '',
      model: '',
      manufacturer: '',
      purchaseOrderNumber: '',
      warrantyExpiryDate: null,
      depreciationMethod: 'straight-line',
      depreciationRate: null,
      usefulLifeYears: null,
      salvageValue: null,
      status: 'active',
      notes: '',
    },
  });

  // Set form values when editing an asset
  useEffect(() => {
    if (isEditing && asset) {
      form.reset({
        name: asset.name,
        description: asset.description || '',
        category: asset.category,
        subCategory: asset.subCategory || '',
        location: asset.location,
        department: asset.department?._id || '',
        custodian: asset.custodian?._id || '',
        acquisitionDate: new Date(asset.acquisitionDate),
        acquisitionCost: asset.acquisitionCost,
        currency: asset.currency,
        supplier: asset.supplier || '',
        invoiceNumber: asset.invoiceNumber || '',
        serialNumber: asset.serialNumber || '',
        model: asset.model || '',
        manufacturer: asset.manufacturer || '',
        purchaseOrderNumber: asset.purchaseOrderNumber || '',
        warrantyExpiryDate: asset.warrantyExpiryDate ? new Date(asset.warrantyExpiryDate) : null,
        depreciationMethod: asset.depreciationMethod,
        depreciationRate: asset.depreciationRate || null,
        usefulLifeYears: asset.usefulLifeYears || null,
        salvageValue: asset.salvageValue || null,
        status: asset.status,
        notes: asset.notes || '',
      });
    }
  }, [isEditing, asset, form]);

  // Handle form submission
  const onSubmit = async (values: AssetFormValues) => {
    setIsSubmitting(true);

    try {
      // Convert string IDs to object references and handle null values
      const processedValues = {
        ...values,
        warrantyExpiryDate: values.warrantyExpiryDate ? format(values.warrantyExpiryDate, 'yyyy-MM-dd') : undefined,
        acquisitionDate: format(values.acquisitionDate, 'yyyy-MM-dd'),
        department: values.department ? { _id: values.department, name: '' } : undefined,
        custodian: values.custodian ? { _id: values.custodian, firstName: '', lastName: '' } : undefined,
        depreciationRate: values.depreciationRate === null ? undefined : values.depreciationRate,
        usefulLifeYears: values.usefulLifeYears === null ? undefined : values.usefulLifeYears,
        salvageValue: values.salvageValue === null ? undefined : values.salvageValue,
      };

      if (isEditing && asset) {
        // Update existing asset
        await updateAsset(asset._id, processedValues);
      } else {
        // Create new asset
        await createAsset(processedValues);
      }

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error submitting asset form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Asset' : 'Add New Asset'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the details of the existing asset.'
              : 'Enter the details of the new asset to add it to the register.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Asset Name */}
              <FormField
                control={form.control as any}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Asset Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter asset name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category */}
              <FormField
                control={form.control as any}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="IT Equipment">IT Equipment</SelectItem>
                        <SelectItem value="Office Equipment">Office Equipment</SelectItem>
                        <SelectItem value="Vehicles">Vehicles</SelectItem>
                        <SelectItem value="Furniture">Furniture</SelectItem>
                        <SelectItem value="Real Estate">Real Estate</SelectItem>
                        <SelectItem value="Machinery">Machinery</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description */}
            <FormField
              control={form.control as any}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter asset description"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Location */}
              <FormField
                control={form.control as any}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Asset location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control as any}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="under-maintenance">Under Maintenance</SelectItem>
                        <SelectItem value="disposed">Disposed</SelectItem>
                        <SelectItem value="lost">Lost</SelectItem>
                        <SelectItem value="written-off">Written Off</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              {/* Acquisition Date */}
              <FormField
                control={form.control as any}
                name="acquisitionDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Acquisition Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Acquisition Cost */}
              <FormField
                control={form.control as any}
                name="acquisitionCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Acquisition Cost (MWK)</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              {/* Depreciation Method */}
              <FormField
                control={form.control as any}
                name="depreciationMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Depreciation Method</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="straight-line">Straight Line</SelectItem>
                        <SelectItem value="reducing-balance">Reducing Balance</SelectItem>
                        <SelectItem value="units-of-production">Units of Production</SelectItem>
                        <SelectItem value="none">None</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Useful Life Years */}
              <FormField
                control={form.control as any}
                name="usefulLifeYears"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Useful Life (Years)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        value={field.value === null ? '' : field.value}
                        onChange={(e) => {
                          const value = e.target.value === '' ? null : Number(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : isEditing ? 'Update Asset' : 'Add Asset'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

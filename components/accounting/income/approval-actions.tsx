"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  MessageSquare,
  AlertTriangle,
  History
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';

interface ApprovalHistoryItem {
  approver: {
    name: string;
    email: string;
    role: string;
  };
  status: 'approved' | 'rejected';
  date: Date;
  comments?: string;
  level: number;
}

interface ApprovalWorkflow {
  currentApprover?: {
    name: string;
    email: string;
    role: string;
  };
  currentLevel: number;
  status: 'pending' | 'approved' | 'rejected';
  approvalHistory: ApprovalHistoryItem[];
}

interface ApprovalActionsProps {
  incomeId: string;
  currentUserId: string;
  canApprove?: boolean;
  onApprovalComplete?: () => void;
}

export function ApprovalActions({
  incomeId,
  currentUserId,
  canApprove = true,
  onApprovalComplete
}: ApprovalActionsProps) {
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const [comments, setComments] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch approval history
  const {
    data: approvalData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['income-approval-history', incomeId],
    queryFn: async () => {
      const response = await fetch(`/api/accounting/income/${incomeId}/approval-history`);
      if (!response.ok) {
        throw new Error('Failed to fetch approval history');
      }
      const result = await response.json();
      return result.approvalHistory as ApprovalWorkflow;
    },
    enabled: !!incomeId,
  });

  // Process approval mutation
  const processApprovalMutation = useMutation({
    mutationFn: async ({ action, comments }: {
      action: 'approve' | 'reject';
      comments?: string;
    }) => {
      const response = await fetch('/api/accounting/income/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          incomeId,
          action,
          comments
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process approval');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Success",
        description: `Income ${variables.action}d successfully`,
      });
      
      // Refetch approval history
      refetch();
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['income-approvals'] });
      queryClient.invalidateQueries({ queryKey: ['income', incomeId] });
      
      // Close dialog and reset state
      setIsDialogOpen(false);
      setApprovalAction(null);
      setComments('');
      
      // Call completion callback
      onApprovalComplete?.();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleApprovalAction = (action: 'approve' | 'reject') => {
    setApprovalAction(action);
    setIsDialogOpen(true);
  };

  const handleSubmitApproval = () => {
    if (!approvalAction) return;

    processApprovalMutation.mutate({
      action: approvalAction,
      comments: comments.trim() || undefined
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      case 'pending':
        return <Badge variant="outline"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const isCurrentApprover = approvalData?.currentApprover && 
    approvalData.currentApprover.email === currentUserId; // Assuming currentUserId is email

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Approval Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Failed to load approval status</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!approvalData) {
    return null;
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Approval Status
          </CardTitle>
          <CardDescription>
            Current approval workflow status and history
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Current Status</p>
              <p className="text-sm text-muted-foreground">
                Level {approvalData.currentLevel} approval
              </p>
            </div>
            {getStatusBadge(approvalData.status)}
          </div>

          {/* Current Approver */}
          {approvalData.currentApprover && approvalData.status === 'pending' && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <User className="h-4 w-4 text-blue-600" />
                <p className="font-medium text-blue-800">Waiting for Approval</p>
              </div>
              <p className="text-sm text-blue-700">
                {approvalData.currentApprover.name} ({approvalData.currentApprover.role})
              </p>
              <p className="text-xs text-blue-600">{approvalData.currentApprover.email}</p>
            </div>
          )}

          {/* Approval Actions */}
          {canApprove && isCurrentApprover && approvalData.status === 'pending' && (
            <div className="space-y-3">
              <Separator />
              <div>
                <p className="font-medium mb-3">Take Action</p>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleApprovalAction('approve')}
                    disabled={processApprovalMutation.isPending}
                    className="flex-1"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleApprovalAction('reject')}
                    disabled={processApprovalMutation.isPending}
                    className="flex-1"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Approval History */}
          {approvalData.approvalHistory.length > 0 && (
            <div className="space-y-3">
              <Separator />
              <div>
                <p className="font-medium mb-3">Approval History</p>
                <div className="space-y-3">
                  {approvalData.approvalHistory.map((item, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="mt-1">
                        {item.status === 'approved' ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium">{item.approver.name}</p>
                          <Badge variant="outline" className="text-xs">
                            Level {item.level}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-1">
                          {item.approver.role} • {item.approver.email}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {format(new Date(item.date), 'MMM dd, yyyy HH:mm')}
                        </div>
                        {item.comments && (
                          <div className="mt-2 p-2 bg-muted rounded text-sm">
                            <div className="flex items-center gap-1 mb-1">
                              <MessageSquare className="h-3 w-3" />
                              <span className="font-medium">Comments:</span>
                            </div>
                            <p>{item.comments}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approve' ? 'Approve' : 'Reject'} Income Record
            </DialogTitle>
            <DialogDescription>
              {approvalAction === 'approve' 
                ? 'Are you sure you want to approve this income record?'
                : 'Please provide a reason for rejecting this income record.'
              }
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="comments">
                Comments {approvalAction === 'reject' && <span className="text-red-500">*</span>}
              </Label>
              <Textarea
                id="comments"
                placeholder={
                  approvalAction === 'approve' 
                    ? 'Optional comments...'
                    : 'Please explain why you are rejecting this income record...'
                }
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={processApprovalMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitApproval}
                disabled={
                  processApprovalMutation.isPending ||
                  (approvalAction === 'reject' && !comments.trim())
                }
                variant={approvalAction === 'approve' ? 'default' : 'destructive'}
              >
                {processApprovalMutation.isPending ? 'Processing...' : 
                 approvalAction === 'approve' ? 'Approve' : 'Reject'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

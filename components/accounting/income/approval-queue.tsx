"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  AlertCircle,
  DollarSign,
  Calendar,
  User,
  FileText
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

interface PendingApproval {
  _id: string;
  date: Date;
  source: string;
  amount: number;
  reference: string;
  description?: string;
  submittedAt: Date;
  createdBy: {
    name: string;
    email: string;
  };
  budget?: {
    name: string;
  };
  budgetCategory?: {
    name: string;
  };
  approvalWorkflow: {
    currentLevel: number;
    status: string;
  };
}

interface ApprovalQueueProps {
  title?: string;
  description?: string;
  showActions?: boolean;
}

export function ApprovalQueue({
  title = "Income Approval Queue",
  description = "Review and approve pending income records",
  showActions = true
}: ApprovalQueueProps) {
  const [selectedApproval, setSelectedApproval] = useState<PendingApproval | null>(null);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const [comments, setComments] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch pending approvals
  const {
    data: approvalsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['income-approvals'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/income/approve');
      if (!response.ok) {
        throw new Error('Failed to fetch pending approvals');
      }
      return response.json();
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Process approval mutation
  const processApprovalMutation = useMutation({
    mutationFn: async ({ incomeId, action, comments }: {
      incomeId: string;
      action: 'approve' | 'reject';
      comments?: string;
    }) => {
      const response = await fetch('/api/accounting/income/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          incomeId,
          action,
          comments
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process approval');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Success",
        description: `Income ${variables.action}d successfully`,
      });
      
      // Refetch approvals
      queryClient.invalidateQueries({ queryKey: ['income-approvals'] });
      
      // Close dialog and reset state
      setIsDialogOpen(false);
      setSelectedApproval(null);
      setApprovalAction(null);
      setComments('');
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleApprovalAction = (approval: PendingApproval, action: 'approve' | 'reject') => {
    setSelectedApproval(approval);
    setApprovalAction(action);
    setIsDialogOpen(true);
  };

  const handleSubmitApproval = () => {
    if (!selectedApproval || !approvalAction) return;

    processApprovalMutation.mutate({
      incomeId: selectedApproval._id,
      action: approvalAction,
      comments: comments.trim() || undefined
    });
  };

  const getSourceLabel = (source: string) => {
    const sourceLabels: Record<string, string> = {
      'government_subvention': 'Government Subvention',
      'registration_fees': 'Registration Fees',
      'licensing_fees': 'Licensing Fees',
      'donations': 'Donations',
      'other': 'Other'
    };
    return sourceLabels[source] || source;
  };

  const getPriorityBadge = (amount: number) => {
    if (amount >= 5000000) {
      return <Badge variant="destructive">High Priority</Badge>;
    } else if (amount >= 1000000) {
      return <Badge variant="default">Medium Priority</Badge>;
    } else {
      return <Badge variant="secondary">Low Priority</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-muted-foreground">Failed to load pending approvals</p>
            <Button onClick={() => refetch()} className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const approvals = approvalsData?.approvals || [];

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {title}
              </CardTitle>
              <CardDescription>{description}</CardDescription>
            </div>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {approvals.length} pending
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {approvals.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-muted-foreground">No pending approvals</p>
              <p className="text-sm text-muted-foreground mt-2">
                All income records are up to date
              </p>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Submitted By</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Level</TableHead>
                    {showActions && <TableHead className="text-right">Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {approvals.map((approval: PendingApproval) => (
                    <TableRow key={approval._id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {format(new Date(approval.date), 'MMM dd, yyyy')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          {getSourceLabel(approval.source)}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(approval.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          {approval.reference}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{approval.createdBy.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {format(new Date(approval.submittedAt), 'MMM dd, HH:mm')}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(approval.amount)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          Level {approval.approvalWorkflow.currentLevel}
                        </Badge>
                      </TableCell>
                      {showActions && (
                        <TableCell className="text-right">
                          <div className="flex items-center gap-2 justify-end">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleApprovalAction(approval, 'approve')}
                              disabled={processApprovalMutation.isPending}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleApprovalAction(approval, 'reject')}
                              disabled={processApprovalMutation.isPending}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approve' ? 'Approve' : 'Reject'} Income Record
            </DialogTitle>
            <DialogDescription>
              {selectedApproval && (
                <>
                  {approvalAction === 'approve' 
                    ? 'Are you sure you want to approve this income record?'
                    : 'Please provide a reason for rejecting this income record.'
                  }
                  <div className="mt-4 p-4 bg-muted rounded-lg">
                    <p><strong>Amount:</strong> {formatCurrency(selectedApproval.amount)}</p>
                    <p><strong>Source:</strong> {getSourceLabel(selectedApproval.source)}</p>
                    <p><strong>Reference:</strong> {selectedApproval.reference}</p>
                  </div>
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="comments">
                Comments {approvalAction === 'reject' && <span className="text-red-500">*</span>}
              </Label>
              <Textarea
                id="comments"
                placeholder={
                  approvalAction === 'approve' 
                    ? 'Optional comments...'
                    : 'Please explain why you are rejecting this income record...'
                }
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={processApprovalMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitApproval}
                disabled={
                  processApprovalMutation.isPending ||
                  (approvalAction === 'reject' && !comments.trim())
                }
                variant={approvalAction === 'approve' ? 'default' : 'destructive'}
              >
                {processApprovalMutation.isPending ? 'Processing...' : 
                 approvalAction === 'approve' ? 'Approve' : 'Reject'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

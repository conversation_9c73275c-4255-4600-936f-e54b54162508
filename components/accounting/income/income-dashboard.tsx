"use client"

import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  ComposedChart,
  ReferenceLine
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  BarChart4,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Zap
} from 'lucide-react';
import { useIncomeStats } from '@/lib/hooks/accounting/use-income-stats';
import { useIncome } from '@/lib/hooks/accounting/use-income';
import { useBudget } from '@/lib/hooks/accounting/use-budget';
import { useIsMobile } from '@/lib/hooks/use-responsive';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/accounting/shared/date-range-picker';
import { AdvancedForecastingDashboard } from '@/components/accounting/income/advanced-forecasting-dashboard';
import { IncomeEmptyState, IncomeChartEmptyState } from '@/components/accounting/income/income-empty-state';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Format compact currency for mobile
const formatCompactCurrency = (value: number) => {
  if (value >= **********) {
    return `MWK ${(value / **********).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`;
  }
  return formatCurrency(value);
};

// Calculate percentage change
const calculatePercentageChange = (current: number, previous: number) => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

interface IncomeDashboardProps {
  fiscalYear: string;
  dateRange?: DateRange;
  onDateRangeChange?: (range: DateRange | undefined) => void;
}

export function IncomeDashboard({
  fiscalYear,
  dateRange,
  onDateRangeChange
}: IncomeDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');
  const [comparisonPeriod, setComparisonPeriod] = useState<string>('previous');
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const isMobile = useIsMobile();

  // Fetch income statistics
  const { 
    data: incomeStats, 
    isLoading: isLoadingIncomeStats,
    error: incomeStatsError 
  } = useIncomeStats(fiscalYear, dateRange);

  // Fetch budget data for comparison
  const { activeBudgets } = useBudget();

  // Add timeout handling for loading states
  useEffect(() => {
    if (isLoadingIncomeStats) {
      const timeout = setTimeout(() => {
        console.warn('IncomeDashboard: Loading timeout reached');
        setLoadingTimeout(true);
      }, 15000); // 15 second timeout

      return () => {
        clearTimeout(timeout);
        setLoadingTimeout(false);
      };
    }
  }, [isLoadingIncomeStats]);

  // Retry function
  const handleRetry = () => {
    setLoadingTimeout(false);
    window.location.reload();
  };

  // Calculate KPIs and metrics
  const kpis = useMemo(() => {
    if (!incomeStats) return null;

    const currentTotal = incomeStats.totalIncome || 0;
    const budgetedTotal = incomeStats.totalBudgeted || 0;
    const previousTotal = incomeStats.previousPeriodIncome || 0;
    
    const budgetVariance = currentTotal - budgetedTotal;
    const budgetVariancePercentage = budgetedTotal > 0 ? (budgetVariance / budgetedTotal) * 100 : 0;
    const periodChange = calculatePercentageChange(currentTotal, previousTotal);
    
    const averageMonthlyIncome = incomeStats.monthlyIncome 
      ? incomeStats.monthlyIncome.reduce((sum, month) => sum + month.amount, 0) / incomeStats.monthlyIncome.length
      : 0;

    const incomeGrowthRate = incomeStats.monthlyIncome && incomeStats.monthlyIncome.length >= 2
      ? calculatePercentageChange(
          incomeStats.monthlyIncome[incomeStats.monthlyIncome.length - 1].amount,
          incomeStats.monthlyIncome[0].amount
        )
      : 0;

    return {
      totalIncome: currentTotal,
      budgetedIncome: budgetedTotal,
      budgetVariance,
      budgetVariancePercentage,
      periodChange,
      averageMonthlyIncome,
      incomeGrowthRate,
      totalSources: incomeStats.incomeBySource?.length || 0,
      largestSource: incomeStats.incomeBySource?.[0] || null,
      budgetUtilization: budgetedTotal > 0 ? (currentTotal / budgetedTotal) * 100 : 0
    };
  }, [incomeStats]);

  // Prepare trend data for charts
  const trendData = useMemo(() => {
    if (!incomeStats?.monthlyIncome) return [];
    
    return incomeStats.monthlyIncome.map((month, index) => ({
      ...month,
      cumulative: incomeStats.monthlyIncome
        .slice(0, index + 1)
        .reduce((sum, m) => sum + m.amount, 0),
      target: incomeStats.totalBudgeted ? incomeStats.totalBudgeted / 12 : 0,
      variance: month.amount - (incomeStats.totalBudgeted ? incomeStats.totalBudgeted / 12 : 0)
    }));
  }, [incomeStats]);

  if (loadingTimeout) {
    return (
      <IncomeEmptyState
        type="loading-timeout"
        title="Dashboard Loading Timeout"
        description="The income dashboard is taking longer than expected to load."
        onRetry={handleRetry}
      />
    );
  }

  if (isLoadingIncomeStats && !loadingTimeout) {
    return <IncomeDashboardSkeleton />;
  }

  if (incomeStatsError) {
    return (
      <IncomeEmptyState
        type="error"
        title="Failed to Load Dashboard"
        description="There was an error loading the income dashboard data."
        error={incomeStatsError.message}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Income Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive income analysis for {fiscalYear}
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <DateRangePicker
            date={dateRange}
            onDateChange={onDateRangeChange}
          />
          <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Monthly</SelectItem>
              <SelectItem value="quarter">Quarterly</SelectItem>
              <SelectItem value="year">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Income */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isMobile 
                ? formatCompactCurrency(kpis?.totalIncome || 0)
                : formatCurrency(kpis?.totalIncome || 0)
              }
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              {kpis && kpis.periodChange !== 0 && (
                <>
                  {kpis.periodChange > 0 ? (
                    <ArrowUpRight className="h-3 w-3 text-green-500" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 text-red-500" />
                  )}
                  <span className={kpis.periodChange > 0 ? 'text-green-500' : 'text-red-500'}>
                    {Math.abs(kpis.periodChange).toFixed(1)}%
                  </span>
                </>
              )}
              <span>vs previous period</span>
            </div>
          </CardContent>
        </Card>

        {/* Budget Performance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Performance</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {kpis?.budgetUtilization.toFixed(1)}%
            </div>
            <Progress 
              value={Math.min(kpis?.budgetUtilization || 0, 100)} 
              className="mt-2"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
              <span>
                {isMobile 
                  ? formatCompactCurrency(kpis?.totalIncome || 0)
                  : formatCurrency(kpis?.totalIncome || 0)
                }
              </span>
              <span>
                {isMobile 
                  ? formatCompactCurrency(kpis?.budgetedIncome || 0)
                  : formatCurrency(kpis?.budgetedIncome || 0)
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Income Sources */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Income Sources</CardTitle>
            <PieChartIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis?.totalSources}</div>
            <div className="text-xs text-muted-foreground">
              {kpis?.largestSource && (
                <>
                  Largest: {kpis.largestSource.source} 
                  ({((kpis.largestSource.amount / (kpis.totalIncome || 1)) * 100).toFixed(1)}%)
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Growth Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {kpis?.incomeGrowthRate.toFixed(1)}%
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              {kpis && kpis.incomeGrowthRate !== 0 && (
                <>
                  {kpis.incomeGrowthRate > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span>Year-to-date trend</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="sources">Sources</TabsTrigger>
          <TabsTrigger value="budget">Budget</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
        </TabsList>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Monthly Income Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Income Trend</CardTitle>
                <CardDescription>
                  Monthly income progression with budget targets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={trendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => formatCompactCurrency(value)} />
                      <Tooltip
                        formatter={(value: number) => [formatCurrency(value), '']}
                        labelFormatter={(label) => `Month: ${label}`}
                      />
                      <Legend />
                      <Bar dataKey="amount" fill="#0088FE" name="Actual Income" />
                      <Line
                        type="monotone"
                        dataKey="target"
                        stroke="#FF8042"
                        strokeDasharray="5 5"
                        name="Budget Target"
                      />
                      <Line
                        type="monotone"
                        dataKey="cumulative"
                        stroke="#00C49F"
                        name="Cumulative"
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Variance Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Variance</CardTitle>
                <CardDescription>
                  Monthly variance from budget targets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={trendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => formatCompactCurrency(value)} />
                      <Tooltip
                        formatter={(value: number) => [formatCurrency(value), '']}
                        labelFormatter={(label) => `Month: ${label}`}
                      />
                      <ReferenceLine y={0} stroke="#000" />
                      <Bar
                        dataKey="variance"
                        fill={(entry: any) => entry.variance >= 0 ? "#00C49F" : "#FF8042"}
                        name="Variance"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Sources Tab */}
        <TabsContent value="sources" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Income by Source Pie Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Income Distribution</CardTitle>
                <CardDescription>
                  Income breakdown by source
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={incomeStats?.incomeBySource || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ source, percent }) => `${source}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="amount"
                      >
                        {(incomeStats?.incomeBySource || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [formatCurrency(value), 'Amount']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Source Performance Table */}
            <Card>
              <CardHeader>
                <CardTitle>Source Performance</CardTitle>
                <CardDescription>
                  Detailed breakdown by income source
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {incomeStats?.incomeBySource?.map((source, index) => (
                    <div key={source.source} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <div>
                          <p className="font-medium">{source.source}</p>
                          <p className="text-sm text-muted-foreground">
                            {((source.amount / (kpis?.totalIncome || 1)) * 100).toFixed(1)}% of total
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {isMobile
                            ? formatCompactCurrency(source.amount)
                            : formatCurrency(source.amount)
                          }
                        </p>
                        <Badge variant={source.amount > 0 ? "default" : "secondary"}>
                          {source.count} transactions
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Budget Tab */}
        <TabsContent value="budget" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Budget vs Actual */}
            <Card>
              <CardHeader>
                <CardTitle>Budget vs Actual</CardTitle>
                <CardDescription>
                  Comparison of budgeted vs actual income
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Budget Utilization</span>
                    <span className="font-medium">{kpis?.budgetUtilization.toFixed(1)}%</span>
                  </div>
                  <Progress value={Math.min(kpis?.budgetUtilization || 0, 100)} />

                  <div className="grid grid-cols-2 gap-4 pt-4">
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Budgeted</p>
                      <p className="text-lg font-bold">
                        {isMobile
                          ? formatCompactCurrency(kpis?.budgetedIncome || 0)
                          : formatCurrency(kpis?.budgetedIncome || 0)
                        }
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Actual</p>
                      <p className="text-lg font-bold">
                        {isMobile
                          ? formatCompactCurrency(kpis?.totalIncome || 0)
                          : formatCurrency(kpis?.totalIncome || 0)
                        }
                      </p>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span>Variance</span>
                      <span className={`font-medium ${
                        (kpis?.budgetVariance || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {(kpis?.budgetVariance || 0) >= 0 ? '+' : ''}
                        {isMobile
                          ? formatCompactCurrency(kpis?.budgetVariance || 0)
                          : formatCurrency(kpis?.budgetVariance || 0)
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Budget Alerts */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Alerts</CardTitle>
                <CardDescription>
                  Important budget-related notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {kpis?.budgetUtilization && kpis.budgetUtilization > 100 && (
                    <div className="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-red-800">Budget Exceeded</p>
                        <p className="text-sm text-red-600">
                          Income has exceeded budget by {(kpis.budgetUtilization - 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  )}

                  {kpis?.budgetUtilization && kpis.budgetUtilization >= 80 && kpis.budgetUtilization <= 100 && (
                    <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-yellow-800">Approaching Budget</p>
                        <p className="text-sm text-yellow-600">
                          {(100 - kpis.budgetUtilization).toFixed(1)}% remaining in budget
                        </p>
                      </div>
                    </div>
                  )}

                  {kpis?.budgetUtilization && kpis.budgetUtilization < 50 && (
                    <div className="flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-blue-800">Budget On Track</p>
                        <p className="text-sm text-blue-600">
                          Income is well within budget limits
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Forecast Tab */}
        <TabsContent value="forecast" className="space-y-4">
          <AdvancedForecastingDashboard
            fiscalYear={fiscalYear}
            onForecastGenerated={(forecast) => {
              console.log('Forecast generated:', forecast);
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Loading skeleton component
function IncomeDashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-48" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-4 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

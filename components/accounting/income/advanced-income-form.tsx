// components/accounting/income/advanced-income-form.tsx
"use client"

import React, { useState, useEffect } from "react"
import { useToast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, CalendarIcon, AlertCircle, CheckCircle } from 'lucide-react'
import { format } from 'date-fns'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { useIncomeStore, Income } from '@/lib/stores/enhanced-income-store'
import { RealTimeBudgetTracker } from '@/components/accounting/shared/real-time-budget-tracker'
import { BudgetVarianceAlert } from '@/components/accounting/shared/budget-variance-alert'
import { TransactionBudgetLink } from '@/components/accounting/shared/transaction-budget-link'

// Form data interface
interface AdvancedIncomeFormData {
  date: Date
  source: 'government_subvention' | 'registration_fees' | 'licensing_fees' | 'donations' | 'other'
  subSource?: string
  amount: string
  reference: string
  description?: string
  fiscalYear: string
  status: 'draft' | 'pending_approval' | 'approved' | 'received' | 'rejected' | 'cancelled'
  paymentMethod?: string
  bankAccount?: string
  budget: string
  budgetName?: string
  budgetCategory: string
  budgetCategoryName?: string
  budgetSubcategory?: string
  budgetSubcategoryName?: string
  appliedToBudget: boolean
  notes?: string
}

interface AdvancedIncomeFormProps {
  income?: Income | null
  onSubmit: (data: any) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function AdvancedIncomeForm({
  income,
  onSubmit,
  onCancel,
  isLoading = false
}: AdvancedIncomeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)
  const [initError, setInitError] = useState<string | null>(null)
  const [enableAdvancedFeatures, setEnableAdvancedFeatures] = useState(false)

  // Use toast hook
  const { toast } = useToast()

  // Use enhanced income store
  const {
    // Form data
    budgets,
    budgetCategories,
    budgetSubcategories,
    incomeSources,

    // Loading states
    isLoadingBudgets,
    isLoadingCategories,
    isFormDataReady,

    // Actions
    fetchBudgetCategories,
    fetchBudgetSubcategories,
    initializeFormData,
    getCurrentFiscalYear,
    getActiveFiscalYears,
    getActiveBankAccounts,
    getActivePaymentMethods,

    // Budget state
    selectedBudget,
    setSelectedBudget,
  } = useIncomeStore()

  // Form state using simple useState
  const [formData, setFormData] = useState<AdvancedIncomeFormData>(() => {
    if (income) {
      return {
        date: new Date(income.date),
        source: income.source,
        subSource: income.subSource || '',
        amount: income.amount.toString(),
        reference: income.reference,
        description: income.description || '',
        fiscalYear: income.fiscalYear,
        status: income.status,
        paymentMethod: income.paymentMethod || '',
        bankAccount: income.bankAccount || '',
        budget: income.budget || '',
        budgetName: income.budgetName || '',
        budgetCategory: income.budgetCategory || '',
        budgetCategoryName: income.budgetCategoryName || '',
        budgetSubcategory: income.budgetSubcategory || '',
        budgetSubcategoryName: income.budgetSubcategoryName || '',
        appliedToBudget: income.appliedToBudget !== undefined ? income.appliedToBudget : true,
        notes: income.notes || '',
      }
    } else {
      return {
        date: new Date(),
        source: 'government_subvention',
        subSource: '',
        amount: '',
        reference: '',
        description: '',
        fiscalYear: getCurrentFiscalYear(),
        status: 'draft',
        paymentMethod: '',
        bankAccount: '',
        budget: '',
        budgetName: '',
        budgetCategory: '',
        budgetCategoryName: '',
        budgetSubcategory: '',
        budgetSubcategoryName: '',
        appliedToBudget: true,
        notes: '',
      }
    }
  })

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Safe initialization effect
  useEffect(() => {
    const initializeForm = async () => {
      try {
        setIsInitializing(true)
        setInitError(null)

        console.log('Initializing advanced income form...')
        
        // Initialize all form data
        await initializeFormData()

        // Enable advanced features after basic form is ready
        setTimeout(() => {
          setEnableAdvancedFeatures(true)
        }, 300)

      } catch (error) {
        console.error('Form initialization error:', error)
        setInitError('Failed to initialize form. Please try again.')
      } finally {
        setIsInitializing(false)
      }
    }

    initializeForm()
  }, [initializeFormData])

  // Update form field
  const updateField = (field: keyof AdvancedIncomeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Budget selection handlers
  const handleBudgetChange = async (budgetId: string, budgetName: string) => {
    const budget = budgets.find(b => (b._id || b.id) === budgetId)
    setSelectedBudget(budget || null)
    
    updateField('budget', budgetId)
    updateField('budgetName', budgetName)
    
    // Reset category and subcategory
    updateField('budgetCategory', '')
    updateField('budgetCategoryName', '')
    updateField('budgetSubcategory', '')
    updateField('budgetSubcategoryName', '')

    if (budgetId) {
      await fetchBudgetCategories(budgetId)
    }
  }

  const handleCategoryChange = async (categoryId: string, categoryName: string) => {
    updateField('budgetCategory', categoryId)
    updateField('budgetCategoryName', categoryName)
    
    // Reset subcategory
    updateField('budgetSubcategory', '')
    updateField('budgetSubcategoryName', '')

    if (categoryId) {
      await fetchBudgetSubcategories(categoryId)
    }
  }

  const handleSubcategoryChange = (subcategoryId: string, subcategoryName: string) => {
    updateField('budgetSubcategory', subcategoryId)
    updateField('budgetSubcategoryName', subcategoryName)
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.date) newErrors.date = 'Date is required'
    if (!formData.source) newErrors.source = 'Source is required'
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0'
    if (!formData.reference.trim()) newErrors.reference = 'Reference is required'
    if (!formData.fiscalYear) newErrors.fiscalYear = 'Fiscal year is required'
    if (!formData.budget) newErrors.budget = 'Budget is required'
    if (!formData.budgetCategory) newErrors.budgetCategory = 'Budget category is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isSubmitting) return

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Process form data
      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
      }

      console.log('Submitting advanced income form:', processedValues)
      await onSubmit(processedValues)

      toast({
        title: income ? 'Income Updated' : 'Income Created',
        description: income ? 'The income has been updated successfully' : 'The income has been created successfully',
      })

    } catch (error: unknown) {
      console.error('Error saving income:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Loading Advanced Income Form...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Initializing form data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error fallback if initialization failed
  if (initError) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-destructive">Form Error</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{initError}</AlertDescription>
          </Alert>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="mt-4"
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{income ? 'Edit Income' : 'Record New Income'}</CardTitle>
          <Badge variant={enableAdvancedFeatures ? "default" : "secondary"}>
            {enableAdvancedFeatures ? "Advanced Features Enabled" : "Loading Features..."}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <Separator />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date Field */}
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full pl-3 text-left font-normal"
                      disabled={!isFormDataReady}
                    >
                      {formData.date ? (
                        format(formData.date, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && updateField('date', date)}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
              </div>

              {/* Fiscal Year Field */}
              <div className="space-y-2">
                <Label htmlFor="fiscalYear">Fiscal Year *</Label>
                <Select
                  value={formData.fiscalYear}
                  onValueChange={(value) => updateField('fiscalYear', value)}
                  disabled={!isFormDataReady}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {getActiveFiscalYears().length > 0 ? (
                      getActiveFiscalYears().map(fiscalYear => (
                        <SelectItem key={fiscalYear.year} value={fiscalYear.year}>
                          {fiscalYear.year} {fiscalYear.isCurrent ? '(Current)' : fiscalYear.isActive ? '(Active)' : ''}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="2024" disabled>
                        No fiscal years available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.fiscalYear && <p className="text-sm text-destructive">{errors.fiscalYear}</p>}
              </div>
            </div>

            {/* Income Source and Sub-source */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="source">Income Source *</Label>
                <Select
                  value={formData.source}
                  onValueChange={(value) => updateField('source', value)}
                  disabled={!isFormDataReady}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select income source" />
                  </SelectTrigger>
                  <SelectContent>
                    {incomeSources.map(source => (
                      <SelectItem key={source.value} value={source.value}>
                        {source.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.source && <p className="text-sm text-destructive">{errors.source}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="subSource">Sub-source</Label>
                <Input
                  id="subSource"
                  placeholder="Enter sub-source (optional)"
                  value={formData.subSource || ''}
                  onChange={(e) => updateField('subSource', e.target.value)}
                  disabled={!isFormDataReady}
                />
              </div>
            </div>

            {/* Amount and Reference */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (MWK) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount"
                  value={formData.amount}
                  onChange={(e) => updateField('amount', e.target.value)}
                  disabled={!isFormDataReady}
                />
                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="reference">Reference *</Label>
                <Input
                  id="reference"
                  placeholder="Enter reference number"
                  value={formData.reference}
                  onChange={(e) => updateField('reference', e.target.value)}
                  disabled={!isFormDataReady}
                />
                {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
              </div>
            </div>
          </div>

          {/* Budget Integration Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Budget Integration</h3>
            <Separator />

            {!enableAdvancedFeatures && (
              <Alert>
                <Loader2 className="h-4 w-4 animate-spin" />
                <AlertDescription>
                  Loading budget integration features...
                </AlertDescription>
              </Alert>
            )}

            {/* Budget Selection */}
            <div className="space-y-2">
              <Label htmlFor="budget">Budget *</Label>
              {isLoadingBudgets ? (
                <div className="flex items-center space-x-2 h-10 px-3 py-2 border border-input bg-background rounded-md">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading budgets...</span>
                </div>
              ) : (
                <Select
                  value={formData.budget}
                  onValueChange={(value) => {
                    const budget = budgets.find(b => (b._id || b.id) === value)
                    handleBudgetChange(value, budget?.name || '')
                  }}
                  disabled={!enableAdvancedFeatures}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgets.length > 0 ? (
                      budgets.map(budget => (
                        <SelectItem key={budget._id || budget.id} value={budget._id || budget.id}>
                          {budget.name} ({budget.fiscalYear})
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-budgets" disabled>
                        No budgets available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              )}
              {errors.budget && <p className="text-sm text-destructive">{errors.budget}</p>}
            </div>

            {/* Budget Category */}
            {selectedBudget && (
              <div className="space-y-2">
                <Label htmlFor="budgetCategory">Budget Category *</Label>
                {isLoadingCategories ? (
                  <div className="flex items-center space-x-2 h-10 px-3 py-2 border border-input bg-background rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Loading categories...</span>
                  </div>
                ) : (
                  <Select
                    value={formData.budgetCategory}
                    onValueChange={(value) => {
                      const category = budgetCategories.find(c => (c._id || c.id) === value)
                      handleCategoryChange(value, category?.name || '')
                    }}
                    disabled={!enableAdvancedFeatures}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget category" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetCategories.length > 0 ? (
                        budgetCategories.map(category => (
                          <SelectItem key={category._id || category.id} value={category._id || category.id}>
                            {category.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                )}
                {errors.budgetCategory && <p className="text-sm text-destructive">{errors.budgetCategory}</p>}
              </div>
            )}

            {/* Budget Subcategory */}
            {budgetSubcategories.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="budgetSubcategory">Budget Subcategory (Optional)</Label>
                <Select
                  value={formData.budgetSubcategory || ''}
                  onValueChange={(value) => {
                    const subcategory = budgetSubcategories.find(s => (s._id || s.id) === value)
                    handleSubcategoryChange(value, subcategory?.name || '')
                  }}
                  disabled={!enableAdvancedFeatures}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select subcategory (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetSubcategories.map(subcategory => (
                      <SelectItem key={subcategory._id || subcategory.id} value={subcategory._id || subcategory.id}>
                        {subcategory.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Status and Payment Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Status and Payment Information</h3>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => updateField('status', value)}
                  disabled={!isFormDataReady}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Submit for Approval</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="received">Received</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && <p className="text-sm text-destructive">{errors.status}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  value={formData.paymentMethod || ''}
                  onValueChange={(value) => updateField('paymentMethod', value)}
                  disabled={!isFormDataReady}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {getActivePaymentMethods().map(method => (
                      <SelectItem key={method.id} value={method.id}>
                        {method.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bankAccount">Bank Account</Label>
              <Select
                value={formData.bankAccount || ''}
                onValueChange={(value) => updateField('bankAccount', value)}
                disabled={!isFormDataReady}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select bank account" />
                </SelectTrigger>
                <SelectContent>
                  {getActiveBankAccounts().map(account => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} - {account.accountNumber}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Additional Information</h3>
            <Separator />

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter description (optional)"
                className="min-h-[80px]"
                value={formData.description || ''}
                onChange={(e) => updateField('description', e.target.value)}
                disabled={!isFormDataReady}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Enter additional notes (optional)"
                className="min-h-[80px]"
                value={formData.notes || ''}
                onChange={(e) => updateField('notes', e.target.value)}
                disabled={!isFormDataReady}
              />
            </div>
          </div>

          {/* Budget Impact Preview */}
          {enableAdvancedFeatures && formData.amount && formData.budget && formData.budgetCategory && parseFloat(formData.amount) > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Budget Impact Preview</h3>
              <Separator />

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  This income of <strong>MWK {parseFloat(formData.amount).toLocaleString()}</strong> will be added to:
                  <br />
                  <strong>Budget:</strong> {formData.budgetName || 'Selected Budget'}
                  <br />
                  <strong>Category:</strong> {formData.budgetCategoryName || 'Selected Category'}
                  {formData.budgetSubcategoryName && (
                    <>
                      <br />
                      <strong>Subcategory:</strong> {formData.budgetSubcategoryName}
                    </>
                  )}
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Phase 2: Enhanced Budget Integration */}
          {enableAdvancedFeatures && formData.budget && formData.budgetCategory && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Real-time Budget Integration</h3>
              <Separator />

              {/* Real-time Budget Tracker */}
              <div className="space-y-4">
                <h4 className="text-md font-medium">Budget Overview</h4>
                <RealTimeBudgetTracker
                  budgetId={formData.budget}
                  refreshInterval={30000}
                  showCategories={true}
                  showTrends={true}
                  compact={false}
                  className="border-2 border-blue-200"
                />
              </div>

              {/* Transaction Budget Link */}
              {income && (
                <div className="space-y-4">
                  <h4 className="text-md font-medium">Transaction Budget Link</h4>
                  <TransactionBudgetLink
                    transactionId={income._id || income.id || ''}
                    transactionType="income"
                    budgetId={formData.budget}
                    categoryId={formData.budgetCategory}
                    subcategoryId={formData.budgetSubcategory}
                    amount={parseFloat(formData.amount) || 0}
                    showImpactPreview={true}
                    allowUnlink={true}
                    autoRefresh={true}
                    className="border border-green-200"
                  />
                </div>
              )}

              {/* Budget Impact Preview for New Transactions */}
              {!income && formData.amount && parseFloat(formData.amount) > 0 && (
                <div className="space-y-4">
                  <h4 className="text-md font-medium">Budget Impact Preview</h4>
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <p className="font-medium text-green-800">
                          Projected Budget Impact
                        </p>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Income Amount:</p>
                            <p className="font-semibold">MWK {parseFloat(formData.amount).toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Budget Category:</p>
                            <p className="font-semibold">{formData.budgetCategoryName}</p>
                          </div>
                        </div>
                        <p className="text-xs text-green-700">
                          This income will be automatically linked to the selected budget category upon creation.
                        </p>
                      </div>
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </div>
          )}
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || !isFormDataReady}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {income ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            income ? 'Update Income' : 'Create Income'
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

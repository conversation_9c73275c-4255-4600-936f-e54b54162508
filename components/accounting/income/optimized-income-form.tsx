'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Form data interface
interface OptimizedIncomeFormData {
  date: Date;
  source: string;
  subSource?: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: string;
  paymentMethod?: string;
  bankAccount?: string;
  budget: string;
  budgetCategory: string;
  notes?: string;
}

interface OptimizedIncomeFormProps {
  income?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data to prevent API calls during form interaction
const INCOME_SOURCES = [
  { value: 'government_subvention', label: 'Government Subvention' },
  { value: 'registration_fees', label: 'Registration Fees' },
  { value: 'licensing_fees', label: 'Licensing Fees' },
  { value: 'donations', label: 'Donations' },
  { value: 'other', label: 'Other' },
];

const FISCAL_YEARS = [
  { value: '2024-2025', label: '2024-2025 (Current)' },
  { value: '2023-2024', label: '2023-2024' },
  { value: '2025-2026', label: '2025-2026' },
];

const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft' },
  { value: 'pending_approval', label: 'Pending Approval' },
  { value: 'approved', label: 'Approved' },
  { value: 'received', label: 'Received' },
];

export function OptimizedIncomeForm({
  income,
  onSubmit,
  onCancel,
  isLoading = false
}: OptimizedIncomeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [formData, setFormData] = useState<OptimizedIncomeFormData>(() => ({
    date: income?.date ? new Date(income.date) : new Date(),
    source: income?.source || 'government_subvention',
    subSource: income?.subSource || '',
    amount: income?.amount?.toString() || '',
    reference: income?.reference || '',
    description: income?.description || '',
    fiscalYear: income?.fiscalYear || '2024-2025',
    status: income?.status || 'draft',
    paymentMethod: income?.paymentMethod || '',
    bankAccount: income?.bankAccount || '',
    budget: income?.budget || '',
    budgetCategory: income?.budgetCategory || '',
    notes: income?.notes || '',
  }));

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Cached form options
  const [formOptions, setFormOptions] = useState({
    budgets: [] as any[],
    budgetCategories: [] as any[],
    bankAccounts: [] as any[],
    paymentMethods: [] as any[],
  });

  const { toast } = useToast();

  // Debounced form data to prevent excessive re-renders
  const debouncedFormData = useDebounce(formData, 300);

  // Initialize form data once
  useEffect(() => {
    const initializeForm = async () => {
      try {
        setIsInitializing(true);
        
        // Fetch all required data in parallel
        const [budgetsRes, bankAccountsRes, paymentMethodsRes] = await Promise.all([
          fetch('/api/accounting/budget?status=active').then(r => r.ok ? r.json() : { budgets: [] }),
          fetch('/api/accounting/bank-accounts').then(r => r.ok ? r.json() : { bankAccounts: [] }),
          fetch('/api/accounting/payment-methods').then(r => r.ok ? r.json() : { paymentMethods: [] }),
        ]);

        setFormOptions({
          budgets: budgetsRes.budgets || [],
          budgetCategories: [],
          bankAccounts: bankAccountsRes.bankAccounts || [],
          paymentMethods: paymentMethodsRes.paymentMethods || [],
        });

      } catch (error) {
        console.error('Form initialization error:', error);
        toast({
          title: 'Initialization Error',
          description: 'Failed to load form data. Some features may be limited.',
          variant: 'destructive',
        });
      } finally {
        setIsInitializing(false);
      }
    };

    initializeForm();
  }, [toast]);

  // Memoized update function to prevent re-renders
  const updateField = useCallback((field: keyof OptimizedIncomeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    setErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });
  }, []);

  // Handle budget change and fetch categories
  const handleBudgetChange = useCallback(async (budgetId: string) => {
    updateField('budget', budgetId);
    updateField('budgetCategory', ''); // Reset category

    if (budgetId) {
      try {
        const response = await fetch(`/api/accounting/budget/${budgetId}`);
        if (response.ok) {
          const budget = await response.json();
          const incomeCategories = budget.categories?.filter((cat: any) => cat.type === 'income') || [];
          setFormOptions(prev => ({
            ...prev,
            budgetCategories: incomeCategories
          }));
        }
      } catch (error) {
        console.error('Error fetching budget categories:', error);
      }
    } else {
      setFormOptions(prev => ({
        ...prev,
        budgetCategories: []
      }));
    }
  }, [updateField]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.source) newErrors.source = 'Source is required';
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0';
    if (!formData.reference.trim()) newErrors.reference = 'Reference is required';
    if (!formData.fiscalYear) newErrors.fiscalYear = 'Fiscal year is required';
    if (!formData.budget) newErrors.budget = 'Budget is required';
    if (!formData.budgetCategory) newErrors.budgetCategory = 'Budget category is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Process form data
      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
      };

      await onSubmit(processedValues);

      toast({
        title: income ? 'Income Updated' : 'Income Created',
        description: income ? 'The income has been updated successfully' : 'The income has been created successfully',
      });

    } catch (error: unknown) {
      console.error('Error saving income:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, isSubmitting, validateForm, onSubmit, income, toast]);

  // Memoized form sections to prevent unnecessary re-renders
  const basicInfoSection = useMemo(() => (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Basic Information</h3>
      <Separator />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Date Field */}
        <div className="space-y-2">
          <Label htmlFor="date">Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full pl-3 text-left font-normal"
                disabled={isInitializing}
              >
                {formData.date ? (
                  format(formData.date, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.date}
                onSelect={(date) => date && updateField('date', date)}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
        </div>

        {/* Fiscal Year Field */}
        <div className="space-y-2">
          <Label htmlFor="fiscalYear">Fiscal Year *</Label>
          <Select
            value={formData.fiscalYear}
            onValueChange={(value) => updateField('fiscalYear', value)}
            disabled={isInitializing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {FISCAL_YEARS.map(year => (
                <SelectItem key={year.value} value={year.value}>
                  {year.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.fiscalYear && <p className="text-sm text-destructive">{errors.fiscalYear}</p>}
        </div>
      </div>

      {/* Income Source and Amount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="source">Income Source *</Label>
          <Select
            value={formData.source}
            onValueChange={(value) => updateField('source', value)}
            disabled={isInitializing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select income source" />
            </SelectTrigger>
            <SelectContent>
              {INCOME_SOURCES.map(source => (
                <SelectItem key={source.value} value={source.value}>
                  {source.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.source && <p className="text-sm text-destructive">{errors.source}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="amount">Amount (MWK) *</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            placeholder="Enter amount"
            value={formData.amount}
            onChange={(e) => updateField('amount', e.target.value)}
            disabled={isInitializing}
          />
          {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
        </div>
      </div>

      {/* Reference and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="reference">Reference *</Label>
          <Input
            id="reference"
            placeholder="Enter reference number"
            value={formData.reference}
            onChange={(e) => updateField('reference', e.target.value)}
            disabled={isInitializing}
          />
          {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => updateField('status', value)}
            disabled={isInitializing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {STATUS_OPTIONS.map(status => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  ), [formData, errors, isInitializing, updateField]);

  const budgetSection = useMemo(() => (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Budget Integration</h3>
      <Separator />

      {/* Budget Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="budget">Budget *</Label>
          <Select
            value={formData.budget}
            onValueChange={handleBudgetChange}
            disabled={isInitializing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select budget" />
            </SelectTrigger>
            <SelectContent>
              {formOptions.budgets.map(budget => (
                <SelectItem key={budget._id || budget.id} value={budget._id || budget.id}>
                  {budget.name} ({budget.fiscalYear})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.budget && <p className="text-sm text-destructive">{errors.budget}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="budgetCategory">Budget Category *</Label>
          <Select
            value={formData.budgetCategory}
            onValueChange={(value) => updateField('budgetCategory', value)}
            disabled={isInitializing || !formData.budget}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {formOptions.budgetCategories.map(category => (
                <SelectItem key={category._id || category.id} value={category._id || category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.budgetCategory && <p className="text-sm text-destructive">{errors.budgetCategory}</p>}
        </div>
      </div>
    </div>
  ), [formData.budget, formData.budgetCategory, errors, isInitializing, formOptions.budgets, formOptions.budgetCategories, handleBudgetChange, updateField]);

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Loading Income Form...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Initializing form data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{income ? 'Edit Income' : 'Record New Income'}</CardTitle>
          <Badge variant="default">Optimized Form</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {basicInfoSection}
          {budgetSection}

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              value={formData.description || ''}
              onChange={(e) => updateField('description', e.target.value)}
              disabled={isInitializing}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || isInitializing}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {income ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            income ? 'Update Income' : 'Create Income'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

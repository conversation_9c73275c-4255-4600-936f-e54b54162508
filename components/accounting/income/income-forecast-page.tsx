"use client"

import React, { useState } from 'react';
import { DashboardShell } from "@/components/dashboard-shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Brain, 
  TrendingUp,
  BarChart4,
  Lightbulb,
  Target,
  Activity,
  Zap
} from "lucide-react";
import Link from "next/link";
import { AdvancedForecastingDashboard } from '@/components/accounting/income/advanced-forecasting-dashboard';
import { AdvancedForecastResult } from '@/lib/services/accounting/advanced-forecasting-service';
import { useBudget } from '@/lib/hooks/accounting/use-budget';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

export function IncomeForecastPage() {
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string>('2024');
  const [selectedBudgetId, setSelectedBudgetId] = useState<string>('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [currentForecast, setCurrentForecast] = useState<AdvancedForecastResult | null>(null);

  // Fetch budgets and categories
  const { activeBudgets, isLoading: isLoadingBudgets, getBudgetCategories } = useBudget();
  const { data: categories, isLoading: isLoadingCategories } = getBudgetCategories(selectedBudgetId, 'income');

  // Generate fiscal year options
  const fiscalYears = Array.from({ length: 5 }, (_, i) => {
    const year = new Date().getFullYear() - 2 + i;
    return year.toString();
  });

  const handleForecastGenerated = (forecast: AdvancedForecastResult) => {
    setCurrentForecast(forecast);
  };

  const getForecastSummary = () => {
    if (!currentForecast) return null;

    const totalForecast = currentForecast.forecast.reduce((sum, point) => sum + point.forecasted, 0);
    const optimisticScenario = currentForecast.scenarios.find(s => s.scenario === 'optimistic');
    const pessimisticScenario = currentForecast.scenarios.find(s => s.scenario === 'pessimistic');

    return {
      totalForecast,
      optimistic: optimisticScenario?.totalProjected || 0,
      pessimistic: pessimisticScenario?.totalProjected || 0,
      modelAccuracy: currentForecast.model.accuracy.r2,
      modelType: currentForecast.model.type,
      dataPoints: currentForecast.metadata.dataPoints,
      recommendations: currentForecast.recommendations.length
    };
  };

  const summary = getForecastSummary();

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Advanced Income Forecasting</h1>
            <p className="text-muted-foreground">
              AI-powered income predictions with scenario analysis and strategic insights
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/income/overview">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Income</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Configuration Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Forecast Scope
            </CardTitle>
            <CardDescription>
              Select the scope for your income forecast analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <label className="text-sm font-medium">Fiscal Year</label>
                <Select value={selectedFiscalYear} onValueChange={setSelectedFiscalYear}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {fiscalYears.map(year => (
                      <SelectItem key={year} value={year}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Budget (Optional)</label>
                <Select 
                  value={selectedBudgetId} 
                  onValueChange={(value) => {
                    setSelectedBudgetId(value);
                    setSelectedCategoryId(''); // Reset category when budget changes
                  }}
                  disabled={isLoadingBudgets}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All budgets" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Budgets</SelectItem>
                    {activeBudgets?.map(budget => (
                      <SelectItem key={budget._id} value={budget._id}>
                        {budget.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Category (Optional)</label>
                <Select 
                  value={selectedCategoryId} 
                  onValueChange={setSelectedCategoryId}
                  disabled={isLoadingCategories || !selectedBudgetId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    {categories?.map(category => (
                      <SelectItem key={category._id} value={category._id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Forecast Summary Cards */}
        {summary && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Forecast Total</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(summary.totalForecast)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Next {currentForecast?.metadata.forecastHorizon} months
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Model Accuracy</CardTitle>
                <BarChart4 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(summary.modelAccuracy * 100).toFixed(1)}%
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <Badge variant="outline" className="text-xs">
                    {summary.modelType.toUpperCase()}
                  </Badge>
                  <span>R² Score</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Scenario Range</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">
                  {formatCurrency(summary.pessimistic)} - {formatCurrency(summary.optimistic)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Pessimistic to Optimistic
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI Insights</CardTitle>
                <Lightbulb className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary.recommendations}
                </div>
                <p className="text-xs text-muted-foreground">
                  Recommendations available
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Advanced Forecasting Dashboard */}
        <AdvancedForecastingDashboard
          fiscalYear={selectedFiscalYear}
          budgetId={selectedBudgetId || undefined}
          categoryId={selectedCategoryId || undefined}
          onForecastGenerated={handleForecastGenerated}
        />

        {/* Getting Started Guide */}
        {!currentForecast && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Getting Started with AI Forecasting
              </CardTitle>
              <CardDescription>
                Learn how to use advanced forecasting features effectively
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Forecasting Models</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span><strong>Auto:</strong> AI selects the best model automatically</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      <span><strong>Linear:</strong> Simple trend-based forecasting</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      <span><strong>Exponential:</strong> Growth/decay pattern forecasting</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <BarChart4 className="h-4 w-4" />
                      <span><strong>Seasonal:</strong> Pattern-based with seasonality</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Key Features</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div>• <strong>Scenario Analysis:</strong> Optimistic, realistic, and pessimistic projections</div>
                    <div>• <strong>Anomaly Detection:</strong> Identifies unusual patterns in historical data</div>
                    <div>• <strong>Confidence Intervals:</strong> Statistical uncertainty ranges</div>
                    <div>• <strong>AI Recommendations:</strong> Actionable insights and alerts</div>
                    <div>• <strong>Budget Impact:</strong> Automatic budget variance analysis</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardShell>
  );
}

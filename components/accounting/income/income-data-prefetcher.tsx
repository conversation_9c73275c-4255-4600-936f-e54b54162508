// components/accounting/income/income-data-prefetcher.tsx
'use client'

import { useEffect, useState } from 'react'
import { create } from 'zustand'
import { toast } from '@/components/ui/use-toast'

// Define the income prefetcher store
interface IncomeDataPrefetcherState {
  isLoading: boolean
  isReady: boolean
  error: string | null
  retryCount: number
  budgets: Array<{ _id: string, name: string, fiscalYear: string }>
  budgetCategories: Array<{ _id: string, name: string, budgetId: string }>
  setIsLoading: (isLoading: boolean) => void
  setIsReady: (isReady: boolean) => void
  setError: (error: string | null) => void
  setBudgets: (budgets: Array<{ _id: string, name: string, fiscalYear: string }>) => void
  setBudgetCategories: (categories: Array<{ _id: string, name: string, budgetId: string }>) => void
  incrementRetryCount: () => void
  resetRetryCount: () => void
}

export const useIncomeDataPrefetcher = create<IncomeDataPrefetcherState>((set) => ({
  isLoading: true,
  isReady: false,
  error: null,
  retryCount: 0,
  budgets: [],
  budgetCategories: [],
  setIsLoading: (isLoading) => set({ isLoading }),
  setIsReady: (isReady) => set({ isReady }),
  setError: (error) => set({ error }),
  setBudgets: (budgets) => set({ budgets }),
  setBudgetCategories: (budgetCategories) => set({ budgetCategories }),
  incrementRetryCount: () => set((state) => ({ retryCount: state.retryCount + 1 })),
  resetRetryCount: () => set({ retryCount: 0 })
}))

export const IncomeDataPrefetcher = () => {
  const {
    isLoading,
    isReady,
    error,
    retryCount,
    setIsLoading,
    setIsReady,
    setError,
    setBudgets,
    setBudgetCategories,
    incrementRetryCount,
    resetRetryCount
  } = useIncomeDataPrefetcher()
  const [retryTimeout, setRetryTimeout] = useState<NodeJS.Timeout | null>(null)

  // Function to prefetch all required data for income forms
  const prefetchIncomeData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if data is already cached in localStorage
      const cachedBudgets = localStorage.getItem('income-form-budgets')
      const cachedCategories = localStorage.getItem('income-form-budget-categories')
      const cacheTimestamp = localStorage.getItem('income-form-cache-timestamp')
      
      // Check if cache is still valid (5 minutes)
      const isCacheValid = cacheTimestamp && 
        (Date.now() - parseInt(cacheTimestamp)) < 5 * 60 * 1000

      if (isCacheValid && cachedBudgets && cachedCategories) {
        // Use cached data
        setBudgets(JSON.parse(cachedBudgets))
        setBudgetCategories(JSON.parse(cachedCategories))
        setIsReady(true)
        resetRetryCount()
        return
      }

      // First fetch budgets
      const budgetsResponse = await fetch('/api/accounting/budget', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!budgetsResponse.ok) {
        throw new Error(`Failed to fetch budgets: ${budgetsResponse.status} ${budgetsResponse.statusText}`)
      }

      const budgetsData = await budgetsResponse.json()

      // Process and store budgets
      const processedBudgets = Array.isArray(budgetsData) ? budgetsData :
        (budgetsData.budgets || budgetsData.data || [])

      // Debug logging
      console.log('Fetched budgets:', processedBudgets)
      if (processedBudgets.length > 0) {
        console.log('First budget structure:', processedBudgets[0])
      }

      // If no budgets available, set empty arrays
      if (processedBudgets.length === 0) {
        setBudgets([])
        setBudgetCategories([])
        setIsReady(true)
        resetRetryCount()
        return
      }

      // Fetch categories for all budgets
      const categoryPromises = processedBudgets.map((budget: any) => {
        // Budget model transforms _id to id, so use id field
        const budgetId = budget.id || budget._id
        if (!budgetId) {
          console.warn('Budget missing ID:', budget)
          return Promise.resolve({ categories: [] })
        }

        return fetch(`/api/accounting/budget/${budgetId}/categories?type=income`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        }).then(response => {
          if (!response.ok) {
            console.warn(`Failed to fetch categories for budget ${budgetId}:`, response.status)
            return { categories: [] }
          }
          return response.json()
        }).catch(error => {
          console.warn(`Error fetching categories for budget ${budgetId}:`, error)
          return { categories: [] }
        })
      })

      const categoriesResponses = await Promise.all(categoryPromises)

      // Flatten all categories from all budgets
      const allCategories = categoriesResponses.reduce((acc: any[], response: any) => {
        const categories = response.categories || []
        return acc.concat(categories)
      }, [])

      // Cache the data
      localStorage.setItem('income-form-budgets', JSON.stringify(processedBudgets))
      localStorage.setItem('income-form-budget-categories', JSON.stringify(allCategories))
      localStorage.setItem('income-form-cache-timestamp', Date.now().toString())

      // Update store
      setBudgets(processedBudgets)
      setBudgetCategories(allCategories)

      // If we get here, all data was fetched successfully
      setIsReady(true)
      resetRetryCount()

      // Clear any existing retry timeout
      if (retryTimeout) {
        clearTimeout(retryTimeout)
        setRetryTimeout(null)
      }
    } catch (error: unknown) {
      console.error('Error prefetching income form data:', error)

      // Set error message
      setError(error instanceof Error ? error.message : 'Failed to prefetch required data')

      // Increment retry count
      incrementRetryCount()

      // Schedule retry with exponential backoff
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 30000) // Max 30 seconds

      const timeout = setTimeout(() => {
        prefetchIncomeData()
      }, retryDelay)

      setRetryTimeout(timeout)

      // Show toast only on first error
      if (retryCount === 0) {
        toast({
          title: "Data Loading Error",
          description: "We're having trouble loading form data. Retrying in the background...",
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Prefetch data on component mount
  useEffect(() => {
    prefetchIncomeData()
  }, [])

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout)
        setRetryTimeout(null)
      }
    }
  }, [retryTimeout])

  // This component doesn't render anything visible
  return null
}

export default IncomeDataPrefetcher

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { useIncomeDataPrefetcher } from './income-data-prefetcher';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, Zap } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';

// Ultra-optimized form data interface
interface UltraOptimizedIncomeFormData {
  date: Date;
  source: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: string;
  budget: string;
  budgetCategory: string;
  notes?: string;
}

interface UltraOptimizedIncomeFormProps {
  income?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data to prevent any API calls during form interaction
const STATIC_FORM_DATA = {
  incomeSources: [
    { value: 'government_subvention', label: 'Government Subvention' },
    { value: 'registration_fees', label: 'Registration Fees' },
    { value: 'licensing_fees', label: 'Licensing Fees' },
    { value: 'donations', label: 'Donations' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2024-2025', label: '2024-2025 (Current)' },
    { value: '2023-2024', label: '2023-2024' },
    { value: '2025-2026', label: '2025-2026' },
  ],
  statusOptions: [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'received', label: 'Received' },
  ],
  budgets: [
    { value: 'budget-2024-2025', label: 'Main Budget 2024-2025' },
    { value: 'budget-2023-2024', label: 'Main Budget 2023-2024' },
    { value: 'budget-special', label: 'Special Projects Budget' },
  ],
  budgetCategories: [
    { value: 'income-government', label: 'Government Revenue' },
    { value: 'income-fees', label: 'Fee Collection' },
    { value: 'income-donations', label: 'Donations & Grants' },
    { value: 'income-other', label: 'Other Income' },
  ],
};

export function UltraOptimizedIncomeForm({
  income,
  onSubmit,
  onCancel,
  isLoading = false
}: UltraOptimizedIncomeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get prefetched data
  const { isReady, isLoading: isDataLoading, budgets, budgetCategories } = useIncomeDataPrefetcher();

  // Initialize form data with defaults based on prefetched data
  const [formData, setFormData] = useState<UltraOptimizedIncomeFormData>(() => ({
    date: income?.date ? new Date(income.date) : new Date(),
    source: income?.source || 'government_subvention',
    amount: income?.amount?.toString() || '',
    reference: income?.reference || '',
    description: income?.description || '',
    fiscalYear: income?.fiscalYear || '2024-2025',
    status: income?.status || 'draft',
    budget: income?.budget || (budgets.length > 0 ? (budgets[0].id || budgets[0]._id) : ''),
    budgetCategory: income?.budgetCategory || (budgetCategories.length > 0 ? (budgetCategories[0].id || budgetCategories[0]._id) : ''),
    notes: income?.notes || '',
  }));

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const { toast } = useToast();

  // Ultra-fast update function with minimal re-renders
  const updateField = useCallback((field: keyof UltraOptimizedIncomeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field immediately
    setErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });
  }, []);

  // Validate form with minimal processing
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.source) newErrors.source = 'Source is required';
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0';
    if (!formData.reference.trim()) newErrors.reference = 'Reference is required';
    if (!formData.fiscalYear) newErrors.fiscalYear = 'Fiscal year is required';
    if (!formData.budget) newErrors.budget = 'Budget is required';
    if (!formData.budgetCategory) newErrors.budgetCategory = 'Budget category is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission with optimized processing
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Process form data with minimal overhead
      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
        appliedToBudget: true, // Always apply to budget
      };

      await onSubmit(processedValues);

      toast({
        title: income ? 'Income Updated' : 'Income Created',
        description: income ? 'The income has been updated successfully' : 'The income has been created successfully',
      });

    } catch (error: unknown) {
      console.error('Error saving income:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, isSubmitting, validateForm, onSubmit, income, toast]);

  // Memoized form sections for optimal performance
  const basicInfoSection = useMemo(() => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Basic Information</h3>
        <Badge variant="outline" className="text-xs">
          <Zap className="h-3 w-3 mr-1" />
          Ultra Fast
        </Badge>
      </div>
      <Separator />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Date Field */}
        <div className="space-y-2">
          <Label htmlFor="date">Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full pl-3 text-left font-normal"
              >
                {formData.date ? (
                  format(formData.date, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.date}
                onSelect={(date) => date && updateField('date', date)}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
        </div>

        {/* Fiscal Year Field */}
        <div className="space-y-2">
          <Label htmlFor="fiscalYear">Fiscal Year *</Label>
          <Select
            value={formData.fiscalYear}
            onValueChange={(value) => updateField('fiscalYear', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {STATIC_FORM_DATA.fiscalYears.map(year => (
                <SelectItem key={year.value} value={year.value}>
                  {year.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.fiscalYear && <p className="text-sm text-destructive">{errors.fiscalYear}</p>}
        </div>
      </div>

      {/* Income Source and Amount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="source">Income Source *</Label>
          <Select
            value={formData.source}
            onValueChange={(value) => updateField('source', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select income source" />
            </SelectTrigger>
            <SelectContent>
              {STATIC_FORM_DATA.incomeSources.map(source => (
                <SelectItem key={source.value} value={source.value}>
                  {source.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.source && <p className="text-sm text-destructive">{errors.source}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="amount">Amount (MWK) *</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            placeholder="Enter amount"
            value={formData.amount}
            onChange={(e) => updateField('amount', e.target.value)}
          />
          {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
        </div>
      </div>

      {/* Reference and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="reference">Reference *</Label>
          <Input
            id="reference"
            placeholder="Enter reference number"
            value={formData.reference}
            onChange={(e) => updateField('reference', e.target.value)}
          />
          {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => updateField('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {STATIC_FORM_DATA.statusOptions.map(status => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  ), [formData.date, formData.fiscalYear, formData.source, formData.amount, formData.reference, formData.status, errors, updateField]);

  const budgetSection = useMemo(() => {
    if (!isReady || isDataLoading) {
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Budget Integration</h3>
          <Separator />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Budget *</Label>
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Label>Budget Category *</Label>
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Budget Integration</h3>
        <Separator />

        {/* Budget Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="budget">Budget *</Label>
            <Select
              value={formData.budget}
              onValueChange={(value) => updateField('budget', value)}
              disabled={!isReady}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select budget" />
              </SelectTrigger>
              <SelectContent>
                {budgets.map(budget => (
                  <SelectItem key={budget.id || budget._id} value={budget.id || budget._id}>
                    {budget.name} ({budget.fiscalYear})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.budget && <p className="text-sm text-destructive">{errors.budget}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="budgetCategory">Budget Category *</Label>
            <Select
              value={formData.budgetCategory}
              onValueChange={(value) => updateField('budgetCategory', value)}
              disabled={!isReady}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {budgetCategories.map(category => (
                  <SelectItem key={category.id || category._id} value={category.id || category._id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.budgetCategory && <p className="text-sm text-destructive">{errors.budgetCategory}</p>}
          </div>
        </div>
      </div>
    );
  }, [formData.budget, formData.budgetCategory, errors, updateField, isReady, isDataLoading, budgets, budgetCategories]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{income ? 'Edit Income' : 'Record New Income'}</CardTitle>
          <Badge variant="default" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Ultra Optimized
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {basicInfoSection}
          {budgetSection}

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              value={formData.description || ''}
              onChange={(e) => updateField('description', e.target.value)}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!isReady || isDataLoading || isSubmitting || isLoading}
        >
          {!isReady || isDataLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {income ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            income ? 'Update Income' : 'Create Income'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

"use client"

import React, { useState } from 'react';
import { DashboardShell } from "@/components/dashboard-shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  Clock, 
  CheckCircle, 
  XCircle,
  Users,
  TrendingUp,
  AlertTriangle,
  RefreshCw
} from "lucide-react";
import Link from "next/link";
import { ApprovalQueue } from '@/components/accounting/income/approval-queue';
import { useQuery } from '@tanstack/react-query';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

export function IncomeApprovalsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  // Fetch approval statistics
  const {
    data: statsData,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['income-approval-stats'],
    queryFn: async () => {
      const response = await fetch('/api/accounting/income/approve');
      if (!response.ok) {
        throw new Error('Failed to fetch approval statistics');
      }
      const result = await response.json();
      
      // Calculate statistics from the approvals data
      const approvals = result.approvals || [];
      const totalPending = approvals.length;
      const highPriority = approvals.filter((a: any) => a.amount >= 5000000).length;
      const mediumPriority = approvals.filter((a: any) => a.amount >= 1000000 && a.amount < 5000000).length;
      const lowPriority = approvals.filter((a: any) => a.amount < 1000000).length;
      const totalValue = approvals.reduce((sum: number, a: any) => sum + a.amount, 0);
      
      return {
        totalPending,
        highPriority,
        mediumPriority,
        lowPriority,
        totalValue,
        approvals
      };
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = () => {
    refetchStats();
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Income Approvals</h1>
            <p className="text-muted-foreground">
              Review and approve pending income records for the Teachers Council of Malawi
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/income/overview">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Income</span>
              </Link>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="gap-1"
              onClick={handleRefresh}
              disabled={isLoadingStats}
            >
              <RefreshCw className={`h-4 w-4 ${isLoadingStats ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoadingStats ? '...' : statsData?.totalPending || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Total value: {isLoadingStats ? '...' : formatCurrency(statsData?.totalValue || 0)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Priority</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {isLoadingStats ? '...' : statsData?.highPriority || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Amount ≥ {formatCurrency(5000000)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Medium Priority</CardTitle>
              <TrendingUp className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {isLoadingStats ? '...' : statsData?.mediumPriority || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(1000000)} - {formatCurrency(4999999)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Priority</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {isLoadingStats ? '...' : statsData?.lowPriority || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Amount &lt; {formatCurrency(1000000)}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            <CardDescription>
              Filter and search through pending approvals
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by reference, source, or submitter..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High Priority</SelectItem>
                  <SelectItem value="medium">Medium Priority</SelectItem>
                  <SelectItem value="low">Low Priority</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Approval Tabs */}
        <Tabs defaultValue="pending" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending Approvals
              {statsData?.totalPending > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {statsData.totalPending}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Recently Approved
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              Recently Rejected
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-4">
            <ApprovalQueue
              title="Pending Income Approvals"
              description="Income records waiting for your approval"
              showActions={true}
            />
          </TabsContent>

          <TabsContent value="approved" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Recently Approved Income Records
                </CardTitle>
                <CardDescription>
                  Income records that have been approved in the last 30 days
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-muted-foreground">Recently approved records will appear here</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    This feature will be available in the next update
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rejected" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <XCircle className="h-5 w-5 text-red-500" />
                  Recently Rejected Income Records
                </CardTitle>
                <CardDescription>
                  Income records that have been rejected in the last 30 days
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <p className="text-muted-foreground">Recently rejected records will appear here</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    This feature will be available in the next update
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}

"use client"

import React, { useState, use<PERSON>emo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import {
  GitMerge,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  Target,
  Zap,
  FileText,
  Upload,
  Download,
  Settings,
  RefreshCw,
  Eye,
  ThumbsUp,
  ThumbsDown,
  X
} from 'lucide-react';
import { useIncomeReconciliation } from '@/lib/hooks/accounting/use-income-reconciliation';
import { 
  ReconciliationTransaction,
  ReconciliationMatch,
  ReconciliationVariance
} from '@/lib/services/accounting/income-reconciliation-service';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Format compact currency for mobile
const formatCompactCurrency = (value: number) => {
  if (value >= **********) {
    return `MWK ${(value / **********).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`;
  }
  return formatCurrency(value);
};

interface ReconciliationDashboardProps {
  onReconciliationComplete?: (sessionId: string) => void;
}

export function ReconciliationDashboard({ onReconciliationComplete }: ReconciliationDashboardProps) {
  const [sessionName, setSessionName] = useState('');
  const [sessionDescription, setSessionDescription] = useState('');
  const [selectedSource, setSelectedSource] = useState('bank');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    endDate: new Date()
  });
  const [reconciliationSettings, setReconciliationSettings] = useState({
    matchingRules: ['exact_match', 'amount_date_match'],
    autoApproveThreshold: 0.95,
    requireManualReview: true,
    enableDuplicateDetection: true
  });
  const [internalTransactions, setInternalTransactions] = useState<ReconciliationTransaction[]>([]);
  const [externalTransactions, setExternalTransactions] = useState<ReconciliationTransaction[]>([]);
  const [currentResults, setCurrentResults] = useState<{
    matches: ReconciliationMatch[];
    variances: ReconciliationVariance[];
    statistics: Record<string, number>;
  } | null>(null);

  const {
    configuration,
    isLoadingConfiguration,
    startReconciliation,
    isStartingReconciliation,
    reconciliationError,
    updateMatchStatus,
    isUpdatingMatchStatus,
    validateReconciliationRequest,
    getDefaultSettings,
    formatReconciliationResults,
    calculateReconciliationMetrics
  } = useIncomeReconciliation();

  // Handle file upload for transactions
  const handleFileUpload = (file: File, type: 'internal' | 'external') => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        const transactions: ReconciliationTransaction[] = data.map((item: Record<string, unknown>, index: number) => ({
          id: `${type}_${index}`,
          source: type,
          amount: Number(item.amount) || 0,
          date: new Date(item.date as string),
          description: String(item.description || ''),
          reference: String(item.reference || ''),
          accountNumber: String(item.accountNumber || ''),
          payerName: String(item.payerName || ''),
          category: String(item.category || ''),
          metadata: item
        }));

        if (type === 'internal') {
          setInternalTransactions(transactions);
        } else {
          setExternalTransactions(transactions);
        }
      } catch (error) {
        console.error('Error parsing file:', error);
      }
    };
    reader.readAsText(file);
  };

  // Start reconciliation process
  const handleStartReconciliation = async () => {
    const request = {
      name: sessionName,
      description: sessionDescription,
      source: selectedSource,
      dateRange,
      settings: reconciliationSettings,
      internalTransactions,
      externalTransactions
    };

    const validationErrors = validateReconciliationRequest(request);
    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors);
      return;
    }

    const result = await startReconciliation(request);
    if (result) {
      setCurrentResults(result.results);
      onReconciliationComplete?.(result.session.id);
    }
  };

  // Handle match status update
  const handleMatchStatusUpdate = async (matchId: string, status: 'approved' | 'rejected', notes?: string) => {
    const success = await updateMatchStatus({ matchId, status, notes });
    if (success && currentResults) {
      // Update local state
      const updatedMatches = currentResults.matches.map(match =>
        match.id === matchId ? { ...match, status } : match
      );
      setCurrentResults({
        ...currentResults,
        matches: updatedMatches
      });
    }
  };

  // Format results for display
  const formattedResults = useMemo(() => {
    if (!currentResults) return null;
    return formatReconciliationResults(currentResults);
  }, [currentResults, formatReconciliationResults]);

  // Calculate metrics
  const metrics = useMemo(() => {
    if (!currentResults) return null;
    return calculateReconciliationMetrics(currentResults);
  }, [currentResults, calculateReconciliationMetrics]);

  // Get confidence badge variant
  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.9) return { variant: 'default' as const, label: 'High' };
    if (confidence >= 0.7) return { variant: 'secondary' as const, label: 'Medium' };
    return { variant: 'destructive' as const, label: 'Low' };
  };

  // Get variance severity badge
  const getVarianceBadge = (severity: string) => {
    const variants = {
      critical: 'destructive' as const,
      high: 'destructive' as const,
      medium: 'secondary' as const,
      low: 'default' as const
    };
    return variants[severity as keyof typeof variants] || 'default';
  };

  if (isLoadingConfiguration) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Income Reconciliation</CardTitle>
          <CardDescription>Loading reconciliation configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Configuration Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Reconciliation Setup
          </CardTitle>
          <CardDescription>
            Configure and start a new income reconciliation session
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Session Details */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sessionName">Session Name</Label>
                <Input
                  id="sessionName"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  placeholder="Enter session name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sessionDescription">Description (Optional)</Label>
                <Textarea
                  id="sessionDescription"
                  value={sessionDescription}
                  onChange={(e) => setSessionDescription(e.target.value)}
                  placeholder="Enter session description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Data Source</Label>
                <Select value={selectedSource} onValueChange={setSelectedSource}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {configuration?.supportedSources.map(source => (
                      <SelectItem key={source} value={source}>
                        {source.charAt(0).toUpperCase() + source.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <div className="space-y-3">
                <Label>Auto-Approve Threshold: {reconciliationSettings.autoApproveThreshold}</Label>
                <Slider
                  value={[reconciliationSettings.autoApproveThreshold]}
                  onValueChange={([value]) => setReconciliationSettings(prev => ({
                    ...prev,
                    autoApproveThreshold: value
                  }))}
                  min={0.5}
                  max={1}
                  step={0.05}
                  className="w-full"
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={reconciliationSettings.requireManualReview}
                    onCheckedChange={(checked) => setReconciliationSettings(prev => ({
                      ...prev,
                      requireManualReview: checked
                    }))}
                  />
                  <Label>Require Manual Review</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={reconciliationSettings.enableDuplicateDetection}
                    onCheckedChange={(checked) => setReconciliationSettings(prev => ({
                      ...prev,
                      enableDuplicateDetection: checked
                    }))}
                  />
                  <Label>Enable Duplicate Detection</Label>
                </div>
              </div>
            </div>
          </div>

          {/* File Upload Section */}
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <Label>Internal Transactions</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 mb-2">Upload internal transactions file</p>
                <input
                  type="file"
                  accept=".json,.csv"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, 'internal');
                  }}
                  className="hidden"
                  id="internal-upload"
                />
                <Button variant="outline" size="sm" asChild>
                  <label htmlFor="internal-upload" className="cursor-pointer">
                    Choose File
                  </label>
                </Button>
                {internalTransactions.length > 0 && (
                  <p className="text-sm text-green-600 mt-2">
                    {internalTransactions.length} transactions loaded
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <Label>External Transactions</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 mb-2">Upload external transactions file</p>
                <input
                  type="file"
                  accept=".json,.csv"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, 'external');
                  }}
                  className="hidden"
                  id="external-upload"
                />
                <Button variant="outline" size="sm" asChild>
                  <label htmlFor="external-upload" className="cursor-pointer">
                    Choose File
                  </label>
                </Button>
                {externalTransactions.length > 0 && (
                  <p className="text-sm text-green-600 mt-2">
                    {externalTransactions.length} transactions loaded
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleStartReconciliation}
              disabled={isStartingReconciliation || !sessionName || internalTransactions.length === 0 || externalTransactions.length === 0}
              className="gap-2"
            >
              {isStartingReconciliation ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <GitMerge className="h-4 w-4" />
              )}
              {isStartingReconciliation ? 'Processing...' : 'Start Reconciliation'}
            </Button>
          </div>

          {reconciliationError && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Reconciliation Error</AlertTitle>
              <AlertDescription>{reconciliationError.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Results Section */}
      {currentResults && formattedResults && metrics && (
        <>
          {/* Summary Statistics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Match Rate</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formattedResults.summary.matchRate.toFixed(1)}%
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>{formattedResults.summary.matchedTransactions} of {formattedResults.summary.totalTransactions}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Confidence</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formattedResults.summary.confidence.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  Average match confidence
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Auto Matched</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formattedResults.matches.autoMatched}
                </div>
                <div className="text-xs text-muted-foreground">
                  {metrics.efficiency.automationRate.toFixed(1)}% automation rate
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Variances</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formattedResults.variances.total}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formattedResults.variances.critical + formattedResults.variances.high} high priority
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Results */}
          <Tabs defaultValue="matches" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="matches">Matches</TabsTrigger>
              <TabsTrigger value="variances">Variances</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="export">Export</TabsTrigger>
            </TabsList>

            {/* Matches Tab */}
            <TabsContent value="matches" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Transaction Matches</CardTitle>
                  <CardDescription>
                    Review and approve transaction matches
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Match Statistics */}
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {formattedResults.matches.autoMatched}
                        </div>
                        <div className="text-sm text-green-700">Auto Matched</div>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {formattedResults.matches.pending}
                        </div>
                        <div className="text-sm text-yellow-700">Pending Review</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {formattedResults.matches.approved}
                        </div>
                        <div className="text-sm text-blue-700">Approved</div>
                      </div>
                    </div>

                    {/* Matches Table */}
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Internal Transaction</TableHead>
                            <TableHead>External Transaction</TableHead>
                            <TableHead>Confidence</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {currentResults.matches.slice(0, 10).map((match) => {
                            const confidenceBadge = getConfidenceBadge(match.confidence);
                            return (
                              <TableRow key={match.id}>
                                <TableCell>
                                  <div className="space-y-1">
                                    <div className="font-medium">
                                      {formatCurrency(match.internalTransaction.amount)}
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {match.internalTransaction.description}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {match.internalTransaction.date.toLocaleDateString()}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="space-y-1">
                                    <div className="font-medium">
                                      {formatCurrency(match.externalTransaction.amount)}
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {match.externalTransaction.description}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {match.externalTransaction.date.toLocaleDateString()}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={confidenceBadge.variant}>
                                    {(match.confidence * 100).toFixed(0)}% {confidenceBadge.label}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={
                                    match.status === 'auto-matched' ? 'default' :
                                    match.status === 'approved' ? 'default' :
                                    match.status === 'rejected' ? 'destructive' : 'secondary'
                                  }>
                                    {match.status.replace('_', ' ').toUpperCase()}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {match.status === 'pending' && (
                                    <div className="flex space-x-2">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleMatchStatusUpdate(match.id, 'approved')}
                                        disabled={isUpdatingMatchStatus}
                                      >
                                        <ThumbsUp className="h-3 w-3" />
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleMatchStatusUpdate(match.id, 'rejected')}
                                        disabled={isUpdatingMatchStatus}
                                      >
                                        <ThumbsDown className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  )}
                                  {match.status !== 'pending' && (
                                    <Button size="sm" variant="ghost">
                                      <Eye className="h-3 w-3" />
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>

                    {currentResults.matches.length > 10 && (
                      <div className="text-center">
                        <Button variant="outline">
                          View All {currentResults.matches.length} Matches
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Variances Tab */}
            <TabsContent value="variances" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Reconciliation Variances</CardTitle>
                  <CardDescription>
                    Review and resolve reconciliation variances
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Variance Statistics */}
                    <div className="grid gap-4 md:grid-cols-4">
                      <div className="text-center p-4 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {formattedResults.variances.critical}
                        </div>
                        <div className="text-sm text-red-700">Critical</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">
                          {formattedResults.variances.high}
                        </div>
                        <div className="text-sm text-orange-700">High</div>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {formattedResults.variances.medium}
                        </div>
                        <div className="text-sm text-yellow-700">Medium</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {formattedResults.variances.low}
                        </div>
                        <div className="text-sm text-green-700">Low</div>
                      </div>
                    </div>

                    {/* Variances Table */}
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Type</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Severity</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {currentResults.variances.slice(0, 10).map((variance) => (
                            <TableRow key={variance.id}>
                              <TableCell>
                                <Badge variant="outline">
                                  {variance.type.replace('_', ' ').toUpperCase()}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div className="font-medium">{variance.description}</div>
                                  {variance.internalTransaction && (
                                    <div className="text-sm text-muted-foreground">
                                      Internal: {variance.internalTransaction.description}
                                    </div>
                                  )}
                                  {variance.externalTransaction && (
                                    <div className="text-sm text-muted-foreground">
                                      External: {variance.externalTransaction.description}
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                {variance.internalTransaction && formatCurrency(variance.internalTransaction.amount)}
                                {variance.externalTransaction && formatCurrency(variance.externalTransaction.amount)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={getVarianceBadge(variance.severity)}>
                                  {variance.severity.toUpperCase()}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button size="sm" variant="outline">
                                  <Eye className="h-3 w-3 mr-1" />
                                  Review
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {currentResults.variances.length > 10 && (
                      <div className="text-center">
                        <Button variant="outline">
                          View All {currentResults.variances.length} Variances
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Efficiency Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Automation Rate</span>
                          <span className="font-medium">{metrics.efficiency.automationRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.efficiency.automationRate} />
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Manual Review Rate</span>
                          <span className="font-medium">{metrics.efficiency.manualReviewRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.efficiency.manualReviewRate} />
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Variance Rate</span>
                          <span className="font-medium">{metrics.efficiency.varianceRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.efficiency.varianceRate} />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quality Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Match Accuracy</span>
                          <span className="font-medium">{metrics.quality.matchAccuracy.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.quality.matchAccuracy} />
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Completeness</span>
                          <span className="font-medium">{metrics.quality.completeness.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.quality.completeness} />
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Data Integrity</span>
                          <span className="font-medium">{metrics.quality.dataIntegrity.toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.quality.dataIntegrity} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Export Tab */}
            <TabsContent value="export" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Export Results</CardTitle>
                  <CardDescription>
                    Export reconciliation results in various formats
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <Button variant="outline" className="gap-2">
                      <Download className="h-4 w-4" />
                      Export to Excel
                    </Button>
                    <Button variant="outline" className="gap-2">
                      <Download className="h-4 w-4" />
                      Export to PDF
                    </Button>
                    <Button variant="outline" className="gap-2">
                      <Download className="h-4 w-4" />
                      Export to CSV
                    </Button>
                    <Button variant="outline" className="gap-2">
                      <FileText className="h-4 w-4" />
                      Generate Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}

"use client"

import React, { use<PERSON>tate, use<PERSON><PERSON><PERSON> } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ComposedChart,
  Bar,
  Reference<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  BarChart4,
  Settings,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap,
  Target,
  Activity,
  Brain,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { useAdvancedForecasting } from '@/lib/hooks/accounting/use-advanced-forecasting';
import { AdvancedForecastResult, ForecastOptions } from '@/lib/services/accounting/advanced-forecasting-service';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Format compact currency for mobile
const formatCompactCurrency = (value: number) => {
  if (value >= **********) {
    return `MWK ${(value / **********).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`;
  }
  return formatCurrency(value);
};

interface AdvancedForecastingDashboardProps {
  fiscalYear: string;
  budgetId?: string;
  categoryId?: string;
  onForecastGenerated?: (forecast: AdvancedForecastResult) => void;
}

export function AdvancedForecastingDashboard({
  fiscalYear,
  budgetId,
  categoryId,
  onForecastGenerated
}: AdvancedForecastingDashboardProps) {
  const [forecastOptions, setForecastOptions] = useState<Partial<ForecastOptions>>({
    forecastHorizon: 6,
    confidenceLevel: 0.95,
    includeSeasonality: true,
    detectAnomalies: true,
    anomalyThreshold: 2.5,
    includeScenarios: true,
    modelType: 'auto',
    minDataPoints: 6
  });

  const [currentForecast, setCurrentForecast] = useState<AdvancedForecastResult | null>(null);
  const [selectedScenario, setSelectedScenario] = useState<'optimistic' | 'realistic' | 'pessimistic'>('realistic');

  const {
    configuration,
    isLoadingConfiguration,
    generateForecast,
    isGeneratingForecast,
    forecastError,
    validateForecastRequest,
    formatForecastForChart,
    calculateForecastSummary
  } = useAdvancedForecasting();

  // Generate forecast
  const handleGenerateForecast = async () => {
    const validationErrors = validateForecastRequest({
      fiscalYear,
      budgetId,
      categoryId,
      options: forecastOptions
    });

    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors);
      return;
    }

    const result = await generateForecast({
      fiscalYear,
      budgetId,
      categoryId,
      options: forecastOptions
    });

    if (result) {
      setCurrentForecast(result);
      onForecastGenerated?.(result);
    }
  };

  // Format chart data
  const chartData = useMemo(() => {
    if (!currentForecast) return null;
    return formatForecastForChart(currentForecast);
  }, [currentForecast, formatForecastForChart]);

  // Calculate summary statistics
  const summary = useMemo(() => {
    if (!currentForecast) return null;
    return calculateForecastSummary(currentForecast);
  }, [currentForecast, calculateForecastSummary]);

  // Get trend icon and color
  const getTrendIcon = (trend: 'increasing' | 'decreasing' | 'stable') => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  // Get volatility badge
  const getVolatilityBadge = (volatility: 'low' | 'medium' | 'high') => {
    const variants = {
      low: 'default',
      medium: 'secondary',
      high: 'destructive'
    } as const;
    
    return <Badge variant={variants[volatility]}>{volatility.toUpperCase()}</Badge>;
  };

  // Get recommendation icon
  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'opportunity':
        return <Lightbulb className="h-4 w-4 text-yellow-500" />;
      case 'budget_adjustment':
        return <Target className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  if (isLoadingConfiguration) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Advanced Income Forecasting</CardTitle>
          <CardDescription>Loading forecasting configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Configuration Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Forecast Configuration
          </CardTitle>
          <CardDescription>
            Configure advanced forecasting parameters and generate predictions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Forecast Horizon */}
            <div className="space-y-2">
              <Label>Forecast Horizon</Label>
              <Select
                value={forecastOptions.forecastHorizon?.toString()}
                onValueChange={(value) => setForecastOptions(prev => ({
                  ...prev,
                  forecastHorizon: parseInt(value)
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {configuration?.forecastHorizons.map(horizon => (
                    <SelectItem key={horizon.value} value={horizon.value.toString()}>
                      {horizon.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Model Type */}
            <div className="space-y-2">
              <Label>Forecasting Model</Label>
              <Select
                value={forecastOptions.modelType}
                onValueChange={(value: 'auto' | 'linear' | 'exponential' | 'seasonal') => 
                  setForecastOptions(prev => ({ ...prev, modelType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {configuration?.availableModels.map(model => (
                    <SelectItem key={model.type} value={model.type}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Confidence Level */}
            <div className="space-y-2">
              <Label>Confidence Level</Label>
              <Select
                value={forecastOptions.confidenceLevel?.toString()}
                onValueChange={(value) => setForecastOptions(prev => ({
                  ...prev,
                  confidenceLevel: parseFloat(value)
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {configuration?.confidenceLevels.map(level => (
                    <SelectItem key={level.value} value={level.value.toString()}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Anomaly Threshold */}
            <div className="space-y-3">
              <Label>Anomaly Detection Threshold: {forecastOptions.anomalyThreshold}</Label>
              <Slider
                value={[forecastOptions.anomalyThreshold || 2.5]}
                onValueChange={([value]) => setForecastOptions(prev => ({
                  ...prev,
                  anomalyThreshold: value
                }))}
                min={1}
                max={5}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Options */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={forecastOptions.includeSeasonality}
                  onCheckedChange={(checked) => setForecastOptions(prev => ({
                    ...prev,
                    includeSeasonality: checked
                  }))}
                />
                <Label>Include Seasonality</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  checked={forecastOptions.detectAnomalies}
                  onCheckedChange={(checked) => setForecastOptions(prev => ({
                    ...prev,
                    detectAnomalies: checked
                  }))}
                />
                <Label>Detect Anomalies</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  checked={forecastOptions.includeScenarios}
                  onCheckedChange={(checked) => setForecastOptions(prev => ({
                    ...prev,
                    includeScenarios: checked
                  }))}
                />
                <Label>Include Scenarios</Label>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleGenerateForecast}
              disabled={isGeneratingForecast}
              className="gap-2"
            >
              {isGeneratingForecast ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Brain className="h-4 w-4" />
              )}
              {isGeneratingForecast ? 'Generating...' : 'Generate Forecast'}
            </Button>
          </div>

          {forecastError && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Forecast Error</AlertTitle>
              <AlertDescription>{forecastError.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Forecast Results */}
      {currentForecast && summary && (
        <>
          {/* Summary Statistics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Model Accuracy</CardTitle>
                <BarChart4 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(summary.modelAccuracy * 100).toFixed(1)}%
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <Badge variant="outline">{summary.modelType.toUpperCase()}</Badge>
                  <span>R² Score</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
                {getTrendIcon(summary.trends.longTerm)}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary.growthRate > 0 ? '+' : ''}{summary.growthRate.toFixed(1)}%
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>Projected vs Historical</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Volatility</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-2">
                  {getVolatilityBadge(summary.trends.volatility)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {summary.trends.cyclical ? 'Cyclical patterns detected' : 'No cyclical patterns'}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Data Quality</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary.dataPoints}
                </div>
                <div className="text-xs text-muted-foreground">
                  Historical data points
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Analysis */}
          <Tabs defaultValue="forecast" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="forecast">Forecast</TabsTrigger>
              <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
              <TabsTrigger value="recommendations">Insights</TabsTrigger>
            </TabsList>

            {/* Forecast Tab */}
            <TabsContent value="forecast" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Income Forecast</CardTitle>
                  <CardDescription>
                    Historical data and {summary.forecastHorizon}-month forecast with confidence intervals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {chartData && (
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <ComposedChart data={[...chartData.historical, ...chartData.forecast]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis tickFormatter={(value) => formatCompactCurrency(value)} />
                          <Tooltip
                            formatter={(value: number, name: string) => [
                              formatCurrency(value),
                              name === 'value' ? 'Historical' :
                              name === 'forecasted' ? 'Forecast' : name
                            ]}
                          />
                          <Legend />

                          {/* Historical data */}
                          <Line
                            type="monotone"
                            dataKey="value"
                            stroke="#0088FE"
                            strokeWidth={2}
                            name="Historical"
                            connectNulls={false}
                          />

                          {/* Forecast line */}
                          <Line
                            type="monotone"
                            dataKey="forecasted"
                            stroke="#00C49F"
                            strokeWidth={2}
                            strokeDasharray="5 5"
                            name="Forecast"
                            connectNulls={false}
                          />

                          {/* Confidence intervals */}
                          <Area
                            type="monotone"
                            dataKey="upperBound"
                            stackId="confidence"
                            stroke="none"
                            fill="#00C49F"
                            fillOpacity={0.1}
                            name="Upper Bound"
                          />
                          <Area
                            type="monotone"
                            dataKey="lowerBound"
                            stackId="confidence"
                            stroke="none"
                            fill="#00C49F"
                            fillOpacity={0.1}
                            name="Lower Bound"
                          />
                        </ComposedChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Scenarios Tab */}
            <TabsContent value="scenarios" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Scenario Analysis</h3>
                  <p className="text-sm text-muted-foreground">
                    Compare different forecast scenarios
                  </p>
                </div>
                <Select value={selectedScenario} onValueChange={(value: 'optimistic' | 'realistic' | 'pessimistic') => setSelectedScenario(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="optimistic">Optimistic</SelectItem>
                    <SelectItem value="realistic">Realistic</SelectItem>
                    <SelectItem value="pessimistic">Pessimistic</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                {currentForecast.scenarios.map((scenario) => (
                  <Card key={scenario.scenario} className={selectedScenario === scenario.scenario ? 'ring-2 ring-primary' : ''}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="capitalize">{scenario.scenario}</span>
                        <Badge variant={
                          scenario.scenario === 'optimistic' ? 'default' :
                          scenario.scenario === 'pessimistic' ? 'destructive' : 'secondary'
                        }>
                          {scenario.adjustmentFactor > 1 ? '+' : ''}{((scenario.adjustmentFactor - 1) * 100).toFixed(0)}%
                        </Badge>
                      </CardTitle>
                      <CardDescription>{scenario.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm text-muted-foreground">Total Projected</p>
                          <p className="text-lg font-bold">{formatCurrency(scenario.totalProjected)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Budget Impact</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant={
                              scenario.budgetImpact.riskLevel === 'high' ? 'destructive' :
                              scenario.budgetImpact.riskLevel === 'medium' ? 'secondary' : 'default'
                            }>
                              {scenario.budgetImpact.riskLevel.toUpperCase()}
                            </Badge>
                            <span className="text-sm">{scenario.budgetImpact.utilizationRate.toFixed(1)}%</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {chartData && (
                <Card>
                  <CardHeader>
                    <CardTitle>Scenario Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={chartData.forecast}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis tickFormatter={(value) => formatCompactCurrency(value)} />
                          <Tooltip formatter={(value: number) => [formatCurrency(value), '']} />
                          <Legend />

                          {chartData.scenarios.map((scenario, index) => (
                            <Line
                              key={scenario.scenario}
                              type="monotone"
                              dataKey="value"
                              data={scenario.data}
                              stroke={['#00C49F', '#0088FE', '#FF8042'][index]}
                              strokeWidth={2}
                              name={scenario.scenario.charAt(0).toUpperCase() + scenario.scenario.slice(1)}
                            />
                          ))}
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Trends Tab */}
            <TabsContent value="trends" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Trend Analysis</CardTitle>
                    <CardDescription>Short-term and long-term trend indicators</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Short-term Trend</span>
                      <div className="flex items-center space-x-2">
                        {getTrendIcon(summary.trends.shortTerm)}
                        <span className="capitalize">{summary.trends.shortTerm}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Long-term Trend</span>
                      <div className="flex items-center space-x-2">
                        {getTrendIcon(summary.trends.longTerm)}
                        <span className="capitalize">{summary.trends.longTerm}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Volatility</span>
                      {getVolatilityBadge(summary.trends.volatility)}
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Cyclical Pattern</span>
                      <Badge variant={summary.trends.cyclical ? 'default' : 'secondary'}>
                        {summary.trends.cyclical ? 'Detected' : 'None'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Model Performance</CardTitle>
                    <CardDescription>Forecasting model accuracy metrics</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">R² Score</span>
                        <span className="font-medium">{(currentForecast.model.accuracy.r2 * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={currentForecast.model.accuracy.r2 * 100} />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">MAPE</span>
                        <span className="font-medium">{currentForecast.model.accuracy.mape.toFixed(1)}%</span>
                      </div>
                      <Progress value={Math.max(0, 100 - currentForecast.model.accuracy.mape)} />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Model Confidence</span>
                        <span className="font-medium">{(currentForecast.model.confidence * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={currentForecast.model.confidence * 100} />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Recommendations Tab */}
            <TabsContent value="recommendations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lightbulb className="h-5 w-5" />
                    AI-Powered Insights & Recommendations
                  </CardTitle>
                  <CardDescription>
                    Actionable insights based on forecast analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {currentForecast.recommendations.length > 0 ? (
                    <div className="space-y-4">
                      {currentForecast.recommendations.map((recommendation, index) => (
                        <Alert key={index} variant={recommendation.priority === 'high' ? 'destructive' : 'default'}>
                          {getRecommendationIcon(recommendation.type)}
                          <AlertTitle className="flex items-center justify-between">
                            <span>{recommendation.title}</span>
                            <div className="flex items-center space-x-2">
                              <Badge variant={
                                recommendation.priority === 'high' ? 'destructive' :
                                recommendation.priority === 'medium' ? 'secondary' : 'outline'
                              }>
                                {recommendation.priority.toUpperCase()}
                              </Badge>
                              {recommendation.actionRequired && (
                                <Badge variant="outline">Action Required</Badge>
                              )}
                            </div>
                          </AlertTitle>
                          <AlertDescription>
                            {recommendation.description}
                            {recommendation.impact !== 0 && (
                              <div className="mt-2">
                                <span className="text-sm font-medium">
                                  Estimated Impact: {recommendation.impact > 0 ? '+' : ''}{(recommendation.impact * 100).toFixed(1)}%
                                </span>
                              </div>
                            )}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <p className="text-muted-foreground">No specific recommendations at this time</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Your income forecast looks stable and within expected parameters
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}

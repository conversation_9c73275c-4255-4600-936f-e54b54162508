'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { CalendarIcon, DollarSign, FileText, Tag } from 'lucide-react';

interface DynamicIncomeFormData {
  date: Date;
  source: 'government_subvention' | 'registration_fees' | 'licensing_fees' | 'donations' | 'other';
  subSource?: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'received' | 'cancelled';
  notes?: string;
}

interface DynamicIncomeFormProps {
  income?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function DynamicIncomeForm({
  income,
  onSubmit,
  onCancel,
  isLoading = false
}: DynamicIncomeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize form data - NO BUDGET SELECTION REQUIRED
  const [formData, setFormData] = useState<DynamicIncomeFormData>(() => ({
    date: income?.date ? new Date(income.date) : new Date(),
    source: income?.source || 'government_subvention',
    subSource: income?.subSource || '',
    amount: income?.amount?.toString() || '',
    reference: income?.reference || '',
    description: income?.description || '',
    fiscalYear: income?.fiscalYear || '2025-2026',
    status: income?.status || 'draft',
    notes: income?.notes || '',
  }));

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form field
  const updateField = useCallback((field: keyof DynamicIncomeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Amount must be greater than 0';
    }

    if (!formData.reference.trim()) {
      newErrors.reference = 'Reference is required';
    }

    if (!formData.source) {
      newErrors.source = 'Income source is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Process form data - NO BUDGET LINKING REQUIRED
      // The system will automatically create budget and categories
      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
        // The budget fund service will automatically:
        // 1. Create budget for fiscal year if doesn't exist
        // 2. Create category for income source if doesn't exist  
        // 3. Link income to budget and category
        // 4. Update budget totals based on status
      };

      await onSubmit(processedValues);

      toast({
        title: income ? 'Income Updated' : 'Income Created',
        description: income 
          ? 'Income has been updated and budget automatically adjusted' 
          : 'Income has been created and budget automatically updated',
      });

    } catch (error) {
      console.error('Error submitting income form:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save income',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, isSubmitting, validateForm, onSubmit, income, toast]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          {income ? 'Edit Income Record' : 'Create Income Record'}
        </CardTitle>
        <CardDescription>
          {income 
            ? 'Update income details. Budget will be automatically adjusted.'
            : 'Add new income. Budget and categories will be created automatically based on the income source.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Income Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date.toISOString().split('T')[0]}
                onChange={(e) => updateField('date', new Date(e.target.value))}
                disabled={isSubmitting}
              />
              {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount (MWK) *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => updateField('amount', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
            </div>
          </div>

          {/* Income Source - This drives automatic budget category creation */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="source">Income Source * 
                <span className="text-xs text-muted-foreground ml-2">
                  (Budget category will be created automatically)
                </span>
              </Label>
              <Select
                value={formData.source}
                onValueChange={(value) => updateField('source', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select income source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="government_subvention">Government Subvention</SelectItem>
                  <SelectItem value="registration_fees">Registration Fees</SelectItem>
                  <SelectItem value="licensing_fees">Licensing Fees</SelectItem>
                  <SelectItem value="donations">Donations</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.source && <p className="text-sm text-destructive">{errors.source}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="subSource">Sub-source (Optional)</Label>
              <Input
                id="subSource"
                placeholder="e.g., Specific donor, department"
                value={formData.subSource}
                onChange={(e) => updateField('subSource', e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Reference and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="reference">Reference Number *</Label>
              <Input
                id="reference"
                placeholder="e.g., REF-2025-001"
                value={formData.reference}
                onChange={(e) => updateField('reference', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status *
                <span className="text-xs text-muted-foreground ml-2">
                  (Affects budget calculations)
                </span>
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => updateField('status', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft (Projected Budget)</SelectItem>
                  <SelectItem value="approved">Approved (Expected Budget)</SelectItem>
                  <SelectItem value="received">Received (Actual Budget)</SelectItem>
                  <SelectItem value="pending_approval">Pending Approval</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Fiscal Year - Auto-determines budget */}
          <div className="space-y-2">
            <Label htmlFor="fiscalYear">Fiscal Year *
              <span className="text-xs text-muted-foreground ml-2">
                (Budget will be created for this fiscal year if it doesn't exist)
              </span>
            </Label>
            <Select
              value={formData.fiscalYear}
              onValueChange={(value) => updateField('fiscalYear', value)}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select fiscal year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2025-2026">2025-2026</SelectItem>
                <SelectItem value="2024-2025">2024-2025</SelectItem>
                <SelectItem value="2026-2027">2026-2027</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Additional details about this income..."
              value={formData.description}
              onChange={(e) => updateField('description', e.target.value)}
              disabled={isSubmitting}
              rows={3}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Internal notes..."
              value={formData.notes}
              onChange={(e) => updateField('notes', e.target.value)}
              disabled={isSubmitting}
              rows={2}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="min-w-[120px]"
            >
              {isSubmitting ? 'Saving...' : income ? 'Update Income' : 'Create Income'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

'use client'

import React, { useState, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  FileSpreadsheet, 
  Download, 
  Upload, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Info
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useIncomeStore } from "@/lib/stores/enhanced-income-store"

interface BulkIncomeUploadProps {
  onUploadComplete?: () => void;
  onClose?: () => void;
}

interface ImportResult {
  success: boolean;
  message: string;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors?: Array<{
    row: number;
    errors?: string[];
    error?: string;
    data: any;
  }>;
  importedIncomes?: Array<{
    id: string;
    reference: string;
    amount: number;
    source: string;
    status: string;
  }>;
}

export function BulkIncomeUpload({ onUploadComplete, onClose }: BulkIncomeUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [activeTab, setActiveTab] = useState("upload")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()
  const { bulkImportIncomes } = useIncomeStore()

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      // Validate file type
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase()
      if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
        setUploadError('Please select a CSV or Excel file')
        return
      }

      // Validate file size (5MB limit)
      if (selectedFile.size > 5 * 1024 * 1024) {
        setUploadError('File size must be less than 5MB')
        return
      }

      setFile(selectedFile)
      setUploadError(null)
    }
  }

  const handleUpload = async () => {
    if (!file) {
      setUploadError('Please select a file to upload')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setUploadError(null)

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 500)

      // Upload file
      const result = await bulkImportIncomes(file)
      
      clearInterval(progressInterval)
      setUploadProgress(100)

      setImportResult(result)

      if (result.success) {
        toast({
          title: "Import Successful",
          description: `Successfully imported ${result.successCount} income transactions`,
          variant: "default"
        })

        if (onUploadComplete) {
          onUploadComplete()
        }
      } else {
        toast({
          title: "Import Failed",
          description: result.message,
          variant: "destructive"
        })
      }

      // Switch to results tab
      setActiveTab("results")
    } catch (error: unknown) {
      console.error('Error uploading file:', error)
      setUploadError(error instanceof Error ? error.message : 'An error occurred during upload')
      setUploadProgress(0)
    } finally {
      setIsUploading(false)
    }
  }

  const handleDownloadTemplate = () => {
    console.log('Downloading template...')

    // Download the template file from our API with cache-busting parameter
    const timestamp = new Date().getTime()
    window.location.href = `/api/accounting/income/template?t=${timestamp}`

    // Show success toast
    toast({
      title: "Template Downloaded",
      description: "The income import template has been downloaded with all required fields.",
      variant: "default"
    })
  }

  const resetUpload = () => {
    setFile(null)
    setUploadProgress(0)
    setUploadError(null)
    setImportResult(null)
    setActiveTab("upload")
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5" />
          Bulk Income Upload
        </CardTitle>
        <CardDescription>
          Import multiple income transactions from a CSV or Excel file
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="results" disabled={!importResult}>Results</TabsTrigger>
            <TabsTrigger value="template">Template</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4 py-4">
            <div className="flex items-center justify-center p-4 border-2 border-dashed rounded-md">
              <div className="space-y-2 text-center">
                <FileSpreadsheet className="mx-auto h-12 w-12 text-muted-foreground" />
                <div className="text-sm">
                  <Label htmlFor="file-upload" className="relative cursor-pointer rounded-md font-medium text-primary">
                    <span>Upload a file</span>
                    <Input
                      id="file-upload"
                      ref={fileInputRef}
                      type="file"
                      className="sr-only"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileChange}
                      disabled={isUploading}
                    />
                  </Label>
                  <p className="text-xs text-muted-foreground">CSV or Excel files up to 5MB</p>
                </div>
              </div>
            </div>

            {file && (
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                  <div className="flex items-center space-x-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    <span className="text-sm font-medium">{file.name}</span>
                    <Badge variant="secondary">{(file.size / 1024).toFixed(1)} KB</Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFile(null)}
                    disabled={isUploading}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            )}

            {uploadError && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertTitle>Upload Error</AlertTitle>
                <AlertDescription>{uploadError}</AlertDescription>
              </Alert>
            )}

            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}

            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handleDownloadTemplate}
                disabled={isUploading}
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
              <div className="space-x-2">
                {onClose && (
                  <Button variant="outline" onClick={onClose} disabled={isUploading}>
                    Cancel
                  </Button>
                )}
                <Button
                  onClick={handleUpload}
                  disabled={!file || isUploading}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  {isUploading ? 'Uploading...' : 'Upload File'}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="results" className="space-y-4 py-4">
            {importResult && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Info className="h-4 w-4 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium">Total Rows</p>
                          <p className="text-2xl font-bold">{importResult.totalRows}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <div>
                          <p className="text-sm font-medium">Successful</p>
                          <p className="text-2xl font-bold text-green-600">{importResult.successCount}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <XCircle className="h-4 w-4 text-red-500" />
                        <div>
                          <p className="text-sm font-medium">Errors</p>
                          <p className="text-2xl font-bold text-red-600">{importResult.errorCount}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert variant={importResult.success ? "default" : "destructive"}>
                  {importResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <AlertTitle>
                    {importResult.success ? 'Import Completed' : 'Import Failed'}
                  </AlertTitle>
                  <AlertDescription>{importResult.message}</AlertDescription>
                </Alert>

                {importResult.errors && importResult.errors.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Import Errors</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-48">
                        <div className="space-y-2">
                          {importResult.errors.map((error, index) => (
                            <div key={index} className="p-2 border rounded text-sm">
                              <p className="font-medium">Row {error.row}:</p>
                              <ul className="list-disc list-inside text-red-600 text-xs">
                                {error.errors?.map((err, i) => (
                                  <li key={i}>{err}</li>
                                )) || <li>{error.error}</li>}
                              </ul>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={resetUpload}>
                    Upload Another File
                  </Button>
                  {onClose && (
                    <Button onClick={onClose}>
                      Close
                    </Button>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="template" className="space-y-4 py-4">
            <div className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Template Information</AlertTitle>
                <AlertDescription>
                  Download the template file to see the required format for bulk income import.
                  The template includes examples and reference sheets for valid values.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Required Fields</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-xs space-y-1">
                      <p><strong>date:</strong> Transaction date (YYYY-MM-DD)</p>
                      <p><strong>source:</strong> Income source type</p>
                      <p><strong>amount:</strong> Income amount (numbers only)</p>
                      <p><strong>reference:</strong> Unique reference number</p>
                      <p><strong>fiscalYear:</strong> Fiscal year (YYYY-YYYY)</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Optional Fields</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-xs space-y-1">
                      <p><strong>description:</strong> Income description</p>
                      <p><strong>status:</strong> Income status (defaults to draft)</p>
                      <p><strong>budget:</strong> Budget name</p>
                      <p><strong>budgetCategory:</strong> Budget category</p>
                      <p><strong>notes:</strong> Additional notes</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Button onClick={handleDownloadTemplate} className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

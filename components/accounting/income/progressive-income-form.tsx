'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, Zap, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';

// Progressive form data interface
interface ProgressiveIncomeFormData {
  date: Date;
  source: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: string;
  budget: string;
  budgetCategory: string;
  notes?: string;
}

interface ProgressiveIncomeFormProps {
  income?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data that loads instantly (no backend dependency)
const INSTANT_FORM_DATA = {
  incomeSources: [
    { value: 'government_subvention', label: 'Government Subvention' },
    { value: 'registration_fees', label: 'Registration Fees' },
    { value: 'licensing_fees', label: 'Licensing Fees' },
    { value: 'donations', label: 'Donations' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2024-2025', label: '2024-2025 (Current)' },
    { value: '2023-2024', label: '2023-2024' },
    { value: '2025-2026', label: '2025-2026' },
  ],
  statusOptions: [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'received', label: 'Received' },
  ],
};

// Progressive data loader for backend-dependent fields
class ProgressiveDataLoader {
  private static instance: ProgressiveDataLoader;
  private budgets: any[] = [];
  private budgetCategories: any[] = [];
  private isLoading = false;
  private isLoaded = false;

  static getInstance(): ProgressiveDataLoader {
    if (!ProgressiveDataLoader.instance) {
      ProgressiveDataLoader.instance = new ProgressiveDataLoader();
    }
    return ProgressiveDataLoader.instance;
  }

  async loadBudgetData(): Promise<{ budgets: any[], budgetCategories: any[] }> {
    if (this.isLoaded) {
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    if (this.isLoading) {
      // Wait for current loading to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    try {
      this.isLoading = true;

      // Check localStorage first
      const cachedBudgets = localStorage.getItem('income-form-budgets');
      const cachedCategories = localStorage.getItem('income-form-budget-categories');
      const cacheTimestamp = localStorage.getItem('income-form-cache-timestamp');

      // Check if cache is valid (5 minutes)
      const isCacheValid = cacheTimestamp && 
        (Date.now() - parseInt(cacheTimestamp)) < 5 * 60 * 1000;

      if (isCacheValid && cachedBudgets && cachedCategories) {
        this.budgets = JSON.parse(cachedBudgets);
        this.budgetCategories = JSON.parse(cachedCategories);
        this.isLoaded = true;
        return { budgets: this.budgets, budgetCategories: this.budgetCategories };
      }

      // Fetch fresh data
      const budgetsResponse = await fetch('/api/accounting/budget', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!budgetsResponse.ok) {
        throw new Error(`Failed to fetch budgets: ${budgetsResponse.status}`);
      }

      const budgetsData = await budgetsResponse.json();
      const processedBudgets = Array.isArray(budgetsData) ? budgetsData : 
        (budgetsData.budgets || budgetsData.data || []);

      if (processedBudgets.length === 0) {
        this.budgets = [];
        this.budgetCategories = [];
        this.isLoaded = true;
        return { budgets: this.budgets, budgetCategories: this.budgetCategories };
      }

      // Fetch categories for all budgets
      const categoryPromises = processedBudgets.map((budget: any) => {
        const budgetId = budget.id || budget._id;
        if (!budgetId) return Promise.resolve({ categories: [] });
        
        return fetch(`/api/accounting/budget/${budgetId}/categories`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        }).then(response => {
          if (!response.ok) return { categories: [] };
          return response.json();
        }).catch(() => ({ categories: [] }));
      });

      const categoriesResponses = await Promise.all(categoryPromises);
      const allCategories = categoriesResponses.reduce((acc: any[], response: any) => {
        const categories = response.categories || [];
        return acc.concat(categories);
      }, []);

      // Cache the data
      localStorage.setItem('income-form-budgets', JSON.stringify(processedBudgets));
      localStorage.setItem('income-form-budget-categories', JSON.stringify(allCategories));
      localStorage.setItem('income-form-cache-timestamp', Date.now().toString());

      this.budgets = processedBudgets;
      this.budgetCategories = allCategories;
      this.isLoaded = true;

      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    } catch (error) {
      console.error('Error loading budget data:', error);
      return { budgets: [], budgetCategories: [] };
    } finally {
      this.isLoading = false;
    }
  }
}

export function ProgressiveIncomeForm({
  income,
  onSubmit,
  onCancel,
  isLoading = false
}: ProgressiveIncomeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [budgetDataState, setBudgetDataState] = useState<{
    budgets: any[];
    budgetCategories: any[];
    isLoading: boolean;
    isLoaded: boolean;
  }>({
    budgets: [],
    budgetCategories: [],
    isLoading: false,
    isLoaded: false
  });

  // Initialize form data with instant defaults (no backend dependency)
  const [formData, setFormData] = useState<ProgressiveIncomeFormData>(() => ({
    date: income?.date ? new Date(income.date) : new Date(),
    source: income?.source || 'government_subvention',
    amount: income?.amount?.toString() || '',
    reference: income?.reference || '',
    description: income?.description || '',
    fiscalYear: income?.fiscalYear || '2024-2025',
    status: income?.status || 'draft',
    budget: income?.budget || '',
    budgetCategory: income?.budgetCategory || '',
    notes: income?.notes || '',
  }));

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();

  // Progressive data loader instance
  const dataLoader = ProgressiveDataLoader.getInstance();

  // Load budget data when budget dropdown is focused/clicked
  const handleBudgetDropdownOpen = useCallback(async () => {
    if (budgetDataState.isLoaded || budgetDataState.isLoading) return;

    setBudgetDataState(prev => ({ ...prev, isLoading: true }));

    try {
      const { budgets, budgetCategories } = await dataLoader.loadBudgetData();
      setBudgetDataState({
        budgets,
        budgetCategories,
        isLoading: false,
        isLoaded: true
      });

      // Set default values if not already set
      if (!formData.budget && budgets.length > 0) {
        const defaultBudgetId = budgets[0].id || budgets[0]._id;
        setFormData(prev => ({ ...prev, budget: defaultBudgetId }));
      }
      if (!formData.budgetCategory && budgetCategories.length > 0) {
        const defaultCategoryId = budgetCategories[0].id || budgetCategories[0]._id;
        setFormData(prev => ({ ...prev, budgetCategory: defaultCategoryId }));
      }
    } catch (error) {
      console.error('Error loading budget data:', error);
      setBudgetDataState(prev => ({ ...prev, isLoading: false }));
      toast({
        title: 'Error',
        description: 'Failed to load budget data. Please try again.',
        variant: 'destructive',
      });
    }
  }, [budgetDataState.isLoaded, budgetDataState.isLoading, formData.budget, formData.budgetCategory, dataLoader, toast]);

  // Ultra-fast update function
  const updateField = useCallback((field: keyof ProgressiveIncomeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field immediately
    setErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });
  }, []);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.source) newErrors.source = 'Source is required';
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0';
    if (!formData.reference.trim()) newErrors.reference = 'Reference is required';
    if (!formData.fiscalYear) newErrors.fiscalYear = 'Fiscal year is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
        appliedToBudget: !!formData.budget,
      };

      await onSubmit(processedValues);

      toast({
        title: income ? 'Income Updated' : 'Income Created',
        description: income ? 'The income has been updated successfully' : 'The income has been created successfully',
      });

    } catch (error: unknown) {
      console.error('Error saving income:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, isSubmitting, validateForm, onSubmit, income, toast]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{income ? 'Edit Income' : 'Record New Income'}</CardTitle>
          <Badge variant="default" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Progressive Loading
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Instant Loading Section - No Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Badge variant="outline" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Instant Load
              </Badge>
            </div>
            <Separator />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date Field */}
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full pl-3 text-left font-normal"
                    >
                      {formData.date ? (
                        format(formData.date, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && updateField('date', date)}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
              </div>

              {/* Fiscal Year Field */}
              <div className="space-y-2">
                <Label htmlFor="fiscalYear">Fiscal Year *</Label>
                <Select
                  value={formData.fiscalYear}
                  onValueChange={(value) => updateField('fiscalYear', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.fiscalYears.map(year => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.fiscalYear && <p className="text-sm text-destructive">{errors.fiscalYear}</p>}
              </div>
            </div>

            {/* Income Source and Amount */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="source">Income Source *</Label>
                <Select
                  value={formData.source}
                  onValueChange={(value) => updateField('source', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select income source" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.incomeSources.map(source => (
                      <SelectItem key={source.value} value={source.value}>
                        {source.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.source && <p className="text-sm text-destructive">{errors.source}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Amount (MWK) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount"
                  value={formData.amount}
                  onChange={(e) => updateField('amount', e.target.value)}
                />
                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
              </div>
            </div>

            {/* Reference and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Reference *</Label>
                <Input
                  id="reference"
                  placeholder="Enter reference number"
                  value={formData.reference}
                  onChange={(e) => updateField('reference', e.target.value)}
                />
                {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => updateField('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.statusOptions.map(status => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Progressive Loading Section - Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Budget Integration (Optional)</h3>
              <Badge variant="outline" className="text-xs">
                {budgetDataState.isLoaded ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Loaded
                  </>
                ) : (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    On-Demand
                  </>
                )}
              </Badge>
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Budget Selection */}
              <div className="space-y-2">
                <Label htmlFor="budget">Budget</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budget}
                    onValueChange={(value) => updateField('budget', value)}
                    onOpenChange={(open) => {
                      if (open) handleBudgetDropdownOpen();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgets.map(budget => (
                        <SelectItem key={budget.id || budget._id} value={budget.id || budget._id}>
                          {budget.name} ({budget.fiscalYear})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {/* Budget Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="budgetCategory">Budget Category</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budgetCategory}
                    onValueChange={(value) => updateField('budgetCategory', value)}
                    disabled={!budgetDataState.isLoaded}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgetCategories.map(category => (
                        <SelectItem key={category.id || category._id} value={category.id || category._id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{category.name}</span>
                            <span className="text-xs text-muted-foreground ml-2">
                              ({category.type || 'general'})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              value={formData.description || ''}
              onChange={(e) => updateField('description', e.target.value)}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {income ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            income ? 'Update Income' : 'Create Income'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

"use client"

import React, { useState } from 'react';
import { DashboardShell } from "@/components/dashboard-shell";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  GitMerge, 
  TrendingUp,
  BarChart4,
  AlertTriangle,
  CheckCircle,
  Activity,
  Target,
  Zap,
  Clock,
  FileText,
  Settings
} from "lucide-react";
import Link from "next/link";
import { ReconciliationDashboard } from '@/components/accounting/income/reconciliation-dashboard';
import { useReconciliationStatistics } from '@/lib/hooks/accounting/use-income-reconciliation';

// Format currency for Malawian Kwacha
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

export function IncomeReconciliationPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');
  const [completedSessionId, setCompletedSessionId] = useState<string | null>(null);

  // Calculate date range based on selected period
  const getDateRange = () => {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
    }
    
    return { startDate, endDate };
  };

  const { startDate, endDate } = getDateRange();

  // Fetch reconciliation statistics
  const { 
    data: statisticsData, 
    isLoading: isLoadingStatistics 
  } = useReconciliationStatistics(startDate, endDate);

  const statistics = statisticsData?.statistics;

  const handleReconciliationComplete = (sessionId: string) => {
    setCompletedSessionId(sessionId);
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Income Reconciliation</h1>
            <p className="text-muted-foreground">
              Multi-source income reconciliation with advanced matching algorithms and variance analysis
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/accounting/income/overview">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Income</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistics Overview */}
        {statistics && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statistics.totalSessions}
                </div>
                <p className="text-xs text-muted-foreground">
                  {statistics.completedSessions} completed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statistics.totalMatches}
                </div>
                <p className="text-xs text-muted-foreground">
                  {statistics.autoMatches} auto-matched
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Confidence</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(statistics.averageConfidence * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Match accuracy score
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Variances</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statistics.totalVariances}
                </div>
                <p className="text-xs text-muted-foreground">
                  Requiring attention
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        <Tabs defaultValue="reconciliation" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="reconciliation">New Reconciliation</TabsTrigger>
            <TabsTrigger value="sessions">Recent Sessions</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* New Reconciliation Tab */}
          <TabsContent value="reconciliation" className="space-y-4">
            <ReconciliationDashboard onReconciliationComplete={handleReconciliationComplete} />
          </TabsContent>

          {/* Recent Sessions Tab */}
          <TabsContent value="sessions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Reconciliation Sessions
                </CardTitle>
                <CardDescription>
                  View and manage recent reconciliation sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Recent sessions will be displayed here
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Complete a reconciliation session to see results
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart4 className="h-5 w-5" />
                    Reconciliation Performance
                  </CardTitle>
                  <CardDescription>
                    Key performance metrics for reconciliation processes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {statistics ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Automation Rate</span>
                        <span className="text-sm">
                          {statistics.totalMatches > 0 
                            ? ((statistics.autoMatches / statistics.totalMatches) * 100).toFixed(1)
                            : 0}%
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Session Completion Rate</span>
                        <span className="text-sm">
                          {statistics.totalSessions > 0 
                            ? ((statistics.completedSessions / statistics.totalSessions) * 100).toFixed(1)
                            : 0}%
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Average Processing Time</span>
                        <span className="text-sm">
                          {(statistics.processingTime / 1000).toFixed(1)}s
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">
                        No performance data available
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Quality Metrics
                  </CardTitle>
                  <CardDescription>
                    Data quality and accuracy indicators
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {statistics ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Match Confidence</span>
                        <Badge variant="default">
                          {(statistics.averageConfidence * 100).toFixed(1)}%
                        </Badge>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Manual Review Rate</span>
                        <span className="text-sm">
                          {statistics.totalMatches > 0 
                            ? ((statistics.manualMatches / statistics.totalMatches) * 100).toFixed(1)
                            : 0}%
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Variance Detection</span>
                        <Badge variant={statistics.totalVariances > 0 ? "destructive" : "default"}>
                          {statistics.totalVariances} found
                        </Badge>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Target className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">
                        No quality metrics available
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Getting Started Guide */}
        {!completedSessionId && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitMerge className="h-5 w-5" />
                Getting Started with Income Reconciliation
              </CardTitle>
              <CardDescription>
                Learn how to use the advanced reconciliation features effectively
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Reconciliation Process</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <span><strong>Setup:</strong> Configure session name, source, and matching rules</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span><strong>Upload:</strong> Import internal and external transaction files</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span><strong>Process:</strong> Automated matching with confidence scoring</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      <span><strong>Review:</strong> Manual review and approval of matches</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Key Features</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div>• <strong>Multi-Source Support:</strong> Bank, government, and external systems</div>
                    <div>• <strong>Advanced Matching:</strong> Fuzzy logic and similarity algorithms</div>
                    <div>• <strong>Confidence Scoring:</strong> Statistical confidence in matches</div>
                    <div>• <strong>Variance Detection:</strong> Automatic identification of discrepancies</div>
                    <div>• <strong>Duplicate Detection:</strong> Built-in duplicate transaction detection</div>
                    <div>• <strong>Export Options:</strong> Multiple formats for reporting</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardShell>
  );
}

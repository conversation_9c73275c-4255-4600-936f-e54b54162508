'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Target,
  Brain,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Calendar,
  DollarSign
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart as RechartsPieChart, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface PredictiveAnalytics {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  predictions: {
    nextMonth: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
    nextQuarter: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
    endOfYear: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
  };
  trends: {
    incomeGrowth: number;
    expenseGrowth: number;
    seasonality: 'high' | 'medium' | 'low';
    volatility: 'high' | 'medium' | 'low';
  };
  riskFactors: {
    id: string;
    type: 'budget_overrun' | 'revenue_shortfall' | 'seasonal_impact' | 'external_factor';
    severity: 'high' | 'medium' | 'low';
    probability: number;
    impact: number;
    description: string;
    mitigation: string;
  }[];
  opportunities: {
    id: string;
    type: 'cost_savings' | 'revenue_increase' | 'efficiency_gain' | 'optimization';
    potential: number;
    effort: 'high' | 'medium' | 'low';
    timeframe: 'immediate' | 'short_term' | 'long_term';
    description: string;
    actionItems: string[];
  }[];
  aiInsights: {
    summary: string;
    keyFindings: string[];
    recommendations: {
      priority: 'high' | 'medium' | 'low';
      category: 'budget' | 'process' | 'strategy' | 'risk';
      title: string;
      description: string;
      expectedImpact: number;
    }[];
  };
  lastUpdated: Date;
}

interface PredictiveBudgetAnalyticsProps {
  budgetId: string;
  refreshInterval?: number;
  showAdvancedMetrics?: boolean;
  className?: string;
}

export function PredictiveBudgetAnalytics({
  budgetId,
  refreshInterval = 300000, // 5 minutes
  showAdvancedMetrics = true,
  className = ''
}: PredictiveBudgetAnalyticsProps) {
  const [analytics, setAnalytics] = useState<PredictiveAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('predictions');
  
  const isMobile = useIsMobile();

  // Fetch predictive analytics data
  const fetchAnalytics = async () => {
    try {
      setError(null);
      const response = await fetch(`/api/accounting/analytics/predictive-budget/${budgetId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch predictive analytics');
      }
      
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching predictive analytics:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch analytics');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchAnalytics();
    
    const interval = setInterval(fetchAnalytics, refreshInterval);
    return () => clearInterval(interval);
  }, [budgetId, refreshInterval]);

  // Get trend icon and color
  const getTrendIcon = (value: number) => {
    if (value > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (value < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get severity badge variant
  const getSeverityVariant = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'default';
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!analytics) {
    return null;
  }

  // Prepare chart data
  const predictionChartData = [
    {
      period: 'Current',
      income: 0,
      expenses: 0,
      variance: 0
    },
    {
      period: 'Next Month',
      income: analytics.predictions.nextMonth.income,
      expenses: analytics.predictions.nextMonth.expenses,
      variance: analytics.predictions.nextMonth.variance
    },
    {
      period: 'Next Quarter',
      income: analytics.predictions.nextQuarter.income,
      expenses: analytics.predictions.nextQuarter.expenses,
      variance: analytics.predictions.nextQuarter.variance
    },
    {
      period: 'End of Year',
      income: analytics.predictions.endOfYear.income,
      expenses: analytics.predictions.endOfYear.expenses,
      variance: analytics.predictions.endOfYear.variance
    }
  ];

  const riskChartData = analytics.riskFactors.map(risk => ({
    name: risk.type.replace('_', ' '),
    probability: risk.probability * 100,
    impact: risk.impact
  }));

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-purple-500" />
              <span>Predictive Budget Analytics</span>
            </CardTitle>
            <CardDescription>
              AI-powered budget predictions and insights for {analytics.budgetName}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              AI Powered
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {analytics.fiscalYear}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="predictions">Predictions</TabsTrigger>
            <TabsTrigger value="risks">Risk Analysis</TabsTrigger>
            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
          </TabsList>

          {/* Predictions Tab */}
          <TabsContent value="predictions" className="space-y-6">
            {/* Prediction Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    <span>Next Month</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Variance</span>
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(analytics.predictions.nextMonth.variance)}
                        <span className="font-semibold">
                          {formatCurrency(Math.abs(analytics.predictions.nextMonth.variance))}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Confidence</span>
                      <span className={`font-semibold ${getConfidenceColor(analytics.predictions.nextMonth.confidence)}`}>
                        {(analytics.predictions.nextMonth.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4 text-green-500" />
                    <span>Next Quarter</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Variance</span>
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(analytics.predictions.nextQuarter.variance)}
                        <span className="font-semibold">
                          {formatCurrency(Math.abs(analytics.predictions.nextQuarter.variance))}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Confidence</span>
                      <span className={`font-semibold ${getConfidenceColor(analytics.predictions.nextQuarter.confidence)}`}>
                        {(analytics.predictions.nextQuarter.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Target className="h-4 w-4 text-purple-500" />
                    <span>End of Year</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Variance</span>
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(analytics.predictions.endOfYear.variance)}
                        <span className="font-semibold">
                          {formatCurrency(Math.abs(analytics.predictions.endOfYear.variance))}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Confidence</span>
                      <span className={`font-semibold ${getConfidenceColor(analytics.predictions.endOfYear.confidence)}`}>
                        {(analytics.predictions.endOfYear.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Prediction Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Budget Predictions</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={predictionChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis tickFormatter={(value) => formatCurrency(value, { compact: true })} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Area type="monotone" dataKey="income" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="expenses" stackId="2" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} />
                    <Line type="monotone" dataKey="variance" stroke="#8b5cf6" strokeWidth={3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Risk Analysis Tab */}
          <TabsContent value="risks" className="space-y-6">
            <div className="space-y-4">
              {analytics.riskFactors.map((risk) => (
                <Card key={risk.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base capitalize">
                        {risk.type.replace('_', ' ')}
                      </CardTitle>
                      <Badge variant={getSeverityVariant(risk.severity)}>
                        {risk.severity} risk
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">{risk.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <span className="text-sm font-medium">Probability</span>
                          <Progress value={risk.probability * 100} className="mt-1" />
                          <span className="text-xs text-muted-foreground">
                            {(risk.probability * 100).toFixed(0)}%
                          </span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">Impact</span>
                          <Progress value={risk.impact} className="mt-1" />
                          <span className="text-xs text-muted-foreground">
                            {formatCurrency(risk.impact)}
                          </span>
                        </div>
                      </div>
                      
                      <div className="bg-blue-50 p-3 rounded-md">
                        <h4 className="text-sm font-medium text-blue-800 mb-1">Mitigation Strategy</h4>
                        <p className="text-sm text-blue-700">{risk.mitigation}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Opportunities Tab */}
          <TabsContent value="opportunities" className="space-y-6">
            <div className="space-y-4">
              {analytics.opportunities.map((opportunity) => (
                <Card key={opportunity.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base capitalize">
                        {opportunity.type.replace('_', ' ')}
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{opportunity.timeframe}</Badge>
                        <Badge variant="secondary">{opportunity.effort} effort</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">{opportunity.description}</p>
                      
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">Potential Value</span>
                          <span className="font-semibold text-green-600">
                            {formatCurrency(opportunity.potential)}
                          </span>
                        </div>
                      </div>
                      
                      <div className="bg-green-50 p-3 rounded-md">
                        <h4 className="text-sm font-medium text-green-800 mb-2">Action Items</h4>
                        <ul className="space-y-1">
                          {opportunity.actionItems.map((item, index) => (
                            <li key={index} className="text-sm text-green-700 flex items-start space-x-2">
                              <span className="text-green-500 mt-1">•</span>
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-purple-500" />
                  <span>AI Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{analytics.aiInsights.summary}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Key Findings</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {analytics.aiInsights.keyFindings.map((finding, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span className="text-sm">{finding}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">AI Recommendations</h3>
              {analytics.aiInsights.recommendations.map((rec, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{rec.title}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'secondary' : 'outline'}>
                          {rec.priority} priority
                        </Badge>
                        <Badge variant="outline">{rec.category}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Expected Impact:</span>
                      <span className="font-semibold text-green-600">
                        {formatCurrency(rec.expectedImpact)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Last Updated */}
        <div className="mt-6 text-xs text-muted-foreground text-center">
          Last updated: {analytics.lastUpdated.toLocaleString()}
        </div>
      </CardContent>
    </Card>
  );
}

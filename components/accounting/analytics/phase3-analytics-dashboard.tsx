'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Zap, 
  Network, 
  BarChart3, 
  TrendingUp, 
  AlertTriangle,
  Target,
  Activity,
  Sparkles,
  Settings,
  RefreshCw
} from 'lucide-react';
import { PredictiveBudgetAnalytics } from './predictive-budget-analytics';
import { AutomatedApprovalWorkflow } from '../workflows/automated-approval-workflow';
import { AIFinancialInsights } from './ai-financial-insights';
import { CrossModuleIntegrationHub } from '../integration/cross-module-integration-hub';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface Phase3AnalyticsDashboardProps {
  fiscalYear: string;
  budgetId?: string;
  userId: string;
  userRole: string;
  refreshInterval?: number;
  className?: string;
}

interface DashboardMetrics {
  totalInsights: number;
  pendingApprovals: number;
  systemHealth: number;
  automationRate: number;
  lastUpdated: Date;
}

export function Phase3AnalyticsDashboard({
  fiscalYear,
  budgetId,
  userId,
  userRole,
  refreshInterval = 300000, // 5 minutes
  className = ''
}: Phase3AnalyticsDashboardProps) {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const isMobile = useIsMobile();

  // Fetch dashboard metrics
  const fetchDashboardMetrics = async () => {
    try {
      setError(null);
      const params = new URLSearchParams({
        fiscalYear,
        userId,
        userRole,
        ...(budgetId && { budgetId })
      });

      const response = await fetch(`/api/accounting/analytics/phase3-metrics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard metrics');
      }
      
      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch dashboard metrics');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchDashboardMetrics();
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchDashboardMetrics();
    
    const interval = setInterval(fetchDashboardMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [fiscalYear, budgetId, userId, userRole, refreshInterval]);

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2 text-2xl">
                <Sparkles className="h-6 w-6 text-purple-500" />
                <span>Phase 3: Advanced Analytics & Automation</span>
              </CardTitle>
              <CardDescription className="text-base">
                AI-powered financial insights, predictive analytics, and automated workflows
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-sm">
                <Brain className="h-4 w-4 mr-1" />
                AI Powered
              </Badge>
              <Badge variant="secondary" className="text-sm">
                {fiscalYear}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Metrics Summary */}
        {metrics && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Brain className="h-4 w-4 text-purple-500" />
                    <span>AI Insights</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {metrics.totalInsights}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Active insights generated
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-blue-500" />
                    <span>Pending Approvals</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {metrics.pendingApprovals}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Awaiting workflow action
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Activity className="h-4 w-4 text-green-500" />
                    <span>System Health</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${getHealthScoreColor(metrics.systemHealth)}`}>
                    {metrics.systemHealth}%
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Overall system status
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Target className="h-4 w-4 text-orange-500" />
                    <span>Automation Rate</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {metrics.automationRate}%
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Processes automated
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-4 text-xs text-muted-foreground text-center">
              Last updated: {metrics.lastUpdated.toLocaleString()}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="predictive" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Predictive</span>
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Workflows</span>
          </TabsTrigger>
          <TabsTrigger value="integration" className="flex items-center space-x-2">
            <Network className="h-4 w-4" />
            <span>Integration</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* AI Financial Insights */}
            <AIFinancialInsights
              fiscalYear={fiscalYear}
              budgetId={budgetId}
              refreshInterval={refreshInterval}
              showAdvancedMetrics={true}
              className="h-fit"
            />

            {/* Quick Workflow Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-blue-500" />
                  <span>Workflow Summary</span>
                </CardTitle>
                <CardDescription>
                  Quick overview of approval workflows
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AutomatedApprovalWorkflow
                  userId={userId}
                  userRole={userRole}
                  showOnlyMyItems={true}
                  refreshInterval={refreshInterval}
                  className="border-0 shadow-none p-0"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Predictive Analytics Tab */}
        <TabsContent value="predictive" className="space-y-6">
          {budgetId ? (
            <PredictiveBudgetAnalytics
              budgetId={budgetId}
              refreshInterval={refreshInterval}
              showAdvancedMetrics={true}
            />
          ) : (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select a budget to view predictive analytics.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-6">
          <AutomatedApprovalWorkflow
            userId={userId}
            userRole={userRole}
            showOnlyMyItems={false}
            refreshInterval={refreshInterval}
          />
        </TabsContent>

        {/* Integration Tab */}
        <TabsContent value="integration" className="space-y-6">
          <CrossModuleIntegrationHub
            refreshInterval={60000} // 1 minute for integration monitoring
            showAdvancedMetrics={true}
          />
        </TabsContent>
      </Tabs>

      {/* Phase 3 Features Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            <span>Phase 3 Features</span>
          </CardTitle>
          <CardDescription>
            Advanced capabilities now available in your accounting system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <Brain className="h-8 w-8 text-purple-500" />
              <div>
                <div className="font-semibold text-purple-800">AI Insights</div>
                <div className="text-sm text-purple-600">Smart financial analysis</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <TrendingUp className="h-8 w-8 text-blue-500" />
              <div>
                <div className="font-semibold text-blue-800">Predictive Analytics</div>
                <div className="text-sm text-blue-600">Future budget forecasting</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <Zap className="h-8 w-8 text-green-500" />
              <div>
                <div className="font-semibold text-green-800">Auto Workflows</div>
                <div className="text-sm text-green-600">Intelligent approvals</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
              <Network className="h-8 w-8 text-orange-500" />
              <div>
                <div className="font-semibold text-orange-800">Integration Hub</div>
                <div className="text-sm text-orange-600">Cross-module sync</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

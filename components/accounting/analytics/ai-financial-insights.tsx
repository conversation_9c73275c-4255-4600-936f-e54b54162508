'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Lightbulb,
  Target,
  Zap,
  BarChart3,
  PieChart,
  Activity,
  DollarSign,
  Calendar,
  Users,
  Building,
  Sparkles
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { formatCurrency } from '@/lib/utils/currency';
import { useIsMobile } from '@/lib/hooks/use-responsive';

interface AIInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk' | 'efficiency' | 'compliance';
  category: 'budget' | 'cash_flow' | 'spending' | 'revenue' | 'performance' | 'forecast';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short_term' | 'long_term';
  actionable: boolean;
  recommendations: string[];
  dataPoints: number;
  generatedAt: Date;
  relevantPeriod: {
    start: Date;
    end: Date;
  };
  metrics: {
    currentValue: number;
    previousValue?: number;
    changePercentage?: number;
    benchmark?: number;
  };
}

interface FinancialHealthScore {
  overall: number;
  categories: {
    budgetCompliance: number;
    cashFlowStability: number;
    spendingEfficiency: number;
    revenueGrowth: number;
    riskManagement: number;
  };
  trends: {
    improving: string[];
    declining: string[];
    stable: string[];
  };
  alerts: {
    critical: number;
    warning: number;
    info: number;
  };
}

interface PredictiveModel {
  id: string;
  name: string;
  type: 'revenue_forecast' | 'expense_prediction' | 'budget_variance' | 'cash_flow';
  accuracy: number;
  lastTrained: Date;
  predictions: {
    period: string;
    value: number;
    confidence: number;
    range: {
      min: number;
      max: number;
    };
  }[];
  features: string[];
  performance: {
    mape: number; // Mean Absolute Percentage Error
    rmse: number; // Root Mean Square Error
    r2: number;   // R-squared
  };
}

interface AIFinancialInsightsProps {
  fiscalYear: string;
  budgetId?: string;
  refreshInterval?: number;
  showAdvancedMetrics?: boolean;
  className?: string;
}

export function AIFinancialInsights({
  fiscalYear,
  budgetId,
  refreshInterval = 300000, // 5 minutes
  showAdvancedMetrics = true,
  className = ''
}: AIFinancialInsightsProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [healthScore, setHealthScore] = useState<FinancialHealthScore | null>(null);
  const [models, setModels] = useState<PredictiveModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('insights');
  const [selectedInsightType, setSelectedInsightType] = useState<string>('all');
  
  const isMobile = useIsMobile();

  // Fetch AI insights and analytics
  const fetchAIInsights = async () => {
    try {
      setError(null);
      const params = new URLSearchParams({
        fiscalYear,
        ...(budgetId && { budgetId }),
        includeModels: showAdvancedMetrics.toString()
      });

      const response = await fetch(`/api/accounting/analytics/ai-insights?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch AI insights');
      }
      
      const data = await response.json();
      setInsights(data.insights);
      setHealthScore(data.healthScore);
      setModels(data.models || []);
    } catch (error) {
      console.error('Error fetching AI insights:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch AI insights');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load and refresh interval
  useEffect(() => {
    fetchAIInsights();
    
    const interval = setInterval(fetchAIInsights, refreshInterval);
    return () => clearInterval(interval);
  }, [fiscalYear, budgetId, refreshInterval]);

  // Get insight icon
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case 'anomaly': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'opportunity': return <Lightbulb className="h-4 w-4 text-yellow-500" />;
      case 'risk': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'efficiency': return <Target className="h-4 w-4 text-green-500" />;
      case 'compliance': return <Building className="h-4 w-4 text-purple-500" />;
      default: return <Brain className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get impact badge variant
  const getImpactVariant = (impact: string) => {
    switch (impact) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'default';
    }
  };

  // Get urgency color
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'text-red-600';
      case 'short_term': return 'text-orange-600';
      case 'long_term': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  // Filter insights by type
  const getFilteredInsights = () => {
    if (selectedInsightType === 'all') return insights;
    return insights.filter(insight => insight.type === selectedInsightType);
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-32 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-purple-500" />
              <span>AI Financial Insights</span>
            </CardTitle>
            <CardDescription>
              Advanced AI-powered financial analysis and recommendations
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              AI Powered
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {fiscalYear}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
            <TabsTrigger value="health">Health Score</TabsTrigger>
            <TabsTrigger value="models">Predictive Models</TabsTrigger>
            <TabsTrigger value="recommendations">Actions</TabsTrigger>
          </TabsList>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            {/* Insight Type Filter */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedInsightType === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedInsightType('all')}
              >
                All ({insights.length})
              </Button>
              {['trend', 'anomaly', 'opportunity', 'risk', 'efficiency', 'compliance'].map((type) => {
                const count = insights.filter(i => i.type === type).length;
                return (
                  <Button
                    key={type}
                    variant={selectedInsightType === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedInsightType(type)}
                    disabled={count === 0}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)} ({count})
                  </Button>
                );
              })}
            </div>

            {/* Insights List */}
            <div className="space-y-4">
              {getFilteredInsights().map((insight) => (
                <Card key={insight.id} className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          {getInsightIcon(insight.type)}
                          <CardTitle className="text-base">{insight.title}</CardTitle>
                          <Badge variant={getImpactVariant(insight.impact)}>
                            {insight.impact} impact
                          </Badge>
                          <Badge variant="outline" className={getUrgencyColor(insight.urgency)}>
                            {insight.urgency}
                          </Badge>
                        </div>
                        <CardDescription>{insight.description}</CardDescription>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="text-sm font-medium">
                          Confidence: {(insight.confidence * 100).toFixed(0)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {insight.dataPoints} data points
                        </div>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Metrics */}
                    {insight.metrics && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <span className="text-sm text-muted-foreground">Current</span>
                          <div className="font-semibold">
                            {formatCurrency(insight.metrics.currentValue)}
                          </div>
                        </div>
                        {insight.metrics.previousValue && (
                          <div>
                            <span className="text-sm text-muted-foreground">Previous</span>
                            <div className="font-semibold">
                              {formatCurrency(insight.metrics.previousValue)}
                            </div>
                          </div>
                        )}
                        {insight.metrics.changePercentage && (
                          <div>
                            <span className="text-sm text-muted-foreground">Change</span>
                            <div className={`font-semibold flex items-center space-x-1 ${
                              insight.metrics.changePercentage > 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {insight.metrics.changePercentage > 0 ? (
                                <TrendingUp className="h-3 w-3" />
                              ) : (
                                <TrendingDown className="h-3 w-3" />
                              )}
                              <span>{Math.abs(insight.metrics.changePercentage).toFixed(1)}%</span>
                            </div>
                          </div>
                        )}
                        {insight.metrics.benchmark && (
                          <div>
                            <span className="text-sm text-muted-foreground">Benchmark</span>
                            <div className="font-semibold">
                              {formatCurrency(insight.metrics.benchmark)}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Recommendations */}
                    {insight.actionable && insight.recommendations.length > 0 && (
                      <div className="bg-blue-50 p-3 rounded-md">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">AI Recommendations</h4>
                        <ul className="space-y-1">
                          {insight.recommendations.map((rec, index) => (
                            <li key={index} className="text-sm text-blue-700 flex items-start space-x-2">
                              <span className="text-blue-500 mt-1">•</span>
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Confidence Bar */}
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>AI Confidence</span>
                        <span>{(insight.confidence * 100).toFixed(0)}%</span>
                      </div>
                      <Progress value={insight.confidence * 100} />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Health Score Tab */}
          <TabsContent value="health" className="space-y-6">
            {healthScore && (
              <>
                {/* Overall Health Score */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Activity className="h-5 w-5 text-green-500" />
                      <span>Financial Health Score</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center space-y-4">
                      <div className={`text-6xl font-bold ${getHealthScoreColor(healthScore.overall)}`}>
                        {healthScore.overall}
                      </div>
                      <div className="text-lg text-muted-foreground">
                        Overall Financial Health
                      </div>
                      <Progress value={healthScore.overall} className="w-full" />
                    </div>
                  </CardContent>
                </Card>

                {/* Category Scores */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(healthScore.categories).map(([category, score]) => (
                    <Card key={category}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm capitalize">
                          {category.replace(/([A-Z])/g, ' $1').trim()}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className={`text-2xl font-bold ${getHealthScoreColor(score)}`}>
                            {score}
                          </div>
                          <Progress value={score} />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Trends */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-green-600 flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4" />
                        <span>Improving</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {healthScore.trends.improving.map((item, index) => (
                          <li key={index} className="text-sm text-green-700">• {item}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-red-600 flex items-center space-x-2">
                        <TrendingDown className="h-4 w-4" />
                        <span>Declining</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {healthScore.trends.declining.map((item, index) => (
                          <li key={index} className="text-sm text-red-700">• {item}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-blue-600 flex items-center space-x-2">
                        <Activity className="h-4 w-4" />
                        <span>Stable</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {healthScore.trends.stable.map((item, index) => (
                          <li key={index} className="text-sm text-blue-700">• {item}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                {/* Alerts Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Alert Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {healthScore.alerts.critical}
                        </div>
                        <div className="text-sm text-muted-foreground">Critical</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                          {healthScore.alerts.warning}
                        </div>
                        <div className="text-sm text-muted-foreground">Warning</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {healthScore.alerts.info}
                        </div>
                        <div className="text-sm text-muted-foreground">Info</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          {/* Predictive Models Tab */}
          <TabsContent value="models" className="space-y-6">
            {showAdvancedMetrics && models.length > 0 && (
              <div className="space-y-4">
                {models.map((model) => (
                  <Card key={model.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">{model.name}</CardTitle>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {(model.accuracy * 100).toFixed(1)}% accurate
                          </Badge>
                          <Badge variant="secondary">
                            {model.type.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Model Performance */}
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <span className="text-sm text-muted-foreground">MAPE</span>
                          <div className="font-semibold">{model.performance.mape.toFixed(2)}%</div>
                        </div>
                        <div>
                          <span className="text-sm text-muted-foreground">RMSE</span>
                          <div className="font-semibold">{model.performance.rmse.toFixed(2)}</div>
                        </div>
                        <div>
                          <span className="text-sm text-muted-foreground">R²</span>
                          <div className="font-semibold">{model.performance.r2.toFixed(3)}</div>
                        </div>
                      </div>

                      {/* Predictions Chart */}
                      <ResponsiveContainer width="100%" height={200}>
                        <LineChart data={model.predictions}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis tickFormatter={(value) => formatCurrency(value, { compact: true })} />
                          <Tooltip formatter={(value) => formatCurrency(value as number)} />
                          <Line type="monotone" dataKey="value" stroke="#8b5cf6" strokeWidth={2} />
                          <Line type="monotone" dataKey="range.min" stroke="#d1d5db" strokeDasharray="5 5" />
                          <Line type="monotone" dataKey="range.max" stroke="#d1d5db" strokeDasharray="5 5" />
                        </LineChart>
                      </ResponsiveContainer>

                      {/* Model Features */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">Model Features</h4>
                        <div className="flex flex-wrap gap-1">
                          {model.features.map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        Last trained: {model.lastTrained.toLocaleDateString()}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="space-y-6">
            <div className="space-y-4">
              {insights
                .filter(insight => insight.actionable && insight.recommendations.length > 0)
                .sort((a, b) => {
                  const urgencyOrder = { immediate: 3, short_term: 2, long_term: 1 };
                  const impactOrder = { high: 3, medium: 2, low: 1 };
                  return (urgencyOrder[b.urgency] + impactOrder[b.impact]) - (urgencyOrder[a.urgency] + impactOrder[a.impact]);
                })
                .map((insight) => (
                  <Card key={insight.id} className="border-l-4 border-l-green-500">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">{insight.title}</CardTitle>
                        <div className="flex items-center space-x-2">
                          <Badge variant={getImpactVariant(insight.impact)}>
                            {insight.impact} impact
                          </Badge>
                          <Badge variant="outline" className={getUrgencyColor(insight.urgency)}>
                            {insight.urgency}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">{insight.description}</p>
                        
                        <div className="bg-green-50 p-3 rounded-md">
                          <h4 className="text-sm font-medium text-green-800 mb-2">Recommended Actions</h4>
                          <ul className="space-y-1">
                            {insight.recommendations.map((rec, index) => (
                              <li key={index} className="text-sm text-green-700 flex items-start space-x-2">
                                <span className="text-green-500 mt-1">•</span>
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            Confidence: {(insight.confidence * 100).toFixed(0)}%
                          </span>
                          <Button size="sm" variant="outline">
                            <Target className="h-4 w-4 mr-2" />
                            Create Action Plan
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

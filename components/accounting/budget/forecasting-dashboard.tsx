'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Brain,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Lightbulb,
  Target,
  Activity
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { useBudgetStore } from '@/lib/stores/budget-store'

interface ForecastPeriod {
  period: string
  startDate: string
  endDate: string
  forecastedIncome: number
  forecastedExpense: number
  forecastedNet: number
  confidence: number
  factors: string[]
}

interface BudgetForecast {
  budgetId: string
  budgetName: string
  forecastType: 'linear' | 'seasonal' | 'growth' | 'ai'
  forecastPeriods: ForecastPeriod[]
  totalForecastedIncome: number
  totalForecastedExpense: number
  totalForecastedNet: number
  averageConfidence: number
  riskFactors: string[]
  recommendations: string[]
}

interface ForecastingDashboardProps {
  budgetId?: string
}

// Helper function to format currency
const formatMWK = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Helper function to format compact currency
const formatCompactMWK = (value: number) => {
  if (Math.abs(value) >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`
  }
  if (Math.abs(value) >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`
  }
  return formatMWK(value)
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 border rounded-lg shadow-lg">
        <p className="font-semibold mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="capitalize">{entry.dataKey.replace(/([A-Z])/g, ' $1').trim()}:</span>
            <span className="font-semibold">{formatMWK(entry.value)}</span>
          </div>
        ))}
      </div>
    )
  }
  return null
}

export function ForecastingDashboard({ budgetId }: ForecastingDashboardProps) {
  const [selectedBudgetId, setSelectedBudgetId] = useState(budgetId)
  const [forecast, setForecast] = useState<BudgetForecast | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [forecastType, setForecastType] = useState<'linear' | 'seasonal' | 'growth' | 'ai'>('ai')
  const [forecastMonths, setForecastMonths] = useState(12)
  
  const { budgets, fetchBudgets } = useBudgetStore()

  useEffect(() => {
    fetchBudgets()
  }, [fetchBudgets])

  useEffect(() => {
    if (selectedBudgetId) {
      generateForecast()
    }
  }, [selectedBudgetId, forecastType, forecastMonths])

  const generateForecast = async () => {
    if (!selectedBudgetId) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/budget/forecasting/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgetId: selectedBudgetId,
          forecastMonths,
          forecastType
        })
      })

      if (response.ok) {
        const forecastData = await response.json()
        setForecast(forecastData)
      } else {
        throw new Error('Failed to generate forecast')
      }

    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate budget forecast',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600'
    if (confidence >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return 'default'
    if (confidence >= 60) return 'secondary'
    return 'destructive'
  }

  const getTrendIcon = (value: number) => {
    return value >= 0 ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Brain className="h-6 w-6" />
            Budget Forecasting & Projections
          </h2>
          <p className="text-muted-foreground">
            AI-powered budget predictions and scenario analysis
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedBudgetId} onValueChange={setSelectedBudgetId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select a budget" />
            </SelectTrigger>
            <SelectContent>
              {budgets.map((budget) => (
                <SelectItem key={budget.id} value={budget.id}>
                  {budget.name} ({budget.fiscalYear})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="icon"
            onClick={generateForecast}
            disabled={isLoading || !selectedBudgetId}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {!selectedBudgetId ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select a Budget</h3>
              <p className="text-muted-foreground">
                Choose a budget from the dropdown to generate forecasts
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="forecast" className="space-y-4">
          <TabsList>
            <TabsTrigger value="forecast">Forecast</TabsTrigger>
            <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="forecast" className="space-y-4">
            {/* Forecast Controls */}
            <Card>
              <CardHeader>
                <CardTitle>Forecast Configuration</CardTitle>
                <CardDescription>
                  Configure forecast parameters and methodology
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Forecast Type</label>
                    <Select value={forecastType} onValueChange={(value: any) => setForecastType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ai">AI Ensemble</SelectItem>
                        <SelectItem value="seasonal">Seasonal</SelectItem>
                        <SelectItem value="growth">Growth Trend</SelectItem>
                        <SelectItem value="linear">Linear Trend</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Forecast Period</label>
                    <Select value={forecastMonths.toString()} onValueChange={(value) => setForecastMonths(parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="6">6 Months</SelectItem>
                        <SelectItem value="12">12 Months</SelectItem>
                        <SelectItem value="18">18 Months</SelectItem>
                        <SelectItem value="24">24 Months</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button onClick={generateForecast} disabled={isLoading} className="w-full">
                      {isLoading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Brain className="h-4 w-4 mr-2" />}
                      Generate Forecast
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Forecast Results */}
            {forecast && (
              <>
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Forecasted Income</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCompactMWK(forecast.totalForecastedIncome)}
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        {getTrendIcon(forecast.totalForecastedIncome)}
                        <span className="text-xs text-muted-foreground">
                          {forecastMonths} month projection
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Forecasted Expenses</CardTitle>
                      <TrendingDown className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-red-600">
                        {formatCompactMWK(forecast.totalForecastedExpense)}
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        {getTrendIcon(-forecast.totalForecastedExpense)}
                        <span className="text-xs text-muted-foreground">
                          {forecastMonths} month projection
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Net Position</CardTitle>
                      <Target className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className={`text-2xl font-bold ${forecast.totalForecastedNet >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCompactMWK(forecast.totalForecastedNet)}
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        {getTrendIcon(forecast.totalForecastedNet)}
                        <span className="text-xs text-muted-foreground">
                          Projected surplus/deficit
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Confidence Level</CardTitle>
                      <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className={`text-2xl font-bold ${getConfidenceColor(forecast.averageConfidence)}`}>
                        {forecast.averageConfidence.toFixed(1)}%
                      </div>
                      <div className="mt-2">
                        <Progress value={forecast.averageConfidence} className="h-2" />
                        <Badge variant={getConfidenceBadge(forecast.averageConfidence)} className="mt-2">
                          {forecast.averageConfidence >= 80 ? 'High' : forecast.averageConfidence >= 60 ? 'Medium' : 'Low'} Confidence
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Forecast Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle>Forecast Visualization</CardTitle>
                    <CardDescription>
                      {forecast.forecastType.toUpperCase()} forecast for {forecast.budgetName}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={forecast.forecastPeriods}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                            dataKey="period" 
                            tick={{ fontSize: 12 }}
                            angle={-45}
                            textAnchor="end"
                            height={60}
                          />
                          <YAxis tickFormatter={formatCompactMWK} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Area 
                            type="monotone" 
                            dataKey="forecastedIncome" 
                            stackId="1"
                            stroke="#10b981" 
                            fill="#10b981"
                            fillOpacity={0.3}
                            name="Forecasted Income" 
                          />
                          <Area 
                            type="monotone" 
                            dataKey="forecastedExpense" 
                            stackId="2"
                            stroke="#ef4444" 
                            fill="#ef4444"
                            fillOpacity={0.3}
                            name="Forecasted Expense" 
                          />
                          <Line 
                            type="monotone" 
                            dataKey="forecastedNet" 
                            stroke="#3b82f6" 
                            strokeWidth={2}
                            name="Net Position" 
                          />
                          <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                {/* Risk Factors and Recommendations */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {forecast.riskFactors.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                          Risk Factors
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {forecast.riskFactors.map((risk, index) => (
                            <Alert key={index} variant="destructive">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertDescription>{risk}</AlertDescription>
                            </Alert>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {forecast.recommendations.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Lightbulb className="h-5 w-5 text-blue-600" />
                          Recommendations
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {forecast.recommendations.map((recommendation, index) => (
                            <Alert key={index}>
                              <CheckCircle className="h-4 w-4" />
                              <AlertDescription>{recommendation}</AlertDescription>
                            </Alert>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </>
            )}

            {isLoading && (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Generating Forecast</h3>
                    <p className="text-muted-foreground">
                      Analyzing historical data and generating predictions...
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="scenarios" className="space-y-4">
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Scenario Analysis</h3>
                  <p className="text-muted-foreground">
                    What-if analysis and scenario planning tools coming soon
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">AI Insights</h3>
                  <p className="text-muted-foreground">
                    Advanced insights and recommendations coming soon
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

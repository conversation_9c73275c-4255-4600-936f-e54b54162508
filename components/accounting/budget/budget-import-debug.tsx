'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, TestTube, Upload, CheckCircle2, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

export function BudgetImportDebug() {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isTestingImport, setIsTestingImport] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Test the connection and basic functionality
  const testConnection = async () => {
    setIsTestingConnection(true);
    setError(null);
    setTestResults(null);

    try {
      const response = await fetch('/api/accounting/budget/test-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          test: true
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Test failed');
      }

      setTestResults(data);
      toast({
        title: 'Test Successful',
        description: 'Budget import functionality is working correctly',
        variant: 'default'
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Test Failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Test the actual bulk import functionality
  const testBulkImport = async () => {
    setIsTestingImport(true);
    setError(null);
    setTestResults(null);

    try {
      // Create test budget data including some that might already exist
      const testBudgets = [
        {
          name: 'Debug Test Budget 1 - ' + new Date().toISOString(),
          description: 'Test budget for debugging import issues',
          fiscalYear: '2024-2025',
          startDate: '2024-07-01',
          endDate: '2025-06-30',
          status: 'draft'
        },
        {
          name: 'Debug Test Budget 2 - ' + new Date().toISOString(),
          description: 'Second test budget for debugging',
          fiscalYear: '2025-2026',
          startDate: '2025-07-01',
          endDate: '2026-06-30',
          status: 'draft'
        },
        {
          name: 'Duplicate Test Budget',
          description: 'This budget might already exist to test skip functionality',
          fiscalYear: '2024-2025',
          startDate: '2024-07-01',
          endDate: '2025-06-30',
          status: 'draft'
        }
      ];

      console.log('Testing bulk import with data:', testBudgets);

      const response = await fetch('/api/accounting/budget/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgets: testBudgets
        })
      });

      const data = await response.json();
      console.log('Bulk import response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Bulk import failed');
      }

      setTestResults(data);

      // Show detailed results
      const created = data.budgets?.length || 0;
      const skipped = data.skipped?.length || 0;
      const message = data.summary?.message || `Created: ${created}, Skipped: ${skipped}`;

      toast({
        title: 'Bulk Import Completed',
        description: message,
        variant: 'default'
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Bulk Import Failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsTestingImport(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Budget Import Debug Tool
          </CardTitle>
          <CardDescription>
            Use this tool to test and debug budget import functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={testConnection}
              disabled={isTestingConnection}
              variant="outline"
              className="h-20 flex flex-col gap-2"
            >
              {isTestingConnection ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <CheckCircle2 className="h-5 w-5" />
              )}
              <span>Test Connection & Auth</span>
              <span className="text-xs text-muted-foreground">
                Test authentication and basic functionality
              </span>
            </Button>

            <Button
              onClick={testBulkImport}
              disabled={isTestingImport}
              variant="outline"
              className="h-20 flex flex-col gap-2"
            >
              {isTestingImport ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Upload className="h-5 w-5" />
              )}
              <span>Test Bulk Import</span>
              <span className="text-xs text-muted-foreground">
                Test the actual bulk import functionality
              </span>
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {testResults && (
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Test Results</AlertTitle>
              <AlertDescription className="text-green-700">
                <pre className="mt-2 text-xs overflow-auto max-h-40 bg-white p-2 rounded border">
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>Instructions:</strong></p>
            <ol className="list-decimal list-inside space-y-1">
              <li>First, click "Test Connection & Auth" to verify authentication and database connectivity</li>
              <li>If that succeeds, click "Test Bulk Import" to test the actual import functionality</li>
              <li>Check the browser console for detailed logs</li>
              <li>If tests fail, the error details will help identify the issue</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

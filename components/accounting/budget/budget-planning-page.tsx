"use client"

import { useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusCircle, ArrowLeft, Database } from "lucide-react"
import Link from "next/link"
import { BudgetPlanning } from "@/components/accounting/budget/budget-planning"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { BudgetImportExport } from "@/components/accounting/budget/budget-import-export"
import { BudgetBulkImport } from "@/components/accounting/budget/budget-bulk-import"
import { BulkOperationsModal } from "@/components/accounting/budget/bulk-operations-modal"
import { useBudgetStore } from "@/lib/stores/budget-store"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function BudgetPlanningPage() {
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isCreateBudgetOpen, setIsCreateBudgetOpen] = useState(false)
  const [isBulkImportDialogOpen, setIsBulkImportDialogOpen] = useState(false)
  const [selectedBudgets, setSelectedBudgets] = useState<string[]>([])
  const { currentBudget, budgets, fetchBudgets } = useBudgetStore()

  const handleImportClick = () => {
    if (currentBudget) {
      setIsImportDialogOpen(true)
    }
  }

  const handleBulkImportClick = () => {
    setIsBulkImportDialogOpen(true)
  }

  const handleBulkOperationsSuccess = () => {
    // Refresh budgets list and clear selections
    fetchBudgets()
    setSelectedBudgets([])
  }

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Budget Planning</h1>
            <p className="text-muted-foreground">Create and manage budget plans for the Teachers Council of Malawi.</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1" asChild>
              <Link href="/accounting">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Accounting</span>
              </Link>
            </Button>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1"
                    onClick={handleBulkImportClick}
                  >
                    <Database className="h-4 w-4" />
                    <span>Bulk Import</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Import multiple budgets, categories, or items at once
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <BulkOperationsModal
              selectedBudgets={selectedBudgets}
              onSuccess={handleBulkOperationsSuccess}
              trigger={
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1"
                        disabled={selectedBudgets.length === 0}
                      >
                        <Database className="h-4 w-4" />
                        <span>Bulk Operations ({selectedBudgets.length})</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      Perform bulk operations on selected budgets
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              }
            />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    className="gap-1"
                    onClick={() => setIsCreateBudgetOpen(true)}
                  >
                    <PlusCircle className="h-4 w-4" />
                    <span>New Budget</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Create a new budget plan
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Import Dialog */}
        <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Import Budget Items</DialogTitle>
              <DialogDescription>
                Import items into the selected budget: {currentBudget?.name}
              </DialogDescription>
            </DialogHeader>
            {currentBudget && (
              <BudgetImportExport
                budgetId={currentBudget.id}
                onSuccess={() => setIsImportDialogOpen(false)}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Bulk Import Dialog */}
        <Dialog open={isBulkImportDialogOpen} onOpenChange={setIsBulkImportDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Bulk Import</DialogTitle>
              <DialogDescription>
                Import multiple budgets, categories, or items at once.
                This feature allows you to create new budgets or add to existing ones.
              </DialogDescription>
            </DialogHeader>
            <BudgetBulkImport
              onSuccess={() => setIsBulkImportDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Budget Planning Component */}
        <BudgetPlanning
          isCreateBudgetOpen={isCreateBudgetOpen}
          setIsCreateBudgetOpen={setIsCreateBudgetOpen}
          selectedBudgets={selectedBudgets}
          onBudgetSelectionChange={setSelectedBudgets}
        />
      </div>
    </DashboardShell>
  )
}

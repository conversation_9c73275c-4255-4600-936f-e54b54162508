"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  AlertTriangle,
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircle2,
  Download,
  FileBarChart,
  FileText,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { EmptyState } from '@/components/empty-state';
import { formatCurrency } from '@/lib/utils';
import { useBudgetStore } from '@/lib/stores/budget-store';

interface BudgetVarianceAnalysisProps {
  budgetId?: string;
}

export function BudgetVarianceAnalysis({ budgetId }: BudgetVarianceAnalysisProps) {
  const [activeTab, setActiveTab] = useState('summary');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [varianceData, setVarianceData] = useState<any | null>(null);
  
  const { selectedBudget } = useBudgetStore();
  
  // Use the provided budgetId or the selected budget from the store
  const currentBudgetId = budgetId || selectedBudget?.id;

  // Fetch variance data
  const fetchVarianceData = async () => {
    if (!currentBudgetId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/accounting/budget/${currentBudgetId}/variance`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch budget variance data');
      }
      
      const data = await response.json();
      setVarianceData(data);
    } catch (error: unknown) {
      console.error('Error fetching budget variance data:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount or when budgetId changes
  useEffect(() => {
    if (currentBudgetId) {
      fetchVarianceData();
    }
  }, [currentBudgetId]);

  // Generate chart data
  const generateChartData = () => {
    if (!varianceData) return [];
    
    return varianceData.categories.map((category: unknown) => ({
      name: category.category.name,
      budgeted: category.budgeted,
      actual: category.actual,
      variance: category.variance,
      variancePercentage: category.variancePercentage
    }));
  };

  // Generate pie chart data
  const generatePieChartData = () => {
    if (!varianceData) return [];
    
    return [
      { name: 'Over Budget', value: varianceData.summary.overBudgetCategories },
      { name: 'Under Budget', value: varianceData.summary.underBudgetCategories },
      { name: 'On Budget', value: varianceData.summary.onBudgetCategories }
    ];
  };

  // Pie chart colors
  const COLORS = ['#ef4444', '#22c55e', '#3b82f6'];

  // Export variance report
  const exportVarianceReport = () => {
    if (!varianceData) return;
    
    // Create CSV content
    let csvContent = 'Category,Type,Budgeted,Actual,Variance,Variance %\n';
    
    // Add categories
    varianceData.categories.forEach((category: unknown) => {
      csvContent += `${category.category.name},${category.category.type},${category.budgeted},${category.actual},${category.variance},${category.variancePercentage.toFixed(2)}%\n`;
      
      // Add items
      category.items.forEach((item: unknown) => {
        csvContent += `  ${item.item.name},${category.category.type},${item.budgeted},${item.actual},${item.variance},${item.variancePercentage.toFixed(2)}%\n`;
      });
    });
    
    // Add summary
    csvContent += '\nSummary\n';
    csvContent += `Total,Both,${varianceData.summary.totalBudgeted},${varianceData.summary.totalActual},${varianceData.summary.totalVariance},${varianceData.summary.totalVariancePercentage.toFixed(2)}%\n`;
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `budget-variance-${varianceData.budget.name}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: 'Report Exported',
      description: 'Budget variance report has been exported as CSV',
    });
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Variance Analysis</CardTitle>
          <CardDescription>Loading variance data...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
          <Skeleton className="h-[200px] w-full" />
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Variance Analysis</CardTitle>
          <CardDescription>Error loading variance data</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<AlertTriangle className="h-12 w-12 text-destructive" />}
            title="Error Loading Variance Data"
            description={error}
            actions={
              <Button onClick={fetchVarianceData}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            }
          />
        </CardContent>
      </Card>
    );
  }

  // Render empty state if no budget is selected
  if (!currentBudgetId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Variance Analysis</CardTitle>
          <CardDescription>Select a budget to view variance analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<FileBarChart className="h-12 w-12 text-muted-foreground" />}
            title="No Budget Selected"
            description="Please select a budget to view variance analysis"
          />
        </CardContent>
      </Card>
    );
  }

  // Render empty state if no variance data
  if (!varianceData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Variance Analysis</CardTitle>
          <CardDescription>No variance data available</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<FileBarChart className="h-12 w-12 text-muted-foreground" />}
            title="No Variance Data"
            description="There is no variance data available for this budget"
            actions={
              <Button onClick={fetchVarianceData}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Data
              </Button>
            }
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Budget Variance Analysis</CardTitle>
          <CardDescription>
            Compare budgeted vs. actual amounts for {varianceData.budget.name}
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchVarianceData}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportVarianceReport}
            disabled={isLoading}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summary" className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Budgeted</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(varianceData.summary.totalBudgeted)}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Actual</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(varianceData.summary.totalActual)}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Variance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${varianceData.summary.totalVariance < 0 ? 'text-red-500' : varianceData.summary.totalVariance > 0 ? 'text-green-500' : ''}`}>
                    {formatCurrency(varianceData.summary.totalVariance)}
                    <span className="text-sm ml-1">
                      ({varianceData.summary.totalVariancePercentage.toFixed(2)}%)
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Variance Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                    <span>Over Budget: {varianceData.summary.overBudgetCategories} categories</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                    <span>Under Budget: {varianceData.summary.underBudgetCategories} categories</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                    <span>On Budget: {varianceData.summary.onBudgetCategories} categories</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="categories" className="space-y-4 pt-4">
            {varianceData.categories.map((category: unknown, index: number) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-medium">
                      {category.category.name}
                      <Badge className={`ml-2 ${category.category.type === 'income' ? 'bg-blue-100 text-blue-800' : 'bg-amber-100 text-amber-800'}`}>
                        {category.category.type}
                      </Badge>
                    </CardTitle>
                    <Badge className={`${Math.abs(category.variancePercentage) < 5 ? 'bg-green-100 text-green-800' : Math.abs(category.variancePercentage) < 15 ? 'bg-amber-100 text-amber-800' : 'bg-red-100 text-red-800'}`}>
                      {category.variancePercentage > 0 ? '+' : ''}{category.variancePercentage.toFixed(2)}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="grid grid-cols-3 gap-4 mb-2">
                    <div>
                      <div className="text-sm text-muted-foreground">Budgeted</div>
                      <div className="font-medium">{formatCurrency(category.budgeted)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Actual</div>
                      <div className="font-medium">{formatCurrency(category.actual)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Variance</div>
                      <div className={`font-medium ${category.variance < 0 ? 'text-red-500' : category.variance > 0 ? 'text-green-500' : ''}`}>
                        {formatCurrency(category.variance)}
                      </div>
                    </div>
                  </div>
                  
                  <Progress 
                    value={Math.min(100, (category.actual / category.budgeted) * 100)} 
                    className={`h-2 ${category.actual > category.budgeted ? 'bg-red-100' : 'bg-blue-100'}`}
                    indicatorClassName={category.actual > category.budgeted ? 'bg-red-500' : 'bg-blue-500'}
                  />
                </CardContent>
              </Card>
            ))}
          </TabsContent>
          
          <TabsContent value="charts" className="space-y-4 pt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Budget vs. Actual by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={generateChartData()}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      <Legend />
                      <Bar dataKey="budgeted" name="Budgeted" fill="#3b82f6" />
                      <Bar dataKey="actual" name="Actual" fill="#ef4444" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Variance Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={generatePieChartData()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {generatePieChartData().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => value} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  FileText,
  Plus,
  Trash2,
  Copy,
  Calendar,
  Loader2,
  RefreshCw,
  Search,
  CheckCircle2,
  AlertTriangle
} from 'lucide-react';
import { EmptyState } from '@/components/empty-state';
import { useBudgetStore } from '@/lib/stores/budget-store';
import { formatDate } from '@/lib/utils';

interface BudgetTemplatesProps {
  onCreateBudget?: (budget: unknown) => void;
}

export function BudgetTemplates({ onCreateBudget }: BudgetTemplatesProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [templates, setTemplates] = useState<unknown[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreateFromBudgetDialogOpen, setIsCreateFromBudgetDialogOpen] = useState(false);
  const [isCreateBudgetDialogOpen, setIsCreateBudgetDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Form states
  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateDescription, setNewTemplateDescription] = useState('');
  const [isDefaultTemplate, setIsDefaultTemplate] = useState(false);
  
  const [newBudgetName, setNewBudgetName] = useState('');
  const [newBudgetDescription, setNewBudgetDescription] = useState('');
  const [newBudgetFiscalYear, setNewBudgetFiscalYear] = useState('');
  const [newBudgetStartDate, setNewBudgetStartDate] = useState('');
  const [newBudgetEndDate, setNewBudgetEndDate] = useState('');
  
  const { budgets } = useBudgetStore();
  const [selectedBudgetId, setSelectedBudgetId] = useState('');

  // Fetch templates
  const fetchTemplates = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/accounting/budget/templates');
      
      if (!response.ok) {
        throw new Error('Failed to fetch budget templates');
      }
      
      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (error) {
      console.error('Error fetching budget templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Create a new template
  const createTemplate = async () => {
    if (!newTemplateName) {
      toast({
        title: 'Error',
        description: 'Template name is required',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/accounting/budget/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newTemplateName,
          description: newTemplateDescription,
          isDefault: isDefaultTemplate
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create budget template');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: 'Budget template created successfully',
      });
      
      // Reset form
      setNewTemplateName('');
      setNewTemplateDescription('');
      setIsDefaultTemplate(false);
      setIsCreateDialogOpen(false);
      
      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error creating budget template:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a template from a budget
  const createTemplateFromBudget = async () => {
    if (!newTemplateName || !selectedBudgetId) {
      toast({
        title: 'Error',
        description: 'Template name and budget selection are required',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/accounting/budget/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newTemplateName,
          description: newTemplateDescription,
          isDefault: isDefaultTemplate,
          budgetId: selectedBudgetId
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create budget template from budget');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: 'Budget template created successfully from budget',
      });
      
      // Reset form
      setNewTemplateName('');
      setNewTemplateDescription('');
      setIsDefaultTemplate(false);
      setSelectedBudgetId('');
      setIsCreateFromBudgetDialogOpen(false);
      
      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error creating budget template from budget:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a budget from a template
  const createBudgetFromTemplate = async () => {
    if (!selectedTemplate || !newBudgetName || !newBudgetFiscalYear || !newBudgetStartDate || !newBudgetEndDate) {
      toast({
        title: 'Error',
        description: 'All fields are required',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/accounting/budget/templates/${selectedTemplate.id}/create-budget`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newBudgetName,
          description: newBudgetDescription,
          fiscalYear: newBudgetFiscalYear,
          startDate: newBudgetStartDate,
          endDate: newBudgetEndDate
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create budget from template');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: 'Budget created successfully from template',
      });
      
      // Reset form
      setNewBudgetName('');
      setNewBudgetDescription('');
      setNewBudgetFiscalYear('');
      setNewBudgetStartDate('');
      setNewBudgetEndDate('');
      setIsCreateBudgetDialogOpen(false);
      
      // Notify parent component
      if (onCreateBudget) {
        onCreateBudget(data.budget);
      }
    } catch (error) {
      console.error('Error creating budget from template:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a template
  const deleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/accounting/budget/templates/${templateId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete budget template');
      }
      
      toast({
        title: 'Success',
        description: 'Budget template deleted successfully',
      });
      
      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error deleting budget template:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform operation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter templates by search query
  const filteredTemplates = templates.filter(template => 
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (template.description && template.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Budget Templates</CardTitle>
          <CardDescription>
            Create and manage reusable budget templates
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchTemplates}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsCreateFromBudgetDialogOpen(true)}
          >
            <Copy className="mr-2 h-4 w-4" />
            From Budget
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Template
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : filteredTemplates.length > 0 ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Default</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTemplates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{template.description || '-'}</TableCell>
                    <TableCell>
                      {template.isDefault ? (
                        <Badge className="bg-green-100 text-green-800">Default</Badge>
                      ) : (
                        <span className="text-muted-foreground">No</span>
                      )}
                    </TableCell>
                    <TableCell>{template.createdBy?.name || '-'}</TableCell>
                    <TableCell>{formatDate(template.createdAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setIsCreateBudgetDialogOpen(true);
                          }}
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteTemplate(template.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <EmptyState
            icon={<FileText className="h-12 w-12 text-muted-foreground" />}
            title="No Templates"
            description="You don't have any budget templates yet"
            actions={
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Template
              </Button>
            }
          />
        )}
      </CardContent>
      
      {/* Create Template Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Budget Template</DialogTitle>
            <DialogDescription>
              Create a new empty budget template
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                placeholder="Annual Operating Budget Template"
                value={newTemplateName}
                onChange={(e) => setNewTemplateName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-description">Description (Optional)</Label>
              <Textarea
                id="template-description"
                placeholder="Template for annual operating budgets"
                value={newTemplateDescription}
                onChange={(e) => setNewTemplateDescription(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-default"
                checked={isDefaultTemplate}
                onCheckedChange={(checked) => setIsDefaultTemplate(checked === true)}
              />
              <Label htmlFor="is-default">Set as default template</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createTemplate} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Create Template from Budget Dialog */}
      <Dialog open={isCreateFromBudgetDialogOpen} onOpenChange={setIsCreateFromBudgetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Template from Budget</DialogTitle>
            <DialogDescription>
              Create a new template based on an existing budget
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="template-name-from-budget">Template Name</Label>
              <Input
                id="template-name-from-budget"
                placeholder="Annual Operating Budget Template"
                value={newTemplateName}
                onChange={(e) => setNewTemplateName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-description-from-budget">Description (Optional)</Label>
              <Textarea
                id="template-description-from-budget"
                placeholder="Template for annual operating budgets"
                value={newTemplateDescription}
                onChange={(e) => setNewTemplateDescription(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="budget-select">Select Budget</Label>
              <select
                id="budget-select"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={selectedBudgetId}
                onChange={(e) => setSelectedBudgetId(e.target.value)}
              >
                <option value="">Select a budget</option>
                {budgets.map((budget) => (
                  <option key={budget.id} value={budget.id}>
                    {budget.name} ({budget.fiscalYear})
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-default-from-budget"
                checked={isDefaultTemplate}
                onCheckedChange={(checked) => setIsDefaultTemplate(checked === true)}
              />
              <Label htmlFor="is-default-from-budget">Set as default template</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateFromBudgetDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createTemplateFromBudget} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Create Budget from Template Dialog */}
      <Dialog open={isCreateBudgetDialogOpen} onOpenChange={setIsCreateBudgetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Budget from Template</DialogTitle>
            <DialogDescription>
              Create a new budget based on the template: {selectedTemplate?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="budget-name">Budget Name</Label>
              <Input
                id="budget-name"
                placeholder="Annual Operating Budget 2025"
                value={newBudgetName}
                onChange={(e) => setNewBudgetName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="budget-description">Description (Optional)</Label>
              <Textarea
                id="budget-description"
                placeholder="Annual operating budget for fiscal year 2025"
                value={newBudgetDescription}
                onChange={(e) => setNewBudgetDescription(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fiscal-year">Fiscal Year</Label>
              <Input
                id="fiscal-year"
                placeholder="2025"
                value={newBudgetFiscalYear}
                onChange={(e) => setNewBudgetFiscalYear(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">Start Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="start-date"
                    type="date"
                    className="pl-8"
                    value={newBudgetStartDate}
                    onChange={(e) => setNewBudgetStartDate(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date">End Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="end-date"
                    type="date"
                    className="pl-8"
                    value={newBudgetEndDate}
                    onChange={(e) => setNewBudgetEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateBudgetDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createBudgetFromTemplate} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Budget
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

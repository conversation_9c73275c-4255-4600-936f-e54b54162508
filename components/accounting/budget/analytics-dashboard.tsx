'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Calendar,
  BarChart3
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { useBudgetStore } from '@/lib/stores/budget-store'

interface BudgetPerformanceMetrics {
  budgetId: string
  budgetName: string
  fiscalYear: string
  totalBudgeted: number
  totalActual: number
  variance: number
  variancePercentage: number
  incomeMetrics: {
    budgeted: number
    actual: number
    variance: number
    variancePercentage: number
  }
  expenseMetrics: {
    budgeted: number
    actual: number
    variance: number
    variancePercentage: number
  }
  categoryBreakdown: Array<{
    categoryId: string
    categoryName: string
    type: 'income' | 'expense'
    budgeted: number
    actual: number
    variance: number
    variancePercentage: number
  }>
}

interface BudgetKPIs {
  budgetUtilization: number
  budgetAccuracy: number
  incomeRealization: number
  expenseControl: number
  netPosition: number
  budgetedNetPosition: number
}

interface TrendAnalysis {
  period: string
  budgeted: number
  actual: number
  variance: number
  cumulativeBudgeted: number
  cumulativeActual: number
  cumulativeVariance: number
}

interface AnalyticsDashboardProps {
  budgetId?: string
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

// Helper function to format currency
const formatMWK = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Helper function to format percentage
const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`
}

export function AnalyticsDashboard({ budgetId }: AnalyticsDashboardProps) {
  const [selectedBudgetId, setSelectedBudgetId] = useState(budgetId)
  const [performanceMetrics, setPerformanceMetrics] = useState<BudgetPerformanceMetrics | null>(null)
  const [kpis, setKPIs] = useState<BudgetKPIs | null>(null)
  const [trendData, setTrendData] = useState<TrendAnalysis[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [periodType, setPeriodType] = useState<'monthly' | 'quarterly'>('monthly')
  
  const { budgets, fetchBudgets } = useBudgetStore()

  useEffect(() => {
    fetchBudgets()
  }, [fetchBudgets])

  useEffect(() => {
    if (selectedBudgetId) {
      loadAnalyticsData()
    }
  }, [selectedBudgetId, periodType])

  const loadAnalyticsData = async () => {
    if (!selectedBudgetId) return

    setIsLoading(true)
    try {
      // Load performance metrics
      const metricsResponse = await fetch(`/api/accounting/budget/${selectedBudgetId}/analytics/performance`)
      if (metricsResponse.ok) {
        const metrics = await metricsResponse.json()
        setPerformanceMetrics(metrics)
      }

      // Load KPIs
      const kpisResponse = await fetch(`/api/accounting/budget/${selectedBudgetId}/analytics/kpis`)
      if (kpisResponse.ok) {
        const kpisData = await kpisResponse.json()
        setKPIs(kpisData)
      }

      // Load trend analysis
      const trendResponse = await fetch(`/api/accounting/budget/${selectedBudgetId}/analytics/trends?period=${periodType}`)
      if (trendResponse.ok) {
        const trends = await trendResponse.json()
        setTrendData(trends)
      }

    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load analytics data',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getVarianceColor = (percentage: number) => {
    if (Math.abs(percentage) <= 5) return 'text-green-600'
    if (Math.abs(percentage) <= 15) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getVarianceIcon = (percentage: number) => {
    if (Math.abs(percentage) <= 5) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (Math.abs(percentage) <= 15) return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    return <AlertTriangle className="h-4 w-4 text-red-600" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Budget Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive performance analysis and insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedBudgetId} onValueChange={setSelectedBudgetId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select a budget" />
            </SelectTrigger>
            <SelectContent>
              {budgets.map((budget) => (
                <SelectItem key={budget.id} value={budget.id}>
                  {budget.name} ({budget.fiscalYear})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="icon"
            onClick={loadAnalyticsData}
            disabled={isLoading || !selectedBudgetId}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {!selectedBudgetId ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select a Budget</h3>
              <p className="text-muted-foreground">
                Choose a budget from the dropdown to view analytics
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* KPI Cards */}
            {kpis && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
                    <Target className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatPercentage(kpis.budgetUtilization)}</div>
                    <Progress value={kpis.budgetUtilization} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Budget Accuracy</CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatPercentage(kpis.budgetAccuracy)}</div>
                    <Progress value={kpis.budgetAccuracy} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Income Realization</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatPercentage(kpis.incomeRealization)}</div>
                    <Progress value={kpis.incomeRealization} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Net Position</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatMWK(kpis.netPosition)}</div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Budgeted: {formatMWK(kpis.budgetedNetPosition)}
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Performance Summary */}
            {performanceMetrics && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Income vs Expense Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Income</span>
                        <div className="flex items-center gap-2">
                          {getVarianceIcon(performanceMetrics.incomeMetrics.variancePercentage)}
                          <span className={getVarianceColor(performanceMetrics.incomeMetrics.variancePercentage)}>
                            {formatPercentage(performanceMetrics.incomeMetrics.variancePercentage)}
                          </span>
                        </div>
                      </div>
                      <Progress 
                        value={Math.min((performanceMetrics.incomeMetrics.actual / performanceMetrics.incomeMetrics.budgeted) * 100, 100)} 
                        className="h-2"
                      />
                      <div className="text-xs text-muted-foreground">
                        {formatMWK(performanceMetrics.incomeMetrics.actual)} of {formatMWK(performanceMetrics.incomeMetrics.budgeted)}
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Expenses</span>
                        <div className="flex items-center gap-2">
                          {getVarianceIcon(performanceMetrics.expenseMetrics.variancePercentage)}
                          <span className={getVarianceColor(performanceMetrics.expenseMetrics.variancePercentage)}>
                            {formatPercentage(performanceMetrics.expenseMetrics.variancePercentage)}
                          </span>
                        </div>
                      </div>
                      <Progress 
                        value={Math.min((performanceMetrics.expenseMetrics.actual / performanceMetrics.expenseMetrics.budgeted) * 100, 100)} 
                        className="h-2"
                      />
                      <div className="text-xs text-muted-foreground">
                        {formatMWK(performanceMetrics.expenseMetrics.actual)} of {formatMWK(performanceMetrics.expenseMetrics.budgeted)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Budget Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Total Budgeted</span>
                        <span className="font-semibold">{formatMWK(performanceMetrics.totalBudgeted)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Total Actual</span>
                        <span className="font-semibold">{formatMWK(performanceMetrics.totalActual)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Variance</span>
                        <div className="flex items-center gap-2">
                          <span className={`font-semibold ${getVarianceColor(performanceMetrics.variancePercentage)}`}>
                            {formatMWK(performanceMetrics.variance)}
                          </span>
                          <Badge variant={Math.abs(performanceMetrics.variancePercentage) <= 5 ? "default" : "destructive"}>
                            {formatPercentage(performanceMetrics.variancePercentage)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            {performanceMetrics && (
              <Card>
                <CardHeader>
                  <CardTitle>Budget vs Actual Performance</CardTitle>
                  <CardDescription>
                    Comparison of budgeted amounts versus actual performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={[
                      {
                        name: 'Income',
                        budgeted: performanceMetrics.incomeMetrics.budgeted,
                        actual: performanceMetrics.incomeMetrics.actual
                      },
                      {
                        name: 'Expenses',
                        budgeted: performanceMetrics.expenseMetrics.budgeted,
                        actual: performanceMetrics.expenseMetrics.actual
                      }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis tickFormatter={(value) => formatMWK(value)} />
                      <Tooltip formatter={(value) => formatMWK(value as number)} />
                      <Legend />
                      <Bar dataKey="budgeted" fill="#8884d8" name="Budgeted" />
                      <Bar dataKey="actual" fill="#82ca9d" name="Actual" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Select value={periodType} onValueChange={(value: 'monthly' | 'quarterly') => setPeriodType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {trendData.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Budget Trend Analysis</CardTitle>
                  <CardDescription>
                    {periodType === 'monthly' ? 'Monthly' : 'Quarterly'} budget performance over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={trendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis tickFormatter={(value) => formatMWK(value)} />
                      <Tooltip formatter={(value) => formatMWK(value as number)} />
                      <Legend />
                      <Line type="monotone" dataKey="budgeted" stroke="#8884d8" name="Budgeted" />
                      <Line type="monotone" dataKey="actual" stroke="#82ca9d" name="Actual" />
                      <Line type="monotone" dataKey="variance" stroke="#ff7300" name="Variance" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            {performanceMetrics && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Category Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {performanceMetrics.categoryBreakdown.map((category) => (
                        <div key={category.categoryId} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{category.categoryName}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant={category.type === 'income' ? 'default' : 'secondary'}>
                                {category.type}
                              </Badge>
                              <span className={`text-sm ${getVarianceColor(category.variancePercentage)}`}>
                                {formatPercentage(category.variancePercentage)}
                              </span>
                            </div>
                          </div>
                          <Progress 
                            value={Math.min((category.actual / category.budgeted) * 100, 100)} 
                            className="h-2"
                          />
                          <div className="text-xs text-muted-foreground">
                            {formatMWK(category.actual)} of {formatMWK(category.budgeted)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Budget Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={performanceMetrics.categoryBreakdown}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ categoryName, budgeted }) => `${categoryName}: ${formatMWK(budgeted)}`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="budgeted"
                        >
                          {performanceMetrics.categoryBreakdown.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatMWK(value as number)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

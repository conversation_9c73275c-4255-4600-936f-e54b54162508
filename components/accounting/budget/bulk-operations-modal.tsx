'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  FileSpreadsheet, 
  Download, 
  AlertCircle, 
  CheckCircle2, 
  Loader2, 
  Trash2, 
  Edit3,
  Upload,
  Eye,
  AlertTriangle
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import * as XLSX from 'xlsx'

interface BulkOperationsModalProps {
  selectedBudgets: string[]
  onSuccess?: () => void
  trigger?: React.ReactNode
}

interface ValidationResult {
  isValid: boolean
  errors: Array<{
    row: number
    field: string
    error: string
    severity: 'error' | 'warning'
  }>
  summary: {
    totalRows: number
    validRows: number
    errorRows: number
    warningRows: number
  }
}

export function BulkOperationsModal({ 
  selectedBudgets, 
  onSuccess,
  trigger 
}: BulkOperationsModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('import')
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [file, setFile] = useState<File | null>(null)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  
  // Bulk delete states
  const [deleteConfirmation, setDeleteConfirmation] = useState('')
  const [deleteRelatedData, setDeleteRelatedData] = useState(true)
  
  // Bulk update states
  const [updateFields, setUpdateFields] = useState({
    status: '',
    fiscalYear: '',
    description: ''
  })

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setValidationResult(null)
      setShowPreview(false)
    }
  }

  const validateImportFile = async () => {
    if (!file) {
      toast({
        title: 'Error',
        description: 'Please select a file first',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(20)

    try {
      // Parse Excel file
      const buffer = await file.arrayBuffer()
      const workbook = XLSX.read(buffer, { type: 'buffer' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet)

      setProgress(50)

      // Validate data
      const response = await fetch('/api/accounting/budget/validate-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          data: jsonData,
          importType: 'budgets',
          validationLevel: 'comprehensive'
        })
      })

      if (!response.ok) {
        throw new Error('Validation failed')
      }

      const result = await response.json()
      setValidationResult(result.validation)
      setShowPreview(true)
      setProgress(100)

      toast({
        title: 'Validation Complete',
        description: `${result.validation.summary.validRows} valid rows, ${result.validation.summary.errorRows} errors`,
        variant: result.validation.isValid ? 'default' : 'destructive'
      })

    } catch (error) {
      toast({
        title: 'Validation Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  const handleBulkImport = async () => {
    if (!validationResult?.isValid) {
      toast({
        title: 'Error',
        description: 'Please fix validation errors before importing',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const response = await fetch('/api/accounting/budget/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgets: validationResult.processedData
        })
      })

      if (!response.ok) {
        throw new Error('Import failed')
      }

      const result = await response.json()
      setProgress(100)

      toast({
        title: 'Import Successful',
        description: `Successfully imported ${result.budgets?.length || 0} budgets`
      })

      onSuccess?.()
      setIsOpen(false)

    } catch (error) {
      toast({
        title: 'Import Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  const handleBulkDelete = async () => {
    if (deleteConfirmation !== 'DELETE') {
      toast({
        title: 'Error',
        description: 'Please type "DELETE" to confirm',
        variant: 'destructive'
      })
      return
    }

    if (selectedBudgets.length === 0) {
      toast({
        title: 'Error',
        description: 'No budgets selected for deletion',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const response = await fetch('/api/accounting/budget/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgetIds: selectedBudgets,
          deleteRelatedData,
          confirmDeletion: true
        })
      })

      if (!response.ok) {
        throw new Error('Delete failed')
      }

      const result = await response.json()
      setProgress(100)

      toast({
        title: 'Delete Successful',
        description: `Successfully deleted ${result.results.budgetsDeleted} budget(s)`
      })

      onSuccess?.()
      setIsOpen(false)

    } catch (error) {
      toast({
        title: 'Delete Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  const handleBulkUpdate = async () => {
    if (selectedBudgets.length === 0) {
      toast({
        title: 'Error',
        description: 'No budgets selected for update',
        variant: 'destructive'
      })
      return
    }

    // Filter out empty update fields
    const filteredUpdates = Object.entries(updateFields)
      .filter(([_, value]) => value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})

    if (Object.keys(filteredUpdates).length === 0) {
      toast({
        title: 'Error',
        description: 'Please specify at least one field to update',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const updates = selectedBudgets.map(budgetId => ({
        budgetId,
        data: filteredUpdates
      }))

      const response = await fetch('/api/accounting/budget/bulk-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ updates })
      })

      if (!response.ok) {
        throw new Error('Update failed')
      }

      const result = await response.json()
      setProgress(100)

      toast({
        title: 'Update Successful',
        description: `Successfully updated ${result.results.successful.length} budget(s)`
      })

      onSuccess?.()
      setIsOpen(false)

    } catch (error) {
      toast({
        title: 'Update Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  const downloadTemplate = () => {
    const template = [
      {
        name: 'Sample Budget 2024',
        description: 'Annual budget for fiscal year 2024',
        fiscalYear: '2024',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        status: 'draft',
        totalIncome: 1000000,
        totalExpense: 950000
      }
    ]

    const ws = XLSX.utils.json_to_sheet(template)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Budget Template')
    XLSX.writeFile(wb, 'budget-import-template.xlsx')
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Edit3 className="h-4 w-4 mr-2" />
            Bulk Operations
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Budget Operations</DialogTitle>
          <DialogDescription>
            Import, update, or delete multiple budgets at once
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="import">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </TabsTrigger>
            <TabsTrigger value="update">
              <Edit3 className="h-4 w-4 mr-2" />
              Update ({selectedBudgets.length})
            </TabsTrigger>
            <TabsTrigger value="delete">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete ({selectedBudgets.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="file-upload">Select Excel File</Label>
                <Button variant="outline" size="sm" onClick={downloadTemplate}>
                  <Download className="h-4 w-4 mr-2" />
                  Download Template
                </Button>
              </div>
              
              <Input
                id="file-upload"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                ref={fileInputRef}
              />

              {file && (
                <div className="flex items-center space-x-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span className="text-sm">{file.name}</span>
                  <Button size="sm" onClick={validateImportFile} disabled={isProcessing}>
                    {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Eye className="h-4 w-4" />}
                    Validate
                  </Button>
                </div>
              )}

              {isProcessing && (
                <div className="space-y-2">
                  <Progress value={progress} />
                  <p className="text-sm text-muted-foreground">Processing...</p>
                </div>
              )}

              {validationResult && showPreview && (
                <div className="space-y-4">
                  <Alert variant={validationResult.isValid ? "default" : "destructive"}>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Validation Results</AlertTitle>
                    <AlertDescription>
                      {validationResult.summary.validRows} valid rows, {validationResult.summary.errorRows} errors, {validationResult.summary.warningRows} warnings
                    </AlertDescription>
                  </Alert>

                  {validationResult.errors.length > 0 && (
                    <div className="max-h-40 overflow-y-auto border rounded p-2">
                      {validationResult.errors.map((error, index) => (
                        <div key={index} className="text-sm text-red-600 mb-1">
                          Row {error.row}: {error.error}
                        </div>
                      ))}
                    </div>
                  )}

                  <Button 
                    onClick={handleBulkImport} 
                    disabled={!validationResult.isValid || isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Upload className="h-4 w-4 mr-2" />}
                    Import {validationResult.summary.validRows} Budget(s)
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="update" className="space-y-4">
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Bulk Update</AlertTitle>
                <AlertDescription>
                  Update {selectedBudgets.length} selected budget(s). Only filled fields will be updated.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={updateFields.status} onValueChange={(value) => setUpdateFields(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="pending_approval">Pending Approval</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fiscalYear">Fiscal Year</Label>
                  <Input
                    id="fiscalYear"
                    value={updateFields.fiscalYear}
                    onChange={(e) => setUpdateFields(prev => ({ ...prev, fiscalYear: e.target.value }))}
                    placeholder="e.g., 2024"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={updateFields.description}
                  onChange={(e) => setUpdateFields(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Budget description"
                />
              </div>

              <Button onClick={handleBulkUpdate} disabled={isProcessing} className="w-full">
                {isProcessing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Edit3 className="h-4 w-4 mr-2" />}
                Update {selectedBudgets.length} Budget(s)
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="delete" className="space-y-4">
            <div className="space-y-4">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Danger Zone</AlertTitle>
                <AlertDescription>
                  This action will permanently delete {selectedBudgets.length} budget(s) and cannot be undone.
                </AlertDescription>
              </Alert>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="deleteRelatedData"
                  checked={deleteRelatedData}
                  onCheckedChange={(checked) => setDeleteRelatedData(checked as boolean)}
                />
                <Label htmlFor="deleteRelatedData">
                  Also delete related categories and items
                </Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmation">
                  Type "DELETE" to confirm deletion of {selectedBudgets.length} budget(s)
                </Label>
                <Input
                  id="confirmation"
                  value={deleteConfirmation}
                  onChange={(e) => setDeleteConfirmation(e.target.value)}
                  placeholder="Type DELETE to confirm"
                />
              </div>

              <Button 
                onClick={handleBulkDelete} 
                disabled={deleteConfirmation !== 'DELETE' || isProcessing}
                variant="destructive"
                className="w-full"
              >
                {isProcessing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
                Delete {selectedBudgets.length} Budget(s)
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {isProcessing && (
          <div className="space-y-2">
            <Progress value={progress} />
            <p className="text-sm text-muted-foreground text-center">
              Processing operation...
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

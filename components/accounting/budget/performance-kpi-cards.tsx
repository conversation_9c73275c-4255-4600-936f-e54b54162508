'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  CheckCircle, 
  AlertTriangle,
  Activity,
  PieChart
} from 'lucide-react'

interface BudgetKPIs {
  budgetUtilization: number
  budgetAccuracy: number
  incomeRealization: number
  expenseControl: number
  netPosition: number
  budgetedNetPosition: number
}

interface PerformanceKPICardsProps {
  kpis: BudgetKPIs
  isLoading?: boolean
}

// Helper function to format currency
const formatMWK = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Helper function to format percentage
const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`
}

// Helper function to get performance color
const getPerformanceColor = (value: number, type: 'percentage' | 'utilization' | 'accuracy') => {
  switch (type) {
    case 'percentage':
      if (value >= 90) return 'text-green-600'
      if (value >= 70) return 'text-yellow-600'
      return 'text-red-600'
    case 'utilization':
      if (value >= 80 && value <= 100) return 'text-green-600'
      if (value >= 60 && value <= 120) return 'text-yellow-600'
      return 'text-red-600'
    case 'accuracy':
      if (value >= 95) return 'text-green-600'
      if (value >= 85) return 'text-yellow-600'
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

// Helper function to get performance icon
const getPerformanceIcon = (value: number, type: 'percentage' | 'utilization' | 'accuracy') => {
  const color = getPerformanceColor(value, type)
  const iconClass = `h-4 w-4 ${color}`
  
  switch (type) {
    case 'percentage':
      return value >= 70 ? <TrendingUp className={iconClass} /> : <TrendingDown className={iconClass} />
    case 'utilization':
      return value >= 60 && value <= 120 ? <CheckCircle className={iconClass} /> : <AlertTriangle className={iconClass} />
    case 'accuracy':
      return value >= 85 ? <CheckCircle className={iconClass} /> : <AlertTriangle className={iconClass} />
    default:
      return <Activity className={iconClass} />
  }
}

// Helper function to get progress variant
const getProgressVariant = (value: number, type: 'percentage' | 'utilization' | 'accuracy') => {
  const color = getPerformanceColor(value, type)
  if (color.includes('green')) return 'default'
  if (color.includes('yellow')) return 'secondary'
  return 'destructive'
}

export function PerformanceKPICards({ kpis, isLoading = false }: PerformanceKPICardsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              </CardTitle>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Budget Utilization */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-2xl font-bold">{formatPercentage(kpis.budgetUtilization)}</div>
            {getPerformanceIcon(kpis.budgetUtilization, 'utilization')}
          </div>
          <Progress 
            value={Math.min(kpis.budgetUtilization, 100)} 
            className="h-2"
          />
          <p className="text-xs text-muted-foreground mt-2">
            Optimal range: 80-100%
          </p>
        </CardContent>
      </Card>

      {/* Budget Accuracy */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Budget Accuracy</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-2xl font-bold">{formatPercentage(kpis.budgetAccuracy)}</div>
            {getPerformanceIcon(kpis.budgetAccuracy, 'accuracy')}
          </div>
          <Progress 
            value={kpis.budgetAccuracy} 
            className="h-2"
          />
          <p className="text-xs text-muted-foreground mt-2">
            Target: 95%+
          </p>
        </CardContent>
      </Card>

      {/* Income Realization */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Income Realization</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-2xl font-bold">{formatPercentage(kpis.incomeRealization)}</div>
            {getPerformanceIcon(kpis.incomeRealization, 'percentage')}
          </div>
          <Progress 
            value={Math.min(kpis.incomeRealization, 100)} 
            className="h-2"
          />
          <p className="text-xs text-muted-foreground mt-2">
            Target: 90%+
          </p>
        </CardContent>
      </Card>

      {/* Expense Control */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Expense Control</CardTitle>
          <PieChart className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-2xl font-bold">{formatPercentage(kpis.expenseControl)}</div>
            {getPerformanceIcon(kpis.expenseControl, 'percentage')}
          </div>
          <Progress 
            value={Math.min(kpis.expenseControl, 100)} 
            className="h-2"
          />
          <p className="text-xs text-muted-foreground mt-2">
            Higher is better
          </p>
        </CardContent>
      </Card>

      {/* Net Position */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Net Position</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            <div className={`text-2xl font-bold ${kpis.netPosition >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatMWK(kpis.netPosition)}
            </div>
            {kpis.netPosition >= 0 ? 
              <TrendingUp className="h-4 w-4 text-green-600" /> : 
              <TrendingDown className="h-4 w-4 text-red-600" />
            }
          </div>
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Budgeted:</span>
              <span className={kpis.budgetedNetPosition >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatMWK(kpis.budgetedNetPosition)}
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Variance:</span>
              <span className={kpis.netPosition - kpis.budgetedNetPosition >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatMWK(kpis.netPosition - kpis.budgetedNetPosition)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Overall Performance</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Utilization</span>
              <Badge variant={kpis.budgetUtilization >= 80 && kpis.budgetUtilization <= 100 ? "default" : "destructive"}>
                {formatPercentage(kpis.budgetUtilization)}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Accuracy</span>
              <Badge variant={kpis.budgetAccuracy >= 85 ? "default" : "destructive"}>
                {formatPercentage(kpis.budgetAccuracy)}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Income</span>
              <Badge variant={kpis.incomeRealization >= 70 ? "default" : "destructive"}>
                {formatPercentage(kpis.incomeRealization)}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Control</span>
              <Badge variant={kpis.expenseControl >= 70 ? "default" : "destructive"}>
                {formatPercentage(kpis.expenseControl)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

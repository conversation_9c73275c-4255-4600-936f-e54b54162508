// components/accounting/budget/budget-planning.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { DatePicker } from '@/components/ui/date-picker';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from '@/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  PieChart,
  Plus,
  Save,
  Download,
  FileText,
  Edit,
  Trash2,
  Copy,
  CheckCircle2,
  AlertCircle,
  Loader2,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  FolderPlus,
  FileBarChart,
  Eye
} from 'lucide-react';
import { BudgetImportExport } from './budget-import-export';
import { useBudgetStore } from '@/lib/stores/budget-store';
import { EmptyState } from '@/components/empty-state';
import { SimpleBudgetModals } from './simple-budget-modals';
import { SimpleBudgetApproval } from './simple-budget-approval';
import { BudgetReportExport } from './budget-report-export';
import { AnalyticsDashboard } from './analytics-dashboard';
import { RealTimeBudgetIntegration } from './real-time-budget-integration';
import { BudgetFundOverview } from './budget-fund-overview';
import { Checkbox } from '@/components/ui/checkbox';
import { ErrorState } from '@/components/ui/error-state';
import { ErrorBoundary } from '@/components/error-boundary';
import { useErrorHandler } from '@/lib/frontend/hooks/useErrorHandler';
import { ErrorOverlay } from '@/components/errors/error-overlay';

// Define form data types that use string IDs instead of ObjectId
interface CategoryFormData {
  name: string;
  description?: string;
  type: 'income' | 'expense';
  budget: string;
}

interface SubcategoryFormData {
  name: string;
  description?: string;
  parentCategory: string;
  budget: string;
}

interface ItemFormData {
  name: string;
  description?: string;
  quantity: number;
  frequency: number;
  unitCost: number;
  amount?: number;
  parentCategory: string;
  parentSubcategory?: string;
  budget: string;
}

// Define types for budget data display
interface BudgetCategoryDisplay {
  _id: string;
  name: string;
  budgetedAmount?: number;
  actualAmount?: number;
  total?: number;
  subcategories?: BudgetSubcategoryDisplay[];
}

interface BudgetSubcategoryDisplay {
  _id: string;
  name: string;
  total: number;
  items?: BudgetItemDisplay[];
}

interface BudgetItemDisplay {
  _id: string;
  name: string;
  amount: number;
}

// Helper function to format currency in Malawian Kwacha
const formatMWK = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Budget form schema
const budgetFormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, 'Fiscal year is required'),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
});

// Category form schema
const categoryFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  type: z.enum(['income', 'expense']),
  budget: z.string().min(1, 'Budget ID is required'),
});

// Subcategory form schema
const subcategoryFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  parentCategory: z.string().min(1, 'Parent category ID is required'),
  budget: z.string().min(1, 'Budget ID is required'),
});

// Item form schema
const itemFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  quantity: z.number().min(0, 'Quantity must be a positive number'),
  frequency: z.number().min(1, 'Frequency must be at least 1'),
  unitCost: z.number().min(0, 'Unit cost must be a positive number'),
  parentCategory: z.string().min(1, 'Parent category ID is required'),
  parentSubcategory: z.string().optional(),
  budget: z.string().min(1, 'Budget ID is required'),
});

interface BudgetPlanningProps {
  isCreateBudgetOpen?: boolean;
  setIsCreateBudgetOpen?: (open: boolean) => void;
  selectedBudgets?: string[];
  onBudgetSelectionChange?: (selectedIds: string[]) => void;
}

function BudgetPlanningContent({
  isCreateBudgetOpen: externalIsCreateBudgetOpen,
  setIsCreateBudgetOpen: externalSetIsCreateBudgetOpen,
  selectedBudgets = [],
  onBudgetSelectionChange
}: BudgetPlanningProps = {}) {
  // Get the active tab from localStorage or default to 'create'
  const getInitialActiveTab = () => {
    if (typeof window !== 'undefined') {
      const savedTab = localStorage.getItem('budgetActiveTab');
      return savedTab || 'create';
    }
    return 'create';
  };

  // State
  const [activeTab, setActiveTab] = useState(getInitialActiveTab);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [expandedSubcategories, setExpandedSubcategories] = useState<string[]>([]);
  const [internalIsCreateBudgetOpen, setInternalIsCreateBudgetOpen] = useState(false);

  // Save active tab to localStorage when it changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (typeof window !== 'undefined') {
      localStorage.setItem('budgetActiveTab', tab);
    }
  };

  // Use external state if provided, otherwise use internal state
  const isCreateBudgetOpen = externalIsCreateBudgetOpen !== undefined ? externalIsCreateBudgetOpen : internalIsCreateBudgetOpen;
  const setIsCreateBudgetOpen = externalSetIsCreateBudgetOpen || setInternalIsCreateBudgetOpen;

  // Get store state and actions
  const {
    budgets,
    currentBudget,
    currentBudgetCategories,
    currentBudgetPerformance,
    isLoading,
    fetchBudgets,
    fetchBudgetById,
    fetchBudgetPerformance,
    createBudget,
    updateBudgetStatus,
    createCategory,
    createSubcategory,
    createItem,
    deleteBudget,
    deleteBudgetItem,
    refreshCache
  } = useBudgetStore();

  // Error handling
  const { error, isErrorOpen, handleApiError, handleGenericError, hideError, handleErrorAction } = useErrorHandler();

  // Fetch budgets on component mount
  useEffect(() => {
    console.log('BudgetPlanning: Component mounted, fetching budgets...');
    fetchBudgets();
  }, [fetchBudgets]);

  // Add timeout for loading state to prevent endless loading
  useEffect(() => {
    if (isLoading) {
      console.log('BudgetPlanning: Loading state detected, setting timeout...');
      const timeout = setTimeout(() => {
        console.warn('BudgetPlanning: Loading timeout reached, forcing loading state to false');
        useBudgetStore.setState({ isLoading: false, error: 'Loading timeout - please refresh the page' });
      }, 30000); // 30 second timeout

      return () => {
        clearTimeout(timeout);
      };
    }
  }, [isLoading]);

  // Toggle category expansion
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  // Toggle subcategory expansion
  const toggleSubcategory = (subcategoryId: string) => {
    setExpandedSubcategories(prev =>
      prev.includes(subcategoryId)
        ? prev.filter(id => id !== subcategoryId)
        : [...prev, subcategoryId]
    );
  };

  // Create budget form
  const budgetForm = useForm<z.infer<typeof budgetFormSchema>>({
    resolver: zodResolver(budgetFormSchema) as any,
    defaultValues: {
      name: '',
      description: '',
      fiscalYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1),
      startDate: new Date(new Date().getFullYear(), 6, 1), // July 1st
      endDate: new Date(new Date().getFullYear() + 1, 5, 30), // June 30th
    },
  });

  // Handle budget form submission
  const onSubmitBudgetForm = async (data: z.infer<typeof budgetFormSchema>) => {
    try {
      const budget = await createBudget(data);
      setIsCreateBudgetOpen(false);
      toast({
        title: 'Success',
        description: 'Budget created successfully',
      });
      // Reset form
      budgetForm.reset();
      // Fetch the created budget
      fetchBudgetById(budget.id);
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('fetch')) {
        // This is likely an API error, try to handle it properly
        handleGenericError(error);
      } else {
        // Fallback to toast for other errors
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'An error occurred',
          variant: 'destructive',
        });
      }
    }
  };

  // Note: Category, subcategory, and item forms are now handled by the BudgetModalsProvider context

  // Note: Form handlers are now managed by the BudgetModalsProvider context



  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Budget Planning Error:', error, errorInfo);
        handleGenericError(error);
      }}
    >
      <div className="space-y-6">
        {/* Error Overlay */}
        <ErrorOverlay
          error={error}
          isOpen={isErrorOpen}
          onClose={hideError}
          onAction={handleErrorAction}
        />
      {/* Create Budget Dialog */}
      <Dialog open={isCreateBudgetOpen} onOpenChange={setIsCreateBudgetOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Budget</DialogTitle>
            <DialogDescription>
              Enter the details for the new budget plan
            </DialogDescription>
          </DialogHeader>
          <Form {...budgetForm}>
            <form onSubmit={budgetForm.handleSubmit(onSubmitBudgetForm)} className="space-y-4">
              <FormField
                control={budgetForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter budget name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={budgetForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter budget description" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={budgetForm.control}
                name="fiscalYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Year</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 2025-2026" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={budgetForm.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={budgetForm.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter>
                <Button type="submit" disabled={budgetForm.formState.isSubmitting}>
                  {budgetForm.formState.isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create Budget
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="create">Budget Planning</TabsTrigger>
          <TabsTrigger value="manage">Manage Budgets</TabsTrigger>
          <TabsTrigger value="approval">Budget Approval</TabsTrigger>
          <TabsTrigger value="reports">Reports & Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-4">
          {!currentBudget ? (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Select or Create a Budget</CardTitle>
                  <CardDescription>
                    To add categories and manage budget details, you need to select an existing budget or create a new one
                  </CardDescription>
                </div>
                <Button onClick={() => setIsCreateBudgetOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Budget
                </Button>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : budgets && budgets.length > 0 ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium text-blue-900 dark:text-blue-100">Select a Budget</h4>
                          <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                            Choose an existing budget from the list below to start adding categories and managing budget details.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectedBudgets.length === budgets.length && budgets.length > 0}
                                onCheckedChange={(checked) => {
                                  if (onBudgetSelectionChange) {
                                    onBudgetSelectionChange(checked ? budgets.map(b => b.id) : []);
                                  }
                                }}
                              />
                            </TableHead>
                            <TableHead>Budget Name</TableHead>
                            <TableHead>Fiscal Year</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {budgets.map(budget => (
                            <TableRow key={budget.id}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedBudgets.includes(budget.id)}
                                  onCheckedChange={(checked) => {
                                    if (onBudgetSelectionChange) {
                                      if (checked) {
                                        onBudgetSelectionChange([...selectedBudgets, budget.id]);
                                      } else {
                                        onBudgetSelectionChange(selectedBudgets.filter(id => id !== budget.id));
                                      }
                                    }
                                  }}
                                />
                              </TableCell>
                              <TableCell className="font-medium">{budget.name}</TableCell>
                              <TableCell>{budget.fiscalYear}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  {budget.status === 'draft' && (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4 text-blue-500" />
                                      <span className="text-blue-500">Draft</span>
                                    </>
                                  )}
                                  {budget.status === 'pending_approval' && (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                                      <span className="text-amber-500">Pending Approval</span>
                                    </>
                                  )}
                                  {budget.status === 'approved' && (
                                    <>
                                      <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                      <span className="text-green-500">Approved</span>
                                    </>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={async () => {
                                    try {
                                      if (!budget.id) {
                                        throw new Error('Budget ID is missing');
                                      }

                                      // Show loading toast
                                      toast({
                                        title: 'Loading Budget',
                                        description: `Loading ${budget.name} for editing...`,
                                      });

                                      // Wait for the budget to be loaded
                                      await fetchBudgetById(budget.id);

                                      // Switch to the create tab after budget is loaded
                                      handleTabChange('create');

                                      // Show success toast
                                      toast({
                                        title: 'Budget Selected',
                                        description: `${budget.name} is now ready for category management`,
                                      });
                                    } catch (error) {
                                      handleGenericError(error instanceof Error ? error : new Error('Failed to load budget'));
                                    }
                                  }}
                                  className="bg-blue-600 hover:bg-blue-700 text-white"
                                >
                                  Select & Manage
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                ) : (
                  <EmptyState
                    icon={<PieChart className="h-12 w-12 text-muted-foreground" />}
                    title="No Budgets Found"
                    description="Create a new budget to get started"
                    action={
                      <Button onClick={() => setIsCreateBudgetOpen(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create New Budget
                      </Button>
                    }
                  />
                )}
              </CardContent>
            </Card>
          ) : (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Budget Information</CardTitle>
                    <CardDescription>
                      Basic information for your budget plan
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Clear current budget selection
                      useBudgetStore.setState({
                        currentBudget: null,
                        currentBudgetCategories: [],
                        currentBudgetPerformance: null
                      });
                    }}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Budget List
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="budget-name">Budget Name</Label>
                      <div className="p-2 border rounded-md bg-muted/20">{currentBudget.name}</div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="fiscal-year">Fiscal Year</Label>
                      <div className="p-2 border rounded-md bg-muted/20">{currentBudget.fiscalYear}</div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="budget-description">Description</Label>
                    <div className="p-2 border rounded-md bg-muted/20">{currentBudget.description || 'No description provided'}</div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Start Date</Label>
                      <div className="p-2 border rounded-md bg-muted/20">
                        {new Date(currentBudget.startDate).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-date">End Date</Label>
                      <div className="p-2 border rounded-md bg-muted/20">
                        {new Date(currentBudget.endDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Budget Performance Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Budget Performance</CardTitle>
                  <CardDescription>
                    Summary of budget vs. actual performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-muted/20 p-4 rounded-md">
                      <div className="text-sm font-medium text-muted-foreground">Total Budgeted Income</div>
                      <div className="text-2xl font-bold mt-1">
                        {formatMWK(
                          currentBudgetPerformance?.budgetedIncome ||
                          currentBudget.totalBudgeted ||
                          currentBudget.totalIncome
                        )}
                      </div>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-md">
                      <div className="text-sm font-medium text-muted-foreground">Actual Income</div>
                      <div className="text-2xl font-bold mt-1 text-green-600">
                        {formatMWK(currentBudgetPerformance?.actualIncome || currentBudget.totalActualIncome || 0)}
                      </div>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-md">
                      <div className="text-sm font-medium text-muted-foreground">Actual Expenses</div>
                      <div className="text-2xl font-bold mt-1 text-red-600">
                        {formatMWK(currentBudgetPerformance?.actualExpense || currentBudget.totalActualExpense || 0)}
                      </div>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-md">
                      <div className="text-sm font-medium text-muted-foreground">Net Position</div>
                      <div className="text-2xl font-bold mt-1">
                        {formatMWK(
                          (currentBudget.totalActualIncome || 0) - (currentBudget.totalActualExpense || 0)
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {currentBudget.lastActualUpdateDate ?
                          `Last updated: ${new Date(currentBudget.lastActualUpdateDate).toLocaleDateString()}` :
                          'Not yet updated with actuals'}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (currentBudget?.id) {
                          console.log('Refreshing budget performance for budget:', currentBudget.id);
                          console.log('Current budget:', currentBudget);
                          console.log('Current budget performance:', currentBudgetPerformance);
                          fetchBudgetPerformance(currentBudget.id)
                            .then((result) => {
                              console.log('Budget performance refreshed:', result);
                              toast({
                                title: 'Success',
                                description: 'Budget performance data refreshed',
                              });
                            })
                            .catch(error => {
                              console.error('Error refreshing budget performance:', error);
                              toast({
                                title: 'Error',
                                description: error instanceof Error ? error.message : 'Failed to refresh performance data',
                                variant: 'destructive',
                              });
                            });
                        } else {
                          console.log('No current budget selected');
                          toast({
                            title: 'Warning',
                            description: 'No budget selected',
                            variant: 'destructive',
                          });
                        }
                      }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Refresh Performance Data
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Budget Fund Overview - Shows income-driven budget */}
              <BudgetFundOverview fiscalYear={currentBudget?.fiscalYear || '2025-2026'} />

              {/* Real-time Budget Integration */}
              {currentBudget && (currentBudget.status === 'approved' || currentBudget.status === 'active') && (
                <RealTimeBudgetIntegration
                  budgetId={currentBudget.id}
                  autoRefresh={true}
                  refreshInterval={30}
                />
              )}

              {/* Note: Modal dialogs are now handled by the BudgetModalsProvider context */}

              <Card className="budget-details-section">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        Budget Details
                        {currentBudget && (
                          <div className="flex items-center gap-1">
                            {currentBudget.status === 'draft' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-xs">
                                <AlertCircle className="h-3 w-3" />
                                Draft
                              </div>
                            )}
                            {currentBudget.status === 'pending_approval' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-amber-50 border border-amber-200 rounded-md text-amber-700 text-xs">
                                <AlertCircle className="h-3 w-3" />
                                Pending Approval
                              </div>
                            )}
                            {currentBudget.status === 'approved' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-green-50 border border-green-200 rounded-md text-green-700 text-xs">
                                <CheckCircle2 className="h-3 w-3" />
                                Approved
                              </div>
                            )}
                            {currentBudget.status === 'rejected' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-red-50 border border-red-200 rounded-md text-red-700 text-xs">
                                <AlertCircle className="h-3 w-3" />
                                Rejected
                              </div>
                            )}
                            {currentBudget.status === 'active' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-emerald-50 border border-emerald-200 rounded-md text-emerald-700 text-xs">
                                <CheckCircle2 className="h-3 w-3" />
                                Active
                              </div>
                            )}
                            {currentBudget.status === 'closed' && (
                              <div className="flex items-center gap-1 px-2 py-1 bg-gray-50 border border-gray-200 rounded-md text-gray-700 text-xs">
                                <AlertCircle className="h-3 w-3" />
                                Closed
                              </div>
                            )}
                          </div>
                        )}
                      </CardTitle>
                      <CardDescription>
                        Define income and expense categories for your budget
                        {currentBudget && (
                          <span className="block text-xs text-muted-foreground mt-1">
                            {currentBudget.name} • {currentBudget.fiscalYear}
                          </span>
                        )}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {currentBudget && (
                      <>
                        {/* Simplified Budget Modals */}
                        <SimpleBudgetModals
                          budgetId={currentBudget.id}
                          onSuccess={() => {
                            if (currentBudget?.id) {
                              fetchBudgetById(currentBudget.id);
                            }
                          }}
                        />

                        {/* Refresh Button */}
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            if (currentBudget?.id) {
                              // Show loading state
                              toast({
                                title: 'Refreshing...',
                                description: 'Refreshing budget data from the database',
                              });

                              try {
                                // Refresh the cache and then fetch the current budget
                                refreshCache()
                                  .then(() => {
                                    return fetchBudgetById(currentBudget.id);
                                  })
                                  .catch(error => {
                                    console.error('Error fetching budget by ID:', error);
                                    // Don't show another error toast as refreshCache already shows one
                                  });
                              } catch (error: unknown) {
                                console.error('Error refreshing cache:', error);
                                toast({
                                  title: 'Error',
                                  description: error instanceof Error ? error.message : 'An error occurred',
                                  variant: 'destructive',
                                });
                              }
                            } else {
                              // If no current budget, just refresh the cache
                              try {
                                refreshCache();
                              } catch (error: unknown) {
                                console.error('Error refreshing cache:', error);
                                toast({
                                  title: 'Error',
                                  description: error instanceof Error ? error.message : 'An error occurred',
                                  variant: 'destructive',
                                });
                              }
                            }
                          }}
                          title="Refresh budget data"
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>

                        {/* Hidden Import/Export trigger for the dropdown menu */}
                        <div className="hidden">
                          <BudgetImportExport
                            id="budget-import-export-trigger"
                            budgetId={currentBudget.id}
                            onSuccess={() => {
                              if (currentBudget?.id) {
                                fetchBudgetById(currentBudget.id);
                              }
                            }}
                          />
                        </div>
                      </>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex flex-col justify-center items-center py-8 space-y-4">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <div className="text-sm text-muted-foreground">
                        Loading budget data...
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Current Budget: {currentBudget?.name || 'None'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Categories: {currentBudgetCategories?.length || 0}
                      </div>
                    </div>
                  ) : currentBudgetCategories && currentBudgetCategories.length > 0 ? (
                    <div className="border rounded-md">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[40%]">Description</TableHead>
                            <TableHead>Budgeted</TableHead>
                            <TableHead>Actual</TableHead>
                            <TableHead>Variance</TableHead>
                            <TableHead className="text-right">%</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {(currentBudgetCategories as BudgetCategoryDisplay[]).map((category: BudgetCategoryDisplay, catIndex: number) => {
                            const categoryKey = category._id ? category._id.toString() : `category-${catIndex}`;
                            const categoryRows = [];

                            // Category row
                            categoryRows.push(
                              <TableRow key={`category-row-${categoryKey}`} className="bg-muted/50 hover:bg-muted cursor-pointer" onClick={() => toggleCategory(category._id.toString())}>
                                <TableCell colSpan={1} className="font-bold">
                                  {expandedCategories.includes(category._id.toString()) ? '▼' : '►'} {category.name}
                                </TableCell>
                                <TableCell className="font-bold">{formatMWK(category.budgetedAmount || category.total || 0)}</TableCell>
                                <TableCell className="font-bold">{formatMWK(category.actualAmount || 0)}</TableCell>
                                <TableCell className="font-bold">
                                  {formatMWK((category.budgetedAmount || category.total || 0) - (category.actualAmount || 0))}
                                </TableCell>
                                <TableCell className="text-right font-bold">
                                  {category.budgetedAmount && category.budgetedAmount > 0
                                    ? (((category.budgetedAmount - (category.actualAmount || 0)) / category.budgetedAmount) * 100).toFixed(1) + '%'
                                    : '0.0%'}
                                </TableCell>
                              </TableRow>
                            );

                            // Subcategory rows
                            if (expandedCategories.includes(category._id.toString()) && category.subcategories) {
                              category.subcategories.forEach((subcategory: BudgetSubcategoryDisplay, subIndex: number) => {
                                const subcategoryKey = subcategory._id ? subcategory._id.toString() : `subcategory-${category._id}-${subIndex}`;

                                categoryRows.push(
                                  <TableRow key={`subcategory-row-${subcategoryKey}`} className="bg-muted/20 hover:bg-muted/30 cursor-pointer" onClick={() => toggleSubcategory(subcategory._id.toString())}>
                                    <TableCell colSpan={1} className="pl-6 font-medium">
                                      {expandedSubcategories.includes(subcategory._id.toString()) ? '▼' : '►'} {subcategory.name}
                                    </TableCell>
                                    <TableCell className="font-medium">{formatMWK(subcategory.total)}</TableCell>
                                    <TableCell className="font-medium">{formatMWK(0)}</TableCell>
                                    <TableCell className="font-medium">{formatMWK(subcategory.total)}</TableCell>
                                    <TableCell className="text-right font-medium">100.0%</TableCell>
                                  </TableRow>
                                );

                                // Item rows
                                if (expandedSubcategories.includes(subcategory._id.toString()) && subcategory.items) {
                                  subcategory.items.forEach((item: BudgetItemDisplay, itemIndex: number) => {
                                    const itemKey = item._id ? item._id.toString() : `item-${subcategory._id}-${itemIndex}`;
                                    categoryRows.push(
                                      <TableRow key={`item-row-${itemKey}`} className="hover:bg-muted/10">
                                        <TableCell className="pl-12">
                                          <div className="flex items-center justify-between">
                                            <span>{item.name}</span>
                                            {currentBudget?.status === 'draft' && (
                                              <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                                  >
                                                    <Trash2 className="h-3 w-3" />
                                                  </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                  <AlertDialogHeader>
                                                    <AlertDialogTitle>Delete Budget Item</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                      Are you sure you want to delete "{item.name}"? This action cannot be undone.
                                                    </AlertDialogDescription>
                                                  </AlertDialogHeader>
                                                  <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction
                                                      onClick={async () => {
                                                        try {
                                                          await deleteBudgetItem(currentBudget.id, category._id.toString(), item._id.toString());
                                                          // Refresh the budget data
                                                          await fetchBudgetById(currentBudget.id);
                                                        } catch (error) {
                                                          // Error is already handled in the store
                                                          console.error('Failed to delete budget item:', error);
                                                        }
                                                      }}
                                                    >
                                                      Delete
                                                    </AlertDialogAction>
                                                  </AlertDialogFooter>
                                                </AlertDialogContent>
                                              </AlertDialog>
                                            )}
                                          </div>
                                        </TableCell>
                                        <TableCell>{formatMWK(item.amount)}</TableCell>
                                        <TableCell>{formatMWK(0)}</TableCell>
                                        <TableCell>{formatMWK(item.amount)}</TableCell>
                                        <TableCell className="text-right">100.0%</TableCell>
                                      </TableRow>
                                    );
                                  });
                                }

                                // Add item row for subcategory
                                if (expandedSubcategories.includes(subcategory._id.toString())) {
                                  categoryRows.push(
                                    <TableRow key={`add-item-${subcategory._id}`}>
                                      <TableCell colSpan={5} className="pl-12 border-b-0">
                                        <div className="text-sm text-muted-foreground">
                                          Items can be added through the category management system
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  );
                                }
                              });
                            }

                            // Add subcategory row for category
                            if (expandedCategories.includes(category._id.toString())) {
                              categoryRows.push(
                                <TableRow key={`add-subcategory-${category._id}`}>
                                  <TableCell colSpan={5} className="pl-6 border-b-0">
                                    <div className="text-sm text-muted-foreground">
                                      Subcategories can be added through the category management system
                                    </div>
                                  </TableCell>
                                </TableRow>
                              );
                            }

                            return categoryRows;
                          })}

                          <TableRow key="add-category">
                            <TableCell colSpan={5} className="border-b-0">
                              <div className="text-center py-4 text-muted-foreground">
                                Use the "Add Category" button above to create new budget categories
                              </div>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="p-6 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                              <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-green-900 dark:text-green-100">Add Budget Categories</h4>
                            <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                              Great! You've selected a budget. Now use the <strong>"Add Category"</strong> button above to create income and expense categories.
                            </p>
                          </div>
                        </div>
                      </div>

                      <EmptyState
                        icon={<FileText className="h-12 w-12 text-muted-foreground" />}
                        title="No Categories Yet"
                        description="Start building your budget by adding categories for income and expenses"
                        action={
                          <div className="flex flex-col gap-4 items-center">
                            <div className="text-sm text-muted-foreground">
                              Look for the blue <strong>"Add Category"</strong> button in the header above
                            </div>
                            <p className="text-sm text-muted-foreground">
                              You can also import budget items from Excel or CSV files
                            </p>
                          </div>
                        }
                      />
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Cancel</Button>

                  {/* Dynamic button based on budget status */}
                  {currentBudget?.status === 'draft' && (
                    <Button
                      onClick={() => {
                        if (currentBudget?.id) {
                          updateBudgetStatus(currentBudget.id, 'submit')
                            .then(() => {
                              toast({
                                title: 'Success',
                                description: 'Budget submitted for approval',
                              });
                            })
                            .catch(error => {
                              toast({
                                title: 'Error',
                                description: error instanceof Error ? error.message : 'Failed to submit budget',
                                variant: 'destructive',
                              });
                            });
                        }
                      }}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Submit for Approval
                    </Button>
                  )}

                  {currentBudget?.status === 'pending_approval' && (
                    <Button disabled variant="outline" className="bg-amber-50 border-amber-200 text-amber-700">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Pending Approval
                    </Button>
                  )}

                  {currentBudget?.status === 'approved' && (
                    <Button disabled variant="outline" className="bg-green-50 border-green-200 text-green-700">
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Approved
                    </Button>
                  )}

                  {currentBudget?.status === 'rejected' && (
                    <Button disabled variant="outline" className="bg-red-50 border-red-200 text-red-700">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Rejected
                    </Button>
                  )}

                  {currentBudget?.status === 'active' && (
                    <Button disabled variant="outline" className="bg-emerald-50 border-emerald-200 text-emerald-700">
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Active
                    </Button>
                  )}

                  {currentBudget?.status === 'closed' && (
                    <Button disabled variant="outline" className="bg-gray-50 border-gray-200 text-gray-700">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Closed
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="approval" className="space-y-4">
          {currentBudget ? (
            <SimpleBudgetApproval budgetId={currentBudget.id} />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Budget Approval</CardTitle>
                <CardDescription>
                  Select a budget to review and approve
                </CardDescription>
              </CardHeader>
              <CardContent>
                {budgets && budgets.length > 0 ? (
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground mb-4">
                      Select a budget below to review and manage its approval status:
                    </div>
                    <div className="border rounded-md">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Budget Name</TableHead>
                            <TableHead>Fiscal Year</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Created Date</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {budgets.map(budget => (
                            <TableRow key={budget.id}>
                              <TableCell>
                                <div className="font-medium">{budget.name}</div>
                              </TableCell>
                              <TableCell>{budget.fiscalYear}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  {budget.status === 'draft' && (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4 text-blue-500" />
                                      <span className="text-blue-500">Draft</span>
                                    </>
                                  )}
                                  {budget.status === 'pending_approval' && (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                                      <span className="text-amber-500">Pending Approval</span>
                                    </>
                                  )}
                                  {budget.status === 'approved' && (
                                    <>
                                      <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                      <span className="text-green-500">Approved</span>
                                    </>
                                  )}
                                  {budget.status === 'rejected' && (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                                      <span className="text-red-500">Rejected</span>
                                    </>
                                  )}
                                  {budget.status === 'active' && (
                                    <>
                                      <CheckCircle2 className="mr-2 h-4 w-4 text-emerald-500" />
                                      <span className="text-emerald-500">Active</span>
                                    </>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>{new Date(budget.createdAt).toLocaleDateString()}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={async () => {
                                    try {
                                      await fetchBudgetById(budget.id);
                                      const isApproved = budget.status === 'approved' || budget.status === 'active';
                                      toast({
                                        title: 'Budget Selected',
                                        description: isApproved
                                          ? `${budget.name} details are now available for viewing`
                                          : `${budget.name} is now ready for approval review`,
                                      });
                                    } catch (error) {
                                      toast({
                                        title: 'Error',
                                        description: error instanceof Error ? error.message : 'Failed to load budget',
                                        variant: 'destructive',
                                      });
                                    }
                                  }}
                                >
                                  {budget.status === 'approved' || budget.status === 'active' ? (
                                    <Eye className="mr-2 h-4 w-4" />
                                  ) : (
                                    <FileText className="mr-2 h-4 w-4" />
                                  )}
                                  {budget.status === 'approved' || budget.status === 'active'
                                    ? 'View Details'
                                    : 'Review & Approve'
                                  }
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                ) : (
                  <EmptyState
                    icon={<CheckCircle2 className="h-12 w-12 text-muted-foreground" />}
                    title="No Budgets Found"
                    description="Create a budget first to manage approvals"
                    action={
                      <Button onClick={() => setActiveTab('manage')}>
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Go to Budget Management
                      </Button>
                    }
                  />
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="manage" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Manage Budgets</CardTitle>
                <CardDescription>
                  View and manage all budget plans for the Teachers Council of Malawi
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  // Show loading state
                  toast({
                    title: 'Refreshing...',
                    description: 'Refreshing budget data from the database',
                  });

                  try {
                    // Refresh the cache
                    refreshCache();
                    // Note: We don't need to show a success toast here as refreshCache already shows one
                  } catch (error) {
                    console.error('Error refreshing cache:', error);
                    // Note: We don't need to show an error toast here as refreshCache already shows one
                  }
                }}
                title="Refresh budget list"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : budgets && budgets.length > 0 ? (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Budget Name</TableHead>
                        <TableHead>Fiscal Year</TableHead>
                        <TableHead>Total Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Modified</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {budgets.map(budget => (
                        <TableRow key={budget.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">{budget.name}</div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="xs"
                                  onClick={async () => {
                                    try {
                                      // Select this budget to manage categories
                                      await fetchBudgetById(budget.id);
                                      handleTabChange('create');
                                      toast({
                                        title: 'Budget Selected',
                                        description: `${budget.name} is ready for category management`,
                                      });
                                    } catch (error) {
                                      toast({
                                        title: 'Error',
                                        description: error instanceof Error ? error.message : 'Failed to load budget',
                                        variant: 'destructive',
                                      });
                                    }
                                  }}
                                >
                                  <FolderPlus className="mr-1 h-3 w-3" />
                                  Manage
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="xs"
                                  onClick={async () => {
                                    try {
                                      // Select this budget to view items
                                      await fetchBudgetById(budget.id);
                                      handleTabChange('create');
                                      toast({
                                        title: 'Budget Selected',
                                        description: 'You can now view and manage budget items in the Budget Planning tab',
                                      });
                                    } catch (error) {
                                      toast({
                                        title: 'Error',
                                        description: error instanceof Error ? error.message : 'Failed to load budget',
                                        variant: 'destructive',
                                      });
                                    }
                                  }}
                                >
                                  <FileText className="mr-1 h-3 w-3" />
                                  View Items
                                </Button>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{budget.fiscalYear}</TableCell>
                          <TableCell>{formatMWK(budget.totalIncome || 0)}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {budget.status === 'approved' && (
                                <>
                                  <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                  <span className="text-green-500">Approved</span>
                                </>
                              )}
                              {budget.status === 'pending_approval' && (
                                <>
                                  <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                                  <span className="text-amber-500">Pending Approval</span>
                                </>
                              )}
                              {budget.status === 'draft' && (
                                <>
                                  <AlertCircle className="mr-2 h-4 w-4 text-blue-500" />
                                  <span className="text-blue-500">Draft</span>
                                </>
                              )}
                              {budget.status === 'active' && (
                                <>
                                  <CheckCircle2 className="mr-2 h-4 w-4 text-emerald-500" />
                                  <span className="text-emerald-500">Active</span>
                                </>
                              )}
                              {budget.status === 'closed' && (
                                <>
                                  <AlertCircle className="mr-2 h-4 w-4 text-gray-500" />
                                  <span className="text-gray-500">Closed</span>
                                </>
                              )}
                              {budget.status === 'rejected' && (
                                <>
                                  <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                                  <span className="text-red-500">Rejected</span>
                                </>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{new Date(budget.updatedAt || budget.createdAt).toLocaleDateString()}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  try {
                                    // Show a loading toast
                                    toast({
                                      title: 'Loading Budget',
                                      description: `Loading ${budget.name} for editing...`,
                                    });

                                    // First select the budget
                                    await fetchBudgetById(budget.id);

                                    // Switch to the budget details tab
                                    handleTabChange('create');

                                    // Show success toast
                                    toast({
                                      title: 'Budget Selected',
                                      description: `${budget.name} has been selected for editing`,
                                      className: 'bg-blue-100 border-blue-300 text-blue-800',
                                    });

                                    // Scroll to the budget details section
                                    setTimeout(() => {
                                      const budgetDetailsElement = document.querySelector('.budget-details-section');
                                      if (budgetDetailsElement) {
                                        budgetDetailsElement.scrollIntoView({ behavior: 'smooth' });
                                      }
                                    }, 300);
                                  } catch (error) {
                                    toast({
                                      title: 'Error',
                                      description: error instanceof Error ? error.message : 'Failed to load budget',
                                      variant: 'destructive',
                                    });
                                  }
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Select & Edit
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Download className="h-4 w-4" />
                              </Button>
                              {budget.status === 'draft' && (
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Delete Budget</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to delete this budget? This action cannot be undone.
                                        All categories, subcategories, and items associated with this budget will also be deleted.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => {
                                          deleteBudget(budget.id)
                                            .then(() => {
                                              // Refresh the cache to update the UI
                                              try {
                                                refreshCache();
                                                // Note: We don't need to show a success toast here as deleteBudget already shows one
                                              } catch (error) {
                                                console.error('Error refreshing cache:', error);
                                                // Don't show another error toast as refreshCache already shows one
                                              }
                                            })
                                            .catch(error => {
                                              toast({
                                                title: 'Error',
                                                description: error instanceof Error ? error.message : 'Failed to delete budget',
                                                variant: 'destructive',
                                              });
                                            });
                                        }}
                                      >
                                        Delete
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <EmptyState
                  icon={<PieChart className="h-12 w-12 text-muted-foreground" />}
                  title="No Budgets Found"
                  description="Create a new budget to get started"
                  action={
                    <Button onClick={() => setIsCreateBudgetOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Budget
                    </Button>
                  }
                />
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={() => setIsCreateBudgetOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Budget
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>



        <TabsContent value="reports" className="space-y-4">
          {currentBudget ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Budget Analytics</CardTitle>
                  <CardDescription>
                    View performance metrics and analytics for {currentBudget.name}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AnalyticsDashboard budgetId={currentBudget.id} />
                </CardContent>
              </Card>

              <BudgetReportExport
                budgetId={currentBudget.id}
                budgetName={currentBudget.name}
                fiscalYear={currentBudget.fiscalYear}
              />
            </>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Budget Reports</CardTitle>
                <CardDescription>
                  Generate and view budget reports
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px] flex items-center justify-center">
                <div className="text-center">
                  <FileBarChart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium">Select a Budget</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Please select a budget from the Manage Budgets tab to view reports
                  </p>
                  <Button onClick={() => setActiveTab('manage')}>
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Go to Budget List
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

      </Tabs>
      </div>
    </ErrorBoundary>
  );
}

// Export the simplified component directly
export function BudgetPlanning(props: BudgetPlanningProps = {}) {
  return <BudgetPlanningContent {...props} />;
}

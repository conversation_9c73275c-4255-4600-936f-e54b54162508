'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  FolderPlus,
  FileSpreadsheet,
  Plus,
  ListTree,
  FolderTree,
  FileText,
  Upload,
  Download
} from 'lucide-react'
import { BudgetImportExport } from './budget-import-export'
import { useBudgetStore } from '@/lib/stores/budget-store'

interface BudgetStructureManagerProps {
  budgetId: string;
  onCreateCategory: () => void;
  onCreateSubcategory: () => void;
  onCreateItem: () => void;
  onSuccess?: () => void;
}

export function BudgetStructureManager({
  budgetId,
  onCreateCategory,
  onCreateSubcategory,
  onCreateItem,
  onSuccess
}: BudgetStructureManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('create')

  const { fetchBudgetById } = useBudgetStore()

  const handleSuccess = () => {
    setIsDialogOpen(false)
    if (onSuccess) {
      onSuccess()
    }
    // Refresh budget data
    fetchBudgetById(budgetId)
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <ListTree className="h-4 w-4" />
          <span>Budget Structure</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Manage Budget Structure</DialogTitle>
          <DialogDescription>
            Create or import categories, subcategories, and budget items
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">Create</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <Button
                variant="outline"
                className="h-24 flex flex-col items-center justify-center gap-2"
                onClick={() => {
                  setIsDialogOpen(false)
                  onCreateCategory()
                }}
              >
                <FolderPlus className="h-8 w-8 text-primary" />
                <span className="text-sm font-medium">Create Category</span>
                <span className="text-xs text-muted-foreground">Add income or expense categories</span>
              </Button>

              <Button
                variant="outline"
                className="h-24 flex flex-col items-center justify-center gap-2"
                onClick={() => {
                  setIsDialogOpen(false)
                  onCreateSubcategory()
                }}
              >
                <FolderTree className="h-8 w-8 text-primary" />
                <span className="text-sm font-medium">Create Subcategory</span>
                <span className="text-xs text-muted-foreground">Add subcategories to existing categories</span>
              </Button>

              <Button
                variant="outline"
                className="h-24 flex flex-col items-center justify-center gap-2"
                onClick={() => {
                  setIsDialogOpen(false)
                  onCreateItem()
                }}
              >
                <FileText className="h-8 w-8 text-primary" />
                <span className="text-sm font-medium">Create Budget Item</span>
                <span className="text-xs text-muted-foreground">Add specific budget items with amounts</span>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="import" className="py-4">
            <BudgetImportExport
              budgetId={budgetId}
              onSuccess={handleSuccess}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

// Quick access dropdown menu for budget structure
export function BudgetStructureQuickAccess({
  budgetId,
  onCreateCategory,
  onCreateSubcategory,
  onCreateItem,
  onSuccess
}: BudgetStructureManagerProps) {
  const { fetchBudgetById } = useBudgetStore()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Plus className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Add to Budget</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            // Directly call the onCreateCategory function to open the modal
            onCreateCategory();
          }}
        >
          <FolderPlus className="mr-2 h-4 w-4" />
          <span>Add Category</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            // Directly call the onCreateSubcategory function to open the modal
            onCreateSubcategory();
          }}
        >
          <FolderTree className="mr-2 h-4 w-4" />
          <span>Add Subcategory</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            // Directly call the onCreateItem function to open the modal
            onCreateItem();
          }}
        >
          <FileText className="mr-2 h-4 w-4" />
          <span>Add Budget Item</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => {
          // Open the import dialog
          const importExportElement = document.getElementById('budget-import-export-trigger');
          if (importExportElement) {
            importExportElement.click();
          }
        }}>
          <Upload className="mr-2 h-4 w-4" />
          <span>Import Items</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

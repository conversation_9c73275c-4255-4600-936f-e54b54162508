"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  ArrowUpRight, 
  ArrowDownRight, 
  Bell, 
  BellOff,
  Check,
  X,
  ChevronDown,
  ChevronUp,
  Filter
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { formatCurrency } from '@/lib/utils';

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Format percentage
const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`;
};

interface BudgetVarianceAlertsProps {
  budgetId: string;
  categories: unknown[];
  thresholds?: {
    warning: number;
    critical: number;
  };
}

export function BudgetVarianceAlerts({ 
  budgetId,
  categories,
  thresholds = { warning: 10, critical: 20 }
}: BudgetVarianceAlertsProps) {
  const [activeTab, setActiveTab] = useState('all');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [alertsEnabled, setAlertsEnabled] = useState(true);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({
    key: 'variance',
    direction: 'descending'
  });

  // Calculate variance percentage
  const calculateVariancePercentage = (budgeted: number, actual: number) => {
    if (budgeted === 0) return 0;
    return ((actual - budgeted) / Math.abs(budgeted)) * 100;
  };

  // Determine alert level based on variance percentage
  const getAlertLevel = (variancePercentage: number, type: 'income' | 'expense') => {
    const absVariance = Math.abs(variancePercentage);
    
    // For income, negative variance is bad (under-performing)
    // For expenses, positive variance is bad (over-spending)
    const isNegativeImpact = (type === 'income' && variancePercentage < 0) || 
                            (type === 'expense' && variancePercentage > 0);
    
    if (!isNegativeImpact) return 'success';
    if (absVariance >= thresholds.critical) return 'critical';
    if (absVariance >= thresholds.warning) return 'warning';
    return 'normal';
  };

  // Process categories to add variance data
  const processedCategories = categories.map(category => {
    const budgeted = category.budgetedAmount || category.total || 0;
    const actual = category.actualAmount || 0;
    const variance = actual - budgeted;
    const variancePercentage = calculateVariancePercentage(budgeted, actual);
    const alertLevel = getAlertLevel(variancePercentage, category.type as 'income' | 'expense');
    
    return {
      ...category,
      budgeted,
      actual,
      variance,
      variancePercentage,
      alertLevel
    };
  });

  // Filter categories based on active tab
  const filteredCategories = processedCategories.filter(category => {
    if (activeTab === 'all') return true;
    if (activeTab === 'critical') return category.alertLevel === 'critical';
    if (activeTab === 'warning') return category.alertLevel === 'warning';
    if (activeTab === 'success') return category.alertLevel === 'success';
    return true;
  });

  // Sort categories
  const sortedCategories = [...filteredCategories].sort((a, b) => {
    if (sortConfig.key === 'name') {
      return sortConfig.direction === 'ascending'
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    }
    
    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];
    
    if (sortConfig.direction === 'ascending') {
      return aValue - bValue;
    }
    return bValue - aValue;
  });

  // Toggle item expansion
  const toggleItemExpansion = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Handle sort
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'ascending' 
        ? 'descending' 
        : 'ascending'
    }));
  };

  // Toggle alerts
  const toggleAlerts = () => {
    setAlertsEnabled(prev => !prev);
    
    toast({
      title: alertsEnabled ? 'Alerts Disabled' : 'Alerts Enabled',
      description: alertsEnabled 
        ? 'Budget variance alerts have been disabled' 
        : 'Budget variance alerts have been enabled',
      variant: alertsEnabled ? 'default' : 'default',
    });
  };

  // Count alerts by level
  const alertCounts = {
    critical: processedCategories.filter(c => c.alertLevel === 'critical').length,
    warning: processedCategories.filter(c => c.alertLevel === 'secondary').length,
    success: processedCategories.filter(c => c.alertLevel === 'default').length,
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Budget Variance Alerts</CardTitle>
          <CardDescription>
            Monitor budget performance and get alerts for significant variances
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="alerts-enabled"
              checked={alertsEnabled}
              onCheckedChange={toggleAlerts}
            />
            <Label htmlFor="alerts-enabled">
              {alertsEnabled ? (
                <span className="flex items-center text-sm">
                  <Bell className="h-4 w-4 mr-1 text-primary" />
                  Alerts On
                </span>
              ) : (
                <span className="flex items-center text-sm text-muted-foreground">
                  <BellOff className="h-4 w-4 mr-1" />
                  Alerts Off
                </span>
              )}
            </Label>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleSort('name')}>
                Sort by Name
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('variance')}>
                Sort by Variance Amount
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('variancePercentage')}>
                Sort by Variance Percentage
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="all">
              All ({processedCategories.length})
            </TabsTrigger>
            <TabsTrigger value="critical" className="text-red-500">
              Critical ({alertCounts.critical})
            </TabsTrigger>
            <TabsTrigger value="warning" className="text-amber-500">
              Warning ({alertCounts.warning})
            </TabsTrigger>
            <TabsTrigger value="success" className="text-green-500">
              On Track ({alertCounts.success})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab}>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30%]">Category</TableHead>
                    <TableHead>Budgeted</TableHead>
                    <TableHead>Actual</TableHead>
                    <TableHead>Variance</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedCategories.length > 0 ? (
                    sortedCategories.map(category => (
                      <React.Fragment key={category._id}>
                        <TableRow
                          key={`variance-category-${category._id}`}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => toggleItemExpansion(category._id)}
                        >
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              {expandedItems.includes(category._id) ? (
                                <ChevronUp className="h-4 w-4 mr-2 text-muted-foreground" />
                              ) : (
                                <ChevronDown className="h-4 w-4 mr-2 text-muted-foreground" />
                              )}
                              {category.name}
                              <Badge
                                variant="outline"
                                className="ml-2 capitalize"
                              >
                                {category.type}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>{formatCurrency(category.budgeted)}</TableCell>
                          <TableCell>{formatCurrency(category.actual)}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {category.variance > 0 ? (
                                <ArrowUpRight className={`h-4 w-4 mr-1 ${category.type === 'income' ? 'text-green-500' : 'text-red-500'}`} />
                              ) : category.variance < 0 ? (
                                <ArrowDownRight className={`h-4 w-4 mr-1 ${category.type === 'income' ? 'text-red-500' : 'text-green-500'}`} />
                              ) : null}
                              {formatCurrency(Math.abs(category.variance))}
                              <span className="ml-1 text-xs text-muted-foreground">
                                ({formatPercentage(Math.abs(category.variancePercentage))})
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {category.alertLevel === 'critical' && (
                              <Badge variant="destructive" className="flex items-center gap-1">
                                <AlertTriangle className="h-3 w-3" />
                                Critical
                              </Badge>
                            )}
                            {category.alertLevel === 'warning' && (
                              <Badge variant="secondary" className="flex items-center gap-1 bg-amber-500">
                                <AlertTriangle className="h-3 w-3" />
                                Warning
                              </Badge>
                            )}
                            {category.alertLevel === 'success' && (
                              <Badge variant="default" className="flex items-center gap-1 bg-green-500">
                                <Check className="h-3 w-3" />
                                On Track
                              </Badge>
                            )}
                            {category.alertLevel === 'normal' && (
                              <Badge variant="outline" className="flex items-center gap-1">
                                Normal
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>

                        {expandedItems.includes(category._id) && (
                          <TableRow key={`variance-details-${category._id}`}>
                            <TableCell colSpan={5} className="p-0">
                              <div className="p-4 bg-muted/20">
                                <div className="space-y-4">
                                  <div>
                                    <h4 className="text-sm font-medium mb-1">Performance</h4>
                                    <div className="flex items-center gap-2">
                                      <Progress
                                        value={Math.min(100, (category.actual / category.budgeted) * 100)}
                                        className="h-2"
                                      />
                                      <span className="text-xs">
                                        {formatPercentage((category.actual / category.budgeted) * 100)}
                                      </span>
                                    </div>
                                  </div>

                                  {category.description && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-1">Description</h4>
                                      <p className="text-sm text-muted-foreground">{category.description}</p>
                                    </div>
                                  )}

                                  <div className="flex justify-end">
                                    <Button variant="outline" size="sm">
                                      View Transactions
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        No {activeTab !== 'all' ? activeTab : ''} alerts found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import {
  History,
  FileText,
  Edit,
  Save,
  Plus,
  ArrowRight,
  ArrowUpRight,
  ArrowDownRight,
  Loader2,
  Calendar,
  User,
  Check,
  X,
  AlertTriangle,
  FileBarChart
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { EmptyState } from '@/components/empty-state';

interface BudgetRevisionProps {
  budgetId?: string;
  budgetName?: string;
  fiscalYear?: string;
  onRevisionCreated?: () => void;
}

export function BudgetRevision({
  budgetId,
  budgetName = 'Annual Operating Budget 2025',
  fiscalYear = '2025-2026',
  onRevisionCreated
}: BudgetRevisionProps) {
  const [activeTab, setActiveTab] = useState('history');
  const [isLoading, setIsLoading] = useState(false);
  const [isCreateRevisionOpen, setIsCreateRevisionOpen] = useState(false);
  const [revisionReason, setRevisionReason] = useState('');
  const [revisionNote, setRevisionNote] = useState('');
  const [revisionItems, setRevisionItems] = useState<unknown[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<string>('');
  const [newAmount, setNewAmount] = useState<string>('');

  // Mock revision history
  const revisionHistory = [
    {
      id: 'rev1',
      version: 'v1.1',
      createdAt: '2025-02-05T11:10:00Z',
      createdBy: {
        name: 'Bob Johnson',
        role: 'Budget Officer'
      },
      reason: 'Adjusted for new funding allocation',
      status: 'approved',
      changes: [
        {
          categoryId: 'cat1',
          categoryName: 'Government Subvention',
          itemId: 'item1',
          itemName: 'Annual Government Grant',
          oldAmount: 5000000,
          newAmount: 5500000,
          difference: 500000,
          percentChange: 10
        }
      ]
    },
    {
      id: 'rev2',
      version: 'v1.2',
      createdAt: '2025-03-15T09:30:00Z',
      createdBy: {
        name: 'Jane Smith',
        role: 'Finance Director'
      },
      reason: 'Budget adjustment for Q2 priorities',
      status: 'approved',
      changes: [
        {
          categoryId: 'cat2',
          categoryName: 'Operational Expenses',
          itemId: 'item2',
          itemName: 'Office Supplies',
          oldAmount: 250000,
          newAmount: 200000,
          difference: -50000,
          percentChange: -20
        },
        {
          categoryId: 'cat2',
          categoryName: 'Operational Expenses',
          itemId: 'item3',
          itemName: 'Staff Training',
          oldAmount: 300000,
          newAmount: 400000,
          difference: 100000,
          percentChange: 33.33
        }
      ]
    },
    {
      id: 'rev3',
      version: 'v1.3',
      createdAt: '2025-05-20T14:45:00Z',
      createdBy: {
        name: 'John Doe',
        role: 'Finance Manager'
      },
      reason: 'Mid-year budget revision',
      status: 'pending',
      changes: [
        {
          categoryId: 'cat3',
          categoryName: 'Capital Expenditure',
          itemId: 'item4',
          itemName: 'IT Equipment',
          oldAmount: 1200000,
          newAmount: 1500000,
          difference: 300000,
          percentChange: 25
        }
      ]
    }
  ];

  // Mock categories and items for the budget
  const budgetCategories = [
    {
      id: 'cat1',
      name: 'Government Subvention',
      type: 'income',
      items: [
        { id: 'item1', name: 'Annual Government Grant', amount: 5500000 }
      ]
    },
    {
      id: 'cat2',
      name: 'Operational Expenses',
      type: 'expense',
      items: [
        { id: 'item2', name: 'Office Supplies', amount: 200000 },
        { id: 'item3', name: 'Staff Training', amount: 400000 },
        { id: 'item5', name: 'Utilities', amount: 350000 }
      ]
    },
    {
      id: 'cat3',
      name: 'Capital Expenditure',
      type: 'expense',
      items: [
        { id: 'item4', name: 'IT Equipment', amount: 1500000 },
        { id: 'item6', name: 'Office Furniture', amount: 800000 }
      ]
    }
  ];

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Add item to revision
  const addItemToRevision = () => {
    if (!selectedCategory || !selectedItem || !newAmount) {
      toast({
        title: 'Validation Error',
        description: 'Please select a category, item, and enter a new amount',
        variant: 'destructive',
      });
      return;
    }

    // Find category and item
    const category = budgetCategories.find(c => c.id === selectedCategory);
    if (!category) return;

    const item = category.items.find(i => i.id === selectedItem);
    if (!item) return;

    // Calculate difference and percent change
    const oldAmount = item.amount;
    const parsedNewAmount = parseFloat(newAmount);
    const difference = parsedNewAmount - oldAmount;
    const percentChange = (difference / oldAmount) * 100;

    // Add to revision items
    const newItem = {
      categoryId: category.id,
      categoryName: category.name,
      categoryType: category.type,
      itemId: item.id,
      itemName: item.name,
      oldAmount,
      newAmount: parsedNewAmount,
      difference,
      percentChange
    };

    // Check if item already exists in revision
    const existingIndex = revisionItems.findIndex(i => i.itemId === item.id);
    if (existingIndex >= 0) {
      // Update existing item
      const updatedItems = [...revisionItems];
      updatedItems[existingIndex] = newItem;
      setRevisionItems(updatedItems);
    } else {
      // Add new item
      setRevisionItems([...revisionItems, newItem]);
    }

    // Reset selection
    setSelectedItem('');
    setNewAmount('');

    toast({
      title: 'Item Added',
      description: `${item.name} has been added to the revision`,
    });
  };

  // Remove item from revision
  const removeItemFromRevision = (itemId: string) => {
    setRevisionItems(revisionItems.filter(item => item.itemId !== itemId));

    toast({
      title: 'Item Removed',
      description: 'Item has been removed from the revision',
    });
  };

  // Submit revision
  const submitRevision = async () => {
    if (!revisionReason) {
      toast({
        title: 'Validation Error',
        description: 'Please provide a reason for this revision',
        variant: 'destructive',
      });
      return;
    }

    if (revisionItems.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please add at least one item to the revision',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // In a real implementation, this would call an API
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Close dialog and reset form
      setIsCreateRevisionOpen(false);
      setRevisionReason('');
      setRevisionNote('');
      setRevisionItems([]);
      setSelectedCategory('');
      setSelectedItem('');
      setNewAmount('');

      // Notify parent component
      if (onRevisionCreated) {
        onRevisionCreated();
      }

      toast({
        title: 'Revision Created',
        description: 'Budget revision has been created successfully',
      });
    } catch (error) {
      console.error('Error creating revision:', error);
      toast({
        title: 'Error',
        description: 'Failed to create budget revision',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate total impact of revision
  const calculateTotalImpact = () => {
    const totalDifference = revisionItems.reduce((sum, item) => sum + item.difference, 0);
    const totalPercentage = revisionItems.length > 0
      ? (totalDifference / revisionItems.reduce((sum, item) => sum + item.oldAmount, 0)) * 100
      : 0;

    return {
      totalDifference,
      totalPercentage
    };
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
            <Check className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
            <X className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Budget Revisions</CardTitle>
            <CardDescription>
              Manage and track changes to the budget plan
            </CardDescription>
          </div>
          <Dialog open={isCreateRevisionOpen} onOpenChange={setIsCreateRevisionOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Revision
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Create Budget Revision</DialogTitle>
                <DialogDescription>
                  Make changes to the current budget and submit for approval
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <Label htmlFor="revision-reason">Revision Reason <span className="text-destructive">*</span></Label>
                  <Input
                    id="revision-reason"
                    placeholder="Enter reason for this revision"
                    value={revisionReason}
                    onChange={(e) => setRevisionReason(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="revision-note">Additional Notes</Label>
                  <Textarea
                    id="revision-note"
                    placeholder="Enter any additional notes or context"
                    value={revisionNote}
                    onChange={(e) => setRevisionNote(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="border rounded-md p-4 space-y-4">
                  <h3 className="text-sm font-medium">Add Items to Revision</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger id="category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {budgetCategories.map(category => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name} ({category.type})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="item">Budget Item</Label>
                      <Select
                        value={selectedItem}
                        onValueChange={setSelectedItem}
                        disabled={!selectedCategory}
                      >
                        <SelectTrigger id="item">
                          <SelectValue placeholder="Select item" />
                        </SelectTrigger>
                        <SelectContent>
                          {selectedCategory && budgetCategories
                            .find(c => c.id === selectedCategory)?.items
                            .map(item => (
                              <SelectItem key={item.id} value={item.id}>
                                {item.name} ({formatCurrency(item.amount)})
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="new-amount">New Amount</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="new-amount"
                          type="number"
                          placeholder="Enter new amount"
                          value={newAmount}
                          onChange={(e) => setNewAmount(e.target.value)}
                          disabled={!selectedItem}
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={addItemToRevision}
                          disabled={!selectedCategory || !selectedItem || !newAmount}
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {revisionItems.length > 0 && (
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Category</TableHead>
                          <TableHead>Item</TableHead>
                          <TableHead>Current Amount</TableHead>
                          <TableHead>New Amount</TableHead>
                          <TableHead>Change</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {revisionItems.map(item => (
                          <TableRow key={item.itemId}>
                            <TableCell>
                              <Badge variant={item.categoryType === 'income' ? 'default' : 'secondary'}>
                                {item.categoryType}
                              </Badge>
                              <div className="font-medium">{item.categoryName}</div>
                            </TableCell>
                            <TableCell>{item.itemName}</TableCell>
                            <TableCell>{formatCurrency(item.oldAmount)}</TableCell>
                            <TableCell>{formatCurrency(item.newAmount)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                {item.difference > 0 ? (
                                  <ArrowUpRight className={`h-4 w-4 mr-1 ${item.categoryType === 'income' ? 'text-green-500' : 'text-red-500'}`} />
                                ) : (
                                  <ArrowDownRight className={`h-4 w-4 mr-1 ${item.categoryType === 'income' ? 'text-red-500' : 'text-green-500'}`} />
                                )}
                                {formatCurrency(Math.abs(item.difference))}
                                <span className="ml-1 text-xs text-muted-foreground">
                                  ({item.percentChange.toFixed(1)}%)
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeItemFromRevision(item.itemId)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}

                        {/* Summary row */}
                        <TableRow>
                          <TableCell colSpan={3} className="font-medium">
                            Total Impact
                          </TableCell>
                          <TableCell colSpan={3}>
                            <div className="flex items-center">
                              {calculateTotalImpact().totalDifference > 0 ? (
                                <ArrowUpRight className="h-4 w-4 mr-1 text-amber-500" />
                              ) : (
                                <ArrowDownRight className="h-4 w-4 mr-1 text-amber-500" />
                              )}
                              {formatCurrency(Math.abs(calculateTotalImpact().totalDifference))}
                              <span className="ml-1 text-xs text-muted-foreground">
                                ({calculateTotalImpact().totalPercentage.toFixed(1)}%)
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateRevisionOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={submitRevision}
                  disabled={isLoading || !revisionReason || revisionItems.length === 0}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Submit Revision
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="history" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="history">Revision History</TabsTrigger>
              <TabsTrigger value="compare">Compare Versions</TabsTrigger>
            </TabsList>

            <TabsContent value="history" className="space-y-4 pt-4">
              {revisionHistory.length > 0 ? (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Version</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Created By</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {revisionHistory.map(revision => (
                        <TableRow key={revision.id}>
                          <TableCell className="font-medium">{revision.version}</TableCell>
                          <TableCell>{formatDate(revision.createdAt)}</TableCell>
                          <TableCell>{revision.reason}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              <div>
                                <div>{revision.createdBy.name}</div>
                                <div className="text-xs text-muted-foreground">{revision.createdBy.role}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(revision.status)}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">
                              <FileText className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <EmptyState
                  icon={<History className="h-12 w-12 text-muted-foreground" />}
                  title="No Revisions Found"
                  description="This budget has not been revised yet"
                  action={
                    <Button onClick={() => setIsCreateRevisionOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create First Revision
                    </Button>
                  }
                />
              )}
            </TabsContent>

            <TabsContent value="compare" className="space-y-4 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Version Comparison</CardTitle>
                  <CardDescription>
                    Compare different versions of the budget
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[400px] flex items-center justify-center">
                  <div className="text-center">
                    <FileBarChart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium">Version Comparison</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select two versions to compare changes between them
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Select>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Base version" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="v1.0">v1.0 (Original)</SelectItem>
                          <SelectItem value="v1.1">v1.1</SelectItem>
                          <SelectItem value="v1.2">v1.2</SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="flex items-center justify-center">
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>

                      <Select>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Compare version" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="v1.1">v1.1</SelectItem>
                          <SelectItem value="v1.2">v1.2</SelectItem>
                          <SelectItem value="v1.3">v1.3 (Current)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button variant="outline">
                    <FileBarChart className="mr-2 h-4 w-4" />
                    Compare Versions
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

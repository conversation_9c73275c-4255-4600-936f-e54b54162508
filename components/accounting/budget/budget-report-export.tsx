"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import {
  Download,
  FileSpreadsheet,
  FileText,
  Printer,
  Mail,
  Calendar,
  Settings,
  Loader2
} from 'lucide-react';
import { File } from 'lucide-react';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';

interface BudgetReportExportProps {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
}

export function BudgetReportExport({
  budgetId,
  budgetName,
  fiscalYear
}: BudgetReportExportProps) {
  const [exportFormat, setExportFormat] = useState<string>('pdf');
  const [reportType, setReportType] = useState<string>('summary');
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [includeOptions, setIncludeOptions] = useState({
    charts: true,
    transactions: true,
    notes: true,
    comparisons: true
  });

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);

      // Validate date range
      if (reportType === 'custom' && (!dateRange.from || !dateRange.to)) {
        toast({
          title: 'Date Range Required',
          description: 'Please select both start and end dates for the custom report',
          variant: 'destructive',
        });
        setIsExporting(false);
        return;
      }

      // Prepare export parameters
      const exportParams = {
        budgetId,
        format: exportFormat,
        reportType,
        dateRange: reportType === 'custom' ? dateRange : undefined,
        includeOptions
      };

      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message
      toast({
        title: 'Report Generated',
        description: `Your ${reportType} report has been generated successfully`,
        variant: 'default',
      });

      // Simulate file download
      if (exportFormat === 'pdf' || exportFormat === 'excel' || exportFormat === 'csv') {
        const link = document.createElement('a');
        link.href = '#';
        link.download = `${budgetName.replace(/\s+/g, '-').toLowerCase()}-${reportType}-report.${exportFormat}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (exportFormat === 'print') {
        window.print();
      } else if (exportFormat === 'email') {
        toast({
          title: 'Email Sent',
          description: 'The report has been sent to your email address',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error generating your report. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Budget Report</CardTitle>
        <CardDescription>
          Generate and export budget reports in various formats
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">Report Type</h3>
            <RadioGroup
              defaultValue="summary"
              value={reportType}
              onValueChange={setReportType}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="summary" id="summary" />
                <Label htmlFor="summary">Summary Report</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="detailed" id="detailed" />
                <Label htmlFor="detailed">Detailed Report</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="variance" id="variance" />
                <Label htmlFor="variance">Variance Analysis</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom" />
                <Label htmlFor="custom">Custom Date Range</Label>
              </div>
            </RadioGroup>
          </div>

          {reportType === 'custom' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="from-date">From Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="from-date"
                      variant={"outline"}
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateRange.from ? (
                        format(dateRange.from, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={dateRange.from}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="to-date">To Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="to-date"
                      variant={"outline"}
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateRange.to ? (
                        format(dateRange.to, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={dateRange.to}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}

          <div>
            <h3 className="text-sm font-medium mb-2">Export Format</h3>
            <Tabs defaultValue="pdf" value={exportFormat} onValueChange={setExportFormat}>
              <TabsList className="grid grid-cols-5">
                <TabsTrigger value="pdf" className="flex items-center gap-1">
                  <File className="h-4 w-4" />
                  PDF
                </TabsTrigger>
                <TabsTrigger value="excel" className="flex items-center gap-1">
                  <FileSpreadsheet className="h-4 w-4" />
                  Excel
                </TabsTrigger>
                <TabsTrigger value="csv" className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  CSV
                </TabsTrigger>
                <TabsTrigger value="print" className="flex items-center gap-1">
                  <Printer className="h-4 w-4" />
                  Print
                </TabsTrigger>
                <TabsTrigger value="email" className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  Email
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {exportFormat === 'email' && (
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                defaultValue=""
              />
            </div>
          )}

          <div>
            <h3 className="text-sm font-medium mb-2">Include in Report</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="charts"
                  checked={includeOptions.charts}
                  onCheckedChange={(checked) =>
                    setIncludeOptions(prev => ({ ...prev, charts: checked === true }))
                  }
                />
                <Label htmlFor="charts">Charts & Visualizations</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="transactions"
                  checked={includeOptions.transactions}
                  onCheckedChange={(checked) =>
                    setIncludeOptions(prev => ({ ...prev, transactions: checked === true }))
                  }
                />
                <Label htmlFor="transactions">Transaction Details</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="notes"
                  checked={includeOptions.notes}
                  onCheckedChange={(checked) =>
                    setIncludeOptions(prev => ({ ...prev, notes: checked === true }))
                  }
                />
                <Label htmlFor="notes">Notes & Comments</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="comparisons"
                  checked={includeOptions.comparisons}
                  onCheckedChange={(checked) =>
                    setIncludeOptions(prev => ({ ...prev, comparisons: checked === true }))
                  }
                />
                <Label htmlFor="comparisons">Year-over-Year Comparisons</Label>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">
          <Settings className="mr-2 h-4 w-4" />
          Advanced Options
        </Button>
        <Button onClick={handleExport} disabled={isExporting}>
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Generate Report
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

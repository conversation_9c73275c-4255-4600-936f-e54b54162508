'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { RefreshCw, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface BudgetFundData {
  budgetFund: {
    _id: string;
    name: string;
    description: string;
    fiscalYear: string;
    status: string;
    autoGenerated: boolean;
    lastAutoUpdate: string;
    categories: any[];
  };
  summary: {
    projected: { income: number; expense: number; net: number };
    expected: { income: number; expense: number; net: number };
    actual: { income: number; expense: number; net: number };
  };
}

interface BudgetFundOverviewProps {
  fiscalYear?: string;
}

export function BudgetFundOverview({ fiscalYear = '2025-2026' }: BudgetFundOverviewProps) {
  const [budgetFundData, setBudgetFundData] = useState<BudgetFundData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const fetchBudgetFundData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/accounting/budget-fund?fiscalYear=${fiscalYear}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch budget fund data');
      }
      
      const data = await response.json();
      setBudgetFundData(data);
    } catch (error) {
      console.error('Error fetching budget fund data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load budget fund data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBudgetFundData();
  }, [fiscalYear]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getVarianceColor = (variance: number) => {
    if (variance > 0) return 'text-green-600';
    if (variance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <TrendingUp className="h-4 w-4" />;
    if (variance < 0) return <TrendingDown className="h-4 w-4" />;
    return <DollarSign className="h-4 w-4" />;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Fund Overview</CardTitle>
          <CardDescription>Loading budget fund data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!budgetFundData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Fund Overview</CardTitle>
          <CardDescription>No budget fund data available</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Unable to load budget fund data for {fiscalYear}</p>
        </CardContent>
      </Card>
    );
  }

  const { budgetFund, summary } = budgetFundData;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Budget Fund Overview
              {budgetFund.autoGenerated && (
                <Badge variant="secondary">Auto-Generated</Badge>
              )}
            </CardTitle>
            <CardDescription>
              {budgetFund.name} • {budgetFund.fiscalYear}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchBudgetFundData}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="projected">Projected</TabsTrigger>
              <TabsTrigger value="expected">Expected</TabsTrigger>
              <TabsTrigger value="actual">Actual</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Projected Budget</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(summary.projected.net)}</div>
                    <div className="text-xs text-muted-foreground">
                      Income: {formatCurrency(summary.projected.income)} | 
                      Expenses: {formatCurrency(summary.projected.expense)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Expected Budget</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(summary.expected.net)}</div>
                    <div className="text-xs text-muted-foreground">
                      Income: {formatCurrency(summary.expected.income)} | 
                      Expenses: {formatCurrency(summary.expected.expense)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Actual Budget</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(summary.actual.net)}</div>
                    <div className="text-xs text-muted-foreground">
                      Income: {formatCurrency(summary.actual.income)} | 
                      Expenses: {formatCurrency(summary.actual.expense)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Budget Realization Progress</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Income Realization</span>
                      <span>
                        {summary.expected.income > 0 
                          ? ((summary.actual.income / summary.expected.income) * 100).toFixed(1)
                          : 0}%
                      </span>
                    </div>
                    <Progress 
                      value={summary.expected.income > 0 ? (summary.actual.income / summary.expected.income) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Expense Utilization</span>
                      <span>
                        {summary.expected.expense > 0 
                          ? ((summary.actual.expense / summary.expected.expense) * 100).toFixed(1)
                          : 0}%
                      </span>
                    </div>
                    <Progress 
                      value={summary.expected.expense > 0 ? (summary.actual.expense / summary.expected.expense) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="projected" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Projected Income</CardTitle>
                    <CardDescription>From draft income records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-green-600">
                      {formatCurrency(summary.projected.income)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Projected Expenses</CardTitle>
                    <CardDescription>From draft expense records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-red-600">
                      {formatCurrency(summary.projected.expense)}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="expected" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Expected Income</CardTitle>
                    <CardDescription>From approved income records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-green-600">
                      {formatCurrency(summary.expected.income)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Expected Expenses</CardTitle>
                    <CardDescription>From approved expense records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-red-600">
                      {formatCurrency(summary.expected.expense)}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="actual" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Actual Income</CardTitle>
                    <CardDescription>From received income records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-green-600">
                      {formatCurrency(summary.actual.income)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Actual Expenses</CardTitle>
                    <CardDescription>From paid expense records</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-red-600">
                      {formatCurrency(summary.actual.expense)}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

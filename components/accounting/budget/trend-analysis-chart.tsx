'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Download,
  Calendar
} from 'lucide-react'

interface TrendAnalysis {
  period: string
  budgeted: number
  actual: number
  variance: number
  cumulativeBudgeted: number
  cumulativeActual: number
  cumulativeVariance: number
}

interface TrendAnalysisChartProps {
  data: TrendAnalysis[]
  periodType: 'monthly' | 'quarterly'
  onPeriodChange: (period: 'monthly' | 'quarterly') => void
  isLoading?: boolean
  budgetName?: string
}

// Helper function to format currency
const formatMWK = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Helper function to format compact currency
const formatCompactMWK = (value: number) => {
  if (Math.abs(value) >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`
  }
  if (Math.abs(value) >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`
  }
  return formatMWK(value)
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 border rounded-lg shadow-lg">
        <p className="font-semibold mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="capitalize">{entry.dataKey}:</span>
            <span className="font-semibold">{formatMWK(entry.value)}</span>
          </div>
        ))}
      </div>
    )
  }
  return null
}

export function TrendAnalysisChart({ 
  data, 
  periodType, 
  onPeriodChange, 
  isLoading = false,
  budgetName 
}: TrendAnalysisChartProps) {
  const [chartType, setChartType] = useState<'line' | 'area' | 'cumulative'>('line')
  const [showVariance, setShowVariance] = useState(true)

  // Calculate trend indicators
  const getTrendIndicator = (data: TrendAnalysis[]) => {
    if (data.length < 2) return { trend: 'neutral', percentage: 0 }
    
    const firstPeriod = data[0]
    const lastPeriod = data[data.length - 1]
    const change = lastPeriod.actual - firstPeriod.actual
    const percentage = firstPeriod.actual !== 0 ? (change / firstPeriod.actual) * 100 : 0
    
    return {
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      percentage: Math.abs(percentage)
    }
  }

  const trendIndicator = getTrendIndicator(data)

  // Calculate summary statistics
  const totalBudgeted = data.reduce((sum, item) => sum + item.budgeted, 0)
  const totalActual = data.reduce((sum, item) => sum + item.actual, 0)
  const totalVariance = totalActual - totalBudgeted
  const variancePercentage = totalBudgeted !== 0 ? (totalVariance / totalBudgeted) * 100 : 0

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-200 rounded animate-pulse" />
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Trend Analysis</CardTitle>
          <CardDescription>No trend data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No data available for the selected period
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Budget Trend Analysis
              {trendIndicator.trend === 'up' && <TrendingUp className="h-5 w-5 text-green-600" />}
              {trendIndicator.trend === 'down' && <TrendingDown className="h-5 w-5 text-red-600" />}
            </CardTitle>
            <CardDescription>
              {budgetName && `${budgetName} - `}
              {periodType === 'monthly' ? 'Monthly' : 'Quarterly'} performance over time
              {trendIndicator.trend !== 'neutral' && (
                <span className={`ml-2 ${trendIndicator.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  ({trendIndicator.percentage.toFixed(1)}% {trendIndicator.trend === 'up' ? 'increase' : 'decrease'})
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={chartType} onValueChange={(value: 'line' | 'area' | 'cumulative') => setChartType(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Line Chart</SelectItem>
                <SelectItem value="area">Area Chart</SelectItem>
                <SelectItem value="cumulative">Cumulative</SelectItem>
              </SelectContent>
            </Select>
            <Select value={periodType} onValueChange={onPeriodChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-muted/20 p-3 rounded-lg">
            <div className="text-sm text-muted-foreground">Total Budgeted</div>
            <div className="text-lg font-semibold">{formatCompactMWK(totalBudgeted)}</div>
          </div>
          <div className="bg-muted/20 p-3 rounded-lg">
            <div className="text-sm text-muted-foreground">Total Actual</div>
            <div className="text-lg font-semibold">{formatCompactMWK(totalActual)}</div>
          </div>
          <div className="bg-muted/20 p-3 rounded-lg">
            <div className="text-sm text-muted-foreground">Total Variance</div>
            <div className={`text-lg font-semibold ${totalVariance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCompactMWK(totalVariance)}
            </div>
          </div>
          <div className="bg-muted/20 p-3 rounded-lg">
            <div className="text-sm text-muted-foreground">Variance %</div>
            <div className="flex items-center gap-2">
              <span className={`text-lg font-semibold ${Math.abs(variancePercentage) <= 5 ? 'text-green-600' : Math.abs(variancePercentage) <= 15 ? 'text-yellow-600' : 'text-red-600'}`}>
                {variancePercentage.toFixed(1)}%
              </span>
              <Badge variant={Math.abs(variancePercentage) <= 5 ? "default" : "destructive"}>
                {Math.abs(variancePercentage) <= 5 ? 'Good' : Math.abs(variancePercentage) <= 15 ? 'Fair' : 'Poor'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Chart */}
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'cumulative' ? (
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="period" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tickFormatter={formatCompactMWK} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="cumulativeBudgeted" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="Cumulative Budgeted" 
                />
                <Line 
                  type="monotone" 
                  dataKey="cumulativeActual" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                  name="Cumulative Actual" 
                />
                {showVariance && (
                  <Line 
                    type="monotone" 
                    dataKey="cumulativeVariance" 
                    stroke="#ff7300" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Cumulative Variance" 
                  />
                )}
                <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              </LineChart>
            ) : chartType === 'area' ? (
              <AreaChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="period" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tickFormatter={formatCompactMWK} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="budgeted" 
                  stackId="1"
                  stroke="#8884d8" 
                  fill="#8884d8"
                  fillOpacity={0.3}
                  name="Budgeted" 
                />
                <Area 
                  type="monotone" 
                  dataKey="actual" 
                  stackId="2"
                  stroke="#82ca9d" 
                  fill="#82ca9d"
                  fillOpacity={0.3}
                  name="Actual" 
                />
              </AreaChart>
            ) : (
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="period" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tickFormatter={formatCompactMWK} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="budgeted" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="Budgeted" 
                />
                <Line 
                  type="monotone" 
                  dataKey="actual" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                  name="Actual" 
                />
                {showVariance && (
                  <Line 
                    type="monotone" 
                    dataKey="variance" 
                    stroke="#ff7300" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Variance" 
                  />
                )}
                <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              </LineChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Chart Controls */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t">
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showVariance}
                onChange={(e) => setShowVariance(e.target.checked)}
                className="rounded"
              />
              Show Variance
            </label>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{data.length} {periodType === 'monthly' ? 'months' : 'quarters'} of data</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

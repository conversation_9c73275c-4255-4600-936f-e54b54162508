'use client';

import React, { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { RefreshCw, TrendingUp, TrendingDown, DollarSign, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface BudgetIntegrationData {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  totalBudgetedIncome: number;
  totalBudgetedExpense: number;
  totalActualIncome: number;
  totalActualExpense: number;
  incomeCategories: Array<{
    id: string;
    name: string;
    budgeted: number;
    actual: number;
    variance: number;
    percentage: number;
  }>;
  expenseCategories: Array<{
    id: string;
    name: string;
    budgeted: number;
    actual: number;
    variance: number;
    percentage: number;
  }>;
  lastUpdated: Date;
}

interface RealTimeBudgetIntegrationProps {
  budgetId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
}

export function RealTimeBudgetIntegration({
  budgetId,
  autoRefresh = true,
  refreshInterval = 30
}: RealTimeBudgetIntegrationProps) {
  const [data, setData] = useState<BudgetIntegrationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const { toast } = useToast();

  // Fetch budget integration data
  const fetchBudgetData = async (showLoading = true) => {
    if (!budgetId) return;

    try {
      if (showLoading) setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/accounting/budget/${budgetId}/integration-data`);

      if (!response.ok) {
        throw new Error(`Failed to fetch budget data: ${response.status}`);
      }

      const result = await response.json();
      setData(result);
      setLastRefresh(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch budget data';
      setError(errorMessage);
      console.error('Error fetching budget integration data:', err);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  };

  // Manual refresh
  const handleRefresh = () => {
    fetchBudgetData(true);
    toast({
      title: "Refreshing",
      description: "Updating budget data with latest transactions...",
    });
  };

  // Auto refresh effect
  useEffect(() => {
    if (!budgetId || !autoRefresh) return;

    fetchBudgetData(true);

    const interval = setInterval(() => {
      fetchBudgetData(false); // Silent refresh
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [budgetId, autoRefresh, refreshInterval]);

  // Format currency
  const formatMWK = (value: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Calculate variance color
  const getVarianceColor = (variance: number, isIncome: boolean) => {
    if (isIncome) {
      return variance >= 0 ? 'text-green-600' : 'text-red-600';
    } else {
      return variance <= 0 ? 'text-green-600' : 'text-red-600';
    }
  };

  // Calculate utilization percentage
  const getUtilizationPercentage = (actual: number, budgeted: number) => {
    if (budgeted === 0) return 0;
    return Math.min((actual / budgeted) * 100, 100);
  };

  if (!budgetId) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Select a budget to view real-time integration data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading && !data) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading budget integration data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No budget data available</p>
            <Button onClick={handleRefresh} variant="outline" className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Load Data
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const incomeVariance = data.totalActualIncome - data.totalBudgetedIncome;
  const expenseVariance = data.totalActualExpense - data.totalBudgetedExpense;
  const netPosition = data.totalActualIncome - data.totalActualExpense;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Real-Time Budget Integration
              <Badge variant="outline" className="text-xs">
                Live Data
              </Badge>
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {data.budgetName} • {data.fiscalYear}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {lastRefresh && (
              <span className="text-xs text-muted-foreground">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={handleRefresh} variant="outline" size="sm" disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatMWK(data.totalActualIncome)}</div>
            <p className="text-xs text-muted-foreground">
              Budget: {formatMWK(data.totalBudgetedIncome)}
            </p>
            <p className={`text-xs ${getVarianceColor(incomeVariance, true)}`}>
              {incomeVariance >= 0 ? '+' : ''}{formatMWK(incomeVariance)} variance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatMWK(data.totalActualExpense)}</div>
            <p className="text-xs text-muted-foreground">
              Budget: {formatMWK(data.totalBudgetedExpense)}
            </p>
            <p className={`text-xs ${getVarianceColor(expenseVariance, false)}`}>
              {expenseVariance >= 0 ? '+' : ''}{formatMWK(expenseVariance)} variance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Position</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${netPosition >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatMWK(netPosition)}
            </div>
            <p className="text-xs text-muted-foreground">
              Income - Expenses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
            <Badge variant="outline">
              {Math.round(getUtilizationPercentage(data.totalActualExpense, data.totalBudgetedExpense))}%
            </Badge>
          </CardHeader>
          <CardContent>
            <Progress 
              value={getUtilizationPercentage(data.totalActualExpense, data.totalBudgetedExpense)} 
              className="w-full"
            />
            <p className="text-xs text-muted-foreground mt-2">
              Expense budget utilization
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Income Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            Income Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.incomeCategories.map((category) => (
              <div key={category.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{category.name}</span>
                  <div className="text-right">
                    <div className="font-medium">{formatMWK(category.actual)}</div>
                    <div className="text-xs text-muted-foreground">
                      of {formatMWK(category.budgeted)}
                    </div>
                  </div>
                </div>
                <Progress value={category.percentage} className="w-full" />
                <div className="flex justify-between text-xs">
                  <span className={getVarianceColor(category.variance, true)}>
                    {category.variance >= 0 ? '+' : ''}{formatMWK(category.variance)} variance
                  </span>
                  <span className="text-muted-foreground">
                    {Math.round(category.percentage)}% achieved
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Expense Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-red-600" />
            Expense Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.expenseCategories.map((category) => (
              <div key={category.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{category.name}</span>
                  <div className="text-right">
                    <div className="font-medium">{formatMWK(category.actual)}</div>
                    <div className="text-xs text-muted-foreground">
                      of {formatMWK(category.budgeted)}
                    </div>
                  </div>
                </div>
                <Progress value={category.percentage} className="w-full" />
                <div className="flex justify-between text-xs">
                  <span className={getVarianceColor(category.variance, false)}>
                    {category.variance >= 0 ? '+' : ''}{formatMWK(category.variance)} variance
                  </span>
                  <span className="text-muted-foreground">
                    {Math.round(category.percentage)}% utilized
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

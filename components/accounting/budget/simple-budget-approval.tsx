// components/accounting/budget/simple-budget-approval.tsx
'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  AlertCircle, 
  Send,
  MessageSquare,
  User,
  Calendar
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { useBudgetStore } from '@/lib/stores/budget-store'

interface SimpleBudgetApprovalProps {
  budgetId?: string;
}

export function SimpleBudgetApproval({ budgetId }: SimpleBudgetApprovalProps) {
  const { toast } = useToast();
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { 
    currentBudget, 
    budgets, 
    updateBudgetStatus, 
    fetchBudgets,
    fetchBudgetById 
  } = useBudgetStore();

  // Get the budget to work with
  const budget = budgetId ? budgets.find(b => b.id === budgetId) : currentBudget;

  if (!budget) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Approval</CardTitle>
          <CardDescription>
            Select a budget to view its approval status and take actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No budget selected. Please select a budget from the Budget Planning tab.
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get status badge variant and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'draft':
        return {
          variant: 'secondary' as const,
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'Draft'
        };
      case 'pending_approval':
        return {
          variant: 'default' as const,
          icon: <Clock className="h-4 w-4" />,
          label: 'Pending Approval'
        };
      case 'approved':
        return {
          variant: 'default' as const,
          icon: <CheckCircle2 className="h-4 w-4" />,
          label: 'Approved'
        };
      case 'rejected':
        return {
          variant: 'destructive' as const,
          icon: <XCircle className="h-4 w-4" />,
          label: 'Rejected'
        };
      case 'active':
        return {
          variant: 'default' as const,
          icon: <CheckCircle2 className="h-4 w-4" />,
          label: 'Active'
        };
      default:
        return {
          variant: 'secondary' as const,
          icon: <AlertCircle className="h-4 w-4" />,
          label: status
        };
    }
  };

  const statusDisplay = getStatusDisplay(budget.status);

  // Handle approval actions
  const handleApprove = async () => {
    setIsSubmitting(true);
    try {
      await updateBudgetStatus(budget.id, 'approve');
      toast({
        title: 'Budget Approved',
        description: 'The budget has been approved successfully.',
      });
      // Refresh data
      await fetchBudgets();
      if (budgetId) {
        await fetchBudgetById(budgetId);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to approve budget',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast({
        title: 'Rejection Reason Required',
        description: 'Please provide a reason for rejecting this budget.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await updateBudgetStatus(budget.id, 'reject', rejectionReason);
      toast({
        title: 'Budget Rejected',
        description: 'The budget has been rejected.',
      });
      setIsRejectModalOpen(false);
      setRejectionReason('');
      // Refresh data
      await fetchBudgets();
      if (budgetId) {
        await fetchBudgetById(budgetId);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reject budget',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitForApproval = async () => {
    setIsSubmitting(true);
    try {
      await updateBudgetStatus(budget.id, 'submit');
      toast({
        title: 'Budget Submitted',
        description: 'The budget has been submitted for approval.',
      });
      // Refresh data
      await fetchBudgets();
      if (budgetId) {
        await fetchBudgetById(budgetId);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to submit budget',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleActivate = async () => {
    setIsSubmitting(true);
    try {
      await updateBudgetStatus(budget.id, 'activate');
      toast({
        title: 'Budget Activated',
        description: 'The budget is now active and can be used for transactions.',
      });
      // Refresh data
      await fetchBudgets();
      if (budgetId) {
        await fetchBudgetById(budgetId);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to activate budget',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Budget Approval
            <Badge variant={statusDisplay.variant} className="flex items-center gap-1">
              {statusDisplay.icon}
              {statusDisplay.label}
            </Badge>
          </CardTitle>
          <CardDescription>
            Manage the approval workflow for budget: {budget.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Budget Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Budget Name</Label>
              <div className="p-2 border rounded-md bg-muted/20">{budget.name}</div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Fiscal Year</Label>
              <div className="p-2 border rounded-md bg-muted/20">{budget.fiscalYear}</div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Created Date</Label>
              <div className="p-2 border rounded-md bg-muted/20 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {new Date(budget.createdAt).toLocaleDateString()}
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Total Budget</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {new Intl.NumberFormat('en-MW', {
                  style: 'currency',
                  currency: 'MWK',
                }).format(budget.totalBudgeted || (budget.totalIncome + budget.totalExpense))}
              </div>
            </div>
          </div>

          {/* Approval Information */}
          {budget.approvedBy && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Approval Information</Label>
              <div className="p-3 border rounded-md bg-green-50 dark:bg-green-950/20">
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <User className="h-4 w-4" />
                  <span className="text-sm">
                    Approved by: {
                      typeof budget.approvedBy === 'object' && budget.approvedBy.firstName && budget.approvedBy.lastName
                        ? `${budget.approvedBy.firstName} ${budget.approvedBy.lastName}`
                        : budget.approvedBy
                    } on {new Date(budget.approvedAt!).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            {budget.status === 'draft' && (
              <Button 
                onClick={handleSubmitForApproval}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <Send className="h-4 w-4" />
                Submit for Approval
              </Button>
            )}

            {budget.status === 'pending_approval' && (
              <>
                <Button 
                  onClick={handleApprove}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <CheckCircle2 className="h-4 w-4" />
                  Approve Budget
                </Button>
                <Button 
                  variant="destructive"
                  onClick={() => setIsRejectModalOpen(true)}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <XCircle className="h-4 w-4" />
                  Reject Budget
                </Button>
              </>
            )}

            {budget.status === 'approved' && (
              <Button 
                onClick={handleActivate}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <CheckCircle2 className="h-4 w-4" />
                Activate Budget
              </Button>
            )}

            {budget.status === 'rejected' && (
              <div className="p-3 border rounded-md bg-red-50 dark:bg-red-950/20">
                <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
                  <MessageSquare className="h-4 w-4" />
                  <span className="text-sm">
                    This budget was rejected. Please review and resubmit.
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Rejection Modal */}
      <Dialog open={isRejectModalOpen} onOpenChange={setIsRejectModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Reject Budget</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this budget. This will help the budget creator understand what needs to be changed.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="rejection-reason">Rejection Reason</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Enter the reason for rejecting this budget..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectModalOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isSubmitting || !rejectionReason.trim()}
            >
              {isSubmitting && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />}
              Reject Budget
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

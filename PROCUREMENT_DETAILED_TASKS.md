# Procurement Module - Detailed Implementation Tasks

## Task 1: Create Missing Backend Models

### 1.1 Contract Model Implementation
**File**: `models/procurement/Contract.ts`
**Priority**: High
**Estimated Time**: 2-3 hours

```typescript
import mongoose, { Document, Schema } from 'mongoose';

export interface IContract extends Document {
  contractNumber: string;
  supplierId: mongoose.Types.ObjectId;
  title: string;
  description: string;
  contractType: 'service' | 'supply' | 'maintenance' | 'lease' | 'consulting';
  value: number;
  currency: string;
  startDate: Date;
  endDate: Date;
  renewalDate?: Date;
  autoRenewal: boolean;
  status: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed' | 'suspended';
  terms: string[];
  paymentTerms: string;
  deliveryTerms?: string;
  penaltyClause?: string;
  performanceMetrics?: Array<{
    metric: string;
    target: string;
    measurement: string;
  }>;
  attachments: string[];
  createdBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  reviewDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

**Dependencies**: 
- Supplier model (already exists)
- User model (already exists)

### 1.2 Delivery Model Implementation
**File**: `models/procurement/Delivery.ts`
**Priority**: High
**Estimated Time**: 2-3 hours

```typescript
import mongoose, { Document, Schema } from 'mongoose';

export interface IDeliveryItem {
  purchaseOrderItemId: mongoose.Types.ObjectId;
  itemName: string;
  quantityOrdered: number;
  quantityDelivered: number;
  quantityAccepted: number;
  quantityRejected: number;
  rejectionReason?: string;
  condition: 'good' | 'damaged' | 'defective' | 'incomplete';
  notes?: string;
}

export interface IDelivery extends Document {
  deliveryNumber: string;
  purchaseOrderId: mongoose.Types.ObjectId;
  supplierId: mongoose.Types.ObjectId;
  expectedDate: Date;
  actualDate?: Date;
  status: 'pending' | 'in_transit' | 'delivered' | 'partially_delivered' | 'delayed' | 'cancelled';
  trackingNumber?: string;
  carrier?: string;
  items: IDeliveryItem[];
  receivedBy?: mongoose.Types.ObjectId;
  inspectedBy?: mongoose.Types.ObjectId;
  inspectionDate?: Date;
  inspectionNotes?: string;
  deliveryAddress: string;
  contactPerson: string;
  contactPhone: string;
  attachments?: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

**Dependencies**:
- PurchaseOrder model (already exists)
- Supplier model (already exists)
- User model (already exists)

### 1.3 Procurement Category Model Implementation
**File**: `models/procurement/ProcurementCategory.ts`
**Priority**: Medium
**Estimated Time**: 1-2 hours

```typescript
import mongoose, { Document, Schema } from 'mongoose';

export interface IProcurementCategory extends Document {
  name: string;
  code: string;
  description?: string;
  parentCategory?: mongoose.Types.ObjectId;
  budgetCategory?: mongoose.Types.ObjectId; // Link to accounting budget
  isActive: boolean;
  approvalLimit?: number;
  requiredApprovers: mongoose.Types.ObjectId[];
  defaultSuppliers?: mongoose.Types.ObjectId[];
  leadTime?: number; // in days
  riskLevel: 'low' | 'medium' | 'high';
  complianceRequirements?: string[];
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

**Dependencies**:
- Budget model (from accounting module)
- User model (already exists)
- Supplier model (already exists)

## Task 2: Create Backend Services

### 2.1 Contract Service Implementation
**File**: `lib/backend/services/procurement/ContractService.ts`
**Priority**: High
**Estimated Time**: 3-4 hours

**Key Methods**:
- `createContract(contractData: IContract): Promise<IContract>`
- `updateContract(id: string, updates: Partial<IContract>): Promise<IContract>`
- `renewContract(id: string, renewalData: ContractRenewalData): Promise<IContract>`
- `terminateContract(id: string, reason: string): Promise<IContract>`
- `getExpiringContracts(days: number): Promise<IContract[]>`
- `getContractsBySupplier(supplierId: string): Promise<IContract[]>`
- `validateContractCompliance(contractId: string): Promise<ComplianceReport>`

### 2.2 Delivery Service Implementation
**File**: `lib/backend/services/procurement/DeliveryService.ts`
**Priority**: High
**Estimated Time**: 3-4 hours

**Key Methods**:
- `createDelivery(deliveryData: IDelivery): Promise<IDelivery>`
- `updateDeliveryStatus(id: string, status: string): Promise<IDelivery>`
- `recordGoodsReceipt(id: string, receiptData: GoodsReceiptData): Promise<IDelivery>`
- `getDeliveriesByPO(purchaseOrderId: string): Promise<IDelivery[]>`
- `getOverdueDeliveries(): Promise<IDelivery[]>`
- `generateDeliveryReport(filters: DeliveryFilters): Promise<DeliveryReport>`

### 2.3 Procurement Category Service Implementation
**File**: `lib/backend/services/procurement/ProcurementCategoryService.ts`
**Priority**: Medium
**Estimated Time**: 2-3 hours

**Key Methods**:
- `createCategory(categoryData: IProcurementCategory): Promise<IProcurementCategory>`
- `updateCategory(id: string, updates: Partial<IProcurementCategory>): Promise<IProcurementCategory>`
- `getCategoryHierarchy(): Promise<CategoryHierarchy[]>`
- `getCategoriesByBudget(budgetId: string): Promise<IProcurementCategory[]>`
- `validateApprovalLimits(categoryId: string, amount: number): Promise<ApprovalValidation>`

## Task 3: Create API Routes

### 3.1 Contract API Routes
**Files**: 
- `app/api/procurement/contracts/route.ts`
- `app/api/procurement/contracts/[id]/route.ts`
- `app/api/procurement/contracts/[id]/renew/route.ts`
- `app/api/procurement/contracts/[id]/terminate/route.ts`

**Priority**: High
**Estimated Time**: 4-5 hours

**Endpoints**:
- `GET /api/procurement/contracts` - List contracts with filters
- `POST /api/procurement/contracts` - Create new contract
- `GET /api/procurement/contracts/[id]` - Get contract details
- `PUT /api/procurement/contracts/[id]` - Update contract
- `DELETE /api/procurement/contracts/[id]` - Delete contract
- `POST /api/procurement/contracts/[id]/renew` - Renew contract
- `POST /api/procurement/contracts/[id]/terminate` - Terminate contract

### 3.2 Delivery API Routes
**Files**:
- `app/api/procurement/deliveries/route.ts`
- `app/api/procurement/deliveries/[id]/route.ts`
- `app/api/procurement/deliveries/[id]/receive/route.ts`
- `app/api/procurement/deliveries/tracking/route.ts`

**Priority**: High
**Estimated Time**: 4-5 hours

**Endpoints**:
- `GET /api/procurement/deliveries` - List deliveries with filters
- `POST /api/procurement/deliveries` - Create new delivery
- `GET /api/procurement/deliveries/[id]` - Get delivery details
- `PUT /api/procurement/deliveries/[id]` - Update delivery
- `POST /api/procurement/deliveries/[id]/receive` - Record goods receipt
- `GET /api/procurement/deliveries/tracking` - Track deliveries

### 3.3 Procurement Category API Routes
**Files**:
- `app/api/procurement/categories/route.ts`
- `app/api/procurement/categories/[id]/route.ts`
- `app/api/procurement/categories/hierarchy/route.ts`

**Priority**: Medium
**Estimated Time**: 3-4 hours

**Endpoints**:
- `GET /api/procurement/categories` - List categories
- `POST /api/procurement/categories` - Create category
- `GET /api/procurement/categories/[id]` - Get category details
- `PUT /api/procurement/categories/[id]` - Update category
- `GET /api/procurement/categories/hierarchy` - Get category hierarchy

## Task 4: Create Zustand Stores

### 4.1 Main Procurement Store
**File**: `lib/stores/procurement-store.ts`
**Priority**: High
**Estimated Time**: 4-6 hours

**State Structure**:
```typescript
interface ProcurementState {
  // Entities
  suppliers: Supplier[];
  requisitions: Requisition[];
  purchaseOrders: PurchaseOrder[];
  tenders: Tender[];
  contracts: Contract[];
  deliveries: Delivery[];
  categories: ProcurementCategory[];
  
  // Selected entities
  selectedSupplier: Supplier | null;
  selectedRequisition: Requisition | null;
  selectedPurchaseOrder: PurchaseOrder | null;
  selectedTender: Tender | null;
  selectedContract: Contract | null;
  selectedDelivery: Delivery | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  filters: ProcurementFilters;
  pagination: PaginationState;
  
  // Cache
  lastFetched: Record<string, number>;
  cacheDuration: number;
  
  // Actions for each entity...
}
```

### 4.2 Budget Integration Store
**File**: `lib/stores/procurement-budget-store.ts`
**Priority**: Medium
**Estimated Time**: 3-4 hours

**Integration Points**:
- Budget availability checking
- Budget reservation for requisitions
- Budget commitment for purchase orders
- Expenditure recording for deliveries
- Budget utilization tracking

## Task 5: Frontend Form Components

### 5.1 Contract Form Component
**File**: `components/procurement/forms/contract-form.tsx`
**Priority**: High
**Estimated Time**: 4-5 hours

**Features**:
- Multi-step form for contract creation
- Supplier selection with search
- Contract terms management
- File upload for attachments
- Validation and error handling

### 5.2 Delivery Form Component
**File**: `components/procurement/forms/delivery-form.tsx`
**Priority**: High
**Estimated Time**: 3-4 hours

**Features**:
- Delivery creation from purchase orders
- Item-by-item delivery tracking
- Goods receipt recording
- Quality inspection notes
- Status updates

### 5.3 Enhanced Requisition Form
**File**: `components/procurement/forms/requisition-form.tsx`
**Priority**: Medium
**Estimated Time**: 3-4 hours

**Enhancements**:
- Budget integration
- Category-based approval routing
- Supplier suggestions
- Cost estimation
- Approval workflow visualization

## Task 6: Integration with Accounting Module

### 6.1 Budget Integration Service
**File**: `lib/services/procurement/budget-integration-service.ts`
**Priority**: High
**Estimated Time**: 4-5 hours

**Key Functions**:
- `checkBudgetAvailability(categoryId: string, amount: number): Promise<boolean>`
- `reserveBudget(requisitionId: string, amount: number): Promise<void>`
- `commitBudget(purchaseOrderId: string, amount: number): Promise<void>`
- `recordExpenditure(deliveryId: string, amount: number): Promise<void>`
- `releaseBudget(requisitionId: string): Promise<void>`

### 6.2 Workflow Integration
**File**: `lib/services/procurement/workflow-service.ts`
**Priority**: Medium
**Estimated Time**: 3-4 hours

**Workflow Steps**:
1. Requisition creation with budget check
2. Multi-level approval based on amount and category
3. Purchase order creation with budget commitment
4. Delivery tracking and goods receipt
5. Expenditure recording and budget utilization

## Implementation Timeline

### Week 1: Core Models and Services
- Day 1-2: Contract and Delivery models
- Day 3-4: Contract and Delivery services
- Day 5: Procurement Category model and service

### Week 2: API Routes and Basic Integration
- Day 1-2: Contract API routes
- Day 2-3: Delivery API routes
- Day 4-5: Category API routes and basic testing

### Week 3: Frontend Components and Stores
- Day 1-2: Procurement Zustand store
- Day 3-4: Contract and Delivery forms
- Day 5: Enhanced requisition form

### Week 4: Accounting Integration and Testing
- Day 1-2: Budget integration service
- Day 3-4: Workflow integration
- Day 5: Comprehensive testing and bug fixes

This breakdown provides specific, manageable tasks that can be implemented incrementally while maintaining the existing functionality.

# Budget Integration Setup Guide

## Current Status
✅ **Fixed API Issue**: Removed problematic Expenditure model import from budget integration API
✅ **Income Data**: We have approved income records in the database
✅ **Real-time Component**: Added to budget planning page
❌ **Missing**: Budget and budget categories to link income records

## Steps to Complete Budget Integration

### 1. Create Budget via UI
1. Go to: http://localhost:3000/dashboard/accounting/budget/planning
2. Click "Create New Budget"
3. Fill in details:
   - **Name**: "Teachers Council Budget 2025-2026"
   - **Description**: "Annual budget for Teachers Council of Malawi"
   - **Fiscal Year**: "2025-2026"
   - **Start Date**: July 1, 2025
   - **End Date**: June 30, 2026
4. Click "Create Budget"

### 2. Add Budget Categories
After creating the budget, add these income categories:

#### Income Categories:
1. **Government Subvention**
   - Type: Income
   - Budgeted Amount: MWK 4,000,000,000
   - Description: "Government funding for operations"

2. **Donations**
   - Type: Income  
   - Budgeted Amount: MWK 15,000,000
   - Description: "Donor funding and contributions"

3. **Registration Fees**
   - Type: Income
   - Budgeted Amount: MWK 50,000,000
   - Description: "Teacher registration fees"

#### Expense Categories:
1. **Personnel Costs**
   - Type: Expense
   - Budgeted Amount: MWK 2,000,000,000
   - Description: "Salaries and benefits"

2. **Operations**
   - Type: Expense
   - Budgeted Amount: MWK 500,000,000
   - Description: "Operational expenses"

### 3. Update Budget Status
1. Change budget status from "Draft" to "Approved"
2. This will enable the real-time budget integration component

### 4. Link Income Records to Budget
Once the budget and categories are created, we need to update the existing income records to link to the correct budget and categories.

**Current Income Records:**
- Approved: MWK 3,696,358,900 (Government Subvention)
- Draft: MWK 10,000,000 (Donations)

**Database Update Needed:**
```javascript
// Update the approved income record
db.incomes.updateOne(
  { "_id": ObjectId("683ef4de68bae533c04c36cc") },
  { 
    $set: { 
      "budget": ObjectId("NEW_BUDGET_ID"),
      "budgetCategory": ObjectId("GOVERNMENT_SUBVENTION_CATEGORY_ID")
    }
  }
)

// Update the draft income record  
db.incomes.updateOne(
  { "_id": ObjectId("683f039168bae533c04c3bf9") },
  { 
    $set: { 
      "budget": ObjectId("NEW_BUDGET_ID"),
      "budgetCategory": ObjectId("DONATIONS_CATEGORY_ID")
    }
  }
)
```

### 5. Test Real-time Integration
After completing the above steps:

1. **Budget Planning Page**: Should show the real-time budget integration component
2. **Income Overview**: Should display correct totals
3. **Budget vs Actual**: Should show:
   - Total Budgeted Income: MWK 4,065,000,000
   - Total Actual Income: MWK 3,696,358,900 (from approved income)
   - Variance: MWK -368,641,100
   - Utilization: ~91%

### 6. Expected Results

#### Government Subvention Category:
- Budgeted: MWK 4,000,000,000
- Actual: MWK 3,696,358,900
- Variance: MWK -303,641,100
- Achievement: 92.4%

#### Donations Category:
- Budgeted: MWK 15,000,000
- Actual: MWK 0 (draft income not counted)
- Variance: MWK -15,000,000
- Achievement: 0%

#### Registration Fees Category:
- Budgeted: MWK 50,000,000
- Actual: MWK 0
- Variance: MWK -50,000,000
- Achievement: 0%

## API Endpoints Working
✅ `/api/accounting/budget/{id}/integration-data` - Now working without Expenditure model
✅ `/api/accounting/income?fiscalYear=2025-2026&status=approved,received` - Returns approved income
✅ `/api/accounting/budget` - Lists budgets
✅ `/api/accounting/budget/category` - Creates budget categories

## Next Steps
1. Create the budget and categories via UI
2. Note down the budget ID and category IDs
3. Update income records to link to correct budget/categories
4. Test the real-time integration component
5. Verify that approved income shows up in budget planning page

# Budget Setup Instructions

## Current Issue
The Budget Performance section shows zeros because:
1. **No Budget Created**: There's no budget in the system for fiscal year 2025-2026
2. **Income Records Not Linked**: The existing income records reference budget IDs that don't exist

## Database Analysis
From the income records we have:
```json
{
  "_id": "683ef4de68bae533c04c36cc",
  "amount": **********.0,
  "source": "government_subvention", 
  "status": "approved",
  "budget": "682751c3fd8336739137c051",
  "budgetCategory": "6839e7649fa674826bfd8572",
  "fiscalYear": "2025-2026"
}
```

The income record references:
- **Budget ID**: `682751c3fd8336739137c051` 
- **Budget Category ID**: `6839e7649fa674826bfd8572`

But these IDs likely don't exist in the current database.

## Solution Steps

### Step 1: Create Budget via UI
1. Go to: http://localhost:3000/dashboard/accounting/budget/planning
2. Look for "Create New Budget" button or similar
3. Create budget with these details:
   - **Name**: "Teachers Council Budget 2025-2026"
   - **Description**: "Annual budget for Teachers Council of Malawi"
   - **Fiscal Year**: "2025-2026" 
   - **Start Date**: "2025-07-01"
   - **End Date**: "2026-06-30"
   - **Status**: "approved" (or start with "draft" and approve later)

### Step 2: Create Budget Categories
After creating the budget, add these categories:

#### Income Categories:
1. **Government Subvention**
   - Type: Income
   - Budgeted Amount: MWK 4,000,000,000
   - Description: "Government funding for operations"

2. **Donations** 
   - Type: Income
   - Budgeted Amount: MWK 15,000,000
   - Description: "Donor funding and contributions"

#### Expense Categories:
1. **Personnel Costs**
   - Type: Expense  
   - Budgeted Amount: MWK 2,000,000,000
   - Description: "Salaries and benefits"

2. **Operations**
   - Type: Expense
   - Budgeted Amount: MWK 500,000,000
   - Description: "Operational expenses"

### Step 3: Update Income Records
Once the budget and categories are created, we need to update the existing income records to link to the correct budget and category IDs.

**Method 1: Through Income Edit UI**
1. Go to Income Overview page
2. Edit the approved income record
3. Select the correct budget and category
4. Save the changes

**Method 2: Direct Database Update (if needed)**
```javascript
// Get the new budget and category IDs from the UI
const newBudgetId = "NEW_BUDGET_ID_FROM_UI";
const govSubventionCategoryId = "NEW_CATEGORY_ID_FROM_UI";
const donationsCategoryId = "NEW_DONATIONS_CATEGORY_ID_FROM_UI";

// Update the approved income record
db.incomes.updateOne(
  { "_id": ObjectId("683ef4de68bae533c04c36cc") },
  { 
    $set: { 
      "budget": ObjectId(newBudgetId),
      "budgetCategory": ObjectId(govSubventionCategoryId)
    }
  }
);

// Update the draft income record
db.incomes.updateOne(
  { "_id": ObjectId("683f039168bae533c04c3bf9") },
  { 
    $set: { 
      "budget": ObjectId(newBudgetId),
      "budgetCategory": ObjectId(donationsCategoryId)
    }
  }
);
```

### Step 4: Test Integration
After completing the above steps:

1. **Refresh Budget Planning Page**: Should show the budget in the list
2. **Select the Budget**: Click on the budget to view details
3. **Check Budget Performance**: Should now show:
   - Total Budgeted: MWK 4,515,000,000 (sum of all categories)
   - Actual Income: MWK 3,696,358,900 (from approved income)
   - Actual Expenses: MWK 0 (no expenses yet)
   - Variance: MWK -818,641,100

4. **Check Real-time Integration**: Should also show the same data

### Step 5: Verify Data Flow
1. **Income Overview**: Should show MWK 3,696,358,900
2. **Budget Planning**: Should show same amount in "Actual Income"
3. **Real-time Integration**: Should show budget vs actual comparison
4. **Category Breakdown**: Government Subvention category should show 92.4% achievement

## Expected Results

### Government Subvention Category:
- **Budgeted**: MWK 4,000,000,000
- **Actual**: MWK 3,696,358,900  
- **Variance**: MWK -303,641,100
- **Achievement**: 92.4%

### Donations Category:
- **Budgeted**: MWK 15,000,000
- **Actual**: MWK 0 (draft income not counted)
- **Variance**: MWK -15,000,000
- **Achievement**: 0%

### Overall Budget:
- **Total Budgeted**: MWK 4,515,000,000
- **Total Actual Income**: MWK 3,696,358,900
- **Total Actual Expenses**: MWK 0
- **Net Variance**: MWK -818,641,100
- **Overall Achievement**: 81.9%

## Troubleshooting

If the Budget Performance still shows zeros after these steps:

1. **Check Console Logs**: Look for errors in browser console
2. **Verify Budget Selection**: Ensure a budget is selected in the planning page
3. **Check API Responses**: Use browser dev tools to check API calls
4. **Refresh Performance Data**: Click the "Refresh Performance Data" button
5. **Check Income Linking**: Verify income records have correct budget/category IDs

## Next Steps

Once this is working:
1. **Create Expense Records**: Add some expense records linked to expense categories
2. **Test Status Changes**: Change income status from approved to received
3. **Monitor Real-time Updates**: Verify that status changes immediately update budget calculations
4. **Add More Categories**: Create additional income/expense categories as needed

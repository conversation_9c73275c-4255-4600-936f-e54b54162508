const XLSX = require('xlsx');
const path = require('path');

// Create corrected deductions template data based on provided Excel
const deductionsData = [
  // Header row
  [
    'Name',
    'Code',
    'Description',
    'Is Active',
    'Is Statutory',
    'Is Fixed',
    'Default Amount',
    'Default Percentage',
    'Calculation Base',
    'Applicable Roles',
    'Applicable Departments'
  ],
  // Corrected data rows based on provided Excel
  [
    'Income Tax',
    'PAYE',
    'Statutory income tax deduction',
    'true',
    'true',
    'false',
    '',
    '',
    'taxable',
    'all',
    ''
  ],
  [
    'Pension Contribution',
    'PENSION',
    'Employee contribution to pension scheme',
    'true',
    'true',
    'false',
    '',
    '5',
    'basic',
    'all',
    ''
  ],
  [
    'Absenteeism Deduction',
    'ABSENT',
    'Deduction for unauthorized absences',
    'true',
    'false',
    'true',
    '5000',
    '',
    'basic',
    'all',
    ''
  ],
  [
    'Unpaid Leave Deduction',
    'UNPAID_LEAVE',
    'Deduction for unpaid leave days',
    'true',
    'false',
    'false',
    '',
    '100',
    'daily_rate',
    'all',
    ''
  ],
  [
    'Medical Aid Contribution',
    'MEDICAL',
    'Employee contribution to medical aid scheme for 3 extra family members',
    'true',
    'false',
    'true',
    '40000',
    '',
    'basic',
    'all',
    ''
  ],
  [
    'Emergency Advances',
    'EMERGENCY_ADVANCE',
    'Employee emergency advance deduction',
    'true',
    'false',
    'true',
    '10000',
    '',
    'basic',
    'all',
    ''
  ],
  [
    'Education Advances',
    'EDUCATION_ADVANCE',
    'Employee education advance deduction',
    'true',
    'false',
    'true',
    '15000',
    '',
    'basic',
    'all',
    ''
  ],
  [
    'General Purpose Advances',
    'GENERAL_ADVANCE',
    'Employee general purpose advance deduction',
    'true',
    'false',
    'true',
    '20000',
    '',
    'basic',
    'all',
    ''
  ],
  [
    'Salary Advance',
    'SALARY_ADVANCE',
    'Employee salary advance deduction',
    'true',
    'false',
    'false',
    '',
    '25',
    'basic',
    'all',
    ''
  ],
  [
    'House Ownership Loan',
    'HOUSE_LOAN',
    'Employee house ownership loan deduction',
    'true',
    'false',
    'true',
    '50000',
    '',
    'basic',
    'all',
    ''
  ]
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Create worksheet from the data
const ws = XLSX.utils.aoa_to_sheet(deductionsData);

// Set column widths for better readability
const colWidths = [
  { wch: 25 }, // Name
  { wch: 20 }, // Code
  { wch: 40 }, // Description
  { wch: 10 }, // Is Active
  { wch: 12 }, // Is Statutory
  { wch: 10 }, // Is Fixed
  { wch: 15 }, // Default Amount
  { wch: 18 }, // Default Percentage
  { wch: 15 }, // Calculation Base
  { wch: 15 }, // Applicable Roles
  { wch: 20 }  // Applicable Departments
];

ws['!cols'] = colWidths;

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Deductions Data');

// Create instructions sheet
const instructionsData = [
  ['Deductions Bulk Import Instructions - Corrected Data'],
  [''],
  ['Required Fields:'],
  ['• Name: Full name of the deduction'],
  ['• Code: Unique identifier for the deduction (no spaces, use underscores)'],
  [''],
  ['Optional Fields:'],
  ['• Description: Detailed description of the deduction'],
  ['• Is Active: true/false (defaults to true)'],
  ['• Is Statutory: true/false for government-mandated deductions (defaults to false)'],
  ['• Is Fixed: true for fixed amounts, false for percentage-based (defaults to true)'],
  ['• Default Amount: Fixed amount in MWK (for fixed deductions)'],
  ['• Default Percentage: Percentage value (for percentage-based deductions)'],
  ['• Calculation Base: What the percentage is calculated on (basic, taxable, gross)'],
  ['• Applicable Roles: Comma-separated list of roles, or "all" for all roles'],
  ['• Applicable Departments: Comma-separated list of departments, or leave empty for all'],
  [''],
  ['Data Corrections Made:'],
  ['• Fixed spelling errors (Absentisim → Absenteeism, Emmegency → Emergency, etc.)'],
  ['• Standardized codes (removed spaces, used underscores)'],
  ['• Added proper descriptions for all deductions'],
  ['• Set appropriate default values for missing fields'],
  ['• Configured calculation bases appropriately'],
  [''],
  ['Deduction Types Included:'],
  ['• PAYE: Statutory income tax (percentage-based on taxable income)'],
  ['• PENSION: Statutory pension contribution (5% of basic salary)'],
  ['• ABSENT: Absenteeism penalty (fixed amount)'],
  ['• UNPAID_LEAVE: Unpaid leave deduction (100% of daily rate)'],
  ['• MEDICAL: Medical aid contribution (fixed amount for 3 family members)'],
  ['• EMERGENCY_ADVANCE: Emergency advance repayment (fixed amount)'],
  ['• EDUCATION_ADVANCE: Education advance repayment (fixed amount)'],
  ['• GENERAL_ADVANCE: General purpose advance repayment (fixed amount)'],
  ['• SALARY_ADVANCE: Salary advance repayment (25% of basic salary)'],
  ['• HOUSE_LOAN: House ownership loan repayment (fixed amount)'],
  [''],
  ['Important Notes:'],
  ['• The system will now SKIP existing deductions instead of showing errors'],
  ['• You can safely re-import this file - existing items will be skipped'],
  ['• All amounts are in Malawian Kwacha (MWK)'],
  ['• Percentage values should be entered as numbers (e.g., 5 for 5%)'],
  ['• Use "true" or "false" for boolean fields'],
  ['• Codes must be unique across all deductions'],
  [''],
  ['Tips:'],
  ['• Test with a small batch first'],
  ['• Review the upload results for any skipped or error items'],
  ['• Existing deductions with the same code will be skipped (not duplicated)'],
  ['• Remove the header row if you only want to import specific deductions']
];

const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);

// Set column width for instructions
instructionsWs['!cols'] = [{ wch: 80 }];

// Add instructions sheet
XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions');

// Create the format_excel directory if it doesn't exist
const fs = require('fs');
const formatExcelDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(formatExcelDir)) {
  fs.mkdirSync(formatExcelDir, { recursive: true });
}

// Write the file
const filePath = path.join(formatExcelDir, 'deductions_corrected_data.xlsx');
XLSX.writeFile(wb, filePath);

console.log(`Corrected deductions Excel file created successfully at: ${filePath}`);
console.log('');
console.log('Corrections made to your data:');
console.log('1. Fixed spelling errors:');
console.log('   - Absentisim → Absenteeism');
console.log('   - Upaid leave → Unpaid Leave');
console.log('   - Emmegency → Emergency');
console.log('   - Educataion → Education');
console.log('');
console.log('2. Standardized codes:');
console.log('   - Removed spaces and special characters');
console.log('   - Used consistent naming (UPPER_CASE with underscores)');
console.log('');
console.log('3. Added missing data:');
console.log('   - Proper descriptions for all deductions');
console.log('   - Appropriate default amounts and percentages');
console.log('   - Calculation bases for percentage deductions');
console.log('   - Boolean values for all flag fields');
console.log('');
console.log('4. System improvements:');
console.log('   - Bulk import now SKIPS existing deductions instead of showing errors');
console.log('   - You can safely re-import without creating duplicates');
console.log('   - Better error handling and progress reporting');
console.log('');
console.log('The file is ready for import into the TCM Enterprise Suite!');

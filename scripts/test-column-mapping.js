/**
 * <PERSON><PERSON><PERSON> to test column mapping for Head of Department field
 */

// Simulate the Excel data structure
const excelRow = {
  'name': 'Management',
  'Department Code': 'MGMT',
  'Description': 'Executive management and strategic oversight',
  'Status': 'Active',
  'Head of Department': 'Registrar',
  'Location': 'Head Office',
  'Established Date': '2010-01-01',
  'Budget Allocation': '5000000',
  'Employee Count': '0',
  'Contact Email': '<EMAIL>',
  'Contact Phone': '+265-1-123-001'
};

// Simulate the column mapping from the bulk import route
const COLUMN_DISPLAY_MAPPING = {
  'name': 'name',
  'Name': 'name',
  'Description': 'description',
  'Budget': 'budget',
  'Budget Allocation': 'budget',
  'Department Code': 'departmentCode',
  'Status': 'status',
  'Head of Department': 'headTitle',
  'Location': 'location',
  'Established Date': 'establishedDate',
  'Employee Count': 'employeeCount',
  'Contact Email': 'contactEmail',
  'Contact Phone': 'contactPhone'
};

// Simulate the column mapping logic
const availableColumns = Object.keys(excelRow);
const columnMap = {};

console.log('🔍 Available columns in Excel:', availableColumns);
console.log('\n📋 Column Display Mapping:', COLUMN_DISPLAY_MAPPING);

// Try to map columns based on exact matches or display name mapping
for (const expectedField of Object.keys(COLUMN_DISPLAY_MAPPING)) {
  const mappedField = COLUMN_DISPLAY_MAPPING[expectedField];

  // Check if the expected field name exists in the file
  if (availableColumns.includes(expectedField)) {
    columnMap[mappedField] = expectedField;
  }
  // Check if the mapped field name exists in the file
  else if (availableColumns.includes(mappedField)) {
    columnMap[mappedField] = mappedField;
  }
}

console.log('\n🗺️ Generated Column Map:', columnMap);

// Simulate the row normalization
const normalizedRow = {};
for (const [targetField, sourceColumn] of Object.entries(columnMap)) {
  if (excelRow[sourceColumn] !== undefined && excelRow[sourceColumn] !== null && excelRow[sourceColumn] !== '') {
    normalizedRow[targetField] = excelRow[sourceColumn];
  }
}

console.log('\n✅ Normalized Row:', normalizedRow);

// Check specifically for headTitle
console.log('\n🎯 Head of Department Mapping:');
console.log('Excel column "Head of Department":', excelRow['Head of Department']);
console.log('Mapped to field:', columnMap['headTitle']);
console.log('Normalized value:', normalizedRow['headTitle']);

// Simulate the department data creation
const departmentData = {
  name: normalizedRow.name,
  description: normalizedRow.description || undefined,
  budget: normalizedRow.budget || undefined,
  departmentCode: normalizedRow.departmentCode || undefined,
  location: normalizedRow.location || undefined,
  establishedDate: normalizedRow.establishedDate ? new Date(normalizedRow.establishedDate) : undefined,
  contactEmail: normalizedRow.contactEmail || undefined,
  contactPhone: normalizedRow.contactPhone || undefined,
  status: normalizedRow.status ? normalizedRow.status.toLowerCase() : 'active',
  headTitle: normalizedRow.headTitle || undefined
};

console.log('\n📄 Final Department Data:', departmentData);

// Check if headTitle is included
if (departmentData.headTitle) {
  console.log('\n✅ SUCCESS: headTitle field is included in department data');
} else {
  console.log('\n❌ ERROR: headTitle field is missing from department data');
}

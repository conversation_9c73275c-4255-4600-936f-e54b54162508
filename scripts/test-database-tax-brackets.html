<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Tax Brackets Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Database Tax Brackets Test</h1>
    
    <div class="test-section">
        <h3>1. Check Tax Brackets in Database</h3>
        <button onclick="checkTaxBrackets()">Check Tax Brackets</button>
        <div id="tax-brackets-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Tax Calculation API</h3>
        <input type="number" id="test-salary" placeholder="Enter salary amount" value="3256128" style="width: 200px;">
        <button onclick="testTaxCalculation()">Test Tax Calculation</button>
        <div id="tax-calculation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Create Default Tax Brackets (if needed)</h3>
        <button onclick="createDefaultBrackets()">Create Default Tax Brackets</button>
        <div id="create-brackets-result" class="result"></div>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    credentials: 'include',
                    ...options
                });
                
                const data = await response.json();
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        async function checkTaxBrackets() {
            const resultDiv = document.getElementById('tax-brackets-result');
            resultDiv.innerHTML = 'Loading...';
            
            const result = await makeRequest('/api/payroll/tax-brackets?country=Malawi&currency=MWK&isActive=true');
            
            if (result.ok && result.data.success) {
                const brackets = result.data.data;
                resultDiv.className = 'result success';
                
                let html = `<h4>Found ${brackets.length} active tax bracket(s)</h4>`;
                
                if (brackets.length > 0) {
                    brackets.forEach((bracket, index) => {
                        html += `
                            <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <h5>Tax Bracket ${index + 1}</h5>
                                <p><strong>Country:</strong> ${bracket.country}</p>
                                <p><strong>Currency:</strong> ${bracket.currency}</p>
                                <p><strong>Effective Date:</strong> ${new Date(bracket.effectiveDate).toLocaleDateString()}</p>
                                <p><strong>Status:</strong> ${bracket.isActive ? 'Active' : 'Inactive'}</p>
                                
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Lower Limit</th>
                                            <th>Upper Limit</th>
                                            <th>Rate (%)</th>
                                            <th>Exempt</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${bracket.brackets.map(b => `
                                            <tr>
                                                <td>${b.lowerLimit.toLocaleString()}</td>
                                                <td>${b.upperLimit ? b.upperLimit.toLocaleString() : 'No limit'}</td>
                                                <td>${b.rate}%</td>
                                                <td>${b.isExempt ? 'Yes' : 'No'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        `;
                    });
                } else {
                    html += '<p style="color: orange;">No active tax brackets found. You may need to create default brackets.</p>';
                }
                
                resultDiv.innerHTML = html;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        async function testTaxCalculation() {
            const salary = document.getElementById('test-salary').value;
            const resultDiv = document.getElementById('tax-calculation-result');
            
            if (!salary) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please enter a salary amount';
                return;
            }
            
            resultDiv.innerHTML = 'Testing tax calculation...';
            
            // Test with a sample employee ID (you may need to adjust this)
            const testEmployeeId = '6832ada66922bcad1efb0e01'; // Use an actual employee ID from your system
            
            const result = await makeRequest('/api/payroll/calculate-salary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeId: testEmployeeId,
                    payPeriod: {
                        month: new Date().getMonth() + 1,
                        year: new Date().getFullYear()
                    }
                })
            });
            
            if (result.ok) {
                const data = result.data.success ? result.data.data : result.data;
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Tax Calculation Result</h4>
                    <p><strong>Gross Salary:</strong> MWK ${(data.grossSalary || 0).toLocaleString()}</p>
                    <p><strong>Total Tax:</strong> MWK ${(data.totalTax || 0).toLocaleString()}</p>
                    <p><strong>Total Deductions:</strong> MWK ${(data.totalDeductions || 0).toLocaleString()}</p>
                    <p><strong>Net Salary:</strong> MWK ${(data.netSalary || 0).toLocaleString()}</p>
                    
                    <h5>Components:</h5>
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Taxable</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${(data.components || []).map(comp => `
                                <tr>
                                    <td>${comp.name}</td>
                                    <td>${comp.type}</td>
                                    <td>MWK ${comp.amount.toLocaleString()}</td>
                                    <td>${comp.isTaxable ? 'Yes' : 'No'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    </details>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        async function createDefaultBrackets() {
            const resultDiv = document.getElementById('create-brackets-result');
            resultDiv.innerHTML = 'Creating default tax brackets...';
            
            const result = await makeRequest('/api/payroll/tax-brackets/default', {
                method: 'POST'
            });
            
            if (result.ok) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Success</h4>
                    <p>Default tax brackets created successfully!</p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
                
                // Automatically refresh tax brackets
                setTimeout(() => {
                    checkTaxBrackets();
                }, 1000);
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        // Auto-load tax brackets on page load
        window.onload = function() {
            checkTaxBrackets();
        };
    </script>
</body>
</html>

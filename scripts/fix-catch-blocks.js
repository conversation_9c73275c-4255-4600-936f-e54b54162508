const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  applyFixes: true
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Fix catch blocks with unknown type errors
function fixCatchBlocks(content) {
  // Pattern 1: catch (err) { ... err.message ... }
  let fixedContent = content.replace(
    /catch\s*\(\s*(\w+)(?!\s*:\s*\w+)\s*\)\s*{([^}]*?)(\1)\.message/g,
    (match, varName, beforeMessage, errorVar) => {
      if (errorVar !== varName) return match; // Not the same variable
      
      return `catch (${varName}: unknown) {${beforeMessage}${varName} instanceof Error ? ${varName}.message : 'An error occurred'`;
    }
  );
  
  // Pattern 2: catch (err: unknown) { ... err.message ... } without type check
  fixedContent = fixedContent.replace(
    /catch\s*\(\s*(\w+)\s*:\s*unknown\s*\)\s*{([^}]*?)(?<!\?\s*)(\1)\.message(?!\s*\?)/g,
    (match, varName, beforeMessage, errorVar) => {
      if (errorVar !== varName) return match; // Not the same variable
      if (beforeMessage.includes(`${varName} instanceof Error`)) return match; // Already has type check
      
      return `catch (${varName}: unknown) {${beforeMessage}${varName} instanceof Error ? ${varName}.message : 'An error occurred'`;
    }
  );
  
  // Pattern 3: Add error message variable for toast/setError
  fixedContent = fixedContent.replace(
    /catch\s*\(\s*(\w+)\s*:\s*unknown\s*\)\s*{([^}]*?)(?<!\?\s*)(\1)\.message(?!\s*\?)/g,
    (match, varName, beforeMessage, errorVar) => {
      if (errorVar !== varName) return match; // Not the same variable
      if (beforeMessage.includes(`${varName} instanceof Error`)) return match; // Already has type check
      if (beforeMessage.includes(`const errorMessage =`)) return match; // Already has error message variable
      
      // Add error message variable
      const lines = beforeMessage.split('\n');
      const indentation = lines[lines.length - 1].match(/^\s*/)[0];
      
      return `catch (${varName}: unknown) {${beforeMessage}${indentation}const errorMessage = ${varName} instanceof Error ? ${varName}.message : 'Unknown error';\n${indentation}`;
    }
  );
  
  // Pattern 4: Fix toast descriptions
  fixedContent = fixedContent.replace(
    /description:\s*(['"])([^'"]*?)(\1)(?!\s*\+)/g,
    (match, quote, message, endQuote) => {
      if (message.includes('${')) return match; // Already has template literal
      if (!message.includes('Failed to')) return match; // Not an error message
      
      return `description: ${quote}${message}: \${errorMessage}${endQuote}`;
    }
  );
  
  return fixedContent;
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  
  // Fix catch blocks
  const contentAfterFix = fixCatchBlocks(fixedContent);
  if (contentAfterFix !== fixedContent) {
    fixedContent = contentAfterFix;
    changesApplied = true;
    console.log(`Fixed catch blocks in ${filePath}`);
  }
  
  // Save changes if any were applied
  if (changesApplied && config.applyFixes) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
async function main() {
  console.log('Fixing catch blocks with unknown type errors...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed catch blocks in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
  }
}

// Run the script
main().catch(console.error);

/**
 * <PERSON><PERSON><PERSON> to test the Department model and verify schema fields
 * This script will help us understand why enhanced fields aren't being saved
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

async function testDepartmentModel() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Import the Department model
    const Department = require('../models/Department.ts').default;
    
    console.log('\n📋 Department Model Information:');
    console.log('Model name:', Department.modelName);
    console.log('Collection name:', Department.collection.name);
    
    // Get the schema
    const schema = Department.schema;
    console.log('\n🔍 Schema paths:');
    Object.keys(schema.paths).forEach(path => {
      const schemaType = schema.paths[path];
      console.log(`  ${path}: ${schemaType.instance || schemaType.constructor.name}`);
    });

    // Test creating a department with enhanced fields
    console.log('\n🧪 Testing department creation with enhanced fields...');
    
    const testDepartmentData = {
      name: `Test Department ${Date.now()}`,
      description: 'Test department for schema validation',
      departmentCode: 'TEST',
      location: 'Test Location',
      budget: 100000,
      contactEmail: '<EMAIL>',
      contactPhone: '+265-1-123-999',
      establishedDate: new Date('2024-01-01'),
      status: 'active'
    };

    console.log('📝 Creating department with data:', testDepartmentData);
    
    const createdDepartment = await Department.create(testDepartmentData);
    console.log('✅ Department created successfully!');
    console.log('📄 Created department:', JSON.stringify(createdDepartment.toObject(), null, 2));

    // Verify the department was saved with all fields
    const foundDepartment = await Department.findById(createdDepartment._id).lean();
    console.log('\n🔍 Department found in database:', JSON.stringify(foundDepartment, null, 2));

    // Check which fields are missing
    const expectedFields = ['name', 'description', 'departmentCode', 'location', 'budget', 'contactEmail', 'contactPhone', 'establishedDate', 'status'];
    const missingFields = expectedFields.filter(field => !(field in foundDepartment));
    
    if (missingFields.length > 0) {
      console.log('\n❌ Missing fields in database:', missingFields);
    } else {
      console.log('\n✅ All expected fields are present in the database!');
    }

    // Clean up - delete the test department
    await Department.findByIdAndDelete(createdDepartment._id);
    console.log('\n🧹 Test department deleted');

  } catch (error) {
    console.error('❌ Error testing department model:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testDepartmentModel();

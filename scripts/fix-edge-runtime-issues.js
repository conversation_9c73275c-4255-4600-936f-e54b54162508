#!/usr/bin/env node

/**
 * Fix Edge Runtime Issues Script
 * 
 * This script adds the "runtime = 'nodejs'" configuration to API route files
 * that use Node.js specific APIs like jsonwebtoken which are not supported in Edge Runtime.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all route.ts files in the app/api directory
function findApiRouteFiles() {
  try {
    const result = execSync('find app/api -name "route.ts" -type f', { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error finding API route files:', error);
    return [];
  }
}

// Check if a file contains imports that are not compatible with Edge Runtime
function fileNeedsFixing(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for imports that are not compatible with Edge Runtime
    const incompatibleImports = [
      'jsonwebtoken',
      'mongoose',
      'fs',
      'crypto',
      'process.nextTick',
      'process.version'
    ];
    
    return incompatibleImports.some(importName => content.includes(importName)) && 
           !content.includes('export const runtime = \'nodejs\'');
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error);
    return false;
  }
}

// Fix the file by adding the runtime configuration
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Create a backup of the original file
    const backupPath = `${filePath}.bak`;
    fs.writeFileSync(backupPath, content);
    
    // Add the runtime configuration at the beginning of the file
    const runtimeConfig = 'export const runtime = \'nodejs\';\n\n';
    
    // Check if the file already has imports
    if (content.includes('import ')) {
      // Find the last import statement
      const importLines = content.split('\n').filter(line => line.trim().startsWith('import '));
      const lastImportLine = importLines[importLines.length - 1];
      const lastImportIndex = content.indexOf(lastImportLine) + lastImportLine.length;
      
      // Insert the runtime configuration after the last import
      content = content.slice(0, lastImportIndex) + '\n\n' + runtimeConfig + content.slice(lastImportIndex);
    } else {
      // Add the runtime configuration at the beginning of the file
      content = runtimeConfig + content;
    }
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ Fixed ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error fixing file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('🔍 Finding API route files...');
  const files = findApiRouteFiles();
  console.log(`Found ${files.length} API route files.`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (fileNeedsFixing(file)) {
      console.log(`Fixing ${file}...`);
      if (fixFile(file)) {
        fixedCount++;
      }
    }
  }
  
  console.log(`\n✨ Done! Fixed ${fixedCount} files.`);
}

main();

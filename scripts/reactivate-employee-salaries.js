// Script to reactivate inactive salary records for employees
const { connectToDatabase } = require('../lib/backend/database');
const Employee = require('../models/Employee').default;
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;

async function reactivateEmployeeSalaries() {
  try {
    await connectToDatabase();
    
    console.log('🔧 REACTIVATING INACTIVE EMPLOYEE SALARIES\n');
    console.log('='.repeat(50));
    
    // Get all active employees
    const employees = await Employee.find({ employmentStatus: 'active' }).lean();
    console.log(`📊 Found ${employees.length} active employees\n`);
    
    let reactivatedCount = 0;
    let alreadyActiveCount = 0;
    let noSalaryCount = 0;
    let errorCount = 0;
    
    for (const employee of employees) {
      try {
        console.log(`👤 Processing: ${employee.firstName} ${employee.lastName}`);
        
        // Check if employee already has an active salary
        const activeSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: true
        });
        
        if (activeSalary) {
          console.log(`   ✅ Already has active salary: ${activeSalary.basicSalary.toLocaleString()} MWK`);
          alreadyActiveCount++;
          continue;
        }
        
        // Find the latest inactive salary
        const latestInactiveSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: false
        }).sort({ effectiveDate: -1 });
        
        if (!latestInactiveSalary) {
          console.log(`   ❌ No salary records found`);
          noSalaryCount++;
          continue;
        }
        
        // Check if the salary has an end date in the past
        const now = new Date();
        if (latestInactiveSalary.endDate && new Date(latestInactiveSalary.endDate) < now) {
          console.log(`   ⚠️  Latest salary ended on ${new Date(latestInactiveSalary.endDate).toLocaleDateString()}`);
          console.log(`   🔄 Creating new active salary based on the ended one...`);
          
          // Create a new active salary based on the ended one
          const newSalary = new EmployeeSalary({
            employeeId: employee._id,
            salaryStructureId: latestInactiveSalary.salaryStructureId,
            basicSalary: latestInactiveSalary.basicSalary,
            currency: latestInactiveSalary.currency,
            effectiveDate: now,
            isActive: true,
            allowances: latestInactiveSalary.allowances,
            deductions: latestInactiveSalary.deductions,
            paymentMethod: latestInactiveSalary.paymentMethod,
            bankAccountNumber: latestInactiveSalary.bankAccountNumber,
            createdBy: 'system-reactivation-script',
            notes: `Reactivated salary based on previous record (${latestInactiveSalary._id}) by system script`
          });
          
          await newSalary.save();
          console.log(`   ✅ Created new active salary: ${newSalary.basicSalary.toLocaleString()} MWK`);
          reactivatedCount++;
          
        } else {
          // Reactivate the existing salary
          console.log(`   🔄 Reactivating salary: ${latestInactiveSalary.basicSalary.toLocaleString()} MWK`);
          console.log(`   📅 Original effective date: ${new Date(latestInactiveSalary.effectiveDate).toLocaleDateString()}`);
          
          // Update the salary to be active
          await EmployeeSalary.findByIdAndUpdate(latestInactiveSalary._id, {
            isActive: true,
            endDate: null, // Remove end date
            updatedBy: 'system-reactivation-script',
            updatedAt: new Date(),
            notes: (latestInactiveSalary.notes || '') + ' [Reactivated by system script]'
          });
          
          console.log(`   ✅ Reactivated existing salary record`);
          reactivatedCount++;
        }
        
      } catch (error) {
        console.error(`   ❌ Error processing ${employee.firstName} ${employee.lastName}:`, error.message);
        errorCount++;
      }
      
      console.log(''); // Empty line for readability
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 REACTIVATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Employees Processed: ${employees.length}`);
    console.log(`✅ Salaries Reactivated/Created: ${reactivatedCount}`);
    console.log(`✅ Already Had Active Salaries: ${alreadyActiveCount}`);
    console.log(`❌ No Salary Records Found: ${noSalaryCount}`);
    console.log(`❌ Errors Encountered: ${errorCount}`);
    
    // Verification
    console.log('\n' + '='.repeat(50));
    console.log('🔍 VERIFICATION');
    console.log('='.repeat(50));
    
    const totalActiveSalaries = await EmployeeSalary.countDocuments({ isActive: true });
    const employeesWithActiveSalaries = await EmployeeSalary.distinct('employeeId', { isActive: true });
    
    console.log(`Total Active Salary Records: ${totalActiveSalaries}`);
    console.log(`Employees with Active Salaries: ${employeesWithActiveSalaries.length}`);
    console.log(`Employees Still Missing Salaries: ${employees.length - employeesWithActiveSalaries.length}`);
    
    if (reactivatedCount > 0) {
      console.log('\n✅ Salary reactivation completed successfully!');
      console.log('🎯 You can now test payroll calculations again.');
    } else {
      console.log('\n⚠️  No salaries were reactivated.');
      console.log('💡 This might mean all employees already have active salaries,');
      console.log('   or there are other issues that need investigation.');
    }
    
    // Next steps
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Test payroll calculation with the reactivated salaries');
    console.log('2. If issues persist, run the deep-scan script again');
    console.log('3. Check for any remaining employees without active salaries');
    
  } catch (error) {
    console.error('❌ Reactivation process failed:', error);
  }
}

// Run the reactivation
reactivateEmployeeSalaries().then(() => {
  console.log('\n🏁 Reactivation process completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Reactivation process failed:', error);
  process.exit(1);
});

/**
 * <PERSON><PERSON><PERSON> to run budget component tests
 * This script runs the unit tests for the budget components
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Test files to run
const testFiles = [
  '__tests__/components/accounting/budget/budget-approval-workflow.test.tsx',
  '__tests__/components/accounting/budget/budget-audit-trail.test.tsx',
  '__tests__/components/accounting/budget/budget-revision.test.tsx',
  '__tests__/components/accounting/budget/budget-notifications.test.tsx'
];

// Check if test files exist
const missingFiles = testFiles.filter(file => !fs.existsSync(path.resolve(process.cwd(), file)));
if (missingFiles.length > 0) {
  console.error(`${colors.fg.red}Error: The following test files are missing:${colors.reset}`);
  missingFiles.forEach(file => console.error(`  - ${file}`));
  process.exit(1);
}

// Run tests
console.log(`${colors.fg.cyan}${colors.bright}Running Budget Component Tests${colors.reset}\n`);

let hasErrors = false;

testFiles.forEach(testFile => {
  const fileName = path.basename(testFile);
  console.log(`${colors.fg.yellow}Running tests for ${fileName}...${colors.reset}`);
  
  try {
    // Run Jest for the specific test file
    execSync(`npx jest ${testFile} --verbose`, { stdio: 'inherit' });
    console.log(`${colors.fg.green}✓ Tests passed for ${fileName}${colors.reset}\n`);
  } catch (error) {
    console.error(`${colors.fg.red}✗ Tests failed for ${fileName}${colors.reset}\n`);
    hasErrors = true;
  }
});

// Summary
if (hasErrors) {
  console.error(`${colors.fg.red}${colors.bright}Some tests failed. Please check the output above for details.${colors.reset}`);
  process.exit(1);
} else {
  console.log(`${colors.fg.green}${colors.bright}All budget component tests passed successfully!${colors.reset}`);
}

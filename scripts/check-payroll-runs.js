require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

async function main() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpackhrmanager');
    console.log('Connected to MongoDB');

    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('Collections:', collections.map(c => c.name));

    // Define PayrollRun schema (minimal version just for querying)
    const PayrollRunSchema = new Schema({}, { strict: false });
    
    // Check if PayrollRun model exists in different variations
    const modelNames = ['PayrollRun', 'payrollrun', 'payrollRun', 'Payrollrun'];
    
    for (const modelName of modelNames) {
      try {
        // Try to get the model if it exists
        const model = mongoose.models[modelName] || mongoose.model(modelName, PayrollRunSchema);
        const count = await model.countDocuments();
        console.log(`${modelName} count:`, count);
        
        if (count > 0) {
          const runs = await model.find({}).lean();
          console.log(`${modelName} documents:`, JSON.stringify(runs, null, 2));
        }
      } catch (err) {
        console.log(`Error with model ${modelName}:`, err.message);
      }
    }

    // Check for any payroll-related collections
    for (const collection of collections) {
      if (collection.name.toLowerCase().includes('payroll')) {
        console.log(`Checking collection: ${collection.name}`);
        const docs = await mongoose.connection.db.collection(collection.name).find({}).limit(5).toArray();
        console.log(`Sample documents from ${collection.name}:`, JSON.stringify(docs, null, 2));
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();

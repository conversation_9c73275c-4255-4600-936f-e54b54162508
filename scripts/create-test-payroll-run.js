require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Define PayrollRun schema
const PayrollRunSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  payPeriod: {
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    year: {
      type: Number,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'processing', 'completed', 'approved', 'paid', 'cancelled'],
    default: 'draft',
  },
  totalEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  processedEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  totalGrossSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    required: true,
    default: 0,
  },
  totalTax: {
    type: Number,
    required: true,
    default: 0,
  },
  totalNetSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    trim: true,
  },
  notes: {
    type: String,
    trim: true,
  },
  departments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedAt: {
    type: Date,
  },
  processedAt: {
    type: Date,
  },
  paidAt: {
    type: Date,
  },
}, {
  timestamps: true,
});

async function main() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    const MONGODB_URI = 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources';
    console.log('MongoDB URI:', MONGODB_URI);
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Create PayrollRun model
    const PayrollRun = mongoose.models.PayrollRun || mongoose.model('PayrollRun', PayrollRunSchema);

    // Create a test user ID
    const testUserId = new mongoose.Types.ObjectId();

    // Get current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // 1-12
    const currentYear = now.getFullYear();

    // Calculate start and end dates for the month
    const startDate = new Date(currentYear, currentMonth - 1, 1);
    const endDate = new Date(currentYear, currentMonth, 0);

    // Create a test payroll run
    const payrollRun = new PayrollRun({
      name: `Payroll for ${new Date(currentYear, currentMonth - 1).toLocaleString('default', { month: 'long' })} ${currentYear}`,
      description: `Monthly payroll processing for ${new Date(currentYear, currentMonth - 1).toLocaleString('default', { month: 'long' })} ${currentYear}`,
      payPeriod: {
        month: currentMonth,
        year: currentYear,
        startDate,
        endDate,
      },
      status: 'completed',
      totalEmployees: 10,
      processedEmployees: 10,
      totalGrossSalary: 5000000,
      totalDeductions: 500000,
      totalTax: 750000,
      totalNetSalary: 3750000,
      currency: 'MWK',
      notes: 'Test payroll run created via script',
      createdBy: testUserId,
      processedAt: new Date(),
    });

    // Save the payroll run
    await payrollRun.save();
    console.log('Test payroll run created:', payrollRun);

    // List all payroll runs
    const payrollRuns = await PayrollRun.find({});
    console.log('All payroll runs:', payrollRuns);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();

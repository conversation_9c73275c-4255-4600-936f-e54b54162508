# Object Type Fixing Tools

This directory contains tools to automatically detect and fix object typing issues in the codebase. These tools help replace `any` types with more specific type definitions.

## Available Tools

### 1. Targeted Object Type Fixer (`fix-object-types.js`)

This script fixes specific object type issues in pre-defined files. It replaces `any` types with detailed interface definitions for common objects like:

- `Integration`
- `SyncJob`
- `BudgetData`
- `MongoFilter`

#### Usage

```bash
npm run fix-object-types
```

### 2. Auto-Detect and Fix Tool (`auto-detect-fix-types.js`)

This script automatically scans the entire codebase for common object typing issues and fixes them. It:

1. Detects patterns like `integration: any`, `syncJob: any`, etc.
2. Replaces them with proper type definitions
3. Automatically adds the necessary interface definitions to the files

#### Usage

```bash
npm run auto-fix-types
```

## Type Definitions

The tools define the following interfaces:

### Integration

```typescript
interface Integration {
  id: string;
  name: string;
  type: string;
  provider: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  authType?: string;
  authData?: {
    token?: string;
    refreshToken?: string;
    expiresAt?: string;
    clientId?: string;
    clientSecret?: string;
    apiKey?: string;
  };
  settings?: Record<string, unknown>;
  lastSync?: string;
  syncFrequency?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  connectionStatus?: 'connected' | 'disconnected' | 'pending';
  errorMessage?: string;
}
```

### SyncJob

```typescript
interface SyncJob {
  id: string;
  name?: string;
  status: 'idle' | 'running' | 'paused' | 'error' | 'completed';
  source: string;
  destination: string;
  dataType: string;
  direction: 'import' | 'export' | 'bidirectional';
  schedule?: {
    frequency: string;
    startDate?: string;
    endDate?: string;
    time?: string;
    days?: string[];
  };
  lastRun?: string;
  nextRun?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  lastRunDuration?: number;
  averageDuration?: number;
  errorMessage?: string;
  errorDetails?: string;
  stats?: {
    totalRuns: number;
    recordsProcessed: number;
    successRate: number;
    errors: number;
  };
}
```

### BudgetData

```typescript
interface BudgetData {
  id?: string;
  name?: string;
  description?: string;
  fiscalYear?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  totalIncome: number;
  totalExpense: number;
  totalActualIncome: number;
  totalActualExpense: number;
  balance?: number;
  categories?: Array<{
    id: string;
    name: string;
    type: 'income' | 'expense';
    budgetedAmount: number;
    actualAmount: number;
    items?: Array<{
      id: string;
      name: string;
      budgetedAmount: number;
      actualAmount: number;
    }>;
  }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}
```

### MongoFilter

```typescript
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}
```

## Customizing the Tools

You can customize the tools by editing the configuration in the script files:

### Adding New Type Definitions

To add a new type definition, add it to the `typeDefinitions` object in the script:

```javascript
typeDefinitions: {
  // Existing definitions...
  
  // Add your new definition
  newType: `interface NewType {
    id: string;
    name: string;
    // Add more properties...
  }`
}
```

### Adding New Pattern Detection

To detect and fix a new pattern, add it to the `patterns` array in the `auto-detect-fix-types.js` script:

```javascript
patterns: [
  // Existing patterns...
  
  // Add your new pattern
  {
    name: 'newType',
    detect: /newType:\s*any/g,
    replacement: 'newType: NewType',
    typeDefinition: 'newType'
  }
]
```

## After Running the Tools

After running these tools, it's recommended to:

1. Run TypeScript compiler to check for any remaining issues:
   ```bash
   npx tsc --noEmit
   ```

2. Run the component scanner to check for other issues:
   ```bash
   npm run scan-components
   ```

## Backup Files

Both tools create backup files (`.bak` extension) before making changes. If you need to revert changes, you can restore from these backups.

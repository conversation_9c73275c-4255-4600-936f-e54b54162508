<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Payroll API Test</h1>
    
    <div class="test-section">
        <h3>1. Test Payroll Runs List</h3>
        <button onclick="testPayrollRuns()">Test GET /api/payroll/runs</button>
        <div id="payroll-runs-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Specific Payroll Run</h3>
        <input type="text" id="payroll-run-id" placeholder="Enter Payroll Run ID" style="width: 300px;">
        <button onclick="testPayrollRunDetails()">Test GET /api/payroll/runs/{id}</button>
        <div id="payroll-run-details-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Payroll Records</h3>
        <input type="text" id="payroll-records-id" placeholder="Enter Payroll Run ID" style="width: 300px;">
        <button onclick="testPayrollRecords()">Test GET /api/payroll/runs/{id}/records</button>
        <div id="payroll-records-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. Test Salary Calculation</h3>
        <input type="text" id="employee-id" placeholder="Enter Employee ID" style="width: 200px;">
        <input type="number" id="month" placeholder="Month (1-12)" min="1" max="12" style="width: 100px;">
        <input type="number" id="year" placeholder="Year" min="2020" max="2030" style="width: 100px;">
        <button onclick="testSalaryCalculation()">Test POST /api/payroll/calculate-salary</button>
        <div id="salary-calculation-result" class="result"></div>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    credentials: 'include',
                    ...options
                });
                
                const data = await response.json();
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        async function testPayrollRuns() {
            const resultDiv = document.getElementById('payroll-runs-result');
            resultDiv.innerHTML = 'Loading...';
            
            const result = await makeRequest('/api/payroll/runs');
            
            if (result.ok) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Success (${result.status})</h4>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
                
                // Auto-fill the first payroll run ID if available
                if (result.data.success && result.data.data.docs && result.data.data.docs.length > 0) {
                    const firstRunId = result.data.data.docs[0]._id;
                    document.getElementById('payroll-run-id').value = firstRunId;
                    document.getElementById('payroll-records-id').value = firstRunId;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        async function testPayrollRunDetails() {
            const runId = document.getElementById('payroll-run-id').value;
            const resultDiv = document.getElementById('payroll-run-details-result');
            
            if (!runId) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please enter a Payroll Run ID';
                return;
            }
            
            resultDiv.innerHTML = 'Loading...';
            
            const result = await makeRequest(`/api/payroll/runs/${runId}`);
            
            if (result.ok) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Success (${result.status})</h4>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        async function testPayrollRecords() {
            const runId = document.getElementById('payroll-records-id').value;
            const resultDiv = document.getElementById('payroll-records-result');
            
            if (!runId) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please enter a Payroll Run ID';
                return;
            }
            
            resultDiv.innerHTML = 'Loading...';
            
            const result = await makeRequest(`/api/payroll/runs/${runId}/records`);
            
            if (result.ok) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Success (${result.status})</h4>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        async function testSalaryCalculation() {
            const employeeId = document.getElementById('employee-id').value;
            const month = document.getElementById('month').value;
            const year = document.getElementById('year').value;
            const resultDiv = document.getElementById('salary-calculation-result');
            
            if (!employeeId || !month || !year) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please fill in all fields (Employee ID, Month, Year)';
                return;
            }
            
            resultDiv.innerHTML = 'Loading...';
            
            const result = await makeRequest('/api/payroll/calculate-salary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeId: employeeId,
                    payPeriod: {
                        month: parseInt(month),
                        year: parseInt(year)
                    }
                })
            });
            
            if (result.ok) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Success (${result.status})</h4>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Error (${result.status})</h4>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        // Auto-load payroll runs on page load
        window.onload = function() {
            testPayrollRuns();
        };
    </script>
</body>
</html>

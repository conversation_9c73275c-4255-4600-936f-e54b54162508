// <PERSON>ript to analyze and identify all files using deprecated payroll services
const fs = require('fs');
const path = require('path');

// Deprecated imports to search for
const deprecatedPatterns = [
  {
    pattern: /from ['"].*salary-calculation-service['"]/g,
    service: 'SalaryCalculationService',
    replacement: 'unifiedPayrollService.calculateEmployeeSalary'
  },
  {
    pattern: /from ['"].*\/services\/payroll\/SalaryService['"]/g,
    service: 'SalaryService',
    replacement: 'unifiedPayrollService'
  },
  {
    pattern: /from ['"].*\/services\/payroll\/PayrollService['"]/g,
    service: 'PayrollService (old)',
    replacement: 'unifiedPayrollService'
  },
  {
    pattern: /from ['"].*\/lib\/services\/accounting\/payroll-service['"]/g,
    service: 'Accounting PayrollService',
    replacement: 'unifiedPayrollService'
  },
  {
    pattern: /from ['"].*\/models\/accounting\/PayrollRecord['"]/g,
    service: 'Accounting PayrollRecord Model',
    replacement: 'models/payroll/PayrollRecord'
  },
  {
    pattern: /from ['"].*\/models\/accounting\/EmployeeSalary['"]/g,
    service: 'Accounting EmployeeSalary Model',
    replacement: 'models/payroll/EmployeeSalary'
  },
  {
    pattern: /salaryCalculationService\.calculateSalary/g,
    service: 'SalaryCalculationService method call',
    replacement: 'unifiedPayrollService.calculateEmployeeSalary'
  },
  {
    pattern: /salaryService\.calculateNetSalary/g,
    service: 'SalaryService method call',
    replacement: 'unifiedPayrollService.calculateEmployeeSalary'
  }
];

// Directories to scan
const directoriesToScan = [
  'app/api',
  'lib/services',
  'components',
  'services',
  'models'
];

// Files to exclude from scanning
const excludePatterns = [
  /node_modules/,
  /\.git/,
  /\.next/,
  /dist/,
  /build/,
  /\.backup$/,
  /unified-payroll-service\.ts$/,
  /cleanup-deprecated-payroll-services\.js$/,
  /analyze-deprecated-dependencies\.js$/
];

function shouldExcludeFile(filePath) {
  return excludePatterns.some(pattern => pattern.test(filePath));
}

function scanFile(filePath) {
  try {
    if (shouldExcludeFile(filePath)) {
      return null;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const findings = [];
    
    for (const deprecated of deprecatedPatterns) {
      const matches = content.match(deprecated.pattern);
      if (matches) {
        findings.push({
          pattern: deprecated.pattern.toString(),
          service: deprecated.service,
          replacement: deprecated.replacement,
          matches: matches.length,
          lines: getLineNumbers(content, deprecated.pattern)
        });
      }
    }
    
    return findings.length > 0 ? { file: filePath, findings } : null;
  } catch (error) {
    console.error(`Error scanning ${filePath}:`, error.message);
    return null;
  }
}

function getLineNumbers(content, pattern) {
  const lines = content.split('\n');
  const lineNumbers = [];
  
  lines.forEach((line, index) => {
    if (pattern.test(line)) {
      lineNumbers.push(index + 1);
    }
  });
  
  return lineNumbers;
}

function scanDirectory(dirPath) {
  const results = [];
  
  try {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory not found: ${dirPath}`);
      return results;
    }
    
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        results.push(...scanDirectory(fullPath));
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx'))) {
        const result = scanFile(fullPath);
        if (result) {
          results.push(result);
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
  
  return results;
}

function generateUpdatePlan(results) {
  console.log('\n📋 UPDATE PLAN\n');
  console.log('The following files need to be updated before cleanup:\n');
  
  const updatePlan = [];
  
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file}`);
    
    const updates = [];
    result.findings.forEach(finding => {
      console.log(`   ❌ ${finding.service} (${finding.matches} occurrences)`);
      console.log(`      Lines: ${finding.lines.join(', ')}`);
      console.log(`      Replace with: ${finding.replacement}`);
      
      updates.push({
        service: finding.service,
        replacement: finding.replacement,
        lines: finding.lines
      });
    });
    
    updatePlan.push({
      file: result.file,
      updates
    });
    
    console.log('');
  });
  
  return updatePlan;
}

function generateCleanupScript(updatePlan) {
  console.log('\n🔧 GENERATED CLEANUP COMMANDS\n');
  
  updatePlan.forEach(plan => {
    console.log(`# Update ${plan.file}`);
    plan.updates.forEach(update => {
      console.log(`# - Replace ${update.service} on lines: ${update.lines.join(', ')}`);
      console.log(`# - Use: ${update.replacement}`);
    });
    console.log('');
  });
}

function analyzeDependencies() {
  console.log('🔍 Analyzing deprecated payroll service dependencies...\n');
  
  let allResults = [];
  
  for (const dir of directoriesToScan) {
    console.log(`Scanning ${dir}...`);
    const results = scanDirectory(dir);
    allResults.push(...results);
  }
  
  console.log(`\n✅ Scan completed. Found ${allResults.length} files with deprecated dependencies.\n`);
  
  if (allResults.length === 0) {
    console.log('🎉 No deprecated dependencies found! Ready for cleanup.');
    return;
  }
  
  // Generate update plan
  const updatePlan = generateUpdatePlan(allResults);
  
  // Generate cleanup commands
  generateCleanupScript(updatePlan);
  
  console.log('\n⚠️  IMPORTANT: Update all files above before running cleanup script!');
  console.log('\n🚀 Next Steps:');
  console.log('1. Update each file listed above');
  console.log('2. Test the application to ensure everything works');
  console.log('3. Run: node scripts/cleanup-deprecated-payroll-services.js');
  console.log('4. Verify TypeScript compilation passes');
  
  // Save detailed report
  const reportPath = 'DEPRECATED_DEPENDENCIES_REPORT.json';
  fs.writeFileSync(reportPath, JSON.stringify({
    scanDate: new Date().toISOString(),
    totalFiles: allResults.length,
    files: allResults
  }, null, 2));
  
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Run the analysis
analyzeDependencies();

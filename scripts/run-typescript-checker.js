const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  // Whether to run the scanner
  runScanner: true,
  // Whether to generate the report
  generateReport: true,
  // Whether to run the fixer
  runFixer: true,
  // Whether to apply fixes (false for dry run)
  applyFixes: false, // Default to dry run for safety
  // Whether to compile TypeScript after fixing
  compileAfterFix: true
};

// Ensure the TypeScript compiler is installed
try {
  execSync('npx tsc --version', { stdio: 'ignore' });
  console.log('TypeScript is installed.');
} catch (error) {
  console.error('TypeScript is not installed. Please install it with: npm install -g typescript');
  process.exit(1);
}

// Run the TypeScript error scanner
function runScanner() {
  console.log('\n=== Running TypeScript Error Scanner ===\n');
  
  try {
    // Compile the scanner
    console.log('Compiling the scanner...');
    execSync('npx tsc --esModuleInterop scripts/typescript-error-scanner.ts', { stdio: 'inherit' });
    
    // Run the scanner
    console.log('Running the scanner...');
    execSync('node scripts/typescript-error-scanner.js', { stdio: 'inherit' });
    
    // Check if results file exists
    const resultsPath = path.join(process.cwd(), 'typescript-errors.json');
    if (fs.existsSync(resultsPath)) {
      console.log(`Scanner completed. Results saved to: ${resultsPath}`);
      return true;
    } else {
      console.warn('No results file was generated.');
      return false;
    }
  } catch (error) {
    console.error('Error running the scanner:', error.message);
    return false;
  }
}

// Generate the error report
function generateReport() {
  console.log('\n=== Generating TypeScript Error Report ===\n');
  
  try {
    // Check if results file exists
    const resultsPath = path.join(process.cwd(), 'typescript-errors.json');
    if (!fs.existsSync(resultsPath)) {
      console.error('Error: typescript-errors.json not found. Run the scanner first.');
      return false;
    }
    
    // Run the report generator
    console.log('Generating report...');
    execSync('node scripts/generate-typescript-error-report.js', { stdio: 'inherit' });
    
    // Check if report file exists
    const reportPath = path.join(process.cwd(), 'TYPESCRIPT-ERRORS-REPORT.md');
    if (fs.existsSync(reportPath)) {
      console.log(`Report generated at: ${reportPath}`);
      return true;
    } else {
      console.warn('No report file was generated.');
      return false;
    }
  } catch (error) {
    console.error('Error generating the report:', error.message);
    return false;
  }
}

// Run the error fixer
function runFixer() {
  console.log('\n=== Running TypeScript Error Fixer ===\n');
  
  try {
    // Check if results file exists
    const resultsPath = path.join(process.cwd(), 'typescript-errors.json');
    if (!fs.existsSync(resultsPath)) {
      console.error('Error: typescript-errors.json not found. Run the scanner first.');
      return false;
    }
    
    // Update the fixer configuration if needed
    if (!config.applyFixes) {
      console.log('Running in dry-run mode. No files will be modified.');
      
      // Update the fixer script to set applyFixes = false
      const fixerPath = path.join(process.cwd(), 'scripts/fix-typescript-errors.js');
      let fixerContent = fs.readFileSync(fixerPath, 'utf8');
      fixerContent = fixerContent.replace(
        /applyFixes:\s*true/,
        'applyFixes: false'
      );
      fs.writeFileSync(fixerPath, fixerContent);
    } else {
      console.log('Running with applyFixes = true. Files will be modified.');
      
      // Update the fixer script to set applyFixes = true
      const fixerPath = path.join(process.cwd(), 'scripts/fix-typescript-errors.js');
      let fixerContent = fs.readFileSync(fixerPath, 'utf8');
      fixerContent = fixerContent.replace(
        /applyFixes:\s*false/,
        'applyFixes: true'
      );
      fs.writeFileSync(fixerPath, fixerContent);
    }
    
    // Run the fixer
    console.log('Running the fixer...');
    execSync('node scripts/fix-typescript-errors.js', { stdio: 'inherit' });
    
    return true;
  } catch (error) {
    console.error('Error running the fixer:', error.message);
    return false;
  }
}

// Compile TypeScript to check for remaining errors
function compileTypeScript() {
  console.log('\n=== Compiling TypeScript to Check for Remaining Errors ===\n');
  
  try {
    console.log('Running TypeScript compiler...');
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('TypeScript compilation completed.');
    return true;
  } catch (error) {
    console.log('TypeScript compilation completed with errors.');
    return false;
  }
}

// Main function
async function main() {
  console.log('=== TypeScript Error Checker ===\n');
  
  let scannerSuccess = true;
  let reportSuccess = true;
  let fixerSuccess = true;
  let compileSuccess = true;
  
  // Run the scanner if configured
  if (config.runScanner) {
    scannerSuccess = runScanner();
  }
  
  // Generate the report if configured and scanner succeeded
  if (config.generateReport && scannerSuccess) {
    reportSuccess = generateReport();
  }
  
  // Run the fixer if configured and scanner succeeded
  if (config.runFixer && scannerSuccess) {
    fixerSuccess = runFixer();
  }
  
  // Compile TypeScript if configured and fixer succeeded
  if (config.compileAfterFix && fixerSuccess && config.applyFixes) {
    compileSuccess = compileTypeScript();
  }
  
  // Print summary
  console.log('\n=== Summary ===\n');
  console.log(`Scanner: ${scannerSuccess ? 'Success' : 'Failed'}`);
  console.log(`Report: ${reportSuccess ? 'Success' : 'Failed'}`);
  console.log(`Fixer: ${fixerSuccess ? 'Success' : 'Failed'}`);
  if (config.compileAfterFix && config.applyFixes) {
    console.log(`Compilation: ${compileSuccess ? 'Success' : 'Completed with errors'}`);
  }
  
  // Provide next steps
  console.log('\n=== Next Steps ===\n');
  
  if (reportSuccess) {
    console.log('1. Review the TypeScript error report: TYPESCRIPT-ERRORS-REPORT.md');
  }
  
  if (fixerSuccess && !config.applyFixes) {
    console.log('2. Run the fixer with applyFixes = true to apply the fixes:');
    console.log('   node scripts/run-typescript-checker.js --apply-fixes');
  }
  
  if (fixerSuccess && config.applyFixes) {
    console.log('2. Review the applied fixes and fix any remaining errors manually.');
  }
  
  console.log('3. Run the TypeScript compiler to check for remaining errors:');
  console.log('   npx tsc --noEmit');
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.includes('--apply-fixes')) {
  config.applyFixes = true;
}

// Run the script
main().catch(console.error);

// Test Tax Calculation Script
// This script tests the tax calculation logic to ensure it matches the employee salaries page

function calculateTax(taxableAmount) {
  // Use the same calculation logic as employee salaries page
  if (taxableAmount <= 150000) return 0;
  if (taxableAmount <= 500000) return (taxableAmount - 150000) * 0.25;
  if (taxableAmount <= 2550000) return 87500 + (taxableAmount - 500000) * 0.3;
  return 702500 + (taxableAmount - 2550000) * 0.35;
}

function testTaxCalculation() {
  console.log('🧮 Testing Tax Calculation Logic\n');
  
  const testCases = [
    { salary: 100000, expectedTax: 0 },
    { salary: 150000, expectedTax: 0 },
    { salary: 200000, expectedTax: 12500 },
    { salary: 500000, expectedTax: 87500 },
    { salary: 1000000, expectedTax: 237500 },
    { salary: 2550000, expectedTax: 702500 },
    { salary: 3256128, expectedTax: 949644.8 }
  ];
  
  console.log('Test Cases:');
  console.log('===========');
  
  testCases.forEach((testCase, index) => {
    const calculatedTax = calculateTax(testCase.salary);
    const isCorrect = Math.abs(calculatedTax - testCase.expectedTax) < 0.01;
    
    console.log(`${index + 1}. Salary: MWK ${testCase.salary.toLocaleString()}`);
    console.log(`   Expected Tax: MWK ${testCase.expectedTax.toLocaleString()}`);
    console.log(`   Calculated Tax: MWK ${calculatedTax.toLocaleString()}`);
    console.log(`   Result: ${isCorrect ? '✅ PASS' : '❌ FAIL'}`);
    console.log('');
  });
  
  // Detailed breakdown for the problematic salary
  console.log('Detailed Breakdown for MWK 3,256,128:');
  console.log('=====================================');
  
  const salary = 3256128;
  let tax = 0;
  
  // First bracket: 0 - 150,000 @ 0%
  const bracket1 = Math.min(salary, 150000);
  const tax1 = 0;
  tax += tax1;
  console.log(`Bracket 1 (0 - 150,000 @ 0%): MWK ${bracket1.toLocaleString()} → Tax: MWK ${tax1.toLocaleString()}`);
  
  // Second bracket: 150,001 - 500,000 @ 25%
  if (salary > 150000) {
    const bracket2 = Math.min(salary - 150000, 350000);
    const tax2 = bracket2 * 0.25;
    tax += tax2;
    console.log(`Bracket 2 (150,001 - 500,000 @ 25%): MWK ${bracket2.toLocaleString()} → Tax: MWK ${tax2.toLocaleString()}`);
  }
  
  // Third bracket: 500,001 - 2,550,000 @ 30%
  if (salary > 500000) {
    const bracket3 = Math.min(salary - 500000, 2050000);
    const tax3 = bracket3 * 0.3;
    tax += tax3;
    console.log(`Bracket 3 (500,001 - 2,550,000 @ 30%): MWK ${bracket3.toLocaleString()} → Tax: MWK ${tax3.toLocaleString()}`);
  }
  
  // Fourth bracket: Above 2,550,000 @ 35%
  if (salary > 2550000) {
    const bracket4 = salary - 2550000;
    const tax4 = bracket4 * 0.35;
    tax += tax4;
    console.log(`Bracket 4 (Above 2,550,000 @ 35%): MWK ${bracket4.toLocaleString()} → Tax: MWK ${tax4.toLocaleString()}`);
  }
  
  console.log(`\nTotal Tax: MWK ${tax.toLocaleString()}`);
  console.log(`Net Salary: MWK ${(salary - tax).toLocaleString()}`);
  console.log(`Effective Tax Rate: ${((tax / salary) * 100).toFixed(2)}%`);
}

// Run the test
testTaxCalculation();

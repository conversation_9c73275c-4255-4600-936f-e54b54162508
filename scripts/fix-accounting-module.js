/**
 * Fix TypeScript Errors in Accounting Module
 * 
 * This script fixes common TypeScript errors in the accounting module.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directory to scan
const ACCOUNTING_DIR = path.join(process.cwd(), 'app', '(dashboard)', 'dashboard', 'accounting');

// Error patterns and their fixes
const ERROR_FIXES = [
  // Template string errors
  {
    pattern: /'([^']*\$\{[^']*\}[^']*)'/g,
    fix: (match, p1) => `\`${p1}\``,
    name: 'Template String Error',
  },
  
  // Redundant error instanceof Error checks
  {
    pattern: /error instanceof Error \? error instanceof Error \? (error instanceof Error \? )*(error\.message)/g,
    fix: (_, p1) => `error instanceof Error ? ${p1}`,
    name: 'Redundant Error Check',
  },
  
  // initialFocus prop error
  {
    pattern: /<Calendar([^>]*)initialFocus([^>]*)>/g,
    fix: (match) => match.replace(/initialFocus/, ''),
    name: 'initialFocus Prop Error',
  },
  
  // Next.js 15 dynamic route parameter errors
  {
    pattern: /params: \{ ([a-zA-Z0-9_]+): string \}/g,
    fix: (match, p1) => `params: Promise<{ ${p1}: string }>`,
    name: 'Dynamic Route Param Error',
  },
  
  // Duplicate Promise types
  {
    pattern: /Promise<([^>]+)>: Promise<([^>]+)>/g,
    fix: (_, _p1, p2) => `Promise<${p2}>`,
    name: 'Duplicate Promise Error',
  },
];

// Function to fix errors in a file
function fixErrorsInFile(filePath) {
  console.log(`Checking file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let fixCount = 0;
  
  // Apply each fix
  for (const errorFix of ERROR_FIXES) {
    const originalContent = content;
    content = content.replace(errorFix.pattern, errorFix.fix);
    
    if (content !== originalContent) {
      modified = true;
      // Count the number of fixes
      const matches = originalContent.match(errorFix.pattern);
      const count = matches ? matches.length : 0;
      fixCount += count;
      console.log(`  Fixed ${count} ${errorFix.name}(s) in ${filePath}`);
    }
  }
  
  // Write the modified content back to the file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  Total fixes in ${filePath}: ${fixCount}`);
  }
  
  return { fixed: modified, fixCount };
}

// Function to scan a directory recursively for TypeScript files
function scanDirectory(dirPath) {
  const files = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      files.push(...scanDirectory(fullPath));
    } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
      files.push(fullPath);
    }
  }

  return files;
}

// Function to fix null type errors by updating component props
function fixNullTypeErrors(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check if the file contains component props
  if (content.includes('interface') && content.includes('Props')) {
    // Parse the file to find component props interfaces
    const interfaceMatches = content.match(/interface\s+([a-zA-Z0-9_]+Props)\s*\{[^}]*\}/g);
    
    if (interfaceMatches) {
      let modified = false;
      let fixCount = 0;
      let newContent = content;
      
      for (const interfaceMatch of interfaceMatches) {
        // Check if the interface has properties that need to be updated
        const propMatches = interfaceMatch.match(/([a-zA-Z0-9_]+):\s*([a-zA-Z0-9_]+)(\s*\|\s*undefined)?;/g);
        
        if (propMatches) {
          for (const propMatch of propMatches) {
            // Update the prop type to include null
            const updatedProp = propMatch.replace(/:\s*([a-zA-Z0-9_]+)(\s*\|\s*undefined)?;/, ': $1 | null | undefined;');
            
            if (updatedProp !== propMatch) {
              newContent = newContent.replace(propMatch, updatedProp);
              modified = true;
              fixCount++;
            }
          }
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`  Fixed ${fixCount} Null Type Error(s) in ${filePath}`);
      }
      
      return { fixed: modified, fixCount };
    }
  }
  
  return { fixed: false, fixCount: 0 };
}

// Main function to run the error fixer
async function main() {
  console.log('Scanning accounting module directory...');
  const files = scanDirectory(ACCOUNTING_DIR);
  console.log(`Found ${files.length} TypeScript files`);
  
  let totalFixedFiles = 0;
  let totalFixCount = 0;
  
  // Fix errors in each file
  for (const file of files) {
    const { fixed: fixed1, fixCount: fixCount1 } = fixErrorsInFile(file);
    const { fixed: fixed2, fixCount: fixCount2 } = fixNullTypeErrors(file);
    
    if (fixed1 || fixed2) {
      totalFixedFiles++;
      totalFixCount += fixCount1 + fixCount2;
    }
  }
  
  console.log(`\nFixed ${totalFixCount} errors in ${totalFixedFiles} files`);
  
  // Run TypeScript compiler to check for remaining errors
  console.log('\nRunning TypeScript compiler to check for remaining errors...');
  try {
    execSync('npx tsc --noEmit --project tsconfig.json', { stdio: 'inherit' });
    console.log('TypeScript compilation successful!');
  } catch (error) {
    console.log('TypeScript compilation failed. Some errors remain.');
  }
}

// Run the main function
main().catch(console.error);

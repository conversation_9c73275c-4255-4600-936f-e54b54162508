const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Input file (output from typescript-error-scanner.ts)
  inputFile: 'typescript-errors.json',
  // Output markdown report file
  outputFile: 'TYPESCRIPT-ERRORS-REPORT.md',
  // Maximum number of errors to show per file in the report
  maxErrorsPerFile: 10,
  // Whether to include source code in the report
  includeSource: true,
  // Whether to group errors by category
  groupByCategory: true,
  // Whether to include suggestions for fixing errors
  includeSuggestions: true
};

// Error fix suggestions
const errorSuggestions = {
  'Object is possibly null': 
    "Use optional chaining (`?.`) or add a null check before accessing properties.",
  
  'Object is possibly undefined': 
    "Use optional chaining (`?.`) or add an undefined check before accessing properties.",
  
  'Object is possibly null or undefined': 
    "Use optional chaining (`?.`) or add a null/undefined check before accessing properties.",
  
  'Type assignment error': 
    "Ensure the types are compatible or use type assertions if you're certain about the type.",
  
  'Property does not exist on type': 
    "Check if the property name is correct or if you need to extend the type definition.",
  
  'Argument type mismatch': 
    "Make sure the argument type matches the expected parameter type.",
  
  'Object is of type unknown': 
    "Add a type check or type assertion before accessing properties of an unknown type. Example: `if (typeof err === 'object' && err !== null && 'message' in err) { ... }`",
  
  'Expected parameters mismatch': 
    "Check the function signature and ensure you're providing the correct parameters.",
  
  'Property missing in type': 
    "Add the missing property to the type definition or use a type assertion.",
  
  'Parameter implicitly has an any type': 
    "Add an explicit type annotation to the parameter.",
  
  'Binding element implicitly has an any type': 
    "Add an explicit type annotation to the destructured element.",
  
  'Operator cannot be applied to types': 
    "Ensure the operands have compatible types for the operation.",
  
  'Function lacks ending return statement': 
    "Add a return statement at the end of the function or make sure all code paths return a value.",
  
  'Switch case fall-through': 
    "Add a `break` statement at the end of each case or use `// fall through` comment if intentional.",
  
  'Cannot assign to readonly property': 
    "Remove the assignment or change the property to be writable.",
  
  'Cannot assign to property (private)': 
    "Use a public method provided by the class to modify the property.",
  
  'Property does not exist on type (indexed access)': 
    "Use optional chaining (`?.`) or ensure the property exists before accessing it.",
  
  'This expression is not callable': 
    "Ensure the expression is a function before trying to call it.",
  
  'Type conversion error': 
    "Use appropriate type conversion methods or type assertions.",
  
  'Object literal may only specify known properties': 
    "Remove unknown properties or extend the type definition to include them.",
  
  'Union type error': 
    "Narrow the union type using type guards before performing operations specific to one type.",
  
  'Type instantiation error': 
    "Check the generic type parameters and ensure they match the constraints.",
  
  'Type has no construct signatures': 
    "Ensure the type can be instantiated with `new` or use a factory function instead.",
  
  'No overload matches this call': 
    "Check the function overloads and ensure you're calling it with the correct parameters.",
  
  'Type missing properties from type': 
    "Add the missing properties to the object or use a type assertion.",
  
  'Element implicitly has an any type': 
    "Add an explicit type annotation to the element."
};

// Generate a markdown report from the error data
function generateReport() {
  // Read the error data
  const errorData = JSON.parse(fs.readFileSync(config.inputFile, 'utf8'));
  const { errors, errorsByFile } = errorData;
  
  // Start building the report
  let report = `# TypeScript Error Report\n\n`;
  
  // Add summary
  report += `## Summary\n\n`;
  report += `- **Total Errors**: ${errors.length}\n`;
  report += `- **Files with Errors**: ${Object.keys(errorsByFile).length}\n\n`;
  
  // Add top error categories
  const categoryCounts = {};
  for (const error of errors) {
    categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;
  }
  
  report += `## Top Error Categories\n\n`;
  Object.entries(categoryCounts)
    .sort((a, b) => b[1] - a[1])
    .forEach(([category, count]) => {
      report += `- **${category}**: ${count} occurrences\n`;
    });
  
  // Group errors by category if configured
  if (config.groupByCategory) {
    report += `\n## Errors by Category\n\n`;
    
    const errorsByCategory = {};
    for (const error of errors) {
      if (!errorsByCategory[error.category]) {
        errorsByCategory[error.category] = [];
      }
      errorsByCategory[error.category].push(error);
    }
    
    for (const [category, categoryErrors] of Object.entries(errorsByCategory)) {
      report += `### ${category} (${categoryErrors.length} errors)\n\n`;
      
      // Add suggestion for this category if available
      if (config.includeSuggestions && errorSuggestions[category]) {
        report += `**Suggestion**: ${errorSuggestions[category]}\n\n`;
      }
      
      // Show a few examples
      const examples = categoryErrors.slice(0, 3);
      for (const error of examples) {
        report += `- **${error.filePath}:${error.line}:${error.column}**: ${error.message}\n`;
        
        if (config.includeSource && error.source) {
          report += "\n```typescript\n" + error.source + "```\n\n";
        }
      }
      
      if (categoryErrors.length > 3) {
        report += `- ... and ${categoryErrors.length - 3} more\n`;
      }
      
      report += '\n';
    }
  }
  
  // List errors by file
  report += `\n## Errors by File\n\n`;
  
  for (const [filePath, fileErrors] of Object.entries(errorsByFile)) {
    report += `### ${filePath} (${fileErrors.length} errors)\n\n`;
    
    // Show errors up to the configured maximum
    const displayErrors = fileErrors.slice(0, config.maxErrorsPerFile);
    for (const error of displayErrors) {
      report += `- **Line ${error.line}:${error.column}** (${error.category}): ${error.message}\n`;
      
      if (config.includeSource && error.source) {
        report += "\n```typescript\n" + error.source + "```\n\n";
      }
      
      // Add suggestion if available
      if (config.includeSuggestions && errorSuggestions[error.category]) {
        report += `  - **Suggestion**: ${errorSuggestions[error.category]}\n\n`;
      }
    }
    
    if (fileErrors.length > config.maxErrorsPerFile) {
      report += `- ... and ${fileErrors.length - config.maxErrorsPerFile} more errors\n`;
    }
    
    report += '\n';
  }
  
  // Add section on how to fix common errors
  report += `\n## Common Error Fixes\n\n`;
  
  // Unknown type error (specific to the example provided)
  report += `### Handling Unknown Types\n\n`;
  report += `When catching errors, TypeScript treats the error as \`unknown\` for type safety. To fix errors like:\n\n`;
  report += "```typescript\ncatch (err: unknown) {\n  setError(err.message || 'An error occurred') // Error: 'err' is of type 'unknown'\n}\n```\n\n";
  report += `Use type checking or type assertions:\n\n`;
  report += "```typescript\n// Option 1: Type checking\ncatch (err: unknown) {\n  if (err instanceof Error) {\n    setError(err.message || 'An error occurred')\n  } else {\n    setError('An error occurred')\n  }\n}\n\n";
  report += "// Option 2: Type assertion (use with caution)\ncatch (err: unknown) {\n  setError((err as Error).message || 'An error occurred')\n}\n\n";
  report += "// Option 3: Type guard function\nfunction isError(error: unknown): error is Error {\n  return error instanceof Error;\n}\n\ncatch (err: unknown) {\n  if (isError(err)) {\n    setError(err.message || 'An error occurred')\n  } else {\n    setError('An error occurred')\n  }\n}\n```\n\n";
  
  // Add more common error fixes
  for (const [category, suggestion] of Object.entries(errorSuggestions)) {
    if (categoryCounts[category] && categoryCounts[category] > 5) {
      report += `### ${category}\n\n`;
      report += `${suggestion}\n\n`;
    }
  }
  
  // Write the report to file
  fs.writeFileSync(config.outputFile, report);
  console.log(`Report generated at ${config.outputFile}`);
}

// Run the report generator
generateReport();

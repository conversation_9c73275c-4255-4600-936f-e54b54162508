const XLSX = require('xlsx');
const path = require('path');

// Read the corrected Excel file
const filePath = path.join(__dirname, '..', 'format_excel', 'tcm_roles_import_corrected.xlsx');

try {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  
  // Convert to JSON to check structure
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log('=== Excel File Verification ===');
  console.log(`File: ${filePath}`);
  console.log(`Sheet: ${sheetName}`);
  console.log(`Total rows: ${data.length}`);
  
  if (data.length > 0) {
    console.log('\n=== Headers ===');
    console.log('Headers:', data[0]);
    
    console.log('\n=== Expected vs Actual ===');
    const expected = ['Name', 'Code', 'Description', 'Department'];
    const actual = data[0];
    
    expected.forEach((header, index) => {
      const match = actual[index] === header ? '✅' : '❌';
      console.log(`${match} Expected: "${header}" | Actual: "${actual[index]}"`);
    });
    
    console.log('\n=== Sample Data (First 3 rows) ===');
    for (let i = 1; i <= Math.min(3, data.length - 1); i++) {
      console.log(`Row ${i}:`, data[i]);
    }
    
    console.log('\n=== Validation ===');
    let validRows = 0;
    let invalidRows = 0;
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const hasName = row[0] && row[0].toString().trim() !== '';
      const hasCode = row[1] && row[1].toString().trim() !== '';
      
      if (hasName && hasCode) {
        validRows++;
      } else {
        invalidRows++;
        console.log(`❌ Row ${i}: Missing required fields - Name: "${row[0]}", Code: "${row[1]}"`);
      }
    }
    
    console.log(`\n✅ Valid rows: ${validRows}`);
    console.log(`❌ Invalid rows: ${invalidRows}`);
    
    if (invalidRows === 0) {
      console.log('\n🎉 File is ready for import!');
    } else {
      console.log('\n⚠️  File has issues that need to be fixed.');
    }
  } else {
    console.log('❌ File is empty');
  }
  
} catch (error) {
  console.error('Error reading Excel file:', error.message);
}

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  applyFixes: true
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Fix Badge variant issues
function fixBadgeVariants(content) {
  // Replace unsupported 'warning' variant with 'secondary'
  let fixedContent = content.replace(
    /variant\s*=\s*(['"])warning\1/g,
    'variant="secondary"'
  );
  
  // Replace unsupported 'success' variant with 'default'
  fixedContent = fixedContent.replace(
    /variant\s*=\s*(['"])success\1/g,
    'variant="default"'
  );
  
  // Fix dynamic variant assignments
  fixedContent = fixedContent.replace(
    /variant\s*=\s*\{([^}]+)(['"])warning\2([^}]+)\}/g,
    (match, before, quote, after) => {
      return `variant={${before}${quote}secondary${quote}${after}}`;
    }
  );
  
  fixedContent = fixedContent.replace(
    /variant\s*=\s*\{([^}]+)(['"])success\2([^}]+)\}/g,
    (match, before, quote, after) => {
      return `variant={${before}${quote}default${quote}${after}}`;
    }
  );
  
  // Fix conditional variant assignments
  fixedContent = fixedContent.replace(
    /const\s+(\w+)\s*=\s*([^;]+)(['"])warning\3([^;]+);/g,
    (match, varName, before, quote, after) => {
      return `const ${varName} = ${before}${quote}secondary${quote}${after};`;
    }
  );
  
  fixedContent = fixedContent.replace(
    /const\s+(\w+)\s*=\s*([^;]+)(['"])success\3([^;]+);/g,
    (match, varName, before, quote, after) => {
      return `const ${varName} = ${before}${quote}default${quote}${after};`;
    }
  );
  
  // Fix ternary operators with badge variants
  fixedContent = fixedContent.replace(
    /\?\s*(['"])warning\1\s*:/g,
    `? ${'"secondary"'} :`
  );
  
  fixedContent = fixedContent.replace(
    /\?\s*(['"])success\1\s*:/g,
    `? ${'"default"'} :`
  );
  
  return fixedContent;
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  
  // Fix Badge variant issues
  const contentAfterFix = fixBadgeVariants(fixedContent);
  if (contentAfterFix !== fixedContent) {
    fixedContent = contentAfterFix;
    changesApplied = true;
    console.log(`Fixed Badge variant issues in ${filePath}`);
  }
  
  // Save changes if any were applied
  if (changesApplied && config.applyFixes) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
async function main() {
  console.log('Fixing Badge variant issues...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed Badge variant issues in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
  }
}

// Run the script
main().catch(console.error);

#!/bin/bash

# Procurement Module Test Runner
# Comprehensive testing script for Phase 4 integration

set -e

echo "🚀 Starting Procurement Module Test Suite"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_ENV="test"
COVERAGE_THRESHOLD=80
TIMEOUT=30000

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
    esac
}

# Function to run tests with error handling
run_test_suite() {
    local test_name=$1
    local test_pattern=$2
    local description=$3
    
    print_status "info" "Running $test_name..."
    echo "Description: $description"
    echo "Pattern: $test_pattern"
    echo "----------------------------------------"
    
    if npm test -- "$test_pattern" --timeout=$TIMEOUT; then
        print_status "success" "$test_name completed successfully"
    else
        print_status "error" "$test_name failed"
        return 1
    fi
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_status "info" "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_status "error" "Node.js is not installed"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_status "error" "npm is not installed"
        exit 1
    fi
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_status "error" "package.json not found. Please run from project root."
        exit 1
    fi
    
    # Check if test dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_status "warning" "node_modules not found. Installing dependencies..."
        npm install
    fi
    
    print_status "success" "Prerequisites check completed"
    echo ""
}

# Function to setup test environment
setup_test_environment() {
    print_status "info" "Setting up test environment..."
    
    # Set environment variables
    export NODE_ENV=$TEST_ENV
    export NEXT_PUBLIC_APP_ENV=$TEST_ENV
    
    # Create test database if needed
    if [ -f ".env.test" ]; then
        export $(cat .env.test | xargs)
        print_status "info" "Loaded test environment variables"
    else
        print_status "warning" ".env.test file not found"
    fi
    
    print_status "success" "Test environment setup completed"
    echo ""
}

# Function to run linting
run_linting() {
    print_status "info" "Running code linting..."
    
    if npm run lint; then
        print_status "success" "Linting passed"
    else
        print_status "warning" "Linting issues found (continuing with tests)"
    fi
    echo ""
}

# Function to run type checking
run_type_checking() {
    print_status "info" "Running TypeScript type checking..."
    
    if npm run type-check 2>/dev/null || npx tsc --noEmit; then
        print_status "success" "Type checking passed"
    else
        print_status "warning" "Type checking issues found (continuing with tests)"
    fi
    echo ""
}

# Main test execution
main() {
    local start_time=$(date +%s)
    local failed_tests=()
    
    echo "Starting comprehensive test suite at $(date)"
    echo ""
    
    # Prerequisites and setup
    check_prerequisites
    setup_test_environment
    
    # Code quality checks
    run_linting
    run_type_checking
    
    print_status "info" "Starting test execution..."
    echo "========================================"
    
    # 1. Unit Tests - Components
    if ! run_test_suite \
        "Component Unit Tests" \
        "__tests__/procurement/requisition.test.ts" \
        "Testing individual component functionality, user interactions, and error handling"; then
        failed_tests+=("Component Unit Tests")
    fi
    
    # 2. API Integration Tests
    if ! run_test_suite \
        "API Integration Tests" \
        "__tests__/api/procurement/requisition.api.test.ts" \
        "Testing API routes, authentication, authorization, and data operations"; then
        failed_tests+=("API Integration Tests")
    fi
    
    # 3. Bulk Operations Tests
    if ! run_test_suite \
        "Bulk Operations Tests" \
        "__tests__/procurement/*bulk*.test.ts" \
        "Testing bulk import/export functionality and audit trail compliance"; then
        failed_tests+=("Bulk Operations Tests")
    fi
    
    # 4. Error Handling Tests
    if ! run_test_suite \
        "Error Handling Tests" \
        "__tests__/procurement/*error*.test.ts" \
        "Testing error scenarios, recovery mechanisms, and user feedback"; then
        failed_tests+=("Error Handling Tests")
    fi
    
    # 5. Security Tests
    if ! run_test_suite \
        "Security Tests" \
        "__tests__/security/procurement*.test.ts" \
        "Testing authentication, authorization, and data validation"; then
        failed_tests+=("Security Tests")
    fi
    
    # 6. Performance Tests
    if ! run_test_suite \
        "Performance Tests" \
        "__tests__/performance/procurement*.test.ts" \
        "Testing load handling, response times, and resource usage"; then
        failed_tests+=("Performance Tests")
    fi
    
    # 7. Integration Tests
    if ! run_test_suite \
        "Cross-Module Integration Tests" \
        "__tests__/integration/procurement*.test.ts" \
        "Testing integration between procurement modules and other systems"; then
        failed_tests+=("Integration Tests")
    fi
    
    # Generate coverage report
    print_status "info" "Generating coverage report..."
    if npm test -- --coverage --coverageReporters=text --coverageReporters=html; then
        print_status "success" "Coverage report generated"
    else
        print_status "warning" "Coverage report generation failed"
    fi
    
    # Calculate execution time
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))
    
    echo ""
    echo "========================================"
    print_status "info" "Test Suite Summary"
    echo "========================================"
    echo "Execution time: ${minutes}m ${seconds}s"
    echo "Environment: $TEST_ENV"
    echo "Timestamp: $(date)"
    
    # Report results
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_status "success" "All test suites passed! 🎉"
        echo ""
        echo "✅ Component functionality verified"
        echo "✅ API integration working correctly"
        echo "✅ Bulk operations functioning properly"
        echo "✅ Error handling robust"
        echo "✅ Security measures in place"
        echo "✅ Performance within acceptable limits"
        echo "✅ Cross-module integration successful"
        echo ""
        print_status "success" "Procurement Module is ready for production! 🚀"
        exit 0
    else
        print_status "error" "Some test suites failed:"
        for test in "${failed_tests[@]}"; do
            echo "  ❌ $test"
        done
        echo ""
        print_status "error" "Please fix the failing tests before proceeding to production"
        exit 1
    fi
}

# Function to show help
show_help() {
    echo "Procurement Module Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -c, --coverage-only     Run only coverage report"
    echo "  -q, --quick             Run quick test suite (skip performance tests)"
    echo "  -v, --verbose           Verbose output"
    echo "  --component-only        Run only component tests"
    echo "  --api-only              Run only API tests"
    echo "  --security-only         Run only security tests"
    echo ""
    echo "Examples:"
    echo "  $0                      Run full test suite"
    echo "  $0 --quick              Run quick test suite"
    echo "  $0 --component-only     Run only component tests"
    echo "  $0 --coverage-only      Generate coverage report only"
    echo ""
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -c|--coverage-only)
        print_status "info" "Running coverage report only..."
        npm test -- --coverage
        exit 0
        ;;
    -q|--quick)
        print_status "info" "Running quick test suite..."
        # Skip performance and integration tests for quick run
        export QUICK_MODE=true
        ;;
    --component-only)
        print_status "info" "Running component tests only..."
        npm test -- "__tests__/procurement/requisition.test.ts"
        exit 0
        ;;
    --api-only)
        print_status "info" "Running API tests only..."
        npm test -- "__tests__/api/procurement/"
        exit 0
        ;;
    --security-only)
        print_status "info" "Running security tests only..."
        npm test -- "__tests__/security/procurement"
        exit 0
        ;;
    -v|--verbose)
        export VERBOSE=true
        ;;
esac

# Run main function
main "$@"

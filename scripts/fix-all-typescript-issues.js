const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  // Whether to create backups
  createBackups: true,
  // Whether to apply fixes
  applyFixes: true,
  // Scripts to run in order
  scripts: [
    'fix-form-types',
    'fix-unknown-types',
    'fix-catch-blocks',
    'fix-badge-variants',
    'fix-components',
    'fix-object-types',
    'auto-fix-types',
    'fix-remaining'
  ]
};

// Main function
async function main() {
  console.log('=== Running All TypeScript Issue Fixers ===\n');
  
  // Create a timestamp for the backup directory
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, '..', 'backups', `typescript-fixes-${timestamp}`);
  
  // Create backup directory if needed
  if (config.createBackups) {
    if (!fs.existsSync(path.join(__dirname, '..', 'backups'))) {
      fs.mkdirSync(path.join(__dirname, '..', 'backups'));
    }
    fs.mkdirSync(backupDir);
    console.log(`Created backup directory: ${backupDir}`);
    
    // Copy all .ts and .tsx files to the backup directory
    console.log('Creating backups of TypeScript files...');
    execSync(`xcopy /s /i "${path.join(__dirname, '..', 'components')}\\*.ts*" "${path.join(backupDir, 'components')}"`, { stdio: 'ignore' });
    execSync(`xcopy /s /i "${path.join(__dirname, '..', 'app')}\\*.ts*" "${path.join(backupDir, 'app')}"`, { stdio: 'ignore' });
    console.log('Backups created successfully.');
  }
  
  // Run each script in order
  for (const script of config.scripts) {
    console.log(`\n=== Running ${script} ===\n`);
    
    try {
      execSync(`npm run ${script}`, { stdio: 'inherit' });
      console.log(`\n✅ ${script} completed successfully.`);
    } catch (error) {
      console.error(`\n❌ ${script} failed with error: ${error.message}`);
      console.log('Continuing with next script...');
    }
  }
  
  // Run TypeScript compiler to check for remaining issues
  console.log('\n=== Checking for Remaining TypeScript Issues ===\n');
  
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('\n✅ TypeScript compilation completed successfully.');
  } catch (error) {
    console.log('\n⚠️ TypeScript compilation completed with errors.');
    console.log('Some issues may still need manual fixing.');
  }
  
  // Print summary
  console.log('\n=== Summary ===\n');
  console.log(`Ran ${config.scripts.length} fix scripts.`);
  
  if (config.createBackups) {
    console.log(`Created backups in: ${backupDir}`);
  }
  
  console.log('\n=== Next Steps ===\n');
  console.log('1. Review the changes made by the scripts.');
  console.log('2. Fix any remaining TypeScript errors manually.');
  console.log('3. Run the TypeScript compiler to check for remaining issues:');
  console.log('   npx tsc --noEmit');
}

// Run the script
main().catch(console.error);

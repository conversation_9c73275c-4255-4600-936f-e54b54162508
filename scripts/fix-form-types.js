#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix TypeScript form control type issues across all form components
 * This script applies the same type-safe solution we used for tender-form.tsx
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all form component files
const formFiles = [
  'components/procurement/forms/*.tsx',
  'components/forms/*.tsx',
  'components/*/forms/*.tsx',
  'components/*/*-form.tsx',
  'components/*/*/*-form.tsx'
];

function findFormComponents() {
  const files = [];
  formFiles.forEach(pattern => {
    const matches = glob.sync(pattern, { cwd: process.cwd() });
    files.push(...matches);
  });
  return [...new Set(files)]; // Remove duplicates
}

function hasUseFormImport(content) {
  return content.includes('import') && 
         content.includes('useForm') && 
         content.includes('react-hook-form');
}

function hasFormFieldControls(content) {
  return content.includes('FormField') && 
         content.includes('control={form.control}');
}

function fixFormTypeIssues(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Skip if already fixed or doesn't need fixing
  if (!hasUseFormImport(content) || !hasFormFieldControls(content)) {
    console.log(`  Skipped: No form issues found`);
    return;
  }

  // Check if already has type assertion
  if (content.includes('as ReturnType<typeof useForm<')) {
    console.log(`  Skipped: Already has type assertion`);
    return;
  }

  // Pattern 1: Fix useForm declaration with type assertion
  const useFormPattern = /const\s+form\s+=\s+useForm<([^>]+)>\s*\(\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}\s*\)/g;
  
  content = content.replace(useFormPattern, (match, typeParam, formConfig) => {
    modified = true;
    return `const form = useForm({${formConfig}}) as ReturnType<typeof useForm<${typeParam}>>`;
  });

  // Pattern 2: Fix useForm without explicit type parameter
  const useFormNoTypePattern = /const\s+form\s+=\s+useForm\s*\(\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}\s*\)/g;
  
  content = content.replace(useFormNoTypePattern, (match, formConfig) => {
    // Try to find the type from the schema or interface
    const typeMatch = content.match(/interface\s+(\w+FormData|\w+FormValues)/);
    if (typeMatch) {
      modified = true;
      return `const form = useForm({${formConfig}}) as ReturnType<typeof useForm<${typeMatch[1]}>>`;
    }
    return match;
  });

  // Pattern 3: Add mode: "onChange" as const if not present
  if (content.includes('resolver: zodResolver(') && !content.includes('mode:')) {
    content = content.replace(
      /(resolver:\s*zodResolver\([^)]+\))/g,
      '$1,\n    mode: "onChange" as const'
    );
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Fixed: Applied type assertion`);
  } else {
    console.log(`  ⚠️  No changes needed`);
  }
}

function main() {
  console.log('🔧 Fixing TypeScript form control type issues...\n');
  
  const formComponents = findFormComponents();
  
  if (formComponents.length === 0) {
    console.log('No form components found.');
    return;
  }

  console.log(`Found ${formComponents.length} form components:\n`);
  
  formComponents.forEach(fixFormTypeIssues);
  
  console.log('\n✅ Form type fixing completed!');
  console.log('\n📝 Summary:');
  console.log('- Applied type assertions to useForm declarations');
  console.log('- Added mode: "onChange" as const where needed');
  console.log('- All FormField controls should now have proper typing');
  console.log('\n🚀 All forms should now be TypeScript error-free!');
}

if (require.main === module) {
  main();
}

module.exports = { fixFormTypeIssues, findFormComponents };

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Whether to create backups
  createBackups: true,
  // Object type definitions
  typeDefinitions: {
    // Integration type definition
    integration: `interface Integration {
  id: string;
  name: string;
  type: string;
  provider: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  authType?: string;
  authData?: {
    token?: string;
    refreshToken?: string;
    expiresAt?: string;
    clientId?: string;
    clientSecret?: string;
    apiKey?: string;
  };
  settings?: Record<string, unknown>;
  lastSync?: string;
  syncFrequency?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  connectionStatus?: 'connected' | 'disconnected' | 'pending';
  errorMessage?: string;
}`,

    // SyncJob type definition
    syncJob: `interface SyncJob {
  id: string;
  name?: string;
  status: 'idle' | 'running' | 'paused' | 'error' | 'completed';
  source: string;
  destination: string;
  dataType: string;
  direction: 'import' | 'export' | 'bidirectional';
  schedule?: {
    frequency: string;
    startDate?: string;
    endDate?: string;
    time?: string;
    days?: string[];
  };
  lastRun?: string;
  nextRun?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  lastRunDuration?: number;
  averageDuration?: number;
  errorMessage?: string;
  errorDetails?: string;
  stats?: {
    totalRuns: number;
    recordsProcessed: number;
    successRate: number;
    errors: number;
  };
}`,

    // BudgetData type definition
    budgetData: `interface BudgetData {
  id?: string;
  name?: string;
  description?: string;
  fiscalYear?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  totalIncome: number;
  totalExpense: number;
  totalActualIncome: number;
  totalActualExpense: number;
  balance?: number;
  categories?: Array<{
    id: string;
    name: string;
    type: 'income' | 'expense';
    budgetedAmount: number;
    actualAmount: number;
    items?: Array<{
      id: string;
      name: string;
      budgetedAmount: number;
      actualAmount: number;
    }>;
  }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}`,

    // MongoFilter type definition
    mongoFilter: `interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}`
  },
  
  // Files to fix
  filesToFix: [
    // Integration files
    {
      path: 'components/accounting/integrations/integration-data-mapping.tsx',
      replacements: [
        {
          pattern: /integration: any;/g,
          replacement: 'integration: Integration;'
        },
        {
          pattern: /const \[mappings, setMappings\] = useState<Record<string, any>>\({}\);/g,
          replacement: 'const [mappings, setMappings] = useState<Record<string, Array<{ sourceField: string; targetField: string; enabled: boolean }>>>({});'
        },
        {
          pattern: /const \[availableFields, setAvailableFields\] = useState<Record<string, any>>\({}\);/g,
          replacement: 'const [availableFields, setAvailableFields] = useState<Record<string, { source?: Array<{ id: string; name: string }>; target?: Array<{ id: string; name: string }> }>>({});'
        },
        {
          insertPosition: 16, // After the interface declaration
          content: '\n' + config.typeDefinitions.integration
        }
      ]
    },
    {
      path: 'components/accounting/integrations/integration-details.tsx',
      replacements: [
        {
          pattern: /integration: any;/g,
          replacement: 'integration: Integration;'
        },
        {
          insertPosition: 28, // Before the interface
          content: config.typeDefinitions.integration + '\n\n'
        }
      ]
    },
    {
      path: 'components/accounting/integrations/integration-import-export.tsx',
      replacements: [
        {
          pattern: /integration: any;/g,
          replacement: 'integration: Integration;'
        },
        {
          insertPosition: 26, // Before the interface
          content: config.typeDefinitions.integration + '\n\n'
        }
      ]
    },
    {
      path: 'components/accounting/integrations/integration-settings.tsx',
      replacements: [
        {
          pattern: /integration: any;/g,
          replacement: 'integration: Integration;'
        },
        {
          insertPosition: 69, // Before the interface
          content: config.typeDefinitions.integration + '\n\n'
        }
      ]
    },
    
    // SyncJob files
    {
      path: 'components/accounting/synchronization/sync-job-details.tsx',
      replacements: [
        {
          pattern: /syncJob: any;/g,
          replacement: 'syncJob: SyncJob;'
        },
        {
          insertPosition: 18, // Before the interface
          content: config.typeDefinitions.syncJob + '\n\n'
        }
      ]
    },
    {
      path: 'components/accounting/synchronization/sync-job-schedule.tsx',
      replacements: [
        {
          pattern: /syncJob: any;/g,
          replacement: 'syncJob: SyncJob;'
        },
        {
          insertPosition: 39, // Before the interface
          content: config.typeDefinitions.syncJob + '\n\n'
        }
      ]
    },
    {
      path: 'components/accounting/synchronization/sync-job-settings.tsx',
      replacements: [
        {
          pattern: /syncJob: any;/g,
          replacement: 'syncJob: SyncJob;'
        },
        {
          insertPosition: 42, // Before the interface
          content: config.typeDefinitions.syncJob + '\n\n'
        }
      ]
    },
    
    // BudgetData files
    {
      path: 'components/accounting/budget/budget-performance-chart.tsx',
      replacements: [
        {
          pattern: /budgetData: any;/g,
          replacement: 'budgetData: BudgetData;'
        },
        {
          pattern: /categoryData\?: unknown\[\];/g,
          replacement: 'categoryData?: Array<{ id: string; name: string; type: string; budgetedAmount: number; actualAmount: number; total?: number }>;'
        },
        {
          pattern: /monthlyData\?: unknown\[\];/g,
          replacement: 'monthlyData?: Array<{ month: string; budgeted: number; actual: number; percentage: number }>;'
        },
        {
          insertPosition: 62, // Before the interface
          content: config.typeDefinitions.budgetData + '\n\n'
        }
      ]
    },
    {
      path: 'components/accounting/shared/report-generator.tsx',
      replacements: [
        {
          pattern: /incomeData\?: any;/g,
          replacement: 'incomeData?: Array<{ category: string; budgeted: number; actual: number; variance: number }>;'
        },
        {
          pattern: /expenseData\?: any;/g,
          replacement: 'expenseData?: Array<{ category: string; budgeted: number; actual: number; variance: number }>;'
        },
        {
          pattern: /budgetData\?: any;/g,
          replacement: 'budgetData?: BudgetData;'
        },
        {
          insertPosition: 68, // Before these properties
          content: config.typeDefinitions.budgetData + '\n\n'
        }
      ]
    },
    
    // MongoFilter files
    {
      path: 'app/api/procurement/purchase-orders/route.ts',
      replacements: [
        {
          pattern: /interface MongoFilter {\n\s*\[key: string\]: any;/g,
          replacement: config.typeDefinitions.mongoFilter.replace('interface MongoFilter {', 'interface MongoFilter {')
        }
      ]
    },
    {
      path: 'app/api/procurement/requisition/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/procurement/supplier/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/procurement/tender/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/accounting/expenditures/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/accounting/expense/by-budget/[id]/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/accounting/expense/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    },
    {
      path: 'app/api/accounting/income/by-budget/[id]/route.ts',
      replacements: [
        {
          pattern: /\[key: string\]: any;/g,
          replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;'
        }
      ]
    }
  ]
};

// Function to fix a file
function fixFile(fileConfig) {
  const filePath = path.join(__dirname, '..', fileConfig.path);
  
  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${fileConfig.path}`);
    return false;
  }
  
  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let changesApplied = false;
  
  // Apply pattern replacements
  if (fileConfig.replacements) {
    for (const replacement of fileConfig.replacements) {
      if (replacement.pattern && replacement.replacement) {
        // Apply regex replacement
        const newContent = content.replace(replacement.pattern, replacement.replacement);
        if (newContent !== content) {
          content = newContent;
          changesApplied = true;
          console.log(`Applied pattern replacement in ${fileConfig.path}`);
        }
      } else if (replacement.insertPosition !== undefined && replacement.content) {
        // Insert content at specific position
        const lines = content.split('\n');
        if (replacement.insertPosition >= 0 && replacement.insertPosition <= lines.length) {
          lines.splice(replacement.insertPosition, 0, replacement.content);
          content = lines.join('\n');
          changesApplied = true;
          console.log(`Inserted content at line ${replacement.insertPosition} in ${fileConfig.path}`);
        }
      }
    }
  }
  
  // Save changes if any were applied
  if (changesApplied) {
    // Create backup if configured
    if (config.createBackups) {
      fs.writeFileSync(`${filePath}.bak`, originalContent);
      console.log(`Created backup at ${filePath}.bak`);
    }
    
    // Write updated content
    fs.writeFileSync(filePath, content);
    console.log(`Applied fixes to ${fileConfig.path}`);
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Fixing object type issues...');
  
  let fixedFiles = 0;
  
  // Process each file
  for (const fileConfig of config.filesToFix) {
    const wasFixed = fixFile(fileConfig);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed object type issues in ${fixedFiles} out of ${config.filesToFix.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
    console.log('2. Run the component scanner to check for other issues:');
    console.log('   npm run scan-components');
  }
}

// Run the script
main();

/**
 * <PERSON><PERSON><PERSON> to drop the unique index on employeeNumber field
 * This script removes the unique constraint that's causing the duplicate key error
 * 
 * Run with: node scripts/drop-employee-number-index.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Main function to drop the index
async function dropEmployeeNumberIndex() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Employee collection
    const db = mongoose.connection.db;
    const employeeCollection = db.collection('employees');

    // List all indexes
    console.log('Listing current indexes:');
    const indexes = await employeeCollection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index)}`);
    });

    // Find the employeeNumber index
    const employeeNumberIndex = indexes.find(index => 
      index.key && index.key.employeeNumber === 1
    );

    if (employeeNumberIndex) {
      console.log(`\nFound employeeNumber index: ${employeeNumberIndex.name}`);
      
      // Drop the index
      console.log('Dropping the index...');
      await employeeCollection.dropIndex(employeeNumberIndex.name);
      console.log('Index dropped successfully');
      
      // Verify the index was dropped
      console.log('\nVerifying indexes after drop:');
      const updatedIndexes = await employeeCollection.indexes();
      updatedIndexes.forEach((index, i) => {
        console.log(`${i + 1}. ${JSON.stringify(index)}`);
      });
      
      // Check if the index was actually dropped
      const stillExists = updatedIndexes.some(index => 
        index.key && index.key.employeeNumber === 1
      );
      
      if (stillExists) {
        console.error('ERROR: Index still exists after attempting to drop it');
      } else {
        console.log('\nSUCCESS: employeeNumber index has been removed');
      }
    } else {
      console.log('\nNo employeeNumber index found');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
dropEmployeeNumberIndex().catch(console.error);

const XLSX = require('xlsx');
const path = require('path');

// <PERSON>reate corrected salary structures template data based on provided Excel
const salaryStructuresData = [
  // Header row
  [
    'GRADE',
    'SALARY IN MK',
    'Description',
    'Effective Date',
    'Currency',
    'Is Active'
  ],
  // Corrected data rows based on provided Excel with proper formatting
  [
    'TCM 1',
    '4357623.73',
    'Registrar - Highest grade in TCM hierarchy',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 2',
    '3417204.10',
    'Deputy Registrar - Second highest grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 3',
    '2563226.33',
    'Director level - Senior management grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 4',
    '1922662.21',
    'Assistant Director - Middle management grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 5',
    '1507732.10',
    'Senior Officer - Experienced professional grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 7',
    '1040466.37',
    'Officer - Professional grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 9',
    '780448.20',
    'Assistant Officer - Junior professional grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 10',
    '585409.97',
    'Senior Clerk - Experienced support staff',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 11',
    '459072.53',
    'Clerk - Support staff grade',
    '2024-01-01',
    'MWK',
    'true'
  ],
  [
    'TCM 12',
    '360000.00',
    'Office Assistant - Entry level grade',
    '2024-01-01',
    'MWK',
    'true'
  ]
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Create worksheet from the data
const ws = XLSX.utils.aoa_to_sheet(salaryStructuresData);

// Set column widths for better readability
const colWidths = [
  { wch: 15 }, // GRADE
  { wch: 20 }, // SALARY IN MK
  { wch: 40 }, // Description
  { wch: 15 }, // Effective Date
  { wch: 10 }, // Currency
  { wch: 10 }  // Is Active
];

ws['!cols'] = colWidths;

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Salary Structures');

// Create instructions sheet
const instructionsData = [
  ['Salary Structures Bulk Import Instructions - TCM Format'],
  [''],
  ['Required Fields:'],
  ['• GRADE: TCM grade code (e.g., TCM 1, TCM 2, etc.)'],
  [''],
  ['Optional Fields:'],
  ['• SALARY IN MK: Basic salary amount in Malawian Kwacha'],
  ['• Description: Description of the grade level'],
  ['• Effective Date: When the salary structure becomes effective (YYYY-MM-DD)'],
  ['• Currency: Currency code (defaults to MWK)'],
  ['• Is Active: true/false (defaults to true)'],
  [''],
  ['Data Corrections Made:'],
  ['• Removed commas from salary amounts (4,357,623.73 → 4357623.73)'],
  ['• Added proper descriptions for each TCM grade'],
  ['• Set consistent effective date (2024-01-01)'],
  ['• Added currency specification (MWK)'],
  ['• Set all grades as active'],
  ['• Used proper column headers that the system recognizes'],
  [''],
  ['TCM Grade Structure:'],
  ['• TCM 1: Registrar - Highest grade (MWK 4,357,623.73)'],
  ['• TCM 2: Deputy Registrar - Second highest (MWK 3,417,204.10)'],
  ['• TCM 3: Director level - Senior management (MWK 2,563,226.33)'],
  ['• TCM 4: Assistant Director - Middle management (MWK 1,922,662.21)'],
  ['• TCM 5: Senior Officer - Experienced professional (MWK 1,507,732.10)'],
  ['• TCM 7: Officer - Professional grade (MWK 1,040,466.37)'],
  ['• TCM 9: Assistant Officer - Junior professional (MWK 780,448.20)'],
  ['• TCM 10: Senior Clerk - Experienced support (MWK 585,409.97)'],
  ['• TCM 11: Clerk - Support staff (MWK 459,072.53)'],
  ['• TCM 12: Office Assistant - Entry level (MWK 360,000.00)'],
  [''],
  ['Important Notes:'],
  ['• The system will now SKIP existing salary structures instead of showing errors'],
  ['• You can safely re-import this file - existing items will be skipped'],
  ['• Salary amounts should be entered without commas'],
  ['• Dates should be in YYYY-MM-DD format'],
  ['• If Effective Date is not provided, current date will be used'],
  ['• If Currency is not provided, MWK will be used as default'],
  ['• Grade names must be unique across all salary structures'],
  [''],
  ['Column Mapping:'],
  ['• GRADE → Name (salary structure name)'],
  ['• SALARY IN MK → Basic Component Amount'],
  ['• Description → Description'],
  ['• Effective Date → Effective Date'],
  ['• Currency → Currency'],
  ['• Is Active → Is Active'],
  [''],
  ['Tips:'],
  ['• Test with a small batch first'],
  ['• Review the upload results for any skipped or error items'],
  ['• Existing salary structures with the same name will be skipped'],
  ['• Remove the header row if you only want to import specific structures'],
  ['• Ensure salary amounts are numeric (no commas or currency symbols)']
];

const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);

// Set column width for instructions
instructionsWs['!cols'] = [{ wch: 80 }];

// Add instructions sheet
XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions');

// Create the format_excel directory if it doesn't exist
const fs = require('fs');
const formatExcelDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(formatExcelDir)) {
  fs.mkdirSync(formatExcelDir, { recursive: true });
}

// Write the file
const filePath = path.join(formatExcelDir, 'salary_structures_tcm_corrected.xlsx');
XLSX.writeFile(wb, filePath);

console.log(`Corrected TCM salary structures Excel file created successfully at: ${filePath}`);
console.log('');
console.log('Corrections made to your data:');
console.log('1. Data formatting:');
console.log('   - Removed commas from salary amounts');
console.log('   - Standardized number format (4,357,623.73 → 4357623.73)');
console.log('   - Added proper column headers');
console.log('');
console.log('2. Added missing required data:');
console.log('   - Descriptions for each TCM grade level');
console.log('   - Effective date (2024-01-01)');
console.log('   - Currency specification (MWK)');
console.log('   - Active status (true for all grades)');
console.log('');
console.log('3. Column mapping improvements:');
console.log('   - GRADE → Name (system recognizes this)');
console.log('   - SALARY IN MK → Basic Component Amount');
console.log('   - Added support for TCM-specific column names');
console.log('');
console.log('4. System improvements:');
console.log('   - Bulk import now SKIPS existing salary structures instead of errors');
console.log('   - Flexible required fields (only name is required)');
console.log('   - Default values for missing optional fields');
console.log('   - Better error handling and progress reporting');
console.log('');
console.log('TCM Grade Structure Summary:');
console.log('• 10 salary grades from TCM 1 (Registrar) to TCM 12 (Office Assistant)');
console.log('• Salary range: MWK 360,000 to MWK 4,357,623.73');
console.log('• Hierarchical structure reflecting TCM organizational levels');
console.log('');
console.log('The file is ready for import into the TCM Enterprise Suite!');

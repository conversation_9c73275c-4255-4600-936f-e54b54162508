"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
var ts = __importStar(require("typescript"));
// Configuration for the scanner
var config = {
    // Directories to scan
    directories: ['components', 'app'],
    // File extensions to scan
    extensions: ['.tsx', '.ts'],
    // Patterns to look for
    patterns: {
        anyType: true,
        toastVariants: true,
        apiRouteAuth: true,
        nextAuthUsage: true,
        dynamicRouteParams: true,
        formControlTypeIssues: true,
        resolverTypeIssues: true,
        filterIncludesIssues: true,
    }
};
// Main function to scan components
function scanComponents() {
    return __awaiter(this, void 0, void 0, function () {
        var results, files, _i, files_1, filePath, fileContent, issues;
        return __generator(this, function (_a) {
            results = [];
            files = getAllFiles(config.directories, config.extensions);
            // Process each file
            for (_i = 0, files_1 = files; _i < files_1.length; _i++) {
                filePath = files_1[_i];
                fileContent = fs_1.default.readFileSync(filePath, 'utf8');
                issues = scanFile(filePath, fileContent);
                if (issues.length > 0) {
                    results.push({
                        filePath: filePath,
                        issues: issues
                    });
                }
            }
            return [2 /*return*/, results];
        });
    });
}
// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
    var files = [];
    for (var _i = 0, directories_1 = directories; _i < directories_1.length; _i++) {
        var dir = directories_1[_i];
        traverseDirectory(dir, function (filePath) {
            if (extensions.some(function (ext) { return filePath.endsWith(ext); })) {
                files.push(filePath);
            }
        });
    }
    return files;
}
// Traverse directory recursively
function traverseDirectory(dir, callback) {
    if (!fs_1.default.existsSync(dir)) {
        console.warn("Directory does not exist: ".concat(dir));
        return;
    }
    var items = fs_1.default.readdirSync(dir);
    for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {
        var item = items_1[_i];
        var itemPath = path_1.default.join(dir, item);
        var stats = fs_1.default.statSync(itemPath);
        if (stats.isDirectory()) {
            traverseDirectory(itemPath, callback);
        }
        else if (stats.isFile()) {
            callback(itemPath);
        }
    }
}
// Scan a single file for issues
function scanFile(filePath, content) {
    var issues = [];
    // Create source file
    var sourceFile = ts.createSourceFile(filePath, content, ts.ScriptTarget.Latest, true);
    // Check for 'any' type usage
    if (config.patterns.anyType) {
        findAnyTypeUsage(sourceFile, content, issues);
    }
    // Check for toast variant issues
    if (config.patterns.toastVariants) {
        findToastVariantIssues(sourceFile, content, issues);
    }
    // Check for API route authentication issues
    if (config.patterns.apiRouteAuth && filePath.includes('/api/')) {
        findApiAuthIssues(sourceFile, content, issues);
    }
    // Check for next-auth usage
    if (config.patterns.nextAuthUsage) {
        findNextAuthUsage(sourceFile, content, issues);
    }
    // Check for dynamic route parameter issues in Next.js 15
    if (config.patterns.dynamicRouteParams && filePath.includes('/app/')) {
        findDynamicRouteParamIssues(sourceFile, content, issues);
    }
    // Check for form control type issues
    if (config.patterns.formControlTypeIssues) {
        findFormControlTypeIssues(sourceFile, content, issues);
    }
    // Check for resolver type issues
    if (config.patterns.resolverTypeIssues) {
        findResolverTypeIssues(sourceFile, content, issues);
    }
    // Check for filter includes issues
    if (config.patterns.filterIncludesIssues) {
        findFilterIncludesIssues(sourceFile, content, issues);
    }
    return issues;
}
// Find 'any' type usage
function findAnyTypeUsage(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Simple regex pattern to find 'any' type
    var anyTypePattern = /: any(\[\])?/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = anyTypePattern.exec(line)) !== null) {
            issues.push({
                type: 'anyType',
                line: lineIndex + 1,
                column: match.index,
                message: "Avoid using 'any' type. Consider using a more specific type.",
                code: line.trim()
            });
        }
    });
}
// Find toast variant issues
function findToastVariantIssues(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find toast with variant other than 'default' or 'destructive'
    var toastVariantPattern = /variant:\s*['"](?!default|destructive)([^'"]+)['"]/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = toastVariantPattern.exec(line)) !== null) {
            issues.push({
                type: 'toastVariant',
                line: lineIndex + 1,
                column: match.index,
                message: "Toast variant '".concat(match[1], "' is not supported. Only 'default' and 'destructive' are valid."),
                code: line.trim()
            });
        }
    });
}
// Find API route authentication issues
function findApiAuthIssues(sourceFile, content, issues) {
    // Check for next-auth imports in API routes
    if (content.includes('import { getServerSession }') ||
        content.includes('import { getSession }') ||
        content.includes('from "next-auth"') ||
        content.includes('from \'next-auth\'')) {
        issues.push({
            type: 'apiAuth',
            line: 1,
            column: 0,
            message: "API route is using next-auth instead of the project's custom authentication system."
        });
    }
}
// Find next-auth usage
function findNextAuthUsage(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find next-auth imports or usage
    var nextAuthPattern = /next-auth/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = nextAuthPattern.exec(line)) !== null) {
            issues.push({
                type: 'nextAuth',
                line: lineIndex + 1,
                column: match.index,
                message: "Using next-auth instead of the project's custom authentication system.",
                code: line.trim()
            });
        }
    });
}
// Find dynamic route parameter issues in Next.js 15
function findDynamicRouteParamIssues(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find dynamic route parameters without Promise typing
    var dynamicRoutePattern = /params:\s*{\s*([^:]+):\s*string\s*}/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = dynamicRoutePattern.exec(line)) !== null) {
            issues.push({
                type: 'dynamicRouteParams',
                line: lineIndex + 1,
                column: match.index,
                message: "In Next.js 15, dynamic route parameters are passed as Promises and must be typed as { params: Promise<{ id: string }> }",
                code: line.trim()
            });
        }
    });
    // Check for incorrect Promise syntax in route handlers
    if (content.includes(': Promise<NextResponse> {') || content.includes(': Promise<Response>: Promise<NextResponse>')) {
        issues.push({
            type: 'routeHandlerPromise',
            line: 1,
            column: 0,
            message: "Incorrect Promise syntax in route handler. Should be 'Promise<NextResponse>' not 'Promise<Response>: Promise<NextResponse>'."
        });
    }
}
// Find form control type issues
function findFormControlTypeIssues(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find form control without type assertion
    var formControlPattern = /control=\{form\.control\}/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = formControlPattern.exec(line)) !== null) {
            issues.push({
                type: 'formControlType',
                line: lineIndex + 1,
                column: match.index,
                message: "Form control may have type mismatch. Consider using 'control={form.control as any}' to fix type issues.",
                code: line.trim()
            });
        }
    });
}
// Find resolver type issues
function findResolverTypeIssues(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find resolver without type assertion
    var resolverPattern = /resolver:\s*zodResolver\([^)]+\)/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = resolverPattern.exec(line)) !== null) {
            // Check if it doesn't already have 'as any'
            if (!line.includes('as any')) {
                issues.push({
                    type: 'resolverType',
                    line: lineIndex + 1,
                    column: match.index,
                    message: "Zod resolver may have type mismatch. Consider using 'resolver: zodResolver(schema) as any' to fix type issues.",
                    code: line.trim()
                });
            }
        }
    });
}
// Find filter includes issues
function findFilterIncludesIssues(sourceFile, content, issues) {
    var lines = content.split('\n');
    // Pattern to find potentially unsafe includes calls on filter values
    var filterIncludesPattern = /\.getFilterValue\(\)\s*\??\s*\.includes\(/g;
    lines.forEach(function (line, lineIndex) {
        var match;
        while ((match = filterIncludesPattern.exec(line)) !== null) {
            // Check if it doesn't already have type assertion
            if (!line.includes('as string[]') && !line.includes('as Array<')) {
                issues.push({
                    type: 'filterIncludes',
                    line: lineIndex + 1,
                    column: match.index,
                    message: "Potential 'includes' method error on filter value. Consider using '(getFilterValue() as string[] || []).includes()' pattern.",
                    code: line.trim()
                });
            }
        }
    });
}
// Run the scanner and output results
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var results, _i, results_1, result, _a, _b, issue;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    console.log('Scanning components for issues...');
                    return [4 /*yield*/, scanComponents()];
                case 1:
                    results = _c.sent();
                    console.log("\nFound issues in ".concat(results.length, " files:"));
                    // Output results
                    for (_i = 0, results_1 = results; _i < results_1.length; _i++) {
                        result = results_1[_i];
                        console.log("\n".concat(result.filePath, ":"));
                        for (_a = 0, _b = result.issues; _a < _b.length; _a++) {
                            issue = _b[_a];
                            console.log("  [".concat(issue.type, "] Line ").concat(issue.line, ": ").concat(issue.message));
                            if (issue.code) {
                                console.log("    ".concat(issue.code));
                            }
                        }
                    }
                    // Save results to file
                    fs_1.default.writeFileSync('component-scan-results.json', JSON.stringify(results, null, 2));
                    console.log('\nResults saved to component-scan-results.json');
                    return [2 /*return*/];
            }
        });
    });
}
// Run the scanner
main().catch(function (error) {
    console.error('Error running scanner:', error);
    process.exit(1);
});

/**
 * <PERSON><PERSON><PERSON> to update all payroll run totals
 * 
 * Run with: node scripts/update-all-payroll-runs.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpackhrmanager')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define PayrollRun schema
const PayrollRunSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  payPeriod: {
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    year: {
      type: Number,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'processing', 'completed', 'approved', 'paid', 'cancelled'],
    default: 'draft',
  },
  totalEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  processedEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  totalGrossSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    required: true,
    default: 0,
  },
  totalTax: {
    type: Number,
    required: true,
    default: 0,
  },
  totalNetSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    trim: true,
  },
  notes: {
    type: String,
    trim: true,
  },
  departments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedAt: {
    type: Date,
  },
  processedAt: {
    type: Date,
  },
  paidAt: {
    type: Date,
  },
}, {
  timestamps: true,
});

// Define Employee schema
const EmployeeSchema = new Schema({
  firstName: String,
  lastName: String,
  email: String,
  position: String,
  department: String,
  salary: Number,
  // Other fields...
});

// Define EmployeeSalary schema
const EmployeeSalarySchema = new Schema({
  employeeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: true,
  },
  basicSalary: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: 'MWK',
  },
  allowances: [{
    name: String,
    amount: Number,
    percentage: Number,
    isTaxable: Boolean,
  }],
  deductions: [{
    name: String,
    amount: Number,
    percentage: Number,
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  // Other fields...
});

// Create the models
const PayrollRun = mongoose.models.PayrollRun || mongoose.model('PayrollRun', PayrollRunSchema);
const Employee = mongoose.models.Employee || mongoose.model('Employee', EmployeeSchema);
const EmployeeSalary = mongoose.models.EmployeeSalary || mongoose.model('EmployeeSalary', EmployeeSalarySchema);

// Update payroll run totals
async function updatePayrollRunTotals(payrollRun) {
  try {
    console.log(`Updating totals for payroll run: ${payrollRun.name}`);
    
    // Get all employees
    const employees = await Employee.find();
    console.log(`Found ${employees.length} employees`);
    
    // Calculate totals
    let totalGrossSalary = 0;
    let totalDeductions = 0;
    let totalTax = 0;
    let totalNetSalary = 0;
    let processedEmployees = 0;
    
    for (const employee of employees) {
      // Get employee salary
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId: employee._id,
        isActive: true
      });
      
      if (!employeeSalary) {
        console.log(`No active salary found for employee ${employee.firstName} ${employee.lastName}`);
        continue;
      }
      
      // Calculate gross salary (basic + allowances)
      let grossSalary = employeeSalary.basicSalary || 0;
      let totalAllowances = 0;
      
      // Add allowances
      if (employeeSalary.allowances && employeeSalary.allowances.length > 0) {
        for (const allowance of employeeSalary.allowances) {
          let amount = 0;
          
          if (allowance.amount) {
            amount = allowance.amount;
          } else if (allowance.percentage) {
            amount = employeeSalary.basicSalary * (allowance.percentage / 100);
          }
          
          totalAllowances += amount;
        }
      }
      
      grossSalary += totalAllowances;
      
      // Calculate tax (simplified - 15% of gross)
      const taxRate = 0.15; // 15% tax rate as a fallback
      const tax = grossSalary * taxRate;
      
      // Calculate deductions
      let totalEmployeeDeductions = 0;
      
      // Add deductions
      if (employeeSalary.deductions && employeeSalary.deductions.length > 0) {
        for (const deduction of employeeSalary.deductions) {
          let amount = 0;
          
          if (deduction.amount) {
            amount = deduction.amount;
          } else if (deduction.percentage) {
            amount = employeeSalary.basicSalary * (deduction.percentage / 100);
          }
          
          totalEmployeeDeductions += amount;
        }
      }
      
      // Calculate net salary
      const netSalary = grossSalary - tax - totalEmployeeDeductions;
      
      // Update totals
      totalGrossSalary += grossSalary;
      totalTax += tax;
      totalDeductions += totalEmployeeDeductions;
      totalNetSalary += netSalary;
      processedEmployees++;
      
      console.log(`Processed employee ${employee.firstName} ${employee.lastName}: Gross=${grossSalary}, Tax=${tax}, Deductions=${totalEmployeeDeductions}, Net=${netSalary}`);
    }
    
    // Update payroll run
    const updatedPayrollRun = await PayrollRun.findByIdAndUpdate(
      payrollRun._id,
      {
        $set: {
          totalGrossSalary,
          totalDeductions,
          totalTax,
          totalNetSalary,
          processedEmployees
        }
      },
      { new: true }
    );
    
    console.log('Payroll run updated successfully:');
    console.log(`- Total Employees: ${updatedPayrollRun.totalEmployees}`);
    console.log(`- Processed Employees: ${updatedPayrollRun.processedEmployees}`);
    console.log(`- Total Gross Salary: ${updatedPayrollRun.totalGrossSalary}`);
    console.log(`- Total Deductions: ${updatedPayrollRun.totalDeductions}`);
    console.log(`- Total Tax: ${updatedPayrollRun.totalTax}`);
    console.log(`- Total Net Salary: ${updatedPayrollRun.totalNetSalary}`);
    
    return updatedPayrollRun;
  } catch (error) {
    console.error(`Error updating payroll run ${payrollRun._id}:`, error);
    return null;
  }
}

// Update all payroll runs
async function updateAllPayrollRuns() {
  try {
    // Get all payroll runs
    const payrollRuns = await PayrollRun.find({
      status: { $in: ['completed', 'approved', 'paid'] }
    });
    
    console.log(`Found ${payrollRuns.length} payroll runs to update`);
    
    // Update each payroll run
    const results = [];
    for (const payrollRun of payrollRuns) {
      const result = await updatePayrollRunTotals(payrollRun);
      results.push(result);
    }
    
    console.log(`Successfully updated ${results.filter(Boolean).length} payroll runs`);
  } catch (error) {
    console.error('Error updating all payroll runs:', error);
  } finally {
    // Close the database connection
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
updateAllPayrollRuns();

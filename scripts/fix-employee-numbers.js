/**
 * Direct database fix script for employee numbers
 * This script fixes the employeeNumber field for all employees in the database
 *
 * Run with: node scripts/fix-employee-numbers.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Define the Employee schema
const employeeSchema = new mongoose.Schema({
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  employeeNumber: {
    type: String,
    trim: true,
    unique: true,
    required: true
  },
  firstName: String,
  lastName: String,
  // Other fields not needed for migration
}, {
  timestamps: true,
  strict: false // Allow other fields in the document
});

// Create the Employee model
const Employee = mongoose.model('Employee', employeeSchema);

async function fixEmployeeNumbers() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // First, check if there are any employees with null employeeNumber
    const nullEmployeeNumbers = await Employee.countDocuments({
      $or: [
        { employeeNumber: { $exists: false } },
        { employeeNumber: null },
        { employeeNumber: "" }
      ]
    });

    console.log(`Found ${nullEmployeeNumbers} employees with null or missing employeeNumber`);

    // Let's also check for any employee records at all
    const totalEmployees = await Employee.countDocuments({});
    console.log(`Total employees in database: ${totalEmployees}`);

    // Generate a unique prefix for this batch
    const timestamp = Date.now().toString().slice(-6);
    const prefix = `EMP-FIX-${timestamp}-`;

    // Find ALL employees, not just those with null employeeNumber
    console.log('Processing ALL employee records to ensure unique employeeNumber values...');
    const employees = await Employee.find({}).sort({ createdAt: 1 }); // Sort by creation date to process oldest records first

    console.log(`Processing ${employees.length} employees...`);

    // Process each employee
    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (let i = 0; i < employees.length; i++) {
      const employee = employees[i];
      try {
        // Generate a unique employee number
        let newEmployeeNumber = employee.employeeId;

        // If employeeId is null or empty, generate a completely new number
        if (!newEmployeeNumber || newEmployeeNumber.trim() === '') {
          newEmployeeNumber = `${prefix}${(i + 1).toString().padStart(4, '0')}`;
        }

        // Check if this employeeNumber already exists
        const existingWithNumber = await Employee.findOne({
          employeeNumber: newEmployeeNumber,
          _id: { $ne: employee._id } // Exclude the current employee
        });

        // If it exists, create a truly unique number
        if (existingWithNumber) {
          // Add a random suffix to make it unique
          const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
          newEmployeeNumber = `${prefix}${(i + 1).toString().padStart(4, '0')}-${random}`;

          // Double-check that this new number is unique
          const doubleCheck = await Employee.findOne({ employeeNumber: newEmployeeNumber });
          if (doubleCheck) {
            // If still not unique, use timestamp for absolute uniqueness
            newEmployeeNumber = `${prefix}${Date.now()}-${random}`;
          }
        }

        // Update the employee
        employee.employeeNumber = newEmployeeNumber;
        await employee.save();
        successCount++;

        console.log(`Fixed employee: ${employee.firstName} ${employee.lastName} (${employee._id}) - New employeeNumber: ${newEmployeeNumber}`);
      } catch (error) {
        errorCount++;
        const errorInfo = {
          employeeId: employee.employeeId || 'unknown',
          name: `${employee.firstName} ${employee.lastName}`,
          error: error.message
        };
        errors.push(errorInfo);
        console.error(`Error fixing employee ${errorInfo.name} (${errorInfo.employeeId}):`, error.message);
      }
    }

    console.log('\nFix completed:');
    console.log(`- Total employees processed: ${employees.length}`);
    console.log(`- Successfully updated: ${successCount}`);
    console.log(`- Errors: ${errorCount}`);

    if (errors.length > 0) {
      console.log('\nError details:');
      errors.forEach((err, index) => {
        console.log(`${index + 1}. ${err.name} (${err.employeeId}): ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Fix failed:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the fix
fixEmployeeNumbers().catch(console.error);

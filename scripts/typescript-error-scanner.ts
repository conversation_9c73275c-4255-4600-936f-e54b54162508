import * as ts from 'typescript';
import * as path from 'path';
import * as fs from 'fs';

interface ErrorResult {
  filePath: string;
  line: number;
  column: number;
  code: number;
  message: string;
  category: string;
  source?: string;
}

// Configuration
const config = {
  // Root directory to scan (relative to project root)
  rootDir: './components/accounting',
  // Directories to exclude from scanning
  excludeDirs: ['node_modules', '.next', 'out', 'dist', '.git'],
  // File extensions to scan
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  // Whether to include source code in the output
  includeSource: true,
  // Number of lines of context to include before and after the error
  contextLines: 3,
  // Output file path
  outputFile: 'accounting-typescript-errors.json',
  // Categories of errors to scan for
  categories: {
    unknownTypeErrors: true,
    strictNullChecks: true,
    noImplicitAny: true,
    noUncheckedIndexedAccess: true,
    noImplicitReturns: true,
    noFallthroughCasesInSwitch: true,
    exactOptionalPropertyTypes: true,
    noImplicitThis: true,
    noImplicitOverride: true,
    noPropertyAccessFromIndexSignature: true,
    all: true, // Include all TypeScript errors
  }
};

// Get TypeScript configuration
function getTypeScriptConfig(): ts.CompilerOptions {
  const configPath = ts.findConfigFile(
    config.rootDir,
    ts.sys.fileExists,
    'tsconfig.json'
  );

  if (!configPath) {
    throw new Error("Could not find a valid 'tsconfig.json'.");
  }

  const { config: tsConfig } = ts.readConfigFile(configPath, ts.sys.readFile);
  const { options } = ts.parseJsonConfigFileContent(
    tsConfig,
    ts.sys,
    path.dirname(configPath)
  );

  // Enable strict type checking options
  return {
    ...options,
    strict: true,
    noImplicitAny: true,
    strictNullChecks: true,
    noImplicitReturns: true,
    noFallthroughCasesInSwitch: true,
    noUncheckedIndexedAccess: true,
    exactOptionalPropertyTypes: true,
    noImplicitThis: true,
    noImplicitOverride: true,
    noPropertyAccessFromIndexSignature: true,
  };
}

// Get all files to scan
function getFilesToScan(): string[] {
  const files: string[] = [];

  function traverseDirectory(dirPath: string): void {
    if (config.excludeDirs.some(excludeDir => dirPath.includes(excludeDir))) {
      return;
    }

    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        traverseDirectory(fullPath);
      } else if (
        entry.isFile() &&
        config.extensions.some(ext => entry.name.endsWith(ext))
      ) {
        files.push(fullPath);
      }
    }
  }

  traverseDirectory(config.rootDir);
  return files;
}

// Get source code context for an error
function getSourceContext(filePath: string, line: number): string | undefined {
  if (!config.includeSource) {
    return undefined;
  }

  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n');

    const startLine = Math.max(0, line - config.contextLines - 1);
    const endLine = Math.min(lines.length - 1, line + config.contextLines - 1);

    let context = '';
    for (let i = startLine; i <= endLine; i++) {
      const lineNumber = i + 1;
      const prefix = lineNumber === line ? '> ' : '  ';
      context += `${prefix}${lineNumber}: ${lines[i]}\n`;
    }

    return context;
  } catch (error) {
    return undefined;
  }
}

// Categorize TypeScript errors
function categorizeError(code: number): string {
  // Common TypeScript error codes
  const errorCategories: Record<number, string> = {
    2531: 'Object is possibly null',
    2532: 'Object is possibly undefined',
    2533: 'Object is possibly null or undefined',
    2322: 'Type assignment error',
    2339: 'Property does not exist on type',
    2345: 'Argument type mismatch',
    2571: 'Object is of type unknown',
    2554: 'Expected parameters mismatch',
    2741: 'Property missing in type',
    7006: 'Parameter implicitly has an any type',
    7031: 'Binding element implicitly has an any type',
    2365: 'Operator cannot be applied to types',
    2366: 'Function lacks ending return statement',
    2367: 'Switch case fall-through',
    2588: 'Cannot assign to readonly property',
    2540: 'Cannot assign to property (private)',
    2551: 'Property does not exist on type (indexed access)',
    2349: 'This expression is not callable',
    2352: 'Type conversion error',
    2353: 'Object literal may only specify known properties',
    2354: 'Union type error',
    2461: 'Type instantiation error',
    2559: 'Type has no construct signatures',
    2769: 'No overload matches this call',
    2739: 'Type missing properties from type',
    7053: 'Element implicitly has an any type',
  };

  return errorCategories[code] || `TypeScript error (${code})`;
}

// Scan files for TypeScript errors
function scanFiles(): ErrorResult[] {
  const compilerOptions = getTypeScriptConfig();
  const files = getFilesToScan();
  const errors: ErrorResult[] = [];

  // Create a program
  const program = ts.createProgram(files, compilerOptions);
  const checker = program.getTypeChecker();

  // Get all diagnostics
  const diagnostics = [
    ...program.getSemanticDiagnostics(),
    ...program.getSyntacticDiagnostics(),
    ...program.getDeclarationDiagnostics(),
  ];

  // Process each diagnostic
  for (const diagnostic of diagnostics) {
    if (diagnostic.file) {
      const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(
        diagnostic.start!
      );

      const filePath = diagnostic.file.fileName;
      const relativePath = path.relative(process.cwd(), filePath);
      const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
      const category = categorizeError(diagnostic.code);
      const source = getSourceContext(filePath, line + 1);

      errors.push({
        filePath: relativePath,
        line: line + 1,
        column: character + 1,
        code: diagnostic.code,
        message,
        category,
        source,
      });
    }
  }

  return errors;
}

// Main function
async function main() {
  console.log('Scanning for TypeScript errors...');

  try {
    const errors = scanFiles();

    // Group errors by file
    const errorsByFile: Record<string, ErrorResult[]> = {};
    for (const error of errors) {
      if (!errorsByFile[error.filePath]) {
        errorsByFile[error.filePath] = [];
      }
      errorsByFile[error.filePath].push(error);
    }

    // Sort errors by file and line number
    for (const filePath in errorsByFile) {
      errorsByFile[filePath].sort((a, b) => a.line - b.line);
    }

    // Write results to file
    fs.writeFileSync(
      config.outputFile,
      JSON.stringify({ errors, errorsByFile }, null, 2)
    );

    // Print summary
    console.log(`\nFound ${errors.length} TypeScript errors in ${Object.keys(errorsByFile).length} files.`);
    console.log(`Results saved to ${config.outputFile}`);

    // Print top error categories
    const categoryCounts: Record<string, number> = {};
    for (const error of errors) {
      categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;
    }

    console.log('\nTop error categories:');
    Object.entries(categoryCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([category, count]) => {
        console.log(`- ${category}: ${count} occurrences`);
      });

  } catch (error) {
    console.error('Error scanning for TypeScript errors:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
var ts = __importStar(require("typescript"));
var path = __importStar(require("path"));
var fs = __importStar(require("fs"));
// Configuration
var config = {
    // Root directory to scan (relative to project root)
    rootDir: './components/accounting',
    // Directories to exclude from scanning
    excludeDirs: ['node_modules', '.next', 'out', 'dist', '.git'],
    // File extensions to scan
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    // Whether to include source code in the output
    includeSource: true,
    // Number of lines of context to include before and after the error
    contextLines: 3,
    // Output file path
    outputFile: 'accounting-typescript-errors.json',
    // Categories of errors to scan for
    categories: {
        unknownTypeErrors: true,
        strictNullChecks: true,
        noImplicitAny: true,
        noUncheckedIndexedAccess: true,
        noImplicitReturns: true,
        noFallthroughCasesInSwitch: true,
        exactOptionalPropertyTypes: true,
        noImplicitThis: true,
        noImplicitOverride: true,
        noPropertyAccessFromIndexSignature: true,
        all: true, // Include all TypeScript errors
    }
};
// Get TypeScript configuration
function getTypeScriptConfig() {
    var configPath = ts.findConfigFile(config.rootDir, ts.sys.fileExists, 'tsconfig.json');
    if (!configPath) {
        throw new Error("Could not find a valid 'tsconfig.json'.");
    }
    var tsConfig = ts.readConfigFile(configPath, ts.sys.readFile).config;
    var options = ts.parseJsonConfigFileContent(tsConfig, ts.sys, path.dirname(configPath)).options;
    // Enable strict type checking options
    return __assign(__assign({}, options), { strict: true, noImplicitAny: true, strictNullChecks: true, noImplicitReturns: true, noFallthroughCasesInSwitch: true, noUncheckedIndexedAccess: true, exactOptionalPropertyTypes: true, noImplicitThis: true, noImplicitOverride: true, noPropertyAccessFromIndexSignature: true });
}
// Get all files to scan
function getFilesToScan() {
    var files = [];
    function traverseDirectory(dirPath) {
        if (config.excludeDirs.some(function (excludeDir) { return dirPath.includes(excludeDir); })) {
            return;
        }
        var entries = fs.readdirSync(dirPath, { withFileTypes: true });
        var _loop_1 = function (entry) {
            var fullPath = path.join(dirPath, entry.name);
            if (entry.isDirectory()) {
                traverseDirectory(fullPath);
            }
            else if (entry.isFile() &&
                config.extensions.some(function (ext) { return entry.name.endsWith(ext); })) {
                files.push(fullPath);
            }
        };
        for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
            var entry = entries_1[_i];
            _loop_1(entry);
        }
    }
    traverseDirectory(config.rootDir);
    return files;
}
// Get source code context for an error
function getSourceContext(filePath, line) {
    if (!config.includeSource) {
        return undefined;
    }
    try {
        var fileContent = fs.readFileSync(filePath, 'utf8');
        var lines = fileContent.split('\n');
        var startLine = Math.max(0, line - config.contextLines - 1);
        var endLine = Math.min(lines.length - 1, line + config.contextLines - 1);
        var context = '';
        for (var i = startLine; i <= endLine; i++) {
            var lineNumber = i + 1;
            var prefix = lineNumber === line ? '> ' : '  ';
            context += "".concat(prefix).concat(lineNumber, ": ").concat(lines[i], "\n");
        }
        return context;
    }
    catch (error) {
        return undefined;
    }
}
// Categorize TypeScript errors
function categorizeError(code) {
    // Common TypeScript error codes
    var errorCategories = {
        2531: 'Object is possibly null',
        2532: 'Object is possibly undefined',
        2533: 'Object is possibly null or undefined',
        2322: 'Type assignment error',
        2339: 'Property does not exist on type',
        2345: 'Argument type mismatch',
        2571: 'Object is of type unknown',
        2554: 'Expected parameters mismatch',
        2741: 'Property missing in type',
        7006: 'Parameter implicitly has an any type',
        7031: 'Binding element implicitly has an any type',
        2365: 'Operator cannot be applied to types',
        2366: 'Function lacks ending return statement',
        2367: 'Switch case fall-through',
        2588: 'Cannot assign to readonly property',
        2540: 'Cannot assign to property (private)',
        2551: 'Property does not exist on type (indexed access)',
        2349: 'This expression is not callable',
        2352: 'Type conversion error',
        2353: 'Object literal may only specify known properties',
        2354: 'Union type error',
        2461: 'Type instantiation error',
        2559: 'Type has no construct signatures',
        2769: 'No overload matches this call',
        2739: 'Type missing properties from type',
        7053: 'Element implicitly has an any type',
    };
    return errorCategories[code] || "TypeScript error (".concat(code, ")");
}
// Scan files for TypeScript errors
function scanFiles() {
    var compilerOptions = getTypeScriptConfig();
    var files = getFilesToScan();
    var errors = [];
    // Create a program
    var program = ts.createProgram(files, compilerOptions);
    var checker = program.getTypeChecker();
    // Get all diagnostics
    var diagnostics = __spreadArray(__spreadArray(__spreadArray([], program.getSemanticDiagnostics(), true), program.getSyntacticDiagnostics(), true), program.getDeclarationDiagnostics(), true);
    // Process each diagnostic
    for (var _i = 0, diagnostics_1 = diagnostics; _i < diagnostics_1.length; _i++) {
        var diagnostic = diagnostics_1[_i];
        if (diagnostic.file) {
            var _a = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start), line = _a.line, character = _a.character;
            var filePath = diagnostic.file.fileName;
            var relativePath = path.relative(process.cwd(), filePath);
            var message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
            var category = categorizeError(diagnostic.code);
            var source = getSourceContext(filePath, line + 1);
            errors.push({
                filePath: relativePath,
                line: line + 1,
                column: character + 1,
                code: diagnostic.code,
                message: message,
                category: category,
                source: source,
            });
        }
    }
    return errors;
}
// Main function
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var errors, errorsByFile, _i, errors_1, error, filePath, categoryCounts, _a, errors_2, error;
        return __generator(this, function (_b) {
            console.log('Scanning for TypeScript errors...');
            try {
                errors = scanFiles();
                errorsByFile = {};
                for (_i = 0, errors_1 = errors; _i < errors_1.length; _i++) {
                    error = errors_1[_i];
                    if (!errorsByFile[error.filePath]) {
                        errorsByFile[error.filePath] = [];
                    }
                    errorsByFile[error.filePath].push(error);
                }
                // Sort errors by file and line number
                for (filePath in errorsByFile) {
                    errorsByFile[filePath].sort(function (a, b) { return a.line - b.line; });
                }
                // Write results to file
                fs.writeFileSync(config.outputFile, JSON.stringify({ errors: errors, errorsByFile: errorsByFile }, null, 2));
                // Print summary
                console.log("\nFound ".concat(errors.length, " TypeScript errors in ").concat(Object.keys(errorsByFile).length, " files."));
                console.log("Results saved to ".concat(config.outputFile));
                categoryCounts = {};
                for (_a = 0, errors_2 = errors; _a < errors_2.length; _a++) {
                    error = errors_2[_a];
                    categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;
                }
                console.log('\nTop error categories:');
                Object.entries(categoryCounts)
                    .sort(function (a, b) { return b[1] - a[1]; })
                    .slice(0, 10)
                    .forEach(function (_a) {
                    var category = _a[0], count = _a[1];
                    console.log("- ".concat(category, ": ").concat(count, " occurrences"));
                });
            }
            catch (error) {
                console.error('Error scanning for TypeScript errors:', error);
                process.exit(1);
            }
            return [2 /*return*/];
        });
    });
}
// Run the script
main().catch(console.error);

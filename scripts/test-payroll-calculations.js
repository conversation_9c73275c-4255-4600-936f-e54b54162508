// Test script to verify payroll calculations for both services
const mongoose = require('mongoose');

// Mock data for testing
const mockEmployeeSalary = {
  employeeId: new mongoose.Types.ObjectId(),
  basicSalary: 100000, // MWK 100,000
  pensionContributionRate: 5, // 5%
  components: [
    {
      name: 'Housing Allowance',
      type: 'earning',
      amount: 20, // 20% of basic
      isPercentage: true,
      percentageOf: 'basic',
      isTaxable: true,
      isPensionable: false,
      isStatutory: false
    },
    {
      name: 'Transport Allowance',
      type: 'earning',
      amount: 15000, // Fixed amount
      isPercentage: false,
      isTaxable: true,
      isPensionable: false,
      isStatutory: false
    },
    {
      name: 'Medical Deduction',
      type: 'deduction',
      amount: 5000, // Fixed amount
      isPercentage: false,
      isTaxable: false,
      isPensionable: false,
      isStatutory: false
    }
  ],
  bankName: 'Test Bank',
  bankAccountNumber: '**********',
  paymentMethod: 'bank_transfer'
};

const mockTaxConfiguration = {
  brackets: [
    { min: 0, max: 50000, rate: 0 },      // 0% for first 50,000
    { min: 50000, max: 100000, rate: 15 }, // 15% for 50,001 - 100,000
    { min: 100000, max: 0, rate: 30 }      // 30% for above 100,000
  ],
  personalRelief: 10000, // MWK 10,000 personal relief
  pensionDeductible: true,
  pensionDeductionLimit: null, // No limit
  otherDeductions: []
};

// Test calculation function
function testPayrollCalculation() {
  console.log('🧮 PAYROLL CALCULATION TEST');
  console.log('=' .repeat(50));
  
  // Manual calculation for verification
  console.log('\n📊 INPUT DATA:');
  console.log(`Basic Salary: MWK ${mockEmployeeSalary.basicSalary.toLocaleString()}`);
  console.log(`Pension Rate: ${mockEmployeeSalary.pensionContributionRate}%`);
  console.log(`Components: ${mockEmployeeSalary.components.length}`);
  
  // Calculate earnings
  let totalEarnings = mockEmployeeSalary.basicSalary;
  console.log('\n💰 EARNINGS CALCULATION:');
  console.log(`Basic Salary: MWK ${mockEmployeeSalary.basicSalary.toLocaleString()}`);
  
  for (const component of mockEmployeeSalary.components) {
    if (component.type === 'earning') {
      let amount = component.amount;
      if (component.isPercentage) {
        amount = mockEmployeeSalary.basicSalary * (component.amount / 100);
      }
      totalEarnings += amount;
      console.log(`${component.name}: MWK ${amount.toLocaleString()}`);
    }
  }
  
  console.log(`GROSS SALARY: MWK ${totalEarnings.toLocaleString()}`);
  
  // Calculate deductions
  let totalDeductions = 0;
  console.log('\n📉 DEDUCTIONS CALCULATION:');
  
  // Non-statutory deductions first
  for (const component of mockEmployeeSalary.components) {
    if (component.type === 'deduction') {
      let amount = component.amount;
      if (component.isPercentage) {
        amount = mockEmployeeSalary.basicSalary * (component.amount / 100);
      }
      totalDeductions += amount;
      console.log(`${component.name}: MWK ${amount.toLocaleString()}`);
    }
  }
  
  // Pension calculation
  const pensionableAmount = mockEmployeeSalary.basicSalary; // Only basic salary is pensionable
  const pensionContribution = pensionableAmount * (mockEmployeeSalary.pensionContributionRate / 100);
  totalDeductions += pensionContribution;
  console.log(`Pension Contribution (${mockEmployeeSalary.pensionContributionRate}%): MWK ${pensionContribution.toLocaleString()}`);
  
  // Tax calculation
  let taxableAmount = totalEarnings;
  
  // Apply pension deduction if deductible
  if (mockTaxConfiguration.pensionDeductible) {
    taxableAmount -= pensionContribution;
    console.log(`Taxable Amount (after pension): MWK ${taxableAmount.toLocaleString()}`);
  }
  
  // Progressive tax calculation
  let tax = 0;
  let processedAmount = 0;
  
  console.log('\n🏛️ TAX CALCULATION (Progressive):');
  for (const bracket of mockTaxConfiguration.brackets) {
    const min = bracket.min;
    const max = bracket.max === 0 ? Infinity : bracket.max;
    const rate = bracket.rate / 100;
    
    if (taxableAmount > min) {
      const amountInBracket = Math.min(taxableAmount - processedAmount, max - min);
      if (amountInBracket > 0) {
        const taxForBracket = amountInBracket * rate;
        tax += taxForBracket;
        processedAmount += amountInBracket;
        
        console.log(`Bracket ${min.toLocaleString()} - ${max === Infinity ? '∞' : max.toLocaleString()} (${bracket.rate}%): MWK ${amountInBracket.toLocaleString()} × ${bracket.rate}% = MWK ${taxForBracket.toLocaleString()}`);
      }
    }
    
    if (processedAmount >= taxableAmount) break;
  }
  
  console.log(`Tax before relief: MWK ${tax.toLocaleString()}`);
  
  // Apply personal relief
  tax = Math.max(0, tax - mockTaxConfiguration.personalRelief);
  console.log(`Personal Relief: MWK ${mockTaxConfiguration.personalRelief.toLocaleString()}`);
  console.log(`Final Tax: MWK ${tax.toLocaleString()}`);
  
  totalDeductions += tax;
  
  // Net salary
  const netSalary = totalEarnings - totalDeductions;
  
  console.log('\n📋 SUMMARY:');
  console.log(`Gross Salary: MWK ${totalEarnings.toLocaleString()}`);
  console.log(`Total Deductions: MWK ${totalDeductions.toLocaleString()}`);
  console.log(`  - Pension: MWK ${pensionContribution.toLocaleString()}`);
  console.log(`  - Tax: MWK ${tax.toLocaleString()}`);
  console.log(`  - Other: MWK ${(totalDeductions - pensionContribution - tax).toLocaleString()}`);
  console.log(`NET SALARY: MWK ${netSalary.toLocaleString()}`);
  
  console.log('\n✅ Test completed successfully!');
  console.log('This calculation should match the payroll service output.');
}

// Test progressive tax calculation function
function testProgressiveTaxCalculation() {
  console.log('\n🧮 PROGRESSIVE TAX CALCULATION TEST');
  console.log('=' .repeat(50));

  const testCases = [
    { taxableAmount: 100000, expectedTax: 0, description: 'Below tax threshold' },
    { taxableAmount: 200000, expectedTax: 12500, description: 'First bracket only' },
    { taxableAmount: 600000, expectedTax: 117500, description: 'Two brackets' },
    { taxableAmount: 3000000, expectedTax: 860000, description: 'All brackets' } // Corrected expected value
  ];

  // Malawi PAYE tax brackets (2024 rates)
  const taxBrackets = [
    { min: 0, max: 150000, rate: 0 },           // 0% for first MWK 150,000
    { min: 150000, max: 500000, rate: 25 },     // 25% for MWK 150,001 - 500,000
    { min: 500000, max: 2550000, rate: 30 },    // 30% for MWK 500,001 - 2,550,000
    { min: 2550000, max: Infinity, rate: 35 }   // 35% for above MWK 2,550,000
  ];

  function calculateProgressiveTax(taxableAmount) {
    let tax = 0;
    let processedAmount = 0;

    for (const bracket of taxBrackets) {
      const min = bracket.min;
      const max = bracket.max === Infinity ? Infinity : bracket.max;
      const rate = bracket.rate / 100;

      if (taxableAmount > min) {
        const amountInBracket = Math.min(taxableAmount - processedAmount, max - min);

        if (amountInBracket > 0) {
          tax += amountInBracket * rate;
          processedAmount += amountInBracket;
        }
      }

      if (processedAmount >= taxableAmount) break;
    }

    return Math.round(tax * 100) / 100;
  }

  testCases.forEach((testCase, index) => {
    const calculatedTax = calculateProgressiveTax(testCase.taxableAmount);
    const isCorrect = Math.abs(calculatedTax - testCase.expectedTax) < 0.01;

    console.log(`\nTest ${index + 1}: ${testCase.description}`);
    console.log(`Taxable Amount: MWK ${testCase.taxableAmount.toLocaleString()}`);
    console.log(`Expected Tax: MWK ${testCase.expectedTax.toLocaleString()}`);
    console.log(`Calculated Tax: MWK ${calculatedTax.toLocaleString()}`);
    console.log(`Result: ${isCorrect ? '✅ PASS' : '❌ FAIL'}`);
  });

  console.log('\n✅ Progressive tax calculation test completed!');
}

// Run the tests
testPayrollCalculation();
testProgressiveTaxCalculation();

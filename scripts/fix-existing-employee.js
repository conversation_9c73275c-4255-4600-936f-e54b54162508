/**
 * Emergency fix script to update existing employee records
 * This script adds employeeNumber to existing records that don't have it
 * 
 * Run with: node scripts/fix-existing-employee.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Connect to MongoDB directly
async function fixEmployees() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Employee collection
    const db = mongoose.connection.db;
    const employeeCollection = db.collection('employees');

    // Find all employees without employeeNumber field
    const employees = await employeeCollection.find({
      employeeNumber: { $exists: false }
    }).toArray();

    console.log(`Found ${employees.length} employees without employeeNumber field`);

    // Update each employee
    let successCount = 0;
    let errorCount = 0;

    for (const employee of employees) {
      try {
        // Use employeeId as employeeNumber
        const employeeNumber = employee.employeeId || `EMP-FIX-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        
        // Update the employee directly in the database
        const result = await employeeCollection.updateOne(
          { _id: employee._id },
          { $set: { employeeNumber: employeeNumber } }
        );

        if (result.modifiedCount === 1) {
          successCount++;
          console.log(`Updated employee ${employee.firstName} ${employee.lastName} (${employee._id}): employeeNumber = ${employeeNumber}`);
        } else {
          errorCount++;
          console.error(`Failed to update employee ${employee.firstName} ${employee.lastName} (${employee._id})`);
        }
      } catch (error) {
        errorCount++;
        console.error(`Error updating employee ${employee.firstName} ${employee.lastName} (${employee._id}):`, error.message);
      }
    }

    console.log('\nFix completed:');
    console.log(`- Total employees processed: ${employees.length}`);
    console.log(`- Successfully updated: ${successCount}`);
    console.log(`- Errors: ${errorCount}`);

  } catch (error) {
    console.error('Fix failed:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the fix
fixEmployees().catch(console.error);

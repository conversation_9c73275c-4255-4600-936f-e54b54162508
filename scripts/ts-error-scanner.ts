/**
 * TypeScript Error Scanner
 * 
 * This script uses the TypeScript compiler API to scan for errors in TypeScript files.
 * It provides more accurate error detection than simple regex-based scanning.
 */

import * as ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';

interface ErrorResult {
  filePath: string;
  errors: ts.Diagnostic[];
}

// Function to scan a file for TypeScript errors
function scanFileForErrors(filePath: string, compilerOptions: ts.CompilerOptions): Error<PERSON><PERSON>ult {
  // Create a program
  const program = ts.createProgram([filePath], compilerOptions);
  
  // Get pre-emit diagnostics
  const diagnostics = ts.getPreEmitDiagnostics(program);
  
  return {
    filePath,
    errors: diagnostics.filter(d => d.file && d.file.fileName === filePath),
  };
}

// Function to scan a directory recursively for TypeScript files
function scanDirectory(dirPath: string, compilerOptions: ts.CompilerOptions): ErrorResult[] {
  const results: ErrorResult[] = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      results.push(...scanDirectory(fullPath, compilerOptions));
    } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
      const result = scanFileForErrors(fullPath, compilerOptions);
      if (result.errors.length > 0) {
        results.push(result);
      }
    }
  }

  return results;
}

// Function to format diagnostic messages
function formatDiagnostic(diagnostic: ts.Diagnostic): string {
  if (diagnostic.file) {
    const { line, character } = ts.getLineAndCharacterOfPosition(diagnostic.file, diagnostic.start!);
    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
    return `Line ${line + 1}, Col ${character + 1}: ${message}`;
  } else {
    return ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
  }
}

// Function to categorize errors
function categorizeErrors(results: ErrorResult[]): Record<string, number> {
  const categories: Record<string, number> = {};
  
  for (const result of results) {
    for (const error of result.errors) {
      const message = ts.flattenDiagnosticMessageText(error.messageText, '\n');
      
      // Categorize by common error patterns
      if (message.includes("Type 'null' is not assignable to type")) {
        categories['NULL_TYPE_ERROR'] = (categories['NULL_TYPE_ERROR'] || 0) + 1;
      } else if (message.includes("Property") && message.includes("does not exist on type")) {
        categories['PROPERTY_NOT_EXIST'] = (categories['PROPERTY_NOT_EXIST'] || 0) + 1;
      } else if (message.includes("Type 'unknown' is not assignable to type")) {
        categories['UNKNOWN_TYPE_ERROR'] = (categories['UNKNOWN_TYPE_ERROR'] || 0) + 1;
      } else if (message.includes("initialFocus")) {
        categories['INITIAL_FOCUS_ERROR'] = (categories['INITIAL_FOCUS_ERROR'] || 0) + 1;
      } else {
        categories['OTHER'] = (categories['OTHER'] || 0) + 1;
      }
    }
  }
  
  return categories;
}

// Main function to run the error scanner
async function main() {
  const args = process.argv.slice(2);
  const dirPath = args[0] || '.';
  
  console.log(`Scanning directory: ${dirPath}`);
  
  // Read tsconfig.json
  let compilerOptions: ts.CompilerOptions = {
    target: ts.ScriptTarget.ES2020,
    module: ts.ModuleKind.ESNext,
    jsx: ts.JsxEmit.React,
    strict: true,
  };
  
  try {
    const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
      compilerOptions = { ...compilerOptions, ...tsconfig.compilerOptions };
    }
  } catch (error) {
    console.warn('Failed to read tsconfig.json, using default compiler options');
  }
  
  // Scan for errors
  const results = scanDirectory(dirPath, compilerOptions);
  
  // Count total errors
  let totalErrors = 0;
  for (const result of results) {
    totalErrors += result.errors.length;
  }
  
  console.log(`Found ${totalErrors} errors in ${results.length} files`);
  
  // Categorize errors
  const categories = categorizeErrors(results);
  console.log('\nError Categories:');
  for (const [category, count] of Object.entries(categories)) {
    console.log(`  ${category}: ${count}`);
  }
  
  // Print errors
  for (const result of results) {
    console.log(`\nFile: ${result.filePath}`);
    for (const error of result.errors) {
      console.log(`  ${formatDiagnostic(error)}`);
    }
  }
}

// Run the main function
main().catch(console.error);

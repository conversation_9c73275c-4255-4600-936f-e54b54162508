const XLSX = require('xlsx');
const path = require('path');

// Create allowances data with complete information
const allowancesData = [
  // Header row
  [
    'Name',
    'Code', 
    'Description',
    'Is Active',
    'Is Taxable',
    'Is Fixed',
    'Default Amount',
    'Default Percentage',
    'Calculation Base',
    'Applicable Roles',
    'Applicable Departments'
  ],
  // Data rows
  [
    'Housing Allowance',
    'HOUSING',
    'Housing allowance for employees',
    'true',
    'true',
    'false',
    '',
    '15',
    'basic',
    'all',
    ''
  ],
  [
    'Transport Allowance',
    'TRANSPORT',
    'Transport allowance for employees',
    'true',
    'true',
    'true',
    '25000',
    '',
    '',
    'all',
    ''
  ],
  [
    'Fuel Allowance',
    'FUEL',
    'Fuel allowance for employees',
    'true',
    'true',
    'true',
    '15000',
    '',
    '',
    'all',
    ''
  ],
  [
    'Airtime Allowance',
    'AIRTIME',
    'Airtime allowance for employees',
    'true',
    'false',
    'true',
    '5000',
    '',
    '',
    'all',
    ''
  ],
  [
    'Leave Grant',
    'LEAVE_GRANT',
    'Leave grant for employees',
    'true',
    'false',
    'true',
    '10000',
    '',
    '',
    'all',
    ''
  ],
  [
    'Cellphone',
    'CELLPHONE',
    'Cellphone allowance for employees',
    'true',
    'false',
    'true',
    '3000',
    '',
    '',
    'all',
    ''
  ]
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Create worksheet from the data
const ws = XLSX.utils.aoa_to_sheet(allowancesData);

// Set column widths for better readability
const colWidths = [
  { wch: 20 }, // Name
  { wch: 15 }, // Code
  { wch: 35 }, // Description
  { wch: 10 }, // Is Active
  { wch: 12 }, // Is Taxable
  { wch: 10 }, // Is Fixed
  { wch: 15 }, // Default Amount
  { wch: 18 }, // Default Percentage
  { wch: 18 }, // Calculation Base
  { wch: 18 }, // Applicable Roles
  { wch: 22 }  // Applicable Departments
];

ws['!cols'] = colWidths;

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Allowances');

// Create the format_excel directory if it doesn't exist
const fs = require('fs');
const formatExcelDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(formatExcelDir)) {
  fs.mkdirSync(formatExcelDir, { recursive: true });
}

// Write the file
const filePath = path.join(formatExcelDir, 'allowances_complete_data.xlsx');
XLSX.writeFile(wb, filePath);

console.log(`Excel file created successfully at: ${filePath}`);
console.log('This file contains the complete allowances data with all required fields filled in.');

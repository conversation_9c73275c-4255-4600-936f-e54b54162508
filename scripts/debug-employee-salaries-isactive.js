// Script to debug employee salaries isActive values by calling the API directly
const { connectToDatabase } = require('../lib/backend/database');
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;
const Employee = require('../models/Employee').default;

async function debugEmployeeSalariesIsActive() {
  try {
    await connectToDatabase();
    
    console.log('🔍 DEBUGGING EMPLOYEE SALARIES isActive VALUES\n');
    console.log('=' .repeat(80));
    
    // Get all employee salaries with populated employee data
    const employeeSalaries = await EmployeeSalary.find({})
      .populate('employeeId', 'firstName lastName employeeId employeeNumber')
      .sort({ 'employeeId.lastName': 1 })
      .lean();
    
    console.log(`📊 Total Employee Salary Records Found: ${employeeSalaries.length}\n`);
    
    // Count active vs inactive
    let activeCount = 0;
    let inactiveCount = 0;
    
    // Group by employee to see multiple salary records
    const employeeGroups = {};
    
    employeeSalaries.forEach(salary => {
      const employeeId = salary.employeeId?._id?.toString() || 'unknown';
      const employeeName = salary.employeeId 
        ? `${salary.employeeId.firstName || ''} ${salary.employeeId.lastName || ''}`.trim()
        : 'Unknown Employee';
      
      if (!employeeGroups[employeeId]) {
        employeeGroups[employeeId] = {
          name: employeeName,
          employeeNumber: salary.employeeId?.employeeNumber || salary.employeeId?.employeeId || 'N/A',
          salaries: []
        };
      }
      
      employeeGroups[employeeId].salaries.push(salary);
      
      if (salary.isActive) {
        activeCount++;
      } else {
        inactiveCount++;
      }
    });
    
    console.log(`✅ Active Salary Records: ${activeCount}`);
    console.log(`❌ Inactive Salary Records: ${inactiveCount}`);
    console.log(`👥 Unique Employees: ${Object.keys(employeeGroups).length}\n`);
    
    console.log('📋 DETAILED BREAKDOWN BY EMPLOYEE:');
    console.log('=' .repeat(80));
    
    Object.entries(employeeGroups).forEach(([employeeId, group], index) => {
      console.log(`\n${index + 1}. 👤 ${group.name} (${group.employeeNumber})`);
      console.log(`   Employee ID: ${employeeId}`);
      console.log(`   Salary Records: ${group.salaries.length}`);
      
      group.salaries.forEach((salary, salaryIndex) => {
        const status = salary.isActive ? '✅ ACTIVE' : '❌ INACTIVE';
        const effectiveDate = new Date(salary.effectiveDate).toLocaleDateString();
        const endDate = salary.endDate ? new Date(salary.endDate).toLocaleDateString() : 'None';
        const basicSalary = salary.basicSalary?.toLocaleString() || '0';
        
        console.log(`   ${salaryIndex + 1}. ${status}`);
        console.log(`      Salary ID: ${salary._id}`);
        console.log(`      Basic Salary: MWK ${basicSalary}`);
        console.log(`      Effective Date: ${effectiveDate}`);
        console.log(`      End Date: ${endDate}`);
        console.log(`      Currency: ${salary.currency || 'MWK'}`);
        console.log(`      Created: ${new Date(salary.createdAt).toLocaleDateString()}`);
        console.log(`      Updated: ${new Date(salary.updatedAt).toLocaleDateString()}`);
        
        if (salary.notes) {
          console.log(`      Notes: ${salary.notes}`);
        }
        
        console.log('');
      });
    });
    
    console.log('\n🔍 ANALYSIS:');
    console.log('=' .repeat(80));
    
    // Find employees with multiple active salaries
    const multipleActiveSalaries = Object.entries(employeeGroups).filter(([_, group]) => {
      const activeSalaries = group.salaries.filter(s => s.isActive);
      return activeSalaries.length > 1;
    });
    
    if (multipleActiveSalaries.length > 0) {
      console.log(`⚠️  Employees with Multiple Active Salaries: ${multipleActiveSalaries.length}`);
      multipleActiveSalaries.forEach(([employeeId, group]) => {
        const activeSalaries = group.salaries.filter(s => s.isActive);
        console.log(`   - ${group.name}: ${activeSalaries.length} active salaries`);
      });
    } else {
      console.log('✅ No employees have multiple active salaries');
    }
    
    // Find employees with no active salaries
    const noActiveSalaries = Object.entries(employeeGroups).filter(([_, group]) => {
      const activeSalaries = group.salaries.filter(s => s.isActive);
      return activeSalaries.length === 0;
    });
    
    if (noActiveSalaries.length > 0) {
      console.log(`\n❌ Employees with NO Active Salaries: ${noActiveSalaries.length}`);
      noActiveSalaries.forEach(([employeeId, group]) => {
        console.log(`   - ${group.name} (${group.employeeNumber})`);
        // Show the most recent salary for context
        const mostRecent = group.salaries.sort((a, b) => new Date(b.effectiveDate) - new Date(a.effectiveDate))[0];
        if (mostRecent) {
          console.log(`     Most Recent: MWK ${mostRecent.basicSalary?.toLocaleString()} (${new Date(mostRecent.effectiveDate).toLocaleDateString()})`);
          if (mostRecent.endDate) {
            console.log(`     End Date: ${new Date(mostRecent.endDate).toLocaleDateString()}`);
          }
        }
      });
    } else {
      console.log('\n✅ All employees have at least one active salary');
    }
    
    // Check for salaries with end dates in the past but still marked as active
    const activeWithPastEndDate = employeeSalaries.filter(salary => {
      return salary.isActive && salary.endDate && new Date(salary.endDate) < new Date();
    });
    
    if (activeWithPastEndDate.length > 0) {
      console.log(`\n⚠️  Active Salaries with Past End Dates: ${activeWithPastEndDate.length}`);
      activeWithPastEndDate.forEach(salary => {
        const employeeName = salary.employeeId 
          ? `${salary.employeeId.firstName || ''} ${salary.employeeId.lastName || ''}`.trim()
          : 'Unknown Employee';
        console.log(`   - ${employeeName}: End Date ${new Date(salary.endDate).toLocaleDateString()}`);
      });
    }
    
    console.log('\n🎯 SUMMARY:');
    console.log('=' .repeat(80));
    console.log(`Total Records: ${employeeSalaries.length}`);
    console.log(`Active Records: ${activeCount} (${((activeCount / employeeSalaries.length) * 100).toFixed(1)}%)`);
    console.log(`Inactive Records: ${inactiveCount} (${((inactiveCount / employeeSalaries.length) * 100).toFixed(1)}%)`);
    console.log(`Employees with No Active Salary: ${noActiveSalaries.length}`);
    console.log(`Employees with Multiple Active Salaries: ${multipleActiveSalaries.length}`);
    console.log(`Active Salaries with Past End Dates: ${activeWithPastEndDate.length}`);
    
    if (noActiveSalaries.length > 0) {
      console.log('\n🚨 ISSUE IDENTIFIED:');
      console.log(`${noActiveSalaries.length} employees have NO active salary records.`);
      console.log('This explains why payroll calculations are failing with "No active salary found" errors.');
      console.log('\n💡 SOLUTION:');
      console.log('Use the Employee Salaries UI to reactivate salaries for these employees:');
      console.log('http://localhost:3000/dashboard/payroll/employee-salaries');
      console.log('Look for red X icons and click them to reactivate salaries.');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugEmployeeSalariesIsActive().then(() => {
  console.log('\n✅ Debug completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});

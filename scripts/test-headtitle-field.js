/**
 * <PERSON><PERSON><PERSON> to test the headTitle field directly in the database
 */

const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources';

// Define the Department schema (simplified version)
const departmentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Department name is required'],
    trim: true
  },
  description: {
    type: String
  },
  head: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  headTitle: {
    type: String,
    trim: true
  },
  employees: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  budget: {
    type: Number
  },
  departmentCode: {
    type: String,
    trim: true,
    uppercase: true
  },
  location: {
    type: String,
    trim: true
  },
  establishedDate: {
    type: Date
  },
  contactEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  contactPhone: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  }
}, {
  timestamps: true
});

async function testHeadTitleField() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create the Department model
    const Department = mongoose.model('Department', departmentSchema);

    // Test creating a department with headTitle
    console.log('\n🧪 Testing department creation with headTitle...');
    
    const testDepartmentData = {
      name: `Test HeadTitle Department ${Date.now()}`,
      description: 'Test department for headTitle field validation',
      departmentCode: 'TESTH',
      location: 'Test Location',
      budget: 100000,
      contactEmail: '<EMAIL>',
      contactPhone: '+265-1-123-999',
      establishedDate: new Date('2024-01-01'),
      status: 'active',
      headTitle: 'Test Department Head Title'
    };

    console.log('📝 Creating department with data:', JSON.stringify(testDepartmentData, null, 2));
    
    const createdDepartment = await Department.create(testDepartmentData);
    console.log('✅ Department created successfully!');
    console.log('📄 Created department ID:', createdDepartment._id);

    // Verify the department was saved with headTitle
    const foundDepartment = await Department.findById(createdDepartment._id).lean();
    console.log('\n🔍 Department found in database:');
    console.log(JSON.stringify(foundDepartment, null, 2));

    // Check specifically for headTitle
    if (foundDepartment.headTitle) {
      console.log('\n✅ SUCCESS: headTitle field is saved correctly!');
      console.log('📝 headTitle value:', foundDepartment.headTitle);
    } else {
      console.log('\n❌ ERROR: headTitle field is missing from saved document');
    }

    // Clean up - delete the test department
    await Department.findByIdAndDelete(createdDepartment._id);
    console.log('\n🧹 Test department deleted');

  } catch (error) {
    console.error('❌ Error testing headTitle field:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testHeadTitleField();

// Test script to verify salary calculation fixes
const { connectToDatabase } = require('../lib/backend/database');
const { salaryCalculationService } = require('../lib/services/payroll/salary-calculation-service');
const { salaryService } = require('../services/payroll/SalaryService');

async function testSalaryCalculations() {
  try {
    await connectToDatabase();
    
    console.log('🧪 Testing Salary Calculation Services...\n');
    
    // Test data matching your example
    const testEmployeeId = 'test-employee-id'; // Replace with actual employee ID
    const testPayPeriod = { month: 12, year: 2024 };
    
    console.log('📊 Test Data:');
    console.log('- Basic Salary: MWK 3,581,740.80');
    console.log('- Housing Allowance: MWK 9,000.00');
    console.log('- Transport Allowance: MWK 6,000.00');
    console.log('- Expected Gross: MWK 3,596,740.80');
    console.log('- PAYE Tax: MWK 1,068,859.28');
    console.log('- Other Deductions: MWK 8,000.00');
    console.log('- Expected Net: MWK 2,519,881.52\n');
    
    // Test 1: SalaryCalculationService
    console.log('🔧 Testing SalaryCalculationService...');
    try {
      const result1 = await salaryCalculationService.calculateSalary(testEmployeeId, testPayPeriod);
      console.log('✅ SalaryCalculationService Results:');
      console.log(`   Gross Salary: MWK ${result1.grossSalary.toLocaleString()}`);
      console.log(`   Total Deductions: MWK ${result1.totalDeductions.toLocaleString()}`);
      console.log(`   Total Tax: MWK ${result1.totalTax.toLocaleString()}`);
      console.log(`   Net Salary: MWK ${result1.netSalary.toLocaleString()}`);
      console.log(`   Components: ${result1.components.length} items`);
      
      // Verify calculation
      const expectedNet = result1.grossSalary - result1.totalDeductions;
      const calculationCorrect = Math.abs(result1.netSalary - expectedNet) < 0.01;
      console.log(`   ✅ Calculation Check: ${calculationCorrect ? 'CORRECT' : 'INCORRECT'}`);
      if (!calculationCorrect) {
        console.log(`   ❌ Expected: ${expectedNet}, Got: ${result1.netSalary}`);
      }
    } catch (error) {
      console.log(`❌ SalaryCalculationService Error: ${error.message}`);
    }
    
    console.log('\n🔧 Testing SalaryService...');
    try {
      const result2 = await salaryService.calculateNetSalary(testEmployeeId);
      console.log('✅ SalaryService Results:');
      console.log(`   Gross Salary: MWK ${result2.grossSalary.toLocaleString()}`);
      console.log(`   Tax Deduction: MWK ${result2.deductions.tax.toLocaleString()}`);
      console.log(`   Other Deductions: MWK ${result2.deductions.other.toLocaleString()}`);
      console.log(`   Net Salary: MWK ${result2.netSalary.toLocaleString()}`);
      
      // Verify calculation
      const totalDeductions = result2.deductions.tax + result2.deductions.other;
      const expectedNet = result2.grossSalary - totalDeductions;
      const calculationCorrect = Math.abs(result2.netSalary - expectedNet) < 0.01;
      console.log(`   ✅ Calculation Check: ${calculationCorrect ? 'CORRECT' : 'INCORRECT'}`);
      if (!calculationCorrect) {
        console.log(`   ❌ Expected: ${expectedNet}, Got: ${result2.netSalary}`);
      }
    } catch (error) {
      console.log(`❌ SalaryService Error: ${error.message}`);
    }
    
    console.log('\n🎯 Recommendations:');
    console.log('1. If calculations are correct, clear existing payroll records');
    console.log('2. Run a new payroll calculation to test the fixes');
    console.log('3. Check browser cache and refresh the payroll display');
    console.log('4. Verify which service is being used in the UI component');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testSalaryCalculations().then(() => {
  console.log('\n✅ Test completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

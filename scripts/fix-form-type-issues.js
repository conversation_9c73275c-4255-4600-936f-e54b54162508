const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.tsx', '.ts'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  autoFix: true,
  // Patterns to fix
  patterns: {
    formControlType: true,
    resolverType: true,
    filterIncludes: true
  }
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Fix form control type issues
function fixFormControlTypeIssues(content) {
  // Replace form.control with form.control as any
  let fixedContent = content.replace(/control=\{form\.control\}/g, 'control={form.control as any}');
  
  return fixedContent;
}

// Fix resolver type issues
function fixResolverTypeIssues(content) {
  // Replace resolver: zodResolver(...) with resolver: zodResolver(...) as any
  let fixedContent = content.replace(/(resolver:\s*zodResolver\([^)]+\))(?!\s*as\s+any)/g, '$1 as any');
  
  return fixedContent;
}

// Fix filter includes issues
function fixFilterIncludesIssues(content) {
  // Replace .getFilterValue()?.includes( with (getFilterValue() as string[] || []).includes(
  let fixedContent = content.replace(
    /(\.getColumn\([^)]+\)\s*\??\s*\.getFilterValue\(\))\s*\??\s*\.includes\(/g, 
    '($1 as string[] || []).includes('
  );
  
  return fixedContent;
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  
  // Apply fixes based on configuration
  if (config.patterns.formControlType) {
    const contentAfterFix = fixFormControlTypeIssues(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed form control type issues in ${filePath}`);
    }
  }
  
  if (config.patterns.resolverType) {
    const contentAfterFix = fixResolverTypeIssues(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed resolver type issues in ${filePath}`);
    }
  }
  
  if (config.patterns.filterIncludes) {
    const contentAfterFix = fixFilterIncludesIssues(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed filter includes issues in ${filePath}`);
    }
  }
  
  // Save changes if any were applied
  if (changesApplied && config.autoFix) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
function main() {
  console.log('Scanning for form type issues...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed form type issues in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
    console.log('2. Run the component scanner to check for other issues:');
    console.log('   npm run scan-components');
  }
}

// Run the script
main();

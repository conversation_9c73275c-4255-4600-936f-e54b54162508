/**
 * TypeScript Error Finder and Resolver
 * 
 * This script scans TypeScript files for common errors and provides fixes.
 * It can be used to automatically fix issues across the codebase.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import { exec } from 'child_process';

const execPromise = util.promisify(exec);

// Error patterns to search for
const ERROR_PATTERNS = {
  // Template string errors
  TEMPLATE_STRING_ERROR: /'.*\$\{.*\}.*'/g,
  
  // Redundant error instanceof Error checks
  REDUNDANT_ERROR_CHECK: /error instanceof Error \? error instanceof Error \?/g,
  
  // Type 'null' is not assignable to type 'X | undefined'
  NULL_TYPE_ERROR: /Type 'null' is not assignable to type '.*? \| undefined'/,
  
  // Property does not exist on type '{}'
  PROPERTY_NOT_EXIST: /Property '.*?' does not exist on type '\{\}'/,
  
  // Type 'unknown' is not assignable to type
  UNKNOWN_TYPE_ERROR: /Type 'unknown' is not assignable to type/,
  
  // initialFocus prop error
  INITIAL_FOCUS_ERROR: /initialFocus/,
  
  // Next.js 15 dynamic route parameter errors
  DYNAMIC_ROUTE_PARAM_ERROR: /params: \{ .*?: string \}/,
  
  // Duplicate Promise types
  DUPLICATE_PROMISE_ERROR: /Promise<.*?>: Promise<.*?>/,
};

// Function to scan a file for errors
async function scanFileForErrors(filePath: string): Promise<{
  filePath: string;
  errors: Array<{
    type: string;
    line: number;
    content: string;
  }>;
}> {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const errors: Array<{ type: string; line: number; content: string }> = [];

  // Check each line for errors
  lines.forEach((line, index) => {
    // Check for template string errors
    if (ERROR_PATTERNS.TEMPLATE_STRING_ERROR.test(line)) {
      errors.push({
        type: 'TEMPLATE_STRING_ERROR',
        line: index + 1,
        content: line.trim(),
      });
    }

    // Check for redundant error instanceof Error checks
    if (ERROR_PATTERNS.REDUNDANT_ERROR_CHECK.test(line)) {
      errors.push({
        type: 'REDUNDANT_ERROR_CHECK',
        line: index + 1,
        content: line.trim(),
      });
    }

    // Add more error checks as needed
  });

  return {
    filePath,
    errors,
  };
}

// Function to fix errors in a file
async function fixErrorsInFile(filePath: string, errors: Array<{ type: string; line: number; content: string }>): Promise<void> {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix each error
  for (const error of errors) {
    switch (error.type) {
      case 'TEMPLATE_STRING_ERROR':
        // Replace single quotes with backticks for template strings
        const newContent = content.replace(
          ERROR_PATTERNS.TEMPLATE_STRING_ERROR,
          (match) => match.replace(/'/g, '`')
        );
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
        break;

      case 'REDUNDANT_ERROR_CHECK':
        // Simplify redundant error instanceof Error checks
        const simplifiedContent = content.replace(
          ERROR_PATTERNS.REDUNDANT_ERROR_CHECK,
          'error instanceof Error ?'
        );
        if (simplifiedContent !== content) {
          content = simplifiedContent;
          modified = true;
        }
        break;

      // Add more error fixes as needed
    }
  }

  // Write the modified content back to the file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed errors in ${filePath}`);
  }
}

// Function to scan a directory recursively for TypeScript files
async function scanDirectory(dirPath: string): Promise<string[]> {
  const files: string[] = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      files.push(...await scanDirectory(fullPath));
    } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
      files.push(fullPath);
    }
  }

  return files;
}

// Main function to run the error finder and resolver
async function main() {
  const args = process.argv.slice(2);
  const dirPath = args[0] || '.';
  const fixErrors = args.includes('--fix');

  console.log(`Scanning directory: ${dirPath}`);
  const files = await scanDirectory(dirPath);
  console.log(`Found ${files.length} TypeScript files`);

  let totalErrors = 0;
  const fileErrors: Array<{ filePath: string; errors: Array<{ type: string; line: number; content: string }> }> = [];

  // Scan each file for errors
  for (const file of files) {
    const result = await scanFileForErrors(file);
    if (result.errors.length > 0) {
      fileErrors.push(result);
      totalErrors += result.errors.length;
    }
  }

  console.log(`Found ${totalErrors} errors in ${fileErrors.length} files`);

  // Fix errors if requested
  if (fixErrors) {
    for (const fileError of fileErrors) {
      await fixErrorsInFile(fileError.filePath, fileError.errors);
    }
    console.log('Fixed errors');
  } else {
    // Print errors
    for (const fileError of fileErrors) {
      console.log(`\nFile: ${fileError.filePath}`);
      for (const error of fileError.errors) {
        console.log(`  Line ${error.line}: ${error.type} - ${error.content}`);
      }
    }
  }
}

// Run the main function
main().catch(console.error);

// Test Fixed Tax Calculation Script
// This script tests the fixed progressive tax calculation logic

function calculateTaxFromBrackets(taxableAmount, brackets) {
  // Sort brackets by lower limit
  const sortedBrackets = [...brackets].sort((a, b) => a.lowerLimit - b.lowerLimit);
  
  let totalTax = 0;
  
  console.log(`\nCalculating tax for amount: ${taxableAmount.toLocaleString()}`);
  console.log('Brackets:', sortedBrackets.map(b => ({
    range: `${b.lowerLimit} - ${b.upperLimit || 'unlimited'}`,
    rate: b.rate + '%',
    isExempt: b.isExempt
  })));
  
  // Calculate tax for each bracket progressively
  for (const bracket of sortedBrackets) {
    // Skip if taxable amount doesn't reach this bracket
    if (taxableAmount <= bracket.lowerLimit) {
      continue;
    }
    
    // Skip exempt brackets
    if (bracket.isExempt) {
      console.log(`Bracket ${bracket.lowerLimit} - ${bracket.upperLimit || 'unlimited'}: EXEMPT (0%)`);
      continue;
    }
    
    // Calculate the amount that falls into this bracket
    const upperLimit = bracket.upperLimit || Infinity;
    const amountInBracket = Math.min(taxableAmount, upperLimit) - bracket.lowerLimit;
    
    // Only calculate tax if there's a positive amount in this bracket
    if (amountInBracket > 0) {
      const taxInBracket = amountInBracket * (bracket.rate / 100);
      totalTax += taxInBracket;
      
      console.log(`Bracket ${bracket.lowerLimit} - ${bracket.upperLimit || 'unlimited'}: ${amountInBracket.toLocaleString()} @ ${bracket.rate}% = ${taxInBracket.toLocaleString()}`);
    }
  }
  
  console.log(`Total Tax: ${totalTax.toLocaleString()}`);
  console.log(`Net Amount: ${(taxableAmount - totalTax).toLocaleString()}`);
  console.log(`Effective Rate: ${((totalTax / taxableAmount) * 100).toFixed(2)}%`);
  
  return Math.round(totalTax * 100) / 100;
}

function testTaxCalculation() {
  console.log('🧮 Testing Fixed Progressive Tax Calculation\n');
  
  // Malawi tax brackets (as defined in the tax service)
  const brackets = [
    {
      lowerLimit: 0,
      upperLimit: 150000,
      rate: 0,
      isExempt: true
    },
    {
      lowerLimit: 150000,
      upperLimit: 500000,
      rate: 25,
      isExempt: false
    },
    {
      lowerLimit: 500000,
      upperLimit: 2550000,
      rate: 30,
      isExempt: false
    },
    {
      lowerLimit: 2550000,
      upperLimit: undefined, // No upper limit for the highest bracket
      rate: 35,
      isExempt: false
    }
  ];
  
  const testCases = [
    { salary: 100000, expectedTax: 0 },
    { salary: 150000, expectedTax: 0 },
    { salary: 200000, expectedTax: 12500 },
    { salary: 500000, expectedTax: 87500 },
    { salary: 1000000, expectedTax: 237500 },
    { salary: 2550000, expectedTax: 702500 },
    { salary: 3256128, expectedTax: 949644.8 }
  ];
  
  console.log('Test Cases:');
  console.log('===========');
  
  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. Testing salary: MWK ${testCase.salary.toLocaleString()}`);
    console.log(`   Expected tax: MWK ${testCase.expectedTax.toLocaleString()}`);
    
    const calculatedTax = calculateTaxFromBrackets(testCase.salary, brackets);
    const isCorrect = Math.abs(calculatedTax - testCase.expectedTax) < 0.01;
    
    console.log(`   Calculated tax: MWK ${calculatedTax.toLocaleString()}`);
    console.log(`   Result: ${isCorrect ? '✅ PASS' : '❌ FAIL'}`);
    
    if (!isCorrect) {
      console.log(`   ⚠️  Difference: MWK ${Math.abs(calculatedTax - testCase.expectedTax).toLocaleString()}`);
    }
  });
  
  console.log('\n🎯 Manual verification for MWK 3,256,128:');
  console.log('Expected breakdown:');
  console.log('- First 150,000 @ 0% = 0');
  console.log('- Next 350,000 (150,001-500,000) @ 25% = 87,500');
  console.log('- Next 2,050,000 (500,001-2,550,000) @ 30% = 615,000');
  console.log('- Remaining 706,128 (above 2,550,000) @ 35% = 247,144.80');
  console.log('- Total = 949,644.80');
  console.log('- Net = 2,306,483.20');
}

// Run the test
testTaxCalculation();

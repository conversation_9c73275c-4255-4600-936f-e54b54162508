/**
 * <PERSON><PERSON><PERSON> to create test employee salaries
 * 
 * Run with: node scripts/create-test-employee-salaries.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpackhrmanager')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define Employee schema
const EmployeeSchema = new Schema({
  firstName: String,
  lastName: String,
  email: String,
  position: String,
  department: String,
  // Other fields...
});

// Define EmployeeSalary schema
const EmployeeSalarySchema = new Schema({
  employeeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: true,
  },
  salaryStructureId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SalaryStructure',
  },
  basicSalary: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: 'MWK',
  },
  allowances: [{
    name: String,
    amount: Number,
    percentage: Number,
    isTaxable: Boolean,
  }],
  deductions: [{
    name: String,
    amount: Number,
    percentage: Number,
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  effectiveDate: {
    type: Date,
    default: Date.now,
  },
  endDate: {
    type: Date,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

// Define SalaryStructure schema
const SalaryStructureSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  },
  role: {
    type: String,
    trim: true,
  },
  basicSalary: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: 'MWK',
  },
  allowances: [{
    name: String,
    amount: Number,
    percentage: Number,
    isTaxable: Boolean,
  }],
  deductions: [{
    name: String,
    amount: Number,
    percentage: Number,
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

// Create the models
const Employee = mongoose.models.Employee || mongoose.model('Employee', EmployeeSchema);
const EmployeeSalary = mongoose.models.EmployeeSalary || mongoose.model('EmployeeSalary', EmployeeSalarySchema);
const SalaryStructure = mongoose.models.SalaryStructure || mongoose.model('SalaryStructure', SalaryStructureSchema);

// Create test employee salaries
async function createTestEmployeeSalaries() {
  try {
    // Find all employees
    const employees = await Employee.find();
    console.log(`Found ${employees.length} employees`);
    
    if (employees.length === 0) {
      console.log('No employees found. Please create employees first.');
      return;
    }
    
    // Find a test user to use as createdBy
    const User = mongoose.models.User || mongoose.model('User', new Schema({}));
    const testUser = await User.findOne();
    
    if (!testUser) {
      console.error('No users found in the database. Please create a user first.');
      return;
    }
    
    const testUserId = testUser._id;
    console.log(`Using test user ID: ${testUserId}`);
    
    // Find or create a salary structure
    let salaryStructure = await SalaryStructure.findOne({ isActive: true });
    
    if (!salaryStructure) {
      console.log('No active salary structure found. Creating a default one...');
      
      salaryStructure = new SalaryStructure({
        name: 'Default Salary Structure',
        description: 'Default salary structure for all employees',
        basicSalary: 100000,
        currency: 'MWK',
        allowances: [
          {
            name: 'Housing Allowance',
            amount: 20000,
            isTaxable: true,
          },
          {
            name: 'Transport Allowance',
            amount: 15000,
            isTaxable: true,
          }
        ],
        deductions: [
          {
            name: 'Health Insurance',
            amount: 5000,
          },
          {
            name: 'Pension',
            percentage: 5,
          }
        ],
        isActive: true,
        createdBy: testUserId,
        updatedBy: testUserId,
      });
      
      await salaryStructure.save();
      console.log('Created default salary structure');
    }
    
    // Create or update employee salaries
    const results = [];
    
    for (const employee of employees) {
      // Check if employee already has a salary
      const existingSalary = await EmployeeSalary.findOne({
        employeeId: employee._id,
        isActive: true,
      });
      
      if (existingSalary) {
        console.log(`Employee ${employee.firstName} ${employee.lastName} already has an active salary`);
        results.push(existingSalary);
        continue;
      }
      
      // Generate a random basic salary between 80,000 and 200,000
      const basicSalary = Math.floor(Math.random() * 120000) + 80000;
      
      // Create new employee salary
      const employeeSalary = new EmployeeSalary({
        employeeId: employee._id,
        salaryStructureId: salaryStructure._id,
        basicSalary,
        currency: 'MWK',
        allowances: [
          {
            name: 'Housing Allowance',
            amount: Math.floor(basicSalary * 0.2), // 20% of basic salary
            isTaxable: true,
          },
          {
            name: 'Transport Allowance',
            amount: Math.floor(basicSalary * 0.15), // 15% of basic salary
            isTaxable: true,
          }
        ],
        deductions: [
          {
            name: 'Health Insurance',
            amount: 5000,
          },
          {
            name: 'Pension',
            percentage: 5,
          }
        ],
        isActive: true,
        effectiveDate: new Date(),
        createdBy: testUserId,
        updatedBy: testUserId,
      });
      
      await employeeSalary.save();
      console.log(`Created salary for employee ${employee.firstName} ${employee.lastName}`);
      results.push(employeeSalary);
    }
    
    console.log(`Successfully created/updated ${results.length} employee salaries`);
  } catch (error) {
    console.error('Error creating test employee salaries:', error);
  } finally {
    // Close the database connection
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
createTestEmployeeSalaries();

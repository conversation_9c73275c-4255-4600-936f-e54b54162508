/**
 * This script fixes duplicate Mongoose schema indexes by removing redundant index definitions.
 *
 * The issue occurs when a field is defined with both `index: true` in the schema field
 * and also explicitly indexed using `schema.index()`.
 *
 * To run this script:
 * node scripts/fix-mongoose-indexes.js
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Directories to scan for model files
const DIRS_TO_SCAN = [
  'models',
  'models/accounting',
  'models/assessment',
  'models/asset',
  'models/attendance',
  'models/calendar',
  'models/document',
  'models/finance',
  'models/hr',
  'models/integration',
  'models/leave',
  'models/loan',
  'models/notification',
  'models/payroll',
  'models/procurement',
  'models/project',
  'models/recruitment',
  'models/security'
];

// Fields with duplicate indexes reported in the error logs
const DUPLICATE_INDEXES = [
  'email',
  'candidateId',
  'applicationId',
  'jobId',
  'interviewId',
  'offerId',
  'issueId',
  'documentId',
  'entryId',
  'leaveId',
  'recordId',
  'submissionId',
  'assessmentId',
  'assetId',
  'accountNumber',
  'transactionNumber',
  'customerNumber',
  'vendorNumber',
  'employeeNumber',
  'transactionId',
  'movementId',
  'runId',
  'reference',
  'projectId',
  'allocationId',
  'budgetId',
  'riskId',
  'expenseId',
  'resourceId',
  'reservationId',
  'code',
  'loanId',
  'maintenanceId',
  'onboardingId',
  'journalEntryId',
  'importBatch',
  'taskId',
  'userId',
  'ipAddress',
  'timesheetId'
];

// Regular expressions to find index definitions
const INDEX_IN_FIELD_REGEX = /(\w+):\s*{[^}]*index:\s*true[^}]*}/g;
const UNIQUE_IN_FIELD_REGEX = /(\w+):\s*{[^}]*unique:\s*true[^}]*}/g;
const SCHEMA_INDEX_REGEX = /(\w+)Schema\.index\(\s*{\s*(\w+):\s*1\s*}[^)]*\)/g;

async function* walkDir(dir) {
  const files = await readdir(dir);
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);
    if (stats.isDirectory()) {
      yield* walkDir(filePath);
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ts'))) {
      yield filePath;
    }
  }
}

async function fixDuplicateIndexes() {
  console.log('Scanning for model files with duplicate indexes...');

  let modelsFixed = 0;

  for (const baseDir of DIRS_TO_SCAN) {
    const dir = path.resolve(process.cwd(), baseDir);

    try {
      if (!fs.existsSync(dir)) {
        console.log(`Directory ${dir} does not exist, skipping...`);
        continue;
      }

      for await (const filePath of walkDir(dir)) {
        let content = await readFile(filePath, 'utf8');
        let originalContent = content;
        let hasSchemaDefinition = content.includes('new mongoose.Schema') || content.includes('new Schema');

        if (!hasSchemaDefinition) {
          continue;
        }

        // Check for duplicate indexes
        let hasChanges = false;

        for (const field of DUPLICATE_INDEXES) {
          // Check for field with index: true or unique: true
          const hasFieldIndex = new RegExp(`${field}:\\s*{[^}]*index:\\s*true[^}]*}`, 'g').test(content);
          const hasUniqueField = new RegExp(`${field}:\\s*{[^}]*unique:\\s*true[^}]*}`, 'g').test(content);

          // Check for schema.index() call for the same field
          const schemaIndexRegex = new RegExp(`\\w+Schema\\.index\\(\\s*{\\s*${field}:\\s*1\\s*}[^)]*\\)`, 'g');
          const hasSchemaIndex = schemaIndexRegex.test(content);

          if ((hasFieldIndex || hasUniqueField) && hasSchemaIndex) {
            // Remove the schema.index() call
            content = content.replace(new RegExp(`\\w+Schema\\.index\\(\\s*{\\s*${field}:\\s*1\\s*}[^)]*\\)\\s*;?`, 'g'), '');
            hasChanges = true;
            console.log(`Fixed duplicate index for field '${field}' in ${filePath}`);
          }
        }

        if (hasChanges) {
          await writeFile(filePath, content, 'utf8');
          modelsFixed++;
        }
      }
    } catch (err) {
      console.error(`Error processing directory ${dir}:`, err);
    }
  }

  console.log(`Fixed duplicate indexes in ${modelsFixed} model files.`);
}

// Run the script
fixDuplicateIndexes().catch(err => {
  console.error('Error fixing duplicate indexes:', err);
  process.exit(1);
});

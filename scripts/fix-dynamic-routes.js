#!/usr/bin/env node

/**
 * This script fixes the context parameter type in dynamic route handlers.
 * It changes:
 * - context: { params: { id: string } } to context: Promise<{ params: { id: string } }>
 * - context: Promise<{ params: { id: string } }> to context: { params: { id: string } }
 * based on whether the code uses await context or not.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directory to store backups
const BACKUP_DIR = path.join(process.cwd(), 'backups', 'dynamic-route-fixes');

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

/**
 * Fix a file by checking if it uses await context and updating the type accordingly
 */
function fixFile(filePath) {
  console.log(`Processing ${filePath}...`);

  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = false;

  // Create backup
  const relativePath = path.relative(process.cwd(), filePath);
  const backupPath = path.join(BACKUP_DIR, relativePath);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.writeFileSync(backupPath, content);

  // Check if file contains dynamic route handlers
  if (content.includes('export async function')) {

    // Check if file uses await context
    const usesAwaitContext = content.includes('await context');

    if (usesAwaitContext) {
      // Change regular context to Promise context
      newContent = newContent.replace(
        /export\s+async\s+function\s+(\w+)\s*\(\s*(?:req|request):\s*NextRequest,\s*context:\s*\{\s*params:\s*\{\s*([^}]+)\}\s*\}\s*\)/g,
        'export async function $1(req: NextRequest, context: Promise<{ params: { $2 } }>)'
      );

      // Also fix any that might already be Promise but with req instead of request
      newContent = newContent.replace(
        /export\s+async\s+function\s+(\w+)\s*\(\s*req:\s*NextRequest,\s*context:\s*Promise<\{\s*params:\s*\{\s*([^}]+)\}\s*\}>\s*\)/g,
        'export async function $1(req: NextRequest, context: Promise<{ params: { $2 } }>)'
      );

      // Force all dynamic route handlers to use Promise<{ params }> if they use await context
      if (filePath.includes('[id]') || filePath.includes('[') && filePath.includes(']')) {
        newContent = newContent.replace(
          /export\s+async\s+function\s+(\w+)\s*\(\s*(?:req|request):\s*NextRequest,\s*(?:context|params|param):[^{]*\{[^}]*\}\s*\)/g,
          'export async function $1(req: NextRequest, context: Promise<{ params: { id: string } }>)'
        );
      }
    } else {
      // Change Promise context to regular context
      newContent = newContent.replace(
        /export\s+async\s+function\s+(\w+)\s*\(\s*(?:req|request):\s*NextRequest,\s*context:\s*Promise<\{\s*params:\s*\{\s*([^}]+)\}\s*\}>\s*\)/g,
        'export async function $1(req: NextRequest, context: { params: { $2 } })'
      );

      // Force all dynamic route handlers to use { params } if they don't use await context
      if (filePath.includes('[id]') || filePath.includes('[') && filePath.includes(']')) {
        newContent = newContent.replace(
          /export\s+async\s+function\s+(\w+)\s*\(\s*(?:req|request):\s*NextRequest,\s*(?:context|params|param):[^{]*Promise<[^>]*>\s*\)/g,
          'export async function $1(req: NextRequest, context: { params: { id: string } })'
        );
      }
    }

    // Special case for accounting/accounts/[id]/route.ts
    if (filePath.includes('accounting/accounts/[id]/route.ts')) {
      newContent = newContent.replace(
        /export\s+async\s+function\s+(\w+)\s*\(\s*(?:req|request):\s*NextRequest,\s*[^)]*\)/g,
        'export async function $1(req: NextRequest, context: Promise<{ params: { id: string } }>)'
      );
    }

    // Check if content changed
    if (newContent !== content) {
      changes = true;
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed dynamic route in ${filePath}`);
    }
  }

  return changes;
}

/**
 * Find all dynamic route files
 */
function findDynamicRoutes(directory = 'app/api') {
  try {
    // Find all route.ts files in directories with [param] in their name
    const result = execSync(`find ${directory} -path "*\\[*\\]*/route.ts"`, { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error(`Error finding dynamic routes in ${directory}:`, error);
    return [];
  }
}

/**
 * Main function
 */
function main(directory = 'app/api') {
  console.log(`
╔════════════════════════════════════════════════════════════╗
║             DYNAMIC ROUTE PARAMETER FIXER                  ║
╚════════════════════════════════════════════════════════════╝
`);
  console.log(`Starting dynamic route fixes for directory: ${directory}...`);

  const routes = findDynamicRoutes(directory);
  console.log(`Found ${routes.length} dynamic routes to check`);

  let fixedCount = 0;
  const startTime = Date.now();

  for (const route of routes) {
    try {
      const fixed = fixFile(route);
      if (fixed) fixedCount++;
    } catch (error) {
      console.error(`Error fixing ${route}:`, error);
    }
  }

  const duration = ((Date.now() - startTime) / 1000).toFixed(2);

  console.log(`
╔════════════════════════════════════════════════════════════╗
║                        SUMMARY                             ║
╠════════════════════════════════════════════════════════════╣
║  Total dynamic routes checked: ${routes.length.toString().padEnd(21)} ║
║  Files fixed:                 ${fixedCount.toString().padEnd(21)} ║
║  Time taken:                  ${duration}s${' '.repeat(20 - duration.toString().length)} ║
╚════════════════════════════════════════════════════════════╝
`);

  if (fixedCount > 0) {
    console.log(`Backups of modified files were saved to: ${BACKUP_DIR}`);
  }

  console.log('Done!');
}

// Get directory from command line arguments or use default
const directory = process.argv[2] || 'app/api';
main(directory);

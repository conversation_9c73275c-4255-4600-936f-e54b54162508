/**
 * <PERSON><PERSON><PERSON> to fix existing employee records
 * This script updates all existing employee records to ensure they have a valid employeeNumber
 * 
 * Run with: node scripts/fix-existing-employees.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Define the Employee schema
const employeeSchema = new mongoose.Schema({
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  employeeNumber: {
    type: String,
    trim: true,
    // Remove unique constraint temporarily
    // unique: true,
    required: true
  },
  firstName: String,
  lastName: String,
  // Other fields not needed for this script
}, { 
  timestamps: true,
  strict: false // Allow other fields in the document
});

// Create the Employee model
const Employee = mongoose.model('Employee', employeeSchema);

// Main function to fix existing employees
async function fixExistingEmployees() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find all employees
    const employees = await Employee.find({});
    console.log(`Found ${employees.length} employees in database`);

    // Update each employee
    let successCount = 0;
    let errorCount = 0;

    for (const employee of employees) {
      try {
        // Generate a unique employee number
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
        const uniqueEmployeeNumber = `EMP-FIX-${timestamp}-${random}`;

        // Update the employee
        employee.employeeNumber = uniqueEmployeeNumber;
        await employee.save();
        
        successCount++;
        console.log(`Updated employee ${employee.firstName} ${employee.lastName} (${employee._id}): employeeNumber = ${uniqueEmployeeNumber}`);
      } catch (error) {
        errorCount++;
        console.error(`Error updating employee ${employee.firstName} ${employee.lastName} (${employee._id}):`, error.message);
      }
    }

    console.log('\nFix completed:');
    console.log(`- Total employees processed: ${employees.length}`);
    console.log(`- Successfully updated: ${successCount}`);
    console.log(`- Errors: ${errorCount}`);

  } catch (error) {
    console.error('Fix failed:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the fix
fixExistingEmployees().catch(console.error);

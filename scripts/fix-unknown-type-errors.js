const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  applyFixes: true,
  // Patterns to fix
  patterns: {
    unknownTypeErrors: true,
    nullableToUndefined: true,
    formControlType: true,
    filterIncludes: true,
    syntaxErrors: true
  }
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Fix unknown type errors
function fixUnknownTypeErrors(content) {
  // Fix catch blocks with unknown type errors
  let fixedContent = content.replace(
    /catch\s*\(\s*(\w+)\s*:\s*unknown\s*\)\s*{([^}]*?)(\w+)\.message/g,
    (match, varName, beforeMessage, errorVar) => {
      if (errorVar !== varName) return match; // Not the same variable
      
      return `catch (${varName}: unknown) {${beforeMessage}${varName} instanceof Error ? ${varName}.message : 'An error occurred'`;
    }
  );
  
  // Fix unknown type in interfaces
  fixedContent = fixedContent.replace(
    /(\w+)\s*\?:\s*unknown(\s*[,;])/g,
    (match, propName, ending) => {
      // Create a more specific type based on property name
      let specificType = '';
      
      if (propName.toLowerCase().includes('id')) {
        specificType = 'string';
      } else if (propName.toLowerCase().includes('count') || propName.toLowerCase().includes('amount')) {
        specificType = 'number';
      } else if (propName.toLowerCase().includes('is') || propName.toLowerCase().includes('has')) {
        specificType = 'boolean';
      } else if (propName.toLowerCase().includes('date')) {
        specificType = 'Date | string';
      } else if (propName.toLowerCase().includes('items') || propName.toLowerCase().includes('list')) {
        specificType = 'any[]'; // Use any[] as a fallback for array types
      } else {
        specificType = 'Record<string, any>'; // Use Record as a fallback for object types
      }
      
      return `${propName}?: ${specificType}${ending}`;
    }
  );
  
  return fixedContent;
}

// Fix nullable to undefined conversions
function fixNullableToUndefined(content) {
  // Replace null checks with undefined conversions
  let fixedContent = content.replace(
    /(\w+)\s*===\s*null\s*\?\s*null\s*:/g,
    (match, varName) => `${varName} === null ? undefined :`
  );
  
  // Replace null type unions with undefined
  fixedContent = fixedContent.replace(
    /(\w+)\s*:\s*([^|]+)\s*\|\s*null(\s*[,;)])/g,
    (match, propName, typeName, ending) => `${propName}: ${typeName} | undefined${ending}`
  );
  
  return fixedContent;
}

// Fix form control type issues
function fixFormControlType(content) {
  // Replace form.control with form.control as any
  let fixedContent = content.replace(/control=\{form\.control\}/g, 'control={form.control as any}');
  
  // Replace resolver: zodResolver(...) with resolver: zodResolver(...) as any
  fixedContent = fixedContent.replace(
    /(resolver:\s*zodResolver\([^)]+\))(?!\s*as\s+any)/g, 
    '$1 as any'
  );
  
  return fixedContent;
}

// Fix filter includes issues
function fixFilterIncludes(content) {
  // Replace .getFilterValue()?.includes( with (getFilterValue() as string[] || []).includes(
  let fixedContent = content.replace(
    /(\.getColumn\([^)]+\)\s*\??\s*\.getFilterValue\(\))\s*\??\s*\.includes\(/g, 
    '($1 as string[] || []).includes('
  );
  
  // Fix syntax errors with parentheses
  fixedContent = fixedContent.replace(
    /\(\s*\.\s*getColumn/g,
    '.getColumn'
  );
  
  return fixedContent;
}

// Fix syntax errors
function fixSyntaxErrors(content) {
  // Fix missing closing parentheses
  let fixedContent = content;
  
  // Fix extra parentheses
  fixedContent = fixedContent.replace(/\(\s*\.\s*([a-zA-Z])/g, '.$1');
  
  return fixedContent;
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  
  // Apply fixes based on configuration
  if (config.patterns.unknownTypeErrors) {
    const contentAfterFix = fixUnknownTypeErrors(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed unknown type errors in ${filePath}`);
    }
  }
  
  if (config.patterns.nullableToUndefined) {
    const contentAfterFix = fixNullableToUndefined(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed nullable to undefined conversions in ${filePath}`);
    }
  }
  
  if (config.patterns.formControlType) {
    const contentAfterFix = fixFormControlType(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed form control type issues in ${filePath}`);
    }
  }
  
  if (config.patterns.filterIncludes) {
    const contentAfterFix = fixFilterIncludes(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed filter includes issues in ${filePath}`);
    }
  }
  
  if (config.patterns.syntaxErrors) {
    const contentAfterFix = fixSyntaxErrors(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed syntax errors in ${filePath}`);
    }
  }
  
  // Save changes if any were applied
  if (changesApplied && config.applyFixes) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
async function main() {
  console.log('Fixing TypeScript errors...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed TypeScript errors in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
  }
}

// Run the script
main().catch(console.error);

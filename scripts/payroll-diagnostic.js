// Payroll Diagnostic Script
// This script checks the current state of payroll data in the database

const { connectToDatabase } = require('../lib/backend/database');
const { Employee } = require('../models/Employee');
const { EmployeeSalary } = require('../models/EmployeeSalary');
const { PayrollRun } = require('../models/PayrollRun');
const { PayrollRecord } = require('../models/PayrollRecord');

async function runDiagnostic() {
  try {
    console.log('🔍 Starting Payroll Diagnostic...\n');
    
    // Connect to database
    await connectToDatabase();
    console.log('✅ Connected to database\n');

    // 1. Check employees
    const totalEmployees = await Employee.countDocuments();
    const activeEmployees = await Employee.countDocuments({ status: 'active' });
    console.log(`👥 Employees:`);
    console.log(`   Total: ${totalEmployees}`);
    console.log(`   Active: ${activeEmployees}\n`);

    // 2. Check employee salaries
    const totalSalaries = await EmployeeSalary.countDocuments();
    const activeSalaries = await EmployeeSalary.countDocuments({ isActive: true });
    const inactiveSalaries = await EmployeeSalary.countDocuments({ isActive: false });
    
    console.log(`💰 Employee Salaries:`);
    console.log(`   Total: ${totalSalaries}`);
    console.log(`   Active (isActive: true): ${activeSalaries}`);
    console.log(`   Inactive (isActive: false): ${inactiveSalaries}\n`);

    // 3. Check specific salary records for debugging
    const sampleSalaries = await EmployeeSalary.find()
      .populate('employeeId', 'firstName lastName employeeNumber')
      .limit(5)
      .lean();
    
    console.log(`📋 Sample Salary Records:`);
    sampleSalaries.forEach((salary, index) => {
      const employee = salary.employeeId;
      console.log(`   ${index + 1}. ${employee?.firstName} ${employee?.lastName} (${employee?.employeeNumber})`);
      console.log(`      Basic Salary: ${salary.basicSalary} ${salary.currency}`);
      console.log(`      Is Active: ${salary.isActive}`);
      console.log(`      Effective Date: ${salary.effectiveDate}`);
      console.log(`      End Date: ${salary.endDate || 'N/A'}`);
      console.log('');
    });

    // 4. Check payroll runs
    const totalRuns = await PayrollRun.countDocuments();
    const completedRuns = await PayrollRun.countDocuments({ status: 'completed' });
    const draftRuns = await PayrollRun.countDocuments({ status: 'draft' });
    
    console.log(`🏃 Payroll Runs:`);
    console.log(`   Total: ${totalRuns}`);
    console.log(`   Completed: ${completedRuns}`);
    console.log(`   Draft: ${draftRuns}\n`);

    // 5. Check recent payroll run details
    const recentRun = await PayrollRun.findOne().sort({ createdAt: -1 }).lean();
    if (recentRun) {
      console.log(`📊 Most Recent Payroll Run:`);
      console.log(`   ID: ${recentRun._id}`);
      console.log(`   Status: ${recentRun.status}`);
      console.log(`   Total Employees: ${recentRun.totalEmployees || 'N/A'}`);
      console.log(`   Processed Employees: ${recentRun.processedEmployees || 'N/A'}`);
      console.log(`   Total Gross Salary: ${recentRun.totalGrossSalary || 'N/A'} ${recentRun.currency || 'MWK'}`);
      console.log(`   Total Deductions: ${recentRun.totalDeductions || 'N/A'} ${recentRun.currency || 'MWK'}`);
      console.log(`   Total Tax: ${recentRun.totalTax || 'N/A'} ${recentRun.currency || 'MWK'}`);
      console.log(`   Total Net Salary: ${recentRun.totalNetSalary || 'N/A'} ${recentRun.currency || 'MWK'}`);
      console.log(`   Created: ${recentRun.createdAt}`);
      console.log('');

      // 6. Check payroll records for this run
      const recordsCount = await PayrollRecord.countDocuments({ payrollRunId: recentRun._id });
      const sampleRecords = await PayrollRecord.find({ payrollRunId: recentRun._id })
        .populate('employeeId', 'firstName lastName')
        .limit(3)
        .lean();
      
      console.log(`📝 Payroll Records for Recent Run:`);
      console.log(`   Total Records: ${recordsCount}`);
      console.log(`   Sample Records:`);
      sampleRecords.forEach((record, index) => {
        const employee = record.employeeId;
        console.log(`     ${index + 1}. ${employee?.firstName} ${employee?.lastName}`);
        console.log(`        Gross Salary: ${record.grossSalary || 'N/A'} ${record.currency || 'MWK'}`);
        console.log(`        Total Deductions: ${record.totalDeductions || 'N/A'} ${record.currency || 'MWK'}`);
        console.log(`        Total Tax: ${record.totalTax || 'N/A'} ${record.currency || 'MWK'}`);
        console.log(`        Net Salary: ${record.netSalary || 'N/A'} ${record.currency || 'MWK'}`);
        console.log(`        Status: ${record.status}`);
        console.log('');
      });
    }

    // 7. Check for employees with no active salary
    const employeesWithoutSalary = await Employee.aggregate([
      {
        $lookup: {
          from: 'employeesalaries',
          localField: '_id',
          foreignField: 'employeeId',
          as: 'salaries'
        }
      },
      {
        $match: {
          status: 'active',
          $or: [
            { salaries: { $size: 0 } },
            { 'salaries.isActive': { $ne: true } }
          ]
        }
      },
      {
        $project: {
          firstName: 1,
          lastName: 1,
          employeeNumber: 1,
          salaryCount: { $size: '$salaries' },
          activeSalaryCount: {
            $size: {
              $filter: {
                input: '$salaries',
                cond: { $eq: ['$$this.isActive', true] }
              }
            }
          }
        }
      }
    ]);

    console.log(`⚠️  Active Employees Without Active Salary:`);
    console.log(`   Count: ${employeesWithoutSalary.length}`);
    if (employeesWithoutSalary.length > 0) {
      console.log(`   Examples:`);
      employeesWithoutSalary.slice(0, 5).forEach((emp, index) => {
        console.log(`     ${index + 1}. ${emp.firstName} ${emp.lastName} (${emp.employeeNumber})`);
        console.log(`        Total Salaries: ${emp.salaryCount}, Active: ${emp.activeSalaryCount}`);
      });
    }

    console.log('\n✅ Diagnostic completed successfully!');

  } catch (error) {
    console.error('❌ Error running diagnostic:', error);
  } finally {
    process.exit(0);
  }
}

// Run the diagnostic
runDiagnostic();

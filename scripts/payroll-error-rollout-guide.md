# Payroll Error Handling Rollout Guide

## Quick Implementation Templates

### **Backend Route Template**

```typescript
// 1. Add imports
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

// 2. Replace authentication check
const user = await getCurrentUser(req);
if (!user) {
  return errorService.handlePayrollError(
    'UNAUTHORIZED_ACCESS',
    {
      endpoint: req.nextUrl.pathname,
      method: req.method
    }
  );
}

// 3. Replace permission check
if (!hasPermission) {
  return errorService.handlePayrollError(
    'UNAUTHORIZED_ACCESS',
    {
      userId: user.id,
      userRole: user.role,
      endpoint: req.nextUrl.pathname,
      method: req.method
    }
  );
}

// 4. Replace validation errors
if (validationErrors.length > 0) {
  return errorService.handlePayrollError(
    'VALIDATION_FAILED',
    {
      userId: user.id,
      endpoint: req.nextUrl.pathname,
      method: req.method,
      additionalData: { validationErrors }
    }
  );
}

// 5. Replace not found errors
if (!resource) {
  return errorService.handlePayrollError(
    'RESOURCE_NOT_FOUND',
    {
      userId: user.id,
      endpoint: req.nextUrl.pathname,
      method: req.method,
      additionalData: { resourceId: id }
    }
  );
}

// 6. Replace duplicate errors
if (existingResource) {
  return errorService.handlePayrollError(
    'DUPLICATE_ENTRY',
    {
      userId: user.id,
      endpoint: req.nextUrl.pathname,
      method: req.method,
      additionalData: { existingResource }
    }
  );
}

// 7. Replace bulk import errors
if (failedRecords.length > 0) {
  return errorService.handlePayrollError(
    'BULK_IMPORT_FAILED',
    {
      userId: user.id,
      endpoint: req.nextUrl.pathname,
      method: req.method,
      additionalData: {
        totalRecords,
        successfulRecords: successfulRecords.length,
        failedRecords: failedRecords.length,
        errors: failedRecords
      }
    }
  );
}

// 8. Replace generic catch blocks
} catch (error) {
  logger.error('Operation failed', LogCategory.API, error);
  return errorService.createApiResponse(
    ErrorType.SYSTEM,
    'OPERATION_FAILED',
    error instanceof Error ? error.message : 'Unknown error',
    'Operation failed. Please try again.',
    {
      userId: user?.id,
      endpoint: req.nextUrl.pathname,
      method: req.method
    },
    500,
    ErrorSeverity.HIGH
  );
}
```

### **Frontend Component Template**

```typescript
// 1. Add imports
import { useErrorHandler } from "@/hooks/use-error-handler"
import { ErrorOverlay } from "@/components/errors/error-overlay"

// 2. Add hook to component
const { error, isErrorOpen, handleApiError, hideError } = useErrorHandler()

// 3. Replace API error handling
const response = await fetch('/api/endpoint', options)
if (!response.ok) {
  await handleApiError(response)
  return
}

// 4. Add error overlay to JSX
return (
  <>
    {/* Error Overlay */}
    {error && (
      <ErrorOverlay
        error={error}
        isOpen={isErrorOpen}
        onClose={hideError}
        onAction={(action, data) => {
          if (action === 'retry') {
            retryOperation()
          } else if (action === 'download-errors') {
            downloadErrorReport()
          }
          // Add other action handlers as needed
        }}
      />
    )}

    {/* Your existing component JSX */}
    <YourComponent />
  </>
)
```

## Priority Implementation Order

### **Phase 1: Critical Routes (Immediate)**
1. `app/api/payroll/employee-salaries/route.ts`
2. `app/api/payroll/employee-salaries/bulk-import/route.ts`
3. `app/api/payroll/salary-structures/route.ts`
4. `app/api/payroll/tax-brackets/route.ts`

### **Phase 2: Bulk Operations (Week 1)**
1. `app/api/payroll/employee-salaries/bulk-delete/route.ts`
2. `app/api/payroll/salary-structures/bulk-import/route.ts`
3. `app/api/payroll/runs/bulk-approve/route.ts`
4. `app/api/payroll/runs/bulk-pay/route.ts`

### **Phase 3: Frontend Components (Week 2)**
1. `components/payroll/employee-salary/employee-salary-manager.tsx`
2. `components/payroll/employee-salary/bulk-employee-salary-upload.tsx`
3. `components/payroll/salary-structure/salary-structure-manager.tsx`
4. `components/payroll/tax-bracket/tax-bracket-manager.tsx`

## Quick Error Type Reference

| Route Contains | Use Error Type |
|----------------|----------------|
| `bulk-import` | `BULK_IMPORT_FAILED` |
| `bulk-delete` | `BULK_IMPORT_FAILED` |
| `template` | `TEMPLATE_ERROR` |
| `export` | `EXPORT_FAILED` |
| `download` | `EXPORT_FAILED` |
| `calculate` | `SALARY_CALCULATION_ERROR` |
| `payslips` | `PAYSLIP_GENERATION_ERROR` |
| validation logic | `VALIDATION_FAILED` |
| auth check | `UNAUTHORIZED_ACCESS` |
| resource lookup | `RESOURCE_NOT_FOUND` |
| duplicate check | `DUPLICATE_ENTRY` |

## Testing Commands

```bash
# Test authentication errors
curl -X GET http://localhost:3000/api/payroll/runs

# Test validation errors
curl -X POST http://localhost:3000/api/payroll/runs \
  -H "Content-Type: application/json" \
  -d '{}'

# Test not found errors
curl -X GET http://localhost:3000/api/payroll/runs/invalid-id

# Test bulk import errors
curl -X POST http://localhost:3000/api/payroll/employee-salaries/bulk-import \
  -H "Content-Type: application/json" \
  -d '{"data": []}'
```

## Verification Checklist

For each route/component updated:

### **Backend Route**
- [ ] Error service imported
- [ ] Authentication errors use `UNAUTHORIZED_ACCESS`
- [ ] Validation errors use `VALIDATION_FAILED`
- [ ] Not found errors use `RESOURCE_NOT_FOUND`
- [ ] Duplicate errors use `DUPLICATE_ENTRY`
- [ ] Bulk errors use `BULK_IMPORT_FAILED`
- [ ] Generic errors use `createApiResponse`
- [ ] All errors include proper context

### **Frontend Component**
- [ ] Error handler hook imported and used
- [ ] ErrorOverlay component imported
- [ ] API calls use `handleApiError`
- [ ] ErrorOverlay added to JSX
- [ ] Action handlers implemented
- [ ] Fragment properly closed

### **User Experience**
- [ ] Professional error modal appears
- [ ] Error messages are user-friendly
- [ ] Action buttons work correctly
- [ ] Error details page accessible
- [ ] Retry functionality works
- [ ] Toast notifications appear

## Common Issues & Solutions

### **Issue: TypeScript errors with error service**
**Solution**: Ensure proper imports and type assertions
```typescript
import { errorService } from '@/lib/backend/services/error-service';
// Not: import { errorService, ErrorType, ErrorSeverity }
```

### **Issue: Fragment not closed in frontend**
**Solution**: Always close React fragments
```typescript
return (
  <>
    <ErrorOverlay />
    <Component />
  </>  // Don't forget this!
)
```

### **Issue: Error overlay not showing**
**Solution**: Check error state and isErrorOpen
```typescript
{error && isErrorOpen && (
  <ErrorOverlay />
)}
```

### **Issue: Actions not working**
**Solution**: Implement action handlers
```typescript
onAction={(action, data) => {
  switch(action) {
    case 'retry':
      retryOperation();
      break;
    case 'debug':
      debugOperation();
      break;
    // Add other cases
  }
}}
```

## Success Metrics

After implementation, you should see:
- ✅ Professional error overlays instead of generic alerts
- ✅ Structured error logging in console
- ✅ Actionable error messages with suggestions
- ✅ Working retry and debug functionality
- ✅ Comprehensive error details page
- ✅ Consistent error experience across all payroll modules

## Support

If you encounter issues during implementation:
1. Check the comprehensive documentation in `project_guides/`
2. Review existing implementations in updated routes
3. Test with the provided curl commands
4. Verify all imports and exports are correct
5. Check browser console for detailed error information

The error handling architecture is designed to be self-documenting and easy to implement. Follow the templates above for consistent, professional error handling across all payroll routes and components.

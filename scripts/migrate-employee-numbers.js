/**
 * Migration script to update existing employee records
 * This script sets the employeeNumber field to match the employeeId for all employees
 * 
 * Run this script with: node scripts/migrate-employee-numbers.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Define the Employee schema
const employeeSchema = new mongoose.Schema({
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  employeeNumber: {
    type: String,
    trim: true,
    unique: true,
    required: true
  },
  // Other fields not needed for migration
}, { 
  timestamps: true,
  strict: false // Allow other fields in the document
});

// Create the Employee model
const Employee = mongoose.model('Employee', employeeSchema);

async function migrateEmployeeNumbers() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find all employees without employeeNumber
    const employees = await Employee.find({
      $or: [
        { employeeNumber: { $exists: false } },
        { employeeNumber: null }
      ]
    });

    console.log(`Found ${employees.length} employees without employeeNumber`);

    // Update each employee
    let successCount = 0;
    let errorCount = 0;

    for (const employee of employees) {
      try {
        // Set employeeNumber to match employeeId
        employee.employeeNumber = employee.employeeId;
        await employee.save();
        successCount++;
        console.log(`Updated employee ${employee.employeeId} (${employee.firstName} ${employee.lastName})`);
      } catch (error) {
        errorCount++;
        console.error(`Error updating employee ${employee.employeeId}:`, error.message);
      }
    }

    console.log('\nMigration completed:');
    console.log(`- Total employees processed: ${employees.length}`);
    console.log(`- Successfully updated: ${successCount}`);
    console.log(`- Errors: ${errorCount}`);

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
migrateEmployeeNumbers().catch(console.error);

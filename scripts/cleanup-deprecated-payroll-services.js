// <PERSON>ript to safely remove deprecated payroll services and models
const fs = require('fs');
const path = require('path');

// Files and directories to be removed after unification
const deprecatedFiles = [
  // Old payroll services
  'lib/services/payroll/payroll-service.ts',
  'lib/services/payroll/salary-calculation-service.ts',
  'services/payroll/SalaryService.ts',
  'services/payroll/PayrollService.ts',
  'lib/services/accounting/payroll-service.ts',
  
  // Duplicate accounting models (keeping payroll models as primary)
  'models/accounting/PayrollRecord.ts',
  'models/accounting/EmployeeSalary.ts',
  
  // Old integration services (will be replaced by unified integration)
  'lib/services/payroll/payroll-accounting-service.ts',
  'lib/services/accounting/payroll-integration-service.ts'
];

// Files that need import updates
const filesToUpdate = [
  {
    file: 'lib/services/payroll/enhanced-payroll-processor.ts',
    updates: [
      {
        from: "import { salaryCalculationService } from './salary-calculation-service';",
        to: "import { unifiedPayrollService } from './unified-payroll-service';"
      },
      {
        from: "salaryCalculationService.calculateSalary",
        to: "unifiedPayrollService.calculateEmployeeSalary"
      }
    ]
  },
  {
    file: 'lib/services/payroll/payslip-generation-service.ts',
    updates: [
      {
        from: "import { salaryCalculationService } from './salary-calculation-service';",
        to: "import { unifiedPayrollService } from './unified-payroll-service';"
      }
    ]
  },
  {
    file: 'app/api/employees/[id]/salary-revisions/route.ts',
    updates: [
      {
        from: "import { salaryService } from '@/services/payroll/SalaryService';",
        to: "// Removed deprecated SalaryService import"
      }
    ]
  },
  {
    file: 'app/api/employees/[id]/current-salary/route.ts',
    updates: [
      {
        from: "import { salaryService } from '@/services/payroll/SalaryService';",
        to: "// Removed deprecated SalaryService import"
      }
    ]
  }
];

function createBackup(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      const backupPath = `${fullPath}.deprecated.backup`;
      fs.copyFileSync(fullPath, backupPath);
      console.log(`📦 Created backup: ${backupPath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error creating backup for ${filePath}:`, error.message);
    return false;
  }
}

function removeDeprecatedFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`ℹ️  File not found (already removed?): ${filePath}`);
      return true;
    }
    
    // Create backup first
    if (!createBackup(filePath)) {
      console.log(`⚠️  Skipping removal of ${filePath} - backup failed`);
      return false;
    }
    
    // Remove the file
    fs.unlinkSync(fullPath);
    console.log(`🗑️  Removed: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Error removing ${filePath}:`, error.message);
    return false;
  }
}

function updateFileImports(fileUpdate) {
  try {
    const fullPath = path.join(process.cwd(), fileUpdate.file);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${fileUpdate.file}`);
      return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let updated = false;
    
    // Create backup first
    createBackup(fileUpdate.file);
    
    for (const update of fileUpdate.updates) {
      if (content.includes(update.from)) {
        content = content.replace(new RegExp(update.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), update.to);
        updated = true;
        console.log(`✅ Updated import in ${fileUpdate.file}`);
      }
    }
    
    if (updated) {
      fs.writeFileSync(fullPath, content);
      console.log(`📝 Updated file: ${fileUpdate.file}`);
      return true;
    } else {
      console.log(`ℹ️  No updates needed: ${fileUpdate.file}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error updating ${fileUpdate.file}:`, error.message);
    return false;
  }
}

function scanForDeprecatedImports() {
  console.log('\n🔍 Scanning for remaining deprecated imports...\n');
  
  const deprecatedImports = [
    'salary-calculation-service',
    'services/payroll/SalaryService',
    'services/payroll/PayrollService',
    'lib/services/accounting/payroll-service',
    'models/accounting/PayrollRecord',
    'models/accounting/EmployeeSalary'
  ];
  
  // This is a simplified scan - in a real implementation, you'd use a proper file walker
  console.log('⚠️  Manual scan required for:');
  deprecatedImports.forEach(imp => {
    console.log(`   - Files importing: ${imp}`);
  });
  
  console.log('\n💡 Use your IDE\'s "Find in Files" feature to search for these imports');
  console.log('   and update them to use the unified service.');
}

function cleanupDeprecatedServices() {
  console.log('🧹 Starting cleanup of deprecated payroll services...\n');
  
  let removedCount = 0;
  let updatedCount = 0;
  
  // Step 1: Update import statements in remaining files
  console.log('📝 Step 1: Updating import statements...\n');
  for (const fileUpdate of filesToUpdate) {
    if (updateFileImports(fileUpdate)) {
      updatedCount++;
    }
  }
  
  console.log(`\n✅ Updated ${updatedCount} files with new imports\n`);
  
  // Step 2: Remove deprecated files
  console.log('🗑️  Step 2: Removing deprecated files...\n');
  for (const filePath of deprecatedFiles) {
    if (removeDeprecatedFile(filePath)) {
      removedCount++;
    }
  }
  
  console.log(`\n✅ Removed ${removedCount} deprecated files\n`);
  
  // Step 3: Scan for remaining references
  scanForDeprecatedImports();
  
  console.log('\n🎉 Cleanup completed!');
  console.log('\n📋 Summary:');
  console.log(`   - Files updated: ${updatedCount}`);
  console.log(`   - Files removed: ${removedCount}`);
  console.log(`   - Backup files created with .deprecated.backup extension`);
  
  console.log('\n🔄 Next Steps:');
  console.log('1. Run TypeScript compilation to check for any remaining import errors');
  console.log('2. Test the application to ensure everything works with unified service');
  console.log('3. Search for any remaining references to deprecated services');
  console.log('4. Update any documentation that references old services');
  
  console.log('\n⚠️  Rollback Instructions:');
  console.log('If you need to rollback:');
  console.log('1. Restore files: find . -name "*.deprecated.backup" -exec sh -c \'mv "$1" "${1%.deprecated.backup}"\' _ {} \\;');
  console.log('2. Run npm install or yarn install');
  console.log('3. Restart your development server');
}

// Run the cleanup
cleanupDeprecatedServices();

/**
 * This script fixes Mongoose schema warnings by adding suppressReservedKeysWarning option
 * to schemas that use reserved pathnames like 'errors'.
 * 
 * To run this script:
 * node scripts/fix-mongoose-schema-warnings.js
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Directories to scan for model files
const DIRS_TO_SCAN = [
  'models',
  'models/accounting',
  'models/assessment',
  'models/asset',
  'models/attendance',
  'models/calendar',
  'models/document',
  'models/finance',
  'models/hr',
  'models/integration',
  'models/leave',
  'models/loan',
  'models/notification',
  'models/payroll',
  'models/procurement',
  'models/project',
  'models/recruitment',
  'models/security'
];

// Reserved schema pathnames to look for
const RESERVED_PATHNAMES = [
  'errors'
];

// Regular expression to find schema definitions
const SCHEMA_DEF_REGEX = /new\s+(mongoose\.)?Schema\s*\(/g;
const SCHEMA_OPTIONS_REGEX = /new\s+(mongoose\.)?Schema\s*\([^,]*,\s*({[^}]*})\s*\)/g;

async function* walkDir(dir) {
  const files = await readdir(dir);
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);
    if (stats.isDirectory()) {
      yield* walkDir(filePath);
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ts'))) {
      yield filePath;
    }
  }
}

async function fixSchemaWarnings() {
  console.log('Scanning for model files with reserved pathnames...');
  
  let modelsFixed = 0;
  
  for (const baseDir of DIRS_TO_SCAN) {
    const dir = path.resolve(process.cwd(), baseDir);
    
    try {
      if (!fs.existsSync(dir)) {
        console.log(`Directory ${dir} does not exist, skipping...`);
        continue;
      }
      
      for await (const filePath of walkDir(dir)) {
        let content = await readFile(filePath, 'utf8');
        let originalContent = content;
        
        // Check if file contains a schema definition
        if (!SCHEMA_DEF_REGEX.test(content)) {
          continue;
        }
        
        // Reset regex state
        SCHEMA_DEF_REGEX.lastIndex = 0;
        
        // Check if file contains any of the reserved pathnames
        const hasReservedPathname = RESERVED_PATHNAMES.some(pathname => 
          content.includes(`'${pathname}'`) || content.includes(`"${pathname}"`)
        );
        
        if (!hasReservedPathname) {
          continue;
        }
        
        // Check if the schema already has suppressReservedKeysWarning option
        if (content.includes('suppressReservedKeysWarning')) {
          continue;
        }
        
        // Add suppressReservedKeysWarning option to schema options
        let hasChanges = false;
        
        // Case 1: Schema with existing options
        content = content.replace(
          /new\s+(mongoose\.)?Schema\s*\([^,]*,\s*({[^}]*})\s*\)/g,
          (match, mongoosePrefix, options) => {
            // Check if options already has suppressReservedKeysWarning
            if (options.includes('suppressReservedKeysWarning')) {
              return match;
            }
            
            // Add suppressReservedKeysWarning to options
            const newOptions = options.trim().endsWith('}')
              ? options.slice(0, -1) + ', suppressReservedKeysWarning: true}'
              : options + ', suppressReservedKeysWarning: true}';
            
            hasChanges = true;
            return `new ${mongoosePrefix || ''}Schema(${match.split(',')[0].slice(match.indexOf('(') + 1)}, ${newOptions})`;
          }
        );
        
        // Case 2: Schema without options
        if (!hasChanges) {
          content = content.replace(
            /new\s+(mongoose\.)?Schema\s*\(([^)]+)\)/g,
            (match, mongoosePrefix, schemaDefinition) => {
              if (match.includes(',')) {
                // Already has options
                return match;
              }
              
              hasChanges = true;
              return `new ${mongoosePrefix || ''}Schema(${schemaDefinition}, { suppressReservedKeysWarning: true })`;
            }
          );
        }
        
        if (hasChanges) {
          await writeFile(filePath, content, 'utf8');
          modelsFixed++;
          console.log(`Fixed reserved pathname warnings in ${filePath}`);
        }
      }
    } catch (err) {
      console.error(`Error processing directory ${dir}:`, err);
    }
  }
  
  console.log(`Fixed reserved pathname warnings in ${modelsFixed} model files.`);
}

// Run the script
fixSchemaWarnings().catch(err => {
  console.error('Error fixing schema warnings:', err);
  process.exit(1);
});

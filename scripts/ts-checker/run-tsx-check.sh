#!/bin/bash

# Bash script to run TypeScript JSX checker

# Function to show usage
show_usage() {
  echo "Usage: $0 <path> [options]"
  echo "Options:"
  echo "  --project <path>     Path to tsconfig.json"
  echo "  --verbose            Show verbose output"
  echo "  --fix                Try to fix errors (experimental)"
  echo "  --max-errors <num>   Maximum number of errors to report"
  echo "  --no-color           Disable colored output"
  echo "  --absolute-paths     Show absolute file paths"
  exit 1
}

# Check if path is provided
if [ $# -eq 0 ]; then
  show_usage
fi

# Get the path argument
PATH_ARG=$1
shift

# Build the command arguments
ARGS=("$PATH_ARG")

# Parse the remaining arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --project)
      ARGS+=("--project" "$2")
      shift 2
      ;;
    --verbose)
      ARGS+=("--verbose")
      shift
      ;;
    --fix)
      ARGS+=("--fix")
      shift
      ;;
    --max-errors)
      ARGS+=("--max-errors" "$2")
      shift 2
      ;;
    --no-color)
      ARGS+=("--no-color")
      shift
      ;;
    --absolute-paths)
      ARGS+=("--absolute-paths")
      shift
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      ;;
  esac
done

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Run the TypeScript JSX checker
node "$SCRIPT_DIR/dist/tsx-check.js" "${ARGS[@]}"

# Return the exit code from the Node process
exit $?

#!/usr/bin/env node

import * as path from 'path';
import * as fs from 'fs';
import { Command } from 'commander';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TypeCheckOptions } from './core/checker';
import { TypeScriptReporter } from './core/reporter';

// Define the CLI program
const program = new Command();

program
  .name('ts-checker')
  .description('TypeScript error checker')
  .version('1.0.0')
  .argument('<path>', 'File or directory to check')
  .option('-p, --project <path>', 'Path to tsconfig.json')
  .option('-v, --verbose', 'Show verbose output')
  .option('-f, --fix', 'Try to fix errors (experimental)')
  .option('--include-node-modules', 'Include node_modules in the check')
  .option('--max-errors <number>', 'Maximum number of errors to report', parseInt)
  .option('--no-color', 'Disable colored output')
  .option('--absolute-paths', 'Show absolute file paths')
  .option('--tsx', 'Check .tsx files only')
  .option('--ts', 'Check .ts files only')
  .action(async (targetPath: string, options: any) => {
    try {
      // Resolve the target path
      const resolvedPath = path.resolve(targetPath);
      
      // Check if the path exists
      if (!fs.existsSync(resolvedPath)) {
        console.error(`Error: Path does not exist: ${resolvedPath}`);
        process.exit(1);
      }

      // Determine if it's a file or directory
      const isDirectory = fs.statSync(resolvedPath).isDirectory();

      // Create the checker options
      const checkerOptions: TypeCheckOptions = {
        project: options.project,
        verbose: options.verbose,
        fix: options.fix,
        includeNodeModules: options.includeNodeModules,
        maxErrors: options.maxErrors
      };

      // Create the reporter options
      const reporterOptions = {
        verbose: options.verbose,
        relativePaths: !options.absolutePaths,
        colorOutput: options.color
      };

      // Create the checker and reporter
      const checker = new TypeScriptChecker(checkerOptions);
      const reporter = new TypeScriptReporter(reporterOptions);

      // Determine the file pattern based on options
      let filePattern = '**/*.{ts,tsx}';
      if (options.tsx) {
        filePattern = '**/*.tsx';
      } else if (options.ts) {
        filePattern = '**/*.ts';
      }

      // Check the target
      if (isDirectory) {
        const summary = await checker.checkDirectory(resolvedPath, filePattern);
        reporter.printSummary(summary);
        
        // Exit with error code if there are errors
        if (summary.filesWithErrors > 0) {
          process.exit(1);
        }
      } else {
        const result = checker.checkFile(resolvedPath);
        console.log(reporter.formatFileResult(result));
        
        // Exit with error code if there are errors
        if (result.hasErrors) {
          process.exit(1);
        }
      }
    } catch (error) {
      console.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();

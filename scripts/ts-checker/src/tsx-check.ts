#!/usr/bin/env node

import * as path from 'path';
import { Command } from 'commander';
import { Type<PERSON>Che<PERSON> } from './core/checker';
import { TypeScriptReporter } from './core/reporter';

// Define the CLI program
const program = new Command();

program
  .name('tsx-check')
  .description('TypeScript JSX (.tsx) file checker')
  .version('1.0.0')
  .argument('<path>', 'File or directory to check')
  .option('-p, --project <path>', 'Path to tsconfig.json')
  .option('-v, --verbose', 'Show verbose output')
  .option('-f, --fix', 'Try to fix errors (experimental)')
  .option('--max-errors <number>', 'Maximum number of errors to report', parseInt)
  .option('--no-color', 'Disable colored output')
  .option('--absolute-paths', 'Show absolute file paths')
  .action(async (targetPath: string, options: any) => {
    try {
      const checker = new TypeScriptChecker({
        project: options.project,
        verbose: options.verbose,
        fix: options.fix,
        maxErrors: options.maxErrors
      });

      const reporter = new TypeScriptReporter({
        verbose: options.verbose,
        relativePaths: !options.absolutePaths,
        colorOutput: options.color
      });

      // Check if it's a directory or file
      const resolvedPath = path.resolve(targetPath);
      const fs = require('fs');
      const isDirectory = fs.statSync(resolvedPath).isDirectory();

      if (isDirectory) {
        // Check directory with .tsx files only
        const summary = await checker.checkDirectory(resolvedPath, '**/*.tsx');
        reporter.printSummary(summary);
        
        // Exit with error code if there are errors
        if (summary.filesWithErrors > 0) {
          process.exit(1);
        }
      } else {
        // Check single file
        const result = checker.checkFile(resolvedPath);
        console.log(reporter.formatFileResult(result));
        
        // Exit with error code if there are errors
        if (result.hasErrors) {
          process.exit(1);
        }
      }
    } catch (error) {
      console.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();

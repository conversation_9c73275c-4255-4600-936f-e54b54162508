import * as ts from 'typescript';
import * as path from 'path';
import * as fs from 'fs';
import { glob } from 'glob';

export interface TypeCheckOptions {
  project?: string;
  verbose?: boolean;
  fix?: boolean;
  includeNodeModules?: boolean;
  maxErrors?: number;
}

export interface TypeCheckResult {
  filePath: string;
  errors: TypeCheckError[];
  hasErrors: boolean;
}

export interface TypeCheckError {
  line: number;
  character: number;
  message: string;
  code: number;
  category: string;
  source?: string;
}

export interface TypeCheckSummary {
  totalFiles: number;
  filesWithErrors: number;
  totalErrors: number;
  results: TypeCheckResult[];
}

/**
 * TypeScript Checker class for checking TypeScript files for errors
 */
export class TypeScriptChecker {
  private compilerOptions: ts.CompilerOptions;
  private host: ts.CompilerHost;
  private program: ts.Program | null = null;

  constructor(private options: TypeCheckOptions = {}) {
    // Set default compiler options
    this.compilerOptions = {
      target: ts.ScriptTarget.ES2020,
      module: ts.ModuleKind.ESNext,
      moduleResolution: ts.ModuleResolutionKind.NodeJs,
      esModuleInterop: true,
      strict: true,
      skipLibCheck: true,
      forceConsistentCasingInFileNames: true,
      jsx: ts.JsxEmit.React,
      allowJs: true,
      checkJs: false,
    };

    // If a project is specified, load its compiler options
    if (options.project) {
      const configPath = path.resolve(options.project);
      if (fs.existsSync(configPath)) {
        const configFile = ts.readConfigFile(configPath, ts.sys.readFile);
        if (configFile.error) {
          throw new Error(`Error reading tsconfig: ${configFile.error.messageText}`);
        }
        
        const parsedConfig = ts.parseJsonConfigFileContent(
          configFile.config,
          ts.sys,
          path.dirname(configPath)
        );
        
        if (parsedConfig.errors.length > 0) {
          throw new Error(`Error parsing tsconfig: ${parsedConfig.errors[0].messageText}`);
        }
        
        this.compilerOptions = parsedConfig.options;
      } else {
        throw new Error(`Project file not found: ${configPath}`);
      }
    }

    // Create compiler host
    this.host = ts.createCompilerHost(this.compilerOptions);
  }

  /**
   * Check a single file for TypeScript errors
   * @param filePath Path to the file to check
   * @returns TypeCheckResult with errors if any
   */
  public checkFile(filePath: string): TypeCheckResult {
    const resolvedPath = path.resolve(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      return {
        filePath: resolvedPath,
        errors: [{
          line: 0,
          character: 0,
          message: `File not found: ${resolvedPath}`,
          code: 0,
          category: 'Error'
        }],
        hasErrors: true
      };
    }

    // Create a program with just this file
    this.program = ts.createProgram([resolvedPath], this.compilerOptions, this.host);
    
    // Get the source file
    const sourceFile = this.program.getSourceFile(resolvedPath);
    if (!sourceFile) {
      return {
        filePath: resolvedPath,
        errors: [{
          line: 0,
          character: 0,
          message: `Could not get source file: ${resolvedPath}`,
          code: 0,
          category: 'Error'
        }],
        hasErrors: true
      };
    }

    // Get diagnostics
    const syntacticDiagnostics = this.program.getSyntacticDiagnostics(sourceFile);
    const semanticDiagnostics = this.program.getSemanticDiagnostics(sourceFile);
    const allDiagnostics = [...syntacticDiagnostics, ...semanticDiagnostics];

    // Convert diagnostics to errors
    const errors = allDiagnostics.map(diagnostic => {
      let line = 0;
      let character = 0;
      
      if (diagnostic.file && diagnostic.start !== undefined) {
        const { line: lineNum, character: characterNum } = 
          diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
        line = lineNum + 1; // Convert to 1-based
        character = characterNum + 1; // Convert to 1-based
      }

      return {
        line,
        character,
        message: ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n'),
        code: diagnostic.code,
        category: ts.DiagnosticCategory[diagnostic.category],
        source: diagnostic.source
      };
    });

    return {
      filePath: resolvedPath,
      errors,
      hasErrors: errors.length > 0
    };
  }

  /**
   * Check a directory for TypeScript errors
   * @param dirPath Path to the directory to check
   * @param pattern Glob pattern for files to check
   * @returns TypeCheckSummary with results for all files
   */
  public async checkDirectory(dirPath: string, pattern: string = '**/*.{ts,tsx}'): Promise<TypeCheckSummary> {
    const resolvedPath = path.resolve(dirPath);
    
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`Directory not found: ${resolvedPath}`);
    }

    // Find all TypeScript files in the directory
    const files = await glob(pattern, { 
      cwd: resolvedPath, 
      absolute: true,
      ignore: this.options.includeNodeModules ? [] : ['**/node_modules/**']
    });

    if (files.length === 0) {
      return {
        totalFiles: 0,
        filesWithErrors: 0,
        totalErrors: 0,
        results: []
      };
    }

    // Create a program with all files
    this.program = ts.createProgram(files, this.compilerOptions, this.host);
    
    // Check each file
    const results: TypeCheckResult[] = [];
    let totalErrors = 0;
    let filesWithErrors = 0;

    for (const file of files) {
      const sourceFile = this.program.getSourceFile(file);
      if (!sourceFile) continue;

      // Get diagnostics
      const syntacticDiagnostics = this.program.getSyntacticDiagnostics(sourceFile);
      const semanticDiagnostics = this.program.getSemanticDiagnostics(sourceFile);
      const allDiagnostics = [...syntacticDiagnostics, ...semanticDiagnostics];

      // Convert diagnostics to errors
      const errors = allDiagnostics.map(diagnostic => {
        let line = 0;
        let character = 0;
        
        if (diagnostic.file && diagnostic.start !== undefined) {
          const { line: lineNum, character: characterNum } = 
            diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
          line = lineNum + 1; // Convert to 1-based
          character = characterNum + 1; // Convert to 1-based
        }

        return {
          line,
          character,
          message: ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n'),
          code: diagnostic.code,
          category: ts.DiagnosticCategory[diagnostic.category],
          source: diagnostic.source
        };
      });

      const result = {
        filePath: file,
        errors,
        hasErrors: errors.length > 0
      };

      results.push(result);
      
      if (result.hasErrors) {
        filesWithErrors++;
        totalErrors += errors.length;
      }

      // Apply max errors limit if specified
      if (this.options.maxErrors && totalErrors >= this.options.maxErrors) {
        break;
      }
    }

    return {
      totalFiles: files.length,
      filesWithErrors,
      totalErrors,
      results
    };
  }
}

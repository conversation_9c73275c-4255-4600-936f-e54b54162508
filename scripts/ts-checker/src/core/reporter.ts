import * as path from 'path';
import chalk from 'chalk';
import { TypeCheckResult, TypeCheckSummary, TypeCheckError } from './checker';

/**
 * Reporter class for formatting and displaying TypeScript check results
 */
export class TypeScriptReporter {
  private readonly cwd: string;

  constructor(private options: { 
    verbose?: boolean;
    relativePaths?: boolean;
    colorOutput?: boolean;
  } = {}) {
    this.cwd = process.cwd();
    
    // Set default options
    this.options = {
      verbose: false,
      relativePaths: true,
      colorOutput: true,
      ...options
    };
  }

  /**
   * Format a file path for display
   * @param filePath Absolute file path
   * @returns Formatted file path
   */
  private formatFilePath(filePath: string): string {
    if (this.options.relativePaths) {
      return path.relative(this.cwd, filePath);
    }
    return filePath;
  }

  /**
   * Format an error message with color
   * @param message Error message
   * @param category Error category
   * @returns Formatted error message
   */
  private formatErrorMessage(message: string, category: string): string {
    if (!this.options.colorOutput) {
      return message;
    }

    switch (category) {
      case 'Error':
        return chalk.red(message);
      case 'Warning':
        return chalk.yellow(message);
      case 'Suggestion':
        return chalk.blue(message);
      case 'Message':
        return chalk.gray(message);
      default:
        return message;
    }
  }

  /**
   * Format a single error for display
   * @param error TypeCheck error
   * @returns Formatted error string
   */
  private formatError(error: TypeCheckError): string {
    const location = `${error.line}:${error.character}`;
    const formattedMessage = this.formatErrorMessage(error.message, error.category);
    const errorCode = error.code ? `TS${error.code}` : '';
    
    return `  ${chalk.gray(location)} - ${formattedMessage} ${chalk.gray(errorCode)}`;
  }

  /**
   * Format a file result for display
   * @param result TypeCheck result for a file
   * @returns Formatted result string
   */
  public formatFileResult(result: TypeCheckResult): string {
    if (!result.hasErrors) {
      if (this.options.verbose) {
        return `${chalk.green('✓')} ${this.formatFilePath(result.filePath)}`;
      }
      return '';
    }

    const filePath = this.formatFilePath(result.filePath);
    const header = `${chalk.red('✗')} ${chalk.underline(filePath)} (${result.errors.length} ${result.errors.length === 1 ? 'error' : 'errors'})`;
    
    const errorLines = result.errors.map(error => this.formatError(error));
    return [header, ...errorLines].join('\n');
  }

  /**
   * Format a summary of all results
   * @param summary TypeCheck summary
   * @returns Formatted summary string
   */
  public formatSummary(summary: TypeCheckSummary): string {
    const { totalFiles, filesWithErrors, totalErrors, results } = summary;
    
    const fileResults = results
      .filter(result => this.options.verbose || result.hasErrors)
      .map(result => this.formatFileResult(result))
      .filter(Boolean);
    
    let output = fileResults.join('\n\n');
    
    if (output) {
      output += '\n\n';
    }
    
    if (filesWithErrors === 0) {
      output += chalk.green(`✓ All ${totalFiles} files pass type checking`);
    } else {
      output += chalk.red(`✗ Found ${totalErrors} ${totalErrors === 1 ? 'error' : 'errors'} in ${filesWithErrors} ${filesWithErrors === 1 ? 'file' : 'files'} (${totalFiles} files checked)`);
    }
    
    return output;
  }

  /**
   * Print a summary to the console
   * @param summary TypeCheck summary
   */
  public printSummary(summary: TypeCheckSummary): void {
    console.log(this.formatSummary(summary));
  }
}

/**
 * Catalog of common TypeScript errors encountered in the project
 * This can be used as a reference for error patterns and solutions
 */

export interface CommonError {
  code: number;
  description: string;
  example: string;
  solution: string;
}

export const commonErrors: CommonError[] = [
  {
    code: 2304,
    description: "Cannot find name 'X'",
    example: "Cannot find name 'id'",
    solution: "Ensure the variable is declared before use. For dynamic route parameters in Next.js 15, extract them from the params object: const { id } = await params;"
  },
  {
    code: 2339,
    description: "Property 'X' does not exist on type 'Y'",
    example: "Property 'findWithPagination' does not exist on type 'CostCenterService'",
    solution: "Check the class definition to find the correct method name. In this case, use 'paginate' instead of 'findWithPagination'."
  },
  {
    code: 2345,
    description: "Argument of type 'X' is not assignable to parameter of type 'Y'",
    example: "Argument of type '{ path: string; select: string; }' is not assignable to parameter of type 'string'",
    solution: "Ensure the argument matches the expected type. Use type assertions if necessary: ['manager', 'department'] as any"
  },
  {
    code: 2322,
    description: "Type 'X' is not assignable to type 'Y'",
    example: "Type 'string' is not assignable to type 'Date'",
    solution: "Convert the value to the expected type: new Date(stringDate)"
  },
  {
    code: 2531,
    description: "Object is possibly 'null'",
    example: "Object is possibly 'null'",
    solution: "Add a null check before accessing properties: if (obj) { obj.property }"
  },
  {
    code: 2532,
    description: "Object is possibly 'undefined'",
    example: "Object is possibly 'undefined'",
    solution: "Add an undefined check: if (obj !== undefined) { obj.property }"
  },
  {
    code: 2554,
    description: "Expected X arguments, but got Y",
    example: "Expected 2 arguments, but got 1",
    solution: "Check the function signature and provide all required arguments"
  },
  {
    code: 2769,
    description: "No overload matches this call",
    example: "No overload matches this call",
    solution: "Check the function signature and ensure arguments match one of the overloads"
  },
  {
    code: 2307,
    description: "Cannot find module 'X' or its corresponding type declarations",
    example: "Cannot find module '@/lib/backend/logger'",
    solution: "Check the import path. It might need to be updated to the correct location: '@/lib/utils/logger'"
  },
  {
    code: 2416,
    description: "Property 'X' in type 'Y' is not assignable to the same property in base type 'Z'",
    example: "Property 'params' in type '{ params: { id: string; }; }' is not assignable to the same property in base type '{ params: Promise<{ id: string; }>; }'",
    solution: "Update the type to match the base type: { params: Promise<{ id: string }> }"
  },
  {
    code: 2741,
    description: "Property 'X' is missing in type 'Y' but required in type 'Z'",
    example: "Property 'id' is missing in type '{}' but required in type '{ id: string; }'",
    solution: "Add the missing property: { id: 'value' }"
  },
  {
    code: 2352,
    description: "Conversion of type 'X' to type 'Y' may be a mistake",
    example: "Conversion of type 'string' to type 'number' may be a mistake",
    solution: "Use explicit conversion: parseInt(stringValue) or Number(stringValue)"
  },
  {
    code: 2571,
    description: "Object is of type 'unknown'",
    example: "Object is of type 'unknown'",
    solution: "Use type assertion or type guard: (obj as MyType).property or if (typeof obj === 'string') { ... }"
  },
  {
    code: 2339,
    description: "Property 'X' does not exist on type 'unknown'",
    example: "Property '_id' does not exist on type 'unknown'",
    solution: "Use type assertion: (budget as any)._id or add a type guard: if (budget && '_id' in budget) { budget._id }"
  },
  {
    code: 2345,
    description: "Argument of type 'Buffer' is not assignable to parameter of type 'BodyInit'",
    example: "Argument of type 'Buffer' is not assignable to parameter of type 'BodyInit'",
    solution: "Use type assertion: return new NextResponse(buffer as unknown as BodyInit, { ... })"
  },
  {
    code: 2322,
    description: "Type 'Promise<{ id: string; }>' is not assignable to type '{ id: string; }'",
    example: "Type 'Promise<{ id: string; }>' is not assignable to type '{ id: string; }'",
    solution: "Await the Promise: const { id } = await params;"
  },
  {
    code: 2454,
    description: "Variable 'X' is used before being assigned",
    example: "Variable 'id' is used before being assigned",
    solution: "Initialize the variable with a default value: let id: string = 'unknown';"
  },
  {
    code: 2365,
    description: "Operator 'X' cannot be applied to types 'Y' and 'Z'",
    example: "Operator '+' cannot be applied to types 'number | undefined' and 'number'",
    solution: "Add a check or use nullish coalescing: (value ?? 0) + 10"
  },
  {
    code: 2339,
    description: "Property 'X' does not exist on type 'Y[]'",
    example: "Property 'length' does not exist on type '{}[]'",
    solution: "Ensure the array is properly typed or use optional chaining: array?.length"
  },
  {
    code: 2349,
    description: "This expression is not callable",
    example: "This expression is not callable. Type 'typeof import(...)' has no call signatures",
    solution: "Import the function correctly: import { myFunction } from './module'"
  }
];

/**
 * Get a common error by its code
 * @param code Error code
 * @returns Common error or undefined if not found
 */
export function getCommonError(code: number): CommonError | undefined {
  return commonErrors.find(error => error.code === code);
}

/**
 * Get suggestions for an error message
 * @param message Error message
 * @returns Array of possible solutions
 */
export function getSuggestions(message: string): string[] {
  const suggestions: string[] = [];
  
  // Check for property not existing on type
  if (message.includes('does not exist on type')) {
    const propertyMatch = message.match(/Property '([^']+)' does not exist/);
    const typeMatch = message.match(/on type '([^']+)'/);
    
    if (propertyMatch && typeMatch) {
      const property = propertyMatch[1];
      const type = typeMatch[1];
      
      if (type === 'unknown') {
        suggestions.push(`Use type assertion: (obj as any).${property}`);
        suggestions.push(`Add a type guard: if (obj && '${property}' in obj) { obj.${property} }`);
      } else if (type.includes('ObjectId')) {
        suggestions.push(`Convert ObjectId to string: ${property}.toString()`);
      }
    }
  }
  
  // Check for cannot find name
  if (message.includes('Cannot find name')) {
    const nameMatch = message.match(/Cannot find name '([^']+)'/);
    
    if (nameMatch) {
      const name = nameMatch[1];
      suggestions.push(`Declare the variable before use: let ${name}: any;`);
      
      if (name === 'id' || name === 'slug' || name === 'params') {
        suggestions.push(`For Next.js 15 dynamic routes, extract from params: const { ${name} } = await params;`);
      }
    }
  }
  
  return suggestions;
}

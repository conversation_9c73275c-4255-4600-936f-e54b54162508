# Common TypeScript Errors and Solutions

This document catalogs common TypeScript errors encountered in the project and their solutions.

## Next.js 15 Specific Errors

### 1. Dynamic Route Parameters

**Error:**
```
Cannot find name 'id'.ts(2304)
```

**Solution:**
In Next.js 15, dynamic route parameters are passed as Promises and must be awaited:

```typescript
// Before (Next.js 14)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  // ...
}

// After (Next.js 15)
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  // ...
}
```

### 2. Route Handler Return Types

**Error:**
```
Type 'Promise<Response>: Promise<NextResponse>' is not assignable to type 'Promise<NextResponse>'
```

**Solution:**
Fix duplicate Promise types in return type declarations:

```typescript
// Before (incorrect)
export async function GET(): Promise<Response>: Promise<NextResponse> {
  // ...
}

// After (correct)
export async function GET(): Promise<NextResponse> {
  // ...
}
```

## MongoDB/Mongoose Errors

### 1. ObjectId Properties

**Error:**
```
Property 'name' does not exist on type 'ObjectId'.ts(2339)
```

**Solution:**
Use type assertions when accessing properties on populated fields:

```typescript
// Before
const categoryName = category._id.name;

// After
const categoryName = (category as any).name;
// OR
const categoryObj = category as any;
const categoryName = categoryObj.name;
```

### 2. Unknown Type from Lean Queries

**Error:**
```
'budget._id' is of type 'unknown'.ts(18046)
```

**Solution:**
Add type checking and use type assertions:

```typescript
// Before
const id = budget._id.toString();

// After
const budgetId = budget && budget._id ? (budget._id as any).toString() : 'unknown';
```

### 3. MongoDB Query Operators

**Error:**
```
Property '$gte' does not exist on type '{}'. ts(2339)
```

**Solution:**
Use an intermediate variable with the `any` type:

```typescript
// Before
if (startDate) query.date.$gte = startDate;
if (endDate) query.date.$lte = endDate;

// After
const dateQuery: any = {};
if (startDate) dateQuery.$gte = startDate;
if (endDate) dateQuery.$lte = endDate;
query.date = dateQuery;
```

## Type Conversion Errors

### 1. String to Date Conversion

**Error:**
```
Type 'string' is not assignable to type 'Date'. ts(2322)
```

**Solution:**
Explicitly convert strings to Date objects:

```typescript
// Before
const budget = {
  startDate: data.startDate,
  endDate: data.endDate
};

// After
const budget = {
  startDate: new Date(data.startDate),
  endDate: new Date(data.endDate)
};
```

### 2. Buffer to BodyInit Conversion

**Error:**
```
Argument of type 'Buffer' is not assignable to parameter of type 'BodyInit'. ts(2345)
```

**Solution:**
Use type assertions to convert Buffer to BodyInit:

```typescript
// Before
return new NextResponse(buffer, { headers: { ... } });

// After
return new NextResponse(buffer as unknown as BodyInit, { headers: { ... } });
```

## Function and Method Errors

### 1. Missing Method

**Error:**
```
Property 'findWithPagination' does not exist on type 'CostCenterService'. ts(2339)
```

**Solution:**
Check the class definition and use the correct method name:

```typescript
// Before
const result = await costCenterService.findWithPagination(query, options);

// After
const result = await costCenterService.paginate(query, page, limit, sort, populate);
```

### 2. Incorrect Parameter Types

**Error:**
```
Type '{ path: string; select: string; }' is not assignable to type 'string'. ts(2345)
```

**Solution:**
Adjust the parameters to match the expected types:

```typescript
// Before
const result = await service.findById(
  id,
  [
    { path: 'manager', select: 'firstName lastName' },
    { path: 'department', select: 'name' }
  ]
);

// After
const result = await service.findById(
  id,
  ['manager', 'department'] as any
);
```

## Variable Declaration and Scope Errors

### 1. Variable Used Before Declaration

**Error:**
```
Block-scoped variable 'id' used before its declaration. ts(2448)
```

**Solution:**
Declare variables at the beginning of the function scope:

```typescript
// Before
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // ...
    const { id } = await params;
    // ...
  } catch (error) {
    logger.error(`Error deleting item: ${id}`, error); // Error: id not in scope
  }
}

// After
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  let id: string = 'unknown';
  try {
    // ...
    const resolvedParams = await params;
    id = resolvedParams.id;
    // ...
  } catch (error) {
    logger.error(`Error deleting item: ${id}`, error); // Works fine
  }
}
```

### 2. Cannot Redeclare Variable

**Error:**
```
Cannot redeclare block-scoped variable 'id'. ts(2451)
```

**Solution:**
Avoid declaring the same variable multiple times:

```typescript
// Before
const { id } = await params;
// ...
const { id } = await anotherPromise; // Error

// After
const { id } = await params;
// ...
const { anotherValue } = await anotherPromise;
```

## Import Errors

### 1. Incorrect Import Paths

**Error:**
```
Cannot find module '@/lib/backend/logger' or its corresponding type declarations. ts(2307)
```

**Solution:**
Update import paths to match the project structure:

```typescript
// Before
import { logger, LogCategory } from '@/lib/backend/logger';

// After
import { logger } from '@/lib/utils/logger';
```

## Authentication Errors

### 1. Missing Request Parameter

**Error:**
```
Expected 1 arguments, but got 0. ts(2554)
```

**Solution:**
Pass the request object to authentication functions:

```typescript
// Before
const user = await getCurrentUser();

// After
const user = await getCurrentUser(req);
```

## Best Practices for Avoiding TypeScript Errors

1. **Use Type Assertions Carefully**: Only use `as any` when necessary and try to limit its scope.
2. **Check for Null/Undefined**: Always check if objects exist before accessing their properties.
3. **Provide Default Values**: Use nullish coalescing (`??`) or logical OR (`||`) to provide default values.
4. **Understand Promise Handling**: Remember to await Promises and handle errors properly.
5. **Keep Import Paths Updated**: Ensure import paths match the actual file structure.
6. **Use Type Guards**: Implement type guards to narrow types safely.
7. **Document Type Issues**: Comment on complex type assertions to explain why they're necessary.

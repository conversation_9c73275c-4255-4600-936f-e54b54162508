# PowerShell script to run TypeScript JSX checker

param (
    [Parameter(Position=0, Mandatory=$true)]
    [string]$Path,
    
    [Parameter()]
    [string]$Project,
    
    [Parameter()]
    [switch]$Verbose,
    
    [Parameter()]
    [switch]$Fix,
    
    [Parameter()]
    [int]$MaxErrors,
    
    [Parameter()]
    [switch]$NoColor,
    
    [Parameter()]
    [switch]$AbsolutePaths
)

# Build the command arguments
$arguments = @($Path)

if ($Project) {
    $arguments += "--project"
    $arguments += $Project
}

if ($Verbose) {
    $arguments += "--verbose"
}

if ($Fix) {
    $arguments += "--fix"
}

if ($MaxErrors) {
    $arguments += "--max-errors"
    $arguments += $MaxErrors
}

if ($NoColor) {
    $arguments += "--no-color"
}

if ($AbsolutePaths) {
    $arguments += "--absolute-paths"
}

# Get the directory of this script
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Run the TypeScript JSX checker
& node "$scriptDir\dist\tsx-check.js" @arguments

# Return the exit code from the Node process
exit $LASTEXITCODE

# TypeScript Checker

A standalone utility for checking TypeScript errors in projects.

## Features

- Scan directories for TypeScript errors
- Check individual files or entire directories
- Support for both .ts and .tsx files
- Detailed error reporting
- Platform-independent runners

## Usage

### Command Line

```bash
# Check a specific file
npm run ts-check -- path/to/file.ts

# Check a directory
npm run ts-check -- path/to/directory

# Check .tsx files
npm run tsx-check -- path/to/file.tsx

# Check with specific tsconfig
npm run ts-check -- path/to/directory --project path/to/tsconfig.json
```

### Windows PowerShell

```powershell
.\scripts\ts-checker\run-ts-check.ps1 path/to/directory
```

### macOS/Linux

```bash
./scripts/ts-checker/run-ts-check.sh path/to/directory
```

## Common Errors

The checker identifies common TypeScript errors including:

1. Missing imports
2. Type mismatches
3. Property access on undefined/null
4. Incorrect function parameters
5. Promise handling issues
6. Missing properties on objects
7. Incorrect usage of generics
8. Module resolution issues

## Integration

This utility can be used as a standalone package or integrated into your CI/CD pipeline for automated type checking.

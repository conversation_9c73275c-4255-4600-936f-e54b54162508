{"name": "ts-checker", "version": "1.0.0", "description": "TypeScript error checker utility", "main": "dist/cli.js", "bin": {"ts-checker": "dist/cli.js", "ts-check": "dist/ts-check.js", "tsx-check": "dist/tsx-check.js"}, "scripts": {"build": "tsc", "start": "node dist/cli.js", "ts-check": "node dist/ts-check.js", "tsx-check": "node dist/tsx-check.js", "prepublishOnly": "npm run build"}, "keywords": ["typescript", "checker", "linter", "static-analysis"], "author": "", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^9.4.1", "glob": "^8.0.3", "typescript": "^4.9.5"}, "devDependencies": {"@types/glob": "^8.0.0", "@types/node": "^18.11.18"}, "engines": {"node": ">=14.0.0"}}
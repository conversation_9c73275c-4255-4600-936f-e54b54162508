# Manual Usage of TypeScript Checker

This document explains how to use the TypeScript checker manually without integrating it into the main project's package.json scripts.

## Overview

The TypeScript checker is a standalone utility that can scan your project for TypeScript errors. It's designed to be used manually during development to identify and fix TypeScript issues.

## Installation

The TypeScript checker is already set up in the `scripts/ts-checker` directory. To use it, you need to install its dependencies:

```bash
cd scripts/ts-checker
npm install
npm run build
```

This will compile the TypeScript checker and make it ready for use.

## Manual Usage

### Checking TypeScript (.ts) Files

To check a specific TypeScript file or directory:

```bash
# Windows PowerShell
.\scripts\ts-checker\run-ts-check.ps1 path/to/file.ts
.\scripts\ts-checker\run-ts-check.ps1 path/to/directory

# macOS/Linux
./scripts/ts-checker/run-ts-check.sh path/to/file.ts
./scripts/ts-checker/run-ts-check.sh path/to/directory
```

### Checking TypeScript JSX (.tsx) Files

To check a specific TypeScript JSX file or directory:

```bash
# Windows PowerShell
.\scripts\ts-checker\run-tsx-check.ps1 path/to/file.tsx
.\scripts\ts-checker\run-tsx-check.ps1 path/to/directory

# macOS/Linux
./scripts/ts-checker/run-tsx-check.sh path/to/file.tsx
./scripts/ts-checker/run-tsx-check.sh path/to/directory
```

### Common Options

You can pass additional options to the checker:

```bash
# Check with verbose output
.\scripts\ts-checker\run-ts-check.ps1 path/to/directory --verbose

# Check with a specific tsconfig.json
.\scripts\ts-checker\run-ts-check.ps1 path/to/directory --project path/to/tsconfig.json

# Limit the number of errors reported
.\scripts\ts-checker\run-ts-check.ps1 path/to/directory --max-errors 50
```

## Common Use Cases

### Checking API Routes

```bash
.\scripts\ts-checker\run-ts-check.ps1 app/api
```

### Checking Library Code

```bash
.\scripts\ts-checker\run-ts-check.ps1 lib
```

### Checking React Components

```bash
.\scripts\ts-checker\run-tsx-check.ps1 components
```

## Why Manual Usage?

We've chosen to use the TypeScript checker manually rather than integrating it into the project's package.json scripts to avoid potential issues during Vercel deployment. This approach allows us to:

1. Use the checker during development without affecting the build process
2. Avoid adding dependencies that might conflict with Vercel's build environment
3. Keep the project's package.json clean and focused on essential scripts

## Troubleshooting

If you encounter issues with the TypeScript checker:

1. Make sure you've installed the dependencies in the `scripts/ts-checker` directory
2. Check that you've built the checker with `npm run build`
3. Verify that you're using the correct script for the file type (.ts or .tsx)
4. Try running with the `--verbose` flag for more detailed output

## Reference

For more information about the TypeScript checker, see the [README.md](./README.md) and [COMMON_ERRORS.md](./COMMON_ERRORS.md) files in the `scripts/ts-checker` directory.

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Ensure the TypeScript compiler is installed
try {
  execSync('npx tsc --version', { stdio: 'ignore' });
  console.log('TypeScript is installed.');
} catch (error) {
  console.error('TypeScript is not installed. Please install it with: npm install -g typescript');
  process.exit(1);
}

// Compile the TypeScript scanner
console.log('Compiling the component scanner...');
try {
  execSync('npx tsc --esModuleInterop scripts/component-scanner.ts', { stdio: 'inherit' });
  console.log('Scanner compiled successfully.');
} catch (error) {
  console.error('Failed to compile the scanner:', error.message);
  process.exit(1);
}

// Run the compiled scanner
console.log('Running the component scanner...');
try {
  execSync('node scripts/component-scanner.js', { stdio: 'inherit' });
  console.log('<PERSON>anner completed successfully.');
  
  // Check if results file exists
  const resultsPath = path.join(process.cwd(), 'component-scan-results.json');
  if (fs.existsSync(resultsPath)) {
    console.log(`Results saved to: ${resultsPath}`);
  } else {
    console.warn('No results file was generated.');
  }
} catch (error) {
  console.error('Error running the scanner:', error.message);
  process.exit(1);
}

#!/bin/bash

# TCM Enterprise Suite - Development Workflow Setup Script
# This script sets up the development workflow and branching strategy

set -e  # Exit on any error

echo "🚀 Setting up TCM Enterprise Development Workflow..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository. Please run this script from the project root."
    exit 1
fi

# Check if we're on main branch
current_branch=$(git branch --show-current)
if [ "$current_branch" != "main" ]; then
    print_warning "Not on main branch. Switching to main..."
    git checkout main
fi

# Ensure main is up to date
print_status "Updating main branch..."
git pull origin main

# Create development branch if it doesn't exist
if git show-ref --verify --quiet refs/heads/development; then
    print_warning "Development branch already exists"
else
    print_status "Creating development branch..."
    git checkout -b development
    git push -u origin development
    print_success "Development branch created and pushed to remote"
fi

# Switch to development branch
print_status "Switching to development branch..."
git checkout development

# Create .github directories if they don't exist
print_status "Setting up GitHub workflow directories..."
mkdir -p .github/workflows
mkdir -p .github/ISSUE_TEMPLATE

# Create pull request template
print_status "Creating pull request template..."
cat > .github/pull_request_template.md << 'EOF'
## 📋 Description
Brief description of changes made.

## 🎯 Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Refactoring (no functional changes)

## 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Accessibility testing completed

## 📱 Screenshots/Videos
(If applicable, add screenshots or videos demonstrating the changes)

## 📝 Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes

## 🔗 Related Issues
Closes #(issue number)
EOF

# Create bug report template
print_status "Creating issue templates..."
cat > .github/ISSUE_TEMPLATE/bug_report.yml << 'EOF'
name: Bug Report
description: Create a report to help us improve
title: '[BUG] '
labels: ['type: bugfix', 'status: review-needed']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
    validations:
      required: true
  
  - type: dropdown
    id: browsers
    attributes:
      label: What browsers are you seeing the problem on?
      multiple: true
      options:
        - Firefox
        - Chrome
        - Safari
        - Microsoft Edge
  
  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
EOF

# Create feature request template
cat > .github/ISSUE_TEMPLATE/feature_request.yml << 'EOF'
name: Feature Request
description: Suggest an idea for this project
title: '[FEATURE] '
labels: ['type: feature', 'status: review-needed']
body:
  - type: textarea
    id: feature-description
    attributes:
      label: Feature Description
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  
  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe the use case and why this feature would be valuable.
    validations:
      required: true
  
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: A clear and concise description of any alternative solutions or features you've considered.
EOF

# Create VS Code settings
print_status "Creating VS Code configuration..."
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "files.exclude": {
    "**/.next": true,
    "**/node_modules": true,
    "**/.git": true
  },
  "search.exclude": {
    "**/.next": true,
    "**/node_modules": true
  },
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,
  "editor.tabSize": 2,
  "editor.insertSpaces": true
}
EOF

# Create .vscode/extensions.json for recommended extensions
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
EOF

# Install husky if not already installed
if [ ! -d ".husky" ]; then
    print_status "Setting up git hooks with husky..."
    npm install --save-dev husky
    npm run prepare
    
    # Create pre-commit hook
    npx husky add .husky/pre-commit "npm run lint:fix && npm run format"
    
    # Create pre-push hook
    npx husky add .husky/pre-push "npm run test:full"
    
    print_success "Git hooks configured"
fi

# Create .gitignore additions if needed
print_status "Updating .gitignore..."
cat >> .gitignore << 'EOF'

# Development workflow
.vscode/settings.local.json
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port
EOF

# Commit all the workflow setup files
print_status "Committing workflow setup files..."
git add .
git commit -m "chore: setup development workflow and branching strategy

- Add GitHub Actions workflows for CI/CD
- Add branch protection documentation
- Add PR and issue templates
- Configure VS Code settings
- Setup git hooks with husky
- Add comprehensive development guidelines"

# Push to development branch
print_status "Pushing changes to development branch..."
git push origin development

print_success "Development workflow setup complete!"

echo ""
echo "🎉 Next Steps:"
echo "1. Go to GitHub repository settings"
echo "2. Configure branch protection rules (see docs/REPOSITORY_SETUP.md)"
echo "3. Add required secrets for CI/CD (see docs/REPOSITORY_SETUP.md)"
echo "4. Set up Vercel and Railway development environments"
echo "5. Create your first feature branch: git checkout -b feature/your-feature-name"
echo ""
echo "📚 Documentation:"
echo "- Development Workflow: docs/DEVELOPMENT_WORKFLOW.md"
echo "- Repository Setup: docs/REPOSITORY_SETUP.md"
echo ""
print_success "Happy coding! 🚀"

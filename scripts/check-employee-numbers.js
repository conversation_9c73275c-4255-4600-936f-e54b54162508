/**
 * <PERSON><PERSON><PERSON> to check employee numbers in the database
 * This script finds all employees with null employeeNumber and logs them
 * 
 * Run with: node scripts/check-employee-numbers.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Define the Employee schema
const employeeSchema = new mongoose.Schema({
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  employeeNumber: {
    type: String,
    trim: true,
    unique: true,
    required: true
  },
  firstName: String,
  lastName: String,
  // Other fields not needed for this script
}, { 
  timestamps: true,
  strict: false // Allow other fields in the document
});

// Create the Employee model
const Employee = mongoose.model('Employee', employeeSchema);

// Main function to check employee numbers
async function checkEmployeeNumbers() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Count total employees
    const totalEmployees = await Employee.countDocuments({});
    console.log(`Total employees in database: ${totalEmployees}`);

    // Find all employees with null employeeNumber
    const employeesWithNullNumber = await Employee.find({ 
      $or: [
        { employeeNumber: null },
        { employeeNumber: { $exists: false } }
      ]
    });

    console.log(`Found ${employeesWithNullNumber.length} employees with null or missing employeeNumber`);

    // Log each employee with null employeeNumber
    if (employeesWithNullNumber.length > 0) {
      console.log('\nEmployees with null employeeNumber:');
      employeesWithNullNumber.forEach((employee, index) => {
        console.log(`${index + 1}. ${employee.firstName} ${employee.lastName} (${employee._id})`);
      });
    }

    // Find all employees (to see what's in the database)
    const allEmployees = await Employee.find({}).limit(10);
    
    console.log('\nSample of employees in database:');
    allEmployees.forEach((employee, index) => {
      console.log(`${index + 1}. ${employee.firstName} ${employee.lastName} (ID: ${employee._id})`);
      console.log(`   employeeId: ${employee.employeeId}`);
      console.log(`   employeeNumber: ${employee.employeeNumber}`);
      console.log(`   email: ${employee.email}`);
      console.log('---');
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the check
checkEmployeeNumbers().catch(console.error);

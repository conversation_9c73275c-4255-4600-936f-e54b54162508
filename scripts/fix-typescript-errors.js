const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Input file (output from typescript-error-scanner.ts)
  inputFile: 'typescript-errors.json',
  // Whether to create backups before modifying files
  createBackups: true,
  // Whether to actually apply fixes (false for dry run)
  applyFixes: true,
  // Categories of errors to fix automatically
  fixableCategories: {
    'Object is of type unknown': true,
    'Object is possibly null': true,
    'Object is possibly undefined': true,
    'Object is possibly null or undefined': true,
    'Parameter implicitly has an any type': true,
    'Binding element implicitly has an any type': true,
    'Element implicitly has an any type': true,
  }
};

// Read the error data
const errorData = JSON.parse(fs.readFileSync(config.inputFile, 'utf8'));
const { errors, errorsByFile } = errorData;

// Track statistics
const stats = {
  filesProcessed: 0,
  filesModified: 0,
  errorCount: errors.length,
  fixedCount: 0,
  fixesByCategory: {}
};

// Fix functions for different error categories
const fixers = {
  // Fix for 'Object is of type unknown'
  'Object is of type unknown': (fileContent, error) => {
    const lines = fileContent.split('\n');
    const errorLine = lines[error.line - 1];
    
    // Check if it's accessing a property on an unknown object
    const propertyAccessMatch = errorLine.match(/(\w+)\.(\w+)/);
    if (propertyAccessMatch) {
      const [fullMatch, objectName, propertyName] = propertyAccessMatch;
      
      // Replace with type check
      const fixedLine = errorLine.replace(
        `${objectName}.${propertyName}`,
        `(typeof ${objectName} === 'object' && ${objectName} !== null && '${propertyName}' in ${objectName} ? (${objectName} as any).${propertyName} : undefined)`
      );
      
      lines[error.line - 1] = fixedLine;
      return lines.join('\n');
    }
    
    // If it's a catch block with err.message
    if (errorLine.includes('catch') && errorLine.includes('.message')) {
      // Find the catch block
      let catchBlockStart = error.line - 1;
      while (catchBlockStart >= 0 && !lines[catchBlockStart].includes('catch')) {
        catchBlockStart--;
      }
      
      if (catchBlockStart >= 0) {
        // Check if it's already typed
        const catchLine = lines[catchBlockStart];
        if (catchLine.includes('catch (') && !catchLine.includes('Error')) {
          // Add type check for Error
          const fixedLine = catchLine.replace(
            /catch\s*\(\s*(\w+)(\s*:\s*unknown)?\s*\)/,
            (match, varName) => `catch (${varName}) {\n  if (${varName} instanceof Error) {`
          );
          
          lines[catchBlockStart] = fixedLine;
          
          // Find the closing brace of the catch block
          let catchBlockEnd = catchBlockStart + 1;
          let braceCount = 1;
          while (catchBlockEnd < lines.length && braceCount > 0) {
            if (lines[catchBlockEnd].includes('{')) braceCount++;
            if (lines[catchBlockEnd].includes('}')) braceCount--;
            catchBlockEnd++;
          }
          
          // Add closing brace for the if statement
          if (catchBlockEnd < lines.length) {
            lines.splice(catchBlockEnd - 1, 0, '  } else {');
            lines.splice(catchBlockEnd, 0, `    // Handle non-Error case`);
            lines.splice(catchBlockEnd + 1, 0, `    console.error('Unknown error:', ${catchLine.match(/catch\s*\(\s*(\w+)/)[1]});`);
            lines.splice(catchBlockEnd + 2, 0, '  }');
          }
          
          return lines.join('\n');
        }
      }
    }
    
    return fileContent; // Return unchanged if no fix applied
  },
  
  // Fix for 'Object is possibly null' or 'Object is possibly undefined'
  'Object is possibly null': (fileContent, error) => {
    const lines = fileContent.split('\n');
    const errorLine = lines[error.line - 1];
    
    // Add optional chaining
    const propertyAccessMatch = errorLine.match(/(\w+)\.(\w+)/);
    if (propertyAccessMatch) {
      const [fullMatch, objectName, propertyName] = propertyAccessMatch;
      const fixedLine = errorLine.replace(
        `${objectName}.${propertyName}`,
        `${objectName}?.${propertyName}`
      );
      
      lines[error.line - 1] = fixedLine;
      return lines.join('\n');
    }
    
    return fileContent; // Return unchanged if no fix applied
  },
  
  // Reuse the same fix for 'Object is possibly undefined'
  'Object is possibly undefined': function(fileContent, error) {
    return this['Object is possibly null'](fileContent, error);
  },
  
  // Reuse the same fix for 'Object is possibly null or undefined'
  'Object is possibly null or undefined': function(fileContent, error) {
    return this['Object is possibly null'](fileContent, error);
  },
  
  // Fix for 'Parameter implicitly has an any type'
  'Parameter implicitly has an any type': (fileContent, error) => {
    const lines = fileContent.split('\n');
    const errorLine = lines[error.line - 1];
    
    // Add 'unknown' type to parameters without type annotations
    const paramMatch = errorLine.match(/\(\s*(\w+)\s*\)/);
    if (paramMatch) {
      const [fullMatch, paramName] = paramMatch;
      const fixedLine = errorLine.replace(
        `(${paramName})`,
        `(${paramName}: unknown)`
      );
      
      lines[error.line - 1] = fixedLine;
      return lines.join('\n');
    }
    
    return fileContent; // Return unchanged if no fix applied
  },
  
  // Reuse similar fix for 'Binding element implicitly has an any type'
  'Binding element implicitly has an any type': (fileContent, error) => {
    const lines = fileContent.split('\n');
    const errorLine = lines[error.line - 1];
    
    // Add 'unknown' type to destructured elements without type annotations
    const destructureMatch = errorLine.match(/\{\s*(\w+)(\s*,\s*\w+)*\s*\}/);
    if (destructureMatch) {
      // This is a simplistic approach - a more robust solution would parse the entire destructuring pattern
      const fixedLine = errorLine.replace(
        /\{\s*(\w+)(\s*,\s*\w+)*\s*\}/,
        (match) => `${match}: { [key: string]: unknown }`
      );
      
      lines[error.line - 1] = fixedLine;
      return lines.join('\n');
    }
    
    return fileContent; // Return unchanged if no fix applied
  },
  
  // Similar fix for 'Element implicitly has an any type'
  'Element implicitly has an any type': function(fileContent, error) {
    return this['Parameter implicitly has an any type'](fileContent, error);
  }
};

// Process each file with errors
async function processFiles() {
  for (const [filePath, fileErrors] of Object.entries(errorsByFile)) {
    stats.filesProcessed++;
    
    // Skip files that don't exist
    if (!fs.existsSync(filePath)) {
      console.log(`Skipping non-existent file: ${filePath}`);
      continue;
    }
    
    // Read the file content
    let fileContent = fs.readFileSync(filePath, 'utf8');
    let originalContent = fileContent;
    let fileModified = false;
    
    // Group errors by category
    const errorsByCategory = {};
    for (const error of fileErrors) {
      if (!errorsByCategory[error.category]) {
        errorsByCategory[error.category] = [];
      }
      errorsByCategory[error.category].push(error);
    }
    
    // Process each category of errors
    for (const [category, categoryErrors] of Object.entries(errorsByCategory)) {
      // Skip categories we can't fix automatically
      if (!config.fixableCategories[category] || !fixers[category]) {
        continue;
      }
      
      console.log(`Fixing ${categoryErrors.length} '${category}' errors in ${filePath}`);
      
      // Sort errors by line number in descending order to avoid position shifts
      categoryErrors.sort((a, b) => b.line - a.line);
      
      // Apply fixes for each error
      for (const error of categoryErrors) {
        const fixedContent = fixers[category](fileContent, error);
        
        if (fixedContent !== fileContent) {
          fileContent = fixedContent;
          fileModified = true;
          stats.fixedCount++;
          
          // Track fixes by category
          stats.fixesByCategory[category] = (stats.fixesByCategory[category] || 0) + 1;
        }
      }
    }
    
    // Save the modified file
    if (fileModified && config.applyFixes) {
      stats.filesModified++;
      
      // Create backup if configured
      if (config.createBackups) {
        fs.writeFileSync(`${filePath}.bak`, originalContent);
        console.log(`Created backup at ${filePath}.bak`);
      }
      
      // Write the fixed content
      fs.writeFileSync(filePath, fileContent);
      console.log(`Applied fixes to ${filePath}`);
    }
  }
}

// Main function
async function main() {
  console.log('Fixing TypeScript errors...');
  
  try {
    await processFiles();
    
    // Print summary
    console.log('\nSummary:');
    console.log(`- Files processed: ${stats.filesProcessed}`);
    console.log(`- Files modified: ${stats.filesModified}`);
    console.log(`- Total errors: ${stats.errorCount}`);
    console.log(`- Errors fixed: ${stats.fixedCount}`);
    
    console.log('\nFixes by category:');
    for (const [category, count] of Object.entries(stats.fixesByCategory)) {
      console.log(`- ${category}: ${count} fixes`);
    }
    
    if (!config.applyFixes) {
      console.log('\nThis was a dry run. No files were modified.');
      console.log('Set config.applyFixes = true to apply the fixes.');
    }
    
  } catch (error) {
    console.error('Error fixing TypeScript errors:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);

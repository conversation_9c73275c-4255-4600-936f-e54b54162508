// scripts/create-sample-deleted-items.js
const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Import models
const DeletedItem = require('../models/audit/DeletedItems').default;
const User = require('../models/User').default;

const sampleDeletedItems = [
  {
    originalId: "inc_12345",
    originalModel: "Income",
    originalData: {
      reference: "INC-2025-001",
      amount: 150000,
      source: "government_subvention",
      description: "Government Grant Q1 2025",
      date: "2025-01-15",
      status: "approved"
    },
    deletionReason: "Duplicate entry created during data import process. Original entry was already recorded in the system.",
    deletionType: "single",
    deletedByUser: {
      id: "user123",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Finance Manager"
    },
    fiscalYear: "2025-2026",
    reviewStatus: "pending",
    complianceFlags: ["FINANCIAL_AUDIT_REQUIRED", "HIGH_VALUE_TRANSACTION"]
  },
  {
    originalId: "exp_67890",
    originalModel: "Expenditure",
    originalData: {
      expenditureNumber: "EXP-2025-002",
      amount: 75000,
      title: "Office Supplies Purchase",
      description: "Monthly office supplies for all departments",
      date: "2025-01-14"
    },
    deletionReason: "Data correction requested by supervisor on 2025-01-14. Amount was incorrectly entered.",
    deletionType: "bulk",
    deletedByUser: {
      id: "user456",
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "Accountant"
    },
    fiscalYear: "2025-2026",
    reviewStatus: "approved",
    complianceFlags: ["FINANCIAL_AUDIT_REQUIRED"]
  },
  {
    originalId: "emp_11111",
    originalModel: "Employee",
    originalData: {
      employeeNumber: "TCM-2024-001",
      firstName: "Michael",
      lastName: "Johnson",
      email: "<EMAIL>",
      department: "IT",
      position: "System Administrator"
    },
    deletionReason: "Employee left organization - contract terminated on 2025-01-13. All access revoked.",
    deletionType: "single",
    deletedByUser: {
      id: "user789",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      role: "HR Manager"
    },
    fiscalYear: "2025-2026",
    reviewStatus: "flagged",
    complianceFlags: ["HR_RECORD", "PERSONAL_DATA"]
  },
  {
    originalId: "inc_54321",
    originalModel: "Income",
    originalData: {
      reference: "INC-2025-003",
      amount: 25000,
      source: "registration_fees",
      description: "Teacher Registration Fees - January 2025",
      date: "2025-01-12"
    },
    deletionReason: "Incorrect fee calculation. Needs to be recalculated with updated fee structure.",
    deletionType: "single",
    deletedByUser: {
      id: "user101",
      name: "David Brown",
      email: "<EMAIL>",
      role: "Registration Officer"
    },
    fiscalYear: "2025-2026",
    reviewStatus: "investigated",
    complianceFlags: ["FINANCIAL_AUDIT_REQUIRED"]
  },
  {
    originalId: "bud_98765",
    originalModel: "Budget",
    originalData: {
      budgetNumber: "BUD-2025-001",
      title: "IT Infrastructure Budget",
      totalAmount: 500000,
      fiscalYear: "2025-2026",
      department: "IT"
    },
    deletionReason: "Budget restructuring required due to change in government funding allocation.",
    deletionType: "bulk",
    deletedByUser: {
      id: "user202",
      name: "Mary Johnson",
      email: "<EMAIL>",
      role: "Budget Manager"
    },
    fiscalYear: "2025-2026",
    reviewStatus: "pending",
    complianceFlags: ["FINANCIAL_AUDIT_REQUIRED", "HIGH_VALUE_TRANSACTION"]
  }
];

async function createSampleDeletedItems() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing deleted items (optional - remove this in production)
    await DeletedItem.deleteMany({});
    console.log('Cleared existing deleted items');

    // Create sample deleted items
    const createdItems = [];
    
    for (const itemData of sampleDeletedItems) {
      // Set deletion date to recent dates
      const daysAgo = Math.floor(Math.random() * 30); // Random date within last 30 days
      const deletedAt = new Date();
      deletedAt.setDate(deletedAt.getDate() - daysAgo);
      
      // Set recovery deadline (90 days from deletion)
      const recoveryDeadline = new Date(deletedAt);
      recoveryDeadline.setDate(recoveryDeadline.getDate() + 90);
      
      const deletedItem = new DeletedItem({
        ...itemData,
        deletedAt,
        recoveryDeadline,
        deletedBy: new mongoose.Types.ObjectId(), // Dummy ObjectId
        canBeRecovered: true
      });
      
      const saved = await deletedItem.save();
      createdItems.push(saved);
      console.log(`Created deleted item: ${saved.originalModel} - ${saved.originalData.reference || saved.originalData.name || saved.originalId}`);
    }

    console.log(`\n✅ Successfully created ${createdItems.length} sample deleted items`);
    console.log('\nSample data includes:');
    console.log('- Income transactions (2 items)');
    console.log('- Expenditure records (1 item)');
    console.log('- Employee records (1 item)');
    console.log('- Budget items (1 item)');
    console.log('\nReview statuses:');
    console.log('- Pending: 2 items');
    console.log('- Approved: 1 item');
    console.log('- Flagged: 1 item');
    console.log('- Investigated: 1 item');
    
    console.log('\n🎯 You can now test the auditors system at:');
    console.log('- Dashboard: http://localhost:3001/dashboard/auditors');
    console.log('- Deleted Items: http://localhost:3001/dashboard/auditors/deleted-items');

  } catch (error) {
    console.error('Error creating sample deleted items:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  createSampleDeletedItems();
}

module.exports = { createSampleDeletedItems };

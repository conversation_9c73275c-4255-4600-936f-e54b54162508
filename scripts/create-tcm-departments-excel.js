const XLSX = require('xlsx');
const path = require('path');

// TCM Complete Departments Data
const departmentsData = [
  {
    'name': 'Management',
    'Department Code': 'MGMT',
    'Description': 'Executive management and strategic oversight',
    'Status': 'Active',
    'Head of Department': 'Registrar',
    'Location': 'Head Office',
    'Established Date': '2010-01-01',
    'Budget Allocation': 5000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-001'
  },
  {
    'name': 'Registration and Licencing Directorate',
    'Department Code': 'RLD',
    'Description': 'Teacher registration and licensing services',
    'Status': 'Active',
    'Head of Department': 'Director of Registration and Licencing',
    'Location': 'Registration Wing',
    'Established Date': '2010-01-01',
    'Budget Allocation': 8000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-002'
  },
  {
    'name': 'Compliance Directorate',
    'Department Code': 'CD',
    'Description': 'Compliance monitoring and enforcement services',
    'Status': 'Active',
    'Head of Department': 'Director of Compliance Services',
    'Location': 'Compliance Wing',
    'Established Date': '2012-01-01',
    'Budget Allocation': 6000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-003'
  },
  {
    'name': 'Ethics and Professional Standards Section',
    'Department Code': 'EPSS',
    'Description': 'Professional ethics and standards enforcement',
    'Status': 'Active',
    'Head of Department': 'Monitoring and Enforcement Manager',
    'Location': 'Compliance Wing',
    'Established Date': '2012-01-01',
    'Budget Allocation': 3000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-004'
  },
  {
    'name': 'Finance Division',
    'Department Code': 'FD',
    'Description': 'Financial management and accounting services',
    'Status': 'Active',
    'Head of Department': 'Finance Manager',
    'Location': 'Administration Block',
    'Established Date': '2010-01-01',
    'Budget Allocation': 4000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-005'
  },
  {
    'name': 'Human Resource and Administration Division',
    'Department Code': 'HRAD',
    'Description': 'HR and administrative support services',
    'Status': 'Active',
    'Head of Department': 'Human Resource and Administration Manager',
    'Location': 'Administration Block',
    'Established Date': '2010-01-01',
    'Budget Allocation': 3500000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-006'
  },
  {
    'name': 'Education and Training Section',
    'Department Code': 'ETS',
    'Description': 'Professional development and training programs',
    'Status': 'Active',
    'Head of Department': 'Senior Monitoring and Enforcement Officer',
    'Location': 'Training Center',
    'Established Date': '2015-01-01',
    'Budget Allocation': 2500000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-007'
  },
  {
    'name': 'Investigations Section',
    'Department Code': 'IS',
    'Description': 'Professional misconduct investigations',
    'Status': 'Active',
    'Head of Department': 'Investigation Officer',
    'Location': 'Compliance Wing',
    'Established Date': '2013-01-01',
    'Budget Allocation': 2000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-008'
  },
  {
    'name': 'Planning Research and Evaluation Unit',
    'Department Code': 'PREU',
    'Description': 'Strategic planning research and evaluation',
    'Status': 'Active',
    'Head of Department': 'Planning Officer',
    'Location': 'Planning Office',
    'Established Date': '2014-01-01',
    'Budget Allocation': 1500000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-009'
  },
  {
    'name': 'Public Relations and Customer Experience Unit',
    'Department Code': 'PRCEU',
    'Description': 'Public relations and stakeholder engagement',
    'Status': 'Active',
    'Head of Department': 'Public Relations and Engagement Officer',
    'Location': 'Front Office',
    'Established Date': '2016-01-01',
    'Budget Allocation': 1000000,
    'Employee Count': 0,
    'Contact Email': '<EMAIL>',
    'Contact Phone': '+265-1-123-010'
  }
];

// Create workbook and worksheet
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.json_to_sheet(departmentsData);

// Set column widths for better readability
const columnWidths = [
  { wch: 35 }, // name
  { wch: 15 }, // Department Code
  { wch: 45 }, // Description
  { wch: 10 }, // Status
  { wch: 35 }, // Head of Department
  { wch: 20 }, // Location
  { wch: 15 }, // Established Date
  { wch: 15 }, // Budget Allocation
  { wch: 15 }, // Employee Count
  { wch: 25 }, // Contact Email
  { wch: 18 }  // Contact Phone
];

worksheet['!cols'] = columnWidths;

// Add worksheet to workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'TCM Departments');

// Create output directory if it doesn't exist
const fs = require('fs');
const outputDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Write the Excel file
const outputPath = path.join(outputDir, 'TCM_Complete_Departments_Final.xlsx');
XLSX.writeFile(workbook, outputPath);

console.log(`✅ TCM Complete Departments Excel file created successfully!`);
console.log(`📁 File location: ${outputPath}`);
console.log(`📊 Total departments: ${departmentsData.length}`);
console.log(`💰 Total budget allocation: MWK ${departmentsData.reduce((sum, dept) => sum + dept['Budget Allocation'], 0).toLocaleString()}`);
console.log(`👥 Employee count: ${departmentsData.reduce((sum, dept) => sum + dept['Employee Count'], 0)} (Dynamic - will be calculated based on employee assignments)`);
console.log(`🔧 Column mapping: 'name' field properly mapped for system compatibility`);

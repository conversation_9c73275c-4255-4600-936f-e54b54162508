// Deep scan script to analyze payroll employee salary processing issues
const { connectToDatabase } = require('../lib/backend/database');
const Employee = require('../models/Employee').default;
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;

async function deepScanPayrollEmployeeSalaries() {
  try {
    await connectToDatabase();
    
    console.log('🔍 DEEP SCAN: Payroll Employee Salary Processing\n');
    console.log('='.repeat(60));
    
    // Get all active employees
    const employees = await Employee.find({ employmentStatus: 'active' }).lean();
    console.log(`📊 Found ${employees.length} active employees\n`);
    
    // Analyze each employee's salary situation
    let employeesWithActiveSalaries = 0;
    let employeesWithInactiveSalaries = 0;
    let employeesWithNoSalaries = 0;
    let employeesWithMultipleSalaries = 0;
    
    const problemEmployees = [];
    const successEmployees = [];
    
    for (const employee of employees) {
      console.log(`👤 Analyzing: ${employee.firstName} ${employee.lastName} (ID: ${employee._id})`);
      
      // Get all salary records for this employee
      const allSalaries = await EmployeeSalary.find({
        employeeId: employee._id
      }).sort({ effectiveDate: -1 }).lean();
      
      // Get active salary records
      const activeSalaries = allSalaries.filter(s => s.isActive === true);
      
      // Get inactive salary records
      const inactiveSalaries = allSalaries.filter(s => s.isActive === false);
      
      console.log(`   📋 Total salary records: ${allSalaries.length}`);
      console.log(`   ✅ Active salary records: ${activeSalaries.length}`);
      console.log(`   ❌ Inactive salary records: ${inactiveSalaries.length}`);
      
      // Analyze the situation
      if (activeSalaries.length === 0 && inactiveSalaries.length === 0) {
        // No salary records at all
        employeesWithNoSalaries++;
        problemEmployees.push({
          employee,
          issue: 'NO_SALARY_RECORDS',
          description: 'Employee has no salary records at all',
          activeSalaries: 0,
          inactiveSalaries: 0,
          totalSalaries: 0
        });
        console.log(`   🚨 ISSUE: No salary records found`);
        
      } else if (activeSalaries.length === 0 && inactiveSalaries.length > 0) {
        // Has salary records but all are inactive
        employeesWithInactiveSalaries++;
        problemEmployees.push({
          employee,
          issue: 'ALL_SALARIES_INACTIVE',
          description: 'Employee has salary records but all are inactive',
          activeSalaries: 0,
          inactiveSalaries: inactiveSalaries.length,
          totalSalaries: allSalaries.length,
          latestInactiveSalary: inactiveSalaries[0]
        });
        console.log(`   🚨 ISSUE: All salary records are inactive`);
        console.log(`   📅 Latest inactive salary: ${inactiveSalaries[0].basicSalary.toLocaleString()} MWK (Effective: ${new Date(inactiveSalaries[0].effectiveDate).toLocaleDateString()})`);
        
      } else if (activeSalaries.length === 1) {
        // Has exactly one active salary (ideal situation)
        employeesWithActiveSalaries++;
        successEmployees.push({
          employee,
          activeSalary: activeSalaries[0],
          totalSalaries: allSalaries.length
        });
        console.log(`   ✅ SUCCESS: Has 1 active salary record`);
        console.log(`   💰 Active salary: ${activeSalaries[0].basicSalary.toLocaleString()} MWK`);
        
      } else if (activeSalaries.length > 1) {
        // Has multiple active salaries (potential issue)
        employeesWithMultipleSalaries++;
        problemEmployees.push({
          employee,
          issue: 'MULTIPLE_ACTIVE_SALARIES',
          description: 'Employee has multiple active salary records',
          activeSalaries: activeSalaries.length,
          inactiveSalaries: inactiveSalaries.length,
          totalSalaries: allSalaries.length,
          activeSalaryRecords: activeSalaries
        });
        console.log(`   ⚠️  WARNING: Has ${activeSalaries.length} active salary records`);
      }
      
      console.log(''); // Empty line for readability
    }
    
    // Summary Report
    console.log('\n' + '='.repeat(60));
    console.log('📊 SUMMARY REPORT');
    console.log('='.repeat(60));
    console.log(`Total Active Employees: ${employees.length}`);
    console.log(`✅ Employees with Active Salaries: ${employeesWithActiveSalaries}`);
    console.log(`❌ Employees with Inactive Salaries Only: ${employeesWithInactiveSalaries}`);
    console.log(`🚫 Employees with No Salaries: ${employeesWithNoSalaries}`);
    console.log(`⚠️  Employees with Multiple Active Salaries: ${employeesWithMultipleSalaries}`);
    console.log(`🚨 Total Problem Employees: ${problemEmployees.length}`);
    
    // Detailed Problem Analysis
    if (problemEmployees.length > 0) {
      console.log('\n' + '='.repeat(60));
      console.log('🚨 DETAILED PROBLEM ANALYSIS');
      console.log('='.repeat(60));
      
      // Group problems by type
      const problemsByType = {};
      problemEmployees.forEach(emp => {
        if (!problemsByType[emp.issue]) {
          problemsByType[emp.issue] = [];
        }
        problemsByType[emp.issue].push(emp);
      });
      
      Object.keys(problemsByType).forEach(issueType => {
        const employees = problemsByType[issueType];
        console.log(`\n🔍 ${issueType} (${employees.length} employees):`);
        console.log('-'.repeat(40));
        
        employees.forEach(emp => {
          console.log(`   👤 ${emp.employee.firstName} ${emp.employee.lastName}`);
          console.log(`      ID: ${emp.employee._id}`);
          console.log(`      Issue: ${emp.description}`);
          
          if (emp.latestInactiveSalary) {
            console.log(`      Latest Inactive Salary: ${emp.latestInactiveSalary.basicSalary.toLocaleString()} MWK`);
            console.log(`      Effective Date: ${new Date(emp.latestInactiveSalary.effectiveDate).toLocaleDateString()}`);
            console.log(`      End Date: ${emp.latestInactiveSalary.endDate ? new Date(emp.latestInactiveSalary.endDate).toLocaleDateString() : 'None'}`);
          }
          console.log('');
        });
      });
    }
    
    // Recommendations
    console.log('\n' + '='.repeat(60));
    console.log('💡 RECOMMENDATIONS');
    console.log('='.repeat(60));
    
    if (employeesWithInactiveSalaries > 0) {
      console.log(`\n🔧 FIX INACTIVE SALARIES (${employeesWithInactiveSalaries} employees):`);
      console.log('   These employees have salary records but they are marked as inactive.');
      console.log('   Options:');
      console.log('   1. Reactivate the latest salary record');
      console.log('   2. Create new active salary records based on the inactive ones');
      console.log('   3. Update the unified payroll service to handle inactive salaries');
    }
    
    if (employeesWithNoSalaries > 0) {
      console.log(`\n📝 CREATE MISSING SALARIES (${employeesWithNoSalaries} employees):`);
      console.log('   These employees have no salary records at all.');
      console.log('   Action: Create new salary records for these employees.');
    }
    
    if (employeesWithMultipleSalaries > 0) {
      console.log(`\n⚠️  RESOLVE MULTIPLE ACTIVE SALARIES (${employeesWithMultipleSalaries} employees):`);
      console.log('   These employees have multiple active salary records.');
      console.log('   Action: Deactivate older salary records, keep only the latest one active.');
    }
    
    // Generate Fix Scripts
    console.log('\n' + '='.repeat(60));
    console.log('🛠️  SUGGESTED FIX SCRIPTS');
    console.log('='.repeat(60));
    
    if (employeesWithInactiveSalaries > 0) {
      console.log('\n📜 Script to reactivate latest inactive salaries:');
      console.log('```javascript');
      console.log('// Run this to reactivate the latest salary for each employee');
      console.log('const employeeIds = [');
      problemEmployees
        .filter(emp => emp.issue === 'ALL_SALARIES_INACTIVE')
        .forEach(emp => {
          console.log(`  "${emp.employee._id}", // ${emp.employee.firstName} ${emp.employee.lastName}`);
        });
      console.log('];');
      console.log('// Use the reactivate-salaries.js script with these IDs');
      console.log('```');
    }
    
    console.log('\n✅ Deep scan completed!');
    console.log('\nNext steps:');
    console.log('1. Review the problem analysis above');
    console.log('2. Choose the appropriate fix strategy');
    console.log('3. Run the recommended fix scripts');
    console.log('4. Re-test payroll calculations');
    
  } catch (error) {
    console.error('❌ Deep scan failed:', error);
  }
}

// Run the deep scan
deepScanPayrollEmployeeSalaries().then(() => {
  console.log('\n🏁 Deep scan process completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Deep scan process failed:', error);
  process.exit(1);
});

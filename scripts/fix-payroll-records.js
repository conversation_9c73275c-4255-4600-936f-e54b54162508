// Script to recalculate existing payroll records with unified salary calculation logic
const { connectToDatabase } = require('../lib/backend/database');
const { unifiedPayrollService } = require('../lib/services/payroll/unified-payroll-service');
const PayrollRecord = require('../models/payroll/PayrollRecord').default;
const PayrollRun = require('../models/payroll/PayrollRun').default;

async function fixPayrollRecords() {
  try {
    await connectToDatabase();
    
    console.log('🔧 Starting Payroll Records Fix...\n');
    
    // Get all payroll runs that might have incorrect calculations
    const payrollRuns = await PayrollRun.find({
      status: { $in: ['completed', 'approved'] },
      // Only process recent runs to avoid affecting historical data
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
    }).sort({ createdAt: -1 });
    
    console.log(`📊 Found ${payrollRuns.length} recent payroll runs to check\n`);
    
    for (const payrollRun of payrollRuns) {
      console.log(`🔍 Checking Payroll Run: ${payrollRun.name || payrollRun._id}`);
      console.log(`   Period: ${payrollRun.payPeriod.month}/${payrollRun.payPeriod.year}`);
      console.log(`   Status: ${payrollRun.status}`);
      
      // Get payroll records for this run
      const payrollRecords = await PayrollRecord.find({
        payrollRunId: payrollRun._id
      }).populate('employeeId', 'firstName lastName');
      
      console.log(`   Records: ${payrollRecords.length} employees`);
      
      let fixedRecords = 0;
      let totalGrossSalary = 0;
      let totalDeductions = 0;
      let totalTax = 0;
      let totalNetSalary = 0;
      
      for (const record of payrollRecords) {
        try {
          // Recalculate salary using the unified service
          const newCalculation = await unifiedPayrollService.calculateEmployeeSalary(
            record.employeeId._id.toString(),
            {
              month: payrollRun.payPeriod.month,
              year: payrollRun.payPeriod.year
            }
          );
          
          // Check if the calculation is different
          const netSalaryDifference = Math.abs(record.netSalary - newCalculation.netSalary);
          
          if (netSalaryDifference > 0.01) { // If difference is more than 1 cent
            console.log(`   🔧 Fixing ${record.employeeId.firstName} ${record.employeeId.lastName}:`);
            console.log(`      Old Net: MWK ${record.netSalary.toLocaleString()}`);
            console.log(`      New Net: MWK ${newCalculation.netSalary.toLocaleString()}`);
            console.log(`      Difference: MWK ${(newCalculation.netSalary - record.netSalary).toLocaleString()}`);
            
            // Update the record with correct calculations
            record.components = newCalculation.components;
            record.grossSalary = newCalculation.grossSalary;
            record.totalDeductions = newCalculation.totalDeductions;
            record.totalTax = newCalculation.totalTax;
            record.netSalary = newCalculation.netSalary;
            record.updatedAt = new Date();
            
            await record.save();
            fixedRecords++;
          }
          
          // Add to totals
          totalGrossSalary += newCalculation.grossSalary;
          totalDeductions += newCalculation.totalDeductions;
          totalTax += newCalculation.totalTax;
          totalNetSalary += newCalculation.netSalary;
          
        } catch (error) {
          console.log(`   ❌ Error fixing record for ${record.employeeId.firstName} ${record.employeeId.lastName}: ${error.message}`);
        }
      }
      
      // Update payroll run totals
      const totalsDifference = Math.abs(payrollRun.totalNetSalary - totalNetSalary);
      if (totalsDifference > 0.01) {
        console.log(`   🔧 Updating Payroll Run totals:`);
        console.log(`      Old Total Net: MWK ${payrollRun.totalNetSalary.toLocaleString()}`);
        console.log(`      New Total Net: MWK ${totalNetSalary.toLocaleString()}`);
        
        payrollRun.totalGrossSalary = totalGrossSalary;
        payrollRun.totalDeductions = totalDeductions;
        payrollRun.totalTax = totalTax;
        payrollRun.totalNetSalary = totalNetSalary;
        payrollRun.updatedAt = new Date();
        
        await payrollRun.save();
      }
      
      console.log(`   ✅ Fixed ${fixedRecords} records\n`);
    }
    
    console.log('✅ Payroll Records Fix Completed!');
    console.log('\n🎯 Next Steps:');
    console.log('1. Refresh your payroll display to see the corrected values');
    console.log('2. Verify the net salary calculations are now correct');
    console.log('3. Test new payroll runs to ensure the fix is working');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

// Run the fix
fixPayrollRecords().then(() => {
  console.log('\n✅ Fix completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Fix failed:', error);
  process.exit(1);
});

#!/usr/bin/env node

/**
 * Component Copy Script
 *
 * This script copies UI components to ensure they're available during the build process.
 * It's particularly useful for resolving issues with components that exist but aren't being found.
 */

const fs = require('fs');
const path = require('path');

// Define the components to copy
const componentsToCopy = [
  {
    source: 'components/ui/card.tsx',
    destination: '.next/components/ui/card.tsx'
  },
  {
    source: 'components/ui/button.tsx',
    destination: '.next/components/ui/button.tsx'
  },
  {
    source: 'components/dashboard-shell.tsx',
    destination: '.next/components/dashboard-shell.tsx'
  },
  {
    source: 'components/dashboard-header.tsx',
    destination: '.next/components/dashboard-header.tsx'
  },
  {
    source: 'components/dashboard-sidebar.tsx',
    destination: '.next/components/dashboard-sidebar.tsx'
  },
  {
    source: 'components/ui/select.tsx',
    destination: '.next/components/ui/select.tsx'
  }
];

// Create directories if they don't exist
function ensureDirectoryExists(filePath) {
  const dirname = path.dirname(filePath);
  if (!fs.existsSync(dirname)) {
    fs.mkdirSync(dirname, { recursive: true });
  }
}

// Copy files
function copyFiles() {
  console.log('Copying component files...');
  
  componentsToCopy.forEach(({ source, destination }) => {
    try {
      if (fs.existsSync(source)) {
        ensureDirectoryExists(destination);
        fs.copyFileSync(source, destination);
        console.log(`✅ Copied ${source} to ${destination}`);
      } else {
        console.log(`⚠️ Source file not found: ${source}`);
      }
    } catch (error) {
      console.error(`❌ Error copying ${source} to ${destination}:`, error);
    }
  });
  
  console.log('Component copying completed.');
}

// Run the script
copyFiles();

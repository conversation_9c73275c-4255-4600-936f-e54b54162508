# TypeScript Error Checker

This tool scans your codebase for TypeScript errors, generates a detailed report, and can automatically fix common issues.

## Features

- **Comprehensive Error Detection**: Scans your entire codebase for TypeScript errors using the TypeScript Compiler API
- **Detailed Error Reporting**: Generates a markdown report with error details, source code context, and fix suggestions
- **Automatic Error Fixing**: Can automatically fix common TypeScript errors
- **Customizable**: Configure which directories to scan, which errors to fix, and more

## Available Scripts

### All-in-One Script

```bash
# Run the checker in dry-run mode (no files modified)
npm run ts-check

# Run the checker and apply fixes
npm run ts-check-fix
```

### Individual Scripts

```bash
# Run just the scanner
npm run ts-scan

# Generate the report from scan results
npm run ts-report

# Run the fixer (modify the script to set applyFixes: true to apply fixes)
npm run ts-fix
```

## Error Types Detected

The scanner detects all TypeScript errors, including:

- **Type Safety Issues**:
  - `Object is of type unknown`
  - `Object is possibly null`
  - `Object is possibly undefined`
  - `Type assignment error`

- **Implicit Any Issues**:
  - `Parameter implicitly has an any type`
  - `Binding element implicitly has an any type`
  - `Element implicitly has an any type`

- **Property Access Issues**:
  - `Property does not exist on type`
  - `Property does not exist on type (indexed access)`

- **Function and Method Issues**:
  - `Expected parameters mismatch`
  - `Function lacks ending return statement`
  - `No overload matches this call`

- **And many more**

## Automatically Fixable Errors

The fixer can automatically fix these common errors:

### 1. Unknown Type Errors

**Problem:**
```typescript
catch (err: unknown) {
  setError(err.message || 'An error occurred') // Error: 'err' is of type 'unknown'
}
```

**Fix:**
```typescript
catch (err: unknown) {
  if (err instanceof Error) {
    setError(err.message || 'An error occurred')
  } else {
    setError('An error occurred')
  }
}
```

### 2. Null/Undefined Checks

**Problem:**
```typescript
function processUser(user) {
  console.log(user.name) // Error: Object is possibly null or undefined
}
```

**Fix:**
```typescript
function processUser(user) {
  console.log(user?.name) // Added optional chaining
}
```

### 3. Implicit Any Types

**Problem:**
```typescript
function processData(data) { // Error: Parameter implicitly has an any type
  // ...
}
```

**Fix:**
```typescript
function processData(data: unknown) { // Added type annotation
  // ...
}
```

## Configuration

You can customize the behavior of the tools by editing the configuration in each script:

### Scanner Configuration (`typescript-error-scanner.ts`)

```typescript
const config = {
  rootDir: './',
  excludeDirs: ['node_modules', '.next', 'out', 'dist', '.git'],
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  includeSource: true,
  contextLines: 3,
  outputFile: 'typescript-errors.json',
  categories: {
    // Enable/disable specific error categories
    unknownTypeErrors: true,
    strictNullChecks: true,
    // ...
  }
};
```

### Fixer Configuration (`fix-typescript-errors.js`)

```javascript
const config = {
  inputFile: 'typescript-errors.json',
  createBackups: true,
  applyFixes: true, // Set to false for dry run
  fixableCategories: {
    // Enable/disable fixing specific error categories
    'Object is of type unknown': true,
    'Object is possibly null': true,
    // ...
  }
};
```

### Report Generator Configuration (`generate-typescript-error-report.js`)

```javascript
const config = {
  inputFile: 'typescript-errors.json',
  outputFile: 'TYPESCRIPT-ERRORS-REPORT.md',
  maxErrorsPerFile: 10,
  includeSource: true,
  groupByCategory: true,
  includeSuggestions: true
};
```

## Example: Fixing the Unknown Type Error

The specific error you mentioned:

```typescript
// ./app/(dashboard)/admin/database-tools/page.tsx:50:16
Type error: 'err' is of type 'unknown'.
  48 |       })
  49 |     } catch (err: unknown) {
> 50 |       setError(err.message || 'An error occurred')
```

Can be fixed by running:

```bash
npm run ts-check-fix
```

The fixer will automatically add a type check:

```typescript
catch (err: unknown) {
  if (err instanceof Error) {
    setError(err.message || 'An error occurred')
  } else {
    setError('An error occurred')
  }
}
```

## Best Practices

1. **Always run in dry-run mode first** (`npm run ts-check`) to see what changes would be made
2. **Review the error report** before applying fixes
3. **Commit your changes** before running the fixer with `--apply-fixes`
4. **Check the backups** created by the fixer if you need to revert changes
5. **Run the TypeScript compiler** after fixing to check for remaining errors: `npx tsc --noEmit`

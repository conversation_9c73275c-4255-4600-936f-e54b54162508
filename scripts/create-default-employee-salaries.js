// Script to create default salary records for employees who don't have them
const { connectToDatabase } = require('../lib/backend/database');
const Employee = require('../models/Employee').default;
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;

async function createDefaultEmployeeSalaries() {
  try {
    await connectToDatabase();
    
    console.log('🔍 Creating default salary records for employees without active salaries...\n');
    
    // Get all active employees
    const employees = await Employee.find({ employmentStatus: 'active' }).lean();
    console.log(`📊 Found ${employees.length} active employees`);
    
    // Get employees who already have active salary records
    const existingSalaries = await EmployeeSalary.find({ isActive: true }).distinct('employeeId');
    const employeesWithSalaries = new Set(existingSalaries.map(id => id.toString()));
    
    console.log(`💰 ${existingSalaries.length} employees already have salary records`);
    
    // Find employees without salary records
    const employeesWithoutSalaries = employees.filter(emp => 
      !employeesWithSalaries.has(emp._id.toString())
    );
    
    console.log(`❌ ${employeesWithoutSalaries.length} employees need salary records\n`);
    
    if (employeesWithoutSalaries.length === 0) {
      console.log('✅ All employees already have salary records!');
      return;
    }
    
    // Create default salary records
    let created = 0;
    for (const employee of employeesWithoutSalaries) {
      try {
        // Create a default salary structure based on position or use standard amounts
        const defaultBasicSalary = getDefaultSalaryByPosition(employee.position);
        
        const salaryRecord = new EmployeeSalary({
          employeeId: employee._id,
          basicSalary: defaultBasicSalary,
          currency: 'MWK',
          paymentMethod: 'bank_transfer',
          isActive: true,
          effectiveDate: new Date(),
          allowances: [
            {
              name: 'Housing Allowance',
              amount: Math.round(defaultBasicSalary * 0.3), // 30% of basic
              isTaxable: true,
              isPensionable: false
            },
            {
              name: 'Transport Allowance',
              amount: Math.round(defaultBasicSalary * 0.15), // 15% of basic
              isTaxable: true,
              isPensionable: false
            }
          ],
          deductions: [
            {
              name: 'Pension Contribution',
              percentage: 5, // 5% of basic salary
              isStatutory: true
            },
            {
              name: 'Health Insurance',
              amount: 8000, // Fixed amount
              isStatutory: false
            }
          ],
          createdBy: 'system-script',
          notes: 'Default salary record created by system script for payroll testing'
        });
        
        await salaryRecord.save();
        created++;
        
        console.log(`✅ Created salary record for ${employee.firstName} ${employee.lastName} - Basic: MWK ${defaultBasicSalary.toLocaleString()}`);
        
      } catch (error) {
        console.error(`❌ Failed to create salary for ${employee.firstName} ${employee.lastName}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Successfully created ${created} salary records!`);
    console.log('\n📋 Summary:');
    console.log(`   Total employees: ${employees.length}`);
    console.log(`   Had salaries: ${existingSalaries.length}`);
    console.log(`   Needed salaries: ${employeesWithoutSalaries.length}`);
    console.log(`   Created: ${created}`);
    
    console.log('\n✅ You can now run payroll calculations!');
    
  } catch (error) {
    console.error('❌ Error creating default salaries:', error);
  }
}

// Function to determine default salary based on position
function getDefaultSalaryByPosition(position) {
  const positionSalaries = {
    // Management positions
    'CEO': 5000000,
    'Director': 3500000,
    'Manager': 2500000,
    'Assistant Manager': 1800000,
    'Supervisor': 1500000,
    
    // Professional positions
    'Senior Developer': 2200000,
    'Developer': 1800000,
    'Junior Developer': 1200000,
    'Analyst': 1600000,
    'Senior Analyst': 2000000,
    
    // Administrative positions
    'Administrator': 1000000,
    'Secretary': 800000,
    'Receptionist': 600000,
    'Clerk': 700000,
    
    // Finance positions
    'Accountant': 1500000,
    'Senior Accountant': 2000000,
    'Finance Manager': 2500000,
    'Bookkeeper': 1200000,
    
    // HR positions
    'HR Manager': 2200000,
    'HR Officer': 1500000,
    'Recruiter': 1300000,
    
    // Operations
    'Operations Manager': 2300000,
    'Team Lead': 1800000,
    'Coordinator': 1200000,
    
    // Sales & Marketing
    'Sales Manager': 2200000,
    'Sales Representative': 1400000,
    'Marketing Manager': 2100000,
    'Marketing Officer': 1500000,
    
    // Technical positions
    'IT Manager': 2500000,
    'System Administrator': 1800000,
    'Technical Support': 1200000,
    
    // Default fallback
    'default': 1000000
  };
  
  // Try to match position (case insensitive)
  if (position) {
    const normalizedPosition = position.toLowerCase();
    for (const [key, salary] of Object.entries(positionSalaries)) {
      if (normalizedPosition.includes(key.toLowerCase())) {
        return salary;
      }
    }
  }
  
  // Return default if no match found
  return positionSalaries.default;
}

// Run the script
createDefaultEmployeeSalaries().then(() => {
  console.log('\n🏁 Script completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});

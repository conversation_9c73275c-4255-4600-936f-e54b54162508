const XLSX = require('xlsx');
const path = require('path');

// Create salary revisions template data
const salaryRevisionsData = [
  // Header row
  [
    'Employee ID',
    'Employee Email',
    'Employee Name',
    'Department',
    'Revision Type',
    'New Basic Salary',
    'Percentage Increase',
    'Fixed Increase',
    'Effective Date',
    'Reason',
    'Notes',
    'Currency'
  ],
  // Example data rows
  [
    '507f1f77bcf86cd799439011',
    '<EMAIL>',
    '<PERSON>',
    'Finance Department',
    'increment',
    '450000',
    '10',
    '',
    '2024-02-01',
    'Annual performance review increase',
    'Excellent performance rating',
    'MWK'
  ],
  [
    '507f1f77bcf86cd799439012',
    '<EMAIL>',
    '<PERSON>',
    'HR Department',
    'promotion',
    '520000',
    '15',
    '',
    '2024-02-01',
    'Promotion to Senior HR Officer',
    'Promoted due to outstanding leadership',
    'MWK'
  ],
  [
    '',
    '<EMAIL>',
    '<PERSON>',
    'ICT Department',
    'adjustment',
    '380000',
    '',
    '25000',
    '2024-02-01',
    'Market rate adjustment',
    'Salary adjustment to match market rates',
    'MWK'
  ],
  [
    '',
    '<EMAIL>',
    'Sarah Johnson',
    'Compliance Department',
    'annual_review',
    '420000',
    '8',
    '',
    '2024-02-01',
    'Annual salary review',
    'Standard annual increase',
    'MWK'
  ]
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Create worksheet from the data
const ws = XLSX.utils.aoa_to_sheet(salaryRevisionsData);

// Set column widths for better readability
const colWidths = [
  { wch: 25 }, // Employee ID
  { wch: 30 }, // Employee Email
  { wch: 20 }, // Employee Name
  { wch: 20 }, // Department
  { wch: 15 }, // Revision Type
  { wch: 18 }, // New Basic Salary
  { wch: 18 }, // Percentage Increase
  { wch: 15 }, // Fixed Increase
  { wch: 15 }, // Effective Date
  { wch: 35 }, // Reason
  { wch: 40 }, // Notes
  { wch: 10 }  // Currency
];

ws['!cols'] = colWidths;

// Add data validation and comments
const range = XLSX.utils.decode_range(ws['!ref']);

// Add comments to header row
const comments = {
  'A1': 'Employee ID (optional if email is provided)',
  'B1': 'Employee email address (required if ID not provided)',
  'C1': 'Employee full name (for reference)',
  'D1': 'Department name (for reference)',
  'E1': 'Type: increment, promotion, adjustment, demotion, annual_review, other',
  'F1': 'New basic salary amount in MWK',
  'G1': 'Percentage increase (optional, for reference)',
  'H1': 'Fixed amount increase (optional, for reference)',
  'I1': 'Effective date (YYYY-MM-DD format)',
  'J1': 'Reason for salary revision (required)',
  'K1': 'Additional notes (optional)',
  'L1': 'Currency code (default: MWK)'
};

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Salary Revisions');

// Create instructions sheet
const instructionsData = [
  ['Salary Revisions Bulk Import Instructions'],
  [''],
  ['Required Fields:'],
  ['• Employee ID OR Employee Email (at least one must be provided)'],
  ['• Revision Type (increment, promotion, adjustment, demotion, annual_review, other)'],
  ['• New Basic Salary (the new salary amount)'],
  ['• Effective Date (format: YYYY-MM-DD, e.g., 2024-02-01)'],
  ['• Reason (explanation for the salary revision)'],
  [''],
  ['Optional Fields:'],
  ['• Employee Name (for reference only)'],
  ['• Department (for reference only)'],
  ['• Percentage Increase (calculated automatically if not provided)'],
  ['• Fixed Increase (calculated automatically if not provided)'],
  ['• Notes (additional comments)'],
  ['• Currency (defaults to MWK if not provided)'],
  [''],
  ['Important Notes:'],
  ['• Each employee can only have one revision per effective date'],
  ['• Employees must have an active salary record to create revisions'],
  ['• All revisions will be created with "pending" approval status'],
  ['• Effective dates should be in the future or current date'],
  ['• New salary must be greater than 0'],
  [''],
  ['Revision Types:'],
  ['• increment: Regular salary increase'],
  ['• promotion: Salary increase due to promotion'],
  ['• adjustment: Salary adjustment (market rate, etc.)'],
  ['• demotion: Salary decrease due to demotion'],
  ['• annual_review: Annual performance review increase'],
  ['• other: Other types of salary changes'],
  [''],
  ['Example Data:'],
  ['The template includes example data showing different scenarios:'],
  ['• Performance-based increment'],
  ['• Promotion with percentage increase'],
  ['• Market rate adjustment with fixed amount'],
  ['• Annual review increase'],
  [''],
  ['Tips:'],
  ['• You can use either Employee ID or Email to identify employees'],
  ['• If both ID and Email are provided, ID takes precedence'],
  ['• Department and Employee Name are for reference and validation'],
  ['• Remove example data before importing your actual data'],
  ['• Test with a small batch first before importing large datasets']
];

const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);

// Set column width for instructions
instructionsWs['!cols'] = [{ wch: 80 }];

// Add instructions sheet
XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions');

// Create the format_excel directory if it doesn't exist
const fs = require('fs');
const formatExcelDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(formatExcelDir)) {
  fs.mkdirSync(formatExcelDir, { recursive: true });
}

// Write the file
const filePath = path.join(formatExcelDir, 'salary_revisions_template.xlsx');
XLSX.writeFile(wb, filePath);

console.log(`Salary revisions Excel template created successfully at: ${filePath}`);
console.log('This file contains:');
console.log('1. Template with example data for salary revisions');
console.log('2. Instructions sheet with detailed guidelines');
console.log('3. Proper column formatting and validation notes');
console.log('');
console.log('Features included:');
console.log('• Support for both Employee ID and Email identification');
console.log('• Multiple revision types (increment, promotion, adjustment, etc.)');
console.log('• Flexible salary change options (percentage or fixed amount)');
console.log('• Comprehensive validation and error handling');
console.log('• Example data for different scenarios');

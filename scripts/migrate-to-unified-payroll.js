// Migration script to update payroll services to use unified service
const fs = require('fs');
const path = require('path');

const filesToUpdate = [
  {
    file: 'app/api/payroll/runs/[id]/process/route.ts',
    changes: [
      {
        from: "import { payrollService } from '@/lib/services/payroll/payroll-service';",
        to: "import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';"
      },
      {
        from: "const result = await payrollService.processPayrollRun(id, {",
        to: "const result = await unifiedPayrollService.processPayrollRun(id, {"
      }
    ]
  },
  {
    file: 'app/api/payroll/calculate-salary/route.ts',
    changes: [
      {
        from: "import { salaryCalculationService } from '@/lib/services/payroll/salary-calculation-service';",
        to: "import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';"
      },
      {
        from: "const result = await salaryCalculationService.calculateSalary(",
        to: "const result = await unifiedPayrollService.calculateEmployeeSalary("
      }
    ]
  },
  {
    file: 'lib/services/payroll/optimized-payroll-processor.ts',
    changes: [
      {
        from: "import { SalaryCalculationService } from './salary-calculation-service';",
        to: "import { unifiedPayrollService } from './unified-payroll-service';"
      },
      {
        from: "private salaryCalculationService: SalaryCalculationService;",
        to: "// Using unified payroll service instead"
      },
      {
        from: "this.salaryCalculationService = new SalaryCalculationService();",
        to: "// Using unified payroll service singleton"
      },
      {
        from: "const salaryResult = await this.salaryCalculationService.calculateSalary(",
        to: "const salaryResult = await unifiedPayrollService.calculateEmployeeSalary("
      }
    ]
  }
];

function updateFile(filePath, changes) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let updated = false;
    
    for (const change of changes) {
      if (content.includes(change.from)) {
        content = content.replace(new RegExp(change.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), change.to);
        updated = true;
        console.log(`✅ Updated: ${change.from} -> ${change.to}`);
      } else {
        console.log(`⚠️  Pattern not found: ${change.from}`);
      }
    }
    
    if (updated) {
      // Create backup
      fs.writeFileSync(`${fullPath}.backup`, fs.readFileSync(fullPath));
      
      // Write updated content
      fs.writeFileSync(fullPath, content);
      console.log(`✅ Updated file: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

function migrateToUnifiedPayroll() {
  console.log('🔄 Starting migration to unified payroll service...\n');
  
  let totalUpdated = 0;
  
  for (const fileUpdate of filesToUpdate) {
    console.log(`📝 Processing: ${fileUpdate.file}`);
    
    if (updateFile(fileUpdate.file, fileUpdate.changes)) {
      totalUpdated++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  console.log(`✅ Migration completed! Updated ${totalUpdated} files.`);
  console.log('\n🎯 Next Steps:');
  console.log('1. Test the unified payroll service with a new payroll run');
  console.log('2. Verify calculations are now consistent');
  console.log('3. Run the fix-payroll-records.js script to update existing records');
  console.log('4. Monitor logs for any issues');
  console.log('\n📋 Backup files created with .backup extension');
  console.log('   You can restore them if needed: mv file.backup file');
}

// Run the migration
migrateToUnifiedPayroll();

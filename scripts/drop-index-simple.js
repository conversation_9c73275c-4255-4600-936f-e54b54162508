/**
 * <PERSON><PERSON><PERSON> to drop the employeeNumber index
 * This script simply drops the index causing the duplicate key error
 * 
 * Run with: node scripts/drop-index-simple.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Main function to drop the index
async function dropIndex() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Employee collection
    const db = mongoose.connection.db;
    const employeeCollection = db.collection('employees');

    // List all indexes
    console.log('Current indexes:');
    const indexes = await employeeCollection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index, null, 2)}`);
    });

    // Try to drop the employeeNumber index
    try {
      console.log('\nAttempting to drop employeeNumber_1 index...');
      await employeeCollection.dropIndex('employeeNumber_1');
      console.log('Successfully dropped employeeNumber_1 index');
    } catch (error) {
      console.error('Error dropping employeeNumber_1 index:', error.message);
      
      // Try alternative approach - drop all non-_id indexes
      console.log('\nTrying alternative approach - dropping all non-_id indexes...');
      await employeeCollection.dropIndexes();
      console.log('Dropped all non-_id indexes');
    }

    // Verify indexes after dropping
    console.log('\nIndexes after dropping:');
    const remainingIndexes = await employeeCollection.indexes();
    remainingIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index, null, 2)}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
dropIndex().catch(console.error);

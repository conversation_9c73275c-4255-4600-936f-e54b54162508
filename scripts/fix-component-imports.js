#!/usr/bin/env node

/**
 * Component Import Fixer
 *
 * This script fixes component import paths in the codebase to ensure
 * they match the actual file locations. It's particularly useful for
 * resolving issues with duplicate component files or components that
 * have been moved.
 *
 * Usage:
 *   node scripts/fix-component-imports.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Backup directory
const BACKUP_DIR = path.join(process.cwd(), 'backups', 'component-imports');

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Patterns to replace
const replacements = [
  // Fix dashboard-shell imports
  {
    find: /import\s*{\s*DashboardShell\s*}\s*from\s*['"]@\/components\/shell['"]/g,
    replace: "import { DashboardShell } from '@/components/dashboard-shell'",
    description: 'Fix DashboardShell import path'
  },
  // Fix dashboard-header imports
  {
    find: /import\s*{\s*DashboardHeader\s*}\s*from\s*['"]@\/components\/header['"]/g,
    replace: "import { DashboardHeader } from '@/components/dashboard-header'",
    description: 'Fix DashboardHeader import path'
  },
  // Fix description prop to text prop
  {
    find: /<DashboardHeader[^>]*\s+description=["']([^"']*)["']/g,
    replace: (match, description) => match.replace(`description="${description}"`, `text="${description}"`),
    description: 'Fix DashboardHeader description prop to text prop'
  },
  // Fix UI component imports
  {
    find: /import\s*{\s*([^}]*)\s*}\s*from\s*['"]@\/components\/ui\/([^'"]*)['"]/g,
    replace: (match, components, componentName) => {
      // Check if the component file exists
      const componentPath = path.join(process.cwd(), 'components', 'ui', `${componentName}.tsx`);
      if (!fs.existsSync(componentPath)) {
        console.log(`Component file not found: ${componentPath}`);
        // Try to find the component in the shadcn directory
        const shadcnPath = path.join(process.cwd(), 'components', 'shadcn', 'ui', `${componentName}.tsx`);
        if (fs.existsSync(shadcnPath)) {
          return `import { ${components} } from '@/components/shadcn/ui/${componentName}'`;
        }
      }
      return match;
    },
    description: 'Fix UI component import paths'
  }
];

/**
 * Fix a file by applying all replacements
 */
function fixFile(filePath) {
  console.log(`Processing ${filePath}...`);

  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = false;

  // Create backup
  const relativePath = path.relative(process.cwd(), filePath);
  const backupPath = path.join(BACKUP_DIR, relativePath);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.writeFileSync(backupPath, content);

  // Check if file has component imports that need fixing
  if (content.includes('@/components/shell') ||
      content.includes('@/components/header') ||
      content.includes('description=') && content.includes('DashboardHeader') ||
      content.includes('@/components/ui/')) {
    
    console.log(`Found component imports to fix in ${filePath}`);

    // Apply all replacements
    for (const replacement of replacements) {
      if (replacement.find.test(newContent)) {
        newContent = newContent.replace(replacement.find, replacement.replace);
        changes = true;
      }
    }

    // Write changes if needed
    if (changes) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed component imports in ${filePath}`);
      return true;
    }
  }

  return false;
}

/**
 * Find all TSX files
 */
function findTsxFiles(directory = '.') {
  try {
    const result = execSync(`find ${directory} -name "*.tsx"`, { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error(`Error finding TSX files in ${directory}:`, error);
    return [];
  }
}

/**
 * Main function
 */
function main(directory = '.') {
  console.log(`
╔════════════════════════════════════════════════════════════╗
║              COMPONENT IMPORT PATH FIXER                   ║
╚════════════════════════════════════════════════════════════╝
`);
  console.log(`Starting component import fixes for directory: ${directory}...`);

  const files = findTsxFiles(directory);
  console.log(`Found ${files.length} TSX files to check`);

  let fixedCount = 0;
  const startTime = Date.now();

  for (const file of files) {
    try {
      const fixed = fixFile(file);
      if (fixed) fixedCount++;
    } catch (error) {
      console.error(`Error fixing ${file}:`, error);
    }
  }

  const duration = ((Date.now() - startTime) / 1000).toFixed(2);

  console.log(`
╔════════════════════════════════════════════════════════════╗
║                        SUMMARY                             ║
╠════════════════════════════════════════════════════════════╣
║  Total TSX files checked: ${files.length.toString().padEnd(29)} ║
║  Files fixed:             ${fixedCount.toString().padEnd(29)} ║
║  Time taken:              ${duration}s${' '.repeat(28 - duration.toString().length)} ║
╚════════════════════════════════════════════════════════════╝
`);

  if (fixedCount > 0) {
    console.log(`Backups of modified files were saved to: ${BACKUP_DIR}`);
  }

  console.log('Done!');
}

// Get directory from command line arguments or use default
const directory = process.argv[2] || '.';
main(directory);

#!/usr/bin/env node

/**
 * Fix Route Handler Params Script
 *
 * This script fixes the type definition for dynamic route parameters in route handlers.
 * It changes the Promise<{ params: { ... } }> type to { params: { ... } }.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all route.ts files in the app/api directory
function findApiRouteFiles() {
  try {
    const result = execSync('find app/api -name "route.ts" -type f', { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error finding API route files:', error);
    return [];
  }
}

// Check if a file contains the incorrect parameter format
function fileNeedsFixing(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for dynamic route parameters without Promise
    const hasParams = content.includes('{ params }: { params: {') ||
                      content.includes('context: { params: {');

    // Check if it's a route handler file
    const isRouteHandler = content.includes('export async function GET') ||
                          content.includes('export async function POST') ||
                          content.includes('export async function PUT') ||
                          content.includes('export async function PATCH') ||
                          content.includes('export async function DELETE');

    return hasParams && isRouteHandler;
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error);
    return false;
  }
}

// Fix the parameter format in a file
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Create a backup of the original file
    const backupPath = `${filePath}.bak`;
    fs.writeFileSync(backupPath, content);

    // Fix for Next.js 15: Convert regular params to Promise params
    // Match pattern like: { params }: { params: { id: string } }
    content = content.replace(
      /{\s*params\s*}:\s*{\s*params:\s*{\s*([^}]*)\s*}\s*}/g,
      '{ params }: { params: Promise<{ $1 }> }'
    );

    // Fix for context pattern: context: { params: { id: string } }
    content = content.replace(
      /context:\s*{\s*params:\s*{\s*([^}]*)\s*}\s*}/g,
      '{ params }: { params: Promise<{ $1 }> }'
    );

    // Fix references to context.params.id
    content = content.replace(
      /context\.params\.([a-zA-Z0-9_]+)/g,
      '(await params).$1'
    );

    // Add return type Promise<NextResponse> to handler functions
    content = content.replace(
      /(export async function (GET|POST|PUT|PATCH|DELETE)[^{]*)\{/g,
      '$1: Promise<NextResponse> {'
    );

    // Add destructuring and await for params
    content = content.replace(
      /const ([a-zA-Z0-9_]+) = (context\.params|params)\.([a-zA-Z0-9_]+);/g,
      'const { $3: $1 } = await params;'
    );

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);

    console.log(`✅ Fixed ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error fixing file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('🔍 Finding API route files...');
  const files = findApiRouteFiles();
  console.log(`Found ${files.length} API route files.`);

  let fixedCount = 0;

  for (const file of files) {
    if (fileNeedsFixing(file)) {
      console.log(`Fixing ${file}...`);
      if (fixFile(file)) {
        fixedCount++;
      }
    }
  }

  console.log(`\n✨ Done! Fixed ${fixedCount} files.`);
}

main();

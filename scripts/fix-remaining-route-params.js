#!/usr/bin/env node

/**
 * Fix Remaining Route Params Script
 * 
 * This script finds and fixes any remaining route files with incorrect parameter formats.
 * It specifically targets files with the pattern:
 * - context: Promise<{ params: { id: string } }>
 * - context: { params: Promise<{ id: string }> }
 * 
 * And converts them to the correct format:
 * - { params }: { params: Promise<{ id: string }> }
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all API route files
function findApiRouteFiles() {
  try {
    const result = execSync('find ./app -name "route.ts" -type f', { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error finding API route files:', error);
    return [];
  }
}

// Check if a file needs fixing
function needsFixing(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for incorrect parameter formats
    const incorrectFormat1 = /context\s*:\s*Promise\s*<\s*{\s*params\s*:\s*{\s*[a-zA-Z]+\s*:\s*string\s*}\s*}\s*>/;
    const incorrectFormat2 = /context\s*:\s*{\s*params\s*:\s*Promise\s*<\s*{\s*[a-zA-Z]+\s*:\s*string\s*}\s*>\s*}/;
    
    // Also check for references to params.id without resolving the promise
    const incorrectParamsUsage = /params\.([a-zA-Z]+)/;
    
    return incorrectFormat1.test(content) || incorrectFormat2.test(content) || incorrectParamsUsage.test(content);
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error);
    return false;
  }
}

// Fix a file
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix incorrect parameter format 1: context: Promise<{ params: { id: string } }>
    const incorrectFormat1 = /export\s+async\s+function\s+([A-Z]+)\s*\(\s*req\s*:\s*NextRequest\s*,\s*context\s*:\s*Promise\s*<\s*{\s*params\s*:\s*{\s*([a-zA-Z]+)\s*:\s*string\s*}\s*}\s*>\s*\)/g;
    if (incorrectFormat1.test(content)) {
      content = content.replace(incorrectFormat1, (match, method, paramName) => {
        modified = true;
        return `export async function ${method}(req: NextRequest, { params }: { params: Promise<{ ${paramName}: string }> }): Promise<Response>`;
      });
      
      // Add variable declaration and resolve params
      const paramResolvePattern = /try\s*{/;
      const paramName = content.match(/params\s*:\s*Promise\s*<\s*{\s*([a-zA-Z]+)\s*:/)?.[1] || 'id';
      
      if (paramResolvePattern.test(content)) {
        content = content.replace(paramResolvePattern, (match) => {
          return `// Declare ${paramName}Id at function scope
  let ${paramName}Id: string;
  
  try {
    // Resolve the params promise
    const { ${paramName} } = await params;
    ${paramName}Id = ${paramName};`;
        });
        
        // Replace params.id with idVariable
        const paramsUsagePattern = new RegExp(`params\\.${paramName}`, 'g');
        content = content.replace(paramsUsagePattern, `${paramName}Id`);
      }
    }
    
    // Fix incorrect parameter format 2: context: { params: Promise<{ id: string }> }
    const incorrectFormat2 = /export\s+async\s+function\s+([A-Z]+)\s*\(\s*req\s*:\s*NextRequest\s*,\s*context\s*:\s*{\s*params\s*:\s*Promise\s*<\s*{\s*([a-zA-Z]+)\s*:\s*string\s*}\s*>\s*}\s*\)/g;
    if (incorrectFormat2.test(content)) {
      content = content.replace(incorrectFormat2, (match, method, paramName) => {
        modified = true;
        return `export async function ${method}(req: NextRequest, { params }: { params: Promise<{ ${paramName}: string }> }): Promise<Response>`;
      });
      
      // Replace context.params with params
      const contextParamsPattern = /context\.params/g;
      content = content.replace(contextParamsPattern, 'params');
    }
    
    // Add return type if missing
    const missingReturnType = /export\s+async\s+function\s+([A-Z]+)\s*\(\s*req\s*:\s*NextRequest\s*,\s*{\s*params\s*}\s*:\s*{\s*params\s*:\s*Promise\s*<\s*{\s*([a-zA-Z]+)\s*:\s*string\s*}\s*>\s*}\s*\)\s*{/g;
    if (missingReturnType.test(content)) {
      content = content.replace(missingReturnType, (match, method, paramName) => {
        modified = true;
        return `export async function ${method}(req: NextRequest, { params }: { params: Promise<{ ${paramName}: string }> }): Promise<Response> {`;
      });
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('🔍 Finding API route files...');
  const files = findApiRouteFiles();
  console.log(`Found ${files.length} API route files.`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (needsFixing(file)) {
      console.log(`Fixing ${file}...`);
      if (fixFile(file)) {
        console.log(`✅ Fixed ${file}`);
        fixedCount++;
      } else {
        console.log(`⚠️ Could not fix ${file}`);
      }
    }
  }
  
  console.log(`\n✨ Done! Fixed ${fixedCount} files.`);
}

main();

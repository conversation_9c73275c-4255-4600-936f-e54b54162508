#!/usr/bin/env node

/**
 * Comprehensive script to fix TypeScript form control type issues across all form components
 * This applies the same type-safe solution we used for tender-form.tsx and category-form.tsx
 */

const fs = require('fs');
const path = require('path');

// List of all form files that need fixing
const formFiles = [
  'components/procurement/forms/contract-form.tsx',
  'components/procurement/forms/delivery-form.tsx', 
  'components/procurement/forms/purchase-order-form.tsx',
  'components/procurement/forms/requisition-form.tsx',
  'components/forms/supplier-form.tsx',
  'components/forms/deal-form.tsx',
  'components/forms/task-form.tsx',
  'components/forms/invoice-form.tsx',
  'components/forms/payment-form.tsx',
  'components/forms/production-form.tsx',
  'components/assessment/assessment-form.tsx',
  'components/onboarding/onboarding-form.tsx',
  'components/asset/movement/asset-movement-form.tsx',
  'components/asset/maintenance/asset-maintenance-form.tsx',
  'components/accounting/import-export/import-data-form.tsx'
];

function fixFormFile(filePath) {
  console.log(`\n🔧 Processing: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ⚠️  File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Skip if doesn't use react-hook-form
  if (!content.includes('useForm') || !content.includes('react-hook-form')) {
    console.log(`  ⏭️  Skipped: Not a react-hook-form component`);
    return;
  }

  // Skip if already fixed
  if (content.includes('as ReturnType<typeof useForm<')) {
    console.log(`  ✅ Already fixed: Has type assertion`);
    return;
  }

  // Pattern 1: Fix useForm with explicit type parameter
  const useFormWithTypePattern = /const\s+(\w+)\s+=\s+useForm<([^>]+)>\s*\(\s*\{/g;
  content = content.replace(useFormWithTypePattern, (match, formVar, typeParam) => {
    modified = true;
    return `const ${formVar} = useForm({`;
  });

  // Pattern 2: Add mode and type assertion
  const resolverPattern = /(resolver:\s*zodResolver\([^)]+\))/g;
  if (content.match(resolverPattern) && !content.includes('mode:')) {
    content = content.replace(resolverPattern, '$1,\n    mode: "onChange" as const');
    modified = true;
  }

  // Pattern 3: Add type assertion at the end of useForm
  const useFormEndPattern = /(\s+}\s*\))\s*$/gm;
  const lines = content.split('\n');
  let inUseForm = false;
  let braceCount = 0;
  let useFormStartLine = -1;
  let typeParam = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Detect start of useForm
    if (line.includes('const') && line.includes('= useForm(')) {
      inUseForm = true;
      useFormStartLine = i;
      braceCount = (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;
      
      // Try to find type parameter from interface or schema
      const interfaceMatch = content.match(/interface\s+(\w+FormData|\w+FormValues)/);
      const schemaMatch = content.match(/const\s+(\w+)FormSchema\s*=/);
      if (interfaceMatch) {
        typeParam = interfaceMatch[1];
      } else if (schemaMatch) {
        typeParam = schemaMatch[1].charAt(0).toUpperCase() + schemaMatch[1].slice(1) + 'FormData';
      }
      continue;
    }

    if (inUseForm) {
      braceCount += (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;
      
      // End of useForm detected
      if (braceCount === 0 && line.includes('})')) {
        if (typeParam && !line.includes('as ReturnType')) {
          lines[i] = line.replace(/}\)$/, `}) as ReturnType<typeof useForm<${typeParam}>>`);
          modified = true;
        }
        inUseForm = false;
        break;
      }
    }
  }

  if (modified) {
    content = lines.join('\n');
  }

  // Pattern 4: Fix form.watch() calls that might return undefined
  const watchPattern = /form\.watch\("([^"]+)"\)\.map\(/g;
  content = content.replace(watchPattern, '(form.watch("$1") || []).map(');
  if (content.match(watchPattern)) modified = true;

  // Pattern 5: Fix form.getValues() calls that might return undefined
  const getValuesPattern = /const\s+(\w+)\s+=\s+form\.getValues\("([^"]+)"\)\s*\n\s*if\s*\(\s*!\1\.includes/g;
  content = content.replace(getValuesPattern, 'const $1 = form.getValues("$2") || []\n    if (!$1.includes');
  if (content.match(getValuesPattern)) modified = true;

  // Pattern 6: Fix spread operator with potentially undefined arrays
  const spreadPattern = /\[\.\.\.(form\.getValues\("[^"]+"\))/g;
  content = content.replace(spreadPattern, '[...($1 || [])');
  if (content.match(spreadPattern)) modified = true;

  // Pattern 7: Fix deprecated onKeyPress
  content = content.replace(/onKeyPress=/g, 'onKeyDown=');
  if (content.includes('onKeyDown=')) modified = true;

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Fixed: Applied type-safe form fixes`);
  } else {
    console.log(`  ℹ️  No changes needed`);
  }
}

function main() {
  console.log('🚀 Fixing TypeScript form control type issues across all forms...\n');
  
  let fixedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  formFiles.forEach(filePath => {
    try {
      const beforeContent = fs.existsSync(filePath) ? fs.readFileSync(filePath, 'utf8') : '';
      fixFormFile(filePath);
      const afterContent = fs.existsSync(filePath) ? fs.readFileSync(filePath, 'utf8') : '';
      
      if (beforeContent !== afterContent) {
        fixedCount++;
      } else {
        skippedCount++;
      }
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      errorCount++;
    }
  });

  console.log('\n📊 Summary:');
  console.log(`✅ Fixed: ${fixedCount} files`);
  console.log(`⏭️  Skipped: ${skippedCount} files`);
  console.log(`❌ Errors: ${errorCount} files`);
  console.log('\n🎯 All form components should now be TypeScript error-free!');
  console.log('\n📝 Changes applied:');
  console.log('- Removed explicit type parameters from useForm()');
  console.log('- Added mode: "onChange" as const');
  console.log('- Added type assertions using ReturnType<typeof useForm<T>>');
  console.log('- Fixed null safety for form.watch() and form.getValues()');
  console.log('- Replaced deprecated onKeyPress with onKeyDown');
}

if (require.main === module) {
  main();
}

module.exports = { fixFormFile, formFiles };

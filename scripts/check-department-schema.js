/**
 * <PERSON><PERSON><PERSON> to check the actual Department schema in the database
 */

const mongoose = require('mongoose');

// Use the hardcoded MongoDB URI from the connection manager
const MONGODB_URI = 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources';

async function checkDepartmentSchema() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check if Department model is already registered
    console.log('\n📋 Checking registered models:');
    console.log('Registered models:', Object.keys(mongoose.models));

    // Get the database
    const db = mongoose.connection.db;

    // Check collections
    const collections = await db.listCollections().toArray();
    console.log('\n📁 Available collections:');
    collections.forEach(col => {
      console.log(`  - ${col.name}`);
    });

    // Check if departments collection exists
    const departmentsCollection = collections.find(col => col.name === 'departments');
    if (departmentsCollection) {
      console.log('\n🔍 Departments collection found!');

      // Get a sample document to see the actual structure
      const sampleDepartment = await db.collection('departments').findOne();
      if (sampleDepartment) {
        console.log('\n📄 Sample department document:');
        console.log(JSON.stringify(sampleDepartment, null, 2));

        console.log('\n🔑 Available fields in sample document:');
        Object.keys(sampleDepartment).forEach(key => {
          console.log(`  - ${key}: ${typeof sampleDepartment[key]}`);
        });
      } else {
        console.log('\n📭 No departments found in collection');
      }
    } else {
      console.log('\n❌ Departments collection not found');
    }

    // Try to create a test department directly with MongoDB driver
    console.log('\n🧪 Testing direct MongoDB insertion...');
    const testDept = {
      name: `Direct Test ${Date.now()}`,
      description: 'Direct MongoDB test',
      departmentCode: 'DIRECT',
      location: 'Test Location',
      budget: 50000,
      contactEmail: '<EMAIL>',
      contactPhone: '+265-1-999-999',
      establishedDate: new Date(),
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const insertResult = await db.collection('departments').insertOne(testDept);
    console.log('✅ Direct insertion successful:', insertResult.insertedId);

    // Retrieve the inserted document
    const retrievedDept = await db.collection('departments').findOne({ _id: insertResult.insertedId });
    console.log('\n📄 Retrieved document:');
    console.log(JSON.stringify(retrievedDept, null, 2));

    // Clean up
    await db.collection('departments').deleteOne({ _id: insertResult.insertedId });
    console.log('\n🧹 Test document deleted');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

checkDepartmentSchema();

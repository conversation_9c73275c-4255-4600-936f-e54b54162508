const fs = require('fs');
const path = require('path');

// Path to the account form file
const filePath = path.join(__dirname, '..', 'components', 'accounting', 'accounts', 'account-form.tsx');

// Check if the file exists
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Read the file content
let content = fs.readFileSync(filePath, 'utf8');

// Define the fixes to apply
const fixes = [
  // Fix 1: Replace 'any' type in props interface
  {
    pattern: /account\?: any/g,
    replacement: `account?: {
    _id: string;
    accountNumber: string;
    name: string;
    type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
    subtype?: string;
    description?: string;
    isActive: boolean;
    parentAccount?: { _id: string };
    costCenter?: { _id: string };
    tags?: string[];
    fiscalYear?: string;
    reportingGroup?: string;
  }`
  },
  
  // Fix 2: Replace 'any[]' type for parentAccounts
  {
    pattern: /const \[parentAccounts, setParentAccounts\] = useState<any\[\]>\(\[\]\)/g,
    replacement: `const [parentAccounts, setParentAccounts] = useState<Array<{
    _id: string;
    accountNumber: string;
    name: string;
  }>>([])` 
  },
  
  // Fix 3: Replace 'any[]' type for costCenters
  {
    pattern: /const \[costCenters, setCostCenters\] = useState<any\[\]>\(\[\]\)/g,
    replacement: `const [costCenters, setCostCenters] = useState<Array<{
    _id: string;
    code: string;
    name: string;
  }>>([])` 
  }
];

// Apply all fixes
let fixedContent = content;
let fixCount = 0;

for (const fix of fixes) {
  const originalContent = fixedContent;
  fixedContent = fixedContent.replace(fix.pattern, fix.replacement);
  
  if (originalContent !== fixedContent) {
    fixCount++;
  }
}

// Save the file if changes were made
if (content !== fixedContent) {
  // Create a backup of the original file
  fs.writeFileSync(`${filePath}.bak`, content);
  console.log(`Created backup at ${filePath}.bak`);
  
  // Write the fixed content
  fs.writeFileSync(filePath, fixedContent);
  console.log(`Applied ${fixCount} fixes to ${filePath}`);
} else {
  console.log('No changes were needed or applied.');
}

console.log('Done!');

/**
 * TypeScript Error Fixer
 * 
 * This script fixes common TypeScript errors in files.
 * It can be used to automatically fix issues across the codebase.
 */

import * as fs from 'fs';
import * as path from 'path';

// Error patterns and their fixes
const ERROR_FIXES = [
  // Template string errors
  {
    pattern: /'([^']*\$\{[^']*\}[^']*)'/g,
    fix: (match: string, p1: string) => `\`${p1}\``,
    name: 'Template String Error',
  },
  
  // Redundant error instanceof Error checks
  {
    pattern: /error instanceof Error \? error instanceof Error \? (error instanceof Error \? )*(error\.message)/g,
    fix: (_: string, p1: string) => `error instanceof Error ? ${p1}`,
    name: 'Redundant Error Check',
  },
  
  // initialFocus prop error
  {
    pattern: /<Calendar([^>]*)initialFocus([^>]*)>/g,
    fix: (match: string) => match.replace(/initialFocus/, ''),
    name: 'initialFocus Prop Error',
  },
  
  // Next.js 15 dynamic route parameter errors
  {
    pattern: /params: \{ ([a-zA-Z0-9_]+): string \}/g,
    fix: (match: string, p1: string) => `params: Promise<{ ${p1}: string }>`,
    name: 'Dynamic Route Param Error',
  },
  
  // Duplicate Promise types
  {
    pattern: /Promise<([^>]+)>: Promise<([^>]+)>/g,
    fix: (_: string, _p1: string, p2: string) => `Promise<${p2}>`,
    name: 'Duplicate Promise Error',
  },
];

// Function to fix errors in a file
function fixErrorsInFile(filePath: string): { fixed: boolean; fixCount: number } {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let fixCount = 0;
  
  // Apply each fix
  for (const errorFix of ERROR_FIXES) {
    const originalContent = content;
    content = content.replace(errorFix.pattern, errorFix.fix);
    
    if (content !== originalContent) {
      modified = true;
      // Count the number of fixes
      const matches = originalContent.match(errorFix.pattern);
      fixCount += matches ? matches.length : 0;
      console.log(`  Fixed ${errorFix.name} in ${filePath}`);
    }
  }
  
  // Write the modified content back to the file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
  }
  
  return { fixed: modified, fixCount };
}

// Function to scan a directory recursively for TypeScript files
function scanDirectory(dirPath: string): string[] {
  const files: string[] = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      files.push(...scanDirectory(fullPath));
    } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
      files.push(fullPath);
    }
  }

  return files;
}

// Function to fix null type errors by updating component props
function fixNullTypeErrors(filePath: string): { fixed: boolean; fixCount: number } {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check if the file contains component props
  if (content.includes('interface') && content.includes('Props')) {
    // Parse the file to find component props interfaces
    const interfaceMatches = content.match(/interface\s+([a-zA-Z0-9_]+Props)\s*\{[^}]*\}/g);
    
    if (interfaceMatches) {
      let modified = false;
      let fixCount = 0;
      let newContent = content;
      
      for (const interfaceMatch of interfaceMatches) {
        // Check if the interface has properties that need to be updated
        const propMatches = interfaceMatch.match(/([a-zA-Z0-9_]+):\s*([a-zA-Z0-9_]+)(\s*\|\s*undefined)?;/g);
        
        if (propMatches) {
          for (const propMatch of propMatches) {
            // Update the prop type to include null
            const updatedProp = propMatch.replace(/:\s*([a-zA-Z0-9_]+)(\s*\|\s*undefined)?;/, ': $1 | null | undefined;');
            
            if (updatedProp !== propMatch) {
              newContent = newContent.replace(propMatch, updatedProp);
              modified = true;
              fixCount++;
            }
          }
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`  Fixed Null Type Errors in ${filePath}`);
      }
      
      return { fixed: modified, fixCount };
    }
  }
  
  return { fixed: false, fixCount: 0 };
}

// Main function to run the error fixer
async function main() {
  const args = process.argv.slice(2);
  const dirPath = args[0] || '.';
  
  console.log(`Scanning directory: ${dirPath}`);
  const files = scanDirectory(dirPath);
  console.log(`Found ${files.length} TypeScript files`);
  
  let totalFixedFiles = 0;
  let totalFixCount = 0;
  
  // Fix errors in each file
  for (const file of files) {
    const { fixed: fixed1, fixCount: fixCount1 } = fixErrorsInFile(file);
    const { fixed: fixed2, fixCount: fixCount2 } = fixNullTypeErrors(file);
    
    if (fixed1 || fixed2) {
      totalFixedFiles++;
      totalFixCount += fixCount1 + fixCount2;
    }
  }
  
  console.log(`Fixed ${totalFixCount} errors in ${totalFixedFiles} files`);
}

// Run the main function
main().catch(console.error);

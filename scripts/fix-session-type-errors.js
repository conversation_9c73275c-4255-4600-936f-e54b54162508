#!/usr/bin/env node

/**
 * This script fixes Session type errors in API routes
 * by converting session objects to the expected user format for permission checks
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Backup directory
const BACKUP_DIR = path.join(process.cwd(), 'backups', 'session-type-fixes');

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Patterns to replace
const replacements = [
  // Replace next-auth imports with custom auth
  {
    find: /import\s*{\s*getServerSession\s*}\s*from\s*['"]next-auth['"]/g,
    replace: "import { getCurrentUser } from '@/lib/backend/auth/auth'",
    description: 'Replace getServerSession import with getCurrentUser'
  },
  {
    find: /import\s*{\s*getServerSession\s*}\s*from\s*['"]@\/lib\/backend\/auth\/session['"]/g,
    replace: "import { getCurrentUser } from '@/lib/backend/auth/auth'",
    description: 'Replace getServerSession import with getCurrentUser (from session)'
  },
  {
    find: /import\s*{\s*authOptions\s*}\s*from\s*['"]@\/lib\/backend\/auth\/auth-options['"]/g,
    replace: "// Custom auth system doesn't require authOptions",
    description: 'Remove authOptions import'
  },
  {
    find: /import\s*{\s*authOptions\s*}\s*from\s*['"]@\/lib\/backend\/auth\/auth['"]/g,
    replace: "// Custom auth system doesn't require authOptions",
    description: 'Remove authOptions import from auth'
  },
  {
    find: /import\s*{\s*hasRequiredPermissions\s*}\s*from\s*['"]@\/lib\/backend\/utils\/permissions['"]/g,
    replace: "import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'",
    description: 'Fix permissions import path'
  },

  // Replace session fetching
  {
    find: /const\s+session\s*=\s*await\s+getServerSession\s*\(\s*authOptions\s*\)/g,
    replace: "const user = await getCurrentUser(req)",
    description: 'Replace getServerSession with getCurrentUser'
  },
  {
    find: /const\s+session\s*=\s*await\s+getServerSession\s*\(\s*\)/g,
    replace: "const user = await getCurrentUser(req)",
    description: 'Replace getServerSession with getCurrentUser (no authOptions)'
  },
  {
    find: /const\s+user\s*=\s*await\s+getCurrentUser\s*\(\s*request\s*\)/g,
    replace: "const user = await getCurrentUser(req)",
    description: 'Fix request variable name to req'
  },

  // Replace session checks
  {
    find: /if\s*\(\s*!session\s*\|\|\s*!session\.user\s*\)\s*\{\s*return\s+NextResponse\.json\s*\(\s*\{\s*error\s*:\s*['"]Unauthorized['"]\s*\}\s*,\s*\{\s*status\s*:\s*401\s*\}\s*\)\s*;\s*\}/g,
    replace: "if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }",
    description: 'Replace session check with user check'
  },
  {
    find: /if\s*\(\s*!session\s*\)\s*\{\s*return\s+NextResponse\.json\s*\(\s*\{\s*error\s*:\s*['"]Unauthorized['"]\s*\}\s*,\s*\{\s*status\s*:\s*401\s*\}\s*\)\s*;\s*\}/g,
    replace: "if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }",
    description: 'Replace simple session check with user check'
  },

  // Replace hasRequiredPermissions calls with session
  {
    find: /const\s+hasPermission\s*=\s*hasRequiredPermissions\s*\(\s*session\s*,/g,
    replace: "const hasPermission = hasRequiredPermissions(user,",
    description: 'Replace session with user in permission checks'
  },
  {
    find: /const\s+hasPermission\s*=\s*hasRequiredPermissions\s*\(\s*session\.user\s*,/g,
    replace: "const hasPermission = hasRequiredPermissions(user,",
    description: 'Replace session.user with user in permission checks'
  },

  // Replace session.user.id with user.id
  {
    find: /session\.user\.id/g,
    replace: "user.id",
    description: 'Replace session.user.id with user.id'
  },

  // Replace session.user.role with user.role
  {
    find: /session\.user\.role/g,
    replace: "user.role",
    description: 'Replace session.user.role with user.role'
  },

  // Replace direct user.id references that might be missing the user variable
  {
    find: /createdBy:\s*user\.id/g,
    replace: "createdBy: user.id",
    description: 'Ensure user.id is properly referenced'
  },

  // Replace direct user.id references that might be missing the user variable
  {
    find: /updatedBy:\s*user\.id/g,
    replace: "updatedBy: user.id",
    description: 'Ensure user.id is properly referenced'
  },

  // Bulk import specific fixes

  // Fix user._id references in mongoose ObjectId creation
  {
    find: /new\s+mongoose\.Types\.ObjectId\s*\(\s*user\._id\s*\)/g,
    replace: "user.id",
    description: 'Replace mongoose.Types.ObjectId(user._id) with user.id'
  },

  // Fix user._id.toString() references in logger calls
  {
    find: /userId:\s*user\._id\.toString\(\)/g,
    replace: "userId: user.id",
    description: 'Replace user._id.toString() with user.id in logger calls'
  },

  // Remove unused mongoose import after ObjectId fixes
  {
    find: /import\s+mongoose\s+from\s+['"]mongoose['"]/g,
    replace: "// mongoose import removed as it's no longer needed",
    description: 'Remove unused mongoose import'
  },

  // Fix TypeScript type issues in bulk import routes
  {
    find: /const\s+rows\s*=\s*XLSX\.utils\.sheet_to_json\s*\(\s*worksheet\s*,\s*\{[\s\S]*?\}\s*\)/g,
    replace: "const rows = XLSX.utils.sheet_to_json(worksheet, {\n      defval: null,\n      raw: false, // Convert all data to strings\n      blankrows: false // Skip blank rows\n    }) as Record<string, string>[]",
    description: 'Add proper typing to XLSX.utils.sheet_to_json result'
  },

  // Fix parameter mismatch in getCurrentUser calls - match function parameter
  {
    find: /export\s+async\s+function\s+\w+\s*\(\s*request\s*:\s*NextRequest[^)]*\)[^{]*\{\s*try\s*\{\s*\/\/\s*(?:Check|Get)\s+\w+\s*\n\s*const\s+user\s*=\s*await\s+getCurrentUser\s*\(\s*req\s*\)/g,
    replace: (match) => match.replace('getCurrentUser(req)', 'getCurrentUser(request)'),
    description: 'Fix req variable name to match function parameter (request)'
  },

  // Fix parameter mismatch in getCurrentUser calls - match function parameter
  {
    find: /export\s+async\s+function\s+\w+\s*\(\s*req\s*:\s*NextRequest[^)]*\)[^{]*\{\s*try\s*\{\s*\/\/\s*(?:Check|Get)\s+\w+\s*\n\s*const\s+user\s*=\s*await\s+getCurrentUser\s*\(\s*request\s*\)/g,
    replace: (match) => match.replace('getCurrentUser(request)', 'getCurrentUser(req)'),
    description: 'Fix request variable name to match function parameter (req)'
  },

  // Fix Buffer to Blob conversion for NextResponse
  {
    find: /return\s+new\s+NextResponse\s*\(\s*zipBuffer\s*,/g,
    replace: "const stream = new ReadableStream({\n      start(controller) {\n        controller.enqueue(zipBuffer);\n        controller.close();\n      }\n    });\n    \n    return new NextResponse(stream,",
    description: 'Convert Buffer to ReadableStream for NextResponse'
  },

  // Fix indentation in return statements
  {
    find: /if\s*\(\s*!user\s*\)\s*\{\s*return\s+NextResponse\.json\s*\(\s*\{\s*error\s*:\s*['"]Unauthorized['"]\s*\}\s*,\s*\{\s*status\s*:\s*401\s*\}\s*\)\s*;\s*\}/g,
    replace: "if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }",
    description: 'Fix indentation in unauthorized return statements'
  },

  // Fix indentation in return statements (more direct approach)
  {
    find: /if\s*\(\s*!user\s*\)\s*\{\s*\n\s*return/g,
    replace: "if (!user) {\n      return",
    description: 'Fix indentation in unauthorized return statements (direct)'
  },

  // Fix session?.user to user
  {
    find: /\/\/\s*Check\s+if\s+user\s+is\s+authenticated\s*\n\s*if\s*\(\s*!session\?\.\s*user\s*\)\s*\{/g,
    replace: "// Check if user is authenticated\n    if (!user) {",
    description: 'Fix session?.user to user'
  },

  // Fix broken request line
  {
    find: /\/\/\s*Get\s+current\s+user\s*\nrequest\s*\n\s*if\s*\(\s*!user\s*\)\s*\{/g,
    replace: "// Get current user\n    const user = await getCurrentUser(request);\n\n    if (!user) {",
    description: 'Fix broken request line'
  },

  // Fix dynamic route params type
  {
    find: /export\s+async\s+function\s+\w+\s*\(\s*req:\s*NextRequest,\s*context:\s*\{\s*params:\s*\{\s*id:\s*string\s*\}\s*\}\s*\)/g,
    replace: "export async function $1(req: NextRequest, context: Promise<{ params: { id: string } }>)",
    description: 'Fix dynamic route params type'
  },

  // Fix context.params.id to await context
  {
    find: /const\s+account\s*=\s*await\s+Account\.findById\(context\.params\.id\)/g,
    replace: "const { params } = await context;\n    const account = await Account.findById(params.id)",
    description: 'Fix context.params.id to await context'
  },

  // Fix parameter mismatch in API routes (request/req) - more general pattern
  {
    find: /export\s+async\s+function\s+\w+\s*\(\s*request\s*:\s*NextRequest[^)]*\)[^{]*\{\s*try\s*\{\s*\/\/\s*(?:Get|Check)[^}]*\n\s*const\s+user\s*=\s*await\s+getCurrentUser\s*\(\s*req\s*\)/g,
    replace: (match) => match.replace('getCurrentUser(req)', 'getCurrentUser(request)'),
    description: 'Fix req to request in API routes'
  },

  // Fix parameter mismatch in API routes (req/request) - more general pattern
  {
    find: /export\s+async\s+function\s+\w+\s*\(\s*req\s*:\s*NextRequest[^)]*\)[^{]*\{\s*try\s*\{\s*\/\/\s*(?:Get|Check)[^}]*\n\s*const\s+user\s*=\s*await\s+getCurrentUser\s*\(\s*request\s*\)/g,
    replace: (match) => match.replace('getCurrentUser(request)', 'getCurrentUser(req)'),
    description: 'Fix request to req in API routes'
  }
];

/**
 * Fix a file by applying all replacements
 */
function fixFile(filePath) {
  console.log(`Processing ${filePath}...`);

  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = false;

  // Create backup
  const relativePath = path.relative(process.cwd(), filePath);
  const backupPath = path.join(BACKUP_DIR, relativePath);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.writeFileSync(backupPath, content);

  // Check if file has session type errors or bulk import issues
  if ((content.includes('getServerSession') &&
       (content.includes('hasRequiredPermissions(session') ||
        content.includes('session.user.id'))) ||
      (content.includes('user._id') ||
       content.includes('mongoose.Types.ObjectId') ||
       (content.includes('XLSX.utils.sheet_to_json') && !content.includes('as Record<string, string>[]'))) ||
      (content.includes('getCurrentUser(request)') ||
       (content.includes('if (!session)') && content.includes('getCurrentUser'))) ||
      (content.includes('getCurrentUser(req)')) ||
      (content.includes('new NextResponse(zipBuffer,')) ||
      (content.includes('LogCategory.PAYROLL') && !content.includes('import { LogCategory } from')) ||
      (content.includes('export async function POST(request: NextRequest)') && content.includes('const user = await getCurrentUser(req)')) ||
      (content.includes('export async function POST(req: NextRequest)') && content.includes('const user = await getCurrentUser(request)')) ||
      (content.includes('session?.user'))) {
    console.log(`Found session type error or bulk import issue in ${filePath}`);

    // Apply all replacements
    for (const replacement of replacements) {
      if (replacement.find.test(newContent)) {
        newContent = newContent.replace(replacement.find, replacement.replace);
        changes = true;
      }
    }

    // Write changes if needed
    if (changes) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed session type error in ${filePath}`);
      return true;
    }
  }

  return false;
}

/**
 * Find all API route files
 */
function findApiRoutes(directory = 'app/api') {
  try {
    const result = execSync(`find ${directory} -name "route.ts"`, { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error(`Error finding API routes in ${directory}:`, error);
    return [];
  }
}

/**
 * Main function
 */
function main(directory = 'app/api') {
  console.log(`Starting session type error fixes for directory: ${directory}...`);

  const routes = findApiRoutes(directory);
  console.log(`Found ${routes.length} API routes to check`);

  let fixedCount = 0;

  for (const route of routes) {
    try {
      const fixed = fixFile(route);
      if (fixed) fixedCount++;
    } catch (error) {
      console.error(`Error fixing ${route}:`, error);
    }
  }

  console.log(`Fixed session type errors in ${fixedCount} files`);
  console.log('Done!');
}

// Get directory from command line arguments or use default
const directory = process.argv[2] || 'app/api';
main(directory);

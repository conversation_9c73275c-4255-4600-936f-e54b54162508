#!/bin/bash

# Test script for Expenditure Management Phase 3 components
# This script runs comprehensive tests for all Phase 3 features

echo "🧪 Running Expenditure Management Phase 3 Tests"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install Node.js and npm first."
    exit 1
fi

# Install test dependencies if not already installed
print_status "Checking test dependencies..."

# Check if Jest is installed
if ! npm list jest &> /dev/null; then
    print_warning "Jest not found. Installing test dependencies..."
    npm install --save-dev jest @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-environment-jsdom
fi

# Check if react-dropzone is installed (required for receipt upload tests)
if ! npm list react-dropzone &> /dev/null; then
    print_warning "react-dropzone not found. Installing..."
    npm install react-dropzone
fi

print_status "Running Receipt Processing Service Tests..."
npm test -- __tests__/services/receipt-processing-service.test.ts --verbose

if [ $? -eq 0 ]; then
    print_success "Receipt Processing Service tests passed!"
else
    print_error "Receipt Processing Service tests failed!"
    exit 1
fi

print_status "Running AI Analytics Service Tests..."
npm test -- __tests__/services/ai-analytics-service.test.ts --verbose

if [ $? -eq 0 ]; then
    print_success "AI Analytics Service tests passed!"
else
    print_error "AI Analytics Service tests failed!"
    exit 1
fi

print_status "Running Receipt Upload Component Tests..."
npm test -- __tests__/components/receipt-upload.test.tsx --verbose

if [ $? -eq 0 ]; then
    print_success "Receipt Upload Component tests passed!"
else
    print_error "Receipt Upload Component tests failed!"
    exit 1
fi

print_status "Running AI Analytics Dashboard Tests..."
npm test -- __tests__/components/ai-analytics-dashboard.test.tsx --verbose

if [ $? -eq 0 ]; then
    print_success "AI Analytics Dashboard tests passed!"
else
    print_error "AI Analytics Dashboard tests failed!"
    exit 1
fi

print_status "Running Integration Tests..."
npm test -- __tests__/integration/expenditure-form-integration.test.tsx --verbose

if [ $? -eq 0 ]; then
    print_success "Integration tests passed!"
else
    print_error "Integration tests failed!"
    exit 1
fi

print_status "Running Coverage Report..."
npm test -- --coverage --coverageDirectory=coverage/expenditure-management

if [ $? -eq 0 ]; then
    print_success "Coverage report generated successfully!"
    print_status "Coverage report available at: coverage/expenditure-management/lcov-report/index.html"
else
    print_warning "Coverage report generation had issues, but tests passed."
fi

print_status "Running Type Checking..."
if command -v tsc &> /dev/null; then
    tsc --noEmit --project tsconfig.json
    if [ $? -eq 0 ]; then
        print_success "TypeScript type checking passed!"
    else
        print_error "TypeScript type checking failed!"
        exit 1
    fi
else
    print_warning "TypeScript compiler not found. Skipping type checking."
fi

print_status "Running Linting..."
if command -v eslint &> /dev/null; then
    npx eslint lib/services/accounting/ components/accounting/expenditures/ --ext .ts,.tsx
    if [ $? -eq 0 ]; then
        print_success "ESLint checks passed!"
    else
        print_warning "ESLint found some issues. Please review and fix."
    fi
else
    print_warning "ESLint not found. Skipping linting."
fi

echo ""
echo "🎉 All Expenditure Management Phase 3 Tests Completed!"
echo "======================================================"
print_success "✅ Receipt Processing Service: All tests passed"
print_success "✅ AI Analytics Service: All tests passed"
print_success "✅ Receipt Upload Component: All tests passed"
print_success "✅ AI Analytics Dashboard: All tests passed"
print_success "✅ Integration Tests: All tests passed"
print_success "✅ Coverage Report: Generated successfully"

echo ""
print_status "Test Summary:"
echo "- 🧪 Total Test Files: 5"
echo "- 🔬 Service Tests: 2"
echo "- 🎨 Component Tests: 2"
echo "- 🔗 Integration Tests: 1"
echo "- 📊 Coverage Threshold: 70%"
echo "- 🎯 TypeScript: Strict mode enabled"

echo ""
print_status "Next Steps:"
echo "1. Review coverage report for any gaps"
echo "2. Add more edge case tests if needed"
echo "3. Run tests in CI/CD pipeline"
echo "4. Monitor test performance in production"

echo ""
print_success "Phase 3 testing complete! 🚀"

# TypeScript Error Finder and Fixer PowerShell Script

param(
    [Parameter(Mandatory=$true)]
    [string]$Directory
)

Write-Host "TypeScript Error Finder and Fixer" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

Write-Host "Scanning directory: $Directory" -ForegroundColor Yellow

Write-Host "`nStep 1: Running TypeScript Error Scanner..." -ForegroundColor Green
npx ts-node scripts/ts-error-scanner.ts $Directory

Write-Host "`nStep 2: Running TypeScript Error Fixer..." -ForegroundColor Green
npx ts-node scripts/ts-error-fixer.ts $Directory

Write-Host "`nStep 3: Running TypeScript Compiler to verify fixes..." -ForegroundColor Green
npx tsc --noEmit --project tsconfig.json

Write-Host "`nDone!" -ForegroundColor Cyan

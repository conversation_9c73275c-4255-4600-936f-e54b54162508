#!/bin/bash

# Next.js API Route Fixer Wrapper Script
# This script makes it easier to run the API route fixer

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if a directory was provided
if [ $# -eq 0 ]; then
  # No arguments, fix all API routes
  echo "Running API route fixer on all routes..."
  node "$SCRIPT_DIR/nextjs-api-route-fixer.js"
else
  # Fix specific directory
  echo "Running API route fixer on directory: $1"
  node "$SCRIPT_DIR/nextjs-api-route-fixer.js" "$1"
fi

#!/usr/bin/env node

/**
 * Cleanup Backup Files Script
 * 
 * This script finds and deletes all .bak files in the project directory.
 * These backup files were created during the API route fixing process.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all .bak files in the project
function findBackupFiles() {
  try {
    const result = execSync('find . -name "*.bak" -type f', { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error finding backup files:', error);
    return [];
  }
}

// Delete a file
function deleteFile(filePath) {
  try {
    fs.unlinkSync(filePath);
    console.log(`✅ Deleted ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error deleting file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('🔍 Finding backup files...');
  const files = findBackupFiles();
  console.log(`Found ${files.length} backup files.`);
  
  let deletedCount = 0;
  
  for (const file of files) {
    console.log(`Deleting ${file}...`);
    if (deleteFile(file)) {
      deletedCount++;
    }
  }
  
  console.log(`\n✨ Done! Deleted ${deletedCount} backup files.`);
}

main();

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import { VoucherTypeDefinition } from '@/models/accounting/VoucherTypeDefinition';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * Default voucher type definitions
 */
const defaultVoucherTypes = [
  {
    typeId: 'payroll_salary',
    name: 'Payroll Voucher (Salary)',
    description: 'Voucher for monthly salary payments to employees',
    category: 'payment',
    subCategory: 'payroll',
    configuration: {
      requiresApproval: true,
      approvalLevels: 2,
      autoNumbering: true,
      numberPrefix: 'PV',
      allowManualEntry: false,
      requiresAttachments: true,
      defaultFiscalYear: true
    },
    customFields: [
      {
        fieldName: 'payrollRunId',
        fieldType: 'reference',
        label: 'Payroll Run',
        description: 'Reference to the payroll run',
        required: true,
        referenceModel: 'PayrollRun',
        displayOptions: {
          order: 1,
          group: 'Payroll Information',
          readonly: true
        }
      },
      {
        fieldName: 'payPeriod',
        fieldType: 'object',
        label: 'Pay Period',
        description: 'Month and year of the pay period',
        required: true,
        displayOptions: {
          order: 2,
          group: 'Payroll Information',
          readonly: true
        }
      },
      {
        fieldName: 'totalEmployees',
        fieldType: 'number',
        label: 'Total Employees',
        description: 'Number of employees in this payroll run',
        required: true,
        displayOptions: {
          order: 3,
          group: 'Payroll Summary',
          readonly: true
        }
      },
      {
        fieldName: 'totalGrossSalary',
        fieldType: 'number',
        label: 'Total Gross Salary',
        description: 'Total gross salary amount',
        required: true,
        displayOptions: {
          order: 4,
          group: 'Payroll Summary',
          readonly: true
        }
      },
      {
        fieldName: 'totalDeductions',
        fieldType: 'number',
        label: 'Total Deductions',
        description: 'Total deductions amount',
        required: true,
        displayOptions: {
          order: 5,
          group: 'Payroll Summary',
          readonly: true
        }
      },
      {
        fieldName: 'totalNetSalary',
        fieldType: 'number',
        label: 'Total Net Salary',
        description: 'Total net salary amount',
        required: true,
        displayOptions: {
          order: 6,
          group: 'Payroll Summary',
          readonly: true
        }
      },
      {
        fieldName: 'approvedBy',
        fieldType: 'reference',
        label: 'Approved By',
        description: 'User who approved the payroll run',
        required: false,
        referenceModel: 'User',
        displayOptions: {
          order: 7,
          group: 'Approval Information',
          readonly: true
        }
      },
      {
        fieldName: 'approvedAt',
        fieldType: 'date',
        label: 'Approved At',
        description: 'Date when payroll was approved',
        required: false,
        displayOptions: {
          order: 8,
          group: 'Approval Information',
          readonly: true
        }
      }
    ],
    integrationSettings: {
      sourceModule: 'payroll',
      apiEndpoint: '/api/payroll/runs/approved',
      dataTransformer: 'payrollDataTransformer',
      validationRules: ['validatePayrollApproval', 'validateVoucherNotExists']
    },
    displayConfiguration: {
      formLayout: 'two-column',
      fieldGroups: [
        {
          groupName: 'Payroll Information',
          fields: ['payrollRunId', 'payPeriod'],
          collapsible: false,
          defaultExpanded: true
        },
        {
          groupName: 'Payroll Summary',
          fields: ['totalEmployees', 'totalGrossSalary', 'totalDeductions', 'totalNetSalary'],
          collapsible: true,
          defaultExpanded: true
        },
        {
          groupName: 'Approval Information',
          fields: ['approvedBy', 'approvedAt'],
          collapsible: true,
          defaultExpanded: false
        }
      ],
      listViewFields: ['payPeriod', 'totalEmployees', 'totalNetSalary', 'approvedBy'],
      detailViewFields: ['payrollRunId', 'payPeriod', 'totalEmployees', 'totalGrossSalary', 'totalDeductions', 'totalNetSalary', 'approvedBy', 'approvedAt'],
      exportFields: ['payPeriod', 'totalEmployees', 'totalGrossSalary', 'totalDeductions', 'totalNetSalary']
    },
    workflowConfiguration: {
      approvalWorkflow: {
        levels: [
          {
            level: 1,
            roles: ['HR_MANAGER', 'HR_DIRECTOR'],
            required: true
          },
          {
            level: 2,
            roles: ['FINANCE_MANAGER', 'FINANCE_DIRECTOR'],
            required: true
          }
        ]
      },
      notifications: {
        onCreate: true,
        onApproval: true,
        onRejection: true,
        onPosting: true,
        recipients: ['HR_MANAGER', 'FINANCE_MANAGER']
      }
    }
  },
  {
    typeId: 'payment_general',
    name: 'General Payment Voucher',
    description: 'General purpose payment voucher for various expenses',
    category: 'payment',
    subCategory: 'general',
    configuration: {
      requiresApproval: true,
      approvalLevels: 1,
      autoNumbering: true,
      numberPrefix: 'PV',
      allowManualEntry: true,
      requiresAttachments: false,
      defaultFiscalYear: true
    },
    customFields: [
      {
        fieldName: 'vendor',
        fieldType: 'string',
        label: 'Vendor/Supplier',
        description: 'Name of the vendor or supplier',
        required: false,
        displayOptions: {
          order: 1,
          group: 'Payment Details',
          placeholder: 'Enter vendor name'
        }
      },
      {
        fieldName: 'invoiceNumber',
        fieldType: 'string',
        label: 'Invoice Number',
        description: 'Reference invoice number',
        required: false,
        displayOptions: {
          order: 2,
          group: 'Payment Details',
          placeholder: 'Enter invoice number'
        }
      },
      {
        fieldName: 'dueDate',
        fieldType: 'date',
        label: 'Due Date',
        description: 'Payment due date',
        required: false,
        displayOptions: {
          order: 3,
          group: 'Payment Details'
        }
      },
      {
        fieldName: 'category',
        fieldType: 'string',
        label: 'Expense Category',
        description: 'Category of the expense',
        required: false,
        validation: {
          enum: ['Office Supplies', 'Utilities', 'Rent', 'Professional Services', 'Travel', 'Other']
        },
        displayOptions: {
          order: 4,
          group: 'Classification'
        }
      }
    ],
    integrationSettings: {
      sourceModule: 'manual'
    },
    displayConfiguration: {
      formLayout: 'two-column',
      fieldGroups: [
        {
          groupName: 'Payment Details',
          fields: ['vendor', 'invoiceNumber', 'dueDate'],
          collapsible: false,
          defaultExpanded: true
        },
        {
          groupName: 'Classification',
          fields: ['category'],
          collapsible: true,
          defaultExpanded: true
        }
      ],
      listViewFields: ['vendor', 'invoiceNumber', 'category', 'dueDate'],
      detailViewFields: ['vendor', 'invoiceNumber', 'dueDate', 'category'],
      exportFields: ['vendor', 'invoiceNumber', 'dueDate', 'category']
    },
    workflowConfiguration: {
      approvalWorkflow: {
        levels: [
          {
            level: 1,
            roles: ['FINANCE_MANAGER', 'FINANCE_DIRECTOR'],
            amountThreshold: 100000,
            required: true
          }
        ]
      }
    }
  },
  {
    typeId: 'payment_utility',
    name: 'Utility Payment Voucher',
    description: 'Voucher for utility payments (electricity, water, internet, etc.)',
    category: 'payment',
    subCategory: 'utility',
    configuration: {
      requiresApproval: true,
      approvalLevels: 1,
      autoNumbering: true,
      numberPrefix: 'UV',
      allowManualEntry: true,
      requiresAttachments: true,
      defaultFiscalYear: true
    },
    customFields: [
      {
        fieldName: 'utilityType',
        fieldType: 'string',
        label: 'Utility Type',
        description: 'Type of utility service',
        required: true,
        validation: {
          enum: ['Electricity', 'Water', 'Internet', 'Phone', 'Gas', 'Waste Management', 'Security']
        },
        displayOptions: {
          order: 1,
          group: 'Utility Information'
        }
      },
      {
        fieldName: 'serviceProvider',
        fieldType: 'string',
        label: 'Service Provider',
        description: 'Name of the utility service provider',
        required: true,
        displayOptions: {
          order: 2,
          group: 'Utility Information',
          placeholder: 'Enter service provider name'
        }
      },
      {
        fieldName: 'accountNumber',
        fieldType: 'string',
        label: 'Account Number',
        description: 'Utility account number',
        required: false,
        displayOptions: {
          order: 3,
          group: 'Utility Information',
          placeholder: 'Enter account number'
        }
      },
      {
        fieldName: 'billingPeriod',
        fieldType: 'object',
        label: 'Billing Period',
        description: 'Start and end date of billing period',
        required: false,
        displayOptions: {
          order: 4,
          group: 'Billing Information'
        }
      },
      {
        fieldName: 'meterReading',
        fieldType: 'object',
        label: 'Meter Reading',
        description: 'Previous and current meter readings',
        required: false,
        displayOptions: {
          order: 5,
          group: 'Billing Information'
        }
      },
      {
        fieldName: 'consumption',
        fieldType: 'number',
        label: 'Consumption',
        description: 'Units consumed during billing period',
        required: false,
        displayOptions: {
          order: 6,
          group: 'Billing Information'
        }
      }
    ],
    integrationSettings: {
      sourceModule: 'manual'
    },
    displayConfiguration: {
      formLayout: 'two-column',
      fieldGroups: [
        {
          groupName: 'Utility Information',
          fields: ['utilityType', 'serviceProvider', 'accountNumber'],
          collapsible: false,
          defaultExpanded: true
        },
        {
          groupName: 'Billing Information',
          fields: ['billingPeriod', 'meterReading', 'consumption'],
          collapsible: true,
          defaultExpanded: true
        }
      ],
      listViewFields: ['utilityType', 'serviceProvider', 'billingPeriod', 'consumption'],
      detailViewFields: ['utilityType', 'serviceProvider', 'accountNumber', 'billingPeriod', 'meterReading', 'consumption'],
      exportFields: ['utilityType', 'serviceProvider', 'accountNumber', 'billingPeriod', 'consumption']
    }
  }
];

/**
 * Seed voucher types
 */
export async function seedVoucherTypes(): Promise<void> {
  try {
    await connectToDatabase();
    
    logger.info('Starting voucher types seeding', LogCategory.ACCOUNTING);

    // Create a default system user ID (you might want to use an actual admin user ID)
    const systemUserId = new mongoose.Types.ObjectId();

    for (const voucherTypeData of defaultVoucherTypes) {
      // Check if voucher type already exists
      const existingType = await VoucherTypeDefinition.findOne({ 
        typeId: voucherTypeData.typeId 
      });

      if (existingType) {
        logger.info(`Voucher type ${voucherTypeData.typeId} already exists, skipping`, LogCategory.ACCOUNTING);
        continue;
      }

      // Create voucher type
      const voucherType = await VoucherTypeDefinition.create({
        ...voucherTypeData,
        isActive: true,
        version: 1,
        createdBy: systemUserId,
        updatedBy: systemUserId
      });

      logger.info(`Created voucher type: ${voucherType.typeId}`, LogCategory.ACCOUNTING, {
        voucherTypeId: voucherType._id,
        typeId: voucherType.typeId,
        name: voucherType.name
      });
    }

    logger.info('Voucher types seeding completed successfully', LogCategory.ACCOUNTING);

  } catch (error) {
    logger.error('Error seeding voucher types', LogCategory.ACCOUNTING, error);
    throw error;
  }
}

/**
 * Setup the complete dynamic voucher system
 */
export async function setupDynamicVoucherSystem(): Promise<void> {
  try {
    logger.info('Setting up Dynamic Voucher System', LogCategory.ACCOUNTING);

    // 1. Seed voucher types
    await seedVoucherTypes();

    // 2. Create indexes for better performance
    await createVoucherIndexes();

    // 3. Validate system integrity
    await validateSystemIntegrity();

    logger.info('Dynamic Voucher System setup completed successfully', LogCategory.ACCOUNTING);

  } catch (error) {
    logger.error('Error setting up Dynamic Voucher System', LogCategory.ACCOUNTING, error);
    throw error;
  }
}

/**
 * Create database indexes for optimal performance
 */
async function createVoucherIndexes(): Promise<void> {
  try {
    await connectToDatabase();

    // Voucher indexes
    const voucherCollection = mongoose.connection.db.collection('vouchers');
    await voucherCollection.createIndex({ voucherTypeId: 1 });
    await voucherCollection.createIndex({ 'dynamicFields.payrollRunId': 1 });
    await voucherCollection.createIndex({ date: 1, voucherTypeId: 1 });
    await voucherCollection.createIndex({ status: 1, voucherTypeId: 1 });

    // VoucherTypeDefinition indexes
    const typeCollection = mongoose.connection.db.collection('vouchertypedefinitions');
    await typeCollection.createIndex({ typeId: 1 }, { unique: true });
    await typeCollection.createIndex({ category: 1, subCategory: 1 });
    await typeCollection.createIndex({ isActive: 1 });

    logger.info('Database indexes created successfully', LogCategory.ACCOUNTING);

  } catch (error) {
    logger.error('Error creating database indexes', LogCategory.ACCOUNTING, error);
    throw error;
  }
}

/**
 * Validate system integrity
 */
async function validateSystemIntegrity(): Promise<void> {
  try {
    await connectToDatabase();

    // Check if voucher types were created
    const voucherTypesCount = await VoucherTypeDefinition.countDocuments({ isActive: true });
    if (voucherTypesCount === 0) {
      throw new Error('No active voucher types found after seeding');
    }

    // Validate each voucher type
    const voucherTypes = await VoucherTypeDefinition.find({ isActive: true });
    for (const type of voucherTypes) {
      // Validate field definitions
      if (type.customFields) {
        for (const field of type.customFields) {
          if (!field.fieldName || !field.fieldType || !field.label) {
            throw new Error(`Invalid field definition in voucher type ${type.typeId}`);
          }
        }
      }

      // Validate display configuration
      if (!type.displayConfiguration) {
        throw new Error(`Missing display configuration for voucher type ${type.typeId}`);
      }
    }

    logger.info('System integrity validation passed', LogCategory.ACCOUNTING, {
      voucherTypesCount,
      validatedTypes: voucherTypes.length
    });

  } catch (error) {
    logger.error('System integrity validation failed', LogCategory.ACCOUNTING, error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  const command = process.argv[2];

  if (command === 'setup') {
    setupDynamicVoucherSystem()
      .then(() => {
        console.log('✅ Dynamic Voucher System setup completed successfully');
        console.log('🎯 You can now create dynamic vouchers with type-specific fields');
        console.log('📚 See docs/DYNAMIC_VOUCHER_SYSTEM.md for detailed documentation');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Error setting up Dynamic Voucher System:', error);
        process.exit(1);
      });
  } else {
    seedVoucherTypes()
      .then(() => {
        console.log('✅ Voucher types seeded successfully');
        console.log('💡 Run "npm run setup-dynamic-vouchers" to complete the full setup');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Error seeding voucher types:', error);
        process.exit(1);
      });
  }
}

/**
 * <PERSON><PERSON><PERSON> to drop the unique index on department name field
 * This script removes the unique constraint that might cause issues during bulk import
 * 
 * Run with: node scripts/drop-department-index.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Main function to drop the index
async function dropDepartmentNameIndex() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Department collection
    const db = mongoose.connection.db;
    const departmentCollection = db.collection('departments');

    // List all indexes
    console.log('Current indexes:');
    const indexes = await departmentCollection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index, null, 2)}`);
    });

    // Try to drop the name index
    try {
      console.log('\nAttempting to drop name_1 index...');
      await departmentCollection.dropIndex('name_1');
      console.log('Successfully dropped name_1 index');
    } catch (error) {
      console.error('Error dropping name_1 index:', error.message);
      
      // Try alternative approach - drop all non-_id indexes
      console.log('\nTrying alternative approach - dropping all non-_id indexes...');
      await departmentCollection.dropIndexes();
      console.log('Dropped all non-_id indexes');
    }

    // Verify indexes after dropping
    console.log('\nIndexes after dropping:');
    const remainingIndexes = await departmentCollection.indexes();
    remainingIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index, null, 2)}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
dropDepartmentNameIndex().catch(console.error);

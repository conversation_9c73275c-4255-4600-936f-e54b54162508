const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Path to the file with the error
  filePath: './app/(dashboard)/admin/database-tools/page.tsx',
  // Whether to create a backup before modifying the file
  createBackup: true,
  // Whether to actually apply the fix (false for dry run)
  applyFix: true
};

// Main function
async function main() {
  console.log(`Fixing unknown type error in ${config.filePath}...`);
  
  try {
    // Check if the file exists
    if (!fs.existsSync(config.filePath)) {
      console.error(`Error: File not found: ${config.filePath}`);
      process.exit(1);
    }
    
    // Read the file content
    const fileContent = fs.readFileSync(config.filePath, 'utf8');
    
    // Find the catch block with the error
    const catchBlockRegex = /catch\s*\(\s*err\s*:\s*unknown\s*\)\s*{[^}]*setError\s*\(\s*err\.message[^}]*}/gs;
    const catchBlock = fileContent.match(catchBlockRegex);
    
    if (!catchBlock) {
      console.log('Could not find the catch block with the error. The file may have been already fixed or the error is in a different location.');
      return;
    }
    
    // Create the fixed catch block
    const fixedCatchBlock = catchBlock[0].replace(
      /setError\s*\(\s*err\.message\s*\|\|\s*['"]([^'"]+)['"]\s*\)/,
      (match, defaultMessage) => {
        return `if (err instanceof Error) {
      setError(err.message || '${defaultMessage}')
    } else {
      setError('${defaultMessage}')
    }`;
      }
    );
    
    // Apply the fix
    const fixedContent = fileContent.replace(catchBlockRegex, fixedCatchBlock);
    
    // Check if the content was actually modified
    if (fixedContent === fileContent) {
      console.log('No changes were made. The file may have been already fixed or the error is in a different location.');
      return;
    }
    
    // Create a backup if configured
    if (config.createBackup) {
      fs.writeFileSync(`${config.filePath}.bak`, fileContent);
      console.log(`Created backup at ${config.filePath}.bak`);
    }
    
    // Apply the fix if configured
    if (config.applyFix) {
      fs.writeFileSync(config.filePath, fixedContent);
      console.log(`Applied fix to ${config.filePath}`);
      
      // Show the fixed code
      console.log('\nFixed code:');
      console.log(fixedCatchBlock);
    } else {
      console.log('This was a dry run. No changes were made.');
      console.log('Set config.applyFix = true to apply the fix.');
      
      // Show the fixed code
      console.log('\nProposed fix:');
      console.log(fixedCatchBlock);
    }
    
  } catch (error) {
    console.error('Error fixing the file:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);

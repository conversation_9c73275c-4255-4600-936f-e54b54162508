const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.tsx', '.ts'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  autoFix: true,
  // Patterns to fix
  patterns: {
    anyType: true,
    toastVariants: true,
    nextAuthUsage: false, // More complex, handle with caution
    dynamicRouteParams: false // More complex, handle with caution
  }
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Fix 'any' type usage
function fixAnyType(content) {
  // Replace simple 'any' types with more specific types where possible
  let fixedContent = content;
  
  // Replace 'any[]' with 'unknown[]' as a safer alternative
  fixedContent = fixedContent.replace(/: any\[\]/g, ': unknown[]');
  
  // Replace standalone 'any' with 'unknown' as a safer alternative
  fixedContent = fixedContent.replace(/: any([,)\s])/g, ': unknown$1');
  
  // Replace 'any' in useState with 'unknown'
  fixedContent = fixedContent.replace(/useState<any>/g, 'useState<unknown>');
  
  return fixedContent;
}

// Fix toast variant issues
function fixToastVariants(content) {
  // Replace unsupported toast variants with 'destructive'
  let fixedContent = content;
  
  // Replace 'warning' variant with 'destructive'
  fixedContent = fixedContent.replace(/variant:\s*['"]warning['"]/g, 'variant: "destructive"');
  
  // Replace other unsupported variants with 'default'
  fixedContent = fixedContent.replace(/variant:\s*['"](?!default|destructive)([^'"]+)['"]/g, 'variant: "default"');
  
  return fixedContent;
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  
  // Apply fixes based on configuration
  if (config.patterns.anyType) {
    const contentAfterFix = fixAnyType(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed 'any' type issues in ${filePath}`);
    }
  }
  
  if (config.patterns.toastVariants) {
    const contentAfterFix = fixToastVariants(fixedContent);
    if (contentAfterFix !== fixedContent) {
      fixedContent = contentAfterFix;
      changesApplied = true;
      console.log(`Fixed toast variant issues in ${filePath}`);
    }
  }
  
  // Save changes if any were applied
  if (changesApplied && config.autoFix) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
function main() {
  console.log('Scanning for component issues...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed issues in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
    console.log('2. Run the component scanner to check for other issues:');
    console.log('   npm run scan-components');
  }
}

// Run the script
main();

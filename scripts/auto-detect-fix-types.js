const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.tsx', '.ts'],
  // Whether to create backups
  createBackups: true,
  // Whether to fix issues automatically
  autoFix: true,
  // Object type definitions
  typeDefinitions: {
    // Integration type definition
    integration: `interface Integration {
  id: string;
  name: string;
  type: string;
  provider: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  authType?: string;
  authData?: {
    token?: string;
    refreshToken?: string;
    expiresAt?: string;
    clientId?: string;
    clientSecret?: string;
    apiKey?: string;
  };
  settings?: Record<string, unknown>;
  lastSync?: string;
  syncFrequency?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  connectionStatus?: 'connected' | 'disconnected' | 'pending';
  errorMessage?: string;
}`,

    // SyncJob type definition
    syncJob: `interface SyncJob {
  id: string;
  name?: string;
  status: 'idle' | 'running' | 'paused' | 'error' | 'completed';
  source: string;
  destination: string;
  dataType: string;
  direction: 'import' | 'export' | 'bidirectional';
  schedule?: {
    frequency: string;
    startDate?: string;
    endDate?: string;
    time?: string;
    days?: string[];
  };
  lastRun?: string;
  nextRun?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  lastRunDuration?: number;
  averageDuration?: number;
  errorMessage?: string;
  errorDetails?: string;
  stats?: {
    totalRuns: number;
    recordsProcessed: number;
    successRate: number;
    errors: number;
  };
}`,

    // BudgetData type definition
    budgetData: `interface BudgetData {
  id?: string;
  name?: string;
  description?: string;
  fiscalYear?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  totalIncome: number;
  totalExpense: number;
  totalActualIncome: number;
  totalActualExpense: number;
  balance?: number;
  categories?: Array<{
    id: string;
    name: string;
    type: 'income' | 'expense';
    budgetedAmount: number;
    actualAmount: number;
    items?: Array<{
      id: string;
      name: string;
      budgetedAmount: number;
      actualAmount: number;
    }>;
  }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}`,

    // MongoFilter type definition
    mongoFilter: `interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}`
  },
  
  // Patterns to detect and fix
  patterns: [
    {
      name: 'integration',
      detect: /integration:\s*any/g,
      replacement: 'integration: Integration',
      typeDefinition: 'integration'
    },
    {
      name: 'syncJob',
      detect: /syncJob:\s*any/g,
      replacement: 'syncJob: SyncJob',
      typeDefinition: 'syncJob'
    },
    {
      name: 'budgetData',
      detect: /budgetData:\s*any/g,
      replacement: 'budgetData: BudgetData',
      typeDefinition: 'budgetData'
    },
    {
      name: 'mongoFilter',
      detect: /\[key:\s*string\]:\s*any/g,
      replacement: '[key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined',
      typeDefinition: 'mongoFilter'
    },
    {
      name: 'details',
      detect: /details\??:\s*any/g,
      replacement: 'details?: Record<string, unknown>',
      typeDefinition: null
    },
    {
      name: 'data',
      detect: /data:\s*any/g,
      replacement: 'data: Record<string, unknown>',
      typeDefinition: null
    },
    {
      name: 'body',
      detect: /let\s+body:\s*any/g,
      replacement: 'let body: Record<string, unknown>',
      typeDefinition: null
    }
  ]
};

// Get all files in directories with specified extensions
function getAllFiles(directories, extensions) {
  const files = [];
  
  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }
  
  return files;
}

// Traverse directory recursively
function traverseDirectory(dir, callback) {
  const fullDir = path.join(__dirname, '..', dir);
  
  if (!fs.existsSync(fullDir)) {
    console.warn(`Directory does not exist: ${fullDir}`);
    return;
  }
  
  const items = fs.readdirSync(fullDir);
  
  for (const item of items) {
    const itemPath = path.join(fullDir, item);
    const relativePath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      traverseDirectory(relativePath, callback);
    } else if (stats.isFile()) {
      callback(relativePath);
    }
  }
}

// Process a single file
function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  let fixedContent = content;
  let changesApplied = false;
  let typeDefinitionsAdded = new Set();
  
  // Check for each pattern
  for (const pattern of config.patterns) {
    if (pattern.detect.test(content)) {
      // Apply replacement
      fixedContent = fixedContent.replace(pattern.detect, pattern.replacement);
      changesApplied = true;
      console.log(`Fixed '${pattern.name}' type in ${filePath}`);
      
      // Add type definition if needed
      if (pattern.typeDefinition && config.typeDefinitions[pattern.typeDefinition]) {
        typeDefinitionsAdded.add(pattern.typeDefinition);
      }
    }
  }
  
  // Add type definitions if needed
  if (typeDefinitionsAdded.size > 0) {
    // Find a good position to insert type definitions
    // Look for import statements or the first line after them
    const lines = fixedContent.split('\n');
    let insertPosition = 0;
    
    // Find the last import statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith('import ')) {
        insertPosition = i + 1;
      } else if (lines[i].trim() === '' && insertPosition > 0) {
        // Found a blank line after imports
        insertPosition = i + 1;
        break;
      }
    }
    
    // If no imports found, look for 'use client' directive
    if (insertPosition === 0) {
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('use client')) {
          insertPosition = i + 2; // Skip the 'use client' line and the blank line after it
          break;
        }
      }
    }
    
    // If still no position found, insert at the beginning
    if (insertPosition === 0) {
      insertPosition = 0;
    }
    
    // Add type definitions
    let typeDefinitionsText = '\n// Auto-generated type definitions\n';
    for (const typeName of typeDefinitionsAdded) {
      typeDefinitionsText += config.typeDefinitions[typeName] + '\n\n';
    }
    
    lines.splice(insertPosition, 0, typeDefinitionsText);
    fixedContent = lines.join('\n');
    changesApplied = true;
  }
  
  // Save changes if any were applied
  if (changesApplied && config.autoFix) {
    if (config.createBackups) {
      fs.writeFileSync(`${fullPath}.bak`, content);
      console.log(`Created backup at ${fullPath}.bak`);
    }
    
    fs.writeFileSync(fullPath, fixedContent);
    console.log(`Applied fixes to ${filePath}`);
  }
  
  return changesApplied;
}

// Main function
function main() {
  console.log('Auto-detecting and fixing object type issues...');
  
  const files = getAllFiles(config.directories, config.extensions);
  console.log(`Found ${files.length} files to scan`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const wasFixed = processFile(file);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed object type issues in ${fixedFiles} out of ${files.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
    console.log('2. Run the component scanner to check for other issues:');
    console.log('   npm run scan-components');
  }
}

// Run the script
main();

// Simple verification of tax calculation logic
function calculateTax(taxableAmount) {
  console.log(`\nCalculating tax for: ${taxableAmount.toLocaleString()}`);
  
  let tax = 0;
  
  if (taxableAmount <= 150000) {
    tax = 0;
    console.log(`Bracket 1: 0-150,000 @ 0% = ${tax.toLocaleString()}`);
  } else if (taxableAmount <= 500000) {
    tax = (taxableAmount - 150000) * 0.25;
    console.log(`Bracket 2: 150,001-500,000 @ 25%`);
    console.log(`Calculation: (${taxableAmount.toLocaleString()} - 150,000) * 0.25 = ${tax.toLocaleString()}`);
  } else if (taxableAmount <= 2550000) {
    tax = 87500 + (taxableAmount - 500000) * 0.3;
    console.log(`Bracket 3: 500,001-2,550,000 @ 30%`);
    console.log(`Calculation: 87,500 + (${taxableAmount.toLocaleString()} - 500,000) * 0.3 = ${tax.toLocaleString()}`);
  } else {
    tax = 702500 + (taxableAmount - 2550000) * 0.35;
    console.log(`Bracket 4: Above 2,550,000 @ 35%`);
    console.log(`Calculation: 702,500 + (${taxableAmount.toLocaleString()} - 2,550,000) * 0.35 = ${tax.toLocaleString()}`);
  }
  
  const netSalary = taxableAmount - tax;
  const effectiveRate = ((tax / taxableAmount) * 100).toFixed(2);
  
  console.log(`Final Tax: ${tax.toLocaleString()}`);
  console.log(`Net Salary: ${netSalary.toLocaleString()}`);
  console.log(`Effective Rate: ${effectiveRate}%`);
  
  return tax;
}

console.log('🧮 Tax Calculation Verification');
console.log('================================');

// Test the problematic salary
const testSalary = 3256128;
const expectedTax = 949644.8;

const calculatedTax = calculateTax(testSalary);

console.log(`\n✅ Expected: ${expectedTax.toLocaleString()}`);
console.log(`✅ Calculated: ${calculatedTax.toLocaleString()}`);
console.log(`✅ Match: ${Math.abs(calculatedTax - expectedTax) < 0.01 ? 'YES' : 'NO'}`);

// Test a few more cases
console.log('\n📊 Additional Test Cases:');
[200000, 1000000, 2000000, 4000000].forEach(amount => {
  calculateTax(amount);
});

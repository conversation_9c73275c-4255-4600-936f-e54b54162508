import fs from 'fs';
import path from 'path';
import * as ts from 'typescript';

interface ScanResult {
  filePath: string;
  issues: {
    type: string;
    line: number;
    column: number;
    message: string;
    code?: string;
  }[];
}

// Configuration for the scanner
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.tsx', '.ts'],
  // Patterns to look for
  patterns: {
    anyType: true,
    toastVariants: true,
    apiRouteAuth: true,
    nextAuthUsage: true,
    dynamicRouteParams: true,
    formControlTypeIssues: true,
    resolverTypeIssues: true,
    filterIncludesIssues: true,
  }
};

// Main function to scan components
async function scanComponents(): Promise<ScanResult[]> {
  const results: ScanResult[] = [];

  // Get all files to scan
  const files = getAllFiles(config.directories, config.extensions);

  // Process each file
  for (const filePath of files) {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const issues = scanFile(filePath, fileContent);

    if (issues.length > 0) {
      results.push({
        filePath,
        issues
      });
    }
  }

  return results;
}

// Get all files in directories with specified extensions
function getAllFiles(directories: string[], extensions: string[]): string[] {
  const files: string[] = [];

  for (const dir of directories) {
    traverseDirectory(dir, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        files.push(filePath);
      }
    });
  }

  return files;
}

// Traverse directory recursively
function traverseDirectory(dir: string, callback: (filePath: string) => void): void {
  if (!fs.existsSync(dir)) {
    console.warn(`Directory does not exist: ${dir}`);
    return;
  }

  const items = fs.readdirSync(dir);

  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);

    if (stats.isDirectory()) {
      traverseDirectory(itemPath, callback);
    } else if (stats.isFile()) {
      callback(itemPath);
    }
  }
}

// Scan a single file for issues
function scanFile(filePath: string, content: string): ScanResult['issues'] {
  const issues: ScanResult['issues'] = [];

  // Create source file
  const sourceFile = ts.createSourceFile(
    filePath,
    content,
    ts.ScriptTarget.Latest,
    true
  );

  // Check for 'any' type usage
  if (config.patterns.anyType) {
    findAnyTypeUsage(sourceFile, content, issues);
  }

  // Check for toast variant issues
  if (config.patterns.toastVariants) {
    findToastVariantIssues(sourceFile, content, issues);
  }

  // Check for API route authentication issues
  if (config.patterns.apiRouteAuth && filePath.includes('/api/')) {
    findApiAuthIssues(sourceFile, content, issues);
  }

  // Check for next-auth usage
  if (config.patterns.nextAuthUsage) {
    findNextAuthUsage(sourceFile, content, issues);
  }

  // Check for dynamic route parameter issues in Next.js 15
  if (config.patterns.dynamicRouteParams && filePath.includes('/app/')) {
    findDynamicRouteParamIssues(sourceFile, content, issues);
  }

  // Check for form control type issues
  if (config.patterns.formControlTypeIssues) {
    findFormControlTypeIssues(sourceFile, content, issues);
  }

  // Check for resolver type issues
  if (config.patterns.resolverTypeIssues) {
    findResolverTypeIssues(sourceFile, content, issues);
  }

  // Check for filter includes issues
  if (config.patterns.filterIncludesIssues) {
    findFilterIncludesIssues(sourceFile, content, issues);
  }

  return issues;
}

// Find 'any' type usage
function findAnyTypeUsage(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Simple regex pattern to find 'any' type
  const anyTypePattern = /: any(\[\])?/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = anyTypePattern.exec(line)) !== null) {
      issues.push({
        type: 'anyType',
        line: lineIndex + 1,
        column: match.index,
        message: "Avoid using 'any' type. Consider using a more specific type.",
        code: line.trim()
      });
    }
  });
}

// Find toast variant issues
function findToastVariantIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find toast with variant other than 'default' or 'destructive'
  const toastVariantPattern = /variant:\s*['"](?!default|destructive)([^'"]+)['"]/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = toastVariantPattern.exec(line)) !== null) {
      issues.push({
        type: 'toastVariant',
        line: lineIndex + 1,
        column: match.index,
        message: `Toast variant '${match[1]}' is not supported. Only 'default' and 'destructive' are valid.`,
        code: line.trim()
      });
    }
  });
}

// Find API route authentication issues
function findApiAuthIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  // Check for next-auth imports in API routes
  if (content.includes('import { getServerSession }') ||
      content.includes('import { getSession }') ||
      content.includes('from "next-auth"') ||
      content.includes('from \'next-auth\'')) {

    issues.push({
      type: 'apiAuth',
      line: 1,
      column: 0,
      message: "API route is using next-auth instead of the project's custom authentication system."
    });
  }
}

// Find next-auth usage
function findNextAuthUsage(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find next-auth imports or usage
  const nextAuthPattern = /next-auth/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = nextAuthPattern.exec(line)) !== null) {
      issues.push({
        type: 'nextAuth',
        line: lineIndex + 1,
        column: match.index,
        message: "Using next-auth instead of the project's custom authentication system.",
        code: line.trim()
      });
    }
  });
}

// Find dynamic route parameter issues in Next.js 15
function findDynamicRouteParamIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find dynamic route parameters without Promise typing
  const dynamicRoutePattern = /params:\s*{\s*([^:]+):\s*string\s*}/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = dynamicRoutePattern.exec(line)) !== null) {
      issues.push({
        type: 'dynamicRouteParams',
        line: lineIndex + 1,
        column: match.index,
        message: "In Next.js 15, dynamic route parameters are passed as Promises and must be typed as { params: Promise<{ id: string }> }",
        code: line.trim()
      });
    }
  });

  // Check for incorrect Promise syntax in route handlers
  if (content.includes(': Promise<NextResponse> {') || content.includes(': Promise<Response>: Promise<NextResponse>')) {
    issues.push({
      type: 'routeHandlerPromise',
      line: 1,
      column: 0,
      message: "Incorrect Promise syntax in route handler. Should be 'Promise<NextResponse>' not 'Promise<Response>: Promise<NextResponse>'."
    });
  }
}

// Find form control type issues
function findFormControlTypeIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find form control without type assertion
  const formControlPattern = /control=\{form\.control\}/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = formControlPattern.exec(line)) !== null) {
      issues.push({
        type: 'formControlType',
        line: lineIndex + 1,
        column: match.index,
        message: "Form control may have type mismatch. Consider using 'control={form.control as any}' to fix type issues.",
        code: line.trim()
      });
    }
  });
}

// Find resolver type issues
function findResolverTypeIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find resolver without type assertion
  const resolverPattern = /resolver:\s*zodResolver\([^)]+\)/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = resolverPattern.exec(line)) !== null) {
      // Check if it doesn't already have 'as any'
      if (!line.includes('as any')) {
        issues.push({
          type: 'resolverType',
          line: lineIndex + 1,
          column: match.index,
          message: "Zod resolver may have type mismatch. Consider using 'resolver: zodResolver(schema) as any' to fix type issues.",
          code: line.trim()
        });
      }
    }
  });
}

// Find filter includes issues
function findFilterIncludesIssues(sourceFile: ts.SourceFile, content: string, issues: ScanResult['issues']): void {
  const lines = content.split('\n');

  // Pattern to find potentially unsafe includes calls on filter values
  const filterIncludesPattern = /\.getFilterValue\(\)\s*\??\s*\.includes\(/g;

  lines.forEach((line, lineIndex) => {
    let match;
    while ((match = filterIncludesPattern.exec(line)) !== null) {
      // Check if it doesn't already have type assertion
      if (!line.includes('as string[]') && !line.includes('as Array<')) {
        issues.push({
          type: 'filterIncludes',
          line: lineIndex + 1,
          column: match.index,
          message: "Potential 'includes' method error on filter value. Consider using '(getFilterValue() as string[] || []).includes()' pattern.",
          code: line.trim()
        });
      }
    }
  });
}

// Run the scanner and output results
async function main() {
  console.log('Scanning components for issues...');
  const results = await scanComponents();

  console.log(`\nFound issues in ${results.length} files:`);

  // Output results
  for (const result of results) {
    console.log(`\n${result.filePath}:`);

    for (const issue of result.issues) {
      console.log(`  [${issue.type}] Line ${issue.line}: ${issue.message}`);
      if (issue.code) {
        console.log(`    ${issue.code}`);
      }
    }
  }

  // Save results to file
  fs.writeFileSync(
    'component-scan-results.json',
    JSON.stringify(results, null, 2)
  );

  console.log('\nResults saved to component-scan-results.json');
}

// Run the scanner
main().catch(error => {
  console.error('Error running scanner:', error);
  process.exit(1);
});

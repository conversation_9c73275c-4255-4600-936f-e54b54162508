/**
 * <PERSON><PERSON><PERSON> to directly modify the Employee model to remove the unique constraint
 * This script updates the Employee model to ensure employeeNumber is always set
 * 
 * Run with: node scripts/drop-index-direct.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Main function to fix the Employee model
async function fixEmployeeModel() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Employee collection
    const db = mongoose.connection.db;
    const employeeCollection = db.collection('employees');

    // Find all employees with null employeeNumber
    const nullEmployees = await employeeCollection.find({ 
      $or: [
        { employeeNumber: null },
        { employeeNumber: { $exists: false } }
      ]
    }).toArray();

    console.log(`Found ${nullEmployees.length} employees with null employeeNumber`);

    // Update each employee with a unique employeeNumber
    for (const employee of nullEmployees) {
      const timestamp = Date.now().toString().slice(-6);
      const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
      const uniqueEmployeeNumber = `EMP-FIX-${timestamp}-${random}`;

      await employeeCollection.updateOne(
        { _id: employee._id },
        { $set: { employeeNumber: uniqueEmployeeNumber } }
      );

      console.log(`Updated employee ${employee._id}: employeeNumber = ${uniqueEmployeeNumber}`);
    }

    // Create a new unique index on employeeNumber that ignores null values
    console.log('\nCreating a new partial index that ignores null values...');
    await employeeCollection.createIndex(
      { employeeNumber: 1 },
      { 
        unique: true,
        partialFilterExpression: { employeeNumber: { $exists: true, $ne: null } }
      }
    );
    console.log('New index created successfully');

    // List all indexes to verify
    console.log('\nVerifying indexes:');
    const indexes = await employeeCollection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${JSON.stringify(index)}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
fixEmployeeModel().catch(console.error);

# Component Scanner

This tool scans your codebase for common issues and patterns that need to be fixed. It's designed to help identify problems similar to those found in `components/accounting/accounts/account-form.tsx`.

## Issues Detected

The scanner looks for the following issues:

1. **Use of `any` type**
   - Detects usage of the `any` type in TypeScript files
   - Recommends using more specific types instead

2. **Toast Variant Issues**
   - Identifies toast components with unsupported variants
   - Only 'default' and 'destructive' variants are supported

3. **API Route Authentication Issues**
   - Detects API routes using next-auth instead of the project's custom authentication system

4. **Next.js 15 Dynamic Route Parameter Issues**
   - Identifies dynamic route parameters that aren't properly typed as Promises
   - Detects incorrect Promise syntax in route handlers

## How to Run

### Using npm script

```bash
npm run scan-components
```

### Using PowerShell (Windows)

```powershell
.\scripts\run-scanner.ps1
```

### Using Node.js directly

```bash
node scripts\run-scanner.js
```

## Output

The scanner will output issues to the console and also save them to a JSON file named `component-scan-results.json` in the project root.

Example output:

```
Scanning components for issues...

Found issues in 3 files:

components/accounting/accounts/account-form.tsx:
  [anyType] Line 53: Avoid using 'any' type. Consider using a more specific type.
    account?: any

app/api/attendance/check-leave/route.ts:
  [nextAuth] Line 5: Using next-auth instead of the project's custom authentication system.
    import { getServerSession } from "next-auth/next"

app/[id]/page.tsx:
  [dynamicRouteParams] Line 10: In Next.js 15, dynamic route parameters are passed as Promises and must be typed as { params: Promise<{ id: string }> }
    params: { id: string }
```

## Customizing the Scanner

You can customize which issues to scan for by editing the `config` object in `scripts/component-scanner.ts`:

```typescript
const config = {
  // Directories to scan
  directories: ['components', 'app'],
  // File extensions to scan
  extensions: ['.tsx', '.ts'],
  // Patterns to look for
  patterns: {
    anyType: true,
    toastVariants: true,
    apiRouteAuth: true,
    nextAuthUsage: true,
    dynamicRouteParams: true,
  }
};
```

Set any pattern to `false` to disable scanning for that issue.

## Adding New Patterns

To add new patterns to scan for, you can:

1. Add a new property to the `patterns` object in the config
2. Create a new function to detect the pattern
3. Call that function from the `scanFile` function when your pattern is enabled

## Fixing Issues

After identifying issues, you can:

1. Fix them manually one by one
2. Create automated scripts similar to the existing fix scripts in the project
3. Use the scan results as a guide for systematic fixes across the codebase

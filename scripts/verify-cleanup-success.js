// Script to verify the payroll cleanup was successful
const fs = require('fs');
const path = require('path');

// Check if deprecated files were removed
const deprecatedFiles = [
  'lib/services/payroll/payroll-service.ts',
  'lib/services/payroll/salary-calculation-service.ts',
  'services/payroll/SalaryService.ts',
  'services/payroll/PayrollService.ts',
  'lib/services/accounting/payroll-service.ts',
  'models/accounting/PayrollRecord.ts',
  'models/accounting/EmployeeSalary.ts',
  'lib/services/payroll/payroll-accounting-service.ts',
  'lib/services/accounting/payroll-integration-service.ts'
];

// Check if backup files exist
const backupFiles = deprecatedFiles.map(file => `${file}.deprecated.backup`);

// Check if unified service exists
const unifiedServiceFile = 'lib/services/payroll/unified-payroll-service.ts';

// Patterns to search for remaining deprecated imports
const deprecatedPatterns = [
  'salary-calculation-service',
  'services/payroll/SalaryService',
  'services/payroll/PayrollService',
  'lib/services/accounting/payroll-service',
  'models/accounting/PayrollRecord',
  'models/accounting/EmployeeSalary'
];

function checkFileExists(filePath) {
  return fs.existsSync(path.join(process.cwd(), filePath));
}

function scanForDeprecatedImports() {
  const results = [];
  
  // Simple scan of key directories
  const dirsToScan = ['app/api', 'lib/services', 'components'];
  
  for (const dir of dirsToScan) {
    const dirPath = path.join(process.cwd(), dir);
    if (fs.existsSync(dirPath)) {
      scanDirectory(dirPath, results);
    }
  }
  
  return results;
}

function scanDirectory(dirPath, results) {
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, results);
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        scanFile(fullPath, results);
      }
    }
  } catch (error) {
    // Skip directories we can't read
  }
}

function scanFile(filePath, results) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    for (const pattern of deprecatedPatterns) {
      if (content.includes(pattern)) {
        results.push({
          file: path.relative(process.cwd(), filePath),
          pattern: pattern,
          lines: getLineNumbers(content, pattern)
        });
      }
    }
  } catch (error) {
    // Skip files we can't read
  }
}

function getLineNumbers(content, pattern) {
  const lines = content.split('\n');
  const lineNumbers = [];
  
  lines.forEach((line, index) => {
    if (line.includes(pattern)) {
      lineNumbers.push(index + 1);
    }
  });
  
  return lineNumbers;
}

function verifyCleanup() {
  console.log('🔍 Verifying payroll cleanup success...\n');
  
  let success = true;
  
  // Check 1: Verify deprecated files were removed
  console.log('📁 Checking deprecated files removal...');
  let removedCount = 0;
  for (const file of deprecatedFiles) {
    if (!checkFileExists(file)) {
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } else {
      console.log(`❌ Still exists: ${file}`);
      success = false;
    }
  }
  console.log(`   ${removedCount}/${deprecatedFiles.length} files removed\n`);
  
  // Check 2: Verify backup files exist
  console.log('💾 Checking backup files...');
  let backupCount = 0;
  for (const backupFile of backupFiles) {
    if (checkFileExists(backupFile)) {
      console.log(`✅ Backup exists: ${backupFile}`);
      backupCount++;
    } else {
      console.log(`⚠️  Backup missing: ${backupFile}`);
    }
  }
  console.log(`   ${backupCount}/${backupFiles.length} backup files found\n`);
  
  // Check 3: Verify unified service exists
  console.log('🎯 Checking unified service...');
  if (checkFileExists(unifiedServiceFile)) {
    console.log(`✅ Unified service exists: ${unifiedServiceFile}`);
  } else {
    console.log(`❌ Unified service missing: ${unifiedServiceFile}`);
    success = false;
  }
  console.log('');
  
  // Check 4: Scan for remaining deprecated imports
  console.log('🔍 Scanning for remaining deprecated imports...');
  const remainingImports = scanForDeprecatedImports();
  
  if (remainingImports.length === 0) {
    console.log('✅ No deprecated imports found');
  } else {
    console.log(`❌ Found ${remainingImports.length} files with deprecated imports:`);
    remainingImports.forEach(result => {
      console.log(`   ${result.file} - ${result.pattern} (lines: ${result.lines.join(', ')})`);
    });
    success = false;
  }
  console.log('');
  
  // Final result
  if (success) {
    console.log('🎉 CLEANUP VERIFICATION: SUCCESS!');
    console.log('\n✅ All checks passed:');
    console.log('   - Deprecated files removed');
    console.log('   - Backup files created');
    console.log('   - Unified service exists');
    console.log('   - No deprecated imports found');
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Test the application to ensure everything works');
    console.log('2. Run a payroll calculation to verify the unified service');
    console.log('3. Monitor logs for any issues');
    console.log('4. Update documentation if needed');
  } else {
    console.log('❌ CLEANUP VERIFICATION: ISSUES FOUND!');
    console.log('\n🔧 Required Actions:');
    console.log('1. Fix any remaining deprecated imports');
    console.log('2. Ensure all deprecated files are removed');
    console.log('3. Verify unified service is properly implemented');
    console.log('4. Re-run this verification script');
    
    console.log('\n🔄 Rollback Option:');
    console.log('If needed, restore backup files:');
    console.log('find . -name "*.deprecated.backup" -exec sh -c \'mv "$1" "${1%.deprecated.backup}"\' _ {} \\;');
  }
  
  console.log('\n📊 Summary:');
  console.log(`   - Files removed: ${removedCount}/${deprecatedFiles.length}`);
  console.log(`   - Backup files: ${backupCount}/${backupFiles.length}`);
  console.log(`   - Deprecated imports: ${remainingImports.length}`);
  console.log(`   - Overall status: ${success ? 'SUCCESS' : 'NEEDS ATTENTION'}`);
}

// Run verification
verifyCleanup();

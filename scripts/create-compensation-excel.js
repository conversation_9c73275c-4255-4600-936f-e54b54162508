const XLSX = require('xlsx');
const path = require('path');

// Create compensation template data
const compensationData = [
  // Header row
  [
    'Employee ID',
    'Employee Email',
    'Employee Name',
    'Department',
    'Compensation Type',
    'Amount',
    'Effective Date',
    'Description',
    'Notes',
    'Currency',
    'Payroll Run ID',
    'Is Recurring',
    'Frequency',
    'End Date',
    'Taxable',
    'Pensionable'
  ],
  // Example data rows
  [
    '507f1f77bcf86cd799439011',
    '<EMAIL>',
    '<PERSON>',
    'Finance Department',
    'performance_bonus',
    '50000',
    '2024-02-01',
    'Q4 2023 Performance Bonus',
    'Excellent performance rating',
    'MWK',
    '',
    'false',
    'one_time',
    '',
    'true',
    'false'
  ],
  [
    '',
    '<EMAIL>',
    '<PERSON>',
    'HR Department',
    'holiday_bonus',
    '25000',
    '2024-02-01',
    'Christmas Holiday Bonus',
    'Annual holiday bonus',
    'MWK',
    '',
    'false',
    'one_time',
    '',
    'true',
    'false'
  ],
  [
    '',
    '<EMAIL>',
    '<PERSON>',
    'ICT Department',
    'overtime',
    '15000',
    '2024-02-01',
    'January Overtime Payment',
    'Weekend work compensation',
    'MWK',
    '',
    'false',
    'one_time',
    '',
    'true',
    'true'
  ],
  [
    '',
    '<EMAIL>',
    'Sarah Johnson',
    'Compliance Department',
    'special_allowance',
    '20000',
    '2024-02-01',
    'Project Completion Allowance',
    'Special project completion bonus',
    'MWK',
    '',
    'true',
    'monthly',
    '2024-06-30',
    'true',
    'false'
  ],
  [
    '',
    '<EMAIL>',
    'David Brown',
    'Finance Department',
    'retroactive_adjustment',
    '30000',
    '2024-01-01',
    'Salary Adjustment Backpay',
    'Retroactive salary increase',
    'MWK',
    '',
    'false',
    'one_time',
    '',
    'true',
    'true'
  ],
  [
    '',
    '<EMAIL>',
    'Mary White',
    'Administration',
    'one_time_deduction',
    '5000',
    '2024-02-01',
    'Uniform Cost Deduction',
    'Deduction for uniform purchase',
    'MWK',
    '',
    'false',
    'one_time',
    '',
    'false',
    'false'
  ]
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Create worksheet from the data
const ws = XLSX.utils.aoa_to_sheet(compensationData);

// Set column widths for better readability
const colWidths = [
  { wch: 25 }, // Employee ID
  { wch: 30 }, // Employee Email
  { wch: 20 }, // Employee Name
  { wch: 20 }, // Department
  { wch: 20 }, // Compensation Type
  { wch: 15 }, // Amount
  { wch: 15 }, // Effective Date
  { wch: 35 }, // Description
  { wch: 40 }, // Notes
  { wch: 10 }, // Currency
  { wch: 25 }, // Payroll Run ID
  { wch: 12 }, // Is Recurring
  { wch: 12 }, // Frequency
  { wch: 15 }, // End Date
  { wch: 10 }, // Taxable
  { wch: 12 }  // Pensionable
];

ws['!cols'] = colWidths;

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Compensation Data');

// Create instructions sheet
const instructionsData = [
  ['Compensation Bulk Import Instructions'],
  [''],
  ['Required Fields:'],
  ['• Employee ID OR Employee Email (at least one must be provided)'],
  ['• Compensation Type (performance_bonus, holiday_bonus, overtime, special_allowance, one_time_deduction, retroactive_adjustment)'],
  ['• Amount (the compensation amount in MWK)'],
  ['• Effective Date (format: YYYY-MM-DD, e.g., 2024-02-01)'],
  [''],
  ['Optional Fields:'],
  ['• Employee Name (for reference only)'],
  ['• Department (for reference only)'],
  ['• Description (explanation of the compensation)'],
  ['• Notes (additional comments)'],
  ['• Currency (defaults to MWK if not provided)'],
  ['• Payroll Run ID (to associate with specific payroll run)'],
  ['• Is Recurring (true/false, defaults to false)'],
  ['• Frequency (one_time, monthly, quarterly, annually - defaults to one_time)'],
  ['• End Date (for recurring compensations, format: YYYY-MM-DD)'],
  ['• Taxable (true/false, defaults to true)'],
  ['• Pensionable (true/false, defaults to false)'],
  [''],
  ['Compensation Types:'],
  ['• performance_bonus: Performance-based bonus payments'],
  ['• holiday_bonus: Holiday or seasonal bonus payments'],
  ['• overtime: Overtime work compensation'],
  ['• special_allowance: Special project or role allowances'],
  ['• one_time_deduction: One-time deductions (uniform, loans, etc.)'],
  ['• retroactive_adjustment: Backpay or retroactive salary adjustments'],
  [''],
  ['Important Notes:'],
  ['• Each compensation record will be created as pending status'],
  ['• Deductions (one_time_deduction) will be processed as negative amounts'],
  ['• Recurring compensations require an end date'],
  ['• All amounts should be in MWK unless otherwise specified'],
  ['• Effective dates should be current or future dates'],
  ['• Taxable compensations will be subject to income tax calculations'],
  ['• Pensionable compensations will be included in pension calculations'],
  [''],
  ['Example Data:'],
  ['The template includes example data showing different scenarios:'],
  ['• Performance bonus for excellent work'],
  ['• Holiday bonus for seasonal payments'],
  ['• Overtime compensation for extra work'],
  ['• Special allowance for project work'],
  ['• Retroactive adjustment for backpay'],
  ['• One-time deduction for uniform costs'],
  [''],
  ['Tips:'],
  ['• You can use either Employee ID or Email to identify employees'],
  ['• If both ID and Email are provided, ID takes precedence'],
  ['• Department and Employee Name are for reference and validation'],
  ['• Remove example data before importing your actual data'],
  ['• Test with a small batch first before importing large datasets'],
  ['• Ensure all dates are in YYYY-MM-DD format'],
  ['• Use consistent compensation types from the allowed list']
];

const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);

// Set column width for instructions
instructionsWs['!cols'] = [{ wch: 80 }];

// Add instructions sheet
XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions');

// Create the format_excel directory if it doesn't exist
const fs = require('fs');
const formatExcelDir = path.join(__dirname, '..', 'format_excel');
if (!fs.existsSync(formatExcelDir)) {
  fs.mkdirSync(formatExcelDir, { recursive: true });
}

// Write the file
const filePath = path.join(formatExcelDir, 'compensation_bulk_import_template.xlsx');
XLSX.writeFile(wb, filePath);

console.log(`Compensation Excel template created successfully at: ${filePath}`);
console.log('This file contains:');
console.log('1. Template with example data for compensation bulk import');
console.log('2. Instructions sheet with detailed guidelines');
console.log('3. Proper column formatting and validation notes');
console.log('');
console.log('Supported compensation types:');
console.log('• performance_bonus: Performance-based bonus payments');
console.log('• holiday_bonus: Holiday or seasonal bonus payments');
console.log('• overtime: Overtime work compensation');
console.log('• special_allowance: Special project or role allowances');
console.log('• one_time_deduction: One-time deductions (uniform, loans, etc.)');
console.log('• retroactive_adjustment: Backpay or retroactive salary adjustments');
console.log('');
console.log('Features included:');
console.log('• Support for both Employee ID and Email identification');
console.log('• Multiple compensation types with proper categorization');
console.log('• Recurring compensation support with frequency options');
console.log('• Taxable and pensionable flags for proper calculations');
console.log('• Comprehensive validation and error handling');
console.log('• Example data for different scenarios');

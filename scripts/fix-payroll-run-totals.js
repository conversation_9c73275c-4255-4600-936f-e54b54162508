// Fix Payroll Run Totals Script
// This script recalculates and updates totals for existing payroll runs that have 0 values

const mongoose = require('mongoose');

// Database connection
async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpact';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    throw error;
  }
}

// Define schemas (simplified versions)
const PayrollRunSchema = new mongoose.Schema({
  name: String,
  status: String,
  totalEmployees: { type: Number, default: 0 },
  processedEmployees: { type: Number, default: 0 },
  totalGrossSalary: { type: Number, default: 0 },
  totalDeductions: { type: Number, default: 0 },
  totalTax: { type: Number, default: 0 },
  totalNetSalary: { type: Number, default: 0 },
  currency: { type: String, default: 'MWK' },
  payPeriod: {
    month: Number,
    year: Number,
    startDate: Date,
    endDate: Date
  }
}, { timestamps: true });

const PayrollRecordSchema = new mongoose.Schema({
  employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee' },
  payrollRunId: { type: mongoose.Schema.Types.ObjectId, ref: 'PayrollRun' },
  grossSalary: { type: Number, default: 0 },
  totalDeductions: { type: Number, default: 0 },
  totalTax: { type: Number, default: 0 },
  netSalary: { type: Number, default: 0 },
  currency: { type: String, default: 'MWK' },
  status: String
}, { timestamps: true });

// Create models
const PayrollRun = mongoose.models.PayrollRun || mongoose.model('PayrollRun', PayrollRunSchema);
const PayrollRecord = mongoose.models.PayrollRecord || mongoose.model('PayrollRecord', PayrollRecordSchema);

async function fixPayrollRunTotals() {
  try {
    console.log('🔧 Starting Payroll Run Totals Fix...\n');
    
    await connectToDatabase();

    // Find all payroll runs with 0 totals
    const payrollRunsWithZeroTotals = await PayrollRun.find({
      $or: [
        { totalGrossSalary: 0 },
        { totalGrossSalary: { $exists: false } },
        { totalNetSalary: 0 },
        { totalNetSalary: { $exists: false } }
      ]
    });

    console.log(`📊 Found ${payrollRunsWithZeroTotals.length} payroll runs with zero or missing totals\n`);

    if (payrollRunsWithZeroTotals.length === 0) {
      console.log('✅ No payroll runs need fixing!');
      return;
    }

    let fixedCount = 0;
    let skippedCount = 0;

    for (const payrollRun of payrollRunsWithZeroTotals) {
      console.log(`🔍 Processing Payroll Run: ${payrollRun.name} (${payrollRun._id})`);
      
      // Get all payroll records for this run
      const payrollRecords = await PayrollRecord.find({ 
        payrollRunId: payrollRun._id 
      });

      console.log(`   Found ${payrollRecords.length} payroll records`);

      if (payrollRecords.length === 0) {
        console.log(`   ⚠️  No payroll records found, skipping...`);
        skippedCount++;
        continue;
      }

      // Calculate totals from payroll records
      let totalGrossSalary = 0;
      let totalDeductions = 0;
      let totalTax = 0;
      let totalNetSalary = 0;
      let recordsWithData = 0;

      for (const record of payrollRecords) {
        if (record.grossSalary > 0 || record.netSalary > 0) {
          totalGrossSalary += record.grossSalary || 0;
          totalDeductions += record.totalDeductions || 0;
          totalTax += record.totalTax || 0;
          totalNetSalary += record.netSalary || 0;
          recordsWithData++;
        }
      }

      console.log(`   📈 Calculated totals from ${recordsWithData} records with data:`);
      console.log(`      Gross Salary: ${totalGrossSalary.toLocaleString()} ${payrollRun.currency}`);
      console.log(`      Deductions: ${totalDeductions.toLocaleString()} ${payrollRun.currency}`);
      console.log(`      Tax: ${totalTax.toLocaleString()} ${payrollRun.currency}`);
      console.log(`      Net Salary: ${totalNetSalary.toLocaleString()} ${payrollRun.currency}`);

      if (recordsWithData === 0) {
        console.log(`   ⚠️  No records with salary data found, skipping...`);
        skippedCount++;
        continue;
      }

      // Update the payroll run with calculated totals
      const updateResult = await PayrollRun.updateOne(
        { _id: payrollRun._id },
        {
          $set: {
            totalGrossSalary,
            totalDeductions,
            totalTax,
            totalNetSalary,
            processedEmployees: recordsWithData,
            totalEmployees: Math.max(payrollRun.totalEmployees || 0, recordsWithData)
          }
        }
      );

      if (updateResult.modifiedCount > 0) {
        console.log(`   ✅ Successfully updated payroll run totals`);
        fixedCount++;
      } else {
        console.log(`   ❌ Failed to update payroll run`);
      }

      console.log(''); // Empty line for readability
    }

    console.log(`\n📊 Summary:`);
    console.log(`   Fixed: ${fixedCount} payroll runs`);
    console.log(`   Skipped: ${skippedCount} payroll runs`);
    console.log(`   Total processed: ${payrollRunsWithZeroTotals.length} payroll runs`);

    if (fixedCount > 0) {
      console.log(`\n✅ Payroll run totals have been fixed! Please refresh your browser to see the updated values.`);
    }

  } catch (error) {
    console.error('❌ Error fixing payroll run totals:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the fix
fixPayrollRunTotals();

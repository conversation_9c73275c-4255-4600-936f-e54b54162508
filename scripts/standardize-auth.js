#!/usr/bin/env node

/**
 * This script standardizes the authentication approach across the codebase
 * by converting Next-Auth usage to the custom auth system.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Backup directory
const BACKUP_DIR = path.join(process.cwd(), 'backups', 'auth-standardization');

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Patterns to replace
const replacements = [
  // Import replacements
  {
    find: /import\s*{\s*getServerSession\s*}\s*from\s*['"]next-auth['"]/g,
    replace: "import { getCurrentUser } from '@/lib/backend/auth/auth'",
    description: 'Replace getServerSession import with getCurrentUser'
  },
  {
    find: /import\s*{\s*authOptions\s*}\s*from\s*['"]@\/lib\/backend\/auth\/auth['"]/g,
    replace: "// Custom auth system doesn't require authOptions",
    description: 'Remove authOptions import'
  },
  {
    find: /import\s*{\s*UserRole\s*}\s*from\s*['"]@\/types\/user['"]/g,
    replace: "import { UserRole } from '@/types/user-roles'",
    description: 'Standardize UserRole import'
  },
  {
    find: /import\s*{\s*hasRequiredPermissions\s*}\s*from\s*['"]@\/lib\/backend\/utils\/permissions['"]/g,
    replace: "import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'",
    description: 'Standardize permissions import'
  },
  {
    find: /import\s*{\s*useSession\s*}\s*from\s*['"]next-auth\/react['"]/g,
    replace: "import { useAuth } from '@/lib/frontend/hooks/useAuth'",
    description: 'Replace useSession import with useAuth'
  },
  {
    find: /import\s*{\s*signIn,\s*signOut\s*}\s*from\s*['"]next-auth\/react['"]/g,
    replace: "import { login, logout } from '@/lib/frontend/auth'",
    description: 'Replace signIn/signOut imports with login/logout'
  },
  {
    find: /import\s*{\s*signIn\s*}\s*from\s*['"]next-auth\/react['"]/g,
    replace: "import { login } from '@/lib/frontend/auth'",
    description: 'Replace signIn import with login'
  },
  {
    find: /import\s*{\s*signOut\s*}\s*from\s*['"]next-auth\/react['"]/g,
    replace: "import { logout } from '@/lib/frontend/auth'",
    description: 'Replace signOut import with logout'
  },

  // Authentication code replacements
  {
    find: /const\s+session\s*=\s*await\s+getServerSession\s*\(\s*authOptions\s*\)/g,
    replace: "const user = await getCurrentUser(req)",
    description: 'Replace getServerSession with getCurrentUser'
  },
  {
    find: /if\s*\(\s*!session\s*\)\s*\{\s*return\s+NextResponse\.json\s*\(\s*\{\s*error\s*:\s*['"]Unauthorized['"]\s*\}\s*,\s*\{\s*status\s*:\s*401\s*\}\s*\)\s*;\s*\}/g,
    replace: "if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }",
    description: 'Replace session check with user check'
  },
  {
    find: /const\s+session\s*=\s*await\s+getServerSession\s*\(\s*req,\s*res,\s*authOptions\s*\)/g,
    replace: "const user = await getCurrentUser(req)",
    description: 'Replace getServerSession with getCurrentUser (with req, res)'
  },
  {
    find: /const\s+{\s*data:\s*session\s*}\s*=\s*useSession\(\)/g,
    replace: "const { user } = useAuth()",
    description: 'Replace useSession with useAuth'
  },
  {
    find: /const\s+{\s*data:\s*session,\s*status\s*}\s*=\s*useSession\(\)/g,
    replace: "const { user, status } = useAuth()",
    description: 'Replace useSession with useAuth (with status)'
  },
  {
    find: /await\s+signIn\s*\(\s*['"]credentials['"]/g,
    replace: "await login",
    description: 'Replace signIn with login'
  },
  {
    find: /await\s+signOut\s*\(/g,
    replace: "await logout(",
    description: 'Replace signOut with logout'
  },

  // Permission check replacements
  {
    find: /const\s+hasPermission\s*=\s*hasRequiredPermissions\s*\(\s*session\s*,/g,
    replace: "const hasPermission = hasRequiredPermissions(user,",
    description: 'Replace session with user in permission checks'
  },
  {
    find: /hasRequiredPermissions\s*\(\s*session\s*,/g,
    replace: "hasRequiredPermissions(user,",
    description: 'Replace session with user in hasRequiredPermissions calls'
  },
  {
    find: /hasRequiredPermissions\s*\(\s*session\.user\s*,/g,
    replace: "hasRequiredPermissions(user,",
    description: 'Replace session.user with user in hasRequiredPermissions calls'
  },

  // User reference replacements
  {
    find: /session\.user\.id/g,
    replace: "user.id",
    description: 'Replace session.user.id with user.id'
  },
  {
    find: /session\.user\.(name|email|role)/g,
    replace: "user.$1",
    description: 'Replace session.user properties with user properties'
  },
  {
    find: /session\?.user\?.id/g,
    replace: "user?.id",
    description: 'Replace session?.user?.id with user?.id'
  },
  {
    find: /session\?.user\?.(name|email|role)/g,
    replace: "user?.$1",
    description: 'Replace session?.user properties with user properties'
  },
  {
    find: /status\s*===\s*['"]authenticated['"]/g,
    replace: "status === 'authenticated'",
    description: 'Keep authenticated status check'
  },
  {
    find: /status\s*===\s*['"]loading['"]/g,
    replace: "status === 'loading'",
    description: 'Keep loading status check'
  },
  {
    find: /status\s*===\s*['"]unauthenticated['"]/g,
    replace: "status === 'unauthenticated'",
    description: 'Keep unauthenticated status check'
  }
];

/**
 * Fix a file by applying all replacements
 */
function fixFile(filePath) {
  console.log(`Processing ${filePath}...`);

  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = false;

  // Create backup
  const relativePath = path.relative(process.cwd(), filePath);
  const backupPath = path.join(BACKUP_DIR, relativePath);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.writeFileSync(backupPath, content);

  // Check if file uses next-auth
  if (content.includes('next-auth') || content.includes('getServerSession') || content.includes('authOptions')) {
    console.log(`Found next-auth usage in ${filePath}`);

    // Apply all replacements
    for (const replacement of replacements) {
      if (replacement.find.test(newContent)) {
        newContent = newContent.replace(replacement.find, replacement.replace);
        changes = true;
      }
    }

    // Write changes if needed
    if (changes) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed auth in ${filePath}`);
      return true;
    }
  }

  return false;
}

/**
 * Find all TypeScript and JavaScript files in a directory
 */
function findFiles(directory = 'app') {
  try {
    // Find all TypeScript and JavaScript files
    const result = execSync(`find ${directory} -type f -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx"`, { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error(`Error finding files in ${directory}:`, error);
    return [];
  }
}

/**
 * Main function
 */
function main(directory = 'app') {
  console.log(`Starting auth standardization for directory: ${directory}...`);

  const files = findFiles(directory);
  console.log(`Found ${files.length} files to check`);

  let fixedCount = 0;

  for (const file of files) {
    try {
      const fixed = fixFile(file);
      if (fixed) fixedCount++;
    } catch (error) {
      console.error(`Error fixing ${file}:`, error);
    }
  }

  console.log(`Standardized auth in ${fixedCount} files`);
  console.log('Done!');
}

// Get directory from command line arguments or use default
const directory = process.argv[2] || 'app';
main(directory);

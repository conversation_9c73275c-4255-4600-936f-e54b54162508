#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const BUDGET_DIR = './components/accounting/budget';
const BACKUP_DIR = './backups/budget-components';

// Common error patterns to fix
const ERROR_PATTERNS = [
  // Template string errors
  {
    pattern: /description: 'Failed to [^']*: \$\{errorMessage\}'/g,
    replacement: "description: 'Failed to perform operation'"
  },
  {
    pattern: /description: '[^']*: \$\{[^}]*\}'/g,
    replacement: "description: 'An error occurred'"
  },
  
  // Redundant error instanceof Error checks
  {
    pattern: /error instanceof Error \? error instanceof Error \? error instanceof Error \? error instanceof Error \? error\.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : '[^']*'/g,
    replacement: "error instanceof Error ? error.message : 'An error occurred'"
  },
  {
    pattern: /error instanceof Error \? error instanceof Error \? error instanceof Error \? error\.message : 'An error occurred' : 'An error occurred' : '[^']*'/g,
    replacement: "error instanceof Error ? error.message : 'An error occurred'"
  },
  {
    pattern: /error instanceof Error \? error instanceof Error \? error\.message : 'An error occurred' : '[^']*'/g,
    replacement: "error instanceof Error ? error.message : 'An error occurred'"
  },
  
  // Badge variant issues - change 'warning' to 'secondary'
  {
    pattern: /<Badge variant="warning"/g,
    replacement: '<Badge variant="secondary"'
  },
  
  // Alert variant issues - change 'warning' to 'default'
  {
    pattern: /<Alert variant="warning"/g,
    replacement: '<Alert variant="default"'
  },
  
  // Fix any type usage
  {
    pattern: /useState<any\[\]>/g,
    replacement: 'useState<unknown[]>'
  },
  {
    pattern: /: any\[\]/g,
    replacement: ': unknown[]'
  },
  
  // Fix missing imports for common components
  {
    pattern: /^(import.*from '@\/components\/ui\/[^']*';)$/gm,
    replacement: '$1'
  }
];

// Create backup directory
function createBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    console.log(`Created backup directory: ${BACKUP_DIR}`);
  }
}

// Get all TypeScript files in budget directory
function getBudgetFiles() {
  const files = fs.readdirSync(BUDGET_DIR)
    .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
    .map(file => path.join(BUDGET_DIR, file));
  
  console.log(`Found ${files.length} budget component files`);
  return files;
}

// Create backup of a file
function createBackup(filePath) {
  const fileName = path.basename(filePath);
  const backupPath = path.join(BACKUP_DIR, `${fileName}.backup`);
  
  fs.copyFileSync(filePath, backupPath);
  console.log(`Created backup: ${backupPath}`);
}

// Fix common errors in file content
function fixFileContent(content, filePath) {
  let fixedContent = content;
  let changesCount = 0;
  
  // Apply each error pattern fix
  ERROR_PATTERNS.forEach((pattern, index) => {
    const beforeLength = fixedContent.length;
    fixedContent = fixedContent.replace(pattern.pattern, pattern.replacement);
    const afterLength = fixedContent.length;
    
    if (beforeLength !== afterLength) {
      changesCount++;
      console.log(`  Applied fix ${index + 1}: ${pattern.pattern.toString()}`);
    }
  });
  
  // Additional specific fixes for budget components
  
  // Fix missing EmptyState import
  if (fixedContent.includes('<EmptyState') && !fixedContent.includes("import { EmptyState }")) {
    const importSection = fixedContent.match(/^import.*$/gm);
    if (importSection && importSection.length > 0) {
      const lastImport = importSection[importSection.length - 1];
      fixedContent = fixedContent.replace(
        lastImport,
        lastImport + "\nimport { EmptyState } from '@/components/empty-state';"
      );
      changesCount++;
      console.log('  Added EmptyState import');
    }
  }
  
  // Fix missing formatCurrency import
  if (fixedContent.includes('formatCurrency') && !fixedContent.includes("import { formatCurrency }")) {
    const utilsImportMatch = fixedContent.match(/import.*from '@\/lib\/utils';/);
    if (utilsImportMatch) {
      const utilsImport = utilsImportMatch[0];
      if (!utilsImport.includes('formatCurrency')) {
        const newUtilsImport = utilsImport.replace(
          /import\s*{([^}]*)}\s*from\s*'@\/lib\/utils';/,
          (match, imports) => {
            const cleanImports = imports.trim();
            const newImports = cleanImports ? `${cleanImports}, formatCurrency` : 'formatCurrency';
            return `import { ${newImports} } from '@/lib/utils';`;
          }
        );
        fixedContent = fixedContent.replace(utilsImport, newUtilsImport);
        changesCount++;
        console.log('  Added formatCurrency to utils import');
      }
    } else {
      // Add new utils import
      const importSection = fixedContent.match(/^import.*$/gm);
      if (importSection && importSection.length > 0) {
        const lastImport = importSection[importSection.length - 1];
        fixedContent = fixedContent.replace(
          lastImport,
          lastImport + "\nimport { formatCurrency } from '@/lib/utils';"
        );
        changesCount++;
        console.log('  Added formatCurrency import');
      }
    }
  }
  
  return { content: fixedContent, changes: changesCount };
}

// Process a single file
function processFile(filePath) {
  console.log(`\nProcessing: ${filePath}`);
  
  try {
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Create backup
    createBackup(filePath);
    
    // Fix content
    const { content: fixedContent, changes } = fixFileContent(content, filePath);
    
    if (changes > 0) {
      // Write fixed content back to file
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`  ✅ Fixed ${changes} issues in ${path.basename(filePath)}`);
      return changes;
    } else {
      console.log(`  ✅ No issues found in ${path.basename(filePath)}`);
      return 0;
    }
  } catch (error) {
    console.error(`  ❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

// Main function
function main() {
  console.log('🔧 Budget Components Error Fixer');
  console.log('================================');
  
  // Create backup directory
  createBackupDir();
  
  // Get all budget files
  const budgetFiles = getBudgetFiles();
  
  if (budgetFiles.length === 0) {
    console.log('No budget component files found');
    return;
  }
  
  // Process each file
  let totalChanges = 0;
  let processedFiles = 0;
  
  budgetFiles.forEach(filePath => {
    const changes = processFile(filePath);
    totalChanges += changes;
    if (changes > 0) {
      processedFiles++;
    }
  });
  
  // Summary
  console.log('\n📊 Summary');
  console.log('===========');
  console.log(`Total files scanned: ${budgetFiles.length}`);
  console.log(`Files with fixes: ${processedFiles}`);
  console.log(`Total fixes applied: ${totalChanges}`);
  
  if (totalChanges > 0) {
    console.log('\n✅ Budget component fixes completed successfully!');
    console.log(`Backups created in: ${BACKUP_DIR}`);
  } else {
    console.log('\n✅ All budget components are already error-free!');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, processFile, fixFileContent };

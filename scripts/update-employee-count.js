/**
 * <PERSON><PERSON>t to update employee count for payroll runs
 *
 * Run with: node scripts/update-employee-count.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpackhrmanager')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define PayrollRun schema
const PayrollRunSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  payPeriod: {
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    year: {
      type: Number,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'processing', 'completed', 'approved', 'paid', 'cancelled'],
    default: 'draft',
  },
  totalEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  processedEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  totalGrossSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    required: true,
    default: 0,
  },
  totalTax: {
    type: Number,
    required: true,
    default: 0,
  },
  totalNetSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    trim: true,
  },
  departments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  }],
}, {
  timestamps: true,
});

// Create the models
const PayrollRun = mongoose.models.PayrollRun || mongoose.model('PayrollRun', PayrollRunSchema);
const Employee = mongoose.models.Employee || mongoose.model('Employee', new Schema({
  firstName: String,
  lastName: String,
  email: String,
  employmentStatus: {
    type: String,
    default: 'active',
  },
  departmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  },
}));

// Update employee count for all payroll runs
async function updateEmployeeCount() {
  try {
    // Get all payroll runs
    const payrollRuns = await PayrollRun.find();

    console.log(`Found ${payrollRuns.length} payroll runs`);

    // Get total active employees
    const totalEmployees = await Employee.countDocuments({ employmentStatus: 'active' });

    console.log(`Total active employees: ${totalEmployees}`);

    // Update each payroll run
    for (const payrollRun of payrollRuns) {
      // Get employees count based on departments
      let employeeCount = totalEmployees;

      if (payrollRun.departments && payrollRun.departments.length > 0) {
        employeeCount = await Employee.countDocuments({
          employmentStatus: 'active',
          departmentId: { $in: payrollRun.departments }
        });
      }

      // Update payroll run
      const updatedPayrollRun = await PayrollRun.findByIdAndUpdate(
        payrollRun._id,
        {
          $set: {
            totalEmployees: employeeCount
          }
        },
        { new: true }
      );

      console.log(`Updated payroll run ${payrollRun.name}: totalEmployees = ${updatedPayrollRun.totalEmployees}`);
    }

    console.log('All payroll runs updated successfully');
  } catch (error) {
    console.error('Error updating employee count:', error);
  } finally {
    // Close the database connection
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
updateEmployeeCount();

/**
 * This script optimizes the build process for Vercel deployment
 * It copies necessary files to the standalone directory and performs other optimizations
 */

const fs = require('fs');
const path = require('path');

// Copy custom server.js to the standalone directory
try {
  const serverSource = path.join(process.cwd(), 'server.js');
  const serverDest = path.join(process.cwd(), '.next/standalone/server.js');

  if (fs.existsSync(serverSource)) {
    fs.copyFileSync(serverSource, serverDest);
    console.log('✅ Custom server.js copied to standalone directory');
  } else {
    console.log('⚠️ server.js not found in root directory');
  }

  // Copy public directory to standalone for static asset serving
  const publicSource = path.join(process.cwd(), 'public');
  const publicDest = path.join(process.cwd(), '.next/standalone/public');

  if (fs.existsSync(publicSource)) {
    // Create the destination directory if it doesn't exist
    if (!fs.existsSync(publicDest)) {
      fs.mkdirSync(publicDest, { recursive: true });
    }

    // Copy all files from public to standalone/public
    const copyDir = (src, dest) => {
      const entries = fs.readdirSync(src, { withFileTypes: true });

      for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);

        if (entry.isDirectory()) {
          fs.mkdirSync(destPath, { recursive: true });
          copyDir(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    };

    copyDir(publicSource, publicDest);
    console.log('✅ Public directory copied to standalone directory');
  } else {
    console.log('⚠️ Public directory not found');
  }

  console.log('Build optimization completed successfully');
} catch (error) {
  console.error('Error during build optimization:', error);
  process.exit(1);
}

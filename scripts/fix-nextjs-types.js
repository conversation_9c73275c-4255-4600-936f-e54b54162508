#!/usr/bin/env node

/**
 * This script scans through Next.js pages and fixes common type errors:
 * 1. Incorrect params/searchParams types in server components
 * 2. Metadata exports in client components
 * 3. Missing Promise types for params and searchParams
 *
 * Usage: node scripts/fix-nextjs-types.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const APP_DIR = path.join(process.cwd(), 'app');
const BACKUP_DIR = path.join(process.cwd(), 'backup-pages');

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Error patterns to fix
const errorPatterns = {
  // Pattern 1: Server component with non-Promise params
  serverComponentParams: {
    find: /interface\s+(\w+)\s*\{\s*params\s*:\s*\{\s*id\s*:\s*string[^}]*\}\s*;?\s*\}/g,
    replace: 'interface $1 {\n  params: Promise<{\n    id: string;\n  }>;\n}',
    description: 'Server component with non-Promise params'
  },

  // Pattern 2: Server component with non-Promise searchParams
  serverComponentSearchParams: {
    find: /interface\s+(\w+)\s*\{\s*searchParams\s*:\s*\{([^}]*)\}\s*;?\s*\}/g,
    replace: 'interface $1 {\n  searchParams: Promise<{\n$2\n  }>;\n}',
    description: 'Server component with non-Promise searchParams'
  },

  // Pattern 3: Direct params access without awaiting
  paramsAccess: {
    find: /export\s+default\s+async\s+function\s+(\w+)\(\{\s*params\s*\}[^)]*\)\s*\{(?!\s*const\s+resolvedParams)/,
    replace: 'export default async function $1({ params }) {\n  const resolvedParams = await params;',
    description: 'Direct params access without awaiting'
  },

  // Pattern 4: Direct searchParams access without awaiting
  searchParamsAccess: {
    find: /export\s+default\s+async\s+function\s+(\w+)\(\{[^)]*searchParams[^)]*\}\)\s*\{(?!\s*const\s+resolvedSearchParams)/,
    replace: 'export default async function $1({ params, searchParams }) {\n  const resolvedParams = await params;\n  const resolvedSearchParams = await searchParams;',
    description: 'Direct searchParams access without awaiting'
  },

  // Pattern 5: Client component with metadata export
  clientComponentMetadata: {
    find: /"use client"[^]*export\s+const\s+metadata\s*:/,
    replace: '"use client"\n\n// Metadata moved to separate metadata.ts file',
    description: 'Client component with metadata export'
  },

  // Pattern 6: References to params.id that should be resolvedParams.id
  paramsIdReference: {
    find: /params\.id/g,
    replace: 'resolvedParams.id',
    description: 'Reference to params.id that should be resolvedParams.id'
  },

  // Pattern 7: References to searchParams properties that should be resolvedSearchParams
  searchParamsReference: {
    find: /searchParams\.(\w+)/g,
    replace: 'resolvedSearchParams.$1',
    description: 'Reference to searchParams property that should be resolvedSearchParams'
  },

  // Pattern 8: Client component with params interface (convert to useParams hook)
  clientComponentParams: {
    find: /"use client"[^]*interface\s+(\w+)\s*\{\s*params\s*:\s*\{\s*id\s*:\s*string[^}]*\}\s*;?\s*\}[^]*export\s+default\s+function\s+(\w+)\(\{\s*params\s*\}[^)]*\)\s*\{[^]*const\s*\{\s*id\s*\}\s*=\s*params/s,
    replace: '"use client"\n\nimport { useParams } from \'next/navigation\'\n\nexport default function $2() {\n  const params = useParams()\n  const id = params.id as string',
    description: 'Client component with params interface (convert to useParams hook)'
  },

  // Pattern 9: API route with incorrect params type
  apiRouteParams: {
    find: /export\s+async\s+function\s+(GET|POST|PUT|PATCH|DELETE)\(\s*req\s*:\s*NextRequest\s*,\s*\{\s*params\s*\}\s*:\s*\{\s*params\s*:\s*\{\s*id\s*:\s*string\s*\}\s*\}\s*\)/g,
    replace: 'export async function $1(\n  req: NextRequest,\n  context: { params: { id: string } }\n)',
    description: 'API route with incorrect params type'
  },

  // Pattern 10: API route params access
  apiRouteParamsAccess: {
    find: /params\.id/g,
    replace: 'context.params.id',
    description: 'API route params access'
  }
};

// Function to check if a file is a client component
function isClientComponent(content) {
  return content.includes('"use client"') || content.includes("'use client'");
}

// Function to check if a file is a server component
function isServerComponent(content) {
  return content.includes('export default async function');
}

// Function to check if a file is an API route
function isApiRoute(content) {
  return (
    content.includes('export async function GET') ||
    content.includes('export async function POST') ||
    content.includes('export async function PUT') ||
    content.includes('export async function PATCH') ||
    content.includes('export async function DELETE')
  );
}

// Function to create metadata.ts file for client components
function createMetadataFile(filePath, content) {
  const metadataMatch = content.match(/export\s+const\s+metadata\s*:\s*Metadata\s*=\s*\{([^}]*)\}/s);
  if (!metadataMatch) return false;

  const metadataContent = metadataMatch[0];
  const dirPath = path.dirname(filePath);
  const metadataPath = path.join(dirPath, 'metadata.ts');

  // Check if metadata.ts already exists
  if (fs.existsSync(metadataPath)) return false;

  // Extract imports needed for metadata
  const importMatch = content.match(/import\s+\{\s*Metadata\s*\}\s+from\s+['"]next['"];?/);
  const importStatement = importMatch ? importMatch[0] : "import { Metadata } from 'next';";

  // Create metadata.ts file
  const metadataFileContent = `${importStatement}\n\n${metadataContent};`;
  fs.writeFileSync(metadataPath, metadataFileContent);

  console.log(`Created metadata.ts file for ${filePath}`);
  return true;
}

// Function to fix a file
function fixFile(filePath) {
  console.log(`Checking ${filePath}...`);

  // Read file content
  const content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = false;

  // Create backup
  const relativePath = path.relative(process.cwd(), filePath);
  const backupPath = path.join(BACKUP_DIR, relativePath);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.writeFileSync(backupPath, content);

  // Check if it's a client component with metadata
  if (isClientComponent(content) && content.includes('export const metadata')) {
    console.log(`Found client component with metadata in ${filePath}`);
    createMetadataFile(filePath, content);

    // Apply pattern 5
    newContent = newContent.replace(errorPatterns.clientComponentMetadata.find,
                                   errorPatterns.clientComponentMetadata.replace);
    changes = true;
  }

  // Check if it's a client component with params interface
  if (isClientComponent(content) && content.includes('interface') && content.includes('params: {') && content.includes('id: string')) {
    console.log(`Found client component with params interface in ${filePath}`);

    // Apply pattern 8
    const clientParamsPattern = errorPatterns.clientComponentParams.find;
    if (clientParamsPattern.test(newContent)) {
      newContent = newContent.replace(clientParamsPattern,
                                     errorPatterns.clientComponentParams.replace);
      changes = true;
    }
  }

  // Check if it's a server component
  if (isServerComponent(content)) {
    console.log(`Found server component in ${filePath}`);

    // Apply pattern 1
    if (errorPatterns.serverComponentParams.find.test(newContent)) {
      newContent = newContent.replace(errorPatterns.serverComponentParams.find,
                                     errorPatterns.serverComponentParams.replace);
      changes = true;
    }

    // Apply pattern 2
    if (errorPatterns.serverComponentSearchParams.find.test(newContent)) {
      newContent = newContent.replace(errorPatterns.serverComponentSearchParams.find,
                                     errorPatterns.serverComponentSearchParams.replace);
      changes = true;
    }

    // Apply pattern 3
    if (errorPatterns.paramsAccess.find.test(newContent)) {
      newContent = newContent.replace(errorPatterns.paramsAccess.find,
                                     errorPatterns.paramsAccess.replace);
      changes = true;
    }

    // Apply pattern 4
    if (errorPatterns.searchParamsAccess.find.test(newContent)) {
      newContent = newContent.replace(errorPatterns.searchParamsAccess.find,
                                     errorPatterns.searchParamsAccess.replace);
      changes = true;
    }

    // Only apply these if we've already made changes (to avoid false positives)
    if (changes) {
      // Apply pattern 6
      newContent = newContent.replace(errorPatterns.paramsIdReference.find,
                                     errorPatterns.paramsIdReference.replace);

      // Apply pattern 7
      newContent = newContent.replace(errorPatterns.searchParamsReference.find,
                                     errorPatterns.searchParamsReference.replace);
    }
  }

  // Check if it's an API route
  if (isApiRoute(content)) {
    console.log(`Found API route in ${filePath}`);

    // Apply pattern 9
    if (errorPatterns.apiRouteParams.find.test(newContent)) {
      newContent = newContent.replace(errorPatterns.apiRouteParams.find,
                                     errorPatterns.apiRouteParams.replace);
      changes = true;

      // If we've made changes to the API route params, we need to update all references to params.id
      if (changes) {
        // Create a special version of the pattern that only applies to API routes
        // This is to avoid conflicts with other patterns that might use params.id
        const apiParamsPattern = /(\bparams\.)(\w+\b)(?!\.)/g;
        newContent = newContent.replace(apiParamsPattern, 'context.params.$2');
      }
    }
  }

  // Write changes if needed
  if (changes) {
    fs.writeFileSync(filePath, newContent);
    console.log(`Fixed issues in ${filePath}`);
    return true;
  }

  return false;
}

// Function to find all page.tsx and route.ts files
function findPages(directory = 'app') {
  try {
    // Handle special characters in directory names by using a different approach
    let searchDir = directory;

    // If directory contains special characters, use a more compatible approach
    if (directory.includes('(') || directory.includes(')')) {
      console.log('Directory contains special characters, using alternative search method...');
      // For directories with parentheses, we'll use a more general approach
      searchDir = 'app';
      const pagesResult = execSync(`find ${searchDir} -name "page.tsx" | grep "${directory.replace(/[()]/g, '\\$&')}"`, { encoding: 'utf8' });
      const routesResult = execSync(`find ${searchDir} -name "route.ts" | grep "${directory.replace(/[()]/g, '\\$&')}"`, { encoding: 'utf8' });
      return [...pagesResult.split('\n').filter(Boolean), ...routesResult.split('\n').filter(Boolean)];
    }

    const pagesResult = execSync(`find ${searchDir} -name "page.tsx"`, { encoding: 'utf8' });
    const routesResult = execSync(`find ${searchDir} -name "route.ts"`, { encoding: 'utf8' });
    return [...pagesResult.split('\n').filter(Boolean), ...routesResult.split('\n').filter(Boolean)];
  } catch (error) {
    console.error(`Error finding pages in ${directory}:`, error);
    return [];
  }
}

// Main function
function main(directory = 'app') {
  console.log(`Starting Next.js type error fix script for directory: ${directory}...`);

  const pages = findPages(directory);
  console.log(`Found ${pages.length} pages to check`);

  let fixedCount = 0;

  for (const page of pages) {
    try {
      const fixed = fixFile(page);
      if (fixed) fixedCount++;
    } catch (error) {
      console.error(`Error fixing ${page}:`, error);
    }
  }

  console.log(`Fixed issues in ${fixedCount} files`);
  console.log('Done!');
}

// Get directory from command line arguments or use default
const directory = process.argv[2] || 'app';
main(directory);

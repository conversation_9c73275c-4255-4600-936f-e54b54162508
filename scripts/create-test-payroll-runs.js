/**
 * <PERSON><PERSON>t to create test payroll runs
 * 
 * Run with: node scripts/create-test-payroll-runs.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrimpackhrmanager')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define PayrollRun schema
const PayrollRunSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  payPeriod: {
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    year: {
      type: Number,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'processing', 'completed', 'approved', 'paid', 'cancelled'],
    default: 'draft',
  },
  totalEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  processedEmployees: {
    type: Number,
    required: true,
    default: 0,
  },
  totalGrossSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    required: true,
    default: 0,
  },
  totalTax: {
    type: Number,
    required: true,
    default: 0,
  },
  totalNetSalary: {
    type: Number,
    required: true,
    default: 0,
  },
  currency: {
    type: String,
    required: true,
    default: 'MWK',
    trim: true,
  },
  notes: {
    type: String,
    trim: true,
  },
  departments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvedAt: {
    type: Date,
  },
  processedAt: {
    type: Date,
  },
  paidAt: {
    type: Date,
  },
}, {
  timestamps: true,
});

// Create the model
const PayrollRun = mongoose.models.PayrollRun || mongoose.model('PayrollRun', PayrollRunSchema);

// Create test payroll runs
async function createTestPayrollRuns() {
  try {
    // Check if we already have payroll runs
    const existingCount = await PayrollRun.countDocuments();
    console.log(`Found ${existingCount} existing payroll runs`);
    
    if (existingCount > 0) {
      console.log('Payroll runs already exist. Skipping creation.');
      return;
    }
    
    // Find a test user to use as createdBy
    const User = mongoose.models.User || mongoose.model('User', new Schema({}));
    const testUser = await User.findOne();
    
    if (!testUser) {
      console.error('No users found in the database. Please create a user first.');
      return;
    }
    
    const testUserId = testUser._id;
    console.log(`Using test user ID: ${testUserId}`);
    
    // Create payroll runs for the last 6 months
    const payrollRuns = [];
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    for (let i = 0; i < 6; i++) {
      // Calculate month and year
      let month = currentMonth - i;
      let year = currentYear;
      
      // Handle previous year
      if (month <= 0) {
        month += 12;
        year -= 1;
      }
      
      // Calculate start and end dates
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      
      // Create random values
      const totalEmployees = Math.floor(Math.random() * 20) + 5;
      const totalGrossSalary = (Math.floor(Math.random() * 5000) + 5000) * totalEmployees;
      const totalDeductions = Math.floor(totalGrossSalary * 0.1);
      const totalTax = Math.floor(totalGrossSalary * 0.15);
      const totalNetSalary = totalGrossSalary - totalDeductions - totalTax;
      
      // Determine status based on month
      let status;
      if (i === 0) {
        status = 'completed';
      } else if (i === 1) {
        status = 'approved';
      } else {
        status = 'paid';
      }
      
      // Create payroll run
      const payrollRun = new PayrollRun({
        name: `Payroll for ${startDate.toLocaleString('default', { month: 'long' })} ${year}`,
        description: `Monthly payroll processing for ${startDate.toLocaleString('default', { month: 'long' })} ${year}`,
        payPeriod: {
          month,
          year,
          startDate,
          endDate,
        },
        status,
        totalEmployees,
        processedEmployees: totalEmployees,
        totalGrossSalary,
        totalDeductions,
        totalTax,
        totalNetSalary,
        currency: 'MWK',
        notes: `Test payroll run created via script for ${startDate.toLocaleString('default', { month: 'long' })} ${year}`,
        createdBy: testUserId,
        processedAt: new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())),
      });
      
      // Add approval and payment dates for approved and paid runs
      if (status === 'approved' || status === 'paid') {
        payrollRun.approvedBy = testUserId;
        payrollRun.approvedAt = new Date(payrollRun.processedAt.getTime() + 86400000); // 1 day after processing
      }
      
      if (status === 'paid') {
        payrollRun.paidAt = new Date(payrollRun.approvedAt.getTime() + 86400000); // 1 day after approval
      }
      
      payrollRuns.push(payrollRun);
    }
    
    // Save all payroll runs
    const savedRuns = await Promise.all(payrollRuns.map(run => run.save()));
    console.log(`Successfully created ${savedRuns.length} test payroll runs`);
    
    // Log the created runs
    savedRuns.forEach(run => {
      console.log(`- ${run.name} (${run.status}): ${run._id}`);
    });
  } catch (error) {
    console.error('Error creating test payroll runs:', error);
  } finally {
    // Close the database connection
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
createTestPayrollRuns();

#!/usr/bin/env node

/**
 * Fix API Route Params Script (v2)
 * 
 * This script updates the API route handler parameter types to match the Next.js 14+ format.
 * It changes `context: { params: { ... } }` to `{ params }: { params: { ... } }`
 * and updates all references to `context.params.X` to `params.X`.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all route.ts files in the app/api directory
function findApiRouteFiles() {
  try {
    const result = execSync('find app/api -name "route.ts" -type f', { encoding: 'utf8' });
    return result.split('\n').filter(<PERSON><PERSON>an);
  } catch (error) {
    console.error('Error finding API route files:', error);
    return [];
  }
}

// Check if a file contains the old parameter format
function fileNeedsFixing(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return (
      content.includes('context: { params:') || 
      content.includes('context.params.') ||
      content.includes('{ params }: Promise<{ params:')
    );
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error);
    return false;
  }
}

// Fix the parameter format in a file
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Create a backup of the original file
    const backupPath = `${filePath}.bak`;
    fs.writeFileSync(backupPath, content);
    
    // Replace the parameter format in function declarations
    content = content.replace(
      /export async function (GET|POST|PUT|PATCH|DELETE)\(\s*([^,\)]+),\s*context:\s*{\s*params:\s*{([^}]*)}\s*}\s*\)/g,
      'export async function $1(\n  $2,\n  { params }: { params: {$3} }\n)'
    );
    
    // Replace Promise<{ params: ... }> format
    content = content.replace(
      /export async function (GET|POST|PUT|PATCH|DELETE)\(\s*([^,\)]+),\s*{\s*params\s*}:\s*Promise<{\s*params:\s*{([^}]*)}\s*}>\s*\)/g,
      'export async function $1(\n  $2,\n  { params }: { params: {$3} }\n)'
    );
    
    // Replace references to context.params.X with params.X
    content = content.replace(/context\.params\.([a-zA-Z0-9_]+)/g, 'params.$1');
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ Fixed ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error fixing file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('🔍 Finding API route files...');
  const files = findApiRouteFiles();
  console.log(`Found ${files.length} API route files.`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (fileNeedsFixing(file)) {
      console.log(`Fixing ${file}...`);
      if (fixFile(file)) {
        fixedCount++;
      }
    }
  }
  
  console.log(`\n✨ Done! Fixed ${fixedCount} files.`);
}

main();

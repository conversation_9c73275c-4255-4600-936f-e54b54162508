# Check if TypeScript is installed
try {
    $tscVersion = npx tsc --version
    Write-Host "TypeScript is installed: $tscVersion"
}
catch {
    Write-Host "TypeScript is not installed. Please install it with: npm install -g typescript" -ForegroundColor Red
    exit 1
}

# Compile the TypeScript scanner
Write-Host "Compiling the component scanner..." -ForegroundColor Cyan
try {
    npx tsc --esModuleInterop scripts/component-scanner.ts
    Write-Host "Scanner compiled successfully." -ForegroundColor Green
}
catch {
    Write-Host "Failed to compile the scanner: $_" -ForegroundColor Red
    exit 1
}

# Run the compiled scanner
Write-Host "Running the component scanner..." -ForegroundColor Cyan
try {
    node scripts/component-scanner.js
    Write-Host "Scanner completed successfully." -ForegroundColor Green
    
    # Check if results file exists
    $resultsPath = Join-Path -Path $PWD -ChildPath "component-scan-results.json"
    if (Test-Path $resultsPath) {
        Write-Host "Results saved to: $resultsPath" -ForegroundColor Green
    }
    else {
        Write-Host "No results file was generated." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error running the scanner: $_" -ForegroundColor Red
    exit 1
}

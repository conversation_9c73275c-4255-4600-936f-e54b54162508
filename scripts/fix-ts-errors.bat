@echo off
echo TypeScript Error Finder and Fixer
echo ===============================

if "%1"=="" (
  echo Usage: fix-ts-errors.bat [directory]
  echo Example: fix-ts-errors.bat app\(dashboard)\dashboard\accounting
  exit /b 1
)

echo Scanning directory: %1

echo.
echo Step 1: Running TypeScript Error Scanner...
npx ts-node scripts/ts-error-scanner.ts %1

echo.
echo Step 2: Running TypeScript Error Fixer...
npx ts-node scripts/ts-error-fixer.ts %1

echo.
echo Step 3: Running TypeScript Compiler to verify fixes...
npx tsc --noEmit --project tsconfig.json

echo.
echo Done!

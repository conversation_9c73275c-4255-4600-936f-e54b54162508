const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Whether to create backups
  createBackups: true,
  // Files to fix
  filesToFix: [
    // Audit trail detail
    {
      path: 'components/accounting/audit/audit-trail-detail.tsx',
      replacements: [
        {
          pattern: /const changes: { field: string; oldValue: any; newValue: unknown }\[\] = \[\]/g,
          replacement: 'const changes: { field: string; oldValue: unknown; newValue: unknown }[] = []'
        }
      ]
    },
    // Report generator
    {
      path: 'components/accounting/shared/report-generator.tsx',
      replacements: [
        {
          pattern: /incomeData\?: any;/g,
          replacement: 'incomeData?: Array<{ category: string; budgeted: number; actual: number; variance: number }>;'
        },
        {
          pattern: /expenseData\?: any;/g,
          replacement: 'expenseData?: Array<{ category: string; budgeted: number; actual: number; variance: number }>;'
        },
        {
          pattern: /budgetData\?: any;/g,
          replacement: `budgetData?: {
    id?: string;
    name?: string;
    description?: string;
    fiscalYear?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    totalIncome: number;
    totalExpense: number;
    totalActualIncome: number;
    totalActualExpense: number;
    balance?: number;
    categories?: Array<{
      id: string;
      name: string;
      type: 'income' | 'expense';
      budgetedAmount: number;
      actualAmount: number;
    }>;
  };`
        }
      ]
    },
    // Next-auth issue
    {
      path: 'app/api/payroll/salary-structures/bulk-delete/route.ts',
      replacements: [
        {
          pattern: /\/\/ Note: We're using our custom getServerSession, not the one from next-auth/g,
          replacement: '// Using custom authentication system'
        },
        {
          pattern: /import { getServerSession } from "next-auth\/next"/g,
          replacement: 'import { getServerSession } from "@/lib/auth/session"'
        }
      ]
    }
  ]
};

// Function to fix a file
function fixFile(fileConfig) {
  const filePath = path.join(__dirname, '..', fileConfig.path);
  
  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${fileConfig.path}`);
    return false;
  }
  
  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let changesApplied = false;
  
  // Apply pattern replacements
  if (fileConfig.replacements) {
    for (const replacement of fileConfig.replacements) {
      if (replacement.pattern && replacement.replacement) {
        // Apply regex replacement
        const newContent = content.replace(replacement.pattern, replacement.replacement);
        if (newContent !== content) {
          content = newContent;
          changesApplied = true;
          console.log(`Applied pattern replacement in ${fileConfig.path}`);
        }
      }
    }
  }
  
  // Save changes if any were applied
  if (changesApplied) {
    // Create backup if configured
    if (config.createBackups) {
      fs.writeFileSync(`${filePath}.bak`, originalContent);
      console.log(`Created backup at ${filePath}.bak`);
    }
    
    // Write updated content
    fs.writeFileSync(filePath, content);
    console.log(`Applied fixes to ${fileConfig.path}`);
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Fixing remaining issues...');
  
  let fixedFiles = 0;
  
  // Process each file
  for (const fileConfig of config.filesToFix) {
    const wasFixed = fixFile(fileConfig);
    if (wasFixed) {
      fixedFiles++;
    }
  }
  
  console.log(`\nSummary: Fixed issues in ${fixedFiles} out of ${config.filesToFix.length} files`);
  
  if (fixedFiles > 0) {
    console.log('\nRecommended next steps:');
    console.log('1. Run TypeScript compiler to check for any remaining issues:');
    console.log('   npx tsc --noEmit');
    console.log('2. Run the component scanner to check for other issues:');
    console.log('   npm run scan-components');
  }
}

// Run the script
main();

# Income Status Changes and UI Impact Analysis

## Overview

This document provides a detailed analysis of income status changes in the system and their impact on the Budget Planning UI, Income Dashboard, and related financial reporting components.

## Income Status Flow

### Available Statuses
```typescript
type IncomeStatus = 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'received' | 'cancelled';
```

### Status Transition Rules
From the `validTransitions` configuration in the status API:

```typescript
const validTransitions = {
  'draft': ['pending_approval', 'approved', 'received', 'cancelled'],
  'pending_approval': ['approved', 'rejected', 'cancelled'],
  'approved': ['received', 'cancelled'],
  'rejected': ['draft', 'cancelled'],
  'received': ['cancelled'],
  'cancelled': []
};
```

## Detailed Status Analysis

### 1. **DRAFT** Status
**Meaning**: Income record created but not yet processed or approved.

**UI Impact**:
- **Income Dashboard**: Not included in any budget calculations or totals
- **Budget Planning**: No impact on budget vs actual comparisons
- **Income Overview**: Shows in draft count but not in financial totals
- **Stats Cards**: Excluded from all KPI calculations

**Budget Integration**: ❌ **NO BUDGET IMPACT**
- `appliedToBudget` flag ignored
- No transactions created
- Budget actuals remain unchanged

### 2. **PENDING_APPROVAL** Status
**Meaning**: Income submitted for approval workflow.

**UI Impact**:
- **Income Dashboard**: Shows in pending items count
- **Budget Planning**: No budget impact yet
- **Income Overview**: Appears in approval queue
- **Workflow UI**: Visible to approvers with action buttons

**Budget Integration**: ❌ **NO BUDGET IMPACT**
- Awaiting approval decision
- No budget calculations affected

### 3. **APPROVED** Status ⭐
**Meaning**: Income has been approved but money may not be physically received yet.

**UI Impact**:
- **Income Dashboard**:
  - ✅ Included in `totalIncome` calculations
  - ✅ Affects `budgetUtilization` percentage
  - ✅ Updates `budgetVariance` calculations
  - ✅ Impacts trend analysis and forecasting

- **Budget Planning UI**:
  - ✅ Updates "Actual Income" in budget vs actual charts
  - ✅ Affects budget utilization progress bars
  - ✅ Triggers budget variance alerts if thresholds exceeded
  - ✅ Updates real-time budget tracker

- **Stats Cards**:
  - ✅ Included in "Total Income" KPI
  - ✅ Affects "Budget Achievement" percentage
  - ✅ Updates source-wise income breakdown

**Budget Integration**: ✅ **FULL BUDGET IMPACT**
```typescript
// Triggers budget update when status changes to 'approved'
if (doc.appliedToBudget && doc.budgetCategory && doc.status === 'approved') {
  await budgetTransactionService.updateBudgetActuals(doc.budget.toString());
  await budgetTransactionService.updateCategoryActual(doc.budgetCategory.toString());
}
```

**Database Changes**:
- Sets `approvedAt` timestamp
- Sets `approvedBy` user reference
- Creates `statusHistory` entry
- Updates approval workflow status

### 4. **RECEIVED** Status ⭐⭐
**Meaning**: Money has been physically received (most definitive status).

**UI Impact**: 
- **Same as APPROVED** plus additional confidence indicators
- **Income Dashboard**: 
  - ✅ Highest priority in calculations
  - ✅ Shows as "confirmed" income in charts
  - ✅ Updates cash flow projections

- **Budget Planning**:
  - ✅ Treated as "realized" income
  - ✅ Updates actual vs budget with highest confidence
  - ✅ Affects cash flow forecasting

**Budget Integration**: ✅ **FULL BUDGET IMPACT** (Same as approved)

**Database Changes**:
- Sets `receivedAt` timestamp  
- Sets `receivedBy` user reference
- Creates `statusHistory` entry

### 5. **REJECTED** Status
**Meaning**: Income has been rejected during approval process.

**UI Impact**:
- **Income Dashboard**: Excluded from all financial calculations
- **Budget Planning**: No budget impact
- **Income Overview**: Shows in rejected items list
- **Workflow UI**: Shows rejection reason and allows resubmission

**Budget Integration**: ❌ **REMOVES BUDGET IMPACT**
```typescript
// Removes from budget if previously applied
if ((newStatus === 'rejected') && income.appliedToBudget) {
  // Remove from budget calculations
}
```

### 6. **CANCELLED** Status
**Meaning**: Income record has been cancelled.

**UI Impact**:
- **All UIs**: Completely excluded from calculations
- **Reports**: May show in audit trails but not financial totals

**Budget Integration**: ❌ **REMOVES BUDGET IMPACT**

## Budget Planning UI Impact Details

### Real-time Budget Tracker Updates
When income status changes to `approved` or `received`:

1. **Total Income Card**:
   ```typescript
   totalActualIncome += income.amount; // Increases actual income
   incomeVariance = totalActualIncome - totalBudgetedIncome; // Recalculates variance
   ```

2. **Budget Utilization Progress Bar**:
   ```typescript
   budgetUtilization = (totalActualIncome / totalBudgetedIncome) * 100;
   ```

3. **Category-wise Breakdown**:
   - Updates specific income category actuals
   - Recalculates category-level variances
   - Updates category progress indicators

### Alert System Triggers
Based on budget utilization thresholds:

- **🟢 On Track** (80-100%): `budgetUtilization >= 80 && budgetUtilization <= 100`
- **🟡 Needs Attention** (<50%): `budgetUtilization < 50`  
- **🔴 Over Budget** (>100%): `budgetUtilization > 100`

## Income Dashboard Impact

### KPI Cards Updates
When status changes to `approved`/`received`:

1. **Total Income**: Immediate increase by income amount
2. **Budget Performance**: Updates utilization percentage
3. **Period Comparison**: Affects month-over-month comparisons
4. **Growth Rate**: Impacts income growth calculations

### Charts and Visualizations
- **Income Sources Pie Chart**: Updates source distribution
- **Monthly Trend Line**: Adds to current month's total
- **Budget vs Actual Bar Chart**: Updates actual values
- **Cumulative Income Chart**: Increases cumulative totals

## Technical Implementation

### Middleware Triggers
The Income model has post-save middleware that automatically updates budgets:

```typescript
IncomeSchema.post('save', async function(doc) {
  if (doc.appliedToBudget && doc.budgetCategory && 
      (doc.status === 'received' || doc.status === 'approved')) {
    await budgetTransactionService.updateBudgetActuals(doc.budget.toString());
    await budgetTransactionService.updateCategoryActual(doc.budgetCategory.toString());
  }
});
```

### API Integration Points
- **Status Change API**: `/api/accounting/income/[id]/status`
- **Budget Integration Service**: Handles automatic budget updates
- **Real-time Updates**: WebSocket notifications for UI updates

## Specific UI Component Behaviors

### Income Overview Page (`income-overview.tsx`)
**Status-Based Filtering and Display**:

```typescript
// KPI Calculations only include approved/received income
const kpis = useMemo(() => {
  const currentTotal = incomeStats.totalIncome || 0; // Only approved/received
  const budgetUtilization = budgetedTotal > 0 ? (currentTotal / budgetedTotal) * 100 : 0;

  return {
    totalIncome: currentTotal,
    budgetedIncome: budgetedTotal,
    budgetUtilization,
    isOverBudget: budgetUtilization > 100,
    isOnTrack: budgetUtilization >= 80 && budgetUtilization <= 100,
    needsAttention: budgetUtilization < 50
  };
}, [incomeStats]);
```

**Visual Indicators**:
- 🟢 **Green Progress Bar**: Budget utilization 80-100%
- 🟡 **Yellow Progress Bar**: Budget utilization 50-79%
- 🔴 **Red Progress Bar**: Budget utilization >100%

### Income Dashboard (`income-dashboard.tsx`)
**Real-time Metrics Updates**:

1. **Budget Performance Card**:
   ```typescript
   <div className="text-2xl font-bold">
     {kpis?.budgetUtilization.toFixed(1)}%
   </div>
   <Progress value={Math.min(kpis?.budgetUtilization || 0, 100)} />
   ```

2. **Budget Alerts System**:
   ```typescript
   {kpis?.budgetUtilization && kpis.budgetUtilization > 100 && (
     <div className="bg-red-50 border border-red-200">
       <p>Budget Exceeded by {(kpis.budgetUtilization - 100).toFixed(1)}%</p>
     </div>
   )}
   ```

### Budget Planning Integration (`real-time-budget-integration.tsx`)
**Live Budget Tracker Updates**:

```typescript
// Summary Cards Update
<Card>
  <CardTitle>Total Income</CardTitle>
  <div className="text-2xl font-bold">{formatMWK(data.totalActualIncome)}</div>
  <p className="text-xs">Budget: {formatMWK(data.totalBudgetedIncome)}</p>
  <p className={getVarianceColor(incomeVariance, true)}>
    {incomeVariance >= 0 ? '+' : ''}{formatMWK(incomeVariance)} variance
  </p>
</Card>
```

**Category-Level Impact**:
- Updates specific income category actuals
- Recalculates category variance percentages
- Updates category progress bars
- Triggers category-level alerts

### Income Stats Hook (`use-income-stats.ts`)
**Data Aggregation Logic**:

```typescript
// Only includes approved/received income in calculations
const incomeBySource = (incomeData || []).map((item: any) => {
  const budgetCategory = budgetData?.budget?.incomeCategories?.find(
    (cat: any) => cat.source === item.source
  );

  const budgeted = budgetCategory?.amount || 0;
  const variance = item.value - budgeted; // item.value only from approved/received
  const variancePercentage = budgeted > 0 ? (variance / budgeted) * 100 : 0;

  return {
    ...item,
    percentage: totalIncome > 0 ? (item.value / totalIncome) * 100 : 0,
    budgeted,
    variance,
    variancePercentage
  };
});
```

## Database Transaction Flow

### Status Change Sequence
1. **User Action**: Click "Approve" or "Mark as Received"
2. **API Call**: `PUT /api/accounting/income/[id]/status`
3. **Status Update**: Income status changed in database
4. **Middleware Trigger**: Post-save hook executes
5. **Budget Integration**: `budgetIntegrationService.handleNewIncome()`
6. **Transaction Creation**: Creates/updates Transaction record
7. **Budget Actuals Update**: Recalculates budget actual amounts
8. **UI Refresh**: Real-time updates via WebSocket or polling

### Budget Integration Service Flow
```typescript
async handleNewIncome(income) {
  // 1. Find active budget for income date
  const activeBudget = await Budget.findOne({
    startDate: { $lte: income.date },
    endDate: { $gte: income.date },
    status: { $in: ['approved', 'active'] }
  });

  // 2. Find matching income category
  const matchingCategory = activeBudget.incomeCategories.find(
    cat => cat.source === income.source
  );

  // 3. Create/update transaction record
  const transaction = await Transaction.create({
    date: income.date,
    amount: income.amount,
    type: 'income',
    sourceId: income._id,
    sourceType: 'Income',
    metadata: {
      budgetId: activeBudget._id,
      categoryId: matchingCategory._id
    }
  });

  // 4. Update budget actuals
  await this.updateBudgetActuals(activeBudget._id.toString());
}
```

## Performance Considerations

### Real-time Updates
- **WebSocket Integration**: Immediate UI updates when status changes
- **Optimistic Updates**: UI updates before server confirmation
- **Batch Processing**: Multiple status changes processed efficiently
- **Caching Strategy**: Budget calculations cached for performance

### Data Consistency
- **Transaction Isolation**: Status changes are atomic
- **Rollback Capability**: Failed budget updates don't affect status change
- **Audit Trail**: Complete history of status changes maintained

## Summary

**Financial Impact Statuses**: `approved` and `received`
**Non-Impact Statuses**: `draft`, `pending_approval`, `rejected`, `cancelled`

The system treats both `approved` and `received` statuses as financially realized income for budget planning purposes, with `received` being the most definitive confirmation of actual income receipt.

**Key UI Behaviors**:
- Only `approved` and `received` income affects budget calculations
- Real-time updates across all budget-related components
- Progressive visual indicators based on budget utilization
- Comprehensive alert system for budget variances
- Category-level tracking and variance analysis

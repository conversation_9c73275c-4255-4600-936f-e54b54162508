{"scanDate": "2025-05-31T13:37:20.250Z", "totalFiles": 11, "files": [{"file": "app\\api\\accounting\\employee-salaries\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/models\\/accounting\\/EmployeeSalary['\"]/g", "service": "Accounting EmployeeSalary Model", "replacement": "models/payroll/EmployeeSalary", "matches": 1, "lines": [5]}]}, {"file": "app\\api\\accounting\\payroll\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/lib\\/services\\/accounting\\/payroll-service['\"]/g", "service": "Accounting PayrollService", "replacement": "unifiedPayrollService", "matches": 1, "lines": [6]}]}, {"file": "app\\api\\accounting\\payroll\\[id]\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/lib\\/services\\/accounting\\/payroll-service['\"]/g", "service": "Accounting PayrollService", "replacement": "unifiedPayrollService", "matches": 1, "lines": [6]}]}, {"file": "app\\api\\employees\\[id]\\current-salary\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/services\\/payroll\\/SalaryService['\"]/g", "service": "SalaryService", "replacement": "unifiedPayrollService", "matches": 1, "lines": [9]}]}, {"file": "app\\api\\employees\\[id]\\salary-history\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/services\\/payroll\\/SalaryService['\"]/g", "service": "SalaryService", "replacement": "unifiedPayrollService", "matches": 1, "lines": [9]}]}, {"file": "app\\api\\employees\\[id]\\salary-revisions\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/services\\/payroll\\/SalaryService['\"]/g", "service": "SalaryService", "replacement": "unifiedPayrollService", "matches": 1, "lines": [9]}]}, {"file": "app\\api\\payroll\\process\\route.ts", "findings": [{"pattern": "/from ['\"].*\\/services\\/payroll\\/PayrollService['\"]/g", "service": "PayrollService (old)", "replacement": "unifiedPayrollService", "matches": 1, "lines": [8]}]}, {"file": "lib\\services\\accounting\\payroll-service.ts", "findings": [{"pattern": "/from ['\"].*\\/models\\/accounting\\/PayrollRecord['\"]/g", "service": "Accounting PayrollRecord Model", "replacement": "models/payroll/PayrollRecord", "matches": 1, "lines": [2]}, {"pattern": "/from ['\"].*\\/models\\/accounting\\/EmployeeSalary['\"]/g", "service": "Accounting EmployeeSalary Model", "replacement": "models/payroll/EmployeeSalary", "matches": 1, "lines": [3]}]}, {"file": "lib\\services\\payroll\\enhanced-payroll-processor.ts", "findings": [{"pattern": "/from ['\"].*salary-calculation-service['\"]/g", "service": "SalaryCalculationService", "replacement": "unifiedPayrollService.calculateEmployeeSalary", "matches": 1, "lines": [7]}, {"pattern": "/salaryCalculationService\\.calculateSalary/g", "service": "SalaryCalculationService method call", "replacement": "unifiedPayrollService.calculateEmployeeSalary", "matches": 1, "lines": [309]}]}, {"file": "lib\\services\\payroll\\payroll-service.ts", "findings": [{"pattern": "/from ['\"].*salary-calculation-service['\"]/g", "service": "SalaryCalculationService", "replacement": "unifiedPayrollService.calculateEmployeeSalary", "matches": 1, "lines": [4]}, {"pattern": "/salaryCalculationService\\.calculateSalary/g", "service": "SalaryCalculationService method call", "replacement": "unifiedPayrollService.calculateEmployeeSalary", "matches": 1, "lines": [141]}]}, {"file": "lib\\services\\payroll\\payslip-generation-service.ts", "findings": [{"pattern": "/from ['\"].*salary-calculation-service['\"]/g", "service": "SalaryCalculationService", "replacement": "unifiedPayrollService.calculateEmployeeSalary", "matches": 1, "lines": [8]}]}]}
# TCM Enterprise Suite

A comprehensive Enterprise Resource Planning (ERP) system designed for businesses and organizations in Malawi, with multi-platform deployment capabilities.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Run development server
npm run dev
```

## 📋 Key Project Documents

### **TCM_ENTERPRISE_SUITE_BUSINESS_ARCHITECTURE_AND_SERVICES.md**
Complete system architecture and business overview including:
- Technology stack and deployment strategy
- Module breakdown (Employee, HR, Payroll, Accounting)
- Integration architecture and business value proposition
- Future roadmap and development plans

### **HR_PAYROLL_ACCOUNTING_UPDATE_TRACKER.md**
Core module status tracker for production readiness:
- Employee Management, HR, Payroll, and Accounting module status
- Feature-by-feature implementation tracking
- Critical production readiness checklist
- Testing and quality assurance requirements

### **ENTITY_RECONCILIATION_PLAN.md**
Strategic plan for resolving HR/Employee entity conflation:
- Clear separation of Employee (core entity) vs HR (process management)
- Migration plan for service layer reorganization
- Route structure cleanup and API consolidation
- Implementation timeline and risk mitigation

## 🏗️ System Architecture

### Core Modules
- **Employee Management** - Core entity management for organizational staff
- **Human Resources** - HR processes, recruitment, performance, training
- **Payroll** - Salary processing, tax calculation, payslip generation
- **Accounting** - Financial management, ledger, reporting, banking

### Technology Stack
- **Frontend**: Next.js 15.2.4, React 19, TypeScript, Tailwind CSS
- **Backend**: Node.js, Next.js API Routes, MongoDB
- **Deployment**: Vercel (primary), Railway.com (secondary)
- **Authentication**: Custom JWT-based system

## 📁 Project Structure

```
├── app/                    # Next.js app directory
├── components/             # React components
├── lib/                    # Utilities and services
├── models/                 # MongoDB models
├── types/                  # TypeScript type definitions
├── project_guides/         # Development guides and documentation
└── [Key Documents]         # Three main project documents
```

## 🔧 Development

### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- Environment variables configured

### Available Scripts
```bash
npm run dev          # Development server
npm run build        # Production build
npm run start        # Production server
npm run lint         # ESLint
npm run type-check   # TypeScript checking
```

## 🌍 Deployment

### Multi-Platform Support
- **Vercel**: Primary deployment with TypeScript bypass for rapid deployment
- **Railway.com**: Secondary deployment with full feature parity
- **Database**: MongoDB Atlas with automatic scaling

### Environment Configuration
The system supports multiple deployment environments with automatic client selection for multi-tenant operations.

## 📚 Documentation

All development guides, module trackers, and technical documentation have been organized in the `project_guides/` directory. This includes:

- Module development trackers
- Implementation guides
- Technical specifications
- Development scripts and utilities

## 🤝 Contributing

1. Review the key project documents above
2. Check the relevant module tracker in `project_guides/`
3. Follow the entity reconciliation guidelines
4. Ensure TypeScript compliance (with bypass for deployment)

## 📄 License

This project is proprietary software developed for the Teachers Council of Malawi and licensed clients.

## 🆘 Support

For technical support or questions about the system architecture, refer to:
1. The three key documents in the root directory
2. Module-specific documentation in `project_guides/docs/`
3. Development trackers for current status and known issues

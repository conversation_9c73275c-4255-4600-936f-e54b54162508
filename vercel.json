{"buildCommand": "pnpm run vercel-build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "pnpm install", "regions": ["iad1"], "env": {"NEXT_PUBLIC_API_URL": "/api", "NODE_ENV": "production", "SKIP_TYPE_CHECK": "true"}, "cleanUrls": true, "trailingSlash": false, "rewrites": [{"source": "/docs/:path*", "destination": "/docs/:path*"}], "github": {"silent": true}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}